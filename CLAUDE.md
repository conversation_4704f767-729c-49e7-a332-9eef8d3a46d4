# CLAUDE.md - Episteme Context Engineering Configuration

> **Note**: For deployment documentation, see `CLAUDE_DEPLOYMENT.md`
> This file follows Context Engineering standards for AI-assisted development workflows.

## 🔄 Project Awareness & Context - **Research-First Evidence-Based Development**

- **Research is Source of Truth** - All implementations must be backed by fresh, official documentation from the `/research/` directory. Your knowledge is outdated; use scraped documentation as absolute truth.
- **Always read `TASK.md`** at conversation start to understand current priorities, especially critical security vulnerabilities and Context Engineering research coordination.
- **Check Current Status** - Analysis-engine has critical security vulnerabilities (idna, protobuf) that block production deployment. All work must prioritize security resolution.
- **Use `/research/` directory FIRST** - Before any implementation, consult relevant research documentation:
  - `research/rust/` - Security, performance, unsafe patterns, production deployment
  - `research/python/` - FastAPI, ML frameworks, NLP pipelines, LLM integration
  - `research/google-cloud/` - Cloud Run, Spanner, monitoring, deployment patterns
  - `research/security/` - Vulnerability management, secure deployment, compliance
- **Reference `/examples/` directory** - Use established patterns for consistent implementation
- **Validation Framework Integration** - Build upon existing `validation-results/analysis-engine-prod-readiness/` evidence and scripts
- **Multi-Agent Coordination** - Support Context Engineering multi-agent research and implementation patterns

## 🧱 Code Structure & Modularity

- **Never create a file longer than 500 lines of code.** If a file approaches this limit, refactor by splitting it into modules or helper files.
- **Organize code into clearly separated modules**, grouped by feature or responsibility:
  - `main.rs` / `lib.rs` - Main entry points and core logic
  - `api/` - API handlers and middleware
  - `services/` - Business logic services
  - `models/` - Data models and schemas
  - `storage/` - Database and cache layers
  - `utils/` - Utility functions and helpers
- **Use clear, consistent imports** (prefer relative imports within packages).
- **Follow Rust best practices**: Use `Result<T, E>` for error handling, avoid `unwrap()` in production code, use proper lifetime annotations.
- **Use environment variables** with proper defaults and validation.

## 🧪 Testing & Reliability - **Docker & Self-Testing**

- **You must use Docker** and you must do unit tests so when I open the dashboard, it just works. I don't have to worry about fixing anything.
- **Always create comprehensive tests for new features** (functions, structs, modules, API endpoints).
- **After updating any logic**, check whether existing tests need updates. If so, update them.
- **Tests should live in appropriate locations**:
  - Unit tests: `#[cfg(test)]` modules in same file or `tests/` subdirectory
  - Integration tests: `tests/` directory at crate root
- **Include at least**:
  - 1 test for expected/happy path use
  - 1 edge case test
  - 1 failure/error case test
- **Use Docker commands** whenever executing commands, including for tests.
- **Set up Docker** - Setup a Docker instance for development and be aware of Docker output for self-improvement.

## 📚 Context Engineering Standards - **Research-First Evidence-Based Development**

### Research & Documentation Standards
- **Official Documentation Only** - Use ONLY official documentation from `/research/` directory and authoritative sources
- **Comprehensive Research Required** - 30-100+ pages per technology area for complete context
- **Quality Validation** - Verify scraped content completeness, re-scrape on failure until successful
- **Research-Backed Decisions** - All implementation decisions must reference specific research documentation
- **Current Security Context** - Analysis-engine has critical vulnerabilities requiring immediate research-backed resolution

### Evidence-Based Development Requirements
- **Security Priority** - All work must address current security vulnerabilities (idna, protobuf upgrades)
- **Memory Safety** - Document all unsafe blocks with SAFETY comments based on official Rust documentation
- **Performance Validation** - Use research-backed benchmarking and optimization patterns
- **Production Readiness** - Follow research-documented production deployment and monitoring patterns

### Multi-Agent Coordination Standards
- **Research Agent Deployment** - Support 6 specialized research agents for comprehensive documentation gathering
- **Context Handoff** - Provide complete project context to agents with no prior knowledge assumptions
- **Evidence Collection** - Systematic evidence gathering in `validation-results/` framework
- **Validation Loops** - Implement continuous testing and self-correction based on research patterns

### Validation Framework Integration
- **Build on Existing Evidence** - Use `validation-results/analysis-engine-prod-readiness/` findings as foundation
- **Systematic Validation** - Execute validation scripts and collect evidence at each implementation step
- **Quality Gates** - Implement research-backed validation criteria before proceeding to next phase
- **Progressive Success** - Security fixes → Performance validation → Production deployment

## 🎯 PRP (Product Requirements Prompt) Workflow Integration

### Feature Development Process
1. **Create INITIAL.md** - Describe feature requirements using the template
2. **Generate PRP** - Use `/generate-prp INITIAL.md` to create comprehensive implementation blueprint
3. **Execute PRP** - Use `/execute-prp PRPs/feature.md` to implement with validation loops
4. **Validate & Iterate** - Run validation commands until all tests pass

### PRP Standards
- **Context is King** - Include ALL necessary documentation, examples, and caveats
- **Information Dense** - Use keywords and patterns from the codebase
- **Validation Loops** - Provide executable tests/lints the AI can run and fix
- **Anti-Patterns** - Document what NOT to do and common pitfalls

## ✅ Task Completion & Documentation

- **Mark completed tasks in `TASK.md`** immediately after finishing them.
- **Add new sub-tasks or TODOs** discovered during development to `TASK.md` under a "Discovered During Work" section.
- **Update documentation** when new features are added, dependencies change, or setup steps are modified.
- **Comment non-obvious code** and ensure everything is understandable to a mid-level developer.
- **Add inline `// Reason:` comments** for complex logic explaining the why, not just the what.

## 📎 Style & Conventions - **Episteme Standards**

### Rust Standards
- **Follow Rust 2021 edition best practices**
- **Use `cargo fmt` and `cargo clippy`** for consistent formatting and linting
- **Type hints and documentation** - Use proper type annotations and doc comments
- **Error handling** - Use `Result<T, E>`, `anyhow` for application errors, `thiserror` for library errors
- **Async patterns** - Use `tokio` for async runtime, proper async/await patterns
- **Memory safety** - Avoid `unsafe` unless absolutely necessary and well-documented

### Project-Specific Patterns
- **Use `Axum` for web APIs** with proper middleware patterns
- **Use `Spanner` for persistence** with connection pooling
- **Use `Redis` for caching** with graceful degradation
- **Use `Tree-sitter` for parsing** with proper language registry management
- **Follow existing patterns** in `services/analysis-engine/src/` for consistency

### Documentation Standards
- **Write docstrings for every public function** using Rust doc comment style:
```rust
/// Brief summary of what this function does.
///
/// # Arguments
/// * `param1` - Description of parameter
///
/// # Returns
/// * `Result<T, E>` - Description of return value
///
/// # Errors
/// * Description of when this function returns an error
///
/// # Examples
/// ```
/// let result = example_function("input");
/// assert!(result.is_ok());
/// ```
pub fn example_function(param1: &str) -> Result<String, Error> {
    // Implementation
}
```

## 🧠 AI Behavior Rules - **Context Engineering Principles**

- **Never assume missing context. Ask questions if uncertain.**
- **Never hallucinate libraries or functions** – only use known, verified Rust crates and their documented APIs.
- **Always confirm file paths and module names** exist before referencing them in code or tests.
- **Never delete or overwrite existing code** unless explicitly instructed or part of a task from `TASK.md`.
- **Use the `/examples/` directory** to understand existing patterns before implementing new features.
- **Consult `/research/` directory** for third-party API documentation and implementation details.
- **Follow validation loops** - implement, test, fix, repeat until all validations pass.

## 🔒 Security & Production Standards

### Security Requirements
- **Zero tolerance for vulnerabilities** - Address all security issues immediately
- **Input validation** - Validate all user inputs and external data
- **Rate limiting** - Implement appropriate rate limiting for APIs
- **Authentication & Authorization** - Use proper JWT patterns and middleware
- **Audit logging** - Log all security-relevant operations
- **Resource limits** - Enforce file size, timeout, and memory limits

### Production Readiness
- **No `unwrap()` or `expect()` in production code** - Use proper error handling
- **Comprehensive error handling** - Handle all error cases gracefully
- **Performance optimization** - Use lazy static for regex, connection pooling, caching
- **Monitoring & Metrics** - Implement Prometheus metrics and health checks
- **Graceful degradation** - Handle external service failures appropriately

## 🚀 Deployment & Operations

### Docker Standards
- **All development through Docker** - Use Docker for consistent environments
- **Multi-stage builds** - Optimize Docker images for production
- **Health checks** - Implement proper health check endpoints
- **Environment configuration** - Use environment variables with proper defaults

### Cloud Run Deployment
- **Follow existing deployment patterns** in `scripts/` directory
- **Use Google Cloud services** - Spanner, Redis, Cloud Storage, Pub/Sub
- **Implement proper scaling** - Configure auto-scaling and resource limits
- **Monitor deployment** - Use Cloud Monitoring and logging

---

## 📋 Quick Reference

### Common Commands
```bash
# Development
cargo build --release
cargo test
cargo clippy
cargo fmt

# Docker
docker build -t episteme/analysis-engine .
docker run -p 8001:8001 episteme/analysis-engine

# Deployment
./scripts/ccl-deploy
./scripts/ccl-validate
```

### Key Directories
- `services/analysis-engine/` - Main Rust service
- `examples/` - Implementation patterns and examples
- `research/` - Third-party API documentation
- `PRPs/` - Product Requirements Prompts
- `.claudedocs/` - AI development tracking
- `docs/` - Project documentation

### Integration Points
- **Spanner Database** - Primary data storage
- **Redis Cache** - Performance optimization
- **Cloud Storage** - File storage and processing
- **Pub/Sub** - Event-driven architecture
- **Tree-sitter** - Multi-language parsing

