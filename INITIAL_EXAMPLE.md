# INITIAL_EXAMPLE.md - Repository Analysis API Feature

This is an example of a well-structured feature request that demonstrates how to use the Context Engineering workflow for the Episteme analysis engine.

## FEATURE:
Implement a comprehensive repository analysis API that accepts Git repository URLs, clones repositories, performs multi-language AST parsing using Tree-sitter, extracts code metrics and patterns, runs security analysis, and stores results in Spanner with Redis caching. The service should handle repositories up to 1M lines of code within 5 minutes, support 50+ concurrent analyses, and provide real-time progress updates via WebSocket.

### Specific Requirements:
- **Input**: Git repository URL, branch (optional), analysis configuration
- **Processing**: Clone repo, parse files with Tree-sitter, extract AST patterns, run security scans
- **Output**: Comprehensive analysis report with metrics, patterns, security findings
- **Performance**: <5 minutes for 1M LOC, 50+ concurrent analyses
- **Storage**: Results in Spanner, cache in Redis with 24-hour TTL
- **Real-time**: WebSocket progress updates during analysis
- **Security**: Input validation, resource limits, sandboxed execution

## EXAMPLES:
Reference these existing patterns for consistent implementation:

- **examples/analysis-engine/service_pattern.rs** - Follow this pattern for service structure and organization
- **examples/analysis-engine/api_handler.rs** - Use this for API endpoint implementation and request handling
- **examples/analysis-engine/ast_parser.rs** - Apply these Tree-sitter parsing patterns and unsafe block handling
- **examples/analysis-engine/error_handling.rs** - Use these error handling patterns and Result types
- **examples/analysis-engine/testing_pattern.rs** - Follow this testing approach for unit and integration tests
- **examples/database/spanner_queries.rs** - Use these patterns for database operations and connection pooling
- **examples/api/middleware.rs** - Apply authentication and rate limiting middleware patterns
- **examples/security/input_validation.rs** - Use these input validation and sanitization patterns

## DOCUMENTATION:
Consult these official documentation sources for implementation details:

### Research Directory References:
- **research/rust/tokio.md** - Async runtime patterns, task spawning, and concurrency management
- **research/rust/axum.md** - Web framework patterns, middleware, and WebSocket implementation
- **research/google-cloud/spanner.md** - Database integration, query patterns, and transaction management
- **research/google-cloud/storage.md** - File storage patterns for repository caching
- **research/tree-sitter/core.md** - AST parsing implementation, language registry, and unsafe patterns
- **research/tree-sitter/rust-bindings.md** - Rust-specific Tree-sitter integration patterns
- **research/security/input-validation.md** - Security patterns and validation strategies

### Official API Documentation:
- **https://docs.rs/tokio/latest/tokio/** - Async runtime and task management
- **https://docs.rs/axum/latest/axum/** - Web framework and WebSocket support
- **https://docs.rs/tree-sitter/latest/tree_sitter/** - AST parsing library
- **https://cloud.google.com/spanner/docs/reference/libraries** - Spanner client library
- **https://docs.rs/redis/latest/redis/** - Redis client integration

## OTHER CONSIDERATIONS:
Important gotchas and requirements that AI assistants commonly miss:

### Tree-sitter Integration:
- **Unsafe Block Handling**: Tree-sitter requires specific unsafe block patterns for FFI calls - see examples/analysis-engine/ast_parser.rs for proper implementation
- **Language Registry**: Must properly initialize and manage language parsers for 18+ supported languages
- **Memory Management**: Tree-sitter objects require careful lifetime management to prevent segfaults
- **Error Handling**: Parser failures should be graceful and not crash the service

### Performance & Resource Management:
- **Resource Limits**: Enforce 10MB max file size, 30s parse timeout per file, 2GB max memory per analysis
- **Concurrent Processing**: Use Tokio semaphores to limit concurrent analyses to 50
- **Progress Tracking**: Implement WebSocket progress updates for long-running analyses
- **Graceful Degradation**: Service should continue working if Redis cache is unavailable

### Database & Caching:
- **Connection Pooling**: Use existing Spanner connection pool patterns from storage/mod.rs
- **Transaction Management**: Wrap related database operations in transactions
- **Cache Strategy**: Implement Redis caching with 24-hour TTL and cache invalidation
- **Data Consistency**: Ensure analysis results are consistent between cache and database

### Security Requirements:
- **Input Validation**: All repository URLs must be validated and sanitized
- **Rate Limiting**: Implement per-user rate limiting for analysis requests
- **Authentication**: Use existing JWT middleware for API authentication
- **Sandboxed Execution**: Consider sandboxed git clone operations for security
- **Resource Exhaustion**: Prevent DoS attacks through resource limit enforcement

### API Design:
- **RESTful Endpoints**: Follow existing API patterns in src/api/
- **Error Responses**: Use consistent error response format across all endpoints
- **WebSocket Integration**: Implement progress updates without blocking HTTP endpoints
- **OpenAPI Documentation**: Update API documentation for new endpoints

### Monitoring & Observability:
- **Prometheus Metrics**: Add metrics for analysis duration, success/failure rates, resource usage
- **Structured Logging**: Use tracing crate for comprehensive logging with correlation IDs
- **Health Checks**: Update health check endpoints to include new service status
- **Error Tracking**: Implement proper error categorization and tracking

### Testing Requirements:
- **Unit Tests**: Comprehensive unit tests for all components with >90% coverage
- **Integration Tests**: End-to-end tests with real repositories and database
- **Performance Tests**: Validate 1M LOC processing within 5-minute requirement
- **Security Tests**: Test input validation, rate limiting, and resource limits
- **Chaos Testing**: Test graceful degradation when external services fail

### Deployment Considerations:
- **Docker Integration**: Ensure new service works with existing Docker setup
- **Cloud Run Compatibility**: Service must be compatible with Google Cloud Run deployment
- **Environment Configuration**: Use environment variables for all configuration
- **Graceful Shutdown**: Implement proper shutdown handling for in-progress analyses

This example demonstrates how to provide comprehensive context while being specific about requirements, referencing existing patterns, and highlighting important considerations that ensure successful implementation through the Context Engineering workflow.
