{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://ccl.dev/schemas/ast-output-v1.json", "title": "Repository Analysis AST Output", "description": "Complete AST output from Repository Analysis API for consumption by Query Intelligence and Pattern Detection services", "version": "1.0.0", "type": "object", "required": ["repository", "analysis", "metadata"], "properties": {"repository": {"type": "object", "description": "Repository information and context", "required": ["url", "commit", "branch", "id"], "properties": {"id": {"type": "string", "description": "Unique repository identifier", "pattern": "^repo_[a-zA-Z0-9]{16}$"}, "url": {"type": "string", "format": "uri", "description": "Repository URL (HTTPS or SSH)"}, "commit": {"type": "string", "pattern": "^[a-f0-9]{40}$", "description": "Full commit SHA"}, "branch": {"type": "string", "description": "Branch name analyzed"}, "size_bytes": {"type": "integer", "minimum": 0, "description": "Total repository size in bytes"}, "clone_time_ms": {"type": "integer", "minimum": 0, "description": "Time taken to clone repository"}}}, "analysis": {"type": "object", "description": "Complete analysis results", "required": ["files", "metrics", "languages", "embeddings"], "properties": {"files": {"type": "array", "description": "Per-file analysis results", "items": {"$ref": "#/definitions/FileAnalysis"}, "maxItems": 10000}, "metrics": {"$ref": "#/definitions/RepositoryMetrics"}, "languages": {"$ref": "#/definitions/LanguageBreakdown"}, "embeddings": {"type": "array", "description": "Semantic embeddings for code chunks", "items": {"$ref": "#/definitions/CodeEmbedding"}}, "patterns": {"type": "array", "description": "Pre-detected patterns (optional)", "items": {"$ref": "#/definitions/PreDetectedPattern"}}}}, "metadata": {"type": "object", "description": "Analysis metadata and performance info", "required": ["version", "timestamp", "duration_ms", "analysis_id"], "properties": {"analysis_id": {"type": "string", "description": "Unique analysis identifier", "pattern": "^analysis_[a-zA-Z0-9]{16}$"}, "version": {"type": "string", "description": "Repository Analysis API version", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "timestamp": {"type": "string", "format": "date-time", "description": "Analysis completion timestamp"}, "duration_ms": {"type": "integer", "minimum": 0, "description": "Total analysis duration in milliseconds"}, "performance": {"$ref": "#/definitions/PerformanceMetrics"}, "warnings": {"type": "array", "description": "Non-fatal warnings during analysis", "items": {"type": "object", "required": ["code", "message"], "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "file_path": {"type": "string"}}}}}}}, "definitions": {"FileAnalysis": {"type": "object", "description": "Analysis results for a single file", "required": ["path", "language", "ast", "metrics", "content_hash"], "properties": {"path": {"type": "string", "description": "Relative file path from repository root"}, "language": {"type": "string", "description": "Detected programming language"}, "content_hash": {"type": "string", "description": "SHA-256 hash of file content", "pattern": "^[a-f0-9]{64}$"}, "size_bytes": {"type": "integer", "minimum": 0, "description": "File size in bytes"}, "ast": {"$ref": "#/definitions/ASTNode"}, "metrics": {"$ref": "#/definitions/FileMetrics"}, "chunks": {"type": "array", "description": "Code chunks for embedding", "items": {"$ref": "#/definitions/CodeChunk"}}, "symbols": {"type": "array", "description": "Extracted symbols (functions, classes, etc.)", "items": {"$ref": "#/definitions/Symbol"}}}}, "ASTNode": {"type": "object", "description": "Abstract Syntax Tree node", "required": ["type", "range"], "properties": {"type": {"type": "string", "description": "AST node type (language-specific)"}, "name": {"type": "string", "description": "Node name/identifier (if applicable)"}, "range": {"type": "object", "description": "Source code position range", "required": ["start", "end"], "properties": {"start": {"$ref": "#/definitions/Position"}, "end": {"$ref": "#/definitions/Position"}}}, "children": {"type": "array", "description": "Child AST nodes", "items": {"$ref": "#/definitions/ASTNode"}}, "properties": {"type": "object", "description": "Language-specific node properties", "additionalProperties": true}, "text": {"type": "string", "description": "Source text for this node"}}}, "Position": {"type": "object", "description": "Source code position", "required": ["line", "column", "byte"], "properties": {"line": {"type": "integer", "minimum": 0, "description": "Line number (0-based)"}, "column": {"type": "integer", "minimum": 0, "description": "Column number (0-based)"}, "byte": {"type": "integer", "minimum": 0, "description": "Byte offset from file start"}}}, "FileMetrics": {"type": "object", "description": "Code metrics for a single file", "required": ["lines_of_code", "complexity", "maintainability_index"], "properties": {"lines_of_code": {"type": "integer", "minimum": 0, "description": "Total lines of code (excluding comments/blanks)"}, "total_lines": {"type": "integer", "minimum": 0, "description": "Total lines including comments and blanks"}, "complexity": {"type": "integer", "minimum": 0, "description": "Cyclomatic complexity"}, "maintainability_index": {"type": "number", "minimum": 0, "maximum": 100, "description": "Maintainability index (0-100)"}, "function_count": {"type": "integer", "minimum": 0, "description": "Number of functions/methods"}, "class_count": {"type": "integer", "minimum": 0, "description": "Number of classes/types"}, "comment_ratio": {"type": "number", "minimum": 0, "maximum": 1, "description": "Ratio of comment lines to code lines"}}}, "RepositoryMetrics": {"type": "object", "description": "Aggregate repository metrics", "required": ["total_files", "total_lines", "total_complexity"], "properties": {"total_files": {"type": "integer", "minimum": 0, "description": "Total number of analyzed files"}, "total_lines": {"type": "integer", "minimum": 0, "description": "Total lines of code across all files"}, "total_complexity": {"type": "integer", "minimum": 0, "description": "Sum of complexity across all files"}, "average_complexity": {"type": "number", "minimum": 0, "description": "Average complexity per file"}, "maintainability_score": {"type": "number", "minimum": 0, "maximum": 100, "description": "Overall maintainability score"}, "technical_debt_minutes": {"type": "integer", "minimum": 0, "description": "Estimated technical debt in minutes"}, "test_coverage_estimate": {"type": "number", "minimum": 0, "maximum": 1, "description": "Estimated test coverage ratio"}}}, "LanguageBreakdown": {"type": "object", "description": "Programming language distribution", "required": ["primary_language", "languages"], "properties": {"primary_language": {"type": "string", "description": "Most prevalent language"}, "languages": {"type": "object", "description": "Language statistics", "patternProperties": {"^[a-zA-Z][a-zA-Z0-9+#-]*$": {"type": "object", "required": ["lines", "files", "percentage"], "properties": {"lines": {"type": "integer", "minimum": 0}, "files": {"type": "integer", "minimum": 0}, "percentage": {"type": "number", "minimum": 0, "maximum": 100}, "bytes": {"type": "integer", "minimum": 0}}}}}}}, "CodeChunk": {"type": "object", "description": "Code chunk for embedding and retrieval", "required": ["chunk_id", "content", "range", "type"], "properties": {"chunk_id": {"type": "string", "description": "Unique chunk identifier", "pattern": "^chunk_[a-zA-Z0-9]{16}$"}, "content": {"type": "string", "description": "Code content", "maxLength": 8192}, "range": {"type": "object", "required": ["start", "end"], "properties": {"start": {"$ref": "#/definitions/Position"}, "end": {"$ref": "#/definitions/Position"}}}, "type": {"type": "string", "enum": ["function", "class", "method", "block", "comment", "import"], "description": "Type of code chunk"}, "language": {"type": "string", "description": "Programming language"}, "context": {"type": "object", "description": "Additional context for the chunk", "properties": {"parent_symbol": {"type": "string"}, "imports": {"type": "array", "items": {"type": "string"}}, "dependencies": {"type": "array", "items": {"type": "string"}}}}}}, "CodeEmbedding": {"type": "object", "description": "Semantic embedding for code chunk", "required": ["chunk_id", "vector", "model"], "properties": {"chunk_id": {"type": "string", "description": "Reference to code chunk", "pattern": "^chunk_[a-zA-Z0-9]{16}$"}, "vector": {"type": "array", "description": "Embedding vector", "items": {"type": "number"}, "minItems": 768, "maxItems": 1536}, "model": {"type": "string", "description": "Embedding model used (gemini-embedding-001 is preferred for new implementations)", "enum": ["gemini-embedding-001", "text-embedding-ada-002", "text-embedding-3-small", "text-embedding-3-large"]}, "metadata": {"type": "object", "description": "Embedding metadata", "properties": {"tokens_used": {"type": "integer", "minimum": 0}, "created_at": {"type": "string", "format": "date-time"}}}}}, "Symbol": {"type": "object", "description": "Extracted code symbol", "required": ["name", "type", "range"], "properties": {"name": {"type": "string", "description": "Symbol name"}, "type": {"type": "string", "enum": ["function", "method", "class", "interface", "variable", "constant", "type", "namespace"], "description": "Symbol type"}, "range": {"type": "object", "required": ["start", "end"], "properties": {"start": {"$ref": "#/definitions/Position"}, "end": {"$ref": "#/definitions/Position"}}}, "visibility": {"type": "string", "enum": ["public", "private", "protected", "internal"], "description": "Symbol visibility"}, "signature": {"type": "string", "description": "Function/method signature"}, "documentation": {"type": "string", "description": "Associated documentation/comments"}}}, "PreDetectedPattern": {"type": "object", "description": "Pattern detected during AST analysis", "required": ["pattern_id", "pattern_type", "confidence", "location"], "properties": {"pattern_id": {"type": "string", "description": "Pattern identifier"}, "pattern_type": {"type": "string", "enum": ["design_pattern", "anti_pattern", "security_issue", "performance_issue", "code_smell"], "description": "Type of pattern detected"}, "confidence": {"type": "number", "minimum": 0, "maximum": 1, "description": "Detection confidence score"}, "location": {"type": "object", "required": ["file_path", "range"], "properties": {"file_path": {"type": "string"}, "range": {"type": "object", "required": ["start", "end"], "properties": {"start": {"$ref": "#/definitions/Position"}, "end": {"$ref": "#/definitions/Position"}}}}}, "description": {"type": "string", "description": "Pattern description"}}}, "PerformanceMetrics": {"type": "object", "description": "Analysis performance metrics", "required": ["parsing_time_ms", "embedding_time_ms", "total_memory_mb"], "properties": {"parsing_time_ms": {"type": "integer", "minimum": 0, "description": "Time spent parsing ASTs"}, "embedding_time_ms": {"type": "integer", "minimum": 0, "description": "Time spent generating embeddings"}, "total_memory_mb": {"type": "number", "minimum": 0, "description": "Peak memory usage in MB"}, "files_per_second": {"type": "number", "minimum": 0, "description": "Average files processed per second"}, "cache_hit_ratio": {"type": "number", "minimum": 0, "maximum": 1, "description": "Cache hit ratio during analysis"}}}}}