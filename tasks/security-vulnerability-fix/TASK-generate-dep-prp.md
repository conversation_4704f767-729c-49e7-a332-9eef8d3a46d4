# FEATURE: Generate PRP for Critical Dependency Vulnerability Resolution

## GOAL:
Generate a comprehensive Product Requirements Prompt (PRP) to guide the resolution of critical security vulnerabilities in the `analysis-engine` service. The PRP must be based on the evidence and research already gathered.

## CONTEXT:
The `analysis-engine` is blocked from production deployment due to two critical vulnerabilities identified by `cargo audit`. This task is on the critical path. The PRP will orchestrate the upgrade of `idna` and `protobuf` dependencies.

### Key Context Documents:
- **Primary Mandate**: `ANALYSIS_ENGINE_PRODUCTION_READINESS.md`
- **Vulnerability Evidence**: `validation-results/analysis-engine-prod-readiness/evidence/cargo-audit.txt`
- **Research**: `research/security/vulnerability-management.md` and `research/rust/security-best-practices.md`

## PRP REQUIREMENTS:

### 1. Goal Definition:
- Clearly state the goal: Remediate critical `idna` and `protobuf` vulnerabilities in `analysis-engine`.

### 2. Context Section:
- Reference the blocking nature of these vulnerabilities for production deployment.
- Link to the `cargo-audit.txt` evidence file.
- Mention that research is complete and secure upgrade paths are documented in the `/research` directory.

### 3. Implementation Requirements:
- **`idna` Upgrade**:
    - Specify the change from version `0.4.0` to `>=1.0.0`.
    - State that the action is to modify `services/analysis-engine/Cargo.toml`.
- **`protobuf` Upgrade**:
    - Specify the change from version `2.28.0` to `>=3.7.2`.
    - State that the action is to modify `services/analysis-engine/Cargo.toml`.

### 4. Validation Loops (Crucial):
- Define a strict, multi-step validation process:
    1.  `cargo clean` - To ensure a fresh build.
    2.  `cargo build --workspace` - To confirm the project compiles without errors after the upgrade.
    3.  `cargo test --workspace` - To ensure all existing tests pass, verifying no breaking changes were introduced.
    4.  `cargo audit -f validation-results/analysis-engine-prod-readiness/security/evidence/cargo-audit-after-dep-upgrade.txt` - To run the audit and save the new report to the specified evidence location, confirming the vulnerabilities are resolved.

### 5. Success Criteria:
- The PRP must define clear, measurable success criteria:
    - `idna` and `protobuf` versions are updated in `Cargo.toml`.
    - The build succeeds and all tests pass.
    - The final `cargo audit` report is clean and shows zero vulnerabilities for these packages.

### 6. Evidence Collection:
- The PRP must mandate that the final, clean `cargo audit` report is saved to the correct path: `validation-results/analysis-engine-prod-readiness/security/evidence/cargo-audit-after-dep-upgrade.txt`.
- It should also require the creation of a brief summary report at `validation-results/analysis-engine-prod-readiness/security/reports/dependency-upgrade-report.md`.

## FINAL PRP OUTPUT:
- The generated PRP file should be named `PRPs/active/resolve-critical-dependency-vulnerabilities.md`.
- It must be comprehensive, actionable, and follow the structure defined in `.claude/commands/generate-prp.md`.
