# Validate Checkpoint Command - Systematic Validation Gate Management

This command manages validation checkpoints following Context Engineering principles for systematic quality assurance.

## Command Usage
```
/validate-checkpoint [gate-type] --evidence-required [--service=all|analysis-engine|query-intelligence]
```

**Gate Types**: security, performance, integration, deployment, quality

## Process Overview

Implement systematic validation gates with evidence-based validation following Context Engineering standards.

### Validation Gate Framework

#### **Security Validation Gate**
```
/validate-checkpoint security --evidence-required
```

**Validation Criteria:**
```yaml
Security Compliance Requirements:
  Vulnerability Resolution:
    - idna dependency upgraded to >=1.0.0
    - protobuf dependency upgraded to >=3.7.2
    - cargo audit reports zero vulnerabilities
    - All security advisories addressed

  Code Quality:
    - No unwrap()/expect() in production code paths
    - All clippy errors resolved
    - Code formatting consistent (cargo fmt)
    - Build system clean (build.rs issues resolved)

  Memory Safety:
    - All unsafe blocks documented with SAFETY comments
    - Memory safety patterns validated
    - No potential segfaults in Tree-sitter integration
    - Resource cleanup properly implemented

  Security Testing:
    - Input validation tests implemented
    - Authentication/authorization tests passing
    - Rate limiting validation functional
    - Security audit tests comprehensive
```

**Evidence Requirements:**
- cargo audit output showing zero vulnerabilities
- clippy output with no errors or warnings
- Documentation of all unsafe blocks
- Security test suite results

#### **Performance Validation Gate**
```
/validate-checkpoint performance --evidence-required
```

**Validation Criteria:**
```yaml
Performance Requirements:
  Analysis Engine:
    - 1M LOC repository processing in <5 minutes
    - 50+ concurrent analyses supported
    - Memory usage within 4GB limits
    - Resource limits enforced (10MB files, 30s timeouts)

  Query Intelligence:
    - Query response time <200ms
    - 100+ concurrent queries supported
    - NLP processing optimized
    - Caching strategy effective

  System Performance:
    - Database connection pooling optimized
    - Redis caching performance validated
    - API response times within SLA
    - Resource utilization efficient
```

**Evidence Requirements:**
- Benchmark test results with timing data
- Load testing reports with concurrent request handling
- Resource usage monitoring data
- Performance optimization documentation

#### **Integration Validation Gate**
```
/validate-checkpoint integration --evidence-required
```

**Validation Criteria:**
```yaml
Integration Requirements:
  API Contracts:
    - Schema compatibility validated
    - Version compatibility ensured
    - Error response formats consistent
    - Authentication flow functional

  Data Flow:
    - End-to-end pipeline tested
    - Data transformation validated
    - Error propagation handled gracefully
    - Performance optimized

  Service Communication:
    - HTTP communication robust
    - Timeout handling appropriate
    - Retry mechanisms implemented
    - Circuit breakers functional
```

**Evidence Requirements:**
- API contract validation results
- End-to-end test suite results
- Integration performance benchmarks
- Error handling test results

#### **Deployment Validation Gate**
```
/validate-checkpoint deployment --evidence-required
```

**Validation Criteria:**
```yaml
Deployment Requirements:
  Infrastructure:
    - Cloud Run configuration validated
    - Auto-scaling parameters set
    - Health checks operational
    - Monitoring infrastructure ready

  Deployment Process:
    - Zero-downtime deployment tested
    - Rollback procedures validated
    - Environment parity confirmed
    - CI/CD pipeline functional

  Production Readiness:
    - Environment variables configured
    - Secrets management secure
    - Logging and monitoring operational
    - Disaster recovery procedures ready
```

**Evidence Requirements:**
- Deployment test results
- Health check validation
- Monitoring dashboard screenshots
- Rollback procedure documentation

#### **Quality Validation Gate**
```
/validate-checkpoint quality --evidence-required
```

**Validation Criteria:**
```yaml
Quality Requirements:
  Test Coverage:
    - Unit test coverage >90%
    - Integration test coverage comprehensive
    - End-to-end test scenarios complete
    - Performance test suite functional

  Code Quality:
    - Code review standards met
    - Documentation complete and current
    - Error handling comprehensive
    - Logging and monitoring adequate

  Validation Loops:
    - All validation scripts functional
    - Self-correction mechanisms operational
    - Quality gates enforced
    - Continuous validation implemented
```

**Evidence Requirements:**
- Test coverage reports
- Code quality metrics
- Documentation completeness audit
- Validation framework test results

### Validation Process

1. **Evidence Collection**
   ```bash
   # Collect evidence for validation gate
   ./validation-results/[service]/scripts/collect-evidence.sh [gate-type]
   ```

2. **Criteria Verification**
   ```bash
   # Verify all criteria met
   ./validation-results/[service]/scripts/verify-criteria.sh [gate-type]
   ```

3. **Evidence Documentation**
   ```bash
   # Document evidence and results
   ./validation-results/[service]/scripts/document-validation.sh [gate-type]
   ```

4. **Gate Approval**
   ```bash
   # Approve gate passage or identify blockers
   ./validation-results/[service]/scripts/approve-gate.sh [gate-type]
   ```

### Validation Evidence Structure

```
validation-results/
├── analysis-engine-prod-readiness/
│   ├── security/
│   │   ├── evidence/
│   │   ├── reports/
│   │   └── validation-status.md
│   ├── performance/
│   ├── integration/
│   ├── deployment/
│   └── quality/
└── query-intelligence-prod-readiness/
    ├── security/
    ├── performance/
    ├── integration/
    ├── deployment/
    └── quality/
```

### Gate Status Tracking

Each validation gate maintains status:

```yaml
Gate Status:
  name: "Security Validation Gate"
  status: "PASSED" | "FAILED" | "IN_PROGRESS" | "BLOCKED"
  timestamp: "2025-07-15T10:30:00Z"
  evidence_collected: true
  criteria_met: 
    - vulnerability_resolution: true
    - code_quality: true
    - memory_safety: true
    - security_testing: true
  blockers: []
  next_actions: []
```

### Best Practices

1. **Evidence-Based**: All validations backed by concrete evidence
2. **Systematic**: Follow consistent validation procedures
3. **Comprehensive**: Cover all aspects of production readiness
4. **Traceable**: Maintain audit trail of all validations
5. **Actionable**: Provide clear next steps for any failures

This command ensures systematic, evidence-based validation following Context Engineering principles for production readiness assurance.
