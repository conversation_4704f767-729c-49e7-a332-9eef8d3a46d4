# Deploy Research Agent Command - Specialized Documentation Research

This command deploys specialized research agents following Context Engineering multi-agent research patterns.

## Command Usage
```
/deploy-research-agent [technology] --focus=[focus-areas] --pages=[target-count] --official-only
```

**Technologies**: rust, python, gcp, security, performance, integration

## Process Overview

Deploy specialized research agents to gather comprehensive, official documentation using Context7 and systematic scraping approaches.

### Research Agent Deployment Protocol

#### **Rust Research Agent**
```
/deploy-research-agent rust --focus=security-performance-production --pages=50+ --official-only
```

**Agent Context:**
```markdown
# Rust Research Agent - Official Documentation Gathering

## Your Mission
Gather comprehensive Rust ecosystem documentation focusing on security, performance, and production deployment patterns for the Episteme analysis engine.

## Research Targets (50+ pages minimum)
### Security Documentation:
- https://doc.rust-lang.org/nomicon/safe-unsafe-meaning.html - Unsafe Rust patterns
- https://rustsec.org/ - Security advisory database
- https://doc.rust-lang.org/cargo/commands/cargo-audit.html - Dependency auditing
- https://cheats.rs/#security - Security best practices
- https://docs.rs/security-framework/latest/security_framework/ - Security frameworks

### Performance Documentation:
- https://doc.rust-lang.org/book/ch12-06-writing-to-stderr-instead-of-stdout.html - Performance patterns
- https://docs.rs/tokio/latest/tokio/runtime/ - Async runtime optimization
- https://docs.rs/regex/latest/regex/ - Regex performance and lazy_static patterns
- https://doc.rust-lang.org/std/collections/ - Collection performance characteristics

### Production Deployment:
- https://docs.rs/axum/latest/axum/ - Web framework production patterns
- https://cloud.google.com/run/docs/quickstarts/build-and-deploy/deploy-rust-service - Rust on Cloud Run
- https://docs.rs/tracing/latest/tracing/ - Production logging and monitoring

## Research Protocol
1. **Scrape Systematically**: Use Context7 to scrape each URL comprehensively
2. **Quality Validation**: Verify content completeness, re-scrape if minimal content
3. **Organize by Topic**: Store in research/rust/ with descriptive filenames
4. **Include Metadata**: Add source URL, version, and scraping date to each file

## Success Criteria
- [ ] 50+ pages of official Rust documentation scraped
- [ ] Security, performance, and production topics covered comprehensively
- [ ] All content stored in research/rust/ with proper organization
- [ ] Quality validation completed for all scraped content
```

#### **Python/NLP Research Agent**
```
/deploy-research-agent python --focus=fastapi-ml-nlp-production --pages=50+ --official-only
```

**Agent Context:**
```markdown
# Python/NLP Research Agent - Official Documentation Gathering

## Your Mission
Gather comprehensive Python and NLP documentation for query-intelligence service production readiness.

## Research Targets (50+ pages minimum)
### FastAPI Production:
- https://fastapi.tiangolo.com/deployment/ - Production deployment patterns
- https://fastapi.tiangolo.com/advanced/security/ - Security and authentication
- https://fastapi.tiangolo.com/advanced/middleware/ - Middleware patterns
- https://fastapi.tiangolo.com/tutorial/testing/ - Testing strategies

### ML/NLP Frameworks:
- https://huggingface.co/docs/transformers/main_classes/pipelines - NLP pipelines
- https://scikit-learn.org/stable/modules/model_persistence.html - Model deployment
- https://docs.python.org/3/library/asyncio.html - Async patterns for ML

### Vector Search and LLM Integration:
- https://cloud.google.com/vertex-ai/docs - LLM integration patterns
- https://python.langchain.com/docs/get_started/introduction - LLM frameworks

## Success Criteria
- [ ] 50+ pages of Python/NLP documentation scraped
- [ ] FastAPI, ML, and NLP production patterns covered
- [ ] Content organized in research/python/ directory
```

#### **Google Cloud Research Agent**
```
/deploy-research-agent gcp --focus=cloud-run-spanner-monitoring --pages=50+ --official-only
```

**Agent Context:**
```markdown
# Google Cloud Research Agent - Official Documentation Gathering

## Your Mission
Gather comprehensive Google Cloud documentation for production deployment and infrastructure.

## Research Targets (50+ pages minimum)
### Cloud Run Production:
- https://cloud.google.com/run/docs/deploying - Production deployment
- https://cloud.google.com/run/docs/configuring/services - Service configuration
- https://cloud.google.com/run/docs/monitoring - Monitoring and logging
- https://cloud.google.com/run/docs/troubleshooting - Troubleshooting patterns

### Spanner and Database:
- https://cloud.google.com/spanner/docs/best-practices - Performance best practices
- https://cloud.google.com/spanner/docs/connection-pooling - Connection management
- https://cloud.google.com/spanner/docs/monitoring - Database monitoring

### Monitoring and Operations:
- https://cloud.google.com/monitoring/docs - Cloud Monitoring
- https://cloud.google.com/logging/docs - Cloud Logging
- https://cloud.google.com/error-reporting/docs - Error reporting

## Success Criteria
- [ ] 50+ pages of GCP documentation scraped
- [ ] Cloud Run, Spanner, and monitoring covered comprehensively
- [ ] Content organized in research/google-cloud/ directory
```

### Research Quality Standards

Each research agent must follow these standards:

1. **Official Sources Only**
   - Use only official documentation sites
   - No blogs, tutorials, or third-party content
   - Verify authenticity of all sources

2. **Comprehensive Scraping**
   - Target 30-100+ pages per technology area
   - Include code examples and best practices
   - Capture complete documentation sections

3. **Quality Validation**
   - Verify scraped content is complete and readable
   - Re-scrape if page 404s or has minimal content
   - Ensure proper formatting and structure

4. **Organized Storage**
   ```
   research/[technology]/
   ├── [topic]-[subtopic].md
   ├── metadata.yml
   └── scraping-log.md
   ```

5. **Metadata Inclusion**
   ```yaml
   source_url: "https://official-docs-url.com"
   version: "1.2.3"
   scraped_date: "2025-07-15"
   content_type: "official_documentation"
   quality_validated: true
   ```

### Research Execution Commands

```bash
# Deploy all research agents simultaneously
/deploy-research-agent rust --focus=security-performance-production --pages=50+ --official-only &
/deploy-research-agent python --focus=fastapi-ml-nlp-production --pages=50+ --official-only &
/deploy-research-agent gcp --focus=cloud-run-spanner-monitoring --pages=50+ --official-only &
/deploy-research-agent security --focus=vulnerability-secure-deployment --pages=30+ --official-only &
/deploy-research-agent performance --focus=benchmarking-optimization --pages=30+ --official-only &
/deploy-research-agent integration --focus=microservices-api-monitoring --pages=30+ --official-only &

# Wait for all agents to complete
wait

# Validate research completeness
/validate-research-completeness --min-pages=200 --quality-check
```

This command ensures systematic, comprehensive research gathering following Context Engineering principles before PRP generation.
