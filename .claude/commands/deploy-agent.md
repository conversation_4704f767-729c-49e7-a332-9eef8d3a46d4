# Deploy Agent Command - Specialized Agent Deployment with Complete Context

This command deploys specialized agents with complete project context following Context Engineering multi-agent patterns.

## Command Usage
```
/deploy-agent [agent-type] --context=complete-project --focus=[focus-area] --deliverable=[expected-output]
```

**Agent Types**: security, performance, integration, deployment, quality, research

## Process Overview

Deploy specialized agents with comprehensive project context, ensuring each agent has complete understanding while focusing on specific deliverables.

### Agent Deployment Protocol

#### **Security Agent Deployment**
```
/deploy-agent security --context=complete-project --focus=vulnerability-resolution --deliverable=security-compliance-report
```

**Complete Agent Context:**
```markdown
# Security Agent - Complete Project Context

## Project Overview
You are working on the Episteme project, a comprehensive code analysis platform with two main services:
- **Analysis Engine** (Rust): AST parsing, code analysis, Tree-sitter integration
- **Query Intelligence** (Python): NLP processing, semantic search, LLM integration

## Your Mission
Resolve critical security vulnerabilities and achieve security compliance for production deployment.

## Current Security Status
Based on validation-results/analysis-engine-prod-readiness/ANALYSIS_ENGINE_PRODUCTION_STATUS.md:
- **Critical Vulnerabilities**: idna 0.4.0 (needs >=1.0.0), protobuf 2.28.0 (needs >=3.7.2)
- **Code Quality Issues**: Clippy errors in build.rs, formatting issues
- **Memory Safety**: 22 undocumented unsafe blocks need SAFETY comments

## Repository Structure
- **Root**: /Users/<USER>/Documents/GitHub/episteme
- **Analysis Engine**: services/analysis-engine/ (Rust codebase)
- **Query Intelligence**: services/query-intelligence/ (Python codebase)
- **Documentation**: docs/, PRPs/, examples/, research/
- **Validation Results**: validation-results/analysis-engine-prod-readiness/

## Your Specific Tasks
1. **Dependency Upgrades**: Upgrade idna and protobuf to secure versions
2. **Code Quality**: Fix clippy errors and formatting issues
3. **Memory Safety**: Document all unsafe blocks with SAFETY comments
4. **Security Validation**: Implement comprehensive security testing

## Success Criteria
- [ ] cargo audit reports no vulnerabilities
- [ ] All clippy errors resolved
- [ ] All unsafe blocks documented
- [ ] Security tests implemented and passing

## Context Engineering Standards
- Use official documentation from research/ directory
- Follow examples/ patterns for implementation
- Implement validation loops for self-correction
- Provide evidence-based recommendations

## Deliverable
Create comprehensive security compliance report with evidence of all fixes and validations.
```

#### **Performance Agent Deployment**
```
/deploy-agent performance --context=complete-project --focus=benchmarking-optimization --deliverable=performance-validation-report
```

**Complete Agent Context:**
```markdown
# Performance Agent - Complete Project Context

## Project Overview
Episteme analysis platform requiring high-performance code analysis and query processing.

## Your Mission
Validate and optimize performance to meet production requirements:
- **Analysis Engine**: Process 1M LOC repositories in <5 minutes
- **Query Intelligence**: Handle 100+ concurrent queries with <200ms response time

## Current Performance Status
- Validation framework exists in validation-results/analysis-engine-prod-readiness/
- Benchmark scripts ready: scripts/benchmark-validation.sh
- Performance requirements defined but not yet validated

## Your Specific Tasks
1. **Execute Benchmarks**: Run 1M LOC processing tests
2. **Concurrent Load Testing**: Test 100+ concurrent requests
3. **Resource Validation**: Verify 10MB file limits, 30s timeouts
4. **Optimization**: Identify and resolve performance bottlenecks

## Success Criteria
- [ ] 1M LOC benchmark <5 minutes
- [ ] 100+ concurrent requests handled
- [ ] Resource limits enforced
- [ ] Performance optimizations implemented

## Deliverable
Performance validation report with benchmark results and optimization recommendations.
```

#### **Integration Agent Deployment**
```
/deploy-agent integration --context=complete-project --focus=cross-service-validation --deliverable=integration-test-report
```

**Complete Agent Context:**
```markdown
# Integration Agent - Complete Project Context

## Project Overview
Validate integration between analysis-engine (Rust) and query-intelligence (Python) services.

## Your Mission
Ensure robust, secure, and performant integration between services.

## Integration Architecture
- **Data Flow**: Repository → Analysis Engine → AST Output → Query Intelligence → Enhanced Queries
- **Communication**: RESTful APIs with JSON payloads
- **Authentication**: JWT tokens and service-to-service auth

## Your Specific Tasks
1. **API Contract Validation**: Ensure schema compatibility
2. **End-to-End Testing**: Test complete data flow pipeline
3. **Error Handling**: Validate graceful failure modes
4. **Performance**: Optimize cross-service communication

## Success Criteria
- [ ] API contracts validated
- [ ] End-to-end tests passing
- [ ] Error handling robust
- [ ] Integration performance optimized

## Deliverable
Integration test report with validation evidence and optimization recommendations.
```

### Agent Context Template

Each agent receives this standardized context structure:

```markdown
# [Agent Type] Agent - Complete Project Context

## Project Overview
[Complete project description and architecture]

## Your Mission
[Specific agent objectives and focus area]

## Current Status
[Relevant current state and validation results]

## Repository Structure
[Key directories and files relevant to agent]

## Your Specific Tasks
[Detailed task breakdown with clear deliverables]

## Success Criteria
[Measurable success criteria with validation requirements]

## Context Engineering Standards
[Standards for evidence-based development and validation]

## Coordination Protocol
[How to coordinate with other agents and report progress]

## Deliverable
[Specific output expected from agent]
```

### Agent Coordination Protocols

1. **Evidence Collection**
   - Save all evidence to validation-results/[agent-type]/
   - Use standardized evidence formats
   - Include validation commands and outputs

2. **Progress Reporting**
   - Update shared progress tracking
   - Report completion of major milestones
   - Escalate blockers to master coordinator

3. **Context Sharing**
   - Maintain complete project understanding
   - Share relevant findings with other agents
   - Coordinate interdependent validations

4. **Quality Assurance**
   - Follow validation loops for self-correction
   - Use evidence-based decision making
   - Implement comprehensive testing

## Best Practices

1. **Complete Context**: Each agent receives full project understanding
2. **No Prior Knowledge**: Assume agent has no previous project knowledge
3. **Specific Focus**: Clear focus area with measurable deliverables
4. **Evidence-Based**: All recommendations backed by testing
5. **Coordination**: Regular synchronization with other agents

This command ensures systematic, coordinated agent deployment following Context Engineering principles for maximum effectiveness.
