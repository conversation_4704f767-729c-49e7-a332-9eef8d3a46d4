# Coordinate Production Command - Multi-Agent Production Readiness Orchestration

This command orchestrates the complete production readiness workflow using Context Engineering multi-agent coordination patterns.

## Command Usage
```
/coordinate-production [--phase=all|security|performance|integration|deployment|quality] [--service=all|analysis-engine|query-intelligence]
```

## Process Overview

You will follow the Context Engineering multi-agent coordination approach to systematically achieve 100% production readiness across both services.

### Phase 1: Master Coordination Setup

1. **Load Master Coordination Context**
   - Read PRODUCTION_COORDINATION_MASTER.md for complete orchestration plan
   - Review current validation results from validation-results/ directory
   - Understand project structure, constraints, and requirements
   - Establish coordination framework and validation gates

2. **Initialize Shared Context Package**
   - Create comprehensive project context for all agents
   - Include project structure, architecture, constraints, and objectives
   - Prepare service-specific context for analysis-engine and query-intelligence
   - Set up shared evidence collection and progress tracking systems

### Phase 2: Specialized Agent Deployment

Deploy 6 specialized agents following Context Engineering patterns:

#### **Security Agent Deployment**
```bash
/deploy-agent security --context=complete-project --focus=vulnerability-resolution
```
**Agent Context Package:**
- **Project Understanding**: Complete Episteme architecture, services, and constraints
- **Current Status**: validation-results/analysis-engine-prod-readiness/ANALYSIS_ENGINE_PRODUCTION_STATUS.md
- **Specific Focus**: Resolve idna and protobuf vulnerabilities, document unsafe blocks
- **Deliverables**: Security compliance report, vulnerability resolution evidence
- **Validation Criteria**: Clean cargo audit, documented unsafe blocks, security tests passing

#### **Performance Agent Deployment**
```bash
/deploy-agent performance --context=complete-project --focus=benchmarking-optimization
```
**Agent Context Package:**
- **Project Understanding**: Performance requirements (1M LOC <5min, 100+ concurrent)
- **Current Status**: Validation framework in place, benchmarks ready to execute
- **Specific Focus**: Execute performance benchmarks, optimize bottlenecks
- **Deliverables**: Performance validation report, optimization recommendations
- **Validation Criteria**: All performance benchmarks passing, resource limits enforced

#### **Integration Agent Deployment**
```bash
/deploy-agent integration --context=complete-project --focus=cross-service-validation
```
**Agent Context Package:**
- **Project Understanding**: Analysis-engine and query-intelligence integration architecture
- **Current Status**: Services exist but integration needs validation
- **Specific Focus**: Validate API contracts, test data flow, ensure robust error handling
- **Deliverables**: Integration test report, API contract validation
- **Validation Criteria**: End-to-end tests passing, error handling robust

#### **Deployment Agent Deployment**
```bash
/deploy-agent deployment --context=complete-project --focus=production-orchestration
```
**Agent Context Package:**
- **Project Understanding**: Cloud Run deployment, monitoring, zero-downtime requirements
- **Current Status**: Analysis-engine deployed, query-intelligence needs deployment
- **Specific Focus**: Orchestrate production deployment with validation gates
- **Deliverables**: Deployment readiness report, rollback procedures
- **Validation Criteria**: Zero-downtime deployment tested, monitoring operational

#### **Quality Agent Deployment**
```bash
/deploy-agent quality --context=complete-project --focus=testing-validation
```
**Agent Context Package:**
- **Project Understanding**: Quality standards, testing requirements, validation frameworks
- **Current Status**: Test coverage exists but needs comprehensive validation
- **Specific Focus**: Implement comprehensive testing and validation frameworks
- **Deliverables**: Quality assurance report, test coverage analysis
- **Validation Criteria**: >90% test coverage, all validation loops operational

#### **Research Agent Deployment**
```bash
/deploy-agent research --context=complete-project --focus=best-practices-updates
```
**Agent Context Package:**
- **Project Understanding**: Technology stack (Rust, Python, GCP, ML/NLP)
- **Current Status**: Research directory exists but may need updates
- **Specific Focus**: Gather latest best practices, update research documentation
- **Deliverables**: Technology recommendations report, updated research docs
- **Validation Criteria**: Research directory current, best practices implemented

### Phase 3: Validation Gate Management

Implement systematic validation gates following Context Engineering principles:

#### **Gate 1: Security Validation**
```bash
/validate-checkpoint security --evidence-required
```
**Validation Criteria:**
- All security vulnerabilities resolved (idna, protobuf upgraded)
- All unsafe blocks documented with SAFETY comments
- Security audit clean (cargo audit passes)
- Security tests implemented and passing

#### **Gate 2: Performance Validation**
```bash
/validate-checkpoint performance --evidence-required
```
**Validation Criteria:**
- 1M LOC benchmark completes in <5 minutes
- 100+ concurrent requests handled successfully
- Resource limits enforced (10MB files, 30s timeouts)
- Memory usage optimized and within limits

#### **Gate 3: Integration Validation**
```bash
/validate-checkpoint integration --evidence-required
```
**Validation Criteria:**
- Cross-service API contracts validated
- End-to-end data flow tested successfully
- Error handling robust across service boundaries
- Monitoring and tracing operational

#### **Gate 4: Deployment Validation**
```bash
/validate-checkpoint deployment --evidence-required
```
**Validation Criteria:**
- Zero-downtime deployment procedures tested
- Rollback procedures validated
- Health checks operational
- Production environment ready

#### **Gate 5: Quality Validation**
```bash
/validate-checkpoint quality --evidence-required
```
**Validation Criteria:**
- Test coverage >90% across both services
- All validation loops operational
- Documentation complete and current
- Production readiness certified

### Phase 4: Agent Coordination and Synchronization

1. **Progress Synchronization**
   - Regular coordination between agents
   - Shared evidence collection and validation
   - Dependency management and sequencing
   - Issue escalation and resolution

2. **Context Handoff Management**
   - Ensure complete context transfer between agents
   - Maintain project understanding across all agents
   - Coordinate interdependent validations
   - Manage shared resources and validation frameworks

3. **Quality Assurance**
   - Continuous validation throughout the process
   - Evidence-based decision making
   - Systematic issue resolution
   - Comprehensive documentation and reporting

## Agent Context Template

Each deployed agent receives this comprehensive context:

```markdown
# Agent Context Package

## Project Overview
- **Project**: Episteme Analysis Engine and Query Intelligence Production Readiness
- **Objective**: Achieve 100% production readiness for both services
- **Architecture**: Rust analysis-engine + Python query-intelligence + GCP infrastructure
- **Current Status**: [Specific status for agent focus area]

## Your Specific Role
- **Agent Type**: [Security/Performance/Integration/Deployment/Quality/Research]
- **Focus Area**: [Specific area of responsibility]
- **Deliverables**: [Specific outputs expected]
- **Validation Criteria**: [How success is measured]

## Project Context
- **Repository Root**: /Users/<USER>/Documents/GitHub/episteme
- **Key Services**: services/analysis-engine/ (Rust), services/query-intelligence/ (Python)
- **Documentation**: docs/, PRPs/, examples/, research/, .claudedocs/
- **Current Validation**: validation-results/analysis-engine-prod-readiness/

## Coordination Protocol
- **Evidence Collection**: Save all evidence to validation-results/[your-agent]/
- **Progress Reporting**: Update shared progress tracking
- **Issue Escalation**: Report blockers to master coordinator
- **Context Sharing**: Maintain complete project understanding

## Success Criteria
- [Agent-specific success criteria]
- Evidence-based validation required
- Integration with overall production readiness objective
```

## Best Practices

1. **Complete Context**: Each agent receives full project understanding
2. **Evidence-Based**: All decisions backed by testing and documentation
3. **Systematic Progression**: Clear validation gates and dependencies
4. **Quality Focus**: Comprehensive validation at each step
5. **Coordination**: Regular synchronization and context sharing

Execute this command to orchestrate systematic, multi-agent production readiness achievement following Context Engineering principles.
