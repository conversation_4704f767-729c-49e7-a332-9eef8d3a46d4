# Execute PRP Command - Context Engineering Implementation

This command executes Product Requirements Prompts (PRPs) to implement features using Context Engineering principles with validation loops.

## Command Usage
```
/execute-prp PRPs/[feature-name].md
```

## Process Overview

You will follow this systematic implementation process with continuous validation:

### Phase 1: Context Loading & Planning

1. **Load Complete PRP Context**
   - Read the entire PRP file: `$ARGUMENTS`
   - Load all referenced documentation from `research/` directory
   - Examine all referenced examples from `examples/` directory
   - Understand the complete implementation requirements

2. **Create Detailed Task Plan**
   - Use TodoWrite to create comprehensive task list from PRP
   - Break down implementation into 15-20 minute tasks
   - Include validation checkpoints after each major component
   - Plan for iterative development with testing at each step

3. **Validate Prerequisites**
   - Confirm all required dependencies are available
   - Verify existing codebase patterns are understood
   - Check that development environment is ready
   - Ensure all referenced files and documentation are accessible

### Phase 2: Iterative Implementation

For each task in the implementation plan:

1. **Implement Component**
   - Follow the specific patterns from referenced examples
   - Use official documentation from research directory as source of truth
   - Apply Episteme-specific conventions and patterns
   - Implement proper error handling and validation

2. **Immediate Validation**
   - Run syntax and style checks: `cargo fmt && cargo clippy`
   - Execute unit tests for the component: `cargo test [component]`
   - Verify integration with existing code
   - Check performance and resource usage

3. **Fix Issues**
   - If validation fails, analyze the error messages
   - Refer back to examples and documentation for correct patterns
   - Apply fixes and re-run validation
   - Continue until all validations pass

4. **Update Progress**
   - Mark task as complete in TodoWrite
   - Document any discoveries or pattern changes
   - Note any deviations from original plan
   - Update next tasks if needed

### Phase 3: Integration & System Testing

1. **Full System Integration**
   - Ensure all components work together correctly
   - Test integration points with existing services
   - Verify database operations and caching behavior
   - Check API endpoints and middleware integration

2. **Comprehensive Testing**
   - Run full test suite: `cargo test`
   - Execute integration tests with real services
   - Perform load testing if performance requirements specified
   - Validate security requirements and input handling

3. **Performance Validation**
   - Test against performance requirements (e.g., 1M LOC in <5 minutes)
   - Monitor resource usage and memory consumption
   - Verify resource limits are enforced (10MB files, 30s timeout)
   - Check graceful degradation under load

### Phase 4: Production Readiness

1. **Code Quality Validation**
   - Ensure no `unwrap()` or `expect()` in production code
   - Verify comprehensive error handling
   - Check proper logging and monitoring integration
   - Validate security patterns and input sanitization

2. **Documentation & Monitoring**
   - Add/update relevant documentation
   - Implement Prometheus metrics if required
   - Add structured logging with appropriate levels
   - Update health check endpoints if needed

3. **Final Validation Loop**
   - Run all validation commands from PRP
   - Execute manual testing scenarios
   - Verify all success criteria are met
   - Confirm no regressions in existing functionality

## Validation Commands

Execute these commands at each validation checkpoint:

### Level 1: Syntax & Style
```bash
cargo fmt                    # Format code
cargo clippy -- -D warnings # Lint with warnings as errors
cargo check                  # Type checking
```

### Level 2: Unit Testing
```bash
cargo test [module] -- --nocapture  # Run specific module tests
cargo test -- --nocapture           # Run all tests
```

### Level 3: Integration Testing
```bash
# Start service in development mode
cargo run --bin analysis-engine &

# Test endpoints (example)
curl -X POST http://localhost:8001/api/v1/analyze \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token" \
  -d '{"repository_url": "https://github.com/test/repo"}'

# Stop service
pkill analysis-engine
```

### Level 4: Performance Testing
```bash
# Run with sample data within limits
# Monitor memory usage and response times
# Test resource limit enforcement
```

## Error Handling Strategy

When validation fails:

1. **Analyze Error Messages**
   - Read compiler errors or test failures carefully
   - Identify the root cause of the issue
   - Check if it's a pattern mismatch or logic error

2. **Consult Context**
   - Re-examine relevant examples for correct patterns
   - Check research documentation for proper API usage
   - Verify against PRP requirements and constraints

3. **Apply Fixes**
   - Make targeted fixes based on error analysis
   - Don't make broad changes that might introduce new issues
   - Test fixes incrementally

4. **Re-validate**
   - Run validation commands again
   - Ensure fix doesn't break other components
   - Continue iteration until all validations pass

## Success Criteria Validation

Before marking implementation complete, verify:

- [ ] All PRP success criteria are met
- [ ] All validation commands pass without errors
- [ ] Performance requirements are satisfied
- [ ] Security requirements are implemented
- [ ] Integration with existing services works correctly
- [ ] Error handling is comprehensive and appropriate
- [ ] Monitoring and logging are properly implemented
- [ ] Documentation is updated as needed

## Best Practices

1. **Follow Examples Religiously** - Use existing patterns consistently
2. **Validate Early and Often** - Don't accumulate technical debt
3. **Use Official Documentation** - Never rely on outdated AI knowledge
4. **Implement Incrementally** - Build and test small components
5. **Handle Errors Properly** - No unwrap/expect in production code
6. **Monitor Resource Usage** - Respect limits and performance requirements
7. **Document Decisions** - Note any deviations or discoveries

## Integration with Episteme

This command is optimized for Episteme analysis engine implementation:

- **Rust Ecosystem** - Uses Cargo for building, testing, and validation
- **Async Patterns** - Follows Tokio async/await conventions
- **Database Integration** - Includes Spanner and Redis validation
- **Tree-sitter Parsing** - Handles unsafe blocks and language registry
- **Performance Focus** - Validates against 1M LOC processing requirements
- **Security Standards** - Enforces input validation and resource limits
- **Production Readiness** - Ensures code meets production deployment standards

## Continuous Improvement

After successful implementation:

1. **Update Examples** - Add new patterns to examples directory if applicable
2. **Enhance Documentation** - Update research directory if new insights discovered
3. **Refine PRPs** - Note any improvements for future PRP generation
4. **Share Learnings** - Document any gotchas or best practices discovered

Execute this process systematically to achieve working, production-ready implementations through Context Engineering validation loops.
