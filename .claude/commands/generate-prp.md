# Generate PRP Command - Context Engineering for Episteme

This command generates comprehensive Product Requirements Prompts (PRPs) from feature requests using Context Engineering principles.

## Command Usage
```
/generate-prp FEATURE_FILE.md
```

## Process Overview

You will follow this systematic process to create a comprehensive PRP:

### Phase 1: Research & Context Gathering

1. **Read the Feature Request**
   - Parse the INITIAL.md or feature request file: `$ARGUMENTS`
   - Extract requirements, examples, documentation references, and considerations
   - Identify the scope and complexity of the feature

2. **Analyze Existing Codebase**
   - Examine the current codebase structure in `services/analysis-engine/src/`
   - Identify existing patterns and conventions to follow
   - Look for similar implementations that can serve as templates
   - Note integration points and dependencies

3. **Gather Documentation Context**
   - Read relevant files from the `research/` directory as specified in the feature request
   - Include official documentation patterns and API examples
   - Note any gotchas, limitations, or special requirements
   - Ensure all third-party integrations use official documentation

4. **Review Examples**
   - Examine relevant examples from the `examples/` directory
   - Understand patterns for service structure, API handlers, error handling, testing
   - Note anti-patterns and best practices demonstrated

### Phase 2: PRP Generation

1. **Create Comprehensive Context**
   - Use the template from `PRPs/templates/prp_base.md`
   - Include ALL necessary documentation, examples, and caveats
   - Reference specific files and sections from research and examples directories
   - Add Episteme-specific patterns and requirements

2. **Define Implementation Blueprint**
   - Break down the feature into specific, actionable tasks
   - Provide detailed implementation guidance for each task
   - Include code patterns and examples for complex implementations
   - Specify integration points with existing services

3. **Create Validation Loops**
   - Define executable tests and validation commands
   - Include unit test patterns and integration test requirements
   - Specify performance and security validation criteria
   - Provide self-correction mechanisms for common issues

4. **Add Anti-Patterns and Gotchas**
   - Document what NOT to do based on Episteme experience
   - Include common pitfalls and how to avoid them
   - Reference security requirements and resource limits
   - Note performance considerations and optimization patterns

### Phase 3: Quality Assurance

1. **Completeness Check**
   - Ensure all requirements from the feature request are addressed
   - Verify all referenced examples and documentation are included
   - Confirm validation loops are comprehensive and executable
   - Check that success criteria are specific and measurable

2. **Context Density Validation**
   - Verify the PRP includes sufficient context for implementation
   - Ensure official documentation is referenced appropriately
   - Confirm examples are relevant and properly explained
   - Validate that gotchas and considerations are addressed

3. **Confidence Scoring**
   - Rate the PRP completeness on a scale of 1-10
   - Identify any missing context or unclear requirements
   - Note areas that may require additional research or clarification
   - Aim for 8+ confidence score for complex features

### Phase 4: PRP Finalization

1. **Generate PRP File**
   - Create the PRP file in `PRPs/active/[feature-name].md`
   - Use descriptive naming that reflects the feature purpose
   - Include metadata: creation date, confidence score, complexity level

2. **Validation Summary**
   - Provide a summary of the research conducted
   - List all documentation and examples referenced
   - Note any assumptions or areas requiring clarification
   - Include next steps for PRP execution

## Output Format

The generated PRP should follow this structure:

```markdown
# PRP: [Feature Name] - [Brief Description]

**Created**: [Date]
**Confidence Score**: [1-10]/10
**Complexity**: [Low/Medium/High]
**Estimated Implementation**: [Time estimate]

[Use PRPs/templates/prp_base.md as the template]

## Research Summary
- **Documentation Reviewed**: [List of research files consulted]
- **Examples Referenced**: [List of example files used]
- **Codebase Analysis**: [Summary of existing code patterns found]
- **Integration Points**: [Key integration requirements identified]

## Implementation Confidence
- **Context Completeness**: [Assessment of available context]
- **Pattern Clarity**: [Assessment of implementation patterns]
- **Validation Coverage**: [Assessment of validation loops]
- **Risk Factors**: [Any identified risks or unknowns]
```

## Best Practices

1. **Comprehensive Research** - Always examine all relevant documentation and examples
2. **Official Sources Only** - Use only official documentation from the research directory
3. **Pattern Consistency** - Ensure new implementations follow existing Episteme patterns
4. **Validation Focus** - Include executable validation commands for self-correction
5. **Context Density** - Provide rich context to minimize AI hallucination
6. **Evidence-Based** - Base all recommendations on documented patterns and official sources

## Error Handling

If the feature request is incomplete or unclear:
1. **Identify Missing Information** - List specific gaps in requirements
2. **Request Clarification** - Ask for additional details or examples
3. **Provide Guidance** - Suggest how to improve the feature request
4. **Partial PRP** - Generate what's possible and note limitations

## Integration with Episteme

This command is specifically designed for the Episteme analysis engine:
- **Rust-First** - Assumes Rust implementation with Tokio async patterns
- **Analysis Engine Context** - Includes Tree-sitter, Spanner, Redis integration patterns
- **Performance Requirements** - Includes 1M LOC processing and resource limit considerations
- **Security Standards** - Incorporates input validation and security best practices
- **Production Readiness** - Ensures generated PRPs lead to production-ready code

Execute this process systematically to create comprehensive PRPs that enable successful feature implementation through Context Engineering principles.
