# Execute Research Coordination - Multi-Agent Documentation Gathering

This command executes the complete research coordination system to gather 200+ pages of official documentation across 6 technology areas using parallel agent deployment.

## Command Usage
```
/execute-research-coordination
```

## Execution Process

### Phase 1: System Initialization

First, I'll verify the research coordination infrastructure is ready:

```bash
# Check infrastructure
ls -la research/coordination/
ls -la research/coordination/agents/
ls -la research/coordination/validation/

# Verify existing documentation
echo "Current documentation status:"
find research/ -name "*.md" -type f | wc -l
```

### Phase 2: Deploy Research Agents

I'll deploy 6 specialized research agents in parallel to gather documentation:

#### 1. Rust Research Agent (Target: 50+ pages)
```yaml
focus_areas:
  - Memory safety and ownership
  - Async programming with Tokio
  - Error handling patterns
  - Unsafe code guidelines
  - Production deployment
  
sources:
  - doc.rust-lang.org
  - docs.rs
  - tokio.rs
  - rust-lang.github.io
```

#### 2. Python/NLP Research Agent (Target: 50+ pages)
```yaml
focus_areas:
  - FastAPI production patterns
  - Transformers and NLP
  - Vector search optimization
  - LLM integration
  - Async Python patterns
  
sources:
  - docs.python.org
  - fastapi.tiangolo.com
  - huggingface.co
  - langchain.readthedocs.io
```

#### 3. Google Cloud Research Agent (Target: 50+ pages)
```yaml
focus_areas:
  - Cloud Run deployment
  - Spanner optimization
  - Redis caching strategies
  - Pub/Sub messaging
  - Monitoring and logging
  
sources:
  - cloud.google.com
  - googleapis.dev
```

#### 4. Security Research Agent (Target: 30+ pages)
```yaml
focus_areas:
  - OWASP Top 10 prevention
  - Vulnerability scanning
  - Secure deployment
  - Compliance standards
  - Threat modeling
  
sources:
  - owasp.org
  - nvd.nist.gov
  - cve.mitre.org
```

#### 5. Performance Research Agent (Target: 30+ pages)
```yaml
focus_areas:
  - Benchmarking methodologies
  - Profiling techniques
  - Optimization patterns
  - Resource management
  - Monitoring metrics
  
sources:
  - brendangregg.com
  - intel.com/developer
  - prometheus.io/docs
```

#### 6. Integration Research Agent (Target: 30+ pages)
```yaml
focus_areas:
  - Microservices patterns
  - API design best practices
  - OpenTelemetry integration
  - Event-driven architecture
  - Health check patterns
  
sources:
  - opentelemetry.io
  - grpc.io
  - kafka.apache.org
```

### Phase 3: Parallel Execution & Monitoring

The orchestrator will:

1. **Deploy all agents simultaneously** using the Task tool
2. **Monitor real-time progress** with status updates every 5 seconds
3. **Handle failures** with exponential backoff retry logic
4. **Validate content** using quality scoring system
5. **Track coverage** against target requirements

### Phase 4: Quality Validation

Each scraped page will be validated for:

- **Content Length**: Minimum 500 characters
- **Source Authority**: Official documentation only
- **Completeness**: No error pages or 404s
- **Quality Score**: Based on code examples, structure, and depth

### Phase 5: Report Generation

After completion, I'll generate:

1. **Coverage Report**
   - Total pages gathered per technology
   - Word count statistics
   - Quality metrics

2. **Validation Report**
   - Pass/fail rates
   - Content quality scores
   - Failed URLs for review

3. **Executive Summary**
   - Achievement against 200+ page target
   - Technology coverage breakdown
   - Recommendations for gaps

## Expected Output

```
Starting multi-agent research coordination...
Initializing research agents...
Successfully initialized 6 agents

Deploying agents in parallel...
Starting rust research agent...
Starting python_nlp research agent...
Starting gcp research agent...
Starting security research agent...
Starting performance research agent...
Starting integration research agent...

Monitoring agent execution...
Progress: 47/240 pages (19.6%) - Active agents: 6

✓ Completed: security - 32/30 pages
✓ Completed: performance - 35/30 pages
✓ Completed: integration - 31/30 pages
✓ Completed: rust - 52/50 pages
✓ Completed: python_nlp - 48/50 pages
✓ Completed: gcp - 51/50 pages

All agents completed!

Validating research results...

============================================================
RESEARCH GATHERING COMPLETE
============================================================
Total Pages: 249
Total Words: 487,532
Coverage: 124.5%
Quality: 93.7%

Report saved to: research/coordination/reports/research_summary.md
```

## Validation Steps

After execution, I'll validate:

```bash
# Check total documentation count
find research/ -name "*.md" -type f | wc -l

# Verify coverage by technology
echo "Rust:" && find research/rust -name "*.md" | wc -l
echo "Python:" && find research/python -name "*.md" | wc -l
echo "Google Cloud:" && find research/google-cloud -name "*.md" | wc -l
echo "Security:" && find research/security -name "*.md" | wc -l
echo "Performance:" && find research/performance -name "*.md" | wc -l
echo "Integration:" && find research/integration -name "*.md" | wc -l

# View summary report
cat research/coordination/reports/research_summary.md
```

## Success Criteria

- [x] 6 research agents deployed successfully
- [ ] 200+ pages of documentation gathered
- [ ] >90% validation pass rate
- [ ] All technologies have adequate coverage
- [ ] Research organized in proper directories
- [ ] Summary report generated

## Error Recovery

If any agents fail or coverage is insufficient:

1. **Identify gaps** in the coverage report
2. **Re-deploy specific agents** with additional URLs
3. **Validate supplementary gathering**
4. **Update final report** with combined results

## Next Steps

Once research gathering is complete:

1. Review the executive summary report
2. Address any coverage gaps identified
3. Validate documentation quality
4. Use gathered research for PRP generation
5. Archive successful URLs for future updates

This command orchestrates the complete research gathering process, ensuring comprehensive documentation coverage for evidence-based development.