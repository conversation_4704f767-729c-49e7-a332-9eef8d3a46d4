# SuperClaude Configuration for Episteme Project

This file documents the SuperClaude features and configurations active in the Episteme project.

## 🎯 Active Configuration

### Core Philosophy
- **Evidence-Based Development**: All claims must be backed by testing, metrics, or documentation
- **Security-First**: Zero tolerance for vulnerabilities
- **Performance-Optimized**: UltraCompressed mode available for large operations

### 🔌 MCP Servers Enabled

1. **Context7** ✅
   - Purpose: Official library documentation & examples
   - Use: `/analyze --c7` for research-first methodology
   - Token Cost: Low-Medium

2. **Sequential** ✅
   - Purpose: Multi-step problem solving & architectural thinking
   - Use: `/analyze --seq` for deep system analysis
   - Token Cost: Medium-High

3. **Magic** ✅
   - Purpose: UI component generation & design system integration
   - Use: `/build --react --magic` for component generation
   - Token Cost: Medium

4. **Puppeteer** ✅
   - Purpose: E2E testing, performance validation, browser automation
   - Use: `/test --e2e --pup` for quality assurance
   - Token Cost: Low (action-based)

5. **GCP-MCP** ✅
   - Purpose: Google Cloud Platform integration
   - Use: For Spanner, Cloud Storage, Vertex AI operations

6. **Vertex AI** ✅
   - Purpose: AI/ML operations, embeddings, documentation
   - Use: For code analysis and pattern detection

### 📋 Task Management

**Two-Tier Architecture Active**:
- Level 1: `.claudedocs/tasks/` - Persistent cross-session tasks
- Level 2: TodoWrite/TodoRead - Session-specific execution tracking

**Auto-Triggers Configured**:
- 3+ steps → Auto-trigger TodoList
- Database changes → REQUIRE todos
- 30+ minute tasks → AUTO-TRIGGER
- 6+ files → AUTO-TRIGGER

### 🎭 Available Personas

All 9 SuperClaude personas are available:
- `--persona-frontend`: UI/UX, React/Vue components
- `--persona-backend`: API design, scalability
- `--persona-architect`: System design, architecture
- `--persona-analyzer`: Root cause analysis
- `--persona-security`: Threat modeling, audits
- `--persona-qa`: Testing, quality assurance
- `--persona-performance`: Optimization, profiling
- `--persona-refactorer`: Code quality, technical debt
- `--persona-mentor`: Documentation, tutorials

### ⚡ Performance Features

- **UltraCompressed Mode**: `--uc` flag for ~70% token reduction
- **Parallel MCP Execution**: Independent calls run simultaneously
- **Intelligent Caching**: Context7 (1hr), Magic (2hr), Sequential (session)

### 🔒 Security Configuration

- OWASP Top 10 coverage with automated detection
- CVE scanning for vulnerabilities
- Dependency security with license compliance
- Zero hardcoded secrets policy

### 📁 Documentation Structure

```
.claudedocs/
├── reports/      # Analysis reports, security scans
├── metrics/      # Performance metrics, coverage
├── summaries/    # Session summaries, decisions
├── checkpoints/  # Project milestones
└── tasks/        # Level 1 persistent tasks
```

### 🚀 Quick Commands

**Analysis Engine Focus**:
```bash
# Security audit
/scan --security --owasp --deps

# Performance analysis
/analyze --performance --seq --profile

# Architecture review
/design --arch --seq --ultrathink

# Code quality
/improve --quality --refactor --iterate
```

## Integration Status

✅ Global SuperClaude configuration active
✅ Project-specific MCP servers configured
✅ Task management system ready
✅ Documentation structure created
✅ Evidence-based standards enforced

---
*SuperClaude v2.0.1 integrated with Episteme Analysis Engine*