# Context Engineering Claude Code Hooks Configuration
# Production-ready monitoring and validation for agent coordination

# Pre-Tool Use Hooks - Validation and Security

[[hooks]]
event = "PreToolUse"
[hooks.matcher]
tool_name = "str-replace-editor"
command = "python ./.context-engineering/validate-security.py"
description = "Validate security compliance before file edits"

[[hooks]]
event = "PreToolUse"
[hooks.matcher]
tool_name = "save-file"
command = "python ./.context-engineering/validate-research-backing.py"
description = "Ensure all new files reference research documentation"

[[hooks]]
event = "PreToolUse"
[hooks.matcher]
tool_name = "launch-process"
command = "python ./.context-engineering/validate-command-safety.py"
description = "Validate command safety and resource limits"

# Post-Tool Use Hooks - Evidence Collection and State Updates

[[hooks]]
event = "PostToolUse"
[hooks.matcher]
tool_name = "str-replace-editor"
command = "python ./.context-engineering/collect-edit-evidence.py"
description = "Collect evidence for file modifications"

[[hooks]]
event = "PostToolUse"
[hooks.matcher]
tool_name = "launch-process"
command = "python ./.context-engineering/collect-execution-evidence.py"
description = "Collect evidence for command executions"

[[hooks]]
event = "PostToolUse"
[hooks.matcher]
tool_name = "save-file"
command = "python ./.context-engineering/update-project-state.py"
description = "Update project state after file creation"

# PRP Lifecycle Hooks

[[hooks]]
event = "PreToolUse"
[hooks.matcher]
tool_name = "save-file"
[hooks.matcher.args]
file_path = "PRPs/active/*.md"
command = "python ./.context-engineering/validate-prp-quality.py"
description = "Validate PRP quality and Context Engineering compliance"

[[hooks]]
event = "PostToolUse"
[hooks.matcher]
tool_name = "save-file"
[hooks.matcher.args]
file_path = "PRPs/active/*.md"
command = "python ./.context-engineering/register-prp-lifecycle.py"
description = "Register PRP in lifecycle management system"

# Validation Framework Hooks

[[hooks]]
event = "PreToolUse"
[hooks.matcher]
tool_name = "launch-process"
[hooks.matcher.args]
command = "cargo *"
command = "python ./.context-engineering/pre-validation-check.py"
description = "Pre-validation checks for Rust commands"

[[hooks]]
event = "PostToolUse"
[hooks.matcher]
tool_name = "launch-process"
[hooks.matcher.args]
command = "cargo audit"
command = "python ./.context-engineering/process-audit-results.py"
description = "Process cargo audit results and update security status"

[[hooks]]
event = "PostToolUse"
[hooks.matcher]
tool_name = "launch-process"
[hooks.matcher.args]
command = "cargo clippy*"
command = "python ./.context-engineering/process-clippy-results.py"
description = "Process clippy results and update code quality status"

# Research Integration Hooks

[[hooks]]
event = "PreToolUse"
[hooks.matcher]
tool_name = "str-replace-editor"
command = "python ./.context-engineering/check-research-references.py"
description = "Ensure code changes reference appropriate research documentation"

[[hooks]]
event = "PostToolUse"
[hooks.matcher]
tool_name = "str-replace-editor"
command = "python ./.context-engineering/update-research-mapping.py"
description = "Update research-to-implementation mapping"

# Agent Coordination Hooks

[[hooks]]
event = "PreToolUse"
[hooks.matcher]
tool_name = "*"
command = "python ./.context-engineering/agent-coordination-check.py"
description = "Check agent coordination status and permissions"

[[hooks]]
event = "PostToolUse"
[hooks.matcher]
tool_name = "*"
command = "python ./.context-engineering/update-agent-status.py"
description = "Update agent status and coordination state"

# Quality Assurance Hooks

[[hooks]]
event = "PreToolUse"
[hooks.matcher]
tool_name = "str-replace-editor"
[hooks.matcher.args]
file_path = "services/analysis-engine/src/*.rs"
command = "python ./.context-engineering/validate-rust-safety.py"
description = "Validate Rust code safety and memory management"

[[hooks]]
event = "PostToolUse"
[hooks.matcher]
tool_name = "str-replace-editor"
[hooks.matcher.args]
file_path = "services/analysis-engine/src/*.rs"
command = "python ./.context-engineering/update-safety-documentation.py"
description = "Update safety documentation and unsafe block tracking"

# Performance Monitoring Hooks

[[hooks]]
event = "PreToolUse"
[hooks.matcher]
tool_name = "launch-process"
[hooks.matcher.args]
command = "*benchmark*"
command = "python ./.context-engineering/prepare-performance-monitoring.py"
description = "Prepare performance monitoring for benchmark execution"

[[hooks]]
event = "PostToolUse"
[hooks.matcher]
tool_name = "launch-process"
[hooks.matcher.args]
command = "*benchmark*"
command = "python ./.context-engineering/collect-performance-metrics.py"
description = "Collect and analyze performance metrics"

# Error Handling and Recovery Hooks

[[hooks]]
event = "ToolError"
[hooks.matcher]
tool_name = "*"
command = "python ./.context-engineering/handle-tool-error.py"
description = "Handle tool errors with automated recovery mechanisms"

[[hooks]]
event = "ToolTimeout"
[hooks.matcher]
tool_name = "*"
command = "python ./.context-engineering/handle-tool-timeout.py"
description = "Handle tool timeouts with graceful degradation"

# Notification and Alerting Hooks

[[hooks]]
event = "PostToolUse"
[hooks.matcher]
tool_name = "launch-process"
[hooks.matcher.args]
command = "cargo audit"
command = "python ./.context-engineering/security-alert-system.py"
description = "Send security alerts for critical findings"

[[hooks]]
event = "PostToolUse"
[hooks.matcher]
tool_name = "*"
command = "python ./.context-engineering/progress-notification.py"
description = "Send progress notifications for major milestones"

# Backup and Recovery Hooks

[[hooks]]
event = "PreToolUse"
[hooks.matcher]
tool_name = "str-replace-editor"
command = "python ./.context-engineering/backup-before-edit.py"
description = "Create backup before critical file modifications"

[[hooks]]
event = "PostToolUse"
[hooks.matcher]
tool_name = "str-replace-editor"
command = "python ./.context-engineering/verify-edit-integrity.py"
description = "Verify edit integrity and create recovery points"

# Context Engineering Compliance Hooks

[[hooks]]
event = "PreToolUse"
[hooks.matcher]
tool_name = "*"
command = "python ./.context-engineering/enforce-context-engineering-standards.py"
description = "Enforce Context Engineering standards for all operations"

[[hooks]]
event = "PostToolUse"
[hooks.matcher]
tool_name = "*"
command = "python ./.context-engineering/validate-context-engineering-compliance.py"
description = "Validate Context Engineering compliance after operations"
