{"permissions": {"allow": ["mcp__vertex-ai-server__explain_topic_with_docs", "mcp__vertex-ai-server__answer_query_websearch", "mcp__gcp-mcp__list-projects", "mcp__gcp-mcp__select-project", "mcp__vertex-ai-server__answer_query_websearch", "mcp__gcp-mcp__run-gcp-code", "mcp__vertex-ai-server__get_directory_tree", "mcp__sequential-thinking__sequentialthinking", "mcp__Context7__resolve-library-id", "mcp__Context7__get-library-docs", "mcp__vertex-ai-server__get_directory_tree", "mcp__vertex-ai-server__get_doc_snippets", "mcp__vertex-ai-server__read_file_content", "mcp__vertex-ai-server__list_directory_contents", "mcp__vertex-ai-server__read_file_content", "mcp__vertex-ai-server__list_directory_contents", "mcp__magic__generate_component", "mcp__magic__list_components", "mcp__magic__update_component", "mcp__puppeteer__puppeteer_navigate", "mcp__puppeteer__puppeteer_screenshot", "mcp__puppeteer__puppeteer_click", "mcp__puppeteer__puppeteer_fill"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["filesystem", "memory", "puppeteer", "sequential-thinking", "fetch", "gcp-mcp", "magic", "context7"], "disabledMcpjsonServers": ["github", "google", "postgres"]}