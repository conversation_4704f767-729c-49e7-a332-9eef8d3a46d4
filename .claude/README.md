# .claude/ Directory

This directory contains Claude Code configuration for the Episteme project.

## Contents

- **settings.local.json** - MCP server configuration and permissions
- **SUPERCLAUDE_CONFIG.md** - SuperClaude integration status and features

## Note

This directory is for Claude Code configuration only. For AI-assisted development tracking and documentation, see `.claudedocs/`.

Since this project doesn't use project-level .claude files (relying on global SuperClaude configuration instead), we keep only the essential configuration files here.