# Pattern Mining Service

The Pattern Mining service is a Python-based ML/AI microservice that analyzes code repositories to detect patterns, best practices, and potential improvements using Google's Gemini 2.5 and Vertex AI.

## Service Overview

**Status**: Production Ready  
**Technology**: Python 3.11+, FastAPI, Ray, Redis  
**AI/ML**: Google Gemini 2.5 Flash, Vertex AI  
**Infrastructure**: Google Cloud Platform (Cloud Run, BigQuery, Spanner)

## Key Features

### 🧠 ML/AI Capabilities
- **Pattern Detection**: Advanced code pattern analysis using Gemini 2.5 Flash
- **Similarity Analysis**: Vector-based code similarity detection
- **Anomaly Detection**: Statistical and ML-based anomaly identification
- **Multi-Language Support**: AST-based analysis for 18+ programming languages

### 🚀 Performance & Scale
- **High Throughput**: Processes 1M+ lines of code per minute
- **Distributed Processing**: Ray cluster for parallel analysis
- **Multi-Tier Caching**: Redis with vector search capabilities
- **Real-Time & Batch**: Supports both streaming and batch processing

### 🔒 Security Features
- **Secret Management**: Automatic 24-hour API key rotation
- **Access Control**: 7-role RBAC with parameter-level permissions
- **Audit Logging**: Comprehensive audit trail with 90-day retention
- **AI Security**: Prompt injection protection and response validation

## Quick Links

- [API Documentation](./api/README.md)
- [Architecture Overview](./architecture/README.md)
- [Developer Guide](./guides/developer-guide.md)
- [Deployment Guide](./guides/deployment-guide.md)
- [Operations Runbook](./operations-runbook.md)
- [Troubleshooting](./troubleshooting/README.md)

## Service Architecture

```mermaid
graph TB
    subgraph "Pattern Mining Service"
        API[FastAPI Server]
        PM[Pattern Detector]
        GC[Gemini Client]
        RC[Redis Cache]
        RAY[Ray Cluster]
    end
    
    subgraph "Google Cloud"
        GEM[Gemini 2.5 Flash]
        VAI[Vertex AI]
        BQ[BigQuery]
        SP[Spanner]
        SM[Secret Manager]
    end
    
    subgraph "External"
        AE[Analysis Engine]
        MP[Marketplace]
    end
    
    API --> PM
    PM --> GC
    GC --> GEM
    PM --> VAI
    PM --> RC
    PM --> RAY
    PM --> BQ
    PM --> SP
    GC --> SM
    
    AE --> API
    API --> MP
```

## Getting Started

For quick setup and development instructions, see the [Developer Guide](./guides/developer-guide.md).

For production deployment, refer to the [Deployment Guide](./guides/deployment-guide.md).

## Configuration

The service uses a comprehensive configuration system with 165+ parameters organized by security level:
- **PUBLIC**: General configuration
- **INTERNAL**: Service-specific settings
- **SENSITIVE**: Authentication tokens
- **SECRET**: API keys and passwords

See the service's `.env.example` for configuration options.

## Support

For operational procedures and troubleshooting, see:
- [Operations Runbook](./operations-runbook.md)
- [Troubleshooting Guide](./troubleshooting/README.md)

For detailed implementation history and security audit reports, check the service's archive directory.