# Pattern Mining Deployment Guide

This guide provides comprehensive instructions for deploying the Pattern Mining service to production environments on Google Cloud Platform.

## Prerequisites

### Required Access
- GCP Project with billing enabled
- Necessary IAM roles:
  - `roles/cloudrun.admin`
  - `roles/secretmanager.admin`
  - `roles/bigquery.admin`
  - `roles/spanner.admin`
  - `roles/redis.admin`
  - `roles/iam.serviceAccountAdmin`

### Required Tools
- Google Cloud SDK (`gcloud`)
- Docker
- Kubernetes CLI (`kubectl`)
- Terraform (optional, for IaC)

## Architecture Overview

```mermaid
graph TB
    subgraph "External"
        Users[Users]
        LB[Cloud Load Balancer]
    end
    
    subgraph "Cloud Run Services"
        PM[Pattern Mining Service]
        PMDR[Pattern Mining DR]
    end
    
    subgraph "Data Layer"
        Redis[Memorystore Redis]
        BQ[BigQuery]
        Spanner[Cloud Spanner]
    end
    
    subgraph "ML/AI"
        Gemini[Gemini API]
        Vertex[Vertex AI]
    end
    
    subgraph "Security"
        SM[Secret Manager]
        IAM[Cloud IAM]
    end
    
    Users --> LB
    LB --> PM
    LB --> <PERSON><PERSON>
    PM --> Redis
    PM --> BQ
    PM --> <PERSON><PERSON>
    PM --> Gemini
    PM --> Vertex
    PM --> SM
    PM --> IAM
```

## Pre-Deployment Setup

### 1. GCP Project Configuration

```bash
# Set project
export PROJECT_ID="your-project-id"
export REGION="us-central1"
gcloud config set project $PROJECT_ID

# Enable required APIs
gcloud services enable \
    cloudrun.googleapis.com \
    cloudbuild.googleapis.com \
    secretmanager.googleapis.com \
    bigquery.googleapis.com \
    spanner.googleapis.com \
    redis.googleapis.com \
    aiplatform.googleapis.com \
    monitoring.googleapis.com \
    logging.googleapis.com
```

### 2. Service Account Setup

```bash
# Create service account
gcloud iam service-accounts create pattern-mining-sa \
    --display-name="Pattern Mining Service Account"

# Grant necessary permissions
export SA_EMAIL="pattern-mining-sa@${PROJECT_ID}.iam.gserviceaccount.com"

# Assign roles
for role in \
    roles/bigquery.dataEditor \
    roles/bigquery.jobUser \
    roles/spanner.databaseUser \
    roles/secretmanager.secretAccessor \
    roles/aiplatform.user \
    roles/logging.logWriter \
    roles/monitoring.metricWriter \
    roles/cloudtrace.agent
do
    gcloud projects add-iam-policy-binding $PROJECT_ID \
        --member="serviceAccount:${SA_EMAIL}" \
        --role="$role"
done
```

### 3. Infrastructure Setup

#### Create BigQuery Dataset
```bash
bq mk --dataset \
    --location=$REGION \
    --description="Pattern Mining cache and analytics" \
    ${PROJECT_ID}:pattern_mining
```

#### Create Spanner Instance
```bash
gcloud spanner instances create pattern-mining-prod \
    --config=regional-${REGION} \
    --description="Pattern Mining production database" \
    --nodes=3
    
# Create database
gcloud spanner databases create pattern-mining \
    --instance=pattern-mining-prod
```

#### Create Redis Instance
```bash
gcloud redis instances create pattern-mining-cache \
    --size=10 \
    --region=$REGION \
    --redis-version=redis_6_x \
    --tier=standard \
    --replica-count=1 \
    --enable-auth
```

### 4. Secret Management

```bash
# Create secrets
echo -n "your-gemini-api-key" | gcloud secrets create gemini-api-key --data-file=-
echo -n "$(openssl rand -base64 32)" | gcloud secrets create jwt-secret --data-file=-
echo -n "$(openssl rand -base64 32)" | gcloud secrets create database-password --data-file=-

# Grant access to service account
for secret in gemini-api-key jwt-secret database-password; do
    gcloud secrets add-iam-policy-binding $secret \
        --member="serviceAccount:${SA_EMAIL}" \
        --role="roles/secretmanager.secretAccessor"
done
```

## Build and Deploy

### 1. Build Container Image

```bash
# Clone repository
git clone https://github.com/episteme/pattern-mining.git
cd pattern-mining

# Build with Cloud Build
gcloud builds submit \
    --config=cloudbuild.yaml \
    --substitutions=_SERVICE_NAME=pattern-mining,_REGION=$REGION
```

### 2. Deploy to Cloud Run

```bash
# Deploy service
gcloud run deploy pattern-mining \
    --image=gcr.io/${PROJECT_ID}/pattern-mining:latest \
    --platform=managed \
    --region=$REGION \
    --service-account=$SA_EMAIL \
    --set-env-vars="GCP_PROJECT_ID=${PROJECT_ID}" \
    --set-secrets="GEMINI_API_KEY=gemini-api-key:latest" \
    --set-secrets="JWT_SECRET=jwt-secret:latest" \
    --set-secrets="DATABASE_PASSWORD=database-password:latest" \
    --vpc-connector=pattern-mining-connector \
    --min-instances=2 \
    --max-instances=50 \
    --cpu=2 \
    --memory=4Gi \
    --timeout=300 \
    --concurrency=100 \
    --ingress=internal-and-cloud-load-balancing
```

### 3. Configure Load Balancer

```bash
# Reserve static IP
gcloud compute addresses create pattern-mining-ip \
    --global

# Create NEG (Network Endpoint Group)
gcloud compute network-endpoint-groups create pattern-mining-neg \
    --region=$REGION \
    --network-endpoint-type=serverless \
    --cloud-run-service=pattern-mining

# Create backend service
gcloud compute backend-services create pattern-mining-backend \
    --global \
    --load-balancing-scheme=EXTERNAL \
    --protocol=HTTPS

# Add backend
gcloud compute backend-services add-backend pattern-mining-backend \
    --global \
    --network-endpoint-group=pattern-mining-neg \
    --network-endpoint-group-region=$REGION

# Create URL map
gcloud compute url-maps create pattern-mining-lb \
    --default-service=pattern-mining-backend

# Create HTTPS proxy
gcloud compute target-https-proxies create pattern-mining-https-proxy \
    --url-map=pattern-mining-lb \
    --ssl-certificates=pattern-mining-cert

# Create forwarding rule
gcloud compute forwarding-rules create pattern-mining-https-rule \
    --global \
    --address=pattern-mining-ip \
    --target-https-proxy=pattern-mining-https-proxy \
    --ports=443
```

## Database Migration

### 1. Run Alembic Migrations

```bash
# Connect to Cloud SQL proxy
cloud_sql_proxy -instances=${PROJECT_ID}:${REGION}:pattern-mining=tcp:5432 &

# Set environment variables
export DATABASE_URL="postgresql://pattern_mining:${DB_PASSWORD}@localhost:5432/pattern_mining"

# Run migrations
alembic upgrade head
```

### 2. Initialize BigQuery Tables

```python
# scripts/init_bigquery.py
from google.cloud import bigquery

client = bigquery.Client()
dataset_id = f"{PROJECT_ID}.pattern_mining"

# Create pattern cache table
table_id = f"{dataset_id}.pattern_cache"
schema = [
    bigquery.SchemaField("id", "STRING", mode="REQUIRED"),
    bigquery.SchemaField("repository_id", "STRING", mode="REQUIRED"),
    bigquery.SchemaField("patterns", "JSON", mode="REQUIRED"),
    bigquery.SchemaField("created_at", "TIMESTAMP", mode="REQUIRED"),
    bigquery.SchemaField("ttl", "TIMESTAMP", mode="REQUIRED"),
]

table = bigquery.Table(table_id, schema=schema)
table = client.create_table(table)
print(f"Created table {table.table_id}")
```

## Configuration

### 1. Environment Variables

Create a Cloud Run service YAML with all configurations:

```yaml
# service.yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: pattern-mining
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/minScale: "2"
        autoscaling.knative.dev/maxScale: "50"
    spec:
      serviceAccountName: pattern-mining-sa@PROJECT_ID.iam.gserviceaccount.com
      containers:
      - image: gcr.io/PROJECT_ID/pattern-mining:latest
        resources:
          limits:
            cpu: "2"
            memory: "4Gi"
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "INFO"
        - name: REDIS_HOST
          value: "********"  # Redis internal IP
        - name: BIGQUERY_DATASET
          value: "pattern_mining"
        - name: SPANNER_INSTANCE
          value: "pattern-mining-prod"
        - name: SPANNER_DATABASE
          value: "pattern-mining"
        - name: RAY_CLUSTER_ADDRESS
          value: "ray://ray-head:10001"
        - name: GEMINI_MODEL
          value: "gemini-2.5-flash"
        - name: VERTEX_AI_LOCATION
          value: "us-central1"
```

### 2. Apply Configuration

```bash
gcloud run services replace service.yaml --region=$REGION
```

## Post-Deployment

### 1. Health Checks

```bash
# Get service URL
SERVICE_URL=$(gcloud run services describe pattern-mining \
    --region=$REGION \
    --format='value(status.url)')

# Check health endpoint
curl -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
    ${SERVICE_URL}/health

# Expected response:
# {
#   "status": "healthy",
#   "version": "1.0.0",
#   "services": {
#     "database": "connected",
#     "redis": "connected",
#     "bigquery": "connected",
#     "gemini": "connected"
#   }
# }
```

### 2. Enable Monitoring

```bash
# Create uptime check
gcloud monitoring uptime create pattern-mining-health \
    --display-name="Pattern Mining Health Check" \
    --uri="${SERVICE_URL}/health" \
    --check-interval=60s

# Create alerts
gcloud alpha monitoring policies create \
    --notification-channels=YOUR_CHANNEL_ID \
    --alert-strategy-auto-close-duration=1800s \
    --display-name="Pattern Mining High Error Rate" \
    --condition-display-name="Error rate > 5%" \
    --condition-metric-type="run.googleapis.com/request_count" \
    --condition-metric-filter='resource.type="cloud_run_revision" AND metric.label.response_code_class="5xx"'
```

### 3. Configure Auto-scaling

```bash
# Update auto-scaling parameters
gcloud run services update pattern-mining \
    --region=$REGION \
    --min-instances=2 \
    --max-instances=100 \
    --cpu-throttling \
    --scale-to-zero-delay=15m \
    --execution-environment=gen2
```

## Security Hardening

### 1. Enable VPC Service Controls

```bash
# Create VPC perimeter
gcloud access-context-manager perimeters create PatternMiningPerimeter \
    --title="Pattern Mining Security Perimeter" \
    --resources=projects/${PROJECT_ID} \
    --restricted-services=bigquery.googleapis.com,spanner.googleapis.com
```

### 2. Configure WAF Rules

```bash
# Create Cloud Armor policy
gcloud compute security-policies create pattern-mining-waf \
    --description="Pattern Mining WAF Policy"

# Add rules
gcloud compute security-policies rules create 1000 \
    --security-policy=pattern-mining-waf \
    --expression="origin.region_code == 'CN' || origin.region_code == 'RU'" \
    --action=deny-403

# Apply to backend
gcloud compute backend-services update pattern-mining-backend \
    --security-policy=pattern-mining-waf \
    --global
```

### 3. Enable Audit Logging

```bash
# Enable audit logs
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:${SA_EMAIL}" \
    --role="roles/logging.privateLogViewer" \
    --condition="expression=resource.name.endsWith('pattern-mining'),title=PatternMiningOnly"
```

## Maintenance

### 1. Rolling Updates

```bash
# Build new version
gcloud builds submit --tag gcr.io/${PROJECT_ID}/pattern-mining:v2.0.0

# Deploy with traffic splitting
gcloud run deploy pattern-mining \
    --image=gcr.io/${PROJECT_ID}/pattern-mining:v2.0.0 \
    --region=$REGION \
    --tag=v2 \
    --no-traffic

# Gradually shift traffic
gcloud run services update-traffic pattern-mining \
    --region=$REGION \
    --to-tags=v2=10

# Monitor metrics, then increase
gcloud run services update-traffic pattern-mining \
    --region=$REGION \
    --to-tags=v2=50

# Complete migration
gcloud run services update-traffic pattern-mining \
    --region=$REGION \
    --to-latest
```

### 2. Backup and Restore

```bash
# Backup Spanner database
gcloud spanner backups create pattern-mining-backup-$(date +%Y%m%d) \
    --instance=pattern-mining-prod \
    --database=pattern-mining \
    --retention-period=30d \
    --async

# Export BigQuery data
bq extract \
    --destination_format=AVRO \
    --compression=SNAPPY \
    pattern_mining.pattern_cache \
    gs://pattern-mining-backups/bigquery/$(date +%Y%m%d)/*.avro
```

## Troubleshooting

### Common Issues

1. **Cloud Run timeout errors**
   ```bash
   # Increase timeout
   gcloud run services update pattern-mining \
       --timeout=600 \
       --region=$REGION
   ```

2. **Memory issues**
   ```bash
   # Increase memory and CPU
   gcloud run services update pattern-mining \
       --memory=8Gi \
       --cpu=4 \
       --region=$REGION
   ```

3. **Cold start issues**
   ```bash
   # Keep instances warm
   gcloud run services update pattern-mining \
       --min-instances=5 \
       --region=$REGION
   ```

### Debug Commands

```bash
# View logs
gcloud logging read "resource.type=cloud_run_revision AND \
    resource.labels.service_name=pattern-mining" \
    --limit=50 \
    --format=json

# SSH into container (if enabled)
gcloud run services update pattern-mining \
    --args="/bin/bash" \
    --command="/bin/bash" \
    --region=$REGION

# Get metrics
gcloud monitoring time-series list \
    --filter='metric.type="run.googleapis.com/request_latencies" AND \
             resource.label.service_name="pattern-mining"' \
    --format=table
```

## Disaster Recovery

### Multi-Region Setup

```bash
# Deploy to secondary region
export DR_REGION="us-east1"

gcloud run deploy pattern-mining-dr \
    --image=gcr.io/${PROJECT_ID}/pattern-mining:latest \
    --platform=managed \
    --region=$DR_REGION \
    --service-account=$SA_EMAIL
    
# Configure traffic management
gcloud compute url-maps add-path-matcher pattern-mining-lb \
    --default-service=pattern-mining-backend \
    --path-matcher-name=region-matcher \
    --backend-service-path-rules="/api/*=pattern-mining-backend-dr"
```

## Performance Tuning

### 1. Connection Pooling
```yaml
# In deployment config
env:
- name: DATABASE_POOL_SIZE
  value: "20"
- name: DATABASE_MAX_OVERFLOW
  value: "40"
- name: REDIS_MAX_CONNECTIONS
  value: "50"
```

### 2. Caching Configuration
```yaml
env:
- name: CACHE_TTL_PATTERNS
  value: "3600"  # 1 hour
- name: CACHE_TTL_EMBEDDINGS
  value: "86400"  # 24 hours
- name: CACHE_WARMING_ENABLED
  value: "true"
```

## Cost Optimization

1. **Use Committed Use Discounts**
   ```bash
   gcloud compute commitments create pattern-mining-cud \
       --plan=TWELVE_MONTH \
       --resources=vcpu=100,memory=400
   ```

2. **Configure Budget Alerts**
   ```bash
   gcloud billing budgets create \
       --billing-account=BILLING_ACCOUNT_ID \
       --display-name="Pattern Mining Budget" \
       --budget-amount=5000 \
       --threshold-rule=percent=80
   ```

3. **Enable Autopilot for GKE (if using Ray on GKE)**
   ```bash
   gcloud container clusters update ray-cluster \
       --enable-autopilot \
       --region=$REGION
   ```