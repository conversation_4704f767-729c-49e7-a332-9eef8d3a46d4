# 🚀 Pattern Mining Security Quick Start Guide

This guide provides rapid setup instructions for the Pattern Mining service security features implemented in Phase 1.2.

## 📋 Prerequisites

- <PERSON>er and Docker Compose installed
- Redis running (for audit logging)
- Python 3.11+ environment
- Access to Google Cloud Project (for production)

## 🔐 5-Minute Security Setup

### 1. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Generate secure secrets
python -c "import secrets; print('SECRET_KEY=' + secrets.token_urlsafe(64))" >> .env
python -c "import secrets; print('JWT_SECRET=' + secrets.token_urlsafe(64))" >> .env
```

### 2. Enable Configuration Access Control

Add these to your `.env` file:

```bash
# Configuration Security (REQUIRED)
CONFIG_ACCESS_CONTROL_ENABLED=true
CONFIG_AUDIT_RETENTION_DAYS=90
CONFIG_SECURITY_MONITORING_ENABLED=true

# Secret Rotation (REQUIRED for Gemini API)
SECRET_ROTATION_ENABLED=true
GEMINI_API_KEY_ROTATION_ENABLED=true
```

### 3. Start Core Services

```bash
# Start Redis (required for audit logging)
docker-compose -f docker-compose.base.yml up -d redis

# Start the service with security enabled
docker-compose -f docker-compose.base.yml up -d pattern-mining
```

### 4. Verify Security Status

```bash
# Get development tokens for testing
curl http://localhost:8000/api/v1/auth/dev-tokens

# Check security status (use admin token)
curl -H "Authorization: Bearer admin-dev-token" \
     http://localhost:8000/api/v1/security/metrics
```

## 🔑 Authentication & Authorization

### Development Authentication

For development, use role-based tokens:

```bash
# Available roles and their token prefixes:
admin-*         # Full access to all parameters
security-*      # Security parameter access
operator-*      # Operational parameter access
developer-*     # Development parameter access
monitor-*       # Read-only monitoring access
service-*       # Service account access
readonly-*      # Public parameter read-only
```

Example:
```bash
curl -H "Authorization: Bearer admin-test-123" \
     http://localhost:8000/api/v1/config/parameters
```

### Production Authentication

In production, use JWT tokens with proper claims:

```python
import jwt
from datetime import datetime, timedelta

# Generate production JWT
payload = {
    "user_id": "<EMAIL>",
    "role": "operator",  # One of: admin, security_admin, operator, etc.
    "session_id": "unique-session-id",
    "exp": datetime.utcnow() + timedelta(hours=1),
    "iat": datetime.utcnow()
}

token = jwt.encode(payload, JWT_SECRET, algorithm="HS256")
```

## 📊 Security Monitoring

### 1. Access Security Dashboard

```bash
# View comprehensive security dashboard
curl -H "Authorization: Bearer admin-token" \
     http://localhost:8000/api/v1/security/dashboard

# Get active security alerts
curl -H "Authorization: Bearer security-token" \
     http://localhost:8000/api/v1/security/alerts
```

### 2. Monitor Configuration Access

```bash
# View audit logs
curl -H "Authorization: Bearer admin-token" \
     "http://localhost:8000/api/v1/config/audit?hours=24"

# Check suspicious activity
curl -H "Authorization: Bearer security-token" \
     http://localhost:8000/api/v1/security/alerts?severity=high
```

### 3. Prometheus Metrics

Access security metrics at `http://localhost:8000/api/v1/security/metrics`:

```
config_security_score                    # 0-100 security health score
config_violations_critical               # Critical security violations
access_requests_failed_last_hour         # Failed access attempts
security_alerts_total                    # Active security alerts
```

## 🛡️ Configuration Security

### Secure Parameter Access

```python
# Example: Securely access configuration
import requests

headers = {"Authorization": "Bearer developer-token"}

# List accessible parameters (filtered by role)
response = requests.get(
    "http://localhost:8000/api/v1/config/parameters",
    headers=headers
)

# Get specific parameter (with security redaction)
response = requests.get(
    "http://localhost:8000/api/v1/config/parameters/database_url",
    headers=headers
)
# Returns: {"value": "[REDACTED]", "security_level": "secret"}
```

### Update Configuration Securely

```python
# Validate changes before applying
validation = requests.post(
    "http://localhost:8000/api/v1/config/security/validate",
    headers={"Authorization": "Bearer operator-token"},
    json={
        "worker_timeout": 180,
        "cache_ttl": 7200
    }
)

if validation.json()["overall_valid"]:
    # Apply changes
    response = requests.put(
        "http://localhost:8000/api/v1/config/parameters/worker_timeout",
        headers=headers,
        json={"value": 180}
    )
```

## 🔄 Secret Rotation

### Automatic Gemini API Key Rotation

The service automatically rotates Gemini API keys every 24 hours:

```bash
# Check rotation status
curl -H "Authorization: Bearer admin-token" \
     http://localhost:8000/api/v1/security/secret-rotation/status

# Force immediate rotation (emergency)
curl -X POST -H "Authorization: Bearer admin-token" \
     http://localhost:8000/api/v1/security/rotate-gemini-keys
```

### Manual Secret Rotation

```python
from pattern_mining.security.secret_rotation import SecretRotationManager

# Initialize manager
rotation_manager = SecretRotationManager(settings, redis_client, secret_manager)

# Rotate specific secret
await rotation_manager.rotate_secret("gemini_api_key")
```

## 🚨 Emergency Procedures

### 1. Suspected Compromise

```bash
# Force rotate all secrets
curl -X POST -H "Authorization: Bearer admin-token" \
     http://localhost:8000/api/v1/security/emergency/rotate-all

# Lock down configuration
curl -X POST -H "Authorization: Bearer admin-token" \
     http://localhost:8000/api/v1/security/emergency/lockdown
```

### 2. Reset Configuration

```bash
# Reset to secure defaults
curl -X POST -H "Authorization: Bearer admin-token" \
     http://localhost:8000/api/v1/security/reset-config
```

### 3. Export Audit Logs

```bash
# Export last 7 days of audit logs
curl -H "Authorization: Bearer admin-token" \
     "http://localhost:8000/api/v1/config/audit/export?days=7" \
     -o audit_logs.json
```

## ✅ Security Checklist

Before deploying:

- [ ] Changed all default passwords in `.env`
- [ ] Enabled configuration access control
- [ ] Configured audit log retention (compliance)
- [ ] Set up secret rotation for API keys
- [ ] Tested authentication with each role
- [ ] Verified security dashboard access
- [ ] Configured monitoring alerts
- [ ] Reviewed security audit results
- [ ] Documented emergency contacts

## 📚 Additional Resources

- [Full Security Documentation](docs/security-configuration.md)
- [Configuration Parameter Reference](CONFIGURATION_REFERENCE.md)
- [Security Architecture](docs/security/README.md)
- [Compliance Guide](docs/compliance/README.md)

## 🆘 Support

- **Security Issues**: Report immediately to security team
- **Configuration Help**: See [Configuration Guide](docs/configuration-guide.md)
- **Emergency**: Follow [Incident Response Plan](docs/security/incident-response.md)

---

**Remember**: Security is everyone's responsibility. Always follow the principle of least privilege and report any suspicious activity immediately.