# 📖 Pattern Mining Configuration Reference

Complete reference for all 165+ configuration parameters in the Pattern Mining service.

## 📋 Table of Contents

1. [Application Settings](#application-settings)
2. [Security Configuration](#security-configuration)
3. [Database Configuration](#database-configuration)
4. [Redis Configuration](#redis-configuration)
5. [Google Cloud Configuration](#google-cloud-configuration)
6. [ML/AI Configuration](#mlai-configuration)
7. [Performance Configuration](#performance-configuration)
8. [Cache Configuration](#cache-configuration)
9. [Monitoring Configuration](#monitoring-configuration)
10. [Configuration Access Control](#configuration-access-control)

## Parameter Security Levels

Each parameter has a security classification:

- 🟢 **PUBLIC**: Safe to log and expose
- 🟡 **INTERNAL**: Internal use, can log but not expose externally
- 🟠 **SENSITIVE**: Should not be logged or exposed
- 🔴 **SECRET**: Must be encrypted, never logged

---

## Application Settings

### Basic Application Configuration

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `app_name` | string | `pattern-mining` | 🟢 PUBLIC | Application identifier |
| `app_version` | string | `1.0.0` | 🟢 PUBLIC | Application version |
| `environment` | string | `development` | 🟢 PUBLIC | Environment (development/staging/production) |
| `debug` | boolean | `false` | 🟡 INTERNAL | Debug mode flag |

### Server Configuration

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `host` | string | `0.0.0.0` | 🟡 INTERNAL | Server bind host |
| `port` | integer | `8000` | 🟡 INTERNAL | Server port (1024-65535) |
| `workers` | integer | `1` | 🟡 INTERNAL | Number of worker processes |
| `max_workers` | integer | `4` | 🟡 INTERNAL | Maximum worker processes |
| `worker_connections` | integer | `1000` | 🟡 INTERNAL | Max connections per worker |
| `worker_timeout` | integer | `120` | 🟡 INTERNAL | Worker timeout in seconds |
| `timeout` | integer | `120` | 🟡 INTERNAL | Request timeout |
| `keepalive` | integer | `5` | 🟡 INTERNAL | Keep-alive timeout |
| `graceful_timeout` | integer | `30` | 🟡 INTERNAL | Graceful shutdown timeout |

---

## Security Configuration

### Authentication & Authorization

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `secret_key` | string | *required* | 🔴 SECRET | Session encryption key (min 32 chars) |
| `jwt_secret` | string | *required* | 🔴 SECRET | JWT signing secret (min 32 chars) |
| `cors_origins` | list | `["*"]` | 🟠 SENSITIVE | Allowed CORS origins |

### Rate Limiting

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `rate_limit_per_minute` | integer | `100` | 🟡 INTERNAL | Requests per minute limit |
| `rate_limit_burst` | integer | `20` | 🟡 INTERNAL | Burst request allowance |
| `api_rate_limit` | integer | `100` | 🟡 INTERNAL | API-specific rate limit |

---

## Database Configuration

### PostgreSQL Settings

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `database_url` | string | *required* | 🔴 SECRET | PostgreSQL connection string |
| `database_pool_size` | integer | `10` | 🟡 INTERNAL | Connection pool size |
| `database_max_overflow` | integer | `20` | 🟡 INTERNAL | Max overflow connections |
| `database_echo` | boolean | `false` | 🟠 SENSITIVE | Log SQL queries |

---

## Redis Configuration

### Basic Redis Settings

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `redis_url` | string | `redis://localhost:6379` | 🔴 SECRET | Redis connection URL |
| `redis_password` | string | `None` | 🔴 SECRET | Redis password |
| `redis_max_connections` | integer | `50` | 🟡 INTERNAL | Max Redis connections |
| `redis_max_connections_per_node` | integer | `10` | 🟡 INTERNAL | Max connections per node |
| `redis_connection_timeout` | float | `5.0` | 🟡 INTERNAL | Connection timeout (seconds) |
| `redis_socket_timeout` | float | `5.0` | 🟡 INTERNAL | Socket timeout (seconds) |
| `redis_socket_keepalive` | boolean | `true` | 🟡 INTERNAL | Enable socket keep-alive |

### Redis Advanced Settings

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `redis_health_check_interval` | float | `30.0` | 🟡 INTERNAL | Health check interval |
| `redis_retry_on_timeout` | boolean | `true` | 🟡 INTERNAL | Retry on timeout |
| `redis_max_retries` | integer | `3` | 🟡 INTERNAL | Maximum retry attempts |
| `redis_retry_backoff_factor` | float | `1.0` | 🟡 INTERNAL | Retry backoff factor |
| `redis_decode_responses` | boolean | `true` | 🟡 INTERNAL | Decode responses to strings |
| `redis_db` | integer | `0` | 🟡 INTERNAL | Redis database number |

### Redis Security

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `redis_ssl` | boolean | `false` | 🟠 SENSITIVE | Enable SSL/TLS |
| `redis_ssl_ca_certs` | string | `None` | 🟠 SENSITIVE | CA certificates path |
| `redis_ssl_cert_reqs` | string | `required` | 🟠 SENSITIVE | SSL certificate requirements |

### Redis Clustering

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `redis_cluster_mode` | boolean | `false` | 🟡 INTERNAL | Enable cluster mode |
| `redis_cluster_nodes` | list | `[]` | 🟡 INTERNAL | Cluster node addresses |
| `redis_cluster_skip_full_coverage_check` | boolean | `false` | 🟡 INTERNAL | Skip coverage check |
| `redis_cluster_readonly_mode` | boolean | `false` | 🟡 INTERNAL | Read-only cluster mode |

### Redis Sentinel

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `redis_sentinel_mode` | boolean | `false` | 🟡 INTERNAL | Enable Sentinel mode |
| `redis_sentinel_service_name` | string | `mymaster` | 🟡 INTERNAL | Sentinel service name |
| `redis_sentinel_password` | string | `None` | 🔴 SECRET | Sentinel password |

### Redis Caching

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `redis_cache_key_prefix` | string | `pattern_mining:` | 🟡 INTERNAL | Cache key prefix |
| `redis_cache_compression_threshold` | integer | `1024` | 🟡 INTERNAL | Compression threshold (bytes) |
| `redis_cache_default_ttl` | integer | `3600` | 🟡 INTERNAL | Default TTL (seconds) |
| `redis_cache_max_key_length` | integer | `250` | 🟡 INTERNAL | Max key length |
| `redis_cache_serialization_format` | string | `json` | 🟡 INTERNAL | Serialization format |

---

## Google Cloud Configuration

### GCP Project Settings

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `gcp_project_id` | string | *required* | 🟠 SENSITIVE | GCP project ID |
| `gcp_location` | string | `us-central1` | 🟡 INTERNAL | Default GCP location |
| `google_application_credentials` | string | `None` | 🔴 SECRET | Service account key path |

### BigQuery Configuration

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `bigquery_dataset_id` | string | *required* | 🟠 SENSITIVE | BigQuery dataset ID |
| `bigquery_location` | string | `US` | 🟡 INTERNAL | BigQuery location |
| `bigquery_table_prefix` | string | `pattern_mining` | 🟡 INTERNAL | Table name prefix |
| `bigquery_max_connections` | integer | `10` | 🟡 INTERNAL | Max concurrent connections |
| `bigquery_connection_timeout` | integer | `30` | 🟡 INTERNAL | Connection timeout |
| `bigquery_read_timeout` | integer | `300` | 🟡 INTERNAL | Read timeout |
| `bigquery_max_retry_attempts` | integer | `3` | 🟡 INTERNAL | Max retry attempts |
| `bigquery_dry_run` | boolean | `false` | 🟡 INTERNAL | Dry run mode |
| `bigquery_use_cache` | boolean | `true` | 🟡 INTERNAL | Enable query cache |
| `bigquery_max_bytes_billed` | integer | `None` | 🟠 SENSITIVE | Max bytes billed limit |
| `bigquery_max_results` | integer | `10000` | 🟡 INTERNAL | Max query results |
| `bigquery_job_timeout` | integer | `300` | 🟡 INTERNAL | Job timeout (seconds) |

### Gemini API Configuration

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `gemini_api_key` | string | *required* | 🔴 SECRET | Gemini API key |
| `gemini_model` | string | `gemini-2.5-flash` | 🟡 INTERNAL | Gemini model name |
| `gemini_requests_per_minute` | integer | `60` | 🟡 INTERNAL | API rate limit |
| `gemini_tokens_per_minute` | integer | `1000000` | 🟡 INTERNAL | Token rate limit |

---

## ML/AI Configuration

### Model Settings

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `ml_model_storage_path` | string | `/tmp/models` | 🟠 SENSITIVE | Model storage directory |
| `ml_batch_size` | integer | `32` | 🟡 INTERNAL | Batch processing size |
| `ml_max_sequence_length` | integer | `512` | 🟡 INTERNAL | Max sequence length |
| `embedding_model` | string | `all-MiniLM-L6-v2` | 🟡 INTERNAL | Embedding model name |
| `embedding_dimension` | integer | `384` | 🟡 INTERNAL | Embedding dimensions |

### GPU Configuration

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `enable_gpu` | boolean | `false` | 🟡 INTERNAL | Enable GPU acceleration |
| `gpu_enabled` | boolean | `false` | 🟡 INTERNAL | GPU enabled flag |
| `enable_distributed` | boolean | `false` | 🟡 INTERNAL | Enable distributed processing |

---

## Performance Configuration

### Request Handling

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `max_request_size` | integer | `10485760` | 🟡 INTERNAL | Max request size (bytes) |
| `response_timeout` | integer | `25` | 🟡 INTERNAL | Response timeout (seconds) |
| `max_batch_size` | integer | `100` | 🟡 INTERNAL | Max batch size |
| `batch_timeout_seconds` | integer | `3600` | 🟡 INTERNAL | Batch timeout |

### Pattern Detection

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `max_patterns_per_request` | integer | `100` | 🟡 INTERNAL | Max patterns per request |
| `pattern_confidence_threshold` | float | `0.7` | 🟡 INTERNAL | Confidence threshold |
| `enable_streaming` | boolean | `true` | 🟡 INTERNAL | Enable streaming responses |

### WebSocket Configuration

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `websocket_max_connections` | integer | `1000` | 🟡 INTERNAL | Max WebSocket connections |
| `websocket_heartbeat_interval` | integer | `30` | 🟡 INTERNAL | Heartbeat interval (seconds) |

---

## Cache Configuration

### Basic Cache Settings

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `enable_caching` | boolean | `true` | 🟡 INTERNAL | Enable caching |
| `cache_ttl` | integer | `3600` | 🟡 INTERNAL | Cache TTL (seconds) |
| `cache_ttl_seconds` | integer | `3600` | 🟡 INTERNAL | Cache TTL alternate |
| `cache_max_size` | integer | `1000` | 🟡 INTERNAL | Max cache entries |

### Multi-Level Cache

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `cache_l1_max_size` | integer | `1000` | 🟡 INTERNAL | L1 cache size |
| `cache_l1_ttl` | integer | `3600` | 🟡 INTERNAL | L1 cache TTL |
| `cache_l2_ttl` | integer | `7200` | 🟡 INTERNAL | L2 cache TTL |
| `cache_promotion_threshold` | integer | `2` | 🟡 INTERNAL | Promotion threshold |

### Cache Warming

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `cache_warmup_enabled` | boolean | `true` | 🟡 INTERNAL | Enable cache warmup |
| `cache_warmup_concurrency` | integer | `10` | 🟡 INTERNAL | Warmup concurrency |

### Distributed Cache

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `distributed_cache_enabled` | boolean | `false` | 🟡 INTERNAL | Enable distributed cache |
| `distributed_cache_replication_factor` | integer | `2` | 🟡 INTERNAL | Replication factor |
| `distributed_cache_consistency_level` | string | `eventual` | 🟡 INTERNAL | Consistency level |
| `distributed_cache_read_timeout` | float | `3.0` | 🟡 INTERNAL | Read timeout |
| `distributed_cache_write_timeout` | float | `5.0` | 🟡 INTERNAL | Write timeout |
| `distributed_cache_read_repair` | boolean | `true` | 🟡 INTERNAL | Enable read repair |

### Cache Monitoring

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `cache_monitoring_enabled` | boolean | `true` | 🟡 INTERNAL | Enable monitoring |
| `cache_monitoring_interval` | float | `30.0` | 🟡 INTERNAL | Monitoring interval |
| `cache_monitoring_retention_days` | integer | `7` | 🟡 INTERNAL | Data retention days |
| `cache_alerting_enabled` | boolean | `true` | 🟡 INTERNAL | Enable alerts |
| `cache_alert_cooldown` | integer | `300` | 🟡 INTERNAL | Alert cooldown |

---

## Monitoring Configuration

### Metrics & Tracing

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `enable_metrics` | boolean | `true` | 🟡 INTERNAL | Enable metrics collection |
| `metrics_port` | integer | `9090` | 🟠 SENSITIVE | Metrics server port |
| `enable_tracing` | boolean | `true` | 🟡 INTERNAL | Enable distributed tracing |
| `jaeger_endpoint` | string | `http://localhost:14268` | 🟠 SENSITIVE | Jaeger endpoint |

### Logging

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `log_level` | string | `INFO` | 🟡 INTERNAL | Log level |
| `log_format` | string | `json` | 🟡 INTERNAL | Log format (json/text) |

### Analytics

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `analytics_retention_days` | integer | `90` | 🟡 INTERNAL | Analytics retention |
| `enable_real_time_analytics` | boolean | `true` | 🟡 INTERNAL | Real-time analytics |

---

## Configuration Access Control

### Access Control Settings (NEW - Phase 1.2)

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `config_access_control_enabled` | boolean | `true` | 🟡 INTERNAL | Enable RBAC for config |
| `config_audit_retention_days` | integer | `90` | 🟡 INTERNAL | Audit log retention |
| `config_permission_cache_ttl` | integer | `300` | 🟡 INTERNAL | Permission cache TTL |

### Audit Logging

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `config_audit_log_enabled` | boolean | `true` | 🟡 INTERNAL | Enable audit logging |
| `config_audit_log_level` | string | `INFO` | 🟡 INTERNAL | Audit log level |
| `config_suspicious_activity_threshold` | integer | `10` | 🟡 INTERNAL | Suspicious activity threshold |
| `config_failed_access_alert_threshold` | integer | `5` | 🟡 INTERNAL | Failed access alert threshold |

### Security Monitoring

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `config_security_monitoring_enabled` | boolean | `true` | 🟡 INTERNAL | Enable security monitoring |
| `config_security_score_threshold` | integer | `80` | 🟡 INTERNAL | Minimum security score |
| `config_violation_alert_enabled` | boolean | `true` | 🟡 INTERNAL | Enable violation alerts |

### Secret Rotation

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `secret_rotation_enabled` | boolean | `true` | 🟡 INTERNAL | Enable secret rotation |
| `secret_rotation_interval_hours` | integer | `24` | 🟡 INTERNAL | Rotation interval |
| `secret_rotation_grace_period_hours` | integer | `2` | 🟡 INTERNAL | Grace period |
| `gemini_api_key_rotation_enabled` | boolean | `true` | 🟡 INTERNAL | Enable Gemini key rotation |

---

## Development Settings

### Development Mode

| Parameter | Type | Default | Security | Description |
|-----------|------|---------|----------|-------------|
| `hot_reload` | boolean | `true` | 🟡 INTERNAL | Enable hot reload |
| `skip_migrations` | boolean | `false` | 🟡 INTERNAL | Skip DB migrations |
| `preload_models` | boolean | `false` | 🟡 INTERNAL | Preload ML models |

---

## Best Practices

1. **Security Levels**: Never log or expose parameters marked as SECRET
2. **Environment Variables**: Use environment variables for all sensitive values
3. **Secret Management**: Use Google Secret Manager or similar in production
4. **Validation**: All parameters are validated on startup
5. **Access Control**: Use role-based access to limit parameter visibility
6. **Audit Logging**: All configuration changes are logged
7. **Rotation**: Enable automatic rotation for all API keys and secrets

## Configuration Validation

Run the configuration security test to validate your setup:

```bash
python test_config_security.py
```

This will check:
- ✅ Parameter validation
- ✅ Access control rules
- ✅ Audit logging
- ✅ Security compliance

---

**Last Updated**: Phase 1.2 Security Audit Completion
**Total Parameters**: 165+
**Security Classifications**: PUBLIC (25%), INTERNAL (50%), SENSITIVE (15%), SECRET (10%)