# Pattern Mining Developer Guide

This guide provides comprehensive instructions for developing and contributing to the Pattern Mining service.

## Prerequisites

### Required Software
- Python 3.11 or higher
- Docker and Docker Compose
- Git
- Google Cloud SDK (gcloud)
- Redis (for local development)

### Recommended Tools
- Poetry (for dependency management)
- pyenv (for Python version management)
- VS Code or PyCharm
- pre-commit hooks

## Development Setup

### 1. Clone the Repository
```bash
git clone https://github.com/episteme/pattern-mining.git
cd pattern-mining
```

### 2. Set Up Python Environment
```bash
# Using pyenv (recommended)
pyenv install 3.11.7
pyenv local 3.11.7

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

### 3. Configure Environment
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
# Required variables:
# - GEMINI_API_KEY: Your Gemini API key
# - GCP_PROJECT_ID: Your GCP project ID
# - DATABASE_URL: PostgreSQL connection string
```

### 4. Set Up Pre-commit Hooks
```bash
pre-commit install
pre-commit run --all-files  # Test the setup
```

### 5. Start Development Services
```bash
# Start all services
docker-compose up -d

# Or start specific services
docker-compose up -d redis postgres

# Start the FastAPI server
uvicorn src.pattern_mining.main:app --reload --port 8000
```

## Project Structure

```
pattern-mining/
├── src/
│   └── pattern_mining/
│       ├── api/              # API endpoints
│       ├── core/             # Core business logic
│       ├── ml/               # ML/AI components
│       ├── models/           # Data models
│       ├── services/         # External service clients
│       ├── security/         # Security components
│       ├── utils/            # Utility functions
│       └── main.py          # Application entry point
├── tests/
│   ├── unit/                # Unit tests
│   ├── integration/         # Integration tests
│   └── e2e/                 # End-to-end tests
├── scripts/                 # Development scripts
├── docs/                    # Service documentation
└── k8s/                     # Kubernetes manifests
```

## Development Workflow

### 1. Creating a New Feature

```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make changes
# Write tests
# Update documentation

# Run tests locally
pytest tests/unit -v
pytest tests/integration -v

# Run linting
ruff check .
black .
mypy .

# Commit with conventional commits
git add .
git commit -m "feat: add new pattern detection algorithm"
```

### 2. Writing Tests

#### Unit Test Example
```python
# tests/unit/test_pattern_detector.py
import pytest
from pattern_mining.core.detector import PatternDetector

class TestPatternDetector:
    @pytest.fixture
    def detector(self):
        return PatternDetector()
    
    def test_detect_singleton_pattern(self, detector):
        code = '''
        class Singleton:
            _instance = None
            
            def __new__(cls):
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                return cls._instance
        '''
        
        patterns = detector.detect(code, language="python")
        assert any(p.type == "singleton" for p in patterns)
```

#### Integration Test Example
```python
# tests/integration/test_gemini_integration.py
import pytest
from pattern_mining.services.gemini import SecureGeminiClient

@pytest.mark.integration
class TestGeminiIntegration:
    @pytest.fixture
    def client(self):
        return SecureGeminiClient()
    
    async def test_analyze_code_snippet(self, client):
        result = await client.analyze_code(
            code="def hello(): print('world')",
            language="python"
        )
        assert result.patterns is not None
```

### 3. API Development

#### Adding a New Endpoint
```python
# src/pattern_mining/api/v1/patterns.py
from fastapi import APIRouter, Depends, HTTPException
from pattern_mining.models import PatternRequest, PatternResponse
from pattern_mining.core.detector import PatternDetector
from pattern_mining.security.auth import get_current_user

router = APIRouter()

@router.post("/detect", response_model=PatternResponse)
async def detect_patterns(
    request: PatternRequest,
    current_user: dict = Depends(get_current_user),
    detector: PatternDetector = Depends()
):
    """Detect patterns in provided code."""
    try:
        patterns = await detector.analyze(
            code=request.code,
            language=request.language,
            options=request.options
        )
        return PatternResponse(patterns=patterns)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

### 4. Working with ML Models

#### Using Gemini API
```python
# src/pattern_mining/ml/gemini_analyzer.py
from pattern_mining.services.gemini import SecureGeminiClient
from pattern_mining.security.prompt_guard import PromptGuard

class GeminiAnalyzer:
    def __init__(self):
        self.client = SecureGeminiClient()
        self.guard = PromptGuard()
    
    async def analyze_patterns(self, code: str, context: dict) -> dict:
        # Validate prompt for security
        prompt = self._build_prompt(code, context)
        if not self.guard.is_safe(prompt):
            raise ValueError("Potentially unsafe prompt detected")
        
        # Call Gemini API
        response = await self.client.generate(
            prompt=prompt,
            model="gemini-2.5-flash",
            temperature=0.1
        )
        
        # Validate response
        if not self.guard.is_safe_response(response):
            raise ValueError("Suspicious response detected")
        
        return self._parse_response(response)
```

## Configuration Management

### Environment Variables
The service uses a hierarchical configuration system:

```python
# src/pattern_mining/config/settings.py
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # API Configuration
    api_prefix: str = "/api/v1"
    debug: bool = False
    
    # Security
    jwt_secret: str
    jwt_algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # ML/AI
    gemini_api_key: str
    gemini_model: str = "gemini-2.5-flash"
    max_prompt_length: int = 10000
    
    # Cache
    redis_url: str = "redis://localhost:6379"
    cache_ttl: int = 3600
    
    class Config:
        env_file = ".env"
```

### Security Levels
Configuration parameters have different security levels:
- **PUBLIC**: Can be exposed in logs
- **INTERNAL**: Internal use only
- **SENSITIVE**: Must be masked in logs
- **SECRET**: Never log, rotate regularly

## Performance Optimization

### 1. Caching Strategy
```python
from pattern_mining.cache import CacheManager

class PatternService:
    def __init__(self):
        self.cache = CacheManager()
    
    async def get_patterns(self, repo_id: str) -> List[Pattern]:
        # Try L1 cache (in-memory)
        patterns = self.cache.get_l1(f"patterns:{repo_id}")
        if patterns:
            return patterns
        
        # Try L2 cache (Redis)
        patterns = await self.cache.get_l2(f"patterns:{repo_id}")
        if patterns:
            self.cache.set_l1(f"patterns:{repo_id}", patterns)
            return patterns
        
        # Compute and cache
        patterns = await self._compute_patterns(repo_id)
        await self.cache.set_all_tiers(f"patterns:{repo_id}", patterns)
        return patterns
```

### 2. Batch Processing
```python
from ray import serve
import ray

@serve.deployment(num_replicas=3)
class PatternBatchProcessor:
    async def process_batch(self, files: List[str]) -> dict:
        # Distribute work across Ray cluster
        futures = []
        for chunk in self._chunk_files(files, size=100):
            future = self._process_chunk.remote(chunk)
            futures.append(future)
        
        # Gather results
        results = await ray.get(futures)
        return self._merge_results(results)
```

## Security Best Practices

### 1. Input Validation
```python
from pydantic import BaseModel, validator
import re

class CodeInput(BaseModel):
    code: str
    language: str
    
    @validator('code')
    def validate_code_safety(cls, v):
        # Check for potential security issues
        if len(v) > 1_000_000:  # 1MB limit
            raise ValueError("Code too large")
        
        # Check for suspicious patterns
        suspicious_patterns = [
            r'exec\s*\(',
            r'eval\s*\(',
            r'__import__',
            r'subprocess',
        ]
        for pattern in suspicious_patterns:
            if re.search(pattern, v, re.IGNORECASE):
                raise ValueError(f"Suspicious pattern detected: {pattern}")
        
        return v
```

### 2. API Security
```python
from fastapi import Security, HTTPException
from pattern_mining.security.permissions import check_permission

async def require_permission(permission: str):
    async def permission_checker(
        current_user: dict = Depends(get_current_user)
    ):
        if not check_permission(current_user, permission):
            raise HTTPException(
                status_code=403,
                detail=f"Permission '{permission}' required"
            )
        return current_user
    return permission_checker

# Usage
@router.post("/admin/rotate-keys")
async def rotate_keys(
    user = Depends(require_permission("security.rotate_keys"))
):
    # Implementation
```

## Debugging Tips

### 1. Enable Debug Logging
```python
import structlog

logger = structlog.get_logger()

# In your code
logger.debug("Processing pattern detection", 
    repo_id=repo_id, 
    file_count=len(files)
)
```

### 2. Use Debug Endpoints
```bash
# Get service health
curl http://localhost:8000/health

# Get debug metrics
curl http://localhost:8000/debug/metrics

# Profile a slow endpoint
curl http://localhost:8000/debug/profile?endpoint=/api/v1/patterns/analyze
```

### 3. Local Debugging with VS Code
```json
// .vscode/launch.json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug FastAPI",
            "type": "python",
            "request": "launch",
            "module": "uvicorn",
            "args": [
                "src.pattern_mining.main:app",
                "--reload",
                "--port", "8000"
            ],
            "env": {
                "PYTHONPATH": "${workspaceFolder}/src"
            }
        }
    ]
}
```

## Common Issues

### 1. Gemini API Rate Limits
```python
# Implement exponential backoff
from tenacity import retry, wait_exponential, stop_after_attempt

@retry(
    wait=wait_exponential(multiplier=1, min=4, max=60),
    stop=stop_after_attempt(5)
)
async def call_gemini_api(prompt: str):
    # API call implementation
```

### 2. Memory Issues with Large Repos
```python
# Use streaming processing
async def analyze_large_repo(repo_path: str):
    async for file_batch in stream_files(repo_path, batch_size=100):
        results = await process_batch(file_batch)
        yield results
```

### 3. Redis Connection Issues
```python
# Implement connection pooling
from redis.asyncio import ConnectionPool

pool = ConnectionPool(
    host='localhost',
    port=6379,
    max_connections=50,
    retry_on_timeout=True
)
```

## Contributing Guidelines

1. **Code Style**: Follow PEP 8 and use Black for formatting
2. **Testing**: Maintain >90% test coverage
3. **Documentation**: Update docs for any API changes
4. **Commits**: Use conventional commit format
5. **Reviews**: All PRs require approval from 2 reviewers

## Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Google Gemini API](https://ai.google.dev/gemini-api/docs)
- [Ray Documentation](https://docs.ray.io/)
- [Redis Vector Similarity](https://redis.io/docs/stack/search/reference/vectors/)
- [Project Contributing Guide](../../CONTRIBUTING.md)