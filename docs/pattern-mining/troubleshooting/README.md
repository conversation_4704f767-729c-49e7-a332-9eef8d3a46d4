# Pattern Mining Troubleshooting Guide

This guide helps diagnose and resolve common issues with the Pattern Mining service.

## Quick Diagnostics

### Service Health Check
```bash
# Check if service is responding
curl -f https://pattern-mining.ccl-platform.com/health || echo "Service is down"

# Get detailed health status
curl -H "Authorization: Bearer $TOKEN" \
    https://pattern-mining.ccl-platform.com/health/detailed
```

### Common Symptoms and Solutions

| Symptom | Likely Cause | Quick Fix |
|---------|--------------|-----------|
| 500 errors | Service crash | Restart service |
| 429 errors | Rate limiting | Reduce request rate |
| Timeout errors | Long processing | Check Ray cluster |
| Empty responses | Cache issues | Clear Redis cache |
| Auth failures | Token expired | Refresh JWT token |

## Detailed Troubleshooting

### 1. Service Won't Start

#### Symptoms
- Container crashes immediately
- Health check fails continuously
- "Failed to start" in logs

#### Diagnosis
```bash
# Check recent logs
gcloud logging read \
    'resource.labels.service_name="pattern-mining" AND severity>=ERROR' \
    --limit=50 \
    --format="table(timestamp,jsonPayload.error)"

# Check environment variables
gcloud run services describe pattern-mining \
    --format="yaml(spec.template.spec.containers[0].env[].name)"

# Verify secrets are accessible
gcloud secrets versions access latest --secret=gemini-api-key
```

#### Common Causes and Solutions

**Missing Environment Variables**
```bash
# List required variables
cat .env.example | grep REQUIRED

# Set missing variables
gcloud run services update pattern-mining \
    --set-env-vars="MISSING_VAR=value"
```

**Database Connection Failed**
```bash
# Test database connection
gcloud sql connect pattern-mining-db --user=postgres

# Check connection string format
echo $DATABASE_URL  # Should be: ********************************/dbname

# Verify Cloud SQL proxy is running
ps aux | grep cloud_sql_proxy
```

**Invalid Gemini API Key**
```bash
# Test API key
curl -X POST https://generativelanguage.googleapis.com/v1/models/gemini-2.5-flash:generateContent \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $GEMINI_API_KEY" \
    -d '{"contents": [{"parts": [{"text": "test"}]}]}'

# Rotate if needed
gcloud secrets versions add gemini-api-key --data-file=new-key.txt
```

### 2. High Error Rate

#### Symptoms
- Error rate > 5%
- Many 5xx responses
- Degraded performance

#### Diagnosis
```bash
# Get error breakdown
gcloud logging read \
    'resource.labels.service_name="pattern-mining" AND severity>=ERROR' \
    --format="table(jsonPayload.error_code)" \
    | sort | uniq -c | sort -rn

# Check specific error patterns
gcloud logging read \
    'resource.labels.service_name="pattern-mining" AND textPayload:"TimeoutError"' \
    --limit=10
```

#### Common Error Types

**Gemini API Errors**
```python
# Error: "ResourceExhausted: Quota exceeded"
# Solution: Implement exponential backoff

# Add to code:
from tenacity import retry, wait_exponential, stop_after_attempt

@retry(
    wait=wait_exponential(multiplier=1, min=4, max=60),
    stop=stop_after_attempt(5)
)
async def call_gemini_with_retry(prompt):
    return await gemini_client.generate(prompt)
```

**Memory Errors**
```bash
# Error: "Cannot allocate memory"
# Check memory usage
gcloud monitoring read \
    --filter='metric.type="run.googleapis.com/container/memory/utilization"' \
    --format="table(points[0].value.double_value)"

# Increase memory limit
gcloud run services update pattern-mining --memory=8Gi
```

**Database Connection Pool Exhausted**
```python
# Error: "TimeoutError: QueuePool limit exceeded"
# Increase pool size
DATABASE_POOL_SIZE=50
DATABASE_MAX_OVERFLOW=100

# Or fix connection leaks
# Ensure all sessions are closed:
async with get_db() as session:
    # operations
    # session auto-closes
```

### 3. Slow Performance

#### Symptoms
- Response time > 1s
- Queue backing up
- Users complaining

#### Diagnosis
```bash
# Check latency percentiles
gcloud monitoring read \
    --filter='metric.type="run.googleapis.com/request_latencies"' \
    --format="table(metric.labels.response_code_class,points[0].value.distribution_value.mean)"

# Profile slow endpoints
curl https://pattern-mining.ccl-platform.com/debug/profile?duration=30

# Check cache hit rate
redis-cli -h $REDIS_HOST INFO stats | grep hit_rate
```

#### Performance Optimizations

**Low Cache Hit Rate**
```bash
# Check cache configuration
redis-cli -h $REDIS_HOST CONFIG GET maxmemory

# Increase cache size if needed
gcloud redis instances update pattern-mining-cache --size=20

# Warm cache with common queries
python scripts/cache_warmer.py --patterns=common
```

**Ray Cluster Bottleneck**
```bash
# Check Ray cluster status
ray status --address=$RAY_ADDRESS

# Scale workers
ray submit --address=$RAY_ADDRESS \
    scale_cluster.py --num-workers=50

# Check task queue
ray queue size --address=$RAY_ADDRESS
```

**Database Query Optimization**
```sql
-- Find slow queries
SELECT query, mean_time_ms, call_count
FROM pg_stat_statements
WHERE mean_time_ms > 100
ORDER BY mean_time_ms DESC
LIMIT 10;

-- Add missing indexes
CREATE INDEX CONCURRENTLY idx_patterns_repo_id 
ON patterns(repository_id) 
WHERE deleted_at IS NULL;
```

### 4. Authentication Issues

#### Symptoms
- 401 Unauthorized errors
- "Invalid token" messages
- Permission denied errors

#### Diagnosis
```bash
# Decode JWT token
echo $TOKEN | cut -d. -f2 | base64 -d | jq .

# Check token expiration
echo $TOKEN | cut -d. -f2 | base64 -d | jq .exp | xargs -I {} date -d @{}

# Verify JWT secret is correct
gcloud secrets versions access latest --secret=jwt-secret
```

#### Common Auth Issues

**Expired Token**
```bash
# Generate new token
curl -X POST https://pattern-mining.ccl-platform.com/auth/token \
    -H "Content-Type: application/json" \
    -d '{"username": "user", "password": "pass"}'
```

**Invalid Permissions**
```python
# Check user roles
GET /api/v1/users/me
# Response: {"roles": ["developer"], "permissions": ["read:patterns"]}

# Required permission missing
# Contact admin to grant permission
```

**CORS Issues**
```javascript
// Error: "CORS policy blocked"
// Add to backend:
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://app.example.com"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### 5. Data Inconsistencies

#### Symptoms
- Missing patterns
- Duplicate results
- Stale data

#### Diagnosis
```bash
# Check data freshness
bq query --use_legacy_sql=false \
    "SELECT MAX(updated_at) as last_update FROM pattern_mining.patterns"

# Verify cache coherence
python scripts/verify_cache_consistency.py

# Check for orphaned jobs
SELECT COUNT(*) FROM analysis_jobs 
WHERE status = 'processing' 
AND updated_at < NOW() - INTERVAL '1 hour';
```

#### Data Fixes

**Cache Invalidation Issues**
```python
# Force cache clear for specific pattern
redis-cli -h $REDIS_HOST DEL "pattern:12345"

# Clear all caches for a repository
redis-cli -h $REDIS_HOST --scan --pattern "repo:abc123:*" | \
    xargs redis-cli -h $REDIS_HOST DEL
```

**Stuck Processing Jobs**
```bash
# Find stuck jobs
python scripts/find_stuck_jobs.py --older-than=2h

# Reset stuck jobs
UPDATE analysis_jobs 
SET status = 'failed', 
    error = 'Timeout after 2 hours' 
WHERE status = 'processing' 
AND updated_at < NOW() - INTERVAL '2 hours';
```

### 6. Memory Leaks

#### Symptoms
- Gradually increasing memory usage
- OOM kills
- Performance degradation over time

#### Diagnosis
```bash
# Monitor memory over time
watch -n 60 'gcloud monitoring read \
    --filter="metric.type=\"run.googleapis.com/container/memory/utilization\"" \
    --format="table(points[0].value.double_value,points[0].interval.end_time)"'

# Get memory profile
kubectl exec -it pattern-mining-pod -- \
    python -m memory_profiler scripts/memory_snapshot.py

# Check for growing objects
kubectl exec -it pattern-mining-pod -- \
    python -c "import gc; print(gc.get_stats())"
```

#### Memory Leak Fixes

**Connection Leaks**
```python
# Bad: Connection not closed
def get_data():
    conn = create_connection()
    return conn.query("SELECT * FROM patterns")

# Good: Using context manager
def get_data():
    with create_connection() as conn:
        return conn.query("SELECT * FROM patterns")
```

**Cache Growth**
```python
# Add size limits to in-memory cache
from cachetools import LRUCache

# Limit to 1000 items
pattern_cache = LRUCache(maxsize=1000)
```

### 7. External Service Issues

#### Gemini API Issues
```bash
# Check API status
curl https://status.cloud.google.com/incidents.json | \
    jq '.[] | select(.service_name == "Google AI")'

# Test with different model
gcloud run services update pattern-mining \
    --set-env-vars="GEMINI_MODEL=gemini-pro"

# Enable fallback mode
gcloud run services update pattern-mining \
    --set-env-vars="ENABLE_GEMINI_FALLBACK=true"
```

#### BigQuery Issues
```bash
# Check quota usage
bq show --project_id=$PROJECT_ID | grep -A5 "Quota"

# Test query
bq query --use_legacy_sql=false "SELECT 1"

# Switch to batch priority for non-urgent queries
UPDATE bigquery_config SET priority = 'BATCH' WHERE query_type = 'analytics';
```

## Debug Tools

### Enable Debug Mode
```bash
# Turn on debug logging
gcloud run services update pattern-mining \
    --set-env-vars="LOG_LEVEL=DEBUG,DEBUG_MODE=true"

# Access debug endpoints
curl https://pattern-mining.ccl-platform.com/debug/config
curl https://pattern-mining.ccl-platform.com/debug/connections
curl https://pattern-mining.ccl-platform.com/debug/cache-stats
```

### Performance Profiling
```python
# Add to code for profiling
from pyinstrument import Profiler

profiler = Profiler()
profiler.start()
# ... code to profile ...
profiler.stop()
print(profiler.output_text(unicode=True, color=True))
```

### Request Tracing
```bash
# Enable distributed tracing
gcloud run services update pattern-mining \
    --set-env-vars="ENABLE_TRACING=true,TRACE_SAMPLE_RATE=0.1"

# View traces in Cloud Trace
gcloud trace traces list --project=$PROJECT_ID
```

## Log Analysis

### Useful Log Queries

```bash
# Find all unique error types
gcloud logging read \
    'resource.labels.service_name="pattern-mining" AND severity>=ERROR' \
    --format="value(jsonPayload.error_type)" | sort | uniq -c

# Get requests by user
gcloud logging read \
    'resource.labels.service_name="pattern-mining" AND jsonPayload.user_id!=""' \
    --format="table(jsonPayload.user_id,jsonPayload.endpoint)" \
    | sort | uniq -c

# Find slow queries
gcloud logging read \
    'resource.labels.service_name="pattern-mining" AND jsonPayload.duration_ms>1000' \
    --format="table(timestamp,jsonPayload.endpoint,jsonPayload.duration_ms)"
```

## Emergency Recovery

### Service Completely Down
```bash
#!/bin/bash
# emergency_restart.sh

# 1. Force restart all instances
gcloud run services update pattern-mining --max-instances=0
sleep 10
gcloud run services update pattern-mining --max-instances=50

# 2. Clear all caches
redis-cli -h $REDIS_HOST FLUSHALL

# 3. Reset circuit breakers
gcloud run services update pattern-mining \
    --set-env-vars="CIRCUIT_BREAKER_RESET=true"

# 4. Deploy last known good version
gcloud run deploy pattern-mining \
    --image=gcr.io/$PROJECT_ID/pattern-mining:stable
```

### Data Recovery
```bash
# Restore from BigQuery backup
bq cp pattern_mining.patterns_backup_20250110 pattern_mining.patterns

# Restore from Spanner backup  
gcloud spanner databases restore pattern-mining \
    --source-backup=pattern-mining-backup-20250110 \
    --instance=pattern-mining-prod
```

## Prevention Tips

1. **Monitor Proactively**
   - Set up alerts before issues occur
   - Review metrics daily
   - Conduct load tests monthly

2. **Maintain Runbooks**
   - Update after each incident
   - Test procedures quarterly
   - Train team on procedures

3. **Implement Safeguards**
   - Circuit breakers for external calls
   - Rate limiting on all endpoints
   - Graceful degradation modes

4. **Regular Maintenance**
   - Update dependencies monthly
   - Clean up old data
   - Optimize slow queries

Remember: Most issues can be prevented with good monitoring and proactive maintenance.