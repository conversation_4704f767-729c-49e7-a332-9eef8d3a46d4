# Pattern Mining API Documentation

The Pattern Mining service provides RESTful APIs for code pattern detection, similarity analysis, and ML-based code insights.

## Base URL

```
Production: https://pattern-mining.ccl-platform.com/api/v1
Development: http://localhost:8000/api/v1
```

## Authentication

All API endpoints require JWT authentication with role-based access control.

```bash
Authorization: Bearer <jwt_token>
```

## API Endpoints

### Pattern Detection

#### Analyze Repository
```http
POST /patterns/analyze
```

Analyzes a code repository for patterns and best practices.

**Request Body:**
```json
{
  "repository_url": "https://github.com/org/repo",
  "branch": "main",
  "languages": ["python", "javascript"],
  "analysis_types": ["patterns", "anomalies", "similarities"],
  "options": {
    "max_file_size": 1048576,
    "exclude_patterns": ["**/test/**", "**/vendor/**"],
    "confidence_threshold": 0.8
  }
}
```

**Response:**
```json
{
  "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "processing",
  "estimated_completion": "2025-01-10T15:30:00Z",
  "patterns_found": 0,
  "webhook_url": "https://example.com/webhook/analysis-complete"
}
```

#### Get Analysis Results
```http
GET /patterns/analysis/{analysis_id}
```

Retrieves results of a pattern analysis.

**Response:**
```json
{
  "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "completed_at": "2025-01-10T15:25:00Z",
  "summary": {
    "files_analyzed": 234,
    "patterns_detected": 45,
    "anomalies_found": 3,
    "similarity_clusters": 12
  },
  "patterns": [
    {
      "id": "pat_123",
      "type": "anti-pattern",
      "name": "Hardcoded Credentials",
      "severity": "critical",
      "confidence": 0.95,
      "occurrences": 2,
      "locations": [
        {
          "file": "src/config.py",
          "line": 45,
          "column": 12
        }
      ],
      "recommendation": "Use environment variables or secret management service"
    }
  ]
}
```

### Code Similarity

#### Find Similar Code
```http
POST /similarity/search
```

Searches for code similar to a given snippet.

**Request Body:**
```json
{
  "code_snippet": "def calculate_total(items):\n    return sum(item.price for item in items)",
  "language": "python",
  "repository_scope": ["org/repo1", "org/repo2"],
  "similarity_threshold": 0.8,
  "max_results": 10
}
```

**Response:**
```json
{
  "matches": [
    {
      "similarity_score": 0.92,
      "repository": "org/repo1",
      "file": "src/utils/calculations.py",
      "line_range": [23, 25],
      "code": "def compute_sum(products):\n    return sum(p.cost for p in products)"
    }
  ]
}
```

### ML Model Operations

#### Train Custom Model
```http
POST /ml/models/train
```

Trains a custom pattern detection model for specific use cases.

**Request Body:**
```json
{
  "model_name": "security-patterns-v1",
  "training_data": {
    "positive_examples": ["repo1", "repo2"],
    "negative_examples": ["repo3"],
    "pattern_types": ["security", "authentication"]
  },
  "hyperparameters": {
    "learning_rate": 0.001,
    "batch_size": 32,
    "epochs": 10
  }
}
```

#### Get Model Metrics
```http
GET /ml/models/{model_id}/metrics
```

Returns performance metrics for a trained model.

### Configuration Management

#### List Configuration Parameters
```http
GET /config/parameters
```

Lists configuration parameters accessible to the current user.

**Query Parameters:**
- `category`: Filter by category (security, ml, cache, etc.)
- `security_level`: Filter by security level (PUBLIC, INTERNAL, SENSITIVE, SECRET)

#### Update Configuration
```http
PUT /config/parameters/{parameter_name}
```

Updates a configuration parameter (requires appropriate permissions).

**Request Body:**
```json
{
  "value": "new_value",
  "reason": "Performance optimization"
}
```

### Security & Monitoring

#### Get Security Status
```http
GET /security/status
```

Returns current security status and metrics.

**Response:**
```json
{
  "status": "healthy",
  "metrics": {
    "blocked_prompts": 0,
    "suspicious_responses": 0,
    "api_key_rotations": 5,
    "configuration_violations": 0,
    "last_security_scan": "2025-01-10T15:30:00Z"
  },
  "active_threats": []
}
```

#### Force Key Rotation
```http
POST /security/rotate-keys
```

Forces immediate rotation of API keys (requires SECURITY_ADMIN role).

## Webhooks

The service supports webhooks for asynchronous operations:

```json
{
  "event": "analysis.completed",
  "timestamp": "2025-01-10T15:25:00Z",
  "data": {
    "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "completed",
    "patterns_found": 45
  }
}
```

## Rate Limiting

- **Default**: 100 requests/minute per user
- **Authenticated**: 1000 requests/minute
- **Enterprise**: Custom limits

## Error Responses

Standard error format:
```json
{
  "error": {
    "code": "INVALID_PARAMETER",
    "message": "The 'language' parameter must be one of: python, javascript, go, rust",
    "details": {
      "parameter": "language",
      "provided": "c++",
      "valid_values": ["python", "javascript", "go", "rust"]
    }
  }
}
```

Common error codes:
- `AUTHENTICATION_REQUIRED`: Missing or invalid JWT token
- `PERMISSION_DENIED`: Insufficient permissions for operation
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `INVALID_PARAMETER`: Invalid request parameter
- `RESOURCE_NOT_FOUND`: Requested resource doesn't exist
- `INTERNAL_ERROR`: Server error (check status page)

## SDK Support

Official SDKs available:
- Python: `pip install ccl-pattern-mining`
- JavaScript/TypeScript: `npm install @ccl/pattern-mining`
- Go: `go get github.com/ccl/pattern-mining-go`

## API Versioning

The API uses URL versioning. Current version: `v1`

Breaking changes will result in a new version (e.g., `v2`). Previous versions will be supported for at least 6 months after a new version is released.