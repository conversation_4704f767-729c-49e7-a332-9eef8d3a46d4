# Pattern Mining Operations Runbook

This runbook provides operational procedures, troubleshooting guides, and emergency response protocols for the Pattern Mining service.

## Service Overview

**Service**: Pattern Mining  
**Type**: ML/AI Microservice  
**Criticality**: High  
**SLA**: 99.9% uptime  
**Recovery Time Objective (RTO)**: 15 minutes  
**Recovery Point Objective (RPO)**: 5 minutes  

## Architecture Summary

```
Components:
- Cloud Run: API service (auto-scaling 2-50 instances)
- Redis: In-memory cache (10GB, 3 replicas)
- BigQuery: Historical cache and analytics
- Spanner: Transactional data (3 nodes)
- Ray Cluster: Distributed processing (5-100 workers)
- Gemini API: AI/ML analysis
```

## On-Call Responsibilities

### Primary On-Call
- Monitor service health and alerts
- Respond to incidents within 5 minutes
- Perform initial triage and mitigation
- Escalate to secondary if needed

### Secondary On-Call
- Support primary during major incidents
- Handle complex troubleshooting
- Coordinate with external teams

## Standard Operating Procedures

### Daily Operations

#### Morning Checklist (9 AM)
```bash
# 1. Check service health
curl -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
    https://pattern-mining.ccl-platform.com/health

# 2. Check error rates
gcloud logging read 'severity>=ERROR AND resource.labels.service_name="pattern-mining"' \
    --limit=20 --format=json

# 3. Check queue depth
redis-cli -h $REDIS_HOST get pattern_mining:queue:depth

# 4. Verify Ray cluster status
ray status --address=$RAY_ADDRESS
```

#### End-of-Day Checklist (6 PM)
```bash
# 1. Review daily metrics
./scripts/daily_metrics_report.sh

# 2. Check for stuck jobs
SELECT * FROM spanner.analysis_jobs 
WHERE status = 'processing' AND updated_at < NOW() - INTERVAL '1 hour';

# 3. Clear old cache entries
redis-cli -h $REDIS_HOST --scan --pattern "cache:*:expired" | xargs redis-cli DEL

# 4. Verify backups completed
gsutil ls gs://pattern-mining-backups/$(date +%Y-%m-%d)/
```

### Deployment Procedures

#### Standard Deployment
```bash
# 1. Pre-deployment checks
./scripts/pre_deploy_check.sh

# 2. Build and test
gcloud builds submit --config=cloudbuild.yaml

# 3. Deploy to staging
gcloud run deploy pattern-mining-staging \
    --image=gcr.io/$PROJECT_ID/pattern-mining:$VERSION \
    --region=us-central1

# 4. Run smoke tests
pytest tests/smoke/ --env=staging

# 5. Deploy to production with canary
gcloud run deploy pattern-mining \
    --image=gcr.io/$PROJECT_ID/pattern-mining:$VERSION \
    --tag=canary \
    --no-traffic

# 6. Route 10% traffic to canary
gcloud run services update-traffic pattern-mining \
    --to-tags=canary=10

# 7. Monitor for 30 minutes
watch -n 30 'gcloud run services describe pattern-mining --format="value(status.traffic[].percent)"'

# 8. Complete rollout
gcloud run services update-traffic pattern-mining --to-latest
```

#### Emergency Rollback
```bash
# 1. Identify last stable version
export STABLE_VERSION=$(gcloud run revisions list \
    --service=pattern-mining \
    --format="value(metadata.name)" \
    --limit=2 | tail -1)

# 2. Immediate rollback
gcloud run services update-traffic pattern-mining \
    --to-revisions=$STABLE_VERSION=100

# 3. Verify service health
curl https://pattern-mining.ccl-platform.com/health

# 4. Notify team
./scripts/notify_rollback.sh $STABLE_VERSION
```

## Monitoring and Alerts

### Key Metrics

| Metric | Normal Range | Warning | Critical |
|--------|--------------|---------|----------|
| Request Latency (p95) | < 200ms | > 500ms | > 1000ms |
| Error Rate | < 0.1% | > 1% | > 5% |
| CPU Usage | < 60% | > 80% | > 90% |
| Memory Usage | < 70% | > 85% | > 95% |
| Queue Depth | < 1000 | > 5000 | > 10000 |
| Cache Hit Rate | > 90% | < 80% | < 60% |

### Alert Response Procedures

#### High Error Rate Alert
```bash
# 1. Check recent errors
gcloud logging read 'severity>=ERROR AND resource.labels.service_name="pattern-mining"' \
    --limit=50 --format=json | jq '.[] | {timestamp: .timestamp, error: .jsonPayload.error}'

# 2. Check if Gemini API is down
curl -X POST https://generativelanguage.googleapis.com/v1/models/gemini-2.5-flash:generateContent \
    -H "Authorization: Bearer $GEMINI_API_KEY" \
    -H "Content-Type: application/json" \
    -d '{"contents": [{"parts": [{"text": "test"}]}]}'

# 3. Check database connectivity
gcloud sql connect pattern-mining-db --user=postgres

# 4. If external service issue, enable circuit breaker
kubectl set env deployment/pattern-mining CIRCUIT_BREAKER_ENABLED=true

# 5. Scale up if needed
gcloud run services update pattern-mining --max-instances=100
```

#### Memory Leak Alert
```bash
# 1. Get memory profile
kubectl exec -it $(kubectl get pod -l app=pattern-mining -o jsonpath="{.items[0].metadata.name}") \
    -- python -m memory_profiler /app/debug/memory_snapshot.py

# 2. Identify leak source
# Look for growing objects in profile

# 3. Restart affected instances
gcloud run services update pattern-mining --max-instances=0
sleep 10
gcloud run services update pattern-mining --max-instances=50

# 4. If persistent, rollback to previous version
```

#### Queue Backup Alert
```bash
# 1. Check queue status
redis-cli -h $REDIS_HOST INFO | grep -E "used_memory|connected_clients|blocked_clients"

# 2. Check Ray cluster status
ray status --address=$RAY_ADDRESS

# 3. Scale Ray workers
ray submit --address=$RAY_ADDRESS scale_workers.py --num-workers=50

# 4. If Ray is down, process locally
kubectl scale deployment pattern-mining-worker --replicas=10

# 5. Clear stuck jobs
python scripts/clear_stuck_jobs.py --older-than=1h
```

## Incident Response

### Severity Levels

| Level | Definition | Response Time | Examples |
|-------|------------|---------------|----------|
| SEV1 | Complete outage | 5 minutes | Service down, data loss |
| SEV2 | Major degradation | 15 minutes | 50% errors, severe slowdown |
| SEV3 | Minor degradation | 1 hour | 10% errors, some features broken |
| SEV4 | Low impact | Next business day | UI issues, non-critical bugs |

### Incident Response Process

1. **Detection**
   - Automated alert triggers
   - User reports issue
   - Monitoring dashboard shows anomaly

2. **Triage** (5 minutes)
   ```bash
   # Run triage script
   ./scripts/incident_triage.sh
   
   # Creates incident ticket
   # Notifies on-call
   # Captures initial diagnostics
   ```

3. **Communication** (10 minutes)
   - Update status page
   - Notify stakeholders via Slack
   - Create incident channel

4. **Investigation** (15-30 minutes)
   ```bash
   # Collect diagnostics
   ./scripts/collect_diagnostics.sh $INCIDENT_ID
   
   # Check recent changes
   git log --since="2 hours ago" --oneline
   
   # Review recent deployments
   gcloud run revisions list --service=pattern-mining --limit=5
   ```

5. **Mitigation**
   - Apply temporary fix
   - Scale resources if needed
   - Enable degraded mode
   - Rollback if necessary

6. **Resolution**
   - Verify fix works
   - Monitor for stability
   - Update status page
   - Close incident

7. **Post-Mortem** (within 48 hours)
   - Document timeline
   - Identify root cause
   - Create action items
   - Share learnings

## Common Issues and Solutions

### Issue: Gemini API Rate Limit Exceeded

**Symptoms**: 429 errors, "quota exceeded" messages

**Solution**:
```bash
# 1. Check current usage
gcloud services api-usage \
    --service=generativelanguage.googleapis.com \
    --format="table(metric,usage)"

# 2. Enable rate limit backoff
kubectl set env deployment/pattern-mining \
    GEMINI_RATE_LIMIT_BACKOFF=true \
    GEMINI_MAX_RETRIES=10

# 3. Distribute load across multiple API keys
kubectl create secret generic gemini-keys \
    --from-literal=key1=$KEY1 \
    --from-literal=key2=$KEY2

# 4. Enable caching for repeated queries
redis-cli -h $REDIS_HOST SET "config:gemini:cache_enabled" "true"
```

### Issue: BigQuery Query Timeout

**Symptoms**: "Query exceeded resource limits" errors

**Solution**:
```bash
# 1. Check query performance
bq query --use_legacy_sql=false \
    "SELECT * FROM \`pattern_mining.__TABLES__\` ORDER BY row_count DESC"

# 2. Add partitioning
bq update --time_partitioning_field=created_at \
    pattern_mining.pattern_cache

# 3. Optimize queries
# Edit src/services/bigquery_service.py
# Add LIMIT clauses and partition filters

# 4. Increase timeout
kubectl set env deployment/pattern-mining BIGQUERY_TIMEOUT=600
```

### Issue: Redis Memory Full

**Symptoms**: "OOM command not allowed" errors

**Solution**:
```bash
# 1. Check memory usage
redis-cli -h $REDIS_HOST INFO memory

# 2. Clear expired keys
redis-cli -h $REDIS_HOST --scan --pattern "*" | \
    xargs -n 1000 redis-cli -h $REDIS_HOST DEL

# 3. Adjust eviction policy
redis-cli -h $REDIS_HOST CONFIG SET maxmemory-policy allkeys-lru

# 4. Scale Redis instance
gcloud redis instances update pattern-mining-cache --size=20
```

## Maintenance Procedures

### Weekly Maintenance

```bash
# Every Monday at 2 AM (automated via cron)
0 2 * * 1 /opt/pattern-mining/scripts/weekly_maintenance.sh

# Manual steps if needed:
# 1. Clear old logs
bq query --use_legacy_sql=false \
    "DELETE FROM pattern_mining.logs WHERE timestamp < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)"

# 2. Optimize database
gcloud spanner databases ddl update pattern-mining \
    --instance=pattern-mining-prod \
    --ddl="CREATE INDEX idx_updated_at ON analysis_jobs(updated_at)"

# 3. Update dependencies
cd /app && pip install -r requirements.txt --upgrade

# 4. Clean up old artifacts
gsutil -m rm -r gs://pattern-mining-artifacts/old/
```

### Monthly Maintenance

```bash
# First Sunday of month at 3 AM
0 3 1-7 * 0 /opt/pattern-mining/scripts/monthly_maintenance.sh

# Manual steps:
# 1. Security audit
./scripts/security_audit.sh > reports/security_$(date +%Y%m).txt

# 2. Performance review
./scripts/performance_analysis.sh --last-month

# 3. Cost analysis
./scripts/cost_report.sh --project=$PROJECT_ID

# 4. Capacity planning
./scripts/capacity_forecast.sh --next-quarter
```

## Emergency Procedures

### Complete Service Outage

```bash
#!/bin/bash
# emergency_recovery.sh

# 1. Switch to DR region
gcloud compute url-maps update pattern-mining-lb \
    --default-service=pattern-mining-backend-dr

# 2. Scale DR instances
gcloud run services update pattern-mining-dr \
    --region=us-east1 \
    --min-instances=10 \
    --max-instances=100

# 3. Update DNS (if using custom domain)
gcloud dns record-sets update pattern-mining.example.com \
    --type=A \
    --zone=example-com \
    --rrdatas=**************  # DR IP

# 4. Notify all stakeholders
./scripts/send_emergency_notification.sh "Pattern Mining switched to DR region"

# 5. Begin investigation of primary region
```

### Data Corruption

```bash
# 1. Stop writes immediately
kubectl set env deployment/pattern-mining READONLY_MODE=true

# 2. Identify corruption extent
bq query --use_legacy_sql=false \
    "SELECT COUNT(*) as corrupted FROM pattern_mining.patterns WHERE patterns IS NULL"

# 3. Restore from backup
gsutil cp gs://pattern-mining-backups/latest/spanner-backup.sql .
gcloud spanner databases restore pattern-mining-restore \
    --source-backup=pattern-mining-backup-20250110 \
    --instance=pattern-mining-prod

# 4. Validate data
python scripts/validate_data_integrity.py

# 5. Resume operations
kubectl set env deployment/pattern-mining READONLY_MODE=false
```

### Security Breach

```bash
# 1. Isolate service
gcloud compute firewall-rules create emergency-block-pattern-mining \
    --action=DENY \
    --rules=all \
    --source-ranges=0.0.0.0/0 \
    --target-tags=pattern-mining

# 2. Rotate all secrets
for secret in $(gcloud secrets list --format="value(name)"); do
    echo "Rotating $secret"
    NEW_VALUE=$(openssl rand -base64 32)
    echo -n "$NEW_VALUE" | gcloud secrets versions add $secret --data-file=-
done

# 3. Revoke all service accounts
gcloud iam service-accounts keys list \
    --iam-account=pattern-mining-sa@$PROJECT_ID.iam.gserviceaccount.com \
    --format="value(name)" | xargs -I {} gcloud iam service-accounts keys delete {} \
    --iam-account=pattern-mining-sa@$PROJECT_ID.iam.gserviceaccount.com

# 4. Enable audit mode
kubectl set env deployment/pattern-mining \
    SECURITY_AUDIT_MODE=true \
    LOG_ALL_REQUESTS=true

# 5. Deploy clean version
./scripts/deploy_clean_build.sh
```

## Contact Information

### Escalation Path

1. **Primary On-Call**: Check PagerDuty
2. **Secondary On-Call**: Check PagerDuty  
3. **Team Lead**: <EMAIL>
4. **Director of Engineering**: <EMAIL>
5. **VP of Engineering**: <EMAIL>

### External Contacts

- **Google Cloud Support**: 1-877-355-5787 (Priority support)
- **Gemini API Support**: <EMAIL>
- **Security Team**: <EMAIL> (for breaches)
- **Legal Team**: <EMAIL> (for data issues)

### Vendor Contacts

| Service | Contact | Account # |
|---------|---------|-----------|
| Google Cloud | <EMAIL> | ******** |
| Datadog | <EMAIL> | DD-98765 |
| PagerDuty | <EMAIL> | PD-11111 |

## Appendix

### Useful Commands

```bash
# Get service logs
gcloud logging read "resource.labels.service_name=pattern-mining" \
    --limit=100 --format=json

# SSH to container (debugging)
gcloud run services update pattern-mining \
    --args="/bin/bash" --no-traffic
gcloud run services proxy pattern-mining --port=8080

# Force garbage collection
kubectl exec -it pattern-mining-pod -- python -c "import gc; gc.collect()"

# Export metrics
curl -s http://localhost:9090/metrics | grep pattern_mining

# Database queries
gcloud spanner databases execute-sql pattern-mining \
    --instance=pattern-mining-prod \
    --sql="SELECT * FROM analysis_jobs ORDER BY created_at DESC LIMIT 10"
```

### Recovery Scripts Location

All emergency scripts are located in:
- Repository: `scripts/emergency/`
- Production server: `/opt/pattern-mining/emergency/`
- Cloud Storage backup: `gs://pattern-mining-emergency-scripts/`

Remember: Stay calm, follow procedures, and communicate clearly during incidents.