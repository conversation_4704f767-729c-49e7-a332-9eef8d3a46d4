# Pattern Mining Architecture

## Overview

The Pattern Mining service is a sophisticated ML/AI microservice designed for large-scale code analysis and pattern detection. It leverages Google's advanced AI models (Gemini 2.5 Flash) and distributed computing (Ray) to provide real-time and batch pattern analysis capabilities.

## System Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web UI]
        SDK[SDKs]
        CLI[CLI Tools]
    end
    
    subgraph "API Gateway"
        GW[Cloud Load Balancer]
        AUTH[Authentication Service]
    end
    
    subgraph "Pattern Mining Service"
        subgraph "API Layer"
            FAST[FastAPI Server]
            MW[Middleware Stack]
            ROUTE[Route Handlers]
        end
        
        subgraph "Business Logic"
            PM[Pattern Manager]
            SM[Similarity Engine]
            AD[Anomaly Detector]
            PC[Pattern Classifier]
        end
        
        subgraph "ML/AI Layer"
            GC[Secure Gemini Client]
            VC[Vertex AI Client]
            EMB[Embedding Generator]
            MOD[Model Manager]
        end
        
        subgraph "Caching Layer"
            L1[L1 In-Memory Cache]
            L2[L2 Redis Cache]
            L3[L3 BigQuery Cache]
        end
        
        subgraph "Processing Layer"
            RAY[Ray Cluster]
            WORK[Worker Nodes]
            SCHED[Task Scheduler]
        end
    end
    
    subgraph "Google Cloud Platform"
        subgraph "AI/ML Services"
            GEM[Gemini 2.5 Flash]
            VAI[Vertex AI]
            NLP[Natural Language API]
        end
        
        subgraph "Storage"
            BQ[BigQuery]
            SP[Cloud Spanner]
            GCS[Cloud Storage]
        end
        
        subgraph "Security"
            SM[Secret Manager]
            IAM[IAM]
            KMS[Cloud KMS]
        end
        
        subgraph "Operations"
            MON[Cloud Monitoring]
            LOG[Cloud Logging]
            TRACE[Cloud Trace]
        end
    end
    
    %% Client connections
    WEB --> GW
    SDK --> GW
    CLI --> GW
    
    %% Gateway to service
    GW --> AUTH
    AUTH --> FAST
    
    %% API Layer flow
    FAST --> MW
    MW --> ROUTE
    ROUTE --> PM
    
    %% Business logic connections
    PM --> SM
    PM --> AD
    PM --> PC
    
    %% ML/AI connections
    PM --> GC
    PM --> VC
    SM --> EMB
    PC --> MOD
    
    %% Caching hierarchy
    PM --> L1
    L1 --> L2
    L2 --> L3
    
    %% Processing distribution
    PM --> RAY
    RAY --> WORK
    RAY --> SCHED
    
    %% Google Cloud connections
    GC --> GEM
    VC --> VAI
    EMB --> NLP
    L3 --> BQ
    PM --> SP
    EMB --> GCS
    GC --> SM
    AUTH --> IAM
    GC --> KMS
    MW --> MON
    MW --> LOG
    MW --> TRACE
```

## Component Details

### API Layer
- **FastAPI Server**: High-performance async web framework
- **Middleware Stack**: Authentication, rate limiting, monitoring, security
- **Route Handlers**: RESTful endpoints for pattern operations

### Business Logic Layer
- **Pattern Manager**: Orchestrates pattern detection workflows
- **Similarity Engine**: Vector-based code similarity analysis
- **Anomaly Detector**: Statistical and ML-based anomaly detection
- **Pattern Classifier**: Categorizes detected patterns

### ML/AI Layer
- **Secure Gemini Client**: Wrapper with prompt injection protection
- **Vertex AI Client**: AutoML and custom model management
- **Embedding Generator**: Creates vector representations of code
- **Model Manager**: Handles model versioning and A/B testing

### Caching Architecture

#### Three-Tier Cache System
1. **L1 Cache (In-Memory)**
   - LRU eviction policy
   - Sub-millisecond access
   - 512MB capacity
   - Hot data storage

2. **L2 Cache (Redis)**
   - Distributed cache with replication
   - Vector similarity search support
   - 10GB capacity
   - TTL-based expiration

3. **L3 Cache (BigQuery)**
   - Persistent historical cache
   - Unlimited capacity
   - Analytical queries
   - Cost-optimized storage

### Processing Layer
- **Ray Cluster**: Distributed computing framework
- **Worker Nodes**: Auto-scaling compute instances
- **Task Scheduler**: Intelligent job distribution

## Data Flow

### Pattern Detection Flow
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant PM as Pattern Manager
    participant Cache
    participant Ray
    participant Gemini
    participant Storage
    
    Client->>API: POST /patterns/analyze
    API->>PM: Create analysis job
    PM->>Cache: Check for cached results
    
    alt Cache hit
        Cache->>PM: Return cached patterns
        PM->>API: Return results
        API->>Client: 200 OK (cached)
    else Cache miss
        PM->>Ray: Distribute analysis tasks
        Ray->>Gemini: Analyze code chunks
        Gemini->>Ray: Return patterns
        Ray->>PM: Aggregate results
        PM->>Storage: Persist patterns
        PM->>Cache: Update cache
        PM->>API: Return results
        API->>Client: 200 OK (fresh)
    end
```

## Security Architecture

### Multi-Layer Security
1. **Network Security**
   - Cloud Load Balancer with DDoS protection
   - Private VPC with firewall rules
   - TLS 1.3 for all external connections

2. **Application Security**
   - JWT-based authentication
   - Role-Based Access Control (7 roles)
   - Parameter-level permissions
   - Input validation and sanitization

3. **AI/ML Security**
   - Prompt injection detection
   - Response validation
   - Rate limiting per model
   - Cost controls and budgets

4. **Data Security**
   - Encryption at rest (Cloud KMS)
   - Encryption in transit (TLS)
   - Secret rotation (24-hour cycle)
   - Audit logging (90-day retention)

## Scalability Design

### Horizontal Scaling
- **API Servers**: Auto-scaling based on CPU/memory
- **Ray Workers**: Dynamic scaling based on queue depth
- **Redis Cluster**: Sharded with automatic failover
- **BigQuery**: Serverless, infinite scale

### Performance Optimization
- **Batch Processing**: Optimized for 1000+ file batches
- **Streaming Pipeline**: Real-time analysis with <100ms latency
- **Connection Pooling**: Reused connections to external services
- **Async Processing**: Non-blocking I/O throughout

## Monitoring & Observability

### Metrics Collection
```yaml
Key Metrics:
  - Request latency (p50, p95, p99)
  - Pattern detection accuracy
  - Cache hit rates (per tier)
  - Model inference time
  - Error rates by endpoint
  - Resource utilization
  - Cost per analysis
```

### Distributed Tracing
- OpenTelemetry instrumentation
- Cloud Trace integration
- Request flow visualization
- Performance bottleneck identification

### Logging Strategy
- Structured JSON logging
- Centralized in Cloud Logging
- Log levels: DEBUG, INFO, WARNING, ERROR
- Automatic PII redaction

## Deployment Architecture

### Multi-Environment Setup
```
Production:
  - Region: us-central1 (primary), us-east1 (DR)
  - Instances: 10-50 (auto-scaling)
  - Ray Cluster: 20-100 workers
  
Staging:
  - Region: us-central1
  - Instances: 2-5
  - Ray Cluster: 5-10 workers
  
Development:
  - Local Docker Compose
  - Single Ray instance
  - Mock external services
```

### CI/CD Pipeline
1. **Source Control**: GitHub with branch protection
2. **Build**: Cloud Build with multi-stage Dockerfile
3. **Test**: Unit, integration, and e2e tests
4. **Deploy**: Blue-green deployment to Cloud Run
5. **Monitor**: Automated rollback on errors

## High Availability

### Redundancy
- **Multi-region deployment**: Active-passive failover
- **Database replication**: Spanner multi-region
- **Cache replication**: Redis with 3 replicas
- **Load balancing**: Global load balancer

### Failure Handling
- **Circuit breakers**: Prevent cascade failures
- **Retry policies**: Exponential backoff
- **Graceful degradation**: Fallback to cached data
- **Health checks**: Liveness and readiness probes

## Cost Optimization

### Resource Management
- **Spot instances**: For batch processing
- **Preemptible VMs**: For Ray workers
- **BigQuery slots**: Reserved for predictable workloads
- **API quotas**: Rate limiting to control costs

### Caching Strategy
- **Aggressive caching**: 95% cache hit rate
- **Smart invalidation**: Dependency-based clearing
- **Cost tracking**: Per-operation cost attribution