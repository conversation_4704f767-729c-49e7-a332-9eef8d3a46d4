# ML Models Guide for Pattern Detection Service

## Overview
This guide provides detailed information about the machine learning models used in the Pattern Detection service as of July 2025. The service employs a hybrid approach combining transformer-based models, Graph Neural Networks, and traditional ML techniques.

## Model Architecture

### 1. Transformer Models

#### CodeT5+ (Primary Model)
- **Model**: `Salesforce/codet5p-770m`
- **Purpose**: Code understanding and generation
- **Key Features**:
  - 770M parameters
  - Trained on 20+ programming languages
  - Identifier-aware pre-training
  - Supports both code-to-text and text-to-code tasks
- **Usage**:
  ```python
  from transformers import AutoModel, AutoTokenizer
  
  model = AutoModel.from_pretrained("Salesforce/codet5p-770m")
  tokenizer = AutoTokenizer.from_pretrained("Salesforce/codet5p-770m")
  ```

#### GraphCodeBERT
- **Model**: `microsoft/graphcodebert-base`
- **Purpose**: Structural code analysis with data flow understanding
- **Key Features**:
  - Incorporates AST and data flow information
  - Pre-trained on 6 programming languages
  - 125M parameters
  - Excels at code search and clone detection
- **Usage**:
  ```python
  model = AutoModel.from_pretrained("microsoft/graphcodebert-base")
  ```

#### Nova (Low-level Code Analysis)
- **Purpose**: Assembly and low-level code pattern detection
- **Key Features**:
  - Hierarchical attention mechanism
  - Contrastive learning objectives
  - Specialized for binary code analysis

### 2. Graph Neural Networks (GNNs)

#### Architecture
```python
class CodeGNN(torch.nn.Module):
    def __init__(self):
        super().__init__()
        self.conv1 = GCNConv(feature_dim, 256)
        self.conv2 = GCNConv(256, 128)
        self.conv3 = GCNConv(128, 64)
        self.classifier = nn.Linear(64, num_patterns)
```

#### Key Components
- **Node Features**: AST node types, token embeddings, metrics
- **Edge Types**: Control flow, data flow, call relationships
- **Aggregation**: Multi-head attention over neighborhoods
- **Training**: Supervised with weak supervision from MLScent

### 3. Gemini 2.5 Pro Integration

#### Capabilities
- **Context Window**: 1 million tokens
- **Use Cases**:
  - Complex pattern reasoning
  - Pattern explanation generation
  - Unknown pattern analysis
  - Code fix suggestions

#### BigQuery Integration
```sql
SELECT 
    ML.GENERATE_TEXT(
        MODEL `pattern_detection.gemini_2_5_pro`,
        PROMPT => code_content,
        temperature => 0.1
    ) AS pattern_analysis
FROM code_samples
```

### 4. Ensemble Architecture

#### Weighting Strategy
```python
ENSEMBLE_WEIGHTS = {
    'codet5': 0.35,         # Primary transformer
    'graphcodebert': 0.25,  # Structural understanding
    'gnn': 0.20,            # Graph patterns
    'rules': 0.10,          # Deterministic baseline
    'gemini': 0.10          # Complex reasoning
}
```

#### Confidence Calibration
- Temperature scaling for neural network outputs
- Platt scaling for ensemble predictions
- Dynamic threshold adjustment based on feedback

## Training Pipeline

### 1. Data Sources
- **GitHub Code**: 10M+ repositories
- **Stack Overflow**: Code snippets with labels
- **Internal Datasets**: Curated pattern examples
- **Weak Supervision**: MLScent labeling functions

### 2. Training Strategy
```python
# Multi-task learning setup
tasks = [
    "pattern_classification",
    "pattern_localization", 
    "confidence_prediction",
    "explanation_generation"
]

# Curriculum learning
stages = [
    {"name": "basic_patterns", "epochs": 10},
    {"name": "complex_patterns", "epochs": 20},
    {"name": "rare_patterns", "epochs": 30}
]
```

### 3. Optimization
- **Optimizer**: AdamW with cosine annealing
- **Learning Rate**: 2e-5 (transformers), 1e-3 (GNN)
- **Batch Size**: 32 (GPU), 256 (TPU)
- **Mixed Precision**: FP16 for efficiency

## Performance Metrics

### Model Comparison (July 2025)
| Model | Precision | Recall | F1 Score | Latency |
|-------|-----------|--------|----------|---------|
| CodeT5+ | 0.94 | 0.89 | 0.91 | 45ms |
| GraphCodeBERT | 0.92 | 0.87 | 0.89 | 38ms |
| GNN | 0.88 | 0.85 | 0.86 | 22ms |
| Ensemble | 0.95 | 0.91 | 0.93 | 120ms |

### Pattern-Specific Performance
```
Design Patterns: 0.96 F1
Anti-patterns: 0.94 F1
Security Issues: 0.97 F1
Performance: 0.91 F1
ML-specific: 0.89 F1
```

## Deployment Considerations

### 1. Model Serving
```yaml
deployment:
  codet5:
    instances: 3
    gpu: T4
    memory: 16GB
  graphcodebert:
    instances: 2
    gpu: T4
    memory: 12GB
  gnn:
    instances: 4
    cpu: true
    memory: 8GB
```

### 2. Optimization Techniques
- **Quantization**: INT8 for inference
- **Model Pruning**: 30% reduction in size
- **Knowledge Distillation**: Student models for edge deployment
- **Caching**: Embedding cache for common patterns

### 3. A/B Testing Framework
```python
@app.post("/api/v2/detect")
async def detect_with_ab_test(request):
    if random.random() < AB_TEST_SPLIT:
        # New model version
        return await detect_v2(request)
    else:
        # Control group
        return await detect_v1(request)
```

## Continuous Learning

### 1. Feedback Loop
- User corrections → Training data
- Confidence scores → Active learning
- Performance metrics → Model selection

### 2. Drift Detection
```python
class DriftDetector:
    def detect_drift(self, predictions, ground_truth):
        # Kolmogorov-Smirnov test
        # Page-Hinkley test
        # ADWIN algorithm
```

### 3. Incremental Training
- Weekly fine-tuning on new patterns
- Monthly full retraining
- Quarterly architecture reviews

## Best Practices

### 1. Model Selection
- Use rule-based for simple, deterministic patterns
- Apply transformers for semantic understanding
- Leverage GNNs for structural patterns
- Escalate to Gemini for complex reasoning

### 2. Performance Optimization
- Batch similar requests
- Pre-compute embeddings
- Use GPU for transformer inference
- Cache frequent patterns

### 3. Cost Management
- Monitor token usage for Gemini
- Use lighter models when possible
- Implement request throttling
- Optimize batch sizes

## Future Roadmap

### Q3 2025
- Integration with GPT-4 Code Interpreter
- Support for 10 additional languages
- Real-time IDE integration

### Q4 2025
- Custom model fine-tuning API
- Federated learning for enterprise
- Quantum-resistant pattern detection

## References
1. CodeT5+: Open Code Large Language Models
2. GraphCodeBERT: Pre-training Code Representations
3. MLScent: Machine Learning Code Smell Detection
4. Gemini 2.5 Technical Report