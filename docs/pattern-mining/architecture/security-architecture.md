# Security Configuration Guide

## Overview

This document provides comprehensive guidance on configuring and using the security features of the Pattern Mining Service. The security system provides enterprise-grade protection with multiple layers of defense.

## Architecture Overview

The security system consists of five main components:

1. **Authentication System** - JWT, OAuth2, MFA, and session management
2. **Authorization System** - RBAC, resource-based permissions, and access control
3. **Rate Limiting** - Token bucket, sliding window, and burst protection
4. **Security Middleware** - Request validation, CSRF protection, and security headers
5. **Encryption & Secrets** - Data encryption, secret management, and key rotation

## Quick Start

### Basic Setup

```python
from pattern_mining.security.integration import SecurityIntegration
from pattern_mining.config.settings import Settings

# Initialize security
settings = Settings()
security = SecurityIntegration(settings)

# Initialize security components
await security.initialize()

# Setup middleware on FastAPI app
await security.setup_middleware(app)
```

### Environment Variables

```bash
# Required
SECRET_KEY="your-secret-key-here"
REDIS_URL="redis://localhost:6379"
GCP_PROJECT_ID="your-gcp-project"

# Optional
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=15
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7
API_RATE_LIMIT=100
RATE_LIMIT_BURST=10
MAX_REQUEST_SIZE=10485760  # 10MB
```

## Authentication Configuration

### JWT Authentication

```python
from pattern_mining.security.authentication import JWTAuthenticator

# Configure JWT
jwt_auth = JWTAuthenticator(
    secret_key="your-secret-key",
    algorithm="HS256",
    access_token_expire_minutes=15,
    refresh_token_expire_days=7
)

# Create tokens
access_token = jwt_auth.create_access_token(user)
refresh_token = jwt_auth.create_refresh_token(user)

# Verify tokens
payload = await jwt_auth.verify_token(access_token)
```

### Multi-Factor Authentication

```python
from pattern_mining.security.authentication import MFAAuthenticator

# Setup MFA
mfa_auth = MFAAuthenticator(app_name="Pattern Mining Service")

# Generate secret for user
secret = mfa_auth.generate_secret()
qr_url = mfa_auth.generate_qr_code_url(user_email, secret)

# Verify TOTP token
is_valid = mfa_auth.verify_totp(secret, user_token)

# Generate backup codes
backup_codes = mfa_auth.generate_backup_codes(count=8)
```

### Session Management

```python
from pattern_mining.security.authentication import SessionManager

# Create session manager
session_manager = SessionManager(
    redis_client=redis_client,
    session_timeout_minutes=30,
    max_sessions_per_user=5
)

# Create session
session = await session_manager.create_session(
    user_id="user_123",
    ip_address="***********",
    user_agent="Browser/1.0"
)

# Validate session
session = await session_manager.get_session(session_id)
```

## Authorization Configuration

### Role-Based Access Control

```python
from pattern_mining.security.authorization import RoleManager, Permission

# Setup role manager
role_manager = RoleManager(redis_client)

# Initialize default roles
await role_manager.initialize_default_roles()

# Assign role to user
await role_manager.assign_role_to_user("user_123", "admin")

# Check user roles
roles = await role_manager.get_user_roles("user_123")
```

### Permission Management

```python
from pattern_mining.security.authorization import PermissionManager, Resource, Action

# Setup permission manager
permission_manager = PermissionManager(redis_client)

# Grant specific permission
await permission_manager.grant_permission(
    user_id="user_123",
    resource_type=Resource.PATTERN,
    resource_id="pattern_456",
    action=Action.UPDATE,
    granted_by="admin_789"
)

# Check permission
has_permission = await permission_manager.check_permission(
    user_id="user_123",
    resource_type=Resource.PATTERN,
    resource_id="pattern_456",
    action=Action.UPDATE
)
```

### Access Control Example

```python
from pattern_mining.security.authorization import AccessRequest

# Create access request
request = AccessRequest(
    user_id="user_123",
    resource_type=Resource.PATTERN,
    resource_id="pattern_456",
    action=Action.READ,
    context={"ip_address": "***********"}
)

# Check authorization
authorized = await rbac_authorizer.authorize(request)
```

## Rate Limiting Configuration

### Basic Rate Limiting

```python
from pattern_mining.security.rate_limiting import RateLimitManager, RateLimitType

# Setup rate limit manager
rate_limit_manager = RateLimitManager(
    redis_client=redis_client,
    settings=settings
)

# Check rate limit
result = await rate_limit_manager.check_rate_limit(
    key="user_123",
    limit_type=RateLimitType.USER,
    endpoint="/api/v1/patterns"
)

if not result.allowed:
    raise HTTPException(
        status_code=429,
        detail="Rate limit exceeded",
        headers={"Retry-After": str(result.retry_after)}
    )
```

### Custom Rate Limits

```python
# Configure endpoint-specific limits
endpoint_limits = {
    "/api/v1/patterns/detect": RateLimitConfig(limit=50, window_seconds=60),
    "/api/v1/patterns/batch": RateLimitConfig(limit=10, window_seconds=60),
    "/api/v1/ml/train": RateLimitConfig(limit=5, window_seconds=3600),
}

# Apply burst protection
burst_result = await rate_limit_manager.check_burst_limit(
    key="user_123",
    limit_type=RateLimitType.USER
)
```

## Security Middleware Configuration

### Complete Middleware Stack

```python
from pattern_mining.security.middleware import SecurityMiddleware

# Add security middleware
app.add_middleware(
    SecurityMiddleware,
    settings=settings,
    redis_client=redis_client,
    jwt_authenticator=jwt_authenticator,
    rbac_authorizer=rbac_authorizer,
    rate_limit_manager=rate_limit_manager
)
```

### Individual Middleware Components

```python
from pattern_mining.security.middleware import (
    AuthenticationMiddleware,
    AuthorizationMiddleware,
    ValidationMiddleware,
    SecurityHeadersMiddleware,
    CSRFProtectionMiddleware
)

# Authentication middleware
auth_middleware = AuthenticationMiddleware(jwt_authenticator)

# Request validation
validation_middleware = ValidationMiddleware(settings)

# Security headers
headers_middleware = SecurityHeadersMiddleware(settings)

# CSRF protection
csrf_middleware = CSRFProtectionMiddleware(redis_client)
```

## Encryption & Secrets Configuration

### Data Encryption

```python
from pattern_mining.security.encryption import EncryptionManager, KeyType

# Setup encryption manager
encryption_manager = EncryptionManager(
    settings=settings,
    redis_client=redis_client
)

# Encrypt sensitive data
encrypted_data = await encryption_manager.encrypt_data(
    "sensitive information",
    key_type=KeyType.DATA
)

# Decrypt data
decrypted_data = await encryption_manager.decrypt_data(
    encrypted_data,
    key_type=KeyType.DATA
)
```

### Secret Management

```python
from pattern_mining.security.encryption import SecretManager

# Setup secret manager
secret_manager = SecretManager(
    settings=settings,
    redis_client=redis_client,
    encryption_manager=encryption_manager
)

# Store secret
version_id = await secret_manager.store_secret(
    "database_password",
    "secret_password_123"
)

# Retrieve secret
password = await secret_manager.get_secret("database_password")
```

### API Key Management

```python
from pattern_mining.security.encryption import KeyManager

# Setup key manager
key_manager = KeyManager(
    redis_client=redis_client,
    encryption_manager=encryption_manager
)

# Generate API key
api_key = await key_manager.generate_api_key(
    user_id="user_123",
    name="Analytics API Key",
    permissions=["pattern:read", "analysis:execute"]
)

# Validate API key
key_data = await key_manager.validate_api_key(api_key)
```

## FastAPI Integration

### Dependency Injection

```python
from pattern_mining.security.integration import (
    get_current_user_dependency,
    require_authentication,
    require_permission
)

# Optional authentication
@app.get("/api/v1/patterns")
async def get_patterns(
    user: Optional[Dict] = Depends(get_current_user_dependency)
):
    # User might be None for public endpoints
    pass

# Required authentication
@app.post("/api/v1/patterns")
async def create_pattern(
    pattern_data: PatternCreateRequest,
    user: Dict = Depends(require_authentication)
):
    # User is guaranteed to be authenticated
    pass

# Required permission
@app.delete("/api/v1/patterns/{pattern_id}")
async def delete_pattern(
    pattern_id: str,
    user: Dict = Depends(require_permission("pattern", "delete"))
):
    # User has delete permission for patterns
    pass
```

### Error Handling

```python
from fastapi import HTTPException
from pattern_mining.security.authentication import AuthenticationError
from pattern_mining.security.authorization import AuthorizationError

@app.exception_handler(AuthenticationError)
async def auth_exception_handler(request: Request, exc: AuthenticationError):
    return JSONResponse(
        status_code=401,
        content={"detail": str(exc)},
        headers={"WWW-Authenticate": "Bearer"}
    )

@app.exception_handler(AuthorizationError)
async def authz_exception_handler(request: Request, exc: AuthorizationError):
    return JSONResponse(
        status_code=403,
        content={"detail": str(exc)}
    )
```

## Security Best Practices

### 1. Token Management

```python
# Use short-lived access tokens
JWT_ACCESS_TOKEN_EXPIRE_MINUTES = 15

# Use longer-lived refresh tokens
JWT_REFRESH_TOKEN_EXPIRE_DAYS = 7

# Implement token rotation
async def refresh_token(refresh_token: str):
    new_access_token = await jwt_auth.refresh_access_token(refresh_token)
    return new_access_token
```

### 2. Password Security

```python
from passlib.context import CryptContext

# Use strong password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Hash password
hashed_password = pwd_context.hash(plain_password)

# Verify password
is_valid = pwd_context.verify(plain_password, hashed_password)
```

### 3. Input Validation

```python
from pydantic import BaseModel, validator

class PatternCreateRequest(BaseModel):
    name: str
    description: str
    code: str
    
    @validator('name')
    def validate_name(cls, v):
        if len(v) > 100:
            raise ValueError('Name too long')
        return v
    
    @validator('code')
    def validate_code(cls, v):
        # Validate code content
        if '<script>' in v.lower():
            raise ValueError('Invalid code content')
        return v
```

### 4. Rate Limiting Strategy

```python
# Different limits for different endpoints
RATE_LIMITS = {
    "/api/v1/patterns": {"limit": 100, "window": 60},
    "/api/v1/ml/train": {"limit": 5, "window": 3600},
    "/api/v1/analysis": {"limit": 50, "window": 60}
}

# Implement progressive penalties
PENALTY_MULTIPLIERS = {
    1: 1.0,  # First violation
    2: 2.0,  # Second violation
    3: 5.0,  # Third violation
    4: 10.0  # Fourth violation
}
```

## Monitoring & Auditing

### Security Metrics

```python
# Track authentication attempts
await security.track_auth_attempt(
    user_id="user_123",
    success=True,
    method="jwt",
    ip_address="***********"
)

# Monitor rate limiting
await security.track_rate_limit_violation(
    key="user_123",
    endpoint="/api/v1/patterns",
    limit_type="user"
)

# Audit permission checks
await security.audit_permission_check(
    user_id="user_123",
    resource="pattern_456",
    action="delete",
    granted=False
)
```

### Security Dashboard

```python
# Get security metrics
metrics = await security.get_security_metrics()

# Perform security audit
audit_results = await security.perform_security_audit()

# Check for security issues
security_issues = await security.check_security_issues()
```

## Troubleshooting

### Common Issues

1. **Token Verification Fails**
   - Check secret key configuration
   - Verify token hasn't expired
   - Check Redis connectivity

2. **Rate Limiting Too Aggressive**
   - Adjust rate limits in settings
   - Check Redis key expiration
   - Verify client identification

3. **Authorization Denied**
   - Check user roles and permissions
   - Verify resource ownership
   - Check access request context

### Debug Mode

```python
# Enable debug logging
import logging
logging.getLogger("pattern_mining.security").setLevel(logging.DEBUG)

# Test authentication
try:
    payload = await jwt_auth.verify_token(token)
    print(f"Token valid: {payload}")
except Exception as e:
    print(f"Token invalid: {e}")

# Test authorization
result = await rbac_authorizer.authorize(access_request)
print(f"Authorization result: {result}")
```

## Production Deployment

### Environment Configuration

```bash
# Production settings
SECRET_KEY="$(openssl rand -hex 32)"
REDIS_URL="redis://redis-cluster:6379"
GCP_PROJECT_ID="production-project"

# Security settings
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=15
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7
API_RATE_LIMIT=1000
RATE_LIMIT_BURST=100
MAX_REQUEST_SIZE=52428800  # 50MB

# Enable security features
ENABLE_HTTPS=true
ENABLE_CSRF_PROTECTION=true
ENABLE_RATE_LIMITING=true
ENABLE_AUDIT_LOGGING=true
```

### Health Checks

```python
@app.get("/health/security")
async def security_health():
    try:
        # Check Redis connection
        await redis_client.ping()
        
        # Check key rotation status
        keys_status = await encryption_manager.check_key_rotation()
        
        # Check certificate expiration
        cert_status = await certificate_manager.check_certificate_expiry()
        
        return {
            "status": "healthy",
            "redis": "connected",
            "keys": keys_status,
            "certificates": cert_status
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }
```

## Security Updates

### Key Rotation

```python
# Schedule regular key rotation
async def rotate_encryption_keys():
    # Rotate data encryption keys
    await encryption_manager.rotate_key(KeyType.DATA)
    
    # Rotate API keys (if needed)
    await key_manager.rotate_expired_keys()
    
    # Update secrets
    await secret_manager.rotate_secrets()

# Schedule rotation (example with APScheduler)
from apscheduler.schedulers.asyncio import AsyncIOScheduler

scheduler = AsyncIOScheduler()
scheduler.add_job(
    rotate_encryption_keys,
    'interval',
    days=30,  # Rotate every 30 days
    id='key_rotation'
)
```

### Security Patches

```python
# Check for security vulnerabilities
async def security_scan():
    # Run vulnerability scan
    vulnerabilities = await security_scanner.scan()
    
    # Check for CVEs
    cves = await cve_checker.check_dependencies()
    
    # Generate security report
    report = await generate_security_report(vulnerabilities, cves)
    
    return report
```

This comprehensive security configuration provides enterprise-grade protection for the Pattern Mining Service. Regular security audits, monitoring, and updates ensure the system remains secure against evolving threats.