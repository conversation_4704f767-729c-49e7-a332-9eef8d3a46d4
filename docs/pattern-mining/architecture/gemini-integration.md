# Gemini API Integration for Pattern Mining

## Overview

This document describes the comprehensive Gemini API integration for the pattern mining service, providing advanced AI-powered code analysis, pattern detection, and reasoning capabilities using Google's Gemini 2.5 Pro model with 1M token context window.

## Architecture

The integration consists of five main components:

1. **Configuration Layer** (`config/gemini.py`) - Centralized configuration management
2. **Client Layer** (`ml/gemini_client.py`) - Production-ready API client
3. **Analysis Layer** (`ml/gemini_analyzer.py`) - Pattern analysis and reasoning
4. **Embedding Layer** (`ml/gemini_embeddings.py`) - Code similarity and vector operations
5. **Integration Layer** (`ml/gemini_integration.py`) - Hybrid reasoning with local models

## Features

### Core Capabilities

- **Pattern Detection**: Advanced pattern recognition using Gemini 2.5 Pro
- **Code Explanation**: Natural language explanations of code functionality
- **Anti-pattern Detection**: Identification of code smells and anti-patterns
- **Security Analysis**: Vulnerability detection and security recommendations
- **Performance Analysis**: Performance bottleneck identification
- **Refactoring Suggestions**: Intelligent code improvement recommendations
- **Documentation Generation**: Automatic documentation creation

### Advanced Features

- **Hybrid Reasoning**: Combination of Gemini and local models for enhanced accuracy
- **Confidence Scoring**: Multi-level confidence assessment and aggregation
- **Vector Similarity**: FAISS-powered similarity search and clustering
- **Batch Processing**: Efficient batch analysis for large codebases
- **Real-time Analysis**: Sub-second response times with intelligent caching
- **Rate Limiting**: Production-ready rate limiting and quota management

## Component Details

### 1. Configuration (`config/gemini.py`)

The configuration module provides comprehensive settings for all aspects of the Gemini integration:

```python
from pattern_mining.config.gemini import GeminiConfig, get_gemini_config

config = get_gemini_config()
print(f"Model: {config.default_model}")
print(f"Context window: {config.context_window} tokens")
print(f"Rate limit: {config.requests_per_minute} requests/minute")
```

#### Key Configuration Options

- **Models**: Support for all Gemini models (Pro, Flash, Vision)
- **Generation Parameters**: Temperature, top-p, top-k, output tokens
- **Rate Limiting**: Requests per minute, tokens per minute, concurrent requests
- **Safety Settings**: Configurable content filtering and safety thresholds
- **Caching**: Response caching with TTL and size limits
- **Performance**: Batch processing, parallel requests, optimization flags

### 2. Client (`ml/gemini_client.py`)

The client provides production-ready access to the Gemini API with comprehensive error handling:

```python
from pattern_mining.ml.gemini_client import create_gemini_client

async with create_gemini_client() as client:
    response = await client.generate_content(
        prompt="Explain the Factory pattern",
        model=GeminiModel.GEMINI_2_5_PRO
    )
    print(response["text"])
```

#### Key Features

- **Async/Await Support**: Full async support for high-performance applications
- **Rate Limiting**: Token bucket algorithm with configurable limits
- **Retry Logic**: Exponential backoff with configurable retry policies
- **Caching**: Response caching with TTL and LRU eviction
- **Monitoring**: Comprehensive metrics collection and performance tracking
- **Error Handling**: Graceful error handling with detailed error reporting

### 3. Analysis (`ml/gemini_analyzer.py`)

The analyzer provides sophisticated code analysis capabilities:

```python
from pattern_mining.ml.gemini_analyzer import create_gemini_analyzer, AnalysisType, CodeContext

async with create_gemini_analyzer() as analyzer:
    context = CodeContext(
        code="class Singleton: ...",
        language="python",
        file_path="singleton.py"
    )
    
    results = await analyzer.analyze_patterns(
        context, 
        [AnalysisType.PATTERN_DETECTION, AnalysisType.SECURITY_ANALYSIS]
    )
    
    for analysis_type, result in results.items():
        print(f"{analysis_type}: {result.confidence:.2f} confidence")
        print(f"Findings: {len(result.findings)}")
```

#### Analysis Types

- **PATTERN_DETECTION**: Design patterns, architectural patterns, code patterns
- **CODE_EXPLANATION**: Natural language explanations for different audiences
- **DOCUMENTATION_GENERATION**: API docs, user guides, technical documentation
- **ANTI_PATTERN_DETECTION**: Code smells, anti-patterns, quality issues
- **REFACTORING_SUGGESTIONS**: Improvement recommendations with priorities
- **SECURITY_ANALYSIS**: Vulnerability detection and security recommendations
- **PERFORMANCE_ANALYSIS**: Performance bottlenecks and optimization opportunities

### 4. Embeddings (`ml/gemini_embeddings.py`)

The embedding service provides advanced vector operations for code similarity:

```python
from pattern_mining.ml.gemini_embeddings import create_embedding_service, EmbeddingType

async with create_embedding_service() as embeddings:
    # Generate embeddings
    embedding, metadata = await embeddings.generate_embedding(
        content="def factorial(n): ...",
        embedding_type=EmbeddingType.FUNCTION,
        language="python"
    )
    
    # Similarity search
    results = await embeddings.search_similar(
        query_content="recursive function",
        query_type=EmbeddingType.QUERY,
        k=5
    )
    
    for result in results:
        print(f"Similarity: {result.similarity_score:.3f}")
        print(f"Code: {result.metadata.source_code[:50]}...")
```

#### Embedding Types

- **CODE**: General code snippets
- **FUNCTION**: Function definitions
- **CLASS**: Class definitions
- **MODULE**: Module-level code
- **DOCUMENTATION**: Documentation text
- **COMMENT**: Code comments
- **PATTERN**: Pattern implementations
- **QUERY**: Search queries

#### Vector Operations

- **FAISS Integration**: High-performance similarity search
- **Clustering**: K-means and hierarchical clustering
- **Duplicate Detection**: Near-duplicate code identification
- **Batch Processing**: Efficient batch embedding generation
- **Caching**: Multi-level caching for performance optimization

### 5. Integration (`ml/gemini_integration.py`)

The integration layer provides hybrid reasoning combining Gemini with local models:

```python
from pattern_mining.ml.gemini_integration import create_gemini_integration, IntegrationMode

async with create_gemini_integration() as integration:
    result = await integration.analyze_code_hybrid(
        code="class UserManager: ...",
        language="python",
        integration_mode=IntegrationMode.HYBRID_PARALLEL
    )
    
    print(f"Combined confidence: {result.combined_confidence:.3f}")
    print(f"Gemini confidence: {result.confidence_breakdown['gemini']:.3f}")
    print(f"Local confidence: {result.confidence_breakdown['local']:.3f}")
```

#### Integration Modes

- **GEMINI_ONLY**: Use only Gemini for analysis
- **LOCAL_ONLY**: Use only local models
- **HYBRID_PARALLEL**: Run both in parallel and combine results
- **HYBRID_SEQUENTIAL**: Use local first, fallback to Gemini if needed
- **CONFIDENCE_WEIGHTED**: Weight results based on confidence scores
- **ENSEMBLE**: Advanced ensemble method with agreement scoring

## Usage Examples

### Basic Pattern Detection

```python
from pattern_mining.ml import create_gemini_analyzer, AnalysisType, CodeContext

async def analyze_code():
    async with create_gemini_analyzer() as analyzer:
        context = CodeContext(
            code="""
            class DatabaseConnection:
                _instance = None
                
                def __new__(cls):
                    if cls._instance is None:
                        cls._instance = super().__new__(cls)
                    return cls._instance
            """,
            language="python"
        )
        
        results = await analyzer.analyze_patterns(
            context, [AnalysisType.PATTERN_DETECTION]
        )
        
        result = results[AnalysisType.PATTERN_DETECTION]
        print(f"Confidence: {result.confidence}")
        
        for finding in result.findings:
            print(f"Pattern: {finding.get('pattern_type')}")
            print(f"Quality: {finding.get('quality_score')}")
```

### Security Analysis

```python
from pattern_mining.ml import create_gemini_analyzer, AnalysisType, CodeContext

async def security_analysis():
    async with create_gemini_analyzer() as analyzer:
        context = CodeContext(
            code="""
            def authenticate(username, password):
                query = f"SELECT * FROM users WHERE username='{username}' AND password='{password}'"
                cursor.execute(query)
                return cursor.fetchone()
            """,
            language="python"
        )
        
        security_result = await analyzer.analyze_security(context)
        
        print(f"Vulnerabilities found: {security_result['total_found']}")
        print(f"High severity: {security_result['high_severity']}")
        
        for vuln in security_result['vulnerabilities']:
            print(f"- {vuln['type']}: {vuln['severity']}")
```

### Hybrid Analysis

```python
from pattern_mining.ml import create_gemini_integration, IntegrationMode

async def hybrid_analysis():
    async with create_gemini_integration() as integration:
        result = await integration.analyze_code_hybrid(
            code="def bubble_sort(arr): ...",
            language="python",
            integration_mode=IntegrationMode.ENSEMBLE
        )
        
        print(f"Final confidence: {result.combined_confidence:.3f}")
        print(f"Processing time: {result.processing_time:.3f}s")
        print(f"Agreement score: {result.confidence_breakdown.get('agreement', 0):.3f}")
```

### Embedding and Similarity Search

```python
from pattern_mining.ml import create_embedding_service, EmbeddingType

async def similarity_search():
    async with create_embedding_service() as embeddings:
        # Generate embeddings for code snippets
        code_samples = [
            "def quicksort(arr): ...",
            "def mergesort(arr): ...",
            "def bubblesort(arr): ..."
        ]
        
        batch_results = await embeddings.batch_generate_embeddings(
            contents=code_samples,
            embedding_types=[EmbeddingType.FUNCTION] * 3,
            languages=["python"] * 3
        )
        
        # Add to index
        embeddings_list = [result[0] for result in batch_results]
        metadata_list = [result[1] for result in batch_results]
        await embeddings.add_to_index(embeddings_list, metadata_list)
        
        # Search for similar functions
        results = await embeddings.search_similar(
            query_content="sorting algorithm implementation",
            query_type=EmbeddingType.QUERY,
            k=3
        )
        
        for result in results:
            print(f"Similarity: {result.similarity_score:.3f}")
            print(f"Code: {result.metadata.source_code}")
```

## Performance Optimization

### Caching Strategy

The integration implements multi-level caching for optimal performance:

1. **Response Caching**: Generated responses cached with TTL
2. **Embedding Caching**: Vector embeddings cached with LRU eviction
3. **Analysis Caching**: Analysis results cached by code hash
4. **Model Caching**: Model instances cached to avoid reloading

### Rate Limiting

Production-ready rate limiting with:

- **Token Bucket Algorithm**: Smooth rate limiting with burst capacity
- **Adaptive Limits**: Dynamic adjustment based on API quotas
- **Backoff Strategy**: Exponential backoff with jitter
- **Queue Management**: Request queuing during rate limit periods

### Batch Processing

Efficient batch processing capabilities:

- **Batch Embeddings**: Generate multiple embeddings in single request
- **Batch Analysis**: Analyze multiple code snippets simultaneously
- **Parallel Processing**: Utilize async/await for concurrent operations
- **Resource Pooling**: Connection pooling and resource management

## Configuration

### Environment Variables

Set these environment variables for proper configuration:

```bash
# Authentication
GEMINI_API_KEY=your_api_key_here

# Model settings
GEMINI_DEFAULT_MODEL=gemini-2.5-pro
GEMINI_TEMPERATURE=0.1
GEMINI_MAX_OUTPUT_TOKENS=8192

# Rate limiting
GEMINI_REQUESTS_PER_MINUTE=60
GEMINI_TOKENS_PER_MINUTE=1000000
GEMINI_CONCURRENT_REQUESTS=10

# Caching
GEMINI_ENABLE_CACHING=true
GEMINI_CACHE_TTL=3600
GEMINI_CACHE_MAX_SIZE=1000

# Performance
GEMINI_ENABLE_BATCH_PROCESSING=true
GEMINI_BATCH_SIZE=10
GEMINI_ENABLE_PARALLEL_PROCESSING=true
```

### Safety Settings

Configure content filtering and safety:

```python
from pattern_mining.config.gemini import GeminiConfig

config = GeminiConfig(
    safety_settings={
        "HARM_CATEGORY_HARASSMENT": "BLOCK_NONE",
        "HARM_CATEGORY_HATE_SPEECH": "BLOCK_NONE",
        "HARM_CATEGORY_SEXUALLY_EXPLICIT": "BLOCK_NONE",
        "HARM_CATEGORY_DANGEROUS_CONTENT": "BLOCK_NONE"
    }
)
```

## Monitoring and Metrics

### Client Metrics

The client provides comprehensive metrics:

```python
async with create_gemini_client() as client:
    metrics = client.get_metrics()
    print(f"Total requests: {metrics['total_requests']}")
    print(f"Success rate: {metrics['success_rate']:.2%}")
    print(f"Average response time: {metrics['avg_response_time']:.3f}s")
    print(f"Cache hit rate: {metrics['cache_hit_rate']:.2%}")
```

### Performance Monitoring

Monitor performance across all components:

```python
async with create_gemini_integration() as integration:
    metrics = integration.get_metrics()
    print(f"Hybrid analyses: {metrics['hybrid_analyses']}")
    print(f"Average confidence: {metrics['average_confidence']:.3f}")
    print(f"Average agreement: {metrics['average_agreement']:.3f}")
```

## Error Handling

### Retry Logic

Comprehensive retry logic with exponential backoff:

- **Transient Errors**: Automatic retry for network issues
- **Rate Limit Errors**: Intelligent backoff and retry
- **API Errors**: Graceful handling of API-specific errors
- **Timeout Errors**: Configurable timeout handling

### Fallback Mechanisms

Robust fallback mechanisms:

- **Local Model Fallback**: Fall back to local models on API failure
- **Cached Results**: Serve cached results when API is unavailable
- **Degraded Mode**: Reduced functionality when services are limited
- **Circuit Breaker**: Prevent cascade failures

## Security Considerations

### API Key Management

- **Environment Variables**: Store API keys in environment variables
- **Secret Management**: Integration with secret management services
- **Rotation**: Support for API key rotation
- **Audit Logging**: Log API key usage for security auditing

### Data Protection

- **No Data Retention**: No code data stored by default
- **Encryption**: All data encrypted in transit and at rest
- **Anonymization**: Optional code anonymization for analysis
- **Compliance**: GDPR, HIPAA, and SOC2 compliance ready

## Testing

### Unit Tests

Comprehensive unit tests cover all components:

```bash
# Run all Gemini integration tests
pytest tests/unit/test_gemini_integration.py -v

# Run specific test categories
pytest tests/unit/test_gemini_integration.py::TestGeminiClient -v
pytest tests/unit/test_gemini_integration.py::TestGeminiAnalyzer -v
```

### Integration Tests

End-to-end integration tests validate the complete pipeline:

```bash
# Run integration tests
pytest tests/integration/test_gemini_e2e.py -v
```

### Performance Tests

Performance tests ensure optimal performance:

```bash
# Run performance tests
pytest tests/performance/test_gemini_performance.py -v
```

## Troubleshooting

### Common Issues

1. **API Key Not Found**
   - Set `GEMINI_API_KEY` environment variable
   - Verify API key is valid and has proper permissions

2. **Rate Limit Exceeded**
   - Increase `GEMINI_REQUESTS_PER_MINUTE` setting
   - Implement request queuing or backoff

3. **Timeout Errors**
   - Increase `GEMINI_REQUEST_TIMEOUT` setting
   - Check network connectivity

4. **Memory Issues**
   - Reduce `GEMINI_CACHE_MAX_SIZE`
   - Enable batch processing limits

### Debug Mode

Enable debug mode for detailed logging:

```python
config = GeminiConfig(
    debug_mode=True,
    log_requests=True
)
```

## Contributing

### Development Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up environment:
```bash
export GEMINI_API_KEY=your_key_here
export GEMINI_DEBUG_MODE=true
```

3. Run tests:
```bash
pytest tests/ -v
```

### Code Style

- Follow PEP 8 guidelines
- Use type hints for all functions
- Add comprehensive docstrings
- Include error handling for all external calls

## License

This integration is part of the CCL Platform and follows the project's licensing terms.

## Support

For support and questions:
- Create an issue in the project repository
- Check the troubleshooting section
- Review the API documentation
- Contact the development team

---

*This documentation is automatically updated with each release. Last updated: January 2025*