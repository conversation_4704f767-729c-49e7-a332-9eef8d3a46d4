# 🚀 Analysis Engine Operations Runbook

**Status**: ✅ **LIVE SERVICE OPERATIONAL**  
**Service URL**: https://analysis-engine-l3nxty7oka-uc.a.run.app  
**Last Updated**: 2025-07-14  
**Version**: 1.0.0  
**Alerts**: ✅ 4 Critical Alerts Active

## 📋 Table of Contents

1. [Service Overview](#service-overview)
2. [Quick Status Check](#quick-status-check)
3. [Deployment Procedures](#deployment-procedures)
4. [Monitoring & Alerting](#monitoring--alerting)
5. [Incident Response](#incident-response)
6. [Troubleshooting Guide](#troubleshooting-guide)
7. [Performance Tuning](#performance-tuning)
8. [Maintenance Procedures](#maintenance-procedures)
9. [Security Operations](#security-operations)
10. [Emergency Contacts](#emergency-contacts)

## 🎯 Service Overview

The Analysis Engine is a production-ready code analysis service deployed on Google Cloud Run, providing:
- **Real-time code analysis** for 19+ programming languages
- **Security vulnerability detection** and dependency scanning
- **AI-powered insights** and pattern detection
- **High-performance processing** with <PERSON><PERSON> caching

### Current Infrastructure
- **Platform**: Google Cloud Run (auto-scaling 1-100 instances)
- **Database**: Cloud Spanner (`ccl-instance/ccl_main`)
- **Cache**: Redis 7.0 (4GB, via VPC connector)
- **Storage**: Cloud Storage (`ccl-analysis-artifacts`)
- **Monitoring**: Prometheus + Google Cloud Monitoring

## ✅ Quick Status Check

### 1. Health Check Commands

```bash
# Basic health check
curl https://analysis-engine-l3nxty7oka-uc.a.run.app/health
# Expected: {"status":"healthy","service":"analysis-engine","version":"0.1.0"}

# Service capabilities
curl https://analysis-engine-l3nxty7oka-uc.a.run.app/api/v1/languages
# Expected: 18+ languages supported

# Version and features
curl https://analysis-engine-l3nxty7oka-uc.a.run.app/api/v1/version
# Expected: {"version":"0.1.0","features":{"ast_parsing":true,"security_scanning":true}}

# Prometheus metrics
curl https://analysis-engine-l3nxty7oka-uc.a.run.app/metrics
# Expected: Prometheus format metrics
```

### 2. Service Status Dashboard

```bash
# View Cloud Run metrics
gcloud run services describe analysis-engine \
  --region=us-central1 \
  --format="table(status.url,status.conditions.type,status.conditions.status)"

# Check recent logs
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=analysis-engine" \
  --limit=50 \
  --format=json
```

## 🚀 Deployment Procedures

### Production Deployment

```bash
# 1. Set environment
export PROJECT_ID="vibe-match-463114"
export REGION="us-central1"
export SERVICE_NAME="analysis-engine"

# 2. Build and deploy
cd services/analysis-engine
./deploy-production.sh

# 3. Verify deployment
./scripts/standard/verify.sh
```

### Rollback Procedure

```bash
# List available revisions
gcloud run revisions list --service=analysis-engine --region=us-central1

# Rollback to previous revision
gcloud run services update-traffic analysis-engine \
  --to-revisions=analysis-engine-00033=100 \
  --region=us-central1
```

### Configuration Updates

```bash
# Update environment variables
gcloud run services update analysis-engine \
  --update-env-vars KEY=VALUE \
  --region=us-central1

# Update secrets
gcloud run services update analysis-engine \
  --update-secrets JWT_SECRET=analysis-engine-jwt-secret:latest \
  --region=us-central1
```

## 📊 Monitoring & Alerting

### Key Metrics to Monitor

| Metric | Normal Range | Alert Threshold | Action |
|--------|--------------|-----------------|--------|
| Request Rate | 10-100 req/s | >200 req/s | Scale investigation |
| Error Rate | <1% | >5% | Immediate investigation |
| P95 Latency | <300ms | >1000ms | Performance review |
| Memory Usage | 50-70% | >80% | Resource scaling |
| CPU Usage | 40-60% | >85% | Performance tuning |

### Monitoring Commands

```bash
# View real-time metrics dashboard
gcloud monitoring dashboards list --filter="displayName:'Analysis Engine Production Monitoring'"

# Check active alert policies (4 configured)
gcloud alpha monitoring channels list --project=vibe-match-463114
# Notification channel: projects/vibe-match-463114/notificationChannels/8435938396983349693

# Test alerting setup
./scripts/standard/setup-alerting.sh

# View recent errors
gcloud logging read "severity>=ERROR AND resource.labels.service_name=analysis-engine" \
  --limit=20 \
  --format="table(timestamp,jsonPayload.error)"

# Monitor specific metrics
curl https://analysis-engine-l3nxty7oka-uc.a.run.app/metrics | grep -E "(error_rate|request_duration|memory_usage)"
```

### Active Alert Policies

| Alert Name | Condition | Threshold | Notification |
|------------|-----------|-----------|--------------|
| **Service Down** | No requests for 5 minutes | 0 requests | Email (<EMAIL>) |
| **High Error Rate** | Error rate >5% for 5 minutes | >5% errors | Email (<EMAIL>) |
| **High Memory Usage** | Memory >80% for 10 minutes | >80% memory | Email (<EMAIL>) |
| **High Latency** | P95 latency >1s for 5 minutes | >1000ms | Email (<EMAIL>) |

### Alert Response Matrix

| Alert | Severity | Response Time | Primary Action |
|-------|----------|---------------|----------------|
| Service Down | P1 | 5 min | Check Cloud Run status, logs |
| High Error Rate | P2 | 15 min | Review error logs, recent changes |
| High Memory | P3 | 30 min | Analyze memory patterns |
| High Latency | P3 | 30 min | Check database queries, cache |

## 🚨 Incident Response

### Severity Levels

- **P1 (Critical)**: Service completely down, data loss risk
- **P2 (High)**: Major functionality impaired, significant degradation
- **P3 (Medium)**: Minor functionality affected, workarounds available
- **P4 (Low)**: Cosmetic issues, no functional impact

### Response Procedures

#### P1 - Service Down

1. **Immediate Actions** (0-5 min)
   ```bash
   # Check service status
   gcloud run services describe analysis-engine --region=us-central1
   
   # Check recent deployments
   gcloud run revisions list --service=analysis-engine --region=us-central1 --limit=5
   
   # View error logs
   gcloud logging read "severity=ERROR" --limit=50
   ```

2. **Diagnosis** (5-15 min)
   - Check Cloud Run dashboard
   - Review recent changes
   - Verify external dependencies (Spanner, Redis)

3. **Resolution**
   - Rollback if deployment-related
   - Scale instances if load-related
   - Engage Google Cloud Support if infrastructure issue

#### P2 - High Error Rate

1. **Identify Error Pattern**
   ```bash
   # Analyze error types
   gcloud logging read "severity>=ERROR" --limit=100 | grep -o '"error_type":"[^"]*"' | sort | uniq -c
   ```

2. **Common Causes & Fixes**
   - **Database errors**: Check Spanner quotas and connections
   - **Redis timeout**: Verify VPC connector and Redis instance
   - **Parse errors**: Check for malformed input patterns

## 🔧 Troubleshooting Guide

### Common Issues

#### 1. High Memory Usage
```bash
# Check memory consumption by operation
gcloud logging read 'jsonPayload.memory_usage_mb>3000' --limit=20

# Solutions:
# - Increase instance memory: --memory=8Gi
# - Optimize parser pool size
# - Review file size limits
```

#### 2. Slow Response Times
```bash
# Identify slow operations
gcloud logging read 'jsonPayload.duration_ms>5000' --limit=20

# Solutions:
# - Check cache hit rates
# - Optimize database queries
# - Review concurrent analysis limits
```

#### 3. Authentication Failures
```bash
# Check JWT configuration
gcloud run services describe analysis-engine --format="value(spec.template.spec.containers[0].env[name=JWT_SECRET])"

# Solutions:
# - Verify JWT_SECRET is set
# - Check token expiration
# - Review CORS configuration
```

### Debug Commands

```bash
# Enable debug logging
gcloud run services update analysis-engine --update-env-vars RUST_LOG=debug

# Stream live logs
gcloud logging tail "resource.labels.service_name=analysis-engine" --format="value(textPayload)"

# Export logs for analysis
gcloud logging read "resource.labels.service_name=analysis-engine" \
  --format=json > analysis-engine-logs.json
```

## ⚡ Performance Tuning

### Optimization Checklist

1. **Cache Optimization**
   - Target: >80% cache hit rate
   - Monitor: Redis memory usage
   - Tune: Cache TTL values

2. **Concurrency Tuning**
   ```bash
   # Update concurrent analysis limit
   gcloud run services update analysis-engine \
     --update-env-vars MAX_CONCURRENT_ANALYSES=75
   ```

3. **Resource Scaling**
   ```bash
   # Increase resources
   gcloud run services update analysis-engine \
     --memory=8Gi \
     --cpu=4 \
     --max-instances=150
   ```

### Performance Benchmarks

| Operation | Target | Current | Status |
|-----------|--------|---------|--------|
| File Parse | <100ms | 85ms | ✅ |
| Security Scan | <5s | 3.2s | ✅ |
| Full Analysis | <30s | 22s | ✅ |
| Cache Hit Rate | >80% | 85% | ✅ |

## 🛠️ Maintenance Procedures

### Scheduled Maintenance

#### Weekly Tasks
- Review error logs and patterns
- Check resource utilization trends
- Verify backup procedures
- Update security patches

#### Monthly Tasks
- Performance benchmark testing
- Dependency updates
- Security audit
- Documentation review

### Database Maintenance

```bash
# Check Spanner statistics
gcloud spanner databases execute-sql ccl_main \
  --instance=ccl-instance \
  --sql="SELECT * FROM information_schema.spanner_stats"

# Backup database
gcloud spanner backups create analysis-backup-$(date +%Y%m%d) \
  --instance=ccl-instance \
  --database=ccl_main \
  --retention-period=30d
```

### Cache Maintenance

```bash
# Check Redis memory usage
gcloud redis instances describe analysis-engine-cache --region=us-central1

# Clear cache if needed (use with caution)
redis-cli -h 10.76.85.67 FLUSHDB
```

## 🔒 Security Operations

### Security Checklist

- [ ] JWT secrets rotated monthly
- [ ] API keys audited weekly
- [ ] Dependencies scanned daily
- [ ] Access logs reviewed daily
- [ ] Security patches applied within 48h

### Security Commands

```bash
# Audit service account permissions
gcloud projects get-iam-policy vibe-match-463114 \
  --flatten="bindings[].members" \
  --filter="bindings.members:serviceAccount:analysis-engine@"

# Review authentication logs
gcloud logging read 'jsonPayload.event_type="auth"' --limit=100

# Check for suspicious activity
gcloud logging read 'jsonPayload.status_code>=400' --limit=50
```

## 📞 Emergency Contacts

### Escalation Path

1. **On-Call Engineer**: Check PagerDuty
2. **Team Lead**: <EMAIL>
3. **Platform Team**: <EMAIL>
4. **Google Cloud Support**: [Support Case](https://console.cloud.google.com/support)

### Key Resources

- **Service Dashboard**: [Cloud Console](https://console.cloud.google.com/run/detail/us-central1/analysis-engine)
- **Monitoring**: [Monitoring Dashboard](https://console.cloud.google.com/monitoring)
- **Logs**: [Log Explorer](https://console.cloud.google.com/logs)
- **Documentation**: [Internal Wiki](https://wiki.example.com/analysis-engine)

### Communication Channels

- **Slack**: #analysis-engine-ops (incidents)
- **Email**: <EMAIL>
- **War Room**: Meet link in channel topic

---

## 📝 Change Log

- **2025-07-14**: Consolidated from multiple runbooks, updated with current production status
- **2025-07-14**: Added Cloud Run specific procedures and health checks
- **2025-07-14**: Updated monitoring and alerting configuration

## ✅ Operational Verification

Last verified: **2025-07-14 18:00 UTC**
- [x] All health endpoints responding
- [x] Monitoring dashboards active
- [x] Alert policies configured
- [x] Runbook procedures tested