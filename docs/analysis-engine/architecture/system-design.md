# 📐 Analysis Engine Architecture Guide

## Table of Contents
- [Overview](#overview)
- [System Architecture](#system-architecture)
- [Core Components](#core-components)
- [Data Flow](#data-flow)
- [Technology Stack](#technology-stack)
- [Design Patterns](#design-patterns)
- [Scalability & Performance](#scalability--performance)
- [Security Architecture](#security-architecture)
- [Integration Points](#integration-points)

## Overview

The Analysis Engine is designed as a high-performance, cloud-native microservice that provides code analysis capabilities for the CCL platform. It follows domain-driven design principles with clear boundaries and responsibilities.

### Design Principles
1. **Memory Safety**: Rust's ownership model with centralized unsafe code isolation
2. **Thread Safety**: RwLock-based concurrent operations with parser pooling
3. **Fault Tolerance**: Circuit breakers, graceful degradation, comprehensive error handling
4. **Performance**: Lazy static regex compilation (100x improvement), optimized for 1M+ LOC
5. **Security**: Zero-trust architecture with DoS protection and resource limits
6. **Reliability**: Intelligent caching with git commit validation
7. **Observability**: Structured logging, metrics, health checks, and monitoring dashboards

## System Architecture

### High-Level Architecture

```
┌──────────────────────────────────────────────────────────────────┐
│                         External Clients                          │
│         (Web UI, SDK, CLI, Other Services)                      │
└───────────────────────┬──────────────────────────────────────────┘
                        │ HTTPS/WSS
┌───────────────────────▼──────────────────────────────────────────┐
│                    API Gateway Layer                              │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │   REST API  │  │  WebSocket   │  │  Health Endpoints      │ │
│  │  (Actix-web)│  │   Server     │  │  (/health, /ready)     │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
└───────────────────────┬──────────────────────────────────────────┘
                        │
┌───────────────────────▼──────────────────────────────────────────┐
│                  Authentication & Authorization                   │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │ JWT Handler │  │ API Key Auth │  │  Rate Limiter          │ │
│  │             │  │              │  │  (Redis/Memory)        │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
└───────────────────────┬──────────────────────────────────────────┘
                        │
┌───────────────────────▼──────────────────────────────────────────┐
│                    Resource Protection Layer                      │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │  File Size  │  │Parse Timeout │  │  Memory Monitor        │ │
│  │  Limiter    │  │  Enforcer    │  │  (ResourceMonitor)     │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │           Dependency Count Limiter (DependencyScanner)      │ │
│  └─────────────────────────────────────────────────────────────┘ │
└───────────────────────┬──────────────────────────────────────────┘
                        │
┌───────────────────────▼──────────────────────────────────────────┐
│                       Core Analysis Engine                        │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │  Analyzer   │  │Parser Engine │  │  Pattern Detector      │ │
│  │  Service    │  │(Tree-sitter) │  │  (AST-based)          │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │  Language   │  │  Embeddings  │  │  Progress Tracker      │ │
│  │  Detector   │  │  Generator   │  │  (Broadcast Channel)   │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
└───────────────────────┬──────────────────────────────────────────┘
                        │
┌───────────────────────▼──────────────────────────────────────────┐
│                    Storage & Caching Layer                        │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │   Spanner   │  │Cloud Storage │  │  Redis Cache           │ │
│  │  (Metadata) │  │ (Artifacts)  │  │  (Results/Patterns)    │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
└───────────────────────┬──────────────────────────────────────────┘
                        │
┌───────────────────────▼──────────────────────────────────────────┐
│                    External Services Layer                        │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │ Vertex AI   │  │   Pub/Sub    │  │  Other CCL Services    │ │
│  │(Embeddings) │  │  (Events)    │  │  (via gRPC/REST)       │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
└──────────────────────────────────────────────────────────────────┘
```

### Component Details

#### 1. API Gateway Layer
- **REST API**: Actix-web based HTTP server
- **WebSocket Server**: Real-time progress updates
- **Health Endpoints**: Kubernetes-compatible health checks

#### 2. Authentication Layer (Tower Middleware)
- **JWT Handler**: Comprehensive JWT validation with key rotation support
  - Multiple signing key management
  - Token revocation via JTI tracking
  - Device binding for enhanced security
  - Session revocation capabilities
- **API Key Auth**: Secure PBKDF2-based validation
  - 100,000 iteration hashing with unique salts
  - Efficient O(1) prefix-based lookups
  - Database-backed with expiration support
- **Rate Limiter**: Dual-layer rate limiting
  - Redis-based distributed rate limiting
  - In-memory fallback with DashMap
  - Per-user configurable limits

#### 3. Resource Protection Layer (DoS Prevention)
- **File Size Limiter**: Prevents large file attacks (10MB default)
  - Configurable via `MAX_FILE_SIZE_BYTES`
  - Checked before file reading begins
- **Parse Timeout Enforcer**: Prevents CPU exhaustion (30s default)
  - Configurable via `PARSE_TIMEOUT_SECONDS`
  - Uses tokio::time::timeout for enforcement
- **Memory Monitor**: Prevents memory exhaustion
  - ResourceMonitor tracks memory usage
  - Configurable limit via `MAX_ANALYSIS_MEMORY_MB`
- **Dependency Count Limiter**: Prevents dependency bomb attacks
  - Limits to 10K dependencies by default
  - Configurable via `MAX_DEPENDENCY_COUNT`

#### 4. Core Analysis Engine
- **Analyzer Service**: Orchestrates the analysis workflow
- **Parser Engine**: Tree-sitter based multi-language parsing
  - Parser pooling for efficiency
  - Lazy static regex compilation (100x performance improvement)
- **Pattern Detector**: AST-based pattern matching
- **Language Detector**: Automatic language detection
- **Embeddings Generator**: Vertex AI integration with circuit breaker
- **Progress Tracker**: WebSocket broadcast channels

#### 5. Storage Layer
- **Spanner**: Transactional data (users, analyses, patterns)
- **Cloud Storage**: File artifacts and analysis results
- **Redis**: Caching and rate limiting

## Core Components

### 1. Middleware Stack (`src/api/middleware/`)

The middleware layer uses Tower pattern for production-grade request handling:

#### Module Structure
```
src/api/middleware/
├── auth_layer.rs      # Tower-based authentication (1,021 lines)
├── rate_limit.rs      # Governor-based rate limiting
├── request_id.rs      # Request tracking and correlation
├── security.rs        # Comprehensive security headers
├── metrics.rs         # Prometheus metrics collection
└── mod.rs            # Clean public API
```

#### Key Features
- **Tower Middleware Pattern**: Composable, type-safe middleware stack
- **Request Extensions**: Authentication data passed via request extensions
- **Dual Authentication**: JWT tokens and API keys with unified handling
- **Security Headers**: HSTS, CSP, CORS, and other security headers
- **Metrics Collection**: Request duration, status codes, and auth metrics

### 2. Parser Module (`src/parser/`)

The parser module has been refactored from a monolithic 2,682-line file into a well-organized modular structure:

#### Module Architecture
```
src/parser/
├── mod.rs                      # Main coordination (355 lines)
├── language_registry.rs        # Type-safe language management
├── parser_pool.rs             # Parser pool with dynamic sizing
├── config.rs                  # Configuration structures
├── streaming/                 # Streaming file processing
│   ├── file_processor.rs      
│   ├── memory_monitor.rs      
│   ├── progress_reporter.rs   
│   └── hasher.rs             
├── ast/                       # AST processing
│   ├── builder.rs            
│   ├── symbol_extractor.rs   
│   └── chunk_extractor.rs    
├── strategies/                # Parsing strategies
│   ├── regular.rs            
│   ├── streaming.rs          
│   └── chunked.rs           
└── language_detection.rs      # Language detection logic
```

```rust
pub struct TreeSitterParser {
    parser_pools: Arc<RwLock<HashMap<String, Arc<ParserPool>>>>,
    config: ParserConfig,
    language_detector: LanguageDetector,
    metrics_calculator: LanguageMetricsCalculator,
    ast_builder: AstBuilder,
    symbol_extractor: SymbolExtractor,
    chunk_extractor: ChunkExtractor,
}
```

**Key Improvements**:
- **Parser Pooling**: Dynamic parser pools per language with intelligent sizing
- **Type-Safe Language Registry**: Eliminated unsafe code with proper language management
- **Streaming Support**: Dedicated streaming processor for large files with memory monitoring
- **Modular AST Processing**: Separate builders for AST, symbols, and code chunks
- **Configurable Strategies**: Different parsing strategies based on file size
- **Progress Reporting**: Real-time progress updates for long-running operations

**Supported Languages** (30+):
- Core: Rust, Python, JavaScript, TypeScript, Go, Java, C, C++
- Web: HTML, CSS, JSON, YAML, XML, TOML, Markdown
- Mobile: Swift, Kotlin, Objective-C
- Data Science: R, Julia
- Functional: Scala, Erlang, Elixir
- Systems: Zig, D
- Other: Lua, Dart, Nix, SQL, Ruby, Bash

**Performance Features**:
- Concurrent parser pools with semaphore-based limiting
- Memory monitoring with configurable thresholds
- Streaming hash calculation for large files
- Lazy static regex compilation
- Resource-aware pool sizing

### 3. Analyzer Service (`src/services/analyzer/`)

The analyzer module has been refactored from a monolithic 1,542-line file into a well-organized modular structure:

#### Module Architecture
```
src/services/analyzer/
├── mod.rs                    # Core AnalysisService struct (~250 lines)
├── repository.rs             # Repository operations (~200 lines)
├── file_collector.rs         # File collection and filtering (~130 lines)
├── file_processor.rs         # Parallel file processing (~290 lines)
├── streaming_processor.rs    # Streaming for large files (~370 lines)
├── progress.rs              # Progress tracking (~160 lines)
├── performance.rs           # Performance metrics (~290 lines)
├── storage.rs               # Storage operations (~240 lines)
├── events.rs                # Event publishing (~200 lines)
└── results.rs               # Result processing (~280 lines)
```

```rust
pub struct AnalysisService {
    parser: Arc<TreeSitterParser>,
    embeddings_service: Arc<EmbeddingsService>,
    git_service: Arc<GitService>,
    language_detector: Arc<LanguageDetector>,
    storage_client: Arc<StorageOperations>,
    spanner_client: Option<Arc<SpannerOperations>>,
    pubsub_client: Arc<PubSubOperations>,
    cache_manager: Arc<CacheManager>,
    backpressure_manager: Option<Arc<BackpressureManager>>,
}
```

**Key Improvements**:
- **Single Responsibility**: Each module handles a specific aspect of analysis
- **Enhanced Testability**: Components can be tested independently
- **Better Performance**: Streaming processor for repositories >1GB
- **Resource Management**: Integrated backpressure and performance monitoring
- **Clean Architecture**: Clear separation between orchestration and implementation

**Workflow**:
1. Repository Management: Clone, size calculation, commit tracking
2. File Collection: Smart filtering with configurable patterns
3. Parallel Processing: Adaptive batch sizing based on system resources
4. Streaming Support: Memory-efficient processing for large files
5. Progress Tracking: Real-time updates via WebSocket
6. Performance Monitoring: Resource usage tracking and optimization
7. Storage Orchestration: Multi-tier storage with Spanner/GCS/Redis
8. Event Management: PubSub integration and webhook support
9. Result Processing: Intelligent partitioning and warning generation

### 3. Pattern Detector (`src/patterns/`)

AST-based pattern detection engine:

```rust
pub struct PatternDetector {
    patterns: Vec<Pattern>,
}

pub struct Pattern {
    pub name: String,
    pub category: String,
    pub ast_query: String,  // Tree-sitter query
}
```

**Features**:
- Structural pattern matching
- No false positives from comments
- Support for complex queries
- Extensible pattern definitions

### 4. WebSocket Progress Tracker

Real-time progress updates using Tokio broadcast channels:

```rust
pub struct ProgressBroadcaster {
    channels: Arc<RwLock<HashMap<String, Sender<ProgressUpdate>>>>,
}

pub struct ProgressUpdate {
    pub analysis_id: String,
    pub status: AnalysisStatus,
    pub percentage: f32,
    pub message: String,
}
```

## Data Flow

### 1. Analysis Request Flow

```
Client Request → API Gateway → Authentication → Rate Limiting
    ↓
Analyzer Service → Download Repository → Language Detection
    ↓
Parse Files (Parallel) → AST Generation → Pattern Detection
    ↓
Generate Embeddings → Store Results → Publish Events
    ↓
WebSocket Updates → Client
```

### 2. Intelligent Caching Strategy

```
Request → Check Redis Cache with Git Commit Validation
    ↓ (miss or stale)
Get Current Commit Hash → Compare with Cached Hash
    ↓ (different)
Process → Store in Cache with Commit Hash
    ↓
Return Result

Cache Structure:
{
  "commit_hash": "abc123...",
  "analyses": [...],
  "cached_at": "2025-01-08T10:00:00Z"
}

Cache Keys:
- analysis:{repo_url}:{branch} (with commit validation)
- patterns:{pattern_id}:{file_hash}
- embeddings:{content_hash}
```

### 3. Error Handling Flow

```
Error Occurs → Log with Context → Determine Severity
    ↓
Recoverable? → Yes → Retry with Backoff → Continue
    ↓ No
Return Error Response → Update Analysis Status → Notify Client
```

## Technology Stack

### Core Technologies
- **Language**: Rust 1.70+
- **Web Framework**: Actix-web 4.0
- **Async Runtime**: Tokio
- **Parser**: Tree-sitter
- **Serialization**: Serde

### Infrastructure
- **Container**: Docker with multi-stage builds
- **Orchestration**: Cloud Run (managed Kubernetes)
- **CI/CD**: Cloud Build
- **Monitoring**: Cloud Logging, Cloud Monitoring

### Google Cloud Services
- **Spanner**: Distributed SQL database
- **Cloud Storage**: Object storage for artifacts
- **Vertex AI**: ML embeddings and models
- **Pub/Sub**: Event streaming
- **Secret Manager**: Secure credential storage

### External Dependencies
- **Redis**: Caching and rate limiting
- **JWT**: firebase-admin-sdk
- **HTTP Client**: reqwest with rustls

## Design Patterns

### 1. Repository Pattern
All data access goes through repository interfaces:

```rust
#[async_trait]
pub trait AnalysisRepository {
    async fn create(&self, analysis: &Analysis) -> Result<()>;
    async fn get(&self, id: &str) -> Result<Option<Analysis>>;
    async fn update_status(&self, id: &str, status: AnalysisStatus) -> Result<()>;
}
```

### 2. Circuit Breaker Pattern
Prevents cascading failures in external service calls:

```rust
pub struct CircuitBreaker<T> {
    failure_threshold: u32,
    recovery_timeout: Duration,
    state: Arc<Mutex<CircuitState>>,
}
```

### 3. Builder Pattern
Complex object construction with validation:

```rust
pub struct AnalysisRequestBuilder {
    repository_url: Option<String>,
    branch: Option<String>,
    patterns: Vec<String>,
    languages: Vec<String>,
}
```

### 4. Observer Pattern
WebSocket progress updates via broadcast channels:

```rust
pub trait ProgressObserver {
    fn on_progress(&self, update: ProgressUpdate);
    fn on_complete(&self, result: AnalysisResult);
    fn on_error(&self, error: AnalysisError);
}
```

## Scalability & Performance

### Horizontal Scaling
- Stateless design allows unlimited instances
- Load balancing via Cloud Run
- Shared state in Redis/Spanner

### Performance Optimizations
1. **Parallel Processing**: Rayon for CPU-bound tasks
2. **Streaming I/O**: Tokio for large files
3. **Batch Processing**: Configurable batch sizes
4. **Memory Pooling**: Reuse allocations
5. **Lazy Loading**: Parse only what's needed

### Resource Limits
- Max file size: 100MB
- Max repository size: 1GB
- Request timeout: 5 minutes
- Memory limit: 4GB per instance
- CPU limit: 4 vCPUs

### Caching Strategy
- L1 Cache: In-memory LRU (per instance)
- L2 Cache: Redis (shared)
- L3 Cache: Cloud Storage (persistent)

## Security Architecture

### Defense in Depth
1. **Network Security**: VPC, firewall rules
2. **Application Security**: Input validation, sanitization
3. **Data Security**: Encryption at rest and in transit
4. **Access Control**: RBAC with JWT claims
5. **Audit Logging**: All actions logged

### Authentication Flow
```
Client → JWT Token → Validate Signature → Check Expiry
    ↓
Extract Claims → Verify User in DB → Check Permissions
    ↓
Rate Limit Check → Process Request
```

### Security Headers
```rust
middleware::DefaultHeaders::new()
    .header("X-Content-Type-Options", "nosniff")
    .header("X-Frame-Options", "DENY")
    .header("X-XSS-Protection", "1; mode=block")
    .header("Strict-Transport-Security", "max-age=31536000")
```

## Integration Points

### 1. Inbound Integrations
- **Web UI**: REST API and WebSocket
- **CLI**: REST API with streaming
- **SDK**: Language-specific clients
- **CI/CD**: Webhook endpoints

### 2. Outbound Integrations
- **Query Intelligence**: Pattern results
- **Pattern Mining**: Analysis data
- **Marketplace**: Pattern definitions
- **Notification Service**: Status updates

### 3. Event Schema
```json
{
  "eventId": "uuid",
  "eventType": "analysis.completed",
  "timestamp": "2024-01-01T00:00:00Z",
  "payload": {
    "analysisId": "uuid",
    "repositoryUrl": "https://github.com/org/repo",
    "patternsFound": 42,
    "duration": 4523
  }
}
```

## Deployment Architecture

### Cloud Run Configuration
```yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: analysis-engine
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/execution-environment: gen2
    spec:
      containers:
        - image: gcr.io/project/analysis-engine
          resources:
            limits:
              memory: 4Gi
              cpu: "4"
          env:
            - name: RUST_LOG
              value: info
```

### Multi-Region Strategy
- Primary: us-central1
- Secondary: europe-west1, asia-northeast1
- Data replication via Spanner
- Cross-region load balancing

---

## 🚀 Future Enhancement Architecture

The Analysis Engine is planned for major enhancements across six phases, transforming it from a high-performance AST parser into an AI-powered code intelligence platform.

### Phase 1: AI-Enhanced Intelligence Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        AI-Enhanced Analysis Engine                               │
├─────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────┐  ┌─────────────────────┐  ┌─────────────────────────┐ │
│  │   ASTSDL Model      │  │   LLM Integration   │  │  Predictive Analysis    │ │
│  │   (Deep Learning)   │  │   (GPT-4/Claude)    │  │  Engine                 │ │
│  │                     │  │                     │  │                         │ │
│  │ • Semantic Patterns │  │ • Code Intent       │  │ • Quality Forecasting   │ │
│  │ • Functionality     │  │ • Complexity        │  │ • Performance Impact    │ │
│  │ • Confidence Score  │  │ • Recommendations   │  │ • Vulnerability Risks   │ │
│  └─────────────────────┘  └─────────────────────┘  └─────────────────────────┘ │
│                                      │                                         │
│  ┌─────────────────────┐  ┌─────────────────────┐  ┌─────────────────────────┐ │
│  │  Traditional AST    │  │  Reinforcement      │  │  Historical Analysis   │ │
│  │  Pattern Detection  │  │  Learning Optimizer │  │  Data Repository       │ │
│  │  (Fallback)         │  │                     │  │                         │ │
│  └─────────────────────┘  └─────────────────────┘  └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### Phase 2: Performance Revolution Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                    Distributed Analysis Architecture                            │
├─────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────┐  ┌─────────────────────┐  ┌─────────────────────────┐ │
│  │  Analysis           │  │  Incremental        │  │  Streaming              │ │
│  │  Coordinator        │  │  Parser             │  │  Analyzer               │ │
│  │                     │  │                     │  │                         │ │
│  │ • Task Partitioning │  │ • Git Diff Analysis │  │ • Chunk Processing      │ │
│  │ • Load Balancing    │  │ • AST Diff/Merge    │  │ • Memory Management     │ │
│  │ • Result Aggregation│  │ • Cache Management  │  │ • Backpressure Control  │ │
│  └─────────────────────┘  └─────────────────────┘  └─────────────────────────┘ │
│                                      │                                         │
│  ┌─────────────────────┐  ┌─────────────────────┐  ┌─────────────────────────┐ │
│  │  Worker Pool        │  │  Distributed Cache  │  │  Performance Monitor   │ │
│  │  (Auto-scaling)     │  │  (Redis Cluster)    │  │  (Metrics & Alerting)  │ │
│  └─────────────────────┘  └─────────────────────┘  └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### Phase 3: Advanced Security Intelligence Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        Security Intelligence Platform                           │
├─────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────┐  ┌─────────────────────┐  ┌─────────────────────────┐ │
│  │  ML-Enhanced SAST   │  │  IAST Integration   │  │  Threat Intelligence   │ │
│  │                     │  │                     │  │                         │ │
│  │ • ML Classifier     │  │ • Code Instrumentation│ • CVE Correlation      │ │
│  │ • False Positive    │  │ • Attack Simulation │  │ • Real-time Updates    │ │
│  │   Filter            │  │ • Runtime Detection │  │ • Severity Scoring     │ │
│  │ • 90% Accuracy      │  │ • Execution Analysis│  │ • Automated Remediation│ │
│  └─────────────────────┘  └─────────────────────┘  └─────────────────────────┘ │
│                                      │                                         │
│  ┌─────────────────────┐  ┌─────────────────────┐  ┌─────────────────────────┐ │
│  │  Zero-Day Detection │  │  Behavioral Analysis│  │  Security Dashboard    │ │
│  │  (Anomaly Detection)│  │  (Pattern Deviation)│  │  (Unified View)        │ │
│  └─────────────────────┘  └─────────────────────┘  └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### Phase 4: Universal Language Support Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                     Universal Language Parser System                            │
├─────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────┐  ┌─────────────────────┐  ┌─────────────────────────┐ │
│  │  Language Detector  │  │  Parser Fallback    │  │  Emerging Language     │ │
│  │  (Auto-detection)   │  │  Chain              │  │  Support               │ │
│  │                     │  │                     │  │                         │ │
│  │ • File Analysis     │  │ • Tree-sitter (1st) │  │ • Zig, Carbon, Mojo    │ │
│  │ • Content Analysis  │  │ • Custom Adapter    │  │ • V, Nim, Julia        │ │
│  │ • Confidence Score  │  │ • LLM Fallback      │  │ • Dynamic Registration │ │
│  └─────────────────────┘  └─────────────────────┘  └─────────────────────────┘ │
│                                      │                                         │
│  ┌─────────────────────┐  ┌─────────────────────┐  ┌─────────────────────────┐ │
│  │  Grammar Analyzer   │  │  Parser Factory     │  │  Language Registry     │ │
│  │  (Rule Generation)  │  │  (Auto-generation)  │  │  (35+ Languages)       │ │
│  └─────────────────────┘  └─────────────────────┘  └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### Phase 5: Cloud-Native Microservices Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                      Microservices Service Mesh                                │
├─────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────┐  ┌─────────────────────┐  ┌─────────────────────────┐ │
│  │  Parser Service     │  │  Pattern Service    │  │  Security Service      │ │
│  │  (Language Support) │  │  (AI Detection)     │  │  (Vulnerability Scan)  │ │
│  │                     │  │                     │  │                         │ │
│  │ • Auto-scaling      │  │ • ML Models         │  │ • SAST/DAST/IAST       │ │
│  │ • Load Balancing    │  │ • Pattern Cache     │  │ • Threat Intelligence  │ │
│  │ • Health Monitoring │  │ • Confidence Score  │  │ • Zero-day Detection   │ │
│  └─────────────────────┘  └─────────────────────┘  └─────────────────────────┘ │
│                                      │                                         │
│  ┌─────────────────────┐  ┌─────────────────────┐  ┌─────────────────────────┐ │
│  │  Orchestrator       │  │  Cache Service      │  │  Monitoring & Alerts   │ │
│  │  Service            │  │  (Distributed)      │  │  (Observability)       │ │
│  │                     │  │                     │  │                         │ │
│  │ • Workflow Mgmt     │  │ • Redis Cluster     │  │ • Prometheus/Grafana   │ │
│  │ • Result Aggregation│  │ • Intelligent TTL   │  │ • Jaeger Tracing       │ │
│  │ • Circuit Breakers  │  │ • Multi-level Cache │  │ • Centralized Logging  │ │
│  └─────────────────────┘  └─────────────────────┘  └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### Phase 6: Collaborative Intelligence Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                    Real-time Collaborative Platform                            │
├─────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────┐  ┌─────────────────────┐  ┌─────────────────────────┐ │
│  │  Real-time Analysis │  │  Collaboration      │  │  Knowledge Graph       │ │
│  │  Service            │  │  Engine             │  │  (Neo4j)               │ │
│  │                     │  │                     │  │                         │ │
│  │ • Live Code Changes │  │ • Shared Workspace  │  │ • Code Relationships   │ │
│  │ • Incremental       │  │ • Team Insights     │  │ • Pattern Connections  │ │
│  │   Analysis          │  │ • Conflict Resolution│ • Architecture Graphs   │ │
│  │ • WebSocket Scaling │  │ • Cursor Sharing    │  │ • Recommendation Engine│ │
│  └─────────────────────┘  └─────────────────────┘  └─────────────────────────┘ │
│                                      │                                         │
│  ┌─────────────────────┐  ┌─────────────────────┐  ┌─────────────────────────┐ │
│  │  IDE Integrations   │  │  CI/CD Pipeline     │  │  Developer Tools       │ │
│  │  (Plugins)          │  │  Integration        │  │  (CLI, Git Hooks)      │ │
│  │                     │  │                     │  │                         │ │
│  │ • VS Code, IntelliJ │  │ • GitHub Actions    │  │ • Pre-commit Analysis  │ │
│  │ • Vim/Neovim, Emacs │  │ • GitLab CI/CD      │  │ • Code Review Support  │ │
│  │ • Real-time Feedback│  │ • Jenkins, Azure    │  │ • Learning Paths       │ │
│  └─────────────────────┘  └─────────────────────┘  └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### Enhanced Performance Targets

| Metric | Current | Phase 1 | Phase 2 | Phase 3 | Phase 4 | Phase 5 | Phase 6 |
|--------|---------|---------|---------|---------|---------|---------|---------|
| **Analysis Speed** | 4.5 min | 3.5 min | 90 sec | 75 sec | 60 sec | 45 sec | 30 sec |
| **Pattern Accuracy** | 95% | 97% | 98% | 99% | 99.2% | 99.5% | 99.8% |
| **Language Support** | 19 | 22 | 25 | 28 | 35+ | 35+ | 35+ |
| **False Positives** | 5% | 3% | 2% | 1% | 0.5% | 0.3% | 0.1% |
| **Concurrent Analysis** | 50 | 100 | 250 | 500 | 1000 | 2000 | 5000 |
| **Memory per Instance** | 4GB | 4GB | 3GB | 2.5GB | 2GB | 1.5GB | 1GB |
| **API Response Time** | 100ms | 80ms | 60ms | 40ms | 30ms | 20ms | 10ms |

### Technology Evolution

```
Current Stack → Enhanced Stack
─────────────────────────────────────────────────────────────────────────────────
Rust + Tree-sitter → AI-Enhanced Rust + ML Models + LLM Integration
Single Service → Distributed Microservices + Service Mesh
Spanner + Redis → Multi-cloud + Distributed Cache + Knowledge Graph
Basic Patterns → AI Pattern Detection + Predictive Analysis
REST + WebSocket → Real-time Collaboration + IDE Integration
Manual Scaling → Auto-scaling + Edge Computing
```

This enhanced architecture transforms the Analysis Engine from a high-performance code parser into an intelligent, AI-powered platform that revolutionizes how developers understand and work with code.

---

## Security Analysis Module (`src/services/security/`)

**Status**: ✅ **COMPLETED** - The security module has been successfully refactored from a monolithic 3,840-line file into a modular, maintainable architecture with 15 focused modules.

### Module Architecture
```
src/services/security/
├── mod.rs                      # Main orchestration (200 lines)
├── types.rs                    # Shared types and traits (150 lines)
├── vulnerability/              # Vulnerability detection
│   ├── mod.rs                  # Public API
│   ├── detector.rs             # Core detection logic (400 lines)
│   ├── patterns.rs             # Pattern database (850 lines)
│   └── ml_classifier.rs        # ML-enhanced classification (500 lines)
├── dependency/                 # Dependency scanning
│   ├── mod.rs                  # Public API
│   ├── scanner.rs              # Core scanning logic (400 lines)
│   ├── vulnerability_check.rs  # CVE database checking (200 lines)
│   └── parsers/                # Package manager parsers
│       ├── mod.rs              # Parser registry
│       ├── npm.rs              # Node.js packages (300 lines)
│       ├── python.rs           # Python packages (300 lines)
│       ├── cargo.rs            # Rust packages (200 lines)
│       ├── maven.rs            # Java packages (200 lines)
│       ├── gradle.rs           # Gradle builds (200 lines)
│       ├── dotnet.rs           # .NET packages (200 lines)
│       ├── php.rs              # PHP packages (200 lines)
│       ├── go.rs               # Go modules (200 lines)
│       └── ruby.rs             # Ruby gems (200 lines)
├── secrets/                    # Secrets detection
│   ├── mod.rs                  # Public API
│   ├── detector.rs             # Core detection with entropy (300 lines)
│   └── patterns.rs             # Secret patterns database (200 lines)
├── compliance/                 # Compliance checking
│   ├── mod.rs                  # Public API
│   ├── checker.rs              # Core compliance logic (300 lines)
│   └── rules.rs                # Framework rules (400 lines)
├── threat/                     # Threat modeling
│   ├── mod.rs                  # Public API
│   └── modeler.rs              # Threat modeling logic (400 lines)
└── risk/                       # Risk assessment
    ├── mod.rs                  # Public API
    └── assessor.rs             # Risk scoring logic (200 lines)
```

### Key Features
- **Modular Design**: Each component has a single responsibility
- **Enhanced Maintainability**: Files under 600 lines each
- **Better Testability**: Components can be tested in isolation
- **Comprehensive Coverage**: Supports 9 package managers and 8 compliance frameworks
- **ML-Enhanced Detection**: Optional ML classifier for vulnerability detection
- **Threat Intelligence**: Comprehensive threat modeling with business impact assessment

---

This architecture provides a solid foundation for a scalable, secure, and performant code analysis service. For implementation details, see the [Developer Guide](../guides/developer-guide.md) and the [Enhancement Checklist](../ENHANCEMENT_CHECKLIST.md).