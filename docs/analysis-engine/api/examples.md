# Analysis Engine API Usage Examples

This document provides comprehensive examples for using the Analysis Engine API endpoints.

## Table of Contents
- [Authentication](#authentication)
- [Health Checks](#health-checks)
- [Repository Analysis](#repository-analysis)
- [Pattern Detection](#pattern-detection)
- [Security Analysis](#security-analysis)
- [Status Monitoring](#status-monitoring)
- [Error Handling](#error-handling)

## Authentication

Most endpoints require authentication via JWT token or API key.

### Using JWT Token
```bash
# Get JWT token (if authentication endpoint is available)
TOKEN=$(curl -X POST https://analysis-engine-572735000332.us-central1.run.app/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "your-username", "password": "your-password"}' \
  | jq -r '.token')

# Use token in subsequent requests
curl -H "Authorization: Bearer $TOKEN" \
  https://analysis-engine-572735000332.us-central1.run.app/api/v1/analyses
```

### Using API Key
```bash
curl -H "x-api-key: your-api-key-here" \
  https://analysis-engine-572735000332.us-central1.run.app/api/v1/analyses
```

## Health Checks

### Basic Health Check
```bash
# Check if service is running
curl https://analysis-engine-572735000332.us-central1.run.app/health

# Expected response:
# {"status":"healthy","service":"analysis-engine","version":"0.1.0"}
```

### Detailed Readiness Check
```bash
# Check all dependencies
curl https://analysis-engine-572735000332.us-central1.run.app/health/ready

# Expected response:
# {
#   "ready": true,
#   "service": "analysis-engine",
#   "checks": {
#     "spanner": true,
#     "storage": true,
#     "pubsub": true
#   }
# }
```

## Repository Analysis

### Start a Basic Analysis
```bash
# Analyze a public repository
curl -X POST https://analysis-engine-572735000332.us-central1.run.app/api/v1/analyze \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -d '{
    "repository_url": "https://github.com/golang/example",
    "branch": "master"
  }'

# Response:
# {
#   "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
#   "status": "pending",
#   "created_at": "2025-07-14T12:00:00Z"
# }
```

### Advanced Analysis with Options
```bash
# Full analysis with all features enabled
curl -X POST https://analysis-engine-572735000332.us-central1.run.app/api/v1/analyze \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -d '{
    "repository_url": "https://github.com/rust-lang/rust-clippy",
    "branch": "master",
    "enable_patterns": true,
    "enable_embeddings": true,
    "enable_security": true,
    "languages": ["rust"],
    "exclude_paths": ["tests/", "benches/"],
    "max_file_size": 5000000,
    "timeout_seconds": 300
  }'
```

### Analyze Specific Commit
```bash
curl -X POST https://analysis-engine-572735000332.us-central1.run.app/api/v1/analyze \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -d '{
    "repository_url": "https://github.com/torvalds/linux",
    "commit_sha": "a1b2c3d4e5f6",
    "enable_patterns": true,
    "languages": ["c", "cpp"]
  }'
```

## Pattern Detection

### Quick Pattern Detection
```bash
# Detect patterns without full analysis
curl -X POST https://analysis-engine-572735000332.us-central1.run.app/api/v1/patterns/detect \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -d '{
    "repository_url": "https://github.com/expressjs/express",
    "pattern_types": ["singleton", "factory", "observer"],
    "min_confidence": 0.8
  }'
```

### Language-Specific Pattern Detection
```bash
# Python patterns
curl -X POST https://analysis-engine-572735000332.us-central1.run.app/api/v1/patterns/detect \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -d '{
    "repository_url": "https://github.com/django/django",
    "languages": ["python"],
    "pattern_types": ["decorator", "context_manager", "metaclass"],
    "include_anti_patterns": true
  }'
```

## Security Analysis

### Security-Focused Analysis
```bash
curl -X POST https://analysis-engine-572735000332.us-central1.run.app/api/v1/analyze/security \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -d '{
    "repository_url": "https://github.com/OWASP/WebGoat",
    "security_config": {
      "enable_vulnerability_detection": true,
      "enable_dependency_scanning": true,
      "enable_secrets_detection": true,
      "enable_compliance_checking": true,
      "compliance_frameworks": ["owasp", "cwe", "sans"],
      "scan_depth": "deep",
      "threat_intel_enabled": true
    }
  }'
```

### Dependency Vulnerability Scan
```bash
curl -X POST https://analysis-engine-572735000332.us-central1.run.app/api/v1/dependencies/scan \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -d '{
    "repository_url": "https://github.com/nodejs/node",
    "package_managers": ["npm", "yarn"],
    "check_transitive": true,
    "severity_threshold": "medium"
  }'
```

## Status Monitoring

### Check Analysis Status
```bash
# Get status of a specific analysis
ANALYSIS_ID="550e8400-e29b-41d4-a716-446655440000"
curl https://analysis-engine-572735000332.us-central1.run.app/api/v1/analyses/$ANALYSIS_ID/status \
  -H "x-api-key: your-api-key"

# Response:
# {
#   "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
#   "status": "in_progress",
#   "progress": 65,
#   "current_stage": "parsing_files",
#   "files_processed": 1250,
#   "total_files": 1923,
#   "started_at": "2025-07-14T12:00:00Z",
#   "estimated_completion": "2025-07-14T12:05:00Z"
# }
```

### Get Analysis Results
```bash
# Retrieve completed analysis results
curl https://analysis-engine-572735000332.us-central1.run.app/api/v1/analyses/$ANALYSIS_ID \
  -H "x-api-key: your-api-key"

# Response includes:
# - Repository metadata
# - Language breakdown
# - Code quality metrics
# - Detected patterns
# - Security vulnerabilities
# - Dependencies
```

### List User's Analyses
```bash
# Get all analyses for authenticated user
curl https://analysis-engine-572735000332.us-central1.run.app/api/v1/analyses \
  -H "x-api-key: your-api-key" \
  -G -d "limit=10" -d "offset=0" -d "status=completed"
```

## Error Handling

### Common Error Responses

#### 400 Bad Request
```json
{
  "error": "Invalid request",
  "details": "Repository URL is not valid",
  "code": "INVALID_REPOSITORY_URL"
}
```

#### 401 Unauthorized
```json
{
  "error": "Authentication required",
  "details": "Missing or invalid API key",
  "code": "AUTH_REQUIRED"
}
```

#### 429 Rate Limited
```json
{
  "error": "Rate limit exceeded",
  "details": "Too many requests. Please retry after 60 seconds",
  "retry_after": 60,
  "code": "RATE_LIMITED"
}
```

#### 500 Internal Server Error
```json
{
  "error": "Internal server error",
  "details": "An unexpected error occurred",
  "request_id": "req_123456",
  "code": "INTERNAL_ERROR"
}
```

## Advanced Usage Examples

### Batch Analysis
```bash
# Analyze multiple repositories
curl -X POST https://analysis-engine-572735000332.us-central1.run.app/api/v1/analyze/batch \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -d '{
    "repositories": [
      {"url": "https://github.com/golang/go", "branch": "master"},
      {"url": "https://github.com/rust-lang/rust", "branch": "master"},
      {"url": "https://github.com/python/cpython", "branch": "main"}
    ],
    "common_config": {
      "enable_patterns": true,
      "enable_security": true
    }
  }'
```

### Webhook Integration
```bash
# Set up webhook for analysis completion
curl -X POST https://analysis-engine-572735000332.us-central1.run.app/api/v1/webhooks \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -d '{
    "url": "https://your-server.com/webhook/analysis-complete",
    "events": ["analysis.completed", "analysis.failed"],
    "secret": "your-webhook-secret"
  }'
```

### Export Analysis Results
```bash
# Export results in different formats
curl https://analysis-engine-572735000332.us-central1.run.app/api/v1/analyses/$ANALYSIS_ID/export?format=json \
  -H "x-api-key: your-api-key" \
  -o analysis-results.json

# Supported formats: json, csv, sarif, markdown
```

## Language-Specific Examples

### Get Supported Languages
```bash
curl https://analysis-engine-572735000332.us-central1.run.app/api/v1/languages

# Response:
# {
#   "languages": [
#     {"id": "rust", "name": "Rust", "extensions": [".rs"]},
#     {"id": "go", "name": "Go", "extensions": [".go"]},
#     {"id": "python", "name": "Python", "extensions": [".py"]},
#     ...
#   ]
# }
```

### Language-Specific Configuration
```bash
# Rust-specific analysis
curl -X POST https://analysis-engine-572735000332.us-central1.run.app/api/v1/analyze \
  -H "Content-Type: application/json" \
  -H "x-api-key: your-api-key" \
  -d '{
    "repository_url": "https://github.com/tokio-rs/tokio",
    "language_config": {
      "rust": {
        "check_unsafe_blocks": true,
        "detect_panics": true,
        "analyze_macros": true,
        "cargo_features": ["full"]
      }
    }
  }'
```

## Performance Tips

1. **Use Caching**: Results are cached for 24 hours for identical requests
2. **Exclude Unnecessary Paths**: Use `exclude_paths` to skip test/vendor directories
3. **Specify Languages**: Providing `languages` array speeds up analysis
4. **Use Appropriate Timeouts**: Set `timeout_seconds` based on repository size
5. **Enable Only Needed Features**: Don't enable all features if not required

## Rate Limits

- **Unauthenticated**: 10 requests/hour
- **Authenticated**: 100 requests/hour
- **Premium**: 1000 requests/hour

Check your current rate limit status:
```bash
curl -I https://analysis-engine-572735000332.us-central1.run.app/api/v1/rate-limit \
  -H "x-api-key: your-api-key"

# Headers:
# X-RateLimit-Limit: 100
# X-RateLimit-Remaining: 95
# X-RateLimit-Reset: 1626267600
```

## Troubleshooting

### Timeout Issues
If analysis times out, try:
- Reducing repository size with `exclude_paths`
- Increasing `timeout_seconds` (max: 600)
- Using specific `languages` filter

### Large Repository Handling
For repositories >1M LOC:
- Use branch/commit specific analysis
- Enable only essential features
- Consider batch processing by directory

### Authentication Failures
- Ensure API key is valid and not expired
- Check rate limit hasn't been exceeded
- Verify API key has required permissions