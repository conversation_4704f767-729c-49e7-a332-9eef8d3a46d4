# 📚 Analysis Engine API Reference

**Base URL**: `https://analysis-engine-572735000332.us-central1.run.app`  
**API Version**: `v1`  
**Authentication**: JWT Bearer token or API Key

## 📋 Table of Contents

1. [Authentication](#authentication)
2. [Core Endpoints](#core-endpoints)
3. [Analysis Endpoints](#analysis-endpoints)
4. [Security Endpoints](#security-endpoints)
5. [Monitoring Endpoints](#monitoring-endpoints)
6. [WebSocket API](#websocket-api)
7. [Error Responses](#error-responses)
8. [Rate Limiting](#rate-limiting)

## 🔐 Authentication

The API supports two authentication methods:

### JWT Bearer Token
```http
Authorization: Bearer <jwt_token>
```

### API Key
```http
x-api-key: <api_key>
```

## 🎯 Core Endpoints

### Health Check
```http
GET /health
```

**Response**:
```json
{
  "status": "healthy",
  "service": "analysis-engine",
  "version": "0.1.0"
}
```

### Readiness Check
```http
GET /health/ready
```

**Response**:
```json
{
  "ready": true,
  "service": "analysis-engine",
  "checks": {
    "spanner": true,
    "storage": true,
    "pubsub": true
  }
}
```

### Version Information
```http
GET /api/v1/version
```

**Response**:
```json
{
  "version": "0.1.0",
  "build_time": "2025-07-14T10:00:00Z",
  "commit": "abc123def",
  "features": {
    "ai_enabled": true,
    "security_scanning": true,
    "pattern_detection": true
  }
}
```

### Supported Languages
```http
GET /api/v1/languages
```

**Response**:
```json
{
  "languages": [
    {
      "id": "rust",
      "name": "Rust",
      "extensions": [".rs"],
      "features": ["parsing", "security", "patterns"]
    },
    {
      "id": "go",
      "name": "Go",
      "extensions": [".go"],
      "features": ["parsing", "security", "patterns"]
    }
    // ... more languages
  ]
}
```

## 📊 Analysis Endpoints

### Start Analysis
```http
POST /api/v1/analyze
```

**Request Body**:
```json
{
  "repository_url": "https://github.com/owner/repo",
  "branch": "main",
  "commit_sha": "optional-commit-sha",
  "enable_patterns": true,
  "enable_embeddings": false,
  "enable_security": true,
  "languages": ["rust", "go"],
  "exclude_paths": ["tests/", "vendor/"],
  "max_file_size": 5000000,
  "timeout_seconds": 300,
  "webhook_url": "https://your-server.com/webhook"
}
```

**Response**:
```json
{
  "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "pending",
  "created_at": "2025-07-14T12:00:00Z",
  "estimated_completion": "2025-07-14T12:05:00Z",
  "websocket_url": "wss://analysis-engine.../ws/550e8400-e29b-41d4-a716-446655440000"
}
```

### Get Analysis Status
```http
GET /api/v1/analyses/{analysis_id}/status
```

**Response**:
```json
{
  "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "in_progress",
  "progress": 65,
  "current_stage": "parsing_files",
  "stages_completed": ["clone_repository", "detect_languages"],
  "stages_pending": ["security_analysis", "pattern_detection"],
  "files_processed": 1250,
  "total_files": 1923,
  "started_at": "2025-07-14T12:00:00Z",
  "estimated_completion": "2025-07-14T12:05:00Z"
}
```

### Get Analysis Results
```http
GET /api/v1/analyses/{analysis_id}
```

**Response**:
```json
{
  "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "repository": {
    "url": "https://github.com/owner/repo",
    "branch": "main",
    "commit_sha": "abc123def",
    "analyzed_at": "2025-07-14T12:00:00Z"
  },
  "summary": {
    "total_files": 1923,
    "total_lines": 250000,
    "languages": {
      "rust": 45,
      "go": 30,
      "javascript": 15,
      "other": 10
    }
  },
  "metrics": {
    "complexity": {
      "cyclomatic_complexity": 3.2,
      "cognitive_complexity": 4.5
    },
    "maintainability": {
      "maintainability_index": 75.3,
      "technical_debt_hours": 120
    },
    "quality": {
      "code_quality_score": 8.5,
      "test_coverage_estimate": 65
    }
  },
  "patterns": [
    {
      "pattern_type": "singleton",
      "occurrences": 12,
      "confidence": 0.95,
      "locations": [...]
    }
  ],
  "security": {
    "vulnerabilities": [],
    "dependencies": {
      "total": 145,
      "vulnerable": 2,
      "outdated": 23
    }
  }
}
```

### List User Analyses
```http
GET /api/v1/analyses
```

**Query Parameters**:
- `limit` (default: 20, max: 100)
- `offset` (default: 0)
- `status` (filter by status)
- `from_date` (ISO 8601)
- `to_date` (ISO 8601)

**Response**:
```json
{
  "analyses": [
    {
      "analysis_id": "...",
      "repository_url": "...",
      "status": "completed",
      "created_at": "..."
    }
  ],
  "total": 150,
  "limit": 20,
  "offset": 0
}
```

### Delete Analysis
```http
DELETE /api/v1/analyses/{analysis_id}
```

**Response**:
```json
{
  "message": "Analysis deleted successfully",
  "analysis_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

## 🔒 Security Endpoints

### Security-Focused Analysis
```http
POST /api/v1/analyze/security
```

**Request Body**:
```json
{
  "repository_url": "https://github.com/owner/repo",
  "security_config": {
    "enable_vulnerability_detection": true,
    "enable_dependency_scanning": true,
    "enable_secrets_detection": true,
    "enable_compliance_checking": true,
    "compliance_frameworks": ["owasp", "cwe", "sans"],
    "scan_depth": "deep",
    "threat_intel_enabled": true
  }
}
```

### Dependency Vulnerability Scan
```http
POST /api/v1/dependencies/scan
```

**Request Body**:
```json
{
  "repository_url": "https://github.com/owner/repo",
  "package_managers": ["npm", "pip", "cargo"],
  "check_transitive": true,
  "severity_threshold": "medium"
}
```

### Pattern Detection
```http
POST /api/v1/patterns/detect
```

**Request Body**:
```json
{
  "repository_url": "https://github.com/owner/repo",
  "pattern_types": ["singleton", "factory", "observer"],
  "languages": ["java", "python"],
  "min_confidence": 0.8
}
```

## 📈 Monitoring Endpoints

### Metrics (Prometheus Format)
```http
GET /metrics
```

### Granular Metrics
```http
GET /api/v1/metrics/granular
```

**Query Parameters**:
- `operation` - Filter by operation name
- `language` - Filter by language
- `last_minutes` - Time range
- `detailed` - Include detailed breakdowns

### Operation Metrics
```http
GET /api/v1/metrics/operations/{operation_name}
```

### Language Metrics
```http
GET /api/v1/metrics/languages
```

## 🔌 WebSocket API

### Real-time Progress Updates
```websocket
wss://analysis-engine-572735000332.us-central1.run.app/ws/{analysis_id}
```

**Connection**:
```javascript
const ws = new WebSocket('wss://analysis-engine.../ws/550e8400-e29b-41d4-a716-446655440000');
ws.onmessage = (event) => {
  const update = JSON.parse(event.data);
  console.log(update);
};
```

**Message Types**:

#### Progress Update
```json
{
  "type": "progress",
  "stage": "parsing_files",
  "progress": 45,
  "message": "Parsing file 450 of 1000",
  "timestamp": "2025-07-14T12:02:30Z"
}
```

#### Stage Complete
```json
{
  "type": "stage_complete",
  "stage": "parsing_files",
  "duration_seconds": 45,
  "files_processed": 1000,
  "timestamp": "2025-07-14T12:03:00Z"
}
```

#### Analysis Complete
```json
{
  "type": "analysis_complete",
  "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "total_duration_seconds": 180,
  "result_url": "/api/v1/analyses/550e8400-e29b-41d4-a716-446655440000"
}
```

## ❌ Error Responses

### Standard Error Format
```json
{
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Repository URL is not valid",
    "details": {
      "field": "repository_url",
      "reason": "URL must be a valid Git repository"
    }
  },
  "request_id": "req_123456789"
}
```

### Common Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `INVALID_REQUEST` | 400 | Request validation failed |
| `UNAUTHORIZED` | 401 | Missing or invalid authentication |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `RATE_LIMITED` | 429 | Rate limit exceeded |
| `INTERNAL_ERROR` | 500 | Internal server error |
| `SERVICE_UNAVAILABLE` | 503 | Service temporarily unavailable |

### Rate Limit Error
```json
{
  "error": {
    "code": "RATE_LIMITED",
    "message": "Rate limit exceeded",
    "details": {
      "limit": 100,
      "remaining": 0,
      "reset_at": "2025-07-14T13:00:00Z"
    }
  },
  "retry_after": 3600
}
```

## 🚦 Rate Limiting

### Limits
- **Unauthenticated**: 10 requests/hour
- **Authenticated**: 100 requests/hour  
- **Premium**: 1000 requests/hour

### Headers
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1626267600
```

### Best Practices
1. Cache analysis results when possible
2. Use webhooks for completion notifications
3. Batch multiple files in single analysis
4. Monitor rate limit headers
5. Implement exponential backoff for retries

## 📝 Additional Resources

- [API Usage Examples](./examples.md)
- [Client Libraries](#)
- [Postman Collection](#)
- [OpenAPI Specification](./openapi.yaml)