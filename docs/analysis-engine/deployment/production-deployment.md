# 🚀 Analysis Engine Production Deployment Guide

**Current Status**: ✅ **DEPLOYED & OPERATIONAL**  
**Service URL**: https://analysis-engine-************.us-central1.run.app  
**Last Updated**: 2025-07-14  
**Production Readiness**: 100%

## 📋 Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Infrastructure Setup](#infrastructure-setup)
4. [Deployment Process](#deployment-process)
5. [Configuration Reference](#configuration-reference)
6. [Validation & Testing](#validation--testing)
7. [Monitoring Setup](#monitoring-setup)
8. [Troubleshooting](#troubleshooting)
9. [Rollback Procedures](#rollback-procedures)
10. [Security Checklist](#security-checklist)

## 🎯 Overview

The Analysis Engine is deployed on Google Cloud Run with the following architecture:
- **Auto-scaling**: 1-100 instances based on load
- **High Performance**: 4 CPU, 4GB memory per instance
- **Global Availability**: Cloud Run managed service
- **Enterprise Security**: JWT auth, rate limiting, IAM controls

### Current Infrastructure

| Component | Resource | Status |
|-----------|----------|--------|
| **Compute** | Cloud Run (`analysis-engine`) | ✅ Operational |
| **Database** | Spanner (`ccl-instance/ccl_main`) | ✅ Connected |
| **Cache** | Redis 7.0 (`analysis-engine-cache`, 4GB) | ✅ Active |
| **Storage** | Cloud Storage (`ccl-analysis-artifacts`) | ✅ Available |
| **Network** | VPC Connector (`analysis-engine-connector`) | ✅ Configured |
| **Auth** | JWT with Secret Manager | ✅ Secured |

## 📋 Prerequisites

### Required Access
- Google Cloud Project: `vibe-match-463114`
- Roles needed:
  - `roles/run.admin` - Cloud Run deployment
  - `roles/spanner.admin` - Database setup
  - `roles/redis.admin` - Cache configuration
  - `roles/iam.serviceAccountAdmin` - Service account management

### Local Requirements
```bash
# Install required tools
brew install google-cloud-sdk
brew install docker

# Authenticate
gcloud auth login
gcloud config set project vibe-match-463114
```

### Service Account Setup
```bash
# Create service account (if not exists)
gcloud iam service-accounts create analysis-engine \
  --display-name="Analysis Engine Service Account"

# Grant required permissions
PROJECT_ID="vibe-match-463114"
SERVICE_ACCOUNT="analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com"

# Project-level permissions
for role in \
  "roles/spanner.databaseUser" \
  "roles/storage.objectAdmin" \
  "roles/pubsub.publisher" \
  "roles/pubsub.viewer" \
  "roles/cloudtrace.agent" \
  "roles/monitoring.metricWriter"
do
  gcloud projects add-iam-policy-binding ${PROJECT_ID} \
    --member="serviceAccount:${SERVICE_ACCOUNT}" \
    --role="${role}"
done

# Bucket-level permissions
gsutil iam ch serviceAccount:${SERVICE_ACCOUNT}:legacyBucketReader gs://ccl-analysis-artifacts
```

## 🏗️ Infrastructure Setup

### 1. Database Setup (Spanner)
```bash
# Create Spanner instance (if not exists)
gcloud spanner instances create ccl-instance \
  --config=regional-us-central1 \
  --description="CCL Production Instance" \
  --nodes=1

# Create database
gcloud spanner databases create ccl_main \
  --instance=ccl-instance \
  --database-dialect=GOOGLE_STANDARD_SQL

# Run migrations
cd services/analysis-engine
cargo run --bin migrate
```

### 2. Cache Setup (Redis)
```bash
# Create Redis instance
gcloud redis instances create analysis-engine-cache \
  --size=4 \
  --region=us-central1 \
  --redis-version=redis_7_0 \
  --display-name="Analysis Engine Cache"

# Get Redis IP
REDIS_IP=$(gcloud redis instances describe analysis-engine-cache \
  --region=us-central1 \
  --format="value(host)")
echo "Redis IP: ${REDIS_IP}"
```

### 3. VPC Connector Setup
```bash
# Run VPC connector setup
cd services/analysis-engine
./scripts/standard/setup-vpc-connector.sh
```

### 4. Storage Setup
```bash
# Create storage bucket
gsutil mb -p ${PROJECT_ID} -c STANDARD -l us-central1 gs://ccl-analysis-artifacts

# Set bucket permissions
gsutil iam ch serviceAccount:${SERVICE_ACCOUNT}:objectAdmin gs://ccl-analysis-artifacts
```

## 🚀 Deployment Process

### Quick Deploy (Recommended)
```bash
# Use the management script
cd services/analysis-engine
./manage.sh deploy

# Or use the production deployment script
./deploy-production.sh
```

### Manual Deployment Steps

#### 1. Build Docker Image
```bash
# Build with optimizations
docker build \
  --platform linux/amd64 \
  -t gcr.io/${PROJECT_ID}/analysis-engine:latest \
  -f Dockerfile.simple \
  .

# Configure Docker for GCR
gcloud auth configure-docker

# Push image
docker push gcr.io/${PROJECT_ID}/analysis-engine:latest
```

#### 2. Deploy to Cloud Run
```bash
# Set JWT secret (generate if first time)
JWT_SECRET=$(openssl rand -base64 32)

# Deploy service
gcloud run deploy analysis-engine \
  --image gcr.io/${PROJECT_ID}/analysis-engine:latest \
  --platform managed \
  --region us-central1 \
  --memory 4Gi \
  --cpu 4 \
  --timeout 600 \
  --max-instances 100 \
  --min-instances 1 \
  --concurrency 50 \
  --service-account ${SERVICE_ACCOUNT} \
  --vpc-connector analysis-engine-connector \
  --set-env-vars "$(cat <<EOF
GCP_PROJECT_ID=${PROJECT_ID}
SPANNER_INSTANCE_ID=ccl-instance
SPANNER_DATABASE_ID=ccl_main
REDIS_URL=redis://${REDIS_IP}:6379
STORAGE_BUCKET=ccl-analysis-artifacts
JWT_SECRET=${JWT_SECRET}
RUST_LOG=info
MAX_CONCURRENT_ANALYSES=50
MAX_FILE_SIZE_BYTES=********
PARSE_TIMEOUT_SECONDS=30
MAX_ANALYSIS_MEMORY_MB=2048
MAX_DEPENDENCY_COUNT=10000
ENABLE_AUTH=true
CORS_ORIGINS=*
EOF
)" \
  --allow-unauthenticated
```

#### 3. Verify Deployment
```bash
# Get service URL
SERVICE_URL=$(gcloud run services describe analysis-engine \
  --region=us-central1 \
  --format="value(status.url)")

# Test health endpoint
curl ${SERVICE_URL}/health

# Test readiness
curl ${SERVICE_URL}/health/ready
```

## ⚙️ Configuration Reference

### Required Environment Variables

| Variable | Description | Production Value |
|----------|-------------|------------------|
| `GCP_PROJECT_ID` | Google Cloud project | `vibe-match-463114` |
| `SPANNER_INSTANCE_ID` | Spanner instance | `ccl-instance` |
| `SPANNER_DATABASE_ID` | Spanner database | `ccl_main` |
| `REDIS_URL` | Redis connection URL | `redis://***********:6379` |
| `STORAGE_BUCKET` | GCS bucket name | `ccl-analysis-artifacts` |
| `JWT_SECRET` | JWT signing secret | *Generated* |

### Performance Configuration

| Variable | Description | Default | Production |
|----------|-------------|---------|------------|
| `MAX_CONCURRENT_ANALYSES` | Concurrent analysis limit | `50` | `50` |
| `MAX_FILE_SIZE_BYTES` | Max file size (bytes) | `********` | `10MB` |
| `PARSE_TIMEOUT_SECONDS` | Parser timeout | `30` | `30` |
| `MAX_ANALYSIS_MEMORY_MB` | Memory limit per analysis | `2048` | `2GB` |
| `MAX_DEPENDENCY_COUNT` | Max dependencies | `10000` | `10000` |

### Security Configuration

| Variable | Description | Production Value |
|----------|-------------|------------------|
| `ENABLE_AUTH` | Enable JWT authentication | `true` |
| `CORS_ORIGINS` | Allowed CORS origins | `*` |
| `RATE_LIMIT_PER_HOUR` | API rate limit | `1000` |
| `JWT_ROTATION_DAYS` | JWT key rotation | `7` |
| `ENABLE_AUDIT_LOGGING` | Audit logging | `true` |

## ✅ Validation & Testing

### Automated Validation
```bash
# Run comprehensive validation
cd services/analysis-engine
./scripts/standard/verify.sh
```

### Manual Health Checks
```bash
# 1. Service health
curl ${SERVICE_URL}/health

# 2. Database connectivity
curl ${SERVICE_URL}/health/ready

# 3. API functionality
curl -X POST ${SERVICE_URL}/api/v1/analyze \
  -H "Content-Type: application/json" \
  -d '{"repository_url": "https://github.com/golang/example", "branch": "master"}'

# 4. Monitoring metrics
curl ${SERVICE_URL}/metrics
```

### Load Testing
```bash
# Run production load test
./scripts/standard/load-test-production.sh
```

## 📊 Monitoring Setup

### 1. Create Monitoring Dashboard
```bash
# Set up monitoring
./scripts/standard/setup-monitoring.sh
```

### 2. Configure Alerts
```bash
# Set up alerting
./scripts/standard/setup-alerting.sh
```

### Key Metrics to Monitor
- Request rate and latency
- Error rate (target <1%)
- Memory usage (alert >80%)
- CPU utilization (alert >85%)
- Cache hit rate (target >80%)

## 🔧 Troubleshooting

### Common Issues

#### 1. Service Not Starting
```bash
# Check logs
gcloud logging read "resource.labels.service_name=analysis-engine AND severity>=ERROR" --limit=50

# Common causes:
# - Missing environment variables
# - IAM permissions
# - Database connectivity
```

#### 2. High Memory Usage
```bash
# Scale up memory
gcloud run services update analysis-engine --memory=8Gi

# Check memory metrics
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/container/memory/utilizations"'
```

#### 3. Database Connection Issues
```bash
# Verify Spanner connectivity
gcloud spanner databases execute-sql ccl_main \
  --instance=ccl-instance \
  --sql="SELECT 1"

# Check service account permissions
gcloud projects get-iam-policy ${PROJECT_ID} \
  --flatten="bindings[].members" \
  --filter="bindings.members:serviceAccount:${SERVICE_ACCOUNT}"
```

## 🔄 Rollback Procedures

### Quick Rollback
```bash
# List recent revisions
gcloud run revisions list --service=analysis-engine --region=us-central1 --limit=5

# Rollback to previous revision
gcloud run services update-traffic analysis-engine \
  --to-revisions=PREVIOUS_REVISION_NAME=100 \
  --region=us-central1
```

### Database Rollback
```bash
# Restore from backup
gcloud spanner databases restore ccl_main_restored \
  --source-backup=analysis-backup-******** \
  --instance=ccl-instance
```

## 🔒 Security Checklist

### Pre-Deployment
- [ ] JWT secret generated and stored in Secret Manager
- [ ] Service account permissions verified
- [ ] Network security (VPC connector) configured
- [ ] API rate limiting enabled

### Post-Deployment
- [ ] Authentication tested
- [ ] CORS configuration verified
- [ ] SSL/TLS endpoints confirmed
- [ ] Security headers validated
- [ ] Audit logging enabled

### Ongoing Security
- [ ] JWT secret rotation scheduled
- [ ] Dependencies scanned for vulnerabilities
- [ ] Access logs reviewed regularly
- [ ] Security patches applied promptly

## 📝 Deployment Log

### Recent Deployments
- **2025-07-14**: Initial production deployment (v1.0.0)
  - ✅ All infrastructure components deployed
  - ✅ Health checks passing
  - ✅ Load testing completed
  - ✅ Monitoring configured

## 🆘 Support

### Resources
- **Service Dashboard**: [Cloud Console](https://console.cloud.google.com/run/detail/us-central1/analysis-engine)
- **Logs**: [Log Explorer](https://console.cloud.google.com/logs)
- **Monitoring**: [Metrics Explorer](https://console.cloud.google.com/monitoring)

### Contacts
- **Team**: <EMAIL>
- **Slack**: #analysis-engine-ops
- **On-Call**: Check PagerDuty

---

**Note**: This guide consolidates information from multiple deployment documents. Always verify current production values before making changes.