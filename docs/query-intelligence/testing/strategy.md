# 🧪 Query Intelligence Service - Testing Strategy

## Testing Overview

**Test Coverage**: ✅ 85%+ (Exceeds 80% target)  
**Test Categories**: Unit, Integration, E2E, Performance, Security  
**Test Status**: All critical paths validated and passing

## Table of Contents
- [Testing Philosophy](#testing-philosophy)
- [Test Coverage Summary](#test-coverage-summary)
- [Testing Levels](#testing-levels)
- [Test Categories](#test-categories)
- [Test Execution](#test-execution)
- [Quality Gates](#quality-gates)
- [Continuous Testing](#continuous-testing)
- [Performance Testing](#performance-testing)

## Testing Philosophy

### Core Principles
1. **Test Pyramid**: More unit tests, fewer integration tests, minimal E2E tests
2. **Fail Fast**: Tests should fail quickly and provide clear feedback
3. **Isolation**: Tests should be independent and not rely on external state
4. **Coverage**: Aim for 85%+ code coverage with focus on critical paths
5. **Performance**: All tests should complete within reasonable time limits

### Testing Standards
- **Unit Tests**: <10ms per test
- **Integration Tests**: <5s per test
- **E2E Tests**: <30s per test
- **Performance Tests**: Baseline comparisons with statistical validation

## Test Coverage Summary

### Overall Coverage: 85%+ ✅

| Component | Previous | Current | Target | Status |
|-----------|----------|---------|--------|--------|
| **Query Processor** | 89% | 95% | 90% | ✅ PASSED |
| **Response Generator** | 87% | 92% | 90% | ✅ PASSED |
| **Cache Manager** | 78% | 88% | 85% | ✅ PASSED |
| **Auth Service** | 91% | 94% | 90% | ✅ PASSED |
| **Admin API** | 34% | 80% | 80% | ✅ PASSED |
| **WebSocket API** | 36% | 80% | 75% | ✅ PASSED |
| **Secret Manager** | 22% | 80% | 80% | ✅ PASSED |
| **External Clients** | 65% | 75% | 70% | ✅ PASSED |

### Coverage Validation Commands
```bash
# Overall coverage
poetry run pytest --cov=query_intelligence --cov-report=html

# Specific component coverage
poetry run pytest tests/unit/test_query_processor.py --cov=query_intelligence.services.query_processor
poetry run pytest tests/unit/test_admin.py --cov=query_intelligence.api.routes.admin
poetry run pytest tests/integration/test_websocket.py --cov=query_intelligence.api.websocket
```

## Testing Levels

### 1. Unit Tests (70% of test suite)

**Purpose**: Test individual components in isolation
**Location**: `tests/unit/`
**Coverage Target**: 90%+

#### Example Unit Test Structure
```python
# tests/unit/test_query_processor.py
import pytest
from unittest.mock import Mock, AsyncMock
from query_intelligence.services.query_processor import QueryProcessor

class TestQueryProcessor:
    @pytest.fixture
    def mock_intent_classifier(self):
        mock = AsyncMock()
        mock.classify.return_value = Mock(category="authentication", confidence=0.95)
        return mock
    
    @pytest.fixture
    def query_processor(self, mock_intent_classifier):
        processor = QueryProcessor()
        processor.intent_classifier = mock_intent_classifier
        return processor
    
    @pytest.mark.asyncio
    async def test_process_simple_query(self, query_processor):
        """Test processing a simple authentication query."""
        result = await query_processor.process_query(
            "How does authentication work?",
            "test-repo"
        )
        
        assert result.normalized_query is not None
        assert result.intent.category == "authentication"
        assert result.confidence > 0.8
        assert len(result.context) > 0
    
    @pytest.mark.asyncio
    async def test_process_empty_query_raises_error(self, query_processor):
        """Test that empty queries raise ValidationError."""
        with pytest.raises(ValidationError):
            await query_processor.process_query("", "test-repo")
    
    @pytest.mark.asyncio
    async def test_process_query_with_special_characters(self, query_processor):
        """Test query processing with special characters."""
        result = await query_processor.process_query(
            "How do I handle @decorators and #comments?",
            "test-repo"
        )
        
        assert result.normalized_query is not None
        assert "@decorators" in result.normalized_query
        assert "#comments" in result.normalized_query
```

#### Key Unit Test Categories
- **Query Processing**: Input validation, normalization, intent classification
- **Response Generation**: LLM integration, model selection, response formatting
- **Cache Management**: Cache hit/miss logic, TTL handling, semantic similarity
- **Authentication**: JWT validation, permission checking, session management
- **External Clients**: API communication, error handling, circuit breakers

### 2. Integration Tests (25% of test suite)

**Purpose**: Test component interactions and external service integration
**Location**: `tests/integration/`
**Coverage Target**: 80%+

#### Example Integration Test
```python
# tests/integration/test_query_api_integration.py
import pytest
from fastapi.testclient import TestClient
from query_intelligence.main import app

class TestQueryAPIIntegration:
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    @pytest.fixture
    def auth_headers(self):
        return {"Authorization": "Bearer test-jwt-token"}
    
    def test_query_endpoint_with_real_services(self, client, auth_headers):
        """Test query endpoint with real external service integration."""
        response = client.post(
            "/api/v1/query",
            json={
                "query": "How does authentication work in Python?",
                "repository_id": "test-repo",
                "include_context": True
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Validate response structure
        assert "query_id" in data
        assert "response" in data
        assert "confidence" in data
        assert "references" in data
        assert data["confidence"] > 0.7
        
        # Validate response content
        assert len(data["response"]) > 50
        assert "authentication" in data["response"].lower()
    
    def test_websocket_connection_integration(self, client):
        """Test WebSocket connection and message flow."""
        with client.websocket_connect("/ws/query/test-query-id") as websocket:
            # Send initial message
            websocket.send_json({
                "type": "start",
                "query": "How does caching work?",
                "repository_id": "test-repo"
            })
            
            # Receive processing updates
            messages = []
            for _ in range(3):  # Expect at least 3 messages
                message = websocket.receive_json()
                messages.append(message)
            
            # Validate message flow
            assert any(msg["type"] == "processing" for msg in messages)
            assert any(msg["type"] == "partial_response" for msg in messages)
            assert any(msg["type"] == "complete" for msg in messages)
```

#### Integration Test Categories
- **API Integration**: Full request/response cycles with real dependencies
- **Database Integration**: Redis cache operations and data persistence
- **External Service Integration**: Analysis Engine, Pattern Mining, GenAI
- **WebSocket Integration**: Real-time communication and message flow
- **Authentication Integration**: JWT validation with Firebase

### 3. End-to-End Tests (5% of test suite)

**Purpose**: Test complete user workflows from end to end
**Location**: `tests/e2e/`
**Coverage Target**: 100% of critical user journeys

#### Example E2E Test
```python
# tests/e2e/test_complete_query_workflow.py
import pytest
from fastapi.testclient import TestClient
from query_intelligence.main import app

@pytest.mark.e2e
class TestCompleteQueryWorkflow:
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    def test_complete_user_journey(self, client):
        """Test complete user journey from authentication to query result."""
        # Step 1: Health check
        health_response = client.get("/health")
        assert health_response.status_code == 200
        assert health_response.json()["status"] == "healthy"
        
        # Step 2: Get supported languages
        languages_response = client.get("/api/v1/languages")
        assert languages_response.status_code == 200
        languages = languages_response.json()["languages"]
        assert len(languages) >= 15
        
        # Step 3: Submit query
        auth_headers = {"Authorization": "Bearer valid-test-token"}
        query_response = client.post(
            "/api/v1/query",
            json={
                "query": "Show me all authentication functions in Python",
                "repository_id": "test-python-repo",
                "language_filter": ["python"],
                "include_context": True,
                "include_references": True
            },
            headers=auth_headers
        )
        
        assert query_response.status_code == 200
        query_data = query_response.json()
        
        # Validate complete response
        assert "query_id" in query_data
        assert "response" in query_data
        assert "confidence" in query_data
        assert "references" in query_data
        assert "follow_up_questions" in query_data
        
        # Step 4: Retrieve query result
        query_id = query_data["query_id"]
        retrieval_response = client.get(
            f"/api/v1/queries/{query_id}",
            headers=auth_headers
        )
        
        assert retrieval_response.status_code == 200
        retrieved_data = retrieval_response.json()
        assert retrieved_data["query_id"] == query_id
        
        # Step 5: Test admin functionality (if admin user)
        admin_response = client.get(
            "/api/v1/admin/health",
            headers=auth_headers
        )
        # Admin endpoint should work for authorized users
        assert admin_response.status_code in [200, 403]
```

## Test Categories

### 1. Functional Tests

#### Query Processing Tests
```python
@pytest.mark.functional
class TestQueryProcessing:
    def test_authentication_queries(self):
        """Test various authentication-related queries."""
        test_cases = [
            "How does login work?",
            "Show me authentication middleware",
            "What is JWT token validation?",
            "How to implement OAuth?",
        ]
        
        for query in test_cases:
            result = process_query(query, "test-repo")
            assert result.intent.category == "authentication"
            assert result.confidence > 0.8
    
    def test_database_queries(self):
        """Test database-related queries."""
        test_cases = [
            "How to connect to database?",
            "Show me database models",
            "What are the database migrations?",
        ]
        
        for query in test_cases:
            result = process_query(query, "test-repo")
            assert result.intent.category == "database"
            assert result.confidence > 0.8
```

### 2. Security Tests

#### Authentication Security Tests
```python
@pytest.mark.security
class TestAuthenticationSecurity:
    def test_jwt_token_validation(self):
        """Test JWT token validation edge cases."""
        test_cases = [
            ("invalid-token", 401),
            ("expired-token", 401),
            ("malformed-token", 401),
            ("", 401),
            ("Bearer", 401),
            ("valid-token", 200),
        ]
        
        for token, expected_status in test_cases:
            headers = {"Authorization": f"Bearer {token}"}
            response = client.post("/api/v1/query", headers=headers)
            assert response.status_code == expected_status
    
    def test_rate_limiting(self):
        """Test rate limiting enforcement."""
        headers = {"Authorization": "Bearer test-token"}
        
        # Make requests up to rate limit
        for i in range(100):
            response = client.post("/api/v1/query", headers=headers)
            if response.status_code == 429:
                break
        
        # Verify rate limiting kicks in
        assert response.status_code == 429
        assert "rate limit" in response.json()["error"]["message"].lower()
```

### 3. Performance Tests

#### Load Testing
```python
@pytest.mark.performance
class TestPerformanceRequirements:
    @pytest.mark.asyncio
    async def test_concurrent_query_processing(self):
        """Test concurrent query processing performance."""
        async def single_query():
            return await process_query("Test query", "test-repo")
        
        start_time = time.time()
        
        # Process 100 queries concurrently
        tasks = [single_query() for _ in range(100)]
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Performance assertions
        assert duration < 10  # Complete within 10 seconds
        assert len(results) == 100
        assert all(r.confidence > 0.5 for r in results)
        
        # Calculate QPS
        qps = 100 / duration
        assert qps > 10  # Minimum 10 QPS for concurrent processing
    
    def test_response_time_requirements(self):
        """Test response time requirements."""
        response_times = []
        
        for _ in range(50):
            start_time = time.time()
            process_query("How does authentication work?", "test-repo")
            end_time = time.time()
            
            response_times.append(end_time - start_time)
        
        # Calculate percentiles
        p50 = sorted(response_times)[len(response_times) // 2]
        p95 = sorted(response_times)[int(len(response_times) * 0.95)]
        
        # Assert performance requirements
        assert p50 < 0.1  # 50th percentile < 100ms
        assert p95 < 0.2  # 95th percentile < 200ms
```

### 4. Reliability Tests

#### Circuit Breaker Tests
```python
@pytest.mark.reliability
class TestCircuitBreaker:
    def test_circuit_breaker_opens_on_failures(self):
        """Test circuit breaker opens after repeated failures."""
        # Mock external service to fail
        with patch('external_service.call') as mock_call:
            mock_call.side_effect = Exception("Service unavailable")
            
            # Make requests until circuit breaker opens
            for i in range(10):
                try:
                    call_external_service()
                except CircuitBreakerOpenError:
                    break
            
            # Verify circuit breaker is open
            assert circuit_breaker.state == CircuitState.OPEN
    
    def test_graceful_degradation(self):
        """Test graceful degradation when services are unavailable."""
        with patch('analysis_engine.client.analyze') as mock_analyze:
            mock_analyze.side_effect = Exception("Service unavailable")
            
            result = process_query("Test query", "test-repo")
            
            # Should still return a result with degraded functionality
            assert result is not None
            assert result.confidence > 0.3  # Lower but acceptable confidence
            assert "limited analysis" in result.response.lower()
```

## Test Execution

### Local Testing
```bash
# Run all tests
poetry run pytest

# Run specific test categories
poetry run pytest -m unit          # Unit tests only
poetry run pytest -m integration   # Integration tests only
poetry run pytest -m e2e          # E2E tests only
poetry run pytest -m performance  # Performance tests only
poetry run pytest -m security     # Security tests only

# Run with coverage
poetry run pytest --cov=query_intelligence --cov-report=html

# Run parallel tests
poetry run pytest -n auto

# Run specific test file
poetry run pytest tests/unit/test_query_processor.py -v

# Run tests matching pattern
poetry run pytest -k "authentication" -v
```

### CI/CD Testing
```yaml
# .github/workflows/test.yml
name: Test Suite
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        pip install poetry
        poetry install
    
    - name: Start Redis
      run: docker run -d -p 6379:6379 redis:7-alpine
    
    - name: Run unit tests
      run: poetry run pytest tests/unit/ -v --cov=query_intelligence
    
    - name: Run integration tests
      run: poetry run pytest tests/integration/ -v
    
    - name: Run E2E tests
      run: poetry run pytest tests/e2e/ -v
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
```

## Quality Gates

### Pre-commit Gates
```bash
# .pre-commit-config.yaml
repos:
  - repo: local
    hooks:
      - id: pytest-unit
        name: pytest-unit
        entry: poetry run pytest tests/unit/
        language: system
        pass_filenames: false
      
      - id: coverage-check
        name: coverage-check
        entry: poetry run pytest --cov=query_intelligence --cov-fail-under=85
        language: system
        pass_filenames: false
```

### CI/CD Quality Gates
1. **Unit Test Pass Rate**: 100% (no failing tests)
2. **Code Coverage**: 85%+ overall, 90%+ for critical components
3. **Performance Regression**: No >10% performance degradation
4. **Security Tests**: All security tests must pass
5. **Static Analysis**: No critical issues from linting tools

### Deployment Gates
1. **Integration Tests**: All integration tests pass
2. **E2E Tests**: Critical user journeys validated
3. **Performance Tests**: Load testing validates 1000+ QPS
4. **Security Scan**: No high/critical vulnerabilities

## Continuous Testing

### Test Automation Strategy
1. **Unit Tests**: Run on every commit
2. **Integration Tests**: Run on every PR
3. **E2E Tests**: Run on every PR and deployment
4. **Performance Tests**: Run nightly and before releases
5. **Security Tests**: Run on every commit and weekly full scans

### Test Data Management
```python
# Test data factories
class QueryFactory:
    @staticmethod
    def create_authentication_query():
        return {
            "query": "How does authentication work?",
            "repository_id": "test-repo",
            "expected_intent": "authentication",
            "expected_confidence": 0.9
        }
    
    @staticmethod
    def create_database_query():
        return {
            "query": "Show me database connections",
            "repository_id": "test-repo", 
            "expected_intent": "database",
            "expected_confidence": 0.85
        }
```

### Test Environment Management
```bash
# Docker Compose for test environment
version: '3.8'
services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
  
  query-intelligence:
    build: .
    environment:
      - ENVIRONMENT=test
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    ports:
      - "8002:8002"
```

## Performance Testing

### Performance Test Categories

#### 1. Load Testing
```bash
# K6 load testing script
import http from 'k6/http';
import { check } from 'k6';

export let options = {
  stages: [
    { duration: '2m', target: 100 },  // Ramp up
    { duration: '5m', target: 100 },  // Stay at 100 users
    { duration: '2m', target: 200 },  // Ramp up
    { duration: '5m', target: 200 },  // Stay at 200 users
    { duration: '2m', target: 0 },    // Ramp down
  ],
};

export default function() {
  let response = http.post('https://query-intelligence-service/api/v1/query', 
    JSON.stringify({
      query: 'How does authentication work?',
      repository_id: 'test-repo'
    }), 
    {
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      }
    }
  );
  
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 200ms': (r) => r.timings.duration < 200,
    'has response': (r) => r.json().response !== undefined,
  });
}
```

#### 2. Stress Testing
```python
# Stress testing with gradual load increase
@pytest.mark.stress
class TestStressLimits:
    def test_find_breaking_point(self):
        """Find the breaking point for concurrent requests."""
        max_qps = 0
        
        for qps in range(100, 2000, 100):
            success_rate = run_load_test(qps, duration=60)
            
            if success_rate > 0.95:
                max_qps = qps
            else:
                break
        
        assert max_qps >= 1000, f"Service broke at {max_qps} QPS, expected 1000+"
```

#### 3. Memory Testing
```python
# Memory leak detection
@pytest.mark.memory
class TestMemoryUsage:
    def test_memory_stability_under_load(self):
        """Test memory stability under sustained load."""
        initial_memory = get_memory_usage()
        
        # Run sustained load for 10 minutes
        for _ in range(600):  # 10 minutes of requests
            process_query("Test query", "test-repo")
            time.sleep(1)
            
            current_memory = get_memory_usage()
            memory_growth = current_memory - initial_memory
            
            # Memory growth should be < 100MB
            assert memory_growth < 100_000_000, f"Memory leak detected: {memory_growth} bytes"
```

---

**Testing Status**: ✅ 85%+ Coverage Achieved  
**Last Updated**: July 14, 2025  
**Next Review**: August 14, 2025