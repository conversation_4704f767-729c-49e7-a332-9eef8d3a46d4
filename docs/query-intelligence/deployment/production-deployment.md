# 🚀 Query Intelligence Service - Production Deployment Guide

## Deployment Status

**Current Status**: ✅ **DEPLOYED TO PRODUCTION**  
**Service URL**: https://query-intelligence-l3nxty7oka-uc.a.run.app  
**Deployment Date**: July 14, 2025  
**Version**: 2.0.0

## Table of Contents
- [Prerequisites](#prerequisites)
- [Environment Setup](#environment-setup)
- [Deployment Process](#deployment-process)
- [Configuration](#configuration)
- [Validation](#validation)
- [Monitoring Setup](#monitoring-setup)
- [Rollback Procedures](#rollback-procedures)
- [Troubleshooting](#troubleshooting)

## Prerequisites

### Required Tools
```bash
# Install required tools
pip install poetry
npm install -g @google-cloud/cli
pip install docker
```

### Google Cloud Setup
```bash
# Authenticate with Google Cloud
gcloud auth login
gcloud config set project vibe-match-463114

# Configure Docker for Google Cloud
gcloud auth configure-docker
```

### Required Permissions
- Cloud Run Admin
- Storage Admin
- Secret Manager Secret Accessor
- Service Account User

## Environment Setup

### 1. Environment Variables

Create production environment file:
```bash
# Production environment variables
cat > .env.production << EOF
# Core Configuration
ENVIRONMENT=production
PORT=8002
HOST=0.0.0.0

# Google Cloud Configuration
GCP_PROJECT_ID=vibe-match-463114
GCP_REGION=us-central1
GOOGLE_API_KEY=\${GOOGLE_API_KEY_SECRET}

# Authentication
JWT_SECRET=\${JWT_SECRET_SECRET}
FIREBASE_PROJECT_ID=vibe-match-463114

# Redis Configuration
REDIS_URL=redis://redis-cache-service:6379
REDIS_MAX_CONNECTIONS=100

# Service URLs
ANALYSIS_ENGINE_URL=https://analysis-engine-************.us-central1.run.app
PATTERN_MINING_URL=https://pattern-mining-service.ccl.dev

# Performance Configuration
MAX_QUERY_LENGTH=10000
RATE_LIMIT_PER_HOUR=1000
SEMANTIC_CACHE_ENABLED=true
CACHE_TTL_SECONDS=3600

# Security Configuration
ENABLE_WEBSOCKET_AUTH=true
ENABLE_SECURITY_CONTROLS=true
ENABLE_RATE_LIMITING=true

# Monitoring
ENABLE_METRICS=true
LOG_LEVEL=INFO
STRUCTURED_LOGGING=true
EOF
```

### 2. Secret Management

Store secrets in Google Secret Manager:
```bash
# Create secrets
echo "your-google-api-key" | gcloud secrets create google-api-key --data-file=-
echo "your-jwt-secret" | gcloud secrets create jwt-secret --data-file=-

# Grant access to Cloud Run service account
gcloud secrets add-iam-policy-binding google-api-key \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/secretmanager.secretAccessor"

gcloud secrets add-iam-policy-binding jwt-secret \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/secretmanager.secretAccessor"
```

## Deployment Process

### 1. Automated Deployment Script

Create deployment script:
```bash
#!/bin/bash
# deploy-production.sh

set -e

echo "🚀 Starting Query Intelligence Service Production Deployment"

# Build and push container
echo "📦 Building container image..."
docker build -t gcr.io/vibe-match-463114/query-intelligence:latest .
docker push gcr.io/vibe-match-463114/query-intelligence:latest

# Deploy to Cloud Run
echo "🚀 Deploying to Cloud Run..."
gcloud run deploy query-intelligence \
  --image gcr.io/vibe-match-463114/query-intelligence:latest \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --port 8002 \
  --memory 16Gi \
  --cpu 4 \
  --concurrency 1000 \
  --max-instances 100 \
  --min-instances 5 \
  --execution-environment gen2 \
  --cpu-boost \
  --set-env-vars="ENVIRONMENT=production" \
  --set-secrets="GOOGLE_API_KEY=google-api-key:latest,JWT_SECRET=jwt-secret:latest" \
  --service-account <EMAIL>

echo "✅ Deployment completed successfully!"

# Validate deployment
echo "🔍 Validating deployment..."
SERVICE_URL=$(gcloud run services describe query-intelligence --region us-central1 --format "value(status.url)")
curl -f "$SERVICE_URL/health" || (echo "❌ Health check failed" && exit 1)

echo "✅ Deployment validation successful!"
echo "🌐 Service URL: $SERVICE_URL"
```

### 2. Manual Deployment Steps

Step-by-step deployment:

```bash
# 1. Clone repository
git clone https://github.com/episteme/ccl.git
cd ccl/services/query-intelligence

# 2. Build container
docker build -t gcr.io/vibe-match-463114/query-intelligence:v2.0.0 .

# 3. Push to registry
docker push gcr.io/vibe-match-463114/query-intelligence:v2.0.0

# 4. Deploy to Cloud Run
gcloud run deploy query-intelligence \
  --image gcr.io/vibe-match-463114/query-intelligence:v2.0.0 \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --port 8002 \
  --memory 16Gi \
  --cpu 4 \
  --concurrency 1000 \
  --max-instances 100 \
  --min-instances 5
```

## Configuration

### Cloud Run Service Configuration

```yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: query-intelligence
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/cpu-throttling: "false"
        autoscaling.knative.dev/minScale: "5"
        autoscaling.knative.dev/maxScale: "100"
        run.googleapis.com/memory: "16Gi"
        run.googleapis.com/cpu: "4"
    spec:
      containerConcurrency: 1000
      containers:
      - image: gcr.io/vibe-match-463114/query-intelligence:latest
        ports:
        - containerPort: 8002
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: PORT
          value: "8002"
        - name: GCP_PROJECT_ID
          value: "vibe-match-463114"
        - name: GCP_REGION
          value: "us-central1"
        - name: GOOGLE_API_KEY
          valueFrom:
            secretKeyRef:
              key: latest
              name: google-api-key
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              key: latest
              name: jwt-secret
        resources:
          limits:
            cpu: "4"
            memory: "16Gi"
        livenessProbe:
          httpGet:
            path: /health
            port: 8002
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8002
          initialDelaySeconds: 10
          periodSeconds: 5
      serviceAccountName: <EMAIL>
```

### Service Account Configuration

```bash
# Create service account
gcloud iam service-accounts create query-intelligence \
  --display-name="Query Intelligence Service"

# Grant required permissions
gcloud projects add-iam-policy-binding vibe-match-463114 \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/secretmanager.secretAccessor"

gcloud projects add-iam-policy-binding vibe-match-463114 \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/logging.logWriter"

gcloud projects add-iam-policy-binding vibe-match-463114 \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/monitoring.metricWriter"
```

## Validation

### 1. Health Check Validation

```bash
# Basic health check
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/health

# Expected response:
# {"status":"healthy","timestamp":"2025-07-14T12:00:00Z"}

# Readiness check
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/ready

# Expected response:
# {"status":"ready","checks":{"redis":"ok","analysis_engine":"ok"},"timestamp":"2025-07-14T12:00:00Z"}
```

### 2. API Endpoint Validation

```bash
# Test query endpoint (requires authentication)
curl -X POST https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "query": "How does authentication work?",
    "repository_id": "test-repo"
  }'

# Test languages endpoint
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/languages

# Test version endpoint
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/version
```

### 3. Performance Validation

```bash
# Load testing
cd tests/performance
npm install
npm run test:load

# WebSocket testing
cd tests/websocket_concurrent
npm install
npm run test:concurrent

# Memory profiling
python tests/performance/memory_profiling.py
```

## Monitoring Setup

### 1. Cloud Monitoring Alerts

Create monitoring alerts:
```bash
# High error rate alert
gcloud alpha monitoring policies create --policy-from-file=monitoring/error-rate-alert.yaml

# High latency alert
gcloud alpha monitoring policies create --policy-from-file=monitoring/latency-alert.yaml

# Memory usage alert
gcloud alpha monitoring policies create --policy-from-file=monitoring/memory-alert.yaml
```

### 2. Logging Configuration

```bash
# Create log-based metrics
gcloud logging metrics create query_error_rate \
  --description="Query processing error rate" \
  --log-filter='resource.type="cloud_run_revision" AND resource.labels.service_name="query-intelligence" AND severity>=ERROR'

gcloud logging metrics create query_latency \
  --description="Query processing latency" \
  --log-filter='resource.type="cloud_run_revision" AND resource.labels.service_name="query-intelligence" AND jsonPayload.query_duration_ms>0'
```

### 3. Uptime Monitoring

```bash
# Create uptime check
gcloud monitoring uptime-checks create \
  --display-name="Query Intelligence Health Check" \
  --monitored-resource-type=cloud_run_revision \
  --uri=https://query-intelligence-l3nxty7oka-uc.a.run.app/health \
  --check-interval=60s \
  --timeout=10s
```

## Rollback Procedures

### 1. Automated Rollback

```bash
#!/bin/bash
# rollback.sh

set -e

echo "🔄 Starting rollback procedure..."

# Get current and previous revisions
CURRENT_REVISION=$(gcloud run revisions list --service=query-intelligence --region=us-central1 --limit=1 --format="value(metadata.name)")
PREVIOUS_REVISION=$(gcloud run revisions list --service=query-intelligence --region=us-central1 --limit=2 --format="value(metadata.name)" | tail -n 1)

echo "📋 Current revision: $CURRENT_REVISION"
echo "📋 Previous revision: $PREVIOUS_REVISION"

# Perform rollback
echo "⏪ Rolling back to previous revision..."
gcloud run services update-traffic query-intelligence \
  --to-revisions="$PREVIOUS_REVISION=100" \
  --region us-central1

# Validate rollback
echo "🔍 Validating rollback..."
sleep 30
curl -f https://query-intelligence-l3nxty7oka-uc.a.run.app/health || (echo "❌ Rollback validation failed" && exit 1)

echo "✅ Rollback completed successfully!"
```

### 2. Manual Rollback Steps

```bash
# 1. List revisions
gcloud run revisions list --service=query-intelligence --region=us-central1

# 2. Update traffic to previous revision
gcloud run services update-traffic query-intelligence \
  --to-revisions=query-intelligence-00002-abc=100 \
  --region us-central1

# 3. Validate health
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/health
```

## Troubleshooting

### Common Issues

#### 1. Service Won't Start
```bash
# Check logs
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=query-intelligence" --limit=50

# Check environment variables
gcloud run services describe query-intelligence --region=us-central1 --format="export"

# Common fixes:
# - Verify secrets are accessible
# - Check service account permissions
# - Validate environment variables
```

#### 2. High Latency
```bash
# Check memory usage
gcloud monitoring metrics list --filter="metric.type=run.googleapis.com/container/memory/utilizations"

# Check CPU usage
gcloud monitoring metrics list --filter="metric.type=run.googleapis.com/container/cpu/utilizations"

# Solutions:
# - Increase memory allocation
# - Increase CPU allocation
# - Check Redis connectivity
```

#### 3. Authentication Errors
```bash
# Verify secrets
gcloud secrets versions access latest --secret=google-api-key
gcloud secrets versions access latest --secret=jwt-secret

# Check service account permissions
gcloud projects get-iam-policy vibe-match-463114 --flatten="bindings[].members" --filter="bindings.members:<EMAIL>"

# Solutions:
# - Regenerate API keys
# - Update secret values
# - Verify IAM permissions
```

### Emergency Procedures

#### 1. Service Down
```bash
# Immediate rollback
./rollback.sh

# If rollback fails, redeploy previous working image
gcloud run deploy query-intelligence \
  --image gcr.io/vibe-match-463114/query-intelligence:v1.9.0 \
  --region us-central1
```

#### 2. High Error Rate
```bash
# Check recent logs
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=query-intelligence AND severity>=ERROR" --limit=100

# Scale down to minimum instances
gcloud run services update query-intelligence \
  --min-instances=1 \
  --region us-central1

# Monitor and investigate
```

## Production Checklist

### Pre-Deployment
- [ ] Environment variables configured
- [ ] Secrets stored in Secret Manager
- [ ] Service account permissions granted
- [ ] Docker image built and pushed
- [ ] Health checks implemented
- [ ] Monitoring alerts configured

### Deployment
- [ ] Deploy to staging first
- [ ] Validate staging deployment
- [ ] Deploy to production
- [ ] Validate production deployment
- [ ] Monitor for 30 minutes

### Post-Deployment
- [ ] Health checks passing
- [ ] Performance within SLA
- [ ] Error rates acceptable
- [ ] Monitoring alerts configured
- [ ] Documentation updated
- [ ] Team notified

---

**Production Status**: ✅ DEPLOYED  
**Last Updated**: July 14, 2025  
**Next Review**: August 14, 2025