# Query Intelligence Service - Production Operations Guide

## 📋 Overview

This guide provides comprehensive production operations procedures, best practices, and operational knowledge for the Query Intelligence service. It serves as the definitive reference for day-to-day operations, maintenance, and optimization.

**Service Details:**
- **Service Name**: Query Intelligence
- **Environment**: Production (Google Cloud Run)
- **Version**: 2.0.0
- **URL**: https://query-intelligence.ccl.dev
- **Team**: Query Intelligence Team
- **Owner**: <EMAIL>

## 🎯 Production Service Overview

### Architecture Summary
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │────│  Query Intel    │────│  Analysis Engine│
│   (Cloud Run)   │    │   Service       │    │   (Rust API)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                               │                        │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Redis Cache   │    │ Pattern Mining  │
                       │ (Memorystore)   │    │   Service       │
                       └─────────────────┘    └─────────────────┘
                               │
                       ┌─────────────────┐
                       │   Pinecone      │
                       │ (Vector Store)  │
                       └─────────────────┘
```

### Key Metrics (Current Status)
- **Availability**: 99.95%
- **Response Time (P95)**: 85ms
- **Throughput**: 1000+ QPS
- **Error Rate**: <0.1%
- **Cache Hit Rate**: 75%+
- **Test Coverage**: 85%+
- **Security Score**: 95/100

## 🔧 Daily Operations

### Morning Health Check Routine

**Time**: 08:00 UTC daily

**Procedure**:
```bash
#!/bin/bash
# Daily health check routine

echo "=== Daily Health Check - $(date) ==="

# 1. Service health verification
echo "Checking service health..."
SERVICE_STATUS=$(curl -s https://query-intelligence.ccl.dev/health | jq -r '.status')
if [ "$SERVICE_STATUS" = "healthy" ]; then
    echo "✅ Service health: Healthy"
else
    echo "❌ Service health: $SERVICE_STATUS"
    # Trigger alert
fi

# 2. Performance metrics check
echo "Checking performance metrics..."
RESPONSE_TIME=$(gcloud monitoring read \
  --filter='metric.type="run.googleapis.com/request_latencies"' \
  --start-time="-1h" \
  --format="value(points[0].value.percentileValue[2].value)")

if (( $(echo "$RESPONSE_TIME < 0.1" | bc -l) )); then
    echo "✅ Response time: ${RESPONSE_TIME}s (Good)"
else
    echo "⚠️ Response time: ${RESPONSE_TIME}s (Elevated)"
fi

# 3. Error rate check
echo "Checking error rate..."
ERROR_COUNT=$(gcloud monitoring read \
  --filter='metric.type="run.googleapis.com/request_count" AND metric.labels.response_code_class="5xx"' \
  --start-time="-1h" \
  --format="value(points[0].value.int64Value)")

if [ "$ERROR_COUNT" -lt 10 ]; then
    echo "✅ Error rate: Low ($ERROR_COUNT errors/hour)"
else
    echo "⚠️ Error rate: High ($ERROR_COUNT errors/hour)"
fi

# 4. Cache performance check
echo "Checking cache performance..."
CACHE_STATS=$(curl -s -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/api/v1/admin/cache/stats)
HIT_RATE=$(echo "$CACHE_STATS" | jq -r '.hit_rate')

if (( $(echo "$HIT_RATE > 0.7" | bc -l) )); then
    echo "✅ Cache hit rate: ${HIT_RATE} (Good)"
else
    echo "⚠️ Cache hit rate: ${HIT_RATE} (Low)"
fi

# 5. Circuit breaker status
echo "Checking circuit breakers..."
CB_STATUS=$(curl -s https://query-intelligence.ccl.dev/circuit-breakers | jq -r '.circuit_breakers | keys[]')
for breaker in $CB_STATUS; do
    STATE=$(curl -s https://query-intelligence.ccl.dev/circuit-breakers | jq -r ".circuit_breakers.$breaker.state")
    if [ "$STATE" = "CLOSED" ]; then
        echo "✅ Circuit breaker $breaker: CLOSED"
    else
        echo "⚠️ Circuit breaker $breaker: $STATE"
    fi
done

echo "=== Daily Health Check Complete ==="
```

### Weekly Operations Review

**Time**: Monday 09:00 UTC weekly

**Procedure**:
```bash
#!/bin/bash
# Weekly operations review

echo "=== Weekly Operations Review - $(date) ==="

# 1. Performance trends analysis
echo "Analyzing performance trends..."
gcloud monitoring read \
  --filter='metric.type="run.googleapis.com/request_latencies"' \
  --start-time="-7d" \
  --format="table(resource.labels.service_name, points[0].value.percentileValue[2].value)" > weekly_performance.txt

# 2. Error pattern analysis
echo "Analyzing error patterns..."
gcloud logs read \
  "resource.type=cloud_run_revision AND severity>=ERROR" \
  --freshness=7d \
  --format="json" | jq -r '.jsonPayload.error' | sort | uniq -c | sort -nr > weekly_errors.txt

# 3. Capacity planning review
echo "Reviewing capacity metrics..."
gcloud monitoring read \
  --filter='metric.type="run.googleapis.com/container/cpu/utilizations"' \
  --start-time="-7d" \
  --format="table(resource.labels.service_name, points[0].value.doubleValue)" > weekly_cpu.txt

# 4. Cache optimization review
echo "Reviewing cache performance..."
WEEKLY_CACHE_STATS=$(curl -s -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/api/v1/admin/cache/stats)
echo "$WEEKLY_CACHE_STATS" | jq '.' > weekly_cache_stats.json

# 5. Security metrics review
echo "Reviewing security metrics..."
gcloud logs read \
  "resource.type=cloud_run_revision AND (httpRequest.status=401 OR httpRequest.status=403)" \
  --freshness=7d \
  --format="json" | jq -r '.httpRequest.remoteIp' | sort | uniq -c | sort -nr > weekly_security.txt

# 6. Generate weekly report
echo "Generating weekly report..."
cat > weekly_report.md << EOF
# Weekly Operations Report - $(date +%Y-%m-%d)

## Performance Summary
- Average Response Time: $(grep -o '[0-9.]*' weekly_performance.txt | awk '{sum+=$1} END {print sum/NR}')s
- CPU Utilization: $(grep -o '[0-9.]*' weekly_cpu.txt | awk '{sum+=$1} END {print sum/NR}')%
- Cache Hit Rate: $(echo "$WEEKLY_CACHE_STATS" | jq -r '.hit_rate')

## Error Analysis
$(head -10 weekly_errors.txt)

## Security Events
$(head -10 weekly_security.txt)

## Recommendations
- Review error patterns for optimization opportunities
- Monitor capacity trends for scaling decisions
- Optimize cache settings based on hit rates
EOF

echo "✅ Weekly report generated: weekly_report.md"
```

### Monthly Maintenance Tasks

**Time**: First Monday of each month

**Procedure**:
```bash
#!/bin/bash
# Monthly maintenance tasks

echo "=== Monthly Maintenance - $(date) ==="

# 1. Security audit
echo "Conducting security audit..."
gcloud projects get-iam-policy vibe-match-463114 \
  --flatten="bindings[].members" \
  --filter="bindings.members:query-intelligence-sa@*" > monthly_permissions.txt

# 2. Dependency updates check
echo "Checking for dependency updates..."
cd /path/to/query-intelligence
poetry show --outdated > monthly_dependencies.txt

# 3. Certificate expiry check
echo "Checking certificate expiry..."
gcloud compute ssl-certificates list \
  --filter="name:query-intelligence" \
  --format="table(name, expire_time)" > monthly_certificates.txt

# 4. Backup verification
echo "Verifying backup procedures..."
gcloud run services describe query-intelligence \
  --region=us-central1 \
  --format="export" > monthly_config_backup.yaml

# 5. Performance baseline update
echo "Updating performance baselines..."
gcloud monitoring read \
  --filter='metric.type="run.googleapis.com/request_latencies"' \
  --start-time="-30d" \
  --format="json" > monthly_performance_baseline.json

# 6. Cost optimization review
echo "Reviewing cost optimization..."
gcloud billing budgets list \
  --billing-account=BILLING_ACCOUNT_ID \
  --filter="displayName:query-intelligence" > monthly_costs.txt

echo "✅ Monthly maintenance complete"
```

## 📊 Performance Optimization

### Cache Optimization

**Objective**: Maintain >75% cache hit rate

**Monitoring**:
```bash
# Check cache performance
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/api/v1/admin/cache/stats

# Monitor cache memory usage
redis-cli -h $REDIS_HOST info memory

# Analyze cache patterns
redis-cli -h $REDIS_HOST --scan --pattern "cache:*" | head -100
```

**Optimization Actions**:
```bash
# Optimize TTL settings
gcloud run services update query-intelligence \
  --set-env-vars="CACHE_TTL=1800,SEMANTIC_CACHE_TTL=3600" \
  --region=us-central1

# Clear fragmented cache
curl -X DELETE -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/api/v1/admin/cache/fragmented

# Warm cache with frequent queries
curl -X POST -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/api/v1/admin/cache/warm
```

### Response Time Optimization

**Target**: P95 response time <100ms

**Monitoring**:
```bash
# Check response time distribution
gcloud monitoring read \
  --filter='metric.type="run.googleapis.com/request_latencies"' \
  --start-time="-1h" \
  --format="table(points[0].value.percentileValue[].value)"

# Analyze slow queries
gcloud logs read \
  "resource.type=cloud_run_revision AND jsonPayload.response_time>0.1" \
  --limit=50 \
  --format="json"
```

**Optimization Actions**:
```bash
# Scale up resources for better performance
gcloud run services update query-intelligence \
  --cpu=8 \
  --memory=32Gi \
  --region=us-central1

# Enable CPU boost
gcloud run services update query-intelligence \
  --cpu-boost \
  --region=us-central1

# Optimize concurrent request handling
gcloud run services update query-intelligence \
  --concurrency=10 \
  --region=us-central1
```

### Cost Optimization

**Objective**: Optimize resource usage while maintaining performance

**Monitoring**:
```bash
# Check resource utilization
gcloud monitoring read \
  --filter='metric.type="run.googleapis.com/container/cpu/utilizations"' \
  --start-time="-24h"

# Check instance scaling patterns
gcloud run services describe query-intelligence \
  --region=us-central1 \
  --format="value(spec.template.metadata.annotations)"
```

**Optimization Actions**:
```bash
# Adjust instance scaling based on usage patterns
gcloud run services update query-intelligence \
  --min-instances=3 \
  --max-instances=100 \
  --region=us-central1

# Optimize memory allocation
gcloud run services update query-intelligence \
  --memory=8Gi \
  --region=us-central1
```

## 🔐 Security Operations

### Access Control Management

**Daily Tasks**:
```bash
# Review access logs
gcloud logs read \
  "resource.type=cloud_run_revision AND (httpRequest.status=401 OR httpRequest.status=403)" \
  --freshness=1d \
  --format="json"

# Check for suspicious patterns
gcloud logs read \
  "resource.type=cloud_run_revision AND httpRequest.status=429" \
  --freshness=1d \
  --format="json"
```

**Weekly Tasks**:
```bash
# Review service account permissions
gcloud projects get-iam-policy vibe-match-463114 \
  --flatten="bindings[].members" \
  --filter="bindings.members:query-intelligence-sa@*"

# Check secret rotation schedule
gcloud secrets versions list jwt-secret-key \
  --format="table(name, created_time, state)"
```

**Monthly Tasks**:
```bash
# Rotate JWT secret
gcloud secrets versions add jwt-secret-key \
  --data-file=new-jwt-secret.key

# Update service with new secret
gcloud run services update query-intelligence \
  --update-secrets=JWT_SECRET_KEY=jwt-secret-key:latest \
  --region=us-central1

# Review and update security policies
gcloud alpha monitoring policies list \
  --filter="displayName:'Query Intelligence Security'"
```

### Security Monitoring

**Real-time Monitoring**:
```bash
# Monitor authentication failures
gcloud logs read \
  "resource.type=cloud_run_revision AND jsonPayload.event='jwt_verification_failed'" \
  --follow

# Monitor rate limiting
gcloud logs read \
  "resource.type=cloud_run_revision AND jsonPayload.event='rate_limit_exceeded'" \
  --follow
```

**Security Alerts**:
```yaml
# Alert conditions
- High authentication failure rate (>10/minute)
- Unusual IP access patterns
- Rate limiting frequently triggered
- Admin endpoint access attempts
- Circuit breaker security events
```

## 📈 Capacity Planning

### Scaling Decision Matrix

| Metric | Current | Warning | Critical | Action |
|--------|---------|---------|----------|---------|
| CPU Usage | <60% | 60-80% | >80% | Scale up CPU |
| Memory Usage | <70% | 70-85% | >85% | Scale up memory |
| Response Time | <100ms | 100-200ms | >200ms | Scale instances |
| Error Rate | <0.1% | 0.1-1% | >1% | Scale + investigate |
| Cache Hit Rate | >75% | 65-75% | <65% | Optimize cache |

### Scaling Procedures

**Scale Up**:
```bash
# Increase instance count
gcloud run services update query-intelligence \
  --min-instances=10 \
  --max-instances=300 \
  --region=us-central1

# Increase resource allocation
gcloud run services update query-intelligence \
  --cpu=8 \
  --memory=32Gi \
  --region=us-central1
```

**Scale Down**:
```bash
# Decrease instance count
gcloud run services update query-intelligence \
  --min-instances=5 \
  --max-instances=200 \
  --region=us-central1

# Decrease resource allocation
gcloud run services update query-intelligence \
  --cpu=4 \
  --memory=16Gi \
  --region=us-central1
```

### Capacity Forecasting

**Monthly Capacity Review**:
```bash
# Analyze traffic trends
gcloud monitoring read \
  --filter='metric.type="run.googleapis.com/request_count"' \
  --start-time="-30d" \
  --format="json" > monthly_traffic.json

# Analyze resource utilization trends
gcloud monitoring read \
  --filter='metric.type="run.googleapis.com/container/cpu/utilizations"' \
  --start-time="-30d" \
  --format="json" > monthly_cpu.json

# Generate capacity forecast
python capacity_forecast.py monthly_traffic.json monthly_cpu.json
```

## 🚨 Emergency Procedures

### Service Degradation Response

**Immediate Actions (0-5 minutes)**:
```bash
# Enable fallback mode
gcloud run services update query-intelligence \
  --set-env-vars="ENABLE_FALLBACK_MODE=true" \
  --region=us-central1

# Scale up resources
gcloud run services update query-intelligence \
  --min-instances=20 \
  --max-instances=500 \
  --region=us-central1

# Reset circuit breakers
curl -X POST -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/api/v1/admin/circuit-breakers/reset
```

**Investigation Actions (5-15 minutes)**:
```bash
# Check service health
curl -s https://query-intelligence.ccl.dev/health | jq '.'

# Check recent logs
gcloud logs read \
  "resource.type=cloud_run_revision AND severity>=ERROR" \
  --limit=100 \
  --format="json"

# Check external dependencies
curl -f https://analysis-engine.ccl.dev/health
curl -f https://pattern-mining.ccl.dev/health
redis-cli -h $REDIS_HOST ping
```

### Complete Service Outage Response

**Immediate Actions (0-10 minutes)**:
```bash
# Check service status
gcloud run services describe query-intelligence \
  --region=us-central1 \
  --format="json"

# Check recent deployments
gcloud run revisions list \
  --service=query-intelligence \
  --region=us-central1 \
  --limit=5

# Rollback if needed
PREVIOUS_REVISION=$(gcloud run revisions list \
  --service=query-intelligence \
  --region=us-central1 \
  --limit=2 \
  --format="value(metadata.name)" | tail -n 1)

gcloud run services update-traffic query-intelligence \
  --to-revisions=$PREVIOUS_REVISION=100 \
  --region=us-central1
```

## 📞 Contact Information

### Team Structure

| Role | Contact | Response Time | Responsibilities |
|------|---------|---------------|------------------|
| On-call Engineer | <EMAIL> | 15 minutes | Incident response, troubleshooting |
| Senior Engineer | <EMAIL> | 30 minutes | Complex issues, architecture changes |
| Team Lead | <EMAIL> | 1 hour | Escalation, resource allocation |
| Engineering Manager | <EMAIL> | 2 hours | Business impact, strategic decisions |

### Escalation Procedures

**P0 (Critical)**:
1. Immediately page on-call engineer
2. Create incident channel #incident-qi-[timestamp]
3. Notify senior engineer within 15 minutes
4. Notify team lead within 30 minutes
5. Notify engineering manager within 1 hour

**P1 (High)**:
1. Contact on-call engineer
2. Create incident channel if needed
3. Notify senior engineer within 1 hour
4. Notify team lead within 2 hours

**P2 (Medium)**:
1. Create ticket in issue tracking system
2. Notify on-call engineer
3. Schedule resolution within 4 hours

### External Dependencies

| Service | Contact | SLA | Escalation |
|---------|---------|-----|------------|
| Analysis Engine | <EMAIL> | 99.9% | <EMAIL> |
| Pattern Mining | <EMAIL> | 99.5% | <EMAIL> |
| Google Cloud Run | GCP Support | 99.95% | Enterprise support case |
| Redis (Memorystore) | GCP Support | 99.9% | Enterprise support case |

## 📋 Operational Checklists

### Pre-Deployment Checklist

- [ ] All tests passing (85%+ coverage)
- [ ] Security scan completed
- [ ] Performance benchmarks met
- [ ] Documentation updated
- [ ] Monitoring configured
- [ ] Rollback plan prepared
- [ ] Team notified

### Post-Deployment Checklist

- [ ] Health checks passing
- [ ] Metrics collection active
- [ ] Performance within SLA
- [ ] Error rates acceptable
- [ ] Security controls functional
- [ ] Monitoring alerts configured
- [ ] Documentation updated

### Monthly Review Checklist

- [ ] Performance trends analyzed
- [ ] Security audit completed
- [ ] Capacity planning updated
- [ ] Cost optimization reviewed
- [ ] Dependencies updated
- [ ] Backup procedures verified
- [ ] Team training completed

## 📚 Knowledge Base

### Common Issues and Solutions

**Issue**: High response times
**Solution**: Check cache hit rates, scale resources, optimize queries

**Issue**: Circuit breakers opening
**Solution**: Check external services, reset breakers, implement fallbacks

**Issue**: Authentication failures
**Solution**: Check JWT secrets, verify token format, review permissions

**Issue**: Rate limiting triggered
**Solution**: Analyze traffic patterns, adjust limits, implement backoff

### Best Practices

1. **Monitoring**: Monitor key metrics continuously
2. **Alerting**: Set appropriate thresholds for alerts
3. **Scaling**: Scale proactively based on trends
4. **Security**: Rotate secrets regularly
5. **Documentation**: Keep runbooks updated
6. **Testing**: Test procedures regularly
7. **Communication**: Communicate changes clearly

### Useful Commands

```bash
# Service status
gcloud run services describe query-intelligence --region=us-central1

# Recent logs
gcloud logs read "resource.type=cloud_run_revision" --limit=100

# Metrics
gcloud monitoring read --filter='metric.type="run.googleapis.com/request_count"'

# Circuit breakers
curl https://query-intelligence.ccl.dev/circuit-breakers

# Cache stats
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/api/v1/admin/cache/stats
```

## 🎯 Continuous Improvement

### Monthly KPI Reviews

**Key Performance Indicators**:
- Availability: Target >99.95%
- Response Time: Target <100ms (P95)
- Error Rate: Target <0.1%
- Cache Hit Rate: Target >75%
- Security Score: Target >95/100

**Review Process**:
1. Collect metrics for previous month
2. Compare against targets
3. Identify improvement opportunities
4. Plan optimization initiatives
5. Update procedures as needed

### Quarterly Optimization

**Areas for Review**:
- Architecture optimization
- Performance improvements
- Security enhancements
- Cost optimization
- Process improvements

**Optimization Cycle**:
1. Identify optimization opportunities
2. Plan implementation
3. Test changes thoroughly
4. Deploy incrementally
5. Monitor results
6. Document learnings

---

**Last Updated**: July 2025  
**Review Schedule**: Monthly  
**Owner**: Query Intelligence Team  
**Next Review**: August 2025