# Query Intelligence Performance Troubleshooting Guide

## Overview

This guide helps diagnose and resolve performance issues in the Query Intelligence service. Target performance: <100ms p95 latency.

## Performance Issue Categories

### 1. High Latency (>100ms)

#### Symptoms
- Response times consistently exceed 100ms
- Users report slow query processing
- Timeout errors in logs

#### Possible Causes

1. **Model Selection Issues**
   - Complex queries using lightweight models
   - Simple queries using heavyweight models
   - Model routing disabled or misconfigured

2. **Cache Misses**
   - Low cache hit rate (<80%)
   - Cache eviction due to memory pressure
   - Semantic cache disabled

3. **External Service Bottlenecks**
   - Analysis Engine slow responses
   - Pattern Mining service overloaded
   - Google GenAI API rate limits

4. **Resource Constraints**
   - CPU throttling on Cloud Run
   - Memory pressure causing GC pauses
   - Insufficient instance scaling

#### Diagnostic Steps

1. **Check Current Performance Metrics**
```bash
# Real-time latency monitoring
gcloud monitoring read \
  --filter='metric.type="run.googleapis.com/request_latencies" AND \
    resource.labels.service_name="query-intelligence"' \
  --start-time="-30m" \
  --format=json | jq '.[] | {
    time: .points[0].interval.endTime,
    p50: .points[0].value.distributionValue.bucketCounts[50],
    p95: .points[0].value.distributionValue.bucketCounts[95],
    p99: .points[0].value.distributionValue.bucketCounts[99]
  }'

# Check operation-specific latencies
gcloud logs read "jsonPayload.operation='process_query' AND \
  jsonPayload.duration_ms>100" \
  --limit=20 --format=json | jq '.[] | {
    timestamp: .timestamp,
    query: .jsonPayload.query,
    duration_ms: .jsonPayload.duration_ms,
    model_used: .jsonPayload.model_used
  }'
```

2. **Analyze Cache Performance**
```bash
# Get cache statistics
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/api/v1/admin/cache/stats | jq '.'

# Check cache hit rates by endpoint
gcloud logs read "jsonPayload.cache_hit IS NOT NULL" \
  --limit=1000 --format=json | \
  jq -r 'group_by(.jsonPayload.endpoint) | 
    map({
      endpoint: .[0].jsonPayload.endpoint,
      hit_rate: (map(select(.jsonPayload.cache_hit == true)) | length) / length
    })'
```

3. **Model Performance Analysis**
```bash
# Check model latencies
gcloud logs read "jsonPayload.model_latency_ms IS NOT NULL" \
  --limit=100 --format=json | \
  jq 'group_by(.jsonPayload.model_used) |
    map({
      model: .[0].jsonPayload.model_used,
      avg_latency: (map(.jsonPayload.model_latency_ms) | add / length),
      count: length
    })'
```

#### Resolution Steps

##### 1. Optimize Model Selection

```bash
# Enable model routing if disabled
gcloud run services update query-intelligence \
  --set-env-vars="USE_MODEL_ROUTING=true"

# Adjust model thresholds
gcloud run services update query-intelligence \
  --set-env-vars="SIMPLE_QUERY_MODEL=gemini-2.5-flash-lite,\
    COMPLEX_QUERY_MODEL=gemini-2.5-pro"
```

Configuration for optimal routing:
```python
# Model selection criteria
if query_complexity < 0.3:
    model = "gemini-2.5-flash-lite"  # <50ms latency
elif query_complexity < 0.7:
    model = "gemini-2.5-flash"       # <100ms latency
else:
    model = "gemini-2.5-pro"         # <200ms latency
```

##### 2. Improve Cache Performance

```bash
# Increase cache TTL for stable content
gcloud run services update query-intelligence \
  --set-env-vars="CACHE_TTL_HOURS=48"

# Enable cache warming
curl -X POST -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  https://query-intelligence.ccl.dev/api/v1/admin/cache/warm \
  -d '{
    "patterns": ["explain*", "find*function*", "debug*error*"],
    "repositories": ["main-repo", "popular-repo"]
  }'
```

##### 3. Scale Resources

```bash
# Increase minimum instances for consistent performance
gcloud run services update query-intelligence \
  --min-instances=10 \
  --max-instances=500 \
  --cpu=2 \
  --memory=4Gi

# Enable CPU boost for faster cold starts
gcloud run services update query-intelligence \
  --set-annotations="run.googleapis.com/cpu-boost=true"
```

### 2. Timeout Errors

#### Symptoms
- "Query timeout after 30 seconds" errors
- Circuit breakers opening due to timeouts
- Incomplete responses

#### Diagnostic Steps

```bash
# Find timeout patterns
gcloud logs read "jsonPayload.error='timeout' OR \
  jsonPayload.error_type='TimeoutError'" \
  --limit=50 --format=json | \
  jq '.[] | {
    time: .timestamp,
    operation: .jsonPayload.operation,
    duration: .jsonPayload.duration_ms,
    query_length: .jsonPayload.query_length
  }'

# Check upstream service latencies
for service in analysis-engine pattern-mining; do
  echo "=== $service latencies ==="
  gcloud logs read "jsonPayload.upstream_service='$service' AND \
    jsonPayload.upstream_latency_ms>5000" \
    --limit=10
done
```

#### Resolution

1. **Implement Query Optimization**
```bash
# Reduce max token limits for faster responses
gcloud run services update query-intelligence \
  --set-env-vars="MAX_RESPONSE_TOKENS=1024,MAX_CODE_CHUNKS=10"
```

2. **Add Timeout Cascading**
```python
# Timeout hierarchy (in settings)
QUERY_TIMEOUT_SECONDS = 30
UPSTREAM_TIMEOUT_SECONDS = 20  # Less than query timeout
MODEL_TIMEOUT_SECONDS = 15     # Less than upstream
```

### 3. Resource Exhaustion

#### Symptoms
- Memory usage >80%
- CPU throttling alerts
- OOM (Out of Memory) kills

#### Diagnostic Steps

```bash
# Check memory usage
gcloud monitoring read \
  --filter='metric.type="run.googleapis.com/container/memory/utilizations" AND \
    resource.labels.service_name="query-intelligence"' \
  --start-time="-1h" | jq '.[] | {
    time: .points[0].interval.endTime,
    memory_percent: (.points[0].value.doubleValue * 100)
  }'

# Find memory leaks
gcloud logs read "jsonPayload.memory_mb IS NOT NULL" \
  --limit=100 --format=json | \
  jq 'sort_by(.timestamp) | .[] | {
    time: .timestamp,
    memory_mb: .jsonPayload.memory_mb,
    active_connections: .jsonPayload.active_connections
  }'
```

#### Resolution

1. **Optimize Memory Usage**
```bash
# Reduce cache size
gcloud run services update query-intelligence \
  --set-env-vars="CACHE_MAX_SIZE=5000"

# Limit concurrent requests
gcloud run services update query-intelligence \
  --concurrency=10
```

2. **Implement Memory Monitoring**
```python
# Add memory pressure handling
import psutil

async def check_memory_pressure():
    memory_percent = psutil.virtual_memory().percent
    if memory_percent > 80:
        await cache_manager.evict_oldest(count=1000)
        logger.warning("memory_pressure_high", percent=memory_percent)
```

### 4. Cache Performance Issues

#### Symptoms
- Cache hit rate <80%
- Redis connection pool exhaustion
- Slow cache operations

#### Cache Performance Diagnostics

```bash
# Redis slow queries
redis-cli --latency-history

# Cache operation latencies
gcloud logs read "jsonPayload.cache_operation IS NOT NULL" \
  --limit=1000 --format=json | \
  jq 'group_by(.jsonPayload.cache_operation) |
    map({
      operation: .[0].jsonPayload.cache_operation,
      avg_ms: (map(.jsonPayload.cache_latency_ms) | add / length),
      p95_ms: (map(.jsonPayload.cache_latency_ms) | sort | .[length * 0.95])
    })'

# Connection pool stats
redis-cli CLIENT LIST | grep query-intelligence | wc -l
```

#### Cache Optimization

1. **Optimize Redis Configuration**
```bash
# Increase connection pool size
gcloud run services update query-intelligence \
  --set-env-vars="REDIS_MAX_CONNECTIONS=100"

# Enable pipelining for batch operations
redis-cli CONFIG SET pipeline-max-length 1000
```

2. **Implement Smart Caching**
```python
# Cache key optimization
def generate_cache_key(query: str, context: dict) -> str:
    # Normalize query for better hit rate
    normalized = query.lower().strip()
    # Include only relevant context
    context_hash = hashlib.md5(
        f"{context['repository_id']}:{context['user_segment']}".encode()
    ).hexdigest()[:8]
    return f"query:v2:{context_hash}:{hashlib.md5(normalized.encode()).hexdigest()}"
```

## Performance Optimization Strategies

### 1. Query Optimization

```bash
# Enable query optimization suggestions
curl -X POST https://query-intelligence.ccl.dev/api/v1/query/optimize \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "your complex query here",
    "repository_id": "repo-id",
    "previous_confidence": 0.3
  }'
```

### 2. Model Selection Optimization

```python
# Intelligent model routing based on query analysis
ROUTING_RULES = {
    "simple_patterns": {
        "keywords": ["what is", "define", "list", "show"],
        "max_length": 50,
        "model": "gemini-2.5-flash-lite"
    },
    "medium_complexity": {
        "keywords": ["explain", "how does", "why"],
        "max_length": 200,
        "model": "gemini-2.5-flash"
    },
    "complex_analysis": {
        "keywords": ["analyze", "debug", "optimize", "refactor"],
        "model": "gemini-2.5-pro"
    }
}
```

### 3. Parallel Processing

```python
# Parallelize independent operations
async def process_query_optimized(query: str):
    # Run these in parallel
    tasks = [
        analyze_intent(query),
        get_embeddings(query),
        check_cache(query)
    ]
    
    intent, embeddings, cached = await asyncio.gather(*tasks)
    
    if cached:
        return cached
    
    # Continue with processing...
```

## Performance Monitoring Dashboard

### Key Metrics to Track

1. **Latency Metrics**
   - p50, p95, p99 response times
   - Model inference latency
   - Cache operation latency
   - Upstream service latency

2. **Throughput Metrics**
   - Requests per second
   - Concurrent connections
   - Queue depth

3. **Resource Metrics**
   - CPU utilization
   - Memory usage
   - Network I/O
   - Redis connection count

### Alert Thresholds

```yaml
alerts:
  - name: high_latency_p95
    condition: response_time_p95 > 100ms for 5 minutes
    severity: warning
    
  - name: very_high_latency_p95
    condition: response_time_p95 > 200ms for 2 minutes
    severity: critical
    
  - name: low_cache_hit_rate
    condition: cache_hit_rate < 0.7 for 10 minutes
    severity: warning
    
  - name: memory_pressure
    condition: memory_usage > 0.8 for 5 minutes
    severity: warning
```

## Capacity Planning

### Load Testing

```bash
# Gradual load test
for rate in 10 50 100 200 500; do
  echo "Testing at $rate RPS"
  vegeta attack -duration=30s -rate=$rate \
    -targets=targets.txt \
    -output=results_${rate}.bin
  
  vegeta report results_${rate}.bin
  sleep 30  # Cool down between tests
done

# Analyze results
vegeta plot results_*.bin > performance.html
```

### Scaling Recommendations

| Load (RPS) | Min Instances | CPU | Memory | Cache Size |
|------------|---------------|-----|---------|-----------|
| <50 | 5 | 1 | 2Gi | 5000 |
| 50-200 | 10 | 2 | 4Gi | 10000 |
| 200-500 | 20 | 2 | 4Gi | 20000 |
| >500 | 50 | 4 | 8Gi | 50000 |

## Performance Optimization Checklist

- [ ] Model routing enabled and configured correctly
- [ ] Cache TTL appropriate for content stability
- [ ] Semantic caching enabled for LLM responses
- [ ] Connection pooling optimized for Redis
- [ ] Circuit breakers configured with appropriate thresholds
- [ ] Query timeout cascading implemented
- [ ] CPU boost enabled for Cloud Run
- [ ] Minimum instances set based on load patterns
- [ ] Monitoring alerts configured for key metrics
- [ ] Regular cache warming for popular queries