# Query Intelligence Integration Issues Troubleshooting

## Overview

This guide provides comprehensive troubleshooting procedures for resolving integration issues between the Query Intelligence service and its external dependencies.

## External Service Integration Issues

### 1. Google GenAI API Issues

#### Connection and Authentication Problems

**Symptoms:**
- `401 Unauthorized` errors  
- `403 Forbidden` responses
- Connection timeout errors
- API key validation failures

**Diagnosis:**
```bash
# Test API key validity
curl -H "Authorization: Bearer $GOOGLE_API_KEY" \
  "https://generativelanguage.googleapis.com/v1beta/models"

# Check environment variables
echo $GOOGLE_API_KEY
gcloud auth list

# Verify Secret Manager access
gcloud secrets versions access latest --secret="google-genai-api-key"
```

**Common Solutions:**

1. **API Key Issues**
   ```bash
   # Regenerate API key in Google Cloud Console
   # Update Secret Manager
   echo "new-api-key" | gcloud secrets versions add google-genai-api-key --data-file=-
   
   # Restart service to pick up new key
   gcloud run services update query-intelligence --region=us-central1
   ```

2. **Quota Exhaustion**
   ```bash
   # Check quota usage in Cloud Console
   # Navigate to: APIs & Services > Quotas > Generative AI API
   
   # Request quota increase if needed
   # Implement exponential backoff for rate limiting
   ```

### 2. Redis Cache Integration Issues

#### Connection Problems

**Symptoms:**
- Redis connection timeouts
- High cache miss rates
- Memory issues in Redis

**Diagnosis:**
```bash
# Test Redis connectivity
redis-cli -h redis-host -p 6379 ping

# Check Redis memory usage
redis-cli info memory

# Monitor cache performance
redis-cli info stats | grep -E "(hits|misses)"
```

**Solutions:**
```python
# Improved Redis configuration
import aioredis

async def create_redis_pool():
    return aioredis.ConnectionPool(
        host='redis-host',
        port=6379,
        db=0,
        max_connections=100,
        retry_on_timeout=True,
        health_check_interval=30,
        socket_connect_timeout=5,
        socket_timeout=10
    )
```

### 3. Vector Database (Pinecone) Integration Issues

#### Performance Problems

**Symptoms:**
- Slow vector search responses
- Connection timeout errors
- Index not found errors

**Diagnosis:**
```python
# Test Pinecone connectivity
import pinecone

def diagnose_pinecone():
    try:
        pinecone.init(api_key=os.getenv("PINECONE_API_KEY"))
        index = pinecone.Index("query-intelligence")
        
        # Test basic operations
        stats = index.describe_index_stats()
        print(f"Index stats: {stats}")
        
        return True
    except Exception as e:
        print(f"Pinecone error: {e}")
        return False
```

**Solutions:**
- Optimize Pinecone configuration for same region as service
- Implement connection pooling
- Use batch operations for multiple queries

## Authentication and Authorization Issues

### JWT Token Problems

**Symptoms:**
- Token validation errors
- Expired token errors
- Missing or invalid claims

**Diagnosis:**
```python
import jwt

def debug_jwt_token(token: str):
    try:
        # Decode without verification to inspect
        unverified = jwt.decode(token, options={"verify_signature": False})
        print(f"Token claims: {unverified}")
        
        # Check expiration
        exp = unverified.get('exp')
        if exp and datetime.fromtimestamp(exp) < datetime.utcnow():
            print("Token expired")
            
        return unverified
    except Exception as e:
        print(f"Token error: {e}")
```

**Solutions:**
- Implement automatic token refresh
- Verify JWT secret configuration
- Check service account permissions

## Performance Issues

### High Latency Problems

**Diagnosis:**
```bash
# Check response time distribution
curl -w "@curl-format.txt" -o /dev/null -s \
  https://query-intelligence.ccl.dev/api/v1/query

# Monitor application metrics
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/admin/metrics
```

**Solutions:**
- Review cache hit rates (target > 75%)
- Optimize model selection routing
- Check resource utilization
- Implement request tracing

## Emergency Response Procedures

### Complete Service Outage
```bash
# 1. Check service status
gcloud run services describe query-intelligence --region=us-central1

# 2. Check recent deployments  
gcloud run revisions list --service=query-intelligence --region=us-central1 --limit=5

# 3. Rollback if needed
PREVIOUS_REVISION=$(gcloud run revisions list \
  --service=query-intelligence \
  --region=us-central1 \
  --limit=2 \
  --format="value(metadata.name)" | tail -n 1)

gcloud run services update-traffic query-intelligence \
  --to-revisions=$PREVIOUS_REVISION=100 \
  --region=us-central1
```

### High Error Rate
```bash
# 1. Check error logs
gcloud logs read "resource.type=cloud_run_revision" \
  --filter="severity>=ERROR" \
  --limit=50

# 2. Scale up if needed
gcloud run services update query-intelligence \
  --min-instances=10 \
  --max-instances=500 \
  --region=us-central1
```

## Quick Diagnosis Checklist

```yaml
Service Health:
  - [ ] /health endpoint responding (< 1s)
  - [ ] /ready endpoint shows all dependencies healthy
  - [ ] Error rate < 0.1%
  - [ ] Response time p95 < 100ms

External Dependencies:
  - [ ] Google GenAI API accessible
  - [ ] Pinecone vector search working  
  - [ ] Redis cache operational
  - [ ] Analysis Engine responding

Authentication:
  - [ ] JWT tokens validating correctly
  - [ ] Service account permissions adequate
  - [ ] API keys valid and not expired
  - [ ] Secret Manager accessible

Performance:
  - [ ] Cache hit rate > 75%
  - [ ] Memory usage < 2GB per instance
  - [ ] CPU utilization < 80%
  - [ ] No circuit breakers open
```

---

**Last Updated**: July 2025  
**Status**: Production Ready  
**Next Review**: August 2025