# Query Intelligence Service Troubleshooting Guide

## Quick Diagnostics

### Health Check Procedures

1. **Basic Health Check**
   ```bash
   curl -f https://query-intelligence.ccl.dev/health
   ```
   
   Expected response:
   ```json
   {
     "status": "healthy",
     "service": "query-intelligence",
     "version": "1.0.0",
     "checks": {
       "redis": "ok",
       "analysis_engine": "ok",
       "pattern_mining": "ok"
     }
   }
   ```

2. **Readiness Check**
   ```bash
   curl -f https://query-intelligence.ccl.dev/ready
   ```

3. **Circuit Breaker Status**
   ```bash
   curl -H "Authorization: Bearer $ADMIN_TOKEN" \
        https://query-intelligence.ccl.dev/circuit-breakers
   ```

### Common Error Codes and Meanings

| Error Code | Meaning | Quick Fix |
|------------|---------|-----------|
| 401 | Invalid or expired JWT token | Refresh authentication token |
| 403 | Insufficient permissions | Check user roles and permissions |
| 429 | Rate limit exceeded | Wait for rate limit window to reset |
| 500 | Internal server error | Check service logs |
| 502 | Bad gateway | Check upstream services |
| 503 | Service unavailable | Service is degraded or starting up |

### Quick Fix Checklist

1. **Service Won't Start**
   - [ ] Check environment variables are set
   - [ ] Verify Redis is accessible
   - [ ] Confirm Google API credentials are valid
   - [ ] Check Cloud Run service logs

2. **Authentication Failures**
   - [ ] Verify JWT_SECRET_KEY is consistent across deployments
   - [ ] Check token expiration time
   - [ ] Ensure Authorization header format is correct: `Bearer <token>`

3. **Performance Issues**
   - [ ] Check Redis connection pool status
   - [ ] Monitor circuit breaker states
   - [ ] Verify cache hit rates
   - [ ] Check external API rate limits

### When to Escalate

Escalate to platform team when:
- Circuit breakers remain OPEN for >10 minutes
- Health checks fail consistently for >5 minutes
- Error rate exceeds 5% for any endpoint
- Response times exceed 100ms p95 threshold
- Memory usage exceeds 80% of container limit

## Common Issues by Category

### Authentication Issues

#### 401 Unauthorized - Invalid JWT Token

**Symptoms:**
```json
{
  "detail": "Invalid authentication credentials"
}
```

**Diagnostic Steps:**
```bash
# Check JWT token validity
echo $JWT_TOKEN | base64 -d | jq .

# Verify JWT secret is set correctly
gcloud secrets versions access latest --secret="jwt-secret-key"

# Check service logs
gcloud logs read "resource.type=cloud_run_revision AND \
  resource.labels.service_name=query-intelligence AND \
  jsonPayload.error=jwt_verification_failed" \
  --limit=50 --format=json
```

**Resolution:**
1. Refresh the authentication token
2. Ensure JWT_SECRET_KEY matches across all services
3. Verify token hasn't expired (check `exp` claim)

#### 403 Forbidden - Insufficient Permissions

**Symptoms:**
```json
{
  "detail": "Admin role required"
}
```

**Resolution:**
1. Check user's roles in the JWT token
2. Ensure required roles are properly assigned
3. Verify role-based access control configuration

### Performance Problems

#### High Latency (>100ms)

**Symptoms:**
- Response times exceed 100ms threshold
- Timeouts on query processing

**Diagnostic Commands:**
```bash
# Check Cloud Run metrics
gcloud monitoring read \
  --filter='metric.type="run.googleapis.com/request_latencies"' \
  --start-time="-1h" \
  --format=json

# Monitor cache performance
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/api/v1/admin/cache/stats
```

**Resolution:**
1. Enable cache warming for frequently accessed data
2. Optimize model selection (use lighter models for simple queries)
3. Scale up Cloud Run instances if CPU constrained

### Integration Failures

#### Circuit Breaker OPEN

**Symptoms:**
```json
{
  "detail": "Circuit breaker 'analysis_engine' is OPEN"
}
```

**Diagnostic Steps:**
```bash
# Check circuit breaker status
curl https://query-intelligence.ccl.dev/circuit-breakers

# Check upstream service health
curl https://analysis-engine.ccl.dev/health
```

**Resolution:**
1. Wait 60 seconds for circuit breaker recovery timeout
2. Check health of upstream service
3. Monitor logs for connection errors:
   ```bash
   gcloud logs read "jsonPayload.error=~'connection_failed'" --limit=20
   ```

#### Redis Connection Failed

**Symptoms:**
- Health check shows `"redis": "connection_error"`
- Cache operations fail

**Resolution:**
```bash
# Check Redis connectivity
redis-cli -h $REDIS_HOST -p $REDIS_PORT ping

# Verify Redis URL format
echo $REDIS_URL  # Should be: redis://host:port

# Check Redis memory usage
redis-cli info memory
```

### Configuration Errors

#### Missing Environment Variables

**Symptoms:**
- Service fails to start
- Default values being used inappropriately

**Critical Variables to Check:**
```bash
# List all environment variables
gcloud run services describe query-intelligence \
  --platform=managed --region=us-central1 \
  --format="value(spec.template.spec.containers[0].env[].name)"

# Required variables:
- REDIS_URL
- ANALYSIS_ENGINE_URL
- PATTERN_MINING_URL
- GCP_PROJECT_ID
- GOOGLE_APPLICATION_CREDENTIALS (for production)
- JWT_SECRET_KEY (from Secret Manager)
```

### Deployment Issues

#### Cold Start Delays

**Symptoms:**
- First request after idle period is slow
- Health checks timeout during startup

**Resolution:**
```yaml
# Ensure minimum instances in Cloud Run config
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/cpu-boost: "true"
    spec:
      containerConcurrency: 20
      serviceAccountName: query-intelligence-sa
  traffic:
    - minInstanceCount: 5
      maxInstanceCount: 200
```

## Diagnostic Tools

### Log Analysis Commands

```bash
# View recent errors
gcloud logs read "severity>=ERROR AND \
  resource.labels.service_name=query-intelligence" \
  --limit=50 --format=json | jq '.jsonPayload'

# Track specific request
gcloud logs read "jsonPayload.request_id='$REQUEST_ID'" \
  --format=json

# Monitor rate limiting
gcloud logs read "jsonPayload.event='rate_limit_exceeded'" \
  --limit=20

# Check authentication failures
gcloud logs read "jsonPayload.event='jwt_verification_failed' OR \
  jsonPayload.status_code=401" --limit=30
```

### Metrics to Check

```bash
# Request latencies
gcloud monitoring read \
  --filter='metric.type="run.googleapis.com/request_latencies" AND \
    resource.labels.service_name="query-intelligence"' \
  --start-time="-1h"

# Error rates
gcloud monitoring read \
  --filter='metric.type="run.googleapis.com/request_count" AND \
    metric.labels.response_code_class="5xx"' \
  --start-time="-1h"

# CPU utilization
gcloud monitoring read \
  --filter='metric.type="run.googleapis.com/container/cpu/utilizations"' \
  --start-time="-1h"
```

### Debug Endpoints

```bash
# Cache statistics
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/api/v1/admin/cache/stats

# Clear specific cache
curl -X DELETE -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/api/v1/admin/cache

# Force circuit breaker reset (use with caution)
curl -X POST -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/api/v1/admin/circuit-breakers/reset
```

### Testing Tools

```bash
# Load test with controlled rate
for i in {1..10}; do
  curl -X POST https://query-intelligence.ccl.dev/api/v1/query \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d '{
      "query": "explain the authentication system",
      "repository_id": "test-repo"
    }' &
  sleep 0.1
done

# WebSocket connection test
wscat -c wss://query-intelligence.ccl.dev/api/v1/ws/query \
  -H "Authorization: Bearer $TOKEN"
```

## Emergency Procedures

### Service Degradation Response

1. **Immediate Actions:**
   ```bash
   # Scale up instances
   gcloud run services update query-intelligence \
     --min-instances=10 --max-instances=500
   
   # Enable fallback mode
   gcloud run services update query-intelligence \
     --set-env-vars="ENABLE_FALLBACK_MODE=true"
   ```

2. **Disable Non-Critical Features:**
   ```bash
   # Disable semantic search temporarily
   gcloud run services update query-intelligence \
     --set-env-vars="SEMANTIC_CACHE_ENABLED=false"
   ```

3. **Monitor Recovery:**
   ```bash
   watch -n 5 'curl -s https://query-intelligence.ccl.dev/health | jq .'
   ```

### Data Recovery

```bash
# Export cache data before Redis restart
redis-cli --rdb /backup/cache-backup.rdb

# Clear corrupted cache
redis-cli FLUSHDB

# Warm cache with essential data
curl -X POST -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/api/v1/admin/cache/warm
```

## Preventive Measures

1. **Set up alerts for:**
   - Response time >100ms (p95)
   - Error rate >1%
   - Circuit breaker state changes
   - Redis memory >80%

2. **Regular maintenance:**
   - Review and update rate limits monthly
   - Clean up old cache entries weekly
   - Update model configurations based on usage patterns

3. **Monitoring dashboard:**
   - Create custom dashboard in Cloud Console
   - Include key metrics: latency, errors, circuit breakers
   - Set up PagerDuty integration for critical alerts