# 🔧 Query Intelligence Service - Troubleshooting Guide

## Service Information

**Service**: Query Intelligence  
**URL**: https://query-intelligence-l3nxty7oka-uc.a.run.app  
**Status**: ✅ Production Ready  
**Support**: #query-intelligence-support

## Table of Contents
- [Quick Diagnostics](#quick-diagnostics)
- [Common Issues](#common-issues)
- [Performance Issues](#performance-issues)
- [Authentication Issues](#authentication-issues)
- [Integration Issues](#integration-issues)
- [Deployment Issues](#deployment-issues)
- [Monitoring and Logs](#monitoring-and-logs)
- [Emergency Procedures](#emergency-procedures)

## Quick Diagnostics

### Health Check Commands
```bash
# Basic health check
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/health

# Detailed readiness check
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/ready

# Service version
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/version

# Supported languages
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/languages
```

### Service Status Indicators
| Indicator | Healthy | Warning | Critical |
|-----------|---------|---------|----------|
| **Response Time** | <100ms | 100-500ms | >500ms |
| **Error Rate** | <1% | 1-5% | >5% |
| **Memory Usage** | <80% | 80-90% | >90% |
| **CPU Usage** | <70% | 70-85% | >85% |

## Common Issues

### 1. Service Not Responding

#### Symptoms
- Health endpoints return 503 or timeout
- No response from service
- High latency (>5 seconds)

#### Diagnosis
```bash
# Check service status
gcloud run services describe query-intelligence --region=us-central1

# Check recent logs
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence"' \
  --limit=20

# Check resource usage
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/container/memory/utilizations"'
```

#### Solutions
1. **Restart Service**
```bash
gcloud run services update query-intelligence \
  --set-env-vars="RESTART_TIMESTAMP=$(date +%s)" \
  --region=us-central1
```

2. **Scale Up Instances**
```bash
gcloud run services update query-intelligence \
  --min-instances=10 \
  --region=us-central1
```

3. **Check Dependencies**
```bash
# Check Redis
redis-cli -h [REDIS_HOST] ping

# Check Analysis Engine
curl https://analysis-engine-572735000332.us-central1.run.app/health

# Check Google GenAI API quota
gcloud logging read 'jsonPayload.error_type="quota_exceeded"' --limit=10
```

### 2. High Error Rate (>5%)

#### Symptoms
- API endpoints returning 500 errors
- Authentication failures
- Timeout errors

#### Diagnosis
```bash
# Check error distribution
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence" 
  AND severity>=ERROR' \
  --limit=50 \
  --format="table(timestamp,severity,jsonPayload.error_type,jsonPayload.message)"

# Check HTTP status codes
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence"' \
  --limit=100 \
  --format="table(timestamp,httpRequest.status,httpRequest.requestUrl)"
```

#### Common Error Types and Solutions

##### 5xx Server Errors
```bash
# Check for recent deployments
gcloud run revisions list --service=query-intelligence --region=us-central1 --limit=5

# Rollback if needed
PREVIOUS_REVISION=$(gcloud run revisions list --service=query-intelligence --region=us-central1 --limit=2 --format="value(metadata.name)" | tail -n 1)
gcloud run services update-traffic query-intelligence \
  --to-revisions="$PREVIOUS_REVISION=100" \
  --region=us-central1
```

##### 4xx Client Errors
```bash
# Check authentication issues
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence" 
  AND httpRequest.status=401' \
  --limit=20

# Check validation errors
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence" 
  AND httpRequest.status=400' \
  --limit=20
```

### 3. Performance Degradation

#### Symptoms
- Response time >200ms p95
- High CPU or memory usage
- Query processing timeouts

#### Diagnosis
```bash
# Check performance metrics
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/request_latencies"' \
  --interval.end-time=$(date -Iseconds) \
  --interval.start-time=$(date -d '1 hour ago' -Iseconds)

# Check resource utilization
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/container/cpu/utilizations"'

gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/container/memory/utilizations"'
```

#### Solutions
1. **Increase Resources**
```bash
gcloud run services update query-intelligence \
  --memory=32Gi \
  --cpu=8 \
  --region=us-central1
```

2. **Clear Cache**
```bash
# Clear Redis cache
redis-cli -h [REDIS_HOST] FLUSHDB

# Or clear specific cache keys
redis-cli -h [REDIS_HOST] DEL "cache:*"
```

3. **Check External Services**
```bash
# Test Analysis Engine performance
time curl https://analysis-engine-572735000332.us-central1.run.app/health

# Test Google GenAI API
gcloud logs read 'jsonPayload.genai_latency_ms>1000' --limit=10
```

## Performance Issues

### High Latency Troubleshooting

#### Step 1: Identify Bottleneck
```bash
# Check end-to-end latency
curl -w "@curl-format.txt" -s -o /dev/null \
  https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query \
  -H "Authorization: Bearer test-token" \
  -H "Content-Type: application/json" \
  -d '{"query":"test","repository_id":"test"}'

# curl-format.txt content:
# time_namelookup:  %{time_namelookup}\n
# time_connect:     %{time_connect}\n
# time_appconnect:  %{time_appconnect}\n
# time_pretransfer: %{time_pretransfer}\n
# time_redirect:    %{time_redirect}\n
# time_starttransfer: %{time_starttransfer}\n
# time_total:       %{time_total}\n
```

#### Step 2: Check Component Latency
```bash
# Check cache performance
gcloud logs read 'jsonPayload.cache_latency_ms>50' --limit=20

# Check LLM response time
gcloud logs read 'jsonPayload.llm_latency_ms>1000' --limit=20

# Check database operations
gcloud logs read 'jsonPayload.db_latency_ms>100' --limit=20
```

#### Step 3: Performance Optimization
```bash
# Enable performance mode
gcloud run services update query-intelligence \
  --set-env-vars="PERFORMANCE_MODE=true" \
  --cpu-boost \
  --region=us-central1

# Adjust cache settings
gcloud run services update query-intelligence \
  --set-env-vars="CACHE_TTL_SECONDS=7200,SEMANTIC_CACHE_THRESHOLD=0.9" \
  --region=us-central1
```

### Memory Issues

#### Symptoms
- Memory usage >90%
- Out of memory errors
- Container restarts

#### Diagnosis
```bash
# Check memory trends
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/container/memory/utilizations"' \
  --interval.end-time=$(date -Iseconds) \
  --interval.start-time=$(date -d '24 hours ago' -Iseconds)

# Check for memory leaks
gcloud logs read 'jsonPayload.memory_usage_mb>1000' --limit=50
```

#### Solutions
```bash
# Increase memory allocation
gcloud run services update query-intelligence \
  --memory=32Gi \
  --region=us-central1

# Restart service to clear memory
gcloud run services update query-intelligence \
  --set-env-vars="RESTART_TIMESTAMP=$(date +%s)" \
  --region=us-central1

# Enable garbage collection optimization
gcloud run services update query-intelligence \
  --set-env-vars="PYTHONMALLOC=malloc" \
  --region=us-central1
```

## Authentication Issues

### JWT Token Problems

#### Symptoms
- 401 Unauthorized errors
- "Invalid token" messages
- Authentication middleware failures

#### Diagnosis
```bash
# Check authentication errors
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence" 
  AND jsonPayload.error_type="authentication"' \
  --limit=20

# Check token validation errors
gcloud logs read 'jsonPayload.jwt_error' --limit=20
```

#### Solutions
1. **Verify JWT Secret**
```bash
# Check secret value
gcloud secrets versions access latest --secret=jwt-secret

# Update secret if needed
echo "new-jwt-secret" | gcloud secrets versions add jwt-secret --data-file=-
```

2. **Check Token Format**
```bash
# Valid token format test
curl -X POST https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..." \
  -H "Content-Type: application/json" \
  -d '{"query":"test","repository_id":"test"}'
```

3. **Firebase Authentication Issues**
```bash
# Test Firebase token validation
curl -X POST "https://identitytoolkit.googleapis.com/v1/accounts:lookup?key=API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"idToken":"FIREBASE_TOKEN"}'
```

### Rate Limiting Issues

#### Symptoms
- 429 Too Many Requests errors
- Rate limit headers in responses
- Sudden traffic spikes

#### Diagnosis
```bash
# Check rate limit hits
gcloud logs read 'httpRequest.status=429' --limit=50

# Check rate limit configuration
gcloud run services describe query-intelligence --region=us-central1 \
  --format="value(spec.template.spec.containers[0].env[?name='RATE_LIMIT_PER_HOUR'].value)"
```

#### Solutions
```bash
# Temporarily increase rate limits
gcloud run services update query-intelligence \
  --set-env-vars="RATE_LIMIT_PER_HOUR=2000" \
  --region=us-central1

# Clear rate limit counters
redis-cli -h [REDIS_HOST] DEL "rate_limit:*"

# Implement IP-based rate limiting bypass for specific IPs
gcloud run services update query-intelligence \
  --set-env-vars="RATE_LIMIT_WHITELIST=*******,*******" \
  --region=us-central1
```

## Integration Issues

### Analysis Engine Connection Problems

#### Symptoms
- "Analysis Engine unavailable" errors
- Timeouts when calling analysis engine
- Circuit breaker open errors

#### Diagnosis
```bash
# Test Analysis Engine directly
curl https://analysis-engine-572735000332.us-central1.run.app/health

# Check integration errors
gcloud logs read 'jsonPayload.integration_error="analysis_engine"' --limit=20

# Check circuit breaker status
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/admin/circuit-breakers
```

#### Solutions
1. **Reset Circuit Breaker**
```bash
# Reset circuit breaker via admin API
curl -X POST https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/admin/circuit-breakers/reset \
  -H "Authorization: Bearer admin-token"
```

2. **Update Service URL**
```bash
# Update Analysis Engine URL if it changed
gcloud run services update query-intelligence \
  --set-env-vars="ANALYSIS_ENGINE_URL=https://new-analysis-engine-url" \
  --region=us-central1
```

3. **Enable Fallback Mode**
```bash
gcloud run services update query-intelligence \
  --set-env-vars="ANALYSIS_ENGINE_FALLBACK=true" \
  --region=us-central1
```

### Redis Connection Issues

#### Symptoms
- Cache miss rate 100%
- Redis connection errors
- Rate limiting not working

#### Diagnosis
```bash
# Test Redis connectivity
redis-cli -h [REDIS_HOST] ping

# Check Redis logs
gcloud logs read 'jsonPayload.redis_error' --limit=20

# Check cache statistics
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/admin/cache/stats
```

#### Solutions
```bash
# Update Redis URL
gcloud run services update query-intelligence \
  --set-env-vars="REDIS_URL=redis://new-redis-host:6379" \
  --region=us-central1

# Restart Redis service
# (Contact infrastructure team)

# Enable Redis failover mode
gcloud run services update query-intelligence \
  --set-env-vars="REDIS_FAILOVER_MODE=true" \
  --region=us-central1
```

## Deployment Issues

### Container Build Problems

#### Symptoms
- Build failures in CI/CD
- Container won't start
- Import errors

#### Solutions
```bash
# Test local build
docker build -t query-intelligence:test .

# Check build logs
gcloud builds log [BUILD_ID]

# Test container locally
docker run -p 8002:8002 -e ENVIRONMENT=test query-intelligence:test
```

### Environment Variable Issues

#### Symptoms
- Configuration errors
- Missing secrets
- Service won't start

#### Diagnosis
```bash
# Check current environment variables
gcloud run services describe query-intelligence --region=us-central1 \
  --format="value(spec.template.spec.containers[0].env[].name,spec.template.spec.containers[0].env[].value)"

# Check secret access
gcloud secrets versions access latest --secret=google-api-key
gcloud secrets versions access latest --secret=jwt-secret
```

#### Solutions
```bash
# Update environment variable
gcloud run services update query-intelligence \
  --set-env-vars="NEW_VAR=value" \
  --region=us-central1

# Update secret
echo "new-secret-value" | gcloud secrets versions add secret-name --data-file=-

# Remove environment variable
gcloud run services update query-intelligence \
  --remove-env-vars="OLD_VAR" \
  --region=us-central1
```

## Monitoring and Logs

### Log Analysis Commands

#### Error Analysis
```bash
# Recent errors by type
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence" 
  AND severity>=ERROR' \
  --limit=50 \
  --format="table(timestamp,severity,jsonPayload.error_type,jsonPayload.message)"

# Authentication errors
gcloud logs read 'jsonPayload.error_type="authentication"' \
  --limit=20 \
  --format="table(timestamp,httpRequest.remoteIp,jsonPayload.message)"

# Performance issues
gcloud logs read 'jsonPayload.query_duration_ms>5000' \
  --limit=20 \
  --format="table(timestamp,jsonPayload.query_duration_ms,jsonPayload.query_id)"
```

#### Performance Analysis
```bash
# Slow queries
gcloud logs read 'jsonPayload.query_duration_ms>2000' \
  --limit=50 \
  --format="table(timestamp,jsonPayload.query_duration_ms,jsonPayload.query)"

# Cache performance
gcloud logs read 'jsonPayload.cache_hit_rate<0.5' \
  --limit=20 \
  --format="table(timestamp,jsonPayload.cache_hit_rate,jsonPayload.cache_size)"

# External service latency
gcloud logs read 'jsonPayload.analysis_engine_latency_ms>1000' \
  --limit=20 \
  --format="table(timestamp,jsonPayload.analysis_engine_latency_ms)"
```

### Metrics Monitoring

#### Key Metrics to Watch
```bash
# Request rate
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/request_count"'

# Error rate
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/request_count"' \
  --filter='metric.label.response_code_class="5xx"'

# Memory usage
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/container/memory/utilizations"'

# Custom application metrics
gcloud monitoring time-series list \
  --filter='metric.type="custom.googleapis.com/query_intelligence/query_duration"'
```

## Emergency Procedures

### Service Down - Critical

#### Immediate Actions (0-5 minutes)
1. **Check service status**
```bash
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/health
```

2. **Check recent deployments**
```bash
gcloud run revisions list --service=query-intelligence --region=us-central1 --limit=5
```

3. **Rollback if recent deployment**
```bash
PREVIOUS_REVISION=$(gcloud run revisions list --service=query-intelligence --region=us-central1 --limit=2 --format="value(metadata.name)" | tail -n 1)
gcloud run services update-traffic query-intelligence \
  --to-revisions="$PREVIOUS_REVISION=100" \
  --region=us-central1
```

#### Secondary Actions (5-15 minutes)
1. **Scale up instances**
```bash
gcloud run services update query-intelligence \
  --min-instances=20 \
  --region=us-central1
```

2. **Check logs for root cause**
```bash
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence" 
  AND severity>=ERROR' \
  --limit=50
```

3. **Enable fallback mode**
```bash
gcloud run services update query-intelligence \
  --set-env-vars="FALLBACK_MODE=true" \
  --region=us-central1
```

### Performance Degradation - High Priority

#### Immediate Actions
1. **Check resource usage**
```bash
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/container/memory/utilizations"'
```

2. **Scale resources**
```bash
gcloud run services update query-intelligence \
  --memory=32Gi \
  --cpu=8 \
  --region=us-central1
```

3. **Clear cache if needed**
```bash
redis-cli -h [REDIS_HOST] FLUSHDB
```

### Escalation Contacts

#### On-Call Escalation
- **Primary**: @oncall-query-intelligence (Slack)
- **Secondary**: @senior-query-intelligence (Slack)
- **Manager**: @manager-query-intelligence (Slack)

#### External Dependencies
- **Analysis Engine**: @analysis-engine-team
- **Infrastructure**: @infrastructure-team
- **Security**: @security-team

#### Emergency Phone Numbers
- **Primary On-call**: +1-XXX-XXX-XXXX
- **Engineering Manager**: +1-XXX-XXX-XXXX

---

**Last Updated**: July 14, 2025  
**Document Owner**: Query Intelligence Team  
**Review Schedule**: Monthly