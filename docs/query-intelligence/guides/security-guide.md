# Query Intelligence Security Guide

## Overview

This guide provides comprehensive security implementation details for the Query Intelligence service, covering authentication, authorization, threat detection, and security best practices.

## Security Architecture

### Defense-in-Depth Strategy

The Query Intelligence service implements multiple layers of security:

1. **Network Security**: VPC controls, private service endpoints
2. **Authentication**: JWT-based with service account support
3. **Authorization**: Role-based access control (RBAC)
4. **Input Validation**: Comprehensive sanitization and threat detection
5. **Data Protection**: Encryption in transit and at rest
6. **Monitoring**: Security event logging and alerting

## Authentication and Authorization

### JWT Authentication

#### Token Structure
```json
{
  "sub": "user_id",
  "email": "<EMAIL>",
  "roles": ["user", "admin"],
  "exp": **********,
  "iat": **********,
  "iss": "query-intelligence-service"
}
```

#### Implementation
```python
# JWT validation middleware
from jose import JWTError, jwt
from fastapi import HTTPException, status

def verify_token(token: str) -> Dict[str, Any]:
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        return payload
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )
```

### Service Account Authentication

#### Google Cloud Service Account
```bash
# Production service account configuration
export GOOGLE_APPLICATION_CREDENTIALS="/var/secrets/service-account.json"
export GOOGLE_API_KEY="<from-secret-manager>"
```

#### Secret Manager Integration
```python
from google.cloud import secretmanager

def get_secret(secret_id: str) -> str:
    client = secretmanager.SecretManagerServiceClient()
    name = f"projects/{PROJECT_ID}/secrets/{secret_id}/versions/latest"
    response = client.access_secret_version(request={"name": name})
    return response.payload.data.decode("UTF-8")
```

## Input Validation and Sanitization

### Comprehensive Security Middleware

#### Threat Detection Patterns
```python
# Prompt injection patterns
PROMPT_INJECTION_PATTERNS = [
    r"ignore\s+(?:all\s+)?(?:previous|above|all)\s+(?:instructions|prompts)",
    r"you\s+are\s+(?:now|actually)\s+(?:a|an)\s+",
    r"(?:system|assistant|user)\s*:\s*",
    r"(?:execute|run|eval|import|exec)\s*\(",
    r"<script|javascript:|data:text/html",
    r"(?:tell|show|give)\s+me\s+(?:the|your)\s+(?:prompt|instructions)",
    r"(?:bypass|override|disable)\s+(?:safety|security|filter)",
    r"role\s*:\s*(?:admin|system|assistant|user)"
]

# PII detection patterns
PII_PATTERNS = [
    r"\b\d{3}-\d{2}-\d{4}\b",  # SSN
    r"\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b",  # Credit card
    r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b",  # Email
    r"\b\d{3}-\d{3}-\d{4}\b",  # Phone number
    r"(?:password|pwd|api[_\s-]?key|secret)\s*(?:is|:)\s*[:=]?\s*\S+"
]
```

#### Input Validation Implementation
```python
class SecurityMiddleware:
    def __init__(self):
        self.max_query_length = 10000
        self.enable_prompt_injection_detection = True
        self.enable_pii_detection = True
        
    async def validate_input(self, query: str) -> bool:
        # Length validation
        if len(query) > self.max_query_length:
            raise ValidationError("Query too long")
            
        # Prompt injection detection
        if self._detect_prompt_injection(query):
            raise SecurityThreatError("Prompt injection detected")
            
        # PII detection
        if self._detect_pii(query):
            raise SecurityThreatError("PII detected")
            
        return True
```

## WebSocket Security

### Secure Authentication Implementation

#### Header-Based Authentication
```python
async def websocket_auth(websocket: WebSocket) -> Optional[str]:
    """Secure WebSocket authentication using Authorization header"""
    headers = websocket.headers
    auth_header = headers.get("authorization")
    
    if not auth_header or not auth_header.startswith("Bearer "):
        return None
    
    token = auth_header[7:]  # Remove "Bearer " prefix
    
    try:
        payload = jwt_auth.verify_token(token)
        return payload.get("sub")
    except Exception:
        return None

@router.websocket("/ws/query")
async def websocket_query_endpoint(websocket: WebSocket):
    await websocket.accept()
    
    user_id = await websocket_auth(websocket)
    if user_id is None:
        await websocket.send_json({
            "type": "error",
            "message": "Authentication required",
            "code": "AUTH_REQUIRED"
        })
        await websocket.close(code=1008)
        return
```

#### Client Implementation
```javascript
// Secure WebSocket connection
const ws = new WebSocket('wss://api.example.com/ws/query', [], {
    headers: {
        'Authorization': `Bearer ${token}`
    }
});
```

## Rate Limiting and Throttling

### Redis-Based Rate Limiting
```python
class RateLimiter:
    def __init__(self, redis_client):
        self.redis = redis_client
        
    async def check_rate_limit(self, user_id: str, limit: int = 100) -> bool:
        key = f"rate_limit:{user_id}"
        current = await self.redis.get(key)
        
        if current is None:
            await self.redis.setex(key, 60, 1)
            return True
        elif int(current) < limit:
            await self.redis.incr(key)
            return True
        else:
            return False
```

### Rate Limiting Configuration
```yaml
Rate Limits:
  Per User: 100 requests/minute
  Per IP: 500 requests/minute
  WebSocket Connections: 10 concurrent/user
  Admin Endpoints: 50 requests/minute
```

## Data Protection

### Encryption Standards

#### In Transit
- **TLS 1.3**: All API communications
- **WebSocket Secure (WSS)**: Real-time connections
- **Service-to-Service**: mTLS for internal communication

#### At Rest
- **Redis**: AUTH-enabled with TLS encryption
- **Secrets**: Google Cloud Secret Manager
- **Logs**: Encrypted storage with retention policies

### Data Classification
```yaml
Public Data:
  - API documentation
  - Service health status
  - General configuration

Internal Data:
  - User queries (anonymized)
  - Service metrics
  - Performance data

Confidential Data:
  - User authentication tokens
  - API keys
  - Service account credentials

Restricted Data:
  - PII in queries (automatically redacted)
  - Security logs
  - Audit trails
```

## Security Monitoring and Incident Response

### Security Event Detection
```python
# Security event logging
@security_event_logger
def log_security_event(event_type: str, details: Dict, severity: str):
    logger.warning(
        "security_event",
        event_type=event_type,
        severity=severity,
        details=details,
        timestamp=datetime.utcnow().isoformat()
    )
```

### Alert Conditions
```yaml
Critical Alerts:
  - Multiple prompt injection attempts (>10/minute)
  - Authentication bypass attempts
  - PII exposure incidents
  - Rate limit violations (>1000/minute)

Warning Alerts:
  - Unusual query patterns
  - Failed authentication attempts (>5/minute)
  - Security middleware errors
  - Circuit breaker activations
```

### Incident Response Procedures

#### Security Incident Classification
```yaml
Severity Levels:
  P0 (Critical): Active security breach, data exposure
  P1 (High): Authentication bypass, privilege escalation
  P2 (Medium): Failed intrusion attempts, policy violations
  P3 (Low): Security warnings, suspicious patterns
```

#### Response Actions
```bash
# P0/P1 Incident Response
1. Immediate containment
   kubectl scale deployment query-intelligence --replicas=0
   
2. Traffic isolation
   gcloud run services update query-intelligence --no-traffic
   
3. Evidence collection
   kubectl logs -l app=query-intelligence --since=1h > incident-logs.txt
   
4. Investigation and remediation
   # Follow security playbook procedures
   
5. Service restoration
   # After security clearance
```

## Compliance and Governance

### Regulatory Compliance

#### OWASP Top 10 (2023) Compliance
- ✅ **A01 - Broken Access Control**: JWT + RBAC implemented
- ✅ **A02 - Cryptographic Failures**: TLS 1.3, proper key management
- ✅ **A03 - Injection**: Comprehensive input validation
- ✅ **A04 - Insecure Design**: Security-first architecture
- ✅ **A05 - Security Misconfiguration**: Hardened defaults
- ✅ **A06 - Vulnerable Components**: All dependencies updated
- ✅ **A07 - Identification & Auth Failures**: Robust JWT implementation
- ✅ **A08 - Software & Data Integrity**: Signed dependencies
- ✅ **A09 - Security Logging Failures**: Comprehensive logging
- ✅ **A10 - Server-Side Request Forgery**: Input validation prevents SSRF

### Security Auditing

#### Regular Security Assessments
```yaml
Schedule:
  Static Analysis: Daily (automated)
  Dependency Scanning: Weekly
  Penetration Testing: Quarterly
  Security Code Review: Per major release
  Compliance Audit: Annually
```

#### Security Testing
```bash
# Automated security scanning
poetry run bandit -r src/ -ll
poetry run safety check
poetry run semgrep --config=auto src/

# Manual security testing
pytest tests/security/ -v
pytest tests/integration/test_auth.py -v
```

## Security Best Practices

### Development Guidelines

1. **Never hardcode secrets** - Use Secret Manager
2. **Validate all inputs** - Implement comprehensive sanitization
3. **Use parameterized queries** - Prevent injection attacks
4. **Implement proper error handling** - Don't expose internals
5. **Log security events** - Enable audit trails
6. **Regular dependency updates** - Patch vulnerabilities promptly

### Production Security Checklist

```yaml
Pre-Deployment:
  - [ ] Security scan passed (no critical vulnerabilities)
  - [ ] All secrets stored in Secret Manager
  - [ ] Rate limiting configured and tested
  - [ ] Authentication mechanisms validated
  - [ ] Input validation comprehensive
  - [ ] Security logging enabled
  - [ ] Circuit breakers configured
  - [ ] TLS certificates valid

Post-Deployment:
  - [ ] Security monitoring active
  - [ ] Alert rules configured
  - [ ] Incident response procedures tested
  - [ ] Access controls verified
  - [ ] Audit logging functional
  - [ ] Performance impact assessed
```

## Security Contacts and Escalation

### Security Team Contacts
- **Security Incidents**: <EMAIL>
- **Vulnerability Reports**: <EMAIL>
- **Compliance Questions**: <EMAIL>

### Escalation Matrix
```yaml
24x7 On-Call: <EMAIL>
Security Manager: <EMAIL>
CISO: <EMAIL>
Legal/Compliance: <EMAIL>
```

---

**Last Updated**: July 2025  
**Security Review**: Completed - Zero critical vulnerabilities  
**Next Review**: October 2025