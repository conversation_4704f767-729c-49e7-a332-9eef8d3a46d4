# Query Intelligence Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the Query Intelligence service to Google Cloud Run. It covers both staging and production deployments with security best practices.

## Prerequisites

### Required Tools
```bash
# Install required tools
gcloud auth login
gcloud config set project vibe-match-463114

# Docker and Cloud Build
docker --version
gcloud components install cloud-build-local

# Application dependencies
poetry --version
python --version  # Requires 3.11+
```

### Required Permissions
```yaml
IAM Roles Required:
  - Cloud Run Admin
  - Service Account User
  - Secret Manager Admin
  - Cloud Build Editor
  - Artifact Registry Writer
```

### Infrastructure Dependencies
```yaml
Required Services:
  - Redis instance (Memorystore)
  - Secret Manager secrets
  - VPC network configuration
  - Load balancer setup
  
External Dependencies:
  - analysis-engine service
  - pattern-mining service
  - Pinecone vector database
```

## Environment Setup

### 1. Secret Manager Configuration

#### Create Required Secrets
```bash
# Google GenAI API Key
gcloud secrets create google-genai-api-key --data-file=<api-key-file>

# JWT Secret Key
gcloud secrets create jwt-secret-key --data-file=<jwt-secret-file>

# Pinecone API Key
gcloud secrets create pinecone-api-key --data-file=<pinecone-key-file>

# Redis Connection String
gcloud secrets create redis-connection-string --data-file=<redis-connection-file>
```

#### Verify Secrets
```bash
# List all secrets
gcloud secrets list --filter="name:query-intelligence"

# Test secret access
gcloud secrets versions access latest --secret="google-genai-api-key"
```

### 2. Service Account Setup

#### Create Service Account
```bash
# Create service account
gcloud iam service-accounts create query-intelligence-sa \
    --display-name="Query Intelligence Service Account"

# Grant required permissions
gcloud projects add-iam-policy-binding vibe-match-463114 \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/secretmanager.secretAccessor"

gcloud projects add-iam-policy-binding vibe-match-463114 \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/monitoring.metricWriter"
```

#### Generate Service Account Key
```bash
# Generate key file
gcloud iam service-accounts keys create service-account.json \
    --iam-account=<EMAIL>

# Store in Secret Manager
gcloud secrets create service-account-key --data-file=service-account.json
```

## Build Process

### 1. Docker Image Build

#### Build Configuration
```dockerfile
# Dockerfile optimized for Cloud Run
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy dependencies
COPY pyproject.toml poetry.lock ./

# Install dependencies
RUN pip install poetry && \
    poetry config virtualenvs.create false && \
    poetry install --only=main

# Copy application
COPY src/ ./src/

# Set environment
ENV PYTHONPATH=/app/src
ENV PORT=8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Run application
CMD ["uvicorn", "query_intelligence.main:app", "--host", "0.0.0.0", "--port", "8080"]
```

#### Build Commands
```bash
# Local build and test
docker build -t query-intelligence:local .
docker run -p 8080:8080 --env-file .env.local query-intelligence:local

# Cloud Build
gcloud builds submit --tag gcr.io/vibe-match-463114/query-intelligence:latest
```

### 2. Cloud Build Configuration

#### cloudbuild.yaml
```yaml
steps:
  # Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/query-intelligence:$SHORT_SHA', '.']
  
  # Push to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/query-intelligence:$SHORT_SHA']
  
  # Run security scan
  - name: 'gcr.io/cloud-builders/gcloud'
    args: ['beta', 'container', 'images', 'scan', 'gcr.io/$PROJECT_ID/query-intelligence:$SHORT_SHA']
  
  # Deploy to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'query-intelligence'
      - '--image=gcr.io/$PROJECT_ID/query-intelligence:$SHORT_SHA'
      - '--platform=managed'
      - '--region=us-central1'
      - '--service-account=query-intelligence-sa@$PROJECT_ID.iam.gserviceaccount.com'

images:
  - 'gcr.io/$PROJECT_ID/query-intelligence:$SHORT_SHA'

timeout: '1200s'
```

## Deployment Configurations

### 1. Staging Environment

#### Staging Configuration
```yaml
# staging.yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: query-intelligence-staging
  namespace: default
  annotations:
    run.googleapis.com/ingress: internal-and-cloud-load-balancing
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/execution-environment: gen2
        run.googleapis.com/cpu-boost: "true"
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "10"
    spec:
      serviceAccountName: <EMAIL>
      containerConcurrency: 20
      containers:
      - image: gcr.io/vibe-match-463114/query-intelligence:latest
        ports:
        - containerPort: 8080
        resources:
          limits:
            cpu: 2
            memory: 8Gi
        env:
        - name: ENVIRONMENT
          value: "staging"
        - name: GCP_PROJECT_ID
          value: "vibe-match-463114"
        - name: USE_SECRET_MANAGER
          value: "true"
        - name: ENABLE_METRICS
          value: "true"
```

#### Deploy to Staging
```bash
# Deploy staging
gcloud run services replace staging.yaml --region=us-central1

# Verify deployment
curl -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  https://query-intelligence-staging-<hash>-uc.a.run.app/health

# Run integration tests
pytest tests/integration/ --env=staging
```

### 2. Production Environment

#### Production Configuration
```yaml
# production.yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: query-intelligence
  namespace: default
  annotations:
    run.googleapis.com/ingress: internal-and-cloud-load-balancing
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/execution-environment: gen2
        run.googleapis.com/cpu-boost: "true"
        autoscaling.knative.dev/minScale: "5"
        autoscaling.knative.dev/maxScale: "200"
    spec:
      serviceAccountName: <EMAIL>
      containerConcurrency: 20
      containers:
      - image: gcr.io/vibe-match-463114/query-intelligence:PRODUCTION_TAG
        ports:
        - containerPort: 8080
        resources:
          limits:
            cpu: 4
            memory: 16Gi
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: GCP_PROJECT_ID
          value: "vibe-match-463114"
        - name: USE_SECRET_MANAGER
          value: "true"
        - name: MIN_INSTANCES
          value: "5"
        - name: MAX_INSTANCES
          value: "200"
        - name: ENABLE_METRICS
          value: "true"
        - name: ENABLE_SECURITY_CONTROLS
          value: "true"
```

#### Production Deployment Steps
```bash
# 1. Pre-deployment validation
./scripts/validate-production.sh

# 2. Blue-green deployment
gcloud run services replace production.yaml --region=us-central1

# 3. Traffic migration (gradual)
gcloud run services update-traffic query-intelligence \
  --to-revisions=LATEST=10 --region=us-central1

# 4. Monitor and validate
./scripts/monitor-deployment.sh

# 5. Complete traffic migration
gcloud run services update-traffic query-intelligence \
  --to-revisions=LATEST=100 --region=us-central1
```

## Load Balancer Configuration

### 1. Global Load Balancer Setup

#### Backend Service Configuration
```bash
# Create backend service
gcloud compute backend-services create query-intelligence-backend \
    --global \
    --load-balancing-scheme=EXTERNAL \
    --protocol=HTTP

# Add Cloud Run NEG
gcloud compute network-endpoint-groups create query-intelligence-neg \
    --region=us-central1 \
    --network-endpoint-type=serverless \
    --cloud-run-service=query-intelligence

gcloud compute backend-services add-backend query-intelligence-backend \
    --global \
    --network-endpoint-group=query-intelligence-neg \
    --network-endpoint-group-region=us-central1
```

#### URL Map and SSL Configuration
```bash
# Create URL map
gcloud compute url-maps create query-intelligence-map \
    --default-backend-service=query-intelligence-backend

# Create SSL certificate
gcloud compute ssl-certificates create query-intelligence-ssl \
    --domains=api.episteme.ai

# Create HTTPS load balancer
gcloud compute target-https-proxies create query-intelligence-proxy \
    --url-map=query-intelligence-map \
    --ssl-certificates=query-intelligence-ssl

# Create forwarding rule
gcloud compute forwarding-rules create query-intelligence-rule \
    --global \
    --target-https-proxy=query-intelligence-proxy \
    --ports=443
```

## Monitoring and Observability

### 1. Health Check Configuration

#### Liveness and Readiness Probes
```yaml
# Cloud Run health check configuration
livenessProbe:
  httpGet:
    path: /health
    port: 8080
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

readinessProbe:
  httpGet:
    path: /ready
    port: 8080
  initialDelaySeconds: 5
  periodSeconds: 5
  timeoutSeconds: 3
  failureThreshold: 2
```

### 2. Logging Configuration

#### Structured Logging Setup
```python
# Configure structured logging
import structlog

structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_log_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.positional_to_keyword,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)
```

### 3. Metrics Collection

#### Prometheus Metrics Setup
```python
# Prometheus metrics configuration
from prometheus_client import Counter, Histogram, Gauge

# Define metrics
query_counter = Counter('queries_total', 'Total queries', ['status', 'intent'])
query_duration = Histogram('query_duration_seconds', 'Query duration')
active_connections = Gauge('active_connections', 'Active WebSocket connections')
```

## Security Hardening

### 1. Network Security

#### VPC Configuration
```bash
# Create VPC network
gcloud compute networks create query-intelligence-vpc \
    --subnet-mode=custom

# Create subnet
gcloud compute networks subnets create query-intelligence-subnet \
    --network=query-intelligence-vpc \
    --range=********/24 \
    --region=us-central1
```

#### Firewall Rules
```bash
# Allow health checks
gcloud compute firewall-rules create allow-health-checks \
    --network=query-intelligence-vpc \
    --action=ALLOW \
    --direction=INGRESS \
    --source-ranges=130.211.0.0/22,35.191.0.0/16 \
    --target-tags=query-intelligence \
    --rules=tcp:8080

# Allow internal communication
gcloud compute firewall-rules create allow-internal \
    --network=query-intelligence-vpc \
    --action=ALLOW \
    --direction=INGRESS \
    --source-ranges=********/24 \
    --target-tags=query-intelligence \
    --rules=tcp:8080
```

### 2. IAM and Access Control

#### Minimum Required Permissions
```yaml
Service Account Permissions:
  - secretmanager.versions.access
  - monitoring.timeSeries.create
  - logging.logEntries.create
  - cloudtrace.traces.patch

Resource Access:
  - Cloud Run service (read/write)
  - Secret Manager secrets (read)
  - Cloud Monitoring (write)
  - Cloud Logging (write)
```

## Deployment Automation

### 1. CI/CD Pipeline

#### GitHub Actions Workflow
```yaml
# .github/workflows/deploy.yml
name: Deploy Query Intelligence

on:
  push:
    branches: [main]
    paths: ['services/query-intelligence/**']

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Run tests
        run: |
          cd services/query-intelligence
          poetry install
          poetry run pytest
          poetry run bandit -r src/

  deploy-staging:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Deploy to staging
        run: |
          gcloud auth activate-service-account --key-file=${{ secrets.GCP_SA_KEY }}
          gcloud builds submit --config=cloudbuild-staging.yaml

  deploy-production:
    needs: deploy-staging
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v4
      - name: Deploy to production
        run: |
          gcloud auth activate-service-account --key-file=${{ secrets.GCP_SA_KEY }}
          gcloud builds submit --config=cloudbuild-production.yaml
```

### 2. Deployment Scripts

#### Automated Deployment Script
```bash
#!/bin/bash
# deploy.sh - Automated deployment script

set -e

ENVIRONMENT=${1:-staging}
IMAGE_TAG=${2:-latest}

echo "Deploying Query Intelligence to $ENVIRONMENT..."

# Validate environment
if [[ "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
  echo "Error: Environment must be 'staging' or 'production'"
  exit 1
fi

# Build and push image
echo "Building Docker image..."
gcloud builds submit --tag gcr.io/vibe-match-463114/query-intelligence:$IMAGE_TAG

# Deploy to Cloud Run
echo "Deploying to Cloud Run..."
gcloud run deploy query-intelligence-$ENVIRONMENT \
  --image gcr.io/vibe-match-463114/query-intelligence:$IMAGE_TAG \
  --platform managed \
  --region us-central1 \
  --service-account <EMAIL> \
  --set-env-vars ENVIRONMENT=$ENVIRONMENT

# Verify deployment
echo "Verifying deployment..."
SERVICE_URL=$(gcloud run services describe query-intelligence-$ENVIRONMENT \
  --region us-central1 --format 'value(status.url)')

curl -f $SERVICE_URL/health || {
  echo "Error: Health check failed"
  exit 1
}

echo "Deployment successful: $SERVICE_URL"
```

## Rollback Procedures

### 1. Automatic Rollback

#### Cloud Run Revision Management
```bash
# List revisions
gcloud run revisions list --service=query-intelligence --region=us-central1

# Rollback to previous revision
PREVIOUS_REVISION=$(gcloud run revisions list \
  --service=query-intelligence \
  --region=us-central1 \
  --limit=2 \
  --format="value(metadata.name)" | tail -n 1)

gcloud run services update-traffic query-intelligence \
  --to-revisions=$PREVIOUS_REVISION=100 \
  --region=us-central1
```

### 2. Emergency Procedures

#### Circuit Breaker Activation
```bash
# Emergency traffic stop
gcloud run services update-traffic query-intelligence \
  --to-revisions=LATEST=0 \
  --region=us-central1

# Scale down to zero
gcloud run services update query-intelligence \
  --min-instances=0 \
  --max-instances=0 \
  --region=us-central1
```

## Troubleshooting

### Common Deployment Issues

#### 1. Container Startup Failures
```bash
# Check logs
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=query-intelligence" --limit=50

# Debug locally
docker run -it --entrypoint=/bin/bash gcr.io/vibe-match-463114/query-intelligence:latest
```

#### 2. Secret Access Issues
```bash
# Verify service account permissions
gcloud projects get-iam-policy vibe-match-463114 \
  --flatten="bindings[].members" \
  --filter="bindings.members:<EMAIL>"

# Test secret access
gcloud secrets versions access latest --secret=google-genai-api-key \
  --impersonate-service-account=<EMAIL>
```

#### 3. Performance Issues
```bash
# Check resource utilization
gcloud monitoring metrics list --filter="resource.type=cloud_run_revision"

# Scale up temporarily
gcloud run services update query-intelligence \
  --cpu=8 \
  --memory=32Gi \
  --min-instances=10 \
  --region=us-central1
```

## Post-Deployment Checklist

### Verification Steps
```yaml
- [ ] Service health check passes
- [ ] All environment variables configured
- [ ] Secret Manager access working
- [ ] Authentication endpoints responding
- [ ] WebSocket connections functional
- [ ] Metrics collection active
- [ ] Logging properly configured
- [ ] Circuit breakers operational
- [ ] Rate limiting functional
- [ ] Security scans passed
- [ ] Performance benchmarks met
- [ ] Monitoring alerts configured
```

---

**Last Updated**: July 2025  
**Deployment Status**: Production Ready  
**Next Review**: August 2025