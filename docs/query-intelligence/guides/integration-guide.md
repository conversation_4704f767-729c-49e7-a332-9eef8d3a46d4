# Query Intelligence Integration Guide

## Overview

This guide provides comprehensive instructions for integrating with the Query Intelligence service. Whether you're building a client application, integrating with another microservice, or setting up monitoring, this guide covers all integration patterns with production-ready examples.

## Quick Start Integration

### Basic Python Client

```python
import httpx
import asyncio
from typing import Optional, Dict, Any

class QueryIntelligenceClient:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url.rstrip('/')
        self.token = token
        self.client = httpx.AsyncClient(
            timeout=30.0,
            headers={
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
        )
    
    async def query(self, query: str, repository_id: str, **kwargs) -> Dict[str, Any]:
        """Send a query and get response"""
        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/query",
                json={
                    "query": query,
                    "repository_id": repository_id,
                    **kwargs
                }
            )
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 429:
                raise Exception("Rate limit exceeded. Please wait before retrying.")
            elif e.response.status_code == 401:
                raise Exception("Authentication failed. Check your token.")
            else:
                raise Exception(f"Query failed: {e.response.text}")
    
    async def close(self):
        await self.client.aclose()

# Usage example
async def main():
    client = QueryIntelligenceClient(
        base_url="https://query-intelligence.ccl.dev",
        token="your-jwt-token"
    )
    
    try:
        result = await client.query(
            "How does authentication work?",
            "repo-123",
            filters={"file_pattern": "*.py"}
        )
        print(f"Answer: {result['answer']}")
        print(f"Confidence: {result['confidence']}")
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(main())
```

### Basic JavaScript Client

```javascript
class QueryIntelligenceClient {
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl.replace(/\/$/, '');
        this.token = token;
    }
    
    async query(query, repositoryId, options = {}) {
        const response = await fetch(`${this.baseUrl}/api/v1/query`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                query,
                repository_id: repositoryId,
                ...options
            })
        });
        
        if (!response.ok) {
            if (response.status === 429) {
                throw new Error('Rate limit exceeded. Please wait before retrying.');
            } else if (response.status === 401) {
                throw new Error('Authentication failed. Check your token.');
            } else {
                const errorText = await response.text();
                throw new Error(`Query failed: ${errorText}`);
            }
        }
        
        return response.json();
    }
}

// Usage example
const client = new QueryIntelligenceClient(
    'https://query-intelligence.ccl.dev',
    'your-jwt-token'
);

try {
    const result = await client.query(
        'How does authentication work?',
        'repo-123',
        { filters: { file_pattern: '*.js' } }
    );
    console.log('Answer:', result.answer);
    console.log('Confidence:', result.confidence);
} catch (error) {
    console.error('Query failed:', error.message);
}
```

## Authentication and Authorization

### JWT Token Structure

The service requires JWT tokens with the following claims:

```json
{
  "sub": "user_id",
  "iat": 1625097600,
  "exp": 1625101200,
  "roles": ["user", "admin"],
  "repository_access": ["repo-123", "repo-456"]
}
```

### Token Management

```python
import jwt
import time
from datetime import datetime, timedelta

def generate_token(user_id: str, secret: str, roles: list = None) -> str:
    """Generate a JWT token for API access"""
    payload = {
        "sub": user_id,
        "iat": int(time.time()),
        "exp": int((datetime.utcnow() + timedelta(hours=1)).timestamp()),
        "roles": roles or ["user"]
    }
    return jwt.encode(payload, secret, algorithm="HS256")

def validate_token(token: str, secret: str) -> dict:
    """Validate and decode a JWT token"""
    try:
        payload = jwt.decode(token, secret, algorithms=["HS256"])
        return payload
    except jwt.ExpiredSignatureError:
        raise Exception("Token has expired")
    except jwt.InvalidTokenError:
        raise Exception("Invalid token")

# Usage
token = generate_token("user-123", "your-secret-key", ["user", "admin"])
payload = validate_token(token, "your-secret-key")
```

### Service Account Setup

For production service-to-service communication:

```python
import json
from google.auth import jwt

def create_service_account_token(service_account_file: str, target_audience: str) -> str:
    """Create a service account JWT token"""
    with open(service_account_file, 'r') as f:
        service_account_info = json.load(f)
    
    credentials = jwt.Credentials.from_service_account_info(
        service_account_info,
        audience=target_audience
    )
    
    return credentials.token

# Usage for Google Cloud
token = create_service_account_token(
    "/path/to/service-account.json",
    "https://query-intelligence.ccl.dev"
)
```

## REST API Integration

### Synchronous Query Processing

```python
import httpx
import asyncio
from typing import Optional, List, Dict, Any

class QueryIntelligenceAPI:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url
        self.token = token
        self.session = httpx.AsyncClient(
            timeout=30.0,
            headers={"Authorization": f"Bearer {token}"}
        )
    
    async def process_query(
        self,
        query: str,
        repository_id: str,
        filters: Optional[Dict] = None,
        options: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Process a single query"""
        payload = {
            "query": query,
            "repository_id": repository_id
        }
        
        if filters:
            payload["filters"] = filters
        if options:
            payload["options"] = options
        
        response = await self.session.post(
            f"{self.base_url}/api/v1/query",
            json=payload
        )
        
        if response.status_code == 429:
            # Handle rate limiting
            retry_after = int(response.headers.get("Retry-After", 60))
            await asyncio.sleep(retry_after)
            return await self.process_query(query, repository_id, filters, options)
        
        response.raise_for_status()
        return response.json()
    
    async def optimize_query(
        self,
        query: str,
        repository_id: str,
        previous_confidence: Optional[float] = None
    ) -> Dict[str, Any]:
        """Get query optimization suggestions"""
        payload = {
            "query": query,
            "repository_id": repository_id
        }
        
        if previous_confidence is not None:
            payload["previous_confidence"] = previous_confidence
        
        response = await self.session.post(
            f"{self.base_url}/api/v1/query/optimize",
            json=payload
        )
        response.raise_for_status()
        return response.json()
    
    async def batch_queries(
        self,
        queries: List[Dict[str, Any]],
        max_concurrent: int = 5
    ) -> List[Dict[str, Any]]:
        """Process multiple queries with concurrency control"""
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_single(query_data):
            async with semaphore:
                return await self.process_query(**query_data)
        
        tasks = [process_single(query) for query in queries]
        return await asyncio.gather(*tasks, return_exceptions=True)

# Usage example
async def main():
    api = QueryIntelligenceAPI(
        "https://query-intelligence.ccl.dev",
        "your-jwt-token"
    )
    
    # Single query
    result = await api.process_query(
        "How does caching work?",
        "repo-123",
        filters={"file_pattern": "*.py", "exclude_tests": True},
        options={"max_results": 10, "confidence_threshold": 0.8}
    )
    
    # Query optimization
    optimization = await api.optimize_query(
        "find cache",
        "repo-123",
        previous_confidence=0.5
    )
    
    # Batch processing
    queries = [
        {"query": "How does auth work?", "repository_id": "repo-123"},
        {"query": "Find database logic", "repository_id": "repo-123"},
        {"query": "Explain error handling", "repository_id": "repo-123"}
    ]
    results = await api.batch_queries(queries)
    
    await api.session.aclose()
```

### Response Handling

```python
def handle_query_response(response: Dict[str, Any]) -> None:
    """Process query response with proper error handling"""
    
    # Check confidence level
    confidence = response.get("confidence", 0.0)
    if confidence < 0.7:
        print(f"⚠️ Low confidence result: {confidence:.2f}")
    
    # Display answer
    print(f"Answer: {response['answer']}")
    
    # Show code references
    for ref in response.get("references", []):
        print(f"📄 {ref['file_path']}:{ref['start_line']}-{ref['end_line']}")
        print(f"   Relevance: {ref['relevance_score']:.2f}")
        if ref.get("snippet"):
            print(f"   Code: {ref['snippet'][:100]}...")
    
    # Display follow-up questions
    follow_ups = response.get("follow_up_questions", [])
    if follow_ups:
        print("💡 Follow-up questions:")
        for question in follow_ups:
            print(f"   - {question}")
    
    # Show metadata
    metadata = response.get("metadata", {})
    print(f"🔍 Model: {metadata.get('model_used', 'unknown')}")
    print(f"⏱️ Time: {response.get('execution_time_ms', 0):.1f}ms")
    print(f"📊 Chunks: {metadata.get('chunks_used', 0)}/{metadata.get('chunks_retrieved', 0)}")
```

## WebSocket Integration

### Real-time Streaming

```python
import websockets
import json
import asyncio
from typing import AsyncGenerator, Dict, Any

class QueryIntelligenceWebSocket:
    def __init__(self, ws_url: str, token: str):
        self.ws_url = ws_url
        self.token = token
        self.websocket = None
    
    async def connect(self):
        """Establish WebSocket connection with authentication"""
        uri = f"{self.ws_url}/api/v1/ws/query?token={self.token}"
        self.websocket = await websockets.connect(uri)
    
    async def stream_query(
        self,
        query: str,
        repository_id: str,
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream query response in real-time"""
        if not self.websocket:
            await self.connect()
        
        # Send query
        query_data = {
            "query": query,
            "repository_id": repository_id,
            **kwargs
        }
        await self.websocket.send(json.dumps(query_data))
        
        # Receive streaming responses
        try:
            async for message in self.websocket:
                data = json.loads(message)
                yield data
                
                if data.get("type") == "done":
                    break
                elif data.get("type") == "error":
                    raise Exception(f"Query error: {data.get('message')}")
        except websockets.exceptions.ConnectionClosed:
            raise Exception("WebSocket connection closed unexpectedly")
    
    async def close(self):
        """Close WebSocket connection"""
        if self.websocket:
            await self.websocket.close()

# Usage example
async def stream_example():
    ws_client = QueryIntelligenceWebSocket(
        "wss://query-intelligence.ccl.dev",
        "your-jwt-token"
    )
    
    try:
        answer_chunks = []
        references = []
        
        async for chunk in ws_client.stream_query(
            "How does the authentication system work?",
            "repo-123"
        ):
            if chunk["type"] == "acknowledged":
                print("🔄 Query acknowledged")
            elif chunk["type"] == "intent_analyzed":
                print(f"🎯 Intent: {chunk['intent']} (confidence: {chunk['confidence']:.2f})")
            elif chunk["type"] == "search_complete":
                print(f"🔍 Found {chunk['results_found']} results in {chunk['search_time_ms']}ms")
            elif chunk["type"] == "reference":
                references.append(chunk["reference"])
                print(f"📄 Reference: {chunk['reference']['file_path']}")
            elif chunk["type"] == "text":
                answer_chunks.append(chunk["content"])
                print(chunk["content"], end="", flush=True)
            elif chunk["type"] == "done":
                print("\n✅ Query complete")
                metadata = chunk.get("metadata", {})
                print(f"Final confidence: {metadata.get('confidence', 0):.2f}")
                
        # Process complete answer
        full_answer = "".join(answer_chunks)
        print(f"\nFull answer length: {len(full_answer)} characters")
        print(f"References found: {len(references)}")
        
    finally:
        await ws_client.close()
```

### WebSocket with Reconnection

```python
import asyncio
import logging
from typing import Callable, Optional

class ReliableWebSocketClient:
    def __init__(self, ws_url: str, token: str, max_retries: int = 5):
        self.ws_url = ws_url
        self.token = token
        self.max_retries = max_retries
        self.websocket = None
        self.reconnect_delay = 1  # Start with 1 second
        self.logger = logging.getLogger(__name__)
    
    async def connect_with_retry(self) -> bool:
        """Connect with exponential backoff retry"""
        for attempt in range(self.max_retries):
            try:
                uri = f"{self.ws_url}/api/v1/ws/query?token={self.token}"
                self.websocket = await websockets.connect(uri)
                self.reconnect_delay = 1  # Reset delay on successful connection
                self.logger.info("WebSocket connected successfully")
                return True
            except Exception as e:
                self.logger.warning(f"Connection attempt {attempt + 1} failed: {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.reconnect_delay)
                    self.reconnect_delay = min(self.reconnect_delay * 2, 30)  # Cap at 30 seconds
        
        return False
    
    async def send_with_retry(self, message: str) -> bool:
        """Send message with automatic reconnection"""
        if not self.websocket:
            if not await self.connect_with_retry():
                return False
        
        try:
            await self.websocket.send(message)
            return True
        except websockets.exceptions.ConnectionClosed:
            self.logger.warning("Connection closed, attempting to reconnect")
            if await self.connect_with_retry():
                await self.websocket.send(message)
                return True
            return False
    
    async def receive_with_reconnect(self) -> Optional[str]:
        """Receive message with automatic reconnection"""
        if not self.websocket:
            if not await self.connect_with_retry():
                return None
        
        try:
            return await self.websocket.recv()
        except websockets.exceptions.ConnectionClosed:
            self.logger.warning("Connection closed during receive")
            return None
```

## Service-to-Service Integration

### Microservice Integration Pattern

```python
import httpx
import asyncio
from typing import Optional, Dict, Any
from contextlib import asynccontextmanager

class QueryIntelligenceService:
    """Production-ready service integration with circuit breaker"""
    
    def __init__(
        self,
        base_url: str,
        service_account_token: str,
        timeout: float = 30.0,
        max_retries: int = 3
    ):
        self.base_url = base_url
        self.timeout = timeout
        self.max_retries = max_retries
        self.client = httpx.AsyncClient(
            timeout=timeout,
            headers={
                "Authorization": f"Bearer {service_account_token}",
                "Content-Type": "application/json",
                "User-Agent": "internal-service/1.0"
            }
        )
        self.circuit_breaker = CircuitBreaker()
    
    @asynccontextmanager
    async def circuit_breaker_context(self):
        """Context manager for circuit breaker protection"""
        if self.circuit_breaker.is_open():
            raise Exception("Circuit breaker is OPEN - Query Intelligence unavailable")
        
        try:
            yield
            self.circuit_breaker.record_success()
        except Exception as e:
            self.circuit_breaker.record_failure()
            raise e
    
    async def query_with_fallback(
        self,
        query: str,
        repository_id: str,
        fallback_response: Optional[str] = None
    ) -> Dict[str, Any]:
        """Query with circuit breaker and fallback"""
        
        async with self.circuit_breaker_context():
            for attempt in range(self.max_retries):
                try:
                    response = await self.client.post(
                        f"{self.base_url}/api/v1/query",
                        json={
                            "query": query,
                            "repository_id": repository_id,
                            "options": {
                                "confidence_threshold": 0.7,
                                "max_results": 5
                            }
                        }
                    )
                    
                    if response.status_code == 429:
                        # Rate limit - wait and retry
                        retry_after = int(response.headers.get("Retry-After", 60))
                        await asyncio.sleep(min(retry_after, 120))  # Cap wait time
                        continue
                    
                    response.raise_for_status()
                    return response.json()
                    
                except httpx.TimeoutException:
                    if attempt == self.max_retries - 1:
                        if fallback_response:
                            return {
                                "answer": fallback_response,
                                "confidence": 0.0,
                                "metadata": {"fallback": True}
                            }
                        raise Exception("Query Intelligence timeout")
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
                
                except httpx.HTTPStatusError as e:
                    if e.response.status_code in [500, 502, 503, 504]:
                        # Server error - retry
                        if attempt == self.max_retries - 1:
                            raise Exception(f"Query Intelligence server error: {e.response.status_code}")
                        await asyncio.sleep(2 ** attempt)
                    else:
                        # Client error - don't retry
                        raise Exception(f"Query Intelligence client error: {e.response.status_code}")
    
    async def health_check(self) -> bool:
        """Check service health"""
        try:
            response = await self.client.get(f"{self.base_url}/health", timeout=5.0)
            return response.status_code == 200
        except Exception:
            return False
    
    async def close(self):
        await self.client.aclose()

class CircuitBreaker:
    """Simple circuit breaker implementation"""
    
    def __init__(self, failure_threshold: int = 5, timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = 0
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def is_open(self) -> bool:
        if self.state == "OPEN":
            if time.time() - self.last_failure_time > self.timeout:
                self.state = "HALF_OPEN"
                return False
            return True
        return False
    
    def record_success(self):
        self.failure_count = 0
        self.state = "CLOSED"
    
    def record_failure(self):
        self.failure_count += 1
        self.last_failure_time = time.time()
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"
```

### Load Balancing and Service Discovery

```python
import random
from typing import List
import httpx

class LoadBalancedQueryService:
    """Load balanced access to multiple Query Intelligence instances"""
    
    def __init__(self, endpoints: List[str], token: str):
        self.endpoints = endpoints
        self.token = token
        self.clients = {
            endpoint: httpx.AsyncClient(
                timeout=30.0,
                headers={"Authorization": f"Bearer {token}"}
            ) for endpoint in endpoints
        }
        self.health_status = {endpoint: True for endpoint in endpoints}
    
    async def get_healthy_endpoint(self) -> str:
        """Get a healthy endpoint using round-robin"""
        healthy_endpoints = [
            endpoint for endpoint, healthy in self.health_status.items()
            if healthy
        ]
        
        if not healthy_endpoints:
            raise Exception("No healthy Query Intelligence endpoints available")
        
        return random.choice(healthy_endpoints)
    
    async def query_with_failover(self, query: str, repository_id: str) -> Dict[str, Any]:
        """Query with automatic failover to healthy endpoints"""
        last_exception = None
        
        # Try healthy endpoints first
        healthy_endpoints = [
            endpoint for endpoint, healthy in self.health_status.items()
            if healthy
        ]
        
        for endpoint in healthy_endpoints:
            try:
                client = self.clients[endpoint]
                response = await client.post(
                    f"{endpoint}/api/v1/query",
                    json={"query": query, "repository_id": repository_id}
                )
                response.raise_for_status()
                return response.json()
                
            except Exception as e:
                last_exception = e
                self.health_status[endpoint] = False
                # Try next endpoint
                continue
        
        # If all healthy endpoints failed, try unhealthy ones
        for endpoint in self.endpoints:
            if self.health_status[endpoint]:
                continue  # Already tried
            
            try:
                client = self.clients[endpoint]
                response = await client.post(
                    f"{endpoint}/api/v1/query",
                    json={"query": query, "repository_id": repository_id}
                )
                response.raise_for_status()
                self.health_status[endpoint] = True  # Mark as healthy again
                return response.json()
                
            except Exception as e:
                last_exception = e
                continue
        
        raise Exception(f"All Query Intelligence endpoints failed. Last error: {last_exception}")
    
    async def periodic_health_check(self):
        """Periodic health check for all endpoints"""
        for endpoint in self.endpoints:
            try:
                client = self.clients[endpoint]
                response = await client.get(f"{endpoint}/health", timeout=5.0)
                self.health_status[endpoint] = response.status_code == 200
            except Exception:
                self.health_status[endpoint] = False
```

## Monitoring and Observability

### Metrics Collection

```python
import time
from prometheus_client import Counter, Histogram, Gauge, start_http_server
from typing import Dict, Any

# Prometheus metrics
QUERY_REQUESTS = Counter(
    'query_intelligence_requests_total',
    'Total Query Intelligence requests',
    ['status', 'intent']
)

QUERY_DURATION = Histogram(
    'query_intelligence_request_duration_seconds',
    'Query Intelligence request duration'
)

QUERY_CONFIDENCE = Histogram(
    'query_intelligence_confidence',
    'Query Intelligence response confidence'
)

ACTIVE_CONNECTIONS = Gauge(
    'query_intelligence_active_connections',
    'Active WebSocket connections'
)

class MonitoredQueryClient:
    """Query Intelligence client with comprehensive monitoring"""
    
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url
        self.token = token
        self.client = httpx.AsyncClient(
            timeout=30.0,
            headers={"Authorization": f"Bearer {token}"}
        )
    
    async def monitored_query(
        self,
        query: str,
        repository_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Query with full monitoring and metrics"""
        start_time = time.time()
        status = "error"
        intent = "unknown"
        
        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/query",
                json={
                    "query": query,
                    "repository_id": repository_id,
                    **kwargs
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                status = "success"
                intent = result.get("intent", "unknown")
                
                # Record confidence metric
                confidence = result.get("confidence", 0.0)
                QUERY_CONFIDENCE.observe(confidence)
                
                # Log successful query
                logger.info(
                    "query_completed",
                    query=query[:100],  # Truncate for privacy
                    intent=intent,
                    confidence=confidence,
                    execution_time_ms=result.get("execution_time_ms", 0),
                    references_count=len(result.get("references", []))
                )
                
                return result
            else:
                status = f"http_{response.status_code}"
                response.raise_for_status()
                
        except httpx.TimeoutException:
            status = "timeout"
            logger.warning("query_timeout", query=query[:100])
            raise
        except httpx.HTTPStatusError as e:
            status = f"http_{e.response.status_code}"
            logger.error(
                "query_http_error",
                query=query[:100],
                status_code=e.response.status_code,
                error=str(e)
            )
            raise
        except Exception as e:
            status = "error"
            logger.error("query_error", query=query[:100], error=str(e))
            raise
        finally:
            # Record metrics
            duration = time.time() - start_time
            QUERY_REQUESTS.labels(status=status, intent=intent).inc()
            QUERY_DURATION.observe(duration)

# Start Prometheus metrics server
def start_metrics_server(port: int = 8000):
    start_http_server(port)
    logger.info(f"Metrics server started on port {port}")
```

### Distributed Tracing

```python
from opentelemetry import trace
from opentelemetry.exporter.jaeger.thrift import JaegerExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
import httpx

# Configure tracing
trace.set_tracer_provider(TracerProvider())
tracer = trace.get_tracer(__name__)

jaeger_exporter = JaegerExporter(
    agent_host_name="localhost",
    agent_port=6831,
)

span_processor = BatchSpanProcessor(jaeger_exporter)
trace.get_tracer_provider().add_span_processor(span_processor)

class TracedQueryClient:
    """Query Intelligence client with distributed tracing"""
    
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url
        self.token = token
        self.client = httpx.AsyncClient(
            timeout=30.0,
            headers={"Authorization": f"Bearer {token}"}
        )
    
    async def traced_query(
        self,
        query: str,
        repository_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Query with distributed tracing"""
        
        with tracer.start_as_current_span("query_intelligence_request") as span:
            # Add span attributes
            span.set_attribute("query.repository_id", repository_id)
            span.set_attribute("query.length", len(query))
            span.set_attribute("service.name", "query-intelligence")
            
            try:
                # Add trace headers for correlation
                trace_headers = {
                    "X-Trace-Id": format(span.get_span_context().trace_id, '032x'),
                    "X-Span-Id": format(span.get_span_context().span_id, '016x')
                }
                
                response = await self.client.post(
                    f"{self.base_url}/api/v1/query",
                    json={
                        "query": query,
                        "repository_id": repository_id,
                        **kwargs
                    },
                    headers=trace_headers
                )
                
                span.set_attribute("http.status_code", response.status_code)
                
                if response.status_code == 200:
                    result = response.json()
                    span.set_attribute("query.intent", result.get("intent", "unknown"))
                    span.set_attribute("query.confidence", result.get("confidence", 0.0))
                    span.set_attribute("query.references_count", len(result.get("references", [])))
                    span.set_status(trace.Status(trace.StatusCode.OK))
                    return result
                else:
                    span.set_status(trace.Status(trace.StatusCode.ERROR, f"HTTP {response.status_code}"))
                    response.raise_for_status()
                    
            except Exception as e:
                span.set_status(trace.Status(trace.StatusCode.ERROR, str(e)))
                span.record_exception(e)
                raise
```

## Testing Integration

### Integration Test Setup

```python
import pytest
import asyncio
from unittest.mock import Mock, patch
import httpx
import json

class MockQueryIntelligence:
    """Mock Query Intelligence service for testing"""
    
    def __init__(self):
        self.queries = []
        self.responses = {}
        self.rate_limit_count = 0
        self.rate_limit_threshold = 50
    
    def add_response(self, query: str, response: dict):
        """Add a mock response for a specific query"""
        self.responses[query] = response
    
    async def handle_request(self, request_data: dict) -> dict:
        """Handle mock request"""
        query = request_data.get("query", "")
        self.queries.append(query)
        
        # Simulate rate limiting
        self.rate_limit_count += 1
        if self.rate_limit_count > self.rate_limit_threshold:
            raise httpx.HTTPStatusError(
                "Rate limit exceeded",
                request=Mock(),
                response=Mock(status_code=429, headers={"Retry-After": "60"})
            )
        
        # Return mock response
        if query in self.responses:
            return self.responses[query]
        
        # Default response
        return {
            "answer": f"Mock answer for: {query}",
            "intent": "EXPLAIN",
            "confidence": 0.85,
            "references": [],
            "execution_time_ms": 50,
            "metadata": {"model_used": "mock-model"}
        }

@pytest.fixture
def mock_query_service():
    """Fixture for mock Query Intelligence service"""
    return MockQueryIntelligence()

@pytest.fixture
async def query_client():
    """Fixture for Query Intelligence client"""
    client = QueryIntelligenceClient(
        "https://test-query-intelligence.com",
        "test-token"
    )
    yield client
    await client.close()

class TestQueryIntelligenceIntegration:
    """Integration tests for Query Intelligence"""
    
    @pytest.mark.asyncio
    async def test_successful_query(self, query_client, mock_query_service):
        """Test successful query processing"""
        
        expected_response = {
            "answer": "Authentication uses JWT tokens",
            "intent": "EXPLAIN",
            "confidence": 0.92,
            "references": [
                {
                    "file_path": "auth.py",
                    "start_line": 1,
                    "end_line": 10,
                    "relevance_score": 0.95
                }
            ]
        }
        
        mock_query_service.add_response(
            "How does authentication work?",
            expected_response
        )
        
        with patch.object(query_client, 'client') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = expected_response
            mock_response.raise_for_status = Mock()
            mock_client.post.return_value = mock_response
            
            result = await query_client.query(
                "How does authentication work?",
                "repo-123"
            )
            
            assert result["answer"] == "Authentication uses JWT tokens"
            assert result["confidence"] == 0.92
            assert len(result["references"]) == 1
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, query_client):
        """Test rate limiting handling"""
        
        with patch.object(query_client, 'client') as mock_client:
            # First call succeeds
            mock_response_success = Mock()
            mock_response_success.status_code = 200
            mock_response_success.json.return_value = {"answer": "Success"}
            mock_response_success.raise_for_status = Mock()
            
            # Second call hits rate limit
            mock_response_rate_limited = Mock()
            mock_response_rate_limited.status_code = 429
            mock_response_rate_limited.headers = {"Retry-After": "1"}
            
            mock_client.post.side_effect = [
                mock_response_success,
                httpx.HTTPStatusError(
                    "Rate limit exceeded",
                    request=Mock(),
                    response=mock_response_rate_limited
                )
            ]
            
            # First query should succeed
            result1 = await query_client.query("Test query 1", "repo-123")
            assert result1["answer"] == "Success"
            
            # Second query should raise rate limit exception
            with pytest.raises(Exception, match="Rate limit exceeded"):
                await query_client.query("Test query 2", "repo-123")
    
    @pytest.mark.asyncio
    async def test_authentication_failure(self, query_client):
        """Test authentication failure handling"""
        
        with patch.object(query_client, 'client') as mock_client:
            mock_response = Mock()
            mock_response.status_code = 401
            
            mock_client.post.side_effect = httpx.HTTPStatusError(
                "Unauthorized",
                request=Mock(),
                response=mock_response
            )
            
            with pytest.raises(Exception, match="Authentication failed"):
                await query_client.query("Test query", "repo-123")

# Load testing with pytest-benchmark
@pytest.mark.benchmark
class TestPerformance:
    """Performance tests for Query Intelligence integration"""
    
    @pytest.mark.asyncio
    async def test_query_latency_benchmark(self, benchmark, query_client):
        """Benchmark query latency"""
        
        async def query_operation():
            return await query_client.query(
                "How does caching work?",
                "repo-123"
            )
        
        result = await benchmark.pedantic(
            query_operation,
            rounds=10,
            iterations=1
        )
        
        # Assert performance requirements
        assert benchmark.stats.stats.mean < 0.1  # < 100ms average
        assert result["confidence"] > 0.0

# Contract testing
def test_query_request_contract():
    """Test Query Intelligence request contract"""
    
    valid_request = {
        "query": "test query",
        "repository_id": "repo-123",
        "filters": {
            "file_pattern": "*.py",
            "exclude_tests": True
        }
    }
    
    # Validate request schema
    assert isinstance(valid_request["query"], str)
    assert len(valid_request["query"]) > 0
    assert isinstance(valid_request["repository_id"], str)
    assert isinstance(valid_request["filters"], dict)

def test_query_response_contract():
    """Test Query Intelligence response contract"""
    
    valid_response = {
        "answer": "Test answer",
        "intent": "EXPLAIN",
        "confidence": 0.85,
        "references": [
            {
                "file_path": "test.py",
                "start_line": 1,
                "end_line": 10,
                "relevance_score": 0.9
            }
        ],
        "execution_time_ms": 50,
        "metadata": {
            "model_used": "gemini-2.5-flash"
        }
    }
    
    # Validate response schema
    assert isinstance(valid_response["answer"], str)
    assert valid_response["intent"] in ["EXPLAIN", "FIND", "DEBUG", "ANALYZE", "COMPARE"]
    assert 0.0 <= valid_response["confidence"] <= 1.0
    assert isinstance(valid_response["references"], list)
    assert valid_response["execution_time_ms"] > 0
```

## Common Integration Issues

### Authentication Problems

**Issue**: 401 Unauthorized responses
```python
# Problem: Invalid or expired JWT token
headers = {"Authorization": "Bearer invalid-token"}

# Solution: Proper token validation
def validate_and_refresh_token(token: str) -> str:
    try:
        payload = jwt.decode(token, verify=False)  # Don't verify for expiry check
        exp = payload.get('exp', 0)
        
        if time.time() > exp - 300:  # Refresh 5 minutes before expiry
            return refresh_token(token)
        return token
    except jwt.InvalidTokenError:
        return get_new_token()
```

**Issue**: Token expiry during long-running operations
```python
class TokenManager:
    def __init__(self, token: str, refresh_callback):
        self.token = token
        self.refresh_callback = refresh_callback
        self.last_refresh = time.time()
    
    async def get_valid_token(self) -> str:
        # Check if token needs refresh (refresh every 50 minutes for 1-hour tokens)
        if time.time() - self.last_refresh > 3000:  # 50 minutes
            self.token = await self.refresh_callback()
            self.last_refresh = time.time()
        return self.token
```

### Rate Limiting

**Issue**: Hitting rate limits
```python
class RateLimitedClient:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url
        self.token = token
        self.last_request_time = 0
        self.min_interval = 1.2  # 50 requests per minute = 1.2 seconds between requests
    
    async def query_with_rate_limit(self, query: str, repository_id: str) -> dict:
        # Ensure minimum interval between requests
        now = time.time()
        time_since_last = now - self.last_request_time
        
        if time_since_last < self.min_interval:
            await asyncio.sleep(self.min_interval - time_since_last)
        
        self.last_request_time = time.time()
        
        # Make request with exponential backoff on rate limit
        for attempt in range(3):
            try:
                return await self._make_request(query, repository_id)
            except RateLimitException as e:
                if attempt == 2:  # Last attempt
                    raise
                await asyncio.sleep(2 ** attempt * 60)  # 1, 2, 4 minutes
```

### Connection Issues

**Issue**: Timeouts and connection failures
```python
class RobustQueryClient:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url
        self.token = token
        
        # Configure robust HTTP client
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(
                connect=10.0,    # Connection timeout
                read=30.0,       # Read timeout
                write=10.0,      # Write timeout
                pool=5.0         # Pool timeout
            ),
            limits=httpx.Limits(
                max_keepalive_connections=20,
                max_connections=100
            ),
            retries=3
        )
    
    async def query_with_retry(self, query: str, repository_id: str) -> dict:
        for attempt in range(3):
            try:
                response = await self.client.post(
                    f"{self.base_url}/api/v1/query",
                    json={"query": query, "repository_id": repository_id}
                )
                response.raise_for_status()
                return response.json()
                
            except (httpx.TimeoutException, httpx.ConnectError) as e:
                if attempt == 2:
                    raise ConnectionError(f"Failed to connect after 3 attempts: {e}")
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
```

### WebSocket Issues

**Issue**: WebSocket disconnections
```python
class ReliableWebSocketQuery:
    async def stream_with_reconnect(self, query: str, repository_id: str):
        max_reconnects = 3
        reconnect_count = 0
        
        while reconnect_count <= max_reconnects:
            try:
                async with websockets.connect(
                    f"{self.ws_url}?token={self.token}",
                    ping_interval=30,  # Send ping every 30 seconds
                    ping_timeout=10,   # Wait 10 seconds for pong
                    close_timeout=10   # Close timeout
                ) as websocket:
                    
                    # Send query
                    await websocket.send(json.dumps({
                        "query": query,
                        "repository_id": repository_id
                    }))
                    
                    # Receive responses
                    async for message in websocket:
                        data = json.loads(message)
                        yield data
                        
                        if data.get("type") == "done":
                            return
                    
            except websockets.exceptions.ConnectionClosed:
                reconnect_count += 1
                if reconnect_count <= max_reconnects:
                    await asyncio.sleep(2 ** reconnect_count)
                    continue
                else:
                    raise ConnectionError("Max reconnection attempts exceeded")
```

## Best Practices Summary

### 1. Authentication
- Always validate tokens before making requests
- Implement token refresh for long-running operations
- Use service accounts for service-to-service communication
- Store tokens securely (environment variables, secret managers)

### 2. Error Handling
- Implement exponential backoff for retries
- Handle rate limiting gracefully
- Use circuit breakers for external service protection
- Provide meaningful error messages to users

### 3. Performance
- Respect rate limits (50 requests/minute)
- Use connection pooling for HTTP clients
- Implement caching for repeated queries
- Monitor response times and set appropriate timeouts

### 4. Monitoring
- Track request/response metrics
- Implement distributed tracing
- Log important events with correlation IDs
- Set up alerts for error rates and latency

### 5. Testing
- Use mocks for unit testing
- Implement contract testing
- Load test your integrations
- Test error scenarios and edge cases

---

**Last Updated**: July 2025  
**API Version**: v1.0.0  
**Contact**: <EMAIL>