# Query Intelligence Performance Tuning Guide

## Overview

This guide provides comprehensive performance optimization strategies for the Query Intelligence service, covering caching, model optimization, resource tuning, and scaling configurations.

## Performance Targets

### Service Level Objectives (SLOs)
```yaml
Response Time:
  p50: < 50ms
  p95: < 100ms (Currently achieving 85ms)
  p99: < 500ms

Throughput:
  Target: 1000+ QPS
  Achieved: 1400+ QPS sustained

Availability:
  Target: 99.95%
  Cold Start: < 2s

Resource Utilization:
  Memory: < 2GB per instance
  CPU: < 80% average utilization
  Cache Hit Rate: > 75% (Currently 75%)
```

## Multi-Level Caching Optimization

### Cache Architecture Overview

```
┌─────────────────────┐
│   Client Cache      │ (Browser/SDK - Optional)
└──────────┬──────────┘
           │
┌──────────▼──────────┐
│  In-Memory Cache    │ (LRU - Hot Data ~1ms)
│  - 100 entries max  │
│  - 5 min TTL        │
└──────────┬──────────┘
           │
┌──────────▼──────────┐
│   Redis Cache       │ (Distributed ~10-50ms)
│  - Query results    │
│  - Embeddings       │
└─────────────────────┘
```

### 1. L1 Cache (In-Memory) Performance

#### Configuration
```python
# Optimized LRU cache settings
class MemoryCacheConfig:
    QUERY_CACHE_SIZE = 1000        # Most frequent queries
    EMBEDDING_CACHE_SIZE = 5000    # Vector embeddings
    RESPONSE_CACHE_SIZE = 500      # Generated responses
    TTL_SECONDS = 3600             # 1 hour expiration

# Cache Types and Key Patterns
class CacheKeyPatterns:
    QUERY_RESULT = "query_cache:{repository_id}:{query_hash}:{filters_hash}"
    EMBEDDING = "embedding:{context_type}:{text_hash}"
    HOT_QUERY = "hot_query:{pattern_hash}"  # For <50 char queries
```

#### Implementation
```python
from functools import lru_cache
import asyncio
from typing import Dict, Any

class OptimizedMemoryCache:
    def __init__(self):
        self._query_cache = {}
        self._embedding_cache = {}
        self._access_counts = {}
        
    @lru_cache(maxsize=1000)
    def get_cached_response(self, query_hash: str) -> Dict[str, Any]:
        """LRU cache for frequent queries"""
        return self._query_cache.get(query_hash)
    
    async def warm_cache(self):
        """Pre-populate cache with popular queries"""
        popular_queries = await self._get_popular_queries()
        for query in popular_queries:
            await self._precompute_response(query)
```

#### Performance Metrics
```yaml
L1 Cache Performance:
  Hit Rate: 30-35%
  Average Latency: ~1ms
  Memory Usage: ~500MB
  Eviction Strategy: LRU with TTL
```

### 2. L2 Cache (Redis) Optimization

#### Redis Configuration
```redis
# redis.conf optimizations
maxmemory 8gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000

# Connection pooling
tcp-keepalive 300
timeout 300
```

#### Connection Pool Tuning
```python
import aioredis
from aioredis.connection import ConnectionPool

# Optimized Redis connection pool
async def create_redis_pool():
    return ConnectionPool(
        connection_class=aioredis.Connection,
        host='redis-host',
        port=6379,
        db=0,
        max_connections=50,        # Pool size
        retry_on_timeout=True,
        socket_keepalive=True,
        socket_keepalive_options={
            1: 1,  # TCP_KEEPIDLE
            2: 3,  # TCP_KEEPINTVL  
            3: 5   # TCP_KEEPCNT
        }
    )
```

#### Cache Key Strategy
```python
class CacheKeyGenerator:
    @staticmethod
    def query_key(query: str, context: str) -> str:
        """Generate optimized cache keys"""
        query_hash = hashlib.sha256(f"{query}{context}".encode()).hexdigest()[:16]
        return f"query:{query_hash}"
    
    @staticmethod
    def embedding_key(text: str) -> str:
        """Cache key for embeddings"""
        text_hash = hashlib.sha256(text.encode()).hexdigest()[:16]
        return f"embedding:{text_hash}"
```

#### Performance Metrics
```yaml
L2 Cache Performance:
  Hit Rate: 60-65%
  Average Latency: 5-15ms
  Memory Usage: 4-6GB
  Network Latency: <2ms (same region)
```

### 3. L3 Cache (Semantic Similarity)

#### Semantic Caching Implementation
```python
from sentence_transformers import SentenceTransformer
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

class SemanticCache:
    def __init__(self, similarity_threshold=0.85):
        self.similarity_threshold = similarity_threshold
        self.embeddings_cache = {}
        self.query_embeddings = []
        self.query_responses = []
        
    async def get_similar_query(self, query_embedding: np.ndarray) -> tuple:
        """Find semantically similar cached queries"""
        if len(self.query_embeddings) == 0:
            return None, 0.0
            
        similarities = cosine_similarity([query_embedding], self.query_embeddings)[0]
        max_similarity_idx = np.argmax(similarities)
        max_similarity = similarities[max_similarity_idx]
        
        if max_similarity >= self.similarity_threshold:
            return self.query_responses[max_similarity_idx], max_similarity
            
        return None, max_similarity
```

### 4. Caching Strategies

#### Write-Through Caching
All successful query results are cached immediately:
```python
# After generating response
await cache_manager.cache_query_result(
    query, repository_id, result, filters
)
```

#### Cache-Aside Pattern
Check cache before expensive operations:
```python
# Check cache first
cached = await cache_manager.get_cached_query_result(...)
if cached:
    return cached
    
# Generate and cache
result = await process_query(...)
await cache_manager.cache_query_result(...)
```

#### Adaptive TTL Strategy
TTL adjusts based on result confidence:
```python
def calculate_ttl(confidence_score: float) -> int:
    """Dynamic TTL based on result confidence"""
    if confidence_score > 0.8:
        return 3600  # High confidence: 1 hour
    elif confidence_score > 0.5:
        return 2700  # Medium confidence: 45 minutes
    else:
        return 1800  # Low confidence: 30 minutes
```

#### Cache Warming
Popular queries are pre-cached:
```python
async def warm_cache(repository_id: str):
    """Pre-populate cache with popular queries"""
    popular_queries = await get_popular_queries(repository_id)
    for query in popular_queries:
        await precompute_and_cache(query, repository_id)
```

#### Performance Metrics
```yaml
L3 Cache Performance:
  Hit Rate: 15-20%
  Average Latency: 20-30ms
  Similarity Threshold: 0.85
  False Positive Rate: <2%
```

## LLM Model Optimization

### 1. Intelligent Model Routing

#### Model Selection Strategy
```python
class ModelRouter:
    def __init__(self):
        self.models = {
            'flash': {
                'max_tokens': 2048,
                'latency': '~50ms',
                'cost': 'low',
                'use_cases': ['simple queries', 'factual questions']
            },
            'flash-lite': {
                'max_tokens': 1024,
                'latency': '~25ms', 
                'cost': 'lowest',
                'use_cases': ['short responses', 'quick answers']
            },
            'pro': {
                'max_tokens': 4096,
                'latency': '~200ms',
                'cost': 'high',
                'use_cases': ['complex analysis', 'detailed explanations']
            }
        }
    
    def select_model(self, query: str, context: dict) -> str:
        """Intelligent model selection based on query complexity"""
        query_length = len(query.split())
        complexity_score = self._calculate_complexity(query, context)
        
        if complexity_score < 0.3 and query_length < 20:
            return 'flash-lite'
        elif complexity_score < 0.7 and query_length < 50:
            return 'flash'
        else:
            return 'pro'
```

### 2. Request Batching and Pooling

#### Batch Processing
```python
import asyncio
from collections import defaultdict

class BatchProcessor:
    def __init__(self, batch_size=10, batch_timeout=0.1):
        self.batch_size = batch_size
        self.batch_timeout = batch_timeout
        self.pending_requests = defaultdict(list)
        
    async def process_batch(self, model: str, requests: list):
        """Process multiple requests in a single API call"""
        try:
            # Combine requests for batch processing
            batch_prompt = self._create_batch_prompt(requests)
            response = await self.llm_client.generate(
                model=model,
                prompt=batch_prompt,
                max_tokens=2048
            )
            
            # Split and distribute responses
            return self._split_batch_response(response, requests)
        except Exception as e:
            # Fallback to individual processing
            return await self._process_individually(requests)
```

### 3. Response Streaming Optimization

#### Streaming Configuration
```python
class StreamingOptimizer:
    def __init__(self):
        self.chunk_size = 50          # Characters per chunk
        self.flush_interval = 0.05    # 50ms between chunks
        self.buffer_size = 1024       # WebSocket buffer
        
    async def stream_response(self, websocket, response_generator):
        """Optimized response streaming"""
        buffer = ""
        last_flush = time.time()
        
        async for chunk in response_generator:
            buffer += chunk
            
            # Flush conditions
            should_flush = (
                len(buffer) >= self.chunk_size or
                time.time() - last_flush >= self.flush_interval or
                chunk.endswith(('.', '!', '?', '\n'))
            )
            
            if should_flush:
                await websocket.send_json({
                    "type": "text",
                    "content": buffer,
                    "timestamp": time.time()
                })
                buffer = ""
                last_flush = time.time()
```

## Database and Vector Search Optimization

### 1. Pinecone Configuration

#### Index Optimization
```python
import pinecone

# Optimized Pinecone configuration
pinecone.init(
    api_key=os.getenv("PINECONE_API_KEY"),
    environment="us-west1-gcp"
)

# Index configuration
index_config = {
    "name": "query-intelligence",
    "dimension": 768,
    "metric": "cosine",
    "pod_type": "p1.x2",      # Performance optimized
    "replicas": 2,            # High availability
    "shards": 2               # Horizontal scaling
}
```

#### Query Optimization
```python
class OptimizedVectorSearch:
    def __init__(self, index):
        self.index = index
        self.batch_size = 100
        
    async def search_with_filters(self, 
                                 embedding: list, 
                                 filters: dict,
                                 top_k: int = 20) -> list:
        """Optimized vector search with filters"""
        # Use metadata filters to reduce search space
        filter_conditions = {
            "repository_id": {"$eq": filters.get("repository_id")},
            "file_type": {"$in": filters.get("file_types", [])}
        }
        
        # Parallel search with different similarity thresholds
        search_tasks = [
            self.index.query(
                vector=embedding,
                filter=filter_conditions,
                top_k=top_k,
                include_metadata=True
            )
        ]
        
        results = await asyncio.gather(*search_tasks)
        return self._merge_and_rerank(results)
```

### 2. Embedding Generation Optimization

#### Batch Embedding Generation
```python
from sentence_transformers import SentenceTransformer
import torch

class OptimizedEmbeddingGenerator:
    def __init__(self):
        self.model = SentenceTransformer('all-mpnet-base-v2')
        self.batch_size = 32
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.model.to(self.device)
        
    async def generate_embeddings_batch(self, texts: list) -> list:
        """Generate embeddings in optimized batches"""
        embeddings = []
        
        for i in range(0, len(texts), self.batch_size):
            batch = texts[i:i + self.batch_size]
            
            # Generate embeddings with optimizations
            with torch.no_grad():
                batch_embeddings = self.model.encode(
                    batch,
                    batch_size=self.batch_size,
                    show_progress_bar=False,
                    convert_to_tensor=True,
                    device=self.device
                )
            
            embeddings.extend(batch_embeddings.cpu().numpy())
            
        return embeddings
```

## Resource Optimization

### 1. Cloud Run Configuration

#### Optimized Cloud Run Settings
```yaml
# Optimal Cloud Run configuration
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: query-intelligence
  annotations:
    run.googleapis.com/execution-environment: gen2
    run.googleapis.com/cpu-boost: "true"
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/minScale: "5"
        autoscaling.knative.dev/maxScale: "200"
        run.googleapis.com/cpu-boost: "true"
    spec:
      containerConcurrency: 20
      containers:
      - image: gcr.io/project/query-intelligence:latest
        resources:
          limits:
            cpu: 4
            memory: 16Gi
        env:
        - name: PYTHONOPTIMIZE
          value: "2"
        - name: PYTHONUNBUFFERED
          value: "1"
```

#### Concurrency Tuning
```python
# FastAPI optimization
from fastapi import FastAPI
import uvicorn

app = FastAPI(
    title="Query Intelligence",
    docs_url="/docs" if os.getenv("ENVIRONMENT") != "production" else None,
    redoc_url=None,
    openapi_url="/openapi.json" if os.getenv("ENVIRONMENT") != "production" else None
)

# Uvicorn configuration for production
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8080,
        workers=1,                    # Single worker (Cloud Run handles scaling)
        loop="uvloop",               # Faster event loop
        http="httptools",            # Faster HTTP parser
        access_log=False,            # Disable access logs for performance
        server_header=False,         # Remove server header
        date_header=False           # Remove date header
    )
```

### 2. Memory Management

#### Memory Optimization
```python
import gc
import psutil
from typing import Optional

class MemoryManager:
    def __init__(self, max_memory_mb: int = 2000):
        self.max_memory_mb = max_memory_mb
        
    def check_memory_usage(self) -> float:
        """Monitor memory usage"""
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        return memory_mb
        
    async def cleanup_if_needed(self):
        """Cleanup memory if usage is high"""
        current_memory = self.check_memory_usage()
        
        if current_memory > self.max_memory_mb * 0.8:
            # Clear caches
            self.cache_manager.clear_expired()
            
            # Force garbage collection
            gc.collect()
            
            # Log memory usage
            logger.info("memory_cleanup", 
                       before_mb=current_memory,
                       after_mb=self.check_memory_usage())
```

### 3. Connection Pool Optimization

#### Database Connection Pooling
```python
from sqlalchemy.pool import QueuePool
import aioredis

# Optimized connection pools
class ConnectionManager:
    def __init__(self):
        # Redis connection pool
        self.redis_pool = aioredis.ConnectionPool(
            host='redis-host',
            port=6379,
            max_connections=50,
            retry_on_timeout=True,
            health_check_interval=30
        )
        
        # HTTP client with connection pooling
        self.http_session = aiohttp.ClientSession(
            connector=aiohttp.TCPConnector(
                limit=100,              # Total pool size
                limit_per_host=20,      # Per host limit
                ttl_dns_cache=300,      # DNS cache TTL
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            ),
            timeout=aiohttp.ClientTimeout(total=30)
        )
```

## Monitoring and Profiling

### 1. Performance Monitoring

#### Custom Metrics
```python
from prometheus_client import Counter, Histogram, Gauge
import time

# Performance metrics
query_duration = Histogram(
    'query_intelligence_query_duration_seconds',
    'Time spent processing queries',
    ['intent', 'model', 'cache_status']
)

cache_hit_rate = Gauge(
    'query_intelligence_cache_hit_rate',
    'Cache hit rate by cache level',
    ['cache_level']
)

memory_usage = Gauge(
    'query_intelligence_memory_usage_mb',
    'Memory usage in MB'
)

@query_duration.labels(intent='explain', model='flash', cache_status='hit').time()
async def process_query_with_metrics(query: str) -> dict:
    """Process query with performance monitoring"""
    start_time = time.time()
    
    try:
        result = await process_query(query)
        return result
    finally:
        # Update memory metrics
        memory_usage.set(psutil.Process().memory_info().rss / 1024 / 1024)
```

### 2. Application Profiling

#### Performance Profiling
```python
import cProfile
import pstats
from functools import wraps

def profile_performance(func):
    """Decorator for profiling function performance"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        if os.getenv("ENABLE_PROFILING") == "true":
            profiler = cProfile.Profile()
            profiler.enable()
            
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                profiler.disable()
                stats = pstats.Stats(profiler)
                stats.sort_stats('cumulative')
                
                # Log top functions
                stats.print_stats(10)
        else:
            return await func(*args, **kwargs)
    return wrapper
```

## Load Testing and Benchmarking

### 1. Load Testing Configuration

#### K6 Load Test Script
```javascript
import http from 'k6/http';
import ws from 'k6/ws';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '5m', target: 100 },   // Ramp up
    { duration: '10m', target: 100 },  // Stay at 100 users
    { duration: '5m', target: 500 },   // Ramp up to 500
    { duration: '10m', target: 500 },  // Stay at 500 users
    { duration: '5m', target: 1000 },  // Ramp up to 1000
    { duration: '10m', target: 1000 }, // Stay at 1000 users
    { duration: '5m', target: 0 },     // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<100'],   // 95% under 100ms
    http_req_failed: ['rate<0.1'],      // Error rate under 0.1%
    ws_connecting: ['p(95)<1000'],      // WebSocket connection under 1s
  },
};

export default function () {
  // REST API testing
  let response = http.post('https://api.example.com/api/v1/query', 
    JSON.stringify({
      query: 'How does authentication work?',
      repository_id: 'test-repo'
    }), 
    {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      }
    }
  );
  
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 100ms': (r) => r.timings.duration < 100,
  });
  
  sleep(1);
}
```

### 2. Benchmark Results

#### Performance Baselines
```yaml
Benchmark Results (July 2025):
  Load Test Configuration:
    - Duration: 30 minutes
    - Peak Load: 1000 concurrent users
    - Query Types: Mixed (simple/complex)
    
  Results:
    Response Time:
      p50: 45ms
      p95: 85ms
      p99: 150ms
      
    Throughput:
      Peak QPS: 1400
      Sustained QPS: 1200
      
    Error Rate: 0.05%
    
    Cache Performance:
      L1 Hit Rate: 32%
      L2 Hit Rate: 63%
      L3 Hit Rate: 18%
      Overall: 75%
      
    Resource Utilization:
      CPU: 65% average
      Memory: 1.8GB average
      Network: 50Mbps average
```

## Optimization Checklist

### Pre-Production Optimization
```yaml
- [ ] Multi-level caching implemented and tuned
- [ ] Model routing optimized for query complexity
- [ ] Connection pools configured for load
- [ ] Memory management and cleanup automated
- [ ] Response streaming optimized
- [ ] Vector search indexes optimized
- [ ] Cloud Run configuration tuned
- [ ] Performance monitoring enabled
- [ ] Load testing completed
- [ ] Benchmark baselines established
```

### Continuous Optimization
```yaml
- [ ] Weekly performance reviews
- [ ] Cache hit rate monitoring (target >75%)
- [ ] Response time monitoring (target <100ms p95)
- [ ] Resource utilization optimization
- [ ] Cost optimization analysis
- [ ] Load testing regression suite
- [ ] Performance alerting configured
- [ ] Capacity planning updated
```

## Troubleshooting Performance Issues

### Common Performance Problems

#### 1. High Response Times
```bash
# Check cache performance
curl -H "Authorization: Bearer $TOKEN" \
  https://api.example.com/admin/cache/stats

# Monitor query patterns
kubectl logs -l app=query-intelligence --tail=100 | grep "slow_query"

# Check resource utilization
gcloud monitoring metrics list --filter="resource.type=cloud_run_revision"
```

#### 2. Memory Issues
```bash
# Monitor memory usage
kubectl top pods -l app=query-intelligence

# Check for memory leaks
curl -H "Authorization: Bearer $TOKEN" \
  https://api.example.com/admin/metrics | grep memory

# Force garbage collection
curl -X POST -H "Authorization: Bearer $TOKEN" \
  https://api.example.com/admin/maintenance/gc
```

#### 3. Cache Performance Issues
```bash
# Analyze cache hit rates
redis-cli info stats | grep hits

# Check cache key distribution
redis-cli --scan --pattern "query:*" | wc -l

# Monitor cache expiration
redis-cli info keyspace
```

---

**Last Updated**: July 2025  
**Performance Status**: Optimized - Meeting all SLOs  
**Next Review**: August 2025