# Query Intelligence Developer Guide

## Overview

Welcome to the Query Intelligence service developer guide! This comprehensive guide will help you set up your development environment, understand the codebase, and contribute effectively to the service.

The Query Intelligence service is a production-ready Python microservice built with FastAPI that processes natural language queries about code. It's part of the CCL (Codebase Context Layer) platform and integrates with multiple services to provide intelligent code understanding capabilities.

## Table of Contents

1. [Development Environment Setup](#development-environment-setup)
2. [Project Structure and Architecture](#project-structure-and-architecture)
3. [Development Workflow](#development-workflow)
4. [Testing Guide](#testing-guide)
5. [Debugging and Troubleshooting](#debugging-and-troubleshooting)
6. [Contributing Guidelines](#contributing-guidelines)
7. [Local Development Tips](#local-development-tips)

## Development Environment Setup

### Prerequisites

Before you begin, ensure you have the following installed:

- **Python 3.11+**: Required for the service (check with `python --version`)
- **Poetry**: Dependency management (install with `pip install poetry`)
- **Docker**: For containerized development and testing
- **Google Cloud SDK**: For GCP interactions (`gcloud` CLI)
- **Redis**: For caching (local or Docker)
- **Git**: Version control

### Step-by-Step Setup

#### 1. Clone the Repository

```bash
git clone https://github.com/episteme/ccl-platform.git
cd ccl-platform/services/query-intelligence
```

#### 2. Install Dependencies

```bash
# Install Python dependencies using Poetry
poetry install

# Activate the virtual environment
poetry shell
```

#### 3. Environment Configuration

Create a `.env` file based on the example:

```bash
cp .env.example .env
```

Edit `.env` with your development settings:

```bash
# Development Environment Configuration
ENVIRONMENT=development
LOG_LEVEL=DEBUG

# External Services (use Docker or local instances)
REDIS_URL=redis://localhost:6379
ANALYSIS_ENGINE_URL=http://localhost:8001
PATTERN_MINING_URL=http://localhost:8003

# Google GenAI Configuration (Development)
USE_VERTEX_AI=false  # Use Gemini API for development
GOOGLE_API_KEY=your-gemini-api-key  # Get from https://makersuite.google.com/app/apikey

# Pinecone Configuration (Optional for development)
PINECONE_API_KEY=your-pinecone-key
PINECONE_INDEX_NAME=dev-embeddings

# Security (Development defaults)
JWT_SECRET_KEY=dev-secret-key-change-in-production
USE_SECRET_MANAGER=false
```

#### 4. Start Redis (Docker)

```bash
# Run Redis in Docker
docker run -d --name redis-dev -p 6379:6379 redis:7-alpine

# Verify Redis is running
docker exec -it redis-dev redis-cli ping
# Should return: PONG
```

#### 5. Verify Installation

```bash
# Check Poetry installation
poetry --version

# Verify Python dependencies
poetry run python -c "import fastapi; print(f'FastAPI version: {fastapi.__version__}')"

# Run the service
poetry run uvicorn query_intelligence.main:app --reload --port 8002
```

Visit http://localhost:8002/docs to see the Swagger UI.

### IDE Setup

#### VS Code Configuration

Create `.vscode/settings.json`:

```json
{
  "python.defaultInterpreterPath": "${workspaceFolder}/.venv/bin/python",
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": false,
  "python.linting.flake8Enabled": false,
  "python.formatting.provider": "black",
  "python.formatting.blackPath": "${workspaceFolder}/.venv/bin/black",
  "python.linting.mypyEnabled": true,
  "python.linting.mypyPath": "${workspaceFolder}/.venv/bin/mypy",
  "[python]": {
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.organizeImports": true
    }
  },
  "python.testing.pytestEnabled": true,
  "python.testing.pytestArgs": ["tests", "-v"]
}
```

Recommended VS Code extensions:
- Python (Microsoft)
- Pylance
- Python Test Explorer
- Docker
- Thunder Client (API testing)

#### PyCharm Configuration

1. Set Python interpreter to Poetry virtual environment
2. Enable Black formatter: Settings → Tools → Black
3. Configure pytest: Settings → Tools → Python Integrated Tools → Testing → pytest
4. Enable type checking: Settings → Editor → Inspections → Python → Type checker

### Development Tools Setup

#### Code Formatting and Linting

```bash
# Format code with Black
poetry run black src/

# Check linting with Ruff
poetry run ruff check src/

# Type checking with mypy
poetry run mypy src/

# Run all checks
poetry run black src/ && poetry run ruff check src/ && poetry run mypy src/
```

#### Pre-commit Hooks (Optional)

Create `.pre-commit-config.yaml`:

```yaml
repos:
  - repo: https://github.com/psf/black
    rev: 24.1.0
    hooks:
      - id: black
        language_version: python3.11
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.1.0
    hooks:
      - id: ruff
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.0.0
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
```

Install pre-commit:
```bash
poetry add --group dev pre-commit
poetry run pre-commit install
```

## Project Structure and Architecture

### Directory Structure

```
services/query-intelligence/
├── src/
│   └── query_intelligence/
│       ├── __init__.py
│       ├── main.py              # FastAPI application entry point
│       ├── api/                 # API endpoints
│       │   ├── query.py         # Main query endpoints
│       │   ├── websocket.py     # WebSocket streaming
│       │   └── admin.py         # Admin endpoints
│       ├── clients/             # External service clients
│       │   ├── redis.py         # Redis cache client
│       │   ├── analysis_engine.py
│       │   └── pattern_mining.py
│       ├── config/              # Configuration
│       │   └── settings.py      # Pydantic settings
│       ├── middleware/          # FastAPI middleware
│       │   ├── auth.py          # JWT authentication
│       │   ├── rate_limit.py    # Rate limiting
│       │   └── security.py      # Security headers
│       ├── models/              # Data models
│       │   ├── query.py         # Query/Response models
│       │   ├── embeddings.py    # Embedding models
│       │   └── response.py      # Response formatting
│       ├── services/            # Business logic
│       │   ├── llm_service_v2.py # Google GenAI integration
│       │   ├── query_processor.py # Query processing
│       │   ├── semantic_search.py # Vector search
│       │   └── cache_manager.py   # Cache management
│       └── utils/               # Utilities
│           ├── circuit_breaker.py # Circuit breaker pattern
│           └── metrics.py         # Prometheus metrics
├── tests/                       # Test suite
│   ├── unit/                    # Unit tests
│   ├── integration/             # Integration tests
│   └── conftest.py             # Test configuration
├── Dockerfile                   # Container definition
├── pyproject.toml              # Poetry configuration
└── .env.example                # Environment example
```

### Key Modules and Responsibilities

#### API Layer (`api/`)
- **query.py**: REST endpoints for query processing
- **websocket.py**: Real-time streaming responses
- **admin.py**: Administrative operations (cache, circuit breakers)

#### Service Layer (`services/`)
- **llm_service_v2.py**: Manages Google GenAI/Vertex AI interactions
- **query_processor.py**: Orchestrates query processing pipeline
- **semantic_search.py**: Handles vector similarity search
- **cache_manager.py**: Semantic caching for responses

#### Client Layer (`clients/`)
- **redis.py**: Redis connection pooling and operations
- **analysis_engine.py**: Integration with Analysis Engine service
- **pattern_mining.py**: Integration with Pattern Mining service

### Design Patterns

#### 1. Dependency Injection

The service uses FastAPI's dependency injection system:

```python
from fastapi import Depends
from .clients.redis import get_redis_client

async def process_query(
    query: str,
    redis_client = Depends(get_redis_client)
):
    # Use injected dependencies
    cached = await redis_client.get(f"query:{query}")
```

#### 2. Async/Await Patterns

All I/O operations use async/await:

```python
async def fetch_embeddings(query: str) -> List[float]:
    # Async HTTP call
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{settings.ANALYSIS_ENGINE_URL}/embeddings",
            json={"text": query}
        )
    return response.json()["embeddings"]
```

#### 3. Circuit Breaker Pattern

Protects against cascading failures:

```python
from .utils.circuit_breaker import CircuitBreaker

analysis_breaker = CircuitBreaker(
    failure_threshold=5,
    recovery_timeout=60,
    call_timeout=10
)

@analysis_breaker
async def call_analysis_engine(data):
    # Protected external call
    pass
```

### Configuration Management

Settings are managed via Pydantic:

```python
from .config.settings import get_settings

settings = get_settings()

# Access configuration
model_name = settings.GEMINI_MODEL_NAME
redis_url = settings.REDIS_URL
```

### Error Handling

Consistent error handling across the service:

```python
from fastapi import HTTPException
import structlog

logger = structlog.get_logger()

try:
    result = await process_query(query)
except ValueError as e:
    logger.error("validation_error", error=str(e))
    raise HTTPException(status_code=422, detail=str(e))
except Exception as e:
    logger.error("processing_error", error=str(e), exc_info=True)
    raise HTTPException(status_code=500, detail="Internal server error")
```

## Development Workflow

### Git Workflow

1. **Branch Naming Convention**
   ```bash
   feature/qi-<issue-number>-<brief-description>
   bugfix/qi-<issue-number>-<brief-description>
   hotfix/qi-<issue-number>-<brief-description>
   ```

2. **Creating a Feature Branch**
   ```bash
   git checkout -b feature/qi-123-add-caching
   ```

3. **Keeping Branch Updated**
   ```bash
   git checkout main
   git pull origin main
   git checkout feature/qi-123-add-caching
   git rebase main
   ```

### Commit Message Standards

Follow [Conventional Commits](https://www.conventionalcommits.org/):

```bash
# Format: <type>(<scope>): <subject>

# Examples:
git commit -m "feat(cache): implement semantic caching for LLM responses"
git commit -m "fix(auth): correct JWT token expiration handling"
git commit -m "test(query): add integration tests for streaming endpoint"
git commit -m "docs(api): update WebSocket connection documentation"
git commit -m "perf(search): optimize vector similarity search"
git commit -m "refactor(llm): simplify model routing logic"
```

### Code Review Process

1. **Before Submitting PR**
   - Run all tests: `poetry run pytest`
   - Check coverage: `poetry run pytest --cov=query_intelligence --cov-report=html`
   - Format code: `poetry run black src/`
   - Lint code: `poetry run ruff check src/`
   - Type check: `poetry run mypy src/`

2. **PR Checklist**
   - [ ] Tests pass with >90% coverage
   - [ ] Code follows style guidelines
   - [ ] Documentation updated
   - [ ] No hardcoded secrets
   - [ ] Error handling implemented
   - [ ] Logging added for debugging
   - [ ] Performance impact considered

3. **Review Guidelines**
   - Focus on correctness and maintainability
   - Check for security vulnerabilities
   - Verify error handling
   - Ensure proper async/await usage
   - Validate external service interactions

### Testing Requirements

- **Unit Test Coverage**: >90% required
- **Integration Tests**: Required for new endpoints
- **Load Tests**: Required for performance-critical changes
- **Security Tests**: Required for auth/security changes

### Documentation Requirements

- Update API documentation for endpoint changes
- Add docstrings to all public functions
- Update README for significant changes
- Document configuration changes in .env.example

## Testing Guide

### Unit Testing

#### Writing Unit Tests

```python
# tests/unit/test_query_processor.py
import pytest
from unittest.mock import AsyncMock, patch
from query_intelligence.services.query_processor import QueryProcessor

@pytest.mark.asyncio
async def test_process_query_with_cache_hit(mock_query_result):
    """Test query processing with cache hit"""
    processor = QueryProcessor()
    
    # Mock cache hit
    with patch('query_intelligence.services.cache_manager.get_cached_response') as mock_cache:
        mock_cache.return_value = mock_query_result
        
        result = await processor.process_query(
            "How does authentication work?",
            repository_id="test-repo"
        )
        
        assert result == mock_query_result
        mock_cache.assert_called_once()
```

#### Running Unit Tests

```bash
# Run all unit tests
poetry run pytest tests/unit/

# Run specific test file
poetry run pytest tests/unit/test_query_processor.py

# Run with coverage
poetry run pytest tests/unit/ --cov=query_intelligence --cov-report=term-missing

# Run with verbose output
poetry run pytest tests/unit/ -v

# Run specific test
poetry run pytest tests/unit/test_query_processor.py::test_process_query_with_cache_hit
```

### Integration Testing

#### Writing Integration Tests

```python
# tests/integration/test_query_api.py
import pytest
from httpx import AsyncClient
from query_intelligence.main import app

@pytest.mark.asyncio
async def test_query_endpoint_integration(auth_headers, sample_query_request):
    """Test full query processing pipeline"""
    async with AsyncClient(app=app, base_url="http://test") as client:
        response = await client.post(
            "/api/v1/query",
            json=sample_query_request,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "answer" in data
        assert "references" in data
        assert data["intent"] in ["EXPLAIN", "FIND", "ANALYZE"]
```

#### Running Integration Tests

```bash
# Start required services
docker-compose up -d redis

# Run integration tests
poetry run pytest tests/integration/

# Run with real external services (slower)
INTEGRATION_TEST_MODE=real poetry run pytest tests/integration/
```

### Load Testing

#### Using Locust for Load Testing

Create `tests/load/locustfile.py`:

```python
from locust import HttpUser, task, between

class QueryUser(HttpUser):
    wait_time = between(1, 3)
    
    def on_start(self):
        # Get auth token
        self.client.headers['Authorization'] = f'Bearer {self.get_token()}'
    
    @task
    def query_simple(self):
        self.client.post("/api/v1/query", json={
            "query": "What is the main function?",
            "repository_id": "test-repo"
        })
    
    @task
    def query_complex(self):
        self.client.post("/api/v1/query", json={
            "query": "Explain the authentication flow in detail",
            "repository_id": "test-repo"
        })
```

Run load tests:

```bash
# Install locust
poetry add --group dev locust

# Run load test
poetry run locust -f tests/load/locustfile.py --host=http://localhost:8002

# Run headless
poetry run locust -f tests/load/locustfile.py \
  --host=http://localhost:8002 \
  --users=100 \
  --spawn-rate=10 \
  --run-time=60s
```

### Test Data Management

#### Using Fixtures

```python
# tests/conftest.py
@pytest.fixture
def sample_code_chunks():
    """Sample code chunks for testing"""
    return [
        {
            "content": "def authenticate(request):\n    token = request.headers.get('Authorization')",
            "file_path": "auth.py",
            "start_line": 10,
            "end_line": 20,
            "relevance": 0.95
        }
    ]

@pytest.fixture
async def mock_llm_response():
    """Mock LLM response for testing"""
    return {
        "text": "The authentication works by validating JWT tokens",
        "usage": {
            "prompt_tokens": 100,
            "completion_tokens": 50
        }
    }
```

### Mocking External Services

#### Mock Google GenAI

```python
from unittest.mock import patch, MagicMock

@patch('google.genai.Client')
def test_with_mock_genai(mock_client):
    # Configure mock
    mock_instance = MagicMock()
    mock_client.return_value = mock_instance
    
    mock_response = MagicMock()
    mock_response.text = "Mocked response"
    mock_instance.models.generate_content.return_value = mock_response
    
    # Test your code
    result = await generate_response("test query")
    assert result == "Mocked response"
```

#### Mock Redis

```python
from unittest.mock import AsyncMock

@pytest.fixture
def mock_redis():
    redis = AsyncMock()
    redis.get = AsyncMock(return_value=None)
    redis.set = AsyncMock(return_value=True)
    redis.expire = AsyncMock(return_value=True)
    return redis
```

### Test Coverage

#### Coverage Requirements

- **Target**: >90% overall coverage
- **Critical Paths**: 100% coverage required
- **New Code**: Must have tests before merge

#### Generate Coverage Report

```bash
# Generate terminal report
poetry run pytest --cov=query_intelligence --cov-report=term-missing

# Generate HTML report
poetry run pytest --cov=query_intelligence --cov-report=html
# Open htmlcov/index.html in browser

# Generate XML report (for CI)
poetry run pytest --cov=query_intelligence --cov-report=xml

# Fail if coverage below threshold
poetry run pytest --cov=query_intelligence --cov-fail-under=90
```

## Debugging and Troubleshooting

### Local Debugging

#### Using VS Code Debugger

Create `.vscode/launch.json`:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Query Intelligence",
      "type": "python",
      "request": "launch",
      "module": "uvicorn",
      "args": [
        "query_intelligence.main:app",
        "--reload",
        "--port", "8002"
      ],
      "env": {
        "PYTHONPATH": "${workspaceFolder}/src"
      },
      "envFile": "${workspaceFolder}/.env"
    },
    {
      "name": "Debug Current Test",
      "type": "python",
      "request": "launch",
      "module": "pytest",
      "args": [
        "${file}",
        "-v"
      ]
    }
  ]
}
```

#### Using Python Debugger (pdb)

```python
# Add breakpoint in code
import pdb; pdb.set_trace()

# Or use the built-in breakpoint()
breakpoint()  # Python 3.7+
```

### Logging Best Practices

#### Structured Logging

```python
import structlog

logger = structlog.get_logger()

# Log with context
logger.info(
    "query_processed",
    query_id="123",
    duration_ms=85.5,
    cache_hit=True,
    model_used="gemini-2.5-flash"
)

# Log errors with full context
try:
    result = await process_query(query)
except Exception as e:
    logger.error(
        "query_processing_failed",
        query_id="123",
        error=str(e),
        error_type=type(e).__name__,
        exc_info=True  # Include stack trace
    )
```

#### Debugging Log Levels

```bash
# Set debug logging
LOG_LEVEL=DEBUG poetry run uvicorn query_intelligence.main:app --reload

# Filter logs by level
poetry run uvicorn query_intelligence.main:app --log-level debug 2>&1 | grep ERROR
```

### Performance Profiling

#### Using cProfile

```python
import cProfile
import pstats

# Profile a function
def profile_query_processing():
    profiler = cProfile.Profile()
    profiler.enable()
    
    # Run your code
    asyncio.run(process_query("test query"))
    
    profiler.disable()
    stats = pstats.Stats(profiler)
    stats.sort_stats('cumulative')
    stats.print_stats(20)  # Top 20 functions
```

#### Memory Profiling

```bash
# Install memory profiler
poetry add --group dev memory-profiler

# Profile memory usage
poetry run mprof run python profile_script.py
poetry run mprof plot
```

### Common Development Issues

#### Issue 1: Redis Connection Failed

**Symptoms**: `redis.exceptions.ConnectionError`

**Solution**:
```bash
# Check if Redis is running
docker ps | grep redis

# Start Redis if not running
docker run -d --name redis-dev -p 6379:6379 redis:7-alpine

# Test connection
redis-cli ping
```

#### Issue 2: Google GenAI Authentication Error

**Symptoms**: `google.auth.exceptions.DefaultCredentialsError`

**Solution**:
```bash
# For development, use API key
export GOOGLE_API_KEY="your-api-key"
export USE_VERTEX_AI=false

# For production, use service account
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account.json"
export USE_VERTEX_AI=true
```

#### Issue 3: Import Errors

**Symptoms**: `ModuleNotFoundError`

**Solution**:
```bash
# Ensure you're in Poetry shell
poetry shell

# Reinstall dependencies
poetry install

# Check PYTHONPATH
export PYTHONPATH="${PYTHONPATH}:${PWD}/src"
```

#### Issue 4: Type Checking Errors

**Symptoms**: mypy errors

**Solution**:
```bash
# Install type stubs
poetry add --group dev types-requests types-redis

# Run mypy with specific configuration
poetry run mypy src/ --ignore-missing-imports
```

### Docker Debugging

#### Debugging in Docker

```bash
# Build with debug symbols
docker build -t query-intelligence:debug \
  --build-arg INSTALL_DEV_DEPS=true .

# Run with debugging enabled
docker run -it --rm \
  -p 8002:8002 \
  -p 5678:5678 \
  -e DEBUGGER=true \
  query-intelligence:debug

# Attach to running container
docker exec -it <container-id> /bin/bash
```

#### Docker Logs

```bash
# View logs
docker logs -f <container-id>

# View last 100 lines
docker logs --tail 100 <container-id>

# Filter logs
docker logs <container-id> 2>&1 | grep ERROR
```

## Contributing Guidelines

### Code Style Guide

#### Python Style

Follow PEP 8 with these additions:

1. **Line Length**: 88 characters (Black default)
2. **Imports**: Use `isort` for consistent ordering
3. **Docstrings**: Google style for all public functions

```python
def process_query(
    query: str,
    repository_id: str,
    filters: Optional[QueryFilters] = None
) -> QueryResult:
    """Process a natural language query about code.
    
    Args:
        query: The natural language query
        repository_id: ID of the repository to search
        filters: Optional filters to apply
        
    Returns:
        QueryResult containing the answer and references
        
    Raises:
        ValueError: If query is empty or too long
        RepositoryNotFoundError: If repository doesn't exist
    """
    # Implementation
```

#### Type Hints

Always use type hints:

```python
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

async def search_code(
    query: str,
    limit: int = 10
) -> List[CodeReference]:
    """Search for code references"""
    pass

class QueryRequest(BaseModel):
    query: str
    repository_id: str
    filters: Optional[Dict[str, Any]] = None
```

### Security Considerations

#### Never Hardcode Secrets

```python
# ❌ Bad
API_KEY = "sk-1234567890abcdef"

# ✅ Good
from .config.settings import get_settings
settings = get_settings()
api_key = settings.get_google_api_key()
```

#### Input Validation

```python
from pydantic import BaseModel, validator

class QueryRequest(BaseModel):
    query: str
    
    @validator('query')
    def validate_query(cls, v):
        if not v or not v.strip():
            raise ValueError("Query cannot be empty")
        if len(v) > 10000:
            raise ValueError("Query too long")
        # Check for potential injection
        if any(char in v for char in ['<script>', 'DROP TABLE']):
            raise ValueError("Invalid characters in query")
        return v.strip()
```

#### Secure External Calls

```python
import httpx
from .utils.circuit_breaker import CircuitBreaker

breaker = CircuitBreaker()

@breaker
async def call_external_service(data: dict) -> dict:
    async with httpx.AsyncClient(
        timeout=httpx.Timeout(10.0),
        limits=httpx.Limits(max_connections=100)
    ) as client:
        response = await client.post(
            "https://api.example.com/endpoint",
            json=data,
            headers={"Authorization": f"Bearer {get_token()}"}
        )
        response.raise_for_status()
        return response.json()
```

### Performance Considerations

#### Async Best Practices

```python
# ❌ Bad - Sequential
results = []
for item in items:
    result = await process_item(item)
    results.append(result)

# ✅ Good - Concurrent
import asyncio
results = await asyncio.gather(*[
    process_item(item) for item in items
])
```

#### Caching Strategy

```python
from functools import lru_cache
from .services.cache_manager import semantic_cache

@semantic_cache(ttl=3600)
async def expensive_operation(query: str) -> str:
    """Cached operation with 1-hour TTL"""
    return await generate_response(query)

@lru_cache(maxsize=1000)
def get_embedding_model():
    """Cache model instance"""
    return SentenceTransformer('all-mpnet-base-v2')
```

#### Connection Pooling

```python
# Reuse HTTP clients
_http_client = None

async def get_http_client() -> httpx.AsyncClient:
    global _http_client
    if _http_client is None:
        _http_client = httpx.AsyncClient(
            limits=httpx.Limits(
                max_connections=100,
                max_keepalive_connections=20
            )
        )
    return _http_client
```

### PR Submission Checklist

Before submitting a PR, ensure:

- [ ] All tests pass: `poetry run pytest`
- [ ] Coverage >90%: `poetry run pytest --cov=query_intelligence --cov-fail-under=90`
- [ ] Code formatted: `poetry run black src/ tests/`
- [ ] Linting passes: `poetry run ruff check src/ tests/`
- [ ] Type checking passes: `poetry run mypy src/`
- [ ] Documentation updated
- [ ] Changelog updated
- [ ] No hardcoded secrets
- [ ] No console.log or print statements
- [ ] Error handling added
- [ ] Logging added for debugging
- [ ] Performance impact considered

## Local Development Tips

### Hot Reloading

Enable automatic reloading on code changes:

```bash
# FastAPI auto-reload
poetry run uvicorn query_intelligence.main:app --reload --port 8002

# Watch for file changes
poetry run watchfiles 'pytest tests/unit/' src/ tests/
```

### Testing with Different Models

#### Model Configuration

```bash
# Test with Flash (fast, good for development)
export GEMINI_MODEL_NAME=gemini-2.5-flash

# Test with Pro (slower, better quality)
export GEMINI_MODEL_NAME=gemini-2.5-pro

# Test with Flash Lite (fastest, simpler queries)
export SIMPLE_QUERY_MODEL=gemini-2.5-flash-lite
export USE_MODEL_ROUTING=true
```

#### Model Routing Testing

```python
# Force specific model in tests
from unittest.mock import patch

@patch.dict(os.environ, {"GEMINI_MODEL_NAME": "gemini-2.5-pro"})
async def test_with_pro_model():
    # Test complex query processing
    pass
```

### Cache Management

#### Clear Cache During Development

```bash
# Clear Redis cache
redis-cli FLUSHDB

# Clear specific keys
redis-cli DEL "cache:query:*"

# Monitor cache operations
redis-cli MONITOR
```

#### Cache Debugging

```python
# Add cache debugging
import structlog
logger = structlog.get_logger()

async def get_cached_response(query_hash: str):
    logger.debug("cache_lookup", key=query_hash)
    result = await redis.get(f"cache:{query_hash}")
    if result:
        logger.info("cache_hit", key=query_hash)
    else:
        logger.info("cache_miss", key=query_hash)
    return result
```

### External Service Simulation

#### Mock Analysis Engine

```python
# tests/mocks/analysis_engine.py
from fastapi import FastAPI
from fastapi.responses import JSONResponse

app = FastAPI()

@app.get("/health")
async def health():
    return {"status": "ok"}

@app.post("/search")
async def search(request: dict):
    return {
        "chunks": [
            {
                "content": "Mock code content",
                "file_path": "mock/file.py",
                "relevance": 0.95
            }
        ],
        "total_count": 1
    }

# Run mock service
# uvicorn tests.mocks.analysis_engine:app --port 8001
```

### Database Seeding

#### Seed Test Data

```python
# scripts/seed_dev_data.py
import asyncio
from query_intelligence.clients.redis import get_redis_client

async def seed_cache():
    redis = get_redis_client()
    
    # Add sample cached queries
    test_queries = [
        ("How does auth work?", "Authentication uses JWT tokens..."),
        ("What is the main function?", "The main function initializes...")
    ]
    
    for query, response in test_queries:
        await redis.set(
            f"cache:query:{hash(query)}",
            response,
            ex=3600
        )
    
    print(f"Seeded {len(test_queries)} cached queries")

if __name__ == "__main__":
    asyncio.run(seed_cache())
```

### Performance Testing

#### Local Load Testing

```bash
# Quick performance test
for i in {1..100}; do
  curl -X POST http://localhost:8002/api/v1/query \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{"query": "test query '$i'", "repository_id": "test"}' &
done
wait

# Monitor resource usage
docker stats
```

#### Profile Endpoints

```python
# Add profiling middleware
from pyinstrument import Profiler

@app.middleware("http")
async def profile_request(request: Request, call_next):
    if "profile" in request.query_params:
        profiler = Profiler()
        profiler.start()
        response = await call_next(request)
        profiler.stop()
        
        return HTMLResponse(profiler.output_html())
    
    return await call_next(request)
```

### Development Scripts

Create helpful scripts in `scripts/`:

```bash
# scripts/dev-setup.sh
#!/bin/bash
set -e

echo "Setting up Query Intelligence development environment..."

# Install dependencies
poetry install

# Start Redis
docker run -d --name redis-dev -p 6379:6379 redis:7-alpine || true

# Copy env file
cp .env.example .env

# Run initial tests
poetry run pytest tests/unit/test_main.py

echo "✅ Development environment ready!"
echo "Run 'poetry run uvicorn query_intelligence.main:app --reload' to start"
```

## Conclusion

This guide provides a comprehensive overview of developing for the Query Intelligence service. Remember to:

1. Always write tests for new functionality
2. Follow the established patterns and conventions
3. Consider performance and security implications
4. Document significant changes
5. Ask for help when needed

For additional resources:
- [API Documentation](/docs/query-intelligence/api/README.md)
- [Operations Runbook](/docs/query-intelligence/operations-runbook.md)
- [Architecture Documentation](/PRPs/services/query-intelligence.md)

Happy coding! 🚀

---

**Last Updated**: July 2025  
**Maintained By**: Query Intelligence Team  
**Questions?** Reach out on #query-intelligence-dev