# 💻 Query Intelligence Service - Developer Guide

## ✅ **Live Service Status**
**Current Status**: OPERATIONAL since July 2025  
**Service Health**: https://query-intelligence-l3nxty7oka-uc.a.run.app/health  
**Port**: 8002 | **Database**: Redis Connected | **Languages**: 15+ Active

## Table of Contents
- [🚀 Quick Start with Live Service](#quick-start-with-live-service)
- [Development Setup](#development-setup)
- [Project Structure](#project-structure)
- [Development Workflow](#development-workflow)
- [Testing Strategy](#testing-strategy)
- [Debugging Guide](#debugging-guide)
- [Performance Profiling](#performance-profiling)
- [Best Practices](#best-practices)
- [Contributing Guidelines](#contributing-guidelines)

## 🚀 Quick Start with Live Service

### **Verify Service is Running**
```bash
# Check if service is operational
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/health
# Expected: {"status":"healthy","timestamp":"2025-07-14T12:00:00Z"}

# Test query endpoint (requires authentication)
curl -X POST https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "query": "How does authentication work?",
    "repository_id": "test-repo"
  }'
```

### **Local Development Quick Start**
```bash
# Clone and setup
git clone https://github.com/episteme/ccl.git
cd ccl/services/query-intelligence

# Install dependencies
poetry install

# Setup environment
cp .env.example .env.local

# Start Redis
docker run -d -p 6379:6379 redis:7-alpine

# Start development server
poetry run uvicorn query_intelligence.main:app --reload --port 8002
```

## Development Setup

### **Prerequisites**
```bash
# Required software
python --version  # 3.11+
poetry --version  # 1.5+
docker --version  # 20.0+
redis-cli --version  # 7.0+

# Install Poetry (if not installed)
curl -sSL https://install.python-poetry.org | python3 -
```

### **Environment Configuration**
```bash
# Create local environment file
cat > .env.local << EOF
# Development environment variables
ENVIRONMENT=development
PORT=8002
HOST=0.0.0.0

# Google Cloud Configuration (Development)
GCP_PROJECT_ID=vibe-match-463114
GCP_REGION=us-central1
GOOGLE_API_KEY=your-development-api-key

# Authentication (Development)
JWT_SECRET=development-jwt-secret
FIREBASE_PROJECT_ID=vibe-match-463114

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_MAX_CONNECTIONS=50

# Service URLs (Development)
ANALYSIS_ENGINE_URL=https://analysis-engine-572735000332.us-central1.run.app
PATTERN_MINING_URL=https://pattern-mining-service.ccl.dev

# Development Settings
MAX_QUERY_LENGTH=10000
RATE_LIMIT_PER_HOUR=10000
SEMANTIC_CACHE_ENABLED=true
CACHE_TTL_SECONDS=300

# Security (Relaxed for Development)
ENABLE_WEBSOCKET_AUTH=false
ENABLE_SECURITY_CONTROLS=true
ENABLE_RATE_LIMITING=false

# Debugging
LOG_LEVEL=DEBUG
ENABLE_DEBUG_ROUTES=true
STRUCTURED_LOGGING=false
EOF
```

### **Local Services Setup**
```bash
# Start Redis
docker run -d --name query-intelligence-redis \
  -p 6379:6379 \
  redis:7-alpine

# Verify Redis connection
redis-cli ping
# Expected: PONG

# Optional: Start Redis with persistence
docker run -d --name query-intelligence-redis \
  -p 6379:6379 \
  -v redis-data:/data \
  redis:7-alpine redis-server --appendonly yes
```

### **Python Environment Setup**
```bash
# Install dependencies
poetry install

# Install development dependencies
poetry install --with dev

# Verify installation
poetry run python -c "import query_intelligence; print('Installation successful')"

# Setup pre-commit hooks
poetry run pre-commit install
```

## Project Structure

### **Overall Architecture**
```
services/query-intelligence/
├── query_intelligence/           # Main application package
│   ├── __init__.py
│   ├── main.py                  # FastAPI application entry point
│   ├── config.py                # Configuration management
│   ├── models/                  # Data models and schemas
│   │   ├── __init__.py
│   │   ├── query.py            # Query models
│   │   ├── response.py         # Response models
│   │   └── auth.py             # Authentication models
│   ├── api/                     # API layer
│   │   ├── __init__.py
│   │   ├── routes/             # API route handlers
│   │   │   ├── __init__.py
│   │   │   ├── query.py        # Query endpoints
│   │   │   ├── admin.py        # Admin endpoints
│   │   │   └── websocket.py    # WebSocket handlers
│   │   ├── middleware/         # Custom middleware
│   │   │   ├── __init__.py
│   │   │   ├── auth.py         # Authentication middleware
│   │   │   ├── rate_limit.py   # Rate limiting
│   │   │   └── logging.py      # Request logging
│   │   └── dependencies.py     # FastAPI dependencies
│   ├── services/               # Business logic layer
│   │   ├── __init__.py
│   │   ├── query_processor.py  # Query processing logic
│   │   ├── response_generator.py # AI response generation
│   │   ├── cache_manager.py    # Caching logic
│   │   ├── auth_service.py     # Authentication service
│   │   └── external/           # External service clients
│   │       ├── analysis_engine.py
│   │       ├── pattern_mining.py
│   │       └── genai_client.py
│   ├── core/                   # Core utilities
│   │   ├── __init__.py
│   │   ├── exceptions.py       # Custom exceptions
│   │   ├── logging.py          # Logging configuration
│   │   ├── metrics.py          # Metrics collection
│   │   └── security.py         # Security utilities
│   └── utils/                  # Helper functions
│       ├── __init__.py
│       ├── validation.py       # Input validation
│       ├── text_processing.py  # Text utilities
│       └── monitoring.py       # Health checks
├── tests/                      # Test suite
│   ├── unit/                   # Unit tests
│   ├── integration/            # Integration tests
│   ├── e2e/                    # End-to-end tests
│   ├── performance/            # Performance tests
│   └── fixtures/               # Test fixtures
├── docs/                       # Service-specific documentation
├── scripts/                    # Utility scripts
├── Dockerfile                  # Container definition
├── pyproject.toml             # Poetry configuration
├── .env.example               # Environment template
└── README.md                  # Service overview
```

### **Key Components**

#### **1. FastAPI Application (`main.py`)**
```python
from fastapi import FastAPI
from query_intelligence.api.routes import query, admin, websocket
from query_intelligence.api.middleware.auth import AuthMiddleware
from query_intelligence.core.logging import setup_logging

def create_app() -> FastAPI:
    app = FastAPI(
        title="Query Intelligence Service",
        description="Natural language query processing for codebases",
        version="2.0.0"
    )
    
    # Setup middleware
    app.add_middleware(AuthMiddleware)
    
    # Include routers
    app.include_router(query.router, prefix="/api/v1")
    app.include_router(admin.router, prefix="/api/v1/admin")
    app.include_router(websocket.router, prefix="/ws")
    
    return app

app = create_app()
```

#### **2. Query Processing Service**
```python
class QueryProcessor:
    def __init__(self):
        self.intent_classifier = IntentClassifier()
        self.context_extractor = ContextExtractor()
        self.query_optimizer = QueryOptimizer()
    
    async def process_query(
        self, 
        query: str, 
        repository_id: str
    ) -> ProcessedQuery:
        # Implementation details
        pass
```

#### **3. Response Generator**
```python
class ResponseGenerator:
    def __init__(self):
        self.genai_client = GoogleGenAIClient()
        self.model_router = ModelRouter()
    
    async def generate_response(
        self, 
        processed_query: ProcessedQuery
    ) -> QueryResponse:
        # Implementation details
        pass
```

## Development Workflow

### **1. Feature Development**
```bash
# Create feature branch
git checkout -b feature/new-query-optimization

# Make changes
# ... code changes ...

# Run tests
poetry run pytest tests/unit/
poetry run pytest tests/integration/

# Run linting and formatting
poetry run black query_intelligence/ tests/
poetry run ruff check query_intelligence/ tests/ --fix
poetry run mypy query_intelligence/

# Commit changes
git add .
git commit -m "feat: add advanced query optimization"

# Push and create PR
git push origin feature/new-query-optimization
```

### **2. Testing Workflow**
```bash
# Run all tests
poetry run pytest

# Run specific test categories
poetry run pytest tests/unit/                    # Unit tests only
poetry run pytest tests/integration/             # Integration tests only
poetry run pytest tests/e2e/                     # End-to-end tests only

# Run with coverage
poetry run pytest --cov=query_intelligence --cov-report=html

# Run specific test file
poetry run pytest tests/unit/test_query_processor.py -v

# Run specific test
poetry run pytest tests/unit/test_query_processor.py::test_process_simple_query -v
```

### **3. Local Service Testing**
```bash
# Start service in development mode
poetry run uvicorn query_intelligence.main:app --reload --port 8002

# Test health endpoint
curl http://localhost:8002/health

# Test readiness endpoint
curl http://localhost:8002/ready

# Test query endpoint (with dev token)
curl -X POST http://localhost:8002/api/v1/query \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer dev-token" \
  -d '{
    "query": "How does authentication work?",
    "repository_id": "test-repo"
  }'
```

## Testing Strategy

### **1. Unit Tests**
```python
# Example unit test: tests/unit/test_query_processor.py
import pytest
from query_intelligence.services.query_processor import QueryProcessor

@pytest.fixture
def query_processor():
    return QueryProcessor()

@pytest.mark.asyncio
async def test_process_simple_query(query_processor):
    result = await query_processor.process_query(
        "What is authentication?",
        "test-repo"
    )
    
    assert result.normalized_query is not None
    assert result.intent.category == "authentication"
    assert result.confidence > 0.8
```

### **2. Integration Tests**
```python
# Example integration test: tests/integration/test_query_api.py
import pytest
from fastapi.testclient import TestClient
from query_intelligence.main import app

@pytest.fixture
def client():
    return TestClient(app)

def test_query_endpoint_success(client, auth_headers):
    response = client.post(
        "/api/v1/query",
        json={
            "query": "How does authentication work?",
            "repository_id": "test-repo"
        },
        headers=auth_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "response" in data
    assert "confidence" in data
    assert data["confidence"] > 0.8
```

### **3. Performance Tests**
```python
# Example performance test: tests/performance/test_load.py
import asyncio
import time
import pytest
from query_intelligence.services.query_processor import QueryProcessor

@pytest.mark.asyncio
async def test_concurrent_query_processing():
    processor = QueryProcessor()
    
    async def process_query(i):
        return await processor.process_query(
            f"Test query {i}",
            "test-repo"
        )
    
    start_time = time.time()
    
    # Process 100 queries concurrently
    tasks = [process_query(i) for i in range(100)]
    results = await asyncio.gather(*tasks)
    
    end_time = time.time()
    duration = end_time - start_time
    
    assert len(results) == 100
    assert duration < 10  # Should complete in under 10 seconds
    assert all(r.confidence > 0.5 for r in results)
```

### **4. E2E Tests**
```python
# Example E2E test: tests/e2e/test_full_workflow.py
import pytest
from fastapi.testclient import TestClient
from query_intelligence.main import app

@pytest.mark.e2e
def test_complete_query_workflow(client, auth_headers):
    # Test health check
    health_response = client.get("/health")
    assert health_response.status_code == 200
    
    # Test query processing
    query_response = client.post(
        "/api/v1/query",
        json={
            "query": "Show me authentication functions",
            "repository_id": "test-repo",
            "include_context": True
        },
        headers=auth_headers
    )
    
    assert query_response.status_code == 200
    data = query_response.json()
    
    # Validate response structure
    assert "query_id" in data
    assert "response" in data
    assert "confidence" in data
    assert "references" in data
    
    # Test query retrieval
    query_id = data["query_id"]
    retrieval_response = client.get(
        f"/api/v1/queries/{query_id}",
        headers=auth_headers
    )
    
    assert retrieval_response.status_code == 200
    retrieved_data = retrieval_response.json()
    assert retrieved_data["query_id"] == query_id
```

## Debugging Guide

### **1. Local Debugging Setup**
```python
# Add to main.py for development
import logging
import uvicorn

if __name__ == "__main__":
    logging.basicConfig(level=logging.DEBUG)
    uvicorn.run(
        "query_intelligence.main:app",
        host="0.0.0.0",
        port=8002,
        reload=True,
        log_level="debug"
    )
```

### **2. Debug Routes (Development Only)**
```python
# Debug routes in query_intelligence/api/routes/debug.py
from fastapi import APIRouter, Depends
from query_intelligence.core.config import get_settings

router = APIRouter()

@router.get("/debug/config")
async def debug_config(settings=Depends(get_settings)):
    if settings.environment != "development":
        raise HTTPException(status_code=404)
    
    return {
        "environment": settings.environment,
        "redis_url": settings.redis_url,
        "cache_enabled": settings.semantic_cache_enabled,
        "debug_enabled": settings.enable_debug_routes
    }

@router.get("/debug/cache")
async def debug_cache():
    # Return cache statistics
    pass
```

### **3. Logging Configuration**
```python
# query_intelligence/core/logging.py
import logging
import structlog
from query_intelligence.core.config import get_settings

def setup_logging():
    settings = get_settings()
    
    if settings.structured_logging:
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
    else:
        logging.basicConfig(
            level=getattr(logging, settings.log_level),
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
```

### **4. Common Debugging Scenarios**

#### **High Latency Issues**
```python
# Add timing middleware for debugging
import time
from fastapi import Request

@app.middleware("http")
async def timing_middleware(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    
    if process_time > 1.0:  # Log slow requests
        logger.warning(
            "Slow request",
            path=request.url.path,
            method=request.method,
            duration=process_time
        )
    
    response.headers["X-Process-Time"] = str(process_time)
    return response
```

#### **Memory Issues**
```python
# Memory profiling decorator
import functools
import tracemalloc

def profile_memory(func):
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        tracemalloc.start()
        
        result = await func(*args, **kwargs)
        
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        logger.info(
            "Memory usage",
            function=func.__name__,
            current_mb=current / 1024 / 1024,
            peak_mb=peak / 1024 / 1024
        )
        
        return result
    return wrapper
```

## Performance Profiling

### **1. Application Profiling**
```python
# Profile query processing
import cProfile
import pstats

def profile_query_processing():
    profiler = cProfile.Profile()
    profiler.enable()
    
    # Run query processing
    # ... your code here ...
    
    profiler.disable()
    stats = pstats.Stats(profiler)
    stats.sort_stats('cumulative')
    stats.print_stats(20)
```

### **2. Memory Profiling**
```bash
# Install memory profiler
poetry add --group dev memory-profiler

# Profile memory usage
poetry run python -m memory_profiler query_intelligence/services/query_processor.py
```

### **3. Line Profiling**
```bash
# Install line profiler
poetry add --group dev line-profiler

# Add @profile decorator to functions and run
poetry run kernprof -l -v query_intelligence/services/query_processor.py
```

### **4. Load Testing**
```bash
# Run load tests
cd tests/performance
npm install
npm run test:load

# Custom load test
poetry run python -m pytest tests/performance/test_load.py -v
```

## Best Practices

### **1. Code Style**
```python
# Follow PEP 8 and type hints
from typing import Optional, List, Dict, Any
import asyncio

async def process_query(
    query: str,
    repository_id: str,
    options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Process a natural language query.
    
    Args:
        query: The natural language query string
        repository_id: Identifier for the repository
        options: Optional processing options
        
    Returns:
        Processed query response
        
    Raises:
        ValidationError: If query is invalid
        ProcessingError: If processing fails
    """
    if not query or len(query.strip()) == 0:
        raise ValidationError("Query cannot be empty")
    
    # Implementation
    pass
```

### **2. Error Handling**
```python
# Use custom exceptions
from query_intelligence.core.exceptions import (
    QueryProcessingError,
    ValidationError,
    AuthenticationError
)

async def safe_query_processing(query: str) -> Dict[str, Any]:
    try:
        result = await process_query(query)
        return result
    except ValidationError as e:
        logger.warning("Query validation failed", error=str(e))
        raise
    except QueryProcessingError as e:
        logger.error("Query processing failed", error=str(e))
        raise
    except Exception as e:
        logger.error("Unexpected error", error=str(e))
        raise QueryProcessingError("Internal processing error")
```

### **3. Configuration Management**
```python
# Use Pydantic for configuration
from pydantic import BaseSettings, Field

class Settings(BaseSettings):
    environment: str = Field(default="development")
    port: int = Field(default=8002)
    redis_url: str = Field(default="redis://localhost:6379")
    google_api_key: str = Field(...)
    
    class Config:
        env_file = ".env"
        case_sensitive = False
```

### **4. Async Best Practices**
```python
# Use async context managers
import asyncio
from contextlib import asynccontextmanager

@asynccontextmanager
async def get_redis_client():
    redis = await aioredis.create_redis_pool("redis://localhost:6379")
    try:
        yield redis
    finally:
        redis.close()
        await redis.wait_closed()

# Use asyncio.gather for concurrent operations
async def process_multiple_queries(queries: List[str]) -> List[Dict[str, Any]]:
    tasks = [process_query(query) for query in queries]
    return await asyncio.gather(*tasks)
```

## Contributing Guidelines

### **1. Development Process**
1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for your changes
5. Run the test suite (`poetry run pytest`)
6. Run linting (`poetry run black . && poetry run ruff check .`)
7. Commit your changes (`git commit -m 'Add amazing feature'`)
8. Push to the branch (`git push origin feature/amazing-feature`)
9. Open a Pull Request

### **2. Pull Request Guidelines**
- Include a clear description of the changes
- Add tests for new functionality
- Update documentation if needed
- Ensure all tests pass
- Follow the existing code style
- Keep commits atomic and well-described

### **3. Code Review Process**
- All PRs require at least one review
- Address review feedback promptly
- Ensure CI/CD pipeline passes
- Update documentation if API changes

### **4. Release Process**
```bash
# Update version
poetry version patch  # or minor, major

# Update CHANGELOG.md
# Create release PR
# Merge to main
# Tag release
git tag v2.1.0
git push origin v2.1.0
```

---

**Documentation Status**: ✅ Complete  
**Last Updated**: July 14, 2025  
**Next Review**: August 14, 2025