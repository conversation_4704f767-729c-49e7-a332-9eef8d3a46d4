# 🛠️ Query Intelligence Service - Operations Runbook

## Service Information

**Service Name**: Query Intelligence  
**Status**: ✅ Production Ready  
**URL**: https://query-intelligence-l3nxty7oka-uc.a.run.app  
**Owner**: Query Intelligence Team  
**On-Call**: #query-intelligence-alerts

## Table of Contents
- [Service Overview](#service-overview)
- [Health Monitoring](#health-monitoring)
- [Performance Monitoring](#performance-monitoring)
- [Incident Response](#incident-response)
- [Common Operations](#common-operations)
- [Troubleshooting](#troubleshooting)
- [Escalation Procedures](#escalation-procedures)
- [Maintenance Procedures](#maintenance-procedures)

## Service Overview

### Architecture
- **Platform**: Google Cloud Run (serverless)
- **Language**: Python 3.11 with FastAPI
- **Dependencies**: Redis, Analysis Engine, Pattern Mining, Google GenAI
- **Data Storage**: Redis (caching), No persistent data

### Key Metrics
- **Response Time**: <100ms p95 target (currently 85ms)
- **Throughput**: 1000+ QPS sustained
- **Error Rate**: <1% target (currently <0.1%)
- **Availability**: 99.9% target

## Health Monitoring

### 1. Health Endpoints

#### Primary Health Check
```bash
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/health
```

**Expected Response**:
```json
{
  "status": "healthy",
  "timestamp": "2025-07-14T12:00:00Z"
}
```

**Alert Conditions**:
- HTTP status != 200
- Response time > 5 seconds
- No response

#### Readiness Check
```bash
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/ready
```

**Expected Response**:
```json
{
  "status": "ready",
  "checks": {
    "redis": "ok",
    "analysis_engine": "ok",
    "pattern_mining": "ok",
    "genai_service": "ok"
  },
  "timestamp": "2025-07-14T12:00:00Z"
}
```

**Alert Conditions**:
- Any dependency status != "ok"
- Overall status != "ready"

### 2. Monitoring Dashboard

#### Google Cloud Console
- **URL**: https://console.cloud.google.com/run/detail/us-central1/query-intelligence
- **Metrics**: Request count, latency, error rate, memory usage

#### Key Metrics to Monitor
```bash
# Request rate
gcloud monitoring metrics list --filter="metric.type=run.googleapis.com/request_count"

# Latency
gcloud monitoring metrics list --filter="metric.type=run.googleapis.com/request_latencies"

# Memory usage
gcloud monitoring metrics list --filter="metric.type=run.googleapis.com/container/memory/utilizations"

# Error rate
gcloud monitoring metrics list --filter="metric.type=run.googleapis.com/container/error_count"
```

### 3. Automated Alerts

#### Critical Alerts (Immediate Response)
- **Service Down**: Health check failures for >2 minutes
- **High Error Rate**: >5% error rate for >5 minutes
- **High Latency**: p95 >500ms for >5 minutes
- **Memory Exhaustion**: >90% memory usage for >5 minutes

#### Warning Alerts (Response within 1 hour)
- **Moderate Error Rate**: >1% error rate for >10 minutes
- **Moderate Latency**: p95 >200ms for >10 minutes
- **High Memory Usage**: >80% memory usage for >15 minutes
- **Dependency Issues**: Any dependency unhealthy

## Performance Monitoring

### 1. Response Time Monitoring

```bash
# Check current latency
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/request_latencies"' \
  --interval.end-time=$(date -Iseconds) \
  --interval.start-time=$(date -d '1 hour ago' -Iseconds)

# Alert thresholds:
# - p50 > 100ms: Warning
# - p95 > 200ms: Warning  
# - p95 > 500ms: Critical
# - p99 > 1000ms: Critical
```

### 2. Throughput Monitoring

```bash
# Check request rate
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/request_count"' \
  --interval.end-time=$(date -Iseconds) \
  --interval.start-time=$(date -d '1 hour ago' -Iseconds)

# Performance targets:
# - Sustained: 1000 QPS
# - Peak: 2000 QPS
# - Auto-scaling: Up to 100 instances
```

### 3. Error Rate Monitoring

```bash
# Check error rate
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence" 
  AND httpRequest.status>=400' \
  --limit=50 \
  --format="table(timestamp,httpRequest.status,httpRequest.requestUrl)"

# Error rate thresholds:
# - >1% for 10 minutes: Warning
# - >5% for 5 minutes: Critical
# - >10% for 2 minutes: Emergency
```

## Incident Response

### 1. Incident Classification

#### Severity 1 (Critical)
- **Definition**: Service completely down or >50% error rate
- **Response Time**: Immediate (< 5 minutes)
- **Escalation**: On-call engineer + manager

#### Severity 2 (High)
- **Definition**: Degraded performance or >10% error rate
- **Response Time**: < 15 minutes
- **Escalation**: On-call engineer

#### Severity 3 (Medium)
- **Definition**: Minor performance issues or dependency warnings
- **Response Time**: < 1 hour
- **Escalation**: During business hours

### 2. Incident Response Playbook

#### Step 1: Initial Assessment (2 minutes)
```bash
# Check service health
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/health

# Check recent logs
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence"' \
  --limit=20

# Check current metrics
gcloud monitoring metrics list --filter="metric.type=run.googleapis.com/request_count"
```

#### Step 2: Identify Root Cause (5 minutes)
```bash
# Check for recent deployments
gcloud run revisions list --service=query-intelligence --region=us-central1 --limit=5

# Check dependency health
curl https://analysis-engine-572735000332.us-central1.run.app/health
redis-cli -h [REDIS_HOST] ping

# Check resource usage
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/container/memory/utilizations"'
```

#### Step 3: Immediate Mitigation (10 minutes)
```bash
# Option 1: Scale up instances
gcloud run services update query-intelligence \
  --min-instances=10 \
  --region=us-central1

# Option 2: Rollback to previous revision
PREVIOUS_REVISION=$(gcloud run revisions list --service=query-intelligence --region=us-central1 --limit=2 --format="value(metadata.name)" | tail -n 1)
gcloud run services update-traffic query-intelligence \
  --to-revisions="$PREVIOUS_REVISION=100" \
  --region=us-central1

# Option 3: Emergency circuit breaker
# (Disable specific features via environment variables)
```

#### Step 4: Validation and Monitoring (15 minutes)
```bash
# Validate fix
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/health
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/ready

# Monitor metrics for 15 minutes
watch -n 30 'gcloud monitoring time-series list --filter="metric.type=run.googleapis.com/request_latencies"'
```

### 3. Communication Templates

#### Initial Alert
```
🚨 INCIDENT: Query Intelligence Service
Severity: [CRITICAL/HIGH/MEDIUM]
Started: [TIMESTAMP]
Impact: [DESCRIPTION]
Current Action: [INVESTIGATING/MITIGATING]
ETA: [TIME]
```

#### Status Update
```
📊 UPDATE: Query Intelligence Incident
Status: [IN PROGRESS/RESOLVED]
Action Taken: [DESCRIPTION]
Current Metrics: [ERROR RATE/LATENCY]
Next Update: [TIME]
```

#### Resolution
```
✅ RESOLVED: Query Intelligence Incident
Duration: [TIME]
Root Cause: [DESCRIPTION]
Fix Applied: [DESCRIPTION]
Follow-up: [POST-MORTEM SCHEDULED]
```

## Common Operations

### 1. Scaling Operations

#### Manual Scaling
```bash
# Scale up for high traffic
gcloud run services update query-intelligence \
  --min-instances=10 \
  --max-instances=200 \
  --region=us-central1

# Scale down for maintenance
gcloud run services update query-intelligence \
  --min-instances=1 \
  --max-instances=50 \
  --region=us-central1
```

#### Auto-scaling Configuration
```bash
# Configure auto-scaling
gcloud run services update query-intelligence \
  --concurrency=1000 \
  --cpu-throttling=false \
  --region=us-central1
```

### 2. Configuration Updates

#### Environment Variables
```bash
# Update environment variable
gcloud run services update query-intelligence \
  --set-env-vars="RATE_LIMIT_PER_HOUR=2000" \
  --region=us-central1

# Update secrets
gcloud run services update query-intelligence \
  --set-secrets="GOOGLE_API_KEY=google-api-key:latest" \
  --region=us-central1
```

#### Memory and CPU
```bash
# Update resource allocation
gcloud run services update query-intelligence \
  --memory=32Gi \
  --cpu=8 \
  --region=us-central1
```

### 3. Log Management

#### View Recent Logs
```bash
# All logs
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence"' \
  --limit=100

# Error logs only
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence" 
  AND severity>=ERROR' \
  --limit=50

# Specific time range
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence"' \
  --after="2025-07-14T12:00:00Z" \
  --before="2025-07-14T13:00:00Z"
```

#### Log Analysis
```bash
# Query processing times
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence" 
  AND jsonPayload.query_duration_ms>0' \
  --format="table(timestamp,jsonPayload.query_duration_ms,jsonPayload.query_id)"

# Authentication errors
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence" 
  AND jsonPayload.error_type="authentication"' \
  --format="table(timestamp,jsonPayload.error_message,httpRequest.remoteIp)"
```

## Troubleshooting

### 1. High Latency Issues

#### Diagnosis
```bash
# Check memory usage
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/container/memory/utilizations"'

# Check CPU usage
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/container/cpu/utilizations"'

# Check Redis connectivity
redis-cli -h [REDIS_HOST] ping
redis-cli -h [REDIS_HOST] info memory
```

#### Solutions
```bash
# Solution 1: Increase resources
gcloud run services update query-intelligence \
  --memory=32Gi \
  --cpu=8 \
  --region=us-central1

# Solution 2: Clear Redis cache
redis-cli -h [REDIS_HOST] FLUSHDB

# Solution 3: Restart service
gcloud run services update query-intelligence \
  --set-env-vars="RESTART_TIMESTAMP=$(date +%s)" \
  --region=us-central1
```

### 2. High Error Rate

#### Diagnosis
```bash
# Check recent errors
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence" 
  AND severity>=ERROR' \
  --limit=50

# Check dependency health
curl https://analysis-engine-572735000332.us-central1.run.app/health
curl https://pattern-mining.ccl.dev/health

# Check authentication issues
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence" 
  AND httpRequest.status=401' \
  --limit=20
```

#### Solutions
```bash
# Solution 1: Check API keys
gcloud secrets versions access latest --secret=google-api-key

# Solution 2: Restart dependencies
# (Contact respective service teams)

# Solution 3: Enable fallback mode
gcloud run services update query-intelligence \
  --set-env-vars="FALLBACK_MODE=true" \
  --region=us-central1
```

### 3. Memory Issues

#### Diagnosis
```bash
# Check memory metrics
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/container/memory/utilizations"'

# Check for memory leaks in logs
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence" 
  AND jsonPayload.message:memory' \
  --limit=50
```

#### Solutions
```bash
# Solution 1: Increase memory limit
gcloud run services update query-intelligence \
  --memory=32Gi \
  --region=us-central1

# Solution 2: Restart service
gcloud run services update query-intelligence \
  --set-env-vars="RESTART_TIMESTAMP=$(date +%s)" \
  --region=us-central1
```

## Escalation Procedures

### 1. On-Call Escalation
- **Level 1**: On-call engineer (Slack: @oncall-query-intelligence)
- **Level 2**: Senior engineer (Slack: @senior-query-intelligence)
- **Level 3**: Engineering manager (Slack: @manager-query-intelligence)
- **Level 4**: Director of Engineering

### 2. External Dependencies
- **Analysis Engine**: Contact @analysis-engine-team
- **Pattern Mining**: Contact @pattern-mining-team
- **Google GenAI**: Check Google Cloud status page
- **Redis**: Check Redis cluster status

### 3. Emergency Contacts
- **Primary On-call**: +1-XXX-XXX-XXXX
- **Secondary On-call**: +1-XXX-XXX-XXXX
- **Manager**: +1-XXX-XXX-XXXX

## Maintenance Procedures

### 1. Scheduled Maintenance

#### Pre-maintenance Checklist
- [ ] Notify stakeholders 24 hours in advance
- [ ] Prepare rollback plan
- [ ] Verify backup systems
- [ ] Schedule during low-traffic hours

#### Maintenance Window
```bash
# 1. Set maintenance mode
gcloud run services update query-intelligence \
  --set-env-vars="MAINTENANCE_MODE=true" \
  --region=us-central1

# 2. Wait for current requests to complete
sleep 60

# 3. Perform maintenance operations
# (Deploy new version, update dependencies, etc.)

# 4. Validate changes
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/health

# 5. Disable maintenance mode
gcloud run services update query-intelligence \
  --remove-env-vars="MAINTENANCE_MODE" \
  --region=us-central1
```

### 2. Routine Operations

#### Daily Checks
- [ ] Review error logs from past 24 hours
- [ ] Check performance metrics
- [ ] Verify all dependencies are healthy
- [ ] Monitor disk and memory usage

#### Weekly Checks
- [ ] Review and analyze performance trends
- [ ] Check for security updates
- [ ] Validate backup procedures
- [ ] Review capacity planning

#### Monthly Checks
- [ ] Conduct disaster recovery test
- [ ] Review and update documentation
- [ ] Analyze cost optimization opportunities
- [ ] Update monitoring thresholds

---

**Last Updated**: July 14, 2025  
**Next Review**: August 14, 2025  
**Document Owner**: Query Intelligence Team