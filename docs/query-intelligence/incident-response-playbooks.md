# Query Intelligence Service - Incident Response Playbooks

## 🚨 Overview

This document provides detailed step-by-step incident response playbooks for the Query Intelligence service. These playbooks complement the Operations Runbook and provide specific procedures for common incident scenarios.

**Service Details:**
- **Service Name**: query-intelligence
- **Environment**: Production (Google Cloud Run)
- **URL**: https://query-intelligence.ccl.dev
- **Team**: Query Intelligence Team
- **On-call**: <EMAIL>

## 📋 Incident Classification Matrix

| Severity | Response Time | Definition | Examples |
|----------|---------------|------------|----------|
| **P0 - Critical** | 15 minutes | Complete service outage | Health endpoint down, all requests failing |
| **P1 - High** | 1 hour | Major functionality impacted | >50% error rate, >5s response time |
| **P2 - Medium** | 4 hours | Degraded performance | Elevated latency, circuit breakers open |
| **P3 - Low** | 24 hours | Minor issues | Low cache hit rate, warnings |

## 🔥 P0 - Critical Incident Playbooks

### P0.1 - Complete Service Outage

**Symptoms:**
- Health endpoint returns 503 or times out
- All API requests failing
- No metrics being reported
- Users unable to access service

**Immediate Response (0-15 minutes):**

1. **Confirm the incident**
   ```bash
   # Check service health
   curl -f https://query-intelligence.ccl.dev/health
   
   # Check Cloud Run service status
   gcloud run services describe query-intelligence --region=us-central1
   ```

2. **Check recent deployments**
   ```bash
   # List recent revisions
   gcloud run revisions list --service=query-intelligence --region=us-central1 --limit=5
   
   # Check deployment logs
   gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=query-intelligence" --limit=20
   ```

3. **Assess impact**
   ```bash
   # Check error rates
   gcloud monitoring read --filter='metric.type="run.googleapis.com/request_count"' --start-time="-30m"
   
   # Check active instances
   gcloud run services describe query-intelligence --region=us-central1 --format="value(status.conditions[0].message)"
   ```

4. **Immediate mitigation**
   ```bash
   # If recent deployment issue - rollback immediately
   PREVIOUS_REVISION=$(gcloud run revisions list --service=query-intelligence --region=us-central1 --limit=2 --format="value(metadata.name)" | tail -n 1)
   
   gcloud run services update-traffic query-intelligence \
     --to-revisions=$PREVIOUS_REVISION=100 \
     --region=us-central1
   ```

**Escalation (15-30 minutes):**

5. **Create incident channel**
   ```bash
   # Template for incident channel
   # Channel: #incident-qi-[TIMESTAMP]
   # Title: "P0: Query Intelligence Service Complete Outage"
   # Initial status: "Investigating service outage, impact: 100% of users"
   ```

6. **Notify stakeholders**
   ```bash
   # Send to:
   # - <EMAIL>
   # - <EMAIL>
   # - <EMAIL>
   ```

**Investigation (30-60 minutes):**

7. **Deep dive analysis**
   ```bash
   # Check container logs
   gcloud logs read "resource.type=cloud_run_revision AND severity>=ERROR" --limit=100
   
   # Check infrastructure issues
   gcloud compute operations list --filter="operationType=compute.instances.start" --limit=10
   
   # Check quotas
   gcloud compute project-info describe --format="value(quotas[].usage,quotas[].limit)"
   ```

8. **Service dependencies**
   ```bash
   # Check Redis
   gcloud redis instances describe query-intelligence-cache --region=us-central1
   
   # Check analysis-engine
   curl -f https://analysis-engine.ccl.dev/health
   
   # Check pattern-mining
   curl -f https://pattern-mining.ccl.dev/health
   ```

**Resolution Actions:**
- Scale up resources if capacity issue
- Fix configuration if config problem
- Coordinate with infrastructure team for platform issues
- Implement temporary workarounds if needed

### P0.2 - Data Loss Incident

**Symptoms:**
- Users reporting lost query history
- Cache completely empty
- Database connection errors
- Data corruption detected

**Immediate Response (0-15 minutes):**

1. **Stop all write operations**
   ```bash
   # Enable read-only mode
   gcloud run services update query-intelligence \
     --set-env-vars="READ_ONLY_MODE=true" \
     --region=us-central1
   ```

2. **Assess data integrity**
   ```bash
   # Check Redis data
   redis-cli -h $REDIS_HOST info keyspace
   
   # Check recent backups
   gcloud storage ls gs://query-intelligence-backups/
   ```

3. **Preserve evidence**
   ```bash
   # Take Redis snapshot
   redis-cli -h $REDIS_HOST --rdb /tmp/emergency-dump.rdb
   
   # Export logs
   gcloud logs read "resource.type=cloud_run_revision" --format=json > incident_logs.json
   ```

**Recovery Actions:**
- Restore from most recent backup
- Verify data integrity
- Gradually restore write operations
- Communicate with affected users

### P0.3 - Security Breach

**Symptoms:**
- Unauthorized access detected
- Suspicious API usage patterns
- JWT token compromise
- External security alert

**Immediate Response (0-15 minutes):**

1. **Isolate the service**
   ```bash
   # Disable public access
   gcloud run services update query-intelligence \
     --ingress=internal \
     --region=us-central1
   ```

2. **Revoke all tokens**
   ```bash
   # Rotate JWT secret
   gcloud secrets versions add jwt-secret-key --data-file=new-secret.key
   
   # Update service with new secret
   gcloud run services update query-intelligence \
     --update-secrets=JWT_SECRET_KEY=jwt-secret-key:latest \
     --region=us-central1
   ```

3. **Capture forensic data**
   ```bash
   # Export access logs
   gcloud logs read "resource.type=cloud_run_revision AND httpRequest.status=401" --format=json > security_logs.json
   
   # Check IP patterns
   gcloud logs read "resource.type=cloud_run_revision" --format="value(httpRequest.remoteIp)" | sort | uniq -c | sort -nr
   ```

**Investigation Actions:**
- Analyze access patterns
- Check for data exfiltration
- Coordinate with security team
- Document timeline and impact

## ⚠️ P1 - High Priority Incident Playbooks

### P1.1 - High Error Rate (>50%)

**Symptoms:**
- Error rate exceeding 50%
- Multiple 500/503 status codes
- Circuit breakers opening
- User reports of failures

**Investigation (0-30 minutes):**

1. **Identify error patterns**
   ```bash
   # Check error distribution
   gcloud logs read "resource.type=cloud_run_revision AND httpRequest.status>=500" --format=json --limit=100 | jq '.httpRequest.status' | sort | uniq -c
   
   # Check error types
   gcloud logs read "resource.type=cloud_run_revision AND severity=ERROR" --format=json --limit=50 | jq '.jsonPayload.error'
   ```

2. **Check circuit breaker status**
   ```bash
   curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     https://query-intelligence.ccl.dev/circuit-breakers
   ```

3. **Analyze request patterns**
   ```bash
   # Check request volume
   gcloud monitoring read --filter='metric.type="run.googleapis.com/request_count"' --start-time="-1h"
   
   # Check response times
   gcloud monitoring read --filter='metric.type="run.googleapis.com/request_latencies"' --start-time="-1h"
   ```

**Mitigation Actions:**

4. **Reset circuit breakers if needed**
   ```bash
   curl -X POST -H "Authorization: Bearer $ADMIN_TOKEN" \
     https://query-intelligence.ccl.dev/api/v1/admin/circuit-breakers/reset
   ```

5. **Scale up resources**
   ```bash
   gcloud run services update query-intelligence \
     --memory=16Gi \
     --cpu=8 \
     --min-instances=10 \
     --max-instances=300 \
     --region=us-central1
   ```

6. **Enable fallback mode**
   ```bash
   gcloud run services update query-intelligence \
     --set-env-vars="ENABLE_FALLBACK_MODE=true" \
     --region=us-central1
   ```

### P1.2 - Extreme Latency (>5s response time)

**Symptoms:**
- P95 response time >5 seconds
- Request timeouts
- User reports of slow responses
- Gateway timeouts

**Investigation (0-30 minutes):**

1. **Check current performance**
   ```bash
   # Check response time distribution
   gcloud monitoring read --filter='metric.type="run.googleapis.com/request_latencies"' --start-time="-30m"
   
   # Check CPU/memory usage
   gcloud monitoring read --filter='metric.type="run.googleapis.com/container/cpu/utilizations"' --start-time="-30m"
   ```

2. **Analyze bottlenecks**
   ```bash
   # Check cache performance
   curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     https://query-intelligence.ccl.dev/api/v1/admin/cache/stats
   
   # Check external service latency
   curl -w "@curl-format.txt" -o /dev/null https://analysis-engine.ccl.dev/health
   ```

3. **Check for resource constraints**
   ```bash
   # Check memory usage
   gcloud monitoring read --filter='metric.type="run.googleapis.com/container/memory/utilizations"' --start-time="-30m"
   
   # Check instance scaling
   gcloud run services describe query-intelligence --region=us-central1 --format="value(status.conditions[0].message)"
   ```

**Mitigation Actions:**

4. **Optimize resource allocation**
   ```bash
   # Scale up CPU and memory
   gcloud run services update query-intelligence \
     --cpu=8 \
     --memory=32Gi \
     --region=us-central1
   ```

5. **Enable aggressive caching**
   ```bash
   gcloud run services update query-intelligence \
     --set-env-vars="CACHE_TTL=3600,AGGRESSIVE_CACHING=true" \
     --region=us-central1
   ```

6. **Reduce external dependencies**
   ```bash
   # Temporarily disable non-essential features
   gcloud run services update query-intelligence \
     --set-env-vars="DISABLE_PATTERN_MINING=true,DISABLE_SEMANTIC_SEARCH=true" \
     --region=us-central1
   ```

### P1.3 - Critical Dependency Failure

**Symptoms:**
- Analysis Engine completely down
- Pattern Mining service unavailable
- Redis cluster failure
- Multiple circuit breakers open

**Investigation (0-30 minutes):**

1. **Identify failed dependencies**
   ```bash
   # Check circuit breaker status
   curl https://query-intelligence.ccl.dev/circuit-breakers
   
   # Test each dependency
   curl -f https://analysis-engine.ccl.dev/health
   curl -f https://pattern-mining.ccl.dev/health
   redis-cli -h $REDIS_HOST ping
   ```

2. **Assess impact**
   ```bash
   # Check fallback activation
   gcloud logs read "jsonPayload.event=fallback_activated" --limit=50
   
   # Check error rates by endpoint
   gcloud logs read "resource.type=cloud_run_revision AND httpRequest.status>=500" --format=json | jq '.httpRequest.requestUrl' | sort | uniq -c
   ```

**Mitigation Actions:**

3. **Enable comprehensive fallback**
   ```bash
   gcloud run services update query-intelligence \
     --set-env-vars="ENABLE_FALLBACK_MODE=true,FALLBACK_LEVEL=comprehensive" \
     --region=us-central1
   ```

4. **Coordinate with dependency teams**
   ```bash
   # Template for dependency team communication
   # Subject: "URGENT: Query Intelligence affected by [SERVICE] outage"
   # Body: Include impact metrics, timeline, and assistance needed
   ```

5. **Implement temporary workarounds**
   ```bash
   # Use cached responses where possible
   gcloud run services update query-intelligence \
     --set-env-vars="CACHE_FALLBACK_ENABLED=true,CACHE_FALLBACK_TTL=86400" \
     --region=us-central1
   ```

## 📊 P2 - Medium Priority Incident Playbooks

### P2.1 - Degraded Performance

**Symptoms:**
- Response time 200-500ms (elevated but not critical)
- Cache hit rate <65%
- Some circuit breakers in half-open state
- Increased resource usage

**Investigation (0-60 minutes):**

1. **Performance analysis**
   ```bash
   # Check detailed metrics
   curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     https://query-intelligence.ccl.dev/api/v1/admin/metrics
   
   # Analyze query patterns
   curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     https://query-intelligence.ccl.dev/api/v1/admin/queries/stats
   ```

2. **Resource optimization**
   ```bash
   # Check for memory leaks
   gcloud monitoring read --filter='metric.type="run.googleapis.com/container/memory/utilizations"' --start-time="-2h"
   
   # Check CPU patterns
   gcloud monitoring read --filter='metric.type="run.googleapis.com/container/cpu/utilizations"' --start-time="-2h"
   ```

**Optimization Actions:**

3. **Cache optimization**
   ```bash
   # Clear fragmented cache
   curl -X DELETE -H "Authorization: Bearer $ADMIN_TOKEN" \
     https://query-intelligence.ccl.dev/api/v1/admin/cache/fragmented
   
   # Warm cache with frequent queries
   curl -X POST -H "Authorization: Bearer $ADMIN_TOKEN" \
     https://query-intelligence.ccl.dev/api/v1/admin/cache/warm
   ```

4. **Gradual scaling**
   ```bash
   # Slightly increase resources
   gcloud run services update query-intelligence \
     --cpu=2 \
     --memory=12Gi \
     --region=us-central1
   ```

### P2.2 - Circuit Breaker Issues

**Symptoms:**
- One or more circuit breakers stuck in OPEN state
- Intermittent failures to external services
- Fallback responses being served
- External service timeouts

**Investigation (0-60 minutes):**

1. **Circuit breaker analysis**
   ```bash
   # Get detailed circuit breaker status
   curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     https://query-intelligence.ccl.dev/circuit-breakers
   
   # Check failure patterns
   gcloud logs read "jsonPayload.event=circuit_breaker_opened" --limit=50
   ```

2. **External service health**
   ```bash
   # Test each external service
   curl -v https://analysis-engine.ccl.dev/health
   curl -v https://pattern-mining.ccl.dev/health
   
   # Check service response times
   for i in {1..5}; do
     curl -w "@curl-format.txt" -o /dev/null https://analysis-engine.ccl.dev/health
     sleep 1
   done
   ```

**Resolution Actions:**

3. **Reset circuit breakers**
   ```bash
   # Reset specific breaker
   curl -X POST -H "Authorization: Bearer $ADMIN_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"breaker_name": "analysis_engine"}' \
     https://query-intelligence.ccl.dev/api/v1/admin/circuit-breakers/reset
   ```

4. **Adjust thresholds temporarily**
   ```bash
   # Increase failure threshold if external service is flaky
   gcloud run services update query-intelligence \
     --set-env-vars="CIRCUIT_BREAKER_FAILURE_THRESHOLD=10,CIRCUIT_BREAKER_RECOVERY_TIMEOUT=120" \
     --region=us-central1
   ```

## 🔍 P3 - Low Priority Incident Playbooks

### P3.1 - Cache Performance Issues

**Symptoms:**
- Cache hit rate <50%
- Increased response times
- High cache miss rates
- Memory usage concerns

**Investigation (0-2 hours):**

1. **Cache analysis**
   ```bash
   # Get cache statistics
   curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     https://query-intelligence.ccl.dev/api/v1/admin/cache/stats
   
   # Check Redis memory usage
   redis-cli -h $REDIS_HOST info memory
   ```

2. **Cache pattern analysis**
   ```bash
   # Check cache key patterns
   redis-cli -h $REDIS_HOST --scan --pattern "cache:*" | head -50
   
   # Check TTL distribution
   redis-cli -h $REDIS_HOST --scan --pattern "cache:*" | xargs redis-cli -h $REDIS_HOST TTL
   ```

**Optimization Actions:**

3. **Cache optimization**
   ```bash
   # Optimize TTL settings
   gcloud run services update query-intelligence \
     --set-env-vars="CACHE_TTL=1800,SEMANTIC_CACHE_TTL=3600" \
     --region=us-central1
   
   # Clear stale entries
   curl -X DELETE -H "Authorization: Bearer $ADMIN_TOKEN" \
     https://query-intelligence.ccl.dev/api/v1/admin/cache/stale
   ```

### P3.2 - Log Volume Issues

**Symptoms:**
- High log volume
- Increased logging costs
- Log processing delays
- Storage quota warnings

**Investigation (0-2 hours):**

1. **Log analysis**
   ```bash
   # Check log volume by severity
   gcloud logs read "resource.type=cloud_run_revision" --format=json | jq '.severity' | sort | uniq -c
   
   # Check verbose logging components
   gcloud logs read "jsonPayload.logger_name" --format=json | jq '.jsonPayload.logger_name' | sort | uniq -c
   ```

**Optimization Actions:**

2. **Adjust log levels**
   ```bash
   # Reduce log verbosity
   gcloud run services update query-intelligence \
     --set-env-vars="LOG_LEVEL=INFO,DISABLE_DEBUG_LOGGING=true" \
     --region=us-central1
   ```

## 🛠️ Recovery Procedures

### Post-Incident Recovery

1. **Service restoration verification**
   ```bash
   # Health check
   curl -f https://query-intelligence.ccl.dev/health
   
   # Functional test
   curl -X POST https://query-intelligence.ccl.dev/api/v1/query \
     -H "Authorization: Bearer $TEST_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"query": "test recovery", "repository_id": "test-repo"}'
   ```

2. **Performance validation**
   ```bash
   # Check response times
   gcloud monitoring read --filter='metric.type="run.googleapis.com/request_latencies"' --start-time="-15m"
   
   # Check error rates
   gcloud monitoring read --filter='metric.type="run.googleapis.com/request_count"' --start-time="-15m"
   ```

3. **Resource cleanup**
   ```bash
   # Reset temporary configurations
   gcloud run services update query-intelligence \
     --remove-env-vars="ENABLE_FALLBACK_MODE,READ_ONLY_MODE" \
     --region=us-central1
   
   # Restore normal resource allocation
   gcloud run services update query-intelligence \
     --cpu=4 \
     --memory=16Gi \
     --min-instances=5 \
     --max-instances=200 \
     --region=us-central1
   ```

### Post-Incident Review

1. **Incident documentation**
   - Create detailed incident report
   - Document timeline and actions taken
   - Identify root cause analysis
   - List lessons learned

2. **Process improvements**
   - Update runbooks based on learnings
   - Improve monitoring and alerting
   - Enhance automation where possible
   - Plan preventive measures

3. **Team communication**
   - Share incident summary with team
   - Conduct post-incident review meeting
   - Update documentation
   - Plan follow-up actions

## 📞 Contact Information

### Escalation Matrix

| Level | Role | Contact | Response Time |
|-------|------|---------|---------------|
| L1 | On-call Engineer | <EMAIL> | 15 minutes |
| L2 | Senior Engineer | <EMAIL> | 30 minutes |
| L3 | Team Lead | <EMAIL> | 1 hour |
| L4 | Engineering Manager | <EMAIL> | 2 hours |

### External Dependencies

| Service | Contact | Emergency Contact |
|---------|---------|-------------------|
| Analysis Engine | <EMAIL> | <EMAIL> |
| Pattern Mining | <EMAIL> | <EMAIL> |
| Google Cloud | GCP Support Case | <EMAIL> |
| Redis | Redis Enterprise Support | <EMAIL> |

## 📋 Incident Response Checklist

### During Incident
- [ ] Confirm incident severity
- [ ] Create incident channel
- [ ] Document actions taken
- [ ] Communicate with stakeholders
- [ ] Implement mitigation steps
- [ ] Monitor for recovery
- [ ] Update status regularly

### After Incident
- [ ] Verify complete recovery
- [ ] Reset configurations
- [ ] Document lessons learned
- [ ] Schedule post-incident review
- [ ] Update procedures
- [ ] Communicate resolution

---

**Last Updated**: July 2025  
**Review Schedule**: Monthly  
**Owner**: Query Intelligence Team  
**Next Review**: August 2025