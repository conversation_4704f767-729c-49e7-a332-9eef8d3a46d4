# Query Intelligence Operations Runbook

## Overview

This runbook provides comprehensive operational procedures for the Query Intelligence service in production. It covers monitoring, incident response, maintenance, and troubleshooting procedures.

## Service Overview

- **Service Name**: query-intelligence
- **Runtime**: Cloud Run Gen2
- **Language**: Python 3.11+
- **Framework**: FastAPI
- **Database**: <PERSON><PERSON> (cache), <PERSON><PERSON><PERSON> (vectors)
- **External Dependencies**: analysis-engine, pattern-mining, Google GenAI

## Production Environment

### Service Configuration

```yaml
# Cloud Run Configuration
Service: query-intelligence
Region: us-central1
Platform: Cloud Run Gen2
Min Instances: 5
Max Instances: 200
Memory: 16Gi
CPU: 4
Concurrency: 20
CPU Boost: Enabled
```

### Key Environment Variables

```bash
ENVIRONMENT=production
GCP_PROJECT_ID=vibe-match-463114
GCP_REGION=us-central1
# Google GenAI SDK (migrated from deprecated Vertex AI SDK)
GOOGLE_API_KEY=<from-secret-manager>
USE_SECRET_MANAGER=true
MIN_INSTANCES=5
MAX_INSTANCES=200
SEMANTIC_CACHE_ENABLED=true
ENABLE_WEBSOCKET_AUTH=true
ENABLE_SECURITY_CONTROLS=true
```

## Monitoring and Alerting

### Health Endpoints

#### Primary Health Checks

| Endpoint | Purpose | Expected Response Time |
|----------|---------|----------------------|
| `/health` | Liveness probe | <50ms |
| `/ready` | Readiness probe | <100ms |
| `/metrics` | Prometheus metrics | <200ms |

#### Health Check Commands

```bash
# Basic health check
curl -f https://query-intelligence.ccl.dev/health

# Detailed readiness check
curl -f https://query-intelligence.ccl.dev/ready

# Circuit breaker status
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/circuit-breakers
```

### Key Performance Indicators (KPIs)

#### Service Level Objectives (SLOs)

| Metric | Target | Critical Threshold | Alert Threshold |
|--------|--------|-------------------|------------------|
| Response Time (p95) | <100ms | >500ms | >200ms |
| Availability | 99.95% | <99.0% | <99.5% |
| Error Rate | <0.1% | >1.0% | >0.5% |
| Cache Hit Rate | >75% | <50% | <65% |
| Cold Start Time | <2s | >10s | >5s |

### Prometheus Metrics

#### Core Metrics to Monitor

```promql
# Query volume
query_intelligence_queries_total

# Response time
histogram_quantile(0.95, rate(query_intelligence_query_duration_seconds_bucket[5m]))

# Error rate
rate(query_intelligence_queries_total{status="error"}[5m]) / rate(query_intelligence_queries_total[5m])

# Cache hit rate
query_intelligence_cache_hit_rate

# Active connections
query_intelligence_active_connections

# Circuit breaker state
query_intelligence_circuit_breaker_state
```

### Alerting Rules

#### Critical Alerts

```yaml
groups:
  - name: query-intelligence-critical
    rules:
      - alert: ServiceDown
        expr: up{job="query-intelligence"} == 0
        for: 30s
        labels:
          severity: critical
        annotations:
          summary: "Query Intelligence service is down"
          
      - alert: HighErrorRate
        expr: rate(query_intelligence_queries_total{status="error"}[5m]) > 0.01
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate: {{ $value | humanizePercentage }}"
          
      - alert: HighLatency
        expr: histogram_quantile(0.95, rate(query_intelligence_query_duration_seconds_bucket[5m])) > 0.5
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High response latency: {{ $value }}s"
```

#### Warning Alerts

```yaml
  - name: query-intelligence-warning
    rules:
      - alert: ModerateLatchency
        expr: histogram_quantile(0.95, rate(query_intelligence_query_duration_seconds_bucket[5m])) > 0.2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Elevated response latency: {{ $value }}s"
          
      - alert: LowCacheHitRate
        expr: query_intelligence_cache_hit_rate < 0.65
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Low cache hit rate: {{ $value | humanizePercentage }}"
          
      - alert: CircuitBreakerOpen
        expr: query_intelligence_circuit_breaker_state == 1
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Circuit breaker {{ $labels.breaker }} is OPEN"
```

## Incident Response

### Incident Classification

#### Severity Levels

| Severity | Definition | Response Time | Examples |
|----------|------------|---------------|----------|
| **P0 - Critical** | Service completely down | 15 minutes | Total service outage, data loss |
| **P1 - High** | Major functionality impacted | 1 hour | >50% error rate, >5s response time |
| **P2 - Medium** | Degraded performance | 4 hours | Elevated latency, circuit breakers open |
| **P3 - Low** | Minor issues | 24 hours | Low cache hit rate, warnings |

### Incident Response Procedures

#### P0 - Critical Incidents

1. **Immediate Response (0-15 minutes)**
   ```bash
   # Check service status
   gcloud run services describe query-intelligence --region=us-central1
   
   # Check recent deployments
   gcloud run revisions list --service=query-intelligence --region=us-central1
   
   # Check logs for errors
   gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=query-intelligence" --limit=100
   ```

2. **Escalation (15-30 minutes)**
   - Page on-call engineer
   - Create incident channel
   - Start incident timeline
   - Notify stakeholders

3. **Mitigation (30-60 minutes)**
   ```bash
   # Rollback to previous revision if needed
   gcloud run services update-traffic query-intelligence \
     --to-revisions=REVISION-001=100 --region=us-central1
   
   # Scale up instances if capacity issue
   gcloud run services update query-intelligence \
     --min-instances=10 --max-instances=500 --region=us-central1
   ```

#### P1 - High Priority Incidents

1. **Investigation (0-60 minutes)**
   ```bash
   # Check external dependencies
   curl -f http://analysis-engine:8001/health
   curl -f http://pattern-mining:8003/health
   
   # Check Redis connectivity
   kubectl exec -it redis-pod -- redis-cli ping
   
   # Check circuit breaker status
   curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     https://query-intelligence.ccl.dev/api/v1/admin/health
   ```

2. **Diagnosis Commands**
   ```bash
   # Check memory and CPU usage
   gcloud monitoring metrics list --filter="resource.type=cloud_run_revision"
   
   # Check quota limits
   gcloud quotas list --service=run.googleapis.com
   
   # Review error patterns
   gcloud logs read --format="json" \
     "resource.type=cloud_run_revision AND jsonPayload.level=ERROR" \
     --limit=50
   ```

### Common Incident Scenarios

#### Scenario 1: High Error Rate

**Symptoms:**
- Error rate >1%
- 500/503 status codes in logs
- Circuit breakers opening

**Investigation:**
```bash
# Check error distribution
gcloud logs read \
  "resource.type=cloud_run_revision AND httpRequest.status>=400" \
  --format="json" --limit=100

# Check external service health
kubectl get pods -l app=analysis-engine
kubectl get pods -l app=pattern-mining

# Check Redis connectivity
redis-cli -h $REDIS_HOST -p $REDIS_PORT ping
```

**Resolution:**
1. Identify failing component
2. Reset circuit breakers if needed
3. Scale external services if capacity issue
4. Clear cache if corruption suspected

#### Scenario 2: High Latency

**Symptoms:**
- P95 latency >200ms
- Timeout errors
- Slow database queries

**Investigation:**
```bash
# Check query patterns
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/api/v1/admin/queries/stats

# Check cache performance
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/api/v1/admin/cache/stats

# Check external service latency
curl -w "@curl-format.txt" -o /dev/null http://analysis-engine:8001/health
```

**Resolution:**
1. Enable aggressive caching
2. Clear cache if fragmented
3. Scale up instances
4. Review query optimization

#### Scenario 3: Circuit Breaker Issues

**Symptoms:**
- Circuit breakers in OPEN state
- External service timeouts
- Fallback responses activated

**Investigation:**
```bash
# Check circuit breaker status
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/circuit-breakers

# Check external service logs
kubectl logs -l app=analysis-engine --tail=100
kubectl logs -l app=pattern-mining --tail=100
```

**Resolution:**
```bash
# Reset specific circuit breaker
curl -X POST -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"breaker_name": "analysis_engine"}' \
  https://query-intelligence.ccl.dev/api/v1/admin/circuit-breakers/reset

# Reset all circuit breakers
curl -X POST -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/api/v1/admin/circuit-breakers/reset
```

## Maintenance Procedures

### Regular Maintenance Tasks

#### Daily Tasks

1. **Health Check Dashboard Review**
   - Check SLO compliance
   - Review error rate trends
   - Verify cache performance

2. **Log Analysis**
   ```bash
   # Check for anomalies
   gcloud logs read \
     "resource.type=cloud_run_revision AND severity>=WARNING" \
     --freshness=1d --limit=50
   
   # Check quota usage
   gcloud logging metrics list --filter="name:query_intelligence"
   ```

#### Weekly Tasks

1. **Performance Review**
   ```bash
   # Generate performance report
   curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     https://query-intelligence.ccl.dev/api/v1/admin/metrics > weekly_metrics.json
   
   # Cache optimization check
   curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     https://query-intelligence.ccl.dev/api/v1/admin/cache/stats > cache_stats.json
   ```

2. **Capacity Planning**
   - Review instance scaling patterns
   - Analyze query volume trends
   - Check quota limits and projections

#### Monthly Tasks

1. **Security Review**
   ```bash
   # Check service account permissions
   gcloud projects get-iam-policy $PROJECT_ID \
     --flatten="bindings[].members" \
     --filter="bindings.members:query-intelligence-sa@*"
   
   # Review secret rotation schedule
   gcloud secrets versions list query-intelligence-secrets
   ```

2. **Dependency Updates**
   - Review Python package security updates
   - Check for CVEs in base images
   - Plan upgrade schedule

### Cache Management

#### Cache Monitoring

```bash
# Check cache statistics
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/api/v1/admin/cache/stats

# Monitor Redis memory usage
redis-cli -h $REDIS_HOST info memory

# Check cache hit rates by query type
redis-cli -h $REDIS_HOST --scan --pattern "cache:query:*" | head -20
```

#### Cache Maintenance

```bash
# Clear stale cache entries (if needed)
curl -X POST -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"cache_type": "redis"}' \
  https://query-intelligence.ccl.dev/api/v1/admin/cache/clear

# Optimize Redis memory
redis-cli -h $REDIS_HOST MEMORY USAGE cache:*

# Check for memory fragmentation
redis-cli -h $REDIS_HOST info memory | grep frag
```

### Database Operations

#### Redis Maintenance

```bash
# Check Redis cluster health
redis-cli -h $REDIS_HOST cluster nodes

# Monitor replication lag
redis-cli -h $REDIS_HOST info replication

# Backup Redis data (if needed)
redis-cli -h $REDIS_HOST --rdb /backup/dump.rdb

# Check for slow queries
redis-cli -h $REDIS_HOST slowlog get 10
```

#### Pinecone Maintenance

```bash
# Check index statistics
curl -X GET "https://api.pinecone.io/v1/indexes/$INDEX_NAME/stats" \
  -H "Api-Key: $PINECONE_API_KEY"

# Monitor quota usage
curl -X GET "https://api.pinecone.io/v1/quota" \
  -H "Api-Key: $PINECONE_API_KEY"

# Check index health
curl -X GET "https://api.pinecone.io/v1/indexes/$INDEX_NAME" \
  -H "Api-Key: $PINECONE_API_KEY"
```

## Deployment Procedures

### Pre-deployment Checklist

1. **Code Quality Verification**
   ```bash
   # Run tests
   poetry run pytest tests/ --cov=query_intelligence --cov-fail-under=90
   
   # Security scan
   poetry run bandit -r src/ -ll
   poetry run safety check
   
   # Lint and format
   poetry run ruff check src/
   poetry run black --check src/
   ```

2. **Configuration Validation**
   ```bash
   # Validate environment variables
   poetry run python -c "from query_intelligence.config.settings import get_settings; get_settings()"
   
   # Check secrets availability
   gcloud secrets versions access latest --secret="query-intelligence-secrets"
   ```

### Deployment Steps

1. **Build and Push Image**
   ```bash
   # Build image
   gcloud builds submit --config cloudbuild.yaml --region=us-central1
   
   # Verify image
   gcloud container images list --repository=gcr.io/$PROJECT_ID
   ```

2. **Deploy to Staging**
   ```bash
   # Deploy to staging
   gcloud run deploy query-intelligence-staging \
     --image gcr.io/$PROJECT_ID/query-intelligence:$VERSION \
     --region=us-central1 \
     --set-env-vars="ENVIRONMENT=staging"
   
   # Run smoke tests
   curl -f https://query-intelligence-staging.ccl.dev/health
   ```

3. **Production Deployment**
   ```bash
   # Deploy with gradual traffic migration
   gcloud run deploy query-intelligence \
     --image gcr.io/$PROJECT_ID/query-intelligence:$VERSION \
     --region=us-central1 \
     --no-traffic
   
   # Gradually migrate traffic
   gcloud run services update-traffic query-intelligence \
     --to-revisions=$NEW_REVISION=10 --region=us-central1
   
   # Monitor for 5 minutes, then increase to 50%
   gcloud run services update-traffic query-intelligence \
     --to-revisions=$NEW_REVISION=50,$OLD_REVISION=50 --region=us-central1
   
   # Complete migration if healthy
   gcloud run services update-traffic query-intelligence \
     --to-revisions=$NEW_REVISION=100 --region=us-central1
   ```

### Post-deployment Verification

```bash
# Health checks
curl -f https://query-intelligence.ccl.dev/health
curl -f https://query-intelligence.ccl.dev/ready

# Functional test
curl -X POST https://query-intelligence.ccl.dev/api/v1/query \
  -H "Authorization: Bearer $TEST_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"query": "test query", "repository_id": "test-repo"}'

# Monitor metrics for 15 minutes
watch -n 30 'curl -s https://query-intelligence.ccl.dev/metrics | grep query_intelligence_queries_total'
```

### Rollback Procedures

```bash
# Immediate rollback if critical issues
gcloud run services update-traffic query-intelligence \
  --to-revisions=$PREVIOUS_REVISION=100 --region=us-central1

# Monitor rollback success
curl -f https://query-intelligence.ccl.dev/health

# Check error rates return to normal
gcloud logs read \
  "resource.type=cloud_run_revision AND severity>=ERROR" \
  --freshness=5m --limit=10
```

## Security Operations

### Access Management

#### Service Account Review

```bash
# Check service account permissions
gcloud iam service-accounts get-iam-policy \
  query-intelligence-sa@$PROJECT_ID.iam.gserviceaccount.com

# Review key usage
gcloud iam service-accounts keys list \
  --iam-account=query-intelligence-sa@$PROJECT_ID.iam.gserviceaccount.com
```

#### Secret Management

```bash
# Rotate secrets (monthly)
gcloud secrets versions add query-intelligence-secrets \
  --data-file=new-secrets.json

# Update secret references
gcloud run services update query-intelligence \
  --update-secrets=/var/secrets/service-account.json=query-intelligence-secrets:latest \
  --region=us-central1

# Verify secret access
gcloud secrets versions access latest --secret="query-intelligence-secrets"
```

### Audit and Compliance

#### Audit Log Review

```bash
# Review authentication logs
gcloud logging read \
  'protoPayload.serviceName="run.googleapis.com" AND protoPayload.methodName="google.cloud.run.v1.Services.UpdateService"' \
  --freshness=7d --limit=50

# Check access patterns
gcloud logging read \
  'resource.type="cloud_run_revision" AND httpRequest.requestMethod!=""' \
  --freshness=1d --format="json" | jq '.[] | {timestamp, httpRequest}'
```

#### Security Monitoring

```bash
# Check for suspicious activity
gcloud logging read \
  'resource.type="cloud_run_revision" AND (httpRequest.status=401 OR httpRequest.status=403)' \
  --freshness=1d --limit=100

# Monitor rate limiting
gcloud logging read \
  'resource.type="cloud_run_revision" AND httpRequest.status=429' \
  --freshness=1d --limit=50
```

## Disaster Recovery

### Backup Procedures

#### Configuration Backup

```bash
# Export service configuration
gcloud run services describe query-intelligence \
  --region=us-central1 --format="export" > service-config-backup.yaml

# Backup environment variables
gcloud run services describe query-intelligence \
  --region=us-central1 --format="json" | \
  jq '.spec.template.spec.template.spec.containers[0].env' > env-backup.json
```

#### Data Backup

```bash
# Redis backup (if persistent data)
redis-cli -h $REDIS_HOST --rdb /backup/redis-$(date +%Y%m%d).rdb

# Backup secrets
gcloud secrets versions list query-intelligence-secrets \
  --format="json" > secrets-backup.json
```

### Recovery Procedures

#### Service Recovery

```bash
# Restore from backup configuration
gcloud run services replace service-config-backup.yaml --region=us-central1

# Verify service health
curl -f https://query-intelligence.ccl.dev/health

# Warm up cache
curl -X POST -H "Authorization: Bearer $ADMIN_TOKEN" \
  https://query-intelligence.ccl.dev/api/v1/admin/cache/clear
```

#### Regional Failover

```bash
# Deploy to backup region
gcloud run deploy query-intelligence-backup \
  --image gcr.io/$PROJECT_ID/query-intelligence:latest \
  --region=us-east1 \
  --set-env-vars="ENVIRONMENT=production"

# Update DNS to point to backup region
# (This would be handled by your DNS management system)

# Monitor backup region health
curl -f https://query-intelligence-backup.ccl.dev/health
```

## Contact Information

### On-Call Rotation

- **Primary**: <EMAIL>
- **Secondary**: <EMAIL>
- **Manager**: <EMAIL>

### Escalation Path

1. **L1**: On-call engineer (15 minutes)
2. **L2**: Senior engineer (30 minutes)
3. **L3**: Engineering manager (1 hour)
4. **L4**: VP Engineering (2 hours)

### External Contacts

- **Google Cloud Support**: Case ID in #gcp-support
- **Pinecone Support**: <EMAIL>
- **Redis Support**: Enterprise support portal

---

**Last Updated**: July 2025  
**Review Schedule**: Monthly  
**Owner**: Query Intelligence Team