# REST API Endpoints

## Query Processing API

### Process Query

Process a natural language query about code and return a complete response.

```http
POST /api/v1/query
```

**Headers:**
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "query": "How does the authentication middleware work?",
  "repository_id": "repo-123",
  "session_id": "session-456",
  "user_id": "user-789",
  "filters": {
    "file_pattern": "*.py",
    "exclude_tests": true,
    "exclude_patterns": ["__pycache__", "*.pyc"],
    "include_patterns": ["src/**", "lib/**"],
    "language": "python",
    "max_file_size": 1000000
  },
  "options": {
    "stream": false,
    "max_results": 10,
    "include_snippets": true,
    "confidence_threshold": 0.7
  }
}
```

**Request Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `query` | string | Yes | Natural language query (1-1000 chars) |
| `repository_id` | string | Yes | Repository identifier |
| `session_id` | string | No | Session identifier for context |
| `user_id` | string | No | User identifier (auto-filled from JWT) |
| `filters` | object | No | Search filters |
| `options` | object | No | Query options |

**Filter Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| `file_pattern` | string | File pattern to include (e.g., "*.py", "*.js") |
| `exclude_tests` | boolean | Exclude test files |
| `exclude_patterns` | array | Array of patterns to exclude |
| `include_patterns` | array | Array of patterns to include |
| `language` | string | Programming language filter |
| `max_file_size` | integer | Maximum file size in bytes |

**Option Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| `stream` | boolean | Enable streaming response |
| `max_results` | integer | Maximum number of results (1-50) |
| `include_snippets` | boolean | Include code snippets in response |
| `confidence_threshold` | float | Minimum confidence threshold (0.0-1.0) |

**Response:**
```json
{
  "answer": "The authentication middleware uses JWT tokens with HS256 algorithm. It validates tokens on each request and extracts user information for authorization.",
  "intent": "EXPLAIN",
  "confidence": 0.92,
  "references": [
    {
      "file_path": "src/middleware/auth.py",
      "start_line": 45,
      "end_line": 67,
      "snippet": "class JWTAuth:\n    def __init__(self):\n        self.secret = get_secret()\n        self.algorithm = 'HS256'",
      "relevance_score": 0.95,
      "language": "python",
      "file_size": 2048,
      "last_modified": "2025-07-10T08:30:00Z"
    }
  ],
  "execution_time_ms": 87.3,
  "follow_up_questions": [
    "What JWT algorithm is used?",
    "How are tokens validated?",
    "What happens when a token expires?"
  ],
  "metadata": {
    "model_used": "gemini-2.5-flash",
    "chunks_retrieved": 15,
    "chunks_used": 5,
    "cache_hit": false,
    "search_time_ms": 23.1,
    "generation_time_ms": 64.2,
    "token_usage": {
      "prompt_tokens": 1200,
      "completion_tokens": 350,
      "total_tokens": 1550
    }
  }
}
```

**Status Codes:**
- `200 OK` - Query processed successfully
- `400 Bad Request` - Invalid request parameters
- `401 Unauthorized` - Missing or invalid authentication
- `422 Unprocessable Entity` - Validation errors
- `429 Too Many Requests` - Rate limit exceeded
- `500 Internal Server Error` - Processing error

**Example Request:**
```bash
curl -X POST https://query-intelligence.ccl.dev/api/v1/query \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "query": "How does authentication work?",
    "repository_id": "repo-123",
    "filters": {
      "file_pattern": "*.py",
      "exclude_tests": true
    }
  }'
```

### Query Optimization

Get optimization hints for improving query results.

```http
POST /api/v1/query/optimize
```

**Headers:**
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "query": "find auth",
  "repository_id": "repo-123",
  "previous_confidence": 0.45,
  "previous_results_count": 2
}
```

**Request Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `query` | string | Yes | Query to optimize (1-1000 chars) |
| `repository_id` | string | Yes | Repository identifier |
| `previous_confidence` | float | No | Previous query confidence (0.0-1.0) |
| `previous_results_count` | integer | No | Previous results count |

**Response:**
```json
{
  "optimizations": [
    {
      "type": "SPECIFICITY",
      "suggestion": "Be more specific about what aspect of authentication you're interested in",
      "example": "How does JWT authentication work?",
      "impact": "HIGH"
    },
    {
      "type": "CONTEXT",
      "suggestion": "Add context about the component or layer",
      "example": "How does authentication work in the middleware layer?",
      "impact": "MEDIUM"
    },
    {
      "type": "LANGUAGE",
      "suggestion": "Specify the programming language",
      "example": "How does authentication work in Python?",
      "impact": "MEDIUM"
    }
  ],
  "quality_score": 0.35,
  "optimized_query": "How does JWT authentication work in the middleware layer?"
}
```

**Optimization Types:**

| Type | Description |
|------|-------------|
| `SPECIFICITY` | Query is too vague, needs more specific terms |
| `CONTEXT` | Missing context about scope or component |
| `LANGUAGE` | Should specify programming language |
| `INTENT` | Unclear intent, needs clarification |
| `SCOPE` | Scope too broad or too narrow |
| `TECHNICAL` | Missing technical terminology |

**Impact Levels:**

| Level | Description |
|-------|-------------|
| `HIGH` | Significant improvement expected |
| `MEDIUM` | Moderate improvement expected |
| `LOW` | Minor improvement expected |

**Example Request:**
```bash
curl -X POST https://query-intelligence.ccl.dev/api/v1/query/optimize \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "query": "find auth",
    "repository_id": "repo-123",
    "previous_confidence": 0.45
  }'
```

## Admin API

### System Metrics

Get system-wide performance metrics.

```http
GET /api/v1/admin/metrics
```

**Headers:**
```http
Authorization: Bearer <admin_jwt_token>
```

**Response:**
```json
{
  "total_queries": 15420,
  "cache_hit_rate": 0.78,
  "average_response_time_ms": 82.5,
  "active_users": 145,
  "queries_per_minute": 23.4,
  "error_rate": 0.003
}
```

**Metrics Description:**

| Metric | Description |
|--------|-------------|
| `total_queries` | Total number of queries processed |
| `cache_hit_rate` | Percentage of queries served from cache |
| `average_response_time_ms` | Average response time in milliseconds |
| `active_users` | Number of active users in the last hour |
| `queries_per_minute` | Average queries per minute (last 5 minutes) |
| `error_rate` | Percentage of queries that resulted in errors |

### Service Health

Get health status of all services.

```http
GET /api/v1/admin/health
```

**Headers:**
```http
Authorization: Bearer <admin_jwt_token>
```

**Response:**
```json
{
  "redis": "healthy",
  "analysis_engine": "healthy",
  "pattern_mining": "degraded",
  "llm_service": "healthy",
  "circuit_breakers": {
    "analysis_engine": "CLOSED",
    "pattern_mining": "HALF_OPEN",
    "llm_service": "CLOSED"
  }
}
```

**Health Status Values:**

| Status | Description |
|--------|-------------|
| `healthy` | Service is operating normally |
| `degraded` | Service is functional but performance is reduced |
| `unhealthy` | Service is not responding or failing |
| `unknown` | Service status cannot be determined |

### Query Statistics

Get detailed query statistics.

```http
GET /api/v1/admin/queries/stats
```

**Headers:**
```http
Authorization: Bearer <admin_jwt_token>
```

**Response:**
```json
{
  "top_queries": [
    {
      "query": "How does authentication work?",
      "count": 342
    },
    {
      "query": "Find database connection logic",
      "count": 287
    }
  ],
  "query_intents": {
    "EXPLAIN": 5420,
    "FIND": 3210,
    "DEBUG": 1890,
    "ANALYZE": 1240,
    "COMPARE": 890,
    "UNKNOWN": 120
  },
  "language_distribution": {
    "python": 6540,
    "javascript": 4320,
    "java": 2110,
    "go": 1890,
    "typescript": 1650
  },
  "average_confidence": 0.847
}
```

### Cache Statistics

Get cache performance statistics.

```http
GET /api/v1/admin/cache/stats
```

**Headers:**
```http
Authorization: Bearer <admin_jwt_token>
```

**Response:**
```json
{
  "memory_cache_size": 1240,
  "memory_hit_rate": 0.82,
  "redis_keys": 15420,
  "redis_memory_mb": 256.8,
  "hot_queries": [
    "How does authentication work?",
    "Find database connection logic",
    "Explain error handling patterns"
  ]
}
```

### Cache Management

Clear cache data.

```http
POST /api/v1/admin/cache/clear
```

**Headers:**
```http
Authorization: Bearer <admin_jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "cache_type": "all"
}
```

**Cache Types:**

| Type | Description |
|------|-------------|
| `all` | Clear all caches |
| `memory` | Clear in-memory cache only |
| `redis` | Clear Redis cache only |

**Response:**
```json
{
  "status": "success",
  "cleared": "all",
  "timestamp": "2025-07-10T10:30:00Z"
}
```

### Circuit Breaker Management

Reset circuit breakers.

```http
POST /api/v1/admin/circuit-breakers/reset
```

**Headers:**
```http
Authorization: Bearer <admin_jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "breaker_name": "analysis_engine"
}
```

**Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `breaker_name` | string | No | Specific breaker to reset (omit for all) |

**Response:**
```json
{
  "status": "success",
  "reset": "analysis_engine",
  "timestamp": "2025-07-10T10:30:00Z"
}
```

### Configuration

Get current service configuration.

```http
GET /api/v1/admin/config
```

**Headers:**
```http
Authorization: Bearer <admin_jwt_token>
```

**Response:**
```json
{
  "environment": "production",
  "service_name": "query-intelligence",
  "port": 8002,
  "cache_ttl_hours": 24,
  "rate_limit_requests": 100,
  "rate_limit_window": 60,
  "max_query_length": 1000,
  "query_timeout_seconds": 30,
  "llm_model": "gemini-2.5-flash",
  "embedding_model": "all-mpnet-base-v2",
  "enable_metrics": true,
  "enable_input_validation": true,
  "circuit_breaker_config": {
    "failure_threshold": 5,
    "recovery_timeout": 60,
    "success_threshold": 3
  }
}
```

## Prometheus Metrics

### Metrics Endpoint

```http
GET /metrics
```

**Response Format:** Prometheus exposition format

**Available Metrics:**

| Metric | Type | Description |
|--------|------|-------------|
| `query_intelligence_queries_total` | Counter | Total queries by intent and status |
| `query_intelligence_query_duration_seconds` | Histogram | Query processing duration |
| `query_intelligence_model_latency_seconds` | Histogram | Model inference time by tier |
| `query_intelligence_cache_hit_rate` | Gauge | Cache hit rate percentage |
| `query_intelligence_token_usage_total` | Counter | Token consumption by model |
| `query_intelligence_active_connections` | Gauge | Active WebSocket connections |
| `query_intelligence_circuit_breaker_state` | Gauge | Circuit breaker state (0=closed, 1=open, 2=half-open) |

**Example Metrics:**
```
# HELP query_intelligence_queries_total Total number of queries processed
# TYPE query_intelligence_queries_total counter
query_intelligence_queries_total{intent="EXPLAIN",status="success"} 5420
query_intelligence_queries_total{intent="FIND",status="success"} 3210
query_intelligence_queries_total{intent="DEBUG",status="error"} 12

# HELP query_intelligence_query_duration_seconds Query processing duration
# TYPE query_intelligence_query_duration_seconds histogram
query_intelligence_query_duration_seconds_bucket{le="0.05"} 1240
query_intelligence_query_duration_seconds_bucket{le="0.1"} 8920
query_intelligence_query_duration_seconds_bucket{le="0.5"} 15420
query_intelligence_query_duration_seconds_bucket{le="+Inf"} 15430
query_intelligence_query_duration_seconds_sum 1287.45
query_intelligence_query_duration_seconds_count 15430
```

## Request/Response Examples

### Successful Query Processing

**Request:**
```bash
curl -X POST https://query-intelligence.ccl.dev/api/v1/query \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "query": "How is user authentication handled in the login flow?",
    "repository_id": "repo-123",
    "filters": {
      "file_pattern": "*.py",
      "exclude_tests": true
    },
    "options": {
      "max_results": 5,
      "confidence_threshold": 0.8
    }
  }'
```

**Response:**
```json
{
  "answer": "User authentication in the login flow is handled through a multi-step process:\n\n1. **Username/Password Validation**: The LoginService validates credentials against the database\n2. **JWT Generation**: Upon successful validation, a JWT token is generated with user claims\n3. **Token Storage**: The token is stored securely and returned to the client\n4. **Session Management**: The AuthMiddleware validates the token on subsequent requests\n\nThe implementation uses bcrypt for password hashing and includes rate limiting to prevent brute force attacks.",
  "intent": "EXPLAIN",
  "confidence": 0.94,
  "references": [
    {
      "file_path": "src/services/auth_service.py",
      "start_line": 45,
      "end_line": 78,
      "snippet": "class LoginService:\n    def authenticate_user(self, username: str, password: str) -> Optional[User]:\n        user = self.user_repository.get_by_username(username)\n        if user and bcrypt.checkpw(password.encode(), user.password_hash):\n            return user\n        return None",
      "relevance_score": 0.96,
      "language": "python"
    },
    {
      "file_path": "src/middleware/auth_middleware.py",
      "start_line": 23,
      "end_line": 41,
      "snippet": "def validate_jwt_token(self, token: str) -> Optional[dict]:\n    try:\n        payload = jwt.decode(token, self.secret, algorithms=['HS256'])\n        return payload\n    except jwt.InvalidTokenError:\n        return None",
      "relevance_score": 0.92,
      "language": "python"
    }
  ],
  "execution_time_ms": 94.7,
  "follow_up_questions": [
    "How are passwords securely stored?",
    "What happens when a JWT token expires?",
    "How does the rate limiting work for login attempts?"
  ],
  "metadata": {
    "model_used": "gemini-2.5-flash",
    "chunks_retrieved": 12,
    "chunks_used": 4,
    "cache_hit": false,
    "search_time_ms": 28.4,
    "generation_time_ms": 66.3,
    "token_usage": {
      "prompt_tokens": 1456,
      "completion_tokens": 298,
      "total_tokens": 1754
    }
  }
}
```

### Error Response Examples

**Rate Limit Exceeded:**
```json
{
  "error": "RATE_LIMIT_EXCEEDED",
  "message": "Too many requests. Please try again later.",
  "details": {
    "limit": 50,
    "window": 60,
    "retry_after": 42
  },
  "timestamp": "2025-07-10T10:30:00Z",
  "request_id": "req-123456"
}
```

**Validation Error:**
```json
{
  "error": "VALIDATION_ERROR",
  "message": "Request validation failed",
  "details": {
    "query": "Query is required and cannot be empty",
    "repository_id": "Repository ID must be a valid UUID"
  },
  "timestamp": "2025-07-10T10:30:00Z",
  "request_id": "req-123456"
}
```

**Service Unavailable:**
```json
{
  "error": "SERVICE_UNAVAILABLE",
  "message": "Analysis engine is temporarily unavailable",
  "details": {
    "service": "analysis_engine",
    "circuit_breaker": "OPEN",
    "estimated_recovery": "2025-07-10T10:32:00Z"
  },
  "timestamp": "2025-07-10T10:30:00Z",
  "request_id": "req-123456"
}
```

---

**Last Updated**: July 2025  
**API Version**: v1.0.0  
**Contact**: <EMAIL>