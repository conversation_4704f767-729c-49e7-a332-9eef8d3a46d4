# 🔌 Query Intelligence API Documentation

## ✅ **Live Service Status**
**Service**: OPERATIONAL since July 2025  
**Base URL**: `https://query-intelligence-l3nxty7oka-uc.a.run.app`  
**Health Check**: `{"status":"healthy","timestamp":"2025-07-14T12:00:00Z"}`

## Table of Contents
- [🚀 Quick API Test](#quick-api-test)
- [✅ Working Endpoints](#working-endpoints)
- [Authentication](#authentication)
- [REST API](#rest-api)
- [WebSocket API](#websocket-api)
- [Error Handling](#error-handling)
- [Rate Limiting](#rate-limiting)
- [Request/Response Examples](#requestresponse-examples)

## 🚀 Quick API Test

### **Test Live Service**
```bash
# Verify service is running
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/health
# ✅ Response: {"status":"healthy","timestamp":"2025-07-14T12:00:00Z"}

# Test query endpoint (requires auth)
curl -X POST https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "query": "How does authentication work in this codebase?",
    "repository_id": "example-repo"
  }'
# ✅ Response: {"response":"...","confidence":0.95,"query_id":"..."}
```

## ✅ Working Endpoints

### **Operational API Endpoints** (Verified)
- `GET /health` - **✅ WORKING** - Service health check
- `GET /ready` - **✅ WORKING** - Readiness check with dependencies
- `POST /api/v1/query` - **✅ WORKING** - Natural language query processing
- `GET /api/v1/languages` - **✅ WORKING** - Supported programming languages
- `GET /api/v1/version` - **✅ WORKING** - Service version information

### Base URLs
- **Production**: `https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1` ✅ **OPERATIONAL**
- **Health Endpoints**: Direct service URL (no /api/v1 prefix)

### Content Types
- Request: `application/json`
- Response: `application/json`
- WebSocket: `text` (JSON-encoded messages)

## Authentication

The API uses JWT-based authentication with multiple options:

### 1. JWT Bearer Token (Recommended)
```http
Authorization: Bearer <jwt-token>
```

JWT tokens include comprehensive validation:
- Firebase Auth integration
- Token expiration and refresh
- Role-based access control
- Session tracking

Required JWT claims:
```json
{
  "sub": "user-id",
  "exp": **********,
  "iat": **********,
  "aud": "query-intelligence",
  "iss": "https://securetoken.google.com/your-project",
  "email": "<EMAIL>",
  "email_verified": true
}
```

### 2. Service Account Authentication
For service-to-service communication:
```http
Authorization: Bearer <service-account-token>
```

## REST API

### Health Endpoints

#### GET /health
Basic health check endpoint.

**Response**
```json
{
  "status": "healthy",
  "timestamp": "2025-07-14T12:00:00Z"
}
```

#### GET /ready
Readiness probe that checks all dependencies.

**Response**
```json
{
  "status": "ready",
  "checks": {
    "redis": "ok",
    "analysis_engine": "ok",
    "pattern_mining": "ok",
    "genai_service": "ok"
  },
  "timestamp": "2025-07-14T12:00:00Z"
}
```

### Core Query Endpoints

#### POST /api/v1/query
Process a natural language query about code.

**Request Body**
```json
{
  "query": "How does authentication work in this codebase?",
  "repository_id": "example-repo",
  "include_context": true,
  "include_references": true,
  "max_results": 10,
  "language_filter": ["python", "typescript"],
  "options": {
    "use_semantic_cache": true,
    "include_confidence_score": true,
    "stream_response": false
  }
}
```

**Parameters**
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `query` | string | Yes | Natural language query (max 10,000 chars) |
| `repository_id` | string | Yes | Repository identifier |
| `include_context` | boolean | No | Include code context (default: true) |
| `include_references` | boolean | No | Include code references (default: true) |
| `max_results` | integer | No | Maximum results to return (default: 10) |
| `language_filter` | string[] | No | Filter by programming languages |
| `options.use_semantic_cache` | boolean | No | Use semantic caching (default: true) |
| `options.include_confidence_score` | boolean | No | Include confidence score (default: true) |
| `options.stream_response` | boolean | No | Stream response via WebSocket (default: false) |

**Response** (200 OK)
```json
{
  "query_id": "550e8400-e29b-41d4-a716-446655440000",
  "response": "Authentication in this codebase is handled through JWT tokens...",
  "confidence": 0.95,
  "intent": "authentication_inquiry",
  "language": "python",
  "references": [
    {
      "file_path": "src/auth/middleware.py",
      "line_number": 42,
      "code_snippet": "def authenticate_user(token: str) -> User:",
      "relevance_score": 0.92
    }
  ],
  "follow_up_questions": [
    "How are JWT tokens generated?",
    "What happens when authentication fails?"
  ],
  "processing_time_ms": 85,
  "cached": true,
  "timestamp": "2025-07-14T12:00:00Z"
}
```

#### GET /api/v1/queries/{query_id}
Retrieve a previous query result.

**Response**
```json
{
  "query_id": "550e8400-e29b-41d4-a716-446655440000",
  "original_query": "How does authentication work?",
  "response": "Authentication in this codebase...",
  "confidence": 0.95,
  "created_at": "2025-07-14T12:00:00Z",
  "status": "completed"
}
```

#### GET /api/v1/languages
List supported programming languages.

**Response**
```json
{
  "languages": [
    {
      "name": "python",
      "display_name": "Python",
      "file_extensions": [".py"],
      "supported_features": ["syntax_analysis", "semantic_search"]
    },
    {
      "name": "typescript",
      "display_name": "TypeScript",
      "file_extensions": [".ts", ".tsx"],
      "supported_features": ["syntax_analysis", "semantic_search", "type_analysis"]
    }
  ],
  "total_count": 15
}
```

#### GET /api/v1/version
Get service version information.

**Response**
```json
{
  "version": "2.0.0",
  "build_date": "2025-07-14",
  "commit_hash": "abc123def456",
  "environment": "production",
  "features": {
    "semantic_cache": true,
    "websocket_streaming": true,
    "multi_language_support": true
  }
}
```

## WebSocket API

### Connection
```javascript
const ws = new WebSocket('wss://query-intelligence-l3nxty7oka-uc.a.run.app/ws/query/{query_id}');
```

### Authentication
WebSocket connections require JWT authentication:
```javascript
const ws = new WebSocket('wss://query-intelligence-l3nxty7oka-uc.a.run.app/ws/query/{query_id}', {
  headers: {
    'Authorization': 'Bearer YOUR_JWT_TOKEN'
  }
});
```

### Message Format

**Query Processing Update**
```json
{
  "type": "processing",
  "query_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "analyzing",
  "progress": 45.5,
  "message": "Analyzing code patterns...",
  "timestamp": "2025-07-14T12:00:30Z"
}
```

**Partial Response**
```json
{
  "type": "partial_response",
  "query_id": "550e8400-e29b-41d4-a716-446655440000",
  "content": "Authentication in this codebase is handled...",
  "confidence": 0.85,
  "timestamp": "2025-07-14T12:00:45Z"
}
```

**Complete Response**
```json
{
  "type": "complete",
  "query_id": "550e8400-e29b-41d4-a716-446655440000",
  "response": "Complete response here...",
  "confidence": 0.95,
  "references": [...],
  "processing_time_ms": 1250,
  "timestamp": "2025-07-14T12:01:00Z"
}
```

**Error**
```json
{
  "type": "error",
  "query_id": "550e8400-e29b-41d4-a716-446655440000",
  "error": "Query processing failed",
  "error_code": "PROCESSING_ERROR",
  "timestamp": "2025-07-14T12:00:30Z"
}
```

### Client Example
```javascript
const queryId = "550e8400-e29b-41d4-a716-446655440000";
const ws = new WebSocket(`wss://query-intelligence-l3nxty7oka-uc.a.run.app/ws/query/${queryId}`);

ws.onopen = () => {
  console.log('Connected to query stream');
};

ws.onmessage = (event) => {
  const update = JSON.parse(event.data);
  
  switch (update.type) {
    case 'processing':
      updateProgress(update.progress);
      updateStatus(update.message);
      break;
    
    case 'partial_response':
      appendToResponse(update.content);
      break;
    
    case 'complete':
      showFinalResponse(update.response);
      showReferences(update.references);
      break;
    
    case 'error':
      handleError(update.error);
      break;
  }
};

ws.onerror = (error) => {
  console.error('WebSocket error:', error);
};

ws.onclose = () => {
  console.log('Disconnected from query stream');
};
```

## Error Handling

### Error Response Format
```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded. Please retry after 3600 seconds.",
    "details": {
      "limit": 1000,
      "remaining": 0,
      "reset_at": "2025-07-14T13:00:00Z"
    }
  },
  "request_id": "req_123456",
  "timestamp": "2025-07-14T12:00:00Z"
}
```

### Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `UNAUTHORIZED` | 401 | Missing or invalid authentication |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `VALIDATION_ERROR` | 400 | Invalid request parameters |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests |
| `QUERY_TOO_LONG` | 400 | Query exceeds maximum length |
| `PROCESSING_ERROR` | 500 | Query processing failed |
| `SERVICE_UNAVAILABLE` | 503 | Temporary service issue |
| `TIMEOUT` | 504 | Request timeout |

## Rate Limiting

Rate limits are enforced per user/token:

| Tier | Requests/Hour | Concurrent Queries | Max Query Length |
|------|---------------|-------------------|------------------|
| Free | 100 | 1 | 1,000 chars |
| Pro | 1,000 | 5 | 5,000 chars |
| Team | 10,000 | 20 | 10,000 chars |
| Enterprise | Unlimited | Unlimited | 50,000 chars |

### Rate Limit Headers
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 950
X-RateLimit-Reset: **********
```

## Request/Response Examples

### Example 1: Simple Query
```bash
curl -X POST https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Show me all authentication functions",
    "repository_id": "my-repo"
  }'
```

### Example 2: Advanced Query with Filters
```bash
curl -X POST https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "How do I handle database connections?",
    "repository_id": "my-repo",
    "language_filter": ["python"],
    "max_results": 5,
    "include_context": true,
    "options": {
      "use_semantic_cache": true,
      "include_confidence_score": true
    }
  }'
```

### Example 3: Streaming Query
```bash
curl -X POST https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Explain the entire authentication flow",
    "repository_id": "my-repo",
    "options": {
      "stream_response": true
    }
  }'
# Response includes websocket_url for streaming
```

---

For more detailed API examples and integration patterns, see the [Integration Guide](../guides/integration-guide.md).