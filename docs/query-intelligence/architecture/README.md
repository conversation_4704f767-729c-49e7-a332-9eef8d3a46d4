# 📐 Query Intelligence Architecture Guide

## Table of Contents
- [Overview](#overview)
- [System Architecture](#system-architecture)
- [Core Components](#core-components)
- [Data Flow](#data-flow)
- [Technology Stack](#technology-stack)
- [Design Patterns](#design-patterns)
- [Scalability & Performance](#scalability--performance)
- [Security Architecture](#security-architecture)
- [Integration Points](#integration-points)

## Overview

The Query Intelligence Service is designed as a high-performance, AI-powered microservice that provides natural language query processing for codebases. It follows modern cloud-native principles with comprehensive security, scalability, and observability.

### Design Principles
1. **AI-First Architecture**: Built around large language models with intelligent caching
2. **Microservice Design**: Loosely coupled, independently deployable
3. **Security by Design**: Zero-trust architecture with comprehensive authentication
4. **Performance Optimization**: Multi-level caching and intelligent query routing
5. **Fault Tolerance**: Circuit breakers, graceful degradation, comprehensive error handling
6. **Observability**: Structured logging, metrics, health checks, and monitoring
7. **Scalability**: Horizontal scaling with stateless design

## System Architecture

### High-Level Architecture

```
┌──────────────────────────────────────────────────────────────────┐
│                         External Clients                          │
│         (Web UI, SDK, CLI, Other Services)                      │
└───────────────────────┬──────────────────────────────────────────┘
                        │ HTTPS/WSS
┌───────────────────────▼──────────────────────────────────────────┐
│                    API Gateway Layer                              │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │   REST API  │  │  WebSocket   │  │  Health Endpoints      │ │
│  │  (FastAPI)  │  │   Server     │  │  (/health, /ready)     │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
└───────────────────────┬──────────────────────────────────────────┘
                        │
┌───────────────────────▼──────────────────────────────────────────┐
│                  Authentication & Authorization                   │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │ JWT Handler │  │Firebase Auth │  │  Rate Limiter          │ │
│  │             │  │ Integration  │  │  (Redis/Memory)        │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
└───────────────────────┬──────────────────────────────────────────┘
                        │
┌───────────────────────▼──────────────────────────────────────────┐
│                    Query Processing Layer                         │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │ Query       │  │Intent Engine │  │  Context Processor     │ │
│  │ Processor   │  │& Classifier  │  │  (Code Context)        │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │ Semantic    │  │Query Router  │  │  Response Generator    │ │
│  │ Cache       │  │& Optimizer   │  │  (LLM Integration)     │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
└───────────────────────┬──────────────────────────────────────────┘
                        │
┌───────────────────────▼──────────────────────────────────────────┐
│                    AI & Intelligence Layer                        │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │ Google      │  │Pattern Mining│  │  Response Streaming    │ │
│  │ GenAI SDK   │  │ Integration  │  │  (WebSocket)           │ │
│  │ (Gemini)    │  │              │  │                        │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │ Circuit     │  │Model Router  │  │  Confidence Scoring    │ │
│  │ Breakers    │  │(Flash/Pro)   │  │  & Validation          │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
└───────────────────────┬──────────────────────────────────────────┘
                        │
┌───────────────────────▼──────────────────────────────────────────┐
│                    Integration Layer                              │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │ Analysis    │  │Pattern Mining│  │  External Services     │ │
│  │ Engine      │  │ Service      │  │  (Firebase, Monitoring)│ │
│  │ Client      │  │ Client       │  │                        │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
└───────────────────────┬──────────────────────────────────────────┘
                        │
┌───────────────────────▼──────────────────────────────────────────┐
│                    Storage & Caching Layer                        │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │    Redis    │  │   Memory     │  │  Session Storage       │ │
│  │  (Semantic  │  │   Cache      │  │  (Temporary)           │ │
│  │   Cache)    │  │ (Query Data) │  │                        │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
└──────────────────────────────────────────────────────────────────┘
```

### Component Details

#### 1. API Gateway Layer (FastAPI)
- **REST API**: High-performance async HTTP server
- **WebSocket Server**: Real-time query streaming
- **Health Endpoints**: Kubernetes-compatible health checks
- **OpenAPI Integration**: Automatic API documentation

#### 2. Authentication Layer
- **JWT Handler**: Comprehensive JWT validation with Firebase integration
  - Token validation and refresh
  - Role-based access control
  - Session tracking and management
- **Rate Limiter**: Multi-tier rate limiting
  - Redis-based distributed rate limiting
  - In-memory fallback for high performance
  - Per-user configurable limits

#### 3. Query Processing Layer
- **Query Processor**: Natural language query analysis and normalization
- **Intent Engine**: Query classification and routing logic
- **Context Processor**: Code context extraction and enrichment
- **Semantic Cache**: Intelligent caching with similarity matching
- **Query Router**: Load balancing and optimization
- **Response Generator**: LLM integration and response formatting

#### 4. AI & Intelligence Layer
- **Google GenAI SDK**: Gemini 2.5 model integration
  - Flash models for quick responses
  - Pro models for complex queries
  - Streaming response support
- **Pattern Mining Integration**: ML-based pattern detection
- **Response Streaming**: Real-time WebSocket updates
- **Circuit Breakers**: Fault tolerance for external services
- **Model Router**: Intelligent model selection
- **Confidence Scoring**: Response quality validation

#### 5. Integration Layer
- **Analysis Engine Client**: AST parsing and code intelligence
- **Pattern Mining Client**: ML pattern detection service
- **External Services**: Firebase, monitoring, and third-party integrations

#### 6. Storage Layer
- **Redis**: Semantic caching and rate limiting
- **Memory Cache**: High-speed query data caching
- **Session Storage**: Temporary data for active queries

## Core Components

### 1. Query Processor (`src/query_intelligence/services/query_processor.py`)

The query processor handles natural language query analysis and preprocessing:

```python
class QueryProcessor:
    def __init__(self):
        self.intent_classifier = IntentClassifier()
        self.query_optimizer = QueryOptimizer()
        self.context_extractor = ContextExtractor()
    
    async def process_query(self, query: str, repository_id: str) -> ProcessedQuery:
        # Validate and normalize query
        normalized_query = self.normalize_query(query)
        
        # Extract intent and classify
        intent = await self.intent_classifier.classify(normalized_query)
        
        # Optimize query for better results
        optimized_query = self.query_optimizer.optimize(normalized_query, intent)
        
        # Extract relevant context
        context = await self.context_extractor.extract(repository_id, optimized_query)
        
        return ProcessedQuery(
            original=query,
            normalized=normalized_query,
            optimized=optimized_query,
            intent=intent,
            context=context
        )
```

**Key Features**:
- Natural language normalization
- Intent classification (authentication, database, API, etc.)
- Query optimization for better LLM responses
- Context extraction from codebase
- Input validation and sanitization

### 2. AI Response Generator (`src/query_intelligence/services/response_generator.py`)

Handles LLM integration and response generation:

```python
class ResponseGenerator:
    def __init__(self):
        self.genai_client = GoogleGenAIClient()
        self.model_router = ModelRouter()
        self.streaming_handler = StreamingHandler()
    
    async def generate_response(
        self, 
        processed_query: ProcessedQuery,
        stream: bool = False
    ) -> QueryResponse:
        # Select appropriate model based on complexity
        model = self.model_router.select_model(processed_query)
        
        # Generate response
        if stream:
            return await self.generate_streaming_response(processed_query, model)
        else:
            return await self.generate_complete_response(processed_query, model)
```

**Model Selection Logic**:
- **Gemini Flash**: Simple queries, quick responses (<2s)
- **Gemini Flash-Lite**: Very simple queries, ultra-fast (<500ms)
- **Gemini Pro**: Complex queries, detailed analysis (2-10s)

### 3. Semantic Cache Manager (`src/query_intelligence/services/cache_manager.py`)

Intelligent caching with semantic similarity:

```python
class SemanticCacheManager:
    def __init__(self):
        self.redis_client = Redis()
        self.embeddings_service = EmbeddingsService()
        self.similarity_threshold = 0.85
    
    async def get_cached_response(self, query: str) -> Optional[CachedResponse]:
        # Generate query embedding
        query_embedding = await self.embeddings_service.embed(query)
        
        # Search for similar cached queries
        similar_queries = await self.find_similar_queries(query_embedding)
        
        # Return best match if above threshold
        if similar_queries and similar_queries[0].similarity > self.similarity_threshold:
            return similar_queries[0].response
        
        return None
```

**Cache Strategy**:
- Semantic similarity matching (85% threshold)
- LRU eviction with TTL
- Redis-based distributed caching
- Memory fallback for high performance

### 4. WebSocket Stream Handler (`src/query_intelligence/api/websocket.py`)

Real-time query streaming:

```python
class QueryStreamHandler:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.response_generator = ResponseGenerator()
    
    async def handle_streaming_query(
        self, 
        websocket: WebSocket, 
        query_id: str,
        processed_query: ProcessedQuery
    ):
        # Add connection to active connections
        self.active_connections[query_id] = websocket
        
        try:
            # Stream response generation
            async for chunk in self.response_generator.generate_streaming(processed_query):
                await websocket.send_json({
                    "type": "partial_response",
                    "query_id": query_id,
                    "content": chunk.content,
                    "confidence": chunk.confidence
                })
            
            # Send completion signal
            await websocket.send_json({
                "type": "complete",
                "query_id": query_id
            })
        finally:
            # Clean up connection
            self.active_connections.pop(query_id, None)
```

## Data Flow

### 1. Query Processing Flow

```
Client Request → FastAPI Router → Authentication Middleware → Rate Limiting
    ↓
Query Validation → Query Processor → Intent Classification → Context Extraction
    ↓
Semantic Cache Check → Cache Hit? → Return Cached Response
    ↓ (Cache Miss)
AI Response Generation → Model Selection → LLM Processing → Response Formatting
    ↓
Cache Storage → Response Return → Client
```

### 2. Streaming Query Flow

```
WebSocket Connection → JWT Validation → Query Processing → Streaming Response
    ↓
Real-time Updates → Partial Responses → Progress Indicators → Final Response
    ↓
Connection Cleanup → Session Termination
```

### 3. Caching Strategy

```
Query Input → Embedding Generation → Similarity Search → Cache Validation
    ↓ (Miss)
Process Query → Generate Response → Store with Embedding → Return to Client
    ↓ (Hit)
Validate Freshness → Return Cached Response
```

## Technology Stack

### Core Technologies
- **Language**: Python 3.11+
- **Web Framework**: FastAPI (async web framework)
- **AI/ML**: Google GenAI SDK (Gemini 2.5 models)
- **WebSocket**: FastAPI WebSocket support
- **Async Runtime**: AsyncIO

### Infrastructure
- **Container**: Docker with multi-stage builds
- **Orchestration**: Google Cloud Run (serverless)
- **CI/CD**: Cloud Build
- **Monitoring**: Cloud Logging, Cloud Monitoring

### Google Cloud Services
- **Cloud Run**: Serverless container hosting
- **Firebase Auth**: Authentication and user management
- **Secret Manager**: Secure credential storage
- **Cloud Monitoring**: Observability and alerting

### External Dependencies
- **Redis**: Caching and rate limiting
- **Google GenAI**: Large language models
- **Analysis Engine**: Code parsing and analysis
- **Pattern Mining**: ML pattern detection

## Design Patterns

### 1. Service Layer Pattern
All business logic is encapsulated in service classes:

```python
@dataclass
class QueryIntelligenceService:
    query_processor: QueryProcessor
    response_generator: ResponseGenerator
    cache_manager: CacheManager
    
    async def process_query(self, request: QueryRequest) -> QueryResponse:
        # Service orchestration logic
        pass
```

### 2. Circuit Breaker Pattern
Prevents cascading failures in external service calls:

```python
class CircuitBreaker:
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.state = CircuitState.CLOSED
    
    async def call(self, func, *args, **kwargs):
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitState.HALF_OPEN
            else:
                raise CircuitBreakerOpenError()
        
        try:
            result = await func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise
```

### 3. Strategy Pattern
Different processing strategies for different query types:

```python
class QueryStrategy(ABC):
    @abstractmethod
    async def process(self, query: ProcessedQuery) -> QueryResponse:
        pass

class SimpleQueryStrategy(QueryStrategy):
    async def process(self, query: ProcessedQuery) -> QueryResponse:
        # Fast processing for simple queries
        pass

class ComplexQueryStrategy(QueryStrategy):
    async def process(self, query: ProcessedQuery) -> QueryResponse:
        # Comprehensive processing for complex queries
        pass
```

### 4. Observer Pattern
WebSocket updates via broadcast channels:

```python
class QueryObserver(ABC):
    @abstractmethod
    async def on_progress(self, update: ProgressUpdate):
        pass
    
    @abstractmethod
    async def on_complete(self, result: QueryResponse):
        pass
    
    @abstractmethod
    async def on_error(self, error: QueryError):
        pass
```

## Scalability & Performance

### Horizontal Scaling
- **Stateless Design**: All state stored in Redis/external services
- **Load Balancing**: Cloud Run automatic load balancing
- **Auto-scaling**: Based on request volume and latency

### Performance Optimizations
1. **Multi-level Caching**: Memory + Redis + semantic caching
2. **Async Processing**: Non-blocking I/O for all operations
3. **Connection Pooling**: Reuse HTTP connections
4. **Response Streaming**: Real-time partial responses
5. **Model Selection**: Appropriate model for query complexity

### Resource Limits
- **Max query length**: 10,000 characters
- **Request timeout**: 30 seconds
- **Memory limit**: 16GB per instance (Cloud Run)
- **CPU limit**: 4 vCPUs per instance
- **Concurrent requests**: 1000 per instance

### Caching Strategy
- **L1 Cache**: In-memory (per instance, <100ms)
- **L2 Cache**: Redis (shared, <10ms)
- **L3 Cache**: Semantic cache (similarity-based)

## Security Architecture

### Defense in Depth
1. **Network Security**: HTTPS/WSS encryption, Cloud Run security
2. **Application Security**: Input validation, sanitization, JWT validation
3. **Data Security**: No persistent sensitive data storage
4. **Access Control**: JWT-based authentication with Firebase
5. **Audit Logging**: All requests and responses logged

### Authentication Flow
```
Client → JWT Token → Firebase Validation → User Verification → Permission Check
    ↓
Rate Limit Check → Request Processing → Response Generation → Audit Log
```

### Security Headers
```python
middleware = [
    SecurityHeadersMiddleware(),  # HSTS, CSP, X-Frame-Options
    CORSMiddleware(),             # Cross-origin protection
    AuthenticationMiddleware(),   # JWT validation
    RateLimitMiddleware(),        # Request throttling
]
```

## Integration Points

### 1. Inbound Integrations
- **Web UI**: REST API and WebSocket
- **CLI Tools**: REST API with authentication
- **SDK**: Language-specific clients
- **Other Services**: Service-to-service authentication

### 2. Outbound Integrations
- **Analysis Engine**: Code parsing and AST analysis
- **Pattern Mining**: ML-based pattern detection
- **Google GenAI**: Large language model inference
- **Firebase**: Authentication and user management

### 3. Event Schema
```json
{
  "event_id": "uuid",
  "event_type": "query.completed",
  "timestamp": "2025-07-14T12:00:00Z",
  "payload": {
    "query_id": "uuid",
    "user_id": "user123",
    "query": "How does authentication work?",
    "response_time_ms": 850,
    "confidence": 0.95,
    "model_used": "gemini-pro"
  }
}
```

## Deployment Architecture

### Cloud Run Configuration
```yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: query-intelligence
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/execution-environment: gen2
    spec:
      containers:
        - image: gcr.io/project/query-intelligence
          resources:
            limits:
              memory: 16Gi
              cpu: "4"
          env:
            - name: ENVIRONMENT
              value: production
```

### Multi-Region Strategy
- **Primary**: us-central1 (main deployment)
- **Secondary**: europe-west1, asia-northeast1 (planned)
- **Load Balancing**: Global HTTP(S) Load Balancer
- **Data Consistency**: Eventual consistency via Redis

---

This architecture provides a robust foundation for a scalable, secure, and performant AI-powered query intelligence service. For implementation details, see the [Developer Guide](../guides/developer-guide.md) and [Deployment Guide](../deployment/production-deployment.md).