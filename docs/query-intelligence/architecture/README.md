# Query Intelligence Architecture

## Table of Contents
- [System Architecture Overview](#system-architecture-overview)
- [Core Components](#core-components)
- [Technology Stack](#technology-stack)
- [Architectural Principles](#architectural-principles)
- [Component Architecture](#component-architecture)
- [Visual Diagrams](#visual-diagrams)
- [Scalability and Reliability](#scalability-and-reliability)

## System Architecture Overview

Query Intelligence is a high-performance microservice that processes natural language queries about code, built with FastAPI and designed for <100ms response times. The service implements a sophisticated multi-layer architecture optimized for semantic code search and intelligent response generation.

### High-Level Design

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Application]
        SDK[CCL SDK]
        API[API Clients]
    end
    
    subgraph "API Gateway"
        APIGW[API Gateway/Load Balancer]
    end
    
    subgraph "Query Intelligence Service"
        FAST[FastAPI Application]
        MW[Middleware Stack]
        QP[Query Processor]
        CACHE[Cache Manager]
        LLM[LLM Service]
        SS[Semantic Search]
    end
    
    subgraph "External Services"
        REDIS[(Redis Cache)]
        PINECONE[(Pinecone Vector DB)]
        GEMINI[Google GenAI/Vertex AI]
    end
    
    subgraph "Internal Services"
        AE[Analysis Engine]
        PM[Pattern Mining]
    end
    
    WEB --> APIGW
    SDK --> APIGW
    API --> APIGW
    
    APIGW --> FAST
    FAST --> MW
    MW --> QP
    
    QP --> CACHE
    QP --> LLM
    QP --> SS
    
    CACHE --> REDIS
    SS --> PINECONE
    LLM --> GEMINI
    
    QP --> AE
    QP --> PM
    
    style FAST fill:#4CAF50
    style QP fill:#2196F3
    style CACHE fill:#FF9800
    style LLM fill:#9C27B0
```

### Core Components and Responsibilities

1. **API Layer (FastAPI)**
   - RESTful endpoints for synchronous queries
   - WebSocket support for streaming responses
   - Health check and readiness probes
   - Admin API for service management

2. **Service Layer**
   - Query Processor: Orchestrates the entire query pipeline
   - LLM Service: Manages AI model interactions with intelligent routing
   - Semantic Search: Handles vector-based code search
   - Cache Manager: Multi-level caching for performance

3. **Data Layer**
   - Redis: Distributed cache and session storage
   - Pinecone: Vector database for semantic embeddings
   - Google Cloud Storage: Long-term storage integration

4. **External Integration Layer**
   - Google GenAI/Vertex AI: LLM capabilities
   - Analysis Engine: Code parsing and AST analysis
   - Pattern Mining: ML-based pattern detection

### Technology Stack Justification

| Component | Technology | Justification |
|-----------|------------|---------------|
| Framework | FastAPI | Async support, automatic OpenAPI docs, high performance |
| Language | Python 3.11+ | Rich ML/AI ecosystem, async capabilities |
| Cache | Redis | Sub-millisecond latency, distributed caching |
| Vector DB | Pinecone | Managed service, optimized for similarity search |
| AI/ML | Google GenAI | State-of-the-art models, Gemini 2.5 Flash/Pro |
| Monitoring | Prometheus + Grafana | Industry standard, rich metrics |
| Logging | structlog | Structured logging for observability |

### Architectural Principles

1. **Stateless Design**
   - No local state storage
   - All state externalized to Redis/databases
   - Enables horizontal scaling

2. **Event-Driven Processing**
   - Asynchronous operations throughout
   - Non-blocking I/O for external calls
   - WebSocket support for real-time updates

3. **Defensive Programming**
   - Circuit breakers for all external services
   - Fallback handlers for service failures
   - Comprehensive error handling

4. **Performance First**
   - Multi-level caching strategy
   - Intelligent model routing
   - Optimized data structures

5. **Security by Design**
   - JWT authentication
   - Rate limiting per user/IP
   - Input validation and sanitization

## Component Architecture

### API Layer (FastAPI Application Structure)

```mermaid
graph LR
    subgraph "FastAPI Application"
        MAIN[main.py]
        
        subgraph "API Routers"
            QUERY[query.py]
            WS[websocket.py]
            ADMIN[admin.py]
        end
        
        subgraph "Middleware"
            AUTH[Authentication]
            RATE[Rate Limiting]
            SEC[Security Headers]
            CORS[CORS Handler]
        end
        
        subgraph "Lifecycle"
            START[Startup Events]
            SHUT[Shutdown Events]
        end
    end
    
    MAIN --> API Routers
    MAIN --> Middleware
    MAIN --> Lifecycle
    
    style MAIN fill:#4CAF50
    style Middleware fill:#FF9800
```

The API layer implements:
- **Request routing** through FastAPI routers
- **Middleware pipeline** for cross-cutting concerns
- **Lifecycle management** for resource initialization/cleanup
- **OpenAPI documentation** auto-generated from type hints

### Service Layer (Business Logic Organization)

```mermaid
graph TB
    subgraph "Service Layer"
        QP[Query Processor<br/>Core Orchestrator]
        
        subgraph "Core Services"
            INTENT[Intent Analyzer]
            EMBED[Embedding Generator]
            SEARCH[Semantic Search]
            RANK[Result Reranker]
            GEN[Response Generator]
        end
        
        subgraph "Support Services"
            CACHE[Cache Manager]
            LANG[Language Detector]
            FALL[Fallback Handler]
            OPT[Query Optimizer]
        end
    end
    
    QP --> INTENT
    QP --> EMBED
    QP --> SEARCH
    QP --> RANK
    QP --> GEN
    
    QP --> CACHE
    QP --> LANG
    QP --> FALL
    QP --> OPT
    
    style QP fill:#2196F3
    style "Core Services" fill:#E3F2FD
    style "Support Services" fill:#FFF3E0
```

Key service responsibilities:
- **Query Processor**: Main orchestration logic
- **Intent Analyzer**: Determines query type and requirements
- **Semantic Search**: Vector-based similarity search
- **Response Generator**: LLM-based answer creation
- **Cache Manager**: Multi-level caching strategy

### Data Layer (Redis, Pinecone Integration)

```mermaid
graph LR
    subgraph "Data Access Layer"
        CM[Cache Manager]
        VS[Vector Store]
        
        subgraph "Redis Operations"
            QUERY[Query Cache]
            EMBED[Embedding Cache]
            SESSION[Session Store]
            RATE[Rate Limit Store]
        end
        
        subgraph "Pinecone Operations"
            INDEX[Index Management]
            UPSERT[Vector Upsert]
            SEARCH[Similarity Search]
            META[Metadata Filtering]
        end
    end
    
    CM --> QUERY
    CM --> EMBED
    CM --> SESSION
    CM --> RATE
    
    VS --> INDEX
    VS --> UPSERT
    VS --> SEARCH
    VS --> META
    
    style CM fill:#FF9800
    style VS fill:#9C27B0
```

Data layer features:
- **Multi-level caching**: Memory → Redis → Source
- **Vector indexing**: 768-dimensional embeddings
- **Metadata filtering**: Repository, language, file type
- **TTL management**: Intelligent cache expiration

### External Integration Layer

```mermaid
graph TB
    subgraph "Integration Clients"
        GENAI[GenAI Client]
        ANALYSIS[Analysis Engine Client]
        PATTERN[Pattern Mining Client]
    end
    
    subgraph "Circuit Breakers"
        CB1[GenAI Circuit Breaker]
        CB2[Analysis Circuit Breaker]
        CB3[Pattern Circuit Breaker]
    end
    
    subgraph "External Services"
        GEMINI[Google GenAI/Vertex AI]
        AE[Analysis Engine Service]
        PM[Pattern Mining Service]
    end
    
    GENAI --> CB1
    ANALYSIS --> CB2
    PATTERN --> CB3
    
    CB1 --> GEMINI
    CB2 --> AE
    CB3 --> PM
    
    style "Circuit Breakers" fill:#F44336
```

Integration patterns:
- **Circuit breaker pattern**: Prevents cascade failures
- **Retry with backoff**: Handles transient failures
- **Timeout management**: Prevents hanging requests
- **Fallback strategies**: Graceful degradation

## Visual Diagrams

### System Architecture Diagram

```mermaid
C4Context
    title Query Intelligence System Context
    
    Person(user, "Developer", "Uses natural language to query code")
    
    System_Boundary(ccl, "CCL Platform") {
        System(qi, "Query Intelligence", "Processes natural language queries")
        System(ae, "Analysis Engine", "Code parsing and AST analysis")
        System(pm, "Pattern Mining", "ML pattern detection")
    }
    
    System_Ext(redis, "Redis", "Distributed cache")
    System_Ext(pinecone, "Pinecone", "Vector database")
    System_Ext(genai, "Google GenAI", "LLM services")
    
    Rel(user, qi, "Queries", "HTTPS/WSS")
    Rel(qi, ae, "Requests analysis", "HTTP")
    Rel(qi, pm, "Detects patterns", "HTTP")
    Rel(qi, redis, "Caches data", "Redis Protocol")
    Rel(qi, pinecone, "Searches vectors", "HTTPS")
    Rel(qi, genai, "Generates responses", "HTTPS")
```

### Request Flow Diagram

```mermaid
sequenceDiagram
    participant Client
    participant API as FastAPI
    participant MW as Middleware
    participant QP as Query Processor
    participant Cache
    participant LLM
    participant Search
    participant Redis
    participant Pinecone
    
    Client->>API: POST /api/v1/query
    API->>MW: Apply middleware
    MW->>MW: Authenticate
    MW->>MW: Rate limit check
    MW->>QP: Process query
    
    QP->>Cache: Check cache
    Cache->>Redis: Get cached result
    
    alt Cache Hit
        Redis-->>Cache: Return result
        Cache-->>QP: Cached response
        QP-->>Client: Return response (<100ms)
    else Cache Miss
        QP->>LLM: Analyze intent
        LLM-->>QP: Intent analysis
        
        QP->>Search: Generate embedding
        Search->>Pinecone: Vector search
        Pinecone-->>Search: Similar chunks
        Search-->>QP: Code chunks
        
        QP->>QP: Rerank results
        
        QP->>LLM: Generate response
        LLM-->>QP: Generated text
        
        QP->>Cache: Store result
        Cache->>Redis: Cache response
        
        QP-->>Client: Return response
    end
```

### Component Interaction Diagram

```mermaid
graph TB
    subgraph "Request Processing Pipeline"
        REQ[Incoming Request]
        
        subgraph "Pre-processing"
            AUTH[Authentication]
            RATE[Rate Limiting]
            VALID[Validation]
        end
        
        subgraph "Core Processing"
            LANG[Language Detection]
            INTENT[Intent Analysis]
            EMBED[Embedding Generation]
            SEARCH[Semantic Search]
            RANK[Result Ranking]
            PATTERN[Pattern Analysis]
            GEN[Response Generation]
        end
        
        subgraph "Post-processing"
            CACHE[Cache Storage]
            METRIC[Metrics Collection]
            LOG[Logging]
        end
        
        RESP[Response]
    end
    
    REQ --> AUTH
    AUTH --> RATE
    RATE --> VALID
    
    VALID --> LANG
    LANG --> INTENT
    INTENT --> EMBED
    EMBED --> SEARCH
    SEARCH --> RANK
    RANK --> PATTERN
    PATTERN --> GEN
    
    GEN --> CACHE
    CACHE --> METRIC
    METRIC --> LOG
    LOG --> RESP
    
    style "Core Processing" fill:#E3F2FD
    style "Pre-processing" fill:#FFEBEE
    style "Post-processing" fill:#E8F5E9
```

### Deployment Architecture

```mermaid
graph TB
    subgraph "Google Cloud Platform"
        subgraph "Cloud Run"
            QI1[Query Intelligence<br/>Instance 1]
            QI2[Query Intelligence<br/>Instance 2]
            QIN[Query Intelligence<br/>Instance N]
        end
        
        subgraph "Managed Services"
            REDIS[Memorystore<br/>Redis]
            SECRET[Secret Manager]
            MONITOR[Cloud Monitoring]
        end
        
        subgraph "External Integrations"
            VERTEX[Vertex AI]
            PINECONE[Pinecone Cloud]
        end
        
        LB[Cloud Load Balancer]
    end
    
    LB --> QI1
    LB --> QI2
    LB --> QIN
    
    QI1 --> REDIS
    QI2 --> REDIS
    QIN --> REDIS
    
    QI1 --> SECRET
    QI1 --> VERTEX
    QI1 --> PINECONE
    
    style "Cloud Run" fill:#4285F4
    style "Managed Services" fill:#34A853
    style "External Integrations" fill:#EA4335
```

## Scalability and Reliability

### Scalability Patterns

1. **Horizontal Scaling**
   - Stateless service design
   - Cloud Run auto-scaling (0-1000 instances)
   - Load balancing across instances

2. **Caching Strategy**
   - L1: In-memory LRU cache (1ms latency)
   - L2: Redis distributed cache (10ms latency)
   - L3: Vector database cache (50ms latency)

3. **Resource Optimization**
   - Connection pooling for Redis/HTTP
   - Async I/O for all external calls
   - Batch processing where applicable

### Reliability Patterns

1. **Circuit Breakers**
   - Failure threshold: 3 consecutive failures
   - Recovery timeout: 30-60 seconds
   - Fallback strategies for each service

2. **Health Checks**
   - Liveness probe: `/health`
   - Readiness probe: `/ready`
   - Dependency health monitoring

3. **Error Handling**
   - Graceful degradation
   - Comprehensive logging
   - User-friendly error messages

4. **Performance Monitoring**
   - Prometheus metrics
   - Custom dashboards
   - SLO tracking (99.9% uptime, <100ms p95)

### Production Readiness

The Query Intelligence service achieves 95% production readiness through:
- Comprehensive test coverage (>85%)
- Performance optimization (<100ms response times)
- Security hardening (JWT auth, rate limiting)
- Operational tooling (metrics, logging, alerts)
- Documentation and runbooks

This architecture supports:
- 1000+ queries per second
- <100ms p95 response time
- 99.9% availability SLA
- Seamless scaling from 0 to 1000 instances