# Query Intelligence Data Flow Architecture

## Table of Contents
- [Overview](#overview)
- [Query Processing Pipeline](#query-processing-pipeline)
- [Data Flow Patterns](#data-flow-patterns)
- [Performance Optimization](#performance-optimization)
- [Data Flow Diagrams](#data-flow-diagrams)

## Overview

The Query Intelligence service implements a sophisticated data flow architecture optimized for <100ms response times. This document details how data moves through the system, from initial query intake to final response delivery.

### Data Flow Overview

```mermaid
graph LR
    subgraph "Input"
        QUERY[Natural Language Query]
        CONTEXT[User Context]
        FILTERS[Search Filters]
    end
    
    subgraph "Processing Pipeline"
        VALIDATE[Validation]
        CACHE[Cache Check]
        PROCESS[Query Processing]
        GENERATE[Response Generation]
    end
    
    subgraph "Output"
        RESPONSE[Structured Response]
        REFS[Code References]
        METRICS[Performance Metrics]
    end
    
    QUERY --> VALIDATE
    CONTEXT --> VALIDATE
    FILTERS --> VALIDATE
    
    VALIDATE --> CACHE
    CACHE --> PROCESS
    PROCESS --> GENERATE
    
    GENERATE --> RESPONSE
    GENERATE --> REFS
    GENERATE --> METRICS
    
    style PROCESS fill:#2196F3
    style CACHE fill:#FF9800
```

## Query Processing Pipeline

### 1. Request Intake and Validation

The pipeline begins with request validation and normalization.

```mermaid
graph TB
    subgraph "Request Intake"
        REQ[HTTP/WebSocket Request]
        
        subgraph "Validation Layer"
            AUTH[Authentication Check]
            RATE[Rate Limit Check]
            SCHEMA[Schema Validation]
            SANITIZE[Input Sanitization]
        end
        
        subgraph "Request Model"
            QUERY[Query Text]
            REPO[Repository ID]
            USER[User Context]
            FILTERS[Filter Criteria]
            SESSION[Session ID]
        end
    end
    
    REQ --> AUTH
    AUTH --> RATE
    RATE --> SCHEMA
    SCHEMA --> SANITIZE
    
    SANITIZE --> QUERY
    SANITIZE --> REPO
    SANITIZE --> USER
    SANITIZE --> FILTERS
    SANITIZE --> SESSION
    
    style AUTH fill:#F44336
    style RATE fill:#FF9800
```

#### Validation Rules

| Field | Validation | Constraints |
|-------|------------|-------------|
| query | Required, string | 1-1000 characters |
| repository_id | Required, UUID | Valid repository |
| user_id | Optional, string | JWT validated |
| filters | Optional, object | Valid filter keys |
| session_id | Optional, UUID | Valid session |

### 2. Intent Analysis Flow

Intent analysis determines query type and processing requirements.

```mermaid
sequenceDiagram
    participant Query
    participant Lang as Language Detector
    participant Intent as Intent Analyzer
    participant LLM as LLM Service
    participant Cache
    
    Query->>Lang: Detect language
    Lang-->>Query: Language result
    
    alt Needs Translation
        Query->>Lang: Translate to English
        Lang-->>Query: Translated query
    end
    
    Query->>Cache: Check intent cache
    
    alt Cache Miss
        Cache-->>Query: Not found
        Query->>Intent: Analyze intent
        Intent->>LLM: Generate analysis
        LLM-->>Intent: Intent JSON
        Intent-->>Query: IntentAnalysis
        Query->>Cache: Store intent
    else Cache Hit
        Cache-->>Query: Cached intent
    end
```

#### Intent Analysis Output

```json
{
  "primary_intent": "explain|find|debug|refactor|analyze|compare",
  "code_elements": ["function_name", "class_name"],
  "scope": "file|module|repository",
  "context_depth": "shallow|normal|deep",
  "confidence": 0.85,
  "language": "en",
  "was_translated": false
}
```

### 3. Embedding Generation

Embeddings enable semantic search across the codebase.

```mermaid
graph LR
    subgraph "Embedding Pipeline"
        TEXT[Query Text]
        PREFIX[Context Prefix]
        MODEL[Embedding Model<br/>all-MiniLM-L6-v2]
        NORM[Normalization]
        VECTOR[768-dim Vector]
    end
    
    TEXT --> PREFIX
    PREFIX --> MODEL
    MODEL --> NORM
    NORM --> VECTOR
    
    style MODEL fill:#9C27B0
```

#### Embedding Cache Strategy

```mermaid
graph TB
    subgraph "Embedding Cache Layers"
        CHECK[Cache Check]
        
        subgraph "L1 Memory"
            MEM[In-Memory LRU<br/>100 items<br/>~1ms]
        end
        
        subgraph "L2 Redis"
            REDIS[Redis Cache<br/>10K items<br/>~10ms]
        end
        
        subgraph "L3 Generate"
            GEN[Generate New<br/>~50ms]
        end
    end
    
    CHECK --> MEM
    MEM -->|Miss| REDIS
    REDIS -->|Miss| GEN
    
    GEN -->|Store| REDIS
    REDIS -->|Promote| MEM
    
    style MEM fill:#90EE90
    style REDIS fill:#87CEEB
    style GEN fill:#FFB6C1
```

### 4. Semantic Search Process

The semantic search process retrieves relevant code chunks.

```mermaid
graph TB
    subgraph "Search Pipeline"
        EMB[Query Embedding]
        
        subgraph "Vector Search"
            PINECONE[Pinecone Search]
            FILTERS[Metadata Filters]
            SIMILARITY[Cosine Similarity]
        end
        
        subgraph "Results"
            CHUNKS[Code Chunks]
            SCORES[Similarity Scores]
            META[Chunk Metadata]
        end
        
        subgraph "Reranking"
            INTENT[Intent-based Scoring]
            RECENCY[Recency Boost]
            COMBINE[Combined Score]
        end
    end
    
    EMB --> PINECONE
    PINECONE --> FILTERS
    FILTERS --> SIMILARITY
    
    SIMILARITY --> CHUNKS
    SIMILARITY --> SCORES
    SIMILARITY --> META
    
    CHUNKS --> INTENT
    SCORES --> INTENT
    INTENT --> RECENCY
    RECENCY --> COMBINE
    
    style PINECONE fill:#9C27B0
    style COMBINE fill:#4CAF50
```

### 5. Response Generation

Response generation combines search results with LLM capabilities.

```mermaid
sequenceDiagram
    participant QP as Query Processor
    participant PM as Pattern Mining
    participant LLM as LLM Service
    participant FH as Fallback Handler
    participant Cache
    
    QP->>PM: Analyze patterns (optional)
    PM-->>QP: Pattern insights
    
    QP->>LLM: Generate response
    Note over LLM: Model selection based on complexity
    
    alt Success
        LLM-->>QP: Generated response
    else Circuit Breaker Open
        LLM-->>QP: Error
        QP->>FH: Handle fallback
        FH-->>QP: Fallback response
    end
    
    QP->>QP: Extract references
    QP->>QP: Generate follow-ups
    QP->>Cache: Store result
    
    QP-->>Client: Complete response
```

### 6. Streaming vs Synchronous Responses

The service supports both streaming and synchronous response patterns.

#### Synchronous Flow
```mermaid
graph LR
    subgraph "Synchronous Response"
        REQ[Request]
        PROCESS[Full Processing]
        RESPONSE[Complete Response]
    end
    
    REQ --> PROCESS
    PROCESS --> RESPONSE
    
    style PROCESS fill:#2196F3
```

#### Streaming Flow (WebSocket)
```mermaid
graph TB
    subgraph "Streaming Response"
        WS[WebSocket Connection]
        
        subgraph "Stream Chunks"
            ACK[Acknowledgment]
            STATUS[Status Updates]
            INTENT[Intent Result]
            REFS[Code References]
            TEXT[Response Text]
            DONE[Completion]
        end
    end
    
    WS --> ACK
    ACK --> STATUS
    STATUS --> INTENT
    INTENT --> REFS
    REFS --> TEXT
    TEXT --> DONE
    
    style WS fill:#4CAF50
```

## Data Flow Patterns

### Caching Strategy

The multi-level caching strategy optimizes for different access patterns.

```mermaid
graph TB
    subgraph "Cache Hierarchy"
        subgraph "L1: Memory Cache"
            MEM_QUERY[Query Results<br/>TTL: 5 min]
            MEM_EMB[Embeddings<br/>TTL: 30 min]
        end
        
        subgraph "L2: Redis Cache"
            REDIS_QUERY[Query Results<br/>TTL: 1 hour]
            REDIS_EMB[Embeddings<br/>TTL: 24 hours]
            REDIS_SESSION[Sessions<br/>TTL: 30 min]
        end
        
        subgraph "L3: Persistent Storage"
            PINECONE[Vector Index]
            SPANNER[Analysis Data]
        end
    end
    
    MEM_QUERY --> REDIS_QUERY
    MEM_EMB --> REDIS_EMB
    
    REDIS_QUERY --> SPANNER
    REDIS_EMB --> PINECONE
    
    style "L1: Memory Cache" fill:#90EE90
    style "L2: Redis Cache" fill:#87CEEB
    style "L3: Persistent Storage" fill:#FFB6C1
```

### Embedding Storage and Retrieval

```mermaid
graph LR
    subgraph "Embedding Lifecycle"
        GEN[Generate<br/>50ms]
        STORE[Store<br/>10ms]
        INDEX[Index<br/>100ms]
        SEARCH[Search<br/>50ms]
        RETRIEVE[Retrieve<br/>10ms]
    end
    
    GEN --> STORE
    STORE --> INDEX
    INDEX --> SEARCH
    SEARCH --> RETRIEVE
    
    style GEN fill:#FF9800
    style SEARCH fill:#4CAF50
```

### Session Management

```mermaid
graph TB
    subgraph "Session Data Flow"
        NEW[New Session]
        
        subgraph "Session Store"
            CREATE[Create Session]
            UPDATE[Update Context]
            EXTEND[Extend TTL]
            EXPIRE[Auto-expire]
        end
        
        subgraph "Session Data"
            HISTORY[Query History]
            CONTEXT[User Context]
            PREFS[Preferences]
        end
    end
    
    NEW --> CREATE
    CREATE --> HISTORY
    CREATE --> CONTEXT
    CREATE --> PREFS
    
    HISTORY --> UPDATE
    UPDATE --> EXTEND
    EXTEND --> EXPIRE
    
    style CREATE fill:#4CAF50
    style EXPIRE fill:#F44336
```

### Rate Limiting Implementation

```mermaid
graph TB
    subgraph "Rate Limit Flow"
        REQ[Request]
        
        subgraph "Rate Check"
            USER[User Limit<br/>100/min]
            IP[IP Limit<br/>1000/min]
            GLOBAL[Global Limit<br/>10K/min]
        end
        
        subgraph "Actions"
            ALLOW[Allow Request]
            THROTTLE[429 Too Many]
            LOG[Log Violation]
        end
    end
    
    REQ --> USER
    USER --> IP
    IP --> GLOBAL
    
    GLOBAL -->|Pass| ALLOW
    GLOBAL -->|Fail| THROTTLE
    THROTTLE --> LOG
    
    style ALLOW fill:#4CAF50
    style THROTTLE fill:#F44336
```

## Performance Optimization

### Cache Hit Flow

Optimized path for cached queries achieving <10ms response times.

```mermaid
graph LR
    subgraph "Fast Path (<10ms)"
        REQ[Request]
        HASH[Hash Key]
        MEM[Memory Check]
        HIT[Cache Hit]
        RESP[Response]
    end
    
    REQ --> HASH
    HASH --> MEM
    MEM --> HIT
    HIT --> RESP
    
    style "Fast Path (<10ms)" fill:#90EE90
```

### Model Selection Logic

Dynamic model selection based on query characteristics.

```mermaid
graph TB
    subgraph "Model Router"
        ANALYZE[Query Analysis]
        
        decision{Complexity Score}
        
        subgraph "Models"
            LITE[Flash-Lite<br/>~20ms]
            FLASH[Flash<br/>~50ms]
            PRO[Pro<br/>~200ms]
        end
    end
    
    ANALYZE --> decision
    decision -->|< 0.3| LITE
    decision -->|0.3-0.7| FLASH
    decision -->|> 0.7| PRO
    
    style LITE fill:#90EE90
    style FLASH fill:#87CEEB
    style PRO fill:#FFB6C1
```

### Parallel Processing Patterns

```mermaid
graph TB
    subgraph "Parallel Operations"
        START[Query Start]
        
        subgraph "Parallel Block 1"
            CACHE[Cache Check]
            HEALTH[Health Checks]
        end
        
        subgraph "Parallel Block 2"
            EMBED[Embedding Gen]
            INTENT[Intent Analysis]
        end
        
        subgraph "Parallel Block 3"
            SEARCH[Vector Search]
            META[Metadata Fetch]
        end
        
        MERGE[Merge Results]
        END[Query Complete]
    end
    
    START --> "Parallel Block 1"
    "Parallel Block 1" --> "Parallel Block 2"
    "Parallel Block 2" --> "Parallel Block 3"
    "Parallel Block 3" --> MERGE
    MERGE --> END
    
    style "Parallel Block 1" fill:#E3F2FD
    style "Parallel Block 2" fill:#E8F5E9
    style "Parallel Block 3" fill:#FFF3E0
```

### Resource Optimization

```mermaid
graph LR
    subgraph "Resource Pool Management"
        subgraph "Connection Pools"
            REDIS[Redis Pool<br/>Max: 100]
            HTTP[HTTP Pool<br/>Max: 50]
            GRPC[gRPC Pool<br/>Max: 20]
        end
        
        subgraph "Thread Pools"
            EMBED_P[Embedding Pool<br/>Workers: 4]
            IO_P[I/O Pool<br/>Workers: 8]
        end
        
        subgraph "Memory Management"
            CACHE_L[Cache Limits<br/>100MB]
            GC[Garbage Collection<br/>Aggressive]
        end
    end
    
    style REDIS fill:#FF9800
    style EMBED_P fill:#9C27B0
    style CACHE_L fill:#4CAF50
```

## Data Flow Diagrams

### End-to-End Query Processing Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant API as API Layer
    participant MW as Middleware
    participant QP as Query Processor
    participant Cache as Cache Manager
    participant Search as Semantic Search
    participant LLM as LLM Service
    participant Metrics as Metrics
    
    C->>API: POST /api/v1/query
    API->>MW: Apply middleware
    MW->>MW: Authenticate & Rate limit
    MW->>QP: Process query
    
    par Cache Check
        QP->>Cache: Check all levels
    and Intent Analysis
        QP->>LLM: Analyze intent
    end
    
    alt Cache Hit
        Cache-->>QP: Cached result
        QP-->>C: Fast response (<10ms)
    else Cache Miss
        QP->>Search: Generate embedding
        Search->>Search: Search vectors
        Search-->>QP: Code chunks
        
        QP->>QP: Rerank results
        
        opt Pattern Analysis
            QP->>QP: Detect patterns
        end
        
        QP->>LLM: Generate response
        LLM-->>QP: Response text
        
        par Cache Storage
            QP->>Cache: Store result
        and Metrics
            QP->>Metrics: Record metrics
        end
        
        QP-->>C: Complete response
    end
```

### Cache Interaction Diagram

```mermaid
graph TB
    subgraph "Cache Operations"
        subgraph "Write Path"
            W_REQ[Write Request]
            W_MEM[Update Memory]
            W_REDIS[Update Redis]
            W_TTL[Set TTL]
        end
        
        subgraph "Read Path"
            R_REQ[Read Request]
            R_MEM[Check Memory]
            R_REDIS[Check Redis]
            R_MISS[Cache Miss]
        end
        
        subgraph "Eviction"
            E_LRU[LRU Eviction]
            E_TTL[TTL Expiry]
            E_SIZE[Size Limit]
        end
    end
    
    W_REQ --> W_MEM
    W_MEM --> W_REDIS
    W_REDIS --> W_TTL
    
    R_REQ --> R_MEM
    R_MEM -->|Miss| R_REDIS
    R_REDIS -->|Miss| R_MISS
    
    W_MEM --> E_LRU
    W_TTL --> E_TTL
    W_MEM --> E_SIZE
    
    style "Write Path" fill:#4CAF50
    style "Read Path" fill:#2196F3
    style "Eviction" fill:#FF9800
```

### WebSocket Streaming Flow

```mermaid
sequenceDiagram
    participant Client
    participant WS as WebSocket
    participant QP as Query Processor
    participant Stream as Stream Manager
    
    Client->>WS: Connect
    WS->>Client: Connection established
    
    Client->>WS: Send query
    WS->>QP: Process query
    
    QP->>Stream: Create stream
    
    loop Streaming chunks
        QP->>Stream: Add chunk
        Stream->>WS: Send chunk
        WS->>Client: Deliver chunk
        Note over Client: Update UI progressively
    end
    
    QP->>Stream: Complete
    Stream->>WS: Send done signal
    WS->>Client: Stream complete
    
    Client->>WS: Close connection
```

### Error Handling Flow

```mermaid
graph TB
    subgraph "Error Handling Pipeline"
        ERROR[Error Occurs]
        
        subgraph "Error Classification"
            TRANSIENT[Transient Error]
            PERMANENT[Permanent Error]
            PARTIAL[Partial Failure]
        end
        
        subgraph "Recovery Actions"
            RETRY[Retry Logic]
            FALLBACK[Fallback Handler]
            DEGRADE[Degraded Mode]
        end
        
        subgraph "Response"
            SUCCESS[Recovered Response]
            PARTIAL_R[Partial Response]
            ERROR_R[Error Response]
        end
    end
    
    ERROR --> TRANSIENT
    ERROR --> PERMANENT
    ERROR --> PARTIAL
    
    TRANSIENT --> RETRY
    PERMANENT --> FALLBACK
    PARTIAL --> DEGRADE
    
    RETRY --> SUCCESS
    FALLBACK --> PARTIAL_R
    DEGRADE --> PARTIAL_R
    PERMANENT --> ERROR_R
    
    style SUCCESS fill:#4CAF50
    style PARTIAL_R fill:#FF9800
    style ERROR_R fill:#F44336
```

## Performance Metrics

### Response Time Breakdown

| Operation | P50 | P95 | P99 |
|-----------|-----|-----|-----|
| Cache Hit | 5ms | 10ms | 15ms |
| Embedding Generation | 40ms | 60ms | 80ms |
| Vector Search | 50ms | 80ms | 120ms |
| LLM Response | 200ms | 400ms | 600ms |
| Total (cached) | 8ms | 15ms | 25ms |
| Total (uncached) | 300ms | 500ms | 800ms |

### Optimization Strategies

1. **Aggressive Caching**
   - Cache at every level
   - Predictive cache warming
   - Smart TTL management

2. **Parallel Processing**
   - Concurrent operations where possible
   - Async I/O throughout
   - Resource pooling

3. **Smart Routing**
   - Model selection by complexity
   - Service fallbacks
   - Circuit breaker patterns

4. **Data Minimization**
   - Compact data formats
   - Selective field loading
   - Response streaming

This data flow architecture ensures Query Intelligence maintains sub-100ms response times for cached queries while providing comprehensive code understanding capabilities for complex queries.