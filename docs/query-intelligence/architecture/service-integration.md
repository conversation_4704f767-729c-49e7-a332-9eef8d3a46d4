# Query Intelligence Service Integration Architecture

## Table of Contents
- [Overview](#overview)
- [Internal Service Integration](#internal-service-integration)
- [External Service Integration](#external-service-integration)
- [Integration Patterns](#integration-patterns)
- [Circuit Breaker Implementation](#circuit-breaker-implementation)
- [Error Handling and Fallback Strategies](#error-handling-and-fallback-strategies)
- [Integration Diagrams](#integration-diagrams)

## Overview

Query Intelligence integrates with multiple internal and external services to provide comprehensive code understanding capabilities. This document details the integration architecture, patterns, and resilience mechanisms.

### Integration Landscape

```mermaid
graph TB
    subgraph "Query Intelligence Core"
        QI[Query Intelligence Service]
        CB[Circuit Breaker Manager]
        FC[Fallback Controller]
    end
    
    subgraph "Internal Services"
        AE[Analysis Engine<br/>Rust - Code Parsing]
        PM[Pattern Mining<br/>Python - ML Patterns]
    end
    
    subgraph "External Services"
        subgraph "Google Cloud"
            GENAI[Google GenAI<br/>Gemini Models]
            VERTEX[Vertex AI<br/>Enterprise Models]
        end
        
        subgraph "Third Party"
            PINECONE[Pinecone<br/>Vector Database]
            REDIS[Redis<br/>Cache & Session]
        end
    end
    
    QI --> CB
    CB --> FC
    
    CB --> AE
    CB --> PM
    CB --> GENAI
    CB --> VERTEX
    CB --> PINECONE
    CB --> REDIS
    
    style QI fill:#4CAF50
    style CB fill:#F44336
    style FC fill:#FF9800
```

## Internal Service Integration

### Analysis Engine Integration

The Analysis Engine (Rust-based) provides code parsing, AST analysis, and performance-critical operations.

#### Integration Details

```python
# Client Configuration
class AnalysisEngineClient:
    base_url: str = "http://analysis-engine:8080"
    timeout: httpx.Timeout(30.0, connect=5.0)
    headers: {"Content-Type": "application/json"}
```

#### Key Endpoints

| Endpoint | Method | Purpose | Response Time |
|----------|--------|---------|---------------|
| `/health` | GET | Health check | <10ms |
| `/analysis/{repo_id}` | GET | Get analysis results | <50ms |
| `/embeddings/search` | POST | Search code embeddings | <100ms |
| `/repositories/{repo_id}/metadata` | GET | Repository metadata | <20ms |

#### Integration Flow

```mermaid
sequenceDiagram
    participant QI as Query Intelligence
    participant CB as Circuit Breaker
    participant AE as Analysis Engine
    participant Cache as Redis Cache
    
    QI->>CB: Request analysis
    CB->>CB: Check circuit state
    
    alt Circuit Open
        CB-->>QI: Return cached/fallback
    else Circuit Closed
        CB->>AE: Forward request
        
        alt Success
            AE-->>CB: Analysis results
            CB->>Cache: Store results
            CB-->>QI: Return results
        else Failure
            AE-->>CB: Error
            CB->>CB: Record failure
            CB-->>QI: Fallback response
        end
    end
```

### Pattern Mining Integration

Pattern Mining (Python-based) provides ML-based pattern detection and code quality insights.

#### Integration Details

```python
# Client Configuration
class PatternMiningClient:
    base_url: str = "http://pattern-mining:8082"
    timeout: httpx.Timeout(45.0, connect=5.0)
    max_retries: int = 3
```

#### Key Endpoints

| Endpoint | Method | Purpose | Response Time |
|----------|--------|---------|---------------|
| `/health` | GET | Health check | <10ms |
| `/patterns/detect` | POST | Detect code patterns | <200ms |
| `/patterns/classify` | POST | Classify patterns | <150ms |
| `/recommendations` | POST | Get improvement recommendations | <300ms |

#### Pattern Detection Flow

```mermaid
graph LR
    subgraph "Pattern Detection Pipeline"
        CHUNKS[Code Chunks]
        DETECT[Pattern Detector]
        CLASS[Pattern Classifier]
        REC[Recommendation Engine]
        INSIGHTS[Pattern Insights]
    end
    
    CHUNKS --> DETECT
    DETECT --> CLASS
    CLASS --> REC
    REC --> INSIGHTS
    
    style DETECT fill:#9C27B0
    style REC fill:#4CAF50
```

### Communication Protocols

Both internal services use REST APIs with the following standards:

1. **Request Format**
   ```json
   {
     "request_id": "uuid",
     "timestamp": "ISO 8601",
     "data": {},
     "metadata": {
       "user_id": "string",
       "repository_id": "string"
     }
   }
   ```

2. **Response Format**
   ```json
   {
     "request_id": "uuid",
     "status": "success|error",
     "data": {},
     "error": null,
     "processing_time_ms": 100
   }
   ```

3. **Error Format**
   ```json
   {
     "error": {
       "code": "ANALYSIS_FAILED",
       "message": "Human-readable error",
       "details": {},
       "trace_id": "uuid"
     }
   }
   ```

## External Service Integration

### Google GenAI SDK Integration

Query Intelligence uses the unified Google GenAI SDK for both Vertex AI and Gemini API access.

#### Model Routing Logic

```mermaid
graph TB
    subgraph "Model Selection"
        QUERY[Query Analysis]
        
        decision{Query Complexity}
        LITE[Gemini 2.5 Flash-Lite<br/>Simple queries]
        FLASH[Gemini 2.5 Flash<br/>Standard queries]
        PRO[Gemini 2.5 Pro<br/>Complex queries]
    end
    
    QUERY --> decision
    decision -->|Simple| LITE
    decision -->|Standard| FLASH
    decision -->|Complex| PRO
    
    style LITE fill:#90EE90
    style FLASH fill:#87CEEB
    style PRO fill:#DDA0DD
```

#### Integration Configuration

```python
class LLMServiceV2:
    # Backend selection
    use_vertex_ai: bool = True  # or False for Gemini API
    
    # Model hierarchy
    model_candidates = [
        "gemini-2.5-flash",      # Primary
        "gemini-2.0-flash-exp",  # Fallback 1
        "gemini-1.5-pro",        # Fallback 2
        "gemini-1.5-flash"       # Fallback 3
    ]
    
    # Generation configs
    configs = {
        "default": GenerateContentConfig(
            temperature=0.3,
            max_output_tokens=2048,
            top_p=0.95
        ),
        "json": GenerateContentConfig(
            temperature=0.1,
            max_output_tokens=1024,
            response_mime_type="application/json"
        ),
        "fast": GenerateContentConfig(
            temperature=0.1,
            max_output_tokens=512
        )
    }
```

#### Model Selection Strategy

| Query Type | Model | Max Tokens | Use Case |
|------------|-------|------------|----------|
| Intent Analysis | Flash-Lite | 512 | Quick classification |
| Code Explanation | Flash | 2048 | Standard responses |
| Complex Analysis | Pro | 4096 | Deep understanding |
| Pattern Insights | Flash | 1024 | Pattern descriptions |

### Pinecone Vector Database Integration

Pinecone provides high-performance vector similarity search for code embeddings.

#### Integration Architecture

```mermaid
graph TB
    subgraph "Vector Operations"
        EMB[Embedding Generator<br/>all-MiniLM-L6-v2]
        
        subgraph "Pinecone Operations"
            UPSERT[Upsert Vectors]
            SEARCH[Similarity Search]
            FILTER[Metadata Filtering]
        end
        
        subgraph "Index Structure"
            INDEX[ccl-code-embeddings<br/>768 dimensions<br/>Cosine similarity]
        end
    end
    
    EMB --> UPSERT
    UPSERT --> INDEX
    
    EMB --> SEARCH
    SEARCH --> INDEX
    SEARCH --> FILTER
    
    style EMB fill:#FF9800
    style INDEX fill:#9C27B0
```

#### Vector Schema

```python
# Embedding format
embedding = {
    "id": "repo_id:file_path:chunk_id",
    "values": [0.1, 0.2, ...],  # 768 dimensions
    "metadata": {
        "repository_id": "string",
        "file_path": "string",
        "language": "string",
        "start_line": 100,
        "end_line": 150,
        "chunk_type": "function|class|module",
        "timestamp": "ISO 8601"
    }
}
```

### Redis Caching Patterns

Redis provides multi-level caching with different TTLs based on data type.

#### Cache Hierarchy

```mermaid
graph LR
    subgraph "Cache Levels"
        L1[Memory Cache<br/>LRU - 100 items<br/>1ms latency]
        L2[Redis Cache<br/>Distributed<br/>10ms latency]
        L3[Source Data<br/>Services/DB<br/>50-200ms latency]
    end
    
    L1 -->|Miss| L2
    L2 -->|Miss| L3
    
    L3 -->|Populate| L2
    L2 -->|Promote| L1
    
    style L1 fill:#90EE90
    style L2 fill:#87CEEB
    style L3 fill:#FFB6C1
```

#### Cache Key Patterns

| Data Type | Key Pattern | TTL | Example |
|-----------|-------------|-----|---------|
| Query Result | `query:v1:{hash}` | 1 hour | `query:v1:abc123` |
| Embedding | `embed:v1:{type}:{hash}` | 24 hours | `embed:v1:query:def456` |
| Session | `session:{user_id}:{session_id}` | 30 minutes | `session:user123:sess456` |
| Rate Limit | `rate:{user_id}:{window}` | 1 minute | `rate:user123:1704067200` |

## Integration Patterns

### Circuit Breaker Pattern

Circuit breakers prevent cascade failures and provide graceful degradation.

#### Implementation

```python
@circuit_breaker(
    name="service_name",
    failure_threshold=3,      # Open after 3 failures
    recovery_timeout=60,      # Try again after 60s
    expected_exception=Exception
)
async def external_service_call():
    # Service call implementation
    pass
```

#### Circuit Breaker States

```mermaid
stateDiagram-v2
    [*] --> Closed
    Closed --> Open: Failure threshold reached
    Open --> HalfOpen: Recovery timeout elapsed
    HalfOpen --> Closed: Success
    HalfOpen --> Open: Failure
    
    Closed: Requests pass through
    Open: Requests fail fast
    HalfOpen: Limited requests allowed
```

### Service Mesh Considerations

While not currently implemented, the architecture supports future service mesh adoption:

1. **Service Discovery**
   - DNS-based discovery via Kubernetes
   - Environment-based configuration
   - Health check propagation

2. **Load Balancing**
   - Client-side load balancing
   - Retry with exponential backoff
   - Request hedging for critical paths

3. **Observability**
   - Distributed tracing headers
   - Metrics collection
   - Structured logging

### Authentication/Authorization Flow

```mermaid
sequenceDiagram
    participant Client
    participant QI as Query Intelligence
    participant Auth as Auth Middleware
    participant Secret as Secret Manager
    participant Service as External Service
    
    Client->>QI: Request + JWT
    QI->>Auth: Validate token
    Auth->>Auth: Verify signature
    
    alt Valid Token
        Auth-->>QI: User context
        QI->>Secret: Get service credentials
        Secret-->>QI: Service API key
        QI->>Service: Authenticated request
        Service-->>QI: Response
        QI-->>Client: Process response
    else Invalid Token
        Auth-->>QI: Unauthorized
        QI-->>Client: 401 Unauthorized
    end
```

## Circuit Breaker Implementation

### Configuration

```python
# Circuit breaker settings per service
CIRCUIT_BREAKER_CONFIG = {
    "analysis_engine": {
        "failure_threshold": 3,
        "recovery_timeout": 30,
        "expected_exceptions": [httpx.HTTPError]
    },
    "pattern_mining": {
        "failure_threshold": 5,
        "recovery_timeout": 60,
        "expected_exceptions": [httpx.HTTPError]
    },
    "llm_service": {
        "failure_threshold": 3,
        "recovery_timeout": 60,
        "expected_exceptions": [Exception]
    }
}
```

### Monitoring

```mermaid
graph TB
    subgraph "Circuit Breaker Monitoring"
        CB[Circuit Breaker]
        
        subgraph "Metrics"
            FAIL[Failure Count]
            SUCCESS[Success Count]
            STATE[Current State]
            LAST[Last State Change]
        end
        
        subgraph "Alerts"
            OPEN_ALERT[Circuit Open Alert]
            RECOVERY[Recovery Alert]
        end
    end
    
    CB --> FAIL
    CB --> SUCCESS
    CB --> STATE
    CB --> LAST
    
    STATE --> OPEN_ALERT
    STATE --> RECOVERY
    
    style CB fill:#F44336
    style OPEN_ALERT fill:#FF5252
```

## Error Handling and Fallback Strategies

### Fallback Hierarchy

```mermaid
graph TD
    subgraph "Fallback Strategy"
        REQ[Request]
        
        decision1{Primary Service}
        decision2{Fallback Service}
        decision3{Cache Available?}
        
        SUCCESS[Success Response]
        FALLBACK[Fallback Response]
        CACHED[Cached Response]
        DEFAULT[Default Response]
    end
    
    REQ --> decision1
    decision1 -->|Success| SUCCESS
    decision1 -->|Failure| decision2
    decision2 -->|Success| FALLBACK
    decision2 -->|Failure| decision3
    decision3 -->|Yes| CACHED
    decision3 -->|No| DEFAULT
    
    style SUCCESS fill:#4CAF50
    style FALLBACK fill:#FF9800
    style CACHED fill:#2196F3
    style DEFAULT fill:#9E9E9E
```

### Service-Specific Fallbacks

| Service | Primary Strategy | Fallback Strategy | Default Response |
|---------|-----------------|-------------------|------------------|
| Analysis Engine | Live analysis | Cached results | Basic parsing |
| Pattern Mining | ML detection | Rule-based patterns | No patterns |
| LLM Service | Gemini Pro | Gemini Flash | Template response |
| Vector Search | Pinecone | Analysis Engine | Keyword search |

### Error Response Format

```json
{
  "status": "degraded",
  "message": "Response generated with limited functionality",
  "degradation": {
    "services_unavailable": ["pattern_mining"],
    "fallback_used": true,
    "confidence_impact": 0.2
  },
  "data": {
    // Partial response data
  }
}
```

## Integration Diagrams

### Service Dependency Diagram

```mermaid
graph TB
    subgraph "Query Intelligence Dependencies"
        QI[Query Intelligence]
        
        subgraph "Critical Dependencies"
            REDIS_CRIT[Redis<br/>Required for operation]
            GENAI_CRIT[GenAI/LLM<br/>Core functionality]
        end
        
        subgraph "Important Dependencies"
            PINECONE_IMP[Pinecone<br/>Enhanced search]
            AE_IMP[Analysis Engine<br/>Code parsing]
        end
        
        subgraph "Optional Dependencies"
            PM_OPT[Pattern Mining<br/>Quality insights]
        end
    end
    
    QI -->|Required| REDIS_CRIT
    QI -->|Required| GENAI_CRIT
    QI -->|Degraded Mode| PINECONE_IMP
    QI -->|Degraded Mode| AE_IMP
    QI -->|Best Effort| PM_OPT
    
    style REDIS_CRIT fill:#F44336
    style GENAI_CRIT fill:#F44336
    style PINECONE_IMP fill:#FF9800
    style AE_IMP fill:#FF9800
    style PM_OPT fill:#4CAF50
```

### Integration Sequence Diagram

```mermaid
sequenceDiagram
    participant C as Client
    participant QI as Query Intelligence
    participant R as Redis
    participant P as Pinecone
    participant AE as Analysis Engine
    participant PM as Pattern Mining
    participant LLM as LLM Service
    
    C->>QI: Query request
    
    par Cache Check
        QI->>R: Check cache
    and Service Health
        QI->>AE: Health check
        QI->>PM: Health check
    end
    
    alt Cache Hit
        R-->>QI: Cached result
        QI-->>C: Quick response
    else Cache Miss
        QI->>LLM: Analyze intent
        LLM-->>QI: Intent
        
        par Vector Search
            QI->>P: Search embeddings
            P-->>QI: Similar chunks
        and Code Analysis
            QI->>AE: Get metadata
            AE-->>QI: Code context
        end
        
        opt Pattern Analysis
            QI->>PM: Detect patterns
            PM-->>QI: Patterns
        end
        
        QI->>LLM: Generate response
        LLM-->>QI: Response
        
        QI->>R: Cache result
        QI-->>C: Complete response
    end
```

### Circuit Breaker State Diagram

```mermaid
stateDiagram-v2
    [*] --> Closed: Initial State
    
    state Closed {
        [*] --> Monitoring
        Monitoring --> Monitoring: Success
        Monitoring --> RecordingFailure: Failure
        RecordingFailure --> Monitoring: failure_count < threshold
        RecordingFailure --> [*]: failure_count >= threshold
    }
    
    Closed --> Open: Threshold Reached
    
    state Open {
        [*] --> Rejecting
        Rejecting --> Rejecting: Reject all calls
        Rejecting --> [*]: Timer expires
    }
    
    Open --> HalfOpen: Recovery Timer
    
    state HalfOpen {
        [*] --> Testing
        Testing --> [*]: Success
        Testing --> [*]: Failure
    }
    
    HalfOpen --> Closed: Test Success
    HalfOpen --> Open: Test Failure
    
    note right of Open: Fast fail all requests
    note right of HalfOpen: Allow limited test traffic
    note right of Closed: Normal operation
```

## Best Practices

1. **Timeout Configuration**
   - Connection timeout: 5 seconds
   - Read timeout: 30 seconds (60s for ML services)
   - Total timeout: Match service SLA

2. **Retry Strategy**
   - Max retries: 3
   - Backoff: Exponential (1s, 2s, 4s)
   - Retry only on transient errors

3. **Health Monitoring**
   - Check dependencies on startup
   - Continuous health monitoring
   - Graceful degradation on failures

4. **Resource Management**
   - Connection pooling
   - Proper cleanup on shutdown
   - Resource limits per service

This integration architecture ensures Query Intelligence maintains high availability and performance even when dependent services experience issues.