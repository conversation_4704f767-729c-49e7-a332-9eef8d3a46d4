# Docker Configuration

This directory contains modular Docker Compose configurations for the CCL platform.

## Structure

- **docker-compose.base.yml**: Core services required for minimal operation
  - PostgreSQL database
  - Redis cache
  - Pattern Mining service

- **docker-compose.dev.yml**: Development services and emulators
  - All CCL services (Analysis Engine, Query Intelligence, Marketplace, etc.)
  - GCP emulators (Spanner, Firestore, Pub/Sub, Storage)
  - API Gateway (nginx)
  - Web frontend

- **docker-compose.monitoring.yml**: Observability stack
  - OpenTelemetry Collector
  - Jaeger (distributed tracing)
  - Prometheus (metrics)
  - Grafana (dashboards)

- **docker-compose.override.yml**: Personal overrides (git-ignored)
  - Use this for local customizations that shouldn't be committed

## Usage

### Using Convenience Scripts

The easiest way to run the services:

```bash
# Minimal setup (postgres, redis, pattern-mining)
../scripts/docker-base.sh up

# Development setup (all services + emulators)
../scripts/docker-dev.sh up

# Full setup (all services + monitoring)
../scripts/docker-full.sh up

# Stop services
../scripts/docker-dev.sh down
```

### Using Docker Compose Directly

```bash
# Base services only
docker-compose -f docker-compose.base.yml up

# Development environment
docker-compose -f docker-compose.base.yml -f docker-compose.dev.yml up

# Full stack with monitoring
docker-compose -f docker-compose.base.yml -f docker-compose.dev.yml -f docker-compose.monitoring.yml up
```

### Common Operations

```bash
# View logs for a specific service
../scripts/docker-dev.sh logs -f pattern-mining

# Restart a service
../scripts/docker-dev.sh restart analysis-engine

# Execute command in a container
../scripts/docker-dev.sh exec postgres psql -U ccl_dev -d ccl_local

# Clean up volumes
../scripts/docker-dev.sh down -v
```

## Service URLs

When running the full development stack:

- **API Gateway**: http://localhost:8000
- **Web Frontend**: http://localhost:3001
- **Individual Services**:
  - Analysis Engine: http://localhost:8001
  - Query Intelligence: http://localhost:8002
  - Pattern Mining: http://localhost:8003
  - Marketplace: http://localhost:8004
  - Collaboration: http://localhost:8005

- **Monitoring**:
  - Jaeger UI: http://localhost:16686
  - Prometheus: http://localhost:9090
  - Grafana: http://localhost:3000 (admin/admin)

- **Databases**:
  - PostgreSQL: localhost:5432
  - Redis: localhost:6379

- **GCP Emulators**:
  - Spanner: localhost:9010
  - Firestore: localhost:8080
  - Pub/Sub: localhost:8085
  - Storage: localhost:4443

## Customization

To customize your local environment without affecting others:

1. Copy the example from `docker-compose.override.yml`
2. Uncomment and modify the settings you need
3. The override file is automatically loaded by docker-compose

Example customizations:
- Change port mappings if you have conflicts
- Add volume mounts for faster rebuilds
- Set environment variables for debugging
- Adjust resource limits