version: '3.8'

services:
  # GCP Emulators
  spanner-emulator:
    image: gcr.io/cloud-spanner-emulator/emulator:latest
    ports:
      - "9010:9010"
      - "9020:9020"
    networks:
      - ccl-network

  firestore-emulator:
    image: google/cloud-sdk:latest
    command: gcloud beta emulators firestore start --host-port=0.0.0.0:8080
    ports:
      - "8080:8080"
    environment:
      FIRESTORE_PROJECT_ID: ccl-local
    networks:
      - ccl-network

  pubsub-emulator:
    image: google/cloud-sdk:latest
    command: gcloud beta emulators pubsub start --host-port=0.0.0.0:8085
    ports:
      - "8085:8085"
    environment:
      PUBSUB_PROJECT_ID: ccl-local
    networks:
      - ccl-network

  storage-emulator:
    image: fsouza/fake-gcs-server:latest
    command: -scheme http -public-host storage.ccl.local:4443
    ports:
      - "4443:4443"
    volumes:
      - ./data/gcs:/data
    networks:
      - ccl-network

  # Additional CCL Services
  analysis-engine:
    build:
      context: ../services/analysis-engine
      dockerfile: Dockerfile.dev
    volumes:
      - ../services/analysis-engine:/app
      - cargo_cache:/usr/local/cargo
      - target_cache:/app/target
    environment:
      RUST_LOG: debug
      DATABASE_URL: ***********************************************/ccl_local
      REDIS_URL: redis://redis:6379
      OTEL_EXPORTER_OTLP_ENDPOINT: http://otel-collector:4317
      SPANNER_EMULATOR_HOST: spanner-emulator:9010
      STORAGE_EMULATOR_HOST: http://storage-emulator:4443
    ports:
      - "8001:8001"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: cargo watch -x run
    networks:
      - ccl-network

  query-intelligence:
    build:
      context: ../services/query-intelligence
      dockerfile: Dockerfile.dev
    volumes:
      - ../services/query-intelligence:/app
      - pip_cache:/root/.cache/pip
    environment:
      PYTHONUNBUFFERED: 1
      DATABASE_URL: ***********************************************/ccl_local
      REDIS_URL: redis://redis:6379
      VERTEX_AI_PROJECT: ccl-local
      VERTEX_AI_LOCATION: us-central1
      GOOGLE_APPLICATION_CREDENTIALS: /app/credentials/dev-key.json
      OTEL_EXPORTER_OTLP_ENDPOINT: http://otel-collector:4317
      SPANNER_EMULATOR_HOST: spanner-emulator:9010
      FIRESTORE_EMULATOR_HOST: firestore-emulator:8080
    ports:
      - "8002:8002"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: python -m uvicorn main:app --reload --host 0.0.0.0 --port 8002
    networks:
      - ccl-network

  marketplace:
    build:
      context: ../services/marketplace
      dockerfile: Dockerfile.dev
    volumes:
      - ../services/marketplace:/app
      - go_cache:/go/pkg/mod
    environment:
      DATABASE_URL: ***********************************************/ccl_local
      SPANNER_EMULATOR_HOST: spanner-emulator:9010
      STORAGE_EMULATOR_HOST: http://storage-emulator:4443
      FIRESTORE_EMULATOR_HOST: firestore-emulator:8080
      STRIPE_SECRET_KEY: sk_test_dummy
      OTEL_EXPORTER_OTLP_ENDPOINT: http://otel-collector:4317
    ports:
      - "8004:8004"
    depends_on:
      postgres:
        condition: service_healthy
      spanner-emulator:
        condition: service_started
    command: air
    networks:
      - ccl-network

  collaboration:
    build:
      context: ../services/collaboration
      dockerfile: Dockerfile.dev
    volumes:
      - ../services/collaboration:/app
      - node_modules_collab:/app/node_modules
    environment:
      NODE_ENV: development
      DATABASE_URL: ***********************************************/ccl_local
      REDIS_URL: redis://redis:6379
      FIRESTORE_EMULATOR_HOST: firestore-emulator:8080
      OTEL_EXPORTER_OTLP_ENDPOINT: http://otel-collector:4317
    ports:
      - "8005:8005"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: npm run dev
    networks:
      - ccl-network

  web:
    build:
      context: ../services/web
      dockerfile: Dockerfile.dev
    volumes:
      - ../services/web:/app
      - node_modules_web:/app/node_modules
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:8000
      NEXT_PUBLIC_WS_URL: ws://localhost:8005
    ports:
      - "3001:3000"
    command: npm run dev
    networks:
      - ccl-network

  # API Gateway (nginx)
  api-gateway:
    image: nginx:alpine
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "8000:80"
    depends_on:
      - analysis-engine
      - query-intelligence
      - pattern-mining
      - marketplace
    networks:
      - ccl-network

volumes:
  cargo_cache:
  target_cache:
  go_cache:
  node_modules_collab:
  node_modules_web:

networks:
  ccl-network:
    external: true
    name: ccl-dev-network