# Main Docker Compose Configuration
# This file orchestrates the modular compose files for the CCL platform
#
# Usage:
#   - Basic services only: docker-compose -f docker-compose.base.yml up
#   - Development setup: docker-compose -f docker-compose.base.yml -f docker-compose.dev.yml up
#   - Full stack with monitoring: docker-compose -f docker-compose.base.yml -f docker-compose.dev.yml -f docker-compose.monitoring.yml up
#
# Or use the convenience scripts:
#   - ./scripts/docker-dev.sh up    # Runs base + dev
#   - ./scripts/docker-full.sh up   # Runs base + dev + monitoring
#
# Individual files:
#   - docker-compose.base.yml: Core services (postgres, redis, pattern-mining)
#   - docker-compose.dev.yml: Development services (emulators, all CCL services)
#   - docker-compose.monitoring.yml: Observability stack (jaeger, prometheus, grafana)
#   - docker-compose.override.yml: Personal overrides (not committed)

version: '3.8'

# This file is intentionally minimal.
# Use the modular files above for actual service definitions.