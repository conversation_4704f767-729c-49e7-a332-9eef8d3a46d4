version: '3.8'

services:
  # Core Infrastructure
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: ccl_dev
      POSTGRES_PASSWORD: dev_password
      POSTGRES_DB: ccl_local
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts/postgres:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ccl_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - ccl-network

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - ccl-network

  # Essential CCL Service
  pattern-mining:
    build:
      context: ../services/pattern-mining
      dockerfile: Dockerfile.dev
    volumes:
      - ../services/pattern-mining:/app
      - pip_cache:/root/.cache/pip
    environment:
      PYTHONUNBUFFERED: 1
      DATABASE_URL: ***********************************************/ccl_local
      BIGQUERY_PROJECT: ccl-local
      BIGQUERY_DATASET: patterns
      GOOGLE_APPLICATION_CREDENTIALS: /app/credentials/dev-key.json
      OTEL_EXPORTER_OTLP_ENDPOINT: http://otel-collector:4317
      SPANNER_EMULATOR_HOST: spanner-emulator:9010
      STORAGE_EMULATOR_HOST: http://storage-emulator:4443
    ports:
      - "8003:8003"
    depends_on:
      postgres:
        condition: service_healthy
    command: python -m uvicorn main:app --reload --host 0.0.0.0 --port 8003
    networks:
      - ccl-network

volumes:
  postgres_data:
  redis_data:
  pip_cache:

networks:
  ccl-network:
    name: ccl-dev-network
    driver: bridge