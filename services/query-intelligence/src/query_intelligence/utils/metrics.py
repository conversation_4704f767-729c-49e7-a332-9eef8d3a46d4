import asyncio
import time
from functools import wraps
from typing import Callable, TypeVar, Optional, Union, Coroutine, Any
from prometheus_client import Counter, Histogram, Gauge
import structlog

logger = structlog.get_logger()

# Define metrics
query_counter = Counter(
    "query_intelligence_queries_total",
    "Total number of queries processed",
    ["intent", "status"],
)

query_duration = Histogram(
    "query_intelligence_query_duration_seconds",
    "Query processing duration in seconds",
    ["intent"],
)

active_queries = Gauge(
    "query_intelligence_active_queries", "Number of queries currently being processed"
)

cache_hits = Counter(
    "query_intelligence_cache_hits_total", "Total number of cache hits"
)

cache_misses = Counter(
    "query_intelligence_cache_misses_total", "Total number of cache misses"
)

embedding_generation_duration = Histogram(
    "query_intelligence_embedding_duration_seconds",
    "Embedding generation duration in seconds",
)

llm_requests = Counter(
    "query_intelligence_llm_requests_total",
    "Total number of LLM requests",
    ["model", "status"],
)

llm_token_usage = Counter(
    "query_intelligence_llm_tokens_total",
    "Total number of tokens used",
    ["model", "token_type"],
)

search_results = Histogram(
    "query_intelligence_search_results_count",
    "Number of search results returned",
    ["repository_id"],
)

response_confidence = Histogram(
    "query_intelligence_response_confidence",
    "Confidence scores of generated responses",
    ["intent"],
)


T = TypeVar("T")


def track_processing_time(metric_name: Optional[str] = None):
    """Decorator to track function execution time"""

    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        async def async_wrapper(*args, **kwargs) -> T:
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                if metric_name:
                    logger.info(f"{metric_name}_duration", duration_seconds=duration)
                return result
            except Exception as e:
                duration = time.time() - start_time
                if metric_name:
                    logger.error(
                        f"{metric_name}_failed", duration_seconds=duration, error=str(e)
                    )
                raise

        @wraps(func)
        def sync_wrapper(*args, **kwargs) -> T:
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                if metric_name:
                    logger.info(f"{metric_name}_duration", duration_seconds=duration)
                return result
            except Exception as e:
                duration = time.time() - start_time
                if metric_name:
                    logger.error(
                        f"{metric_name}_failed", duration_seconds=duration, error=str(e)
                    )
                raise

        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


class MetricsCollector:
    """Utility class for collecting and reporting metrics"""

    @staticmethod
    def record_query(intent: str, status: str = "success"):
        """Record a query with its intent and status"""
        query_counter.labels(intent=intent, status=status).inc()

    @staticmethod
    def record_query_duration(intent: str, duration_seconds: float):
        """Record query processing duration"""
        query_duration.labels(intent=intent).observe(duration_seconds)

    @staticmethod
    def set_active_queries(count: int):
        """Set the number of active queries"""
        active_queries.set(count)

    @staticmethod
    def record_cache_hit(cache_type: str = "general"):
        """Record a cache hit"""
        cache_hits.inc()

    @staticmethod
    def record_cache_miss(cache_type: str = "general"):
        """Record a cache miss"""
        cache_misses.inc()

    @staticmethod
    def record_embedding_duration(duration_seconds: float):
        """Record embedding generation duration"""
        embedding_generation_duration.observe(duration_seconds)

    @staticmethod
    def record_llm_request(model: str, status: str = "success"):
        """Record an LLM request"""
        llm_requests.labels(model=model, status=status).inc()

    @staticmethod
    def record_llm_tokens(model: str, prompt_tokens: int, completion_tokens: int):
        """Record LLM token usage"""
        llm_token_usage.labels(model=model, token_type="prompt").inc(prompt_tokens)
        llm_token_usage.labels(model=model, token_type="completion").inc(
            completion_tokens
        )

    @staticmethod
    def record_search_results(repository_id: str, count: int):
        """Record number of search results"""
        search_results.labels(repository_id=repository_id).observe(count)

    @staticmethod
    def record_response_confidence(intent: str, confidence: float):
        """Record response confidence score"""
        response_confidence.labels(intent=intent).observe(confidence)
