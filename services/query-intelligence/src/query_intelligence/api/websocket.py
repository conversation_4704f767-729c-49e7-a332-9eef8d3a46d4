import json
from fastapi import <PERSON><PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, Depends
from fastapi.exceptions import WebSocketException
import structlog
from typing import Optional

from ..models import QueryRequest, QueryStreamChunk
from ..models.response import GeneratedResponse
from ..services import get_query_processor
from ..middleware.auth import jwt_auth

logger = structlog.get_logger()
router = APIRouter()


async def websocket_auth(websocket: WebSocket) -> Optional[str]:
    """Secure WebSocket authentication using Authorization header"""
    headers = websocket.headers
    auth_header = headers.get("authorization")
    
    if not auth_header:
        return None
    
    # Extract token from "Bearer <token>" format
    if not auth_header.startswith("Bearer "):
        return None
    
    token = auth_header[7:]  # Remove "Bearer " prefix
    
    try:
        payload = jwt_auth.verify_token(token)
        user_id = payload.get("sub")
        logger.info("websocket_authenticated", user_id=user_id)
        return user_id
    except Exception as e:
        logger.warning("websocket_auth_failed", error=str(e))
        return None


@router.websocket("/ws/query")
async def websocket_query_endpoint(
    websocket: WebSocket,
    query_processor=Depends(get_query_processor),
):
    """WebSocket endpoint for streaming query responses
    
    Authentication: Pass JWT token in Authorization header: "Bearer <jwt_token>"
    """
    await websocket.accept()
    
    # Validate token using secure header-based authentication
    user_id = await websocket_auth(websocket)
    
    if user_id is None:
        await websocket.send_json({
            "type": "error",
            "message": "Authentication required. Please provide valid Bearer token in Authorization header.",
            "code": "AUTH_REQUIRED"
        })
        await websocket.close(code=1008)  # Policy Violation
        return

    try:
        while True:
            # Receive query data
            data = await websocket.receive_text()

            try:
                # Parse the request
                request_data = json.loads(data)
                # Add user_id if authenticated
                if user_id and "user_id" not in request_data:
                    request_data["user_id"] = user_id
                    
                request = QueryRequest(**request_data)

                logger.info(
                    "websocket_query_received",
                    query=request.query,
                    repository_id=request.repository_id,
                    user_id=user_id,
                )

                # Send acknowledgment
                await websocket.send_json(
                    {"type": "acknowledged", "query": request.query}
                )

                # Process the query with streaming
                await stream_query_response(websocket, request, query_processor)

            except json.JSONDecodeError:
                await websocket.send_json(
                    {"type": "error", "message": "Invalid JSON format"}
                )
            except Exception as e:
                logger.error("websocket_query_error", error=str(e), exc_info=True)
                await websocket.send_json(
                    {"type": "error", "message": f"Query processing error: {str(e)}"}
                )

    except WebSocketDisconnect:
        logger.info("websocket_disconnected")
    except Exception as e:
        logger.error("websocket_error", error=str(e), exc_info=True)
        await websocket.close()


async def stream_query_response(
    websocket: WebSocket, request: QueryRequest, query_processor
):
    """Stream query response chunks over WebSocket"""

    try:
        # Start processing notification
        await websocket.send_json(
            {"type": "processing_started", "message": "Analyzing your query..."}
        )

        # Analyze intent
        context = query_processor._create_context(request)
        intent_analysis = await query_processor._analyze_intent(request.query, context)

        # Send intent analysis
        await websocket.send_json(
            {
                "type": "intent_analyzed",
                "intent": intent_analysis.primary_intent.value,
                "confidence": intent_analysis.confidence,
            }
        )

        # Generate embedding and search
        await websocket.send_json(
            {"type": "status", "message": "Searching codebase..."}
        )

        query_embedding = await query_processor.semantic_search.generate_embedding(
            request.query, context_type="query"
        )

        search_results = await query_processor.semantic_search.search(
            embedding=query_embedding,
            repository_id=context.repository_id,
            filters=query_processor._build_search_filters(intent_analysis, context),
            limit=20,
        )

        # Send search results summary
        await websocket.send_json(
            {
                "type": "search_complete",
                "results_found": len(search_results.chunks),
                "search_time_ms": search_results.search_time_ms,
            }
        )

        # Rerank chunks
        ranked_chunks = await query_processor._rerank_chunks(
            request.query, intent_analysis, search_results.chunks
        )

        # Extract and send references
        references = query_processor._extract_references(ranked_chunks[:5])
        for ref in references:
            chunk = QueryStreamChunk(type="reference", reference=ref, done=False)
            await websocket.send_json(chunk.model_dump())

        # Stream the response generation
        await websocket.send_json(
            {"type": "status", "message": "Generating response..."}
        )

        # Use streaming response from LLM
        llm_service = query_processor.llm_service
        response_text = ""

        async for text_chunk in llm_service.stream_response(
            request.query, intent_analysis, ranked_chunks[:10], context
        ):
            response_text += text_chunk
            chunk = QueryStreamChunk(type="text", content=text_chunk, done=False)
            await websocket.send_json(chunk.model_dump())

        # Calculate confidence based on the streaming response
        # Use a simplified confidence calculation for streaming
        base_confidence = 0.7

        # Add chunk quality boost (up to 15%)
        if ranked_chunks:
            avg_chunk_score = sum(c.combined_score for c in ranked_chunks[:3]) / min(
                3, len(ranked_chunks)
            )
            base_confidence += 0.15 * avg_chunk_score

        # Add intent confidence boost (up to 10%)
        base_confidence += 0.1 * intent_analysis.confidence

        # Response quality adjustments
        if len(response_text) < 100:
            base_confidence *= 0.8

        # Check for uncertainty phrases
        uncertainty_phrases = ["i'm not sure", "might be", "possibly", "unclear"]
        if any(phrase in response_text.lower() for phrase in uncertainty_phrases):
            base_confidence *= 0.9

        # Cap at 0.95
        confidence = min(base_confidence, 0.95)

        # Create a proper GeneratedResponse object for follow-up generation
        generated_response = GeneratedResponse(
            text=response_text,
            confidence=confidence,
            model_used="streaming-model",  # We don't track the exact model in streaming
            generation_time_ms=None,  # Not tracked for streaming
        )

        # Generate follow-up questions
        follow_ups = await query_processor._generate_follow_ups(
            request.query,
            generated_response,
            context,
        )

        # Send completion with metadata
        chunk = QueryStreamChunk(
            type="done",
            done=True,
            metadata={
                "intent": intent_analysis.primary_intent.value,
                "confidence": confidence,
                "follow_up_questions": follow_ups,
                "total_chunks": len(search_results.chunks),
                "chunks_used": len(ranked_chunks[:10]),
            },
        )
        await websocket.send_json(chunk.model_dump())

    except Exception as e:
        logger.error("streaming_error", error=str(e), exc_info=True)
        await websocket.send_json(
            {"type": "error", "message": f"Streaming error: {str(e)}"}
        )
