"""
Admin dashboard API endpoints for monitoring and management
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, Field
import structlog

from ..middleware.auth import jwt_required, require_admin
from ..clients.redis import get_redis_client
from ..services.cache_manager import get_cache_manager
from ..utils.circuit_breaker import get_circuit_breaker_status
from ..config.settings import get_settings

logger = structlog.get_logger()
router = APIRouter(prefix="/admin", tags=["admin"])
settings = get_settings()


class SystemMetrics(BaseModel):
    """System-wide metrics"""
    total_queries: int
    cache_hit_rate: float
    average_response_time_ms: float
    active_users: int
    queries_per_minute: float
    error_rate: float
    

class ServiceHealth(BaseModel):
    """Health status of services"""
    redis: str
    analysis_engine: str
    pattern_mining: str
    llm_service: str
    circuit_breakers: Dict[str, str]
    

class QueryStats(BaseModel):
    """Query statistics"""
    top_queries: List[Dict[str, Any]]
    query_intents: Dict[str, int]
    language_distribution: Dict[str, int]
    average_confidence: float
    

class CacheStats(BaseModel):
    """Cache statistics"""
    memory_cache_size: int
    memory_hit_rate: float
    redis_keys: int
    redis_memory_mb: float
    hot_queries: List[str]


@router.get("/metrics", response_model=SystemMetrics, dependencies=[Depends(require_admin)])
async def get_system_metrics():
    """Get system-wide metrics"""
    redis_client = get_redis_client()
    
    try:
        # Get metrics from Redis
        total_queries = await redis_client.get("metrics:total_queries") or 0
        cache_hits = await redis_client.get("metrics:cache_hits") or 0
        cache_misses = await redis_client.get("metrics:cache_misses") or 0
        total_response_time = await redis_client.get("metrics:total_response_time") or 0
        error_count = await redis_client.get("metrics:errors") or 0
        
        # Calculate rates
        cache_total = int(cache_hits) + int(cache_misses)
        cache_hit_rate = float(cache_hits) / cache_total if cache_total > 0 else 0.0
        
        avg_response_time = float(total_response_time) / int(total_queries) if int(total_queries) > 0 else 0.0
        
        # Get active users (unique users in last hour)
        active_users = await redis_client.scard("active_users:hour") or 0
        
        # Get QPM (queries per minute) from last 5 minutes
        now = datetime.utcnow()
        qpm_count = 0
        for i in range(5):
            minute_key = (now - timedelta(minutes=i)).strftime("queries:minute:%Y%m%d%H%M")
            count = await redis_client.get(minute_key) or 0
            qpm_count += int(count)
        queries_per_minute = qpm_count / 5.0
        
        # Calculate error rate
        error_rate = float(error_count) / int(total_queries) if int(total_queries) > 0 else 0.0
        
        return SystemMetrics(
            total_queries=int(total_queries),
            cache_hit_rate=cache_hit_rate,
            average_response_time_ms=avg_response_time,
            active_users=int(active_users),
            queries_per_minute=queries_per_minute,
            error_rate=error_rate
        )
        
    except Exception as e:
        logger.error("metrics_retrieval_error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve metrics"
        )


@router.get("/health", response_model=ServiceHealth, dependencies=[Depends(require_admin)])
async def get_service_health():
    """Get health status of all services"""
    redis_client = get_redis_client()
    
    # Check Redis
    try:
        await redis_client.ping()
        redis_status = "healthy"
    except Exception:
        redis_status = "unhealthy"
    
    # Get service statuses from health checks
    analysis_status = await redis_client.get("health:analysis_engine") or "unknown"
    pattern_status = await redis_client.get("health:pattern_mining") or "unknown"
    llm_status = await redis_client.get("health:llm_service") or "unknown"
    
    # Get circuit breaker status
    cb_status = get_circuit_breaker_status()
    cb_summary = {}
    for name, status in cb_status.items():
        cb_summary[name] = status["state"]
    
    return ServiceHealth(
        redis=redis_status,
        analysis_engine=analysis_status,
        pattern_mining=pattern_status,
        llm_service=llm_status,
        circuit_breakers=cb_summary
    )


@router.get("/queries/stats", response_model=QueryStats, dependencies=[Depends(require_admin)])
async def get_query_statistics():
    """Get query statistics"""
    redis_client = get_redis_client()
    
    try:
        # Get top queries
        top_queries_data = await redis_client.zrevrange(
            "queries:popular", 0, 9, withscores=True
        )
        top_queries = [
            {"query": query, "count": int(score)}
            for query, score in top_queries_data
        ]
        
        # Get intent distribution
        intents = ["EXPLAIN", "FIND", "DEBUG", "ANALYZE", "COMPARE", "UNKNOWN"]
        intent_counts = {}
        for intent in intents:
            count = await redis_client.get(f"queries:intent:{intent}") or 0
            intent_counts[intent] = int(count)
        
        # Get language distribution
        languages = await redis_client.hgetall("queries:languages")
        language_dist = {lang: int(count) for lang, count in languages.items()}
        
        # Get average confidence
        total_confidence = await redis_client.get("metrics:total_confidence") or 0
        total_queries = await redis_client.get("metrics:total_queries") or 1
        avg_confidence = float(total_confidence) / int(total_queries)
        
        return QueryStats(
            top_queries=top_queries,
            query_intents=intent_counts,
            language_distribution=language_dist,
            average_confidence=avg_confidence
        )
        
    except Exception as e:
        logger.error("query_stats_error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve query statistics"
        )


@router.get("/cache/stats", response_model=CacheStats, dependencies=[Depends(require_admin)])
async def get_cache_statistics():
    """Get cache statistics"""
    redis_client = get_redis_client()
    cache_manager = get_cache_manager()
    
    try:
        # Get memory cache stats
        memory_size = len(cache_manager._query_memory_cache) + len(cache_manager._embedding_memory_cache)
        memory_hits = await redis_client.get("cache:memory:hits") or 0
        memory_total = await redis_client.get("cache:memory:total") or 1
        memory_hit_rate = float(memory_hits) / float(memory_total)
        
        # Get Redis stats
        redis_info = await redis_client.info()
        redis_keys = redis_info.get("db0", {}).get("keys", 0)
        redis_memory = redis_info.get("used_memory", 0) / (1024 * 1024)  # Convert to MB
        
        # Get hot queries
        hot_queries = list(cache_manager._access_counts.keys())[:10]
        
        return CacheStats(
            memory_cache_size=memory_size,
            memory_hit_rate=memory_hit_rate,
            redis_keys=redis_keys,
            redis_memory_mb=redis_memory,
            hot_queries=hot_queries
        )
        
    except Exception as e:
        logger.error("cache_stats_error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve cache statistics"
        )


@router.post("/cache/clear", dependencies=[Depends(require_admin)])
async def clear_cache(cache_type: str = "all"):
    """Clear cache (all, memory, or redis)"""
    cache_manager = get_cache_manager()
    
    try:
        if cache_type in ["all", "memory"]:
            cache_manager._init_memory_cache()
            logger.info("memory_cache_cleared")
            
        if cache_type in ["all", "redis"]:
            redis_client = get_redis_client()
            await redis_client.flushdb()
            logger.info("redis_cache_cleared")
            
        return {"status": "success", "cleared": cache_type}
        
    except Exception as e:
        logger.error("cache_clear_error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clear cache"
        )


@router.post("/circuit-breakers/reset", dependencies=[Depends(require_admin)])
async def reset_circuit_breaker(breaker_name: Optional[str] = None):
    """Reset circuit breakers"""
    from ..utils.circuit_breaker import reset_circuit_breaker
    
    try:
        if breaker_name:
            reset_circuit_breaker(breaker_name)
            logger.info("circuit_breaker_reset", name=breaker_name)
            return {"status": "success", "reset": breaker_name}
        else:
            # Reset all breakers
            cb_status = get_circuit_breaker_status()
            for name in cb_status.keys():
                reset_circuit_breaker(name)
            logger.info("all_circuit_breakers_reset")
            return {"status": "success", "reset": "all"}
            
    except Exception as e:
        logger.error("circuit_breaker_reset_error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reset circuit breaker"
        )


@router.get("/config")
async def get_configuration(_: Dict = Depends(require_admin)):
    """Get current configuration (sanitized)"""
    return {
        "environment": settings.ENVIRONMENT,
        "service_name": settings.SERVICE_NAME,
        "port": settings.PORT,
        "cache_ttl_hours": settings.CACHE_TTL_HOURS,
        "rate_limit_requests": settings.RATE_LIMIT_REQUESTS,
        "rate_limit_window": settings.RATE_LIMIT_WINDOW_SECONDS,
        "max_query_length": settings.MAX_QUERY_LENGTH,
        "query_timeout_seconds": settings.QUERY_TIMEOUT_SECONDS,
        "llm_model": settings.GEMINI_MODEL_NAME,
        "embedding_model": settings.EMBEDDING_MODEL_NAME,
        "enable_metrics": settings.ENABLE_METRICS,
        "enable_input_validation": settings.ENABLE_INPUT_VALIDATION,
        "circuit_breaker_config": {
            "failure_threshold": settings.CIRCUIT_BREAKER_FAILURE_THRESHOLD,
            "recovery_timeout": settings.CIRCUIT_BREAKER_RECOVERY_TIMEOUT,
            "success_threshold": 2  # Default success threshold
        }
    }