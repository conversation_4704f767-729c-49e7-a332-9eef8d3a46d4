from typing import Optional, Dict, Any, List
from fastapi import APIRouter, Depends, Request
from pydantic import BaseModel, Field
from ..models.query import QueryRequest, QueryResult, IntentAnalysis
from ..services.query_processor import QueryProcessor, get_query_processor
from ..services.query_optimizer import get_query_optimizer, QueryOptimization
from ..middleware.auth import get_optional_user
from ..middleware.rate_limit import create_rate_limit_dependency

router = APIRouter()

# Create endpoint-specific rate limiter
query_rate_limit = create_rate_limit_dependency(
    requests=50, window=60, key_prefix="query_endpoint"  # 50 requests  # per minute
)


@router.post("/query", response_model=QueryResult)
async def process_query(
    request: QueryRequest,
    req: Request,
    query_processor: QueryProcessor = Depends(get_query_processor),
    current_user: Optional[Dict[str, Any]] = Depends(get_optional_user),
    _: None = Depends(query_rate_limit),
):
    # Add user info to request if authenticated
    if current_user:
        request.user_id = current_user.get("user_id")

    # Store user in request state for rate limiting
    req.state.user = current_user

    return await query_processor.process_query(request)


class OptimizationRequest(BaseModel):
    """Request for query optimization hints"""
    query: str = Field(..., min_length=1, max_length=1000)
    repository_id: str
    previous_confidence: Optional[float] = Field(None, ge=0.0, le=1.0)
    previous_results_count: Optional[int] = None


class OptimizationResponse(BaseModel):
    """Response with optimization hints"""
    optimizations: List[Dict[str, Any]]
    quality_score: float = Field(ge=0.0, le=1.0)
    optimized_query: Optional[str] = None


@router.post("/query/optimize", response_model=OptimizationResponse)
async def optimize_query(
    request: OptimizationRequest,
    req: Request,
    query_processor: QueryProcessor = Depends(get_query_processor),
    current_user: Optional[Dict[str, Any]] = Depends(get_optional_user),
    _: None = Depends(query_rate_limit),
):
    """Get optimization hints for improving query results"""
    optimizer = get_query_optimizer()
    
    # Analyze intent first
    context = query_processor._create_context(
        QueryRequest(
            query=request.query,
            repository_id=request.repository_id,
            user_id=current_user.get("user_id") if current_user else None
        )
    )
    intent_analysis = await query_processor._analyze_intent(request.query, context)
    
    # Build previous results context if provided
    previous_results = None
    if request.previous_confidence is not None or request.previous_results_count is not None:
        previous_results = {
            "confidence": request.previous_confidence,
            "total_results": request.previous_results_count or 0
        }
    
    # Get optimization hints
    optimizations = optimizer.analyze_query(
        request.query,
        intent_analysis,
        previous_results
    )
    
    # Calculate quality score
    quality_score = optimizer.get_query_quality_score(request.query, intent_analysis)
    
    # Generate optimized query if score is low
    optimized_query = None
    if quality_score < 0.7 and optimizations:
        optimized_query = optimizer.generate_optimized_query(
            request.query,
            optimizations[:2]  # Use top 2 optimizations
        )
    
    return OptimizationResponse(
        optimizations=[opt.__dict__ for opt in optimizations],
        quality_score=quality_score,
        optimized_query=optimized_query
    )
