from pydantic import BaseModel, <PERSON>
from typing import List, Optional
import numpy as np


class EmbeddingVector(BaseModel):
    """Represents an embedding vector"""

    values: List[float]
    dimension: int = Field(ge=1)

    class Config:
        arbitrary_types_allowed = True

    def to_numpy(self) -> np.ndarray:
        """Convert to numpy array"""
        return np.array(self.values)

    @classmethod
    def from_numpy(cls, array: np.ndarray) -> "EmbeddingVector":
        """Create from numpy array"""
        return cls(values=array.tolist(), dimension=len(array))


class CodeChunk(BaseModel):
    """Represents a chunk of code with metadata"""

    file_path: str
    start_line: int
    end_line: int
    content: str
    language: str
    embedding: Optional[EmbeddingVector] = None
    similarity_score: float = Field(default=0.0, ge=0.0, le=1.0)
    recency_score: float = Field(default=0.0, ge=0.0, le=1.0)
    combined_score: float = Field(default=0.0, ge=0.0, le=1.0)
    metadata: dict = Field(default_factory=dict)


class SearchResult(BaseModel):
    """Result from semantic search"""

    chunks: List[CodeChunk]
    total_results: int
    search_time_ms: float
    query_embedding: Optional[EmbeddingVector] = None
