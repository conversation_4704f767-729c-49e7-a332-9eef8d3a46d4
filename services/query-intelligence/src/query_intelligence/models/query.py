from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from enum import Enum


class QueryIntent(str, Enum):
    """Types of query intents"""

    EXPLAIN = "explain"
    FIND = "find"
    DEBUG = "debug"
    REFACTOR = "refactor"
    ANALYZE = "analyze"
    COMPARE = "compare"
    UNKNOWN = "unknown"


class CodeReference(BaseModel):
    """Reference to a specific code location"""

    file_path: str
    start_line: int
    end_line: int
    snippet: str
    relevance_score: float = Field(ge=0.0, le=1.0)
    language: Optional[str] = None


class QueryRequest(BaseModel):
    """Request model for query processing"""

    query: str = Field(..., min_length=1, max_length=10000)
    repository_id: str
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    context_history: Optional[List[Dict[str, Any]]] = Field(default_factory=list)
    filters: Optional[Dict[str, Any]] = Field(default_factory=dict)
    stream: bool = False


class QueryResult(BaseModel):
    """Result model for query processing"""

    answer: str
    intent: QueryIntent
    confidence: float = Field(ge=0.0, le=1.0)
    references: List[CodeReference] = Field(default_factory=list)
    execution_time_ms: float
    follow_up_questions: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class QueryStreamChunk(BaseModel):
    """Chunk for streaming query responses"""

    type: str  # 'text', 'reference', 'metadata', 'done'
    content: Optional[str] = None
    reference: Optional[CodeReference] = None
    metadata: Optional[Dict[str, Any]] = None
    done: bool = False


class QueryContext(BaseModel):
    """Context for query processing"""

    repository_id: str
    user_id: str
    session_id: Optional[str] = None
    history: List[Dict[str, Any]] = Field(default_factory=list)
    filters: Dict[str, Any] = Field(default_factory=dict)


class IntentAnalysis(BaseModel):
    """Analysis of query intent"""

    primary_intent: QueryIntent
    code_elements: List[str] = Field(default_factory=list)
    scope: str = "repository"  # file, module, repository
    context_depth: str = "normal"  # shallow, normal, deep
    confidence: float = Field(ge=0.0, le=1.0)
