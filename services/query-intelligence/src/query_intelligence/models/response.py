from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime


class GeneratedResponse(BaseModel):
    """Response generated by LLM"""

    text: str
    confidence: float = Field(ge=0.0, le=1.0)
    model_used: str
    prompt_tokens: Optional[int] = None
    completion_tokens: Optional[int] = None
    generation_time_ms: Optional[float] = None


class CachedResponse(BaseModel):
    """Cached query response"""

    query_hash: str
    result: Dict[str, Any]
    created_at: datetime
    expires_at: datetime
    hit_count: int = 0


class AnalysisMetrics(BaseModel):
    """Metrics for query analysis"""

    intent_confidence: float = Field(ge=0.0, le=1.0)
    code_coverage: float = Field(ge=0.0, le=1.0)
    response_quality: float = Field(ge=0.0, le=1.0)
    semantic_relevance: float = Field(ge=0.0, le=1.0)
