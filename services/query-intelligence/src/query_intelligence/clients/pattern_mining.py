"""
Client for Pattern Mining Service integration
Provides pattern detection, classification, and recommendation capabilities
"""

import httpx
from functools import lru_cache
from typing import List, Dict, Optional, Any
import structlog

from ..config.settings import get_settings
from ..utils.circuit_breaker import circuit_breaker, register_circuit_breaker

logger = structlog.get_logger()
settings = get_settings()


class PatternMiningClient:
    """Client for interacting with the Pattern Mining service"""

    def __init__(self, base_url: Optional[str] = None):
        self.base_url = (
            base_url or settings.PATTERN_MINING_URL or "http://pattern-mining:8003"
        )
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            timeout=httpx.Timeout(30.0, connect=5.0),
            headers={"Content-Type": "application/json"},
        )

    @circuit_breaker(
        name="pattern_mining_detect",
        failure_threshold=3,
        recovery_timeout=60,
        expected_exception=httpx.HTTPError,
    )
    async def detect_patterns(
        self,
        code_chunks: List[Dict[str, Any]],
        context: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Detect patterns in code chunks

        Args:
            code_chunks: List of code chunks with AST data
            context: Optional context for pattern detection

        Returns:
            Detected patterns with confidence scores
        """
        try:
            payload = {
                "code_chunks": code_chunks,
                "context": context or {},
                "detection_mode": "comprehensive",
            }

            response = await self.client.post("/patterns/detect", json=payload)
            response.raise_for_status()

            result = response.json()
            logger.info(
                "patterns_detected",
                pattern_count=len(result.get("patterns", [])),
                chunks_analyzed=len(code_chunks),
            )

            return result

        except httpx.ConnectError as e:
            logger.error(
                "pattern_detection_failed",
                error="Connection refused",
                chunks_count=len(code_chunks),
                details=str(e),
            )
            raise
        except httpx.TimeoutException as e:
            logger.error(
                "pattern_detection_failed",
                error="Request timeout",
                chunks_count=len(code_chunks),
                details=str(e),
            )
            raise
        except httpx.HTTPStatusError as e:
            logger.error(
                "pattern_detection_failed",
                error=f"HTTP {e.response.status_code}",
                chunks_count=len(code_chunks),
                details=e.response.text,
            )
            raise
        except httpx.HTTPError:
            logger.error(
                "pattern_detection_failed",
                error="HTTP error",
                chunks_count=len(code_chunks),
                exc_info=True,
            )
            raise

    @circuit_breaker(
        name="pattern_mining_classify",
        failure_threshold=3,
        recovery_timeout=60,
        expected_exception=httpx.HTTPError,
    )
    async def classify_patterns(
        self,
        patterns: List[Dict[str, Any]],
        classification_type: str = "design_pattern",
    ) -> Dict[str, Any]:
        """
        Classify detected patterns into categories

        Args:
            patterns: List of detected patterns
            classification_type: Type of classification (design_pattern, anti_pattern, etc.)

        Returns:
            Classified patterns with categories and confidence
        """
        try:
            payload = {"patterns": patterns, "classification_type": classification_type}

            response = await self.client.post("/patterns/classify", json=payload)
            response.raise_for_status()

            return response.json()

        except httpx.HTTPError as e:
            logger.error(
                "pattern_classification_failed",
                error=str(e),
                pattern_count=len(patterns),
            )
            raise

    @circuit_breaker(
        name="pattern_mining_recommend",
        failure_threshold=3,
        recovery_timeout=60,
        expected_exception=httpx.HTTPError,
    )
    async def get_recommendations(
        self, code_context: Dict[str, Any], intent: str, limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get pattern recommendations based on code context and intent

        Args:
            code_context: Current code context (structure, dependencies, etc.)
            intent: User's intent (refactor, optimize, etc.)
            limit: Maximum number of recommendations

        Returns:
            List of recommended patterns with explanations
        """
        try:
            payload = {"code_context": code_context, "intent": intent, "limit": limit}

            response = await self.client.post("/patterns/recommendations", json=payload)
            response.raise_for_status()

            recommendations = response.json().get("recommendations", [])
            logger.info(
                "pattern_recommendations_retrieved",
                count=len(recommendations),
                intent=intent,
            )

            return recommendations

        except httpx.HTTPError as e:
            logger.error("pattern_recommendations_failed", error=str(e), intent=intent)
            raise

    @circuit_breaker(
        name="pattern_mining_analyze",
        failure_threshold=3,
        recovery_timeout=60,
        expected_exception=httpx.HTTPError,
    )
    async def analyze_code_quality(
        self, repository_id: str, file_patterns: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Analyze code quality and pattern usage in a repository

        Args:
            repository_id: Repository to analyze
            file_patterns: Optional file patterns to filter

        Returns:
            Code quality metrics and pattern analysis
        """
        try:
            payload = {
                "repository_id": repository_id,
                "file_patterns": file_patterns or [],
            }

            response = await self.client.post("/analysis/code-quality", json=payload)
            response.raise_for_status()

            return response.json()

        except httpx.ConnectError as e:
            logger.error(
                "code_quality_analysis_failed",
                error="Connection refused",
                repository_id=repository_id,
                details=str(e),
            )
            raise
        except httpx.TimeoutException as e:
            logger.error(
                "code_quality_analysis_failed",
                error="Request timeout",
                repository_id=repository_id,
                details=str(e),
            )
            raise
        except httpx.HTTPStatusError as e:
            logger.error(
                "code_quality_analysis_failed",
                error=f"HTTP {e.response.status_code}",
                repository_id=repository_id,
                details=e.response.text,
            )
            raise
        except httpx.HTTPError:
            logger.error(
                "code_quality_analysis_failed",
                error="HTTP error",
                repository_id=repository_id,
                exc_info=True,
            )
            raise

    async def health_check(self) -> bool:
        """Check if the pattern mining service is healthy"""
        try:
            response = await self.client.get("/health")
            return response.status_code == 200
        except httpx.ConnectError:
            logger.debug("pattern_mining_health_check", status="connection_refused")
            return False
        except httpx.TimeoutException:
            logger.debug("pattern_mining_health_check", status="timeout")
            return False
        except httpx.HTTPError:
            logger.debug("pattern_mining_health_check", status="http_error")
            return False
        except Exception as e:
            logger.warning(
                "pattern_mining_health_check", status="unexpected_error", error=str(e)
            )
            return False

    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()


@lru_cache()
def get_pattern_mining_client() -> PatternMiningClient:
    """Get a singleton instance of the Pattern Mining client"""
    client = PatternMiningClient()

    # Register circuit breakers for monitoring
    if hasattr(client.detect_patterns, "circuit_breaker"):
        register_circuit_breaker(client.detect_patterns.circuit_breaker)
    if hasattr(client.classify_patterns, "circuit_breaker"):
        register_circuit_breaker(client.classify_patterns.circuit_breaker)
    if hasattr(client.get_recommendations, "circuit_breaker"):
        register_circuit_breaker(client.get_recommendations.circuit_breaker)
    if hasattr(client.analyze_code_quality, "circuit_breaker"):
        register_circuit_breaker(client.analyze_code_quality.circuit_breaker)

    return client
