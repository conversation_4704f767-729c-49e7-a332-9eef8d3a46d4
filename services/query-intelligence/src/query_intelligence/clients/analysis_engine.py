import httpx
from functools import lru_cache
from typing import List, Dict, Optional, Any
import structlog

from ..config.settings import get_settings
from ..utils.circuit_breaker import circuit_breaker, register_circuit_breaker

logger = structlog.get_logger()
settings = get_settings()


class AnalysisEngineClient:
    """Client for interacting with the Analysis Engine service"""

    def __init__(self, base_url: Optional[str] = None):
        self.base_url = base_url or settings.ANALYSIS_ENGINE_URL
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            timeout=httpx.Timeout(30.0, connect=5.0),
            headers={"Content-Type": "application/json"},
        )

    @circuit_breaker(
        name="analysis_engine_get",
        failure_threshold=3,
        recovery_timeout=30,
        expected_exception=httpx.HTTPError,
    )
    async def get_analysis(self, repo_id: str) -> dict:
        """Get analysis results by repository ID"""
        try:
            response = await self.client.get(f"/analysis/{repo_id}")
            response.raise_for_status()
            return response.json()
        except httpx.HTTPError as e:
            logger.error("analysis_engine_get_failed", repo_id=repo_id, error=str(e))
            raise

    @circuit_breaker(
        name="analysis_engine_search",
        failure_threshold=5,
        recovery_timeout=60,
        expected_exception=httpx.HTTPError,
    )
    async def search_embeddings(
        self,
        repository_id: str,
        query_embedding: List[float],
        limit: int = 20,
        filters: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Search for similar code chunks using embeddings"""
        try:
            payload = {
                "repository_id": repository_id,
                "query_embedding": query_embedding,
                "limit": limit,
                "filters": filters or {},
            }

            response = await self.client.post("/embeddings/search", json=payload)
            response.raise_for_status()
            return response.json()

        except httpx.ConnectError as e:
            logger.error(
                "analysis_engine_search_failed",
                repository_id=repository_id,
                error="Connection refused",
                details=str(e),
            )
            raise
        except httpx.TimeoutException as e:
            logger.error(
                "analysis_engine_search_failed",
                repository_id=repository_id,
                error="Request timeout",
                details=str(e),
            )
            raise
        except httpx.HTTPStatusError as e:
            logger.error(
                "analysis_engine_search_failed",
                repository_id=repository_id,
                error=f"HTTP {e.response.status_code}",
                details=e.response.text,
            )
            raise
        except httpx.HTTPError:
            logger.error(
                "analysis_engine_search_failed",
                repository_id=repository_id,
                error="HTTP error",
                exc_info=True,
            )
            raise

    @circuit_breaker(
        name="analysis_engine_metadata",
        failure_threshold=3,
        recovery_timeout=30,
        expected_exception=httpx.HTTPError,
    )
    async def get_repository_metadata(self, repository_id: str) -> Dict[str, Any]:
        """Get repository metadata"""
        try:
            response = await self.client.get(f"/repositories/{repository_id}/metadata")
            response.raise_for_status()
            return response.json()
        except httpx.ConnectError as e:
            logger.error(
                "analysis_engine_metadata_failed",
                repository_id=repository_id,
                error="Connection refused",
                details=str(e),
            )
            raise
        except httpx.TimeoutException as e:
            logger.error(
                "analysis_engine_metadata_failed",
                repository_id=repository_id,
                error="Request timeout",
                details=str(e),
            )
            raise
        except httpx.HTTPStatusError as e:
            logger.error(
                "analysis_engine_metadata_failed",
                repository_id=repository_id,
                error=f"HTTP {e.response.status_code}",
                details=e.response.text,
            )
            raise
        except httpx.HTTPError:
            logger.error(
                "analysis_engine_metadata_failed",
                repository_id=repository_id,
                error="HTTP error",
                exc_info=True,
            )
            raise

    async def health_check(self) -> bool:
        """Check if the analysis engine is healthy"""
        try:
            response = await self.client.get("/health")
            return response.status_code == 200
        except httpx.ConnectError:
            logger.debug("analysis_engine_health_check", status="connection_refused")
            return False
        except httpx.TimeoutException:
            logger.debug("analysis_engine_health_check", status="timeout")
            return False
        except httpx.HTTPError:
            logger.debug("analysis_engine_health_check", status="http_error")
            return False
        except Exception as e:
            logger.warning(
                "analysis_engine_health_check", status="unexpected_error", error=str(e)
            )
            return False

    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()


@lru_cache()
def get_analysis_engine_client() -> AnalysisEngineClient:
    """Get a singleton instance of the Analysis Engine client"""
    client = AnalysisEngineClient()

    # Register circuit breakers for monitoring
    if hasattr(client.get_analysis, "circuit_breaker"):
        register_circuit_breaker(client.get_analysis.circuit_breaker)
    if hasattr(client.search_embeddings, "circuit_breaker"):
        register_circuit_breaker(client.search_embeddings.circuit_breaker)
    if hasattr(client.get_repository_metadata, "circuit_breaker"):
        register_circuit_breaker(client.get_repository_metadata.circuit_breaker)

    return client
