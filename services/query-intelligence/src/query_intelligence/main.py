from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from prometheus_fastapi_instrumentator import Instrumentator
import structlog
import time
import redis.exceptions
import httpx
import asyncio

from .api import query, websocket
from .config.settings import get_settings
from .clients.redis import get_redis_client
from .clients.analysis_engine import get_analysis_engine_client
from .clients.pattern_mining import get_pattern_mining_client
from .middleware.rate_limit import rate_limit_middleware
from .middleware.security import security_middleware_func
from .utils.circuit_breaker import get_circuit_breaker_status

logger = structlog.get_logger()
settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifecycle"""
    # Startup
    logger.info("query_intelligence_starting", port=settings.PORT)

    # Initialize clients
    redis_client = get_redis_client()
    analysis_client = get_analysis_engine_client()
    pattern_client = get_pattern_mining_client()

    # Test connections
    try:
        await redis_client.ping()
        logger.info("redis_connected")
    except redis.exceptions.ConnectionError:
        logger.warning(
            "redis_connection_failed",
            error="Connection refused",
            host=settings.REDIS_URL,
        )
    except redis.exceptions.TimeoutError:
        logger.warning(
            "redis_connection_failed", error="Connection timeout", timeout="default"
        )
    except Exception:
        logger.warning(
            "redis_connection_failed", error="Unexpected error", exc_info=True
        )

    try:
        if await analysis_client.health_check():
            logger.info("analysis_engine_connected", url=settings.ANALYSIS_ENGINE_URL)
    except httpx.ConnectError:
        logger.warning(
            "analysis_engine_connection_failed",
            error="Connection refused",
            url=settings.ANALYSIS_ENGINE_URL,
        )
    except httpx.TimeoutException:
        logger.warning(
            "analysis_engine_connection_failed",
            error="Request timeout",
            timeout=settings.QUERY_TIMEOUT_SECONDS,
        )
    except Exception:
        logger.warning(
            "analysis_engine_connection_failed", error="Unexpected error", exc_info=True
        )

    try:
        if await pattern_client.health_check():
            logger.info("pattern_mining_connected", url=settings.PATTERN_MINING_URL)
    except httpx.ConnectError:
        logger.warning(
            "pattern_mining_connection_failed",
            error="Connection refused",
            url=settings.PATTERN_MINING_URL,
        )
    except httpx.TimeoutException:
        logger.warning(
            "pattern_mining_connection_failed",
            error="Request timeout",
            timeout=settings.QUERY_TIMEOUT_SECONDS,
        )
    except Exception:
        logger.warning(
            "pattern_mining_connection_failed", error="Unexpected error", exc_info=True
        )

    yield

    # Shutdown
    logger.info("query_intelligence_shutting_down")
    await redis_client.aclose()
    await analysis_client.close()


# Create FastAPI app
app = FastAPI(
    title=settings.PROJECT_NAME,
    version="1.0.0",
    description="Natural language query processing for code understanding",
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ALLOWED_ORIGINS,
    allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
    allow_methods=settings.CORS_ALLOWED_METHODS,
    allow_headers=settings.CORS_ALLOWED_HEADERS,
)

# Add security middleware (first to run)
app.middleware("http")(security_middleware_func)

# Add rate limiting middleware
app.middleware("http")(rate_limit_middleware)

# Add Prometheus instrumentation
if settings.ENABLE_METRICS:
    instrumentator = Instrumentator()
    instrumentator.instrument(app).expose(app, endpoint="/metrics")

# Include routers
app.include_router(query.router, prefix="/api/v1", tags=["queries"])
app.include_router(websocket.router, prefix="/api/v1", tags=["websocket"])

# Import and include admin router if enabled
if settings.ENABLE_ADMIN_API:
    from .api import admin
    app.include_router(admin.router, prefix="/api/v1", tags=["admin"])


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    health_status = {
        "status": "healthy",
        "service": settings.SERVICE_NAME,
        "version": "1.0.0",
        "checks": {},
    }

    # Check Redis
    try:
        redis_client = get_redis_client()
        await redis_client.ping()
        health_status["checks"]["redis"] = "ok"
    except redis.exceptions.ConnectionError as e:
        logger.error(
            "redis_health_check_failed", error="Connection failed", details=str(e)
        )
        health_status["checks"]["redis"] = "connection_error"
        health_status["status"] = "unhealthy"
    except redis.exceptions.TimeoutError as e:
        logger.error("redis_health_check_failed", error="Timeout", details=str(e))
        health_status["checks"]["redis"] = "timeout"
        health_status["status"] = "unhealthy"
    except Exception:
        logger.error("redis_health_check_failed", error="Unknown error", exc_info=True)
        health_status["checks"]["redis"] = "failed"
        health_status["status"] = "unhealthy"

    # Check Analysis Engine
    try:
        analysis_client = get_analysis_engine_client()
        if await analysis_client.health_check():
            health_status["checks"]["analysis_engine"] = "ok"
        else:
            health_status["checks"]["analysis_engine"] = "unhealthy"
            health_status["status"] = "degraded"
    except httpx.ConnectError as e:
        logger.error(
            "analysis_engine_health_check_failed",
            error="Connection refused",
            details=str(e),
        )
        health_status["checks"]["analysis_engine"] = "connection_error"
        health_status["status"] = "degraded"
    except httpx.TimeoutException as e:
        logger.error(
            "analysis_engine_health_check_failed",
            error="Request timeout",
            details=str(e),
        )
        health_status["checks"]["analysis_engine"] = "timeout"
        health_status["status"] = "degraded"
    except asyncio.TimeoutError as e:
        logger.error(
            "analysis_engine_health_check_failed", error="Async timeout", details=str(e)
        )
        health_status["checks"]["analysis_engine"] = "timeout"
        health_status["status"] = "degraded"
    except Exception:
        logger.error(
            "analysis_engine_health_check_failed", error="Unknown error", exc_info=True
        )
        health_status["checks"]["analysis_engine"] = "failed"
        health_status["status"] = "degraded"

    # Check Pattern Mining
    try:
        pattern_client = get_pattern_mining_client()
        if await pattern_client.health_check():
            health_status["checks"]["pattern_mining"] = "ok"
        else:
            health_status["checks"]["pattern_mining"] = "unhealthy"
            health_status["status"] = "degraded"
    except httpx.ConnectError as e:
        logger.error(
            "pattern_mining_health_check_failed",
            error="Connection refused",
            details=str(e),
        )
        health_status["checks"]["pattern_mining"] = "connection_error"
        health_status["status"] = "degraded"
    except httpx.TimeoutException as e:
        logger.error(
            "pattern_mining_health_check_failed",
            error="Request timeout",
            details=str(e),
        )
        health_status["checks"]["pattern_mining"] = "timeout"
        health_status["status"] = "degraded"
    except asyncio.TimeoutError as e:
        logger.error(
            "pattern_mining_health_check_failed", error="Async timeout", details=str(e)
        )
        health_status["checks"]["pattern_mining"] = "timeout"
        health_status["status"] = "degraded"
    except Exception:
        logger.error(
            "pattern_mining_health_check_failed", error="Unknown error", exc_info=True
        )
        health_status["checks"]["pattern_mining"] = "failed"
        health_status["status"] = "degraded"

    return health_status


@app.get("/ready")
async def readiness_check():
    """Readiness check endpoint"""
    # For now, if healthy then ready
    health = await health_check()
    if health["status"] == "healthy":
        return {"ready": True}
    else:
        return {"ready": False}, 503


@app.get("/circuit-breakers")
async def circuit_breaker_status():
    """Get status of all circuit breakers"""
    return {"circuit_breakers": get_circuit_breaker_status(), "timestamp": time.time()}
