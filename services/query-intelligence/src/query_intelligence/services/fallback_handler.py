"""
Fallback handlers for when services are unavailable
Provides graceful degradation of functionality
"""

import structlog
from typing import List, Dict, Any, Optional
import hashlib
import re
from collections import defaultdict

from ..models import (
    CodeChunk,
    SearchResult,
    GeneratedResponse,
    IntentAnalysis,
    QueryContext,
    QueryIntent,
)
from ..utils.circuit_breaker import CircuitBreakerError

logger = structlog.get_logger()


class FallbackHandler:
    """Handles fallback logic when services are unavailable"""

    @staticmethod
    async def handle_llm_fallback(
        query: str,
        intent: IntentAnalysis,
        code_chunks: List[CodeChunk],
        context: QueryContext,
        error: Exception,
    ) -> GeneratedResponse:
        """Enhanced fallback when LLM service is unavailable"""
        logger.warning("llm_fallback_triggered", query=query, error=str(error))

        # Use intent-specific templates for better responses
        response_text = FallbackHandler._generate_intent_based_response(
            query, intent, code_chunks
        )

        # Calculate a more nuanced confidence score
        confidence = FallbackHandler._calculate_fallback_confidence(
            intent, code_chunks, response_text
        )

        return GeneratedResponse(
            text=response_text,
            confidence=confidence,
            model_used="fallback-enhanced",
            prompt_tokens=0,
            completion_tokens=0,
            generation_time_ms=0,
        )

    @staticmethod
    def _generate_intent_based_response(
        query: str, intent: IntentAnalysis, code_chunks: List[CodeChunk]
    ) -> str:
        """Generate response based on intent type"""

        if not code_chunks:
            return (
                f"I couldn't find any code relevant to your query about '{query}'. "
                "The AI service is temporarily unavailable. Please try:\n"
                "• Refining your search terms\n"
                "• Checking if the code exists in the repository\n"
                "• Trying again in a few moments"
            )

        # Intent-specific response generation
        if intent.primary_intent == QueryIntent.EXPLAIN:
            return FallbackHandler._generate_explanation_response(
                query, code_chunks, intent
            )
        elif intent.primary_intent == QueryIntent.FIND:
            return FallbackHandler._generate_find_response(query, code_chunks, intent)
        elif intent.primary_intent == QueryIntent.DEBUG:
            return FallbackHandler._generate_debug_response(query, code_chunks, intent)
        elif intent.primary_intent == QueryIntent.ANALYZE:
            return FallbackHandler._generate_analyze_response(
                query, code_chunks, intent
            )
        elif intent.primary_intent == QueryIntent.COMPARE:
            return FallbackHandler._generate_compare_response(
                query, code_chunks, intent
            )
        else:
            return FallbackHandler._generate_generic_response(
                query, code_chunks, intent
            )

    @staticmethod
    def _generate_explanation_response(
        query: str, chunks: List[CodeChunk], intent: IntentAnalysis
    ) -> str:
        """Generate explanation-focused response"""
        response = f"## Code Explanation for: {query}\n\n"

        for i, chunk in enumerate(chunks[:3], 1):
            response += f"### {i}. {chunk.file_path}\n"
            response += f"**Location**: Lines {chunk.start_line}-{chunk.end_line}\n\n"

            # Extract key elements from code
            functions = re.findall(r"def\s+(\w+)\s*\(", chunk.content)
            classes = re.findall(r"class\s+(\w+)\s*[\(:]", chunk.content)

            if functions:
                response += f"**Functions found**: {', '.join(functions)}\n"
            if classes:
                response += f"**Classes found**: {', '.join(classes)}\n"

            # Add code snippet with syntax highlighting hint
            response += "\n```" + chunk.language + "\n"
            response += chunk.content[:400] + (
                "..." if len(chunk.content) > 400 else ""
            )
            response += "\n```\n\n"

        response += (
            "\n*Note: AI-powered detailed explanations are temporarily unavailable. "
            "The above shows the most relevant code sections found.*"
        )
        return response

    @staticmethod
    def _generate_find_response(
        query: str, chunks: List[CodeChunk], intent: IntentAnalysis
    ) -> str:
        """Generate search-focused response"""
        response = f"## Search Results for: {query}\n\n"
        response += f"Found {len(chunks)} relevant code sections:\n\n"

        # Group by file
        files_map = defaultdict(list)
        for chunk in chunks[:10]:  # Show more results for find queries
            files_map[chunk.file_path].append(chunk)

        for file_path, file_chunks in files_map.items():
            response += f"### 📄 {file_path}\n"
            for chunk in file_chunks:
                response += f"- Lines {chunk.start_line}-{chunk.end_line}"

                # Extract first meaningful line
                first_line = next(
                    (
                        line.strip()
                        for line in chunk.content.split("\n")
                        if line.strip()
                    ),
                    "",
                )
                if first_line:
                    response += (
                        f": `{first_line[:60]}{'...' if len(first_line) > 60 else ''}`"
                    )
                response += "\n"
            response += "\n"

        response += (
            "*Note: Advanced semantic search is temporarily unavailable. "
            "Results based on code similarity matching.*"
        )
        return response

    @staticmethod
    def _generate_debug_response(
        query: str, chunks: List[CodeChunk], intent: IntentAnalysis
    ) -> str:
        """Generate debug-focused response"""
        response = f"## Debug Analysis for: {query}\n\n"

        for i, chunk in enumerate(chunks[:3], 1):
            response += f"### {i}. Potential Issue Location: {chunk.file_path}\n"
            response += f"**Lines**: {chunk.start_line}-{chunk.end_line}\n\n"

            # Look for common debug patterns
            error_patterns = [
                (r"raise\s+\w+", "Exception raised"),
                (r"except\s+\w+", "Exception handler"),
                (r"logger\.(error|warning|critical)", "Logging statement"),
                (r"assert\s+", "Assertion"),
                (r"TODO|FIXME|BUG", "Code annotation"),
            ]

            findings = []
            for pattern, description in error_patterns:
                if re.search(pattern, chunk.content, re.IGNORECASE):
                    findings.append(description)

            if findings:
                response += "**Detected patterns**: " + ", ".join(findings) + "\n\n"

            response += "```" + chunk.language + "\n"
            response += chunk.content[:300] + (
                "..." if len(chunk.content) > 300 else ""
            )
            response += "\n```\n\n"

        response += (
            "*Note: Full debugging assistance is temporarily unavailable. "
            "Check the highlighted code sections for potential issues.*"
        )
        return response

    @staticmethod
    def _generate_analyze_response(
        query: str, chunks: List[CodeChunk], intent: IntentAnalysis
    ) -> str:
        """Generate analysis-focused response"""
        response = f"## Code Analysis for: {query}\n\n"

        # Analyze code metrics
        total_lines = sum(chunk.end_line - chunk.start_line + 1 for chunk in chunks)
        languages: Dict[str, int] = defaultdict(int)
        for chunk in chunks:
            languages[chunk.language] += 1

        response += "### Overview\n"
        response += f"- **Total code sections analyzed**: {len(chunks)}\n"
        response += f"- **Total lines of code**: {total_lines}\n"
        response += f"- **Languages**: {', '.join(f'{lang} ({count})' for lang, count in languages.items())}\n\n"

        response += "### Key Code Sections\n\n"

        for i, chunk in enumerate(chunks[:4], 1):
            response += f"#### {i}. {chunk.file_path}\n"

            # Basic complexity analysis
            complexity_indicators = {
                "Nested loops": len(re.findall(r"for\s+.*:\s*\n\s*for", chunk.content)),
                "Conditions": len(re.findall(r"if\s+", chunk.content)),
                "Functions": len(re.findall(r"def\s+\w+", chunk.content)),
                "Classes": len(re.findall(r"class\s+\w+", chunk.content)),
            }

            metrics = [f"{k}: {v}" for k, v in complexity_indicators.items() if v > 0]
            if metrics:
                response += f"**Metrics**: {', '.join(metrics)}\n\n"

            response += "```" + chunk.language + "\n"
            response += chunk.content[:250] + (
                "..." if len(chunk.content) > 250 else ""
            )
            response += "\n```\n\n"

        response += (
            "*Note: Advanced code analysis is temporarily unavailable. "
            "Basic metrics and relevant code sections are shown above.*"
        )
        return response

    @staticmethod
    def _generate_compare_response(
        query: str, chunks: List[CodeChunk], intent: IntentAnalysis
    ) -> str:
        """Generate comparison-focused response"""
        response = f"## Code Comparison for: {query}\n\n"

        if len(chunks) < 2:
            response += "⚠️ Not enough code sections found for comparison.\n\n"
            response += FallbackHandler._generate_generic_response(
                query, chunks, intent
            )
            return response

        # Compare first two chunks
        chunk1, chunk2 = chunks[0], chunks[1]

        response += "### Comparing:\n"
        response += (
            f"1. **{chunk1.file_path}** (lines {chunk1.start_line}-{chunk1.end_line})\n"
        )
        response += f"2. **{chunk2.file_path}** (lines {chunk2.start_line}-{chunk2.end_line})\n\n"

        response += "### Similarities:\n"
        # Find common patterns
        patterns = ["def", "class", "import", "return", "raise"]
        common = []
        for pattern in patterns:
            if pattern in chunk1.content and pattern in chunk2.content:
                common.append(pattern)

        if common:
            response += f"- Both contain: {', '.join(common)}\n"
        response += f"- Language: {chunk1.language} vs {chunk2.language}\n\n"

        response += "### Code Sections:\n\n"
        response += f"#### {chunk1.file_path}\n"
        response += "```" + chunk1.language + "\n"
        response += chunk1.content[:200] + ("..." if len(chunk1.content) > 200 else "")
        response += "\n```\n\n"

        response += f"#### {chunk2.file_path}\n"
        response += "```" + chunk2.language + "\n"
        response += chunk2.content[:200] + ("..." if len(chunk2.content) > 200 else "")
        response += "\n```\n\n"

        response += (
            "*Note: Advanced comparison analysis is temporarily unavailable. "
            "Basic comparison shown above.*"
        )
        return response

    @staticmethod
    def _generate_generic_response(
        query: str, chunks: List[CodeChunk], intent: IntentAnalysis
    ) -> str:
        """Generate generic fallback response"""
        response = f"## Code Results for: {query}\n\n"
        response += f"Intent: {intent.primary_intent.value}\n\n"

        for i, chunk in enumerate(chunks[:3], 1):
            response += f"### {i}. {chunk.file_path}\n"
            response += f"**Lines**: {chunk.start_line}-{chunk.end_line}\n"
            response += f"**Relevance**: {chunk.combined_score:.2f}\n\n"

            response += "```" + chunk.language + "\n"
            response += chunk.content[:300] + (
                "..." if len(chunk.content) > 300 else ""
            )
            response += "\n```\n\n"

        response += (
            "*Note: AI service is temporarily unavailable. "
            "Showing relevant code sections based on similarity matching.*"
        )
        return response

    @staticmethod
    def _calculate_fallback_confidence(
        intent: IntentAnalysis, chunks: List[CodeChunk], response: str
    ) -> float:
        """Calculate confidence for fallback response"""
        base_confidence = 0.3

        # Boost based on number of relevant chunks
        if chunks:
            chunk_boost = min(len(chunks) * 0.05, 0.2)
            base_confidence += chunk_boost

        # Boost based on chunk quality
        if chunks:
            avg_score = sum(c.combined_score for c in chunks[:3]) / min(3, len(chunks))
            quality_boost = avg_score * 0.1
            base_confidence += quality_boost

        # Boost based on intent confidence
        intent_boost = intent.confidence * 0.05
        base_confidence += intent_boost

        # Boost based on response length/quality
        if len(response) > 500:
            base_confidence += 0.05

        return min(base_confidence, 0.6)  # Cap at 0.6 for fallback

    @staticmethod
    async def handle_search_fallback(
        embedding: Any,
        repository_id: str,
        filters: Optional[Dict[str, Any]],
        limit: int,
        error: Exception,
    ) -> SearchResult:
        """Enhanced fallback when search service is unavailable"""
        logger.warning(
            "search_fallback_triggered", repository_id=repository_id, error=str(error)
        )

        # Try to provide some basic results using cached data or patterns
        fallback_chunks = []

        # If we have filters, we can at least provide some guidance
        if filters:
            file_pattern = filters.get("file_pattern", "")
            language = filters.get("language", "")

            # Create informative pseudo-chunks
            if file_pattern:
                fallback_chunks.append(
                    CodeChunk(
                        content=f"Search service unavailable. Looking for files matching: {file_pattern}",
                        file_path=f"pattern:{file_pattern}",
                        start_line=1,
                        end_line=1,
                        language=language or "unknown",
                        metadata={"fallback": True, "pattern": file_pattern, "repository_id": repository_id},
                    )
                )

        return SearchResult(
            chunks=fallback_chunks,
            total_results=len(fallback_chunks),
            search_time_ms=0,
        )

    @staticmethod
    async def handle_cache_fallback(key: str, error: Exception) -> Optional[str]:
        """Fallback when cache service is unavailable"""
        logger.warning("cache_fallback_triggered", key=key, error=str(error))

        # Return None to indicate cache miss
        return None

    @staticmethod
    def generate_cache_key(query: str, repository_id: str) -> str:
        """Generate a cache key for fallback scenarios"""
        key_data = f"{query}:{repository_id}"
        return f"query_cache:{hashlib.md5(key_data.encode()).hexdigest()}"

    @staticmethod
    async def handle_analysis_engine_fallback(
        repository_id: str, error: Exception
    ) -> Dict[str, Any]:
        """Enhanced fallback when analysis engine is unavailable"""
        logger.warning(
            "analysis_engine_fallback_triggered",
            repository_id=repository_id,
            error=str(error),
        )

        # Provide basic analysis capabilities using regex patterns
        return {
            "status": "degraded",
            "message": "Analysis engine temporarily unavailable - using basic analysis",
            "fallback": True,
            "capabilities": {
                "ast_parsing": False,
                "semantic_analysis": False,
                "basic_pattern_matching": True,
                "file_discovery": True,
            },
            "suggestions": [
                "Basic code search is still available",
                "File pattern matching can be used",
                "Keyword search remains functional",
                "Full analysis will resume when service recovers",
            ],
            "patterns": {
                "functions": r"def\s+(\w+)\s*\(",
                "classes": r"class\s+(\w+)\s*[\(:]",
                "imports": r"(?:from|import)\s+(\S+)",
                "variables": r"(\w+)\s*=\s*",
                "comments": r"(?:#|//|/\*|\*\s)",
            },
        }

    @staticmethod
    async def handle_embedding_fallback(
        query: str, context: QueryContext, error: Exception
    ) -> List[float]:
        """Fallback when embedding service is unavailable"""
        logger.warning("embedding_fallback_triggered", query=query, error=str(error))
        
        # Extract keywords from query
        keywords = FallbackHandler._extract_keywords(query)
        
        # Generate a fallback embedding based on keywords
        return FallbackHandler._generate_fallback_embedding(keywords)

    @staticmethod
    async def handle_search_fallback(
        query: str, context: QueryContext, error: Exception
    ) -> SearchResult:
        """Fallback when search service is unavailable"""
        logger.warning("search_fallback_triggered", query=query, error=str(error))
        
        # Use simple keyword-based search
        chunks = FallbackHandler._simple_keyword_search(query, context)
        
        return SearchResult(
            chunks=chunks,
            total_results=len(chunks),
            search_time_ms=0.0
        )

    @staticmethod
    def _extract_keywords(query: str) -> List[str]:
        """Extract keywords from query for fallback embedding"""
        if not query:
            return []
        
        # Simple keyword extraction - remove common stopwords
        stopwords = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
            'above', 'below', 'over', 'under', 'again', 'further', 'then', 'once',
            'here', 'there', 'when', 'where', 'why', 'how', 'all', 'any', 'both', 'each',
            'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor', 'not', 'only',
            'own', 'same', 'so', 'than', 'too', 'very', 'can', 'will', 'just', 'should',
            'now', 'i', 'me', 'my', 'myself', 'we', 'our', 'ours', 'ourselves', 'you',
            'your', 'yours', 'yourself', 'yourselves', 'he', 'him', 'his', 'himself',
            'she', 'her', 'hers', 'herself', 'it', 'its', 'itself', 'they', 'them',
            'their', 'theirs', 'themselves', 'what', 'which', 'who', 'whom', 'this',
            'that', 'these', 'those', 'am', 'is', 'are', 'was', 'were', 'be', 'been',
            'being', 'have', 'has', 'had', 'having', 'do', 'does', 'did', 'doing'
        }
        
        # Extract words and filter
        words = re.findall(r'\b\w+\b', query.lower())
        keywords = [word for word in words if word not in stopwords and len(word) > 2]
        
        return keywords

    @staticmethod
    def _generate_fallback_embedding(keywords: List[str]) -> List[float]:
        """Generate a simple fallback embedding based on keywords"""
        if not keywords:
            return [0.0] * 768
        
        # Create a deterministic embedding based on keywords
        # This is a simple approach that hashes keywords into embedding space
        embedding = [0.0] * 768
        
        for i, keyword in enumerate(keywords):
            # Use hash of keyword to distribute across embedding dimensions
            hash_val = hash(keyword)
            for j in range(768):
                # Spread the hash across multiple dimensions
                embedding[j] += (hash_val >> (j % 32)) & 1
        
        # Normalize to reasonable range
        max_val = max(abs(x) for x in embedding) if embedding else 1
        if max_val > 0:
            embedding = [x / max_val * 0.1 for x in embedding]
        
        return embedding

    @staticmethod
    def _simple_keyword_search(query: str, context: QueryContext) -> List[CodeChunk]:
        """Simple keyword-based search for fallback"""
        if not query:
            return []
        
        # Extract keywords
        keywords = FallbackHandler._extract_keywords(query)
        
        # In a real implementation, this would search through cached code chunks
        # For now, return an empty list as the cache/index is not available
        # This method can be extended to search through any locally cached content
        
        return []


def with_fallback(fallback_handler_name: str):
    """
    Decorator to add fallback handling to async functions

    Args:
        fallback_handler_name: Name of the FallbackHandler method to use

    Example:
        @with_fallback("handle_llm_fallback")
        async def generate_response(...):
            ...
    """

    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except CircuitBreakerError as e:
                # Circuit breaker is open, use fallback
                handler = getattr(FallbackHandler, fallback_handler_name)
                return await handler(*args, error=e, **kwargs)
            except Exception as e:
                # Other errors might also trigger fallback
                if "timeout" in str(e).lower() or "connection" in str(e).lower():
                    handler = getattr(FallbackHandler, fallback_handler_name)
                    return await handler(*args, error=e, **kwargs)
                raise

        return wrapper

    return decorator
