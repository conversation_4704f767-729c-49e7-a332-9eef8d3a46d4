"""
Query optimization service that provides hints and strategies
for improving query performance and accuracy
"""

import re
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import structlog

from ..models import QueryIntent, IntentAnalysis

logger = structlog.get_logger()


class OptimizationHint(Enum):
    """Types of optimization hints"""
    ADD_CONTEXT = "add_context"
    BE_SPECIFIC = "be_specific"
    USE_FILTERS = "use_filters"
    SPLIT_QUERY = "split_query"
    USE_KEYWORDS = "use_keywords"
    SPECIFY_LANGUAGE = "specify_language"
    ADD_TIMEFRAME = "add_timeframe"
    USE_EXAMPLES = "use_examples"


@dataclass
class QueryOptimization:
    """Query optimization recommendation"""
    hint: OptimizationHint
    description: str
    example: Optional[str] = None
    confidence: float = 0.8
    impact: str = "medium"  # low, medium, high


class QueryOptimizer:
    """Service for analyzing and optimizing queries"""
    
    def __init__(self):
        self.min_query_length = 10
        self.max_query_length = 500
        self.optimal_length_range = (20, 200)
        
    def analyze_query(
        self, 
        query: str, 
        intent: IntentAnalysis,
        previous_results: Optional[Dict[str, Any]] = None
    ) -> List[QueryOptimization]:
        """Analyze query and provide optimization hints"""
        
        optimizations = []
        
        # Check query length
        length_opts = self._check_query_length(query)
        optimizations.extend(length_opts)
        
        # Check specificity
        specificity_opts = self._check_specificity(query, intent)
        optimizations.extend(specificity_opts)
        
        # Check for missing context
        context_opts = self._check_context(query, intent)
        optimizations.extend(context_opts)
        
        # Check for potential filters
        filter_opts = self._suggest_filters(query, intent)
        optimizations.extend(filter_opts)
        
        # Check for complex queries that could be split
        split_opts = self._check_query_complexity(query)
        optimizations.extend(split_opts)
        
        # Check based on previous results
        if previous_results:
            result_opts = self._optimize_from_results(query, previous_results)
            optimizations.extend(result_opts)
        
        # Sort by impact and confidence
        optimizations.sort(
            key=lambda x: (
                {"high": 3, "medium": 2, "low": 1}[x.impact],
                x.confidence
            ),
            reverse=True
        )
        
        return optimizations[:5]  # Return top 5 suggestions
        
    def _check_query_length(self, query: str) -> List[QueryOptimization]:
        """Check if query length is optimal"""
        optimizations = []
        query_length = len(query)
        
        if query_length < self.min_query_length:
            optimizations.append(
                QueryOptimization(
                    hint=OptimizationHint.ADD_CONTEXT,
                    description="Your query is very short. Adding more context will help find better results.",
                    example=f"Instead of '{query}', try 'How does {query} work in the authentication system?'",
                    confidence=0.9,
                    impact="high"
                )
            )
        elif query_length > self.max_query_length:
            optimizations.append(
                QueryOptimization(
                    hint=OptimizationHint.SPLIT_QUERY,
                    description="Your query is quite long. Consider breaking it into multiple specific questions.",
                    example="Focus on one aspect at a time for better results.",
                    confidence=0.8,
                    impact="medium"
                )
            )
            
        return optimizations
        
    def _check_specificity(self, query: str, intent: IntentAnalysis) -> List[QueryOptimization]:
        """Check if query is specific enough"""
        optimizations = []
        
        # Check for vague terms
        vague_terms = [
            "something", "stuff", "thing", "this", "that", "it"
        ]
        
        query_lower = query.lower()
        vague_count = sum(1 for term in vague_terms if term in query_lower)
        
        if vague_count >= 1:
            optimizations.append(
                QueryOptimization(
                    hint=OptimizationHint.BE_SPECIFIC,
                    description="Your query contains vague terms. Be more specific about what you're looking for.",
                    example="Instead of 'find the function that does authentication stuff', try 'find the JWT token validation function'",
                    confidence=0.85,
                    impact="high"
                )
            )
            
        # Check for missing technical terms based on intent
        if intent.primary_intent == QueryIntent.FIND:
            if not any(term in query_lower for term in ["function", "class", "method", "variable", "component", "module"]):
                optimizations.append(
                    QueryOptimization(
                        hint=OptimizationHint.USE_KEYWORDS,
                        description="Specify what type of code element you're looking for.",
                        example="Add keywords like 'function', 'class', or 'component' to your search",
                        confidence=0.7,
                        impact="medium"
                    )
                )
                
        return optimizations
        
    def _check_context(self, query: str, intent: IntentAnalysis) -> List[QueryOptimization]:
        """Check if query has sufficient context"""
        optimizations = []
        
        # Check for missing domain context
        if intent.primary_intent in [QueryIntent.EXPLAIN, QueryIntent.DEBUG]:
            # Look for context indicators
            context_indicators = ["in", "for", "when", "during", "with", "using"]
            has_context = any(indicator in query.lower() for indicator in context_indicators)
            
            if not has_context:
                optimizations.append(
                    QueryOptimization(
                        hint=OptimizationHint.ADD_CONTEXT,
                        description="Add context about when or where this code is used.",
                        example=f"Try: '{query} in the user authentication flow' or '{query} when processing API requests'",
                        confidence=0.75,
                        impact="medium"
                    )
                )
        
        # Check for FIND intent missing keywords or too general terms
        if intent.primary_intent == QueryIntent.FIND:
            # Check if query is too general (just plural forms)
            general_terms = ["functions", "classes", "methods", "variables", "components", "modules"]
            is_too_general = any(term == query.lower().strip() for term in general_terms)
            
            if is_too_general:
                optimizations.append(
                    QueryOptimization(
                        hint=OptimizationHint.USE_KEYWORDS,
                        description="Your query is too general. Add specific keywords to narrow the search.",
                        example="Try 'authentication functions' or 'user validation functions'",
                        confidence=0.8,
                        impact="high"
                    )
                )
            else:
                # Check if missing specific keywords
                keywords = ["function", "class", "method", "variable", "component", "module"]
                has_keywords = any(keyword in query.lower() for keyword in keywords)
                
                if not has_keywords:
                    optimizations.append(
                        QueryOptimization(
                            hint=OptimizationHint.USE_KEYWORDS,
                            description="Specify what type of code element you're looking for.",
                            example="Add keywords like 'function', 'class', or 'component' to your search",
                            confidence=0.7,
                            impact="medium"
                        )
                    )
                
        return optimizations
        
    def _suggest_filters(self, query: str, intent: IntentAnalysis) -> List[QueryOptimization]:
        """Suggest potential filters to narrow results"""
        optimizations = []
        
        # Check if query mentions file types without using them as filters
        file_extensions = {
            "python": [".py"],
            "javascript": [".js", ".jsx"],
            "typescript": [".ts", ".tsx"],
            "java": [".java"],
            "go": [".go"],
            "rust": [".rs"],
        }
        
        query_lower = query.lower()
        for lang, exts in file_extensions.items():
            if lang in query_lower:
                optimizations.append(
                    QueryOptimization(
                        hint=OptimizationHint.USE_FILTERS,
                        description=f"Filter results to only {lang.title()} files for more relevant results.",
                        example=f"Use file type filter: {', '.join(exts)}",
                        confidence=0.8,
                        impact="medium"
                    )
                )
                break
                
        # Suggest directory filters for common patterns
        if any(term in query_lower for term in ["test", "tests", "testing"]):
            optimizations.append(
                QueryOptimization(
                    hint=OptimizationHint.USE_FILTERS,
                    description="Filter to test directories for test-related queries.",
                    example="Use path filter: **/test/** or **/tests/**",
                    confidence=0.7,
                    impact="low"
                )
            )
            
        # Suggest filters for function queries
        if "function" in query_lower and intent.primary_intent == QueryIntent.FIND:
            optimizations.append(
                QueryOptimization(
                    hint=OptimizationHint.USE_FILTERS,
                    description="Use filters to narrow down function search results.",
                    example="Filter by file type or directory to find specific functions",
                    confidence=0.7,
                    impact="medium"
                )
            )
            
        return optimizations
        
    def _check_query_complexity(self, query: str) -> List[QueryOptimization]:
        """Check if query is too complex and should be split"""
        optimizations = []
        
        # Check for multiple questions (indicated by multiple question marks or "and")
        question_marks = query.count("?")
        and_count = len(re.findall(r'\band\b', query.lower()))
        
        if question_marks > 1 or and_count >= 2:
            optimizations.append(
                QueryOptimization(
                    hint=OptimizationHint.SPLIT_QUERY,
                    description="Your query contains multiple questions. Split them for better results.",
                    example="Ask each question separately for more focused answers.",
                    confidence=0.85,
                    impact="high"
                )
            )
            
        return optimizations
        
    def _optimize_from_results(
        self, 
        query: str, 
        previous_results: Dict[str, Any]
    ) -> List[QueryOptimization]:
        """Optimize based on previous query results"""
        optimizations = []
        
        # Check if results were too broad
        if previous_results.get("total_results", 0) > 50:
            optimizations.append(
                QueryOptimization(
                    hint=OptimizationHint.USE_FILTERS,
                    description="Your previous query returned many results. Use filters to narrow them down.",
                    confidence=0.9,
                    impact="high"
                )
            )
            
        # Check if results had low confidence
        if previous_results.get("confidence", 1.0) < 0.6:
            optimizations.append(
                QueryOptimization(
                    hint=OptimizationHint.BE_SPECIFIC,
                    description="Low confidence results suggest you should be more specific.",
                    confidence=0.8,
                    impact="high"
                )
            )
            optimizations.append(
                QueryOptimization(
                    hint=OptimizationHint.ADD_CONTEXT,
                    description="Add more context to improve result quality.",
                    confidence=0.7,
                    impact="medium"
                )
            )
            
        # Check if no results were found
        if previous_results.get("total_results", 0) == 0:
            optimizations.append(
                QueryOptimization(
                    hint=OptimizationHint.USE_KEYWORDS,
                    description="No results found. Try using different keywords.",
                    confidence=0.8,
                    impact="high"
                )
            )
            
        return optimizations
        
    def generate_optimized_query(
        self, 
        original_query: str, 
        optimizations: List[QueryOptimization]
    ) -> str:
        """Generate an optimized version of the query based on hints"""
        
        if not optimizations:
            return original_query
            
        optimized = original_query
        
        for opt in optimizations:
            if opt.hint == OptimizationHint.BE_SPECIFIC:
                # Add common specific terms
                if "function" not in optimized.lower():
                    optimized = optimized.replace("code", "function")
                if "this" in optimized.lower():
                    optimized = optimized.replace("this", "the authentication")
                    
            elif opt.hint == OptimizationHint.ADD_CONTEXT:
                # Add common context
                if not any(word in optimized.lower() for word in ["where", "when", "how", "what"]):
                    optimized = f"How does {optimized} work?"
                    
            elif opt.hint == OptimizationHint.USE_KEYWORDS:
                # Add relevant keywords
                if "system" in optimized.lower():
                    optimized = optimized.replace("system", "authentication system")
                    
            elif opt.hint == OptimizationHint.SPLIT_QUERY:
                # Take first part of complex query
                if "and" in optimized.lower():
                    parts = optimized.split(" and ")
                    optimized = parts[0].strip()
                    if not optimized.endswith("?"):
                        optimized += "?"
                        
        return optimized
        
    def get_query_quality_score(self, query: str, intent: IntentAnalysis) -> float:
        """Calculate a quality score for the query (0-1)"""
        score = 0.5  # Base score
        
        # Length score
        length = len(query)
        if self.optimal_length_range[0] <= length <= self.optimal_length_range[1]:
            score += 0.2
        elif length < self.min_query_length or length > self.max_query_length:
            score -= 0.2
            
        # Specificity score
        specific_terms = ["function", "class", "method", "component", "implementation"]
        if any(term in query.lower() for term in specific_terms):
            score += 0.1
            
        # Context score
        context_terms = ["in", "for", "when", "during", "where", "how"]
        if any(term in query.lower() for term in context_terms):
            score += 0.1
            
        # Intent alignment score
        if intent.confidence > 0.8:
            score += 0.1
            
        return min(max(score, 0.0), 1.0)

    def _calculate_length_score(self, query: str) -> float:
        """Calculate length score component"""
        length = len(query)
        
        if self.optimal_length_range[0] <= length <= self.optimal_length_range[1]:
            # Within optimal range
            return 1.0
        elif length < self.min_query_length:
            # Too short
            return length / self.min_query_length
        elif length > self.max_query_length:
            # Too long - penalize more heavily
            excess = length - self.max_query_length
            penalty = min(excess / self.max_query_length, 0.8)  # Cap penalty at 0.8
            return max(0.0, 0.5 - penalty)
        else:
            # Between min and optimal, or optimal and max
            if length < self.optimal_length_range[0]:
                return 0.5 + 0.5 * (length - self.min_query_length) / (self.optimal_length_range[0] - self.min_query_length)
            else:
                return 0.5 + 0.5 * (self.max_query_length - length) / (self.max_query_length - self.optimal_length_range[1])

    def _calculate_specificity_score(self, query: str, intent: IntentAnalysis) -> float:
        """Calculate specificity score component"""
        score = 0.5  # Base score
        
        # Check for vague terms (negative impact)
        vague_terms = ["something", "stuff", "thing", "this", "that", "it"]
        vague_count = sum(1 for term in vague_terms if term in query.lower())
        if vague_count > 0:
            score -= 0.1 * vague_count
        
        # Check for specific terms (positive impact)
        specific_terms = ["function", "class", "method", "component", "implementation", "algorithm"]
        specific_count = sum(1 for term in specific_terms if term in query.lower())
        if specific_count > 0:
            score += 0.1 * specific_count
        
        # Factor in intent confidence
        score += 0.3 * intent.confidence
        
        # Factor in code elements count
        if intent.code_elements:
            score += 0.1 * min(len(intent.code_elements), 3) / 3
        
        return min(max(score, 0.0), 1.0)

    def _calculate_complexity_score(self, query: str) -> float:
        """Calculate complexity score component (lower is worse)"""
        score = 1.0  # Start with perfect score
        
        # Check for multiple questions
        question_marks = query.count("?")
        if question_marks > 1:
            score -= 0.3 * (question_marks - 1)
        
        # Check for multiple "and" conjunctions
        and_count = len(re.findall(r'\band\b', query.lower()))
        if and_count > 1:
            score -= 0.26 * (and_count - 1)
        
        # Check for multiple "or" disjunctions
        or_count = len(re.findall(r'\bor\b', query.lower()))
        if or_count > 1:
            score -= 0.2 * (or_count - 1)
        
        # Check for very long sentences
        sentences = query.split('.')
        if len(sentences) > 2:
            score -= 0.1 * (len(sentences) - 2)
        
        return min(max(score, 0.0), 1.0)


# Singleton instance
_query_optimizer: Optional[QueryOptimizer] = None


def get_query_optimizer() -> QueryOptimizer:
    """Get singleton query optimizer instance"""
    global _query_optimizer
    if _query_optimizer is None:
        _query_optimizer = QueryOptimizer()
    return _query_optimizer