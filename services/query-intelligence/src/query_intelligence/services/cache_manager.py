"""
Advanced caching service for query results and embeddings
Optimized for <100ms response times
"""

import json
import hashlib
import time
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime, timedelta
import structlog
import asyncio
from functools import lru_cache

from ..clients.redis import get_redis_client
from ..models import QueryResult, CodeChunk
from ..models.embeddings import Embedding<PERSON>ector
from ..utils.metrics import MetricsCollector
from ..config.settings import get_settings

logger = structlog.get_logger()
settings = get_settings()
metrics = MetricsCollector()


class CacheManager:
    """High-performance cache manager with multiple optimization strategies"""

    def __init__(self):
        self.redis_client = get_redis_client()
        
        # Cache configuration
        self.query_cache_ttl = 3600  # 1 hour for query results
        self.embedding_cache_ttl = 86400  # 24 hours for embeddings
        self.hot_cache_ttl = 300  # 5 minutes for frequently accessed items
        
        # In-memory LRU cache for hot data
        self.memory_cache_size = 100
        self._init_memory_cache()
        
    def _init_memory_cache(self):
        """Initialize in-memory LRU caches"""
        self._query_memory_cache = {}
        self._embedding_memory_cache = {}
        self._access_counts = {}
        self._cache_timestamps = {}
        
    async def get_cached_query_result(
        self, 
        query: str, 
        repository_id: str,
        filters: Optional[Dict[str, Any]] = None
    ) -> Optional[QueryResult]:
        """Get cached query result with <100ms target latency"""
        start_time = time.time()
        
        try:
            cache_key = self._generate_query_cache_key(query, repository_id, filters)
            
            # Level 1: Check in-memory cache (fastest, ~1ms)
            memory_result = self._get_from_memory_cache(cache_key, "query")
            if memory_result:
                latency = (time.time() - start_time) * 1000
                logger.debug("cache_hit_memory", key=cache_key, latency_ms=latency)
                metrics.record_cache_hit("memory")
                return QueryResult(**memory_result)
            
            # Level 2: Check Redis cache (~10-50ms)
            redis_result = await self._get_from_redis(cache_key)
            if redis_result:
                latency = (time.time() - start_time) * 1000
                logger.debug("cache_hit_redis", key=cache_key, latency_ms=latency)
                metrics.record_cache_hit("redis")
                
                # Promote to memory cache if frequently accessed
                if self._should_promote_to_memory(cache_key):
                    self._set_in_memory_cache(cache_key, redis_result, "query")
                
                return QueryResult(**redis_result)
            
            latency = (time.time() - start_time) * 1000
            logger.debug("cache_miss", key=cache_key, latency_ms=latency)
            metrics.record_cache_miss()
            return None
            
        except Exception as e:
            logger.error("cache_get_error", error=str(e), exc_info=True)
            return None
            
    async def cache_query_result(
        self,
        query: str,
        repository_id: str,
        result: QueryResult,
        filters: Optional[Dict[str, Any]] = None
    ):
        """Cache query result with write-through strategy"""
        try:
            cache_key = self._generate_query_cache_key(query, repository_id, filters)
            result_data = result.model_dump()
            
            # Determine cache duration based on confidence
            ttl = self.query_cache_ttl
            if result.confidence < 0.7:
                ttl = ttl // 2  # Shorter TTL for low confidence results
            
            # Write to Redis (async)
            asyncio.create_task(
                self._set_in_redis(cache_key, result_data, ttl)
            )
            
            # Write to memory cache if hot
            if self._is_hot_query(query):
                self._set_in_memory_cache(cache_key, result_data, "query")
                
            logger.debug("cache_set", key=cache_key, ttl=ttl)
            
        except Exception as e:
            logger.error("cache_set_error", error=str(e), exc_info=True)
            
    async def get_cached_embedding(
        self,
        text: str,
        context_type: str = "query"
    ) -> Optional[List[float]]:
        """Get cached embedding vector"""
        try:
            cache_key = self._generate_embedding_cache_key(text, context_type)
            
            # Check memory cache first
            memory_result = self._get_from_memory_cache(cache_key, "embedding")
            if memory_result:
                metrics.record_cache_hit("memory")
                return memory_result
            
            # Check Redis
            redis_result = await self._get_from_redis(cache_key)
            if redis_result:
                metrics.record_cache_hit("redis")
                return redis_result
                
            metrics.record_cache_miss()
            return None
            
        except Exception as e:
            logger.error("embedding_cache_get_error", error=str(e))
            return None
            
    async def cache_embedding(
        self,
        text: str,
        embedding: List[float],
        context_type: str = "query"
    ):
        """Cache embedding vector"""
        try:
            cache_key = self._generate_embedding_cache_key(text, context_type)
            
            # Always cache embeddings in both layers (they're expensive to compute)
            self._set_in_memory_cache(cache_key, embedding, "embedding")
            await self._set_in_redis(cache_key, embedding, self.embedding_cache_ttl)
            
            logger.debug("embedding_cached", key=cache_key)
            
        except Exception as e:
            logger.error("embedding_cache_set_error", error=str(e))
            
    async def warm_cache(self, repository_id: str):
        """Pre-warm cache with common queries for a repository"""
        try:
            # Get popular queries from analytics (if available)
            popular_queries = await self._get_popular_queries(repository_id)
            
            for query in popular_queries[:10]:  # Top 10 queries
                cache_key = self._generate_query_cache_key(query, repository_id)
                
                # Check if already cached
                if not await self._exists_in_redis(cache_key):
                    # Mark for background processing
                    await self.redis_client.sadd(
                        f"cache:warm:queue:{repository_id}", 
                        query
                    )
                    
            logger.info("cache_warming_scheduled", repository_id=repository_id)
            
        except Exception as e:
            logger.error("cache_warming_error", error=str(e))
            
    def invalidate_repository_cache(self, repository_id: str):
        """Invalidate all cached data for a repository"""
        try:
            # Clear from memory cache
            keys_to_remove = [
                k for k in self._query_memory_cache.keys() 
                if repository_id in k
            ]
            for key in keys_to_remove:
                self._query_memory_cache.pop(key, None)
                
            # Mark for Redis invalidation (async)
            asyncio.create_task(
                self._invalidate_redis_pattern(f"*{repository_id}*")
            )
            
            logger.info("cache_invalidated", repository_id=repository_id)
            
        except Exception as e:
            logger.error("cache_invalidation_error", error=str(e))
            
    # Private helper methods
    
    def _generate_query_cache_key(
        self, 
        query: str, 
        repository_id: str,
        filters: Optional[Dict[str, Any]] = None
    ) -> str:
        """Generate deterministic cache key for query"""
        key_parts = [
            "query_cache",
            repository_id,
            hashlib.md5(query.encode()).hexdigest()
        ]
        
        if filters:
            # Sort filters for consistent keys
            filter_str = json.dumps(filters, sort_keys=True)
            key_parts.append(hashlib.md5(filter_str.encode()).hexdigest()[:8])
            
        return ":".join(key_parts)
        
    def _generate_embedding_cache_key(self, text: str, context_type: str) -> str:
        """Generate cache key for embeddings"""
        text_hash = hashlib.md5(text.encode()).hexdigest()
        return f"embedding:{context_type}:{text_hash}"
        
    def _get_from_memory_cache(self, key: str, cache_type: str) -> Optional[Any]:
        """Get from in-memory LRU cache"""
        cache = (
            self._query_memory_cache if cache_type == "query" 
            else self._embedding_memory_cache
        )
        
        if key in cache:
            # Update access count
            self._access_counts[key] = self._access_counts.get(key, 0) + 1
            self._cache_timestamps[key] = time.time()
            return cache[key]
            
        return None
        
    def _set_in_memory_cache(self, key: str, value: Any, cache_type: str):
        """Set in memory cache with LRU eviction"""
        cache = (
            self._query_memory_cache if cache_type == "query" 
            else self._embedding_memory_cache
        )
        
        # Evict if at capacity
        if len(cache) >= self.memory_cache_size:
            self._evict_lru(cache)
            
        cache[key] = value
        self._access_counts[key] = 1
        self._cache_timestamps[key] = time.time()
        
    def _evict_lru(self, cache: Dict):
        """Evict least recently used item"""
        if not cache:
            return
            
        # Find LRU key
        lru_key = min(
            cache.keys(),
            key=lambda k: self._cache_timestamps.get(k, 0)
        )
        
        cache.pop(lru_key, None)
        self._access_counts.pop(lru_key, None)
        self._cache_timestamps.pop(lru_key, None)
        
    async def _get_from_redis(self, key: str) -> Optional[Any]:
        """Get from Redis with deserialization"""
        try:
            data = await self.redis_client.get(key)
            if data:
                return json.loads(data)
        except Exception as e:
            logger.debug("redis_get_error", key=key, error=str(e))
        return None
        
    async def _set_in_redis(self, key: str, value: Any, ttl: int):
        """Set in Redis with serialization"""
        try:
            await self.redis_client.setex(
                key, 
                ttl, 
                json.dumps(value)
            )
        except Exception as e:
            logger.debug("redis_set_error", key=key, error=str(e))
            
    async def _exists_in_redis(self, key: str) -> bool:
        """Check if key exists in Redis"""
        try:
            return await self.redis_client.exists(key) > 0
        except Exception:
            return False
            
    async def _invalidate_redis_pattern(self, pattern: str):
        """Invalidate Redis keys matching pattern"""
        try:
            cursor = 0
            while True:
                cursor, keys = await self.redis_client.scan(
                    cursor, match=pattern, count=100
                )
                if keys:
                    await self.redis_client.delete(*keys)
                if cursor == 0:
                    break
        except Exception as e:
            logger.error("redis_invalidation_error", pattern=pattern, error=str(e))
            
    def _is_hot_query(self, query: str) -> bool:
        """Determine if query should be in hot cache"""
        # Simple heuristic: short queries are often repeated
        if len(query) < 50:
            return True
            
        # Check for common patterns
        hot_patterns = ["how", "what", "explain", "show", "find", "list"]
        query_lower = query.lower()
        return any(pattern in query_lower for pattern in hot_patterns)
        
    def _should_promote_to_memory(self, cache_key: str) -> bool:
        """Determine if item should be promoted to memory cache"""
        # Simple heuristic: promote items accessed more than once
        access_count = self._access_counts.get(cache_key, 0)
        return access_count >= 2
        
    async def _get_popular_queries(self, repository_id: str) -> List[str]:
        """Get popular queries for cache warming"""
        # This would connect to analytics/monitoring system
        # For now, return common query patterns
        return [
            "How does authentication work?",
            "Show me the main components",
            "What is the database schema?",
            "Find the API endpoints",
            "Explain the architecture",
            "Show error handling code",
            "Find security implementations",
            "List all services",
            "How to run tests?",
            "What are the dependencies?"
        ]
        

# Singleton instance
_cache_manager: Optional[CacheManager] = None


def get_cache_manager() -> CacheManager:
    """Get singleton cache manager instance"""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = CacheManager()
    return _cache_manager