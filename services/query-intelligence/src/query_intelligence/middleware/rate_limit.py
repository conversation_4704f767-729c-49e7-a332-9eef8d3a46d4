import time
from typing import Dict, Callable, Optional
from fastapi import Request, HTTPException, status
from fastapi.responses import Response
import structlog

from ..config.settings import get_settings
from ..clients.redis import get_redis_client

logger = structlog.get_logger()
settings = get_settings()


class RateLimiter:
    """Rate limiting implementation using Redis"""

    def __init__(
        self,
        requests_per_window: Optional[int] = None,
        window_seconds: Optional[int] = None,
        key_prefix: str = "rate_limit",
    ):
        self.requests_per_window = requests_per_window or settings.RATE_LIMIT_REQUESTS
        self.window_seconds = window_seconds or settings.RATE_LIMIT_WINDOW_SECONDS
        self.key_prefix = key_prefix
        self.redis_client = get_redis_client()

    async def is_allowed(self, identifier: str) -> tuple[bool, Dict[str, int]]:
        """Check if request is allowed under rate limit"""
        key = f"{self.key_prefix}:{identifier}"

        try:
            # Use Redis pipeline for atomic operations
            pipe = self.redis_client.pipeline()
            now = int(time.time())
            window_start = now - self.window_seconds

            # Remove old entries
            pipe.zremrangebyscore(key, 0, window_start)

            # Count requests in current window
            pipe.zcard(key)

            # Add current request
            pipe.zadd(key, {str(now): now})

            # Set expiry
            pipe.expire(key, self.window_seconds + 1)

            results = await pipe.execute()
            request_count = results[1]

            # Check if limit exceeded
            if request_count >= self.requests_per_window:
                return False, {
                    "limit": self.requests_per_window,
                    "remaining": 0,
                    "reset": now + self.window_seconds,
                }

            return True, {
                "limit": self.requests_per_window,
                "remaining": self.requests_per_window - request_count - 1,
                "reset": now + self.window_seconds,
            }

        except Exception as e:
            logger.error("rate_limit_check_failed", error=str(e))
            # Allow request on Redis failure
            return True, {
                "limit": self.requests_per_window,
                "remaining": -1,
                "reset": -1,
            }

    def get_identifier(self, request: Request) -> str:
        """Get identifier for rate limiting (IP or user ID)"""
        # Try to get user ID from request state (set by auth middleware)
        if hasattr(request.state, "user") and request.state.user:
            return f"user:{request.state.user.get('user_id')}"

        # Fall back to IP address
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        else:
            client_ip = request.client.host if request.client else "unknown"

        return f"ip:{client_ip}"


# Global rate limiter instance
rate_limiter = RateLimiter()


async def rate_limit_middleware(request: Request, call_next: Callable) -> Response:
    """Middleware to enforce rate limiting"""
    # Skip rate limiting for health checks and metrics
    if request.url.path in ["/health", "/ready", "/metrics"]:
        return await call_next(request)

    try:
        identifier = rate_limiter.get_identifier(request)
        allowed, limit_info = await rate_limiter.is_allowed(identifier)

        if not allowed:
            logger.warning(
                "rate_limit_exceeded", identifier=identifier, path=request.url.path
            )
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded",
                headers={
                    "X-RateLimit-Limit": str(limit_info["limit"]),
                    "X-RateLimit-Remaining": str(limit_info["remaining"]),
                    "X-RateLimit-Reset": str(limit_info["reset"]),
                    "Retry-After": str(settings.RATE_LIMIT_WINDOW_SECONDS),
                },
            )

        # Add rate limit headers to response
        response = await call_next(request)
        response.headers["X-RateLimit-Limit"] = str(limit_info["limit"])
        response.headers["X-RateLimit-Remaining"] = str(limit_info["remaining"])
        response.headers["X-RateLimit-Reset"] = str(limit_info["reset"])

        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error("rate_limit_middleware_error", error=str(e), exc_info=True)
        # On unexpected errors, allow the request
        return await call_next(request)


def create_rate_limit_dependency(
    requests: int = 10, window: int = 60, key_prefix: str = "endpoint"
):
    """Create a rate limit dependency for specific endpoints"""
    limiter = RateLimiter(requests, window, key_prefix)

    async def rate_limit_check(request: Request):
        identifier = limiter.get_identifier(request)
        allowed, limit_info = await limiter.is_allowed(identifier)

        if not allowed:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded for this endpoint",
                headers={
                    "X-RateLimit-Limit": str(limit_info["limit"]),
                    "X-RateLimit-Remaining": str(limit_info["remaining"]),
                    "X-RateLimit-Reset": str(limit_info["reset"]),
                    "Retry-After": str(window),
                },
            )

    return rate_limit_check
