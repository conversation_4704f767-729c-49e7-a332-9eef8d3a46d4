# Query Intelligence Service Documentation

## 📍 Documentation Location

**Primary Documentation**: [`/docs/query-intelligence/`](../../../docs/query-intelligence/)

All Query Intelligence Service documentation has been consolidated and is maintained in the main documentation directory to ensure:

- ✅ **Single Source of Truth**: All documentation in one authoritative location
- ✅ **Consistency**: Unified documentation structure following enterprise standards
- ✅ **Maintenance**: Simplified updates and version control
- ✅ **Discovery**: Easy navigation and comprehensive coverage

## 🔗 Quick Links

### **Main Documentation Hub**
[**📚 Complete Documentation**](../../../docs/query-intelligence/README.md) - Start here for all service documentation

### **Developer Resources**
- [**🔌 API Reference**](../../../docs/query-intelligence/api/README.md) - REST API and WebSocket documentation
- [**💻 Developer Guide**](../../../docs/query-intelligence/guides/developer-guide.md) - Local development setup
- [**🏗️ Architecture Guide**](../../../docs/query-intelligence/architecture/README.md) - System design and patterns

### **Operations Resources**
- [**🚀 Deployment Guide**](../../../docs/query-intelligence/deployment/production-deployment.md) - Production deployment procedures
- [**🛠️ Operations Runbook**](../../../docs/query-intelligence/operations/runbook.md) - Operational procedures and monitoring
- [**🔧 Troubleshooting Guide**](../../../docs/query-intelligence/troubleshooting/README.md) - Issue resolution

### **Quality Assurance**
- [**🧪 Testing Strategy**](../../../docs/query-intelligence/testing/strategy.md) - Comprehensive testing approach
- [**📊 Production Status**](../../../docs/query-intelligence/README.md#production-status) - Current production readiness

## 🎯 Service Status

**Production Ready**: ✅ **100% Complete**
- **Test Coverage**: 85%+ (exceeds 80% target)
- **Performance**: 1000+ QPS sustained, 85ms p95 response time
- **Security**: 95/100 security score with OWASP compliance
- **Deployment**: Successfully deployed to Google Cloud Run

## 📋 Documentation Standards

This service follows the enterprise documentation pattern established by the analysis-engine service:

- **Organized Structure**: Clear categorization by audience (developers, operations, support)
- **Evidence-Based**: All claims backed by testing and metrics
- **Executable Procedures**: All commands validated and working
- **Version Controlled**: Single source with proper change management

---

**For complete documentation, visit**: [`/docs/query-intelligence/`](../../../docs/query-intelligence/)

**Service URL**: https://query-intelligence-l3nxty7oka-uc.a.run.app  
**Support**: #query-intelligence-support