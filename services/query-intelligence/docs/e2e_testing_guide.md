# E2E Testing Guide for Query Intelligence Service

## Overview

This comprehensive End-to-End (E2E) testing suite validates the complete user experience and system reliability of the Query Intelligence Service. The tests cover authentication, query processing, WebSocket streaming, service integration, performance, error recovery, and production scenarios.

## 🎯 Test Coverage

### Test Categories

1. **User Journey Tests** (`test_complete_user_journey.py`)
   - Complete authenticated API query flow
   - WebSocket streaming functionality
   - Multi-query sessions
   - Error recovery scenarios
   - Performance regression detection

2. **Service Integration Tests** (`test_service_integration.py`)
   - Multi-service interaction validation
   - Circuit breaker functionality
   - Service dependency management
   - Timeout and fallback handling
   - Cross-service data flow

3. **Performance Tests** (`test_performance_scenarios.py`)
   - Concurrent API load testing
   - WebSocket streaming performance
   - Sustained load validation
   - Mixed workload scenarios
   - Resource monitoring

4. **Error Recovery Tests** (`test_error_recovery.py`)
   - Redis failure recovery
   - Analysis Engine fallback
   - Multiple service failures
   - Graceful degradation levels
   - System resilience validation

5. **Cache Behavior Tests** (`test_cache_behavior.py`)
   - Cache hit/miss scenarios
   - Cache invalidation
   - TTL behavior validation
   - Concurrent cache access
   - Cache failover handling

6. **Production Scenarios** (`test_production_scenarios.py`)
   - Realistic user behavior simulation
   - Peak load handling
   - Edge case management
   - Security validation
   - Comprehensive readiness checks

## 🚀 Quick Start

### Prerequisites

```bash
# Install dependencies
poetry install

# Install additional E2E testing dependencies
poetry add --group dev websockets psutil
```

### Running Tests

```bash
# Run smoke tests (quick validation)
python scripts/run_e2e_tests.py smoke

# Run full E2E suite
python scripts/run_e2e_tests.py full

# Run specific test category
python scripts/run_e2e_tests.py performance

# Run with verbose output and coverage
python scripts/run_e2e_tests.py integration --verbose --coverage
```

## 📊 Test Suites

### Available Test Suites

| Suite | Description | Duration | Use Case |
|-------|-------------|----------|----------|
| `smoke` | Quick smoke tests for basic functionality | 5 minutes | CI/CD pipeline |
| `integration` | Service integration tests | 10 minutes | Pre-deployment |
| `performance` | Performance and load testing | 30 minutes | Performance validation |
| `resilience` | Error recovery and fallback tests | 15 minutes | Reliability testing |
| `cache` | Cache behavior validation | 10 minutes | Cache system testing |
| `production` | Production-like scenarios | 60 minutes | Production readiness |
| `websocket` | WebSocket functionality tests | 10 minutes | Real-time features |
| `concurrent` | Concurrent execution tests | 15 minutes | Concurrency validation |
| `full` | Complete E2E test suite | 120 minutes | Comprehensive validation |

### Test Suite Commands

```bash
# List available test suites
python scripts/run_e2e_tests.py --list-suites

# Run multiple suites
python scripts/run_e2e_tests.py smoke integration performance

# Run with specific options
python scripts/run_e2e_tests.py production --environment staging --html-report
```

## 🌍 Environment Configuration

### Available Environments

| Environment | Description | Service URL | Mocks |
|-------------|-------------|-------------|-------|
| `local` | Local development | `http://localhost:8000` | Enabled |
| `dev` | Development environment | `http://dev.example.com` | Disabled |
| `staging` | Staging environment | `http://staging.example.com` | Disabled |
| `production` | Production environment | `http://api.example.com` | Disabled |

### Environment Variables

```bash
# Service configuration
export E2E_SERVICE_URL="http://localhost:8000"
export E2E_WEBSOCKET_URL="ws://localhost:8000"
export E2E_REDIS_URL="redis://localhost:6379"
export E2E_ANALYSIS_ENGINE_URL="http://localhost:8001"
export E2E_PATTERN_MINING_URL="http://localhost:8002"

# Test configuration
export E2E_ENABLE_MOCKS="true"
export E2E_TEST_TIMEOUT="300"
export E2E_CONCURRENT_USERS="10"
export E2E_JWT_SECRET="test_secret_key"
```

## 🔧 Configuration and Customization

### Test Configuration

The E2E tests use a comprehensive configuration system:

```python
# tests/e2e/conftest.py
TEST_CONFIG = {
    "service_base_url": "http://localhost:8000",
    "websocket_base_url": "ws://localhost:8000",
    "concurrent_users": 10,
    "test_timeout": 300,
    "enable_mocks": True
}
```

### Custom Test Markers

```bash
# Run tests with specific markers
pytest -m "user_journey and not slow"
pytest -m "performance or concurrent"
pytest -m "e2e and not production"
```

### Performance Thresholds

```python
# Configurable performance thresholds
PERFORMANCE_THRESHOLDS = {
    "max_avg_response_time": 3.0,
    "max_p95_response_time": 8.0,
    "min_success_rate": 95.0,
    "min_requests_per_second": 5.0
}
```

## 📈 Performance Testing

### Load Testing Scenarios

```python
# Concurrent API load testing
async def test_concurrent_api_load():
    concurrent_users = 25
    requests_per_user = 4
    # Validates system under realistic load
```

### WebSocket Performance

```python
# WebSocket streaming performance
async def test_websocket_streaming_performance():
    concurrent_connections = 10
    messages_per_connection = 3
    # Validates real-time streaming capabilities
```

### Sustained Load Testing

```python
# Sustained load validation
async def test_sustained_load_performance():
    requests_per_second = 5.0
    duration_seconds = 30
    # Validates system stability over time
```

## 🛡️ Error Recovery Testing

### Service Failure Scenarios

```python
# Redis failure recovery
async def test_redis_failure_recovery():
    # Simulates Redis failure and validates graceful degradation
    service_mock_manager.fail_service("redis")
    # System should continue operating without Redis
```

### Circuit Breaker Testing

```python
# Circuit breaker validation
async def test_circuit_breaker_behavior():
    # Tests circuit breaker functionality
    # Validates failure detection and recovery
```

### Multi-Service Failures

```python
# Multiple service failure handling
async def test_multiple_service_failure_recovery():
    # Tests system resilience with multiple service failures
    # Validates minimal functionality maintenance
```

## 🔄 Cache Behavior Testing

### Cache Hit/Miss Scenarios

```python
# Cache performance validation
async def test_cache_hit_behavior():
    # Tests cache hit performance
    # Validates cache consistency
```

### Cache Invalidation

```python
# Cache invalidation testing
async def test_cache_invalidation_behavior():
    # Tests cache invalidation mechanisms
    # Validates cache refresh behavior
```

## 🎭 Production Scenarios

### Realistic User Simulation

```python
# Realistic user behavior simulation
async def test_realistic_user_behavior_simulation():
    # Simulates real user patterns
    # Mixed API and WebSocket usage
    # Validates system under realistic load
```

### Peak Load Handling

```python
# Peak load validation
async def test_peak_load_handling():
    # Tests system behavior during peak usage
    # Validates scalability characteristics
```

### Security Validation

```python
# Production security testing
async def test_production_security_validation():
    # Tests security headers and validation
    # Validates input sanitization
    # Tests rate limiting effectiveness
```

## 📊 Reporting and Analysis

### Test Reports

```bash
# Generate HTML report
python scripts/run_e2e_tests.py full --html-report

# Generate JSON report
python scripts/run_e2e_tests.py performance --json-report

# Generate JUnit XML
python scripts/run_e2e_tests.py integration --junit-xml
```

### Coverage Reports

```bash
# Generate coverage report
python scripts/run_e2e_tests.py smoke --coverage

# Coverage files generated:
# - htmlcov/index.html (HTML report)
# - coverage.xml (XML report)
```

### Performance Metrics

The E2E tests collect comprehensive performance metrics:

- Response times (average, median, P95, P99)
- Throughput (requests per second)
- Success rates
- Resource utilization (CPU, memory)
- Error rates and types

## 🔍 Debugging and Troubleshooting

### Verbose Output

```bash
# Run with verbose output
python scripts/run_e2e_tests.py smoke --verbose

# Run with very verbose output
python scripts/run_e2e_tests.py integration --very-verbose

# Capture subprocess output
python scripts/run_e2e_tests.py performance --capture-output
```

### Test Isolation

```bash
# Run specific test
python scripts/run_e2e_tests.py smoke -k "test_authenticated_api_query"

# Run last failed tests
python scripts/run_e2e_tests.py full --last-failed

# Run failed tests first
python scripts/run_e2e_tests.py integration --failed-first
```

### Debugging Failed Tests

```bash
# Stop on first failure
python scripts/run_e2e_tests.py full --fail-fast

# Run with no capture for debugging
python scripts/run_e2e_tests.py smoke --capture=no

# Rerun flaky tests
python scripts/run_e2e_tests.py performance --reruns 3
```

## 🚀 CI/CD Integration

### GitHub Actions Example

```yaml
name: E2E Tests

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        poetry install
        poetry add --group dev websockets psutil
    
    - name: Start services
      run: |
        docker-compose up -d
        sleep 30
    
    - name: Run E2E smoke tests
      run: |
        python scripts/run_e2e_tests.py smoke --junit-xml
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      with:
        name: e2e-test-results
        path: test_reports/
```

### Jenkins Pipeline Example

```groovy
pipeline {
    agent any
    
    stages {
        stage('Setup') {
            steps {
                sh 'poetry install'
                sh 'poetry add --group dev websockets psutil'
            }
        }
        
        stage('Start Services') {
            steps {
                sh 'docker-compose up -d'
                sh 'sleep 30'
            }
        }
        
        stage('E2E Tests') {
            parallel {
                stage('Smoke Tests') {
                    steps {
                        sh 'python scripts/run_e2e_tests.py smoke --junit-xml'
                    }
                }
                
                stage('Integration Tests') {
                    steps {
                        sh 'python scripts/run_e2e_tests.py integration --junit-xml'
                    }
                }
            }
        }
        
        stage('Performance Tests') {
            when {
                branch 'main'
            }
            steps {
                sh 'python scripts/run_e2e_tests.py performance --html-report'
            }
        }
    }
    
    post {
        always {
            archiveArtifacts artifacts: 'test_reports/**/*', allowEmptyArchive: true
            publishHTML([
                allowMissing: false,
                alwaysLinkToLastBuild: false,
                keepAll: true,
                reportDir: 'test_reports',
                reportFiles: '*.html',
                reportName: 'E2E Test Report'
            ])
        }
    }
}
```

## 🎯 Best Practices

### Test Organization

1. **Test Categories**: Organize tests by functionality and purpose
2. **Fixtures**: Use reusable fixtures for setup and teardown
3. **Markers**: Use pytest markers for test categorization
4. **Isolation**: Ensure tests are independent and can run in any order

### Performance Testing

1. **Realistic Load**: Use realistic user patterns and load levels
2. **Baseline Metrics**: Establish performance baselines
3. **Monitoring**: Monitor system resources during tests
4. **Thresholds**: Define clear performance acceptance criteria

### Error Handling

1. **Graceful Degradation**: Test system behavior under failure
2. **Recovery Testing**: Validate system recovery capabilities
3. **Edge Cases**: Test boundary conditions and error scenarios
4. **Monitoring**: Validate error detection and alerting

### Maintenance

1. **Regular Updates**: Keep tests updated with system changes
2. **Flaky Tests**: Identify and fix unstable tests
3. **Performance Regression**: Monitor for performance degradation
4. **Documentation**: Keep test documentation current

## 📝 Contributing

### Adding New Tests

1. Create test files in appropriate categories
2. Use descriptive test names and docstrings
3. Add appropriate pytest markers
4. Include performance assertions
5. Add to test suite configuration

### Test Fixtures

```python
# Example test fixture
@pytest.fixture
async def websocket_client(websocket_base_url):
    client = WebSocketTestClient(websocket_base_url)
    yield client
    if client.connected:
        await client.disconnect()
```

### Performance Assertions

```python
# Example performance assertion
assert avg_response_time <= 3.0, f"Response time too high: {avg_response_time:.2f}s"
assert success_rate >= 95.0, f"Success rate too low: {success_rate:.1f}%"
```

## 🏆 Production Readiness Validation

The E2E test suite provides comprehensive production readiness validation:

### Readiness Criteria

1. **Functionality**: All core features working correctly
2. **Performance**: Meeting performance thresholds
3. **Reliability**: Error recovery and fallback mechanisms
4. **Security**: Authentication and input validation
5. **Scalability**: Handling concurrent load

### Readiness Score

```python
# Production readiness scoring
def calculate_readiness_score():
    score = 0
    
    # Health check (25 points)
    if health_status == "healthy":
        score += 25
    
    # API functionality (25 points)
    if api_confidence >= 0.7:
        score += 25
    
    # WebSocket functionality (25 points)
    if websocket_working:
        score += 25
    
    # Performance (25 points)
    if performance_meets_thresholds:
        score += 25
    
    return score
```

### Readiness Thresholds

- **90+ points**: ✅ PRODUCTION READY - Excellent
- **80-89 points**: ✅ PRODUCTION READY - Good
- **< 80 points**: ❌ NOT PRODUCTION READY

## 🔮 Future Enhancements

### Planned Improvements

1. **Chaos Engineering**: Add chaos testing scenarios
2. **Load Testing**: Enhanced load testing with multiple patterns
3. **Monitoring Integration**: Real-time monitoring during tests
4. **AI-Powered Testing**: Intelligent test generation
5. **Visual Testing**: Screenshot and visual regression testing

### Extensibility

The E2E testing framework is designed to be extensible:

- Add new test categories
- Integrate with monitoring systems
- Support for new service dependencies
- Custom reporting formats
- Integration with external tools

---

## 📞 Support

For questions, issues, or contributions:

1. **Documentation**: Check this guide and inline documentation
2. **Issues**: Create GitHub issues for bugs or feature requests
3. **Discussions**: Use GitHub discussions for questions
4. **Contributing**: Follow the contribution guidelines

---

**E2E Testing Suite v1.0** - Comprehensive production validation for Query Intelligence Service