# Performance Regression Testing Suite

## Overview

The Performance Regression Testing Suite is a comprehensive framework for automated performance testing, regression detection, and continuous monitoring of the query-intelligence service. It provides systematic performance baseline management, statistical regression detection, and CI/CD integration to ensure consistent performance quality.

## Features

### 🏗️ Core Components

1. **Performance Baseline Management**
   - Version-controlled performance baselines
   - Automatic baseline creation from test results
   - Baseline comparison and regression detection
   - Configurable retention and cleanup policies

2. **Statistical Regression Detection**
   - <PERSON>'s t-test for statistical significance
   - Mann-Whitney U test for non-parametric analysis
   - <PERSON>'s d effect size calculation
   - Anomaly detection using z-scores
   - Confidence interval calculations

3. **Comprehensive Benchmark Suite**
   - Single endpoint performance testing
   - Multi-endpoint load testing
   - Stress testing and endurance testing
   - Cache performance analysis
   - WebSocket performance testing

4. **CI/CD Performance Gates**
   - Automated performance testing in CI/CD pipelines
   - Configurable performance thresholds
   - Deployment blocking based on regression detection
   - GitHub Actions integration

5. **Performance Trend Analysis**
   - Long-term performance trend monitoring
   - Predictive analysis and early warning alerts
   - Seasonal pattern detection
   - Volatility analysis

## Architecture

```
tests/regression/
├── __init__.py                    # Package initialization
├── performance_baselines.py       # Baseline management system
├── regression_tests.py           # Regression detection algorithms
├── benchmark_suite.py            # Comprehensive benchmarking
├── ci_performance_gates.py       # CI/CD integration
├── trend_analysis.py             # Performance trend analysis
└── test_regression_suite.py      # Comprehensive test suite
```

## Quick Start

### 1. Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Install additional dependencies for regression testing
pip install scipy numpy
```

### 2. Basic Usage

```bash
# Make the CLI executable
chmod +x scripts/run_performance_regression_tests.py

# Create a performance baseline
python scripts/run_performance_regression_tests.py create-baseline \
  --service-url http://localhost:8000 \
  --baseline-id main_branch \
  --version 1.0.0 \
  --service-version v1.2.3

# Run regression test
python scripts/run_performance_regression_tests.py regression-test \
  --service-url http://localhost:8000 \
  --baseline-id main_branch

# Run benchmark suite
python scripts/run_performance_regression_tests.py benchmark \
  --service-url http://localhost:8000 \
  --suite-type comprehensive

# Run CI/CD performance gates
python scripts/run_performance_regression_tests.py ci-gates \
  --service-url http://localhost:8000 \
  --gates-config performance_gates.json
```

### 3. Python API Usage

```python
from tests.regression.performance_baselines import BaselineManager, BaselineMetrics
from tests.regression.regression_tests import RegressionTest, RegressionTestConfig

# Initialize baseline manager
baseline_manager = BaselineManager("performance_baselines")

# Create baseline metrics
metrics = BaselineMetrics(
    avg_response_time=0.85,
    p95_response_time=1.20,
    requests_per_second=25.0,
    success_rate=98.5,
    error_rate=1.5
)

# Create baseline
baseline = baseline_manager.create_baseline(
    baseline_id="my_baseline",
    version="1.0.0",
    service_version="v1.2.3",
    metrics=metrics,
    test_config={"test_type": "api"}
)

# Run regression test
async def test_function():
    # Your test implementation
    return {"success": True, "response_time": 0.95}

regression_test = RegressionTest(baseline_manager)
config = RegressionTestConfig(concurrent_users=10, requests_per_user=5)
result = await regression_test.run_regression_test("my_baseline", test_function, config)

print(f"Regression detected: {result.is_regression}")
print(f"Confidence score: {result.confidence_score}")
```

## Configuration

### Performance Gates Configuration

Create a `performance_gates.json` file to configure CI/CD gates:

```json
{
  "gates": [
    {
      "gate_id": "performance_regression_gate",
      "name": "Performance Regression Gate",
      "description": "Detects performance regressions in API response times",
      "max_response_time_ms": 2000,
      "min_throughput_rps": 10.0,
      "max_error_rate_percent": 1.0,
      "max_response_time_regression": 20.0,
      "max_throughput_regression": 15.0,
      "max_error_rate_regression": 0.5,
      "action": "block",
      "baseline_id": "main_branch_baseline",
      "test_duration": 60.0,
      "concurrent_users": 10,
      "requests_per_user": 5
    },
    {
      "gate_id": "resource_usage_gate",
      "name": "Resource Usage Gate",
      "description": "Monitors CPU and memory usage during load testing",
      "max_cpu_percent": 80.0,
      "max_memory_percent": 80.0,
      "action": "warn",
      "baseline_id": "resource_baseline"
    }
  ]
}
```

### Regression Test Configuration

```python
config = RegressionTestConfig(
    test_duration=60.0,          # Test duration in seconds
    warmup_duration=10.0,        # Warmup duration in seconds
    concurrent_users=10,         # Number of concurrent users
    requests_per_user=5,         # Requests per user
    confidence_level=0.95,       # Statistical confidence level
    significance_threshold=0.05, # P-value threshold
    effect_size_threshold=0.2,   # Cohen's d threshold
    response_time_threshold=20.0, # % increase threshold
    throughput_threshold=15.0,   # % decrease threshold
    error_rate_threshold=0.5,    # % increase threshold
    max_retries=3,               # Maximum retry attempts
    retry_delay=5.0              # Delay between retries
)
```

## Integration with CI/CD

### GitHub Actions Integration

```yaml
name: Performance Regression Tests

on:
  pull_request:
    branches: [main]
  push:
    branches: [main]

jobs:
  performance-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install scipy numpy
    
    - name: Start service
      run: |
        # Start your service here
        python -m uvicorn main:app --host 0.0.0.0 --port 8000 &
        sleep 10
    
    - name: Run performance gates
      run: |
        python scripts/run_performance_regression_tests.py ci-gates \
          --service-url http://localhost:8000 \
          --gates-config performance_gates.json \
          --auth-token ${{ secrets.API_TOKEN }} \
          --github-output
      env:
        API_TOKEN: ${{ secrets.API_TOKEN }}
    
    - name: Upload performance results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: performance-results
        path: performance_gate_results.json
```

### Docker Integration

```dockerfile
# Add to your Dockerfile for CI testing
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
RUN pip install scipy numpy

COPY . .

# Run performance tests
CMD ["python", "scripts/run_performance_regression_tests.py", "ci-gates", "--service-url", "http://localhost:8000"]
```

## Advanced Features

### Custom Benchmark Tests

```python
from tests.regression.benchmark_suite import BenchmarkConfig, BenchmarkType, BenchmarkRunner

# Create custom benchmark configuration
config = BenchmarkConfig(
    benchmark_id="custom_benchmark",
    benchmark_type=BenchmarkType.LOAD_TEST,
    duration=120.0,
    concurrent_users=20,
    requests_per_user=10,
    endpoints=["/api/v1/query", "/api/v1/search"],
    query_patterns=[
        "What is authentication?",
        "How does caching work?",
        "Find security-related functions"
    ],
    max_response_time=3.0,
    min_throughput=15.0,
    max_error_rate=2.0
)

# Run custom benchmark
benchmark_runner = BenchmarkRunner(baseline_manager)
result = await benchmark_runner.run_benchmark(config, auth_headers)
```

### Performance Trend Monitoring

```python
from tests.regression.trend_analysis import TrendAnalyzer

# Initialize trend analyzer
trend_analyzer = TrendAnalyzer(baseline_manager)

# Analyze trends for a baseline
trends = trend_analyzer.analyze_baseline_trends("main_branch", days=30)

# Generate alerts based on trends
alerts = trend_analyzer.generate_trend_alerts(trends)

# Display alerts
for alert in alerts:
    print(f"Alert: {alert.title}")
    print(f"Severity: {alert.severity.value}")
    print(f"Recommendation: {alert.recommendation}")
```

### Custom Regression Detection

```python
from tests.regression.regression_tests import RegressionDetector

# Initialize detector
detector = RegressionDetector(baseline_manager)

# Analyze specific metrics
baseline_data = [1.0, 1.1, 0.9, 1.05, 0.95]
current_data = [1.3, 1.4, 1.2, 1.35, 1.25]

# Perform statistical tests
t_stat, p_value = detector.welch_t_test(baseline_data, current_data)
effect_size = detector.cohens_d(baseline_data, current_data)

# Comprehensive analysis
analysis = detector.analyze_performance_regression(
    baseline_data, current_data, "response_time", config
)

print(f"Regression detected: {analysis['is_regression']}")
print(f"Statistical significance: {analysis['is_statistically_significant']}")
print(f"Effect size: {analysis['effect_size']}")
```

## Performance Metrics

### Baseline Metrics

- **Response Time**: Average, P50, P95, P99, and maximum response times
- **Throughput**: Requests per second and concurrent request handling
- **Success Rate**: Percentage of successful requests
- **Error Rate**: Percentage of failed requests
- **Resource Usage**: CPU and memory utilization
- **Cache Performance**: Hit/miss rates and cache effectiveness

### Regression Criteria

- **Response Time**: >20% increase in average response time
- **Throughput**: >15% decrease in requests per second
- **Error Rate**: >0.5% increase in error rate
- **Resource Usage**: >25% increase in CPU or memory usage
- **Statistical Significance**: P-value < 0.05
- **Effect Size**: Cohen's d > 0.2

## Monitoring and Alerting

### Real-time Monitoring

The system provides real-time performance monitoring through:

- **Metrics Collection**: Continuous collection of performance metrics
- **Threshold Monitoring**: Real-time threshold violation detection
- **Alert Generation**: Automated alert generation for performance issues
- **Trend Analysis**: Long-term trend monitoring and prediction

### Integration with Monitoring Systems

```python
# Example Prometheus metrics integration
from prometheus_client import Counter, Histogram, Gauge

# Performance metrics
response_time_histogram = Histogram('performance_response_time_seconds', 
                                  'Response time distribution')
throughput_gauge = Gauge('performance_throughput_rps', 
                        'Current throughput in requests per second')
regression_counter = Counter('performance_regressions_total', 
                           'Total number of regressions detected')

# Record metrics in your tests
response_time_histogram.observe(response_time)
throughput_gauge.set(requests_per_second)
if is_regression:
    regression_counter.inc()
```

## Best Practices

### 1. Baseline Management

- **Regular Updates**: Update baselines regularly to reflect current performance
- **Environment Consistency**: Use consistent environments for baseline creation
- **Version Control**: Tag baselines with service versions and git commits
- **Retention Policy**: Implement cleanup policies for old baselines

### 2. Test Configuration

- **Appropriate Duration**: Use sufficient test duration for statistical significance
- **Realistic Load**: Configure load that represents realistic usage patterns
- **Consistent Environment**: Ensure test environment consistency
- **Proper Warmup**: Include warmup periods to avoid cold start effects

### 3. Regression Detection

- **Statistical Significance**: Ensure sufficient sample size for statistical tests
- **Effect Size**: Consider both statistical and practical significance
- **Multiple Metrics**: Monitor multiple performance metrics
- **Threshold Tuning**: Adjust thresholds based on service requirements

### 4. CI/CD Integration

- **Gate Configuration**: Configure appropriate gates for your deployment process
- **Failure Handling**: Implement proper failure handling and notifications
- **Performance Budget**: Set realistic performance budgets
- **Continuous Monitoring**: Monitor performance continuously, not just during deployment

## Troubleshooting

### Common Issues

1. **Insufficient Data Points**
   - Ensure minimum data points for statistical analysis
   - Increase test duration or concurrent users
   - Check for test failures reducing sample size

2. **Inconsistent Results**
   - Verify environment consistency
   - Check for external factors affecting performance
   - Increase test duration to reduce variability

3. **False Positives**
   - Adjust statistical thresholds
   - Consider effect size in addition to statistical significance
   - Implement multiple test runs for verification

4. **Performance Degradation**
   - Check system resources during testing
   - Verify network conditions
   - Review recent code changes

### Debug Mode

```bash
# Run with verbose output
python scripts/run_performance_regression_tests.py regression-test \
  --service-url http://localhost:8000 \
  --baseline-id main_branch \
  --verbose

# Enable debug logging
export DEBUG=1
python scripts/run_performance_regression_tests.py benchmark \
  --service-url http://localhost:8000 \
  --suite-type comprehensive
```

## Contributing

### Development Setup

```bash
# Clone repository
git clone <repository-url>
cd query-intelligence

# Install development dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Install regression testing dependencies
pip install scipy numpy

# Run tests
pytest tests/regression/test_regression_suite.py -v
```

### Adding New Tests

1. Create test configuration in `benchmark_suite.py`
2. Implement test logic using the benchmark framework
3. Add regression detection rules
4. Update CI/CD configuration
5. Add documentation

### Extending the Framework

- **Custom Metrics**: Add new performance metrics to `BaselineMetrics`
- **New Algorithms**: Implement additional regression detection algorithms
- **Integration**: Add integration with new monitoring systems
- **Alerts**: Extend alert generation and notification systems

## License

This performance regression testing suite is part of the query-intelligence service and follows the same license terms.