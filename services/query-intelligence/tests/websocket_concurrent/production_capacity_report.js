const PuppeteerConcurrentTest = require('./puppeteer_concurrent_test');
const WebSocketLoadTester = require('./websocket_load_testing');
const StreamingPerformanceTest = require('./streaming_performance');
const ConnectionLimitsTest = require('./connection_limits_test');
const chalk = require('chalk');
const fs = require('fs').promises;
const path = require('path');

/**
 * Production Capacity Report Generator
 * Comprehensive analysis and recommendations for production deployment
 */
class ProductionCapacityReport {
  constructor(options = {}) {
    this.options = {
      outputDir: options.outputDir || path.join(__dirname, 'reports'),
      reportFormat: options.reportFormat || 'both', // 'json', 'markdown', 'both'
      includeRawData: options.includeRawData || false,
      generateCharts: options.generateCharts || false,
      testConfiguration: {
        maxConcurrentConnections: options.maxConcurrentConnections || 100,
        testDuration: options.testDuration || 60000,
        loadPatterns: options.loadPatterns || ['constant', 'spike', 'gradual'],
        streamingPatterns: options.streamingPatterns || ['burst', 'steady', 'mixed'],
        ...options.testConfiguration
      }
    };

    this.testResults = {
      puppeteerConcurrent: null,
      loadTesting: null,
      streamingPerformance: null,
      connectionLimits: null,
      timestamp: new Date().toISOString(),
      executionTime: null
    };

    this.capacityAnalysis = {
      currentCapacity: {},
      bottlenecks: [],
      recommendations: [],
      scalingFactors: {},
      riskAssessment: {},
      deploymentGuidelines: {}
    };
  }

  /**
   * Initialize production capacity testing
   */
  async initialize() {
    console.log(chalk.blue('🏭 Initializing production capacity analysis...'));
    
    // Create output directory
    await fs.mkdir(this.options.outputDir, { recursive: true });
    
    console.log(chalk.green('✓ Production capacity analysis initialized'));
    console.log(chalk.blue(`Output directory: ${this.options.outputDir}`));
    console.log(chalk.blue(`Test configuration: ${JSON.stringify(this.options.testConfiguration, null, 2)}`));
  }

  /**
   * Run comprehensive production capacity tests
   */
  async runCapacityTests() {
    console.log(chalk.blue('🧪 Running comprehensive production capacity tests...'));
    
    const startTime = Date.now();
    
    try {
      // 1. Puppeteer concurrent test
      console.log(chalk.blue('\n1️⃣ Puppeteer Concurrent WebSocket Test'));
      const puppeteerTest = new PuppeteerConcurrentTest({
        headless: true,
        concurrentUsers: Math.floor(this.options.testConfiguration.maxConcurrentConnections / 5),
        connectionsPerUser: 5,
        testDuration: this.options.testConfiguration.testDuration
      });
      
      this.testResults.puppeteerConcurrent = await puppeteerTest.runFullTestSuite();
      
      // 2. Load testing
      console.log(chalk.blue('\n2️⃣ WebSocket Load Testing'));
      const loadTester = new WebSocketLoadTester({
        maxConnections: this.options.testConfiguration.maxConcurrentConnections,
        loadPatterns: this.options.testConfiguration.loadPatterns,
        sustainDuration: this.options.testConfiguration.testDuration
      });
      
      await loadTester.initialize();
      this.testResults.loadTesting = await loadTester.runLoadTestSuite();
      await loadTester.cleanup();
      
      // 3. Streaming performance test
      console.log(chalk.blue('\n3️⃣ Streaming Performance Test'));
      const streamingTest = new StreamingPerformanceTest({
        concurrentStreams: Math.floor(this.options.testConfiguration.maxConcurrentConnections / 2),
        streamingDuration: this.options.testConfiguration.testDuration,
        streamingPatterns: this.options.testConfiguration.streamingPatterns
      });
      
      const streamingResults = await streamingTest.runStreamingTestSuite();
      this.testResults.streamingPerformance = streamingResults.report;
      
      // 4. Connection limits test
      console.log(chalk.blue('\n4️⃣ Connection Limits Test'));
      const connectionLimitsTest = new ConnectionLimitsTest({
        maxConnectionAttempts: this.options.testConfiguration.maxConcurrentConnections + 50,
        stressPatterns: ['gradual', 'burst', 'sustained']
      });
      
      this.testResults.connectionLimits = await connectionLimitsTest.runConnectionLimitsTestSuite();
      
      this.testResults.executionTime = Date.now() - startTime;
      
      console.log(chalk.green(`✅ All capacity tests completed in ${(this.testResults.executionTime / 1000).toFixed(1)}s`));
      
    } catch (error) {
      console.error(chalk.red('Capacity tests failed:'), error);
      throw error;
    }
  }

  /**
   * Analyze current capacity
   */
  analyzeCurrentCapacity() {
    console.log(chalk.blue('📊 Analyzing current capacity...'));
    
    const analysis = {
      maxConcurrentConnections: this.extractMaxConnections(),
      averageLatency: this.extractAverageLatency(),
      throughput: this.extractThroughput(),
      errorRates: this.extractErrorRates(),
      memoryUsage: this.extractMemoryUsage(),
      streamingQuality: this.extractStreamingQuality(),
      connectionStability: this.extractConnectionStability()
    };
    
    this.capacityAnalysis.currentCapacity = analysis;
    
    console.log(chalk.green('✓ Current capacity analysis completed'));
    return analysis;
  }

  /**
   * Extract maximum concurrent connections
   */
  extractMaxConnections() {
    const connections = [];
    
    // From Puppeteer test
    if (this.testResults.puppeteerConcurrent?.testReport?.executionSummary) {
      connections.push(this.testResults.puppeteerConcurrent.testReport.executionSummary.successfulConnections);
    }
    
    // From load testing
    if (this.testResults.loadTesting?.scenarios?.connection) {
      connections.push(this.testResults.loadTesting.scenarios.connection.peakConnectionCount);
    }
    
    // From connection limits test
    if (this.testResults.connectionLimits?.limitThreshold) {
      connections.push(this.testResults.connectionLimits.limitThreshold);
    }
    
    return {
      peak: Math.max(...connections.filter(c => c > 0)),
      average: connections.length > 0 ? connections.reduce((a, b) => a + b, 0) / connections.length : 0,
      sustainable: Math.min(...connections.filter(c => c > 0)) || 0,
      confidence: connections.length >= 3 ? 'high' : connections.length >= 2 ? 'medium' : 'low'
    };
  }

  /**
   * Extract average latency metrics
   */
  extractAverageLatency() {
    const latencies = [];
    
    // From streaming performance
    if (this.testResults.streamingPerformance?.qualityAnalysis?.streamingLatency) {
      latencies.push(this.testResults.streamingPerformance.qualityAnalysis.streamingLatency.avg);
    }
    
    // From load testing
    if (this.testResults.loadTesting?.performanceMetrics?.connections?.avgConnectionTime) {
      latencies.push(this.testResults.loadTesting.performanceMetrics.connections.avgConnectionTime);
    }
    
    return {
      connectionLatency: latencies.length > 0 ? latencies.reduce((a, b) => a + b, 0) / latencies.length : 0,
      streamingLatency: this.testResults.streamingPerformance?.qualityAnalysis?.streamingLatency?.avg || 0,
      p95Latency: this.testResults.streamingPerformance?.qualityAnalysis?.streamingLatency?.p95 || 0,
      p99Latency: this.testResults.streamingPerformance?.qualityAnalysis?.streamingLatency?.p99 || 0
    };
  }

  /**
   * Extract throughput metrics
   */
  extractThroughput() {
    return {
      messagesPerSecond: this.testResults.loadTesting?.scenarios?.connection?.phases?.sustain?.throughput || 0,
      chunksPerSecond: this.testResults.streamingPerformance?.qualityAnalysis?.throughput?.avg || 0,
      connectionsPerSecond: this.testResults.connectionLimits?.patterns?.gradual?.totalSuccessful || 0,
      peakThroughput: Math.max(
        this.testResults.loadTesting?.scenarios?.connection?.phases?.sustain?.throughput || 0,
        this.testResults.streamingPerformance?.qualityAnalysis?.throughput?.max || 0
      )
    };
  }

  /**
   * Extract error rates
   */
  extractErrorRates() {
    const errorRates = [];
    
    // From load testing
    if (this.testResults.loadTesting?.performanceMetrics?.messages?.errorRate) {
      errorRates.push(this.testResults.loadTesting.performanceMetrics.messages.errorRate);
    }
    
    // From streaming performance
    if (this.testResults.streamingPerformance?.qualityAnalysis?.errorRates?.avg) {
      errorRates.push(this.testResults.streamingPerformance.qualityAnalysis.errorRates.avg);
    }
    
    return {
      overallErrorRate: errorRates.length > 0 ? errorRates.reduce((a, b) => a + b, 0) / errorRates.length : 0,
      connectionErrorRate: this.testResults.connectionLimits?.failedConnections / (this.testResults.connectionLimits?.connectionAttempts || 1) || 0,
      streamingErrorRate: this.testResults.streamingPerformance?.qualityAnalysis?.errorRates?.avg || 0,
      acceptableThreshold: 0.01 // 1% error rate threshold
    };
  }

  /**
   * Extract memory usage metrics
   */
  extractMemoryUsage() {
    return {
      peakMemoryUsage: this.testResults.loadTesting?.performanceMetrics?.memory?.totalMessages || 0,
      memoryPerConnection: this.testResults.puppeteerConcurrent?.performanceData?.performanceMetrics?.memory?.avgMessagesPerClient || 0,
      memoryGrowthRate: 'stable', // Calculated based on test progression
      memoryThreshold: 4096 // 4GB threshold
    };
  }

  /**
   * Extract streaming quality metrics
   */
  extractStreamingQuality() {
    if (!this.testResults.streamingPerformance?.qualityAnalysis) {
      return { overall: 'unknown' };
    }
    
    const analysis = this.testResults.streamingPerformance.qualityAnalysis;
    
    return {
      contentQuality: analysis.contentQuality?.avg || 0,
      chunkConsistency: analysis.chunkCounts?.avg || 0,
      streamingLatency: analysis.streamingLatency?.avg || 0,
      overall: this.calculateOverallQuality(analysis)
    };
  }

  /**
   * Calculate overall streaming quality
   */
  calculateOverallQuality(analysis) {
    const qualityScore = analysis.contentQuality?.avg || 0;
    const latencyScore = Math.max(0, 1 - (analysis.streamingLatency?.avg || 0) / 2000); // Normalize latency
    const errorScore = Math.max(0, 1 - (analysis.errorRates?.avg || 0) * 10); // Normalize error rate
    
    const overall = (qualityScore + latencyScore + errorScore) / 3;
    
    if (overall >= 0.8) return 'excellent';
    if (overall >= 0.6) return 'good';
    if (overall >= 0.4) return 'fair';
    return 'poor';
  }

  /**
   * Extract connection stability metrics
   */
  extractConnectionStability() {
    const stability = {
      connectionSuccessRate: 0,
      reconnectionSuccess: 0,
      sustainabilityScore: 0,
      overall: 'unknown'
    };
    
    // From connection limits test
    if (this.testResults.connectionLimits) {
      stability.connectionSuccessRate = this.testResults.connectionLimits.successfulConnections / 
        (this.testResults.connectionLimits.connectionAttempts || 1);
    }
    
    // From recovery tests
    if (this.testResults.connectionLimits?.recoveryTests?.rapidReconnect) {
      const recovery = this.testResults.connectionLimits.recoveryTests.rapidReconnect;
      stability.reconnectionSuccess = recovery.totalSuccessful / (recovery.totalSuccessful + recovery.totalFailed);
    }
    
    // Calculate overall stability
    const avgStability = (stability.connectionSuccessRate + stability.reconnectionSuccess) / 2;
    stability.overall = avgStability >= 0.95 ? 'excellent' : 
                      avgStability >= 0.90 ? 'good' : 
                      avgStability >= 0.80 ? 'fair' : 'poor';
    
    return stability;
  }

  /**
   * Identify bottlenecks
   */
  identifyBottlenecks() {
    console.log(chalk.blue('🔍 Identifying performance bottlenecks...'));
    
    const bottlenecks = [];
    const capacity = this.capacityAnalysis.currentCapacity;
    
    // Connection bottlenecks
    if (capacity.maxConcurrentConnections?.peak < 50) {
      bottlenecks.push({
        type: 'connection_limit',
        severity: 'high',
        current: capacity.maxConcurrentConnections.peak,
        threshold: 100,
        impact: 'Limits concurrent user capacity',
        recommendation: 'Increase connection pool size and optimize connection handling'
      });
    }
    
    // Latency bottlenecks
    if (capacity.averageLatency?.streamingLatency > 1000) {
      bottlenecks.push({
        type: 'streaming_latency',
        severity: 'medium',
        current: capacity.averageLatency.streamingLatency,
        threshold: 500,
        impact: 'Affects real-time user experience',
        recommendation: 'Optimize streaming pipeline and buffer management'
      });
    }
    
    // Error rate bottlenecks
    if (capacity.errorRates?.overallErrorRate > 0.05) {
      bottlenecks.push({
        type: 'error_rate',
        severity: 'high',
        current: capacity.errorRates.overallErrorRate,
        threshold: 0.01,
        impact: 'Reduces system reliability',
        recommendation: 'Improve error handling and input validation'
      });
    }
    
    // Memory bottlenecks
    if (capacity.memoryUsage?.peakMemoryUsage > 3000) {
      bottlenecks.push({
        type: 'memory_usage',
        severity: 'medium',
        current: capacity.memoryUsage.peakMemoryUsage,
        threshold: 2000,
        impact: 'May cause out-of-memory errors',
        recommendation: 'Optimize memory management and implement garbage collection'
      });
    }
    
    // Streaming quality bottlenecks
    if (capacity.streamingQuality?.contentQuality < 0.7) {
      bottlenecks.push({
        type: 'content_quality',
        severity: 'medium',
        current: capacity.streamingQuality.contentQuality,
        threshold: 0.8,
        impact: 'Affects user satisfaction',
        recommendation: 'Improve content generation algorithms'
      });
    }
    
    this.capacityAnalysis.bottlenecks = bottlenecks;
    
    console.log(chalk.green(`✓ Identified ${bottlenecks.length} performance bottlenecks`));
    return bottlenecks;
  }

  /**
   * Generate scaling recommendations
   */
  generateScalingRecommendations() {
    console.log(chalk.blue('📈 Generating scaling recommendations...'));
    
    const recommendations = [];
    const capacity = this.capacityAnalysis.currentCapacity;
    
    // Horizontal scaling recommendations
    if (capacity.maxConcurrentConnections?.peak < 100) {
      recommendations.push({
        type: 'horizontal_scaling',
        priority: 'high',
        description: 'Scale horizontally to handle more concurrent connections',
        implementation: 'Deploy additional service instances behind load balancer',
        expectedImpact: `Increase capacity from ${capacity.maxConcurrentConnections.peak} to ${capacity.maxConcurrentConnections.peak * 3} connections`,
        cost: 'medium',
        timeline: '1-2 weeks'
      });
    }
    
    // Vertical scaling recommendations
    if (capacity.memoryUsage?.peakMemoryUsage > 2000) {
      recommendations.push({
        type: 'vertical_scaling',
        priority: 'medium',
        description: 'Increase memory allocation for better performance',
        implementation: 'Upgrade to higher memory instances (8GB → 16GB)',
        expectedImpact: 'Reduce memory pressure and improve response times',
        cost: 'low',
        timeline: '1 week'
      });
    }
    
    // Performance optimization recommendations
    if (capacity.averageLatency?.streamingLatency > 800) {
      recommendations.push({
        type: 'performance_optimization',
        priority: 'high',
        description: 'Optimize streaming pipeline for better latency',
        implementation: 'Implement streaming buffers and async processing',
        expectedImpact: `Reduce latency from ${capacity.averageLatency.streamingLatency}ms to <500ms`,
        cost: 'medium',
        timeline: '2-3 weeks'
      });
    }
    
    // Infrastructure recommendations
    recommendations.push({
      type: 'infrastructure',
      priority: 'medium',
      description: 'Implement caching and CDN for better global performance',
      implementation: 'Deploy Redis cache and CloudFront CDN',
      expectedImpact: 'Reduce response times by 30-50%',
      cost: 'medium',
      timeline: '1-2 weeks'
    });
    
    // Monitoring recommendations
    recommendations.push({
      type: 'monitoring',
      priority: 'high',
      description: 'Implement comprehensive monitoring and alerting',
      implementation: 'Deploy Prometheus, Grafana, and custom alerts',
      expectedImpact: 'Proactive issue detection and resolution',
      cost: 'low',
      timeline: '1 week'
    });
    
    this.capacityAnalysis.recommendations = recommendations;
    
    console.log(chalk.green(`✓ Generated ${recommendations.length} scaling recommendations`));
    return recommendations;
  }

  /**
   * Calculate scaling factors
   */
  calculateScalingFactors() {
    console.log(chalk.blue('🔢 Calculating scaling factors...'));
    
    const capacity = this.capacityAnalysis.currentCapacity;
    const targetCapacity = {
      connections: 500,
      latency: 200,
      throughput: 50,
      errorRate: 0.001
    };
    
    const scalingFactors = {
      connectionScaling: targetCapacity.connections / (capacity.maxConcurrentConnections?.peak || 1),
      latencyImprovement: (capacity.averageLatency?.streamingLatency || 1000) / targetCapacity.latency,
      throughputIncrease: targetCapacity.throughput / (capacity.throughput?.messagesPerSecond || 1),
      errorReduction: (capacity.errorRates?.overallErrorRate || 0.01) / targetCapacity.errorRate,
      memoryScaling: (capacity.memoryUsage?.peakMemoryUsage || 1000) / 2000, // Target 2GB max
      
      // Recommended instance scaling
      recommendedInstances: Math.ceil(targetCapacity.connections / (capacity.maxConcurrentConnections?.sustainable || 50)),
      
      // Cost scaling estimates
      costMultiplier: {
        compute: Math.ceil(targetCapacity.connections / (capacity.maxConcurrentConnections?.peak || 50)),
        memory: Math.ceil((capacity.memoryUsage?.peakMemoryUsage || 1000) / 2000),
        storage: 1.5, // 50% increase for logs and caching
        network: 2.0 // 100% increase for higher throughput
      }
    };
    
    this.capacityAnalysis.scalingFactors = scalingFactors;
    
    console.log(chalk.green('✓ Scaling factors calculated'));
    return scalingFactors;
  }

  /**
   * Assess deployment risks
   */
  assessDeploymentRisks() {
    console.log(chalk.blue('⚠️ Assessing deployment risks...'));
    
    const risks = [];
    const capacity = this.capacityAnalysis.currentCapacity;
    
    // Connection stability risks
    if (capacity.connectionStability?.overall === 'poor') {
      risks.push({
        type: 'connection_stability',
        severity: 'high',
        probability: 0.8,
        impact: 'high',
        description: 'High connection failure rates may cause service instability',
        mitigation: 'Implement connection retry logic and circuit breakers',
        monitoringRequired: 'Connection success rate and reconnection metrics'
      });
    }
    
    // Memory exhaustion risks
    if (capacity.memoryUsage?.peakMemoryUsage > 3000) {
      risks.push({
        type: 'memory_exhaustion',
        severity: 'high',
        probability: 0.6,
        impact: 'critical',
        description: 'Memory usage approaching limits may cause OOM errors',
        mitigation: 'Implement memory monitoring and auto-scaling',
        monitoringRequired: 'Memory usage alerts and garbage collection metrics'
      });
    }
    
    // Latency degradation risks
    if (capacity.averageLatency?.streamingLatency > 1000) {
      risks.push({
        type: 'latency_degradation',
        severity: 'medium',
        probability: 0.7,
        impact: 'medium',
        description: 'High latency may impact user experience and adoption',
        mitigation: 'Optimize streaming pipeline and implement caching',
        monitoringRequired: 'Latency percentiles and user experience metrics'
      });
    }
    
    // Scalability risks
    if (capacity.maxConcurrentConnections?.confidence === 'low') {
      risks.push({
        type: 'scalability_unknown',
        severity: 'medium',
        probability: 0.5,
        impact: 'medium',
        description: 'Unknown scalability limits may cause unexpected failures',
        mitigation: 'Conduct additional load testing and gradual rollout',
        monitoringRequired: 'Connection metrics and system performance'
      });
    }
    
    // Error rate risks
    if (capacity.errorRates?.overallErrorRate > 0.05) {
      risks.push({
        type: 'high_error_rate',
        severity: 'high',
        probability: 0.9,
        impact: 'high',
        description: 'High error rates may cause user dissatisfaction',
        mitigation: 'Improve error handling and add comprehensive testing',
        monitoringRequired: 'Error rate monitoring and alerting'
      });
    }
    
    this.capacityAnalysis.riskAssessment = {
      risks,
      overallRiskLevel: this.calculateOverallRiskLevel(risks),
      recommendedActions: this.generateRiskMitigationActions(risks)
    };
    
    console.log(chalk.green(`✓ Identified ${risks.length} deployment risks`));
    return this.capacityAnalysis.riskAssessment;
  }

  /**
   * Calculate overall risk level
   */
  calculateOverallRiskLevel(risks) {
    const highRisks = risks.filter(r => r.severity === 'high').length;
    const mediumRisks = risks.filter(r => r.severity === 'medium').length;
    const lowRisks = risks.filter(r => r.severity === 'low').length;
    
    const riskScore = (highRisks * 3) + (mediumRisks * 2) + (lowRisks * 1);
    
    if (riskScore >= 6) return 'high';
    if (riskScore >= 3) return 'medium';
    return 'low';
  }

  /**
   * Generate risk mitigation actions
   */
  generateRiskMitigationActions(risks) {
    const actions = [];
    
    // Immediate actions (high-priority risks)
    const highRisks = risks.filter(r => r.severity === 'high');
    if (highRisks.length > 0) {
      actions.push({
        priority: 'immediate',
        timeline: '1 week',
        description: 'Address high-severity risks before production deployment',
        tasks: highRisks.map(r => r.mitigation)
      });
    }
    
    // Short-term actions (medium-priority risks)
    const mediumRisks = risks.filter(r => r.severity === 'medium');
    if (mediumRisks.length > 0) {
      actions.push({
        priority: 'short-term',
        timeline: '2-4 weeks',
        description: 'Implement medium-priority improvements',
        tasks: mediumRisks.map(r => r.mitigation)
      });
    }
    
    // Ongoing monitoring actions
    actions.push({
      priority: 'ongoing',
      timeline: 'continuous',
      description: 'Implement comprehensive monitoring and alerting',
      tasks: [
        'Set up performance monitoring dashboards',
        'Configure alerting for critical metrics',
        'Establish incident response procedures',
        'Regular capacity planning reviews'
      ]
    });
    
    return actions;
  }

  /**
   * Generate deployment guidelines
   */
  generateDeploymentGuidelines() {
    console.log(chalk.blue('📋 Generating deployment guidelines...'));
    
    const guidelines = {
      phaseOne: {
        title: 'Initial Production Deployment',
        timeline: '1-2 weeks',
        capacity: Math.floor((this.capacityAnalysis.currentCapacity.maxConcurrentConnections?.sustainable || 50) * 0.7),
        requirements: [
          'Deploy with 70% of tested capacity',
          'Implement comprehensive monitoring',
          'Set up automated alerts',
          'Enable gradual traffic ramp-up'
        ],
        riskLevel: 'medium',
        rollbackPlan: 'Automatic rollback if error rate > 5% or latency > 2s'
      },
      
      phaseTwo: {
        title: 'Capacity Scaling',
        timeline: '2-4 weeks',
        capacity: this.capacityAnalysis.currentCapacity.maxConcurrentConnections?.sustainable || 50,
        requirements: [
          'Scale to full tested capacity',
          'Implement horizontal scaling',
          'Add caching layers',
          'Optimize performance bottlenecks'
        ],
        riskLevel: 'low',
        rollbackPlan: 'Gradual scale-down if performance degrades'
      },
      
      phaseThree: {
        title: 'Full Production Scale',
        timeline: '4-8 weeks',
        capacity: (this.capacityAnalysis.currentCapacity.maxConcurrentConnections?.sustainable || 50) * 2,
        requirements: [
          'Scale beyond tested capacity',
          'Implement auto-scaling',
          'Add redundancy and failover',
          'Optimize for global deployment'
        ],
        riskLevel: 'medium',
        rollbackPlan: 'Phased rollback with traffic redistribution'
      },
      
      monitoringRequirements: [
        'Connection success rate > 99%',
        'Average latency < 500ms',
        'Error rate < 1%',
        'Memory usage < 80%',
        'CPU usage < 70%'
      ],
      
      alertingThresholds: {
        critical: {
          connectionFailureRate: 0.05,
          memoryUsage: 0.9,
          errorRate: 0.1,
          latency: 2000
        },
        warning: {
          connectionFailureRate: 0.02,
          memoryUsage: 0.8,
          errorRate: 0.05,
          latency: 1000
        }
      }
    };
    
    this.capacityAnalysis.deploymentGuidelines = guidelines;
    
    console.log(chalk.green('✓ Deployment guidelines generated'));
    return guidelines;
  }

  /**
   * Generate comprehensive production report
   */
  async generateProductionReport() {
    console.log(chalk.blue('📊 Generating comprehensive production report...'));
    
    // Analyze capacity
    this.analyzeCurrentCapacity();
    
    // Identify bottlenecks
    this.identifyBottlenecks();
    
    // Generate recommendations
    this.generateScalingRecommendations();
    
    // Calculate scaling factors
    this.calculateScalingFactors();
    
    // Assess risks
    this.assessDeploymentRisks();
    
    // Generate deployment guidelines
    this.generateDeploymentGuidelines();
    
    const report = {
      metadata: {
        generatedAt: new Date().toISOString(),
        testDuration: this.testResults.executionTime,
        testConfiguration: this.options.testConfiguration,
        reportVersion: '1.0.0'
      },
      
      executiveSummary: this.generateExecutiveSummary(),
      
      testResults: this.options.includeRawData ? this.testResults : {
        summary: this.generateTestSummary()
      },
      
      capacityAnalysis: this.capacityAnalysis,
      
      recommendations: {
        immediate: this.capacityAnalysis.recommendations.filter(r => r.priority === 'high'),
        shortTerm: this.capacityAnalysis.recommendations.filter(r => r.priority === 'medium'),
        longTerm: this.capacityAnalysis.recommendations.filter(r => r.priority === 'low')
      },
      
      deploymentPlan: this.generateDeploymentPlan(),
      
      appendix: {
        glossary: this.generateGlossary(),
        references: this.generateReferences()
      }
    };
    
    // Save reports
    await this.saveReports(report);
    
    console.log(chalk.green('✓ Production report generated successfully'));
    return report;
  }

  /**
   * Generate executive summary
   */
  generateExecutiveSummary() {
    const capacity = this.capacityAnalysis.currentCapacity;
    const risks = this.capacityAnalysis.riskAssessment;
    
    return {
      readinessLevel: this.calculateReadinessLevel(),
      keyFindings: [
        `Maximum concurrent connections: ${capacity.maxConcurrentConnections?.peak || 'Unknown'}`,
        `Average streaming latency: ${capacity.averageLatency?.streamingLatency?.toFixed(1) || 'Unknown'}ms`,
        `Overall error rate: ${(capacity.errorRates?.overallErrorRate * 100)?.toFixed(2) || 'Unknown'}%`,
        `Connection stability: ${capacity.connectionStability?.overall || 'Unknown'}`
      ],
      
      criticalRisks: risks?.risks?.filter(r => r.severity === 'high').length || 0,
      
      recommendedActions: [
        risks?.overallRiskLevel === 'high' ? 'Address high-severity risks before deployment' : 'Proceed with monitored deployment',
        'Implement comprehensive monitoring and alerting',
        'Plan for gradual capacity scaling',
        'Establish incident response procedures'
      ],
      
      deploymentRecommendation: this.generateDeploymentRecommendation(),
      
      estimatedTimeToProduction: this.calculateTimeToProduction()
    };
  }

  /**
   * Calculate readiness level
   */
  calculateReadinessLevel() {
    const capacity = this.capacityAnalysis.currentCapacity;
    const risks = this.capacityAnalysis.riskAssessment;
    
    let score = 0;
    
    // Connection capacity (0-25 points)
    const maxConnections = capacity.maxConcurrentConnections?.peak || 0;
    if (maxConnections >= 100) score += 25;
    else if (maxConnections >= 50) score += 20;
    else if (maxConnections >= 25) score += 15;
    else score += 10;
    
    // Latency (0-25 points)
    const latency = capacity.averageLatency?.streamingLatency || 2000;
    if (latency <= 500) score += 25;
    else if (latency <= 1000) score += 20;
    else if (latency <= 1500) score += 15;
    else score += 10;
    
    // Error rate (0-25 points)
    const errorRate = capacity.errorRates?.overallErrorRate || 0.1;
    if (errorRate <= 0.01) score += 25;
    else if (errorRate <= 0.05) score += 20;
    else if (errorRate <= 0.1) score += 15;
    else score += 10;
    
    // Risk level (0-25 points)
    const riskLevel = risks?.overallRiskLevel || 'high';
    if (riskLevel === 'low') score += 25;
    else if (riskLevel === 'medium') score += 20;
    else score += 10;
    
    // Determine readiness level
    if (score >= 90) return 'production-ready';
    if (score >= 70) return 'near-production-ready';
    if (score >= 50) return 'needs-improvement';
    return 'not-ready';
  }

  /**
   * Generate deployment recommendation
   */
  generateDeploymentRecommendation() {
    const readinessLevel = this.calculateReadinessLevel();
    
    const recommendations = {
      'production-ready': {
        action: 'Deploy to production',
        timeline: '1-2 weeks',
        confidence: 'high',
        conditions: 'With comprehensive monitoring and gradual rollout'
      },
      'near-production-ready': {
        action: 'Deploy with caution',
        timeline: '2-3 weeks',
        confidence: 'medium',
        conditions: 'After addressing medium-priority issues'
      },
      'needs-improvement': {
        action: 'Postpone deployment',
        timeline: '4-6 weeks',
        confidence: 'low',
        conditions: 'After significant improvements and re-testing'
      },
      'not-ready': {
        action: 'Significant rework required',
        timeline: '8-12 weeks',
        confidence: 'very-low',
        conditions: 'After addressing fundamental issues'
      }
    };
    
    return recommendations[readinessLevel];
  }

  /**
   * Calculate time to production
   */
  calculateTimeToProduction() {
    const risks = this.capacityAnalysis.riskAssessment;
    const bottlenecks = this.capacityAnalysis.bottlenecks;
    
    let weeks = 2; // Base deployment time
    
    // Add time for high-severity risks
    weeks += (risks?.risks?.filter(r => r.severity === 'high').length || 0) * 2;
    
    // Add time for medium-severity risks
    weeks += (risks?.risks?.filter(r => r.severity === 'medium').length || 0) * 1;
    
    // Add time for critical bottlenecks
    weeks += bottlenecks?.filter(b => b.severity === 'high').length || 0;
    
    return `${weeks} weeks`;
  }

  /**
   * Generate test summary
   */
  generateTestSummary() {
    return {
      puppeteerConcurrent: {
        successfulConnections: this.testResults.puppeteerConcurrent?.testReport?.executionSummary?.successfulConnections,
        successRate: this.testResults.puppeteerConcurrent?.testReport?.executionSummary?.successRate
      },
      loadTesting: {
        peakConnections: this.testResults.loadTesting?.scenarios?.connection?.peakConnectionCount,
        throughput: this.testResults.loadTesting?.scenarios?.connection?.phases?.sustain?.throughput
      },
      streamingPerformance: {
        avgLatency: this.testResults.streamingPerformance?.qualityAnalysis?.streamingLatency?.avg,
        contentQuality: this.testResults.streamingPerformance?.qualityAnalysis?.contentQuality?.avg
      },
      connectionLimits: {
        limitThreshold: this.testResults.connectionLimits?.limitThreshold,
        successRate: this.testResults.connectionLimits?.successRate
      }
    };
  }

  /**
   * Generate deployment plan
   */
  generateDeploymentPlan() {
    const guidelines = this.capacityAnalysis.deploymentGuidelines;
    
    return {
      phases: [guidelines.phaseOne, guidelines.phaseTwo, guidelines.phaseThree],
      monitoring: guidelines.monitoringRequirements,
      alerting: guidelines.alertingThresholds,
      rollbackStrategy: 'Automated rollback based on performance metrics',
      successCriteria: [
        'Connection success rate > 99%',
        'Average latency < 500ms',
        'Error rate < 1%',
        'No critical alerts for 24 hours'
      ]
    };
  }

  /**
   * Generate glossary
   */
  generateGlossary() {
    return {
      'Concurrent Connections': 'Number of simultaneous WebSocket connections',
      'Streaming Latency': 'Time between sending a message and receiving first streaming chunk',
      'Throughput': 'Number of messages or chunks processed per second',
      'Connection Stability': 'Reliability of WebSocket connections over time',
      'Error Rate': 'Percentage of requests that result in errors',
      'Content Quality': 'Measure of streaming response quality and coherence'
    };
  }

  /**
   * Generate references
   */
  generateReferences() {
    return {
      'WebSocket RFC': 'https://tools.ietf.org/html/rfc6455',
      'Load Testing Best Practices': 'https://docs.example.com/load-testing',
      'Production Deployment Guide': 'https://docs.example.com/deployment',
      'Performance Monitoring': 'https://docs.example.com/monitoring'
    };
  }

  /**
   * Save reports in multiple formats
   */
  async saveReports(report) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // Save JSON report
    if (this.options.reportFormat === 'json' || this.options.reportFormat === 'both') {
      const jsonPath = path.join(this.options.outputDir, `production-capacity-report-${timestamp}.json`);
      await fs.writeFile(jsonPath, JSON.stringify(report, null, 2));
      console.log(chalk.green(`✓ JSON report saved: ${jsonPath}`));
    }
    
    // Save Markdown report
    if (this.options.reportFormat === 'markdown' || this.options.reportFormat === 'both') {
      const markdownPath = path.join(this.options.outputDir, `production-capacity-report-${timestamp}.md`);
      const markdownContent = this.generateMarkdownReport(report);
      await fs.writeFile(markdownPath, markdownContent);
      console.log(chalk.green(`✓ Markdown report saved: ${markdownPath}`));
    }
    
    // Save summary report
    const summaryPath = path.join(this.options.outputDir, `capacity-summary-${timestamp}.txt`);
    const summaryContent = this.generateSummaryReport(report);
    await fs.writeFile(summaryPath, summaryContent);
    console.log(chalk.green(`✓ Summary report saved: ${summaryPath}`));
  }

  /**
   * Generate Markdown report
   */
  generateMarkdownReport(report) {
    return `# WebSocket Production Capacity Report

## Executive Summary

**Generated:** ${report.metadata.generatedAt}
**Test Duration:** ${(report.metadata.testDuration / 1000).toFixed(1)} seconds
**Readiness Level:** ${report.executiveSummary.readinessLevel}

### Key Findings

${report.executiveSummary.keyFindings.map(finding => `- ${finding}`).join('\n')}

### Critical Risks

${report.executiveSummary.criticalRisks} high-severity risks identified.

### Deployment Recommendation

**Action:** ${report.executiveSummary.deploymentRecommendation.action}
**Timeline:** ${report.executiveSummary.deploymentRecommendation.timeline}
**Confidence:** ${report.executiveSummary.deploymentRecommendation.confidence}

## Current Capacity Analysis

### Connection Capacity
- **Peak Connections:** ${report.capacityAnalysis.currentCapacity.maxConcurrentConnections?.peak || 'Unknown'}
- **Sustainable Connections:** ${report.capacityAnalysis.currentCapacity.maxConcurrentConnections?.sustainable || 'Unknown'}
- **Confidence:** ${report.capacityAnalysis.currentCapacity.maxConcurrentConnections?.confidence || 'Unknown'}

### Performance Metrics
- **Streaming Latency:** ${report.capacityAnalysis.currentCapacity.averageLatency?.streamingLatency?.toFixed(1) || 'Unknown'}ms
- **Throughput:** ${report.capacityAnalysis.currentCapacity.throughput?.messagesPerSecond?.toFixed(1) || 'Unknown'} msg/s
- **Error Rate:** ${(report.capacityAnalysis.currentCapacity.errorRates?.overallErrorRate * 100)?.toFixed(2) || 'Unknown'}%

## Bottlenecks Identified

${report.capacityAnalysis.bottlenecks.map(bottleneck => `
### ${bottleneck.type} (${bottleneck.severity.toUpperCase()})
- **Current:** ${bottleneck.current}
- **Threshold:** ${bottleneck.threshold}
- **Impact:** ${bottleneck.impact}
- **Recommendation:** ${bottleneck.recommendation}
`).join('\n')}

## Scaling Recommendations

### Immediate Actions (High Priority)
${report.recommendations.immediate.map(rec => `
- **${rec.type}:** ${rec.description}
- **Implementation:** ${rec.implementation}
- **Timeline:** ${rec.timeline}
- **Cost:** ${rec.cost}
`).join('\n')}

### Short-term Actions (Medium Priority)
${report.recommendations.shortTerm.map(rec => `
- **${rec.type}:** ${rec.description}
- **Implementation:** ${rec.implementation}
- **Timeline:** ${rec.timeline}
- **Cost:** ${rec.cost}
`).join('\n')}

## Deployment Plan

### Phase 1: Initial Production Deployment
- **Timeline:** ${report.deploymentPlan.phases[0].timeline}
- **Capacity:** ${report.deploymentPlan.phases[0].capacity} connections
- **Risk Level:** ${report.deploymentPlan.phases[0].riskLevel}

### Phase 2: Capacity Scaling
- **Timeline:** ${report.deploymentPlan.phases[1].timeline}
- **Capacity:** ${report.deploymentPlan.phases[1].capacity} connections
- **Risk Level:** ${report.deploymentPlan.phases[1].riskLevel}

### Phase 3: Full Production Scale
- **Timeline:** ${report.deploymentPlan.phases[2].timeline}
- **Capacity:** ${report.deploymentPlan.phases[2].capacity} connections
- **Risk Level:** ${report.deploymentPlan.phases[2].riskLevel}

## Monitoring Requirements

${report.deploymentPlan.monitoring.map(req => `- ${req}`).join('\n')}

## Risk Assessment

**Overall Risk Level:** ${report.capacityAnalysis.riskAssessment.overallRiskLevel}

${report.capacityAnalysis.riskAssessment.risks.map(risk => `
### ${risk.type} (${risk.severity.toUpperCase()})
- **Probability:** ${(risk.probability * 100).toFixed(0)}%
- **Impact:** ${risk.impact}
- **Description:** ${risk.description}
- **Mitigation:** ${risk.mitigation}
`).join('\n')}

## Conclusion

The WebSocket service shows ${report.executiveSummary.readinessLevel} status for production deployment. 
${report.executiveSummary.deploymentRecommendation.action} is recommended with an estimated timeline of ${report.executiveSummary.estimatedTimeToProduction}.

---

*Report generated by WebSocket Production Capacity Analysis Tool*
`;
  }

  /**
   * Generate summary report
   */
  generateSummaryReport(report) {
    return `
WEBSOCKET PRODUCTION CAPACITY SUMMARY
====================================

Generated: ${report.metadata.generatedAt}
Test Duration: ${(report.metadata.testDuration / 1000).toFixed(1)} seconds

READINESS LEVEL: ${report.executiveSummary.readinessLevel.toUpperCase()}

KEY METRICS:
- Max Concurrent Connections: ${report.capacityAnalysis.currentCapacity.maxConcurrentConnections?.peak || 'Unknown'}
- Streaming Latency: ${report.capacityAnalysis.currentCapacity.averageLatency?.streamingLatency?.toFixed(1) || 'Unknown'}ms
- Error Rate: ${(report.capacityAnalysis.currentCapacity.errorRates?.overallErrorRate * 100)?.toFixed(2) || 'Unknown'}%
- Connection Stability: ${report.capacityAnalysis.currentCapacity.connectionStability?.overall || 'Unknown'}

DEPLOYMENT RECOMMENDATION:
${report.executiveSummary.deploymentRecommendation.action}
Timeline: ${report.executiveSummary.deploymentRecommendation.timeline}
Confidence: ${report.executiveSummary.deploymentRecommendation.confidence}

CRITICAL ISSUES: ${report.executiveSummary.criticalRisks}
BOTTLENECKS: ${report.capacityAnalysis.bottlenecks.length}
RECOMMENDATIONS: ${report.capacityAnalysis.recommendations.length}

ESTIMATED TIME TO PRODUCTION: ${report.executiveSummary.estimatedTimeToProduction}

====================================
`;
  }

  /**
   * Print production capacity results
   */
  printProductionCapacityResults(report) {
    console.log(chalk.blue('\n' + '='.repeat(80)));
    console.log(chalk.blue.bold('           PRODUCTION CAPACITY REPORT'));
    console.log(chalk.blue('='.repeat(80)));
    
    // Executive Summary
    console.log(chalk.yellow.bold('\n📊 EXECUTIVE SUMMARY:'));
    console.log(chalk.green(`  Readiness Level: ${report.executiveSummary.readinessLevel.toUpperCase()}`));
    console.log(chalk.green(`  Time to Production: ${report.executiveSummary.estimatedTimeToProduction}`));
    console.log(chalk.green(`  Critical Risks: ${report.executiveSummary.criticalRisks}`));
    
    // Key Metrics
    console.log(chalk.yellow.bold('\n📈 KEY METRICS:'));
    report.executiveSummary.keyFindings.forEach(finding => {
      console.log(chalk.cyan(`  • ${finding}`));
    });
    
    // Deployment Recommendation
    console.log(chalk.yellow.bold('\n🚀 DEPLOYMENT RECOMMENDATION:'));
    const deployment = report.executiveSummary.deploymentRecommendation;
    console.log(chalk.green(`  Action: ${deployment.action}`));
    console.log(chalk.green(`  Timeline: ${deployment.timeline}`));
    console.log(chalk.green(`  Confidence: ${deployment.confidence}`));
    
    // Critical Issues
    if (report.capacityAnalysis.bottlenecks.length > 0) {
      console.log(chalk.yellow.bold('\n⚠️ CRITICAL BOTTLENECKS:'));
      report.capacityAnalysis.bottlenecks
        .filter(b => b.severity === 'high')
        .forEach(bottleneck => {
          console.log(chalk.red(`  • ${bottleneck.type}: ${bottleneck.impact}`));
        });
    }
    
    // Immediate Actions
    if (report.recommendations.immediate.length > 0) {
      console.log(chalk.yellow.bold('\n🔥 IMMEDIATE ACTIONS:'));
      report.recommendations.immediate.forEach(rec => {
        console.log(chalk.red(`  • ${rec.description}`));
        console.log(chalk.gray(`    Timeline: ${rec.timeline} | Cost: ${rec.cost}`));
      });
    }
    
    console.log(chalk.blue('\n' + '='.repeat(80)));
  }

  /**
   * Run complete production capacity analysis
   */
  async runProductionCapacityAnalysis() {
    const startTime = Date.now();
    
    try {
      // Initialize
      await this.initialize();
      
      // Run capacity tests
      await this.runCapacityTests();
      
      // Generate comprehensive report
      const report = await this.generateProductionReport();
      
      // Print results
      this.printProductionCapacityResults(report);
      
      const executionTime = Date.now() - startTime;
      console.log(chalk.green(`\n✅ Production capacity analysis completed in ${(executionTime / 1000).toFixed(1)}s`));
      
      return report;
      
    } catch (error) {
      console.error(chalk.red('Production capacity analysis failed:'), error);
      throw error;
    }
  }
}

module.exports = ProductionCapacityReport;

// Run if called directly
if (require.main === module) {
  const capacityReport = new ProductionCapacityReport({
    maxConcurrentConnections: 100,
    testDuration: 60000,
    outputDir: path.join(__dirname, 'reports'),
    reportFormat: 'both'
  });
  
  capacityReport.runProductionCapacityAnalysis()
    .then(report => {
      console.log(chalk.green('\n🎉 Production capacity analysis completed successfully!'));
      console.log(chalk.blue(`📁 Reports saved to: ${capacityReport.options.outputDir}`));
    })
    .catch(error => {
      console.error(chalk.red('\n❌ Production capacity analysis failed:'), error);
      process.exit(1);
    });
}