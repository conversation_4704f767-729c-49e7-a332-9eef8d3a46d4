{"name": "websocket-concurrent-testing", "version": "1.0.0", "description": "Production-scale WebSocket concurrent testing framework with Puppeteer", "main": "index.js", "scripts": {"test": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit", "test:concurrent": "node puppeteer_concurrent_test.js", "test:load": "node websocket_load_testing.js", "test:streaming": "node streaming_performance.js", "test:limits": "node connection_limits_test.js", "test:capacity": "node production_capacity_report.js", "test:all": "node run_all_tests.js --suite all", "monitor": "node utils/performance_monitor.js", "run": "node run_all_tests.js"}, "dependencies": {"puppeteer": "^21.0.0", "ws": "^8.14.0", "jest": "^29.7.0", "jsonwebtoken": "^9.0.2", "uuid": "^9.0.0", "chalk": "^4.1.2", "cli-table3": "^0.6.3", "yargs": "^17.7.2"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20.0.0", "typescript": "^5.0.0"}, "jest": {"testEnvironment": "node", "testTimeout": 60000, "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"]}}