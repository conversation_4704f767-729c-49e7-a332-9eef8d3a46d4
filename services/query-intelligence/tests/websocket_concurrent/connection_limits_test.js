const WebSocketClientFactory = require('./utils/websocket_client_factory');
const PerformanceMonitor = require('./utils/performance_monitor');
const chalk = require('chalk');
const yargs = require('yargs');

/**
 * Connection Limits Test
 * Tests WebSocket connection limits and management under stress
 */
class ConnectionLimitsTest {
  constructor(options = {}) {
    this.options = {
      maxConnectionAttempts: options.maxConnectionAttempts || 150,
      connectionBatchSize: options.connectionBatchSize || 10,
      batchDelay: options.batchDelay || 1000, // 1 second
      connectionTimeout: options.connectionTimeout || 15000, // 15 seconds
      userLimits: options.userLimits || {
        maxPerUser: 10,
        userCount: 15
      },
      stressPatterns: options.stressPatterns || ['gradual', 'burst', 'sustained', 'mixed'],
      limitValidation: options.limitValidation || true,
      recoveryTesting: options.recoveryTesting || true,
      ...options
    };

    this.clientFactory = new WebSocketClientFactory();
    this.performanceMonitor = new PerformanceMonitor();
    
    this.testResults = {
      startTime: null,
      endTime: null,
      connectionAttempts: 0,
      successfulConnections: 0,
      failedConnections: 0,
      limitReached: false,
      limitThreshold: 0,
      patterns: {},
      userLimitTests: {},
      recoveryTests: {},
      performanceMetrics: {}
    };

    this.connectionHistory = [];
    this.activeConnections = new Map();
  }

  /**
   * Initialize connection limits test
   */
  async initialize() {
    console.log(chalk.blue('🔐 Initializing connection limits test...'));
    
    // Register components
    this.performanceMonitor.registerClientFactory(this.clientFactory);
    
    // Start monitoring
    this.performanceMonitor.start();
    
    // Set up event listeners
    this.setupEventListeners();
    
    console.log(chalk.green('✓ Connection limits test initialized'));
    console.log(chalk.blue(`Target: ${this.options.maxConnectionAttempts} connection attempts`));
    console.log(chalk.blue(`Batch size: ${this.options.connectionBatchSize} connections`));
    console.log(chalk.blue(`Per-user limit: ${this.options.userLimits.maxPerUser} connections`));
  }

  /**
   * Set up event listeners
   */
  setupEventListeners() {
    this.clientFactory.on('client-connected', (client) => {
      this.activeConnections.set(client.id, {
        client,
        connectedAt: Date.now(),
        userId: client.userId
      });
      
      console.log(chalk.green(`🔗 Connection ${client.id} established (${this.activeConnections.size} active)`));
    });

    this.clientFactory.on('client-disconnected', (client) => {
      this.activeConnections.delete(client.id);
      console.log(chalk.yellow(`🔌 Connection ${client.id} closed (${this.activeConnections.size} active)`));
    });

    this.clientFactory.on('client-error', (client, error) => {
      console.log(chalk.red(`❌ Connection ${client.id} error: ${error.message}`));
      
      // Track connection failures
      this.connectionHistory.push({
        timestamp: Date.now(),
        clientId: client.id,
        event: 'error',
        message: error.message,
        activeConnections: this.activeConnections.size
      });
    });

    this.performanceMonitor.on('alert-triggered', (alert) => {
      if (alert.type === 'connections') {
        console.log(chalk.red(`🚨 CONNECTION ALERT: ${alert.message}`));
      }
    });
  }

  /**
   * Run connection limit discovery test
   */
  async runConnectionLimitDiscovery() {
    console.log(chalk.blue('🔍 Discovering connection limits...'));
    
    const startTime = Date.now();
    let connectionAttempts = 0;
    let successfulConnections = 0;
    let failedConnections = 0;
    let limitReached = false;
    let limitThreshold = 0;
    
    // Track consecutive failures to detect limit
    let consecutiveFailures = 0;
    const failureThreshold = 5; // 5 consecutive failures = limit reached
    
    while (connectionAttempts < this.options.maxConnectionAttempts && !limitReached) {
      const batchStart = Date.now();
      const batchPromises = [];
      
      // Create batch of connections
      for (let i = 0; i < this.options.connectionBatchSize && !limitReached; i++) {
        const user = this.clientFactory.createUsers(1)[0];
        
        const connectionPromise = this.clientFactory.createClient(user)
          .then(client => {
            successfulConnections++;
            consecutiveFailures = 0; // Reset failure counter
            
            this.connectionHistory.push({
              timestamp: Date.now(),
              clientId: client.id,
              event: 'connected',
              activeConnections: this.activeConnections.size
            });
            
            return { success: true, client };
          })
          .catch(error => {
            failedConnections++;
            consecutiveFailures++;
            
            this.connectionHistory.push({
              timestamp: Date.now(),
              event: 'failed',
              error: error.message,
              activeConnections: this.activeConnections.size
            });
            
            // Check if we've reached the limit
            if (consecutiveFailures >= failureThreshold) {
              limitReached = true;
              limitThreshold = successfulConnections;
              console.log(chalk.yellow(`🚦 Connection limit detected at ${limitThreshold} connections`));
            }
            
            return { success: false, error };
          });
        
        batchPromises.push(connectionPromise);
        connectionAttempts++;
      }
      
      // Wait for batch to complete
      await Promise.all(batchPromises);
      
      // Log progress
      console.log(chalk.cyan(`Batch completed: ${successfulConnections} successful, ${failedConnections} failed (${this.activeConnections.size} active)`));
      
      // Check memory and performance
      const metrics = this.performanceMonitor.getCurrentMetrics();
      if (metrics.memory.percentage > 0.9) {
        console.log(chalk.red('🚨 Memory usage critical - stopping connection attempts'));
        break;
      }
      
      // Wait before next batch
      if (!limitReached) {
        const batchDuration = Date.now() - batchStart;
        const remainingDelay = this.options.batchDelay - batchDuration;
        if (remainingDelay > 0) {
          await new Promise(resolve => setTimeout(resolve, remainingDelay));
        }
      }
    }
    
    const endTime = Date.now();
    
    return {
      duration: endTime - startTime,
      connectionAttempts,
      successfulConnections,
      failedConnections,
      limitReached,
      limitThreshold,
      successRate: connectionAttempts > 0 ? successfulConnections / connectionAttempts : 0,
      peakConnections: Math.max(...this.connectionHistory.map(h => h.activeConnections || 0))
    };
  }

  /**
   * Run user-based connection limit test
   */
  async runUserConnectionLimitTest() {
    console.log(chalk.blue('👥 Testing per-user connection limits...'));
    
    const userLimitResults = {};
    const maxPerUser = this.options.userLimits.maxPerUser;
    const userCount = this.options.userLimits.userCount;
    
    // Create users
    const users = this.clientFactory.createUsers(userCount);
    
    for (let userIndex = 0; userIndex < users.length; userIndex++) {
      const user = users[userIndex];
      console.log(chalk.cyan(`Testing user ${userIndex + 1}/${users.length}: ${user.userId}`));
      
      const userResults = {
        userId: user.userId,
        attemptedConnections: 0,
        successfulConnections: 0,
        failedConnections: 0,
        limitEnforced: false,
        clients: []
      };
      
      // Attempt to create connections beyond the limit
      for (let connIndex = 0; connIndex < maxPerUser + 5; connIndex++) {
        try {
          const client = await this.clientFactory.createClient(user);
          userResults.successfulConnections++;
          userResults.clients.push(client);
          
          console.log(chalk.green(`  ✓ Connection ${connIndex + 1} for user ${user.userId}`));
          
        } catch (error) {
          userResults.failedConnections++;
          
          if (connIndex >= maxPerUser) {
            userResults.limitEnforced = true;
            console.log(chalk.yellow(`  🚦 User limit enforced at connection ${connIndex + 1}`));
          } else {
            console.log(chalk.red(`  ❌ Unexpected failure at connection ${connIndex + 1}: ${error.message}`));
          }
        }
        
        userResults.attemptedConnections++;
        
        // Small delay between connections
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      userLimitResults[user.userId] = userResults;
      
      // Clean up user's connections
      for (const client of userResults.clients) {
        try {
          await this.clientFactory.closeClient(client.id);
        } catch (error) {
          console.error(chalk.red(`Failed to close client ${client.id}:`, error));
        }
      }
    }
    
    // Calculate summary
    const summary = {
      totalUsers: users.length,
      usersWithLimitsEnforced: Object.values(userLimitResults).filter(r => r.limitEnforced).length,
      avgConnectionsPerUser: Object.values(userLimitResults).reduce((sum, r) => sum + r.successfulConnections, 0) / users.length,
      limitEnforcementRate: Object.values(userLimitResults).filter(r => r.limitEnforced).length / users.length
    };
    
    return {
      userResults: userLimitResults,
      summary
    };
  }

  /**
   * Run connection stress patterns
   */
  async runConnectionStressPatterns() {
    console.log(chalk.blue('⚡ Running connection stress patterns...'));
    
    const patternResults = {};
    
    for (const pattern of this.options.stressPatterns) {
      console.log(chalk.cyan(`Testing ${pattern} pattern...`));
      
      const patternResult = await this.runStressPattern(pattern);
      patternResults[pattern] = patternResult;
      
      // Clean up connections between patterns
      await this.clientFactory.closeAllClients();
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    return patternResults;
  }

  /**
   * Run specific stress pattern
   */
  async runStressPattern(pattern) {
    const startTime = Date.now();
    
    switch (pattern) {
      case 'gradual':
        return await this.runGradualStressPattern();
      case 'burst':
        return await this.runBurstStressPattern();
      case 'sustained':
        return await this.runSustainedStressPattern();
      case 'mixed':
        return await this.runMixedStressPattern();
      default:
        throw new Error(`Unknown stress pattern: ${pattern}`);
    }
  }

  /**
   * Run gradual stress pattern
   */
  async runGradualStressPattern() {
    const steps = 8;
    const connectionsPerStep = 12;
    const stepDuration = 5000; // 5 seconds
    
    const stepResults = [];
    
    for (let step = 0; step < steps; step++) {
      const stepStart = Date.now();
      
      console.log(chalk.yellow(`  Gradual step ${step + 1}/${steps}: adding ${connectionsPerStep} connections`));
      
      // Create connections for this step
      const users = this.clientFactory.createUsers(connectionsPerStep);
      const connectionPromises = users.map(user => 
        this.clientFactory.createClient(user)
          .catch(error => ({ error: error.message }))
      );
      
      const stepConnections = await Promise.all(connectionPromises);
      const successful = stepConnections.filter(r => !r.error).length;
      const failed = stepConnections.length - successful;
      
      stepResults.push({
        step: step + 1,
        attempted: connectionsPerStep,
        successful,
        failed,
        activeConnections: this.activeConnections.size,
        duration: Date.now() - stepStart
      });
      
      console.log(chalk.green(`    ✓ Step ${step + 1}: ${successful}/${connectionsPerStep} successful (${this.activeConnections.size} total active)`));
      
      // Wait before next step
      if (step < steps - 1) {
        await new Promise(resolve => setTimeout(resolve, stepDuration));
      }
    }
    
    return {
      pattern: 'gradual',
      steps,
      connectionsPerStep,
      stepDuration,
      stepResults,
      totalAttempted: steps * connectionsPerStep,
      totalSuccessful: stepResults.reduce((sum, r) => sum + r.successful, 0),
      peakConnections: Math.max(...stepResults.map(r => r.activeConnections))
    };
  }

  /**
   * Run burst stress pattern
   */
  async runBurstStressPattern() {
    const burstSize = 50;
    const burstCount = 3;
    const burstInterval = 8000; // 8 seconds between bursts
    
    const burstResults = [];
    
    for (let burst = 0; burst < burstCount; burst++) {
      const burstStart = Date.now();
      
      console.log(chalk.yellow(`  Burst ${burst + 1}/${burstCount}: ${burstSize} simultaneous connections`));
      
      // Create burst of connections
      const users = this.clientFactory.createUsers(burstSize);
      const burstPromises = users.map(user => 
        this.clientFactory.createClient(user)
          .catch(error => ({ error: error.message }))
      );
      
      const burstConnections = await Promise.all(burstPromises);
      const successful = burstConnections.filter(r => !r.error).length;
      const failed = burstConnections.length - successful;
      
      burstResults.push({
        burst: burst + 1,
        attempted: burstSize,
        successful,
        failed,
        activeConnections: this.activeConnections.size,
        duration: Date.now() - burstStart
      });
      
      console.log(chalk.green(`    ✓ Burst ${burst + 1}: ${successful}/${burstSize} successful (${this.activeConnections.size} total active)`));
      
      // Wait before next burst
      if (burst < burstCount - 1) {
        await new Promise(resolve => setTimeout(resolve, burstInterval));
      }
    }
    
    return {
      pattern: 'burst',
      burstSize,
      burstCount,
      burstInterval,
      burstResults,
      totalAttempted: burstCount * burstSize,
      totalSuccessful: burstResults.reduce((sum, r) => sum + r.successful, 0),
      peakConnections: Math.max(...burstResults.map(r => r.activeConnections))
    };
  }

  /**
   * Run sustained stress pattern
   */
  async runSustainedStressPattern() {
    const sustainedConnections = 60;
    const sustainedDuration = 20000; // 20 seconds
    const connectionRate = 2000; // New connection every 2 seconds
    
    console.log(chalk.yellow(`  Sustained: ${sustainedConnections} connections over ${sustainedDuration}ms`));
    
    const sustainedStart = Date.now();
    const sustainedResults = [];
    let connectionIndex = 0;
    
    // Create initial connections
    const users = this.clientFactory.createUsers(sustainedConnections);
    
    const sustainedInterval = setInterval(async () => {
      if (connectionIndex >= users.length || Date.now() - sustainedStart >= sustainedDuration) {
        clearInterval(sustainedInterval);
        return;
      }
      
      const user = users[connectionIndex];
      
      try {
        const client = await this.clientFactory.createClient(user);
        sustainedResults.push({
          index: connectionIndex,
          timestamp: Date.now(),
          success: true,
          activeConnections: this.activeConnections.size
        });
        
        console.log(chalk.green(`    ✓ Sustained connection ${connectionIndex + 1}: ${this.activeConnections.size} active`));
        
      } catch (error) {
        sustainedResults.push({
          index: connectionIndex,
          timestamp: Date.now(),
          success: false,
          error: error.message,
          activeConnections: this.activeConnections.size
        });
        
        console.log(chalk.red(`    ❌ Sustained connection ${connectionIndex + 1} failed: ${error.message}`));
      }
      
      connectionIndex++;
    }, connectionRate);
    
    // Wait for sustained test to complete
    await new Promise(resolve => setTimeout(resolve, sustainedDuration + 2000));
    
    return {
      pattern: 'sustained',
      sustainedConnections,
      sustainedDuration,
      connectionRate,
      results: sustainedResults,
      attempted: sustainedResults.length,
      successful: sustainedResults.filter(r => r.success).length,
      failed: sustainedResults.filter(r => !r.success).length,
      peakConnections: Math.max(...sustainedResults.map(r => r.activeConnections))
    };
  }

  /**
   * Run mixed stress pattern
   */
  async runMixedStressPattern() {
    const mixedDuration = 25000; // 25 seconds
    const baseConnections = 20;
    const burstSize = 15;
    const burstTimings = [5000, 12000, 18000]; // 5s, 12s, 18s
    
    console.log(chalk.yellow(`  Mixed: ${baseConnections} base + ${burstSize} bursts at ${burstTimings.join(', ')}ms`));
    
    const mixedStart = Date.now();
    const mixedResults = [];
    
    // Create base connections
    const baseUsers = this.clientFactory.createUsers(baseConnections);
    const basePromises = baseUsers.map(user => 
      this.clientFactory.createClient(user)
        .then(client => ({ success: true, client }))
        .catch(error => ({ success: false, error: error.message }))
    );
    
    const baseConnections_result = await Promise.all(basePromises);
    const baseSuccessful = baseConnections_result.filter(r => r.success).length;
    
    mixedResults.push({
      phase: 'base',
      attempted: baseConnections,
      successful: baseSuccessful,
      failed: baseConnections - baseSuccessful,
      activeConnections: this.activeConnections.size
    });
    
    console.log(chalk.green(`    ✓ Base connections: ${baseSuccessful}/${baseConnections} successful`));
    
    // Schedule bursts
    const burstPromises = burstTimings.map((timing, index) => {
      return new Promise(resolve => {
        setTimeout(async () => {
          console.log(chalk.yellow(`    Burst ${index + 1} at ${timing}ms`));
          
          const burstUsers = this.clientFactory.createUsers(burstSize);
          const burstPromises = burstUsers.map(user => 
            this.clientFactory.createClient(user)
              .then(client => ({ success: true, client }))
              .catch(error => ({ success: false, error: error.message }))
          );
          
          const burstConnections = await Promise.all(burstPromises);
          const burstSuccessful = burstConnections.filter(r => r.success).length;
          
          mixedResults.push({
            phase: `burst-${index + 1}`,
            timing,
            attempted: burstSize,
            successful: burstSuccessful,
            failed: burstSize - burstSuccessful,
            activeConnections: this.activeConnections.size
          });
          
          console.log(chalk.green(`      ✓ Burst ${index + 1}: ${burstSuccessful}/${burstSize} successful`));
          
          resolve();
        }, timing);
      });
    });
    
    // Wait for all bursts to complete
    await Promise.all(burstPromises);
    
    // Wait for mixed pattern to complete
    await new Promise(resolve => setTimeout(resolve, mixedDuration));
    
    return {
      pattern: 'mixed',
      baseConnections,
      burstSize,
      burstTimings,
      duration: mixedDuration,
      results: mixedResults,
      totalAttempted: baseConnections + (burstSize * burstTimings.length),
      totalSuccessful: mixedResults.reduce((sum, r) => sum + r.successful, 0),
      peakConnections: Math.max(...mixedResults.map(r => r.activeConnections))
    };
  }

  /**
   * Run connection recovery test
   */
  async runConnectionRecoveryTest() {
    console.log(chalk.blue('🔄 Testing connection recovery...'));
    
    const recoveryResults = {};
    
    // Test 1: Rapid disconnect and reconnect
    recoveryResults.rapidReconnect = await this.testRapidReconnect();
    
    // Test 2: Gradual connection recovery
    recoveryResults.gradualRecovery = await this.testGradualRecovery();
    
    // Test 3: Limit breach recovery
    recoveryResults.limitBreachRecovery = await this.testLimitBreachRecovery();
    
    return recoveryResults;
  }

  /**
   * Test rapid reconnection
   */
  async testRapidReconnect() {
    console.log(chalk.cyan('  Testing rapid reconnection...'));
    
    const connectionCount = 25;
    const reconnectAttempts = 3;
    
    // Create initial connections
    const users = this.clientFactory.createUsers(connectionCount);
    const initialClients = await this.clientFactory.createConcurrentClients(users.length, 1);
    
    console.log(chalk.green(`    ✓ Initial connections: ${initialClients.length}`));
    
    const reconnectResults = [];
    
    for (let attempt = 0; attempt < reconnectAttempts; attempt++) {
      const reconnectStart = Date.now();
      
      // Disconnect all clients
      await this.clientFactory.closeAllClients();
      console.log(chalk.yellow(`    Disconnected all clients (attempt ${attempt + 1})`));
      
      // Attempt to reconnect
      const reconnectUsers = this.clientFactory.createUsers(connectionCount);
      const reconnectClients = await this.clientFactory.createConcurrentClients(reconnectUsers.length, 1);
      
      const reconnectEnd = Date.now();
      
      reconnectResults.push({
        attempt: attempt + 1,
        reconnectTime: reconnectEnd - reconnectStart,
        successful: reconnectClients.length,
        failed: connectionCount - reconnectClients.length
      });
      
      console.log(chalk.green(`    ✓ Reconnect attempt ${attempt + 1}: ${reconnectClients.length}/${connectionCount} successful in ${reconnectEnd - reconnectStart}ms`));
      
      // Wait before next attempt
      if (attempt < reconnectAttempts - 1) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    return {
      connectionCount,
      reconnectAttempts,
      results: reconnectResults,
      avgReconnectTime: reconnectResults.reduce((sum, r) => sum + r.reconnectTime, 0) / reconnectResults.length,
      totalSuccessful: reconnectResults.reduce((sum, r) => sum + r.successful, 0),
      totalFailed: reconnectResults.reduce((sum, r) => sum + r.failed, 0)
    };
  }

  /**
   * Test gradual recovery
   */
  async testGradualRecovery() {
    console.log(chalk.cyan('  Testing gradual recovery...'));
    
    const targetConnections = 40;
    const recoverySteps = 4;
    const connectionsPerStep = targetConnections / recoverySteps;
    
    const recoveryResults = [];
    
    for (let step = 0; step < recoverySteps; step++) {
      const stepStart = Date.now();
      
      const users = this.clientFactory.createUsers(connectionsPerStep);
      const stepClients = await this.clientFactory.createConcurrentClients(users.length, 1);
      
      const stepEnd = Date.now();
      
      recoveryResults.push({
        step: step + 1,
        attempted: connectionsPerStep,
        successful: stepClients.length,
        failed: connectionsPerStep - stepClients.length,
        activeConnections: this.activeConnections.size,
        stepTime: stepEnd - stepStart
      });
      
      console.log(chalk.green(`    ✓ Recovery step ${step + 1}: ${stepClients.length}/${connectionsPerStep} successful (${this.activeConnections.size} total)`));
      
      // Wait between steps
      if (step < recoverySteps - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    return {
      targetConnections,
      recoverySteps,
      connectionsPerStep,
      results: recoveryResults,
      totalSuccessful: recoveryResults.reduce((sum, r) => sum + r.successful, 0),
      totalFailed: recoveryResults.reduce((sum, r) => sum + r.failed, 0),
      avgStepTime: recoveryResults.reduce((sum, r) => sum + r.stepTime, 0) / recoveryResults.length
    };
  }

  /**
   * Test limit breach recovery
   */
  async testLimitBreachRecovery() {
    console.log(chalk.cyan('  Testing limit breach recovery...'));
    
    const breachAttempts = 80; // Attempt to exceed limits
    const recoveryAttempts = 30; // Then try normal connections
    
    // Attempt to breach limits
    const breachUsers = this.clientFactory.createUsers(breachAttempts);
    const breachResults = [];
    
    for (let i = 0; i < breachAttempts; i++) {
      try {
        const client = await this.clientFactory.createClient(breachUsers[i]);
        breachResults.push({ index: i, success: true, activeConnections: this.activeConnections.size });
      } catch (error) {
        breachResults.push({ index: i, success: false, error: error.message, activeConnections: this.activeConnections.size });
      }
    }
    
    const breachSuccessful = breachResults.filter(r => r.success).length;
    console.log(chalk.yellow(`    Breach attempt: ${breachSuccessful}/${breachAttempts} successful`));
    
    // Wait for system to recover
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Test normal connections after breach
    const recoveryUsers = this.clientFactory.createUsers(recoveryAttempts);
    const recoveryResults = [];
    
    for (let i = 0; i < recoveryAttempts; i++) {
      try {
        const client = await this.clientFactory.createClient(recoveryUsers[i]);
        recoveryResults.push({ index: i, success: true, activeConnections: this.activeConnections.size });
      } catch (error) {
        recoveryResults.push({ index: i, success: false, error: error.message, activeConnections: this.activeConnections.size });
      }
    }
    
    const recoverySuccessful = recoveryResults.filter(r => r.success).length;
    console.log(chalk.green(`    Recovery: ${recoverySuccessful}/${recoveryAttempts} successful`));
    
    return {
      breachAttempts,
      recoveryAttempts,
      breachSuccessful,
      recoverySuccessful,
      breachResults,
      recoveryResults,
      recoveryRate: recoveryAttempts > 0 ? recoverySuccessful / recoveryAttempts : 0
    };
  }

  /**
   * Generate comprehensive report
   */
  generateConnectionLimitsReport() {
    const report = {
      configuration: this.options,
      executionSummary: {
        startTime: this.testResults.startTime,
        endTime: this.testResults.endTime,
        duration: this.testResults.endTime - this.testResults.startTime,
        totalConnectionAttempts: this.testResults.connectionAttempts,
        totalSuccessfulConnections: this.testResults.successfulConnections,
        totalFailedConnections: this.testResults.failedConnections,
        limitReached: this.testResults.limitReached,
        limitThreshold: this.testResults.limitThreshold
      },
      limitDiscovery: {
        limitReached: this.testResults.limitReached,
        limitThreshold: this.testResults.limitThreshold,
        successRate: this.testResults.connectionAttempts > 0 
          ? this.testResults.successfulConnections / this.testResults.connectionAttempts 
          : 0
      },
      userLimitTests: this.testResults.userLimitTests,
      stressPatterns: this.testResults.patterns,
      recoveryTests: this.testResults.recoveryTests,
      performanceMetrics: this.testResults.performanceMetrics,
      connectionHistory: this.connectionHistory
    };
    
    return report;
  }

  /**
   * Print connection limits test results
   */
  printConnectionLimitsResults() {
    const report = this.generateConnectionLimitsReport();
    
    console.log(chalk.blue('\n' + '='.repeat(80)));
    console.log(chalk.blue.bold('           CONNECTION LIMITS TEST RESULTS'));
    console.log(chalk.blue('='.repeat(80)));
    
    // Configuration
    console.log(chalk.yellow.bold('\n📋 TEST CONFIGURATION:'));
    console.log(chalk.cyan(`  Max Connection Attempts: ${report.configuration.maxConnectionAttempts}`));
    console.log(chalk.cyan(`  Batch Size: ${report.configuration.connectionBatchSize}`));
    console.log(chalk.cyan(`  Per-User Limit: ${report.configuration.userLimits.maxPerUser}`));
    console.log(chalk.cyan(`  Stress Patterns: ${report.configuration.stressPatterns.join(', ')}`));
    
    // Execution Summary
    console.log(chalk.yellow.bold('\n🚀 EXECUTION SUMMARY:'));
    console.log(chalk.green(`  ✓ Total Attempts: ${report.executionSummary.totalConnectionAttempts}`));
    console.log(chalk.green(`  ✓ Successful: ${report.executionSummary.totalSuccessfulConnections}`));
    console.log(chalk.green(`  ✓ Failed: ${report.executionSummary.totalFailedConnections}`));
    console.log(chalk.green(`  ✓ Success Rate: ${(report.limitDiscovery.successRate * 100).toFixed(1)}%`));
    
    // Limit Discovery
    console.log(chalk.yellow.bold('\n🔍 LIMIT DISCOVERY:'));
    if (report.limitDiscovery.limitReached) {
      console.log(chalk.red(`  🚦 Connection limit reached at ${report.limitDiscovery.limitThreshold} connections`));
    } else {
      console.log(chalk.green(`  ✓ No connection limit reached (tested up to ${report.executionSummary.totalConnectionAttempts} attempts)`));
    }
    
    // User Limit Tests
    if (report.userLimitTests.summary) {
      console.log(chalk.yellow.bold('\n👥 USER LIMIT TESTS:'));
      console.log(chalk.green(`  ✓ Total Users Tested: ${report.userLimitTests.summary.totalUsers}`));
      console.log(chalk.green(`  ✓ Limits Enforced: ${report.userLimitTests.summary.usersWithLimitsEnforced}/${report.userLimitTests.summary.totalUsers}`));
      console.log(chalk.green(`  ✓ Avg Connections per User: ${report.userLimitTests.summary.avgConnectionsPerUser.toFixed(1)}`));
      console.log(chalk.green(`  ✓ Enforcement Rate: ${(report.userLimitTests.summary.limitEnforcementRate * 100).toFixed(1)}%`));
    }
    
    // Stress Patterns
    if (Object.keys(report.stressPatterns).length > 0) {
      console.log(chalk.yellow.bold('\n⚡ STRESS PATTERN RESULTS:'));
      Object.entries(report.stressPatterns).forEach(([pattern, results]) => {
        console.log(chalk.cyan(`  ${pattern.toUpperCase()}:`));
        console.log(chalk.green(`    ✓ Total Attempted: ${results.totalAttempted}`));
        console.log(chalk.green(`    ✓ Total Successful: ${results.totalSuccessful}`));
        console.log(chalk.green(`    ✓ Peak Connections: ${results.peakConnections}`));
        console.log(chalk.green(`    ✓ Success Rate: ${((results.totalSuccessful / results.totalAttempted) * 100).toFixed(1)}%`));
      });
    }
    
    // Recovery Tests
    if (report.recoveryTests.rapidReconnect) {
      console.log(chalk.yellow.bold('\n🔄 RECOVERY TEST RESULTS:'));
      const rapid = report.recoveryTests.rapidReconnect;
      console.log(chalk.cyan(`  RAPID RECONNECT:`));
      console.log(chalk.green(`    ✓ Avg Reconnect Time: ${rapid.avgReconnectTime.toFixed(1)}ms`));
      console.log(chalk.green(`    ✓ Total Successful: ${rapid.totalSuccessful}`));
      console.log(chalk.green(`    ✓ Total Failed: ${rapid.totalFailed}`));
      
      if (report.recoveryTests.gradualRecovery) {
        const gradual = report.recoveryTests.gradualRecovery;
        console.log(chalk.cyan(`  GRADUAL RECOVERY:`));
        console.log(chalk.green(`    ✓ Total Successful: ${gradual.totalSuccessful}/${gradual.targetConnections}`));
        console.log(chalk.green(`    ✓ Avg Step Time: ${gradual.avgStepTime.toFixed(1)}ms`));
      }
    }
    
    console.log(chalk.blue('\n' + '='.repeat(80)));
  }

  /**
   * Run complete connection limits test suite
   */
  async runConnectionLimitsTestSuite() {
    try {
      // Initialize
      await this.initialize();
      
      this.testResults.startTime = Date.now();
      
      // 1. Connection limit discovery
      console.log(chalk.blue('\n1️⃣ Connection Limit Discovery'));
      const limitDiscovery = await this.runConnectionLimitDiscovery();
      Object.assign(this.testResults, limitDiscovery);
      
      // 2. User-based connection limits
      console.log(chalk.blue('\n2️⃣ User-based Connection Limits'));
      const userLimitTests = await this.runUserConnectionLimitTest();
      this.testResults.userLimitTests = userLimitTests;
      
      // 3. Connection stress patterns
      console.log(chalk.blue('\n3️⃣ Connection Stress Patterns'));
      const stressPatterns = await this.runConnectionStressPatterns();
      this.testResults.patterns = stressPatterns;
      
      // 4. Connection recovery tests
      if (this.options.recoveryTesting) {
        console.log(chalk.blue('\n4️⃣ Connection Recovery Tests'));
        const recoveryTests = await this.runConnectionRecoveryTest();
        this.testResults.recoveryTests = recoveryTests;
      }
      
      this.testResults.endTime = Date.now();
      this.testResults.performanceMetrics = this.performanceMonitor.getPerformanceMetrics();
      
      // Print results
      this.printConnectionLimitsResults();
      
      return this.testResults;
      
    } catch (error) {
      console.error(chalk.red('Connection limits test suite failed:'), error);
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    console.log(chalk.blue('🧹 Cleaning up connection limits test resources...'));
    
    await this.clientFactory.closeAllClients();
    this.performanceMonitor.stop();
    
    console.log(chalk.green('✓ Connection limits test cleanup completed'));
  }
}

// CLI interface
const argv = yargs(process.argv.slice(2))
  .option('max-attempts', {
    alias: 'a',
    type: 'number',
    default: 150,
    description: 'Maximum connection attempts'
  })
  .option('batch-size', {
    alias: 'b',
    type: 'number',
    default: 10,
    description: 'Connection batch size'
  })
  .option('user-limit', {
    alias: 'u',
    type: 'number',
    default: 10,
    description: 'Maximum connections per user'
  })
  .option('patterns', {
    alias: 'p',
    type: 'array',
    default: ['gradual', 'burst', 'sustained', 'mixed'],
    description: 'Stress patterns to test'
  })
  .option('recovery', {
    alias: 'r',
    type: 'boolean',
    default: true,
    description: 'Enable recovery testing'
  })
  .help()
  .argv;

// Run if called directly
if (require.main === module) {
  const connectionLimitsTest = new ConnectionLimitsTest({
    maxConnectionAttempts: argv.maxAttempts,
    connectionBatchSize: argv.batchSize,
    userLimits: { maxPerUser: argv.userLimit, userCount: 15 },
    stressPatterns: argv.patterns,
    recoveryTesting: argv.recovery
  });
  
  connectionLimitsTest.runConnectionLimitsTestSuite()
    .then(results => {
      console.log(chalk.green('\n✅ Connection limits test completed successfully!'));
    })
    .catch(error => {
      console.error(chalk.red('\n❌ Connection limits test failed:'), error);
      process.exit(1);
    });
}

module.exports = ConnectionLimitsTest;