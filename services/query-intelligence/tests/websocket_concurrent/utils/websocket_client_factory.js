const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const EventEmitter = require('events');

/**
 * WebSocket Client Factory for concurrent connection testing
 * Creates and manages multiple WebSocket connections with authentication
 */
class WebSocketClientFactory extends EventEmitter {
  constructor(options = {}) {
    super();
    this.baseUrl = options.baseUrl || global.TEST_CONFIG.WS_URL;
    this.jwtSecret = options.jwtSecret || global.TEST_CONFIG.JWT_SECRET;
    this.connectionTimeout = options.connectionTimeout || global.TEST_CONFIG.CONNECTION_TIMEOUT;
    this.messageTimeout = options.messageTimeout || global.TEST_CONFIG.MESSAGE_TIMEOUT;
    this.clients = new Map();
    this.connectionStats = {
      created: 0,
      connected: 0,
      failed: 0,
      disconnected: 0,
      errors: 0
    };
    this.messageStats = {
      sent: 0,
      received: 0,
      acknowledgments: 0,
      errors: 0,
      streamingChunks: 0
    };
  }

  /**
   * Generate JWT token for user authentication
   */
  generateJWT(userId, email) {
    return jwt.sign(
      {
        sub: userId,
        email: email,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour expiration
      },
      this.jwtSecret,
      { algorithm: 'HS256' }
    );
  }

  /**
   * Create multiple users with JWT tokens
   */
  createUsers(count) {
    const users = [];
    for (let i = 0; i < count; i++) {
      const userId = `user-${i}-${uuidv4()}`;
      const email = `user${i}@example.com`;
      const token = this.generateJWT(userId, email);
      users.push({ userId, email, token });
    }
    return users;
  }

  /**
   * Create a single WebSocket client with authentication
   */
  async createClient(user, options = {}) {
    const clientId = `client-${uuidv4()}`;
    const client = {
      id: clientId,
      userId: user.userId,
      ws: null,
      connected: false,
      messages: [],
      stats: {
        messagesReceived: 0,
        messagesSent: 0,
        errors: 0,
        connectionTime: null,
        lastMessageTime: null
      },
      options: options
    };

    try {
      this.connectionStats.created++;
      
      // Create WebSocket connection with authentication header
      const ws = new WebSocket(this.baseUrl, {
        headers: {
          'Authorization': `Bearer ${user.token}`
        },
        timeout: this.connectionTimeout
      });

      client.ws = ws;

      // Set up event handlers
      ws.on('open', () => {
        client.connected = true;
        client.stats.connectionTime = Date.now();
        this.connectionStats.connected++;
        this.emit('client-connected', client);
      });

      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          client.messages.push({
            ...message,
            timestamp: Date.now()
          });
          client.stats.messagesReceived++;
          client.stats.lastMessageTime = Date.now();
          this.messageStats.received++;

          // Track specific message types
          if (message.type === 'acknowledged') {
            this.messageStats.acknowledgments++;
          } else if (message.type === 'text') {
            this.messageStats.streamingChunks++;
          } else if (message.type === 'error') {
            this.messageStats.errors++;
            client.stats.errors++;
          }

          this.emit('message-received', client, message);
        } catch (error) {
          client.stats.errors++;
          this.messageStats.errors++;
          this.emit('message-error', client, error);
        }
      });

      ws.on('error', (error) => {
        client.stats.errors++;
        this.connectionStats.errors++;
        this.emit('client-error', client, error);
      });

      ws.on('close', (code, reason) => {
        client.connected = false;
        this.connectionStats.disconnected++;
        this.emit('client-disconnected', client, { code, reason });
      });

      // Wait for connection to establish
      await this.waitForConnection(client);
      
      this.clients.set(clientId, client);
      return client;

    } catch (error) {
      this.connectionStats.failed++;
      throw new Error(`Failed to create client: ${error.message}`);
    }
  }

  /**
   * Create multiple WebSocket clients concurrently
   */
  async createConcurrentClients(userCount, connectionsPerUser = 1) {
    const users = this.createUsers(userCount);
    const clients = [];
    const promises = [];

    for (const user of users) {
      for (let i = 0; i < connectionsPerUser; i++) {
        promises.push(
          this.createClient(user, { connectionIndex: i })
            .then(client => {
              clients.push(client);
              return client;
            })
            .catch(error => {
              console.error(`Failed to create client for ${user.userId}:`, error);
              return null;
            })
        );
      }
    }

    const results = await Promise.allSettled(promises);
    const successfulClients = results
      .filter(result => result.status === 'fulfilled' && result.value !== null)
      .map(result => result.value);

    this.emit('concurrent-clients-created', {
      requested: userCount * connectionsPerUser,
      successful: successfulClients.length,
      failed: results.length - successfulClients.length
    });

    return successfulClients;
  }

  /**
   * Wait for WebSocket connection to establish
   */
  async waitForConnection(client) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Connection timeout'));
      }, this.connectionTimeout);

      if (client.connected) {
        clearTimeout(timeout);
        resolve();
        return;
      }

      client.ws.on('open', () => {
        clearTimeout(timeout);
        resolve();
      });

      client.ws.on('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });
  }

  /**
   * Send message to specific client
   */
  async sendMessage(client, message) {
    if (!client.connected || !client.ws) {
      throw new Error('Client not connected');
    }

    try {
      const messageStr = JSON.stringify(message);
      client.ws.send(messageStr);
      client.stats.messagesSent++;
      this.messageStats.sent++;
      this.emit('message-sent', client, message);
    } catch (error) {
      client.stats.errors++;
      this.messageStats.errors++;
      throw error;
    }
  }

  /**
   * Send messages to all clients concurrently
   */
  async broadcastMessage(message) {
    const promises = [];
    
    for (const client of this.clients.values()) {
      if (client.connected) {
        promises.push(
          this.sendMessage(client, message)
            .catch(error => {
              console.error(`Failed to send message to ${client.id}:`, error);
              return { clientId: client.id, error };
            })
        );
      }
    }

    const results = await Promise.allSettled(promises);
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.length - successful;

    this.emit('broadcast-complete', { successful, failed, total: results.length });
    return { successful, failed };
  }

  /**
   * Send unique messages to each client
   */
  async sendUniqueMessages(messageFactory) {
    const promises = [];
    
    for (const client of this.clients.values()) {
      if (client.connected) {
        const uniqueMessage = messageFactory(client);
        promises.push(
          this.sendMessage(client, uniqueMessage)
            .catch(error => {
              console.error(`Failed to send unique message to ${client.id}:`, error);
              return { clientId: client.id, error };
            })
        );
      }
    }

    const results = await Promise.allSettled(promises);
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.length - successful;

    return { successful, failed };
  }

  /**
   * Wait for specific message type from client
   */
  async waitForMessage(client, messageType, timeout = this.messageTimeout) {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Timeout waiting for message type: ${messageType}`));
      }, timeout);

      // Check if message already received
      const existingMessage = client.messages.find(msg => msg.type === messageType);
      if (existingMessage) {
        clearTimeout(timeoutId);
        resolve(existingMessage);
        return;
      }

      // Wait for new message
      const messageHandler = (receivedClient, message) => {
        if (receivedClient.id === client.id && message.type === messageType) {
          clearTimeout(timeoutId);
          this.removeListener('message-received', messageHandler);
          resolve(message);
        }
      };

      this.on('message-received', messageHandler);
    });
  }

  /**
   * Wait for streaming completion from client
   */
  async waitForStreamingComplete(client, timeout = global.TEST_CONFIG.STREAMING_TIMEOUT) {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error('Timeout waiting for streaming completion'));
      }, timeout);

      const streamingData = {
        chunks: [],
        references: [],
        metadata: null,
        complete: false
      };

      const messageHandler = (receivedClient, message) => {
        if (receivedClient.id !== client.id) return;

        switch (message.type) {
          case 'text':
            streamingData.chunks.push(message);
            break;
          case 'reference':
            streamingData.references.push(message);
            break;
          case 'done':
            streamingData.metadata = message.metadata;
            streamingData.complete = true;
            clearTimeout(timeoutId);
            this.removeListener('message-received', messageHandler);
            resolve(streamingData);
            break;
          case 'error':
            clearTimeout(timeoutId);
            this.removeListener('message-received', messageHandler);
            reject(new Error(`Streaming error: ${message.message}`));
            break;
        }
      };

      this.on('message-received', messageHandler);
    });
  }

  /**
   * Get client by ID
   */
  getClient(clientId) {
    return this.clients.get(clientId);
  }

  /**
   * Get all clients
   */
  getAllClients() {
    return Array.from(this.clients.values());
  }

  /**
   * Get connected clients
   */
  getConnectedClients() {
    return this.getAllClients().filter(client => client.connected);
  }

  /**
   * Get connection statistics
   */
  getConnectionStats() {
    return {
      ...this.connectionStats,
      activeConnections: this.getConnectedClients().length,
      successRate: this.connectionStats.created > 0 
        ? this.connectionStats.connected / this.connectionStats.created 
        : 0
    };
  }

  /**
   * Get message statistics
   */
  getMessageStats() {
    return {
      ...this.messageStats,
      avgMessagesPerClient: this.clients.size > 0 
        ? this.messageStats.received / this.clients.size 
        : 0,
      errorRate: this.messageStats.received > 0 
        ? this.messageStats.errors / this.messageStats.received 
        : 0
    };
  }

  /**
   * Get detailed performance metrics
   */
  getPerformanceMetrics() {
    const clients = this.getAllClients();
    const connectedClients = this.getConnectedClients();
    
    // Calculate connection times
    const connectionTimes = clients
      .filter(c => c.stats.connectionTime)
      .map(c => c.stats.connectionTime);
    
    // Calculate message response times
    const messageTimes = [];
    clients.forEach(client => {
      if (client.stats.lastMessageTime && client.stats.connectionTime) {
        messageTimes.push(client.stats.lastMessageTime - client.stats.connectionTime);
      }
    });

    return {
      connections: {
        total: clients.length,
        active: connectedClients.length,
        successRate: this.getConnectionStats().successRate,
        avgConnectionTime: connectionTimes.length > 0 
          ? connectionTimes.reduce((a, b) => a + b, 0) / connectionTimes.length 
          : 0
      },
      messages: {
        ...this.getMessageStats(),
        avgResponseTime: messageTimes.length > 0 
          ? messageTimes.reduce((a, b) => a + b, 0) / messageTimes.length 
          : 0
      },
      memory: {
        totalMessages: clients.reduce((sum, c) => sum + c.messages.length, 0),
        avgMessagesPerClient: clients.length > 0 
          ? clients.reduce((sum, c) => sum + c.messages.length, 0) / clients.length 
          : 0
      }
    };
  }

  /**
   * Close specific client
   */
  async closeClient(clientId) {
    const client = this.clients.get(clientId);
    if (!client) {
      throw new Error(`Client not found: ${clientId}`);
    }

    if (client.ws && client.connected) {
      client.ws.close(1000, 'Normal closure');
    }

    this.clients.delete(clientId);
    this.emit('client-closed', client);
  }

  /**
   * Close all clients
   */
  async closeAllClients() {
    const promises = [];
    
    for (const clientId of this.clients.keys()) {
      promises.push(
        this.closeClient(clientId).catch(error => {
          console.error(`Failed to close client ${clientId}:`, error);
        })
      );
    }

    await Promise.allSettled(promises);
    this.clients.clear();
    this.emit('all-clients-closed');
  }

  /**
   * Reset all statistics
   */
  resetStats() {
    this.connectionStats = {
      created: 0,
      connected: 0,
      failed: 0,
      disconnected: 0,
      errors: 0
    };
    this.messageStats = {
      sent: 0,
      received: 0,
      acknowledgments: 0,
      errors: 0,
      streamingChunks: 0
    };
    this.emit('stats-reset');
  }
}

module.exports = WebSocketClientFactory;