const chalk = require('chalk');

/**
 * Streaming Performance Validator
 * Validates WebSocket streaming quality and performance metrics
 */
class StreamingValidator {
  constructor(options = {}) {
    this.options = {
      minStreamingChunks: options.minStreamingChunks || 2,
      maxStreamingLatency: options.maxStreamingLatency || 500, // ms
      minContentQuality: options.minContentQuality || 0.7,
      maxChunkSize: options.maxChunkSize || 1024, // bytes
      expectedMessageTypes: options.expectedMessageTypes || [
        'acknowledged', 'processing_started', 'intent_analyzed', 
        'status', 'search_complete', 'text', 'done'
      ],
      contentValidationRules: options.contentValidationRules || {
        minLength: 10,
        maxLength: 10000,
        requiresAlphanumeric: true,
        forbiddenPatterns: [/<script>/i, /javascript:/i, /on\w+=/i]
      }
    };
    
    this.validationResults = [];
    this.performanceMetrics = {
      totalValidations: 0,
      successfulValidations: 0,
      failedValidations: 0,
      avgStreamingLatency: 0,
      avgContentQuality: 0,
      avgChunkCount: 0
    };
  }

  /**
   * Validate complete streaming session
   */
  async validateStreamingSession(client, streamingData, startTime) {
    const validation = {
      clientId: client.id,
      userId: client.userId,
      startTime,
      endTime: Date.now(),
      duration: Date.now() - startTime,
      passed: true,
      errors: [],
      warnings: [],
      metrics: {}
    };

    this.performanceMetrics.totalValidations++;

    try {
      // Validate message sequence
      this.validateMessageSequence(streamingData, validation);
      
      // Validate streaming performance
      this.validateStreamingPerformance(streamingData, validation);
      
      // Validate content quality
      this.validateContentQuality(streamingData, validation);
      
      // Validate message ordering
      this.validateMessageOrdering(streamingData, validation);
      
      // Validate completion metadata
      this.validateCompletionMetadata(streamingData, validation);
      
      // Calculate overall metrics
      this.calculateValidationMetrics(validation);
      
      if (validation.passed) {
        this.performanceMetrics.successfulValidations++;
      } else {
        this.performanceMetrics.failedValidations++;
      }
      
      this.validationResults.push(validation);
      return validation;
      
    } catch (error) {
      validation.passed = false;
      validation.errors.push(`Validation error: ${error.message}`);
      this.performanceMetrics.failedValidations++;
      this.validationResults.push(validation);
      return validation;
    }
  }

  /**
   * Validate message sequence and types
   */
  validateMessageSequence(streamingData, validation) {
    const allMessages = [
      ...streamingData.chunks,
      ...streamingData.references,
      { type: 'done', metadata: streamingData.metadata }
    ].filter(msg => msg.type);

    // Check for required message types
    const messageTypes = allMessages.map(msg => msg.type);
    const missingTypes = this.options.expectedMessageTypes.filter(
      type => !messageTypes.includes(type)
    );

    if (missingTypes.length > 0) {
      validation.warnings.push(`Missing message types: ${missingTypes.join(', ')}`);
    }

    // Check message type distribution
    const typeDistribution = {};
    messageTypes.forEach(type => {
      typeDistribution[type] = (typeDistribution[type] || 0) + 1;
    });

    validation.metrics.messageTypeDistribution = typeDistribution;
    validation.metrics.totalMessages = allMessages.length;

    // Validate minimum streaming chunks
    if (streamingData.chunks.length < this.options.minStreamingChunks) {
      validation.errors.push(
        `Insufficient streaming chunks: ${streamingData.chunks.length} < ${this.options.minStreamingChunks}`
      );
      validation.passed = false;
    }
  }

  /**
   * Validate streaming performance metrics
   */
  validateStreamingPerformance(streamingData, validation) {
    const chunks = streamingData.chunks;
    if (chunks.length === 0) {
      validation.errors.push('No streaming chunks received');
      validation.passed = false;
      return;
    }

    // Calculate inter-chunk latency
    const latencies = [];
    for (let i = 1; i < chunks.length; i++) {
      const latency = chunks[i].timestamp - chunks[i-1].timestamp;
      latencies.push(latency);
    }

    const avgLatency = latencies.length > 0 
      ? latencies.reduce((a, b) => a + b, 0) / latencies.length 
      : 0;
    
    const maxLatency = latencies.length > 0 ? Math.max(...latencies) : 0;
    const minLatency = latencies.length > 0 ? Math.min(...latencies) : 0;

    validation.metrics.streaming = {
      chunkCount: chunks.length,
      avgLatency,
      maxLatency,
      minLatency,
      latencyVariance: this.calculateVariance(latencies)
    };

    // Check latency thresholds
    if (avgLatency > this.options.maxStreamingLatency) {
      validation.errors.push(
        `High streaming latency: ${avgLatency}ms > ${this.options.maxStreamingLatency}ms`
      );
      validation.passed = false;
    }

    if (maxLatency > this.options.maxStreamingLatency * 2) {
      validation.warnings.push(
        `Peak latency spike: ${maxLatency}ms`
      );
    }

    // Update global metrics
    this.performanceMetrics.avgStreamingLatency = 
      (this.performanceMetrics.avgStreamingLatency * (this.performanceMetrics.totalValidations - 1) + avgLatency) / 
      this.performanceMetrics.totalValidations;
    
    this.performanceMetrics.avgChunkCount = 
      (this.performanceMetrics.avgChunkCount * (this.performanceMetrics.totalValidations - 1) + chunks.length) / 
      this.performanceMetrics.totalValidations;
  }

  /**
   * Validate content quality and safety
   */
  validateContentQuality(streamingData, validation) {
    const chunks = streamingData.chunks;
    let totalContent = '';
    let totalContentLength = 0;
    let chunkSizeViolations = 0;
    let contentQualityScore = 0;

    // Analyze each chunk
    for (const chunk of chunks) {
      const content = chunk.content || '';
      const chunkSize = Buffer.byteLength(content, 'utf8');
      
      totalContent += content;
      totalContentLength += content.length;

      // Check chunk size
      if (chunkSize > this.options.maxChunkSize) {
        chunkSizeViolations++;
        validation.warnings.push(
          `Oversized chunk: ${chunkSize} bytes > ${this.options.maxChunkSize} bytes`
        );
      }

      // Check for forbidden patterns
      const forbiddenPattern = this.options.contentValidationRules.forbiddenPatterns.find(
        pattern => pattern.test(content)
      );
      if (forbiddenPattern) {
        validation.errors.push(
          `Forbidden content pattern detected: ${forbiddenPattern.toString()}`
        );
        validation.passed = false;
      }
    }

    // Validate total content
    const rules = this.options.contentValidationRules;
    
    if (totalContentLength < rules.minLength) {
      validation.errors.push(
        `Content too short: ${totalContentLength} < ${rules.minLength}`
      );
      validation.passed = false;
    }

    if (totalContentLength > rules.maxLength) {
      validation.errors.push(
        `Content too long: ${totalContentLength} > ${rules.maxLength}`
      );
      validation.passed = false;
    }

    // Check alphanumeric requirement
    if (rules.requiresAlphanumeric && !/[a-zA-Z0-9]/.test(totalContent)) {
      validation.errors.push('Content must contain alphanumeric characters');
      validation.passed = false;
    }

    // Calculate content quality score
    contentQualityScore = this.calculateContentQualityScore(totalContent, chunks);
    
    validation.metrics.content = {
      totalLength: totalContentLength,
      chunkCount: chunks.length,
      avgChunkSize: chunks.length > 0 ? totalContentLength / chunks.length : 0,
      chunkSizeViolations,
      qualityScore: contentQualityScore
    };

    // Check quality threshold
    if (contentQualityScore < this.options.minContentQuality) {
      validation.errors.push(
        `Low content quality: ${contentQualityScore} < ${this.options.minContentQuality}`
      );
      validation.passed = false;
    }

    // Update global metrics
    this.performanceMetrics.avgContentQuality = 
      (this.performanceMetrics.avgContentQuality * (this.performanceMetrics.totalValidations - 1) + contentQualityScore) / 
      this.performanceMetrics.totalValidations;
  }

  /**
   * Validate message ordering and consistency
   */
  validateMessageOrdering(streamingData, validation) {
    const chunks = streamingData.chunks;
    
    // Check timestamp ordering
    let orderingErrors = 0;
    for (let i = 1; i < chunks.length; i++) {
      if (chunks[i].timestamp < chunks[i-1].timestamp) {
        orderingErrors++;
      }
    }

    if (orderingErrors > 0) {
      validation.warnings.push(
        `Message ordering issues: ${orderingErrors} out-of-order chunks`
      );
    }

    // Check for duplicate timestamps
    const timestamps = chunks.map(chunk => chunk.timestamp);
    const uniqueTimestamps = new Set(timestamps);
    
    if (uniqueTimestamps.size !== timestamps.length) {
      validation.warnings.push(
        `Duplicate timestamps detected: ${timestamps.length - uniqueTimestamps.size} duplicates`
      );
    }

    validation.metrics.ordering = {
      orderingErrors,
      duplicateTimestamps: timestamps.length - uniqueTimestamps.size,
      totalChunks: chunks.length
    };
  }

  /**
   * Validate completion metadata
   */
  validateCompletionMetadata(streamingData, validation) {
    const metadata = streamingData.metadata;
    
    if (!metadata) {
      validation.errors.push('Missing completion metadata');
      validation.passed = false;
      return;
    }

    // Check required metadata fields
    const requiredFields = ['intent', 'confidence', 'total_chunks', 'chunks_used'];
    const missingFields = requiredFields.filter(field => !(field in metadata));
    
    if (missingFields.length > 0) {
      validation.warnings.push(
        `Missing metadata fields: ${missingFields.join(', ')}`
      );
    }

    // Validate confidence score
    const confidence = metadata.confidence;
    if (typeof confidence === 'number') {
      if (confidence < 0 || confidence > 1) {
        validation.errors.push(
          `Invalid confidence score: ${confidence} (should be 0-1)`
        );
        validation.passed = false;
      }
    }

    // Validate chunk counts
    if (metadata.total_chunks && metadata.chunks_used) {
      if (metadata.chunks_used > metadata.total_chunks) {
        validation.errors.push(
          `Inconsistent chunk counts: used ${metadata.chunks_used} > total ${metadata.total_chunks}`
        );
        validation.passed = false;
      }
    }

    validation.metrics.metadata = {
      hasMetadata: true,
      confidence: confidence,
      totalChunks: metadata.total_chunks,
      chunksUsed: metadata.chunks_used,
      intent: metadata.intent,
      hasFollowUps: Array.isArray(metadata.follow_up_questions) && metadata.follow_up_questions.length > 0
    };
  }

  /**
   * Calculate content quality score
   */
  calculateContentQualityScore(content, chunks) {
    let score = 0.5; // Base score
    
    // Length factor (normalized)
    const lengthScore = Math.min(content.length / 500, 1) * 0.2;
    score += lengthScore;
    
    // Coherence factor (simple heuristic)
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const avgSentenceLength = sentences.length > 0 
      ? content.length / sentences.length 
      : 0;
    const coherenceScore = avgSentenceLength > 10 && avgSentenceLength < 200 ? 0.2 : 0;
    score += coherenceScore;
    
    // Chunk consistency factor
    const avgChunkSize = chunks.length > 0 ? content.length / chunks.length : 0;
    const consistencyScore = avgChunkSize > 5 && avgChunkSize < 100 ? 0.1 : 0;
    score += consistencyScore;
    
    return Math.min(score, 1);
  }

  /**
   * Calculate variance for latency analysis
   */
  calculateVariance(values) {
    if (values.length === 0) return 0;
    
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return variance;
  }

  /**
   * Calculate validation metrics
   */
  calculateValidationMetrics(validation) {
    validation.metrics.overall = {
      passed: validation.passed,
      errorCount: validation.errors.length,
      warningCount: validation.warnings.length,
      duration: validation.duration,
      efficiency: validation.metrics.content ? 
        validation.metrics.content.totalLength / validation.duration : 0
    };
  }

  /**
   * Validate concurrent streaming sessions
   */
  async validateConcurrentSessions(sessions) {
    const validationPromises = sessions.map(session => 
      this.validateStreamingSession(session.client, session.streamingData, session.startTime)
    );

    const validations = await Promise.all(validationPromises);
    
    // Analyze concurrent performance
    const concurrentMetrics = this.analyzeConcurrentPerformance(validations);
    
    return {
      validations,
      concurrentMetrics,
      summary: this.generateValidationSummary(validations)
    };
  }

  /**
   * Analyze concurrent performance patterns
   */
  analyzeConcurrentPerformance(validations) {
    const passedValidations = validations.filter(v => v.passed);
    const failedValidations = validations.filter(v => !v.passed);
    
    // Calculate concurrent metrics
    const concurrentLatencies = validations
      .filter(v => v.metrics.streaming)
      .map(v => v.metrics.streaming.avgLatency);
    
    const concurrentQuality = validations
      .filter(v => v.metrics.content)
      .map(v => v.metrics.content.qualityScore);

    return {
      totalSessions: validations.length,
      successfulSessions: passedValidations.length,
      failedSessions: failedValidations.length,
      successRate: validations.length > 0 ? passedValidations.length / validations.length : 0,
      avgConcurrentLatency: concurrentLatencies.length > 0 
        ? concurrentLatencies.reduce((a, b) => a + b, 0) / concurrentLatencies.length 
        : 0,
      avgConcurrentQuality: concurrentQuality.length > 0 
        ? concurrentQuality.reduce((a, b) => a + b, 0) / concurrentQuality.length 
        : 0,
      latencyVariance: this.calculateVariance(concurrentLatencies),
      qualityVariance: this.calculateVariance(concurrentQuality)
    };
  }

  /**
   * Generate validation summary
   */
  generateValidationSummary(validations) {
    const totalErrors = validations.reduce((sum, v) => sum + v.errors.length, 0);
    const totalWarnings = validations.reduce((sum, v) => sum + v.warnings.length, 0);
    
    return {
      totalValidations: validations.length,
      passedValidations: validations.filter(v => v.passed).length,
      failedValidations: validations.filter(v => !v.passed).length,
      totalErrors,
      totalWarnings,
      globalMetrics: this.performanceMetrics
    };
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics() {
    return {
      ...this.performanceMetrics,
      successRate: this.performanceMetrics.totalValidations > 0 
        ? this.performanceMetrics.successfulValidations / this.performanceMetrics.totalValidations 
        : 0
    };
  }

  /**
   * Get validation results
   */
  getValidationResults() {
    return this.validationResults;
  }

  /**
   * Generate detailed report
   */
  generateReport() {
    const metrics = this.getPerformanceMetrics();
    const results = this.getValidationResults();
    
    return {
      summary: {
        totalValidations: metrics.totalValidations,
        successRate: metrics.successRate,
        avgStreamingLatency: metrics.avgStreamingLatency,
        avgContentQuality: metrics.avgContentQuality,
        avgChunkCount: metrics.avgChunkCount
      },
      details: results,
      recommendations: this.generateRecommendations(metrics, results)
    };
  }

  /**
   * Generate performance recommendations
   */
  generateRecommendations(metrics, results) {
    const recommendations = [];
    
    if (metrics.successRate < 0.95) {
      recommendations.push({
        type: 'performance',
        priority: 'high',
        message: `Low success rate: ${(metrics.successRate * 100).toFixed(1)}%`,
        suggestion: 'Review error patterns and improve error handling'
      });
    }
    
    if (metrics.avgStreamingLatency > this.options.maxStreamingLatency) {
      recommendations.push({
        type: 'latency',
        priority: 'medium',
        message: `High streaming latency: ${metrics.avgStreamingLatency.toFixed(1)}ms`,
        suggestion: 'Optimize streaming buffer size and processing pipeline'
      });
    }
    
    if (metrics.avgContentQuality < this.options.minContentQuality) {
      recommendations.push({
        type: 'quality',
        priority: 'medium',
        message: `Low content quality: ${metrics.avgContentQuality.toFixed(2)}`,
        suggestion: 'Improve content generation and validation logic'
      });
    }
    
    return recommendations;
  }

  /**
   * Reset validation state
   */
  reset() {
    this.validationResults = [];
    this.performanceMetrics = {
      totalValidations: 0,
      successfulValidations: 0,
      failedValidations: 0,
      avgStreamingLatency: 0,
      avgContentQuality: 0,
      avgChunkCount: 0
    };
  }

  /**
   * Print validation report to console
   */
  printReport() {
    const report = this.generateReport();
    
    console.log(chalk.blue('\n=== STREAMING VALIDATION REPORT ==='));
    console.log(chalk.green(`✓ Total Validations: ${report.summary.totalValidations}`));
    console.log(chalk.green(`✓ Success Rate: ${(report.summary.successRate * 100).toFixed(1)}%`));
    console.log(chalk.yellow(`⚡ Avg Streaming Latency: ${report.summary.avgStreamingLatency.toFixed(1)}ms`));
    console.log(chalk.yellow(`📊 Avg Content Quality: ${report.summary.avgContentQuality.toFixed(2)}`));
    console.log(chalk.yellow(`📦 Avg Chunk Count: ${report.summary.avgChunkCount.toFixed(1)}`));
    
    if (report.recommendations.length > 0) {
      console.log(chalk.blue('\n=== RECOMMENDATIONS ==='));
      report.recommendations.forEach(rec => {
        const priority = rec.priority === 'high' ? chalk.red : 
                        rec.priority === 'medium' ? chalk.yellow : chalk.green;
        console.log(priority(`${rec.type.toUpperCase()}: ${rec.message}`));
        console.log(chalk.gray(`  → ${rec.suggestion}`));
      });
    }
  }
}

module.exports = StreamingValidator;