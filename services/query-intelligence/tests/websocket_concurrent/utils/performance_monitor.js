const chalk = require('chalk');
const Table = require('cli-table3');
const EventEmitter = require('events');

/**
 * Real-time Performance Monitor for WebSocket concurrent testing
 * Tracks system resources, connection health, and performance metrics
 */
class PerformanceMonitor extends EventEmitter {
  constructor(options = {}) {
    super();
    this.options = {
      monitoringInterval: options.monitoringInterval || 1000, // 1 second
      memoryThreshold: options.memoryThreshold || global.TEST_CONFIG.MEMORY_LIMIT_MB * 1024 * 1024,
      latencyThreshold: options.latencyThreshold || global.TEST_CONFIG.PERFORMANCE_THRESHOLD_MS,
      connectionThreshold: options.connectionThreshold || global.TEST_CONFIG.MAX_CONCURRENT_CONNECTIONS,
      historySizeLimit: options.historySizeLimit || 300, // 5 minutes of history
      alertThresholds: {
        memory: options.memoryThreshold || 0.8,
        cpu: options.cpuThreshold || 0.8,
        connections: options.connectionThreshold || 0.9,
        latency: options.latencyThreshold || 2000,
        errorRate: options.errorRateThreshold || 0.05
      }
    };

    this.isMonitoring = false;
    this.monitoringInterval = null;
    this.startTime = null;
    this.history = [];
    this.alerts = [];
    this.currentMetrics = {
      timestamp: 0,
      memory: { used: 0, total: 0, percentage: 0 },
      connections: { active: 0, total: 0, failed: 0 },
      messages: { sent: 0, received: 0, errors: 0 },
      performance: { avgLatency: 0, maxLatency: 0, throughput: 0 },
      errors: { count: 0, rate: 0, types: {} }
    };

    this.counters = {
      totalConnections: 0,
      successfulConnections: 0,
      failedConnections: 0,
      totalMessages: 0,
      successfulMessages: 0,
      failedMessages: 0,
      totalErrors: 0
    };

    this.clientFactories = new Set();
    this.streamingValidators = new Set();
  }

  /**
   * Register client factory for monitoring
   */
  registerClientFactory(factory) {
    this.clientFactories.add(factory);
    
    // Listen to factory events
    factory.on('client-connected', (client) => {
      this.counters.successfulConnections++;
      this.emit('connection-established', client);
    });

    factory.on('client-error', (client, error) => {
      this.counters.failedConnections++;
      this.recordError('connection', error);
      this.emit('connection-error', client, error);
    });

    factory.on('message-sent', (client, message) => {
      this.counters.totalMessages++;
      this.counters.successfulMessages++;
    });

    factory.on('message-error', (client, error) => {
      this.counters.failedMessages++;
      this.recordError('message', error);
    });
  }

  /**
   * Register streaming validator for monitoring
   */
  registerStreamingValidator(validator) {
    this.streamingValidators.add(validator);
  }

  /**
   * Start monitoring
   */
  start() {
    if (this.isMonitoring) {
      console.log(chalk.yellow('Monitor already running'));
      return;
    }

    this.isMonitoring = true;
    this.startTime = Date.now();
    
    console.log(chalk.green('🔍 Starting performance monitoring...'));
    console.log(chalk.blue(`Monitor interval: ${this.options.monitoringInterval}ms`));
    console.log(chalk.blue(`Memory threshold: ${(this.options.memoryThreshold / 1024 / 1024).toFixed(1)}MB`));
    console.log(chalk.blue(`Latency threshold: ${this.options.latencyThreshold}ms`));
    
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
      this.checkAlerts();
      this.emit('metrics-collected', this.currentMetrics);
    }, this.options.monitoringInterval);

    this.emit('monitoring-started');
  }

  /**
   * Stop monitoring
   */
  stop() {
    if (!this.isMonitoring) {
      console.log(chalk.yellow('Monitor not running'));
      return;
    }

    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    console.log(chalk.green('🔍 Performance monitoring stopped'));
    this.emit('monitoring-stopped');
  }

  /**
   * Collect current metrics
   */
  collectMetrics() {
    const now = Date.now();
    const memoryUsage = process.memoryUsage();
    
    // Collect memory metrics
    const memoryMetrics = {
      used: memoryUsage.heapUsed,
      total: memoryUsage.heapTotal,
      percentage: memoryUsage.heapUsed / memoryUsage.heapTotal,
      external: memoryUsage.external,
      rss: memoryUsage.rss
    };

    // Collect connection metrics
    const connectionMetrics = this.collectConnectionMetrics();
    
    // Collect message metrics
    const messageMetrics = this.collectMessageMetrics();
    
    // Collect performance metrics
    const performanceMetrics = this.collectPerformanceMetrics();
    
    // Collect error metrics
    const errorMetrics = this.collectErrorMetrics();

    // Update current metrics
    this.currentMetrics = {
      timestamp: now,
      memory: memoryMetrics,
      connections: connectionMetrics,
      messages: messageMetrics,
      performance: performanceMetrics,
      errors: errorMetrics,
      uptime: now - this.startTime
    };

    // Add to history
    this.history.push({ ...this.currentMetrics });
    
    // Limit history size
    if (this.history.length > this.options.historySizeLimit) {
      this.history.shift();
    }
  }

  /**
   * Collect connection metrics from client factories
   */
  collectConnectionMetrics() {
    let totalActive = 0;
    let totalConnected = 0;
    let totalFailed = 0;
    let totalClients = 0;

    for (const factory of this.clientFactories) {
      const stats = factory.getConnectionStats();
      totalActive += stats.activeConnections;
      totalConnected += stats.connected;
      totalFailed += stats.failed;
      totalClients += factory.getAllClients().length;
    }

    return {
      active: totalActive,
      total: totalClients,
      connected: totalConnected,
      failed: totalFailed,
      successRate: totalClients > 0 ? totalConnected / totalClients : 0
    };
  }

  /**
   * Collect message metrics from client factories
   */
  collectMessageMetrics() {
    let totalSent = 0;
    let totalReceived = 0;
    let totalErrors = 0;
    let totalStreamingChunks = 0;

    for (const factory of this.clientFactories) {
      const stats = factory.getMessageStats();
      totalSent += stats.sent;
      totalReceived += stats.received;
      totalErrors += stats.errors;
      totalStreamingChunks += stats.streamingChunks;
    }

    return {
      sent: totalSent,
      received: totalReceived,
      errors: totalErrors,
      streamingChunks: totalStreamingChunks,
      throughput: this.calculateThroughput(totalReceived),
      errorRate: totalReceived > 0 ? totalErrors / totalReceived : 0
    };
  }

  /**
   * Collect performance metrics from client factories
   */
  collectPerformanceMetrics() {
    let totalLatency = 0;
    let maxLatency = 0;
    let clientCount = 0;
    let throughputSum = 0;

    for (const factory of this.clientFactories) {
      const metrics = factory.getPerformanceMetrics();
      
      if (metrics.messages.avgResponseTime > 0) {
        totalLatency += metrics.messages.avgResponseTime;
        clientCount++;
      }
      
      if (metrics.messages.avgResponseTime > maxLatency) {
        maxLatency = metrics.messages.avgResponseTime;
      }
      
      throughputSum += metrics.messages.received;
    }

    const avgLatency = clientCount > 0 ? totalLatency / clientCount : 0;
    const throughput = this.calculateThroughput(throughputSum);

    return {
      avgLatency,
      maxLatency,
      throughput,
      clientCount,
      latencyHealth: this.assessLatencyHealth(avgLatency)
    };
  }

  /**
   * Collect error metrics
   */
  collectErrorMetrics() {
    const errorTypes = {};
    let totalErrors = 0;

    // Aggregate errors from all sources
    for (const factory of this.clientFactories) {
      const stats = factory.getConnectionStats();
      totalErrors += stats.errors;
    }

    const errorRate = this.counters.totalMessages > 0 
      ? totalErrors / this.counters.totalMessages 
      : 0;

    return {
      count: totalErrors,
      rate: errorRate,
      types: errorTypes,
      recent: this.getRecentErrors()
    };
  }

  /**
   * Calculate throughput (messages per second)
   */
  calculateThroughput(messageCount) {
    if (this.history.length < 2) return 0;
    
    const lastMetric = this.history[this.history.length - 1];
    const prevMetric = this.history[this.history.length - 2];
    
    const timeDiff = (lastMetric.timestamp - prevMetric.timestamp) / 1000; // seconds
    const messageDiff = messageCount - (prevMetric.messages?.received || 0);
    
    return timeDiff > 0 ? messageDiff / timeDiff : 0;
  }

  /**
   * Assess latency health
   */
  assessLatencyHealth(avgLatency) {
    if (avgLatency < 500) return 'excellent';
    if (avgLatency < 1000) return 'good';
    if (avgLatency < 2000) return 'fair';
    return 'poor';
  }

  /**
   * Record error for tracking
   */
  recordError(type, error) {
    const errorRecord = {
      type,
      message: error.message || error.toString(),
      timestamp: Date.now(),
      stack: error.stack
    };

    this.alerts.push({
      type: 'error',
      severity: 'warning',
      message: `${type} error: ${errorRecord.message}`,
      timestamp: errorRecord.timestamp
    });

    this.counters.totalErrors++;
    this.emit('error-recorded', errorRecord);
  }

  /**
   * Get recent errors (last 5 minutes)
   */
  getRecentErrors() {
    const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
    return this.alerts.filter(alert => 
      alert.type === 'error' && alert.timestamp > fiveMinutesAgo
    );
  }

  /**
   * Check alert conditions
   */
  checkAlerts() {
    const metrics = this.currentMetrics;
    const thresholds = this.options.alertThresholds;
    
    // Memory alert
    if (metrics.memory.percentage > thresholds.memory) {
      this.triggerAlert('memory', 'high', 
        `Memory usage: ${(metrics.memory.percentage * 100).toFixed(1)}%`);
    }

    // Latency alert
    if (metrics.performance.avgLatency > thresholds.latency) {
      this.triggerAlert('latency', 'high', 
        `High latency: ${metrics.performance.avgLatency.toFixed(1)}ms`);
    }

    // Connection alert
    if (metrics.connections.active > thresholds.connections) {
      this.triggerAlert('connections', 'high', 
        `High connection count: ${metrics.connections.active}`);
    }

    // Error rate alert
    if (metrics.errors.rate > thresholds.errorRate) {
      this.triggerAlert('error-rate', 'high', 
        `High error rate: ${(metrics.errors.rate * 100).toFixed(1)}%`);
    }
  }

  /**
   * Trigger alert
   */
  triggerAlert(type, severity, message) {
    const alert = {
      type,
      severity,
      message,
      timestamp: Date.now()
    };

    // Check if similar alert exists recently (avoid spam)
    const recentSimilar = this.alerts.find(a => 
      a.type === type && 
      a.timestamp > Date.now() - 30000 // 30 seconds
    );

    if (!recentSimilar) {
      this.alerts.push(alert);
      this.emit('alert-triggered', alert);
      
      // Console output
      const severityColor = severity === 'high' ? chalk.red : 
                           severity === 'medium' ? chalk.yellow : chalk.blue;
      console.log(severityColor(`🚨 ALERT [${type.toUpperCase()}]: ${message}`));
    }
  }

  /**
   * Get current metrics
   */
  getCurrentMetrics() {
    return { ...this.currentMetrics };
  }

  /**
   * Get historical metrics
   */
  getHistoricalMetrics(minutes = 5) {
    const cutoffTime = Date.now() - (minutes * 60 * 1000);
    return this.history.filter(metric => metric.timestamp > cutoffTime);
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary() {
    const historical = this.getHistoricalMetrics();
    
    if (historical.length === 0) {
      return { message: 'No historical data available' };
    }

    const latencies = historical.map(h => h.performance.avgLatency).filter(l => l > 0);
    const throughputs = historical.map(h => h.performance.throughput).filter(t => t > 0);
    const memoryUsages = historical.map(h => h.memory.percentage);
    const connectionCounts = historical.map(h => h.connections.active);

    return {
      latency: {
        avg: latencies.length > 0 ? latencies.reduce((a, b) => a + b, 0) / latencies.length : 0,
        min: latencies.length > 0 ? Math.min(...latencies) : 0,
        max: latencies.length > 0 ? Math.max(...latencies) : 0
      },
      throughput: {
        avg: throughputs.length > 0 ? throughputs.reduce((a, b) => a + b, 0) / throughputs.length : 0,
        min: throughputs.length > 0 ? Math.min(...throughputs) : 0,
        max: throughputs.length > 0 ? Math.max(...throughputs) : 0
      },
      memory: {
        avg: memoryUsages.reduce((a, b) => a + b, 0) / memoryUsages.length,
        min: Math.min(...memoryUsages),
        max: Math.max(...memoryUsages)
      },
      connections: {
        avg: connectionCounts.reduce((a, b) => a + b, 0) / connectionCounts.length,
        min: Math.min(...connectionCounts),
        max: Math.max(...connectionCounts)
      },
      uptime: this.currentMetrics.uptime,
      dataPoints: historical.length
    };
  }

  /**
   * Generate capacity recommendations
   */
  generateCapacityRecommendations() {
    const summary = this.getPerformanceSummary();
    const recommendations = [];

    // Memory recommendations
    if (summary.memory.max > 0.8) {
      recommendations.push({
        type: 'memory',
        priority: 'high',
        current: `${(summary.memory.max * 100).toFixed(1)}%`,
        recommendation: 'Increase memory allocation or optimize memory usage',
        action: 'Scale vertically or implement memory optimization'
      });
    }

    // Latency recommendations
    if (summary.latency.avg > 1000) {
      recommendations.push({
        type: 'latency',
        priority: 'medium',
        current: `${summary.latency.avg.toFixed(1)}ms`,
        recommendation: 'Optimize streaming pipeline or reduce processing overhead',
        action: 'Review streaming buffer sizes and processing logic'
      });
    }

    // Throughput recommendations
    if (summary.throughput.avg < 10) {
      recommendations.push({
        type: 'throughput',
        priority: 'medium',
        current: `${summary.throughput.avg.toFixed(1)} msg/s`,
        recommendation: 'Increase message processing capacity',
        action: 'Scale horizontally or optimize message handling'
      });
    }

    // Connection recommendations
    if (summary.connections.max > 80) {
      recommendations.push({
        type: 'connections',
        priority: 'high',
        current: `${summary.connections.max} connections`,
        recommendation: 'Prepare for connection scaling',
        action: 'Implement connection pooling or load balancing'
      });
    }

    return recommendations;
  }

  /**
   * Print real-time dashboard
   */
  printDashboard() {
    console.clear();
    console.log(chalk.blue('='.repeat(80)));
    console.log(chalk.blue.bold('          WEBSOCKET CONCURRENT TESTING - PERFORMANCE DASHBOARD'));
    console.log(chalk.blue('='.repeat(80)));
    
    const metrics = this.currentMetrics;
    const uptime = Math.floor(metrics.uptime / 1000);
    
    // Header info
    console.log(chalk.green(`🕐 Uptime: ${uptime}s | 📊 Monitoring: ${this.isMonitoring ? 'ACTIVE' : 'STOPPED'}`));
    console.log('');

    // Metrics table
    const table = new Table({
      head: ['Metric', 'Current', 'Status', 'Threshold'],
      colWidths: [25, 15, 15, 20]
    });

    // Memory row
    const memoryPercent = (metrics.memory.percentage * 100).toFixed(1);
    const memoryStatus = metrics.memory.percentage > 0.8 ? 
      chalk.red('HIGH') : metrics.memory.percentage > 0.6 ? 
      chalk.yellow('MODERATE') : chalk.green('NORMAL');
    
    table.push([
      'Memory Usage',
      `${memoryPercent}%`,
      memoryStatus,
      `${(this.options.memoryThreshold / 1024 / 1024).toFixed(1)}MB`
    ]);

    // Connections row
    const connectionStatus = metrics.connections.active > 80 ? 
      chalk.red('HIGH') : metrics.connections.active > 50 ? 
      chalk.yellow('MODERATE') : chalk.green('NORMAL');
    
    table.push([
      'Active Connections',
      `${metrics.connections.active}`,
      connectionStatus,
      `${this.options.connectionThreshold}`
    ]);

    // Latency row
    const latencyStatus = metrics.performance.avgLatency > 2000 ? 
      chalk.red('HIGH') : metrics.performance.avgLatency > 1000 ? 
      chalk.yellow('MODERATE') : chalk.green('NORMAL');
    
    table.push([
      'Avg Latency',
      `${metrics.performance.avgLatency.toFixed(1)}ms`,
      latencyStatus,
      `${this.options.latencyThreshold}ms`
    ]);

    // Throughput row
    const throughputStatus = metrics.performance.throughput > 20 ? 
      chalk.green('HIGH') : metrics.performance.throughput > 10 ? 
      chalk.yellow('MODERATE') : chalk.red('LOW');
    
    table.push([
      'Throughput',
      `${metrics.performance.throughput.toFixed(1)} msg/s`,
      throughputStatus,
      '10 msg/s'
    ]);

    // Error rate row
    const errorRatePercent = (metrics.errors.rate * 100).toFixed(2);
    const errorStatus = metrics.errors.rate > 0.05 ? 
      chalk.red('HIGH') : metrics.errors.rate > 0.01 ? 
      chalk.yellow('MODERATE') : chalk.green('LOW');
    
    table.push([
      'Error Rate',
      `${errorRatePercent}%`,
      errorStatus,
      '5%'
    ]);

    console.log(table.toString());

    // Recent alerts
    const recentAlerts = this.alerts.slice(-5);
    if (recentAlerts.length > 0) {
      console.log('\n' + chalk.red.bold('🚨 RECENT ALERTS:'));
      recentAlerts.forEach(alert => {
        const time = new Date(alert.timestamp).toLocaleTimeString();
        const severityColor = alert.severity === 'high' ? chalk.red : chalk.yellow;
        console.log(severityColor(`  [${time}] ${alert.message}`));
      });
    }

    // Performance summary
    const summary = this.getPerformanceSummary();
    if (summary.dataPoints > 0) {
      console.log('\n' + chalk.blue.bold('📈 PERFORMANCE SUMMARY:'));
      console.log(chalk.cyan(`  Latency: ${summary.latency.avg.toFixed(1)}ms avg (${summary.latency.min.toFixed(1)}-${summary.latency.max.toFixed(1)}ms)`));
      console.log(chalk.cyan(`  Throughput: ${summary.throughput.avg.toFixed(1)} msg/s avg (${summary.throughput.min.toFixed(1)}-${summary.throughput.max.toFixed(1)} msg/s)`));
      console.log(chalk.cyan(`  Memory: ${(summary.memory.avg * 100).toFixed(1)}% avg (${(summary.memory.min * 100).toFixed(1)}-${(summary.memory.max * 100).toFixed(1)}%)`));
    }

    console.log('\n' + chalk.gray('Press Ctrl+C to stop monitoring...'));
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics() {
    return {
      currentMetrics: this.currentMetrics,
      historicalMetrics: this.history,
      alerts: this.alerts,
      counters: this.counters,
      performanceSummary: this.getPerformanceSummary(),
      recommendations: this.generateCapacityRecommendations()
    };
  }

  /**
   * Reset monitoring state
   */
  reset() {
    this.history = [];
    this.alerts = [];
    this.counters = {
      totalConnections: 0,
      successfulConnections: 0,
      failedConnections: 0,
      totalMessages: 0,
      successfulMessages: 0,
      failedMessages: 0,
      totalErrors: 0
    };
    this.emit('monitoring-reset');
  }
}

module.exports = PerformanceMonitor;

// If run directly, start monitoring
if (require.main === module) {
  const monitor = new PerformanceMonitor();
  
  // Start monitoring
  monitor.start();
  
  // Print dashboard every 2 seconds
  setInterval(() => {
    monitor.printDashboard();
  }, 2000);
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\nShutting down performance monitor...');
    monitor.stop();
    process.exit(0);
  });
}