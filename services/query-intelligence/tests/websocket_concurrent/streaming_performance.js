const WebSocketClientFactory = require('./utils/websocket_client_factory');
const StreamingValidator = require('./utils/streaming_validator');
const PerformanceMonitor = require('./utils/performance_monitor');
const chalk = require('chalk');
const yargs = require('yargs');

/**
 * Streaming Performance Test
 * Comprehensive testing of WebSocket streaming performance and quality
 */
class StreamingPerformanceTest {
  constructor(options = {}) {
    this.options = {
      concurrentStreams: options.concurrentStreams || 25,
      streamingDuration: options.streamingDuration || 45000, // 45 seconds
      messageTypes: options.messageTypes || ['simple', 'complex', 'analysis'],
      contentQualityThreshold: options.contentQualityThreshold || 0.7,
      latencyThreshold: options.latencyThreshold || 1000, // 1 second
      throughputThreshold: options.throughputThreshold || 10, // chunks/second
      validationStrict: options.validationStrict || false,
      streamingPatterns: options.streamingPatterns || ['burst', 'steady', 'mixed'],
      ...options
    };

    this.clientFactory = new WebSocketClientFactory();
    this.streamingValidator = new StreamingValidator({
      minContentQuality: this.options.contentQualityThreshold,
      maxStreamingLatency: this.options.latencyThreshold
    });
    this.performanceMonitor = new PerformanceMonitor();
    
    this.testResults = {
      startTime: null,
      endTime: null,
      streamingSessions: [],
      performanceMetrics: {},
      validationResults: {},
      patterns: {},
      qualityAnalysis: {},
      recommendations: []
    };

    this.streamingQueries = this.generateStreamingQueries();
  }

  /**
   * Generate diverse streaming queries for testing
   */
  generateStreamingQueries() {
    return {
      simple: [
        'What is the main purpose of this service?',
        'How do I authenticate with the API?',
        'What are the available endpoints?',
        'How is error handling implemented?',
        'What databases are used?'
      ],
      complex: [
        'Explain the complete authentication flow from login to token refresh, including security measures and error handling',
        'Analyze the WebSocket implementation and describe how it handles concurrent connections, message routing, and performance optimization',
        'Provide a comprehensive overview of the caching strategy, including Redis integration, cache invalidation, and performance impact',
        'Describe the database schema design, relationships, and query optimization techniques used throughout the application',
        'Explain the error handling architecture, including circuit breakers, fallback mechanisms, and logging strategies'
      ],
      analysis: [
        'Analyze the code quality and identify potential performance bottlenecks, security vulnerabilities, and maintainability issues',
        'Review the test coverage and suggest improvements for better reliability and continuous integration practices',
        'Evaluate the API design principles and recommend enhancements for better developer experience and scalability',
        'Assess the deployment strategy and infrastructure requirements for high availability and disaster recovery',
        'Examine the monitoring and observability implementation and propose improvements for better operational visibility'
      ]
    };
  }

  /**
   * Initialize streaming performance test
   */
  async initialize() {
    console.log(chalk.blue('🌊 Initializing streaming performance test...'));
    
    // Register components
    this.performanceMonitor.registerClientFactory(this.clientFactory);
    this.performanceMonitor.registerStreamingValidator(this.streamingValidator);
    
    // Start monitoring
    this.performanceMonitor.start();
    
    // Set up event listeners
    this.setupEventListeners();
    
    console.log(chalk.green('✓ Streaming performance test initialized'));
    console.log(chalk.blue(`Target: ${this.options.concurrentStreams} concurrent streams`));
    console.log(chalk.blue(`Duration: ${this.options.streamingDuration}ms`));
    console.log(chalk.blue(`Quality threshold: ${this.options.contentQualityThreshold}`));
  }

  /**
   * Set up event listeners
   */
  setupEventListeners() {
    this.clientFactory.on('message-received', (client, message) => {
      if (message.type === 'text') {
        // Track streaming chunks for real-time analysis
        this.analyzeStreamingChunk(client, message);
      }
    });

    this.performanceMonitor.on('alert-triggered', (alert) => {
      if (alert.type === 'latency' || alert.type === 'memory') {
        console.log(chalk.red(`🚨 STREAMING ALERT: ${alert.message}`));
      }
    });
  }

  /**
   * Analyze streaming chunk in real-time
   */
  analyzeStreamingChunk(client, message) {
    const chunk = message.content || '';
    const timestamp = message.timestamp || Date.now();
    
    // Store chunk metrics for analysis
    if (!client.streamingMetrics) {
      client.streamingMetrics = {
        chunks: [],
        totalLatency: 0,
        chunkCount: 0,
        lastTimestamp: timestamp
      };
    }
    
    const metrics = client.streamingMetrics;
    
    // Calculate inter-chunk latency
    const latency = timestamp - metrics.lastTimestamp;
    metrics.totalLatency += latency;
    metrics.chunkCount++;
    metrics.lastTimestamp = timestamp;
    
    // Store chunk info
    metrics.chunks.push({
      content: chunk,
      timestamp,
      latency,
      size: Buffer.byteLength(chunk, 'utf8')
    });
    
    // Real-time quality check
    if (chunk.length > 0) {
      const qualityScore = this.calculateChunkQuality(chunk);
      if (qualityScore < this.options.contentQualityThreshold) {
        console.log(chalk.yellow(`⚠️ Low quality chunk from ${client.id}: ${qualityScore.toFixed(2)}`));
      }
    }
  }

  /**
   * Calculate chunk quality score
   */
  calculateChunkQuality(chunk) {
    let score = 0.5; // Base score
    
    // Length factor
    if (chunk.length > 5 && chunk.length < 200) {
      score += 0.2;
    }
    
    // Readability factor
    if (/[a-zA-Z]/.test(chunk) && !/^\s*$/.test(chunk)) {
      score += 0.2;
    }
    
    // Coherence factor (simple heuristic)
    if (chunk.includes(' ') && !chunk.includes('�')) {
      score += 0.1;
    }
    
    return Math.min(score, 1);
  }

  /**
   * Run streaming performance test
   */
  async runStreamingPerformanceTest() {
    console.log(chalk.blue('🚀 Running streaming performance test...'));
    
    this.testResults.startTime = Date.now();
    
    // Create connections for streaming test
    const users = this.clientFactory.createUsers(this.options.concurrentStreams);
    const clients = await this.clientFactory.createConcurrentClients(users.length, 1);
    
    console.log(chalk.cyan(`✓ Established ${clients.length} streaming connections`));
    
    const streamingPromises = [];
    
    // Start concurrent streaming sessions
    for (let i = 0; i < clients.length; i++) {
      const client = clients[i];
      const messageType = this.options.messageTypes[i % this.options.messageTypes.length];
      const queries = this.streamingQueries[messageType];
      const query = queries[i % queries.length];
      
      const streamingPromise = this.startStreamingSession(client, query, messageType, i);
      streamingPromises.push(streamingPromise);
      
      // Stagger requests to avoid overwhelming
      if (i % 5 === 0 && i > 0) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    // Wait for all streaming sessions to complete
    console.log(chalk.yellow(`⏳ Waiting for ${streamingPromises.length} streaming sessions...`));
    const sessionResults = await Promise.allSettled(streamingPromises);
    
    // Process results
    const successful = sessionResults.filter(r => r.status === 'fulfilled').map(r => r.value);
    const failed = sessionResults.filter(r => r.status === 'rejected');
    
    this.testResults.endTime = Date.now();
    this.testResults.streamingSessions = successful;
    this.testResults.performanceMetrics = this.performanceMonitor.getPerformanceMetrics();
    this.testResults.validationResults = this.streamingValidator.generateReport();
    
    console.log(chalk.green(`✓ Completed ${successful.length}/${sessionResults.length} streaming sessions`));
    
    return {
      successful,
      failed,
      total: sessionResults.length,
      duration: this.testResults.endTime - this.testResults.startTime
    };
  }

  /**
   * Start individual streaming session
   */
  async startStreamingSession(client, query, messageType, sessionIndex) {
    const startTime = Date.now();
    
    try {
      // Send query
      await this.clientFactory.sendMessage(client, {
        query,
        repository_id: 'streaming-test-repo',
        message_type: messageType
      });
      
      // Wait for streaming completion
      const streamingData = await this.clientFactory.waitForStreamingComplete(client);
      
      if (!streamingData.complete) {
        throw new Error('Streaming session incomplete or timed out');
      }
      
      // Validate streaming session
      const validation = await this.streamingValidator.validateStreamingSession(
        client,
        streamingData,
        startTime
      );
      
      // Calculate additional metrics
      const sessionMetrics = this.calculateSessionMetrics(client, streamingData, startTime);
      
      return {
        sessionIndex,
        clientId: client.id,
        query,
        messageType,
        startTime,
        endTime: Date.now(),
        duration: Date.now() - startTime,
        streamingData,
        validation,
        sessionMetrics,
        success: true
      };
      
    } catch (error) {
      return {
        sessionIndex,
        clientId: client.id,
        query,
        messageType,
        startTime,
        endTime: Date.now(),
        duration: Date.now() - startTime,
        error: error.message,
        success: false
      };
    }
  }

  /**
   * Calculate session-specific metrics
   */
  calculateSessionMetrics(client, streamingData, startTime) {
    const chunks = streamingData.chunks;
    const endTime = Date.now();
    const totalDuration = endTime - startTime;
    
    if (chunks.length === 0) {
      return {
        totalDuration,
        chunkCount: 0,
        avgChunkLatency: 0,
        throughput: 0,
        contentLength: 0,
        avgChunkSize: 0
      };
    }
    
    // Calculate latencies
    const latencies = [];
    for (let i = 1; i < chunks.length; i++) {
      const latency = chunks[i].timestamp - chunks[i-1].timestamp;
      latencies.push(latency);
    }
    
    // Calculate content metrics
    const totalContent = chunks.map(c => c.content || '').join('');
    const contentLength = totalContent.length;
    const avgChunkSize = contentLength / chunks.length;
    
    // Calculate throughput
    const throughput = chunks.length / (totalDuration / 1000);
    
    return {
      totalDuration,
      chunkCount: chunks.length,
      avgChunkLatency: latencies.length > 0 ? latencies.reduce((a, b) => a + b, 0) / latencies.length : 0,
      maxChunkLatency: latencies.length > 0 ? Math.max(...latencies) : 0,
      minChunkLatency: latencies.length > 0 ? Math.min(...latencies) : 0,
      throughput,
      contentLength,
      avgChunkSize,
      latencyVariance: this.calculateVariance(latencies)
    };
  }

  /**
   * Calculate variance
   */
  calculateVariance(values) {
    if (values.length === 0) return 0;
    
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return variance;
  }

  /**
   * Run streaming pattern tests
   */
  async runStreamingPatternTests() {
    console.log(chalk.blue('🔄 Running streaming pattern tests...'));
    
    const patternResults = {};
    
    for (const pattern of this.options.streamingPatterns) {
      console.log(chalk.cyan(`Testing ${pattern} pattern...`));
      
      const patternResult = await this.runStreamingPattern(pattern);
      patternResults[pattern] = patternResult;
      
      // Reset connections between patterns
      await this.clientFactory.closeAllClients();
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    this.testResults.patterns = patternResults;
    return patternResults;
  }

  /**
   * Run specific streaming pattern
   */
  async runStreamingPattern(pattern) {
    const startTime = Date.now();
    
    switch (pattern) {
      case 'burst':
        return await this.runBurstPattern();
      case 'steady':
        return await this.runSteadyPattern();
      case 'mixed':
        return await this.runMixedPattern();
      default:
        throw new Error(`Unknown streaming pattern: ${pattern}`);
    }
  }

  /**
   * Run burst streaming pattern
   */
  async runBurstPattern() {
    const burstSize = 15;
    const burstCount = 3;
    const burstInterval = 10000; // 10 seconds between bursts
    
    const burstResults = [];
    
    for (let burst = 0; burst < burstCount; burst++) {
      console.log(chalk.yellow(`Burst ${burst + 1}/${burstCount} - ${burstSize} concurrent streams`));
      
      // Create connections for burst
      const users = this.clientFactory.createUsers(burstSize);
      const clients = await this.clientFactory.createConcurrentClients(users.length, 1);
      
      // Start streaming sessions
      const burstPromises = clients.map((client, i) => {
        const query = this.streamingQueries.complex[i % this.streamingQueries.complex.length];
        return this.startStreamingSession(client, query, 'complex', i);
      });
      
      const burstStart = Date.now();
      const burstSessionResults = await Promise.allSettled(burstPromises);
      const burstEnd = Date.now();
      
      const successful = burstSessionResults.filter(r => r.status === 'fulfilled').length;
      
      burstResults.push({
        burst: burst + 1,
        duration: burstEnd - burstStart,
        sessions: burstSize,
        successful,
        failed: burstSize - successful
      });
      
      // Close burst connections
      await this.clientFactory.closeAllClients();
      
      // Wait before next burst
      if (burst < burstCount - 1) {
        await new Promise(resolve => setTimeout(resolve, burstInterval));
      }
    }
    
    return {
      pattern: 'burst',
      burstSize,
      burstCount,
      burstInterval,
      results: burstResults,
      totalSessions: burstSize * burstCount,
      totalSuccessful: burstResults.reduce((sum, r) => sum + r.successful, 0)
    };
  }

  /**
   * Run steady streaming pattern
   */
  async runSteadyPattern() {
    const steadyConnections = 20;
    const steadyDuration = 30000; // 30 seconds
    const messageInterval = 3000; // 3 seconds
    
    // Create steady connections
    const users = this.clientFactory.createUsers(steadyConnections);
    const clients = await this.clientFactory.createConcurrentClients(users.length, 1);
    
    console.log(chalk.yellow(`Steady pattern: ${clients.length} connections, ${steadyDuration}ms duration`));
    
    const steadyStart = Date.now();
    const steadyResults = [];
    
    // Send messages at regular intervals
    const messagesSent = [];
    let messageIndex = 0;
    
    const steadyInterval = setInterval(async () => {
      if (Date.now() - steadyStart >= steadyDuration) {
        clearInterval(steadyInterval);
        return;
      }
      
      const promises = clients.map(async (client, i) => {
        const query = this.streamingQueries.simple[messageIndex % this.streamingQueries.simple.length];
        try {
          const session = await this.startStreamingSession(client, query, 'simple', i);
          return session;
        } catch (error) {
          return { error: error.message, clientId: client.id };
        }
      });
      
      const batchResults = await Promise.allSettled(promises);
      const successful = batchResults.filter(r => r.status === 'fulfilled').length;
      
      steadyResults.push({
        messageIndex,
        timestamp: Date.now(),
        sessions: clients.length,
        successful,
        failed: clients.length - successful
      });
      
      messageIndex++;
    }, messageInterval);
    
    // Wait for steady pattern to complete
    await new Promise(resolve => setTimeout(resolve, steadyDuration + 2000));
    
    return {
      pattern: 'steady',
      connections: steadyConnections,
      duration: steadyDuration,
      messageInterval,
      results: steadyResults,
      totalMessages: steadyResults.length,
      totalSuccessful: steadyResults.reduce((sum, r) => sum + r.successful, 0)
    };
  }

  /**
   * Run mixed streaming pattern
   */
  async runMixedPattern() {
    const mixedDuration = 25000; // 25 seconds
    const baseConnections = 10;
    const spikeConnections = 5;
    
    // Create base connections
    const baseUsers = this.clientFactory.createUsers(baseConnections);
    const baseClients = await this.clientFactory.createConcurrentClients(baseUsers.length, 1);
    
    console.log(chalk.yellow(`Mixed pattern: ${baseClients.length} base connections`));
    
    const mixedStart = Date.now();
    const mixedResults = [];
    
    // Start base streaming
    const basePromises = baseClients.map((client, i) => {
      const query = this.streamingQueries.simple[i % this.streamingQueries.simple.length];
      return this.startStreamingSession(client, query, 'simple', i);
    });
    
    // Add spikes at random intervals
    const spikeTimings = [5000, 12000, 18000]; // 5s, 12s, 18s
    const spikePromises = [];
    
    for (const timing of spikeTimings) {
      setTimeout(async () => {
        console.log(chalk.yellow(`Adding spike: ${spikeConnections} connections at ${timing}ms`));
        
        const spikeUsers = this.clientFactory.createUsers(spikeConnections);
        const spikeClients = await this.clientFactory.createConcurrentClients(spikeUsers.length, 1);
        
        const spikeSessions = spikeClients.map((client, i) => {
          const query = this.streamingQueries.complex[i % this.streamingQueries.complex.length];
          return this.startStreamingSession(client, query, 'complex', i);
        });
        
        spikePromises.push(...spikeSessions);
      }, timing);
    }
    
    // Wait for all sessions to complete
    await new Promise(resolve => setTimeout(resolve, mixedDuration));
    
    const allPromises = [...basePromises, ...spikePromises];
    const allResults = await Promise.allSettled(allPromises);
    
    const successful = allResults.filter(r => r.status === 'fulfilled').length;
    
    return {
      pattern: 'mixed',
      baseConnections,
      spikeConnections,
      duration: mixedDuration,
      spikeTimings,
      totalSessions: allPromises.length,
      successful,
      failed: allPromises.length - successful
    };
  }

  /**
   * Analyze streaming quality
   */
  analyzeStreamingQuality() {
    console.log(chalk.blue('🔍 Analyzing streaming quality...'));
    
    const sessions = this.testResults.streamingSessions;
    const qualityMetrics = {
      contentQuality: [],
      streamingLatency: [],
      chunkCounts: [],
      throughput: [],
      errorRates: []
    };
    
    // Analyze each session
    sessions.forEach(session => {
      if (session.success && session.validation) {
        const validation = session.validation;
        const metrics = session.sessionMetrics;
        
        // Content quality
        if (validation.metrics.content) {
          qualityMetrics.contentQuality.push(validation.metrics.content.qualityScore);
        }
        
        // Streaming latency
        if (validation.metrics.streaming) {
          qualityMetrics.streamingLatency.push(validation.metrics.streaming.avgLatency);
        }
        
        // Chunk counts
        if (metrics.chunkCount) {
          qualityMetrics.chunkCounts.push(metrics.chunkCount);
        }
        
        // Throughput
        if (metrics.throughput) {
          qualityMetrics.throughput.push(metrics.throughput);
        }
        
        // Error rates
        const errorRate = validation.errors.length / (validation.errors.length + validation.warnings.length + 1);
        qualityMetrics.errorRates.push(errorRate);
      }
    });
    
    // Calculate statistics
    const qualityAnalysis = {
      contentQuality: this.calculateStatistics(qualityMetrics.contentQuality),
      streamingLatency: this.calculateStatistics(qualityMetrics.streamingLatency),
      chunkCounts: this.calculateStatistics(qualityMetrics.chunkCounts),
      throughput: this.calculateStatistics(qualityMetrics.throughput),
      errorRates: this.calculateStatistics(qualityMetrics.errorRates)
    };
    
    this.testResults.qualityAnalysis = qualityAnalysis;
    return qualityAnalysis;
  }

  /**
   * Calculate statistics for a dataset
   */
  calculateStatistics(data) {
    if (data.length === 0) {
      return { min: 0, max: 0, avg: 0, median: 0, p95: 0, p99: 0 };
    }
    
    const sorted = [...data].sort((a, b) => a - b);
    const sum = data.reduce((a, b) => a + b, 0);
    
    return {
      min: Math.min(...data),
      max: Math.max(...data),
      avg: sum / data.length,
      median: sorted[Math.floor(sorted.length / 2)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)]
    };
  }

  /**
   * Generate recommendations
   */
  generateRecommendations() {
    const recommendations = [];
    const analysis = this.testResults.qualityAnalysis;
    const metrics = this.testResults.performanceMetrics;
    
    // Content quality recommendations
    if (analysis.contentQuality?.avg < this.options.contentQualityThreshold) {
      recommendations.push({
        type: 'content-quality',
        priority: 'high',
        issue: `Low content quality: ${analysis.contentQuality.avg.toFixed(2)}`,
        recommendation: 'Improve content generation algorithms and validation logic',
        impact: 'User experience and response usefulness'
      });
    }
    
    // Latency recommendations
    if (analysis.streamingLatency?.avg > this.options.latencyThreshold) {
      recommendations.push({
        type: 'latency',
        priority: 'medium',
        issue: `High streaming latency: ${analysis.streamingLatency.avg.toFixed(1)}ms`,
        recommendation: 'Optimize streaming buffer sizes and processing pipeline',
        impact: 'Real-time user experience'
      });
    }
    
    // Throughput recommendations
    if (analysis.throughput?.avg < this.options.throughputThreshold) {
      recommendations.push({
        type: 'throughput',
        priority: 'medium',
        issue: `Low throughput: ${analysis.throughput?.avg.toFixed(1)} chunks/s`,
        recommendation: 'Increase processing capacity or optimize chunk generation',
        impact: 'System scalability and performance'
      });
    }
    
    // Error rate recommendations
    if (analysis.errorRates?.avg > 0.05) {
      recommendations.push({
        type: 'error-rate',
        priority: 'high',
        issue: `High error rate: ${(analysis.errorRates.avg * 100).toFixed(1)}%`,
        recommendation: 'Improve error handling and input validation',
        impact: 'System reliability and user trust'
      });
    }
    
    this.testResults.recommendations = recommendations;
    return recommendations;
  }

  /**
   * Generate comprehensive report
   */
  generateStreamingReport() {
    const analysis = this.analyzeStreamingQuality();
    const recommendations = this.generateRecommendations();
    
    return {
      configuration: this.options,
      executionSummary: {
        startTime: this.testResults.startTime,
        endTime: this.testResults.endTime,
        duration: this.testResults.endTime - this.testResults.startTime,
        totalSessions: this.testResults.streamingSessions.length,
        successfulSessions: this.testResults.streamingSessions.filter(s => s.success).length,
        failedSessions: this.testResults.streamingSessions.filter(s => !s.success).length
      },
      qualityAnalysis: analysis,
      patternResults: this.testResults.patterns,
      performanceMetrics: this.testResults.performanceMetrics,
      validationResults: this.testResults.validationResults,
      recommendations: recommendations
    };
  }

  /**
   * Print streaming test results
   */
  printStreamingResults() {
    const report = this.generateStreamingReport();
    
    console.log(chalk.blue('\n' + '='.repeat(80)));
    console.log(chalk.blue.bold('           STREAMING PERFORMANCE TEST RESULTS'));
    console.log(chalk.blue('='.repeat(80)));
    
    // Configuration
    console.log(chalk.yellow.bold('\n📋 TEST CONFIGURATION:'));
    console.log(chalk.cyan(`  Concurrent Streams: ${report.configuration.concurrentStreams}`));
    console.log(chalk.cyan(`  Duration: ${report.configuration.streamingDuration}ms`));
    console.log(chalk.cyan(`  Quality Threshold: ${report.configuration.contentQualityThreshold}`));
    console.log(chalk.cyan(`  Latency Threshold: ${report.configuration.latencyThreshold}ms`));
    
    // Execution Summary
    console.log(chalk.yellow.bold('\n🚀 EXECUTION SUMMARY:'));
    console.log(chalk.green(`  ✓ Total Sessions: ${report.executionSummary.totalSessions}`));
    console.log(chalk.green(`  ✓ Successful: ${report.executionSummary.successfulSessions}`));
    console.log(chalk.green(`  ✓ Failed: ${report.executionSummary.failedSessions}`));
    console.log(chalk.green(`  ✓ Success Rate: ${((report.executionSummary.successfulSessions / report.executionSummary.totalSessions) * 100).toFixed(1)}%`));
    
    // Quality Analysis
    console.log(chalk.yellow.bold('\n📊 QUALITY ANALYSIS:'));
    if (report.qualityAnalysis.contentQuality) {
      console.log(chalk.cyan(`  Content Quality: ${report.qualityAnalysis.contentQuality.avg.toFixed(2)} avg (${report.qualityAnalysis.contentQuality.min.toFixed(2)}-${report.qualityAnalysis.contentQuality.max.toFixed(2)})`));
    }
    if (report.qualityAnalysis.streamingLatency) {
      console.log(chalk.cyan(`  Streaming Latency: ${report.qualityAnalysis.streamingLatency.avg.toFixed(1)}ms avg (${report.qualityAnalysis.streamingLatency.min.toFixed(1)}-${report.qualityAnalysis.streamingLatency.max.toFixed(1)}ms)`));
    }
    if (report.qualityAnalysis.throughput) {
      console.log(chalk.cyan(`  Throughput: ${report.qualityAnalysis.throughput.avg.toFixed(1)} chunks/s avg (${report.qualityAnalysis.throughput.min.toFixed(1)}-${report.qualityAnalysis.throughput.max.toFixed(1)})`));
    }
    
    // Pattern Results
    if (Object.keys(report.patternResults).length > 0) {
      console.log(chalk.yellow.bold('\n🔄 PATTERN RESULTS:'));
      Object.entries(report.patternResults).forEach(([pattern, results]) => {
        console.log(chalk.cyan(`  ${pattern.toUpperCase()}:`));
        console.log(chalk.green(`    ✓ Total Sessions: ${results.totalSessions || 'N/A'}`));
        console.log(chalk.green(`    ✓ Successful: ${results.totalSuccessful || 'N/A'}`));
      });
    }
    
    // Recommendations
    if (report.recommendations.length > 0) {
      console.log(chalk.yellow.bold('\n💡 RECOMMENDATIONS:'));
      report.recommendations.forEach(rec => {
        const priority = rec.priority === 'high' ? chalk.red : chalk.yellow;
        console.log(priority(`  ${rec.type.toUpperCase()}: ${rec.issue}`));
        console.log(chalk.gray(`    → ${rec.recommendation}`));
        console.log(chalk.gray(`    Impact: ${rec.impact}`));
      });
    }
    
    console.log(chalk.blue('\n' + '='.repeat(80)));
  }

  /**
   * Run complete streaming test suite
   */
  async runStreamingTestSuite() {
    try {
      // Initialize
      await this.initialize();
      
      // Run main streaming performance test
      const mainResults = await this.runStreamingPerformanceTest();
      
      // Run streaming pattern tests
      const patternResults = await this.runStreamingPatternTests();
      
      // Print results
      this.printStreamingResults();
      
      // Generate report
      const report = this.generateStreamingReport();
      
      return {
        mainResults,
        patternResults,
        report
      };
      
    } catch (error) {
      console.error(chalk.red('Streaming test suite failed:'), error);
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    console.log(chalk.blue('🧹 Cleaning up streaming test resources...'));
    
    await this.clientFactory.closeAllClients();
    this.performanceMonitor.stop();
    
    console.log(chalk.green('✓ Streaming test cleanup completed'));
  }
}

// CLI interface
const argv = yargs(process.argv.slice(2))
  .option('concurrent-streams', {
    alias: 'c',
    type: 'number',
    default: 25,
    description: 'Number of concurrent streaming sessions'
  })
  .option('duration', {
    alias: 'd',
    type: 'number',
    default: 45000,
    description: 'Test duration in milliseconds'
  })
  .option('quality-threshold', {
    alias: 'q',
    type: 'number',
    default: 0.7,
    description: 'Content quality threshold (0-1)'
  })
  .option('latency-threshold', {
    alias: 'l',
    type: 'number',
    default: 1000,
    description: 'Latency threshold in milliseconds'
  })
  .option('patterns', {
    alias: 'p',
    type: 'array',
    default: ['burst', 'steady', 'mixed'],
    description: 'Streaming patterns to test'
  })
  .help()
  .argv;

// Run if called directly
if (require.main === module) {
  const streamingTest = new StreamingPerformanceTest({
    concurrentStreams: argv.concurrentStreams,
    streamingDuration: argv.duration,
    contentQualityThreshold: argv.qualityThreshold,
    latencyThreshold: argv.latencyThreshold,
    streamingPatterns: argv.patterns
  });
  
  streamingTest.runStreamingTestSuite()
    .then(results => {
      console.log(chalk.green('\n✅ Streaming test completed successfully!'));
    })
    .catch(error => {
      console.error(chalk.red('\n❌ Streaming test failed:'), error);
      process.exit(1);
    });
}

module.exports = StreamingPerformanceTest;