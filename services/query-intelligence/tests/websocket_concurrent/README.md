# WebSocket Concurrent Testing Framework

A comprehensive testing framework for validating WebSocket concurrent connections, streaming performance, and production capacity for the Query Intelligence service.

## Overview

This framework provides production-scale testing for WebSocket connections with:
- **100+ concurrent WebSocket connections** with real browser validation
- **Real-time streaming performance** validation and content quality checks
- **Connection management stress testing** with limit enforcement
- **Memory usage monitoring** and performance tracking
- **Production capacity recommendations** and deployment guidelines

## Framework Components

### Core Components

#### 1. WebSocket Client Factory (`utils/websocket_client_factory.js`)
- Creates and manages multiple WebSocket connections with authentication
- Supports concurrent connection testing with JWT tokens
- Handles connection lifecycle, message sending, and streaming validation
- Provides detailed statistics and performance metrics

#### 2. Streaming Validator (`utils/streaming_validator.js`)
- Validates WebSocket streaming quality and performance
- Checks content quality, latency, and message ordering
- Provides real-time validation with configurable thresholds
- Generates detailed validation reports and recommendations

#### 3. Performance Monitor (`utils/performance_monitor.js`)
- Real-time monitoring of system resources and connection health
- Tracks memory usage, connection counts, and performance metrics
- Provides alerts for threshold breaches and performance issues
- Generates capacity recommendations based on monitoring data

### Test Suites

#### 1. Puppeteer Concurrent Test (`puppeteer_concurrent_test.js`)
Browser-based concurrent WebSocket testing with:
- Real browser WebSocket connections (Chrome/Chromium)
- Concurrent streaming sessions with content validation
- Connection limit testing and stress scenarios
- Performance monitoring and resource tracking

**Usage:**
```bash
npm run test:concurrent
# or
node puppeteer_concurrent_test.js
```

#### 2. WebSocket Load Testing (`websocket_load_testing.js`)
Comprehensive load testing framework with:
- Ramp-up, sustain, and ramp-down phases
- Multiple load patterns (constant, spike, gradual)
- Connection and message throughput testing
- Performance metrics and capacity analysis

**Usage:**
```bash
npm run test:load
# or
node websocket_load_testing.js --max-connections 100 --patterns constant,spike
```

#### 3. Streaming Performance Test (`streaming_performance.js`)
Streaming-focused performance validation with:
- Content quality validation and scoring
- Latency measurement and analysis
- Multiple streaming patterns (burst, steady, mixed)
- Quality recommendations and optimization suggestions

**Usage:**
```bash
npm run test:streaming
# or
node streaming_performance.js --concurrent-streams 25 --quality-threshold 0.7
```

#### 4. Connection Limits Test (`connection_limits_test.js`)
Connection management and limits testing with:
- Connection limit discovery and enforcement
- Per-user connection limits validation
- Stress patterns and recovery testing
- Connection stability and reliability analysis

**Usage:**
```bash
npm run test:limits
# or
node connection_limits_test.js --max-attempts 150 --user-limit 10
```

#### 5. Production Capacity Report (`production_capacity_report.js`)
Comprehensive production readiness analysis with:
- Complete test suite execution
- Capacity analysis and bottleneck identification
- Scaling recommendations and deployment guidelines
- Risk assessment and mitigation strategies

**Usage:**
```bash
node production_capacity_report.js
```

## Installation

```bash
# Install dependencies
npm install

# Install Puppeteer (for browser testing)
npm install puppeteer

# Verify installation
npm test
```

## Configuration

### Environment Variables
```bash
# Test configuration
TEST_BASE_URL=http://localhost:8000
TEST_WS_URL=ws://localhost:8000/api/v1/ws/query
JWT_SECRET=your-jwt-secret-key
MAX_CONCURRENT_CONNECTIONS=100
CONNECTION_TIMEOUT=30000
MESSAGE_TIMEOUT=10000
STREAMING_TIMEOUT=30000
PERFORMANCE_THRESHOLD_MS=2000
MEMORY_LIMIT_MB=4096
```

### Test Configuration
```javascript
// Global test configuration in jest.setup.js
global.TEST_CONFIG = {
  BASE_URL: 'http://localhost:8000',
  WS_URL: 'ws://localhost:8000/api/v1/ws/query',
  MAX_CONCURRENT_CONNECTIONS: 100,
  CONNECTION_TIMEOUT: 30000,
  // ... other settings
};
```

## Usage Examples

### Basic Concurrent Testing
```javascript
const WebSocketClientFactory = require('./utils/websocket_client_factory');
const factory = new WebSocketClientFactory();

// Create 50 concurrent connections
const users = factory.createUsers(50);
const clients = await factory.createConcurrentClients(users.length, 1);

// Send messages concurrently
await factory.broadcastMessage({
  query: 'How does authentication work?',
  repository_id: 'test-repo'
});

// Monitor performance
const metrics = factory.getPerformanceMetrics();
console.log('Performance:', metrics);
```

### Streaming Validation
```javascript
const StreamingValidator = require('./utils/streaming_validator');
const validator = new StreamingValidator({
  minContentQuality: 0.7,
  maxStreamingLatency: 1000
});

// Validate streaming session
const validation = await validator.validateStreamingSession(
  client, 
  streamingData, 
  startTime
);

console.log('Validation results:', validation);
```

### Performance Monitoring
```javascript
const PerformanceMonitor = require('./utils/performance_monitor');
const monitor = new PerformanceMonitor();

// Register factory and start monitoring
monitor.registerClientFactory(factory);
monitor.start();

// Real-time dashboard
setInterval(() => {
  monitor.printDashboard();
}, 2000);
```

## Test Scenarios

### 1. Connection Scaling Test
Tests connection scaling from 1 to 100+ concurrent connections:
- Gradual connection increase
- Peak connection validation
- Connection stability monitoring
- Resource usage tracking

### 2. Streaming Performance Test
Validates streaming quality under concurrent load:
- Content quality scoring
- Latency measurement
- Message ordering validation
- Throughput analysis

### 3. Connection Limits Test
Tests connection limit enforcement:
- Maximum connection discovery
- Per-user limit validation
- Stress pattern testing
- Recovery scenarios

### 4. Production Capacity Analysis
Comprehensive production readiness assessment:
- All test scenarios execution
- Capacity analysis and bottlenecks
- Scaling recommendations
- Deployment guidelines

## Performance Validation Criteria

### Connection Performance
- **Concurrent Connections**: 100+ stable connections
- **Connection Latency**: <2s connection establishment
- **Success Rate**: 99%+ connection success rate
- **Stability**: 99%+ connection stability

### Streaming Performance
- **Streaming Latency**: <500ms message delivery
- **Message Throughput**: 10+ messages/second per connection
- **Content Quality**: >0.7 quality score
- **Ordering**: <1% out-of-order messages

### System Performance
- **Memory Usage**: <4GB for 100 concurrent connections
- **CPU Usage**: <70% average utilization
- **Error Rate**: <1% overall error rate
- **Recovery Time**: <30s for connection recovery

## Output and Reporting

### Test Reports
Each test generates comprehensive reports:
- **JSON Reports**: Machine-readable test results
- **Markdown Reports**: Human-readable analysis
- **Summary Reports**: Executive summaries
- **Performance Metrics**: Detailed performance data

### Capacity Reports
Production capacity analysis includes:
- **Executive Summary**: Readiness assessment
- **Capacity Analysis**: Current performance limits
- **Bottleneck Identification**: Performance constraints
- **Scaling Recommendations**: Improvement suggestions
- **Deployment Guidelines**: Production deployment plan
- **Risk Assessment**: Potential issues and mitigation

### Report Locations
```
tests/websocket_concurrent/reports/
├── production-capacity-report-2025-07-14.json
├── production-capacity-report-2025-07-14.md
├── capacity-summary-2025-07-14.txt
└── performance-metrics-2025-07-14.json
```

## CI/CD Integration

### GitHub Actions Example
```yaml
name: WebSocket Concurrent Testing

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  websocket-testing:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm install
      - name: Run concurrent tests
        run: npm run test:concurrent
      - name: Run load tests
        run: npm run test:load
      - name: Upload test reports
        uses: actions/upload-artifact@v3
        with:
          name: test-reports
          path: tests/websocket_concurrent/reports/
```

## Troubleshooting

### Common Issues

#### 1. Connection Timeout Errors
```bash
# Increase connection timeout
export CONNECTION_TIMEOUT=60000

# Check service availability
curl -f http://localhost:8000/health
```

#### 2. Memory Issues
```bash
# Increase Node.js memory
export NODE_OPTIONS="--max-old-space-size=8192"

# Monitor memory usage
node --inspect performance_monitor.js
```

#### 3. Puppeteer Issues
```bash
# Install Chromium dependencies
sudo apt-get install -y chromium-browser

# Use alternative browser
export PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
```

#### 4. WebSocket Connection Failures
```bash
# Check JWT token validity
node -e "console.log(require('jsonwebtoken').verify(token, secret))"

# Verify WebSocket endpoint
wscat -c ws://localhost:8000/api/v1/ws/query
```

### Debug Mode
```bash
# Enable debug logging
export DEBUG=websocket:*

# Verbose test output
npm run test:concurrent -- --verbose

# Performance monitoring
node utils/performance_monitor.js
```

## Performance Optimization

### Connection Optimization
- Use connection pooling for better resource utilization
- Implement exponential backoff for connection retries
- Monitor connection lifecycle and cleanup

### Streaming Optimization
- Optimize message buffer sizes
- Implement streaming compression
- Use efficient JSON serialization

### Memory Optimization
- Implement message history limits
- Use memory-efficient data structures
- Monitor and prevent memory leaks

## Production Deployment

### Readiness Checklist
- [ ] All tests passing with >99% success rate
- [ ] Connection limits validated and enforced
- [ ] Streaming performance meets requirements
- [ ] Memory usage within acceptable limits
- [ ] Error rates below 1%
- [ ] Monitoring and alerting configured
- [ ] Deployment plan reviewed and approved

### Capacity Planning
Based on test results, plan for:
- **Initial Capacity**: 70% of tested capacity
- **Scaling Strategy**: Horizontal scaling with load balancing
- **Monitoring**: Comprehensive metrics and alerting
- **Rollback Plan**: Automated rollback on performance degradation

### Deployment Phases
1. **Phase 1**: Limited production deployment (1-2 weeks)
2. **Phase 2**: Gradual capacity increase (2-4 weeks)
3. **Phase 3**: Full production scale (4-8 weeks)

## Contributing

### Development Setup
```bash
# Clone repository
git clone <repository-url>
cd tests/websocket_concurrent

# Install dependencies
npm install

# Run tests
npm test

# Run specific test suite
npm run test:concurrent
```

### Code Style
- Use ESLint for code formatting
- Follow Node.js best practices
- Include comprehensive error handling
- Add detailed comments and documentation

### Testing Guidelines
- Write tests for all new features
- Maintain >90% test coverage
- Include performance benchmarks
- Document test scenarios and expected results

## License

This testing framework is part of the Query Intelligence service and follows the same licensing terms.

## Support

For issues and questions:
- Check the troubleshooting section
- Review test logs and reports
- Contact the development team
- Submit GitHub issues for bugs

---

*WebSocket Concurrent Testing Framework - Production Scale Validation*