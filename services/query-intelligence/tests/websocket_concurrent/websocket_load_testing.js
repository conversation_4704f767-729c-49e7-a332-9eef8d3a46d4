const WebSocketClientFactory = require('./utils/websocket_client_factory');
const StreamingValidator = require('./utils/streaming_validator');
const PerformanceMonitor = require('./utils/performance_monitor');
const chalk = require('chalk');
const yargs = require('yargs');

/**
 * WebSocket Load Testing Framework
 * Comprehensive load testing for WebSocket connections and streaming
 */
class WebSocketLoadTester {
  constructor(options = {}) {
    this.options = {
      maxConnections: options.maxConnections || 100,
      rampUpDuration: options.rampUpDuration || 30000, // 30 seconds
      sustainDuration: options.sustainDuration || 60000, // 60 seconds
      rampDownDuration: options.rampDownDuration || 15000, // 15 seconds
      messageFrequency: options.messageFrequency || 5000, // 5 seconds
      connectionBatches: options.connectionBatches || 10,
      batchDelay: options.batchDelay || 3000, // 3 seconds
      loadPatterns: options.loadPatterns || ['constant', 'spike', 'gradual'],
      testScenarios: options.testScenarios || ['connection', 'messaging', 'streaming'],
      targetLatency: options.targetLatency || 2000, // 2 seconds
      targetThroughput: options.targetThroughput || 10, // messages/second
      ...options
    };

    this.clientFactory = new WebSocketClientFactory();
    this.streamingValidator = new StreamingValidator();
    this.performanceMonitor = new PerformanceMonitor();
    
    this.loadTestResults = {
      startTime: null,
      endTime: null,
      phases: {
        rampUp: { duration: 0, results: {} },
        sustain: { duration: 0, results: {} },
        rampDown: { duration: 0, results: {} }
      },
      patterns: {},
      scenarios: {},
      overallMetrics: {}
    };

    this.activeConnections = new Set();
    this.testMessages = this.generateTestMessages();
  }

  /**
   * Generate test messages for load testing
   */
  generateTestMessages() {
    return [
      {
        type: 'simple',
        query: 'What is the main function of this service?',
        repository_id: 'load-test-repo'
      },
      {
        type: 'complex',
        query: 'Explain the authentication flow and security measures implemented in the user management system',
        repository_id: 'load-test-repo'
      },
      {
        type: 'search',
        query: 'Find all database query patterns and explain their performance characteristics',
        repository_id: 'load-test-repo'
      },
      {
        type: 'analysis',
        query: 'Analyze the error handling patterns and suggest improvements for better resilience',
        repository_id: 'load-test-repo'
      },
      {
        type: 'documentation',
        query: 'Generate comprehensive documentation for the API endpoints with examples',
        repository_id: 'load-test-repo'
      }
    ];
  }

  /**
   * Initialize load testing environment
   */
  async initialize() {
    console.log(chalk.blue('🔧 Initializing WebSocket load testing framework...'));
    
    // Register components
    this.performanceMonitor.registerClientFactory(this.clientFactory);
    this.performanceMonitor.registerStreamingValidator(this.streamingValidator);
    
    // Start monitoring
    this.performanceMonitor.start();
    
    // Set up event listeners
    this.setupEventListeners();
    
    console.log(chalk.green('✓ Load testing framework initialized'));
    console.log(chalk.blue(`Target: ${this.options.maxConnections} connections`));
    console.log(chalk.blue(`Phases: ${this.options.rampUpDuration}ms ramp-up, ${this.options.sustainDuration}ms sustain, ${this.options.rampDownDuration}ms ramp-down`));
  }

  /**
   * Set up event listeners for monitoring
   */
  setupEventListeners() {
    this.clientFactory.on('client-connected', (client) => {
      this.activeConnections.add(client.id);
      console.log(chalk.green(`🔗 Client ${client.id} connected (${this.activeConnections.size} active)`));
    });

    this.clientFactory.on('client-disconnected', (client) => {
      this.activeConnections.delete(client.id);
      console.log(chalk.yellow(`🔌 Client ${client.id} disconnected (${this.activeConnections.size} active)`));
    });

    this.clientFactory.on('client-error', (client, error) => {
      console.log(chalk.red(`❌ Client ${client.id} error: ${error.message}`));
    });

    this.performanceMonitor.on('alert-triggered', (alert) => {
      console.log(chalk.red(`🚨 ${alert.type.toUpperCase()} ALERT: ${alert.message}`));
    });
  }

  /**
   * Run connection load test
   */
  async runConnectionLoadTest() {
    console.log(chalk.blue('🔗 Running connection load test...'));
    
    const phaseResults = {
      rampUp: await this.runRampUpPhase(),
      sustain: await this.runSustainPhase(),
      rampDown: await this.runRampDownPhase()
    };

    return {
      phases: phaseResults,
      finalConnectionCount: this.activeConnections.size,
      peakConnectionCount: Math.max(
        phaseResults.rampUp.peakConnections,
        phaseResults.sustain.peakConnections,
        phaseResults.rampDown.peakConnections
      ),
      overallMetrics: this.performanceMonitor.getPerformanceMetrics()
    };
  }

  /**
   * Run ramp-up phase
   */
  async runRampUpPhase() {
    console.log(chalk.blue('📈 Starting ramp-up phase...'));
    const startTime = Date.now();
    
    const connectionsPerBatch = Math.ceil(this.options.maxConnections / this.options.connectionBatches);
    const batchInterval = this.options.rampUpDuration / this.options.connectionBatches;
    
    let createdConnections = 0;
    let successfulConnections = 0;
    let failedConnections = 0;
    let peakConnections = 0;

    for (let batch = 0; batch < this.options.connectionBatches; batch++) {
      const batchStart = Date.now();
      
      try {
        // Create connections for this batch
        const users = this.clientFactory.createUsers(connectionsPerBatch);
        const batchPromises = users.map(user => 
          this.clientFactory.createClient(user)
            .then(client => {
              successfulConnections++;
              return client;
            })
            .catch(error => {
              failedConnections++;
              console.error(chalk.red(`Batch ${batch} connection failed:`, error.message));
              return null;
            })
        );

        await Promise.all(batchPromises);
        createdConnections += connectionsPerBatch;
        
        const currentConnections = this.activeConnections.size;
        if (currentConnections > peakConnections) {
          peakConnections = currentConnections;
        }

        console.log(chalk.cyan(`Batch ${batch + 1}/${this.options.connectionBatches}: ${currentConnections} active connections`));
        
        // Wait before next batch
        if (batch < this.options.connectionBatches - 1) {
          const nextBatchTime = batchInterval - (Date.now() - batchStart);
          if (nextBatchTime > 0) {
            await new Promise(resolve => setTimeout(resolve, nextBatchTime));
          }
        }
        
      } catch (error) {
        console.error(chalk.red(`Batch ${batch} failed:`, error));
        failedConnections += connectionsPerBatch;
      }
    }

    const duration = Date.now() - startTime;
    console.log(chalk.green(`✓ Ramp-up completed in ${duration}ms`));

    return {
      duration,
      createdConnections,
      successfulConnections,
      failedConnections,
      peakConnections,
      connectionRate: successfulConnections / (duration / 1000),
      successRate: createdConnections > 0 ? successfulConnections / createdConnections : 0
    };
  }

  /**
   * Run sustain phase
   */
  async runSustainPhase() {
    console.log(chalk.blue('⚡ Starting sustain phase...'));
    const startTime = Date.now();
    
    const messageInterval = this.options.messageFrequency;
    const endTime = startTime + this.options.sustainDuration;
    
    let messagesSent = 0;
    let messagesReceived = 0;
    let streamingSessions = 0;
    let sustainErrors = 0;
    let peakConnections = this.activeConnections.size;

    // Start message sending loop
    const messagingLoop = async () => {
      while (Date.now() < endTime) {
        const loopStart = Date.now();
        
        try {
          // Send messages to all connected clients
          const clients = this.clientFactory.getConnectedClients();
          const message = this.testMessages[messagesSent % this.testMessages.length];
          
          if (clients.length > 0) {
            const sendPromises = clients.map(async (client) => {
              try {
                await this.clientFactory.sendMessage(client, message);
                messagesSent++;
                
                // Start streaming validation for some messages
                if (messagesSent % 5 === 0) {
                  this.startStreamingValidation(client, message);
                  streamingSessions++;
                }
                
                return true;
              } catch (error) {
                sustainErrors++;
                return false;
              }
            });
            
            await Promise.all(sendPromises);
          }
          
          // Update peak connections
          const currentConnections = this.activeConnections.size;
          if (currentConnections > peakConnections) {
            peakConnections = currentConnections;
          }
          
          // Wait for next message cycle
          const nextMessageTime = messageInterval - (Date.now() - loopStart);
          if (nextMessageTime > 0) {
            await new Promise(resolve => setTimeout(resolve, nextMessageTime));
          }
          
        } catch (error) {
          sustainErrors++;
          console.error(chalk.red('Sustain phase error:'), error);
        }
      }
    };

    // Run messaging loop
    await messagingLoop();
    
    const duration = Date.now() - startTime;
    messagesReceived = this.clientFactory.getMessageStats().received;
    
    console.log(chalk.green(`✓ Sustain phase completed in ${duration}ms`));

    return {
      duration,
      messagesSent,
      messagesReceived,
      streamingSessions,
      sustainErrors,
      peakConnections,
      throughput: messagesSent / (duration / 1000),
      responseRate: messagesSent > 0 ? messagesReceived / messagesSent : 0
    };
  }

  /**
   * Run ramp-down phase
   */
  async runRampDownPhase() {
    console.log(chalk.blue('📉 Starting ramp-down phase...'));
    const startTime = Date.now();
    
    const initialConnections = this.activeConnections.size;
    const connectionsToClose = Math.ceil(initialConnections * 0.8); // Close 80% of connections
    const closeInterval = this.options.rampDownDuration / connectionsToClose;
    
    let closedConnections = 0;
    let closeErrors = 0;
    let peakConnections = initialConnections;

    // Gradually close connections
    const clients = this.clientFactory.getAllClients();
    const clientsToClose = clients.slice(0, connectionsToClose);
    
    for (let i = 0; i < clientsToClose.length; i++) {
      const closeStart = Date.now();
      
      try {
        await this.clientFactory.closeClient(clientsToClose[i].id);
        closedConnections++;
        
        console.log(chalk.yellow(`Closed connection ${i + 1}/${connectionsToClose} (${this.activeConnections.size} remaining)`));
        
        // Wait before closing next connection
        if (i < clientsToClose.length - 1) {
          const nextCloseTime = closeInterval - (Date.now() - closeStart);
          if (nextCloseTime > 0) {
            await new Promise(resolve => setTimeout(resolve, nextCloseTime));
          }
        }
        
      } catch (error) {
        closeErrors++;
        console.error(chalk.red(`Failed to close connection ${i}:`, error));
      }
    }

    const duration = Date.now() - startTime;
    console.log(chalk.green(`✓ Ramp-down completed in ${duration}ms`));

    return {
      duration,
      initialConnections,
      closedConnections,
      closeErrors,
      remainingConnections: this.activeConnections.size,
      peakConnections,
      closeRate: closedConnections / (duration / 1000)
    };
  }

  /**
   * Start streaming validation for a client
   */
  async startStreamingValidation(client, message) {
    try {
      const startTime = Date.now();
      
      // Wait for streaming completion
      const streamingData = await this.clientFactory.waitForStreamingComplete(client);
      
      // Validate streaming session
      const validation = await this.streamingValidator.validateStreamingSession(
        client,
        streamingData,
        startTime
      );
      
      if (!validation.passed) {
        console.log(chalk.yellow(`⚠️ Streaming validation failed for client ${client.id}`));
      }
      
      return validation;
      
    } catch (error) {
      console.error(chalk.red(`Streaming validation error for client ${client.id}:`, error));
      return null;
    }
  }

  /**
   * Run load pattern test
   */
  async runLoadPatternTest(pattern) {
    console.log(chalk.blue(`🔄 Running ${pattern} load pattern test...`));
    
    const startTime = Date.now();
    let patternResults = {};

    switch (pattern) {
      case 'constant':
        patternResults = await this.runConstantLoadPattern();
        break;
      case 'spike':
        patternResults = await this.runSpikeLoadPattern();
        break;
      case 'gradual':
        patternResults = await this.runGradualLoadPattern();
        break;
      default:
        throw new Error(`Unknown load pattern: ${pattern}`);
    }

    const duration = Date.now() - startTime;
    
    return {
      pattern,
      duration,
      results: patternResults,
      metrics: this.performanceMonitor.getPerformanceMetrics()
    };
  }

  /**
   * Run constant load pattern
   */
  async runConstantLoadPattern() {
    const targetConnections = Math.floor(this.options.maxConnections * 0.7);
    const sustainDuration = 45000; // 45 seconds
    
    // Establish connections quickly
    const users = this.clientFactory.createUsers(targetConnections);
    const clients = await this.clientFactory.createConcurrentClients(users.length, 1);
    
    const startTime = Date.now();
    let messagesSent = 0;
    
    // Send messages at constant rate
    const messageInterval = setInterval(async () => {
      const message = this.testMessages[messagesSent % this.testMessages.length];
      const result = await this.clientFactory.broadcastMessage(message);
      messagesSent += result.successful;
    }, 2000);
    
    // Wait for duration
    await new Promise(resolve => setTimeout(resolve, sustainDuration));
    clearInterval(messageInterval);
    
    return {
      targetConnections,
      actualConnections: clients.length,
      duration: sustainDuration,
      messagesSent,
      avgConnectionsPerSecond: clients.length / (sustainDuration / 1000),
      avgMessagesPerSecond: messagesSent / (sustainDuration / 1000)
    };
  }

  /**
   * Run spike load pattern
   */
  async runSpikeLoadPattern() {
    const baseConnections = Math.floor(this.options.maxConnections * 0.3);
    const spikeConnections = Math.floor(this.options.maxConnections * 0.9);
    const spikeDuration = 10000; // 10 seconds
    
    // Establish base load
    const baseUsers = this.clientFactory.createUsers(baseConnections);
    const baseClients = await this.clientFactory.createConcurrentClients(baseUsers.length, 1);
    
    console.log(chalk.cyan(`Base load established: ${baseClients.length} connections`));
    
    // Create spike
    const spikeUsers = this.clientFactory.createUsers(spikeConnections - baseConnections);
    const spikeStart = Date.now();
    const spikeClients = await this.clientFactory.createConcurrentClients(spikeUsers.length, 1);
    
    console.log(chalk.yellow(`Spike created: ${spikeClients.length} additional connections`));
    
    // Sustain spike
    await new Promise(resolve => setTimeout(resolve, spikeDuration));
    
    // Remove spike connections
    for (const client of spikeClients) {
      await this.clientFactory.closeClient(client.id);
    }
    
    const spikeEnd = Date.now();
    
    console.log(chalk.green(`Spike completed: ${this.activeConnections.size} remaining connections`));
    
    return {
      baseConnections: baseClients.length,
      spikeConnections: spikeClients.length,
      peakConnections: baseClients.length + spikeClients.length,
      spikeDuration: spikeEnd - spikeStart,
      remainingConnections: this.activeConnections.size
    };
  }

  /**
   * Run gradual load pattern
   */
  async runGradualLoadPattern() {
    const maxConnections = this.options.maxConnections;
    const steps = 5;
    const stepDuration = 15000; // 15 seconds per step
    const connectionsPerStep = Math.floor(maxConnections / steps);
    
    const stepResults = [];
    
    for (let step = 0; step < steps; step++) {
      const stepStart = Date.now();
      const targetConnections = (step + 1) * connectionsPerStep;
      
      // Add connections for this step
      const users = this.clientFactory.createUsers(connectionsPerStep);
      const clients = await this.clientFactory.createConcurrentClients(users.length, 1);
      
      console.log(chalk.cyan(`Step ${step + 1}/${steps}: ${targetConnections} target connections, ${this.activeConnections.size} actual`));
      
      // Sustain step
      await new Promise(resolve => setTimeout(resolve, stepDuration));
      
      const stepEnd = Date.now();
      stepResults.push({
        step: step + 1,
        targetConnections,
        actualConnections: this.activeConnections.size,
        duration: stepEnd - stepStart,
        newConnections: clients.length
      });
    }
    
    return {
      steps,
      stepDuration,
      stepResults,
      finalConnections: this.activeConnections.size,
      totalDuration: steps * stepDuration
    };
  }

  /**
   * Run comprehensive load test suite
   */
  async runLoadTestSuite() {
    console.log(chalk.blue('🧪 Running comprehensive load test suite...'));
    
    this.loadTestResults.startTime = Date.now();
    
    try {
      // 1. Connection load test
      console.log(chalk.blue('\n1️⃣ Connection Load Test'));
      const connectionResults = await this.runConnectionLoadTest();
      this.loadTestResults.scenarios.connection = connectionResults;
      
      // 2. Load pattern tests
      console.log(chalk.blue('\n2️⃣ Load Pattern Tests'));
      for (const pattern of this.options.loadPatterns) {
        const patternResults = await this.runLoadPatternTest(pattern);
        this.loadTestResults.patterns[pattern] = patternResults;
        
        // Reset connections between patterns
        await this.clientFactory.closeAllClients();
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
      
      // 3. Streaming validation under load
      console.log(chalk.blue('\n3️⃣ Streaming Validation Under Load'));
      const streamingResults = await this.runStreamingLoadTest();
      this.loadTestResults.scenarios.streaming = streamingResults;
      
      this.loadTestResults.endTime = Date.now();
      this.loadTestResults.overallMetrics = this.performanceMonitor.getPerformanceMetrics();
      
      return this.loadTestResults;
      
    } catch (error) {
      console.error(chalk.red('Load test suite failed:'), error);
      throw error;
    }
  }

  /**
   * Run streaming load test
   */
  async runStreamingLoadTest() {
    const streamingConnections = Math.floor(this.options.maxConnections * 0.5);
    const streamingDuration = 30000; // 30 seconds
    
    // Create connections for streaming test
    const users = this.clientFactory.createUsers(streamingConnections);
    const clients = await this.clientFactory.createConcurrentClients(users.length, 1);
    
    console.log(chalk.cyan(`Streaming test: ${clients.length} connections established`));
    
    const startTime = Date.now();
    const streamingSessions = [];
    
    // Start streaming sessions
    for (let i = 0; i < clients.length; i++) {
      const client = clients[i];
      const message = this.testMessages[i % this.testMessages.length];
      
      // Send message and start streaming validation
      const sessionPromise = this.startStreamingValidation(client, message);
      streamingSessions.push(sessionPromise);
      
      // Stagger requests to avoid overwhelming
      if (i % 10 === 0) {
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    }
    
    // Wait for all streaming sessions to complete
    const validationResults = await Promise.allSettled(streamingSessions);
    const successfulValidations = validationResults.filter(r => r.status === 'fulfilled').length;
    
    const endTime = Date.now();
    
    return {
      streamingConnections: clients.length,
      duration: endTime - startTime,
      streamingSessions: streamingSessions.length,
      successfulValidations,
      failedValidations: streamingSessions.length - successfulValidations,
      validationRate: streamingSessions.length > 0 ? successfulValidations / streamingSessions.length : 0,
      avgStreamingLatency: this.streamingValidator.getPerformanceMetrics().avgStreamingLatency
    };
  }

  /**
   * Generate load test report
   */
  generateLoadTestReport() {
    const report = {
      configuration: this.options,
      executionSummary: {
        startTime: this.loadTestResults.startTime,
        endTime: this.loadTestResults.endTime,
        totalDuration: this.loadTestResults.endTime - this.loadTestResults.startTime,
        phasesCompleted: Object.keys(this.loadTestResults.phases).length,
        patternsCompleted: Object.keys(this.loadTestResults.patterns).length,
        scenariosCompleted: Object.keys(this.loadTestResults.scenarios).length
      },
      phaseResults: this.loadTestResults.phases,
      patternResults: this.loadTestResults.patterns,
      scenarioResults: this.loadTestResults.scenarios,
      performanceMetrics: this.loadTestResults.overallMetrics,
      streamingValidation: this.streamingValidator.generateReport(),
      capacityAnalysis: this.performanceMonitor.generateCapacityRecommendations()
    };
    
    return report;
  }

  /**
   * Print load test results
   */
  printLoadTestResults() {
    const report = this.generateLoadTestReport();
    
    console.log(chalk.blue('\n' + '='.repeat(80)));
    console.log(chalk.blue.bold('              WEBSOCKET LOAD TEST RESULTS'));
    console.log(chalk.blue('='.repeat(80)));
    
    // Configuration
    console.log(chalk.yellow.bold('\n📋 TEST CONFIGURATION:'));
    console.log(chalk.cyan(`  Max Connections: ${report.configuration.maxConnections}`));
    console.log(chalk.cyan(`  Ramp-up Duration: ${report.configuration.rampUpDuration}ms`));
    console.log(chalk.cyan(`  Sustain Duration: ${report.configuration.sustainDuration}ms`));
    console.log(chalk.cyan(`  Load Patterns: ${report.configuration.loadPatterns.join(', ')}`));
    
    // Execution Summary
    console.log(chalk.yellow.bold('\n🚀 EXECUTION SUMMARY:'));
    console.log(chalk.green(`  ✓ Total Duration: ${(report.executionSummary.totalDuration / 1000).toFixed(1)}s`));
    console.log(chalk.green(`  ✓ Phases Completed: ${report.executionSummary.phasesCompleted}`));
    console.log(chalk.green(`  ✓ Patterns Tested: ${report.executionSummary.patternsCompleted}`));
    console.log(chalk.green(`  ✓ Scenarios Completed: ${report.executionSummary.scenariosCompleted}`));
    
    // Phase Results
    if (report.scenarioResults.connection) {
      const conn = report.scenarioResults.connection;
      console.log(chalk.yellow.bold('\n🔗 CONNECTION PHASE RESULTS:'));
      console.log(chalk.green(`  ✓ Peak Connections: ${conn.peakConnectionCount}`));
      console.log(chalk.green(`  ✓ Final Connections: ${conn.finalConnectionCount}`));
      console.log(chalk.green(`  ✓ Ramp-up Success Rate: ${(conn.phases.rampUp.successRate * 100).toFixed(1)}%`));
      console.log(chalk.green(`  ✓ Sustain Throughput: ${conn.phases.sustain.throughput.toFixed(1)} msg/s`));
    }
    
    // Pattern Results
    console.log(chalk.yellow.bold('\n🔄 LOAD PATTERN RESULTS:'));
    Object.entries(report.patternResults).forEach(([pattern, results]) => {
      console.log(chalk.cyan(`  ${pattern.toUpperCase()}:`));
      console.log(chalk.green(`    ✓ Duration: ${(results.duration / 1000).toFixed(1)}s`));
      if (results.results.peakConnections) {
        console.log(chalk.green(`    ✓ Peak Connections: ${results.results.peakConnections}`));
      }
    });
    
    // Streaming Results
    if (report.scenarioResults.streaming) {
      const streaming = report.scenarioResults.streaming;
      console.log(chalk.yellow.bold('\n🌊 STREAMING RESULTS:'));
      console.log(chalk.green(`  ✓ Streaming Connections: ${streaming.streamingConnections}`));
      console.log(chalk.green(`  ✓ Validation Rate: ${(streaming.validationRate * 100).toFixed(1)}%`));
      console.log(chalk.green(`  ✓ Avg Latency: ${streaming.avgStreamingLatency.toFixed(1)}ms`));
    }
    
    // Performance Metrics
    console.log(chalk.yellow.bold('\n📊 PERFORMANCE METRICS:'));
    if (report.performanceMetrics.connections) {
      console.log(chalk.cyan(`  Connection Success Rate: ${(report.performanceMetrics.connections.successRate * 100).toFixed(1)}%`));
    }
    if (report.performanceMetrics.messages) {
      console.log(chalk.cyan(`  Message Throughput: ${report.performanceMetrics.messages.avgMessagesPerClient.toFixed(1)} msg/client`));
      console.log(chalk.cyan(`  Message Error Rate: ${(report.performanceMetrics.messages.errorRate * 100).toFixed(2)}%`));
    }
    
    // Capacity Analysis
    if (report.capacityAnalysis.length > 0) {
      console.log(chalk.yellow.bold('\n💡 CAPACITY RECOMMENDATIONS:'));
      report.capacityAnalysis.forEach(rec => {
        const priority = rec.priority === 'high' ? chalk.red : chalk.yellow;
        console.log(priority(`  ${rec.type.toUpperCase()}: ${rec.recommendation}`));
      });
    }
    
    console.log(chalk.blue('\n' + '='.repeat(80)));
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    console.log(chalk.blue('🧹 Cleaning up load test resources...'));
    
    await this.clientFactory.closeAllClients();
    this.performanceMonitor.stop();
    
    console.log(chalk.green('✓ Load test cleanup completed'));
  }
}

// CLI interface
const argv = yargs(process.argv.slice(2))
  .option('max-connections', {
    alias: 'c',
    type: 'number',
    default: 100,
    description: 'Maximum number of concurrent connections'
  })
  .option('ramp-up-duration', {
    alias: 'r',
    type: 'number',
    default: 30000,
    description: 'Ramp-up duration in milliseconds'
  })
  .option('sustain-duration', {
    alias: 's',
    type: 'number',
    default: 60000,
    description: 'Sustain duration in milliseconds'
  })
  .option('patterns', {
    alias: 'p',
    type: 'array',
    default: ['constant', 'spike', 'gradual'],
    description: 'Load patterns to test'
  })
  .option('verbose', {
    alias: 'v',
    type: 'boolean',
    default: false,
    description: 'Enable verbose logging'
  })
  .help()
  .argv;

// Run if called directly
if (require.main === module) {
  const loadTester = new WebSocketLoadTester({
    maxConnections: argv.maxConnections,
    rampUpDuration: argv.rampUpDuration,
    sustainDuration: argv.sustainDuration,
    loadPatterns: argv.patterns
  });
  
  loadTester.initialize()
    .then(() => loadTester.runLoadTestSuite())
    .then(results => {
      loadTester.printLoadTestResults();
      console.log(chalk.green('\n✅ Load test completed successfully!'));
      return loadTester.cleanup();
    })
    .catch(error => {
      console.error(chalk.red('\n❌ Load test failed:'), error);
      return loadTester.cleanup();
    })
    .finally(() => {
      process.exit(0);
    });
}

module.exports = WebSocketLoadTester;