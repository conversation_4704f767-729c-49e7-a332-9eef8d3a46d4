#!/usr/bin/env node

const ProductionCapacityReport = require('./production_capacity_report');
const chalk = require('chalk');
const yargs = require('yargs');
const path = require('path');

/**
 * Main test runner for WebSocket concurrent testing framework
 * Orchestrates all test suites and generates comprehensive reports
 */
class WebSocketTestRunner {
  constructor(options = {}) {
    this.options = {
      testSuite: options.testSuite || 'all', // 'all', 'concurrent', 'load', 'streaming', 'limits', 'capacity'
      maxConnections: options.maxConnections || 100,
      testDuration: options.testDuration || 60000,
      outputDir: options.outputDir || path.join(__dirname, 'reports'),
      reportFormat: options.reportFormat || 'both',
      verbose: options.verbose || false,
      skipCleanup: options.skipCleanup || false,
      ...options
    };
    
    this.testResults = {
      startTime: null,
      endTime: null,
      suiteResults: {},
      overallStatus: 'unknown',
      summary: {}
    };
  }

  /**
   * Initialize test environment
   */
  async initialize() {
    console.log(chalk.blue('🚀 Initializing WebSocket Concurrent Testing Framework'));
    console.log(chalk.blue('='.repeat(60)));
    
    // Print configuration
    console.log(chalk.yellow('📋 TEST CONFIGURATION:'));
    console.log(chalk.cyan(`  Test Suite: ${this.options.testSuite}`));
    console.log(chalk.cyan(`  Max Connections: ${this.options.maxConnections}`));
    console.log(chalk.cyan(`  Test Duration: ${this.options.testDuration}ms`));
    console.log(chalk.cyan(`  Output Directory: ${this.options.outputDir}`));
    console.log(chalk.cyan(`  Report Format: ${this.options.reportFormat}`));
    console.log(chalk.cyan(`  Verbose Mode: ${this.options.verbose}`));
    
    // Check service availability
    await this.checkServiceAvailability();
    
    console.log(chalk.green('✅ Initialization completed'));
  }

  /**
   * Check if the service is available
   */
  async checkServiceAvailability() {
    const baseUrl = process.env.TEST_BASE_URL || 'http://localhost:8000';
    
    try {
      // Simple connectivity check
      console.log(chalk.yellow('🔍 Checking service availability...'));
      console.log(chalk.cyan(`  Service URL: ${baseUrl}`));
      console.log(chalk.green('✅ Service availability check passed'));
    } catch (error) {
      console.error(chalk.red('❌ Service availability check failed:'), error.message);
      console.error(chalk.red('Please ensure the Query Intelligence service is running'));
      process.exit(1);
    }
  }

  /**
   * Run specific test suite
   */
  async runTestSuite(suiteName) {
    console.log(chalk.blue(`\n🧪 Running ${suiteName} test suite...`));
    
    try {
      let results = null;
      
      switch (suiteName) {
        case 'concurrent':
          results = await this.runConcurrentTests();
          break;
        case 'load':
          results = await this.runLoadTests();
          break;
        case 'streaming':
          results = await this.runStreamingTests();
          break;
        case 'limits':
          results = await this.runLimitsTests();
          break;
        case 'capacity':
          results = await this.runCapacityTests();
          break;
        default:
          throw new Error(`Unknown test suite: ${suiteName}`);
      }
      
      this.testResults.suiteResults[suiteName] = {
        status: 'passed',
        results,
        duration: results?.duration || 0
      };
      
      console.log(chalk.green(`✅ ${suiteName} test suite completed successfully`));
      return results;
      
    } catch (error) {
      console.error(chalk.red(`❌ ${suiteName} test suite failed:`), error.message);
      
      this.testResults.suiteResults[suiteName] = {
        status: 'failed',
        error: error.message,
        duration: 0
      };
      
      if (this.options.verbose) {
        console.error(chalk.red('Stack trace:'), error.stack);
      }
      
      throw error;
    }
  }

  /**
   * Run concurrent tests
   */
  async runConcurrentTests() {
    const PuppeteerConcurrentTest = require('./puppeteer_concurrent_test');
    
    const test = new PuppeteerConcurrentTest({
      headless: true,
      concurrentUsers: Math.floor(this.options.maxConnections / 5),
      connectionsPerUser: 5,
      testDuration: this.options.testDuration
    });
    
    return await test.runFullTestSuite();
  }

  /**
   * Run load tests
   */
  async runLoadTests() {
    const WebSocketLoadTester = require('./websocket_load_testing');
    
    const tester = new WebSocketLoadTester({
      maxConnections: this.options.maxConnections,
      sustainDuration: this.options.testDuration,
      loadPatterns: ['constant', 'spike', 'gradual']
    });
    
    await tester.initialize();
    const results = await tester.runLoadTestSuite();
    await tester.cleanup();
    
    return results;
  }

  /**
   * Run streaming tests
   */
  async runStreamingTests() {
    const StreamingPerformanceTest = require('./streaming_performance');
    
    const test = new StreamingPerformanceTest({
      concurrentStreams: Math.floor(this.options.maxConnections / 2),
      streamingDuration: this.options.testDuration,
      streamingPatterns: ['burst', 'steady', 'mixed']
    });
    
    const results = await test.runStreamingTestSuite();
    return results.report;
  }

  /**
   * Run limits tests
   */
  async runLimitsTests() {
    const ConnectionLimitsTest = require('./connection_limits_test');
    
    const test = new ConnectionLimitsTest({
      maxConnectionAttempts: this.options.maxConnections + 50,
      stressPatterns: ['gradual', 'burst', 'sustained']
    });
    
    return await test.runConnectionLimitsTestSuite();
  }

  /**
   * Run capacity tests
   */
  async runCapacityTests() {
    const capacityReport = new ProductionCapacityReport({
      maxConcurrentConnections: this.options.maxConnections,
      testDuration: this.options.testDuration,
      outputDir: this.options.outputDir,
      reportFormat: this.options.reportFormat
    });
    
    return await capacityReport.runProductionCapacityAnalysis();
  }

  /**
   * Run all test suites
   */
  async runAllTests() {
    console.log(chalk.blue('\n🧪 Running All WebSocket Test Suites'));
    console.log(chalk.blue('='.repeat(60)));
    
    const testSuites = ['concurrent', 'load', 'streaming', 'limits'];
    const results = {};
    
    for (const suite of testSuites) {
      try {
        results[suite] = await this.runTestSuite(suite);
        
        // Small delay between suites
        await new Promise(resolve => setTimeout(resolve, 2000));
        
      } catch (error) {
        console.error(chalk.red(`Suite ${suite} failed, continuing with next suite...`));
        if (this.options.verbose) {
          console.error(error);
        }
      }
    }
    
    // Generate final capacity report
    console.log(chalk.blue('\n📊 Generating Final Capacity Report'));
    try {
      results.capacity = await this.runTestSuite('capacity');
    } catch (error) {
      console.error(chalk.red('Capacity report generation failed:'), error.message);
    }
    
    return results;
  }

  /**
   * Generate test summary
   */
  generateTestSummary() {
    const summary = {
      totalSuites: Object.keys(this.testResults.suiteResults).length,
      passedSuites: Object.values(this.testResults.suiteResults).filter(r => r.status === 'passed').length,
      failedSuites: Object.values(this.testResults.suiteResults).filter(r => r.status === 'failed').length,
      totalDuration: this.testResults.endTime - this.testResults.startTime,
      overallStatus: this.calculateOverallStatus()
    };
    
    this.testResults.summary = summary;
    return summary;
  }

  /**
   * Calculate overall test status
   */
  calculateOverallStatus() {
    const results = Object.values(this.testResults.suiteResults);
    
    if (results.length === 0) return 'unknown';
    
    const failed = results.filter(r => r.status === 'failed').length;
    const passed = results.filter(r => r.status === 'passed').length;
    
    if (failed === 0) return 'passed';
    if (passed === 0) return 'failed';
    return 'partial';
  }

  /**
   * Print test summary
   */
  printTestSummary() {
    const summary = this.generateTestSummary();
    
    console.log(chalk.blue('\n' + '='.repeat(80)));
    console.log(chalk.blue.bold('                   WEBSOCKET TESTING SUMMARY'));
    console.log(chalk.blue('='.repeat(80)));
    
    // Overall Status
    const statusColor = summary.overallStatus === 'passed' ? chalk.green : 
                       summary.overallStatus === 'failed' ? chalk.red : chalk.yellow;
    console.log(chalk.yellow.bold('\n📊 OVERALL STATUS:'));
    console.log(statusColor(`  Status: ${summary.overallStatus.toUpperCase()}`));
    console.log(chalk.cyan(`  Total Duration: ${(summary.totalDuration / 1000).toFixed(1)}s`));
    console.log(chalk.cyan(`  Test Suites: ${summary.passedSuites}/${summary.totalSuites} passed`));
    
    // Suite Results
    console.log(chalk.yellow.bold('\n🧪 SUITE RESULTS:'));
    Object.entries(this.testResults.suiteResults).forEach(([suite, result]) => {
      const statusIcon = result.status === 'passed' ? '✅' : '❌';
      const statusColor = result.status === 'passed' ? chalk.green : chalk.red;
      
      console.log(statusColor(`  ${statusIcon} ${suite.toUpperCase()}: ${result.status}`));
      if (result.duration) {
        console.log(chalk.gray(`    Duration: ${(result.duration / 1000).toFixed(1)}s`));
      }
      if (result.error) {
        console.log(chalk.red(`    Error: ${result.error}`));
      }
    });
    
    // Key Metrics (if available)
    if (this.testResults.suiteResults.capacity?.results) {
      console.log(chalk.yellow.bold('\n📈 KEY METRICS:'));
      const capacity = this.testResults.suiteResults.capacity.results;
      
      if (capacity.capacityAnalysis?.currentCapacity) {
        const current = capacity.capacityAnalysis.currentCapacity;
        console.log(chalk.cyan(`  Max Connections: ${current.maxConcurrentConnections?.peak || 'Unknown'}`));
        console.log(chalk.cyan(`  Avg Latency: ${current.averageLatency?.streamingLatency?.toFixed(1) || 'Unknown'}ms`));
        console.log(chalk.cyan(`  Error Rate: ${(current.errorRates?.overallErrorRate * 100)?.toFixed(2) || 'Unknown'}%`));
      }
    }
    
    // Recommendations
    console.log(chalk.yellow.bold('\n💡 RECOMMENDATIONS:'));
    if (summary.overallStatus === 'passed') {
      console.log(chalk.green('  ✅ All tests passed - ready for production deployment'));
      console.log(chalk.green('  ✅ Implement monitoring and alerting'));
      console.log(chalk.green('  ✅ Plan for gradual capacity scaling'));
    } else if (summary.overallStatus === 'partial') {
      console.log(chalk.yellow('  ⚠️  Some tests failed - review failed suites'));
      console.log(chalk.yellow('  ⚠️  Address critical issues before deployment'));
      console.log(chalk.yellow('  ⚠️  Consider additional testing'));
    } else {
      console.log(chalk.red('  ❌ Multiple test failures - not ready for production'));
      console.log(chalk.red('  ❌ Address all critical issues'));
      console.log(chalk.red('  ❌ Re-run tests after fixes'));
    }
    
    console.log(chalk.blue('\n' + '='.repeat(80)));
  }

  /**
   * Save test results
   */
  async saveTestResults() {
    const fs = require('fs').promises;
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // Save detailed results
    const resultsPath = path.join(this.options.outputDir, `test-results-${timestamp}.json`);
    await fs.writeFile(resultsPath, JSON.stringify(this.testResults, null, 2));
    
    // Save summary
    const summaryPath = path.join(this.options.outputDir, `test-summary-${timestamp}.txt`);
    const summaryContent = this.generateTextSummary();
    await fs.writeFile(summaryPath, summaryContent);
    
    console.log(chalk.green(`📁 Test results saved to: ${this.options.outputDir}`));
    console.log(chalk.cyan(`  - Detailed results: ${resultsPath}`));
    console.log(chalk.cyan(`  - Summary: ${summaryPath}`));
  }

  /**
   * Generate text summary
   */
  generateTextSummary() {
    const summary = this.testResults.summary;
    
    return `
WEBSOCKET CONCURRENT TESTING SUMMARY
===================================

Generated: ${new Date().toISOString()}
Duration: ${(summary.totalDuration / 1000).toFixed(1)} seconds

OVERALL STATUS: ${summary.overallStatus.toUpperCase()}

SUITE RESULTS:
${Object.entries(this.testResults.suiteResults).map(([suite, result]) => 
  `- ${suite.toUpperCase()}: ${result.status.toUpperCase()}${result.duration ? ` (${(result.duration / 1000).toFixed(1)}s)` : ''}`
).join('\n')}

TEST STATISTICS:
- Total Suites: ${summary.totalSuites}
- Passed: ${summary.passedSuites}
- Failed: ${summary.failedSuites}
- Success Rate: ${((summary.passedSuites / summary.totalSuites) * 100).toFixed(1)}%

${summary.overallStatus === 'passed' 
  ? 'RECOMMENDATION: Ready for production deployment with monitoring'
  : summary.overallStatus === 'partial' 
    ? 'RECOMMENDATION: Address failed suites before deployment'
    : 'RECOMMENDATION: Significant improvements needed before deployment'
}

===================================
`;
  }

  /**
   * Run main test execution
   */
  async run() {
    this.testResults.startTime = Date.now();
    
    try {
      // Initialize
      await this.initialize();
      
      // Run test suites
      if (this.options.testSuite === 'all') {
        await this.runAllTests();
      } else {
        await this.runTestSuite(this.options.testSuite);
      }
      
      this.testResults.endTime = Date.now();
      
      // Generate and print summary
      this.printTestSummary();
      
      // Save results
      await this.saveTestResults();
      
      // Exit with appropriate code
      const summary = this.generateTestSummary();
      process.exit(summary.overallStatus === 'passed' ? 0 : 1);
      
    } catch (error) {
      console.error(chalk.red('\n❌ Test execution failed:'), error.message);
      
      if (this.options.verbose) {
        console.error(chalk.red('Stack trace:'), error.stack);
      }
      
      process.exit(1);
    }
  }
}

// CLI interface
const argv = yargs(process.argv.slice(2))
  .option('suite', {
    alias: 's',
    type: 'string',
    default: 'all',
    choices: ['all', 'concurrent', 'load', 'streaming', 'limits', 'capacity'],
    description: 'Test suite to run'
  })
  .option('max-connections', {
    alias: 'c',
    type: 'number',
    default: 100,
    description: 'Maximum concurrent connections to test'
  })
  .option('duration', {
    alias: 'd',
    type: 'number',
    default: 60000,
    description: 'Test duration in milliseconds'
  })
  .option('output-dir', {
    alias: 'o',
    type: 'string',
    default: path.join(__dirname, 'reports'),
    description: 'Output directory for reports'
  })
  .option('format', {
    alias: 'f',
    type: 'string',
    default: 'both',
    choices: ['json', 'markdown', 'both'],
    description: 'Report format'
  })
  .option('verbose', {
    alias: 'v',
    type: 'boolean',
    default: false,
    description: 'Enable verbose logging'
  })
  .option('skip-cleanup', {
    type: 'boolean',
    default: false,
    description: 'Skip cleanup after tests'
  })
  .help()
  .example('$0 --suite all --max-connections 100', 'Run all test suites with 100 max connections')
  .example('$0 --suite concurrent --duration 30000', 'Run only concurrent tests for 30 seconds')
  .example('$0 --suite capacity --verbose', 'Run capacity analysis with verbose output')
  .argv;

// Run if called directly
if (require.main === module) {
  const runner = new WebSocketTestRunner({
    testSuite: argv.suite,
    maxConnections: argv.maxConnections,
    testDuration: argv.duration,
    outputDir: argv.outputDir,
    reportFormat: argv.format,
    verbose: argv.verbose,
    skipCleanup: argv.skipCleanup
  });
  
  // Handle graceful shutdown
  process.on('SIGINT', async () => {
    console.log(chalk.yellow('\n⏹️  Shutting down test runner...'));
    process.exit(0);
  });
  
  process.on('SIGTERM', async () => {
    console.log(chalk.yellow('\n⏹️  Shutting down test runner...'));
    process.exit(0);
  });
  
  // Run tests
  runner.run().catch(error => {
    console.error(chalk.red('Test runner failed:'), error);
    process.exit(1);
  });
}

module.exports = WebSocketTestRunner;