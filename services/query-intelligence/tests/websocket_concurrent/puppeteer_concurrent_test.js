const puppeteer = require('puppeteer');
const WebSocketClientFactory = require('./utils/websocket_client_factory');
const StreamingValidator = require('./utils/streaming_validator');
const PerformanceMonitor = require('./utils/performance_monitor');
const chalk = require('chalk');

/**
 * Puppeteer-based concurrent WebSocket testing
 * Tests real browser-based WebSocket connections with concurrent streaming
 */
class PuppeteerConcurrentTest {
  constructor(options = {}) {
    this.options = {
      headless: options.headless !== false, // Default to headless
      devtools: options.devtools || false,
      slowMo: options.slowMo || 0,
      concurrentUsers: options.concurrentUsers || 10,
      connectionsPerUser: options.connectionsPerUser || 5,
      totalConnections: options.totalConnections || 50,
      testDuration: options.testDuration || 60000, // 1 minute
      messageInterval: options.messageInterval || 5000, // 5 seconds
      browserArgs: options.browserArgs || [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--disable-gpu',
        '--window-size=1920,1080'
      ]
    };

    this.browsers = [];
    this.pages = [];
    this.clientFactory = new WebSocketClientFactory();
    this.streamingValidator = new StreamingValidator();
    this.performanceMonitor = new PerformanceMonitor();
    this.testResults = {
      startTime: null,
      endTime: null,
      totalConnections: 0,
      successfulConnections: 0,
      failedConnections: 0,
      concurrentSessions: [],
      performanceMetrics: {},
      validationResults: {}
    };
  }

  /**
   * Initialize testing environment
   */
  async initialize() {
    console.log(chalk.blue('🚀 Initializing Puppeteer concurrent WebSocket testing...'));
    
    // Register components with performance monitor
    this.performanceMonitor.registerClientFactory(this.clientFactory);
    this.performanceMonitor.registerStreamingValidator(this.streamingValidator);
    
    // Start performance monitoring
    this.performanceMonitor.start();
    
    console.log(chalk.green('✓ Testing environment initialized'));
  }

  /**
   * Create browser instances for concurrent testing
   */
  async createBrowsers(count) {
    console.log(chalk.blue(`🌐 Creating ${count} browser instances...`));
    
    const browserPromises = [];
    for (let i = 0; i < count; i++) {
      browserPromises.push(
        puppeteer.launch({
          headless: this.options.headless,
          devtools: this.options.devtools,
          slowMo: this.options.slowMo,
          args: this.options.browserArgs
        }).then(browser => {
          this.browsers.push(browser);
          return browser;
        }).catch(error => {
          console.error(chalk.red(`Failed to create browser ${i}:`), error);
          return null;
        })
      );
    }

    const browsers = await Promise.all(browserPromises);
    const successfulBrowsers = browsers.filter(b => b !== null);
    
    console.log(chalk.green(`✓ Created ${successfulBrowsers.length}/${count} browser instances`));
    return successfulBrowsers;
  }

  /**
   * Create pages with WebSocket connections
   */
  async createConcurrentPages(browsers) {
    console.log(chalk.blue('📄 Creating concurrent pages with WebSocket connections...'));
    
    const pagePromises = [];
    
    browsers.forEach((browser, browserIndex) => {
      for (let pageIndex = 0; pageIndex < this.options.connectionsPerUser; pageIndex++) {
        pagePromises.push(
          this.createPageWithWebSocket(browser, browserIndex, pageIndex)
        );
      }
    });

    const pages = await Promise.all(pagePromises);
    const successfulPages = pages.filter(p => p !== null);
    
    this.pages = successfulPages;
    console.log(chalk.green(`✓ Created ${successfulPages.length} pages with WebSocket connections`));
    
    return successfulPages;
  }

  /**
   * Create individual page with WebSocket connection
   */
  async createPageWithWebSocket(browser, browserIndex, pageIndex) {
    try {
      const page = await browser.newPage();
      
      // Set up page configuration
      await page.setViewport({ width: 1920, height: 1080 });
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
      
      // Navigate to a blank page
      await page.goto('about:blank');
      
      // Generate JWT token for authentication
      const users = this.clientFactory.createUsers(1);
      const user = users[0];
      
      // Inject WebSocket client code
      await page.evaluate((wsUrl, token, userId) => {
        window.wsConnection = null;
        window.wsMessages = [];
        window.wsErrors = [];
        window.wsStats = {
          connected: false,
          messagesReceived: 0,
          messagesSent: 0,
          errors: 0,
          connectionTime: null
        };

        // Create WebSocket connection
        window.connectWebSocket = function() {
          return new Promise((resolve, reject) => {
            try {
              const ws = new WebSocket(wsUrl, [], {
                headers: {
                  'Authorization': `Bearer ${token}`
                }
              });
              
              ws.onopen = function() {
                window.wsStats.connected = true;
                window.wsStats.connectionTime = Date.now();
                window.wsConnection = ws;
                resolve(ws);
              };
              
              ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                window.wsMessages.push({
                  ...message,
                  timestamp: Date.now()
                });
                window.wsStats.messagesReceived++;
              };
              
              ws.onerror = function(error) {
                window.wsErrors.push({
                  error: error.toString(),
                  timestamp: Date.now()
                });
                window.wsStats.errors++;
                reject(error);
              };
              
              ws.onclose = function() {
                window.wsStats.connected = false;
              };
              
              setTimeout(() => {
                if (!window.wsStats.connected) {
                  reject(new Error('Connection timeout'));
                }
              }, 10000);
              
            } catch (error) {
              reject(error);
            }
          });
        };

        // Send WebSocket message
        window.sendMessage = function(message) {
          if (window.wsConnection && window.wsStats.connected) {
            window.wsConnection.send(JSON.stringify(message));
            window.wsStats.messagesSent++;
            return true;
          }
          return false;
        };

        // Get WebSocket stats
        window.getStats = function() {
          return {
            ...window.wsStats,
            messageCount: window.wsMessages.length,
            errorCount: window.wsErrors.length,
            latestMessages: window.wsMessages.slice(-5)
          };
        };

        // Wait for streaming completion
        window.waitForStreaming = function(timeout = 30000) {
          return new Promise((resolve) => {
            const startTime = Date.now();
            const checkCompletion = () => {
              const doneMessage = window.wsMessages.find(msg => msg.type === 'done');
              if (doneMessage) {
                const streamingData = {
                  chunks: window.wsMessages.filter(msg => msg.type === 'text'),
                  references: window.wsMessages.filter(msg => msg.type === 'reference'),
                  metadata: doneMessage.metadata,
                  complete: true
                };
                resolve(streamingData);
              } else if (Date.now() - startTime > timeout) {
                resolve({ complete: false, timeout: true });
              } else {
                setTimeout(checkCompletion, 100);
              }
            };
            checkCompletion();
          });
        };

      }, global.TEST_CONFIG.WS_URL, user.token, user.userId);
      
      // Establish WebSocket connection
      await page.evaluate(() => window.connectWebSocket());
      
      // Store page metadata
      page.metadata = {
        browserIndex,
        pageIndex,
        userId: user.userId,
        token: user.token,
        createdAt: Date.now()
      };
      
      return page;
      
    } catch (error) {
      console.error(chalk.red(`Failed to create page [${browserIndex}:${pageIndex}]:`), error);
      return null;
    }
  }

  /**
   * Run concurrent WebSocket stress test
   */
  async runConcurrentStressTest() {
    console.log(chalk.blue('🔥 Starting concurrent WebSocket stress test...'));
    
    this.testResults.startTime = Date.now();
    
    // Create test queries
    const testQueries = [
      'How does authentication work in this codebase?',
      'Explain the WebSocket implementation details',
      'What are the security measures in place?',
      'Show me the error handling patterns',
      'How is the database connection managed?',
      'What caching strategies are implemented?',
      'Explain the API routing structure',
      'How are user sessions handled?',
      'What testing patterns are used?',
      'Show me the deployment configuration'
    ];

    const concurrentSessions = [];
    const sessionPromises = [];

    // Start concurrent sessions
    for (let i = 0; i < this.pages.length; i++) {
      const page = this.pages[i];
      const query = testQueries[i % testQueries.length];
      
      const sessionPromise = this.runStreamingSession(page, query, i);
      sessionPromises.push(sessionPromise);
      
      // Stagger connection attempts to avoid overwhelming
      if (i % 10 === 0 && i > 0) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    // Wait for all sessions to complete
    console.log(chalk.yellow(`⏳ Waiting for ${sessionPromises.length} concurrent sessions...`));
    const sessionResults = await Promise.allSettled(sessionPromises);
    
    // Process results
    const successfulSessions = sessionResults.filter(r => r.status === 'fulfilled').map(r => r.value);
    const failedSessions = sessionResults.filter(r => r.status === 'rejected');
    
    console.log(chalk.green(`✓ Completed ${successfulSessions.length}/${sessionResults.length} sessions`));
    
    this.testResults.endTime = Date.now();
    this.testResults.totalConnections = this.pages.length;
    this.testResults.successfulConnections = successfulSessions.length;
    this.testResults.failedConnections = failedSessions.length;
    this.testResults.concurrentSessions = successfulSessions;
    
    return {
      successful: successfulSessions,
      failed: failedSessions,
      total: sessionResults.length
    };
  }

  /**
   * Run individual streaming session
   */
  async runStreamingSession(page, query, sessionIndex) {
    const startTime = Date.now();
    
    try {
      // Send query
      const sent = await page.evaluate((query) => {
        return window.sendMessage({
          query: query,
          repository_id: 'test-concurrent-repo'
        });
      }, query);

      if (!sent) {
        throw new Error('Failed to send message - WebSocket not connected');
      }

      // Wait for streaming completion
      const streamingData = await page.evaluate(() => window.waitForStreaming());
      
      if (!streamingData.complete) {
        throw new Error('Streaming timeout or incomplete');
      }

      // Get page statistics
      const stats = await page.evaluate(() => window.getStats());
      
      // Validate streaming session
      const mockClient = {
        id: `puppeteer-${sessionIndex}`,
        userId: page.metadata.userId
      };
      
      const validation = await this.streamingValidator.validateStreamingSession(
        mockClient, 
        streamingData, 
        startTime
      );

      return {
        sessionIndex,
        page: page.metadata,
        query,
        startTime,
        endTime: Date.now(),
        duration: Date.now() - startTime,
        streamingData,
        stats,
        validation,
        success: true
      };

    } catch (error) {
      console.error(chalk.red(`Session ${sessionIndex} failed:`), error.message);
      
      return {
        sessionIndex,
        page: page.metadata,
        query,
        startTime,
        endTime: Date.now(),
        duration: Date.now() - startTime,
        error: error.message,
        success: false
      };
    }
  }

  /**
   * Run connection limit test
   */
  async runConnectionLimitTest() {
    console.log(chalk.blue('🔐 Testing connection limits...'));
    
    const maxConnections = 100;
    const browsers = await this.createBrowsers(Math.ceil(maxConnections / 10));
    
    let connectionCount = 0;
    let successfulConnections = 0;
    
    // Attempt to create maximum connections
    for (let i = 0; i < maxConnections; i++) {
      try {
        const browser = browsers[i % browsers.length];
        const page = await this.createPageWithWebSocket(browser, 0, i);
        
        if (page) {
          connectionCount++;
          successfulConnections++;
          
          // Check if we can still connect
          const connected = await page.evaluate(() => window.wsStats.connected);
          if (!connected) {
            console.log(chalk.yellow(`Connection limit reached at ${i} connections`));
            break;
          }
        }
      } catch (error) {
        console.log(chalk.red(`Connection ${i} failed: ${error.message}`));
        break;
      }
    }
    
    console.log(chalk.green(`✓ Connection limit test: ${successfulConnections}/${maxConnections} connections`));
    
    return {
      maxAttempted: maxConnections,
      successful: successfulConnections,
      limitReached: successfulConnections < maxConnections
    };
  }

  /**
   * Run message throughput test
   */
  async runThroughputTest() {
    console.log(chalk.blue('📊 Testing message throughput...'));
    
    const testDuration = 30000; // 30 seconds
    const messageInterval = 1000; // 1 second
    const startTime = Date.now();
    
    const throughputResults = [];
    
    // Send messages at regular intervals
    const throughputInterval = setInterval(async () => {
      const promises = this.pages.map(async (page, index) => {
        try {
          const sent = await page.evaluate((query) => {
            return window.sendMessage({
              query: query,
              repository_id: 'throughput-test-repo'
            });
          }, `Throughput test query ${Date.now()}`);
          
          return sent;
        } catch (error) {
          return false;
        }
      });
      
      const results = await Promise.all(promises);
      const successful = results.filter(r => r).length;
      
      throughputResults.push({
        timestamp: Date.now(),
        messagesSent: successful,
        totalConnections: this.pages.length
      });
      
    }, messageInterval);
    
    // Stop after test duration
    setTimeout(() => {
      clearInterval(throughputInterval);
    }, testDuration);
    
    // Wait for completion
    await new Promise(resolve => setTimeout(resolve, testDuration + 2000));
    
    // Calculate throughput metrics
    const totalMessages = throughputResults.reduce((sum, r) => sum + r.messagesSent, 0);
    const avgThroughput = totalMessages / (testDuration / 1000);
    
    console.log(chalk.green(`✓ Throughput test: ${avgThroughput.toFixed(1)} messages/second`));
    
    return {
      duration: testDuration,
      totalMessages,
      avgThroughput,
      peakThroughput: Math.max(...throughputResults.map(r => r.messagesSent)),
      results: throughputResults
    };
  }

  /**
   * Generate comprehensive test report
   */
  generateTestReport() {
    const performanceMetrics = this.performanceMonitor.getPerformanceMetrics();
    const validationMetrics = this.streamingValidator.getPerformanceMetrics();
    
    const report = {
      testConfiguration: {
        totalConnections: this.options.totalConnections,
        concurrentUsers: this.options.concurrentUsers,
        connectionsPerUser: this.options.connectionsPerUser,
        testDuration: this.options.testDuration,
        browserCount: this.browsers.length
      },
      
      executionSummary: {
        startTime: this.testResults.startTime,
        endTime: this.testResults.endTime,
        duration: this.testResults.endTime - this.testResults.startTime,
        totalConnections: this.testResults.totalConnections,
        successfulConnections: this.testResults.successfulConnections,
        failedConnections: this.testResults.failedConnections,
        successRate: this.testResults.totalConnections > 0 
          ? this.testResults.successfulConnections / this.testResults.totalConnections 
          : 0
      },
      
      performanceMetrics: {
        connections: performanceMetrics.connections,
        messages: performanceMetrics.messages,
        memory: performanceMetrics.memory
      },
      
      streamingValidation: {
        totalValidations: validationMetrics.totalValidations,
        successfulValidations: validationMetrics.successfulValidations,
        failedValidations: validationMetrics.failedValidations,
        avgStreamingLatency: validationMetrics.avgStreamingLatency,
        avgContentQuality: validationMetrics.avgContentQuality,
        avgChunkCount: validationMetrics.avgChunkCount
      },
      
      capacityRecommendations: this.performanceMonitor.generateCapacityRecommendations(),
      
      detailedResults: {
        concurrentSessions: this.testResults.concurrentSessions,
        validationResults: this.streamingValidator.getValidationResults()
      }
    };
    
    return report;
  }

  /**
   * Print test results to console
   */
  printTestResults() {
    const report = this.generateTestReport();
    
    console.log(chalk.blue('\n' + '='.repeat(80)));
    console.log(chalk.blue.bold('           PUPPETEER CONCURRENT WEBSOCKET TEST RESULTS'));
    console.log(chalk.blue('='.repeat(80)));
    
    // Test Configuration
    console.log(chalk.yellow.bold('\n📋 TEST CONFIGURATION:'));
    console.log(chalk.cyan(`  Total Connections: ${report.testConfiguration.totalConnections}`));
    console.log(chalk.cyan(`  Concurrent Users: ${report.testConfiguration.concurrentUsers}`));
    console.log(chalk.cyan(`  Connections per User: ${report.testConfiguration.connectionsPerUser}`));
    console.log(chalk.cyan(`  Browser Instances: ${report.testConfiguration.browserCount}`));
    
    // Execution Summary
    console.log(chalk.yellow.bold('\n🚀 EXECUTION SUMMARY:'));
    console.log(chalk.green(`  ✓ Successful Connections: ${report.executionSummary.successfulConnections}/${report.executionSummary.totalConnections}`));
    console.log(chalk.green(`  ✓ Success Rate: ${(report.executionSummary.successRate * 100).toFixed(1)}%`));
    console.log(chalk.green(`  ✓ Test Duration: ${(report.executionSummary.duration / 1000).toFixed(1)}s`));
    
    // Performance Metrics
    console.log(chalk.yellow.bold('\n📊 PERFORMANCE METRICS:'));
    console.log(chalk.cyan(`  Average Latency: ${report.performanceMetrics.connections?.avgConnectionTime || 0}ms`));
    console.log(chalk.cyan(`  Message Throughput: ${report.performanceMetrics.messages?.avgMessagesPerClient || 0} msg/client`));
    console.log(chalk.cyan(`  Memory Usage: ${(report.performanceMetrics.memory?.totalMessages || 0)} total messages`));
    
    // Streaming Validation
    console.log(chalk.yellow.bold('\n🔍 STREAMING VALIDATION:'));
    console.log(chalk.green(`  ✓ Successful Validations: ${report.streamingValidation.successfulValidations}/${report.streamingValidation.totalValidations}`));
    console.log(chalk.green(`  ✓ Avg Streaming Latency: ${report.streamingValidation.avgStreamingLatency.toFixed(1)}ms`));
    console.log(chalk.green(`  ✓ Avg Content Quality: ${report.streamingValidation.avgContentQuality.toFixed(2)}`));
    console.log(chalk.green(`  ✓ Avg Chunk Count: ${report.streamingValidation.avgChunkCount.toFixed(1)}`));
    
    // Capacity Recommendations
    if (report.capacityRecommendations.length > 0) {
      console.log(chalk.yellow.bold('\n💡 CAPACITY RECOMMENDATIONS:'));
      report.capacityRecommendations.forEach(rec => {
        const priority = rec.priority === 'high' ? chalk.red : chalk.yellow;
        console.log(priority(`  ${rec.type.toUpperCase()}: ${rec.recommendation}`));
        console.log(chalk.gray(`    Current: ${rec.current} | Action: ${rec.action}`));
      });
    }
    
    console.log(chalk.blue('\n' + '='.repeat(80)));
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    console.log(chalk.blue('🧹 Cleaning up resources...'));
    
    // Stop performance monitoring
    this.performanceMonitor.stop();
    
    // Close all pages
    for (const page of this.pages) {
      try {
        await page.close();
      } catch (error) {
        console.error(chalk.red('Failed to close page:'), error);
      }
    }
    
    // Close all browsers
    for (const browser of this.browsers) {
      try {
        await browser.close();
      } catch (error) {
        console.error(chalk.red('Failed to close browser:'), error);
      }
    }
    
    // Close client factory
    await this.clientFactory.closeAllClients();
    
    console.log(chalk.green('✓ Cleanup completed'));
  }

  /**
   * Run complete test suite
   */
  async runFullTestSuite() {
    try {
      // Initialize
      await this.initialize();
      
      // Create browsers and pages
      const browsers = await this.createBrowsers(this.options.concurrentUsers);
      await this.createConcurrentPages(browsers);
      
      // Run tests
      console.log(chalk.blue('\n🧪 Running complete test suite...'));
      
      // 1. Concurrent stress test
      const stressResults = await this.runConcurrentStressTest();
      
      // 2. Connection limit test
      const limitResults = await this.runConnectionLimitTest();
      
      // 3. Throughput test
      const throughputResults = await this.runThroughputTest();
      
      // Store additional results
      this.testResults.connectionLimitTest = limitResults;
      this.testResults.throughputTest = throughputResults;
      
      // Generate and print report
      this.printTestResults();
      
      // Export metrics
      const exportData = {
        testReport: this.generateTestReport(),
        performanceData: this.performanceMonitor.exportMetrics(),
        streamingValidation: this.streamingValidator.generateReport()
      };
      
      return exportData;
      
    } catch (error) {
      console.error(chalk.red('Test suite failed:'), error);
      throw error;
    } finally {
      await this.cleanup();
    }
  }
}

module.exports = PuppeteerConcurrentTest;

// If run directly
if (require.main === module) {
  const test = new PuppeteerConcurrentTest({
    headless: true,
    concurrentUsers: 10,
    connectionsPerUser: 5,
    totalConnections: 50,
    testDuration: 60000
  });
  
  test.runFullTestSuite()
    .then(results => {
      console.log(chalk.green('\n✅ Test suite completed successfully!'));
      console.log(chalk.blue('Results exported to:', JSON.stringify(results, null, 2)));
    })
    .catch(error => {
      console.error(chalk.red('\n❌ Test suite failed:'), error);
      process.exit(1);
    });
}