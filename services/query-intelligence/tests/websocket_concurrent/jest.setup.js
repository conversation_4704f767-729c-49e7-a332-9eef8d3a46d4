// Jest setup for WebSocket concurrent testing
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

// Global test configuration
global.TEST_CONFIG = {
  BASE_URL: process.env.TEST_BASE_URL || 'http://localhost:8000',
  WS_URL: process.env.TEST_WS_URL || 'ws://localhost:8000/api/v1/ws/query',
  JWT_SECRET: process.env.JWT_SECRET || 'test-secret-key',
  MAX_CONCURRENT_CONNECTIONS: parseInt(process.env.MAX_CONCURRENT_CONNECTIONS) || 100,
  CONNECTION_TIMEOUT: parseInt(process.env.CONNECTION_TIMEOUT) || 30000,
  MESSAGE_TIMEOUT: parseInt(process.env.MESSAGE_TIMEOUT) || 10000,
  STREAMING_TIMEOUT: parseInt(process.env.STREAMING_TIMEOUT) || 30000,
  PERFORMANCE_THRESHOLD_MS: parseInt(process.env.PERFORMANCE_THRESHOLD_MS) || 2000,
  MEMORY_LIMIT_MB: parseInt(process.env.MEMORY_LIMIT_MB) || 4096,
  CONNECTION_SUCCESS_RATE: parseFloat(process.env.CONNECTION_SUCCESS_RATE) || 0.99,
  MESSAGE_THROUGHPUT_MIN: parseInt(process.env.MESSAGE_THROUGHPUT_MIN) || 10
};

// Increase Jest timeout for concurrent tests
jest.setTimeout(120000);