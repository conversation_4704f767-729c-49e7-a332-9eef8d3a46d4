from fastapi.testclient import TestClient
from query_intelligence.main import app
from unittest.mock import patch

client = TestClient(app)


def test_health_check():
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert "service" in data
    assert "version" in data
    assert "checks" in data


@patch("query_intelligence.services.query_processor.QueryProcessor.process_query")
def test_query_endpoint(mock_process_query):
    from query_intelligence.models.query import QueryResult, QueryIntent
    
    mock_process_query.return_value = QueryResult(
        answer="mocked response",
        intent=QueryIntent.EXPLAIN,
        confidence=0.9,
        execution_time_ms=100.0,
        references=[],
        follow_up_questions=[],
        metadata={}
    )

    response = client.post(
        "/api/v1/query",
        json={"query": "test query", "repository_id": "test_repo"},
        headers={"Authorization": "Bearer some-token"},
    )

    assert response.status_code == 200
    data = response.json()
    assert data["answer"] == "mocked response"
    assert data["intent"] == "explain"
