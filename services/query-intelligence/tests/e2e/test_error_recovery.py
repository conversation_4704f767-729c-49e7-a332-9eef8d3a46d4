"""
Error Recovery E2E Tests

Tests fallback mechanisms, circuit breaker behavior, error handling,
and system resilience under various failure conditions.
"""

import pytest
import asyncio
import time
from typing import Dict, List, Any, Optional
from unittest.mock import patch, AsyncMock

from .fixtures import (
    all_services_healthy, all_services_degraded, service_mock_manager,
    error_scenarios, auth_headers, websocket_auth_headers
)
from .utils.websocket_client import WebSocketTestClient
from .utils.service_health import ServiceHealthChecker


class TestErrorRecovery:
    """Error recovery and fallback mechanism tests"""
    
    @pytest.fixture
    def service_base_url(self):
        """Base URL for the service"""
        return "http://localhost:8000"
    
    @pytest.fixture
    def websocket_base_url(self):
        """WebSocket base URL"""
        return "ws://localhost:8000"
    
    @pytest.fixture
    async def health_checker(self):
        """Health checker instance"""
        return ServiceHealthChecker(timeout=5.0)
    
    @pytest.mark.asyncio
    async def test_redis_failure_recovery(self, service_base_url, auth_headers, service_mock_manager):
        """Test Redis failure recovery and graceful degradation"""
        import httpx
        
        # Save original state
        service_mock_manager.save_states()
        
        try:
            # Step 1: Test normal operation with Redis
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": "Test query with Redis working",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                normal_result = response.json()
                assert len(normal_result["response"]["text"]) > 0
            
            # Step 2: Simulate Redis failure
            service_mock_manager.fail_service("redis")
            
            # Step 3: Test graceful degradation
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": "Test query with Redis failed",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                # Should still work without Redis
                assert response.status_code == 200
                degraded_result = response.json()
                assert len(degraded_result["response"]["text"]) > 0
                
                # Response should indicate degraded mode (if implemented)
                # May have lower confidence or different metadata
                assert degraded_result["response"]["confidence"] >= 0.3
            
            # Step 4: Test Redis recovery
            service_mock_manager.restore_service("redis")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": "Test query after Redis recovery",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                recovered_result = response.json()
                assert len(recovered_result["response"]["text"]) > 0
                
                # Performance should be restored
                assert recovered_result["response"]["confidence"] >= 0.5
                
        finally:
            # Restore original state
            service_mock_manager.restore_states()
    
    @pytest.mark.asyncio
    async def test_analysis_engine_failure_recovery(self, service_base_url, auth_headers, service_mock_manager):
        """Test Analysis Engine failure recovery"""
        import httpx
        
        service_mock_manager.save_states()
        
        try:
            # Step 1: Test with Analysis Engine working
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": "Analyze the code quality of this function",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                normal_result = response.json()
            
            # Step 2: Simulate Analysis Engine failure
            service_mock_manager.fail_service("analysis_engine")
            
            # Step 3: Test fallback behavior
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": "Analyze the code quality with failed analysis engine",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                # Should still provide response without analysis engine
                assert response.status_code == 200
                fallback_result = response.json()
                assert len(fallback_result["response"]["text"]) > 0
                
                # May have different content but should still be useful
                assert fallback_result["response"]["confidence"] >= 0.3
            
            # Step 4: Test recovery
            service_mock_manager.restore_service("analysis_engine")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": "Analyze code quality after recovery",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                recovered_result = response.json()
                assert len(recovered_result["response"]["text"]) > 0
                
        finally:
            service_mock_manager.restore_states()
    
    @pytest.mark.asyncio
    async def test_multiple_service_failure_recovery(self, service_base_url, auth_headers, service_mock_manager):
        """Test recovery from multiple service failures"""
        import httpx
        
        service_mock_manager.save_states()
        
        try:
            # Step 1: Simulate multiple service failures
            service_mock_manager.fail_service("redis")
            service_mock_manager.fail_service("analysis_engine")
            service_mock_manager.fail_service("pattern_mining")
            
            # Step 2: Test minimal functionality
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": "Test with multiple services failed",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                # Should still provide basic functionality
                assert response.status_code == 200
                minimal_result = response.json()
                assert len(minimal_result["response"]["text"]) > 0
                
                # May have lower confidence but should still work
                assert minimal_result["response"]["confidence"] >= 0.2
            
            # Step 3: Test gradual recovery
            service_mock_manager.restore_service("redis")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": "Test after Redis recovery",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                partial_result = response.json()
                assert partial_result["response"]["confidence"] >= minimal_result["response"]["confidence"]
            
            # Step 4: Test full recovery
            service_mock_manager.restore_service("analysis_engine")
            service_mock_manager.restore_service("pattern_mining")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": "Test after full recovery",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                full_result = response.json()
                assert full_result["response"]["confidence"] >= partial_result["response"]["confidence"]
                
        finally:
            service_mock_manager.restore_states()
    
    @pytest.mark.asyncio
    async def test_websocket_error_recovery(self, websocket_base_url, websocket_auth_headers):
        """Test WebSocket error recovery"""
        
        # Step 1: Test WebSocket connection error recovery
        client = WebSocketTestClient(websocket_base_url)
        
        try:
            connected = await client.connect(websocket_auth_headers)
            assert connected, "Failed to connect to WebSocket"
            
            # Step 2: Test invalid query error
            events = await client.query_and_receive("")  # Empty query
            assert client.has_error(), "Expected error for empty query"
            
            errors = client.get_errors()
            assert len(errors) > 0, "No error events received"
            
            # Step 3: Test recovery with valid query
            client.clear_events()
            events = await client.query_and_receive("Valid query after error")
            
            # Should recover and work normally
            assert not client.has_error(), "Service should recover from error"
            assert len(events) > 0, "No events after error recovery"
            
            text_content = client.get_text_content()
            assert len(text_content) > 0, "No text content after recovery"
            
        finally:
            if client.connected:
                await client.disconnect()
    
    @pytest.mark.asyncio
    async def test_timeout_error_recovery(self, service_base_url, auth_headers):
        """Test timeout error recovery"""
        import httpx
        
        # Step 1: Test with very short timeout
        async with httpx.AsyncClient(timeout=0.1) as client:
            try:
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": "Complex query that might timeout",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                # If it doesn't timeout, that's also valid
                if response.status_code == 200:
                    result = response.json()
                    assert "response" in result
                    
            except httpx.TimeoutException:
                # Timeout is expected with very short timeout
                pass
        
        # Step 2: Test recovery with normal timeout
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": "Normal query after timeout",
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            
            # Should work normally
            assert response.status_code == 200
            result = response.json()
            assert len(result["response"]["text"]) > 0
    
    @pytest.mark.asyncio
    async def test_authentication_error_recovery(self, service_base_url, auth_headers):
        """Test authentication error recovery"""
        import httpx
        
        # Step 1: Test with invalid token
        invalid_headers = {"Authorization": "Bearer invalid_token"}
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": "Test with invalid token",
                    "repository_id": "test_repo"
                },
                headers=invalid_headers
            )
            
            # Should get authentication error
            assert response.status_code == 401
        
        # Step 2: Test recovery with valid token
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": "Test with valid token",
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            
            # Should work normally
            assert response.status_code == 200
            result = response.json()
            assert len(result["response"]["text"]) > 0
    
    @pytest.mark.asyncio
    async def test_rate_limit_error_recovery(self, service_base_url, auth_headers):
        """Test rate limit error recovery"""
        import httpx
        
        # Step 1: Generate many requests to trigger rate limiting
        async with httpx.AsyncClient() as client:
            rate_limit_responses = []
            
            # Make rapid requests
            for i in range(100):  # High number to trigger rate limiting
                try:
                    response = await client.post(
                        f"{service_base_url}/api/v1/query",
                        json={
                            "query": f"Rate limit test {i}",
                            "repository_id": "test_repo"
                        },
                        headers=auth_headers
                    )
                    rate_limit_responses.append(response.status_code)
                    
                    # If we get rate limited, break
                    if response.status_code == 429:
                        break
                        
                except Exception as e:
                    # Connection errors might occur under high load
                    break
        
        # Step 2: Wait for rate limit to reset
        await asyncio.sleep(5)
        
        # Step 3: Test recovery
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": "Test after rate limit recovery",
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            
            # Should work normally after rate limit reset
            assert response.status_code == 200
            result = response.json()
            assert len(result["response"]["text"]) > 0
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_behavior(self, service_base_url, auth_headers):
        """Test circuit breaker behavior"""
        import httpx
        
        # Step 1: Check circuit breaker status
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{service_base_url}/circuit-breakers")
            
            if response.status_code == 200:
                breaker_status = response.json()
                assert "circuit_breakers" in breaker_status
                
                # Should have circuit breaker information
                breakers = breaker_status["circuit_breakers"]
                assert isinstance(breakers, dict)
        
        # Step 2: Test normal operation
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": "Test circuit breaker normal operation",
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            
            assert response.status_code == 200
            result = response.json()
            assert len(result["response"]["text"]) > 0
    
    @pytest.mark.asyncio
    async def test_error_propagation_and_handling(self, service_base_url, auth_headers):
        """Test error propagation and handling"""
        import httpx
        
        # Step 1: Test malformed request
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "invalid_field": "This should cause validation error"
                },
                headers=auth_headers
            )
            
            # Should get validation error
            assert response.status_code == 422
        
        # Step 2: Test recovery with valid request
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": "Valid query after error",
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            
            # Should work normally
            assert response.status_code == 200
            result = response.json()
            assert len(result["response"]["text"]) > 0
    
    @pytest.mark.asyncio
    async def test_concurrent_error_recovery(self, service_base_url, auth_headers):
        """Test concurrent error recovery"""
        import httpx
        
        async def mixed_request(request_id: int):
            """Mix of valid and invalid requests"""
            async with httpx.AsyncClient() as client:
                if request_id % 3 == 0:
                    # Invalid request
                    response = await client.post(
                        f"{service_base_url}/api/v1/query",
                        json={"invalid": "data"},
                        headers=auth_headers
                    )
                    return {"request_id": request_id, "type": "invalid", "status": response.status_code}
                
                else:
                    # Valid request
                    response = await client.post(
                        f"{service_base_url}/api/v1/query",
                        json={
                            "query": f"Concurrent test {request_id}",
                            "repository_id": "test_repo"
                        },
                        headers=auth_headers
                    )
                    return {"request_id": request_id, "type": "valid", "status": response.status_code}
        
        # Step 1: Run concurrent mixed requests
        tasks = [mixed_request(i) for i in range(20)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Step 2: Validate error handling
        valid_results = [r for r in results if isinstance(r, dict) and r["type"] == "valid"]
        invalid_results = [r for r in results if isinstance(r, dict) and r["type"] == "invalid"]
        
        # Valid requests should mostly succeed
        valid_success = [r for r in valid_results if r["status"] == 200]
        valid_success_rate = len(valid_success) / len(valid_results) * 100
        
        assert valid_success_rate >= 80.0, f"Valid request success rate too low: {valid_success_rate:.1f}%"
        
        # Invalid requests should be handled appropriately
        invalid_handled = [r for r in invalid_results if r["status"] == 422]
        invalid_handled_rate = len(invalid_handled) / len(invalid_results) * 100
        
        assert invalid_handled_rate >= 80.0, f"Invalid request handling rate too low: {invalid_handled_rate:.1f}%"
    
    @pytest.mark.asyncio
    async def test_system_health_during_errors(self, service_base_url, health_checker):
        """Test system health during error conditions"""
        import httpx
        
        # Step 1: Check baseline health
        service_config = {"query_intelligence_url": service_base_url}
        baseline_health = await health_checker.check_all_services(service_config)
        baseline_summary = health_checker.get_system_health_summary(baseline_health)
        
        # Step 2: Generate errors
        async with httpx.AsyncClient() as client:
            error_tasks = []
            
            # Generate various types of errors
            for i in range(10):
                # Invalid requests
                error_tasks.append(client.post(
                    f"{service_base_url}/api/v1/query",
                    json={"invalid": "data"},
                    headers={"Authorization": "Bearer invalid_token"}
                ))
        
        await asyncio.gather(*error_tasks, return_exceptions=True)
        
        # Step 3: Check health after errors
        post_error_health = await health_checker.check_all_services(service_config)
        post_error_summary = health_checker.get_system_health_summary(post_error_health)
        
        # Step 4: Validate system resilience
        # System should maintain basic health despite errors
        assert post_error_summary["overall_status"] != "unavailable", "System became unavailable after errors"
        
        # Health percentage shouldn't drop drastically
        health_drop = baseline_summary["health_percentage"] - post_error_summary["health_percentage"]
        assert health_drop <= 20.0, f"Health dropped too much: {health_drop:.1f}%"
    
    @pytest.mark.asyncio
    async def test_graceful_degradation_levels(self, service_base_url, auth_headers, service_mock_manager):
        """Test different levels of graceful degradation"""
        import httpx
        
        service_mock_manager.save_states()
        
        try:
            # Step 1: Test full functionality
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": "Full functionality test",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                full_result = response.json()
                full_confidence = full_result["response"]["confidence"]
            
            # Step 2: Test Level 1 degradation (Redis failure)
            service_mock_manager.fail_service("redis")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": "Level 1 degradation test",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                level1_result = response.json()
                level1_confidence = level1_result["response"]["confidence"]
            
            # Step 3: Test Level 2 degradation (Redis + Analysis Engine failure)
            service_mock_manager.fail_service("analysis_engine")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": "Level 2 degradation test",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                level2_result = response.json()
                level2_confidence = level2_result["response"]["confidence"]
            
            # Step 4: Validate degradation levels
            # Each level should still work but may have lower confidence
            assert level1_confidence >= 0.3, "Level 1 degradation confidence too low"
            assert level2_confidence >= 0.2, "Level 2 degradation confidence too low"
            
            # Confidence should generally decrease with more failures
            # (though not strictly required in all cases)
            assert level2_confidence <= full_confidence * 1.1, "Degradation not reflected in confidence"
            
        finally:
            service_mock_manager.restore_states()