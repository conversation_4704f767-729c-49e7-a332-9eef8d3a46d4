"""
Complete User Journey E2E Tests

Tests the complete user experience from authentication through query processing
to WebSocket streaming and final response delivery.
"""

import pytest
import asyncio
import json
import time
from typing import Dict, List, Any
from unittest.mock import patch, AsyncMock

from .fixtures import (
    auth_headers, websocket_auth_headers, test_queries, 
    expected_response_patterns, sample_query_request
)
from .utils.websocket_client import WebSocketTestClient, WebSocketEventValidator
from .utils.service_health import ServiceHealthChecker


class TestCompleteUserJourney:
    """Complete user journey tests"""
    
    @pytest.fixture
    def service_base_url(self):
        """Base URL for the service"""
        return "http://localhost:8000"
    
    @pytest.fixture
    def websocket_base_url(self):
        """WebSocket base URL"""
        return "ws://localhost:8000"
    
    @pytest.fixture
    async def health_checker(self):
        """Health checker instance"""
        return ServiceHealthChecker(timeout=10.0)
    
    @pytest.fixture
    async def websocket_client(self, websocket_base_url):
        """WebSocket client instance"""
        client = WebSocketTestClient(websocket_base_url)
        yield client
        if client.connected:
            await client.disconnect()
    
    @pytest.mark.asyncio
    async def test_authenticated_api_query_journey(self, service_base_url, auth_headers, sample_query_request):
        """Test complete authenticated API query journey"""
        import httpx
        
        async with httpx.AsyncClient() as client:
            # Step 1: Verify service health
            health_response = await client.get(f"{service_base_url}/health")
            assert health_response.status_code == 200
            health_data = health_response.json()
            assert health_data["status"] in ["healthy", "degraded"]
            
            # Step 2: Submit query with authentication
            query_response = await client.post(
                f"{service_base_url}/api/v1/query",
                json=sample_query_request,
                headers=auth_headers
            )
            
            assert query_response.status_code == 200
            query_result = query_response.json()
            
            # Step 3: Validate response structure
            assert "query_id" in query_result
            assert "response" in query_result
            assert "metadata" in query_result
            
            # Step 4: Validate response content
            response_data = query_result["response"]
            assert "text" in response_data
            assert "confidence" in response_data
            assert len(response_data["text"]) > 0
            assert 0.0 <= response_data["confidence"] <= 1.0
            
            # Step 5: Validate metadata
            metadata = query_result["metadata"]
            assert "intent" in metadata
            assert "processing_time_ms" in metadata
            assert "references" in metadata
            assert isinstance(metadata["references"], list)
            
            # Step 6: Validate follow-up questions
            if "follow_up_questions" in metadata:
                assert isinstance(metadata["follow_up_questions"], list)
                assert len(metadata["follow_up_questions"]) <= 5
    
    @pytest.mark.asyncio
    async def test_websocket_streaming_journey(self, websocket_client, websocket_auth_headers, test_queries):
        """Test complete WebSocket streaming journey"""
        # Step 1: Connect with authentication
        connected = await websocket_client.connect(websocket_auth_headers)
        assert connected, f"Failed to connect: {websocket_client.connection_errors}"
        
        # Step 2: Send query and receive events
        query = test_queries["explanation"][0]
        events = await websocket_client.query_and_receive(query)
        
        # Step 3: Validate event sequence
        expected_events = [
            "acknowledged",
            "processing_started", 
            "intent_analyzed",
            "search_complete",
            "reference",
            "text",
            "done"
        ]
        
        validator = WebSocketEventValidator(expected_events)
        validation_result = validator.validate_event_sequence(events)
        
        assert validation_result["passed"], f"Event sequence validation failed: {validation_result}"
        
        # Step 4: Validate content quality
        content_validation = validator.validate_content_quality(events)
        assert content_validation["passed"], f"Content quality validation failed: {content_validation}"
        
        # Step 5: Validate text content
        text_content = websocket_client.get_text_content()
        assert len(text_content) > 50, "Response text too short"
        
        # Step 6: Validate references
        references = websocket_client.get_references()
        assert len(references) > 0, "No references provided"
        
        # Step 7: Validate final metadata
        final_metadata = websocket_client.get_final_metadata()
        assert final_metadata is not None, "No final metadata"
        assert "confidence" in final_metadata
        assert "follow_up_questions" in final_metadata
    
    @pytest.mark.asyncio
    async def test_multi_query_session_journey(self, websocket_client, websocket_auth_headers, test_queries):
        """Test multi-query session journey"""
        # Step 1: Connect
        connected = await websocket_client.connect(websocket_auth_headers)
        assert connected
        
        # Step 2: Process multiple queries in sequence
        query_results = []
        
        for intent_type, queries in test_queries.items():
            if intent_type == "code_search":  # Test one intent type
                for query in queries[:2]:  # Test first 2 queries
                    websocket_client.clear_events()
                    
                    events = await websocket_client.query_and_receive(query)
                    
                    # Validate each query response
                    assert len(events) > 0, f"No events for query: {query}"
                    assert not websocket_client.has_error(), f"Error in query: {query}"
                    
                    text_content = websocket_client.get_text_content()
                    assert len(text_content) > 20, f"Insufficient response for query: {query}"
                    
                    query_results.append({
                        "query": query,
                        "events_count": len(events),
                        "text_length": len(text_content),
                        "has_references": len(websocket_client.get_references()) > 0
                    })
        
        # Step 3: Validate session consistency
        assert len(query_results) > 0, "No queries processed"
        assert all(r["has_references"] for r in query_results), "Some queries missing references"
        
        # Step 4: Validate progressive improvement (optional)
        # Later queries might have better performance due to caching
        avg_text_length = sum(r["text_length"] for r in query_results) / len(query_results)
        assert avg_text_length > 100, "Average response length too low"
    
    @pytest.mark.asyncio
    async def test_error_recovery_journey(self, websocket_client, websocket_auth_headers):
        """Test error recovery journey"""
        # Step 1: Connect
        connected = await websocket_client.connect(websocket_auth_headers)
        assert connected
        
        # Step 2: Test invalid query
        invalid_query = ""  # Empty query
        events = await websocket_client.query_and_receive(invalid_query)
        
        # Should receive error event
        assert websocket_client.has_error(), "Expected error for invalid query"
        
        # Step 3: Test recovery with valid query
        websocket_client.clear_events()
        valid_query = "How does authentication work?"
        events = await websocket_client.query_and_receive(valid_query)
        
        # Should recover and process normally
        assert not websocket_client.has_error(), "Service should recover from error"
        assert len(events) > 0, "No events after recovery"
        
        text_content = websocket_client.get_text_content()
        assert len(text_content) > 0, "No text content after recovery"
    
    @pytest.mark.asyncio
    async def test_concurrent_user_journey(self, websocket_base_url, websocket_auth_headers, test_queries):
        """Test concurrent user journey"""
        from .utils.websocket_client import ConcurrentWebSocketTester
        
        # Create concurrent tester
        concurrent_tester = ConcurrentWebSocketTester(websocket_base_url)
        
        def headers_generator(user_id: int) -> Dict[str, str]:
            # Generate unique headers for each user
            token = websocket_auth_headers["Authorization"].replace("Bearer ", "")
            return {"Authorization": f"Bearer {token}_{user_id}"}
        
        # Step 1: Test concurrent connections
        concurrent_users = 5
        queries_per_user = 3
        
        results = await concurrent_tester.test_concurrent_connections(
            concurrent_users=concurrent_users,
            queries_per_user=queries_per_user,
            headers_generator=headers_generator
        )
        
        # Step 2: Validate concurrent performance
        assert results["success_rate"] >= 80.0, f"Low success rate: {results['success_rate']}%"
        assert results["total_queries"] == concurrent_users * queries_per_user
        
        # Step 3: Validate no major errors
        error_count = len(results["errors"])
        max_allowed_errors = concurrent_users * queries_per_user * 0.1  # 10% error threshold
        assert error_count <= max_allowed_errors, f"Too many errors: {error_count}"
    
    @pytest.mark.asyncio
    async def test_performance_regression_journey(self, service_base_url, auth_headers, test_queries):
        """Test performance regression journey"""
        import httpx
        
        response_times = []
        
        async with httpx.AsyncClient() as client:
            # Step 1: Warm up the service
            warmup_request = {
                "query": "warmup query",
                "repository_id": "test_repo"
            }
            await client.post(
                f"{service_base_url}/api/v1/query",
                json=warmup_request,
                headers=auth_headers
            )
            
            # Step 2: Test performance with various queries
            for query in test_queries["code_search"][:3]:
                start_time = time.time()
                
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={"query": query, "repository_id": "test_repo"},
                    headers=auth_headers
                )
                
                end_time = time.time()
                response_time = end_time - start_time
                response_times.append(response_time)
                
                # Validate response
                assert response.status_code == 200
                result = response.json()
                assert "response" in result
                assert len(result["response"]["text"]) > 0
        
        # Step 3: Validate performance metrics
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        
        assert avg_response_time < 5.0, f"Average response time too high: {avg_response_time:.2f}s"
        assert max_response_time < 10.0, f"Maximum response time too high: {max_response_time:.2f}s"
        
        # Step 4: Performance should improve with caching
        # Repeat first query to test caching
        cached_start = time.time()
        response = await client.post(
            f"{service_base_url}/api/v1/query",
            json={"query": test_queries["code_search"][0], "repository_id": "test_repo"},
            headers=auth_headers
        )
        cached_end = time.time()
        cached_time = cached_end - cached_start
        
        # Cached response should be faster (though not guaranteed in test env)
        assert cached_time < 10.0, f"Cached response time too high: {cached_time:.2f}s"
    
    @pytest.mark.asyncio
    async def test_service_integration_journey(self, service_base_url, health_checker):
        """Test service integration journey"""
        # Step 1: Check overall system health
        service_config = {
            "query_intelligence_url": service_base_url,
            # Add other service URLs as needed
        }
        
        health_results = await health_checker.check_all_services(service_config)
        
        # Step 2: Validate primary service
        qi_health = health_results.get("query_intelligence")
        assert qi_health is not None, "Query intelligence service health check failed"
        assert qi_health.response_time_ms < 1000, f"Health check too slow: {qi_health.response_time_ms}ms"
        
        # Step 3: Get system health summary
        system_summary = health_checker.get_system_health_summary(health_results)
        
        # Step 4: Validate system is operational
        assert system_summary["overall_status"] in ["healthy", "degraded"], "System not operational"
        assert system_summary["healthy_services"] > 0, "No healthy services"
        assert system_summary["health_percentage"] >= 50.0, "System health too low"
    
    @pytest.mark.asyncio
    async def test_authentication_flow_journey(self, service_base_url, auth_headers, invalid_jwt_token):
        """Test authentication flow journey"""
        import httpx
        
        async with httpx.AsyncClient() as client:
            # Step 1: Test authenticated request
            auth_response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={"query": "test auth", "repository_id": "test"},
                headers=auth_headers
            )
            
            assert auth_response.status_code == 200, "Authenticated request failed"
            
            # Step 2: Test unauthenticated request
            unauth_response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={"query": "test unauth", "repository_id": "test"}
            )
            
            # Should be rate limited or require auth
            assert unauth_response.status_code in [401, 429], "Unauthenticated request should be blocked"
            
            # Step 3: Test invalid token
            invalid_headers = {"Authorization": f"Bearer {invalid_jwt_token}"}
            invalid_response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={"query": "test invalid", "repository_id": "test"},
                headers=invalid_headers
            )
            
            assert invalid_response.status_code == 401, "Invalid token should be rejected"
    
    @pytest.mark.asyncio
    async def test_query_optimization_journey(self, service_base_url, auth_headers):
        """Test query optimization journey"""
        import httpx
        
        async with httpx.AsyncClient() as client:
            # Step 1: Test query optimization endpoint
            optimization_request = {
                "query": "vague query",
                "repository_id": "test_repo",
                "previous_confidence": 0.5,
                "previous_results_count": 2
            }
            
            opt_response = await client.post(
                f"{service_base_url}/api/v1/query/optimize",
                json=optimization_request,
                headers=auth_headers
            )
            
            assert opt_response.status_code == 200, "Optimization request failed"
            
            # Step 2: Validate optimization response
            opt_result = opt_response.json()
            assert "optimizations" in opt_result
            assert "quality_score" in opt_result
            assert isinstance(opt_result["optimizations"], list)
            assert 0.0 <= opt_result["quality_score"] <= 1.0
            
            # Step 3: Test with optimized query if provided
            if opt_result.get("optimized_query"):
                optimized_response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": opt_result["optimized_query"],
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                assert optimized_response.status_code == 200
                optimized_result = optimized_response.json()
                
                # Optimized query should have better confidence
                optimized_confidence = optimized_result["response"]["confidence"]
                assert optimized_confidence >= 0.5, "Optimized query should have decent confidence"
    
    @pytest.mark.asyncio
    async def test_end_to_end_latency_journey(self, service_base_url, websocket_base_url, auth_headers, websocket_auth_headers):
        """Test end-to-end latency journey"""
        import httpx
        
        # Step 1: Measure API endpoint latency
        api_times = []
        
        async with httpx.AsyncClient() as client:
            for i in range(3):
                start_time = time.time()
                
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={"query": f"latency test {i}", "repository_id": "test"},
                    headers=auth_headers
                )
                
                end_time = time.time()
                api_times.append(end_time - start_time)
                
                assert response.status_code == 200
        
        # Step 2: Measure WebSocket streaming latency
        websocket_client = WebSocketTestClient(websocket_base_url)
        connected = await websocket_client.connect(websocket_auth_headers)
        assert connected
        
        ws_start_time = time.time()
        events = await websocket_client.query_and_receive("latency test websocket")
        ws_end_time = time.time()
        
        await websocket_client.disconnect()
        
        # Step 3: Validate latency metrics
        avg_api_time = sum(api_times) / len(api_times)
        ws_time = ws_end_time - ws_start_time
        
        assert avg_api_time < 10.0, f"API latency too high: {avg_api_time:.2f}s"
        assert ws_time < 15.0, f"WebSocket latency too high: {ws_time:.2f}s"
        
        # Step 4: Validate WebSocket provides streaming benefit
        assert len(events) > 5, "WebSocket should provide multiple events"
        assert any(event.type == "text" for event in events), "WebSocket should stream text"