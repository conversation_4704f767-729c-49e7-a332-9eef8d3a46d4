"""
End-to-End Testing Suite for Query Intelligence Service

This module provides comprehensive E2E testing for the query intelligence service,
covering complete user journeys, service integration, performance, and error recovery.
"""

__version__ = "1.0.0"

# Test categories
TEST_CATEGORIES = {
    "user_journey": "Complete user authentication and query processing flow",
    "service_integration": "Multi-service interaction with circuit breakers",
    "performance": "Load testing and concurrent request handling",
    "error_recovery": "Fallback mechanisms and error handling",
    "cache_behavior": "Multi-level cache validation",
    "production": "Production-like validation scenarios"
}

# Test configuration
DEFAULT_TEST_CONFIG = {
    "timeout": 30,
    "concurrent_users": 10,
    "load_test_duration": 60,
    "max_response_time": 5.0,
    "expected_success_rate": 0.95
}