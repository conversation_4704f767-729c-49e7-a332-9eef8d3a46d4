"""
Cache Behavior E2E Tests

Tests multi-level caching behavior, cache hit/miss scenarios, invalidation,
and cache performance characteristics.
"""

import pytest
import asyncio
import time
import hashlib
from typing import Dict, List, Any, Optional
from unittest.mock import patch, AsyncMock

from .fixtures import (
    mock_redis_client, cache_scenarios, auth_headers, test_queries
)


class TestCacheBehavior:
    """Cache behavior and performance tests"""
    
    @pytest.fixture
    def service_base_url(self):
        """Base URL for the service"""
        return "http://localhost:8000"
    
    @pytest.fixture
    def cache_test_queries(self):
        """Specific queries for cache testing"""
        return {
            "cache_test_1": "What is the purpose of authentication?",
            "cache_test_2": "How does the caching system work?",
            "cache_test_3": "Explain the query processing pipeline",
            "repeated_query": "This query will be repeated for cache testing",
            "complex_query": "Analyze the complete system architecture including authentication, caching, query processing, and error handling mechanisms"
        }
    
    @pytest.mark.asyncio
    async def test_cache_miss_behavior(self, service_base_url, auth_headers, cache_test_queries):
        """Test cache miss behavior and performance"""
        import httpx
        
        cache_miss_metrics = []
        
        # Step 1: Generate unique queries that should cause cache misses
        async with httpx.AsyncClient() as client:
            for i, (query_name, query) in enumerate(cache_test_queries.items()):
                # Make query unique with timestamp to ensure cache miss
                unique_query = f"{query} (timestamp: {time.time()})"
                
                start_time = time.time()
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": unique_query,
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                end_time = time.time()
                
                assert response.status_code == 200
                result = response.json()
                
                cache_miss_metrics.append({
                    "query_name": query_name,
                    "response_time": end_time - start_time,
                    "confidence": result["response"]["confidence"],
                    "text_length": len(result["response"]["text"]),
                    "has_references": len(result["metadata"].get("references", [])) > 0
                })
        
        # Step 2: Validate cache miss behavior
        avg_response_time = sum(m["response_time"] for m in cache_miss_metrics) / len(cache_miss_metrics)
        
        # Cache misses should still complete within reasonable time
        assert avg_response_time <= 10.0, f"Cache miss response time too high: {avg_response_time:.2f}s"
        
        # All cache misses should produce valid responses
        assert all(m["confidence"] >= 0.3 for m in cache_miss_metrics), "Cache miss confidence too low"
        assert all(m["text_length"] > 0 for m in cache_miss_metrics), "Cache miss produced empty response"
    
    @pytest.mark.asyncio
    async def test_cache_hit_behavior(self, service_base_url, auth_headers, cache_test_queries):
        """Test cache hit behavior and performance"""
        import httpx
        
        cache_performance_data = []
        
        # Step 1: Test cache hit performance with repeated queries
        repeated_query = cache_test_queries["repeated_query"]
        
        async with httpx.AsyncClient() as client:
            # First request - should populate cache
            first_start = time.time()
            first_response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": repeated_query,
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            first_end = time.time()
            
            assert first_response.status_code == 200
            first_result = first_response.json()
            
            cache_performance_data.append({
                "request_type": "cache_miss",
                "response_time": first_end - first_start,
                "confidence": first_result["response"]["confidence"],
                "text_length": len(first_result["response"]["text"])
            })
            
            # Subsequent requests - should hit cache
            for i in range(5):
                hit_start = time.time()
                hit_response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": repeated_query,
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                hit_end = time.time()
                
                assert hit_response.status_code == 200
                hit_result = hit_response.json()
                
                cache_performance_data.append({
                    "request_type": "cache_hit",
                    "response_time": hit_end - hit_start,
                    "confidence": hit_result["response"]["confidence"],
                    "text_length": len(hit_result["response"]["text"])
                })
                
                # Small delay between requests
                await asyncio.sleep(0.1)
        
        # Step 2: Validate cache hit behavior
        cache_miss_times = [d["response_time"] for d in cache_performance_data if d["request_type"] == "cache_miss"]
        cache_hit_times = [d["response_time"] for d in cache_performance_data if d["request_type"] == "cache_hit"]
        
        avg_miss_time = sum(cache_miss_times) / len(cache_miss_times)
        avg_hit_time = sum(cache_hit_times) / len(cache_hit_times)
        
        # Cache hits should be faster or at least not significantly slower
        assert avg_hit_time <= avg_miss_time * 1.2, \
            f"Cache hits not faster: miss={avg_miss_time:.2f}s, hit={avg_hit_time:.2f}s"
        
        # Cache hits should produce consistent results
        cache_hit_confidences = [d["confidence"] for d in cache_performance_data if d["request_type"] == "cache_hit"]
        cache_hit_lengths = [d["text_length"] for d in cache_performance_data if d["request_type"] == "cache_hit"]
        
        # Results should be consistent across cache hits
        assert len(set(cache_hit_confidences)) <= 2, "Cache hit confidences too variable"
        assert len(set(cache_hit_lengths)) <= 2, "Cache hit response lengths too variable"
    
    @pytest.mark.asyncio
    async def test_cache_invalidation_behavior(self, service_base_url, auth_headers, mock_redis_client):
        """Test cache invalidation behavior"""
        import httpx
        
        test_query = "Cache invalidation test query"
        
        # Step 1: Populate cache
        async with httpx.AsyncClient() as client:
            initial_response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": test_query,
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            
            assert initial_response.status_code == 200
            initial_result = initial_response.json()
        
        # Step 2: Simulate cache invalidation
        # This would typically be done through an admin endpoint or cache management
        # For testing, we'll simulate it by clearing the mock cache
        if hasattr(mock_redis_client, 'data'):
            mock_redis_client.data.clear()
        
        # Step 3: Test cache miss after invalidation
        async with httpx.AsyncClient() as client:
            post_invalidation_response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": test_query,
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            
            assert post_invalidation_response.status_code == 200
            post_invalidation_result = post_invalidation_response.json()
        
        # Step 4: Validate invalidation worked
        # Results should be consistent but may have slight timing differences
        assert len(post_invalidation_result["response"]["text"]) > 0
        assert post_invalidation_result["response"]["confidence"] >= 0.3
    
    @pytest.mark.asyncio
    async def test_cache_ttl_behavior(self, service_base_url, auth_headers):
        """Test cache TTL (Time To Live) behavior"""
        import httpx
        
        ttl_test_query = "Cache TTL test query"
        
        # Step 1: First request to populate cache
        async with httpx.AsyncClient() as client:
            first_start = time.time()
            first_response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": ttl_test_query,
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            first_end = time.time()
            
            assert first_response.status_code == 200
            first_time = first_end - first_start
        
        # Step 2: Immediate second request (should hit cache)
        async with httpx.AsyncClient() as client:
            second_start = time.time()
            second_response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": ttl_test_query,
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            second_end = time.time()
            
            assert second_response.status_code == 200
            second_time = second_end - second_start
        
        # Step 3: Wait for potential TTL expiration
        # In a real scenario, this would be the actual TTL duration
        await asyncio.sleep(2)
        
        # Step 4: Third request (may or may not hit cache depending on TTL)
        async with httpx.AsyncClient() as client:
            third_start = time.time()
            third_response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": ttl_test_query,
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            third_end = time.time()
            
            assert third_response.status_code == 200
            third_time = third_end - third_start
        
        # Step 5: Validate TTL behavior
        # Second request should be faster (cache hit)
        assert second_time <= first_time * 1.1, "Second request not faster (cache miss?)"
        
        # Third request timing depends on TTL configuration
        # It should still work regardless of cache status
        assert third_time <= 10.0, "Third request too slow"
    
    @pytest.mark.asyncio
    async def test_cache_key_generation(self, service_base_url, auth_headers):
        """Test cache key generation and collision avoidance"""
        import httpx
        
        # Step 1: Test queries that should have different cache keys
        similar_queries = [
            "What is authentication?",
            "What is authentication in this system?",
            "What is the authentication system?",
            "Authentication system explanation"
        ]
        
        response_times = []
        
        async with httpx.AsyncClient() as client:
            for query in similar_queries:
                start_time = time.time()
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": query,
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                end_time = time.time()
                
                assert response.status_code == 200
                response_times.append(end_time - start_time)
        
        # Step 2: Test identical queries (should hit cache)
        identical_query = "Identical query for cache testing"
        
        async with httpx.AsyncClient() as client:
            # First request
            first_start = time.time()
            first_response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": identical_query,
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            first_end = time.time()
            
            assert first_response.status_code == 200
            first_result = first_response.json()
            
            # Second identical request
            second_start = time.time()
            second_response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": identical_query,
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            second_end = time.time()
            
            assert second_response.status_code == 200
            second_result = second_response.json()
        
        # Step 3: Validate cache key behavior
        first_time = first_end - first_start
        second_time = second_end - second_start
        
        # Identical queries should produce identical results
        assert first_result["response"]["text"] == second_result["response"]["text"]
        assert first_result["response"]["confidence"] == second_result["response"]["confidence"]
        
        # Second request should be faster (cache hit)
        assert second_time <= first_time * 1.1, "Cache not working for identical queries"
    
    @pytest.mark.asyncio
    async def test_cache_concurrent_access(self, service_base_url, auth_headers):
        """Test cache behavior under concurrent access"""
        import httpx
        
        concurrent_query = "Concurrent cache access test"
        
        async def concurrent_request(request_id: int):
            """Single concurrent request"""
            async with httpx.AsyncClient() as client:
                start_time = time.time()
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": concurrent_query,
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                end_time = time.time()
                
                return {
                    "request_id": request_id,
                    "status_code": response.status_code,
                    "response_time": end_time - start_time,
                    "result": response.json() if response.status_code == 200 else None
                }
        
        # Step 1: Run concurrent requests
        concurrent_tasks = [concurrent_request(i) for i in range(10)]
        results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)
        
        # Step 2: Validate concurrent cache access
        successful_results = [r for r in results if isinstance(r, dict) and r["status_code"] == 200]
        
        assert len(successful_results) >= 8, "Too many concurrent requests failed"
        
        # All successful requests should produce consistent results
        if len(successful_results) > 1:
            first_result = successful_results[0]["result"]
            
            for result in successful_results[1:]:
                assert result["result"]["response"]["text"] == first_result["response"]["text"]
                assert result["result"]["response"]["confidence"] == first_result["response"]["confidence"]
        
        # Response times should be reasonable
        avg_response_time = sum(r["response_time"] for r in successful_results) / len(successful_results)
        assert avg_response_time <= 5.0, f"Average concurrent response time too high: {avg_response_time:.2f}s"
    
    @pytest.mark.asyncio
    async def test_cache_memory_pressure(self, service_base_url, auth_headers):
        """Test cache behavior under memory pressure"""
        import httpx
        
        # Step 1: Generate many unique queries to fill cache
        cache_fill_queries = []
        
        async with httpx.AsyncClient() as client:
            for i in range(50):  # Generate 50 unique queries
                unique_query = f"Memory pressure test query {i} with unique content {time.time()}"
                
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": unique_query,
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                cache_fill_queries.append(unique_query)
                
                # Small delay to avoid overwhelming the system
                await asyncio.sleep(0.05)
        
        # Step 2: Test cache behavior after filling
        test_query = "Cache memory pressure final test"
        
        async with httpx.AsyncClient() as client:
            # First request
            first_start = time.time()
            first_response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": test_query,
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            first_end = time.time()
            
            assert first_response.status_code == 200
            
            # Second request (should still work)
            second_start = time.time()
            second_response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": test_query,
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            second_end = time.time()
            
            assert second_response.status_code == 200
        
        # Step 3: Validate cache still works under memory pressure
        first_time = first_end - first_start
        second_time = second_end - second_start
        
        # System should still be responsive
        assert first_time <= 10.0, "System too slow under memory pressure"
        assert second_time <= 10.0, "System too slow under memory pressure"
    
    @pytest.mark.asyncio
    async def test_cache_statistics_and_monitoring(self, service_base_url, auth_headers):
        """Test cache statistics and monitoring"""
        import httpx
        
        # Step 1: Generate cache activity
        cache_activity_queries = [
            "Cache statistics test query 1",
            "Cache statistics test query 2",
            "Cache statistics test query 1",  # Repeat for cache hit
            "Cache statistics test query 3",
            "Cache statistics test query 2",  # Repeat for cache hit
        ]
        
        async with httpx.AsyncClient() as client:
            for query in cache_activity_queries:
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": query,
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                await asyncio.sleep(0.1)
        
        # Step 2: Check if cache statistics are available
        # This would depend on the implementation having a cache stats endpoint
        try:
            async with httpx.AsyncClient() as client:
                stats_response = await client.get(
                    f"{service_base_url}/cache-stats",
                    headers=auth_headers
                )
                
                if stats_response.status_code == 200:
                    stats = stats_response.json()
                    
                    # Validate cache statistics if available
                    if "cache_hits" in stats and "cache_misses" in stats:
                        assert stats["cache_hits"] >= 0
                        assert stats["cache_misses"] >= 0
                        assert stats["cache_hits"] + stats["cache_misses"] > 0
        
        except Exception:
            # Cache statistics endpoint might not be available
            pass
    
    @pytest.mark.asyncio
    async def test_cache_failover_behavior(self, service_base_url, auth_headers, mock_redis_client):
        """Test cache failover behavior when cache is unavailable"""
        import httpx
        
        # Step 1: Test with cache working
        async with httpx.AsyncClient() as client:
            working_response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": "Cache failover test with working cache",
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            
            assert working_response.status_code == 200
            working_result = working_response.json()
        
        # Step 2: Simulate cache failure
        mock_redis_client.simulate_failure()
        
        # Step 3: Test with cache failed
        async with httpx.AsyncClient() as client:
            failover_response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": "Cache failover test with failed cache",
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            
            # Should still work without cache
            assert failover_response.status_code == 200
            failover_result = failover_response.json()
            
            # Response should still be valid
            assert len(failover_result["response"]["text"]) > 0
            assert failover_result["response"]["confidence"] >= 0.3
        
        # Step 4: Restore cache and test recovery
        mock_redis_client.restore_connection()
        
        async with httpx.AsyncClient() as client:
            recovery_response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": "Cache failover test after recovery",
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            
            assert recovery_response.status_code == 200
            recovery_result = recovery_response.json()
            
            # Should work normally after recovery
            assert len(recovery_result["response"]["text"]) > 0
            assert recovery_result["response"]["confidence"] >= 0.3