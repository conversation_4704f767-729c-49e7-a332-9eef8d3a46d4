"""
Performance Scenarios E2E Tests

Tests load testing, concurrent request handling, WebSocket streaming performance,
and system behavior under various load conditions.
"""

import pytest
import asyncio
import time
import statistics
from typing import Dict, List, Any, Optional
from unittest.mock import patch, AsyncMock

from .fixtures import (
    performance_monitor, load_test_runner, websocket_load_tester,
    performance_validator, strict_performance_validator, load_test_scenarios,
    performance_scenarios, auth_headers, multiple_user_tokens
)
from .utils.performance_monitors import (
    RealTimeMetricsCollector, PerformanceThresholdMonitor, 
    LoadTestAnalyzer, ConcurrencyTester, RequestMetrics
)
from .utils.websocket_client import WebSocketSessionManager, ConcurrentWebSocketTester


class TestPerformanceScenarios:
    """Performance testing scenarios"""
    
    @pytest.fixture
    def service_base_url(self):
        """Base URL for the service"""
        return "http://localhost:8000"
    
    @pytest.fixture
    def websocket_base_url(self):
        """WebSocket base URL"""
        return "ws://localhost:8000"
    
    @pytest.fixture
    def performance_thresholds(self):
        """Performance thresholds for validation"""
        return {
            "max_avg_response_time": 3.0,
            "max_p95_response_time": 8.0,
            "min_success_rate": 95.0,
            "min_requests_per_second": 5.0,
            "max_cpu_percent": 80.0,
            "max_memory_percent": 80.0
        }
    
    @pytest.fixture
    def strict_thresholds(self):
        """Strict performance thresholds for production testing"""
        return {
            "max_avg_response_time": 1.5,
            "max_p95_response_time": 4.0,
            "min_success_rate": 99.0,
            "min_requests_per_second": 20.0,
            "max_cpu_percent": 60.0,
            "max_memory_percent": 60.0
        }
    
    @pytest.mark.asyncio
    async def test_concurrent_api_load(self, service_base_url, auth_headers, load_test_runner, performance_thresholds):
        """Test concurrent API load performance"""
        import httpx
        
        async def api_request_func(user_id: int = 0, request_num: int = 0):
            """Single API request function"""
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": f"Performance test query {request_num} from user {user_id}",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                if response.status_code != 200:
                    raise Exception(f"HTTP {response.status_code}: {response.text}")
                
                return response.json()
        
        # Step 1: Run concurrent load test
        concurrent_users = 25
        requests_per_user = 4
        
        metrics = await load_test_runner.run_concurrent_requests(
            request_func=api_request_func,
            concurrent_users=concurrent_users,
            requests_per_user=requests_per_user,
            delay_between_requests=0.1
        )
        
        # Step 2: Validate performance metrics
        assert metrics.success_rate >= performance_thresholds["min_success_rate"], \
            f"Success rate too low: {metrics.success_rate:.1f}%"
        
        assert metrics.average_response_time <= performance_thresholds["max_avg_response_time"], \
            f"Average response time too high: {metrics.average_response_time:.2f}s"
        
        assert metrics.p95_response_time <= performance_thresholds["max_p95_response_time"], \
            f"P95 response time too high: {metrics.p95_response_time:.2f}s"
        
        assert metrics.requests_per_second >= performance_thresholds["min_requests_per_second"], \
            f"Requests per second too low: {metrics.requests_per_second:.1f}"
        
        # Step 3: Validate error distribution
        error_rate = (metrics.failed_requests / metrics.total_requests) * 100
        assert error_rate <= 5.0, f"Error rate too high: {error_rate:.1f}%"
    
    @pytest.mark.asyncio
    async def test_websocket_streaming_performance(self, websocket_base_url, websocket_load_tester, auth_headers):
        """Test WebSocket streaming performance"""
        
        # Step 1: Test concurrent WebSocket connections
        concurrent_connections = 10
        messages_per_connection = 3
        
        metrics = await websocket_load_tester.test_concurrent_websockets(
            websocket_url=f"{websocket_base_url}/api/v1/ws/query",
            concurrent_connections=concurrent_connections,
            messages_per_connection=messages_per_connection,
            headers={"Authorization": auth_headers["Authorization"]}
        )
        
        # Step 2: Validate WebSocket performance
        assert metrics.success_rate >= 90.0, f"WebSocket success rate too low: {metrics.success_rate:.1f}%"
        
        assert metrics.average_response_time <= 10.0, \
            f"WebSocket average response time too high: {metrics.average_response_time:.2f}s"
        
        # Step 3: Validate streaming characteristics
        total_expected_messages = concurrent_connections * messages_per_connection
        assert metrics.total_requests >= total_expected_messages * 0.9, \
            "Not enough WebSocket messages processed"
    
    @pytest.mark.asyncio
    async def test_sustained_load_performance(self, service_base_url, auth_headers, load_test_runner):
        """Test sustained load performance"""
        import httpx
        
        async def sustained_request_func():
            """Sustained request function"""
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": "Sustained load test query",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                if response.status_code != 200:
                    raise Exception(f"HTTP {response.status_code}")
                
                return response.json()
        
        # Step 1: Run sustained load test
        requests_per_second = 5.0
        duration_seconds = 30
        
        metrics = await load_test_runner.run_sustained_load(
            request_func=sustained_request_func,
            requests_per_second=requests_per_second,
            duration_seconds=duration_seconds
        )
        
        # Step 2: Validate sustained performance
        expected_requests = requests_per_second * duration_seconds
        assert metrics.total_requests >= expected_requests * 0.9, \
            f"Too few requests processed: {metrics.total_requests}"
        
        assert metrics.success_rate >= 95.0, \
            f"Success rate degraded under sustained load: {metrics.success_rate:.1f}%"
        
        # Step 3: Validate performance stability
        response_times = metrics.response_times
        if len(response_times) >= 10:
            # Check that response times don't degrade significantly over time
            first_half = response_times[:len(response_times)//2]
            second_half = response_times[len(response_times)//2:]
            
            avg_first = statistics.mean(first_half)
            avg_second = statistics.mean(second_half)
            
            # Response time shouldn't increase by more than 50%
            assert avg_second <= avg_first * 1.5, \
                f"Performance degraded over time: {avg_first:.2f}s -> {avg_second:.2f}s"
    
    @pytest.mark.asyncio
    async def test_mixed_workload_performance(self, service_base_url, websocket_base_url, auth_headers):
        """Test mixed API and WebSocket workload performance"""
        import httpx
        from .utils.websocket_client import WebSocketTestClient
        
        # Metrics collection
        start_time = time.time()
        api_results = []
        websocket_results = []
        
        async def api_worker():
            """API worker task"""
            async with httpx.AsyncClient() as client:
                for i in range(5):
                    try:
                        request_start = time.time()
                        response = await client.post(
                            f"{service_base_url}/api/v1/query",
                            json={
                                "query": f"Mixed workload API query {i}",
                                "repository_id": "test_repo"
                            },
                            headers=auth_headers
                        )
                        request_end = time.time()
                        
                        api_results.append({
                            "success": response.status_code == 200,
                            "response_time": request_end - request_start,
                            "query_num": i
                        })
                        
                        await asyncio.sleep(0.5)
                        
                    except Exception as e:
                        api_results.append({
                            "success": False,
                            "error": str(e),
                            "query_num": i
                        })
        
        async def websocket_worker():
            """WebSocket worker task"""
            client = WebSocketTestClient(websocket_base_url)
            
            try:
                connected = await client.connect({"Authorization": auth_headers["Authorization"]})
                if connected:
                    for i in range(3):
                        try:
                            request_start = time.time()
                            events = await client.query_and_receive(
                                f"Mixed workload WebSocket query {i}"
                            )
                            request_end = time.time()
                            
                            websocket_results.append({
                                "success": len(events) > 0 and not client.has_error(),
                                "response_time": request_end - request_start,
                                "events_count": len(events),
                                "query_num": i
                            })
                            
                            await asyncio.sleep(0.5)
                            
                        except Exception as e:
                            websocket_results.append({
                                "success": False,
                                "error": str(e),
                                "query_num": i
                            })
                
                await client.disconnect()
                
            except Exception as e:
                websocket_results.append({
                    "success": False,
                    "error": f"Connection failed: {str(e)}"
                })
        
        # Step 1: Run mixed workload
        await asyncio.gather(
            api_worker(),
            websocket_worker(),
            return_exceptions=True
        )
        
        total_time = time.time() - start_time
        
        # Step 2: Validate mixed workload performance
        api_success_rate = (sum(1 for r in api_results if r["success"]) / len(api_results)) * 100
        ws_success_rate = (sum(1 for r in websocket_results if r["success"]) / len(websocket_results)) * 100
        
        assert api_success_rate >= 90.0, f"API success rate too low: {api_success_rate:.1f}%"
        assert ws_success_rate >= 90.0, f"WebSocket success rate too low: {ws_success_rate:.1f}%"
        
        # Step 3: Validate response times
        api_avg_time = statistics.mean([r["response_time"] for r in api_results if r["success"]])
        ws_avg_time = statistics.mean([r["response_time"] for r in websocket_results if r["success"]])
        
        assert api_avg_time <= 5.0, f"API average response time too high: {api_avg_time:.2f}s"
        assert ws_avg_time <= 15.0, f"WebSocket average response time too high: {ws_avg_time:.2f}s"
    
    @pytest.mark.asyncio
    async def test_resource_monitoring_during_load(self, service_base_url, auth_headers, performance_thresholds):
        """Test resource monitoring during load"""
        import httpx
        
        # Step 1: Set up metrics collection
        metrics_collector = RealTimeMetricsCollector(collection_interval=0.5)
        threshold_monitor = PerformanceThresholdMonitor(performance_thresholds)
        
        metrics_collector.start_collection()
        
        try:
            # Step 2: Generate load while monitoring
            async def load_generator():
                """Generate load for monitoring"""
                async with httpx.AsyncClient() as client:
                    for i in range(20):
                        try:
                            request_start = time.time()
                            response = await client.post(
                                f"{service_base_url}/api/v1/query",
                                json={
                                    "query": f"Resource monitoring test query {i}",
                                    "repository_id": "test_repo"
                                },
                                headers=auth_headers
                            )
                            request_end = time.time()
                            
                            # Record request metrics
                            metrics_collector.record_request(RequestMetrics(
                                request_id=f"req_{i}",
                                start_time=request_start,
                                end_time=request_end,
                                response_time=request_end - request_start,
                                status_code=response.status_code,
                                success=response.status_code == 200
                            ))
                            
                            await asyncio.sleep(0.2)
                            
                        except Exception as e:
                            metrics_collector.record_request(RequestMetrics(
                                request_id=f"req_{i}",
                                start_time=request_start,
                                end_time=time.time(),
                                response_time=time.time() - request_start,
                                status_code=500,
                                success=False,
                                error_message=str(e)
                            ))
            
            # Step 3: Run load and monitor periodically
            load_task = asyncio.create_task(load_generator())
            
            # Monitor thresholds every 2 seconds
            for _ in range(3):
                await asyncio.sleep(2.0)
                current_metrics = metrics_collector.get_current_metrics()
                violations = threshold_monitor.check_thresholds(current_metrics)
                
                # Log violations but don't fail immediately
                if violations:
                    print(f"Threshold violations: {violations}")
            
            await load_task
            
        finally:
            metrics_collector.stop_collection()
        
        # Step 4: Validate final metrics
        final_metrics = metrics_collector.get_current_metrics()
        
        # Check request metrics
        assert final_metrics["requests"]["success_rate"] >= 90.0, \
            f"Success rate too low: {final_metrics['requests']['success_rate']:.1f}%"
        
        assert final_metrics["requests"]["average_response_time"] <= 5.0, \
            f"Average response time too high: {final_metrics['requests']['average_response_time']:.2f}s"
        
        # Check system metrics
        if final_metrics["system"]["average_cpu_percent"] > 0:
            assert final_metrics["system"]["average_cpu_percent"] <= 90.0, \
                f"CPU usage too high: {final_metrics['system']['average_cpu_percent']:.1f}%"
        
        if final_metrics["system"]["average_memory_percent"] > 0:
            assert final_metrics["system"]["average_memory_percent"] <= 90.0, \
                f"Memory usage too high: {final_metrics['system']['average_memory_percent']:.1f}%"
    
    @pytest.mark.asyncio
    async def test_stress_testing_scenarios(self, service_base_url, auth_headers, multiple_user_tokens):
        """Test stress testing scenarios"""
        import httpx
        
        # Step 1: High concurrency stress test
        async def stress_request(user_token: str, request_id: int):
            """Individual stress request"""
            headers = {
                "Authorization": f"Bearer {user_token}",
                "Content-Type": "application/json"
            }
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": f"Stress test query {request_id}",
                        "repository_id": "test_repo"
                    },
                    headers=headers
                )
                
                return {
                    "request_id": request_id,
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "response_time": response.elapsed.total_seconds() if hasattr(response, 'elapsed') else 0
                }
        
        # Step 2: Run stress test with multiple users
        stress_tasks = []
        for i, token in enumerate(multiple_user_tokens[:8]):  # Use 8 users
            for j in range(5):  # 5 requests per user
                stress_tasks.append(stress_request(token, i * 5 + j))
        
        start_time = time.time()
        stress_results = await asyncio.gather(*stress_tasks, return_exceptions=True)
        end_time = time.time()
        
        # Step 3: Analyze stress test results
        successful_results = [r for r in stress_results if isinstance(r, dict) and r["success"]]
        failed_results = [r for r in stress_results if isinstance(r, Exception) or not r.get("success", False)]
        
        total_requests = len(stress_results)
        success_rate = (len(successful_results) / total_requests) * 100
        
        # Step 4: Validate stress test performance
        # Under stress, we allow lower success rates
        assert success_rate >= 75.0, f"Stress test success rate too low: {success_rate:.1f}%"
        
        # Check that the system handled the load
        requests_per_second = total_requests / (end_time - start_time)
        assert requests_per_second >= 5.0, f"Stress test throughput too low: {requests_per_second:.1f} req/s"
        
        # Validate response times under stress
        if successful_results:
            avg_response_time = statistics.mean([r.get("response_time", 0) for r in successful_results])
            assert avg_response_time <= 10.0, f"Stress test response time too high: {avg_response_time:.2f}s"
    
    @pytest.mark.asyncio
    async def test_cache_performance_impact(self, service_base_url, auth_headers):
        """Test cache performance impact"""
        import httpx
        
        # Step 1: Test cache miss scenario
        cache_miss_times = []
        
        async with httpx.AsyncClient() as client:
            for i in range(5):
                start_time = time.time()
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": f"Unique cache miss query {i} {time.time()}",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                end_time = time.time()
                
                assert response.status_code == 200
                cache_miss_times.append(end_time - start_time)
                
                await asyncio.sleep(0.1)
        
        # Step 2: Test cache hit scenario
        cache_hit_times = []
        repeated_query = "Cache hit test query"
        
        async with httpx.AsyncClient() as client:
            for i in range(5):
                start_time = time.time()
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": repeated_query,
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                end_time = time.time()
                
                assert response.status_code == 200
                cache_hit_times.append(end_time - start_time)
                
                await asyncio.sleep(0.1)
        
        # Step 3: Validate cache performance
        avg_cache_miss_time = statistics.mean(cache_miss_times)
        avg_cache_hit_time = statistics.mean(cache_hit_times)
        
        # Cache hits should be faster or at least not significantly slower
        assert avg_cache_hit_time <= avg_cache_miss_time * 1.1, \
            f"Cache not providing performance benefit: miss={avg_cache_miss_time:.2f}s, hit={avg_cache_hit_time:.2f}s"
        
        # Both should be within reasonable bounds
        assert avg_cache_miss_time <= 10.0, f"Cache miss time too high: {avg_cache_miss_time:.2f}s"
        assert avg_cache_hit_time <= 10.0, f"Cache hit time too high: {avg_cache_hit_time:.2f}s"
    
    @pytest.mark.asyncio
    async def test_performance_regression_detection(self, service_base_url, auth_headers):
        """Test performance regression detection"""
        import httpx
        
        # Step 1: Baseline performance measurement
        baseline_times = []
        
        async with httpx.AsyncClient() as client:
            for i in range(10):
                start_time = time.time()
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": f"Baseline performance test {i}",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                end_time = time.time()
                
                assert response.status_code == 200
                baseline_times.append(end_time - start_time)
                
                await asyncio.sleep(0.1)
        
        # Step 2: Performance measurement after load
        # Simulate some load first
        load_tasks = []
        async with httpx.AsyncClient() as client:
            for i in range(20):
                load_tasks.append(client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": f"Load generation query {i}",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                ))
        
        await asyncio.gather(*load_tasks, return_exceptions=True)
        
        # Step 3: Post-load performance measurement
        post_load_times = []
        
        async with httpx.AsyncClient() as client:
            for i in range(10):
                start_time = time.time()
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": f"Post-load performance test {i}",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                end_time = time.time()
                
                assert response.status_code == 200
                post_load_times.append(end_time - start_time)
                
                await asyncio.sleep(0.1)
        
        # Step 4: Validate no significant regression
        baseline_avg = statistics.mean(baseline_times)
        post_load_avg = statistics.mean(post_load_times)
        
        # Post-load performance shouldn't be more than 50% worse
        regression_threshold = baseline_avg * 1.5
        assert post_load_avg <= regression_threshold, \
            f"Performance regression detected: baseline={baseline_avg:.2f}s, post-load={post_load_avg:.2f}s"
        
        # Both should be within reasonable bounds
        assert baseline_avg <= 5.0, f"Baseline performance too slow: {baseline_avg:.2f}s"
        assert post_load_avg <= 7.5, f"Post-load performance too slow: {post_load_avg:.2f}s"
    
    @pytest.mark.asyncio
    async def test_memory_leak_detection(self, service_base_url, auth_headers):
        """Test memory leak detection during sustained load"""
        import httpx
        import psutil
        import os
        
        # Step 1: Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Step 2: Run sustained load
        async with httpx.AsyncClient() as client:
            for batch in range(5):  # 5 batches of 10 requests each
                batch_tasks = []
                
                for i in range(10):
                    batch_tasks.append(client.post(
                        f"{service_base_url}/api/v1/query",
                        json={
                            "query": f"Memory leak test batch {batch} request {i}",
                            "repository_id": "test_repo"
                        },
                        headers=auth_headers
                    ))
                
                await asyncio.gather(*batch_tasks, return_exceptions=True)
                
                # Check memory after each batch
                current_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_increase = current_memory - initial_memory
                
                # Memory increase shouldn't be excessive
                assert memory_increase <= 100, \
                    f"Excessive memory increase after batch {batch}: {memory_increase:.1f}MB"
        
        # Step 3: Final memory check
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        total_memory_increase = final_memory - initial_memory
        
        # Total memory increase should be reasonable
        assert total_memory_increase <= 150, \
            f"Potential memory leak detected: {total_memory_increase:.1f}MB increase"
    
    @pytest.mark.asyncio
    async def test_performance_under_different_query_types(self, service_base_url, auth_headers):
        """Test performance under different query types"""
        import httpx
        
        query_types = {
            "simple": "What is authentication?",
            "complex": "Explain the entire authentication flow including JWT validation, rate limiting, circuit breakers, and error handling mechanisms",
            "code_search": "Find all functions that handle user authentication and authorization",
            "analytical": "Analyze the performance bottlenecks in the query processing pipeline and suggest optimizations"
        }
        
        performance_results = {}
        
        async with httpx.AsyncClient() as client:
            for query_type, query in query_types.items():
                # Test each query type multiple times
                response_times = []
                
                for i in range(5):
                    start_time = time.time()
                    response = await client.post(
                        f"{service_base_url}/api/v1/query",
                        json={
                            "query": f"{query} (iteration {i})",
                            "repository_id": "test_repo"
                        },
                        headers=auth_headers
                    )
                    end_time = time.time()
                    
                    assert response.status_code == 200
                    response_times.append(end_time - start_time)
                    
                    await asyncio.sleep(0.1)
                
                performance_results[query_type] = {
                    "avg_response_time": statistics.mean(response_times),
                    "min_response_time": min(response_times),
                    "max_response_time": max(response_times),
                    "response_times": response_times
                }
        
        # Validate performance characteristics
        for query_type, results in performance_results.items():
            avg_time = results["avg_response_time"]
            max_time = results["max_response_time"]
            
            # All query types should complete within reasonable time
            assert avg_time <= 8.0, f"{query_type} queries too slow: {avg_time:.2f}s average"
            assert max_time <= 15.0, f"{query_type} queries too slow: {max_time:.2f}s maximum"
        
        # Complex queries may be slower, but not excessively so
        simple_avg = performance_results["simple"]["avg_response_time"]
        complex_avg = performance_results["complex"]["avg_response_time"]
        
        # Complex queries shouldn't be more than 3x slower than simple ones
        assert complex_avg <= simple_avg * 3.0, \
            f"Complex queries too much slower: simple={simple_avg:.2f}s, complex={complex_avg:.2f}s"