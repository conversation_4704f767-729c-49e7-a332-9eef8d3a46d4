"""
Service Integration E2E Tests

Tests multi-service integration with circuit breaker validation,
service dependencies, and fallback mechanisms.
"""

import pytest
import asyncio
import time
from typing import Dict, List, Any
from unittest.mock import patch, AsyncMock

from .fixtures import (
    all_services_healthy, all_services_degraded, 
    service_mock_manager, circuit_breaker_scenarios,
    auth_headers, test_queries
)
from .utils.service_health import (
    ServiceHealthChecker, ServiceDependencyValidator, 
    CircuitBreakerTester, ServiceStatus
)


class TestServiceIntegration:
    """Multi-service integration tests"""
    
    @pytest.fixture
    def service_base_url(self):
        """Base URL for the service"""
        return "http://localhost:8000"
    
    @pytest.fixture
    def service_config(self, service_base_url):
        """Service configuration for testing"""
        return {
            "query_intelligence_url": service_base_url,
            "redis_url": "redis://localhost:6379",
            "analysis_engine_url": "http://localhost:8001",
            "pattern_mining_url": "http://localhost:8002"
        }
    
    @pytest.fixture
    async def health_checker(self):
        """Health checker instance"""
        return ServiceHealthChecker(timeout=5.0)
    
    @pytest.fixture
    async def dependency_validator(self, health_checker):
        """Dependency validator instance"""
        return ServiceDependencyValidator(health_checker)
    
    @pytest.fixture
    async def circuit_breaker_tester(self, service_base_url):
        """Circuit breaker tester instance"""
        return CircuitBreakerTester(service_base_url)
    
    @pytest.mark.asyncio
    async def test_all_services_healthy_integration(self, service_config, health_checker, auth_headers):
        """Test integration when all services are healthy"""
        import httpx
        
        # Step 1: Check all services are healthy
        health_results = await health_checker.check_all_services(service_config)
        system_summary = health_checker.get_system_health_summary(health_results)
        
        # Should have high health percentage
        assert system_summary["health_percentage"] >= 80.0, "System health too low"
        
        # Step 2: Test query processing with all services healthy
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{service_config['query_intelligence_url']}/api/v1/query",
                json={
                    "query": "How does the authentication system work?",
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            
            assert response.status_code == 200
            result = response.json()
            
            # Should have high confidence with all services working
            assert result["response"]["confidence"] >= 0.7, "Low confidence with healthy services"
            assert len(result["metadata"]["references"]) > 0, "No references with healthy services"
    
    @pytest.mark.asyncio
    async def test_redis_service_integration(self, service_config, health_checker, auth_headers):
        """Test Redis service integration and caching behavior"""
        import httpx
        
        # Step 1: Check Redis health
        redis_health = await health_checker.check_redis_service(service_config["redis_url"])
        
        if redis_health.status == ServiceStatus.HEALTHY:
            # Step 2: Test cache behavior with Redis healthy
            async with httpx.AsyncClient() as client:
                query_request = {
                    "query": "cache test query",
                    "repository_id": "test_repo"
                }
                
                # First request - should populate cache
                start_time = time.time()
                response1 = await client.post(
                    f"{service_config['query_intelligence_url']}/api/v1/query",
                    json=query_request,
                    headers=auth_headers
                )
                first_time = time.time() - start_time
                
                assert response1.status_code == 200
                
                # Second request - should use cache
                start_time = time.time()
                response2 = await client.post(
                    f"{service_config['query_intelligence_url']}/api/v1/query",
                    json=query_request,
                    headers=auth_headers
                )
                second_time = time.time() - start_time
                
                assert response2.status_code == 200
                
                # Cached response should be faster (though not guaranteed in test env)
                assert second_time <= first_time * 1.2, "Cache not providing performance benefit"
        
        else:
            # Step 3: Test graceful degradation without Redis
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_config['query_intelligence_url']}/api/v1/query",
                    json={
                        "query": "test without redis",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                # Should still work without Redis
                assert response.status_code == 200
                result = response.json()
                assert len(result["response"]["text"]) > 0
    
    @pytest.mark.asyncio
    async def test_analysis_engine_integration(self, service_config, health_checker, auth_headers):
        """Test Analysis Engine service integration"""
        import httpx
        
        # Step 1: Check Analysis Engine health
        analysis_health = await health_checker.check_analysis_engine_service(
            service_config["analysis_engine_url"]
        )
        
        # Step 2: Test query processing with analysis context
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{service_config['query_intelligence_url']}/api/v1/query",
                json={
                    "query": "Analyze the code quality of this function",
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            
            if analysis_health.status == ServiceStatus.HEALTHY:
                # Should have enhanced analysis
                assert response.status_code == 200
                result = response.json()
                
                # Should have analysis-related content
                response_text = result["response"]["text"].lower()
                analysis_keywords = ["analysis", "quality", "code", "function"]
                assert any(keyword in response_text for keyword in analysis_keywords)
            
            else:
                # Should still work without analysis engine
                assert response.status_code == 200
                result = response.json()
                assert len(result["response"]["text"]) > 0
    
    @pytest.mark.asyncio
    async def test_pattern_mining_integration(self, service_config, health_checker, auth_headers):
        """Test Pattern Mining service integration"""
        import httpx
        
        # Step 1: Check Pattern Mining health
        pattern_health = await health_checker.check_pattern_mining_service(
            service_config["pattern_mining_url"]
        )
        
        # Step 2: Test query processing with pattern context
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{service_config['query_intelligence_url']}/api/v1/query",
                json={
                    "query": "Find design patterns in this codebase",
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            
            if pattern_health.status == ServiceStatus.HEALTHY:
                # Should have pattern-related content
                assert response.status_code == 200
                result = response.json()
                
                response_text = result["response"]["text"].lower()
                pattern_keywords = ["pattern", "design", "implementation", "structure"]
                assert any(keyword in response_text for keyword in pattern_keywords)
            
            else:
                # Should still work without pattern mining
                assert response.status_code == 200
                result = response.json()
                assert len(result["response"]["text"]) > 0
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_functionality(self, circuit_breaker_tester, auth_headers):
        """Test circuit breaker functionality"""
        
        # Step 1: Test circuit breaker status endpoint
        breaker_results = await circuit_breaker_tester.test_circuit_breaker_behavior(auth_headers)
        
        assert breaker_results["circuit_breaker_functional"], "Circuit breaker not functional"
        
        # Step 2: Validate circuit breaker tests
        status_test = next(
            (test for test in breaker_results["tests"] if test["test"] == "circuit_breaker_status"),
            None
        )
        
        if status_test and status_test["success"]:
            breaker_data = status_test["data"]
            assert "circuit_breakers" in breaker_data
            assert isinstance(breaker_data["circuit_breakers"], dict)
        
        # Step 3: Test normal query processing
        normal_test = next(
            (test for test in breaker_results["tests"] if test["test"] == "normal_query"),
            None
        )
        
        if normal_test:
            assert normal_test["success"], "Normal query should succeed"
    
    @pytest.mark.asyncio
    async def test_service_dependency_validation(self, dependency_validator, service_config, auth_headers):
        """Test service dependency validation"""
        
        # Step 1: Validate query processing pipeline
        pipeline_validation = await dependency_validator.validate_query_processing_pipeline(service_config)
        
        # Should have successful validations
        assert len(pipeline_validation["validations"]) > 0, "No service validations"
        
        # Primary service should be healthy
        qi_validation = next(
            (v for v in pipeline_validation["validations"] if v["service_name"] == "query_intelligence"),
            None
        )
        
        if qi_validation:
            assert qi_validation["status"] in ["healthy", "degraded"], "Primary service not operational"
        
        # Step 2: Validate WebSocket capabilities
        websocket_validation = await dependency_validator.validate_websocket_capabilities(
            service_config["query_intelligence_url"],
            auth_headers
        )
        
        assert websocket_validation["websocket_healthy"], "WebSocket not healthy"
        assert websocket_validation["connection_successful"], "WebSocket connection failed"
        
        # Step 3: Validate authentication integration
        auth_validation = await dependency_validator.validate_authentication_integration(
            service_config["query_intelligence_url"],
            auth_headers
        )
        
        assert auth_validation["auth_healthy"], "Authentication not healthy"
    
    @pytest.mark.asyncio
    async def test_service_failure_scenarios(self, service_config, auth_headers, service_mock_manager):
        """Test service failure scenarios"""
        import httpx
        
        # Save original states
        service_mock_manager.save_states()
        
        try:
            # Step 1: Test Redis failure
            service_mock_manager.fail_service("redis")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_config['query_intelligence_url']}/api/v1/query",
                    json={
                        "query": "test with redis failure",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                # Should still work without Redis
                assert response.status_code == 200
                result = response.json()
                assert len(result["response"]["text"]) > 0
            
            # Step 2: Test analysis engine failure
            service_mock_manager.fail_service("analysis_engine")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_config['query_intelligence_url']}/api/v1/query",
                    json={
                        "query": "analyze code quality",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                # Should still work without analysis engine
                assert response.status_code == 200
                result = response.json()
                assert len(result["response"]["text"]) > 0
            
            # Step 3: Test multiple service failures
            service_mock_manager.fail_service("pattern_mining")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_config['query_intelligence_url']}/api/v1/query",
                    json={
                        "query": "find patterns",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                # Should still provide basic functionality
                assert response.status_code == 200
                result = response.json()
                assert len(result["response"]["text"]) > 0
        
        finally:
            # Restore original states
            service_mock_manager.restore_states()
    
    @pytest.mark.asyncio
    async def test_service_recovery_behavior(self, service_config, auth_headers, service_mock_manager):
        """Test service recovery behavior"""
        import httpx
        
        # Save original states
        service_mock_manager.save_states()
        
        try:
            # Step 1: Simulate service failure
            service_mock_manager.fail_service("redis")
            
            # Step 2: Test degraded performance
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_config['query_intelligence_url']}/api/v1/query",
                    json={
                        "query": "test degraded performance",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                degraded_result = response.json()
            
            # Step 3: Restore service
            service_mock_manager.restore_service("redis")
            
            # Step 4: Test recovery
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_config['query_intelligence_url']}/api/v1/query",
                    json={
                        "query": "test recovery performance",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                recovered_result = response.json()
            
            # Step 5: Validate recovery
            assert len(recovered_result["response"]["text"]) > 0
            # Performance should be restored (though not guaranteed in test env)
            assert recovered_result["response"]["confidence"] >= 0.5
        
        finally:
            # Restore original states
            service_mock_manager.restore_states()
    
    @pytest.mark.asyncio
    async def test_concurrent_service_integration(self, service_config, auth_headers, test_queries):
        """Test concurrent service integration"""
        import httpx
        
        async def single_query_task(query: str, task_id: int):
            """Single query task for concurrent testing"""
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_config['query_intelligence_url']}/api/v1/query",
                    json={
                        "query": f"{query} (task {task_id})",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                return {
                    "task_id": task_id,
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "response_length": len(response.json().get("response", {}).get("text", "")) if response.status_code == 200 else 0
                }
        
        # Step 1: Run concurrent queries
        concurrent_tasks = []
        for i, query in enumerate(test_queries["code_search"][:5]):
            concurrent_tasks.append(single_query_task(query, i))
        
        results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)
        
        # Step 2: Validate concurrent performance
        successful_results = [r for r in results if isinstance(r, dict) and r["success"]]
        failed_results = [r for r in results if isinstance(r, Exception) or not r.get("success", False)]
        
        success_rate = len(successful_results) / len(results) * 100
        assert success_rate >= 80.0, f"Low concurrent success rate: {success_rate}%"
        
        # Step 3: Validate response quality
        avg_response_length = sum(r["response_length"] for r in successful_results) / len(successful_results)
        assert avg_response_length > 50, "Average response length too low in concurrent test"
    
    @pytest.mark.asyncio
    async def test_cross_service_data_flow(self, service_config, auth_headers):
        """Test cross-service data flow"""
        import httpx
        
        # Step 1: Test query that requires multiple services
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{service_config['query_intelligence_url']}/api/v1/query",
                json={
                    "query": "Analyze the design patterns and code quality in the authentication system",
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            
            assert response.status_code == 200
            result = response.json()
            
            # Should have comprehensive response
            response_text = result["response"]["text"].lower()
            
            # Should contain elements from multiple services
            analysis_keywords = ["quality", "analysis", "code"]
            pattern_keywords = ["pattern", "design", "structure"]
            auth_keywords = ["authentication", "auth", "security"]
            
            has_analysis = any(keyword in response_text for keyword in analysis_keywords)
            has_patterns = any(keyword in response_text for keyword in pattern_keywords)
            has_auth = any(keyword in response_text for keyword in auth_keywords)
            
            # Should have content from multiple domains
            content_domains = sum([has_analysis, has_patterns, has_auth])
            assert content_domains >= 2, "Response should integrate multiple service domains"
    
    @pytest.mark.asyncio
    async def test_service_timeout_handling(self, service_config, auth_headers):
        """Test service timeout handling"""
        import httpx
        
        # Step 1: Test with short timeout
        async with httpx.AsyncClient(timeout=2.0) as client:
            try:
                response = await client.post(
                    f"{service_config['query_intelligence_url']}/api/v1/query",
                    json={
                        "query": "complex query that might timeout",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                # Should either succeed or handle timeout gracefully
                assert response.status_code in [200, 408, 500], "Unexpected status code for timeout test"
                
                if response.status_code == 200:
                    result = response.json()
                    assert "response" in result
                
            except httpx.TimeoutException:
                # Timeout is acceptable for this test
                pass
    
    @pytest.mark.asyncio
    async def test_service_health_monitoring(self, service_config, health_checker):
        """Test service health monitoring over time"""
        
        # Step 1: Monitor health over multiple checks
        health_snapshots = []
        
        for i in range(3):
            health_results = await health_checker.check_all_services(service_config)
            system_summary = health_checker.get_system_health_summary(health_results)
            
            health_snapshots.append({
                "timestamp": time.time(),
                "health_percentage": system_summary["health_percentage"],
                "healthy_services": system_summary["healthy_services"],
                "total_services": system_summary["total_services"]
            })
            
            if i < 2:  # Don't sleep on last iteration
                await asyncio.sleep(1)
        
        # Step 2: Validate health consistency
        health_percentages = [s["health_percentage"] for s in health_snapshots]
        avg_health = sum(health_percentages) / len(health_percentages)
        
        assert avg_health >= 50.0, "Average health percentage too low"
        
        # Health should be relatively stable
        health_variance = max(health_percentages) - min(health_percentages)
        assert health_variance <= 50.0, "Health too unstable"