"""
E2E Test Configuration

Provides configuration, fixtures, and utilities for E2E testing.
"""

import pytest
import asyncio
import os
from typing import Dict, Any, Optional
from unittest.mock import Mock, AsyncMock, patch

# Configure pytest-asyncio
pytest_plugins = ["pytest_asyncio"]

# Test environment configuration
TEST_CONFIG = {
    "service_base_url": os.getenv("E2E_SERVICE_URL", "http://localhost:8000"),
    "websocket_base_url": os.getenv("E2E_WEBSOCKET_URL", "ws://localhost:8000"),
    "redis_url": os.getenv("E2E_REDIS_URL", "redis://localhost:6379"),
    "analysis_engine_url": os.getenv("E2E_ANALYSIS_ENGINE_URL", "http://localhost:8001"),
    "pattern_mining_url": os.getenv("E2E_PATTERN_MINING_URL", "http://localhost:8002"),
    "jwt_secret": os.getenv("E2E_JWT_SECRET", "test_secret_key_for_e2e_testing"),
    "enable_mocks": os.getenv("E2E_ENABLE_MOCKS", "true").lower() == "true",
    "test_timeout": int(os.getenv("E2E_TEST_TIMEOUT", "300")),
    "concurrent_users": int(os.getenv("E2E_CONCURRENT_USERS", "10")),
}


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def test_config():
    """Test configuration"""
    return TEST_CONFIG


@pytest.fixture(scope="session")
def service_urls(test_config):
    """Service URLs for testing"""
    return {
        "service_base_url": test_config["service_base_url"],
        "websocket_base_url": test_config["websocket_base_url"],
        "redis_url": test_config["redis_url"],
        "analysis_engine_url": test_config["analysis_engine_url"],
        "pattern_mining_url": test_config["pattern_mining_url"]
    }


@pytest.fixture(autouse=True)
def setup_test_environment(test_config):
    """Setup test environment"""
    # Set up any necessary environment variables
    os.environ["JWT_SECRET"] = test_config["jwt_secret"]
    os.environ["TESTING"] = "true"
    
    # Configure logging for tests
    import logging
    logging.basicConfig(level=logging.INFO)
    
    yield
    
    # Cleanup after test
    if "TESTING" in os.environ:
        del os.environ["TESTING"]


@pytest.fixture
def mock_external_services(test_config):
    """Mock external services if enabled"""
    if not test_config["enable_mocks"]:
        yield None
        return
    
    # Mock external service calls
    with patch("httpx.AsyncClient") as mock_client:
        # Configure mock responses
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "healthy"}
        mock_response.text = "OK"
        
        mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
        mock_client.return_value.__aenter__.return_value.post.return_value = mock_response
        
        yield mock_client


@pytest.fixture
def performance_test_config():
    """Configuration for performance tests"""
    return {
        "concurrent_users": TEST_CONFIG["concurrent_users"],
        "requests_per_user": 5,
        "max_response_time": 10.0,
        "min_success_rate": 90.0,
        "load_test_duration": 30,
        "stress_test_users": 50
    }


@pytest.fixture
def cache_test_config():
    """Configuration for cache tests"""
    return {
        "cache_ttl": 300,
        "max_cache_size": 1000,
        "cache_hit_threshold": 0.8,
        "cache_miss_tolerance": 0.2
    }


@pytest.fixture
def error_recovery_config():
    """Configuration for error recovery tests"""
    return {
        "max_error_rate": 5.0,
        "recovery_timeout": 30,
        "circuit_breaker_threshold": 10,
        "fallback_timeout": 5.0
    }


@pytest.fixture
def production_test_config():
    """Configuration for production tests"""
    return {
        "sustained_load_duration": 60,
        "peak_load_multiplier": 2.0,
        "acceptable_error_rate": 1.0,
        "max_response_time": 5.0,
        "max_memory_usage": 2048,
        "max_cpu_usage": 80.0,
        "min_availability": 99.0
    }


@pytest.fixture
async def cleanup_test_data():
    """Cleanup test data after tests"""
    # Store cleanup functions
    cleanup_functions = []
    
    def register_cleanup(func):
        """Register a cleanup function"""
        cleanup_functions.append(func)
    
    yield register_cleanup
    
    # Run cleanup functions
    for cleanup_func in cleanup_functions:
        try:
            if asyncio.iscoroutinefunction(cleanup_func):
                await cleanup_func()
            else:
                cleanup_func()
        except Exception as e:
            print(f"Cleanup error: {e}")


@pytest.fixture
def test_markers():
    """Test markers for categorizing tests"""
    return {
        "e2e": "End-to-end integration tests",
        "user_journey": "Complete user journey tests",
        "service_integration": "Multi-service integration tests",
        "performance": "Performance and load testing",
        "error_recovery": "Error recovery and fallback tests",
        "cache_behavior": "Cache behavior validation tests",
        "production": "Production-like scenarios",
        "slow": "Slow running tests",
        "websocket": "WebSocket functionality tests",
        "concurrent": "Concurrent execution tests"
    }


# Custom pytest markers
def pytest_configure(config):
    """Configure pytest with custom markers"""
    markers = [
        "e2e: End-to-end integration tests",
        "user_journey: Complete user journey tests",
        "service_integration: Multi-service integration tests",
        "performance: Performance and load testing",
        "error_recovery: Error recovery and fallback tests",
        "cache_behavior: Cache behavior validation tests",
        "production: Production-like scenarios",
        "slow: Slow running tests",
        "websocket: WebSocket functionality tests",
        "concurrent: Concurrent execution tests"
    ]
    
    for marker in markers:
        config.addinivalue_line("markers", marker)


# Test collection hooks
def pytest_collection_modifyitems(config, items):
    """Modify test items during collection"""
    # Add markers based on test module and function names
    for item in items:
        # Add e2e marker to all tests in e2e directory
        if "e2e" in str(item.fspath):
            item.add_marker(pytest.mark.e2e)
        
        # Add specific markers based on test names
        if "journey" in item.name:
            item.add_marker(pytest.mark.user_journey)
        
        if "integration" in item.name:
            item.add_marker(pytest.mark.service_integration)
        
        if "performance" in item.name or "load" in item.name or "concurrent" in item.name:
            item.add_marker(pytest.mark.performance)
            item.add_marker(pytest.mark.slow)
        
        if "error" in item.name or "recovery" in item.name or "fallback" in item.name:
            item.add_marker(pytest.mark.error_recovery)
        
        if "cache" in item.name:
            item.add_marker(pytest.mark.cache_behavior)
        
        if "production" in item.name:
            item.add_marker(pytest.mark.production)
            item.add_marker(pytest.mark.slow)
        
        if "websocket" in item.name or "ws" in item.name:
            item.add_marker(pytest.mark.websocket)
        
        if "concurrent" in item.name or "parallel" in item.name:
            item.add_marker(pytest.mark.concurrent)


# Test reporting hooks
def pytest_runtest_setup(item):
    """Setup for each test item"""
    # Log test start
    print(f"\\n🧪 Starting test: {item.name}")


def pytest_runtest_teardown(item, nextitem):
    """Teardown for each test item"""
    # Log test completion
    print(f"✅ Completed test: {item.name}")


# Test result hooks
def pytest_runtest_logreport(report):
    """Log test reports"""
    if report.when == "call":
        if report.outcome == "passed":
            print(f"✅ {report.nodeid} - PASSED")
        elif report.outcome == "failed":
            print(f"❌ {report.nodeid} - FAILED")
            if hasattr(report, 'longrepr'):
                print(f"Error: {report.longrepr}")
        elif report.outcome == "skipped":
            print(f"⏭️  {report.nodeid} - SKIPPED")


# Session hooks
def pytest_sessionstart(session):
    """Session start hook"""
    print("\\n🚀 Starting E2E Test Suite")
    print(f"Service URL: {TEST_CONFIG['service_base_url']}")
    print(f"WebSocket URL: {TEST_CONFIG['websocket_base_url']}")
    print(f"Mocks Enabled: {TEST_CONFIG['enable_mocks']}")
    print(f"Test Timeout: {TEST_CONFIG['test_timeout']}s")
    print("-" * 60)


def pytest_sessionfinish(session, exitstatus):
    """Session finish hook"""
    print("\\n" + "=" * 60)
    print("🏁 E2E Test Suite Complete")
    
    # Print summary
    if exitstatus == 0:
        print("✅ All tests passed successfully!")
    else:
        print("❌ Some tests failed. Check the output above.")
    
    print("=" * 60)


# Async test utilities
@pytest.fixture
async def async_test_client():
    """Async HTTP client for testing"""
    import httpx
    
    async with httpx.AsyncClient(timeout=TEST_CONFIG["test_timeout"]) as client:
        yield client


@pytest.fixture
def test_data_generator():
    """Test data generator utility"""
    from .fixtures.test_data import TestDataGenerator
    return TestDataGenerator()


@pytest.fixture
def performance_validator():
    """Performance validation utility"""
    from .fixtures.performance_fixtures import PerformanceValidator
    return PerformanceValidator({
        "min_success_rate": 95.0,
        "max_avg_response_time": 5.0,
        "max_p95_response_time": 10.0,
        "min_requests_per_second": 5.0
    })


# Service health check fixture
@pytest.fixture
async def service_health_check(service_urls):
    """Check service health before running tests"""
    import httpx
    
    health_results = {}
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        for service_name, url in service_urls.items():
            if service_name in ["service_base_url"]:
                try:
                    response = await client.get(f"{url}/health")
                    health_results[service_name] = {
                        "status": response.status_code,
                        "healthy": response.status_code == 200
                    }
                except Exception as e:
                    health_results[service_name] = {
                        "status": "error",
                        "error": str(e),
                        "healthy": False
                    }
    
    # Log health check results
    print("\\n🔍 Service Health Check:")
    for service, result in health_results.items():
        status = "✅ Healthy" if result["healthy"] else "❌ Unhealthy"
        print(f"  {service}: {status}")
    
    return health_results