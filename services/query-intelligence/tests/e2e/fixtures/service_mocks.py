"""
Service Mocks for E2E Testing

Provides mock implementations of external services for testing isolation
and controlled failure scenarios.
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, List, Optional, Any
from datetime import datetime
import httpx
import redis


class MockRedisClient:
    """Mock Redis client for testing cache behavior"""
    
    def __init__(self):
        self.data = {}
        self.connection_healthy = True
        self.latency_ms = 1
        
    async def get(self, key: str) -> Optional[bytes]:
        """Mock get operation"""
        if not self.connection_healthy:
            raise redis.ConnectionError("Redis connection failed")
        
        await asyncio.sleep(self.latency_ms / 1000)
        return self.data.get(key, None)
    
    async def set(self, key: str, value: bytes, ex: int = None) -> bool:
        """Mock set operation"""
        if not self.connection_healthy:
            raise redis.ConnectionError("Redis connection failed")
        
        await asyncio.sleep(self.latency_ms / 1000)
        self.data[key] = value
        return True
    
    async def delete(self, key: str) -> int:
        """Mock delete operation"""
        if not self.connection_healthy:
            raise redis.ConnectionError("Redis connection failed")
        
        if key in self.data:
            del self.data[key]
            return 1
        return 0
    
    async def ping(self) -> bool:
        """Mock ping operation"""
        if not self.connection_healthy:
            raise redis.ConnectionError("Redis connection failed")
        return True
    
    async def aclose(self):
        """Mock close operation"""
        pass
    
    def simulate_failure(self):
        """Simulate Redis connection failure"""
        self.connection_healthy = False
    
    def restore_connection(self):
        """Restore Redis connection"""
        self.connection_healthy = True
    
    def set_latency(self, latency_ms: int):
        """Set artificial latency for testing"""
        self.latency_ms = latency_ms


class MockAnalysisEngineClient:
    """Mock Analysis Engine client for testing"""
    
    def __init__(self):
        self.healthy = True
        self.response_time_ms = 100
        self.failure_rate = 0.0
        self.responses = {
            "analyze": {
                "analysis_id": "test_analysis_123",
                "language": "python",
                "complexity": 0.7,
                "quality_score": 0.85,
                "patterns": ["design_pattern", "optimization_opportunity"],
                "suggestions": ["Consider using async/await", "Add error handling"]
            }
        }
    
    async def health_check(self) -> bool:
        """Mock health check"""
        if not self.healthy:
            raise httpx.ConnectError("Analysis engine connection failed")
        return True
    
    async def analyze_code(self, code: str, language: str) -> Dict[str, Any]:
        """Mock code analysis"""
        if not self.healthy:
            raise httpx.ConnectError("Analysis engine connection failed")
        
        if self.failure_rate > 0:
            import random
            if random.random() < self.failure_rate:
                raise httpx.TimeoutException("Analysis engine timeout")
        
        await asyncio.sleep(self.response_time_ms / 1000)
        return self.responses["analyze"]
    
    async def close(self):
        """Mock close operation"""
        pass
    
    def simulate_failure(self):
        """Simulate service failure"""
        self.healthy = False
    
    def restore_service(self):
        """Restore service health"""
        self.healthy = True
    
    def set_failure_rate(self, rate: float):
        """Set failure rate for testing"""
        self.failure_rate = rate
    
    def set_response_time(self, ms: int):
        """Set response time for testing"""
        self.response_time_ms = ms


class MockPatternMiningClient:
    """Mock Pattern Mining client for testing"""
    
    def __init__(self):
        self.healthy = True
        self.response_time_ms = 150
        self.patterns = [
            {
                "pattern_id": "pattern_1",
                "type": "design_pattern",
                "confidence": 0.9,
                "description": "Observer pattern implementation"
            },
            {
                "pattern_id": "pattern_2",
                "type": "anti_pattern",
                "confidence": 0.8,
                "description": "God object anti-pattern"
            }
        ]
    
    async def health_check(self) -> bool:
        """Mock health check"""
        if not self.healthy:
            raise httpx.ConnectError("Pattern mining connection failed")
        return True
    
    async def find_patterns(self, code: str, repository_id: str) -> List[Dict[str, Any]]:
        """Mock pattern finding"""
        if not self.healthy:
            raise httpx.ConnectError("Pattern mining connection failed")
        
        await asyncio.sleep(self.response_time_ms / 1000)
        return self.patterns
    
    async def close(self):
        """Mock close operation"""
        pass
    
    def simulate_failure(self):
        """Simulate service failure"""
        self.healthy = False
    
    def restore_service(self):
        """Restore service health"""
        self.healthy = True


class MockLLMService:
    """Mock LLM service for testing"""
    
    def __init__(self):
        self.healthy = True
        self.response_time_ms = 500
        self.streaming_enabled = True
        self.responses = {
            "default": "Based on the code analysis, this appears to be a well-structured implementation with good separation of concerns."
        }
    
    async def generate_response(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Mock response generation"""
        if not self.healthy:
            raise Exception("LLM service unavailable")
        
        await asyncio.sleep(self.response_time_ms / 1000)
        return {
            "text": self.responses.get("default", "Mock response"),
            "confidence": 0.85,
            "model_used": "mock-model",
            "generation_time_ms": self.response_time_ms
        }
    
    async def stream_response(self, query: str, intent: Any, chunks: List[Any], context: Dict[str, Any]):
        """Mock streaming response"""
        if not self.healthy:
            raise Exception("LLM service unavailable")
        
        if not self.streaming_enabled:
            yield self.responses.get("default", "Mock response")
            return
        
        # Simulate streaming chunks
        response = self.responses.get("default", "Mock streaming response")
        words = response.split()
        
        for word in words:
            await asyncio.sleep(0.05)  # Simulate streaming delay
            yield word + " "
    
    def simulate_failure(self):
        """Simulate service failure"""
        self.healthy = False
    
    def restore_service(self):
        """Restore service health"""
        self.healthy = True
    
    def disable_streaming(self):
        """Disable streaming for testing"""
        self.streaming_enabled = False


class MockSemanticSearch:
    """Mock semantic search service for testing"""
    
    def __init__(self):
        self.healthy = True
        self.search_results = [
            {
                "chunk_id": "chunk_1",
                "content": "def process_query(query: str) -> str:",
                "file_path": "/test/file1.py",
                "line_number": 10,
                "combined_score": 0.9
            },
            {
                "chunk_id": "chunk_2",
                "content": "class QueryProcessor:",
                "file_path": "/test/file2.py",
                "line_number": 1,
                "combined_score": 0.8
            }
        ]
    
    async def generate_embedding(self, text: str, context_type: str) -> List[float]:
        """Mock embedding generation"""
        if not self.healthy:
            raise Exception("Semantic search service unavailable")
        
        # Return mock embedding
        return [0.1] * 768  # Typical embedding dimension
    
    async def search(self, embedding: List[float], repository_id: str, filters: Dict = None, limit: int = 10):
        """Mock search operation"""
        if not self.healthy:
            raise Exception("Semantic search service unavailable")
        
        from unittest.mock import Mock
        result = Mock()
        result.chunks = self.search_results[:limit]
        result.search_time_ms = 50
        return result
    
    def simulate_failure(self):
        """Simulate service failure"""
        self.healthy = False
    
    def restore_service(self):
        """Restore service health"""
        self.healthy = True


@pytest.fixture
def mock_redis_client():
    """Mock Redis client fixture"""
    return MockRedisClient()


@pytest.fixture
def mock_analysis_engine():
    """Mock Analysis Engine client fixture"""
    return MockAnalysisEngineClient()


@pytest.fixture
def mock_pattern_mining():
    """Mock Pattern Mining client fixture"""
    return MockPatternMiningClient()


@pytest.fixture
def mock_llm_service():
    """Mock LLM service fixture"""
    return MockLLMService()


@pytest.fixture
def mock_semantic_search():
    """Mock semantic search service fixture"""
    return MockSemanticSearch()


@pytest.fixture
def all_services_healthy(mock_redis_client, mock_analysis_engine, mock_pattern_mining, mock_llm_service, mock_semantic_search):
    """Fixture with all services healthy"""
    return {
        "redis": mock_redis_client,
        "analysis_engine": mock_analysis_engine,
        "pattern_mining": mock_pattern_mining,
        "llm_service": mock_llm_service,
        "semantic_search": mock_semantic_search
    }


@pytest.fixture
def all_services_degraded(mock_redis_client, mock_analysis_engine, mock_pattern_mining, mock_llm_service, mock_semantic_search):
    """Fixture with all services in degraded state"""
    mock_redis_client.set_latency(1000)  # 1 second latency
    mock_analysis_engine.set_failure_rate(0.3)  # 30% failure rate
    mock_pattern_mining.simulate_failure()
    mock_llm_service.disable_streaming()
    mock_semantic_search.simulate_failure()
    
    return {
        "redis": mock_redis_client,
        "analysis_engine": mock_analysis_engine,
        "pattern_mining": mock_pattern_mining,
        "llm_service": mock_llm_service,
        "semantic_search": mock_semantic_search
    }


@pytest.fixture
def circuit_breaker_scenarios():
    """Circuit breaker test scenarios"""
    return {
        "open_circuit": lambda service: service.simulate_failure(),
        "half_open": lambda service: service.set_failure_rate(0.5),
        "closed_circuit": lambda service: service.restore_service()
    }


class ServiceMockManager:
    """Manager for coordinating service mocks"""
    
    def __init__(self, services: Dict[str, Any]):
        self.services = services
        self.original_states = {}
    
    def save_states(self):
        """Save current service states"""
        self.original_states = {
            name: {
                "healthy": getattr(service, "healthy", True),
                "connection_healthy": getattr(service, "connection_healthy", True)
            }
            for name, service in self.services.items()
        }
    
    def restore_states(self):
        """Restore saved service states"""
        for name, service in self.services.items():
            if name in self.original_states:
                state = self.original_states[name]
                if hasattr(service, "healthy"):
                    service.healthy = state["healthy"]
                if hasattr(service, "connection_healthy"):
                    service.connection_healthy = state["connection_healthy"]
    
    def fail_service(self, service_name: str):
        """Fail a specific service"""
        if service_name in self.services:
            service = self.services[service_name]
            if hasattr(service, "simulate_failure"):
                service.simulate_failure()
    
    def restore_service(self, service_name: str):
        """Restore a specific service"""
        if service_name in self.services:
            service = self.services[service_name]
            if hasattr(service, "restore_service"):
                service.restore_service()
            elif hasattr(service, "restore_connection"):
                service.restore_connection()


@pytest.fixture
def service_mock_manager(all_services_healthy):
    """Service mock manager fixture"""
    return ServiceMockManager(all_services_healthy)