"""
Test Data for E2E Testing

Provides realistic test queries, expected responses, and test scenarios
for comprehensive end-to-end testing.
"""

import pytest
from typing import Dict, List, Any
from datetime import datetime


# Test queries for different intent types
TEST_QUERIES = {
    "code_search": [
        "Find all functions that handle user authentication",
        "Show me the database connection setup",
        "Where is the error handling implemented?",
        "Find classes that implement the Observer pattern"
    ],
    "explanation": [
        "How does the authentication system work?",
        "What is the purpose of the cache manager?",
        "Explain the query processing pipeline",
        "How are WebSocket connections handled?"
    ],
    "implementation": [
        "How can I implement rate limiting?",
        "What's the best way to add caching?",
        "How should I structure error handling?",
        "Show me how to implement circuit breakers"
    ],
    "debugging": [
        "Why is the API response slow?",
        "What causes the WebSocket connection to drop?",
        "How to fix the Redis connection timeout?",
        "Debug the authentication middleware failure"
    ],
    "optimization": [
        "How can I improve query performance?",
        "What are the bottlenecks in the system?",
        "How to optimize the caching strategy?",
        "Ways to reduce memory usage"
    ]
}

# Complex queries for stress testing
COMPLEX_QUERIES = [
    "Analyze the entire authentication flow from JWT validation through rate limiting to final API response, including error handling and circuit breaker behavior",
    "Explain the multi-level caching strategy including L1 memory cache, L2 Redis cache, and L3 semantic cache with invalidation patterns",
    "Show me how to implement a new microservice that integrates with the existing pattern mining and analysis engine services",
    "Debug the performance bottleneck in the WebSocket streaming response when handling concurrent users with complex queries",
    "Optimize the query processing pipeline to handle 1000 concurrent requests while maintaining sub-second response times"
]

# Expected response patterns for validation
EXPECTED_RESPONSE_PATTERNS = {
    "code_search": {
        "min_confidence": 0.7,
        "should_contain": ["function", "class", "method", "implementation"],
        "should_have_references": True,
        "min_references": 2
    },
    "explanation": {
        "min_confidence": 0.8,
        "should_contain": ["system", "works", "process", "flow"],
        "should_have_references": True,
        "min_references": 1
    },
    "implementation": {
        "min_confidence": 0.75,
        "should_contain": ["implement", "example", "code", "pattern"],
        "should_have_references": True,
        "min_references": 3
    },
    "debugging": {
        "min_confidence": 0.7,
        "should_contain": ["error", "issue", "problem", "solution"],
        "should_have_references": True,
        "min_references": 2
    },
    "optimization": {
        "min_confidence": 0.75,
        "should_contain": ["performance", "optimize", "improve", "efficiency"],
        "should_have_references": True,
        "min_references": 2
    }
}

# Test repository configurations
TEST_REPOSITORIES = {
    "small_repo": {
        "repository_id": "test_repo_small",
        "files_count": 10,
        "lines_of_code": 1000,
        "languages": ["python"],
        "complexity": "low"
    },
    "medium_repo": {
        "repository_id": "test_repo_medium",
        "files_count": 100,
        "lines_of_code": 50000,
        "languages": ["python", "javascript", "typescript"],
        "complexity": "medium"
    },
    "large_repo": {
        "repository_id": "test_repo_large",
        "files_count": 1000,
        "lines_of_code": 500000,
        "languages": ["python", "javascript", "typescript", "rust", "go"],
        "complexity": "high"
    }
}

# WebSocket test scenarios
WEBSOCKET_SCENARIOS = {
    "simple_query": {
        "query": "How does authentication work?",
        "expected_events": [
            "acknowledged",
            "processing_started",
            "intent_analyzed",
            "status",
            "search_complete",
            "reference",
            "text",
            "done"
        ]
    },
    "complex_query": {
        "query": "Explain the entire query processing pipeline with error handling",
        "expected_events": [
            "acknowledged",
            "processing_started",
            "intent_analyzed",
            "status",
            "search_complete",
            "reference",
            "text",
            "done"
        ]
    },
    "error_scenario": {
        "query": "",  # Empty query to trigger error
        "expected_events": ["error"]
    }
}

# Performance test scenarios
PERFORMANCE_SCENARIOS = {
    "concurrent_queries": {
        "concurrent_users": 10,
        "queries_per_user": 5,
        "max_response_time": 5.0,
        "success_rate_threshold": 0.95
    },
    "load_test": {
        "concurrent_users": 50,
        "queries_per_user": 10,
        "max_response_time": 10.0,
        "success_rate_threshold": 0.90
    },
    "stress_test": {
        "concurrent_users": 100,
        "queries_per_user": 5,
        "max_response_time": 15.0,
        "success_rate_threshold": 0.85
    }
}

# Cache test scenarios
CACHE_SCENARIOS = {
    "cache_miss": {
        "query": "What is the cache miss scenario?",
        "expected_cache_hits": 0,
        "expected_cache_misses": 1
    },
    "cache_hit": {
        "query": "What is the cache hit scenario?",
        "expected_cache_hits": 1,
        "expected_cache_misses": 0
    },
    "cache_invalidation": {
        "query": "Test cache invalidation",
        "invalidate_before": True,
        "expected_cache_hits": 0,
        "expected_cache_misses": 1
    }
}

# Error recovery scenarios
ERROR_SCENARIOS = {
    "redis_failure": {
        "failing_service": "redis",
        "expected_behavior": "graceful_degradation",
        "should_succeed": True
    },
    "analysis_engine_failure": {
        "failing_service": "analysis_engine",
        "expected_behavior": "fallback_response",
        "should_succeed": True
    },
    "pattern_mining_failure": {
        "failing_service": "pattern_mining",
        "expected_behavior": "skip_patterns",
        "should_succeed": True
    },
    "llm_service_failure": {
        "failing_service": "llm_service",
        "expected_behavior": "fallback_response",
        "should_succeed": True
    },
    "multiple_service_failure": {
        "failing_services": ["redis", "analysis_engine"],
        "expected_behavior": "minimal_functionality",
        "should_succeed": True
    }
}

# Production-like scenarios
PRODUCTION_SCENARIOS = {
    "peak_load": {
        "description": "Simulate peak usage with mixed query types",
        "concurrent_users": 75,
        "query_mix": {
            "code_search": 0.4,
            "explanation": 0.3,
            "implementation": 0.2,
            "debugging": 0.1
        },
        "duration_seconds": 60
    },
    "service_degradation": {
        "description": "Simulate gradual service degradation",
        "redis_latency_ms": 500,
        "analysis_engine_failure_rate": 0.1,
        "pattern_mining_timeout": True
    },
    "recovery_scenario": {
        "description": "Test service recovery behavior",
        "initial_failures": ["redis", "analysis_engine"],
        "recovery_delay_seconds": 30,
        "expected_recovery_time": 60
    }
}


@pytest.fixture
def test_queries():
    """Fixture providing test queries by intent type"""
    return TEST_QUERIES


@pytest.fixture
def complex_queries():
    """Fixture providing complex queries for stress testing"""
    return COMPLEX_QUERIES


@pytest.fixture
def expected_response_patterns():
    """Fixture providing expected response validation patterns"""
    return EXPECTED_RESPONSE_PATTERNS


@pytest.fixture
def test_repositories():
    """Fixture providing test repository configurations"""
    return TEST_REPOSITORIES


@pytest.fixture
def websocket_scenarios():
    """Fixture providing WebSocket test scenarios"""
    return WEBSOCKET_SCENARIOS


@pytest.fixture
def performance_scenarios():
    """Fixture providing performance test scenarios"""
    return PERFORMANCE_SCENARIOS


@pytest.fixture
def cache_scenarios():
    """Fixture providing cache test scenarios"""
    return CACHE_SCENARIOS


@pytest.fixture
def error_scenarios():
    """Fixture providing error recovery scenarios"""
    return ERROR_SCENARIOS


@pytest.fixture
def production_scenarios():
    """Fixture providing production-like test scenarios"""
    return PRODUCTION_SCENARIOS


@pytest.fixture
def sample_query_request():
    """Fixture providing a sample query request"""
    return {
        "query": "How does the authentication system work?",
        "repository_id": "test_repo_medium",
        "user_id": "test_user_123",
        "context": {
            "intent": "explanation",
            "complexity": "medium"
        }
    }


@pytest.fixture
def sample_websocket_request():
    """Fixture providing a sample WebSocket request"""
    return {
        "query": "Explain the query processing pipeline",
        "repository_id": "test_repo_medium",
        "context": {
            "intent": "explanation",
            "streaming": True
        }
    }


class TestDataGenerator:
    """Generator for dynamic test data"""
    
    @staticmethod
    def generate_query_batch(count: int, intent_type: str = None) -> List[Dict[str, Any]]:
        """Generate a batch of test queries"""
        queries = []
        
        for i in range(count):
            if intent_type:
                query_list = TEST_QUERIES.get(intent_type, TEST_QUERIES["code_search"])
            else:
                # Mix of different intent types
                intent_types = list(TEST_QUERIES.keys())
                intent_type = intent_types[i % len(intent_types)]
                query_list = TEST_QUERIES[intent_type]
            
            query = query_list[i % len(query_list)]
            queries.append({
                "query": query,
                "repository_id": f"test_repo_{i % 3}",
                "user_id": f"test_user_{i}",
                "intent_type": intent_type,
                "batch_index": i
            })
        
        return queries
    
    @staticmethod
    def generate_concurrent_scenario(users: int, queries_per_user: int) -> Dict[str, Any]:
        """Generate a concurrent testing scenario"""
        return {
            "concurrent_users": users,
            "queries_per_user": queries_per_user,
            "total_queries": users * queries_per_user,
            "query_batch": TestDataGenerator.generate_query_batch(users * queries_per_user)
        }


@pytest.fixture
def test_data_generator():
    """Fixture providing test data generator"""
    return TestDataGenerator