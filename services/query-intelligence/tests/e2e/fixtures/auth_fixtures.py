"""
Authentication Fixtures for E2E Testing

Provides JWT tokens, user authentication, and auth-related test utilities.
"""

import pytest
import jwt
import time
from datetime import datetime, timedelta
from typing import Dict, Optional
from unittest.mock import Mock, AsyncMock


@pytest.fixture
def jwt_secret():
    """Test JWT secret key"""
    return "test_secret_key_for_e2e_testing"


@pytest.fixture
def valid_jwt_token(jwt_secret):
    """Generate a valid JWT token for testing"""
    payload = {
        "sub": "test_user_123",
        "user_id": "test_user_123",
        "email": "<EMAIL>",
        "exp": int(time.time()) + 3600,  # 1 hour expiry
        "iat": int(time.time()),
        "role": "user"
    }
    return jwt.encode(payload, jwt_secret, algorithm="HS256")


@pytest.fixture
def expired_jwt_token(jwt_secret):
    """Generate an expired JWT token for testing"""
    payload = {
        "sub": "test_user_123",
        "user_id": "test_user_123",
        "email": "<EMAIL>",
        "exp": int(time.time()) - 3600,  # Expired 1 hour ago
        "iat": int(time.time()) - 7200,  # Issued 2 hours ago
        "role": "user"
    }
    return jwt.encode(payload, jwt_secret, algorithm="HS256")


@pytest.fixture
def admin_jwt_token(jwt_secret):
    """Generate an admin JWT token for testing"""
    payload = {
        "sub": "admin_user_456",
        "user_id": "admin_user_456",
        "email": "<EMAIL>",
        "exp": int(time.time()) + 3600,
        "iat": int(time.time()),
        "role": "admin"
    }
    return jwt.encode(payload, jwt_secret, algorithm="HS256")


@pytest.fixture
def invalid_jwt_token():
    """Generate an invalid JWT token for testing"""
    return "invalid.jwt.token"


@pytest.fixture
def auth_headers(valid_jwt_token):
    """Generate authentication headers for HTTP requests"""
    return {
        "Authorization": f"Bearer {valid_jwt_token}",
        "Content-Type": "application/json"
    }


@pytest.fixture
def admin_auth_headers(admin_jwt_token):
    """Generate admin authentication headers"""
    return {
        "Authorization": f"Bearer {admin_jwt_token}",
        "Content-Type": "application/json"
    }


@pytest.fixture
def websocket_auth_headers(valid_jwt_token):
    """Generate authentication headers for WebSocket connections"""
    return {
        "Authorization": f"Bearer {valid_jwt_token}"
    }


@pytest.fixture
def mock_user_context():
    """Mock user context for testing"""
    return {
        "user_id": "test_user_123",
        "email": "<EMAIL>",
        "role": "user",
        "subscription": "premium",
        "rate_limit": {
            "requests": 50,
            "window": 60
        }
    }


@pytest.fixture
def mock_auth_service():
    """Mock authentication service for testing"""
    mock = Mock()
    mock.verify_token = AsyncMock(return_value={
        "user_id": "test_user_123",
        "email": "<EMAIL>",
        "role": "user"
    })
    mock.get_user_context = AsyncMock(return_value={
        "user_id": "test_user_123",
        "subscription": "premium",
        "permissions": ["query", "stream"]
    })
    return mock


class AuthTestHelper:
    """Helper class for authentication testing"""
    
    def __init__(self, jwt_secret: str):
        self.jwt_secret = jwt_secret
    
    def create_token(self, user_id: str, role: str = "user", expires_in: int = 3600) -> str:
        """Create a JWT token with custom parameters"""
        payload = {
            "sub": user_id,
            "user_id": user_id,
            "email": f"{user_id}@example.com",
            "exp": int(time.time()) + expires_in,
            "iat": int(time.time()),
            "role": role
        }
        return jwt.encode(payload, self.jwt_secret, algorithm="HS256")
    
    def create_headers(self, token: str) -> Dict[str, str]:
        """Create authentication headers"""
        return {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
    
    def verify_token(self, token: str) -> Optional[Dict]:
        """Verify a JWT token"""
        try:
            return jwt.decode(token, self.jwt_secret, algorithms=["HS256"])
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None


@pytest.fixture
def auth_helper(jwt_secret):
    """Authentication helper instance"""
    return AuthTestHelper(jwt_secret)


@pytest.fixture
def multiple_user_tokens(auth_helper):
    """Generate multiple user tokens for concurrent testing"""
    return [
        auth_helper.create_token(f"user_{i}", "user") 
        for i in range(10)
    ]


@pytest.fixture
def rate_limited_user_token(auth_helper):
    """Generate token for a rate-limited user"""
    return auth_helper.create_token("rate_limited_user", "limited")


@pytest.fixture
def premium_user_token(auth_helper):
    """Generate token for a premium user"""
    return auth_helper.create_token("premium_user", "premium")