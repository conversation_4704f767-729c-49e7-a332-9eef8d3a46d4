"""
Performance Testing Fixtures

Provides utilities for load testing, performance monitoring, and
concurrent request simulation.
"""

import pytest
import asyncio
import time
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from statistics import mean, median, stdev
import json
from datetime import datetime


@dataclass
class PerformanceMetrics:
    """Container for performance metrics"""
    total_requests: int
    successful_requests: int
    failed_requests: int
    response_times: List[float]
    errors: List[Dict[str, Any]]
    start_time: float
    end_time: float
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate percentage"""
        if self.total_requests == 0:
            return 0.0
        return (self.successful_requests / self.total_requests) * 100
    
    @property
    def average_response_time(self) -> float:
        """Calculate average response time"""
        return mean(self.response_times) if self.response_times else 0.0
    
    @property
    def median_response_time(self) -> float:
        """Calculate median response time"""
        return median(self.response_times) if self.response_times else 0.0
    
    @property
    def p95_response_time(self) -> float:
        """Calculate 95th percentile response time"""
        if not self.response_times:
            return 0.0
        sorted_times = sorted(self.response_times)
        index = int(0.95 * len(sorted_times))
        return sorted_times[index]
    
    @property
    def p99_response_time(self) -> float:
        """Calculate 99th percentile response time"""
        if not self.response_times:
            return 0.0
        sorted_times = sorted(self.response_times)
        index = int(0.99 * len(sorted_times))
        return sorted_times[index]
    
    @property
    def requests_per_second(self) -> float:
        """Calculate requests per second"""
        duration = self.end_time - self.start_time
        return self.total_requests / duration if duration > 0 else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary"""
        return {
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "failed_requests": self.failed_requests,
            "success_rate": self.success_rate,
            "average_response_time": self.average_response_time,
            "median_response_time": self.median_response_time,
            "p95_response_time": self.p95_response_time,
            "p99_response_time": self.p99_response_time,
            "requests_per_second": self.requests_per_second,
            "total_duration": self.end_time - self.start_time,
            "errors": self.errors
        }


class PerformanceMonitor:
    """Monitor performance metrics during testing"""
    
    def __init__(self):
        self.metrics = PerformanceMetrics(
            total_requests=0,
            successful_requests=0,
            failed_requests=0,
            response_times=[],
            errors=[],
            start_time=0.0,
            end_time=0.0
        )
        self.active_requests = 0
        self.max_concurrent_requests = 0
    
    def start_monitoring(self):
        """Start performance monitoring"""
        self.metrics.start_time = time.time()
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.metrics.end_time = time.time()
    
    def record_request_start(self):
        """Record the start of a request"""
        self.active_requests += 1
        self.max_concurrent_requests = max(self.max_concurrent_requests, self.active_requests)
        self.metrics.total_requests += 1
    
    def record_request_success(self, response_time: float):
        """Record a successful request"""
        self.active_requests -= 1
        self.metrics.successful_requests += 1
        self.metrics.response_times.append(response_time)
    
    def record_request_failure(self, error: Dict[str, Any], response_time: float = None):
        """Record a failed request"""
        self.active_requests -= 1
        self.metrics.failed_requests += 1
        self.metrics.errors.append({
            "timestamp": datetime.utcnow().isoformat(),
            "error": error,
            "response_time": response_time
        })
        if response_time is not None:
            self.metrics.response_times.append(response_time)
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        return {
            **self.metrics.to_dict(),
            "active_requests": self.active_requests,
            "max_concurrent_requests": self.max_concurrent_requests
        }


class LoadTestRunner:
    """Run load tests with configurable parameters"""
    
    def __init__(self, monitor: PerformanceMonitor):
        self.monitor = monitor
        self.running = False
    
    async def run_concurrent_requests(
        self,
        request_func: Callable,
        concurrent_users: int,
        requests_per_user: int,
        delay_between_requests: float = 0.1
    ) -> PerformanceMetrics:
        """Run concurrent requests with multiple users"""
        self.monitor.start_monitoring()
        self.running = True
        
        async def user_session(user_id: int):
            """Simulate a user session with multiple requests"""
            for request_num in range(requests_per_user):
                if not self.running:
                    break
                
                self.monitor.record_request_start()
                start_time = time.time()
                
                try:
                    await request_func(user_id=user_id, request_num=request_num)
                    response_time = time.time() - start_time
                    self.monitor.record_request_success(response_time)
                except Exception as e:
                    response_time = time.time() - start_time
                    self.monitor.record_request_failure({
                        "user_id": user_id,
                        "request_num": request_num,
                        "error": str(e),
                        "error_type": type(e).__name__
                    }, response_time)
                
                await asyncio.sleep(delay_between_requests)
        
        # Start all user sessions concurrently
        tasks = [user_session(user_id) for user_id in range(concurrent_users)]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        self.monitor.stop_monitoring()
        return self.monitor.metrics
    
    async def run_sustained_load(
        self,
        request_func: Callable,
        requests_per_second: float,
        duration_seconds: int
    ) -> PerformanceMetrics:
        """Run sustained load test"""
        self.monitor.start_monitoring()
        self.running = True
        
        request_interval = 1.0 / requests_per_second
        end_time = time.time() + duration_seconds
        
        async def request_loop():
            """Main request loop"""
            while self.running and time.time() < end_time:
                self.monitor.record_request_start()
                start_time = time.time()
                
                try:
                    await request_func()
                    response_time = time.time() - start_time
                    self.monitor.record_request_success(response_time)
                except Exception as e:
                    response_time = time.time() - start_time
                    self.monitor.record_request_failure({
                        "error": str(e),
                        "error_type": type(e).__name__
                    }, response_time)
                
                await asyncio.sleep(request_interval)
        
        await request_loop()
        self.monitor.stop_monitoring()
        return self.monitor.metrics
    
    def stop(self):
        """Stop the load test"""
        self.running = False


class WebSocketLoadTester:
    """Load tester specifically for WebSocket connections"""
    
    def __init__(self, monitor: PerformanceMonitor):
        self.monitor = monitor
        self.connections = []
    
    async def test_concurrent_websockets(
        self,
        websocket_url: str,
        concurrent_connections: int,
        messages_per_connection: int,
        headers: Dict[str, str] = None
    ) -> PerformanceMetrics:
        """Test concurrent WebSocket connections"""
        import websockets
        
        self.monitor.start_monitoring()
        
        async def websocket_session(connection_id: int):
            """Single WebSocket session"""
            try:
                async with websockets.connect(
                    websocket_url,
                    extra_headers=headers or {}
                ) as websocket:
                    for msg_num in range(messages_per_connection):
                        self.monitor.record_request_start()
                        start_time = time.time()
                        
                        try:
                            # Send test message
                            test_message = json.dumps({
                                "query": f"Test query {msg_num} from connection {connection_id}",
                                "repository_id": "test_repo"
                            })
                            
                            await websocket.send(test_message)
                            
                            # Wait for response
                            response = await websocket.recv()
                            response_data = json.loads(response)
                            
                            response_time = time.time() - start_time
                            self.monitor.record_request_success(response_time)
                            
                        except Exception as e:
                            response_time = time.time() - start_time
                            self.monitor.record_request_failure({
                                "connection_id": connection_id,
                                "message_num": msg_num,
                                "error": str(e),
                                "error_type": type(e).__name__
                            }, response_time)
                        
                        await asyncio.sleep(0.1)  # Small delay between messages
                        
            except Exception as e:
                self.monitor.record_request_failure({
                    "connection_id": connection_id,
                    "error": f"WebSocket connection failed: {str(e)}",
                    "error_type": "ConnectionError"
                })
        
        # Start all WebSocket sessions concurrently
        tasks = [websocket_session(conn_id) for conn_id in range(concurrent_connections)]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        self.monitor.stop_monitoring()
        return self.monitor.metrics


class PerformanceValidator:
    """Validate performance against thresholds"""
    
    def __init__(self, thresholds: Dict[str, float]):
        self.thresholds = thresholds
    
    def validate_metrics(self, metrics: PerformanceMetrics) -> Dict[str, Any]:
        """Validate performance metrics against thresholds"""
        results = {
            "passed": True,
            "violations": [],
            "summary": {}
        }
        
        # Check success rate
        if "min_success_rate" in self.thresholds:
            if metrics.success_rate < self.thresholds["min_success_rate"]:
                results["passed"] = False
                results["violations"].append({
                    "metric": "success_rate",
                    "actual": metrics.success_rate,
                    "threshold": self.thresholds["min_success_rate"]
                })
        
        # Check average response time
        if "max_avg_response_time" in self.thresholds:
            if metrics.average_response_time > self.thresholds["max_avg_response_time"]:
                results["passed"] = False
                results["violations"].append({
                    "metric": "average_response_time",
                    "actual": metrics.average_response_time,
                    "threshold": self.thresholds["max_avg_response_time"]
                })
        
        # Check 95th percentile response time
        if "max_p95_response_time" in self.thresholds:
            if metrics.p95_response_time > self.thresholds["max_p95_response_time"]:
                results["passed"] = False
                results["violations"].append({
                    "metric": "p95_response_time",
                    "actual": metrics.p95_response_time,
                    "threshold": self.thresholds["max_p95_response_time"]
                })
        
        # Check requests per second
        if "min_requests_per_second" in self.thresholds:
            if metrics.requests_per_second < self.thresholds["min_requests_per_second"]:
                results["passed"] = False
                results["violations"].append({
                    "metric": "requests_per_second",
                    "actual": metrics.requests_per_second,
                    "threshold": self.thresholds["min_requests_per_second"]
                })
        
        results["summary"] = {
            "success_rate": metrics.success_rate,
            "average_response_time": metrics.average_response_time,
            "p95_response_time": metrics.p95_response_time,
            "requests_per_second": metrics.requests_per_second,
            "total_requests": metrics.total_requests
        }
        
        return results


@pytest.fixture
def performance_monitor():
    """Performance monitor fixture"""
    return PerformanceMonitor()


@pytest.fixture
def load_test_runner(performance_monitor):
    """Load test runner fixture"""
    return LoadTestRunner(performance_monitor)


@pytest.fixture
def websocket_load_tester(performance_monitor):
    """WebSocket load tester fixture"""
    return WebSocketLoadTester(performance_monitor)


@pytest.fixture
def performance_validator():
    """Performance validator fixture with default thresholds"""
    return PerformanceValidator({
        "min_success_rate": 95.0,
        "max_avg_response_time": 2.0,
        "max_p95_response_time": 5.0,
        "min_requests_per_second": 10.0
    })


@pytest.fixture
def strict_performance_validator():
    """Strict performance validator for production testing"""
    return PerformanceValidator({
        "min_success_rate": 99.0,
        "max_avg_response_time": 1.0,
        "max_p95_response_time": 2.0,
        "min_requests_per_second": 50.0
    })


@pytest.fixture
def load_test_scenarios():
    """Load test scenarios for different testing phases"""
    return {
        "smoke_test": {
            "concurrent_users": 5,
            "requests_per_user": 3,
            "expected_success_rate": 100.0,
            "max_response_time": 3.0
        },
        "load_test": {
            "concurrent_users": 25,
            "requests_per_user": 10,
            "expected_success_rate": 95.0,
            "max_response_time": 5.0
        },
        "stress_test": {
            "concurrent_users": 100,
            "requests_per_user": 5,
            "expected_success_rate": 90.0,
            "max_response_time": 10.0
        },
        "endurance_test": {
            "concurrent_users": 50,
            "requests_per_user": 100,
            "expected_success_rate": 95.0,
            "max_response_time": 5.0
        }
    }