"""
Production Scenarios E2E Tests

Tests production-like validation scenarios including realistic load patterns,
mixed workloads, edge cases, and comprehensive system validation.
"""

import pytest
import asyncio
import time
import random
import statistics
from typing import Dict, List, Any, Optional
from unittest.mock import patch, AsyncMock

from .fixtures import (
    production_scenarios, auth_headers, multiple_user_tokens, 
    test_queries, complex_queries, performance_monitor, load_test_runner
)
from .utils.websocket_client import WebSocketSessionManager, ConcurrentWebSocketTester
from .utils.performance_monitors import RealTimeMetricsCollector, LoadTestAnalyzer
from .utils.service_health import ServiceHealthChecker, HealthMonitoringDashboard


class TestProductionScenarios:
    """Production-like validation scenarios"""
    
    @pytest.fixture
    def service_base_url(self):
        """Base URL for the service"""
        return "http://localhost:8000"
    
    @pytest.fixture
    def websocket_base_url(self):
        """WebSocket base URL"""
        return "ws://localhost:8000"
    
    @pytest.fixture
    def production_config(self):
        """Production-like configuration"""
        return {
            "max_concurrent_users": 100,
            "sustained_load_duration": 300,  # 5 minutes
            "peak_load_multiplier": 2.0,
            "acceptable_error_rate": 0.5,  # 0.5%
            "max_response_time": 5.0,
            "max_memory_usage": 2048,  # 2GB
            "max_cpu_usage": 70.0
        }
    
    @pytest.mark.asyncio
    async def test_realistic_user_behavior_simulation(self, service_base_url, websocket_base_url, multiple_user_tokens, test_queries):
        """Test realistic user behavior simulation"""
        import httpx
        
        async def simulate_user_session(user_id: int, user_token: str):
            """Simulate a realistic user session"""
            session_metrics = {
                "user_id": user_id,
                "queries_executed": 0,
                "api_requests": 0,
                "websocket_sessions": 0,
                "errors": [],
                "response_times": []
            }
            
            # Mix of API and WebSocket usage
            session_actions = [
                ("api", 0.6),  # 60% API usage
                ("websocket", 0.4)  # 40% WebSocket usage
            ]
            
            headers = {
                "Authorization": f"Bearer {user_token}",
                "Content-Type": "application/json"
            }
            
            # Simulate 5-10 queries per user session
            num_queries = random.randint(5, 10)
            
            for query_num in range(num_queries):
                try:
                    # Choose action based on weights
                    action = random.choices(
                        [action for action, _ in session_actions],
                        weights=[weight for _, weight in session_actions]
                    )[0]
                    
                    # Select random query
                    query_category = random.choice(list(test_queries.keys()))
                    query = random.choice(test_queries[query_category])
                    
                    if action == "api":
                        # API request
                        start_time = time.time()
                        async with httpx.AsyncClient() as client:
                            response = await client.post(
                                f"{service_base_url}/api/v1/query",
                                json={
                                    "query": f"{query} (user {user_id})",
                                    "repository_id": "test_repo"
                                },
                                headers=headers
                            )
                        end_time = time.time()
                        
                        session_metrics["api_requests"] += 1
                        session_metrics["response_times"].append(end_time - start_time)
                        
                        if response.status_code != 200:
                            session_metrics["errors"].append(f"API error: {response.status_code}")
                    
                    elif action == "websocket":
                        # WebSocket request
                        from .utils.websocket_client import WebSocketTestClient
                        
                        start_time = time.time()
                        ws_client = WebSocketTestClient(websocket_base_url)
                        
                        connected = await ws_client.connect({"Authorization": headers["Authorization"]})
                        if connected:
                            events = await ws_client.query_and_receive(f"{query} (user {user_id})")
                            await ws_client.disconnect()
                            
                            session_metrics["websocket_sessions"] += 1
                            session_metrics["response_times"].append(time.time() - start_time)
                            
                            if ws_client.has_error():
                                session_metrics["errors"].append("WebSocket error")
                        else:
                            session_metrics["errors"].append("WebSocket connection failed")
                    
                    session_metrics["queries_executed"] += 1
                    
                    # Realistic delay between queries
                    await asyncio.sleep(random.uniform(0.5, 3.0))
                    
                except Exception as e:
                    session_metrics["errors"].append(f"Session error: {str(e)}")
            
            return session_metrics
        
        # Step 1: Simulate multiple user sessions
        concurrent_users = min(len(multiple_user_tokens), 20)  # Limit for testing
        
        user_tasks = [
            simulate_user_session(i, multiple_user_tokens[i])
            for i in range(concurrent_users)
        ]
        
        start_time = time.time()
        user_results = await asyncio.gather(*user_tasks, return_exceptions=True)
        end_time = time.time()
        
        # Step 2: Analyze user behavior simulation
        successful_sessions = [r for r in user_results if isinstance(r, dict)]
        failed_sessions = [r for r in user_results if isinstance(r, Exception)]
        
        assert len(successful_sessions) >= concurrent_users * 0.9, \
            f"Too many failed sessions: {len(failed_sessions)}"
        
        # Validate session metrics
        total_queries = sum(s["queries_executed"] for s in successful_sessions)
        total_errors = sum(len(s["errors"]) for s in successful_sessions)
        
        error_rate = (total_errors / total_queries) * 100 if total_queries > 0 else 0
        assert error_rate <= 5.0, f"Error rate too high: {error_rate:.1f}%"
        
        # Validate response times
        all_response_times = []
        for session in successful_sessions:
            all_response_times.extend(session["response_times"])
        
        if all_response_times:
            avg_response_time = statistics.mean(all_response_times)
            assert avg_response_time <= 8.0, f"Average response time too high: {avg_response_time:.2f}s"
    
    @pytest.mark.asyncio
    async def test_peak_load_handling(self, service_base_url, auth_headers, production_config):
        """Test peak load handling capabilities"""
        import httpx
        
        # Step 1: Baseline load test
        baseline_concurrent_users = 10
        baseline_metrics = []
        
        async def baseline_request(user_id: int):
            """Baseline request"""
            async with httpx.AsyncClient() as client:
                start_time = time.time()
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": f"Baseline load test {user_id}",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                end_time = time.time()
                
                return {
                    "success": response.status_code == 200,
                    "response_time": end_time - start_time,
                    "status_code": response.status_code
                }
        
        baseline_tasks = [baseline_request(i) for i in range(baseline_concurrent_users)]
        baseline_results = await asyncio.gather(*baseline_tasks, return_exceptions=True)
        
        baseline_successful = [r for r in baseline_results if isinstance(r, dict) and r["success"]]
        baseline_avg_time = statistics.mean([r["response_time"] for r in baseline_successful])
        
        # Step 2: Peak load test
        peak_concurrent_users = int(baseline_concurrent_users * production_config["peak_load_multiplier"])
        
        async def peak_request(user_id: int):
            """Peak load request"""
            async with httpx.AsyncClient() as client:
                start_time = time.time()
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": f"Peak load test {user_id}",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                end_time = time.time()
                
                return {
                    "success": response.status_code == 200,
                    "response_time": end_time - start_time,
                    "status_code": response.status_code
                }
        
        peak_tasks = [peak_request(i) for i in range(peak_concurrent_users)]
        peak_results = await asyncio.gather(*peak_tasks, return_exceptions=True)
        
        # Step 3: Analyze peak load performance
        peak_successful = [r for r in peak_results if isinstance(r, dict) and r["success"]]
        peak_failed = [r for r in peak_results if isinstance(r, Exception) or not r.get("success", False)]
        
        peak_success_rate = (len(peak_successful) / len(peak_results)) * 100
        assert peak_success_rate >= 80.0, f"Peak load success rate too low: {peak_success_rate:.1f}%"
        
        if peak_successful:
            peak_avg_time = statistics.mean([r["response_time"] for r in peak_successful])
            
            # Peak load should not degrade performance more than 3x
            assert peak_avg_time <= baseline_avg_time * 3.0, \
                f"Peak load degradation too high: baseline={baseline_avg_time:.2f}s, peak={peak_avg_time:.2f}s"
    
    @pytest.mark.asyncio
    async def test_sustained_production_load(self, service_base_url, auth_headers, production_config):
        """Test sustained production load over time"""
        import httpx
        
        # Step 1: Set up metrics collection
        metrics_collector = RealTimeMetricsCollector(collection_interval=1.0)
        metrics_collector.start_collection()
        
        # Step 2: Generate sustained load
        sustained_duration = 60  # 1 minute for testing (reduced from production config)
        requests_per_second = 3.0
        
        async def sustained_request_generator():
            """Generate sustained requests"""
            request_count = 0
            start_time = time.time()
            
            while time.time() - start_time < sustained_duration:
                try:
                    async with httpx.AsyncClient() as client:
                        request_start = time.time()
                        response = await client.post(
                            f"{service_base_url}/api/v1/query",
                            json={
                                "query": f"Sustained load test {request_count}",
                                "repository_id": "test_repo"
                            },
                            headers=auth_headers
                        )
                        request_end = time.time()
                        
                        # Record metrics
                        from .utils.performance_monitors import RequestMetrics
                        metrics_collector.record_request(RequestMetrics(
                            request_id=f"sustained_{request_count}",
                            start_time=request_start,
                            end_time=request_end,
                            response_time=request_end - request_start,
                            status_code=response.status_code,
                            success=response.status_code == 200
                        ))
                        
                        request_count += 1
                        
                        # Maintain request rate
                        await asyncio.sleep(1.0 / requests_per_second)
                        
                except Exception as e:
                    print(f"Sustained load error: {e}")
                    break
        
        # Step 3: Run sustained load
        await sustained_request_generator()
        
        # Step 4: Stop metrics collection and analyze
        metrics_collector.stop_collection()
        final_metrics = metrics_collector.get_current_metrics()
        
        # Validate sustained load performance
        assert final_metrics["requests"]["success_rate"] >= 90.0, \
            f"Sustained load success rate too low: {final_metrics['requests']['success_rate']:.1f}%"
        
        assert final_metrics["requests"]["average_response_time"] <= production_config["max_response_time"], \
            f"Sustained load response time too high: {final_metrics['requests']['average_response_time']:.2f}s"
        
        # Validate system resources
        if final_metrics["system"]["average_cpu_percent"] > 0:
            assert final_metrics["system"]["average_cpu_percent"] <= production_config["max_cpu_usage"], \
                f"CPU usage too high: {final_metrics['system']['average_cpu_percent']:.1f}%"
    
    @pytest.mark.asyncio
    async def test_mixed_workload_production_scenario(self, service_base_url, websocket_base_url, multiple_user_tokens, test_queries, complex_queries):
        """Test mixed workload production scenario"""
        import httpx
        
        # Step 1: Define workload mix
        workload_mix = {
            "simple_api": {"weight": 0.4, "queries": test_queries["code_search"]},
            "complex_api": {"weight": 0.2, "queries": complex_queries[:3]},
            "websocket_simple": {"weight": 0.3, "queries": test_queries["explanation"]},
            "websocket_complex": {"weight": 0.1, "queries": complex_queries[:2]}
        }
        
        # Step 2: Execute mixed workload
        async def mixed_workload_task(task_id: int):
            """Execute mixed workload task"""
            user_token = multiple_user_tokens[task_id % len(multiple_user_tokens)]
            headers = {"Authorization": f"Bearer {user_token}"}
            
            # Choose workload type based on weights
            workload_type = random.choices(
                list(workload_mix.keys()),
                weights=[w["weight"] for w in workload_mix.values()]
            )[0]
            
            workload_config = workload_mix[workload_type]
            query = random.choice(workload_config["queries"])
            
            start_time = time.time()
            
            try:
                if "api" in workload_type:
                    # API request
                    async with httpx.AsyncClient() as client:
                        response = await client.post(
                            f"{service_base_url}/api/v1/query",
                            json={
                                "query": query,
                                "repository_id": "test_repo"
                            },
                            headers=headers
                        )
                        
                        return {
                            "task_id": task_id,
                            "workload_type": workload_type,
                            "success": response.status_code == 200,
                            "response_time": time.time() - start_time,
                            "status_code": response.status_code
                        }
                
                elif "websocket" in workload_type:
                    # WebSocket request
                    from .utils.websocket_client import WebSocketTestClient
                    
                    ws_client = WebSocketTestClient(websocket_base_url)
                    connected = await ws_client.connect(headers)
                    
                    if connected:
                        events = await ws_client.query_and_receive(query)
                        await ws_client.disconnect()
                        
                        return {
                            "task_id": task_id,
                            "workload_type": workload_type,
                            "success": len(events) > 0 and not ws_client.has_error(),
                            "response_time": time.time() - start_time,
                            "events_count": len(events)
                        }
                    else:
                        return {
                            "task_id": task_id,
                            "workload_type": workload_type,
                            "success": False,
                            "response_time": time.time() - start_time,
                            "error": "WebSocket connection failed"
                        }
                        
            except Exception as e:
                return {
                    "task_id": task_id,
                    "workload_type": workload_type,
                    "success": False,
                    "response_time": time.time() - start_time,
                    "error": str(e)
                }
        
        # Step 3: Run mixed workload
        num_tasks = 50
        workload_tasks = [mixed_workload_task(i) for i in range(num_tasks)]
        workload_results = await asyncio.gather(*workload_tasks, return_exceptions=True)
        
        # Step 4: Analyze mixed workload results
        successful_results = [r for r in workload_results if isinstance(r, dict) and r["success"]]
        failed_results = [r for r in workload_results if isinstance(r, Exception) or not r.get("success", False)]
        
        overall_success_rate = (len(successful_results) / len(workload_results)) * 100
        assert overall_success_rate >= 85.0, f"Mixed workload success rate too low: {overall_success_rate:.1f}%"
        
        # Analyze by workload type
        workload_performance = {}
        for workload_type in workload_mix.keys():
            type_results = [r for r in successful_results if r["workload_type"] == workload_type]
            if type_results:
                workload_performance[workload_type] = {
                    "count": len(type_results),
                    "avg_response_time": statistics.mean([r["response_time"] for r in type_results]),
                    "success_rate": 100.0  # All in successful_results
                }
        
        # Validate workload type performance
        for workload_type, perf in workload_performance.items():
            if "complex" in workload_type:
                assert perf["avg_response_time"] <= 15.0, f"{workload_type} too slow: {perf['avg_response_time']:.2f}s"
            else:
                assert perf["avg_response_time"] <= 8.0, f"{workload_type} too slow: {perf['avg_response_time']:.2f}s"
    
    @pytest.mark.asyncio
    async def test_edge_case_handling(self, service_base_url, auth_headers):
        """Test edge case handling in production scenarios"""
        import httpx
        
        # Step 1: Define edge cases
        edge_cases = [
            # Empty and whitespace queries
            {"query": "", "expected_status": [400, 422]},
            {"query": "   ", "expected_status": [400, 422]},
            {"query": "\\n\\t\\r", "expected_status": [400, 422]},
            
            # Very long queries
            {"query": "A" * 10000, "expected_status": [400, 413, 422]},
            
            # Special characters and encoding
            {"query": "Special chars: äöüß 你好 🚀 <script>alert('test')</script>", "expected_status": [200, 400]},
            
            # SQL injection attempts
            {"query": "'; DROP TABLE users; --", "expected_status": [200, 400]},
            
            # JSON injection attempts
            {"query": '{"malicious": "payload"}', "expected_status": [200, 400]},
            
            # Very specific technical queries
            {"query": "Find all buffer overflow vulnerabilities in C code", "expected_status": [200]},
            
            # Queries with unusual repository IDs
            {"query": "Normal query", "repository_id": "", "expected_status": [400, 422]},
            {"query": "Normal query", "repository_id": "a" * 1000, "expected_status": [400, 422]},
        ]
        
        # Step 2: Test edge cases
        edge_case_results = []
        
        async with httpx.AsyncClient() as client:
            for i, edge_case in enumerate(edge_cases):
                try:
                    request_data = {
                        "query": edge_case["query"],
                        "repository_id": edge_case.get("repository_id", "test_repo")
                    }
                    
                    response = await client.post(
                        f"{service_base_url}/api/v1/query",
                        json=request_data,
                        headers=auth_headers
                    )
                    
                    edge_case_results.append({
                        "case_id": i,
                        "query": edge_case["query"][:100],  # Truncate for logging
                        "status_code": response.status_code,
                        "expected_status": edge_case["expected_status"],
                        "handled_correctly": response.status_code in edge_case["expected_status"]
                    })
                    
                except Exception as e:
                    edge_case_results.append({
                        "case_id": i,
                        "query": edge_case["query"][:100],
                        "error": str(e),
                        "handled_correctly": False
                    })
        
        # Step 3: Validate edge case handling
        correctly_handled = [r for r in edge_case_results if r.get("handled_correctly", False)]
        incorrectly_handled = [r for r in edge_case_results if not r.get("handled_correctly", False)]
        
        handling_rate = (len(correctly_handled) / len(edge_case_results)) * 100
        assert handling_rate >= 80.0, f"Edge case handling rate too low: {handling_rate:.1f}%"
        
        # No edge case should crash the system
        system_crashes = [r for r in edge_case_results if r.get("status_code", 0) == 500]
        assert len(system_crashes) == 0, f"System crashes detected: {len(system_crashes)}"
    
    @pytest.mark.asyncio
    async def test_data_consistency_under_load(self, service_base_url, auth_headers):
        """Test data consistency under load"""
        import httpx
        
        # Step 1: Define consistency test query
        consistency_query = "Data consistency test query for production validation"
        
        # Step 2: Execute same query multiple times concurrently
        async def consistency_request(request_id: int):
            """Execute consistency request"""
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": consistency_query,
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return {
                        "request_id": request_id,
                        "success": True,
                        "response_text": result["response"]["text"],
                        "confidence": result["response"]["confidence"],
                        "references_count": len(result["metadata"].get("references", []))
                    }
                else:
                    return {
                        "request_id": request_id,
                        "success": False,
                        "status_code": response.status_code
                    }
        
        # Step 3: Run concurrent consistency requests
        consistency_tasks = [consistency_request(i) for i in range(20)]
        consistency_results = await asyncio.gather(*consistency_tasks, return_exceptions=True)
        
        # Step 4: Validate data consistency
        successful_results = [r for r in consistency_results if isinstance(r, dict) and r["success"]]
        
        assert len(successful_results) >= 18, "Too many consistency test failures"
        
        # All successful results should be identical (or very similar due to caching)
        if len(successful_results) > 1:
            first_result = successful_results[0]
            
            # Check response consistency
            identical_responses = sum(1 for r in successful_results if r["response_text"] == first_result["response_text"])
            consistency_rate = (identical_responses / len(successful_results)) * 100
            
            # Allow for some variation due to timestamps or randomness
            assert consistency_rate >= 90.0, f"Data consistency too low: {consistency_rate:.1f}%"
            
            # Confidence should be consistent
            confidences = [r["confidence"] for r in successful_results]
            confidence_variance = max(confidences) - min(confidences)
            assert confidence_variance <= 0.1, f"Confidence variance too high: {confidence_variance:.3f}"
    
    @pytest.mark.asyncio
    async def test_production_monitoring_and_alerting(self, service_base_url, auth_headers):
        """Test production monitoring and alerting capabilities"""
        import httpx
        
        # Step 1: Check monitoring endpoints
        monitoring_endpoints = [
            "/health",
            "/metrics",
            "/circuit-breakers"
        ]
        
        monitoring_results = {}
        
        async with httpx.AsyncClient() as client:
            for endpoint in monitoring_endpoints:
                try:
                    response = await client.get(f"{service_base_url}{endpoint}")
                    monitoring_results[endpoint] = {
                        "status_code": response.status_code,
                        "response_time": response.elapsed.total_seconds() if hasattr(response, 'elapsed') else 0,
                        "available": response.status_code == 200
                    }
                except Exception as e:
                    monitoring_results[endpoint] = {
                        "error": str(e),
                        "available": False
                    }
        
        # Step 2: Validate monitoring endpoints
        available_endpoints = [ep for ep, result in monitoring_results.items() if result.get("available", False)]
        
        # At least health endpoint should be available
        assert "/health" in available_endpoints, "Health endpoint not available"
        
        # Step 3: Generate load and check monitoring
        load_tasks = []
        async with httpx.AsyncClient() as client:
            for i in range(30):
                load_tasks.append(client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": f"Monitoring test query {i}",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                ))
        
        await asyncio.gather(*load_tasks, return_exceptions=True)
        
        # Step 4: Check monitoring after load
        async with httpx.AsyncClient() as client:
            health_response = await client.get(f"{service_base_url}/health")
            
            if health_response.status_code == 200:
                health_data = health_response.json()
                
                # Health check should provide meaningful data
                assert "status" in health_data
                assert health_data["status"] in ["healthy", "degraded", "unhealthy"]
                
                # Should have service checks
                if "checks" in health_data:
                    assert isinstance(health_data["checks"], dict)
    
    @pytest.mark.asyncio
    async def test_production_security_validation(self, service_base_url, auth_headers):
        """Test production security validation"""
        import httpx
        
        # Step 1: Test security headers
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{service_base_url}/health")
            
            # Check for security headers
            security_headers = [
                "X-Content-Type-Options",
                "X-Frame-Options",
                "X-XSS-Protection",
                "Strict-Transport-Security"
            ]
            
            present_headers = [h for h in security_headers if h in response.headers]
            # At least some security headers should be present
            assert len(present_headers) >= 1, "No security headers found"
        
        # Step 2: Test rate limiting
        rate_limit_responses = []
        
        async with httpx.AsyncClient() as client:
            for i in range(10):
                try:
                    response = await client.post(
                        f"{service_base_url}/api/v1/query",
                        json={
                            "query": f"Rate limit test {i}",
                            "repository_id": "test_repo"
                        },
                        headers=auth_headers
                    )
                    rate_limit_responses.append(response.status_code)
                except Exception:
                    break
        
        # Should have some successful responses
        successful_responses = [r for r in rate_limit_responses if r == 200]
        assert len(successful_responses) >= 5, "Rate limiting too aggressive"
        
        # Step 3: Test input validation
        validation_tests = [
            {"query": "<script>alert('xss')</script>", "should_block": True},
            {"query": "SELECT * FROM users", "should_block": False},  # Should be processed as normal query
            {"query": "Normal query", "should_block": False}
        ]
        
        async with httpx.AsyncClient() as client:
            for test in validation_tests:
                response = await client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": test["query"],
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                )
                
                if test["should_block"]:
                    # XSS should be handled safely
                    assert response.status_code in [200, 400, 422], "XSS not handled properly"
                else:
                    # Normal queries should work
                    assert response.status_code == 200, f"Normal query blocked: {test['query']}"
    
    @pytest.mark.asyncio
    async def test_production_scalability_validation(self, service_base_url, auth_headers, production_config):
        """Test production scalability validation"""
        import httpx
        
        # Step 1: Gradual load increase test
        load_levels = [5, 10, 20, 30]  # Concurrent requests
        scalability_results = []
        
        for load_level in load_levels:
            async def load_test_request(request_id: int):
                """Load test request"""
                async with httpx.AsyncClient() as client:
                    start_time = time.time()
                    response = await client.post(
                        f"{service_base_url}/api/v1/query",
                        json={
                            "query": f"Scalability test load {load_level} request {request_id}",
                            "repository_id": "test_repo"
                        },
                        headers=auth_headers
                    )
                    end_time = time.time()
                    
                    return {
                        "success": response.status_code == 200,
                        "response_time": end_time - start_time,
                        "load_level": load_level
                    }
            
            # Run load test for current level
            load_tasks = [load_test_request(i) for i in range(load_level)]
            load_start = time.time()
            load_results = await asyncio.gather(*load_tasks, return_exceptions=True)
            load_end = time.time()
            
            # Analyze results for this load level
            successful_results = [r for r in load_results if isinstance(r, dict) and r["success"]]
            success_rate = (len(successful_results) / len(load_results)) * 100
            
            if successful_results:
                avg_response_time = statistics.mean([r["response_time"] for r in successful_results])
                throughput = len(successful_results) / (load_end - load_start)
            else:
                avg_response_time = 0
                throughput = 0
            
            scalability_results.append({
                "load_level": load_level,
                "success_rate": success_rate,
                "avg_response_time": avg_response_time,
                "throughput": throughput
            })
        
        # Step 2: Validate scalability characteristics
        # Success rate should remain high across load levels
        for result in scalability_results:
            assert result["success_rate"] >= 80.0, \
                f"Success rate too low at load level {result['load_level']}: {result['success_rate']:.1f}%"
        
        # Response times should not increase dramatically
        base_response_time = scalability_results[0]["avg_response_time"]
        max_response_time = max(r["avg_response_time"] for r in scalability_results)
        
        # Response time increase should be reasonable
        if base_response_time > 0:
            response_time_increase = max_response_time / base_response_time
            assert response_time_increase <= 5.0, \
                f"Response time increase too high: {response_time_increase:.1f}x"
        
        # Throughput should scale reasonably
        throughputs = [r["throughput"] for r in scalability_results]
        max_throughput = max(throughputs)
        
        assert max_throughput >= 5.0, f"Maximum throughput too low: {max_throughput:.1f} req/s"
    
    @pytest.mark.asyncio
    async def test_production_disaster_recovery(self, service_base_url, auth_headers):
        """Test production disaster recovery scenarios"""
        import httpx
        
        # Step 1: Test service availability
        async with httpx.AsyncClient() as client:
            pre_disaster_response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": "Pre-disaster test query",
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            
            assert pre_disaster_response.status_code == 200
            pre_disaster_result = pre_disaster_response.json()
        
        # Step 2: Simulate partial service degradation
        # (This would typically involve failing some dependencies)
        # For testing, we'll simulate with rapid requests that might stress the system
        stress_tasks = []
        
        async with httpx.AsyncClient() as client:
            for i in range(50):
                stress_tasks.append(client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": f"Stress test query {i}",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                ))
        
        stress_results = await asyncio.gather(*stress_tasks, return_exceptions=True)
        
        # Step 3: Test recovery
        await asyncio.sleep(2)  # Allow system to recover
        
        async with httpx.AsyncClient() as client:
            post_stress_response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": "Post-stress recovery test query",
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            
            # System should recover and work normally
            assert post_stress_response.status_code == 200
            post_stress_result = post_stress_response.json()
            
            # Response should be valid
            assert len(post_stress_result["response"]["text"]) > 0
            assert post_stress_result["response"]["confidence"] >= 0.3
        
        # Step 4: Validate disaster recovery
        successful_stress_responses = [
            r for r in stress_results 
            if isinstance(r, httpx.Response) and r.status_code == 200
        ]
        
        # Some stress responses should succeed
        stress_success_rate = (len(successful_stress_responses) / len(stress_results)) * 100
        assert stress_success_rate >= 50.0, f"Stress test success rate too low: {stress_success_rate:.1f}%"
        
        # System should maintain basic functionality
        assert post_stress_response.status_code == 200, "System did not recover properly"
    
    @pytest.mark.asyncio
    async def test_production_readiness_comprehensive(self, service_base_url, websocket_base_url, auth_headers):
        """Comprehensive production readiness validation"""
        import httpx
        
        # Step 1: Service health validation
        async with httpx.AsyncClient() as client:
            health_response = await client.get(f"{service_base_url}/health")
            assert health_response.status_code == 200
            
            health_data = health_response.json()
            assert health_data["status"] in ["healthy", "degraded"]
        
        # Step 2: API functionality validation
        async with httpx.AsyncClient() as client:
            api_response = await client.post(
                f"{service_base_url}/api/v1/query",
                json={
                    "query": "Production readiness validation query",
                    "repository_id": "test_repo"
                },
                headers=auth_headers
            )
            
            assert api_response.status_code == 200
            api_result = api_response.json()
            
            # Validate response structure
            assert "response" in api_result
            assert "metadata" in api_result
            assert len(api_result["response"]["text"]) > 0
            assert api_result["response"]["confidence"] >= 0.3
        
        # Step 3: WebSocket functionality validation
        from .utils.websocket_client import WebSocketTestClient
        
        ws_client = WebSocketTestClient(websocket_base_url)
        try:
            connected = await ws_client.connect({"Authorization": auth_headers["Authorization"]})
            assert connected, "WebSocket connection failed"
            
            events = await ws_client.query_and_receive("Production readiness WebSocket test")
            assert len(events) > 0, "No WebSocket events received"
            assert not ws_client.has_error(), "WebSocket error occurred"
            
            text_content = ws_client.get_text_content()
            assert len(text_content) > 0, "No WebSocket text content"
            
        finally:
            if ws_client.connected:
                await ws_client.disconnect()
        
        # Step 4: Performance validation
        performance_tasks = []
        
        async with httpx.AsyncClient() as client:
            for i in range(10):
                performance_tasks.append(client.post(
                    f"{service_base_url}/api/v1/query",
                    json={
                        "query": f"Performance validation query {i}",
                        "repository_id": "test_repo"
                    },
                    headers=auth_headers
                ))
        
        performance_start = time.time()
        performance_results = await asyncio.gather(*performance_tasks, return_exceptions=True)
        performance_end = time.time()
        
        # Validate performance
        successful_performance = [
            r for r in performance_results 
            if isinstance(r, httpx.Response) and r.status_code == 200
        ]
        
        performance_success_rate = (len(successful_performance) / len(performance_results)) * 100
        assert performance_success_rate >= 90.0, f"Performance validation success rate too low: {performance_success_rate:.1f}%"
        
        total_time = performance_end - performance_start
        throughput = len(successful_performance) / total_time
        assert throughput >= 3.0, f"Performance validation throughput too low: {throughput:.1f} req/s"
        
        # Step 5: Production readiness score
        readiness_score = 0
        
        # Health check (25 points)
        if health_data["status"] == "healthy":
            readiness_score += 25
        elif health_data["status"] == "degraded":
            readiness_score += 15
        
        # API functionality (25 points)
        if api_result["response"]["confidence"] >= 0.7:
            readiness_score += 25
        elif api_result["response"]["confidence"] >= 0.5:
            readiness_score += 20
        elif api_result["response"]["confidence"] >= 0.3:
            readiness_score += 15
        
        # WebSocket functionality (25 points)
        if len(text_content) > 100:
            readiness_score += 25
        elif len(text_content) > 50:
            readiness_score += 20
        elif len(text_content) > 0:
            readiness_score += 15
        
        # Performance (25 points)
        if performance_success_rate >= 95.0 and throughput >= 5.0:
            readiness_score += 25
        elif performance_success_rate >= 90.0 and throughput >= 3.0:
            readiness_score += 20
        elif performance_success_rate >= 80.0 and throughput >= 2.0:
            readiness_score += 15
        
        # Validate production readiness
        assert readiness_score >= 80, f"Production readiness score too low: {readiness_score}/100"
        
        print(f"Production readiness score: {readiness_score}/100")
        
        if readiness_score >= 90:
            print("✅ PRODUCTION READY - Excellent")
        elif readiness_score >= 80:
            print("✅ PRODUCTION READY - Good")
        else:
            print("❌ NOT PRODUCTION READY")