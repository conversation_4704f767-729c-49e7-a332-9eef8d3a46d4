"""
Performance Monitoring Utilities for E2E Testing

Provides real-time performance monitoring, metrics collection,
and performance analysis tools.
"""

import asyncio
import time
import psutil
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from statistics import mean, median
import json
from datetime import datetime
import threading
from collections import defaultdict


@dataclass
class SystemMetrics:
    """System resource metrics"""
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_bytes_sent: float
    network_bytes_recv: float
    timestamp: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "cpu_percent": self.cpu_percent,
            "memory_percent": self.memory_percent,
            "memory_used_mb": self.memory_used_mb,
            "memory_available_mb": self.memory_available_mb,
            "disk_io_read_mb": self.disk_io_read_mb,
            "disk_io_write_mb": self.disk_io_write_mb,
            "network_bytes_sent": self.network_bytes_sent,
            "network_bytes_recv": self.network_bytes_recv,
            "timestamp": self.timestamp
        }


@dataclass
class RequestMetrics:
    """Individual request metrics"""
    request_id: str
    start_time: float
    end_time: float
    response_time: float
    status_code: int
    success: bool
    error_message: Optional[str] = None
    user_id: Optional[str] = None
    endpoint: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "request_id": self.request_id,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "response_time": self.response_time,
            "status_code": self.status_code,
            "success": self.success,
            "error_message": self.error_message,
            "user_id": self.user_id,
            "endpoint": self.endpoint
        }


class RealTimeMetricsCollector:
    """Real-time metrics collector for performance monitoring"""
    
    def __init__(self, collection_interval: float = 1.0):
        self.collection_interval = collection_interval
        self.system_metrics = []
        self.request_metrics = []
        self.collecting = False
        self.collection_thread = None
        self.start_time = None
        self.network_baseline = None
        self.disk_baseline = None
    
    def start_collection(self):
        """Start metrics collection"""
        if self.collecting:
            return
        
        self.collecting = True
        self.start_time = time.time()
        self.system_metrics.clear()
        self.request_metrics.clear()
        
        # Get baselines
        self.network_baseline = psutil.net_io_counters()
        self.disk_baseline = psutil.disk_io_counters()
        
        self.collection_thread = threading.Thread(target=self._collect_system_metrics)
        self.collection_thread.daemon = True
        self.collection_thread.start()
    
    def stop_collection(self):
        """Stop metrics collection"""
        self.collecting = False
        if self.collection_thread:
            self.collection_thread.join(timeout=2.0)
    
    def _collect_system_metrics(self):
        """Collect system metrics in background thread"""
        while self.collecting:
            try:
                # CPU and memory
                cpu_percent = psutil.cpu_percent(interval=None)
                memory = psutil.virtual_memory()
                
                # Network I/O
                net_io = psutil.net_io_counters()
                net_sent = net_io.bytes_sent - self.network_baseline.bytes_sent
                net_recv = net_io.bytes_recv - self.network_baseline.bytes_recv
                
                # Disk I/O
                disk_io = psutil.disk_io_counters()
                disk_read = (disk_io.read_bytes - self.disk_baseline.read_bytes) / 1024 / 1024
                disk_write = (disk_io.write_bytes - self.disk_baseline.write_bytes) / 1024 / 1024
                
                metrics = SystemMetrics(
                    cpu_percent=cpu_percent,
                    memory_percent=memory.percent,
                    memory_used_mb=memory.used / 1024 / 1024,
                    memory_available_mb=memory.available / 1024 / 1024,
                    disk_io_read_mb=disk_read,
                    disk_io_write_mb=disk_write,
                    network_bytes_sent=net_sent,
                    network_bytes_recv=net_recv,
                    timestamp=time.time()
                )
                
                self.system_metrics.append(metrics)
                
            except Exception as e:
                print(f"Error collecting system metrics: {e}")
            
            time.sleep(self.collection_interval)
    
    def record_request(self, request_metrics: RequestMetrics):
        """Record request metrics"""
        self.request_metrics.append(request_metrics)
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        if not self.system_metrics:
            return {}
        
        # System metrics summary
        latest_system = self.system_metrics[-1]
        avg_cpu = mean([m.cpu_percent for m in self.system_metrics])
        avg_memory = mean([m.memory_percent for m in self.system_metrics])
        
        # Request metrics summary
        successful_requests = [r for r in self.request_metrics if r.success]
        failed_requests = [r for r in self.request_metrics if not r.success]
        
        response_times = [r.response_time for r in self.request_metrics]
        
        return {
            "system": {
                "current_cpu_percent": latest_system.cpu_percent,
                "current_memory_percent": latest_system.memory_percent,
                "average_cpu_percent": avg_cpu,
                "average_memory_percent": avg_memory,
                "memory_used_mb": latest_system.memory_used_mb,
                "total_network_sent": latest_system.network_bytes_sent,
                "total_network_recv": latest_system.network_bytes_recv
            },
            "requests": {
                "total_requests": len(self.request_metrics),
                "successful_requests": len(successful_requests),
                "failed_requests": len(failed_requests),
                "success_rate": len(successful_requests) / len(self.request_metrics) * 100 if self.request_metrics else 0,
                "average_response_time": mean(response_times) if response_times else 0,
                "median_response_time": median(response_times) if response_times else 0,
                "max_response_time": max(response_times) if response_times else 0,
                "min_response_time": min(response_times) if response_times else 0
            },
            "collection_duration": time.time() - self.start_time if self.start_time else 0
        }
    
    def get_metrics_history(self) -> Dict[str, Any]:
        """Get complete metrics history"""
        return {
            "system_metrics": [m.to_dict() for m in self.system_metrics],
            "request_metrics": [r.to_dict() for r in self.request_metrics],
            "collection_start": self.start_time,
            "collection_end": time.time()
        }


class PerformanceThresholdMonitor:
    """Monitor performance thresholds and alert on violations"""
    
    def __init__(self, thresholds: Dict[str, float]):
        self.thresholds = thresholds
        self.violations = []
        self.alerts = []
    
    def check_thresholds(self, metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check current metrics against thresholds"""
        violations = []
        
        # Check CPU threshold
        if "max_cpu_percent" in self.thresholds:
            current_cpu = metrics.get("system", {}).get("current_cpu_percent", 0)
            if current_cpu > self.thresholds["max_cpu_percent"]:
                violation = {
                    "type": "cpu_threshold",
                    "metric": "current_cpu_percent",
                    "value": current_cpu,
                    "threshold": self.thresholds["max_cpu_percent"],
                    "timestamp": time.time()
                }
                violations.append(violation)
                self.violations.append(violation)
        
        # Check memory threshold
        if "max_memory_percent" in self.thresholds:
            current_memory = metrics.get("system", {}).get("current_memory_percent", 0)
            if current_memory > self.thresholds["max_memory_percent"]:
                violation = {
                    "type": "memory_threshold",
                    "metric": "current_memory_percent",
                    "value": current_memory,
                    "threshold": self.thresholds["max_memory_percent"],
                    "timestamp": time.time()
                }
                violations.append(violation)
                self.violations.append(violation)
        
        # Check response time threshold
        if "max_response_time" in self.thresholds:
            avg_response_time = metrics.get("requests", {}).get("average_response_time", 0)
            if avg_response_time > self.thresholds["max_response_time"]:
                violation = {
                    "type": "response_time_threshold",
                    "metric": "average_response_time",
                    "value": avg_response_time,
                    "threshold": self.thresholds["max_response_time"],
                    "timestamp": time.time()
                }
                violations.append(violation)
                self.violations.append(violation)
        
        # Check success rate threshold
        if "min_success_rate" in self.thresholds:
            success_rate = metrics.get("requests", {}).get("success_rate", 100)
            if success_rate < self.thresholds["min_success_rate"]:
                violation = {
                    "type": "success_rate_threshold",
                    "metric": "success_rate",
                    "value": success_rate,
                    "threshold": self.thresholds["min_success_rate"],
                    "timestamp": time.time()
                }
                violations.append(violation)
                self.violations.append(violation)
        
        return violations
    
    def get_all_violations(self) -> List[Dict[str, Any]]:
        """Get all threshold violations"""
        return self.violations.copy()
    
    def clear_violations(self):
        """Clear violation history"""
        self.violations.clear()


class LoadTestAnalyzer:
    """Analyze load test results and provide insights"""
    
    def __init__(self):
        self.analysis_results = {}
    
    def analyze_performance_trends(self, metrics_history: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze performance trends over time"""
        system_metrics = metrics_history.get("system_metrics", [])
        request_metrics = metrics_history.get("request_metrics", [])
        
        if not system_metrics or not request_metrics:
            return {"error": "Insufficient data for analysis"}
        
        # Analyze CPU trends
        cpu_values = [m["cpu_percent"] for m in system_metrics]
        cpu_analysis = {
            "average": mean(cpu_values),
            "peak": max(cpu_values),
            "trend": "increasing" if cpu_values[-1] > cpu_values[0] else "decreasing"
        }
        
        # Analyze memory trends
        memory_values = [m["memory_percent"] for m in system_metrics]
        memory_analysis = {
            "average": mean(memory_values),
            "peak": max(memory_values),
            "trend": "increasing" if memory_values[-1] > memory_values[0] else "decreasing"
        }
        
        # Analyze response time trends
        response_times = [r["response_time"] for r in request_metrics]
        response_analysis = {
            "average": mean(response_times),
            "median": median(response_times),
            "p95": sorted(response_times)[int(0.95 * len(response_times))],
            "p99": sorted(response_times)[int(0.99 * len(response_times))],
            "trend": "degrading" if response_times[-10:] > response_times[:10] else "stable"
        }
        
        # Analyze error patterns
        errors = [r for r in request_metrics if not r["success"]]
        error_analysis = {
            "total_errors": len(errors),
            "error_rate": len(errors) / len(request_metrics) * 100,
            "error_types": self._analyze_error_types(errors)
        }
        
        return {
            "cpu_analysis": cpu_analysis,
            "memory_analysis": memory_analysis,
            "response_time_analysis": response_analysis,
            "error_analysis": error_analysis,
            "test_duration": metrics_history.get("collection_end", 0) - metrics_history.get("collection_start", 0)
        }
    
    def _analyze_error_types(self, errors: List[Dict[str, Any]]) -> Dict[str, int]:
        """Analyze error types and frequencies"""
        error_types = defaultdict(int)
        
        for error in errors:
            error_message = error.get("error_message", "unknown")
            if "timeout" in error_message.lower():
                error_types["timeout"] += 1
            elif "connection" in error_message.lower():
                error_types["connection"] += 1
            elif "rate limit" in error_message.lower():
                error_types["rate_limit"] += 1
            else:
                error_types["other"] += 1
        
        return dict(error_types)
    
    def generate_performance_report(self, metrics_history: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        analysis = self.analyze_performance_trends(metrics_history)
        
        # Performance rating
        cpu_rating = self._rate_performance(analysis["cpu_analysis"]["average"], 80, 90)
        memory_rating = self._rate_performance(analysis["memory_analysis"]["average"], 80, 90)
        response_time_rating = self._rate_performance(analysis["response_time_analysis"]["average"], 2.0, 5.0, reverse=True)
        error_rate_rating = self._rate_performance(analysis["error_analysis"]["error_rate"], 5.0, 10.0, reverse=True)
        
        overall_rating = (cpu_rating + memory_rating + response_time_rating + error_rate_rating) / 4
        
        return {
            "overall_rating": overall_rating,
            "performance_breakdown": {
                "cpu": {"rating": cpu_rating, "value": analysis["cpu_analysis"]["average"]},
                "memory": {"rating": memory_rating, "value": analysis["memory_analysis"]["average"]},
                "response_time": {"rating": response_time_rating, "value": analysis["response_time_analysis"]["average"]},
                "error_rate": {"rating": error_rate_rating, "value": analysis["error_analysis"]["error_rate"]}
            },
            "detailed_analysis": analysis,
            "recommendations": self._generate_recommendations(analysis)
        }
    
    def _rate_performance(self, value: float, good_threshold: float, poor_threshold: float, reverse: bool = False) -> float:
        """Rate performance on a scale of 0-100"""
        if reverse:
            if value <= good_threshold:
                return 100.0
            elif value >= poor_threshold:
                return 0.0
            else:
                return 100.0 - ((value - good_threshold) / (poor_threshold - good_threshold) * 100)
        else:
            if value <= good_threshold:
                return 100.0
            elif value >= poor_threshold:
                return 0.0
            else:
                return 100.0 - ((value - good_threshold) / (poor_threshold - good_threshold) * 100)
    
    def _generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate performance recommendations"""
        recommendations = []
        
        # CPU recommendations
        if analysis["cpu_analysis"]["average"] > 80:
            recommendations.append("High CPU usage detected. Consider optimizing CPU-intensive operations.")
        
        # Memory recommendations
        if analysis["memory_analysis"]["average"] > 80:
            recommendations.append("High memory usage detected. Consider memory optimization or increasing available memory.")
        
        # Response time recommendations
        if analysis["response_time_analysis"]["average"] > 2.0:
            recommendations.append("High response times detected. Consider optimizing query processing or adding caching.")
        
        # Error rate recommendations
        if analysis["error_analysis"]["error_rate"] > 5.0:
            recommendations.append("High error rate detected. Review error logs and improve error handling.")
        
        if not recommendations:
            recommendations.append("Performance is within acceptable limits.")
        
        return recommendations


class ConcurrencyTester:
    """Test system behavior under concurrent load"""
    
    def __init__(self, metrics_collector: RealTimeMetricsCollector):
        self.metrics_collector = metrics_collector
        self.concurrent_tasks = []
        self.results = []
    
    async def run_concurrent_test(
        self,
        test_func: Callable,
        concurrent_count: int,
        iterations_per_task: int = 1
    ) -> Dict[str, Any]:
        """Run concurrent test with specified function"""
        
        async def task_runner(task_id: int):
            """Individual task runner"""
            task_results = []
            
            for iteration in range(iterations_per_task):
                start_time = time.time()
                
                try:
                    result = await test_func(task_id=task_id, iteration=iteration)
                    end_time = time.time()
                    
                    request_metrics = RequestMetrics(
                        request_id=f"task_{task_id}_iter_{iteration}",
                        start_time=start_time,
                        end_time=end_time,
                        response_time=end_time - start_time,
                        status_code=200,
                        success=True,
                        user_id=f"user_{task_id}"
                    )
                    
                    self.metrics_collector.record_request(request_metrics)
                    task_results.append(result)
                    
                except Exception as e:
                    end_time = time.time()
                    
                    request_metrics = RequestMetrics(
                        request_id=f"task_{task_id}_iter_{iteration}",
                        start_time=start_time,
                        end_time=end_time,
                        response_time=end_time - start_time,
                        status_code=500,
                        success=False,
                        error_message=str(e),
                        user_id=f"user_{task_id}"
                    )
                    
                    self.metrics_collector.record_request(request_metrics)
                    task_results.append({"error": str(e)})
            
            return task_results
        
        # Start metrics collection
        self.metrics_collector.start_collection()
        
        # Run concurrent tasks
        tasks = [task_runner(i) for i in range(concurrent_count)]
        task_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Stop metrics collection
        self.metrics_collector.stop_collection()
        
        # Analyze results
        successful_tasks = sum(1 for result in task_results if not isinstance(result, Exception))
        failed_tasks = concurrent_count - successful_tasks
        
        return {
            "concurrent_tasks": concurrent_count,
            "iterations_per_task": iterations_per_task,
            "successful_tasks": successful_tasks,
            "failed_tasks": failed_tasks,
            "task_results": task_results,
            "performance_metrics": self.metrics_collector.get_current_metrics()
        }