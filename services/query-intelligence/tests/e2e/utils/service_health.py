"""
Service Health Monitoring Utilities

Provides health checking, service discovery, and dependency validation
for comprehensive E2E testing.
"""

import asyncio
import httpx
import redis
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json


class ServiceStatus(Enum):
    """Service health status"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNAVAILABLE = "unavailable"


@dataclass
class HealthCheckResult:
    """Health check result"""
    service_name: str
    status: ServiceStatus
    response_time_ms: float
    error_message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    timestamp: float = 0.0
    
    def __post_init__(self):
        if self.timestamp == 0.0:
            self.timestamp = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "service_name": self.service_name,
            "status": self.status.value,
            "response_time_ms": self.response_time_ms,
            "error_message": self.error_message,
            "details": self.details,
            "timestamp": self.timestamp
        }


class ServiceHealthChecker:
    """Comprehensive service health checker"""
    
    def __init__(self, timeout: float = 10.0):
        self.timeout = timeout
        self.health_history = []
    
    async def check_query_intelligence_service(self, base_url: str) -> HealthCheckResult:
        """Check main query intelligence service health"""
        start_time = time.time()
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(f"{base_url}/health")
                response_time = (time.time() - start_time) * 1000
                
                if response.status_code == 200:
                    health_data = response.json()
                    
                    # Determine overall status based on health data
                    if health_data.get("status") == "healthy":
                        status = ServiceStatus.HEALTHY
                    elif health_data.get("status") == "degraded":
                        status = ServiceStatus.DEGRADED
                    else:
                        status = ServiceStatus.UNHEALTHY
                    
                    return HealthCheckResult(
                        service_name="query_intelligence",
                        status=status,
                        response_time_ms=response_time,
                        details=health_data
                    )
                else:
                    return HealthCheckResult(
                        service_name="query_intelligence",
                        status=ServiceStatus.UNHEALTHY,
                        response_time_ms=response_time,
                        error_message=f"HTTP {response.status_code}: {response.text}"
                    )
                    
        except httpx.TimeoutException:
            return HealthCheckResult(
                service_name="query_intelligence",
                status=ServiceStatus.UNAVAILABLE,
                response_time_ms=self.timeout * 1000,
                error_message="Request timeout"
            )
        except Exception as e:
            return HealthCheckResult(
                service_name="query_intelligence",
                status=ServiceStatus.UNAVAILABLE,
                response_time_ms=(time.time() - start_time) * 1000,
                error_message=str(e)
            )
    
    async def check_redis_service(self, redis_url: str) -> HealthCheckResult:
        """Check Redis service health"""
        start_time = time.time()
        
        try:
            redis_client = redis.from_url(redis_url)
            
            # Test connection with ping
            await asyncio.get_event_loop().run_in_executor(
                None, redis_client.ping
            )
            
            response_time = (time.time() - start_time) * 1000
            
            # Get Redis info
            info = await asyncio.get_event_loop().run_in_executor(
                None, redis_client.info
            )
            
            return HealthCheckResult(
                service_name="redis",
                status=ServiceStatus.HEALTHY,
                response_time_ms=response_time,
                details={
                    "redis_version": info.get("redis_version"),
                    "connected_clients": info.get("connected_clients"),
                    "used_memory": info.get("used_memory"),
                    "uptime_in_seconds": info.get("uptime_in_seconds")
                }
            )
            
        except redis.ConnectionError:
            return HealthCheckResult(
                service_name="redis",
                status=ServiceStatus.UNAVAILABLE,
                response_time_ms=(time.time() - start_time) * 1000,
                error_message="Redis connection failed"
            )
        except Exception as e:
            return HealthCheckResult(
                service_name="redis",
                status=ServiceStatus.UNAVAILABLE,
                response_time_ms=(time.time() - start_time) * 1000,
                error_message=str(e)
            )
    
    async def check_analysis_engine_service(self, analysis_engine_url: str) -> HealthCheckResult:
        """Check Analysis Engine service health"""
        start_time = time.time()
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(f"{analysis_engine_url}/health")
                response_time = (time.time() - start_time) * 1000
                
                if response.status_code == 200:
                    health_data = response.json()
                    return HealthCheckResult(
                        service_name="analysis_engine",
                        status=ServiceStatus.HEALTHY,
                        response_time_ms=response_time,
                        details=health_data
                    )
                else:
                    return HealthCheckResult(
                        service_name="analysis_engine",
                        status=ServiceStatus.UNHEALTHY,
                        response_time_ms=response_time,
                        error_message=f"HTTP {response.status_code}"
                    )
                    
        except httpx.TimeoutException:
            return HealthCheckResult(
                service_name="analysis_engine",
                status=ServiceStatus.UNAVAILABLE,
                response_time_ms=self.timeout * 1000,
                error_message="Request timeout"
            )
        except Exception as e:
            return HealthCheckResult(
                service_name="analysis_engine",
                status=ServiceStatus.UNAVAILABLE,
                response_time_ms=(time.time() - start_time) * 1000,
                error_message=str(e)
            )
    
    async def check_pattern_mining_service(self, pattern_mining_url: str) -> HealthCheckResult:
        """Check Pattern Mining service health"""
        start_time = time.time()
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(f"{pattern_mining_url}/health")
                response_time = (time.time() - start_time) * 1000
                
                if response.status_code == 200:
                    health_data = response.json()
                    return HealthCheckResult(
                        service_name="pattern_mining",
                        status=ServiceStatus.HEALTHY,
                        response_time_ms=response_time,
                        details=health_data
                    )
                else:
                    return HealthCheckResult(
                        service_name="pattern_mining",
                        status=ServiceStatus.UNHEALTHY,
                        response_time_ms=response_time,
                        error_message=f"HTTP {response.status_code}"
                    )
                    
        except httpx.TimeoutException:
            return HealthCheckResult(
                service_name="pattern_mining",
                status=ServiceStatus.UNAVAILABLE,
                response_time_ms=self.timeout * 1000,
                error_message="Request timeout"
            )
        except Exception as e:
            return HealthCheckResult(
                service_name="pattern_mining",
                status=ServiceStatus.UNAVAILABLE,
                response_time_ms=(time.time() - start_time) * 1000,
                error_message=str(e)
            )
    
    async def check_all_services(self, config: Dict[str, str]) -> Dict[str, HealthCheckResult]:
        """Check all services concurrently"""
        tasks = []
        
        # Query Intelligence service
        if "query_intelligence_url" in config:
            tasks.append(self.check_query_intelligence_service(config["query_intelligence_url"]))
        
        # Redis service
        if "redis_url" in config:
            tasks.append(self.check_redis_service(config["redis_url"]))
        
        # Analysis Engine service
        if "analysis_engine_url" in config:
            tasks.append(self.check_analysis_engine_service(config["analysis_engine_url"]))
        
        # Pattern Mining service
        if "pattern_mining_url" in config:
            tasks.append(self.check_pattern_mining_service(config["pattern_mining_url"]))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        health_results = {}
        for result in results:
            if isinstance(result, HealthCheckResult):
                health_results[result.service_name] = result
                self.health_history.append(result)
            elif isinstance(result, Exception):
                health_results["error"] = HealthCheckResult(
                    service_name="unknown",
                    status=ServiceStatus.UNAVAILABLE,
                    response_time_ms=0,
                    error_message=str(result)
                )
        
        return health_results
    
    def get_system_health_summary(self, health_results: Dict[str, HealthCheckResult]) -> Dict[str, Any]:
        """Get overall system health summary"""
        total_services = len(health_results)
        healthy_services = sum(1 for r in health_results.values() if r.status == ServiceStatus.HEALTHY)
        degraded_services = sum(1 for r in health_results.values() if r.status == ServiceStatus.DEGRADED)
        unhealthy_services = sum(1 for r in health_results.values() if r.status == ServiceStatus.UNHEALTHY)
        unavailable_services = sum(1 for r in health_results.values() if r.status == ServiceStatus.UNAVAILABLE)
        
        # Overall system status
        if unhealthy_services > 0 or unavailable_services > 0:
            if healthy_services == 0:
                overall_status = ServiceStatus.UNAVAILABLE
            else:
                overall_status = ServiceStatus.UNHEALTHY
        elif degraded_services > 0:
            overall_status = ServiceStatus.DEGRADED
        else:
            overall_status = ServiceStatus.HEALTHY
        
        # Average response time
        response_times = [r.response_time_ms for r in health_results.values()]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        return {
            "overall_status": overall_status.value,
            "total_services": total_services,
            "healthy_services": healthy_services,
            "degraded_services": degraded_services,
            "unhealthy_services": unhealthy_services,
            "unavailable_services": unavailable_services,
            "average_response_time_ms": avg_response_time,
            "health_percentage": (healthy_services / total_services * 100) if total_services > 0 else 0
        }
    
    def get_health_history(self) -> List[Dict[str, Any]]:
        """Get health check history"""
        return [result.to_dict() for result in self.health_history]


class ServiceDependencyValidator:
    """Validate service dependencies and integration points"""
    
    def __init__(self, health_checker: ServiceHealthChecker):
        self.health_checker = health_checker
    
    async def validate_query_processing_pipeline(self, config: Dict[str, str]) -> Dict[str, Any]:
        """Validate the complete query processing pipeline"""
        validation_results = {
            "pipeline_healthy": True,
            "validations": [],
            "issues": []
        }
        
        # Check all services
        health_results = await self.health_checker.check_all_services(config)
        
        # Validate query intelligence service
        qi_health = health_results.get("query_intelligence")
        if not qi_health or qi_health.status != ServiceStatus.HEALTHY:
            validation_results["pipeline_healthy"] = False
            validation_results["issues"].append("Query Intelligence service is not healthy")
        
        # Validate Redis (optional but recommended)
        redis_health = health_results.get("redis")
        if redis_health and redis_health.status == ServiceStatus.UNAVAILABLE:
            validation_results["issues"].append("Redis service is unavailable - caching disabled")
        
        # Validate Analysis Engine (optional)
        analysis_health = health_results.get("analysis_engine")
        if analysis_health and analysis_health.status == ServiceStatus.UNAVAILABLE:
            validation_results["issues"].append("Analysis Engine service is unavailable - analysis features disabled")
        
        # Validate Pattern Mining (optional)
        pattern_health = health_results.get("pattern_mining")
        if pattern_health and pattern_health.status == ServiceStatus.UNAVAILABLE:
            validation_results["issues"].append("Pattern Mining service is unavailable - pattern features disabled")
        
        validation_results["validations"] = [result.to_dict() for result in health_results.values()]
        
        return validation_results
    
    async def validate_websocket_capabilities(self, base_url: str, auth_headers: Dict[str, str]) -> Dict[str, Any]:
        """Validate WebSocket capabilities"""
        validation_results = {
            "websocket_healthy": True,
            "connection_successful": False,
            "streaming_functional": False,
            "error_message": None
        }
        
        try:
            import websockets
            
            websocket_url = f"{base_url.replace('http', 'ws')}/api/v1/ws/query"
            
            async with websockets.connect(websocket_url, extra_headers=auth_headers) as websocket:
                validation_results["connection_successful"] = True
                
                # Test streaming with a simple query
                test_query = {
                    "query": "Health check query",
                    "repository_id": "test_repo"
                }
                
                await websocket.send(json.dumps(test_query))
                
                # Wait for acknowledgment
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(response)
                
                if data.get("type") == "acknowledged":
                    validation_results["streaming_functional"] = True
                
        except Exception as e:
            validation_results["websocket_healthy"] = False
            validation_results["error_message"] = str(e)
        
        return validation_results
    
    async def validate_authentication_integration(self, base_url: str, auth_headers: Dict[str, str]) -> Dict[str, Any]:
        """Validate authentication integration"""
        validation_results = {
            "auth_healthy": True,
            "jwt_validation_working": False,
            "rate_limiting_working": False,
            "error_message": None
        }
        
        try:
            async with httpx.AsyncClient() as client:
                # Test authenticated request
                response = await client.post(
                    f"{base_url}/api/v1/query",
                    json={"query": "test", "repository_id": "test"},
                    headers=auth_headers
                )
                
                if response.status_code != 401:  # Should not get unauthorized
                    validation_results["jwt_validation_working"] = True
                
                # Test unauthenticated request
                response = await client.post(
                    f"{base_url}/api/v1/query",
                    json={"query": "test", "repository_id": "test"}
                )
                
                # Should get 401 or rate limited
                if response.status_code in [401, 429]:
                    validation_results["rate_limiting_working"] = True
                
        except Exception as e:
            validation_results["auth_healthy"] = False
            validation_results["error_message"] = str(e)
        
        return validation_results


class CircuitBreakerTester:
    """Test circuit breaker behavior"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
    
    async def test_circuit_breaker_behavior(self, auth_headers: Dict[str, str]) -> Dict[str, Any]:
        """Test circuit breaker behavior under failure conditions"""
        results = {
            "circuit_breaker_functional": True,
            "tests": []
        }
        
        try:
            async with httpx.AsyncClient() as client:
                # Check circuit breaker status endpoint
                response = await client.get(f"{self.base_url}/circuit-breakers")
                
                if response.status_code == 200:
                    breaker_status = response.json()
                    results["tests"].append({
                        "test": "circuit_breaker_status",
                        "success": True,
                        "data": breaker_status
                    })
                else:
                    results["tests"].append({
                        "test": "circuit_breaker_status",
                        "success": False,
                        "error": f"HTTP {response.status_code}"
                    })
                
                # Test query under normal conditions
                response = await client.post(
                    f"{self.base_url}/api/v1/query",
                    json={"query": "test circuit breaker", "repository_id": "test"},
                    headers=auth_headers
                )
                
                results["tests"].append({
                    "test": "normal_query",
                    "success": response.status_code == 200,
                    "status_code": response.status_code
                })
                
        except Exception as e:
            results["circuit_breaker_functional"] = False
            results["tests"].append({
                "test": "circuit_breaker_test",
                "success": False,
                "error": str(e)
            })
        
        return results
    
    async def simulate_service_failure(self, service_name: str) -> Dict[str, Any]:
        """Simulate service failure and test recovery"""
        # This would typically integrate with service mocks
        # For now, return a placeholder result
        return {
            "service": service_name,
            "failure_simulated": True,
            "recovery_time_seconds": 0,
            "circuit_breaker_activated": False
        }


class HealthMonitoringDashboard:
    """Real-time health monitoring dashboard"""
    
    def __init__(self, health_checker: ServiceHealthChecker):
        self.health_checker = health_checker
        self.monitoring_active = False
        self.health_snapshots = []
    
    async def start_monitoring(self, config: Dict[str, str], interval: float = 30.0):
        """Start continuous health monitoring"""
        self.monitoring_active = True
        
        while self.monitoring_active:
            try:
                health_results = await self.health_checker.check_all_services(config)
                system_summary = self.health_checker.get_system_health_summary(health_results)
                
                snapshot = {
                    "timestamp": time.time(),
                    "system_summary": system_summary,
                    "service_details": {name: result.to_dict() for name, result in health_results.items()}
                }
                
                self.health_snapshots.append(snapshot)
                
                # Keep only last 100 snapshots
                if len(self.health_snapshots) > 100:
                    self.health_snapshots = self.health_snapshots[-100:]
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                print(f"Health monitoring error: {e}")
                await asyncio.sleep(interval)
    
    def stop_monitoring(self):
        """Stop health monitoring"""
        self.monitoring_active = False
    
    def get_health_dashboard(self) -> Dict[str, Any]:
        """Get current health dashboard data"""
        if not self.health_snapshots:
            return {"error": "No health data available"}
        
        latest_snapshot = self.health_snapshots[-1]
        
        return {
            "current_status": latest_snapshot["system_summary"],
            "service_details": latest_snapshot["service_details"],
            "health_trend": self._calculate_health_trend(),
            "uptime_percentage": self._calculate_uptime(),
            "alerts": self._generate_alerts()
        }
    
    def _calculate_health_trend(self) -> str:
        """Calculate health trend over time"""
        if len(self.health_snapshots) < 2:
            return "stable"
        
        recent_health = [s["system_summary"]["health_percentage"] for s in self.health_snapshots[-10:]]
        
        if len(recent_health) >= 2:
            trend = recent_health[-1] - recent_health[0]
            if trend > 5:
                return "improving"
            elif trend < -5:
                return "degrading"
        
        return "stable"
    
    def _calculate_uptime(self) -> float:
        """Calculate system uptime percentage"""
        if not self.health_snapshots:
            return 0.0
        
        healthy_snapshots = sum(1 for s in self.health_snapshots if s["system_summary"]["overall_status"] == "healthy")
        return (healthy_snapshots / len(self.health_snapshots)) * 100
    
    def _generate_alerts(self) -> List[Dict[str, Any]]:
        """Generate health alerts"""
        alerts = []
        
        if self.health_snapshots:
            latest = self.health_snapshots[-1]
            
            # Check for unhealthy services
            if latest["system_summary"]["unhealthy_services"] > 0:
                alerts.append({
                    "type": "service_unhealthy",
                    "message": f"{latest['system_summary']['unhealthy_services']} services are unhealthy",
                    "severity": "high"
                })
            
            # Check for degraded services
            if latest["system_summary"]["degraded_services"] > 0:
                alerts.append({
                    "type": "service_degraded",
                    "message": f"{latest['system_summary']['degraded_services']} services are degraded",
                    "severity": "medium"
                })
            
            # Check for high response times
            if latest["system_summary"]["average_response_time_ms"] > 1000:
                alerts.append({
                    "type": "high_response_time",
                    "message": f"Average response time is {latest['system_summary']['average_response_time_ms']:.1f}ms",
                    "severity": "medium"
                })
        
        return alerts