"""
WebSocket Client for E2E Testing

Provides a comprehensive WebSocket client for testing streaming functionality
and real-time communication patterns.
"""

import asyncio
import json
import websockets
import time
from typing import Dict, List, Any, Optional, Callable, AsyncIterator
from dataclasses import dataclass
from enum import Enum


class WebSocketEventType(Enum):
    """WebSocket event types"""
    ACKNOWLEDGED = "acknowledged"
    PROCESSING_STARTED = "processing_started"
    INTENT_ANALYZED = "intent_analyzed"
    STATUS = "status"
    SEARCH_COMPLETE = "search_complete"
    REFERENCE = "reference"
    TEXT = "text"
    DONE = "done"
    ERROR = "error"


@dataclass
class WebSocketEvent:
    """WebSocket event data"""
    type: str
    timestamp: float
    data: Dict[str, Any]
    
    @classmethod
    def from_message(cls, message: str) -> 'WebSocketEvent':
        """Create event from WebSocket message"""
        data = json.loads(message)
        return cls(
            type=data.get("type", "unknown"),
            timestamp=time.time(),
            data=data
        )


class WebSocketTestClient:
    """WebSocket client for testing"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.connection = None
        self.events = []
        self.connected = False
        self.connection_errors = []
    
    async def connect(self, headers: Dict[str, str] = None) -> bool:
        """Connect to WebSocket endpoint"""
        try:
            websocket_url = f"{self.base_url}/api/v1/ws/query"
            self.connection = await websockets.connect(
                websocket_url,
                extra_headers=headers or {}
            )
            self.connected = True
            return True
        except Exception as e:
            self.connection_errors.append({
                "timestamp": time.time(),
                "error": str(e),
                "error_type": type(e).__name__
            })
            return False
    
    async def disconnect(self):
        """Disconnect from WebSocket"""
        if self.connection:
            await self.connection.close()
            self.connected = False
    
    async def send_query(self, query: str, repository_id: str = "test_repo") -> Dict[str, Any]:
        """Send a query and return the request data"""
        if not self.connected:
            raise RuntimeError("WebSocket not connected")
        
        request_data = {
            "query": query,
            "repository_id": repository_id
        }
        
        await self.connection.send(json.dumps(request_data))
        return request_data
    
    async def receive_events(self, timeout: float = 30.0) -> List[WebSocketEvent]:
        """Receive all events until done or timeout"""
        events = []
        start_time = time.time()
        
        try:
            while time.time() - start_time < timeout:
                try:
                    message = await asyncio.wait_for(
                        self.connection.recv(), 
                        timeout=1.0
                    )
                    event = WebSocketEvent.from_message(message)
                    events.append(event)
                    
                    # Stop if we receive done or error
                    if event.type in ["done", "error"]:
                        break
                        
                except asyncio.TimeoutError:
                    continue
                except websockets.exceptions.ConnectionClosed:
                    break
                    
        except Exception as e:
            events.append(WebSocketEvent(
                type="error",
                timestamp=time.time(),
                data={"error": str(e), "error_type": type(e).__name__}
            ))
        
        self.events.extend(events)
        return events
    
    async def query_and_receive(
        self, 
        query: str, 
        repository_id: str = "test_repo",
        timeout: float = 30.0
    ) -> List[WebSocketEvent]:
        """Send query and receive all events"""
        await self.send_query(query, repository_id)
        return await self.receive_events(timeout)
    
    def get_events_by_type(self, event_type: str) -> List[WebSocketEvent]:
        """Get events by type"""
        return [event for event in self.events if event.type == event_type]
    
    def get_text_content(self) -> str:
        """Get concatenated text content from text events"""
        text_events = self.get_events_by_type("text")
        return "".join(event.data.get("content", "") for event in text_events)
    
    def get_references(self) -> List[Dict[str, Any]]:
        """Get all references from reference events"""
        reference_events = self.get_events_by_type("reference")
        return [event.data.get("reference", {}) for event in reference_events]
    
    def get_final_metadata(self) -> Optional[Dict[str, Any]]:
        """Get metadata from done event"""
        done_events = self.get_events_by_type("done")
        if done_events:
            return done_events[-1].data.get("metadata", {})
        return None
    
    def has_error(self) -> bool:
        """Check if any error events were received"""
        return len(self.get_events_by_type("error")) > 0
    
    def get_errors(self) -> List[Dict[str, Any]]:
        """Get all error events"""
        error_events = self.get_events_by_type("error")
        return [event.data for event in error_events]
    
    def clear_events(self):
        """Clear stored events"""
        self.events.clear()


class WebSocketSessionManager:
    """Manage multiple WebSocket sessions for testing"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.sessions = {}
        self.session_counter = 0
    
    async def create_session(self, headers: Dict[str, str] = None) -> str:
        """Create a new WebSocket session"""
        session_id = f"session_{self.session_counter}"
        self.session_counter += 1
        
        client = WebSocketTestClient(self.base_url)
        connected = await client.connect(headers)
        
        if connected:
            self.sessions[session_id] = client
            return session_id
        else:
            raise RuntimeError(f"Failed to create WebSocket session: {client.connection_errors}")
    
    async def close_session(self, session_id: str):
        """Close a WebSocket session"""
        if session_id in self.sessions:
            await self.sessions[session_id].disconnect()
            del self.sessions[session_id]
    
    async def close_all_sessions(self):
        """Close all WebSocket sessions"""
        for session_id in list(self.sessions.keys()):
            await self.close_session(session_id)
    
    def get_session(self, session_id: str) -> Optional[WebSocketTestClient]:
        """Get a WebSocket session by ID"""
        return self.sessions.get(session_id)
    
    def get_all_sessions(self) -> Dict[str, WebSocketTestClient]:
        """Get all active sessions"""
        return self.sessions.copy()
    
    async def broadcast_query(self, query: str, repository_id: str = "test_repo") -> Dict[str, List[WebSocketEvent]]:
        """Send query to all active sessions"""
        results = {}
        
        tasks = []
        for session_id, client in self.sessions.items():
            tasks.append(self._session_query(session_id, client, query, repository_id))
        
        session_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, (session_id, _) in enumerate(self.sessions.items()):
            if isinstance(session_results[i], Exception):
                results[session_id] = [WebSocketEvent(
                    type="error",
                    timestamp=time.time(),
                    data={"error": str(session_results[i])}
                )]
            else:
                results[session_id] = session_results[i]
        
        return results
    
    async def _session_query(self, session_id: str, client: WebSocketTestClient, query: str, repository_id: str) -> List[WebSocketEvent]:
        """Execute query for a single session"""
        return await client.query_and_receive(query, repository_id)


class WebSocketEventValidator:
    """Validate WebSocket events and sequences"""
    
    def __init__(self, expected_events: List[str]):
        self.expected_events = expected_events
        self.validation_results = []
    
    def validate_event_sequence(self, events: List[WebSocketEvent]) -> Dict[str, Any]:
        """Validate event sequence against expected events"""
        actual_events = [event.type for event in events]
        
        validation_result = {
            "passed": True,
            "expected_events": self.expected_events,
            "actual_events": actual_events,
            "missing_events": [],
            "unexpected_events": [],
            "event_timing": []
        }
        
        # Check for missing events
        for expected in self.expected_events:
            if expected not in actual_events:
                validation_result["missing_events"].append(expected)
                validation_result["passed"] = False
        
        # Check for unexpected events
        for actual in actual_events:
            if actual not in self.expected_events and actual != "status":  # Status events are optional
                validation_result["unexpected_events"].append(actual)
        
        # Calculate event timing
        if events:
            start_time = events[0].timestamp
            for event in events:
                validation_result["event_timing"].append({
                    "type": event.type,
                    "timestamp": event.timestamp,
                    "relative_time": event.timestamp - start_time
                })
        
        return validation_result
    
    def validate_content_quality(self, events: List[WebSocketEvent]) -> Dict[str, Any]:
        """Validate content quality of events"""
        validation_result = {
            "passed": True,
            "issues": []
        }
        
        text_events = [e for e in events if e.type == "text"]
        reference_events = [e for e in events if e.type == "reference"]
        done_events = [e for e in events if e.type == "done"]
        
        # Check if we have text content
        if not text_events:
            validation_result["passed"] = False
            validation_result["issues"].append("No text content received")
        
        # Check if we have references
        if not reference_events:
            validation_result["passed"] = False
            validation_result["issues"].append("No references received")
        
        # Check if we have completion event
        if not done_events:
            validation_result["passed"] = False
            validation_result["issues"].append("No completion event received")
        
        # Check content length
        total_text = "".join(event.data.get("content", "") for event in text_events)
        if len(total_text) < 50:
            validation_result["issues"].append("Response content is too short")
        
        # Check metadata in done event
        if done_events:
            metadata = done_events[-1].data.get("metadata", {})
            if "confidence" not in metadata:
                validation_result["issues"].append("Missing confidence in metadata")
            if "follow_up_questions" not in metadata:
                validation_result["issues"].append("Missing follow-up questions in metadata")
        
        return validation_result


class ConcurrentWebSocketTester:
    """Test concurrent WebSocket connections"""
    
    def __init__(self, base_url: str, max_connections: int = 50):
        self.base_url = base_url
        self.max_connections = max_connections
        self.results = []
    
    async def test_concurrent_connections(
        self,
        concurrent_users: int,
        queries_per_user: int,
        headers_generator: Callable[[int], Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """Test concurrent WebSocket connections"""
        
        async def user_session(user_id: int):
            """Single user session"""
            session_results = {
                "user_id": user_id,
                "successful_queries": 0,
                "failed_queries": 0,
                "total_events": 0,
                "errors": []
            }
            
            try:
                headers = headers_generator(user_id) if headers_generator else {}
                client = WebSocketTestClient(self.base_url)
                
                connected = await client.connect(headers)
                if not connected:
                    session_results["errors"].append("Failed to connect")
                    return session_results
                
                for query_num in range(queries_per_user):
                    try:
                        query = f"Test query {query_num} from user {user_id}"
                        events = await client.query_and_receive(query)
                        
                        session_results["total_events"] += len(events)
                        
                        if any(event.type == "error" for event in events):
                            session_results["failed_queries"] += 1
                            session_results["errors"].append(f"Query {query_num} failed")
                        else:
                            session_results["successful_queries"] += 1
                            
                    except Exception as e:
                        session_results["failed_queries"] += 1
                        session_results["errors"].append(f"Query {query_num} exception: {str(e)}")
                
                await client.disconnect()
                
            except Exception as e:
                session_results["errors"].append(f"Session exception: {str(e)}")
            
            return session_results
        
        # Run concurrent user sessions
        tasks = [user_session(user_id) for user_id in range(concurrent_users)]
        user_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Aggregate results
        total_successful = 0
        total_failed = 0
        total_events = 0
        all_errors = []
        
        for result in user_results:
            if isinstance(result, Exception):
                all_errors.append(f"User session exception: {str(result)}")
            else:
                total_successful += result["successful_queries"]
                total_failed += result["failed_queries"]
                total_events += result["total_events"]
                all_errors.extend(result["errors"])
        
        total_queries = total_successful + total_failed
        success_rate = (total_successful / total_queries * 100) if total_queries > 0 else 0
        
        return {
            "concurrent_users": concurrent_users,
            "queries_per_user": queries_per_user,
            "total_queries": total_queries,
            "successful_queries": total_successful,
            "failed_queries": total_failed,
            "success_rate": success_rate,
            "total_events": total_events,
            "errors": all_errors,
            "user_results": user_results
        }