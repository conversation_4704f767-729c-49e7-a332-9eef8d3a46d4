"""
Performance Regression Detection and Analysis

Advanced statistical analysis for detecting performance regressions,
including hypothesis testing, confidence intervals, and anomaly detection.
"""

import time
import asyncio
import statistics
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
from scipy import stats
import json
from datetime import datetime

from .performance_baselines import (
    PerformanceBaseline, BaselineMetrics, BaselineComparison, BaselineManager
)
from ..e2e.utils.performance_monitors import RealTimeMetricsCollector, RequestMetrics


class RegressionSeverity(Enum):
    """Regression severity levels"""
    NONE = "none"
    MINOR = "minor"
    MODERATE = "moderate"
    SEVERE = "severe"
    CRITICAL = "critical"


class RegressionTestType(Enum):
    """Types of regression tests"""
    RESPONSE_TIME = "response_time"
    THROUGHPUT = "throughput"
    ERROR_RATE = "error_rate"
    RESOURCE_USAGE = "resource_usage"
    COMPREHENSIVE = "comprehensive"


@dataclass
class RegressionTestConfig:
    """Configuration for regression tests"""
    
    # Test parameters
    test_duration: float = 60.0  # seconds
    warmup_duration: float = 10.0  # seconds
    concurrent_users: int = 10
    requests_per_user: int = 5
    
    # Statistical parameters
    confidence_level: float = 0.95
    significance_threshold: float = 0.05
    effect_size_threshold: float = 0.2  # Cohen's d
    
    # Regression thresholds
    response_time_threshold: float = 20.0  # % increase
    throughput_threshold: float = 15.0     # % decrease
    error_rate_threshold: float = 0.5      # % increase
    cpu_threshold: float = 25.0            # % increase
    memory_threshold: float = 25.0         # % increase
    
    # Test environment
    environment: str = "test"
    endpoints_to_test: List[str] = field(default_factory=lambda: ["/api/v1/query"])
    
    # Retry configuration
    max_retries: int = 3
    retry_delay: float = 5.0


@dataclass
class RegressionResult:
    """Result of a regression test"""
    
    # Test identification
    test_id: str
    test_type: RegressionTestType
    baseline_id: str
    baseline_version: str
    
    # Test execution
    start_time: datetime
    end_time: datetime
    duration: float
    
    # Results
    is_regression: bool
    severity: RegressionSeverity
    confidence_score: float
    
    # Statistical analysis
    statistical_significance: bool
    p_value: float
    effect_size: float
    
    # Metrics comparison
    baseline_metrics: BaselineMetrics
    current_metrics: BaselineMetrics
    comparison: BaselineComparison
    
    # Detailed analysis
    regression_factors: List[str]
    performance_changes: Dict[str, float]
    recommendations: List[str]
    
    # Raw data
    raw_data: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "test_id": self.test_id,
            "test_type": self.test_type.value,
            "baseline_id": self.baseline_id,
            "baseline_version": self.baseline_version,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat(),
            "duration": self.duration,
            "is_regression": self.is_regression,
            "severity": self.severity.value,
            "confidence_score": self.confidence_score,
            "statistical_significance": self.statistical_significance,
            "p_value": self.p_value,
            "effect_size": self.effect_size,
            "baseline_metrics": self.baseline_metrics.to_dict(),
            "current_metrics": self.current_metrics.to_dict(),
            "comparison": self.comparison.to_dict(),
            "regression_factors": self.regression_factors,
            "performance_changes": self.performance_changes,
            "recommendations": self.recommendations,
            "raw_data": self.raw_data
        }


class RegressionDetector:
    """Statistical regression detector"""
    
    def __init__(self, baseline_manager: BaselineManager):
        self.baseline_manager = baseline_manager
        self.test_results = []
    
    def welch_t_test(self, baseline_data: List[float], current_data: List[float]) -> Tuple[float, float]:
        """
        Perform Welch's t-test for unequal variances
        Returns (t_statistic, p_value)
        """
        if len(baseline_data) < 2 or len(current_data) < 2:
            return 0.0, 1.0
        
        try:
            t_stat, p_value = stats.ttest_ind(baseline_data, current_data, equal_var=False)
            return abs(t_stat), p_value
        except:
            return 0.0, 1.0
    
    def mann_whitney_u_test(self, baseline_data: List[float], current_data: List[float]) -> Tuple[float, float]:
        """
        Perform Mann-Whitney U test (non-parametric)
        Returns (u_statistic, p_value)
        """
        if len(baseline_data) < 2 or len(current_data) < 2:
            return 0.0, 1.0
        
        try:
            u_stat, p_value = stats.mannwhitneyu(baseline_data, current_data, alternative='two-sided')
            return u_stat, p_value
        except:
            return 0.0, 1.0
    
    def cohens_d(self, baseline_data: List[float], current_data: List[float]) -> float:
        """
        Calculate Cohen's d effect size
        """
        if len(baseline_data) < 2 or len(current_data) < 2:
            return 0.0
        
        try:
            baseline_mean = statistics.mean(baseline_data)
            current_mean = statistics.mean(current_data)
            
            baseline_std = statistics.stdev(baseline_data)
            current_std = statistics.stdev(current_data)
            
            # Pooled standard deviation
            pooled_std = ((len(baseline_data) - 1) * baseline_std**2 + 
                         (len(current_data) - 1) * current_std**2) / \
                        (len(baseline_data) + len(current_data) - 2)
            pooled_std = pooled_std**0.5
            
            if pooled_std == 0:
                return 0.0
            
            return abs(current_mean - baseline_mean) / pooled_std
        except:
            return 0.0
    
    def detect_anomalies(self, data: List[float], threshold: float = 2.0) -> List[int]:
        """
        Detect anomalies using z-score
        Returns indices of anomalous values
        """
        if len(data) < 3:
            return []
        
        mean_val = statistics.mean(data)
        std_val = statistics.stdev(data)
        
        if std_val == 0:
            return []
        
        anomalies = []
        for i, value in enumerate(data):
            z_score = abs(value - mean_val) / std_val
            if z_score > threshold:
                anomalies.append(i)
        
        return anomalies
    
    def calculate_confidence_interval(self, data: List[float], confidence: float = 0.95) -> Tuple[float, float]:
        """
        Calculate confidence interval for the mean
        """
        if len(data) < 2:
            return 0.0, 0.0
        
        try:
            mean_val = statistics.mean(data)
            std_val = statistics.stdev(data)
            n = len(data)
            
            # t-distribution critical value
            alpha = 1 - confidence
            t_critical = stats.t.ppf(1 - alpha/2, n - 1)
            
            margin_error = t_critical * (std_val / (n**0.5))
            
            return mean_val - margin_error, mean_val + margin_error
        except:
            return 0.0, 0.0
    
    def analyze_performance_regression(
        self,
        baseline_data: List[float],
        current_data: List[float],
        metric_name: str,
        config: RegressionTestConfig
    ) -> Dict[str, Any]:
        """
        Comprehensive performance regression analysis
        """
        
        # Basic statistics
        baseline_stats = {
            "mean": statistics.mean(baseline_data) if baseline_data else 0.0,
            "median": statistics.median(baseline_data) if baseline_data else 0.0,
            "std": statistics.stdev(baseline_data) if len(baseline_data) > 1 else 0.0,
            "min": min(baseline_data) if baseline_data else 0.0,
            "max": max(baseline_data) if baseline_data else 0.0,
            "count": len(baseline_data)
        }
        
        current_stats = {
            "mean": statistics.mean(current_data) if current_data else 0.0,
            "median": statistics.median(current_data) if current_data else 0.0,
            "std": statistics.stdev(current_data) if len(current_data) > 1 else 0.0,
            "min": min(current_data) if current_data else 0.0,
            "max": max(current_data) if current_data else 0.0,
            "count": len(current_data)
        }
        
        # Percentage change
        percent_change = 0.0
        if baseline_stats["mean"] > 0:
            percent_change = ((current_stats["mean"] - baseline_stats["mean"]) / baseline_stats["mean"]) * 100
        
        # Statistical tests
        t_stat, p_value_t = self.welch_t_test(baseline_data, current_data)
        u_stat, p_value_u = self.mann_whitney_u_test(baseline_data, current_data)
        
        # Effect size
        effect_size = self.cohens_d(baseline_data, current_data)
        
        # Confidence intervals
        baseline_ci = self.calculate_confidence_interval(baseline_data, config.confidence_level)
        current_ci = self.calculate_confidence_interval(current_data, config.confidence_level)
        
        # Anomaly detection
        baseline_anomalies = self.detect_anomalies(baseline_data)
        current_anomalies = self.detect_anomalies(current_data)
        
        # Regression detection
        is_statistically_significant = min(p_value_t, p_value_u) < config.significance_threshold
        is_practically_significant = effect_size > config.effect_size_threshold
        
        # Determine regression based on metric type
        is_regression = False
        if metric_name in ["response_time", "error_rate", "cpu_usage", "memory_usage"]:
            # Higher is worse
            is_regression = (percent_change > 0 and 
                           is_statistically_significant and 
                           is_practically_significant)
        elif metric_name == "throughput":
            # Lower is worse
            is_regression = (percent_change < 0 and 
                           is_statistically_significant and 
                           is_practically_significant)
        
        return {
            "metric_name": metric_name,
            "baseline_stats": baseline_stats,
            "current_stats": current_stats,
            "percent_change": percent_change,
            "statistical_tests": {
                "t_test": {"statistic": t_stat, "p_value": p_value_t},
                "mann_whitney_u": {"statistic": u_stat, "p_value": p_value_u}
            },
            "effect_size": effect_size,
            "confidence_intervals": {
                "baseline": baseline_ci,
                "current": current_ci
            },
            "anomalies": {
                "baseline": baseline_anomalies,
                "current": current_anomalies
            },
            "is_statistically_significant": is_statistically_significant,
            "is_practically_significant": is_practically_significant,
            "is_regression": is_regression
        }


class RegressionAnalyzer:
    """Comprehensive regression analysis"""
    
    def __init__(self, baseline_manager: BaselineManager):
        self.baseline_manager = baseline_manager
        self.detector = RegressionDetector(baseline_manager)
    
    def analyze_baseline_comparison(
        self,
        comparison: BaselineComparison,
        raw_baseline_data: Dict[str, List[float]],
        raw_current_data: Dict[str, List[float]],
        config: RegressionTestConfig
    ) -> Dict[str, Any]:
        """
        Perform comprehensive regression analysis
        """
        
        analyses = {}
        
        # Analyze each metric
        metrics_to_analyze = [
            ("response_time", "avg_response_time"),
            ("throughput", "requests_per_second"),
            ("error_rate", "error_rate"),
            ("cpu_usage", "avg_cpu_percent"),
            ("memory_usage", "avg_memory_percent")
        ]
        
        for metric_name, metric_attr in metrics_to_analyze:
            baseline_data = raw_baseline_data.get(metric_name, [])
            current_data = raw_current_data.get(metric_name, [])
            
            if baseline_data and current_data:
                analysis = self.detector.analyze_performance_regression(
                    baseline_data, current_data, metric_name, config
                )
                analyses[metric_name] = analysis
        
        # Overall regression assessment
        regression_metrics = [m for m in analyses.values() if m.get("is_regression", False)]
        overall_regression = len(regression_metrics) > 0
        
        # Severity calculation
        severity = self._calculate_severity(analyses, comparison)
        
        # Confidence score
        confidence_score = self._calculate_confidence_score(analyses)
        
        # Recommendations
        recommendations = self._generate_recommendations(analyses, comparison)
        
        return {
            "overall_regression": overall_regression,
            "severity": severity,
            "confidence_score": confidence_score,
            "metric_analyses": analyses,
            "recommendations": recommendations,
            "regression_summary": {
                "total_metrics": len(analyses),
                "regressed_metrics": len(regression_metrics),
                "regression_rate": len(regression_metrics) / len(analyses) if analyses else 0
            }
        }
    
    def _calculate_severity(self, analyses: Dict[str, Any], comparison: BaselineComparison) -> RegressionSeverity:
        """Calculate regression severity"""
        
        # Check for critical regressions
        critical_conditions = [
            abs(comparison.response_time_change) > 100,  # 100% increase
            abs(comparison.throughput_change) > 50,       # 50% decrease
            comparison.error_rate_change > 10,            # 10% increase
        ]
        
        if any(critical_conditions):
            return RegressionSeverity.CRITICAL
        
        # Check for severe regressions
        severe_conditions = [
            abs(comparison.response_time_change) > 50,   # 50% increase
            abs(comparison.throughput_change) > 30,      # 30% decrease
            comparison.error_rate_change > 5,            # 5% increase
        ]
        
        if any(severe_conditions):
            return RegressionSeverity.SEVERE
        
        # Check for moderate regressions
        moderate_conditions = [
            abs(comparison.response_time_change) > 25,   # 25% increase
            abs(comparison.throughput_change) > 20,      # 20% decrease
            comparison.error_rate_change > 2,            # 2% increase
        ]
        
        if any(moderate_conditions):
            return RegressionSeverity.MODERATE
        
        # Check for minor regressions
        minor_conditions = [
            abs(comparison.response_time_change) > 10,   # 10% increase
            abs(comparison.throughput_change) > 10,      # 10% decrease
            comparison.error_rate_change > 0.5,          # 0.5% increase
        ]
        
        if any(minor_conditions):
            return RegressionSeverity.MINOR
        
        return RegressionSeverity.NONE
    
    def _calculate_confidence_score(self, analyses: Dict[str, Any]) -> float:
        """Calculate overall confidence score"""
        
        if not analyses:
            return 0.0
        
        confidence_scores = []
        
        for analysis in analyses.values():
            # Base confidence on statistical significance and effect size
            stat_sig = analysis.get("is_statistically_significant", False)
            effect_size = analysis.get("effect_size", 0.0)
            
            score = 0.0
            if stat_sig:
                score += 0.5
            
            # Effect size contribution
            if effect_size > 0.8:  # Large effect
                score += 0.4
            elif effect_size > 0.5:  # Medium effect
                score += 0.3
            elif effect_size > 0.2:  # Small effect
                score += 0.2
            
            # Sample size contribution
            sample_size = min(
                analysis.get("baseline_stats", {}).get("count", 0),
                analysis.get("current_stats", {}).get("count", 0)
            )
            
            if sample_size > 30:
                score += 0.1
            elif sample_size > 10:
                score += 0.05
            
            confidence_scores.append(min(score, 1.0))
        
        return statistics.mean(confidence_scores) if confidence_scores else 0.0
    
    def _generate_recommendations(self, analyses: Dict[str, Any], comparison: BaselineComparison) -> List[str]:
        """Generate recommendations based on analysis"""
        
        recommendations = []
        
        # Response time recommendations
        if "response_time" in analyses and analyses["response_time"].get("is_regression", False):
            change = analyses["response_time"]["percent_change"]
            if change > 50:
                recommendations.append("URGENT: Response time increased by >50%. Review recent changes and optimize critical paths.")
            elif change > 25:
                recommendations.append("Response time significantly increased. Consider adding caching or optimizing database queries.")
            else:
                recommendations.append("Monitor response time trends and consider performance optimizations.")
        
        # Throughput recommendations
        if "throughput" in analyses and analyses["throughput"].get("is_regression", False):
            change = abs(analyses["throughput"]["percent_change"])
            if change > 30:
                recommendations.append("URGENT: Throughput decreased by >30%. Check resource limits and scaling configuration.")
            elif change > 15:
                recommendations.append("Throughput significantly decreased. Review load balancing and resource allocation.")
            else:
                recommendations.append("Monitor throughput trends and consider capacity planning.")
        
        # Error rate recommendations
        if "error_rate" in analyses and analyses["error_rate"].get("is_regression", False):
            change = analyses["error_rate"]["percent_change"]
            if change > 5:
                recommendations.append("URGENT: Error rate increased significantly. Review error logs and fix critical issues.")
            elif change > 2:
                recommendations.append("Error rate increased. Investigate error sources and improve error handling.")
            else:
                recommendations.append("Monitor error trends and improve error handling robustness.")
        
        # Resource usage recommendations
        if "cpu_usage" in analyses and analyses["cpu_usage"].get("is_regression", False):
            recommendations.append("CPU usage increased. Profile application and optimize CPU-intensive operations.")
        
        if "memory_usage" in analyses and analyses["memory_usage"].get("is_regression", False):
            recommendations.append("Memory usage increased. Review memory allocation patterns and optimize memory usage.")
        
        # General recommendations
        if not recommendations:
            recommendations.append("Performance is within acceptable limits. Continue monitoring.")
        
        return recommendations


class RegressionTest:
    """Individual regression test executor"""
    
    def __init__(self, baseline_manager: BaselineManager):
        self.baseline_manager = baseline_manager
        self.analyzer = RegressionAnalyzer(baseline_manager)
    
    async def run_regression_test(
        self,
        baseline_id: str,
        test_func: Callable,
        config: RegressionTestConfig,
        baseline_version: Optional[str] = None
    ) -> RegressionResult:
        """
        Run a comprehensive regression test
        """
        
        test_id = f"regression_test_{int(time.time())}"
        start_time = datetime.now()
        
        # Get baseline
        baseline = self.baseline_manager.get_baseline(baseline_id, baseline_version)
        if not baseline:
            raise ValueError(f"Baseline not found: {baseline_id}:{baseline_version}")
        
        # Collect current performance data
        metrics_collector = RealTimeMetricsCollector()
        
        # Run test with warmup
        await self._run_warmup(test_func, config, metrics_collector)
        
        # Run actual test
        metrics_collector.start_collection()
        
        try:
            current_data = await self._run_performance_test(test_func, config, metrics_collector)
        finally:
            metrics_collector.stop_collection()
        
        end_time = datetime.now()
        
        # Calculate current metrics
        current_metrics = self._calculate_metrics_from_data(current_data, metrics_collector)
        
        # Compare with baseline
        comparison = self.baseline_manager.compare_with_baseline(
            baseline_id, current_metrics, baseline_version
        )
        
        if not comparison:
            raise ValueError("Failed to compare with baseline")
        
        # Perform detailed analysis
        raw_baseline_data = self._extract_raw_baseline_data(baseline)
        raw_current_data = self._extract_raw_current_data(current_data)
        
        analysis = self.analyzer.analyze_baseline_comparison(
            comparison, raw_baseline_data, raw_current_data, config
        )
        
        # Create result
        result = RegressionResult(
            test_id=test_id,
            test_type=RegressionTestType.COMPREHENSIVE,
            baseline_id=baseline_id,
            baseline_version=baseline_version or "latest",
            start_time=start_time,
            end_time=end_time,
            duration=(end_time - start_time).total_seconds(),
            is_regression=analysis["overall_regression"],
            severity=RegressionSeverity(analysis["severity"]),
            confidence_score=analysis["confidence_score"],
            statistical_significance=any(
                m.get("is_statistically_significant", False) 
                for m in analysis["metric_analyses"].values()
            ),
            p_value=min(
                (m.get("statistical_tests", {}).get("t_test", {}).get("p_value", 1.0) 
                 for m in analysis["metric_analyses"].values()),
                default=1.0
            ),
            effect_size=max(
                (m.get("effect_size", 0.0) for m in analysis["metric_analyses"].values()),
                default=0.0
            ),
            baseline_metrics=baseline.metrics,
            current_metrics=current_metrics,
            comparison=comparison,
            regression_factors=comparison.regression_reasons,
            performance_changes={
                "response_time": comparison.response_time_change,
                "throughput": comparison.throughput_change,
                "error_rate": comparison.error_rate_change,
                "cpu_usage": comparison.cpu_usage_change,
                "memory_usage": comparison.memory_usage_change
            },
            recommendations=analysis["recommendations"],
            raw_data={
                "baseline_data": raw_baseline_data,
                "current_data": raw_current_data,
                "analysis": analysis
            }
        )
        
        return result
    
    async def _run_warmup(self, test_func: Callable, config: RegressionTestConfig, metrics_collector: RealTimeMetricsCollector):
        """Run warmup phase"""
        if config.warmup_duration <= 0:
            return
        
        warmup_tasks = []
        for i in range(min(config.concurrent_users, 5)):  # Limited warmup
            warmup_tasks.append(test_func(user_id=i, warmup=True))
        
        await asyncio.gather(*warmup_tasks, return_exceptions=True)
        await asyncio.sleep(config.warmup_duration)
    
    async def _run_performance_test(self, test_func: Callable, config: RegressionTestConfig, metrics_collector: RealTimeMetricsCollector) -> List[Dict[str, Any]]:
        """Run the actual performance test"""
        
        tasks = []
        for user_id in range(config.concurrent_users):
            for request_num in range(config.requests_per_user):
                task = self._run_single_request(test_func, user_id, request_num, metrics_collector)
                tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions and return successful results
        successful_results = [r for r in results if isinstance(r, dict)]
        return successful_results
    
    async def _run_single_request(self, test_func: Callable, user_id: int, request_num: int, metrics_collector: RealTimeMetricsCollector) -> Dict[str, Any]:
        """Run a single test request"""
        
        start_time = time.time()
        
        try:
            result = await test_func(user_id=user_id, request_num=request_num)
            end_time = time.time()
            
            # Record metrics
            request_metrics = RequestMetrics(
                request_id=f"user_{user_id}_req_{request_num}",
                start_time=start_time,
                end_time=end_time,
                response_time=end_time - start_time,
                status_code=200,
                success=True,
                user_id=str(user_id)
            )
            
            metrics_collector.record_request(request_metrics)
            
            return {
                "user_id": user_id,
                "request_num": request_num,
                "response_time": end_time - start_time,
                "success": True,
                "result": result
            }
            
        except Exception as e:
            end_time = time.time()
            
            # Record failed metrics
            request_metrics = RequestMetrics(
                request_id=f"user_{user_id}_req_{request_num}",
                start_time=start_time,
                end_time=end_time,
                response_time=end_time - start_time,
                status_code=500,
                success=False,
                error_message=str(e),
                user_id=str(user_id)
            )
            
            metrics_collector.record_request(request_metrics)
            
            return {
                "user_id": user_id,
                "request_num": request_num,
                "response_time": end_time - start_time,
                "success": False,
                "error": str(e)
            }
    
    def _calculate_metrics_from_data(self, test_data: List[Dict[str, Any]], metrics_collector: RealTimeMetricsCollector) -> BaselineMetrics:
        """Calculate baseline metrics from test data"""
        
        successful_requests = [r for r in test_data if r.get("success", False)]
        failed_requests = [r for r in test_data if not r.get("success", False)]
        
        response_times = [r["response_time"] for r in successful_requests]
        
        system_metrics = metrics_collector.get_current_metrics()
        
        return BaselineMetrics(
            avg_response_time=statistics.mean(response_times) if response_times else 0.0,
            p50_response_time=statistics.median(response_times) if response_times else 0.0,
            p95_response_time=statistics.quantiles(response_times, n=20)[18] if len(response_times) > 10 else 0.0,
            p99_response_time=statistics.quantiles(response_times, n=100)[98] if len(response_times) > 50 else 0.0,
            max_response_time=max(response_times) if response_times else 0.0,
            requests_per_second=len(successful_requests) / max(1, metrics_collector.get_current_metrics().get("collection_duration", 1)),
            concurrent_requests=len(set(r["user_id"] for r in test_data)),
            success_rate=(len(successful_requests) / len(test_data)) * 100 if test_data else 0.0,
            error_rate=(len(failed_requests) / len(test_data)) * 100 if test_data else 0.0,
            avg_cpu_percent=system_metrics.get("system", {}).get("average_cpu_percent", 0.0),
            max_cpu_percent=system_metrics.get("system", {}).get("current_cpu_percent", 0.0),
            avg_memory_percent=system_metrics.get("system", {}).get("average_memory_percent", 0.0),
            max_memory_percent=system_metrics.get("system", {}).get("current_memory_percent", 0.0),
            total_requests=len(test_data),
            sample_size=len(successful_requests),
            test_duration=metrics_collector.get_current_metrics().get("collection_duration", 0.0)
        )
    
    def _extract_raw_baseline_data(self, baseline: PerformanceBaseline) -> Dict[str, List[float]]:
        """Extract raw data from baseline for analysis"""
        
        # Since we don't have access to raw baseline data,
        # we'll simulate it based on the baseline metrics
        # In a real implementation, this would come from stored raw data
        
        return {
            "response_time": [baseline.metrics.avg_response_time] * 10,
            "throughput": [baseline.metrics.requests_per_second] * 10,
            "error_rate": [baseline.metrics.error_rate] * 10,
            "cpu_usage": [baseline.metrics.avg_cpu_percent] * 10,
            "memory_usage": [baseline.metrics.avg_memory_percent] * 10
        }
    
    def _extract_raw_current_data(self, test_data: List[Dict[str, Any]]) -> Dict[str, List[float]]:
        """Extract raw data from current test for analysis"""
        
        successful_requests = [r for r in test_data if r.get("success", False)]
        
        return {
            "response_time": [r["response_time"] for r in successful_requests],
            "throughput": [1.0] * len(successful_requests),  # Simplified throughput
            "error_rate": [0.0 if r.get("success", False) else 1.0 for r in test_data],
            "cpu_usage": [0.0] * len(test_data),  # Would be populated from system metrics
            "memory_usage": [0.0] * len(test_data)  # Would be populated from system metrics
        }