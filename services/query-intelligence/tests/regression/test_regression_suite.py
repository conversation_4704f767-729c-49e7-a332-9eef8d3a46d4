"""
Comprehensive Performance Regression Testing Suite Tests

Integration tests for the complete performance regression testing framework
including baselines, regression detection, benchmarking, and CI/CD gates.
"""

import pytest
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any
import httpx
import json
from unittest.mock import AsyncMock, MagicMock, patch

from .performance_baselines import (
    BaselineManager, BaselineMetrics, PerformanceBaseline, BaselineStatus
)
from .regression_tests import (
    RegressionTest, RegressionTestConfig, RegressionDetector, RegressionAnalyzer
)
from .benchmark_suite import (
    BenchmarkSuite, BenchmarkRunner, BenchmarkConfig, BenchmarkType
)
from .ci_performance_gates import (
    CIPipelineIntegration, PerformanceGate, GatePolicy, GateAction
)
from .trend_analysis import (
    TrendAnalyzer, TrendPredictor, TrendDirection, AlertSeverity
)


class TestPerformanceBaselines:
    """Test performance baseline management"""
    
    def test_baseline_creation(self, tmp_path):
        """Test creating and storing baselines"""
        
        # Initialize baseline manager
        baseline_manager = BaselineManager(str(tmp_path / "baselines"))
        
        # Create test metrics
        metrics = BaselineMetrics(
            avg_response_time=0.85,
            p50_response_time=0.75,
            p95_response_time=1.20,
            p99_response_time=1.80,
            max_response_time=2.50,
            requests_per_second=25.0,
            concurrent_requests=10,
            success_rate=98.5,
            error_rate=1.5,
            avg_cpu_percent=45.0,
            max_cpu_percent=65.0,
            avg_memory_percent=35.0,
            max_memory_percent=50.0,
            total_requests=1000,
            sample_size=985
        )
        
        # Create baseline
        baseline = baseline_manager.create_baseline(
            baseline_id="test_baseline",
            version="1.0.0",
            service_version="v1.2.3",
            metrics=metrics,
            test_config={"test_type": "integration"},
            environment="test"
        )
        
        # Verify baseline creation
        assert baseline.baseline_id == "test_baseline"
        assert baseline.version == "1.0.0"
        assert baseline.metrics.avg_response_time == 0.85
        assert baseline.status == BaselineStatus.ACTIVE
        
        # Retrieve baseline
        retrieved_baseline = baseline_manager.get_baseline("test_baseline", "1.0.0")
        assert retrieved_baseline is not None
        assert retrieved_baseline.metrics.avg_response_time == 0.85
        assert retrieved_baseline.metrics.requests_per_second == 25.0
    
    def test_baseline_comparison(self, tmp_path):
        """Test comparing current metrics with baseline"""
        
        baseline_manager = BaselineManager(str(tmp_path / "baselines"))
        
        # Create baseline
        baseline_metrics = BaselineMetrics(
            avg_response_time=1.0,
            p50_response_time=0.8,
            p95_response_time=1.5,
            p99_response_time=2.0,
            max_response_time=3.0,
            requests_per_second=20.0,
            concurrent_requests=10,
            success_rate=99.0,
            error_rate=1.0,
            avg_cpu_percent=50.0,
            max_cpu_percent=70.0,
            avg_memory_percent=40.0,
            max_memory_percent=60.0
        )
        
        baseline_manager.create_baseline(
            baseline_id="comparison_test",
            version="1.0.0",
            service_version="v1.0.0",
            metrics=baseline_metrics,
            test_config={}
        )
        
        # Create current metrics (showing regression)
        current_metrics = BaselineMetrics(
            avg_response_time=1.3,  # 30% increase
            p50_response_time=1.1,
            p95_response_time=2.0,
            p99_response_time=2.8,
            max_response_time=4.0,
            requests_per_second=16.0,  # 20% decrease
            concurrent_requests=10,
            success_rate=97.0,  # 2% decrease
            error_rate=3.0,  # 200% increase
            avg_cpu_percent=65.0,  # 30% increase
            max_cpu_percent=85.0,
            avg_memory_percent=55.0,  # 37.5% increase
            max_memory_percent=75.0
        )
        
        # Compare with baseline
        comparison = baseline_manager.compare_with_baseline(
            "comparison_test", current_metrics, "1.0.0"
        )
        
        assert comparison is not None
        assert comparison.is_regression == True
        assert comparison.regression_severity == "severe"
        assert comparison.response_time_change == 30.0
        assert comparison.throughput_change == -20.0
        assert comparison.error_rate_change == 200.0
        assert len(comparison.regression_reasons) > 0
    
    def test_baseline_auto_creation(self, tmp_path):
        """Test automatic baseline creation from test results"""
        
        baseline_manager = BaselineManager(str(tmp_path / "baselines"))
        
        # Create mock test results
        test_results = [
            {
                "response_time": 0.8,
                "success_rate": 99.0,
                "cpu_percent": 45.0,
                "memory_percent": 35.0,
                "requests_per_second": 25.0,
                "total_requests": 100,
                "test_type": "api_test"
            },
            {
                "response_time": 0.9,
                "success_rate": 98.5,
                "cpu_percent": 48.0,
                "memory_percent": 38.0,
                "requests_per_second": 23.0,
                "total_requests": 100,
                "test_type": "api_test"
            },
            {
                "response_time": 0.7,
                "success_rate": 99.5,
                "cpu_percent": 42.0,
                "memory_percent": 33.0,
                "requests_per_second": 27.0,
                "total_requests": 100,
                "test_type": "api_test"
            }
        ]
        
        # Auto-create baseline
        baseline = baseline_manager.auto_create_baseline_from_tests(
            baseline_id="auto_baseline",
            version="1.0.0",
            service_version="v1.0.0",
            test_results=test_results
        )
        
        # Verify auto-created baseline
        assert baseline.baseline_id == "auto_baseline"
        assert baseline.metrics.avg_response_time == pytest.approx(0.8, rel=0.1)
        assert baseline.metrics.success_rate == pytest.approx(99.0, rel=0.1)
        assert "auto-generated" in baseline.tags


class TestRegressionDetection:
    """Test regression detection algorithms"""
    
    def test_statistical_regression_detection(self, tmp_path):
        """Test statistical regression detection"""
        
        baseline_manager = BaselineManager(str(tmp_path / "baselines"))
        detector = RegressionDetector(baseline_manager)
        
        # Create baseline data (simulating good performance)
        baseline_data = [0.8, 0.9, 0.7, 0.85, 0.75, 0.8, 0.9, 0.85, 0.7, 0.8]
        
        # Create current data (simulating regression)
        current_data = [1.2, 1.3, 1.1, 1.25, 1.15, 1.2, 1.3, 1.25, 1.1, 1.2]
        
        # Test Welch's t-test
        t_stat, p_value = detector.welch_t_test(baseline_data, current_data)
        
        assert t_stat > 0  # Should detect significant difference
        assert p_value < 0.05  # Should be statistically significant
        
        # Test Cohen's d (effect size)
        effect_size = detector.cohens_d(baseline_data, current_data)
        
        assert effect_size > 0.8  # Should show large effect size
        
        # Test comprehensive regression analysis
        config = RegressionTestConfig()
        analysis = detector.analyze_performance_regression(
            baseline_data, current_data, "response_time", config
        )
        
        assert analysis["is_regression"] == True
        assert analysis["is_statistically_significant"] == True
        assert analysis["is_practically_significant"] == True
        assert analysis["percent_change"] > 0
    
    def test_regression_analyzer(self, tmp_path):
        """Test comprehensive regression analysis"""
        
        baseline_manager = BaselineManager(str(tmp_path / "baselines"))
        analyzer = RegressionAnalyzer(baseline_manager)
        
        # Create baseline metrics
        baseline_metrics = BaselineMetrics(
            avg_response_time=1.0,
            p50_response_time=0.8,
            p95_response_time=1.5,
            p99_response_time=2.0,
            max_response_time=3.0,
            requests_per_second=20.0,
            concurrent_requests=10,
            success_rate=99.0,
            error_rate=1.0,
            avg_cpu_percent=50.0,
            max_cpu_percent=70.0,
            avg_memory_percent=40.0,
            max_memory_percent=60.0
        )
        
        # Create current metrics (with regression)
        current_metrics = BaselineMetrics(
            avg_response_time=1.5,  # 50% increase
            p50_response_time=1.2,
            p95_response_time=2.5,
            p99_response_time=3.5,
            max_response_time=5.0,
            requests_per_second=15.0,  # 25% decrease
            concurrent_requests=10,
            success_rate=95.0,  # 4% decrease
            error_rate=5.0,  # 400% increase
            avg_cpu_percent=75.0,  # 50% increase
            max_cpu_percent=90.0,
            avg_memory_percent=60.0,  # 50% increase
            max_memory_percent=80.0
        )
        
        # Create mock comparison
        from .performance_baselines import BaselineComparison
        comparison = BaselineComparison(
            baseline=PerformanceBaseline(
                baseline_id="test",
                version="1.0.0",
                service_version="v1.0.0",
                created_at=datetime.now(),
                metrics=baseline_metrics
            ),
            current_metrics=current_metrics,
            response_time_change=50.0,
            throughput_change=-25.0,
            error_rate_change=400.0,
            cpu_usage_change=50.0,
            memory_usage_change=50.0,
            is_regression=True,
            regression_severity="severe",
            regression_reasons=["Response time increased by 50%"],
            is_significant=True,
            confidence_level=0.95
        )
        
        # Create mock raw data
        raw_baseline_data = {
            "response_time": [1.0, 0.9, 1.1, 1.0, 0.95],
            "throughput": [20.0, 19.0, 21.0, 20.0, 19.5],
            "error_rate": [1.0, 0.8, 1.2, 1.0, 0.9],
            "cpu_usage": [50.0, 48.0, 52.0, 50.0, 49.0],
            "memory_usage": [40.0, 38.0, 42.0, 40.0, 39.0]
        }
        
        raw_current_data = {
            "response_time": [1.5, 1.4, 1.6, 1.5, 1.45],
            "throughput": [15.0, 14.0, 16.0, 15.0, 14.5],
            "error_rate": [5.0, 4.8, 5.2, 5.0, 4.9],
            "cpu_usage": [75.0, 73.0, 77.0, 75.0, 74.0],
            "memory_usage": [60.0, 58.0, 62.0, 60.0, 59.0]
        }
        
        # Analyze regression
        config = RegressionTestConfig()
        analysis = analyzer.analyze_baseline_comparison(
            comparison, raw_baseline_data, raw_current_data, config
        )
        
        assert analysis["overall_regression"] == True
        assert analysis["severity"] == "severe"
        assert analysis["confidence_score"] > 0.5
        assert len(analysis["recommendations"]) > 0
        assert analysis["regression_summary"]["regressed_metrics"] > 0


class TestBenchmarkSuite:
    """Test benchmark suite functionality"""
    
    @pytest.mark.asyncio
    async def test_benchmark_suite_creation(self):
        """Test creating benchmark suite configurations"""
        
        # Create comprehensive suite
        comprehensive_suite = BenchmarkSuite.create_comprehensive_suite()
        
        assert len(comprehensive_suite) > 0
        assert any(config.benchmark_type == BenchmarkType.SINGLE_ENDPOINT for config in comprehensive_suite)
        assert any(config.benchmark_type == BenchmarkType.LOAD_TEST for config in comprehensive_suite)
        assert any(config.benchmark_type == BenchmarkType.CACHE_PERFORMANCE for config in comprehensive_suite)
        
        # Create quick suite
        quick_suite = BenchmarkSuite.create_quick_suite()
        
        assert len(quick_suite) > 0
        assert all(config.duration <= 30.0 for config in quick_suite)
        assert all(config.concurrent_users <= 10 for config in quick_suite)
    
    @pytest.mark.asyncio
    async def test_benchmark_runner_mock(self, tmp_path):
        """Test benchmark runner with mocked HTTP calls"""
        
        baseline_manager = BaselineManager(str(tmp_path / "baselines"))
        benchmark_runner = BenchmarkRunner(baseline_manager)
        
        # Create test configuration
        config = BenchmarkConfig(
            benchmark_id="test_benchmark",
            benchmark_type=BenchmarkType.SINGLE_ENDPOINT,
            duration=5.0,  # Short duration for testing
            concurrent_users=2,
            requests_per_user=3,
            endpoints=["/api/v1/query"],
            base_url="http://test.example.com"
        )
        
        # Mock HTTP responses
        auth_headers = {"Authorization": "Bearer test_token"}
        
        with patch('httpx.AsyncClient.post') as mock_post:
            # Configure mock to return successful responses
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"result": "success"}
            mock_response.content = b'{"result": "success"}'
            mock_post.return_value = mock_response
            
            # Run benchmark
            result = await benchmark_runner.run_benchmark(config, auth_headers)
            
            # Verify result
            assert result.benchmark_id == "test_benchmark"
            assert result.status.value == "completed"
            assert result.total_requests == 6  # 2 users * 3 requests
            assert result.successful_requests == 6
            assert result.performance_score > 0
            assert len(result.recommendations) > 0


class TestCIPerformanceGates:
    """Test CI/CD performance gates"""
    
    def test_gate_policy_creation(self):
        """Test creating gate policies"""
        
        policy = GatePolicy(
            gate_id="test_gate",
            name="Test Performance Gate",
            description="Test gate for unit testing",
            max_response_time_ms=2000,
            min_throughput_rps=10.0,
            max_error_rate_percent=1.0,
            action=GateAction.BLOCK
        )
        
        assert policy.gate_id == "test_gate"
        assert policy.max_response_time_ms == 2000
        assert policy.action == GateAction.BLOCK
        
        # Test serialization
        policy_dict = policy.to_dict()
        assert policy_dict["gate_id"] == "test_gate"
        assert policy_dict["action"] == "block"
    
    @pytest.mark.asyncio
    async def test_performance_gate_mock(self, tmp_path):
        """Test performance gate with mocked dependencies"""
        
        baseline_manager = BaselineManager(str(tmp_path / "baselines"))
        
        # Create test policy
        policy = GatePolicy(
            gate_id="test_gate",
            name="Test Gate",
            description="Test gate",
            max_response_time_ms=1000,
            min_throughput_rps=15.0,
            max_error_rate_percent=2.0,
            test_duration=5.0,
            concurrent_users=2,
            requests_per_user=2
        )
        
        gate = PerformanceGate(policy, baseline_manager)
        
        # Mock benchmark runner
        with patch.object(gate.benchmark_runner, 'run_benchmark') as mock_benchmark:
            from .benchmark_suite import BenchmarkResult, BenchmarkStatus
            
            # Create mock benchmark result
            mock_result = BenchmarkResult(
                benchmark_id="test_benchmark",
                benchmark_type=BenchmarkType.SINGLE_ENDPOINT,
                start_time=datetime.now(),
                end_time=datetime.now(),
                duration=5.0,
                status=BenchmarkStatus.COMPLETED,
                overall_metrics=BaselineMetrics(
                    avg_response_time=0.8,  # Within threshold
                    p50_response_time=0.7,
                    p95_response_time=1.2,
                    p99_response_time=1.5,
                    max_response_time=2.0,
                    requests_per_second=20.0,  # Above threshold
                    concurrent_requests=2,
                    success_rate=99.0,
                    error_rate=1.0,  # Within threshold
                    avg_cpu_percent=50.0,
                    max_cpu_percent=70.0,
                    avg_memory_percent=40.0,
                    max_memory_percent=60.0
                )
            )
            
            mock_benchmark.return_value = mock_result
            
            # Execute gate
            result = await gate.execute("http://test.example.com", {})
            
            # Verify result
            assert result.gate_id == "test_gate"
            assert result.status.value == "passed"  # Should pass all thresholds
            assert len(result.threshold_violations) == 0
            assert result.current_metrics.avg_response_time == 0.8
    
    def test_ci_pipeline_integration(self, tmp_path):
        """Test CI pipeline integration configuration"""
        
        baseline_manager = BaselineManager(str(tmp_path / "baselines"))
        
        # Create temporary config file
        config_path = tmp_path / "test_gates.json"
        config_data = {
            "gates": [
                {
                    "gate_id": "performance_gate",
                    "name": "Performance Gate",
                    "description": "Main performance gate",
                    "max_response_time_ms": 2000,
                    "min_throughput_rps": 10.0,
                    "action": "block"
                }
            ]
        }
        
        with open(config_path, 'w') as f:
            json.dump(config_data, f)
        
        # Initialize CI integration
        ci_integration = CIPipelineIntegration(baseline_manager, str(config_path))
        
        # Verify configuration loaded
        assert len(ci_integration.gates) == 1
        assert "performance_gate" in ci_integration.gates
        
        gate = ci_integration.gates["performance_gate"]
        assert gate.policy.max_response_time_ms == 2000
        assert gate.policy.action == GateAction.BLOCK


class TestTrendAnalysis:
    """Test trend analysis functionality"""
    
    def test_trend_analyzer_creation(self, tmp_path):
        """Test creating trend analyzer"""
        
        baseline_manager = BaselineManager(str(tmp_path / "baselines"))
        trend_analyzer = TrendAnalyzer(baseline_manager)
        
        assert trend_analyzer.baseline_manager == baseline_manager
        assert trend_analyzer.min_data_points == 5
        assert trend_analyzer.confidence_threshold == 0.7
    
    def test_metric_trend_analysis(self, tmp_path):
        """Test analyzing trends for individual metrics"""
        
        baseline_manager = BaselineManager(str(tmp_path / "baselines"))
        trend_analyzer = TrendAnalyzer(baseline_manager)
        
        # Create degrading trend data
        values = [1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9]
        timestamps = [
            datetime.now() - timedelta(days=9-i) for i in range(10)
        ]
        
        # Analyze trend
        trend = trend_analyzer.analyze_metric_trend(
            "response_time", values, timestamps, baseline_value=1.0
        )
        
        assert trend.metric_name == "response_time"
        assert trend.direction == TrendDirection.DEGRADING
        assert trend.slope > 0  # Positive slope indicates increase
        assert trend.confidence > 0.5  # Should have decent confidence
        assert trend.correlation_coefficient > 0.8  # Strong correlation
        assert trend.data_points == 10
    
    def test_trend_alert_generation(self, tmp_path):
        """Test generating alerts based on trends"""
        
        baseline_manager = BaselineManager(str(tmp_path / "baselines"))
        trend_analyzer = TrendAnalyzer(baseline_manager)
        
        # Create mock trends
        from .trend_analysis import PerformanceTrend
        
        # Degrading response time trend
        degrading_trend = PerformanceTrend(
            metric_name="response_time",
            direction=TrendDirection.DEGRADING,
            slope=0.1,  # Increasing by 0.1s per day
            confidence=0.9,
            correlation_coefficient=0.85,
            p_value=0.01,
            current_value=1.5,
            predicted_value=1.6,
            baseline_value=1.0,
            start_time=datetime.now() - timedelta(days=10),
            end_time=datetime.now(),
            data_points=10,
            trend_strength="strong"
        )
        
        # Volatile throughput trend
        volatile_trend = PerformanceTrend(
            metric_name="throughput",
            direction=TrendDirection.VOLATILE,
            slope=0.0,
            confidence=0.3,
            correlation_coefficient=0.2,
            p_value=0.5,
            current_value=15.0,
            predicted_value=15.0,
            baseline_value=20.0,
            start_time=datetime.now() - timedelta(days=10),
            end_time=datetime.now(),
            data_points=10,
            trend_strength="weak"
        )
        
        trends = {
            "response_time": degrading_trend,
            "throughput": volatile_trend
        }
        
        # Generate alerts
        alerts = trend_analyzer.generate_trend_alerts(trends)
        
        assert len(alerts) == 2  # One for degrading trend, one for volatility
        
        # Check degrading trend alert
        degrading_alert = next(a for a in alerts if "Degradation" in a.title)
        assert degrading_alert.metric_name == "response_time"
        assert degrading_alert.severity == AlertSeverity.WARNING
        assert degrading_alert.trend.direction == TrendDirection.DEGRADING
        
        # Check volatility alert
        volatile_alert = next(a for a in alerts if "Volatility" in a.title)
        assert volatile_alert.metric_name == "throughput"
        assert volatile_alert.severity == AlertSeverity.WARNING
        assert volatile_alert.trend.direction == TrendDirection.VOLATILE
    
    def test_trend_predictor(self):
        """Test trend prediction functionality"""
        
        from .trend_analysis import TrendPredictor
        
        predictor = TrendPredictor()
        
        # Create linear trend data
        values = [1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2.0]
        timestamps = [
            datetime.now() - timedelta(days=10-i) for i in range(11)
        ]
        
        # Test prediction
        predictions = predictor.predict_future_values(values, timestamps, horizon_days=5)
        
        assert len(predictions) == 5
        assert all(isinstance(pred[0], datetime) for pred in predictions)
        assert all(isinstance(pred[1], float) for pred in predictions)
        
        # Predictions should continue the upward trend
        predicted_values = [pred[1] for pred in predictions]
        assert predicted_values[0] > values[-1]  # First prediction should be higher
        assert predicted_values[-1] > predicted_values[0]  # Trend should continue
        
        # Test confidence calculation
        confidence = predictor.calculate_prediction_confidence(values, timestamps)
        assert confidence > 0.9  # Should have high confidence for linear trend
        
        # Test anomaly detection
        anomalous_values = [1.0, 1.1, 5.0, 1.3, 1.4, 1.5, 1.6, 0.1, 1.8, 1.9]
        anomalies = predictor.detect_anomalies(anomalous_values)
        assert len(anomalies) == 2  # Should detect 5.0 and 0.1 as anomalies


# Integration test for the complete system
class TestRegressionSuiteIntegration:
    """Integration tests for the complete regression testing suite"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_regression_testing(self, tmp_path):
        """Test complete end-to-end regression testing workflow"""
        
        # 1. Setup baseline manager
        baseline_manager = BaselineManager(str(tmp_path / "baselines"))
        
        # 2. Create initial baseline
        baseline_metrics = BaselineMetrics(
            avg_response_time=1.0,
            p50_response_time=0.8,
            p95_response_time=1.5,
            p99_response_time=2.0,
            max_response_time=3.0,
            requests_per_second=20.0,
            concurrent_requests=10,
            success_rate=99.0,
            error_rate=1.0,
            avg_cpu_percent=50.0,
            max_cpu_percent=70.0,
            avg_memory_percent=40.0,
            max_memory_percent=60.0
        )
        
        baseline = baseline_manager.create_baseline(
            baseline_id="integration_test_baseline",
            version="1.0.0",
            service_version="v1.0.0",
            metrics=baseline_metrics,
            test_config={"test_type": "integration"}
        )
        
        # 3. Create regression test
        regression_test = RegressionTest(baseline_manager)
        
        # Mock test function that simulates performance regression
        async def mock_test_func(user_id=0, request_num=0, **kwargs):
            # Simulate slower performance
            await asyncio.sleep(0.001)  # Minimal delay for testing
            return {"success": True, "response_time": 1.3}  # 30% slower
        
        # 4. Run regression test
        config = RegressionTestConfig(
            test_duration=1.0,  # Short duration for testing
            concurrent_users=2,
            requests_per_user=3
        )
        
        with patch.object(regression_test, '_run_performance_test') as mock_performance_test:
            # Mock performance test results
            mock_performance_test.return_value = [
                {"user_id": 0, "request_num": 0, "response_time": 1.3, "success": True},
                {"user_id": 0, "request_num": 1, "response_time": 1.2, "success": True},
                {"user_id": 1, "request_num": 0, "response_time": 1.4, "success": True},
                {"user_id": 1, "request_num": 1, "response_time": 1.1, "success": True},
            ]
            
            result = await regression_test.run_regression_test(
                "integration_test_baseline", mock_test_func, config
            )
            
            # 5. Verify regression detection
            assert result.is_regression == True
            assert result.confidence_score > 0.0
            assert len(result.regression_factors) > 0
            assert result.comparison.response_time_change > 0  # Should show increase
    
    def test_complete_workflow_with_ci_gates(self, tmp_path):
        """Test complete workflow including CI/CD gates"""
        
        # 1. Setup components
        baseline_manager = BaselineManager(str(tmp_path / "baselines"))
        
        # 2. Create baseline for CI testing
        baseline_metrics = BaselineMetrics(
            avg_response_time=0.5,
            p50_response_time=0.4,
            p95_response_time=0.8,
            p99_response_time=1.2,
            max_response_time=2.0,
            requests_per_second=50.0,
            concurrent_requests=10,
            success_rate=99.5,
            error_rate=0.5,
            avg_cpu_percent=30.0,
            max_cpu_percent=45.0,
            avg_memory_percent=25.0,
            max_memory_percent=35.0
        )
        
        baseline_manager.create_baseline(
            baseline_id="ci_test_baseline",
            version="1.0.0",
            service_version="v1.0.0",
            metrics=baseline_metrics,
            test_config={"test_type": "ci"}
        )
        
        # 3. Create CI gate policy
        policy = GatePolicy(
            gate_id="ci_regression_gate",
            name="CI Regression Gate",
            description="CI regression detection gate",
            max_response_time_ms=1000,
            min_throughput_rps=30.0,
            max_error_rate_percent=2.0,
            baseline_id="ci_test_baseline",
            action=GateAction.BLOCK
        )
        
        # 4. Create performance gate
        gate = PerformanceGate(policy, baseline_manager)
        
        # 5. Mock benchmark execution
        with patch.object(gate.benchmark_runner, 'run_benchmark') as mock_benchmark:
            from .benchmark_suite import BenchmarkResult, BenchmarkStatus
            
            # Simulate performance within limits
            mock_result = BenchmarkResult(
                benchmark_id="ci_test",
                benchmark_type=BenchmarkType.SINGLE_ENDPOINT,
                start_time=datetime.now(),
                end_time=datetime.now(),
                duration=10.0,
                status=BenchmarkStatus.COMPLETED,
                overall_metrics=BaselineMetrics(
                    avg_response_time=0.6,  # Slightly slower but within limits
                    p50_response_time=0.5,
                    p95_response_time=0.9,
                    p99_response_time=1.3,
                    max_response_time=2.2,
                    requests_per_second=45.0,  # Slightly lower but within limits
                    concurrent_requests=10,
                    success_rate=99.0,
                    error_rate=1.0,  # Within limits
                    avg_cpu_percent=35.0,
                    max_cpu_percent=50.0,
                    avg_memory_percent=30.0,
                    max_memory_percent=40.0
                )
            )
            
            mock_benchmark.return_value = mock_result
            
            # 6. Execute gate (this would be called by CI/CD)
            gate_result = asyncio.run(gate.execute("http://test.example.com", {}))
            
            # 7. Verify CI gate results
            assert gate_result.gate_id == "ci_regression_gate"
            assert gate_result.status.value == "passed"  # Should pass
            assert len(gate_result.threshold_violations) == 0
            assert gate_result.action_taken == GateAction.BLOCK
            
            # 8. Test deployment decision
            should_block = gate_result.status.value == "failed" and gate_result.action_taken == GateAction.BLOCK
            assert should_block == False  # Should not block deployment