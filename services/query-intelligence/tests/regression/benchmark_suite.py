"""
Comprehensive Benchmark Suite

Systematic performance testing across all endpoints with standardized
benchmarking, reporting, and comparison capabilities.
"""

import asyncio
import time
import json
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field, asdict
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import httpx
import statistics
from pathlib import Path

from .performance_baselines import BaselineManager, BaselineMetrics
from .regression_tests import RegressionTest, RegressionTestConfig, RegressionResult
from ..e2e.utils.performance_monitors import RealTimeMetricsCollector, RequestMetrics
from ..e2e.utils.websocket_client import WebSocketTestClient


class BenchmarkType(Enum):
    """Types of benchmarks"""
    SINGLE_ENDPOINT = "single_endpoint"
    MULTI_ENDPOINT = "multi_endpoint"
    LOAD_TEST = "load_test"
    STRESS_TEST = "stress_test"
    ENDURANCE_TEST = "endurance_test"
    SPIKE_TEST = "spike_test"
    CACHE_PERFORMANCE = "cache_performance"
    WEBSOCKET_PERFORMANCE = "websocket_performance"


class BenchmarkStatus(Enum):
    """Benchmark execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class BenchmarkConfig:
    """Configuration for benchmark tests"""
    
    # Test identification
    benchmark_id: str
    benchmark_type: BenchmarkType
    
    # Test parameters
    duration: float = 60.0
    warmup_duration: float = 10.0
    concurrent_users: int = 10
    requests_per_user: int = 10
    
    # Endpoints to test
    endpoints: List[str] = field(default_factory=lambda: ["/api/v1/query"])
    
    # Performance criteria
    max_response_time: float = 5.0
    min_throughput: float = 10.0
    max_error_rate: float = 1.0
    
    # Test scenarios
    query_patterns: List[str] = field(default_factory=lambda: [
        "What is authentication?",
        "How does caching work?",
        "Explain the query processing pipeline",
        "Find functions related to security",
        "Analyze performance bottlenecks"
    ])
    
    # Environment settings
    base_url: str = "http://localhost:8000"
    websocket_url: str = "ws://localhost:8000"
    
    # Regression testing
    baseline_id: Optional[str] = None
    create_baseline: bool = True
    
    # Retry configuration
    max_retries: int = 3
    retry_delay: float = 1.0
    
    # Reporting
    generate_report: bool = True
    report_format: str = "json"  # json, html, csv
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = asdict(self)
        data['benchmark_type'] = self.benchmark_type.value
        return data


@dataclass
class BenchmarkResult:
    """Result of a benchmark test"""
    
    # Test identification
    benchmark_id: str
    benchmark_type: BenchmarkType
    
    # Execution info
    start_time: datetime
    end_time: datetime
    duration: float
    status: BenchmarkStatus
    
    # Performance metrics
    overall_metrics: BaselineMetrics
    endpoint_metrics: Dict[str, BaselineMetrics] = field(default_factory=dict)
    
    # Test results
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    
    # Performance analysis
    performance_score: float = 0.0  # 0-100
    performance_grade: str = "F"    # A, B, C, D, F
    
    # Regression analysis
    regression_result: Optional[RegressionResult] = None
    
    # Detailed results
    request_results: List[Dict[str, Any]] = field(default_factory=list)
    error_summary: Dict[str, int] = field(default_factory=dict)
    
    # System metrics
    system_metrics: Dict[str, Any] = field(default_factory=dict)
    
    # Recommendations
    recommendations: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        data = asdict(self)
        data['benchmark_type'] = self.benchmark_type.value
        data['status'] = self.status.value
        data['start_time'] = self.start_time.isoformat()
        data['end_time'] = self.end_time.isoformat()
        data['overall_metrics'] = self.overall_metrics.to_dict()
        data['endpoint_metrics'] = {k: v.to_dict() for k, v in self.endpoint_metrics.items()}
        if self.regression_result:
            data['regression_result'] = self.regression_result.to_dict()
        return data


class BenchmarkTest:
    """Individual benchmark test"""
    
    def __init__(self, config: BenchmarkConfig, auth_headers: Dict[str, str]):
        self.config = config
        self.auth_headers = auth_headers
        self.metrics_collector = RealTimeMetricsCollector()
        self.results = []
        self.errors = []
    
    async def run(self) -> BenchmarkResult:
        """Run the benchmark test"""
        
        start_time = datetime.now()
        
        try:
            # Run benchmark based on type
            if self.config.benchmark_type == BenchmarkType.SINGLE_ENDPOINT:
                await self._run_single_endpoint_benchmark()
            elif self.config.benchmark_type == BenchmarkType.MULTI_ENDPOINT:
                await self._run_multi_endpoint_benchmark()
            elif self.config.benchmark_type == BenchmarkType.LOAD_TEST:
                await self._run_load_test_benchmark()
            elif self.config.benchmark_type == BenchmarkType.STRESS_TEST:
                await self._run_stress_test_benchmark()
            elif self.config.benchmark_type == BenchmarkType.ENDURANCE_TEST:
                await self._run_endurance_test_benchmark()
            elif self.config.benchmark_type == BenchmarkType.SPIKE_TEST:
                await self._run_spike_test_benchmark()
            elif self.config.benchmark_type == BenchmarkType.CACHE_PERFORMANCE:
                await self._run_cache_performance_benchmark()
            elif self.config.benchmark_type == BenchmarkType.WEBSOCKET_PERFORMANCE:
                await self._run_websocket_performance_benchmark()
            
            status = BenchmarkStatus.COMPLETED
            
        except Exception as e:
            self.errors.append(f"Benchmark failed: {str(e)}")
            status = BenchmarkStatus.FAILED
        
        end_time = datetime.now()
        
        # Calculate metrics
        overall_metrics = self._calculate_overall_metrics()
        endpoint_metrics = self._calculate_endpoint_metrics()
        
        # Calculate performance score
        performance_score = self._calculate_performance_score(overall_metrics)
        performance_grade = self._calculate_performance_grade(performance_score)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(overall_metrics)
        
        # Create result
        result = BenchmarkResult(
            benchmark_id=self.config.benchmark_id,
            benchmark_type=self.config.benchmark_type,
            start_time=start_time,
            end_time=end_time,
            duration=(end_time - start_time).total_seconds(),
            status=status,
            overall_metrics=overall_metrics,
            endpoint_metrics=endpoint_metrics,
            total_requests=len(self.results),
            successful_requests=len([r for r in self.results if r.get("success", False)]),
            failed_requests=len([r for r in self.results if not r.get("success", False)]),
            performance_score=performance_score,
            performance_grade=performance_grade,
            request_results=self.results,
            error_summary=self._summarize_errors(),
            system_metrics=self.metrics_collector.get_current_metrics(),
            recommendations=recommendations
        )
        
        return result
    
    async def _run_single_endpoint_benchmark(self):
        """Run single endpoint benchmark"""
        
        endpoint = self.config.endpoints[0]
        
        # Start metrics collection
        self.metrics_collector.start_collection()
        
        try:
            # Warmup
            await self._run_warmup(endpoint)
            
            # Run actual benchmark
            tasks = []
            for user_id in range(self.config.concurrent_users):
                for request_num in range(self.config.requests_per_user):
                    task = self._make_api_request(endpoint, user_id, request_num)
                    tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for result in results:
                if isinstance(result, dict):
                    self.results.append(result)
                else:
                    self.errors.append(str(result))
        
        finally:
            self.metrics_collector.stop_collection()
    
    async def _run_multi_endpoint_benchmark(self):
        """Run multi-endpoint benchmark"""
        
        # Start metrics collection
        self.metrics_collector.start_collection()
        
        try:
            # Warmup for each endpoint
            for endpoint in self.config.endpoints:
                await self._run_warmup(endpoint)
            
            # Run benchmark across all endpoints
            tasks = []
            for user_id in range(self.config.concurrent_users):
                for request_num in range(self.config.requests_per_user):
                    # Round-robin across endpoints
                    endpoint_idx = (user_id * self.config.requests_per_user + request_num) % len(self.config.endpoints)
                    endpoint = self.config.endpoints[endpoint_idx]
                    
                    task = self._make_api_request(endpoint, user_id, request_num)
                    tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for result in results:
                if isinstance(result, dict):
                    self.results.append(result)
                else:
                    self.errors.append(str(result))
        
        finally:
            self.metrics_collector.stop_collection()
    
    async def _run_load_test_benchmark(self):
        """Run load test benchmark with increasing load"""
        
        # Start metrics collection
        self.metrics_collector.start_collection()
        
        try:
            # Gradually increase load
            load_steps = [2, 5, 10, 15, 20]
            
            for concurrent_users in load_steps:
                if concurrent_users > self.config.concurrent_users:
                    break
                
                # Run test with current load
                tasks = []
                for user_id in range(concurrent_users):
                    for request_num in range(self.config.requests_per_user):
                        endpoint = self.config.endpoints[request_num % len(self.config.endpoints)]
                        task = self._make_api_request(endpoint, user_id, request_num, load_step=concurrent_users)
                        tasks.append(task)
                
                step_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Process step results
                for result in step_results:
                    if isinstance(result, dict):
                        result['load_step'] = concurrent_users
                        self.results.append(result)
                    else:
                        self.errors.append(f"Load step {concurrent_users}: {str(result)}")
                
                # Brief pause between load steps
                await asyncio.sleep(2.0)
        
        finally:
            self.metrics_collector.stop_collection()
    
    async def _run_stress_test_benchmark(self):
        """Run stress test with maximum load"""
        
        # Use higher concurrent users for stress test
        stress_concurrent_users = min(self.config.concurrent_users * 2, 50)
        
        # Start metrics collection
        self.metrics_collector.start_collection()
        
        try:
            # Run stress test
            tasks = []
            for user_id in range(stress_concurrent_users):
                for request_num in range(self.config.requests_per_user):
                    endpoint = self.config.endpoints[request_num % len(self.config.endpoints)]
                    task = self._make_api_request(endpoint, user_id, request_num, stress_test=True)
                    tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for result in results:
                if isinstance(result, dict):
                    result['stress_test'] = True
                    self.results.append(result)
                else:
                    self.errors.append(str(result))
        
        finally:
            self.metrics_collector.stop_collection()
    
    async def _run_endurance_test_benchmark(self):
        """Run endurance test over extended period"""
        
        # Extend duration for endurance test
        endurance_duration = max(self.config.duration * 2, 300)  # At least 5 minutes
        
        # Start metrics collection
        self.metrics_collector.start_collection()
        
        try:
            start_time = time.time()
            
            while time.time() - start_time < endurance_duration:
                # Run batch of requests
                tasks = []
                for user_id in range(self.config.concurrent_users):
                    endpoint = self.config.endpoints[user_id % len(self.config.endpoints)]
                    task = self._make_api_request(endpoint, user_id, 0, endurance_test=True)
                    tasks.append(task)
                
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Process batch results
                for result in batch_results:
                    if isinstance(result, dict):
                        result['endurance_test'] = True
                        result['elapsed_time'] = time.time() - start_time
                        self.results.append(result)
                    else:
                        self.errors.append(str(result))
                
                # Brief pause between batches
                await asyncio.sleep(1.0)
        
        finally:
            self.metrics_collector.stop_collection()
    
    async def _run_spike_test_benchmark(self):
        """Run spike test with sudden load increases"""
        
        # Start metrics collection
        self.metrics_collector.start_collection()
        
        try:
            # Normal load phase
            normal_load = max(self.config.concurrent_users // 4, 2)
            
            # Run normal load
            tasks = []
            for user_id in range(normal_load):
                for request_num in range(self.config.requests_per_user):
                    endpoint = self.config.endpoints[request_num % len(self.config.endpoints)]
                    task = self._make_api_request(endpoint, user_id, request_num, phase="normal")
                    tasks.append(task)
            
            await asyncio.gather(*tasks, return_exceptions=True)
            
            # Spike phase
            spike_load = self.config.concurrent_users * 2
            
            # Run spike load
            spike_tasks = []
            for user_id in range(spike_load):
                for request_num in range(self.config.requests_per_user):
                    endpoint = self.config.endpoints[request_num % len(self.config.endpoints)]
                    task = self._make_api_request(endpoint, user_id, request_num, phase="spike")
                    spike_tasks.append(task)
            
            spike_results = await asyncio.gather(*spike_tasks, return_exceptions=True)
            
            # Process all results
            all_results = tasks + spike_results
            for result in all_results:
                if isinstance(result, dict):
                    self.results.append(result)
                else:
                    self.errors.append(str(result))
        
        finally:
            self.metrics_collector.stop_collection()
    
    async def _run_cache_performance_benchmark(self):
        """Run cache performance benchmark"""
        
        # Start metrics collection
        self.metrics_collector.start_collection()
        
        try:
            # Cache miss phase (unique queries)
            cache_miss_tasks = []
            for user_id in range(self.config.concurrent_users):
                for request_num in range(self.config.requests_per_user):
                    endpoint = self.config.endpoints[0]  # Use first endpoint
                    task = self._make_api_request(
                        endpoint, user_id, request_num, 
                        cache_test=True, cache_phase="miss",
                        unique_query=True
                    )
                    cache_miss_tasks.append(task)
            
            cache_miss_results = await asyncio.gather(*cache_miss_tasks, return_exceptions=True)
            
            # Cache hit phase (repeated queries)
            cache_hit_tasks = []
            repeated_query = self.config.query_patterns[0]
            
            for user_id in range(self.config.concurrent_users):
                for request_num in range(self.config.requests_per_user):
                    endpoint = self.config.endpoints[0]
                    task = self._make_api_request(
                        endpoint, user_id, request_num,
                        cache_test=True, cache_phase="hit",
                        fixed_query=repeated_query
                    )
                    cache_hit_tasks.append(task)
            
            cache_hit_results = await asyncio.gather(*cache_hit_tasks, return_exceptions=True)
            
            # Process all results
            all_results = cache_miss_results + cache_hit_results
            for result in all_results:
                if isinstance(result, dict):
                    self.results.append(result)
                else:
                    self.errors.append(str(result))
        
        finally:
            self.metrics_collector.stop_collection()
    
    async def _run_websocket_performance_benchmark(self):
        """Run WebSocket performance benchmark"""
        
        # Start metrics collection
        self.metrics_collector.start_collection()
        
        try:
            # Create WebSocket connections
            websocket_tasks = []
            
            for user_id in range(self.config.concurrent_users):
                task = self._run_websocket_session(user_id)
                websocket_tasks.append(task)
            
            websocket_results = await asyncio.gather(*websocket_tasks, return_exceptions=True)
            
            # Process results
            for result in websocket_results:
                if isinstance(result, list):
                    self.results.extend(result)
                else:
                    self.errors.append(str(result))
        
        finally:
            self.metrics_collector.stop_collection()
    
    async def _run_warmup(self, endpoint: str):
        """Run warmup requests"""
        
        if self.config.warmup_duration <= 0:
            return
        
        warmup_tasks = []
        for i in range(min(self.config.concurrent_users, 5)):
            task = self._make_api_request(endpoint, i, 0, warmup=True)
            warmup_tasks.append(task)
        
        await asyncio.gather(*warmup_tasks, return_exceptions=True)
        await asyncio.sleep(self.config.warmup_duration)
    
    async def _make_api_request(self, endpoint: str, user_id: int, request_num: int, **kwargs) -> Dict[str, Any]:
        """Make a single API request"""
        
        start_time = time.time()
        
        # Select query based on parameters
        query = self._select_query(user_id, request_num, **kwargs)
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.config.base_url}{endpoint}",
                    json={
                        "query": query,
                        "repository_id": "benchmark_repo"
                    },
                    headers=self.auth_headers
                )
                
                end_time = time.time()
                
                # Record metrics
                request_metrics = RequestMetrics(
                    request_id=f"user_{user_id}_req_{request_num}",
                    start_time=start_time,
                    end_time=end_time,
                    response_time=end_time - start_time,
                    status_code=response.status_code,
                    success=response.status_code == 200,
                    user_id=str(user_id),
                    endpoint=endpoint
                )
                
                self.metrics_collector.record_request(request_metrics)
                
                return {
                    "user_id": user_id,
                    "request_num": request_num,
                    "endpoint": endpoint,
                    "query": query,
                    "response_time": end_time - start_time,
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "response_size": len(response.content) if response.content else 0,
                    **kwargs
                }
        
        except Exception as e:
            end_time = time.time()
            
            # Record failed metrics
            request_metrics = RequestMetrics(
                request_id=f"user_{user_id}_req_{request_num}",
                start_time=start_time,
                end_time=end_time,
                response_time=end_time - start_time,
                status_code=500,
                success=False,
                error_message=str(e),
                user_id=str(user_id),
                endpoint=endpoint
            )
            
            self.metrics_collector.record_request(request_metrics)
            
            return {
                "user_id": user_id,
                "request_num": request_num,
                "endpoint": endpoint,
                "query": query,
                "response_time": end_time - start_time,
                "status_code": 500,
                "success": False,
                "error": str(e),
                **kwargs
            }
    
    async def _run_websocket_session(self, user_id: int) -> List[Dict[str, Any]]:
        """Run a WebSocket session"""
        
        client = WebSocketTestClient(self.config.websocket_url)
        session_results = []
        
        try:
            # Connect
            connected = await client.connect({
                "Authorization": self.auth_headers.get("Authorization", "")
            })
            
            if not connected:
                return [{
                    "user_id": user_id,
                    "websocket_test": True,
                    "success": False,
                    "error": "Failed to connect"
                }]
            
            # Send queries
            for request_num in range(self.config.requests_per_user):
                query = self.config.query_patterns[request_num % len(self.config.query_patterns)]
                
                start_time = time.time()
                
                try:
                    events = await client.query_and_receive(query)
                    end_time = time.time()
                    
                    session_results.append({
                        "user_id": user_id,
                        "request_num": request_num,
                        "websocket_test": True,
                        "query": query,
                        "response_time": end_time - start_time,
                        "success": True,
                        "events_count": len(events)
                    })
                    
                except Exception as e:
                    end_time = time.time()
                    
                    session_results.append({
                        "user_id": user_id,
                        "request_num": request_num,
                        "websocket_test": True,
                        "query": query,
                        "response_time": end_time - start_time,
                        "success": False,
                        "error": str(e)
                    })
            
            await client.disconnect()
            
        except Exception as e:
            session_results.append({
                "user_id": user_id,
                "websocket_test": True,
                "success": False,
                "error": f"Session error: {str(e)}"
            })
        
        return session_results
    
    def _select_query(self, user_id: int, request_num: int, **kwargs) -> str:
        """Select query based on test parameters"""
        
        # Fixed query for cache tests
        if kwargs.get("fixed_query"):
            return kwargs["fixed_query"]
        
        # Unique query for cache miss tests
        if kwargs.get("unique_query"):
            return f"{self.config.query_patterns[0]} (unique {user_id}-{request_num}-{time.time()})"
        
        # Round-robin through patterns
        pattern_idx = (user_id * self.config.requests_per_user + request_num) % len(self.config.query_patterns)
        return self.config.query_patterns[pattern_idx]
    
    def _calculate_overall_metrics(self) -> BaselineMetrics:
        """Calculate overall performance metrics"""
        
        successful_requests = [r for r in self.results if r.get("success", False)]
        failed_requests = [r for r in self.results if not r.get("success", False)]
        
        if not successful_requests:
            return BaselineMetrics(
                avg_response_time=0.0, p50_response_time=0.0, p95_response_time=0.0,
                p99_response_time=0.0, max_response_time=0.0, requests_per_second=0.0,
                concurrent_requests=0, success_rate=0.0, error_rate=100.0,
                avg_cpu_percent=0.0, max_cpu_percent=0.0, avg_memory_percent=0.0,
                max_memory_percent=0.0
            )
        
        response_times = [r["response_time"] for r in successful_requests]
        
        # Get system metrics
        system_metrics = self.metrics_collector.get_current_metrics()
        
        # Calculate throughput
        total_duration = system_metrics.get("collection_duration", 1.0)
        requests_per_second = len(successful_requests) / max(total_duration, 1.0)
        
        return BaselineMetrics(
            avg_response_time=statistics.mean(response_times),
            p50_response_time=statistics.median(response_times),
            p95_response_time=statistics.quantiles(response_times, n=20)[18] if len(response_times) > 10 else max(response_times),
            p99_response_time=statistics.quantiles(response_times, n=100)[98] if len(response_times) > 50 else max(response_times),
            max_response_time=max(response_times),
            requests_per_second=requests_per_second,
            concurrent_requests=self.config.concurrent_users,
            success_rate=(len(successful_requests) / len(self.results)) * 100,
            error_rate=(len(failed_requests) / len(self.results)) * 100,
            avg_cpu_percent=system_metrics.get("system", {}).get("average_cpu_percent", 0.0),
            max_cpu_percent=system_metrics.get("system", {}).get("current_cpu_percent", 0.0),
            avg_memory_percent=system_metrics.get("system", {}).get("average_memory_percent", 0.0),
            max_memory_percent=system_metrics.get("system", {}).get("current_memory_percent", 0.0),
            total_requests=len(self.results),
            sample_size=len(successful_requests),
            test_duration=total_duration
        )
    
    def _calculate_endpoint_metrics(self) -> Dict[str, BaselineMetrics]:
        """Calculate per-endpoint metrics"""
        
        endpoint_metrics = {}
        
        for endpoint in self.config.endpoints:
            endpoint_results = [r for r in self.results if r.get("endpoint") == endpoint]
            
            if not endpoint_results:
                continue
            
            successful_requests = [r for r in endpoint_results if r.get("success", False)]
            failed_requests = [r for r in endpoint_results if not r.get("success", False)]
            
            if not successful_requests:
                continue
            
            response_times = [r["response_time"] for r in successful_requests]
            
            endpoint_metrics[endpoint] = BaselineMetrics(
                avg_response_time=statistics.mean(response_times),
                p50_response_time=statistics.median(response_times),
                p95_response_time=statistics.quantiles(response_times, n=20)[18] if len(response_times) > 10 else max(response_times),
                p99_response_time=statistics.quantiles(response_times, n=100)[98] if len(response_times) > 50 else max(response_times),
                max_response_time=max(response_times),
                requests_per_second=len(successful_requests) / max(self.config.duration, 1.0),
                concurrent_requests=self.config.concurrent_users,
                success_rate=(len(successful_requests) / len(endpoint_results)) * 100,
                error_rate=(len(failed_requests) / len(endpoint_results)) * 100,
                avg_cpu_percent=0.0,  # Would need per-endpoint system metrics
                max_cpu_percent=0.0,
                avg_memory_percent=0.0,
                max_memory_percent=0.0,
                total_requests=len(endpoint_results),
                sample_size=len(successful_requests)
            )
        
        return endpoint_metrics
    
    def _calculate_performance_score(self, metrics: BaselineMetrics) -> float:
        """Calculate performance score (0-100)"""
        
        score = 100.0
        
        # Response time scoring (30% weight)
        if metrics.avg_response_time > self.config.max_response_time:
            response_time_penalty = min(50, (metrics.avg_response_time / self.config.max_response_time - 1) * 30)
            score -= response_time_penalty
        
        # Throughput scoring (30% weight)
        if metrics.requests_per_second < self.config.min_throughput:
            throughput_penalty = min(30, (1 - metrics.requests_per_second / self.config.min_throughput) * 30)
            score -= throughput_penalty
        
        # Error rate scoring (25% weight)
        if metrics.error_rate > self.config.max_error_rate:
            error_penalty = min(25, (metrics.error_rate / self.config.max_error_rate - 1) * 25)
            score -= error_penalty
        
        # Success rate scoring (15% weight)
        if metrics.success_rate < 95:
            success_penalty = (95 - metrics.success_rate) * 0.15
            score -= success_penalty
        
        return max(0.0, score)
    
    def _calculate_performance_grade(self, score: float) -> str:
        """Calculate performance grade based on score"""
        
        if score >= 90:
            return "A"
        elif score >= 80:
            return "B"
        elif score >= 70:
            return "C"
        elif score >= 60:
            return "D"
        else:
            return "F"
    
    def _summarize_errors(self) -> Dict[str, int]:
        """Summarize error types and counts"""
        
        error_summary = {}
        
        for error in self.errors:
            error_type = "unknown"
            
            if "timeout" in error.lower():
                error_type = "timeout"
            elif "connection" in error.lower():
                error_type = "connection"
            elif "rate limit" in error.lower():
                error_type = "rate_limit"
            elif "500" in error:
                error_type = "internal_server_error"
            elif "404" in error:
                error_type = "not_found"
            elif "401" in error or "403" in error:
                error_type = "authentication"
            
            error_summary[error_type] = error_summary.get(error_type, 0) + 1
        
        return error_summary
    
    def _generate_recommendations(self, metrics: BaselineMetrics) -> List[str]:
        """Generate performance recommendations"""
        
        recommendations = []
        
        # Response time recommendations
        if metrics.avg_response_time > self.config.max_response_time:
            recommendations.append(f"Average response time ({metrics.avg_response_time:.2f}s) exceeds target ({self.config.max_response_time}s). Consider optimizing query processing.")
        
        # Throughput recommendations
        if metrics.requests_per_second < self.config.min_throughput:
            recommendations.append(f"Throughput ({metrics.requests_per_second:.1f} req/s) is below target ({self.config.min_throughput} req/s). Consider scaling or optimization.")
        
        # Error rate recommendations
        if metrics.error_rate > self.config.max_error_rate:
            recommendations.append(f"Error rate ({metrics.error_rate:.1f}%) exceeds target ({self.config.max_error_rate}%). Review error handling and system stability.")
        
        # Success rate recommendations
        if metrics.success_rate < 95:
            recommendations.append(f"Success rate ({metrics.success_rate:.1f}%) is below 95%. Investigate failure causes.")
        
        # System resource recommendations
        if metrics.avg_cpu_percent > 80:
            recommendations.append("High CPU usage detected. Consider CPU optimization or scaling.")
        
        if metrics.avg_memory_percent > 80:
            recommendations.append("High memory usage detected. Consider memory optimization or scaling.")
        
        # Performance distribution recommendations
        if metrics.p95_response_time > metrics.avg_response_time * 2:
            recommendations.append("High response time variance detected. Investigate performance consistency.")
        
        if not recommendations:
            recommendations.append("Performance is within acceptable limits.")
        
        return recommendations


class BenchmarkRunner:
    """Comprehensive benchmark runner"""
    
    def __init__(self, baseline_manager: BaselineManager, storage_path: str = "benchmark_results"):
        self.baseline_manager = baseline_manager
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        self.regression_test = RegressionTest(baseline_manager)
    
    async def run_benchmark(self, config: BenchmarkConfig, auth_headers: Dict[str, str]) -> BenchmarkResult:
        """Run a single benchmark"""
        
        # Create benchmark test
        benchmark_test = BenchmarkTest(config, auth_headers)
        
        # Run the test
        result = await benchmark_test.run()
        
        # Run regression analysis if baseline specified
        if config.baseline_id and result.status == BenchmarkStatus.COMPLETED:
            try:
                regression_config = RegressionTestConfig(
                    test_duration=config.duration,
                    concurrent_users=config.concurrent_users,
                    requests_per_user=config.requests_per_user
                )
                
                # Create test function from benchmark results
                async def test_func(user_id: int = 0, request_num: int = 0, **kwargs):
                    return {"success": True, "response_time": result.overall_metrics.avg_response_time}
                
                regression_result = await self.regression_test.run_regression_test(
                    config.baseline_id, test_func, regression_config
                )
                
                result.regression_result = regression_result
                
            except Exception as e:
                result.recommendations.append(f"Regression analysis failed: {str(e)}")
        
        # Create baseline if requested
        if config.create_baseline and result.status == BenchmarkStatus.COMPLETED:
            try:
                baseline_id = config.baseline_id or f"benchmark_{config.benchmark_id}"
                
                self.baseline_manager.create_baseline(
                    baseline_id=baseline_id,
                    version="1.0.0",
                    service_version="latest",
                    metrics=result.overall_metrics,
                    test_config=config.to_dict(),
                    environment="benchmark",
                    tags=["benchmark", config.benchmark_type.value],
                    metadata={"benchmark_id": config.benchmark_id}
                )
                
                result.recommendations.append(f"Created baseline: {baseline_id}")
                
            except Exception as e:
                result.recommendations.append(f"Baseline creation failed: {str(e)}")
        
        # Save result
        await self._save_result(result)
        
        return result
    
    async def run_benchmark_suite(self, configs: List[BenchmarkConfig], auth_headers: Dict[str, str]) -> List[BenchmarkResult]:
        """Run a suite of benchmarks"""
        
        results = []
        
        for config in configs:
            try:
                result = await self.run_benchmark(config, auth_headers)
                results.append(result)
                
                # Brief pause between benchmarks
                await asyncio.sleep(5.0)
                
            except Exception as e:
                # Create failed result
                failed_result = BenchmarkResult(
                    benchmark_id=config.benchmark_id,
                    benchmark_type=config.benchmark_type,
                    start_time=datetime.now(),
                    end_time=datetime.now(),
                    duration=0.0,
                    status=BenchmarkStatus.FAILED,
                    overall_metrics=BaselineMetrics(
                        avg_response_time=0.0, p50_response_time=0.0, p95_response_time=0.0,
                        p99_response_time=0.0, max_response_time=0.0, requests_per_second=0.0,
                        concurrent_requests=0, success_rate=0.0, error_rate=100.0,
                        avg_cpu_percent=0.0, max_cpu_percent=0.0, avg_memory_percent=0.0,
                        max_memory_percent=0.0
                    ),
                    recommendations=[f"Benchmark failed: {str(e)}"]
                )
                
                results.append(failed_result)
        
        return results
    
    async def _save_result(self, result: BenchmarkResult):
        """Save benchmark result to storage"""
        
        # Create filename
        timestamp = result.start_time.strftime("%Y%m%d_%H%M%S")
        filename = f"{result.benchmark_id}_{timestamp}.json"
        filepath = self.storage_path / filename
        
        # Save result
        with open(filepath, 'w') as f:
            json.dump(result.to_dict(), f, indent=2)
    
    def get_benchmark_history(self, benchmark_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get benchmark history"""
        
        history = []
        
        for filepath in self.storage_path.glob("*.json"):
            try:
                with open(filepath, 'r') as f:
                    result_data = json.load(f)
                
                if benchmark_id and result_data.get("benchmark_id") != benchmark_id:
                    continue
                
                history.append(result_data)
                
            except Exception:
                continue
        
        return sorted(history, key=lambda x: x.get("start_time", ""), reverse=True)


class BenchmarkSuite:
    """Predefined benchmark suite configurations"""
    
    @staticmethod
    def create_comprehensive_suite(base_url: str = "http://localhost:8000") -> List[BenchmarkConfig]:
        """Create comprehensive benchmark suite"""
        
        return [
            # Single endpoint performance
            BenchmarkConfig(
                benchmark_id="single_endpoint_query",
                benchmark_type=BenchmarkType.SINGLE_ENDPOINT,
                duration=60.0,
                concurrent_users=10,
                requests_per_user=10,
                endpoints=["/api/v1/query"],
                base_url=base_url,
                baseline_id="query_endpoint_baseline"
            ),
            
            # Load testing
            BenchmarkConfig(
                benchmark_id="load_test_progressive",
                benchmark_type=BenchmarkType.LOAD_TEST,
                duration=120.0,
                concurrent_users=20,
                requests_per_user=5,
                endpoints=["/api/v1/query"],
                base_url=base_url,
                baseline_id="load_test_baseline"
            ),
            
            # Cache performance
            BenchmarkConfig(
                benchmark_id="cache_performance",
                benchmark_type=BenchmarkType.CACHE_PERFORMANCE,
                duration=90.0,
                concurrent_users=15,
                requests_per_user=8,
                endpoints=["/api/v1/query"],
                base_url=base_url,
                baseline_id="cache_baseline"
            ),
            
            # WebSocket performance
            BenchmarkConfig(
                benchmark_id="websocket_performance",
                benchmark_type=BenchmarkType.WEBSOCKET_PERFORMANCE,
                duration=60.0,
                concurrent_users=8,
                requests_per_user=5,
                endpoints=["/api/v1/ws/query"],
                base_url=base_url,
                websocket_url=base_url.replace("http", "ws"),
                baseline_id="websocket_baseline"
            ),
            
            # Stress testing
            BenchmarkConfig(
                benchmark_id="stress_test_max_load",
                benchmark_type=BenchmarkType.STRESS_TEST,
                duration=90.0,
                concurrent_users=30,
                requests_per_user=10,
                endpoints=["/api/v1/query"],
                base_url=base_url,
                baseline_id="stress_test_baseline"
            )
        ]
    
    @staticmethod
    def create_quick_suite(base_url: str = "http://localhost:8000") -> List[BenchmarkConfig]:
        """Create quick benchmark suite for CI/CD"""
        
        return [
            BenchmarkConfig(
                benchmark_id="quick_performance_check",
                benchmark_type=BenchmarkType.SINGLE_ENDPOINT,
                duration=30.0,
                concurrent_users=5,
                requests_per_user=5,
                endpoints=["/api/v1/query"],
                base_url=base_url,
                baseline_id="quick_check_baseline"
            ),
            
            BenchmarkConfig(
                benchmark_id="quick_cache_check",
                benchmark_type=BenchmarkType.CACHE_PERFORMANCE,
                duration=30.0,
                concurrent_users=5,
                requests_per_user=3,
                endpoints=["/api/v1/query"],
                base_url=base_url,
                baseline_id="quick_cache_baseline"
            )
        ]