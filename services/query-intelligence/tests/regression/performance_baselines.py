"""
Performance Baseline Management System

Manages performance baselines for regression testing, including storage,
versioning, and comparison capabilities.
"""

import json
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict, field
from pathlib import Path
import statistics
import hashlib
from enum import Enum

from ..e2e.utils.performance_monitors import RequestMetrics, SystemMetrics


class BaselineStatus(Enum):
    """Baseline status enumeration"""
    ACTIVE = "active"
    ARCHIVED = "archived"
    EXPERIMENTAL = "experimental"
    DEPRECATED = "deprecated"


@dataclass
class BaselineMetrics:
    """Core performance metrics for baseline"""
    
    # Response time metrics
    avg_response_time: float
    p50_response_time: float
    p95_response_time: float
    p99_response_time: float
    max_response_time: float
    
    # Throughput metrics
    requests_per_second: float
    concurrent_requests: int
    
    # Success metrics
    success_rate: float
    error_rate: float
    
    # Resource utilization
    avg_cpu_percent: float
    max_cpu_percent: float
    avg_memory_percent: float
    max_memory_percent: float
    
    # Cache performance
    cache_hit_rate: Optional[float] = None
    cache_miss_rate: Optional[float] = None
    
    # Network metrics
    avg_network_latency: Optional[float] = None
    
    # Test metadata
    test_duration: float = 0.0
    total_requests: int = 0
    sample_size: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BaselineMetrics':
        """Create from dictionary"""
        return cls(**data)


@dataclass
class PerformanceBaseline:
    """Performance baseline with metadata"""
    
    # Identification
    baseline_id: str
    version: str
    service_version: str
    
    # Timing
    created_at: datetime
    expires_at: Optional[datetime] = None
    
    # Status
    status: BaselineStatus = BaselineStatus.ACTIVE
    
    # Metrics
    metrics: BaselineMetrics = field(default_factory=lambda: BaselineMetrics(
        avg_response_time=0.0, p50_response_time=0.0, p95_response_time=0.0,
        p99_response_time=0.0, max_response_time=0.0, requests_per_second=0.0,
        concurrent_requests=0, success_rate=0.0, error_rate=0.0,
        avg_cpu_percent=0.0, max_cpu_percent=0.0, avg_memory_percent=0.0,
        max_memory_percent=0.0
    ))
    
    # Test configuration
    test_config: Dict[str, Any] = field(default_factory=dict)
    
    # Environment info
    environment: str = "test"
    git_commit: Optional[str] = None
    
    # Tags and metadata
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Statistics
    confidence_interval: float = 0.95
    statistical_significance: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        data['expires_at'] = self.expires_at.isoformat() if self.expires_at else None
        data['status'] = self.status.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PerformanceBaseline':
        """Create from dictionary"""
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        data['expires_at'] = datetime.fromisoformat(data['expires_at']) if data['expires_at'] else None
        data['status'] = BaselineStatus(data['status'])
        data['metrics'] = BaselineMetrics.from_dict(data['metrics'])
        return cls(**data)
    
    def is_expired(self) -> bool:
        """Check if baseline is expired"""
        if self.expires_at is None:
            return False
        return datetime.now() > self.expires_at
    
    def get_baseline_hash(self) -> str:
        """Generate hash for baseline identification"""
        content = f"{self.baseline_id}:{self.version}:{self.service_version}:{self.created_at.isoformat()}"
        return hashlib.md5(content.encode()).hexdigest()[:8]


@dataclass
class BaselineComparison:
    """Comparison result between current performance and baseline"""
    
    baseline: PerformanceBaseline
    current_metrics: BaselineMetrics
    
    # Comparison results
    response_time_change: float  # Percentage change
    throughput_change: float     # Percentage change
    error_rate_change: float     # Percentage change
    cpu_usage_change: float      # Percentage change
    memory_usage_change: float   # Percentage change
    
    # Regression indicators
    is_regression: bool
    regression_severity: str  # "minor", "moderate", "severe"
    regression_reasons: List[str]
    
    # Statistical significance
    is_significant: bool
    confidence_level: float
    
    # Recommendations
    recommendations: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = asdict(self)
        data['baseline'] = self.baseline.to_dict()
        data['current_metrics'] = self.current_metrics.to_dict()
        return data


class BaselineManager:
    """Manages performance baselines storage and retrieval"""
    
    def __init__(self, storage_path: str = "performance_baselines"):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        self.baselines_index_file = self.storage_path / "baselines_index.json"
        self.baselines_data_dir = self.storage_path / "baselines"
        self.baselines_data_dir.mkdir(parents=True, exist_ok=True)
        
        self._load_index()
    
    def _load_index(self):
        """Load baselines index"""
        if self.baselines_index_file.exists():
            with open(self.baselines_index_file, 'r') as f:
                self.index = json.load(f)
        else:
            self.index = {
                "baselines": {},
                "latest_versions": {},
                "archived_baselines": [],
                "created_at": datetime.now().isoformat()
            }
    
    def _save_index(self):
        """Save baselines index"""
        with open(self.baselines_index_file, 'w') as f:
            json.dump(self.index, f, indent=2)
    
    def create_baseline(
        self,
        baseline_id: str,
        version: str,
        service_version: str,
        metrics: BaselineMetrics,
        test_config: Dict[str, Any],
        environment: str = "test",
        git_commit: Optional[str] = None,
        tags: List[str] = None,
        metadata: Dict[str, Any] = None,
        expires_in_days: int = 90
    ) -> PerformanceBaseline:
        """Create new performance baseline"""
        
        # Create baseline
        baseline = PerformanceBaseline(
            baseline_id=baseline_id,
            version=version,
            service_version=service_version,
            created_at=datetime.now(),
            expires_at=datetime.now() + timedelta(days=expires_in_days),
            metrics=metrics,
            test_config=test_config,
            environment=environment,
            git_commit=git_commit,
            tags=tags or [],
            metadata=metadata or {}
        )
        
        # Generate filename
        baseline_hash = baseline.get_baseline_hash()
        filename = f"{baseline_id}_{version}_{baseline_hash}.json"
        filepath = self.baselines_data_dir / filename
        
        # Save baseline data
        with open(filepath, 'w') as f:
            json.dump(baseline.to_dict(), f, indent=2)
        
        # Update index
        if baseline_id not in self.index["baselines"]:
            self.index["baselines"][baseline_id] = {}
        
        self.index["baselines"][baseline_id][version] = {
            "filename": filename,
            "created_at": baseline.created_at.isoformat(),
            "service_version": service_version,
            "environment": environment,
            "status": baseline.status.value,
            "hash": baseline_hash
        }
        
        # Update latest version
        self.index["latest_versions"][baseline_id] = version
        
        self._save_index()
        
        return baseline
    
    def get_baseline(self, baseline_id: str, version: Optional[str] = None) -> Optional[PerformanceBaseline]:
        """Get performance baseline by ID and version"""
        if baseline_id not in self.index["baselines"]:
            return None
        
        if version is None:
            version = self.index["latest_versions"].get(baseline_id)
            if version is None:
                return None
        
        if version not in self.index["baselines"][baseline_id]:
            return None
        
        # Load baseline data
        baseline_info = self.index["baselines"][baseline_id][version]
        filepath = self.baselines_data_dir / baseline_info["filename"]
        
        if not filepath.exists():
            return None
        
        with open(filepath, 'r') as f:
            data = json.load(f)
        
        return PerformanceBaseline.from_dict(data)
    
    def list_baselines(self, environment: Optional[str] = None, status: Optional[BaselineStatus] = None) -> List[Dict[str, Any]]:
        """List all baselines with optional filtering"""
        baselines = []
        
        for baseline_id, versions in self.index["baselines"].items():
            for version, info in versions.items():
                if environment and info["environment"] != environment:
                    continue
                if status and info["status"] != status.value:
                    continue
                
                baselines.append({
                    "baseline_id": baseline_id,
                    "version": version,
                    "created_at": info["created_at"],
                    "service_version": info["service_version"],
                    "environment": info["environment"],
                    "status": info["status"],
                    "hash": info["hash"]
                })
        
        return sorted(baselines, key=lambda x: x["created_at"], reverse=True)
    
    def archive_baseline(self, baseline_id: str, version: str) -> bool:
        """Archive a baseline"""
        if baseline_id not in self.index["baselines"]:
            return False
        
        if version not in self.index["baselines"][baseline_id]:
            return False
        
        # Update status
        self.index["baselines"][baseline_id][version]["status"] = BaselineStatus.ARCHIVED.value
        
        # Add to archived list
        self.index["archived_baselines"].append({
            "baseline_id": baseline_id,
            "version": version,
            "archived_at": datetime.now().isoformat()
        })
        
        self._save_index()
        return True
    
    def delete_baseline(self, baseline_id: str, version: str) -> bool:
        """Delete a baseline"""
        if baseline_id not in self.index["baselines"]:
            return False
        
        if version not in self.index["baselines"][baseline_id]:
            return False
        
        # Get filename and delete file
        baseline_info = self.index["baselines"][baseline_id][version]
        filepath = self.baselines_data_dir / baseline_info["filename"]
        
        if filepath.exists():
            filepath.unlink()
        
        # Remove from index
        del self.index["baselines"][baseline_id][version]
        
        # Clean up empty baseline_id
        if not self.index["baselines"][baseline_id]:
            del self.index["baselines"][baseline_id]
            if baseline_id in self.index["latest_versions"]:
                del self.index["latest_versions"][baseline_id]
        
        self._save_index()
        return True
    
    def compare_with_baseline(
        self,
        baseline_id: str,
        current_metrics: BaselineMetrics,
        version: Optional[str] = None,
        regression_thresholds: Optional[Dict[str, float]] = None
    ) -> Optional[BaselineComparison]:
        """Compare current metrics with baseline"""
        baseline = self.get_baseline(baseline_id, version)
        if not baseline:
            return None
        
        if regression_thresholds is None:
            regression_thresholds = {
                "response_time": 20.0,  # 20% increase
                "throughput": 15.0,     # 15% decrease
                "error_rate": 0.5,      # 0.5% increase
                "cpu_usage": 25.0,      # 25% increase
                "memory_usage": 25.0    # 25% increase
            }
        
        # Calculate percentage changes
        def calc_change(current: float, baseline: float) -> float:
            if baseline == 0:
                return 0.0
            return ((current - baseline) / baseline) * 100
        
        response_time_change = calc_change(current_metrics.avg_response_time, baseline.metrics.avg_response_time)
        throughput_change = calc_change(current_metrics.requests_per_second, baseline.metrics.requests_per_second)
        error_rate_change = calc_change(current_metrics.error_rate, baseline.metrics.error_rate)
        cpu_usage_change = calc_change(current_metrics.avg_cpu_percent, baseline.metrics.avg_cpu_percent)
        memory_usage_change = calc_change(current_metrics.avg_memory_percent, baseline.metrics.avg_memory_percent)
        
        # Detect regressions
        regression_reasons = []
        
        if response_time_change > regression_thresholds["response_time"]:
            regression_reasons.append(f"Response time increased by {response_time_change:.1f}%")
        
        if throughput_change < -regression_thresholds["throughput"]:
            regression_reasons.append(f"Throughput decreased by {abs(throughput_change):.1f}%")
        
        if error_rate_change > regression_thresholds["error_rate"]:
            regression_reasons.append(f"Error rate increased by {error_rate_change:.1f}%")
        
        if cpu_usage_change > regression_thresholds["cpu_usage"]:
            regression_reasons.append(f"CPU usage increased by {cpu_usage_change:.1f}%")
        
        if memory_usage_change > regression_thresholds["memory_usage"]:
            regression_reasons.append(f"Memory usage increased by {memory_usage_change:.1f}%")
        
        # Determine regression severity
        is_regression = len(regression_reasons) > 0
        severity = "none"
        
        if is_regression:
            max_change = max(
                abs(response_time_change),
                abs(throughput_change),
                abs(error_rate_change),
                abs(cpu_usage_change),
                abs(memory_usage_change)
            )
            
            if max_change > 50:
                severity = "severe"
            elif max_change > 30:
                severity = "moderate"
            else:
                severity = "minor"
        
        # Generate recommendations
        recommendations = []
        if response_time_change > 10:
            recommendations.append("Consider optimizing query processing or adding caching")
        if throughput_change < -10:
            recommendations.append("Review resource allocation and scaling settings")
        if error_rate_change > 0.1:
            recommendations.append("Investigate error sources and improve error handling")
        if cpu_usage_change > 20:
            recommendations.append("Profile CPU usage and optimize performance bottlenecks")
        if memory_usage_change > 20:
            recommendations.append("Review memory usage patterns and optimize memory allocation")
        
        return BaselineComparison(
            baseline=baseline,
            current_metrics=current_metrics,
            response_time_change=response_time_change,
            throughput_change=throughput_change,
            error_rate_change=error_rate_change,
            cpu_usage_change=cpu_usage_change,
            memory_usage_change=memory_usage_change,
            is_regression=is_regression,
            regression_severity=severity,
            regression_reasons=regression_reasons,
            is_significant=abs(response_time_change) > 5 or abs(throughput_change) > 5,
            confidence_level=0.95,
            recommendations=recommendations
        )
    
    def auto_create_baseline_from_tests(
        self,
        baseline_id: str,
        version: str,
        service_version: str,
        test_results: List[Dict[str, Any]],
        environment: str = "test"
    ) -> PerformanceBaseline:
        """Automatically create baseline from test results"""
        
        # Extract metrics from test results
        response_times = []
        success_rates = []
        cpu_usages = []
        memory_usages = []
        throughputs = []
        
        for result in test_results:
            if "response_time" in result:
                response_times.append(result["response_time"])
            if "success_rate" in result:
                success_rates.append(result["success_rate"])
            if "cpu_percent" in result:
                cpu_usages.append(result["cpu_percent"])
            if "memory_percent" in result:
                memory_usages.append(result["memory_percent"])
            if "requests_per_second" in result:
                throughputs.append(result["requests_per_second"])
        
        # Calculate baseline metrics
        metrics = BaselineMetrics(
            avg_response_time=statistics.mean(response_times) if response_times else 0.0,
            p50_response_time=statistics.median(response_times) if response_times else 0.0,
            p95_response_time=statistics.quantiles(response_times, n=20)[18] if len(response_times) > 10 else 0.0,
            p99_response_time=statistics.quantiles(response_times, n=100)[98] if len(response_times) > 50 else 0.0,
            max_response_time=max(response_times) if response_times else 0.0,
            requests_per_second=statistics.mean(throughputs) if throughputs else 0.0,
            concurrent_requests=len(test_results),
            success_rate=statistics.mean(success_rates) if success_rates else 0.0,
            error_rate=100 - statistics.mean(success_rates) if success_rates else 0.0,
            avg_cpu_percent=statistics.mean(cpu_usages) if cpu_usages else 0.0,
            max_cpu_percent=max(cpu_usages) if cpu_usages else 0.0,
            avg_memory_percent=statistics.mean(memory_usages) if memory_usages else 0.0,
            max_memory_percent=max(memory_usages) if memory_usages else 0.0,
            total_requests=sum(result.get("total_requests", 1) for result in test_results),
            sample_size=len(test_results)
        )
        
        # Create test configuration
        test_config = {
            "test_count": len(test_results),
            "test_types": list(set(result.get("test_type", "unknown") for result in test_results)),
            "auto_generated": True,
            "created_from": "test_results"
        }
        
        return self.create_baseline(
            baseline_id=baseline_id,
            version=version,
            service_version=service_version,
            metrics=metrics,
            test_config=test_config,
            environment=environment,
            tags=["auto-generated", "from-tests"],
            metadata={"source": "automated_test_results"}
        )
    
    def cleanup_expired_baselines(self) -> int:
        """Remove expired baselines"""
        cleaned_count = 0
        
        for baseline_id, versions in list(self.index["baselines"].items()):
            for version in list(versions.keys()):
                baseline = self.get_baseline(baseline_id, version)
                if baseline and baseline.is_expired():
                    self.delete_baseline(baseline_id, version)
                    cleaned_count += 1
        
        return cleaned_count