"""
Performance Regression Testing Suite

This module provides comprehensive performance regression testing capabilities
for the query-intelligence service, including baseline management, regression
detection, and automated monitoring.
"""

__version__ = "1.0.0"

from .performance_baselines import (
    PerformanceBaseline,
    BaselineManager,
    BaselineMetrics,
    BaselineComparison
)

from .regression_tests import (
    RegressionDetector,
    RegressionTest,
    RegressionAnalyzer,
    RegressionResult
)

from .benchmark_suite import (
    BenchmarkSuite,
    BenchmarkTest,
    BenchmarkResult,
    BenchmarkRunner
)

from .trend_analysis import (
    TrendAnalyzer,
    PerformanceTrend,
    TrendPredictor,
    TrendAlert
)

from .ci_performance_gates import (
    PerformanceGate,
    CIPipelineIntegration,
    GateResult,
    GatePolicy
)

__all__ = [
    "PerformanceBaseline",
    "BaselineManager",
    "BaselineMetrics",
    "BaselineComparison",
    "RegressionDetector",
    "RegressionTest",
    "RegressionAnalyzer",
    "RegressionResult",
    "BenchmarkSuite",
    "BenchmarkTest",
    "BenchmarkResult",
    "BenchmarkRunner",
    "TrendAnalyzer",
    "PerformanceTrend",
    "TrendPredictor",
    "TrendAlert",
    "PerformanceGate",
    "CIPipelineIntegration",
    "GateResult",
    "GatePolicy",
]