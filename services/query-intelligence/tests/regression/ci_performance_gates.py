"""
CI/CD Performance Gates

Automated performance testing and regression detection for CI/CD pipelines.
Provides performance gates that can block deployments when regressions are detected.
"""

import asyncio
import json
import os
import sys
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field, asdict
from datetime import datetime
from enum import Enum
from pathlib import Path
import subprocess

from .performance_baselines import BaselineManager, BaselineMetrics
from .regression_tests import RegressionTest, RegressionTestConfig, RegressionResult, RegressionSeverity
from .benchmark_suite import Benchmark<PERSON><PERSON><PERSON>, BenchmarkConfig, BenchmarkType, BenchmarkResult


class GateStatus(Enum):
    """Performance gate status"""
    PASSED = "passed"
    FAILED = "failed"
    WARNING = "warning"
    SKIPPED = "skipped"
    ERROR = "error"


class GateAction(Enum):
    """Action to take when gate fails"""
    BLOCK = "block"        # Block deployment
    WARN = "warn"          # Log warning but continue
    ADVISORY = "advisory"  # Informational only


@dataclass
class GatePolicy:
    """Performance gate policy configuration"""
    
    # Gate identification
    gate_id: str
    name: str
    description: str
    
    # Performance thresholds
    max_response_time_ms: float = 2000  # 2 seconds
    min_throughput_rps: float = 10.0    # 10 requests per second
    max_error_rate_percent: float = 1.0  # 1% error rate
    max_cpu_percent: float = 80.0       # 80% CPU usage
    max_memory_percent: float = 80.0    # 80% memory usage
    
    # Regression thresholds
    max_response_time_regression: float = 20.0  # 20% increase
    max_throughput_regression: float = 15.0     # 15% decrease
    max_error_rate_regression: float = 0.5      # 0.5% increase
    
    # Statistical requirements
    min_confidence_score: float = 0.8
    min_statistical_significance: float = 0.05
    
    # Test configuration
    test_duration: float = 60.0
    concurrent_users: int = 10
    requests_per_user: int = 5
    
    # Gate behavior
    action: GateAction = GateAction.BLOCK
    baseline_id: Optional[str] = None
    
    # Retry configuration
    max_retries: int = 2
    retry_delay: float = 30.0
    
    # Environment settings
    environment: str = "ci"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = asdict(self)
        data['action'] = self.action.value
        return data


@dataclass
class GateResult:
    """Result of a performance gate execution"""
    
    # Gate identification
    gate_id: str
    gate_name: str
    
    # Execution info
    start_time: datetime
    end_time: datetime
    duration: float
    
    # Results
    status: GateStatus
    action_taken: GateAction
    
    # Performance metrics
    current_metrics: BaselineMetrics
    baseline_metrics: Optional[BaselineMetrics] = None
    
    # Regression analysis
    regression_result: Optional[RegressionResult] = None
    
    # Benchmark results
    benchmark_results: List[BenchmarkResult] = field(default_factory=list)
    
    # Threshold violations
    threshold_violations: List[str] = field(default_factory=list)
    
    # Messages and recommendations
    messages: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    
    # Raw data
    raw_data: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        data = asdict(self)
        data['status'] = self.status.value
        data['action_taken'] = self.action_taken.value
        data['start_time'] = self.start_time.isoformat()
        data['end_time'] = self.end_time.isoformat()
        data['current_metrics'] = self.current_metrics.to_dict()
        if self.baseline_metrics:
            data['baseline_metrics'] = self.baseline_metrics.to_dict()
        if self.regression_result:
            data['regression_result'] = self.regression_result.to_dict()
        data['benchmark_results'] = [r.to_dict() for r in self.benchmark_results]
        return data


class PerformanceGate:
    """Individual performance gate"""
    
    def __init__(self, policy: GatePolicy, baseline_manager: BaselineManager):
        self.policy = policy
        self.baseline_manager = baseline_manager
        self.benchmark_runner = BenchmarkRunner(baseline_manager)
        self.regression_test = RegressionTest(baseline_manager)
    
    async def execute(self, service_url: str, auth_headers: Dict[str, str]) -> GateResult:
        """Execute the performance gate"""
        
        start_time = datetime.now()
        
        try:
            # Run performance tests
            benchmark_results = await self._run_performance_tests(service_url, auth_headers)
            
            # Analyze results
            gate_result = await self._analyze_results(benchmark_results, start_time)
            
            return gate_result
            
        except Exception as e:
            # Create error result
            end_time = datetime.now()
            
            return GateResult(
                gate_id=self.policy.gate_id,
                gate_name=self.policy.name,
                start_time=start_time,
                end_time=end_time,
                duration=(end_time - start_time).total_seconds(),
                status=GateStatus.ERROR,
                action_taken=self.policy.action,
                current_metrics=BaselineMetrics(
                    avg_response_time=0.0, p50_response_time=0.0, p95_response_time=0.0,
                    p99_response_time=0.0, max_response_time=0.0, requests_per_second=0.0,
                    concurrent_requests=0, success_rate=0.0, error_rate=100.0,
                    avg_cpu_percent=0.0, max_cpu_percent=0.0, avg_memory_percent=0.0,
                    max_memory_percent=0.0
                ),
                messages=[f"Gate execution failed: {str(e)}"],
                recommendations=["Check service health and configuration"]
            )
    
    async def _run_performance_tests(self, service_url: str, auth_headers: Dict[str, str]) -> List[BenchmarkResult]:
        """Run performance tests for the gate"""
        
        # Create benchmark configuration
        benchmark_config = BenchmarkConfig(
            benchmark_id=f"{self.policy.gate_id}_gate_test",
            benchmark_type=BenchmarkType.SINGLE_ENDPOINT,
            duration=self.policy.test_duration,
            concurrent_users=self.policy.concurrent_users,
            requests_per_user=self.policy.requests_per_user,
            endpoints=["/api/v1/query"],
            base_url=service_url,
            baseline_id=self.policy.baseline_id,
            create_baseline=False,
            max_response_time=self.policy.max_response_time_ms / 1000.0,
            min_throughput=self.policy.min_throughput_rps,
            max_error_rate=self.policy.max_error_rate_percent
        )
        
        # Run benchmark with retries
        retries = 0
        while retries <= self.policy.max_retries:
            try:
                result = await self.benchmark_runner.run_benchmark(benchmark_config, auth_headers)
                
                # Check if test was successful
                if result.status.value == "completed":
                    return [result]
                
                # If failed and we have retries left, try again
                if retries < self.policy.max_retries:
                    retries += 1
                    await asyncio.sleep(self.policy.retry_delay)
                    continue
                
                # No more retries, return failed result
                return [result]
                
            except Exception as e:
                if retries < self.policy.max_retries:
                    retries += 1
                    await asyncio.sleep(self.policy.retry_delay)
                    continue
                
                # Create failed result
                return [BenchmarkResult(
                    benchmark_id=benchmark_config.benchmark_id,
                    benchmark_type=benchmark_config.benchmark_type,
                    start_time=datetime.now(),
                    end_time=datetime.now(),
                    duration=0.0,
                    status=BenchmarkStatus.FAILED,
                    overall_metrics=BaselineMetrics(
                        avg_response_time=0.0, p50_response_time=0.0, p95_response_time=0.0,
                        p99_response_time=0.0, max_response_time=0.0, requests_per_second=0.0,
                        concurrent_requests=0, success_rate=0.0, error_rate=100.0,
                        avg_cpu_percent=0.0, max_cpu_percent=0.0, avg_memory_percent=0.0,
                        max_memory_percent=0.0
                    ),
                    recommendations=[f"Performance test failed: {str(e)}"]
                )]
    
    async def _analyze_results(self, benchmark_results: List[BenchmarkResult], start_time: datetime) -> GateResult:
        """Analyze benchmark results and determine gate status"""
        
        end_time = datetime.now()
        
        if not benchmark_results:
            return GateResult(
                gate_id=self.policy.gate_id,
                gate_name=self.policy.name,
                start_time=start_time,
                end_time=end_time,
                duration=(end_time - start_time).total_seconds(),
                status=GateStatus.ERROR,
                action_taken=self.policy.action,
                current_metrics=BaselineMetrics(
                    avg_response_time=0.0, p50_response_time=0.0, p95_response_time=0.0,
                    p99_response_time=0.0, max_response_time=0.0, requests_per_second=0.0,
                    concurrent_requests=0, success_rate=0.0, error_rate=100.0,
                    avg_cpu_percent=0.0, max_cpu_percent=0.0, avg_memory_percent=0.0,
                    max_memory_percent=0.0
                ),
                messages=["No benchmark results available"],
                recommendations=["Check benchmark configuration and service health"]
            )
        
        # Use the first (and typically only) benchmark result
        benchmark_result = benchmark_results[0]
        current_metrics = benchmark_result.overall_metrics
        
        # Check absolute thresholds
        threshold_violations = []
        status = GateStatus.PASSED
        
        # Response time check
        if current_metrics.avg_response_time > self.policy.max_response_time_ms / 1000.0:
            threshold_violations.append(
                f"Response time ({current_metrics.avg_response_time:.2f}s) exceeds threshold ({self.policy.max_response_time_ms/1000.0:.2f}s)"
            )
            status = GateStatus.FAILED
        
        # Throughput check
        if current_metrics.requests_per_second < self.policy.min_throughput_rps:
            threshold_violations.append(
                f"Throughput ({current_metrics.requests_per_second:.1f} rps) below threshold ({self.policy.min_throughput_rps:.1f} rps)"
            )
            status = GateStatus.FAILED
        
        # Error rate check
        if current_metrics.error_rate > self.policy.max_error_rate_percent:
            threshold_violations.append(
                f"Error rate ({current_metrics.error_rate:.1f}%) exceeds threshold ({self.policy.max_error_rate_percent:.1f}%)"
            )
            status = GateStatus.FAILED
        
        # CPU usage check
        if current_metrics.avg_cpu_percent > self.policy.max_cpu_percent:
            threshold_violations.append(
                f"CPU usage ({current_metrics.avg_cpu_percent:.1f}%) exceeds threshold ({self.policy.max_cpu_percent:.1f}%)"
            )
            status = GateStatus.FAILED
        
        # Memory usage check
        if current_metrics.avg_memory_percent > self.policy.max_memory_percent:
            threshold_violations.append(
                f"Memory usage ({current_metrics.avg_memory_percent:.1f}%) exceeds threshold ({self.policy.max_memory_percent:.1f}%)"
            )
            status = GateStatus.FAILED
        
        # Regression analysis if baseline exists
        baseline_metrics = None
        regression_result = None
        
        if self.policy.baseline_id:
            try:
                baseline = self.baseline_manager.get_baseline(self.policy.baseline_id)
                if baseline:
                    baseline_metrics = baseline.metrics
                    
                    # Check regression thresholds
                    response_time_change = self._calculate_percentage_change(
                        current_metrics.avg_response_time,
                        baseline_metrics.avg_response_time
                    )
                    
                    throughput_change = self._calculate_percentage_change(
                        current_metrics.requests_per_second,
                        baseline_metrics.requests_per_second
                    )
                    
                    error_rate_change = self._calculate_percentage_change(
                        current_metrics.error_rate,
                        baseline_metrics.error_rate
                    )
                    
                    # Check regression thresholds
                    if response_time_change > self.policy.max_response_time_regression:
                        threshold_violations.append(
                            f"Response time regression ({response_time_change:.1f}%) exceeds threshold ({self.policy.max_response_time_regression:.1f}%)"
                        )
                        status = GateStatus.FAILED
                    
                    if throughput_change < -self.policy.max_throughput_regression:
                        threshold_violations.append(
                            f"Throughput regression ({abs(throughput_change):.1f}%) exceeds threshold ({self.policy.max_throughput_regression:.1f}%)"
                        )
                        status = GateStatus.FAILED
                    
                    if error_rate_change > self.policy.max_error_rate_regression:
                        threshold_violations.append(
                            f"Error rate regression ({error_rate_change:.1f}%) exceeds threshold ({self.policy.max_error_rate_regression:.1f}%)"
                        )
                        status = GateStatus.FAILED
                    
                    # Run detailed regression analysis if available
                    if benchmark_result.regression_result:
                        regression_result = benchmark_result.regression_result
                        
                        # Check confidence score
                        if regression_result.confidence_score < self.policy.min_confidence_score:
                            threshold_violations.append(
                                f"Confidence score ({regression_result.confidence_score:.2f}) below threshold ({self.policy.min_confidence_score:.2f})"
                            )
                            status = GateStatus.WARNING
                        
                        # Check statistical significance
                        if regression_result.p_value > self.policy.min_statistical_significance:
                            threshold_violations.append(
                                f"Statistical significance ({regression_result.p_value:.3f}) below threshold ({self.policy.min_statistical_significance:.3f})"
                            )
                            status = GateStatus.WARNING
                        
                        # Check regression severity
                        if regression_result.severity in [RegressionSeverity.SEVERE, RegressionSeverity.CRITICAL]:
                            threshold_violations.append(
                                f"Severe regression detected: {regression_result.severity.value}"
                            )
                            status = GateStatus.FAILED
                        elif regression_result.severity == RegressionSeverity.MODERATE:
                            threshold_violations.append(
                                f"Moderate regression detected: {regression_result.severity.value}"
                            )
                            if status == GateStatus.PASSED:
                                status = GateStatus.WARNING
                    
            except Exception as e:
                threshold_violations.append(f"Baseline comparison failed: {str(e)}")
                status = GateStatus.WARNING
        
        # Generate messages
        messages = []
        if status == GateStatus.PASSED:
            messages.append("All performance thresholds met")
        elif status == GateStatus.WARNING:
            messages.append("Performance within limits but with warnings")
        elif status == GateStatus.FAILED:
            messages.append("Performance thresholds violated")
        
        # Generate recommendations
        recommendations = benchmark_result.recommendations.copy()
        
        if threshold_violations:
            if current_metrics.avg_response_time > self.policy.max_response_time_ms / 1000.0:
                recommendations.append("Optimize response time through caching or query optimization")
            
            if current_metrics.requests_per_second < self.policy.min_throughput_rps:
                recommendations.append("Increase throughput through scaling or performance optimization")
            
            if current_metrics.error_rate > self.policy.max_error_rate_percent:
                recommendations.append("Investigate and fix error sources")
        
        # Determine action based on policy
        action_taken = self.policy.action
        if status == GateStatus.WARNING and self.policy.action == GateAction.BLOCK:
            action_taken = GateAction.WARN
        
        return GateResult(
            gate_id=self.policy.gate_id,
            gate_name=self.policy.name,
            start_time=start_time,
            end_time=end_time,
            duration=(end_time - start_time).total_seconds(),
            status=status,
            action_taken=action_taken,
            current_metrics=current_metrics,
            baseline_metrics=baseline_metrics,
            regression_result=regression_result,
            benchmark_results=benchmark_results,
            threshold_violations=threshold_violations,
            messages=messages,
            recommendations=recommendations,
            raw_data={
                "policy": self.policy.to_dict(),
                "benchmark_results": [r.to_dict() for r in benchmark_results]
            }
        )
    
    def _calculate_percentage_change(self, current: float, baseline: float) -> float:
        """Calculate percentage change from baseline"""
        if baseline == 0:
            return 0.0
        return ((current - baseline) / baseline) * 100


class CIPipelineIntegration:
    """CI/CD pipeline integration for performance gates"""
    
    def __init__(self, baseline_manager: BaselineManager, gates_config_path: str = "performance_gates.json"):
        self.baseline_manager = baseline_manager
        self.gates_config_path = gates_config_path
        self.gates = {}
        self.results = []
        
        self._load_gates_config()
    
    def _load_gates_config(self):
        """Load gates configuration from file"""
        
        config_path = Path(self.gates_config_path)
        if not config_path.exists():
            # Create default configuration
            default_config = {
                "gates": [
                    {
                        "gate_id": "performance_regression_gate",
                        "name": "Performance Regression Gate",
                        "description": "Detects performance regressions in API response times",
                        "max_response_time_ms": 2000,
                        "min_throughput_rps": 10.0,
                        "max_error_rate_percent": 1.0,
                        "max_response_time_regression": 20.0,
                        "max_throughput_regression": 15.0,
                        "max_error_rate_regression": 0.5,
                        "action": "block",
                        "baseline_id": "main_branch_baseline"
                    },
                    {
                        "gate_id": "resource_usage_gate",
                        "name": "Resource Usage Gate",
                        "description": "Monitors CPU and memory usage during load testing",
                        "max_cpu_percent": 80.0,
                        "max_memory_percent": 80.0,
                        "test_duration": 60.0,
                        "concurrent_users": 10,
                        "action": "warn",
                        "baseline_id": "resource_baseline"
                    }
                ]
            }
            
            with open(config_path, 'w') as f:
                json.dump(default_config, f, indent=2)
        
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            for gate_config in config.get("gates", []):
                policy = GatePolicy(
                    gate_id=gate_config["gate_id"],
                    name=gate_config["name"],
                    description=gate_config["description"],
                    max_response_time_ms=gate_config.get("max_response_time_ms", 2000),
                    min_throughput_rps=gate_config.get("min_throughput_rps", 10.0),
                    max_error_rate_percent=gate_config.get("max_error_rate_percent", 1.0),
                    max_cpu_percent=gate_config.get("max_cpu_percent", 80.0),
                    max_memory_percent=gate_config.get("max_memory_percent", 80.0),
                    max_response_time_regression=gate_config.get("max_response_time_regression", 20.0),
                    max_throughput_regression=gate_config.get("max_throughput_regression", 15.0),
                    max_error_rate_regression=gate_config.get("max_error_rate_regression", 0.5),
                    min_confidence_score=gate_config.get("min_confidence_score", 0.8),
                    min_statistical_significance=gate_config.get("min_statistical_significance", 0.05),
                    test_duration=gate_config.get("test_duration", 60.0),
                    concurrent_users=gate_config.get("concurrent_users", 10),
                    requests_per_user=gate_config.get("requests_per_user", 5),
                    action=GateAction(gate_config.get("action", "block")),
                    baseline_id=gate_config.get("baseline_id"),
                    max_retries=gate_config.get("max_retries", 2),
                    retry_delay=gate_config.get("retry_delay", 30.0)
                )
                
                self.gates[policy.gate_id] = PerformanceGate(policy, self.baseline_manager)
                
        except Exception as e:
            print(f"Error loading gates configuration: {e}")
    
    async def run_all_gates(self, service_url: str, auth_headers: Dict[str, str]) -> List[GateResult]:
        """Run all configured performance gates"""
        
        results = []
        
        for gate_id, gate in self.gates.items():
            try:
                print(f"Running performance gate: {gate_id}")
                result = await gate.execute(service_url, auth_headers)
                results.append(result)
                
                # Log result
                self._log_gate_result(result)
                
            except Exception as e:
                print(f"Error running gate {gate_id}: {e}")
                
                # Create error result
                error_result = GateResult(
                    gate_id=gate_id,
                    gate_name=gate.policy.name,
                    start_time=datetime.now(),
                    end_time=datetime.now(),
                    duration=0.0,
                    status=GateStatus.ERROR,
                    action_taken=gate.policy.action,
                    current_metrics=BaselineMetrics(
                        avg_response_time=0.0, p50_response_time=0.0, p95_response_time=0.0,
                        p99_response_time=0.0, max_response_time=0.0, requests_per_second=0.0,
                        concurrent_requests=0, success_rate=0.0, error_rate=100.0,
                        avg_cpu_percent=0.0, max_cpu_percent=0.0, avg_memory_percent=0.0,
                        max_memory_percent=0.0
                    ),
                    messages=[f"Gate execution error: {str(e)}"]
                )
                results.append(error_result)
        
        self.results = results
        return results
    
    def _log_gate_result(self, result: GateResult):
        """Log gate result to console"""
        
        status_symbol = {
            GateStatus.PASSED: "✅",
            GateStatus.WARNING: "⚠️",
            GateStatus.FAILED: "❌",
            GateStatus.ERROR: "💥",
            GateStatus.SKIPPED: "⏭️"
        }
        
        print(f"{status_symbol[result.status]} {result.gate_name}: {result.status.value.upper()}")
        
        if result.messages:
            for message in result.messages:
                print(f"  📝 {message}")
        
        if result.threshold_violations:
            print("  🚨 Threshold violations:")
            for violation in result.threshold_violations:
                print(f"    - {violation}")
        
        if result.recommendations:
            print("  💡 Recommendations:")
            for rec in result.recommendations:
                print(f"    - {rec}")
        
        print(f"  ⏱️ Duration: {result.duration:.1f}s")
        print()
    
    def should_block_deployment(self) -> bool:
        """Check if deployment should be blocked based on gate results"""
        
        blocking_failures = [
            result for result in self.results
            if result.status == GateStatus.FAILED and result.action_taken == GateAction.BLOCK
        ]
        
        return len(blocking_failures) > 0
    
    def get_blocking_failures(self) -> List[GateResult]:
        """Get gate results that would block deployment"""
        
        return [
            result for result in self.results
            if result.status == GateStatus.FAILED and result.action_taken == GateAction.BLOCK
        ]
    
    def get_warnings(self) -> List[GateResult]:
        """Get gate results with warnings"""
        
        return [
            result for result in self.results
            if result.status == GateStatus.WARNING or 
               (result.status == GateStatus.FAILED and result.action_taken == GateAction.WARN)
        ]
    
    def generate_summary_report(self) -> Dict[str, Any]:
        """Generate summary report of all gate results"""
        
        passed = len([r for r in self.results if r.status == GateStatus.PASSED])
        warned = len([r for r in self.results if r.status == GateStatus.WARNING])
        failed = len([r for r in self.results if r.status == GateStatus.FAILED])
        errored = len([r for r in self.results if r.status == GateStatus.ERROR])
        
        should_block = self.should_block_deployment()
        
        return {
            "summary": {
                "total_gates": len(self.results),
                "passed": passed,
                "warned": warned,
                "failed": failed,
                "errored": errored,
                "should_block_deployment": should_block
            },
            "blocking_failures": [r.to_dict() for r in self.get_blocking_failures()],
            "warnings": [r.to_dict() for r in self.get_warnings()],
            "all_results": [r.to_dict() for r in self.results],
            "generated_at": datetime.now().isoformat()
        }
    
    def save_results(self, output_path: str = "performance_gate_results.json"):
        """Save gate results to file"""
        
        report = self.generate_summary_report()
        
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"Performance gate results saved to: {output_path}")
    
    def set_github_output(self):
        """Set GitHub Actions output variables"""
        
        should_block = self.should_block_deployment()
        blocking_count = len(self.get_blocking_failures())
        warning_count = len(self.get_warnings())
        
        # Set GitHub Actions outputs
        github_output = os.getenv("GITHUB_OUTPUT")
        if github_output:
            with open(github_output, 'a') as f:
                f.write(f"performance_gate_passed={not should_block}\n")
                f.write(f"blocking_failures={blocking_count}\n")
                f.write(f"warnings={warning_count}\n")
                f.write(f"total_gates={len(self.results)}\n")
        
        # Set exit code
        if should_block:
            sys.exit(1)
        elif warning_count > 0:
            sys.exit(2)  # Warning exit code
        else:
            sys.exit(0)  # Success


async def main():
    """Main CLI entry point for performance gates"""
    
    import argparse
    
    parser = argparse.ArgumentParser(description="Run performance gates for CI/CD")
    parser.add_argument("--service-url", required=True, help="Service URL to test")
    parser.add_argument("--auth-token", help="Authentication token")
    parser.add_argument("--gates-config", default="performance_gates.json", help="Gates configuration file")
    parser.add_argument("--baseline-storage", default="performance_baselines", help="Baseline storage directory")
    parser.add_argument("--output", default="performance_gate_results.json", help="Output file for results")
    parser.add_argument("--github-output", action="store_true", help="Set GitHub Actions output variables")
    
    args = parser.parse_args()
    
    # Setup authentication headers
    auth_headers = {}
    if args.auth_token:
        auth_headers["Authorization"] = f"Bearer {args.auth_token}"
    else:
        # Try to get from environment
        token = os.getenv("API_TOKEN") or os.getenv("GITHUB_TOKEN")
        if token:
            auth_headers["Authorization"] = f"Bearer {token}"
    
    # Initialize baseline manager
    baseline_manager = BaselineManager(args.baseline_storage)
    
    # Initialize CI pipeline integration
    ci_integration = CIPipelineIntegration(baseline_manager, args.gates_config)
    
    # Run all gates
    results = await ci_integration.run_all_gates(args.service_url, auth_headers)
    
    # Save results
    ci_integration.save_results(args.output)
    
    # Print summary
    summary = ci_integration.generate_summary_report()
    print("\n" + "="*60)
    print("PERFORMANCE GATES SUMMARY")
    print("="*60)
    print(f"Total gates: {summary['summary']['total_gates']}")
    print(f"Passed: {summary['summary']['passed']}")
    print(f"Warnings: {summary['summary']['warned']}")
    print(f"Failed: {summary['summary']['failed']}")
    print(f"Errors: {summary['summary']['errored']}")
    print(f"Should block deployment: {summary['summary']['should_block_deployment']}")
    print("="*60)
    
    # Set GitHub Actions output if requested
    if args.github_output:
        ci_integration.set_github_output()


if __name__ == "__main__":
    asyncio.run(main())