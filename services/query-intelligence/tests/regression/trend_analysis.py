"""
Performance Trend Analysis

Tracks performance trends over time, predicts future performance issues,
and provides early warning alerts for performance degradation.
"""

import time
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import statistics
import numpy as np
from scipy import stats
from pathlib import Path

from .performance_baselines import BaselineManager, BaselineMetrics, PerformanceBaseline


class TrendDirection(Enum):
    """Performance trend direction"""
    IMPROVING = "improving"
    STABLE = "stable"
    DEGRADING = "degrading"
    VOLATILE = "volatile"


class AlertSeverity(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"


@dataclass
class PerformanceTrend:
    """Performance trend data"""
    
    metric_name: str
    direction: TrendDirection
    slope: float  # Rate of change
    confidence: float  # Confidence in trend (0-1)
    
    # Statistical data
    correlation_coefficient: float
    p_value: float
    
    # Trend values
    current_value: float
    predicted_value: float
    baseline_value: float
    
    # Time range
    start_time: datetime
    end_time: datetime
    data_points: int
    
    # Trend analysis
    trend_strength: str  # "weak", "moderate", "strong"
    seasonal_pattern: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "metric_name": self.metric_name,
            "direction": self.direction.value,
            "slope": self.slope,
            "confidence": self.confidence,
            "correlation_coefficient": self.correlation_coefficient,
            "p_value": self.p_value,
            "current_value": self.current_value,
            "predicted_value": self.predicted_value,
            "baseline_value": self.baseline_value,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat(),
            "data_points": self.data_points,
            "trend_strength": self.trend_strength,
            "seasonal_pattern": self.seasonal_pattern
        }


@dataclass
class TrendAlert:
    """Performance trend alert"""
    
    alert_id: str
    metric_name: str
    severity: AlertSeverity
    
    # Alert details
    title: str
    description: str
    recommendation: str
    
    # Trend data
    trend: PerformanceTrend
    threshold_value: float
    current_value: float
    
    # Timing
    created_at: datetime
    predicted_breach_time: Optional[datetime] = None
    
    # Alert metadata
    tags: List[str] = field(default_factory=list)
    acknowledged: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "alert_id": self.alert_id,
            "metric_name": self.metric_name,
            "severity": self.severity.value,
            "title": self.title,
            "description": self.description,
            "recommendation": self.recommendation,
            "trend": self.trend.to_dict(),
            "threshold_value": self.threshold_value,
            "current_value": self.current_value,
            "created_at": self.created_at.isoformat(),
            "predicted_breach_time": self.predicted_breach_time.isoformat() if self.predicted_breach_time else None,
            "tags": self.tags,
            "acknowledged": self.acknowledged
        }


class TrendAnalyzer:
    """Performance trend analyzer"""
    
    def __init__(self, baseline_manager: BaselineManager):
        self.baseline_manager = baseline_manager
        self.trend_storage = Path("performance_trends")
        self.trend_storage.mkdir(parents=True, exist_ok=True)
        
        # Trend analysis configuration
        self.min_data_points = 5
        self.trend_window_days = 30
        self.confidence_threshold = 0.7
        self.significance_threshold = 0.05
    
    def analyze_metric_trend(
        self,
        metric_name: str,
        values: List[float],
        timestamps: List[datetime],
        baseline_value: Optional[float] = None
    ) -> PerformanceTrend:
        """Analyze trend for a specific metric"""
        
        if len(values) < self.min_data_points:
            raise ValueError(f"Need at least {self.min_data_points} data points for trend analysis")
        
        # Convert timestamps to numerical values (days since first timestamp)
        time_values = [(ts - timestamps[0]).total_seconds() / 86400 for ts in timestamps]
        
        # Calculate linear regression
        slope, intercept, correlation_coeff, p_value, std_err = stats.linregress(time_values, values)
        
        # Determine trend direction
        if abs(correlation_coeff) < 0.1:
            direction = TrendDirection.STABLE
        elif correlation_coeff > 0.1:
            if metric_name in ["response_time", "error_rate", "cpu_usage", "memory_usage"]:
                direction = TrendDirection.DEGRADING  # Higher is worse
            else:
                direction = TrendDirection.IMPROVING  # Higher is better
        else:
            if metric_name in ["response_time", "error_rate", "cpu_usage", "memory_usage"]:
                direction = TrendDirection.IMPROVING  # Lower is better
            else:
                direction = TrendDirection.DEGRADING  # Lower is worse
        
        # Calculate confidence based on correlation and significance
        confidence = abs(correlation_coeff) * (1 - p_value)
        
        # Determine trend strength
        if abs(correlation_coeff) > 0.7:
            trend_strength = "strong"
        elif abs(correlation_coeff) > 0.4:
            trend_strength = "moderate"
        else:
            trend_strength = "weak"
        
        # Predict future value (next time point)
        next_time = time_values[-1] + 1  # Next day
        predicted_value = slope * next_time + intercept
        
        # Check for volatility
        if self._is_volatile(values):
            direction = TrendDirection.VOLATILE
        
        # Detect seasonal patterns
        seasonal_pattern = self._detect_seasonal_pattern(values, timestamps)
        
        return PerformanceTrend(
            metric_name=metric_name,
            direction=direction,
            slope=slope,
            confidence=confidence,
            correlation_coefficient=correlation_coeff,
            p_value=p_value,
            current_value=values[-1],
            predicted_value=predicted_value,
            baseline_value=baseline_value or statistics.mean(values),
            start_time=timestamps[0],
            end_time=timestamps[-1],
            data_points=len(values),
            trend_strength=trend_strength,
            seasonal_pattern=seasonal_pattern
        )
    
    def _is_volatile(self, values: List[float]) -> bool:
        """Check if values show high volatility"""
        if len(values) < 3:
            return False
        
        # Calculate coefficient of variation
        mean_val = statistics.mean(values)
        if mean_val == 0:
            return False
        
        std_val = statistics.stdev(values)
        cv = std_val / mean_val
        
        # Consider volatile if CV > 0.5
        return cv > 0.5
    
    def _detect_seasonal_pattern(self, values: List[float], timestamps: List[datetime]) -> bool:
        """Detect seasonal patterns in the data"""
        if len(values) < 14:  # Need at least 2 weeks of data
            return False
        
        # Check for weekly patterns (simplified)
        weekly_averages = []
        for i in range(0, len(values), 7):
            week_values = values[i:i+7]
            if len(week_values) >= 3:
                weekly_averages.append(statistics.mean(week_values))
        
        if len(weekly_averages) < 2:
            return False
        
        # Check if weekly averages show pattern
        week_std = statistics.stdev(weekly_averages)
        overall_std = statistics.stdev(values)
        
        # If weekly variation is much lower than overall, might be seasonal
        return week_std < overall_std * 0.5
    
    def analyze_baseline_trends(self, baseline_id: str, days: int = 30) -> Dict[str, PerformanceTrend]:
        """Analyze trends for all metrics in a baseline over time"""
        
        # Get baseline history (this would need to be implemented in BaselineManager)
        baseline_history = self._get_baseline_history(baseline_id, days)
        
        if not baseline_history:
            return {}
        
        # Extract metrics over time
        metrics_data = {
            "response_time": [],
            "throughput": [],
            "error_rate": [],
            "cpu_usage": [],
            "memory_usage": []
        }
        
        timestamps = []
        
        for entry in baseline_history:
            timestamps.append(entry["timestamp"])
            metrics = entry["metrics"]
            
            metrics_data["response_time"].append(metrics.get("avg_response_time", 0))
            metrics_data["throughput"].append(metrics.get("requests_per_second", 0))
            metrics_data["error_rate"].append(metrics.get("error_rate", 0))
            metrics_data["cpu_usage"].append(metrics.get("avg_cpu_percent", 0))
            metrics_data["memory_usage"].append(metrics.get("avg_memory_percent", 0))
        
        # Analyze trends for each metric
        trends = {}
        
        for metric_name, values in metrics_data.items():
            if len(values) >= self.min_data_points:
                try:
                    # Get baseline value
                    baseline = self.baseline_manager.get_baseline(baseline_id)
                    baseline_value = None
                    if baseline:
                        baseline_value = getattr(baseline.metrics, f"avg_{metric_name.replace('_', '_')}", 0)
                    
                    trend = self.analyze_metric_trend(
                        metric_name, values, timestamps, baseline_value
                    )
                    trends[metric_name] = trend
                    
                except Exception as e:
                    print(f"Error analyzing trend for {metric_name}: {e}")
        
        return trends
    
    def _get_baseline_history(self, baseline_id: str, days: int) -> List[Dict[str, Any]]:
        """Get baseline history (mock implementation)"""
        
        # This would need to be implemented in BaselineManager
        # For now, return empty list
        # In real implementation, this would query stored baseline data over time
        
        return []
    
    def generate_trend_alerts(self, trends: Dict[str, PerformanceTrend]) -> List[TrendAlert]:
        """Generate alerts based on trend analysis"""
        
        alerts = []
        
        # Define alert thresholds
        alert_thresholds = {
            "response_time": {
                "warning": 2.0,  # 2 seconds
                "critical": 5.0   # 5 seconds
            },
            "throughput": {
                "warning": 5.0,   # 5 RPS
                "critical": 1.0   # 1 RPS
            },
            "error_rate": {
                "warning": 1.0,   # 1%
                "critical": 5.0   # 5%
            },
            "cpu_usage": {
                "warning": 80.0,  # 80%
                "critical": 90.0  # 90%
            },
            "memory_usage": {
                "warning": 80.0,  # 80%
                "critical": 90.0  # 90%
            }
        }
        
        for metric_name, trend in trends.items():
            thresholds = alert_thresholds.get(metric_name, {})
            
            # Check if trend is concerning
            if trend.direction == TrendDirection.DEGRADING and trend.confidence > self.confidence_threshold:
                
                # Determine severity
                severity = AlertSeverity.INFO
                threshold_value = 0
                
                if metric_name in ["response_time", "error_rate", "cpu_usage", "memory_usage"]:
                    # Higher is worse
                    if trend.predicted_value > thresholds.get("critical", float('inf')):
                        severity = AlertSeverity.CRITICAL
                        threshold_value = thresholds.get("critical", 0)
                    elif trend.predicted_value > thresholds.get("warning", float('inf')):
                        severity = AlertSeverity.WARNING
                        threshold_value = thresholds.get("warning", 0)
                
                elif metric_name == "throughput":
                    # Lower is worse
                    if trend.predicted_value < thresholds.get("critical", 0):
                        severity = AlertSeverity.CRITICAL
                        threshold_value = thresholds.get("critical", 0)
                    elif trend.predicted_value < thresholds.get("warning", 0):
                        severity = AlertSeverity.WARNING
                        threshold_value = thresholds.get("warning", 0)
                
                if severity != AlertSeverity.INFO:
                    # Calculate predicted breach time
                    predicted_breach_time = self._calculate_breach_time(trend, threshold_value)
                    
                    # Generate alert
                    alert = TrendAlert(
                        alert_id=f"trend_alert_{metric_name}_{int(time.time())}",
                        metric_name=metric_name,
                        severity=severity,
                        title=f"Performance Degradation Trend: {metric_name.title()}",
                        description=f"{metric_name.title()} is showing a {trend.direction.value} trend with {trend.confidence:.1%} confidence. "
                                   f"Current value: {trend.current_value:.2f}, Predicted: {trend.predicted_value:.2f}",
                        recommendation=self._generate_trend_recommendation(metric_name, trend),
                        trend=trend,
                        threshold_value=threshold_value,
                        current_value=trend.current_value,
                        created_at=datetime.now(),
                        predicted_breach_time=predicted_breach_time,
                        tags=["performance", "trend", metric_name]
                    )
                    
                    alerts.append(alert)
            
            # Check for volatility
            elif trend.direction == TrendDirection.VOLATILE:
                alert = TrendAlert(
                    alert_id=f"volatility_alert_{metric_name}_{int(time.time())}",
                    metric_name=metric_name,
                    severity=AlertSeverity.WARNING,
                    title=f"High Volatility Detected: {metric_name.title()}",
                    description=f"{metric_name.title()} is showing high volatility which may indicate instability.",
                    recommendation=f"Investigate causes of {metric_name} volatility and implement stabilization measures.",
                    trend=trend,
                    threshold_value=0,
                    current_value=trend.current_value,
                    created_at=datetime.now(),
                    tags=["performance", "volatility", metric_name]
                )
                
                alerts.append(alert)
        
        return alerts
    
    def _calculate_breach_time(self, trend: PerformanceTrend, threshold_value: float) -> Optional[datetime]:
        """Calculate when threshold will be breached based on trend"""
        
        if trend.slope == 0:
            return None
        
        # Calculate days until breach
        days_to_breach = (threshold_value - trend.current_value) / trend.slope
        
        if days_to_breach <= 0:
            return None  # Already breached or won't breach
        
        return datetime.now() + timedelta(days=days_to_breach)
    
    def _generate_trend_recommendation(self, metric_name: str, trend: PerformanceTrend) -> str:
        """Generate recommendation based on trend"""
        
        recommendations = {
            "response_time": "Consider optimizing query processing, implementing caching, or scaling resources.",
            "throughput": "Review resource allocation, optimize bottlenecks, or implement load balancing.",
            "error_rate": "Investigate error sources, improve error handling, and monitor system health.",
            "cpu_usage": "Optimize CPU-intensive operations, implement caching, or scale compute resources.",
            "memory_usage": "Review memory usage patterns, implement memory optimization, or scale memory resources."
        }
        
        return recommendations.get(metric_name, "Monitor the trend and investigate root causes.")
    
    def save_trends(self, trends: Dict[str, PerformanceTrend], baseline_id: str):
        """Save trend analysis results"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"trends_{baseline_id}_{timestamp}.json"
        filepath = self.trend_storage / filename
        
        data = {
            "baseline_id": baseline_id,
            "analyzed_at": datetime.now().isoformat(),
            "trends": {k: v.to_dict() for k, v in trends.items()}
        }
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
    
    def save_alerts(self, alerts: List[TrendAlert], baseline_id: str):
        """Save trend alerts"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"alerts_{baseline_id}_{timestamp}.json"
        filepath = self.trend_storage / filename
        
        data = {
            "baseline_id": baseline_id,
            "generated_at": datetime.now().isoformat(),
            "alerts": [alert.to_dict() for alert in alerts]
        }
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
    
    def get_trend_history(self, baseline_id: str, days: int = 30) -> List[Dict[str, Any]]:
        """Get trend analysis history"""
        
        history = []
        
        for filepath in self.trend_storage.glob(f"trends_{baseline_id}_*.json"):
            try:
                with open(filepath, 'r') as f:
                    data = json.load(f)
                
                analyzed_at = datetime.fromisoformat(data["analyzed_at"])
                if (datetime.now() - analyzed_at).days <= days:
                    history.append(data)
                    
            except Exception:
                continue
        
        return sorted(history, key=lambda x: x["analyzed_at"], reverse=True)
    
    def get_active_alerts(self, baseline_id: str) -> List[TrendAlert]:
        """Get active alerts for a baseline"""
        
        alerts = []
        
        for filepath in self.trend_storage.glob(f"alerts_{baseline_id}_*.json"):
            try:
                with open(filepath, 'r') as f:
                    data = json.load(f)
                
                # Only include recent alerts (last 7 days)
                generated_at = datetime.fromisoformat(data["generated_at"])
                if (datetime.now() - generated_at).days <= 7:
                    for alert_data in data["alerts"]:
                        if not alert_data.get("acknowledged", False):
                            alert = TrendAlert(
                                alert_id=alert_data["alert_id"],
                                metric_name=alert_data["metric_name"],
                                severity=AlertSeverity(alert_data["severity"]),
                                title=alert_data["title"],
                                description=alert_data["description"],
                                recommendation=alert_data["recommendation"],
                                trend=PerformanceTrend(
                                    metric_name=alert_data["trend"]["metric_name"],
                                    direction=TrendDirection(alert_data["trend"]["direction"]),
                                    slope=alert_data["trend"]["slope"],
                                    confidence=alert_data["trend"]["confidence"],
                                    correlation_coefficient=alert_data["trend"]["correlation_coefficient"],
                                    p_value=alert_data["trend"]["p_value"],
                                    current_value=alert_data["trend"]["current_value"],
                                    predicted_value=alert_data["trend"]["predicted_value"],
                                    baseline_value=alert_data["trend"]["baseline_value"],
                                    start_time=datetime.fromisoformat(alert_data["trend"]["start_time"]),
                                    end_time=datetime.fromisoformat(alert_data["trend"]["end_time"]),
                                    data_points=alert_data["trend"]["data_points"],
                                    trend_strength=alert_data["trend"]["trend_strength"],
                                    seasonal_pattern=alert_data["trend"]["seasonal_pattern"]
                                ),
                                threshold_value=alert_data["threshold_value"],
                                current_value=alert_data["current_value"],
                                created_at=datetime.fromisoformat(alert_data["created_at"]),
                                predicted_breach_time=datetime.fromisoformat(alert_data["predicted_breach_time"]) if alert_data["predicted_breach_time"] else None,
                                tags=alert_data["tags"],
                                acknowledged=alert_data["acknowledged"]
                            )
                            alerts.append(alert)
                    
            except Exception:
                continue
        
        return alerts


class TrendPredictor:
    """Advanced trend prediction using machine learning techniques"""
    
    def __init__(self):
        self.prediction_horizon_days = 7
        self.min_training_data = 14
    
    def predict_future_values(
        self,
        values: List[float],
        timestamps: List[datetime],
        horizon_days: int = 7
    ) -> List[Tuple[datetime, float]]:
        """Predict future values using linear regression"""
        
        if len(values) < self.min_training_data:
            return []
        
        # Convert to numerical time series
        time_values = [(ts - timestamps[0]).total_seconds() / 86400 for ts in timestamps]
        
        # Fit linear regression
        slope, intercept, _, _, _ = stats.linregress(time_values, values)
        
        # Generate predictions
        predictions = []
        last_time = time_values[-1]
        
        for day in range(1, horizon_days + 1):
            future_time = last_time + day
            predicted_value = slope * future_time + intercept
            predicted_date = timestamps[-1] + timedelta(days=day)
            
            predictions.append((predicted_date, predicted_value))
        
        return predictions
    
    def calculate_prediction_confidence(
        self,
        values: List[float],
        timestamps: List[datetime]
    ) -> float:
        """Calculate confidence in predictions"""
        
        if len(values) < self.min_training_data:
            return 0.0
        
        # Use correlation coefficient as confidence measure
        time_values = [(ts - timestamps[0]).total_seconds() / 86400 for ts in timestamps]
        _, _, r_value, _, _ = stats.linregress(time_values, values)
        
        return abs(r_value)
    
    def detect_anomalies(
        self,
        values: List[float],
        threshold_std: float = 2.0
    ) -> List[int]:
        """Detect anomalies using statistical methods"""
        
        if len(values) < 5:
            return []
        
        mean_val = statistics.mean(values)
        std_val = statistics.stdev(values)
        
        anomalies = []
        
        for i, value in enumerate(values):
            if abs(value - mean_val) > threshold_std * std_val:
                anomalies.append(i)
        
        return anomalies
    
    def forecast_breach_probability(
        self,
        trend: PerformanceTrend,
        threshold_value: float,
        horizon_days: int = 7
    ) -> float:
        """Calculate probability of threshold breach"""
        
        if trend.slope == 0:
            return 0.0
        
        # Days until breach at current trend
        days_to_breach = (threshold_value - trend.current_value) / trend.slope
        
        if days_to_breach <= 0:
            return 1.0  # Already breached
        
        if days_to_breach > horizon_days:
            return 0.0  # Won't breach in forecast horizon
        
        # Calculate probability based on trend confidence
        breach_probability = trend.confidence * (1 - days_to_breach / horizon_days)
        
        return min(1.0, max(0.0, breach_probability))