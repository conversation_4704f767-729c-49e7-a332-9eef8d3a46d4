import pytest
from unittest.mock import AsyncMock, patch
from query_intelligence.clients.analysis_engine import AnalysisEngineClient


@pytest.mark.asyncio
async def test_get_analysis():
    with patch("httpx.AsyncClient") as mock_client:
        # Create a proper mock response
        mock_response = AsyncMock()
        mock_response.status_code = 200
        # json() is not async in httpx, so use regular Mock
        mock_response.json = lambda: {"files": []}
        mock_response.raise_for_status = lambda: None

        # Configure the mock client
        mock_client_instance = mock_client.return_value
        mock_client_instance.get = AsyncMock(return_value=mock_response)

        client = AnalysisEngineClient()
        response = await client.get_analysis("test_repo")

        assert response == {"files": []}
        mock_client_instance.get.assert_called_once_with("/analysis/test_repo")
