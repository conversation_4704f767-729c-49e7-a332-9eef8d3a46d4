"""
Unit tests for FallbackHandler service
"""
import pytest
from unittest.mock import Mock, patch

from query_intelligence.services.fallback_handler import FallbackHandler
from query_intelligence.models import (
    CodeChunk,
    IntentAnalysis,
    QueryIntent,
    QueryContext,
    SearchResult,
    GeneratedResponse,
)


class TestFallbackHandler:
    """Test cases for FallbackHandler service"""

    def setup_method(self):
        """Set up test fixtures"""
        self.handler = FallbackHandler()

    @pytest.mark.asyncio
    async def test_handle_llm_fallback_explain_intent(self):
        """Test LLM fallback with EXPLAIN intent"""
        query = "How does authentication work?"
        
        intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN,
            code_elements=["auth", "authentication"],
            scope="repository",
            context_depth="normal",
            confidence=0.8
        )
        
        code_chunks = [
            CodeChunk(
                file_path="src/auth.py",
                start_line=10,
                end_line=20,
                content="def authenticate(user, password):\n    return verify_credentials(user, password)",
                language="python",
                similarity_score=0.9,
                recency_score=0.8,
                combined_score=0.85
            )
        ]
        
        context = QueryContext(
            repository_id="test-repo",
            user_id="test-user",
            history=[],
            filters={}
        )
        
        error = Exception("LLM service unavailable")
        
        with patch.object(FallbackHandler, '_generate_intent_based_response') as mock_generate:
            with patch.object(FallbackHandler, '_calculate_fallback_confidence') as mock_calc:
                mock_generate.return_value = "Fallback response about authentication"
                mock_calc.return_value = 0.6
                
                result = await self.handler.handle_llm_fallback(
                    query, intent, code_chunks, context, error
                )
                
                assert isinstance(result, GeneratedResponse)
                assert result.text == "Fallback response about authentication"
                assert result.confidence == 0.6
                assert result.model_used == "fallback-enhanced"
                
                mock_generate.assert_called_once_with(query, intent, code_chunks)
                mock_calc.assert_called_once_with(intent, code_chunks, "Fallback response about authentication")

    @pytest.mark.asyncio
    async def test_handle_embedding_fallback_success(self):
        """Test embedding fallback with successful keyword extraction"""
        query = "find all authentication functions"
        context = QueryContext(
            repository_id="test-repo",
            user_id="test-user",
            history=[],
            filters={}
        )
        error = Exception("Embedding service unavailable")
        
        with patch.object(FallbackHandler, '_extract_keywords') as mock_extract:
            mock_extract.return_value = ["authentication", "functions"]
            
            result = await self.handler.handle_embedding_fallback(query, context, error)
            
            assert isinstance(result, list)
            assert len(result) == 768  # Default embedding size
            assert all(isinstance(x, float) for x in result)

    @pytest.mark.asyncio
    async def test_handle_search_fallback_success(self):
        """Test search fallback with keyword-based search"""
        query = "authentication functions"
        context = QueryContext(
            repository_id="test-repo",
            user_id="test-user",
            history=[],
            filters={}
        )
        error = Exception("Search service unavailable")
        
        with patch.object(FallbackHandler, '_simple_keyword_search') as mock_search:
            mock_chunks = [
                CodeChunk(
                    file_path="src/auth.py",
                    start_line=10,
                    end_line=20,
                    content="def authenticate(user, password):\n    return True",
                    language="python",
                    similarity_score=0.8,
                    recency_score=0.7,
                    combined_score=0.75
                )
            ]
            mock_search.return_value = mock_chunks
            
            result = await self.handler.handle_search_fallback(query, context, error)
            
            assert isinstance(result, SearchResult)
            assert len(result.chunks) == 1
            assert result.chunks[0].file_path == "src/auth.py"
            assert result.total_results == 1

    def test_generate_intent_based_response_explain(self):
        """Test generating intent-based response for EXPLAIN intent"""
        query = "How does authentication work?"
        
        intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN,
            code_elements=["auth"],
            scope="repository",
            context_depth="normal",
            confidence=0.8
        )
        
        code_chunks = [
            CodeChunk(
                file_path="src/auth.py",
                start_line=10,
                end_line=20,
                content="def authenticate(user, password):\n    return verify_credentials(user, password)",
                language="python",
                similarity_score=0.9,
                recency_score=0.8,
                combined_score=0.85
            )
        ]
        
        result = self.handler._generate_intent_based_response(query, intent, code_chunks)
        
        assert isinstance(result, str)
        assert "authentication" in result.lower()
        assert "src/auth.py" in result

    def test_generate_intent_based_response_find(self):
        """Test generating intent-based response for FIND intent"""
        query = "Find authentication functions"
        
        intent = IntentAnalysis(
            primary_intent=QueryIntent.FIND,
            code_elements=["auth"],
            scope="repository",
            context_depth="normal",
            confidence=0.8
        )
        
        code_chunks = [
            CodeChunk(
                file_path="src/auth.py",
                start_line=10,
                end_line=20,
                content="def authenticate(user, password):\n    return True",
                language="python",
                similarity_score=0.9,
                recency_score=0.8,
                combined_score=0.85
            ),
            CodeChunk(
                file_path="src/auth.py",
                start_line=30,
                end_line=40,
                content="def verify_credentials(user, password):\n    return True",
                language="python",
                similarity_score=0.8,
                recency_score=0.7,
                combined_score=0.75
            )
        ]
        
        result = self.handler._generate_intent_based_response(query, intent, code_chunks)
        
        assert isinstance(result, str)
        assert "2 relevant code sections" in result
        assert "Lines 10-20" in result
        assert "Lines 30-40" in result

    def test_generate_intent_based_response_debug(self):
        """Test generating intent-based response for DEBUG intent"""
        query = "Why isn't authentication working?"
        
        intent = IntentAnalysis(
            primary_intent=QueryIntent.DEBUG,
            code_elements=["auth"],
            scope="repository",
            context_depth="normal",
            confidence=0.8
        )
        
        code_chunks = [
            CodeChunk(
                file_path="src/auth.py",
                start_line=10,
                end_line=20,
                content="def authenticate(user, password):\n    return verify_credentials(user, password)",
                language="python",
                similarity_score=0.9,
                recency_score=0.8,
                combined_score=0.85
            )
        ]
        
        result = self.handler._generate_intent_based_response(query, intent, code_chunks)
        
        assert isinstance(result, str)
        assert "debug" in result.lower()
        assert "src/auth.py" in result

    def test_calculate_fallback_confidence_high_quality(self):
        """Test confidence calculation for high quality fallback"""
        intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN,
            code_elements=["auth"],
            scope="repository",
            context_depth="normal",
            confidence=0.9
        )
        
        code_chunks = [
            CodeChunk(
                file_path="src/auth.py",
                start_line=10,
                end_line=20,
                content="def authenticate(user, password):\n    return verify_credentials(user, password)",
                language="python",
                similarity_score=0.9,
                recency_score=0.8,
                combined_score=0.85
            )
        ]
        
        response_text = "This is a detailed explanation about authentication with specific code examples."
        
        confidence = self.handler._calculate_fallback_confidence(intent, code_chunks, response_text)
        
        assert isinstance(confidence, float)
        assert 0.0 <= confidence <= 1.0
        assert confidence > 0.4  # Should be reasonably high for fallback

    def test_calculate_fallback_confidence_low_quality(self):
        """Test confidence calculation for low quality fallback"""
        intent = IntentAnalysis(
            primary_intent=QueryIntent.UNKNOWN,
            code_elements=[],
            scope="repository",
            context_depth="normal",
            confidence=0.3
        )
        
        code_chunks = []
        response_text = "Sorry, I don't understand."
        
        confidence = self.handler._calculate_fallback_confidence(intent, code_chunks, response_text)
        
        assert isinstance(confidence, float)
        assert 0.0 <= confidence <= 1.0
        assert confidence < 0.5  # Should be low

    def test_extract_keywords_basic(self):
        """Test basic keyword extraction"""
        query = "How does authentication work in this application?"
        
        keywords = self.handler._extract_keywords(query)
        
        assert isinstance(keywords, list)
        assert "authentication" in keywords
        assert "work" in keywords
        assert "application" in keywords
        # Should filter out common words
        assert "how" not in keywords
        assert "does" not in keywords
        assert "in" not in keywords
        assert "this" not in keywords

    def test_extract_keywords_code_terms(self):
        """Test keyword extraction with code terms"""
        query = "Find all functions that handle user authentication and password validation"
        
        keywords = self.handler._extract_keywords(query)
        
        assert isinstance(keywords, list)
        assert "functions" in keywords
        assert "user" in keywords
        assert "authentication" in keywords
        assert "password" in keywords
        assert "validation" in keywords

    def test_extract_keywords_empty_query(self):
        """Test keyword extraction with empty query"""
        query = ""
        
        keywords = self.handler._extract_keywords(query)
        
        assert isinstance(keywords, list)
        assert len(keywords) == 0

    def test_extract_keywords_only_stopwords(self):
        """Test keyword extraction with only stopwords"""
        query = "how does this work"
        
        keywords = self.handler._extract_keywords(query)
        
        assert isinstance(keywords, list)
        assert "work" in keywords  # "work" should be kept
        # Other words should be filtered out
        assert "how" not in keywords
        assert "does" not in keywords
        assert "this" not in keywords

    def test_generate_fallback_embedding_consistent(self):
        """Test that fallback embedding generation is consistent"""
        keywords = ["authentication", "password", "user"]
        
        embedding1 = self.handler._generate_fallback_embedding(keywords)
        embedding2 = self.handler._generate_fallback_embedding(keywords)
        
        assert embedding1 == embedding2  # Should be deterministic
        assert len(embedding1) == 768
        assert all(isinstance(x, float) for x in embedding1)

    def test_generate_fallback_embedding_different_keywords(self):
        """Test that different keywords produce different embeddings"""
        keywords1 = ["authentication", "password"]
        keywords2 = ["database", "query"]
        
        embedding1 = self.handler._generate_fallback_embedding(keywords1)
        embedding2 = self.handler._generate_fallback_embedding(keywords2)
        
        assert embedding1 != embedding2  # Should be different
        assert len(embedding1) == len(embedding2) == 768

    def test_simple_keyword_search_found(self):
        """Test simple keyword search with results"""
        query = "authentication functions"
        context = QueryContext(
            repository_id="test-repo",
            user_id="test-user",
            history=[],
            filters={}
        )
        
        # This would need to be mocked in a real implementation
        # For now, we'll test the method exists and returns a list
        result = self.handler._simple_keyword_search(query, context)
        
        assert isinstance(result, list)
        # The actual implementation would search through cached code chunks
        # For now, we just verify the method signature works

    def test_simple_keyword_search_empty_query(self):
        """Test simple keyword search with empty query"""
        query = ""
        context = QueryContext(
            repository_id="test-repo",
            user_id="test-user",
            history=[],
            filters={}
        )
        
        result = self.handler._simple_keyword_search(query, context)
        
        assert isinstance(result, list)
        assert len(result) == 0  # Should return empty list for empty query