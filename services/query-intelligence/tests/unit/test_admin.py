import pytest
import async<PERSON>
import json
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch, MagicMock
from query_intelligence.main import app
from query_intelligence.config.settings import get_settings
from query_intelligence.api.admin import SystemMetrics, ServiceHealth, QueryStats, CacheStats

# Override settings for testing
settings = get_settings()
settings.JWT_SECRET_KEY = "test_secret_key_for_testing"
settings.ENABLE_ADMIN_API = True


@pytest.fixture
def client():
    """Test client for the FastAPI app"""
    return TestClient(app)


@pytest.fixture
def mock_redis_client():
    """Mock Redis client with comprehensive methods"""
    mock = AsyncMock()
    mock.get.return_value = "0"
    mock.scard.return_value = 0
    mock.zrevrange.return_value = []
    mock.hgetall.return_value = {}
    mock.info.return_value = {"db0": {"keys": 0}, "used_memory": 0}
    mock.ping.return_value = True
    mock.flushdb.return_value = True
    mock.set.return_value = True
    mock.delete.return_value = True
    mock.exists.return_value = True
    mock.ttl.return_value = 3600
    mock.aclose.return_value = None
    return mock


@pytest.fixture
def mock_cache_manager():
    """Mock cache manager"""
    mock = AsyncMock()
    mock._query_memory_cache = {}
    mock._embedding_memory_cache = {}
    mock._access_counts = {}
    mock._init_memory_cache = MagicMock()
    return mock

@pytest.fixture(autouse=True)
def override_dependencies(mock_redis_client, mock_cache_manager):
    """Override dependencies for all tests"""
    with patch(
        "query_intelligence.api.admin.get_redis_client",
        return_value=mock_redis_client,
    ), patch(
        "query_intelligence.api.admin.get_cache_manager",
        return_value=mock_cache_manager,
    ), patch(
        "query_intelligence.api.admin.get_circuit_breaker_status",
        return_value={"test_breaker": {"state": "closed", "failure_count": 0}},
    ), patch(
        "query_intelligence.middleware.auth.jwt.decode",
        return_value={"sub": "admin", "roles": ["admin"], "exp": **********},
    ), patch(
        "query_intelligence.middleware.rate_limit.get_redis_client",
        return_value=mock_redis_client,
    ):
        yield


def get_admin_headers():
    """Helper to get admin headers"""
    return {"Authorization": "Bearer admin_token"}

def get_non_admin_headers():
    """Helper to get non-admin headers"""
    return {"Authorization": "Bearer user_token"}

def get_invalid_headers():
    """Helper to get invalid headers"""
    return {"Authorization": "Bearer invalid_token"}


class TestAdminAPI:
    """Test suite for the Admin API"""

    def test_get_metrics_unauthorized(self, client):
        """Test that metrics endpoint requires authentication"""
        # Test with no authentication header
        response = client.get("/api/v1/admin/metrics")
        assert response.status_code == 403  # No token means no admin role

    def test_get_metrics_success(self, client, mock_redis_client):
        """Test successful retrieval of system metrics"""
        # Mock the Redis calls in the order they're made in the admin.py file
        mock_redis_client.get.side_effect = [
            "100",     # total_queries
            "80",      # cache_hits
            "20",      # cache_misses
            "12345.0", # total_response_time
            "5",       # error_count
            "10",      # qpm for minute 0
            "10",      # qpm for minute 1
            "10",      # qpm for minute 2
            "10",      # qpm for minute 3
            "10",      # qpm for minute 4
        ]
        mock_redis_client.scard.return_value = 10
        response = client.get("/api/v1/admin/metrics", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        assert data["total_queries"] == 100
        assert data["cache_hit_rate"] == 0.8

    def test_get_health_success(self, client, mock_redis_client):
        """Test successful retrieval of service health"""
        mock_redis_client.ping.return_value = True
        mock_redis_client.get.side_effect = ["healthy", "healthy", "healthy"]
        with patch(
            "query_intelligence.api.admin.get_circuit_breaker_status",
            return_value={"test_breaker": {"state": "closed"}},
        ):
            response = client.get("/api/v1/admin/health", headers=get_admin_headers())
            assert response.status_code == 200
            data = response.json()
            assert data["redis"] == "healthy"
            assert data["circuit_breakers"]["test_breaker"] == "closed"

    def test_get_query_stats_success(self, client, mock_redis_client):
        """Test successful retrieval of query statistics"""
        mock_redis_client.zrevrange.return_value = [("test query", 10)]
        mock_redis_client.hgetall.return_value = {"en": "5", "es": "5"}
        response = client.get("/api/v1/admin/queries/stats", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        assert len(data["top_queries"]) == 1
        assert data["language_distribution"]["en"] == 5

    def test_clear_cache_success(self, client):
        """Test successful cache clearing"""
        response = client.post(
            "/api/v1/admin/cache/clear?cache_type=all", headers=get_admin_headers()
        )
        assert response.status_code == 200
        assert response.json() == {"status": "success", "cleared": "all"}

    def test_clear_cache_memory_only(self, client):
        """Test clearing memory cache only"""
        response = client.post(
            "/api/v1/admin/cache/clear?cache_type=memory", headers=get_admin_headers()
        )
        assert response.status_code == 200
        assert response.json() == {"status": "success", "cleared": "memory"}

    def test_clear_cache_redis_only(self, client):
        """Test clearing Redis cache only"""
        response = client.post(
            "/api/v1/admin/cache/clear?cache_type=redis", headers=get_admin_headers()
        )
        assert response.status_code == 200
        assert response.json() == {"status": "success", "cleared": "redis"}

    def test_clear_cache_unauthorized(self, client):
        """Test cache clear requires admin authorization"""
        response = client.post("/api/v1/admin/cache/clear")
        assert response.status_code == 403

    def test_get_cache_stats_success(self, client, mock_redis_client):
        """Test successful retrieval of cache statistics"""
        mock_redis_client.get.side_effect = ["100", "150"]  # memory hits, total
        mock_redis_client.info.return_value = {
            "db0": {"keys": 50},
            "used_memory": 1048576  # 1MB in bytes
        }
        
        with patch(
            "query_intelligence.api.admin.get_cache_manager"
        ) as mock_cache_manager:
            mock_cache = mock_cache_manager.return_value
            mock_cache._query_memory_cache = {i: f"query_{i}" for i in range(10)}
            mock_cache._embedding_memory_cache = {i: f"embed_{i}" for i in range(5)}
            mock_cache._access_counts = {f"query_{i}": i for i in range(20)}
            
            response = client.get("/api/v1/admin/cache/stats", headers=get_admin_headers())
            assert response.status_code == 200
            data = response.json()
            assert data["memory_cache_size"] == 15
            assert data["redis_keys"] == 50
            assert data["redis_memory_mb"] == 1.0
            assert len(data["hot_queries"]) <= 10

    def test_get_cache_stats_unauthorized(self, client):
        """Test cache stats requires admin authorization"""
        response = client.get("/api/v1/admin/cache/stats")
        assert response.status_code == 403

    def test_reset_circuit_breaker_specific(self, client):
        """Test resetting a specific circuit breaker"""
        with patch(
            "query_intelligence.api.admin.reset_circuit_breaker"
        ) as mock_reset:
            response = client.post(
                "/api/v1/admin/circuit-breakers/reset?breaker_name=test_breaker",
                headers=get_admin_headers()
            )
            assert response.status_code == 200
            assert response.json() == {"status": "success", "reset": "test_breaker"}
            mock_reset.assert_called_once_with("test_breaker")

    def test_reset_circuit_breaker_all(self, client):
        """Test resetting all circuit breakers"""
        with patch(
            "query_intelligence.api.admin.get_circuit_breaker_status",
            return_value={"breaker1": {}, "breaker2": {}}
        ), patch(
            "query_intelligence.api.admin.reset_circuit_breaker"
        ) as mock_reset:
            response = client.post(
                "/api/v1/admin/circuit-breakers/reset",
                headers=get_admin_headers()
            )
            assert response.status_code == 200
            assert response.json() == {"status": "success", "reset": "all"}
            assert mock_reset.call_count == 2

    def test_reset_circuit_breaker_unauthorized(self, client):
        """Test circuit breaker reset requires admin authorization"""
        response = client.post("/api/v1/admin/circuit-breakers/reset")
        assert response.status_code == 403

    def test_get_configuration_success(self, client):
        """Test successful retrieval of sanitized configuration"""
        response = client.get("/api/v1/admin/config", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        # Check for expected configuration keys
        expected_keys = [
            "environment", "service_name", "port", "cache_ttl_hours",
            "rate_limit_requests", "max_query_length", "llm_model",
            "enable_metrics", "circuit_breaker_config"
        ]
        for key in expected_keys:
            assert key in data
        # Ensure no sensitive data is exposed
        assert "jwt_secret" not in str(data).lower()
        assert "api_key" not in str(data).lower()

    def test_get_configuration_unauthorized(self, client):
        """Test configuration endpoint requires admin authorization"""
        response = client.get("/api/v1/admin/config")
        assert response.status_code == 403

    def test_metrics_error_handling(self, client, mock_redis_client):
        """Test metrics endpoint handles Redis errors gracefully"""
        mock_redis_client.get.side_effect = Exception("Redis connection failed")
        
        response = client.get("/api/v1/admin/metrics", headers=get_admin_headers())
        assert response.status_code == 500
        assert "Failed to retrieve metrics" in response.json()["detail"]

    def test_health_redis_down(self, client, mock_redis_client):
        """Test health check when Redis is down"""
        mock_redis_client.ping.side_effect = Exception("Connection failed")
        mock_redis_client.get.side_effect = ["healthy", "healthy", "healthy"]
        
        with patch(
            "query_intelligence.api.admin.get_circuit_breaker_status",
            return_value={}
        ):
            response = client.get("/api/v1/admin/health", headers=get_admin_headers())
            assert response.status_code == 200
            data = response.json()
            assert data["redis"] == "unhealthy"

    def test_query_stats_error_handling(self, client, mock_redis_client):
        """Test query stats handles errors gracefully"""
        mock_redis_client.zrevrange.side_effect = Exception("Redis error")
        
        response = client.get("/api/v1/admin/queries/stats", headers=get_admin_headers())
        assert response.status_code == 500
        assert "Failed to retrieve query statistics" in response.json()["detail"]

    def test_cache_stats_error_handling(self, client, mock_redis_client):
        """Test cache stats handles errors gracefully"""
        mock_redis_client.info.side_effect = Exception("Redis info failed")
        
        response = client.get("/api/v1/admin/cache/stats", headers=get_admin_headers())
        assert response.status_code == 500
        assert "Failed to retrieve cache statistics" in response.json()["detail"]

    def test_clear_cache_error_handling(self, client):
        """Test cache clear handles errors gracefully"""
        with patch(
            "query_intelligence.api.admin.get_cache_manager"
        ) as mock_cache_manager:
            mock_cache_manager.return_value._init_memory_cache.side_effect = Exception("Clear failed")
            
            response = client.post(
                "/api/v1/admin/cache/clear?cache_type=memory", headers=get_admin_headers()
            )
            assert response.status_code == 500
            assert "Failed to clear cache" in response.json()["detail"]

    def test_circuit_breaker_reset_error_handling(self, client):
        """Test circuit breaker reset handles errors gracefully"""
        with patch(
            "query_intelligence.api.admin.reset_circuit_breaker",
            side_effect=Exception("Reset failed")
        ):
            response = client.post(
                "/api/v1/admin/circuit-breakers/reset?breaker_name=test",
                headers=get_admin_headers()
            )
            assert response.status_code == 500
            assert "Failed to reset circuit breaker" in response.json()["detail"]

    def test_admin_endpoints_non_admin_user(self, client):
        """Test that non-admin users cannot access admin endpoints"""
        # Mock a non-admin user token
        with patch(
            "query_intelligence.middleware.auth.jwt.decode",
            return_value={"sub": "regular_user", "roles": ["user"], "exp": **********}
        ):
            non_admin_headers = {"Authorization": "Bearer user_token"}
            
            # Test all admin endpoints
            endpoints = [
                ("/api/v1/admin/metrics", "GET"),
                ("/api/v1/admin/health", "GET"),
                ("/api/v1/admin/queries/stats", "GET"),
                ("/api/v1/admin/cache/stats", "GET"),
                ("/api/v1/admin/config", "GET"),
                ("/api/v1/admin/cache/clear", "POST"),
                ("/api/v1/admin/circuit-breakers/reset", "POST")
            ]
            
            for endpoint, method in endpoints:
                if method == "GET":
                    response = client.get(endpoint, headers=non_admin_headers)
                else:
                    response = client.post(endpoint, headers=non_admin_headers)
                assert response.status_code == 403

    def test_query_stats_comprehensive_data(self, client, mock_redis_client):
        """Test query stats with comprehensive data"""
        # Mock all the data retrieval calls
        mock_redis_client.zrevrange.return_value = [
            ("how to authenticate", 25),
            ("find user model", 20),
            ("debug login issue", 15)
        ]
        mock_redis_client.get.side_effect = [
            "50",   # EXPLAIN intent
            "30",   # FIND intent  
            "15",   # DEBUG intent
            "5",    # ANALYZE intent
            "10",   # COMPARE intent
            "5",    # UNKNOWN intent
            "425.0", # total_confidence
            "100"   # total_queries
        ]
        mock_redis_client.hgetall.return_value = {
            "python": "60",
            "javascript": "25", 
            "typescript": "15"
        }
        
        response = client.get("/api/v1/admin/queries/stats", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        
        # Verify top queries
        assert len(data["top_queries"]) == 3
        assert data["top_queries"][0]["query"] == "how to authenticate"
        assert data["top_queries"][0]["count"] == 25
        
        # Verify intent distribution
        assert data["query_intents"]["EXPLAIN"] == 50
        assert data["query_intents"]["FIND"] == 30
        
        # Verify language distribution
        assert data["language_distribution"]["python"] == 60
        assert data["language_distribution"]["javascript"] == 25
        
        # Verify average confidence
        assert data["average_confidence"] == 4.25

    def test_metrics_calculation_edge_cases(self, client, mock_redis_client):
        """Test metrics calculations with edge cases"""
        # Test division by zero cases
        mock_redis_client.get.side_effect = [
            "0",  # total_queries
            "0",  # cache_hits
            "0",  # cache_misses
            "0",  # total_response_time
            "0",  # error_count
            "0",  # qpm_count for minute 0
            "0",  # qpm_count for minute 1
            "0",  # qpm_count for minute 2
            "0",  # qpm_count for minute 3
            "0",  # qpm_count for minute 4
        ]
        mock_redis_client.scard.return_value = 0
        
        response = client.get("/api/v1/admin/metrics", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        
        # All rates should be 0.0 when no data
        assert data["cache_hit_rate"] == 0.0
        assert data["average_response_time_ms"] == 0.0
        assert data["error_rate"] == 0.0
        assert data["queries_per_minute"] == 0.0

    # ==================== NEW COMPREHENSIVE TESTS ====================
    
    def test_jwt_token_validation_scenarios(self, client):
        """Test JWT token validation edge cases"""
        # Test with expired token
        with patch(
            "query_intelligence.middleware.auth.jwt.decode",
            side_effect=Exception("Token expired")
        ):
            response = client.get("/api/v1/admin/metrics", headers=get_admin_headers())
            assert response.status_code == 403
            
        # Test with malformed token
        response = client.get("/api/v1/admin/metrics", headers={"Authorization": "Bearer malformed"})
        assert response.status_code == 403
        
        # Test with missing authorization header
        response = client.get("/api/v1/admin/metrics")
        assert response.status_code == 403
        
        # Test with invalid token format
        response = client.get("/api/v1/admin/metrics", headers={"Authorization": "InvalidFormat token"})
        assert response.status_code == 403

    def test_input_validation_malformed_requests(self, client):
        """Test input validation for malformed requests"""
        # Test clear cache with invalid cache_type
        response = client.post("/api/v1/admin/cache/clear?cache_type=invalid", headers=get_admin_headers())
        assert response.status_code == 200  # Should handle gracefully
        
        # Test circuit breaker reset with extremely long name
        long_name = "a" * 1000
        response = client.post(
            f"/api/v1/admin/circuit-breakers/reset?breaker_name={long_name}",
            headers=get_admin_headers()
        )
        assert response.status_code in [200, 400, 500]  # Should handle gracefully
        
        # Test with SQL injection attempt in parameters
        response = client.post(
            "/api/v1/admin/circuit-breakers/reset?breaker_name='; DROP TABLE users; --",
            headers=get_admin_headers()
        )
        assert response.status_code in [200, 400, 500]  # Should handle gracefully

    def test_concurrent_operations(self, client):
        """Test concurrent access to admin endpoints"""
        import threading
        import queue
        
        results = queue.Queue()
        
        def make_request():
            try:
                response = client.get("/api/v1/admin/metrics", headers=get_admin_headers())
                results.put(response.status_code)
            except Exception as e:
                results.put(f"Error: {e}")
        
        # Create multiple threads
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Check results
        success_count = 0
        while not results.empty():
            result = results.get()
            if result == 200:
                success_count += 1
        
        assert success_count >= 5  # At least half should succeed

    def test_cache_states_and_transitions(self, client, mock_redis_client, mock_cache_manager):
        """Test cache statistics in different states"""
        # Test empty cache state
        mock_cache_manager._query_memory_cache = {}
        mock_cache_manager._embedding_memory_cache = {}
        mock_cache_manager._access_counts = {}
        mock_redis_client.get.side_effect = ["0", "0"]
        mock_redis_client.info.return_value = {"db0": {"keys": 0}, "used_memory": 0}
        
        response = client.get("/api/v1/admin/cache/stats", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        assert data["memory_cache_size"] == 0
        assert data["redis_keys"] == 0
        
        # Test full cache state
        mock_cache_manager._query_memory_cache = {f"query_{i}": f"result_{i}" for i in range(100)}
        mock_cache_manager._embedding_memory_cache = {f"embed_{i}": f"vector_{i}" for i in range(50)}
        mock_cache_manager._access_counts = {f"hot_query_{i}": 100 - i for i in range(20)}
        mock_redis_client.get.side_effect = ["800", "1000"]
        mock_redis_client.info.return_value = {"db0": {"keys": 5000}, "used_memory": 104857600}
        
        response = client.get("/api/v1/admin/cache/stats", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        assert data["memory_cache_size"] == 150
        assert data["redis_keys"] == 5000
        assert data["redis_memory_mb"] == 100.0
        assert len(data["hot_queries"]) <= 10

    def test_circuit_breaker_scenarios(self, client):
        """Test circuit breaker management scenarios"""
        # Test with different circuit breaker states
        test_scenarios = [
            {"state": "closed", "failure_count": 0},
            {"state": "open", "failure_count": 5},
            {"state": "half_open", "failure_count": 3},
        ]
        
        for scenario in test_scenarios:
            with patch(
                "query_intelligence.api.admin.get_circuit_breaker_status",
                return_value={"test_breaker": scenario}
            ):
                response = client.get("/api/v1/admin/health", headers=get_admin_headers())
                assert response.status_code == 200
                data = response.json()
                assert data["circuit_breakers"]["test_breaker"] == scenario["state"]
        
        # Test reset with non-existent breaker
        with patch(
            "query_intelligence.api.admin.reset_circuit_breaker",
            side_effect=KeyError("Breaker not found")
        ):
            response = client.post(
                "/api/v1/admin/circuit-breakers/reset?breaker_name=nonexistent",
                headers=get_admin_headers()
            )
            assert response.status_code == 500

    def test_performance_metrics_calculations(self, client, mock_redis_client):
        """Test performance metrics with various load scenarios"""
        # High load scenario
        mock_redis_client.get.side_effect = [
            "10000",  # total_queries
            "8000",   # cache_hits
            "2000",   # cache_misses
            "50000.0", # total_response_time
            "100",    # error_count
            "50", "45", "55", "60", "40",  # qpm for last 5 minutes
        ]
        mock_redis_client.scard.return_value = 500
        
        response = client.get("/api/v1/admin/metrics", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        
        assert data["total_queries"] == 10000
        assert data["cache_hit_rate"] == 0.8
        assert data["average_response_time_ms"] == 5.0
        assert data["active_users"] == 500
        assert data["queries_per_minute"] == 50.0
        assert data["error_rate"] == 0.01

    def test_redis_connection_failures(self, client):
        """Test behavior when Redis connections fail"""
        # Test Redis connection timeout
        with patch(
            "query_intelligence.api.admin.get_redis_client",
            side_effect=Exception("Connection timeout")
        ):
            response = client.get("/api/v1/admin/metrics", headers=get_admin_headers())
            assert response.status_code == 500
        
        # Test Redis intermittent failures
        mock_redis = AsyncMock()
        mock_redis.ping.side_effect = [True, Exception("Connection lost"), True]
        
        with patch(
            "query_intelligence.api.admin.get_redis_client",
            return_value=mock_redis
        ):
            # First call succeeds
            response1 = client.get("/api/v1/admin/health", headers=get_admin_headers())
            assert response1.status_code == 200
            
            # Second call fails
            response2 = client.get("/api/v1/admin/health", headers=get_admin_headers())
            assert response2.status_code == 200
            data = response2.json()
            assert data["redis"] == "unhealthy"

    def test_service_health_comprehensive(self, client, mock_redis_client):
        """Test comprehensive service health scenarios"""
        # Test all services healthy
        mock_redis_client.get.side_effect = ["healthy", "healthy", "healthy"]
        mock_redis_client.ping.return_value = True
        
        response = client.get("/api/v1/admin/health", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        assert data["redis"] == "healthy"
        assert data["analysis_engine"] == "healthy"
        assert data["pattern_mining"] == "healthy"
        assert data["llm_service"] == "healthy"
        
        # Test mixed health states
        mock_redis_client.get.side_effect = ["degraded", "unhealthy", "healthy"]
        mock_redis_client.ping.return_value = True
        
        response = client.get("/api/v1/admin/health", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        assert data["analysis_engine"] == "degraded"
        assert data["pattern_mining"] == "unhealthy"
        assert data["llm_service"] == "healthy"

    def test_query_statistics_edge_cases(self, client, mock_redis_client):
        """Test query statistics with edge cases"""
        # Test with no query data
        mock_redis_client.zrevrange.return_value = []
        mock_redis_client.hgetall.return_value = {}
        mock_redis_client.get.side_effect = ["0"] * 8
        
        response = client.get("/api/v1/admin/queries/stats", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        assert data["top_queries"] == []
        assert data["language_distribution"] == {}
        assert data["average_confidence"] == 0.0
        
        # Test with extreme values
        mock_redis_client.zrevrange.return_value = [
            ("query_with_very_long_text_" * 100, 1000000)
        ]
        mock_redis_client.hgetall.return_value = {
            "unknown_language": "999999",
            "constructed_lang": "1"
        }
        mock_redis_client.get.side_effect = [
            "999999", "1", "0", "0", "0", "0",  # intents
            "950000.0", "1000000"  # confidence
        ]
        
        response = client.get("/api/v1/admin/queries/stats", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        assert len(data["top_queries"]) == 1
        assert data["language_distribution"]["unknown_language"] == 999999
        assert data["average_confidence"] == 0.95

    def test_cache_clear_combinations(self, client, mock_cache_manager):
        """Test cache clearing with different combinations"""
        # Test clearing non-existent cache type
        response = client.post(
            "/api/v1/admin/cache/clear?cache_type=nonexistent",
            headers=get_admin_headers()
        )
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        
        # Test clearing with empty cache
        mock_cache_manager._query_memory_cache = {}
        mock_cache_manager._embedding_memory_cache = {}
        
        response = client.post(
            "/api/v1/admin/cache/clear?cache_type=memory",
            headers=get_admin_headers()
        )
        assert response.status_code == 200
        
        # Test clearing with populated cache
        mock_cache_manager._query_memory_cache = {"key": "value"}
        mock_cache_manager._embedding_memory_cache = {"key": "vector"}
        
        response = client.post(
            "/api/v1/admin/cache/clear?cache_type=all",
            headers=get_admin_headers()
        )
        assert response.status_code == 200
        mock_cache_manager._init_memory_cache.assert_called()

    def test_configuration_sanitization(self, client):
        """Test configuration endpoint sanitization"""
        response = client.get("/api/v1/admin/config", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        
        # Verify expected configuration is present
        required_fields = [
            "environment", "service_name", "port", "cache_ttl_hours",
            "rate_limit_requests", "max_query_length", "enable_metrics"
        ]
        for field in required_fields:
            assert field in data
        
        # Verify sensitive data is NOT present
        data_str = json.dumps(data).lower()
        sensitive_terms = [
            "secret", "password", "key", "token", "credential",
            "jwt_secret", "api_key", "google_api_key", "pinecone_api_key"
        ]
        for term in sensitive_terms:
            if term in ["key", "service_name", "api_key_header", "jwt_secret_key"]:
                continue  # These are configuration field names, not values
            assert term not in data_str, f"Sensitive term '{term}' found in config response"

    def test_rate_limiting_scenarios(self, client):
        """Test rate limiting behavior for admin endpoints"""
        # Make multiple rapid requests
        responses = []
        for i in range(5):
            response = client.get("/api/v1/admin/metrics", headers=get_admin_headers())
            responses.append(response.status_code)
        
        # Most requests should succeed (rate limiting is mocked)
        success_count = sum(1 for status in responses if status == 200)
        assert success_count >= 3  # At least 3 out of 5 should succeed

    def test_error_handling_comprehensive(self, client, mock_redis_client):
        """Test comprehensive error handling scenarios"""
        # Test different types of Redis errors
        redis_errors = [
            ConnectionError("Connection refused"),
            TimeoutError("Operation timed out"),
            ValueError("Invalid value"),
            KeyError("Key not found"),
            Exception("Unknown error")
        ]
        
        for error in redis_errors:
            mock_redis_client.get.side_effect = error
            response = client.get("/api/v1/admin/metrics", headers=get_admin_headers())
            assert response.status_code == 500
            assert "Failed to retrieve metrics" in response.json()["detail"]
            
            # Reset for next test
            mock_redis_client.get.side_effect = None
            mock_redis_client.get.return_value = "0"

    def test_authentication_bypass_attempts(self, client):
        """Test various authentication bypass attempts"""
        bypass_attempts = [
            {},  # No headers
            {"Authorization": ""},  # Empty authorization
            {"Authorization": "Bearer"},  # Bearer without token
            {"Authorization": "Basic dXNlcjpwYXNz"},  # Wrong auth type
            {"X-API-Key": "admin"},  # Wrong header
            {"Authorization": "Bearer \x00\x01\x02"},  # Binary data
            {"Authorization": "Bearer " + "a" * 10000},  # Extremely long token
        ]
        
        for headers in bypass_attempts:
            response = client.get("/api/v1/admin/metrics", headers=headers)
            assert response.status_code == 403, f"Bypass attempt with headers {headers} should fail"

    def test_response_data_validation(self, client, mock_redis_client):
        """Test response data structure validation"""
        # Test metrics response structure
        mock_redis_client.get.side_effect = ["100", "80", "20", "5000.0", "2"] + ["10"] * 5
        mock_redis_client.scard.return_value = 50
        
        response = client.get("/api/v1/admin/metrics", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        
        # Validate SystemMetrics structure
        metrics = SystemMetrics(**data)
        assert isinstance(metrics.total_queries, int)
        assert isinstance(metrics.cache_hit_rate, float)
        assert isinstance(metrics.average_response_time_ms, float)
        assert isinstance(metrics.active_users, int)
        assert isinstance(metrics.queries_per_minute, float)
        assert isinstance(metrics.error_rate, float)
        
        # Test health response structure
        mock_redis_client.get.side_effect = ["healthy", "healthy", "healthy"]
        mock_redis_client.ping.return_value = True
        
        response = client.get("/api/v1/admin/health", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        
        # Validate ServiceHealth structure
        health = ServiceHealth(**data)
        assert isinstance(health.redis, str)
        assert isinstance(health.analysis_engine, str)
        assert isinstance(health.pattern_mining, str)
        assert isinstance(health.llm_service, str)
        assert isinstance(health.circuit_breakers, dict)

    def test_admin_endpoint_security(self, client):
        """Test security aspects of admin endpoints"""
        # Test CORS headers
        response = client.options("/api/v1/admin/metrics", headers=get_admin_headers())
        # Should handle OPTIONS request
        assert response.status_code in [200, 405]
        
        # Test with different HTTP methods
        methods_to_test = [
            ("PUT", "/api/v1/admin/metrics"),
            ("PATCH", "/api/v1/admin/metrics"),
            ("DELETE", "/api/v1/admin/metrics"),
        ]
        
        for method, endpoint in methods_to_test:
            response = getattr(client, method.lower())(endpoint, headers=get_admin_headers())
            assert response.status_code == 405  # Method not allowed

    def test_large_data_handling(self, client, mock_redis_client):
        """Test handling of large data sets"""
        # Test with large number of queries
        large_queries = [(f"query_{i}", i) for i in range(1000)]
        mock_redis_client.zrevrange.return_value = large_queries[:10]  # Only top 10 returned
        
        large_languages = {f"lang_{i}": str(i * 100) for i in range(100)}
        mock_redis_client.hgetall.return_value = large_languages
        
        mock_redis_client.get.side_effect = ["1000"] * 8
        
        response = client.get("/api/v1/admin/queries/stats", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        
        # Should handle large data gracefully
        assert len(data["top_queries"]) == 10
        assert len(data["language_distribution"]) == 100

    def test_timeout_scenarios(self, client, mock_redis_client):
        """Test timeout handling scenarios"""
        import time
        
        # Mock slow Redis operations
        def slow_get(*args, **kwargs):
            time.sleep(0.1)  # Simulate slow operation
            return "100"
        
        mock_redis_client.get.side_effect = slow_get
        
        # Should complete despite slow operations
        response = client.get("/api/v1/admin/metrics", headers=get_admin_headers())
        # Should either succeed or timeout gracefully
        assert response.status_code in [200, 500, 504]

    def test_data_consistency_checks(self, client, mock_redis_client):
        """Test data consistency in responses"""
        # Test with inconsistent data
        mock_redis_client.get.side_effect = [
            "100",    # total_queries
            "150",    # cache_hits (more than total!)
            "50",     # cache_misses
            "5000.0", # total_response_time
            "200",    # error_count (more than total!)
        ] + ["10"] * 5
        mock_redis_client.scard.return_value = 50
        
        response = client.get("/api/v1/admin/metrics", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        
        # Should handle inconsistent data gracefully
        assert data["total_queries"] == 100
        # Cache hit rate calculation should handle the inconsistency
        assert 0.0 <= data["cache_hit_rate"] <= 1.0 or data["cache_hit_rate"] > 1.0
        # Error rate might be > 1.0 due to inconsistent data
        assert data["error_rate"] >= 0.0
