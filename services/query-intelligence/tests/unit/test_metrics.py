import pytest
from unittest.mock import Mock, patch
import time
import asyncio
from query_intelligence.utils.metrics import (
    MetricsCollector,
    track_processing_time,
    query_counter,
    query_duration,
    active_queries,
    cache_hits,
    cache_misses,
    embedding_generation_duration,
    llm_requests,
    llm_token_usage,
    search_results,
    response_confidence,
)


@pytest.fixture(autouse=True)
def reset_metrics():
    """Reset all metrics before each test"""
    for metric in [
        query_counter,
        query_duration,
        active_queries,
        cache_hits,
        cache_misses,
        embedding_generation_duration,
        llm_requests,
        llm_token_usage,
        search_results,
        response_confidence,
    ]:
        if hasattr(metric, "_metrics"):
            metric._metrics.clear()
        if hasattr(metric, "_value"):
            metric._value.set(0)


class TestMetricsCollector:
    """Test the MetricsCollector class"""

    def test_record_query(self):
        """Test recording a query"""
        with patch.object(query_counter, "labels") as mock_labels:
            mock_counter = Mock()
            mock_labels.return_value = mock_counter
            MetricsCollector.record_query("test_intent", "success")
            mock_labels.assert_called_with(intent="test_intent", status="success")
            mock_counter.inc.assert_called_once()

    def test_record_query_duration(self):
        """Test recording query duration"""
        with patch.object(query_duration, "labels") as mock_labels:
            mock_histogram = Mock()
            mock_labels.return_value = mock_histogram
            MetricsCollector.record_query_duration("test_intent", 0.123)
            mock_labels.assert_called_with(intent="test_intent")
            mock_histogram.observe.assert_called_with(0.123)

    def test_set_active_queries(self):
        """Test setting active queries"""
        with patch.object(active_queries, "set") as mock_set:
            MetricsCollector.set_active_queries(5)
            mock_set.assert_called_with(5)

    def test_record_cache_hit(self):
        """Test recording a cache hit"""
        with patch.object(cache_hits, "inc") as mock_inc:
            MetricsCollector.record_cache_hit()
            mock_inc.assert_called_once()

    def test_record_cache_miss(self):
        """Test recording a cache miss"""
        with patch.object(cache_misses, "inc") as mock_inc:
            MetricsCollector.record_cache_miss()
            mock_inc.assert_called_once()

    def test_record_embedding_duration(self):
        """Test recording embedding duration"""
        with patch.object(embedding_generation_duration, "observe") as mock_observe:
            MetricsCollector.record_embedding_duration(0.456)
            mock_observe.assert_called_with(0.456)

    def test_record_llm_request(self):
        """Test recording an LLM request"""
        with patch.object(llm_requests, "labels") as mock_labels:
            mock_counter = Mock()
            mock_labels.return_value = mock_counter
            MetricsCollector.record_llm_request("test_model", "failure")
            mock_labels.assert_called_with(model="test_model", status="failure")
            mock_counter.inc.assert_called_once()

    def test_record_llm_tokens(self):
        """Test recording LLM token usage"""
        with patch.object(llm_token_usage, "labels") as mock_labels:
            mock_counter = Mock()
            mock_labels.return_value = mock_counter
            MetricsCollector.record_llm_tokens("test_model", 100, 200)
            mock_labels.assert_any_call(model="test_model", token_type="prompt")
            mock_labels.assert_any_call(model="test_model", token_type="completion")
            assert mock_counter.inc.call_count == 2

    def test_record_search_results(self):
        """Test recording search results"""
        with patch.object(search_results, "labels") as mock_labels:
            mock_histogram = Mock()
            mock_labels.return_value = mock_histogram
            MetricsCollector.record_search_results("repo123", 10)
            mock_labels.assert_called_with(repository_id="repo123")
            mock_histogram.observe.assert_called_with(10)

    def test_record_response_confidence(self):
        """Test recording response confidence"""
        with patch.object(response_confidence, "labels") as mock_labels:
            mock_histogram = Mock()
            mock_labels.return_value = mock_histogram
            MetricsCollector.record_response_confidence("test_intent", 0.95)
            mock_labels.assert_called_with(intent="test_intent")
            mock_histogram.observe.assert_called_with(0.95)


class TestTrackProcessingTime:
    """Test the track_processing_time decorator"""

    @pytest.mark.asyncio
    async def test_async_function_success(self):
        """Test decorator with a successful async function"""
        with patch("query_intelligence.utils.metrics.logger") as mock_logger:

            @track_processing_time("test_metric")
            async def sample_async_func():
                await asyncio.sleep(0.01)
                return "done"

            result = await sample_async_func()
            assert result == "done"
            mock_logger.info.assert_called_once()
            call_args = mock_logger.info.call_args[0]
            assert call_args[0] == "test_metric_duration"

    @pytest.mark.asyncio
    async def test_async_function_failure(self):
        """Test decorator with a failing async function"""
        with patch("query_intelligence.utils.metrics.logger") as mock_logger:

            @track_processing_time("test_metric")
            async def sample_async_func():
                raise ValueError("test error")

            with pytest.raises(ValueError):
                await sample_async_func()

            mock_logger.error.assert_called_once()
            call_args = mock_logger.error.call_args[0]
            assert call_args[0] == "test_metric_failed"

    def test_sync_function_success(self):
        """Test decorator with a successful sync function"""
        with patch("query_intelligence.utils.metrics.logger") as mock_logger:

            @track_processing_time("test_metric")
            def sample_sync_func():
                time.sleep(0.01)
                return "done"

            result = sample_sync_func()
            assert result == "done"
            mock_logger.info.assert_called_once()
            call_args = mock_logger.info.call_args[0]
            assert call_args[0] == "test_metric_duration"

    def test_sync_function_failure(self):
        """Test decorator with a failing sync function"""
        with patch("query_intelligence.utils.metrics.logger") as mock_logger:

            @track_processing_time("test_metric")
            def sample_sync_func():
                raise ValueError("test error")

            with pytest.raises(ValueError):
                sample_sync_func()

            mock_logger.error.assert_called_once()
            call_args = mock_logger.error.call_args[0]
            assert call_args[0] == "test_metric_failed"
