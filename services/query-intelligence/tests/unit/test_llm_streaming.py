"""
Unit tests for LLM streaming functionality with new Google GenAI SDK
"""

import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, AsyncMock, patch

from query_intelligence.services.llm_service_v2 import LLMServiceV2
from query_intelligence.models import (
    IntentAnalysis,
    QueryIntent,
    CodeChunk,
    QueryContext,
)


@pytest.mark.asyncio
async def test_llm_streaming_with_new_sdk():
    """Test LLM streaming with new Google GenAI SDK async streaming"""

    # Create a mock client with async streaming capabilities
    mock_client = MagicMock()
    mock_aio = MagicMock()
    mock_models = MagicMock()

    # Create mock streaming response
    async def mock_stream_generator():
        chunks = [
            MagicMock(text="Hello "),
            MagicMock(text="from "),
            MagicMock(text="streaming "),
            MagicMock(text="API!"),
        ]
        for chunk in chunks:
            yield chunk

    # Set up the mock to return our async generator
    mock_models.generate_content_stream = AsyncMock(
        return_value=mock_stream_generator()
    )
    mock_aio.models = mock_models
    mock_client.aio = mock_aio

    # Mock the synchronous generate_content for model initialization
    mock_client.models.generate_content = MagicMock(
        return_value=MagicMock(text="Test response")
    )

    # Patch the Client creation
    with patch("google.genai.Client", return_value=mock_client):
        # Create LLM service
        llm_service = LLMServiceV2()
        llm_service.model_name = "gemini-2.5-flash"

        # Create test inputs
        query = "Test query"
        intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN, confidence=0.9, code_elements=[]
        )
        code_chunks = []
        context = QueryContext(
            session_id="test-session", repository_id="test-repo", user_id="test-user"
        )

        # Collect streamed chunks
        streamed_text = ""
        async for chunk in llm_service.stream_response(
            query, intent, code_chunks, context
        ):
            streamed_text += chunk

        # Verify the streaming worked correctly
        assert streamed_text == "Hello from streaming API!"

        # Verify the async streaming API was called with correct parameters
        mock_models.generate_content_stream.assert_called_once()
        call_args = mock_models.generate_content_stream.call_args
        assert call_args.kwargs["model"] == "gemini-2.5-flash"
        assert "contents" in call_args.kwargs
        assert "config" in call_args.kwargs


@pytest.mark.asyncio
async def test_llm_streaming_error_handling():
    """Test error handling in streaming response"""

    # Create a mock client that raises an error
    mock_client = MagicMock()
    mock_aio = MagicMock()
    mock_models = MagicMock()

    # Create mock streaming response that raises an error
    async def mock_stream_generator_with_error():
        yield MagicMock(text="Start of ")
        raise Exception("Streaming API error")

    mock_models.generate_content_stream = AsyncMock(
        return_value=mock_stream_generator_with_error()
    )
    mock_aio.models = mock_models
    mock_client.aio = mock_aio

    # Mock the synchronous generate_content
    mock_client.models.generate_content = MagicMock(
        return_value=MagicMock(text="Test response")
    )

    with patch("google.genai.Client", return_value=mock_client):
        llm_service = LLMServiceV2()
        llm_service.model_name = "gemini-2.5-flash"

        # Create test inputs
        query = "Test query"
        intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN, confidence=0.9, code_elements=[]
        )
        code_chunks = []
        context = QueryContext(
            session_id="test-session", repository_id="test-repo", user_id="test-user"
        )

        # Collect streamed chunks
        streamed_text = ""
        async for chunk in llm_service.stream_response(
            query, intent, code_chunks, context
        ):
            streamed_text += chunk

        # Should have received the initial chunk and then the error message
        assert "Start of " in streamed_text
        assert "Error generating response: Streaming API error" in streamed_text


@pytest.mark.asyncio
async def test_llm_streaming_with_empty_chunks():
    """Test streaming with some empty chunks"""

    mock_client = MagicMock()
    mock_aio = MagicMock()
    mock_models = MagicMock()

    # Create mock streaming response with some empty chunks
    async def mock_stream_generator():
        chunks = [
            MagicMock(text="Hello "),
            MagicMock(text=None),  # Empty chunk
            MagicMock(text="world"),
            MagicMock(text=""),  # Empty string
            MagicMock(text="!"),
        ]
        for chunk in chunks:
            yield chunk

    mock_models.generate_content_stream = AsyncMock(
        return_value=mock_stream_generator()
    )
    mock_aio.models = mock_models
    mock_client.aio = mock_aio

    # Mock the synchronous generate_content
    mock_client.models.generate_content = MagicMock(
        return_value=MagicMock(text="Test response")
    )

    with patch("google.genai.Client", return_value=mock_client):
        llm_service = LLMServiceV2()
        llm_service.model_name = "gemini-2.5-flash"

        # Create test inputs
        query = "Test query"
        intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN, confidence=0.9, code_elements=[]
        )
        code_chunks = []
        context = QueryContext(
            session_id="test-session", repository_id="test-repo", user_id="test-user"
        )

        # Collect streamed chunks
        streamed_text = ""
        async for chunk in llm_service.stream_response(
            query, intent, code_chunks, context
        ):
            streamed_text += chunk

        # Should only get non-empty chunks
        assert streamed_text == "Hello world!"


@pytest.mark.asyncio
async def test_streaming_with_code_context():
    """Test streaming with code context in prompt"""

    mock_client = MagicMock()
    mock_aio = MagicMock()
    mock_models = MagicMock()

    # Track the prompt that was sent
    sent_prompt = None

    async def mock_stream_generator():
        yield MagicMock(text="Based on the code provided...")

    async def capture_prompt(*args, **kwargs):
        nonlocal sent_prompt
        sent_prompt = kwargs.get("contents", "")
        return mock_stream_generator()

    mock_models.generate_content_stream = AsyncMock(side_effect=capture_prompt)
    mock_aio.models = mock_models
    mock_client.aio = mock_aio

    # Mock the synchronous generate_content
    mock_client.models.generate_content = MagicMock(
        return_value=MagicMock(text="Test response")
    )

    with patch("google.genai.Client", return_value=mock_client):
        llm_service = LLMServiceV2()
        llm_service.model_name = "gemini-2.5-flash"

        # Create test inputs with code chunks
        query = "Explain this authentication code"
        intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN,
            confidence=0.9,
            code_elements=["authenticate", "JWT"],
        )
        code_chunks = [
            CodeChunk(
                chunk_id="1",
                file_path="auth.py",
                start_line=10,
                end_line=20,
                content="def authenticate(token):\n    # JWT validation",
                language="python",
                semantic_score=0.9,
                keyword_score=0.8,
                combined_score=0.85,
            )
        ]
        context = QueryContext(
            session_id="test-session", repository_id="test-repo", user_id="test-user"
        )

        # Collect streamed chunks
        streamed_text = ""
        async for chunk in llm_service.stream_response(
            query, intent, code_chunks, context
        ):
            streamed_text += chunk

        # Verify prompt included code context
        assert sent_prompt is not None
        assert "auth.py" in sent_prompt
        assert "JWT validation" in sent_prompt
        assert "authenticate" in sent_prompt
