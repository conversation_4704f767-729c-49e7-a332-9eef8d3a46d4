"""
Comprehensive tests for CacheManager with multi-level caching and performance optimization.
"""
import pytest
import asyncio
import time
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, Optional, List

from query_intelligence.services.cache_manager import CacheManager, get_cache_manager
from query_intelligence.models import QueryResult, QueryIntent, CodeReference


class TestCacheManager:
    """Test suite for CacheManager."""

    @pytest.fixture
    def mock_redis_client(self):
        """Mock Redis client."""
        client = AsyncMock()
        client.get.return_value = None
        client.setex.return_value = True
        client.delete.return_value = True
        client.exists.return_value = False
        client.scan.return_value = (0, [])
        return client

    @pytest.fixture
    def mock_settings(self):
        """Mock settings."""
        settings = MagicMock()
        settings.REDIS_URL = "redis://localhost:6379"
        settings.CACHE_TTL_HOURS = 24
        return settings

    @pytest.fixture
    def cache_manager(self, mock_redis_client, mock_settings):
        """Create CacheManager instance with mocked dependencies."""
        with patch('query_intelligence.services.cache_manager.get_redis_client', return_value=mock_redis_client), \
             patch('query_intelligence.services.cache_manager.get_settings', return_value=mock_settings):
            return CacheManager()

    @pytest.fixture
    def sample_query_result(self):
        """Create sample query result."""
        return QueryResult(
            answer="Authentication works by validating JWT tokens",
            intent=QueryIntent.EXPLAIN,
            confidence=0.9,
            references=[
                CodeReference(
                    file_path="src/auth.py",
                    start_line=10,
                    end_line=15,
                    snippet="def authenticate(token):",
                    relevance_score=0.95,
                    language="python"
                )
            ],
            execution_time_ms=150.0,
            follow_up_questions=["How are JWT tokens validated?"],
            metadata={"model_used": "gemini-2.5-flash"}
        )

    @pytest.mark.asyncio
    async def test_get_cached_query_result_memory_hit(self, cache_manager, sample_query_result):
        """Test cache hit from memory cache."""
        # Setup: Put result in memory cache
        query = "How does authentication work?"
        repository_id = "test-repo"
        cache_key = cache_manager._generate_query_cache_key(query, repository_id, None)
        
        # Manually set memory cache
        cache_manager._query_memory_cache[cache_key] = sample_query_result.model_dump()
        
        # Act
        result = await cache_manager.get_cached_query_result(query, repository_id)
        
        # Assert
        assert result is not None
        assert isinstance(result, QueryResult)
        assert result.answer == sample_query_result.answer
        assert result.confidence == sample_query_result.confidence

    @pytest.mark.asyncio
    async def test_get_cached_query_result_redis_hit(self, cache_manager, sample_query_result, mock_redis_client):
        """Test cache hit from Redis cache."""
        # Setup: Configure Redis to return cached result
        query = "How does authentication work?"
        repository_id = "test-repo"
        
        mock_redis_client.get.return_value = sample_query_result.model_dump_json()
        
        # Act
        result = await cache_manager.get_cached_query_result(query, repository_id)
        
        # Assert
        assert result is not None
        assert isinstance(result, QueryResult)
        assert result.answer == sample_query_result.answer
        mock_redis_client.get.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_cached_query_result_cache_miss(self, cache_manager, mock_redis_client):
        """Test cache miss (no result in memory or Redis)."""
        # Setup: Both caches empty
        query = "How does authentication work?"
        repository_id = "test-repo"
        
        mock_redis_client.get.return_value = None
        
        # Act
        result = await cache_manager.get_cached_query_result(query, repository_id)
        
        # Assert
        assert result is None
        mock_redis_client.get.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_cached_query_result_with_filters(self, cache_manager, sample_query_result):
        """Test cache with filters."""
        query = "How does authentication work?"
        repository_id = "test-repo"
        filters = {"language": "python", "file_type": "py"}
        
        # Setup memory cache with filters
        cache_key = cache_manager._generate_query_cache_key(query, repository_id, filters)
        cache_manager._query_memory_cache[cache_key] = sample_query_result.model_dump()
        
        # Act
        result = await cache_manager.get_cached_query_result(query, repository_id, filters)
        
        # Assert
        assert result is not None
        assert result.answer == sample_query_result.answer

    @pytest.mark.asyncio
    async def test_cache_query_result_basic(self, cache_manager, sample_query_result, mock_redis_client):
        """Test basic query result caching."""
        query = "How does authentication work?"
        repository_id = "test-repo"
        
        # Act
        await cache_manager.cache_query_result(query, repository_id, sample_query_result)
        
        # Give async task time to complete
        await asyncio.sleep(0.1)
        
        # Assert: Should attempt to cache in Redis
        mock_redis_client.setex.assert_called_once()
        call_args = mock_redis_client.setex.call_args
        assert call_args[0][0].startswith("query_cache:")  # Cache key prefix

    @pytest.mark.asyncio
    async def test_cache_query_result_low_confidence(self, cache_manager, mock_redis_client):
        """Test caching with low confidence result (shorter TTL)."""
        query = "How does authentication work?"
        repository_id = "test-repo"
        
        # Low confidence result
        low_confidence_result = QueryResult(
            answer="Unclear authentication process",
            intent=QueryIntent.EXPLAIN,
            confidence=0.5,  # Low confidence
            references=[],
            execution_time_ms=200.0,
            follow_up_questions=[],
            metadata={}
        )
        
        # Act
        await cache_manager.cache_query_result(query, repository_id, low_confidence_result)
        
        # Give async task time to complete
        await asyncio.sleep(0.1)
        
        # Assert: Should use shorter TTL
        mock_redis_client.setex.assert_called_once()
        call_args = mock_redis_client.setex.call_args
        # TTL should be reduced for low confidence
        assert call_args[0][1] == cache_manager.query_cache_ttl // 2

    @pytest.mark.asyncio
    async def test_cache_query_result_hot_query(self, cache_manager, sample_query_result):
        """Test caching of hot queries (should go to memory)."""
        query = "How does authentication work?"  # Short query = hot
        repository_id = "test-repo"
        
        # Act
        await cache_manager.cache_query_result(query, repository_id, sample_query_result)
        
        # Assert: Should be in memory cache for hot queries
        cache_key = cache_manager._generate_query_cache_key(query, repository_id, None)
        assert cache_key in cache_manager._query_memory_cache

    @pytest.mark.asyncio
    async def test_get_cached_embedding_memory_hit(self, cache_manager):
        """Test embedding cache hit from memory."""
        text = "How does authentication work?"
        context_type = "query"
        embedding = [0.1, 0.2, 0.3, 0.4, 0.5]
        
        # Setup memory cache
        cache_key = cache_manager._generate_embedding_cache_key(text, context_type)
        cache_manager._embedding_memory_cache[cache_key] = embedding
        
        # Act
        result = await cache_manager.get_cached_embedding(text, context_type)
        
        # Assert
        assert result == embedding

    @pytest.mark.asyncio
    async def test_get_cached_embedding_redis_hit(self, cache_manager, mock_redis_client):
        """Test embedding cache hit from Redis."""
        text = "How does authentication work?"
        context_type = "query"
        embedding = [0.1, 0.2, 0.3, 0.4, 0.5]
        
        # Mock Redis to return JSON-serialized embedding
        import json
        mock_redis_client.get.return_value = json.dumps(embedding)
        
        # Act
        result = await cache_manager.get_cached_embedding(text, context_type)
        
        # Assert
        assert result == embedding
        mock_redis_client.get.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_cached_embedding_miss(self, cache_manager, mock_redis_client):
        """Test embedding cache miss."""
        text = "How does authentication work?"
        context_type = "query"
        
        mock_redis_client.get.return_value = None
        
        # Act
        result = await cache_manager.get_cached_embedding(text, context_type)
        
        # Assert
        assert result is None

    @pytest.mark.asyncio
    async def test_cache_embedding_basic(self, cache_manager, mock_redis_client):
        """Test basic embedding caching."""
        text = "How does authentication work?"
        context_type = "query"
        embedding = [0.1, 0.2, 0.3, 0.4, 0.5]
        
        # Act
        await cache_manager.cache_embedding(text, embedding, context_type)
        
        # Assert: Should be in both memory and Redis
        cache_key = cache_manager._generate_embedding_cache_key(text, context_type)
        assert cache_key in cache_manager._embedding_memory_cache
        mock_redis_client.setex.assert_called_once()

    @pytest.mark.asyncio
    async def test_invalidate_repository_cache_basic(self, cache_manager):
        """Test basic repository cache invalidation."""
        repository_id = "test-repo"
        
        # Setup: Add some data to memory cache
        cache_manager._query_memory_cache[f"query_cache:{repository_id}:key1"] = {"test": "data"}
        cache_manager._query_memory_cache[f"query_cache:other-repo:key2"] = {"test": "data"}
        
        # Act
        cache_manager.invalidate_repository_cache(repository_id)
        
        # Assert: Should remove only repository-specific keys
        assert f"query_cache:{repository_id}:key1" not in cache_manager._query_memory_cache
        assert f"query_cache:other-repo:key2" in cache_manager._query_memory_cache

    @pytest.mark.asyncio
    async def test_warm_cache_basic(self, cache_manager, mock_redis_client):
        """Test cache warming functionality."""
        repository_id = "test-repo"
        
        # Setup: Mock Redis to return no existing cache
        mock_redis_client.exists.return_value = False
        
        # Act
        await cache_manager.warm_cache(repository_id)
        
        # Give async task time to complete
        await asyncio.sleep(0.1)
        
        # Assert: Should check for existing cache entries
        mock_redis_client.exists.assert_called()

    @pytest.mark.asyncio
    async def test_generate_query_cache_key_basic(self, cache_manager):
        """Test query cache key generation."""
        query = "How does authentication work?"
        repository_id = "test-repo"
        
        # Act
        key = cache_manager._generate_query_cache_key(query, repository_id, None)
        
        # Assert
        assert isinstance(key, str)
        assert key.startswith("query_cache:")
        assert repository_id in key

    @pytest.mark.asyncio
    async def test_generate_query_cache_key_with_filters(self, cache_manager):
        """Test query cache key generation with filters."""
        query = "How does authentication work?"
        repository_id = "test-repo"
        filters = {"language": "python", "file_type": "py"}
        
        # Act
        key = cache_manager._generate_query_cache_key(query, repository_id, filters)
        
        # Assert
        assert isinstance(key, str)
        assert key.startswith("query_cache:")
        # Different filters should generate different keys
        key2 = cache_manager._generate_query_cache_key(query, repository_id, {"language": "javascript"})
        assert key != key2

    @pytest.mark.asyncio
    async def test_generate_embedding_cache_key(self, cache_manager):
        """Test embedding cache key generation."""
        text = "How does authentication work?"
        context_type = "query"
        
        # Act
        key = cache_manager._generate_embedding_cache_key(text, context_type)
        
        # Assert
        assert isinstance(key, str)
        assert key.startswith("embedding:")
        assert context_type in key

    @pytest.mark.asyncio
    async def test_is_hot_query_short_query(self, cache_manager):
        """Test hot query detection for short queries."""
        query = "How does authentication work?"  # Short query
        
        # Act
        result = cache_manager._is_hot_query(query)
        
        # Assert
        assert result is True

    @pytest.mark.asyncio
    async def test_is_hot_query_with_patterns(self, cache_manager):
        """Test hot query detection with common patterns."""
        queries = [
            "What is the main component?",
            "How does this work?",
            "Explain the process",
            "Show me the code",
            "Find the function",
            "List all methods"
        ]
        
        for query in queries:
            # Act
            result = cache_manager._is_hot_query(query)
            
            # Assert
            assert result is True

    @pytest.mark.asyncio
    async def test_is_hot_query_long_specific_query(self, cache_manager):
        """Test hot query detection for long, specific queries."""
        query = "This is a very long and specific query that should not be considered hot because it's unlikely to be repeated by many users and contains very specific technical details that are unique to this particular use case and doesn't contain common patterns"
        
        # Act
        result = cache_manager._is_hot_query(query)
        
        # Assert
        assert result is False

    @pytest.mark.asyncio
    async def test_memory_cache_operations(self, cache_manager):
        """Test memory cache basic operations."""
        cache_key = "test_key"
        test_data = {"test": "data"}
        
        # Act: Set data
        cache_manager._set_in_memory_cache(cache_key, test_data, "query")
        
        # Assert: Data should be retrievable
        result = cache_manager._get_from_memory_cache(cache_key, "query")
        assert result == test_data
        assert cache_manager._access_counts[cache_key] == 2  # 1 for set, 1 for get

    @pytest.mark.asyncio
    async def test_memory_cache_eviction(self, cache_manager):
        """Test memory cache eviction when full."""
        # Setup: Set small cache size
        cache_manager.memory_cache_size = 2
        
        # Act: Add items beyond capacity
        cache_manager._set_in_memory_cache("key1", {"data": "1"}, "query")
        time.sleep(0.01)  # Ensure different timestamps
        cache_manager._set_in_memory_cache("key2", {"data": "2"}, "query")
        time.sleep(0.01)
        cache_manager._set_in_memory_cache("key3", {"data": "3"}, "query")
        
        # Assert: Oldest item should be evicted
        assert "key1" not in cache_manager._query_memory_cache
        assert "key2" in cache_manager._query_memory_cache
        assert "key3" in cache_manager._query_memory_cache

    @pytest.mark.asyncio
    async def test_get_from_memory_cache_miss(self, cache_manager):
        """Test getting non-existent data from memory cache."""
        cache_key = "nonexistent_key"
        
        # Act
        result = cache_manager._get_from_memory_cache(cache_key, "query")
        
        # Assert
        assert result is None

    @pytest.mark.asyncio
    async def test_cache_error_handling(self, cache_manager, mock_redis_client):
        """Test error handling in cache operations."""
        query = "How does authentication work?"
        repository_id = "test-repo"
        
        # Setup Redis to raise exception
        mock_redis_client.get.side_effect = Exception("Redis connection failed")
        
        # Act: Should not raise exception
        result = await cache_manager.get_cached_query_result(query, repository_id)
        
        # Assert: Should return None on error
        assert result is None

    @pytest.mark.asyncio
    async def test_concurrent_cache_operations(self, cache_manager, sample_query_result):
        """Test concurrent cache operations don't interfere."""
        queries = [f"Query {i}" for i in range(5)]
        repository_id = "test-repo"
        
        # Act: Perform concurrent cache operations
        tasks = []
        for query in queries:
            tasks.append(cache_manager.cache_query_result(query, repository_id, sample_query_result))
        
        # Wait for all tasks to complete
        await asyncio.gather(*tasks)
        
        # Assert: All operations should complete without errors
        # Check that all queries were cached
        for query in queries:
            cache_key = cache_manager._generate_query_cache_key(query, repository_id, None)
            # Hot queries should be in memory cache
            if cache_manager._is_hot_query(query):
                assert cache_key in cache_manager._query_memory_cache

    @pytest.mark.asyncio
    async def test_cache_performance_tracking(self, cache_manager, sample_query_result):
        """Test that cache operations are tracked for performance."""
        query = "How does authentication work?"
        repository_id = "test-repo"
        
        # Setup memory cache for hit
        cache_key = cache_manager._generate_query_cache_key(query, repository_id, None)
        cache_manager._query_memory_cache[cache_key] = sample_query_result.model_dump()
        
        # Act
        start_time = time.time()
        result = await cache_manager.get_cached_query_result(query, repository_id)
        end_time = time.time()
        
        # Assert
        assert result is not None
        # Memory cache should be very fast (< 10ms)
        assert (end_time - start_time) < 0.01

    @pytest.mark.asyncio
    async def test_get_cache_manager_factory(self):
        """Test factory function for cache manager."""
        # Act
        manager = get_cache_manager()
        
        # Assert
        assert manager is not None
        assert isinstance(manager, CacheManager)
        
        # Should return same instance (singleton)
        manager2 = get_cache_manager()
        assert manager is manager2

    @pytest.mark.asyncio
    async def test_get_popular_queries(self, cache_manager):
        """Test getting popular queries for cache warming."""
        repository_id = "test-repo"
        
        # Act
        queries = await cache_manager._get_popular_queries(repository_id)
        
        # Assert
        assert isinstance(queries, list)
        assert len(queries) > 0
        assert all(isinstance(q, str) for q in queries)

    @pytest.mark.asyncio
    async def test_redis_operations(self, cache_manager, mock_redis_client):
        """Test Redis operations."""
        key = "test_key"
        value = {"test": "data"}
        ttl = 3600
        
        # Test set
        await cache_manager._set_in_redis(key, value, ttl)
        mock_redis_client.setex.assert_called_once_with(key, ttl, '{"test": "data"}')
        
        # Test get
        mock_redis_client.get.return_value = '{"test": "data"}'
        result = await cache_manager._get_from_redis(key)
        assert result == value
        
        # Test exists
        mock_redis_client.exists.return_value = 1
        exists = await cache_manager._exists_in_redis(key)
        assert exists is True