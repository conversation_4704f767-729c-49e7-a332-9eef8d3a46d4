import pytest
from unittest.mock import Mock, patch, MagicMock
from langdetect import LangDetectException
from query_intelligence.services.language_detector import (
    MultiLanguageSupport,
    LanguageDetectionResult,
    TranslationResult,
    SupportedLanguage,
    get_language_support,
)


@pytest.fixture
def mock_translator():
    """Mock Google Translator"""
    mock_translator = Mock()
    mock_result = Mock()
    mock_result.text = "translated text"
    mock_result.extra_data = {"confidence": 0.95}
    mock_translator.translate.return_value = mock_result
    return mock_translator


@pytest.fixture
def mock_detect_langs():
    """Mock language detection"""
    with patch(
        "query_intelligence.services.language_detector.detect_langs"
    ) as mock:
        mock_lang = Mock()
        mock_lang.lang = "es"
        mock_lang.prob = 0.85
        mock.return_value = [mock_lang]
        yield mock


class TestLanguageDetection:
    """Test language detection functionality"""

    @pytest.mark.asyncio
    async def test_detect_supported_language(self, mock_detect_langs):
        """Test detection of a supported language"""
        service = MultiLanguageSupport()
        result = await service.detect_language("¿Cómo funciona la autenticación?")
        assert result.detected_language == "es"
        assert result.confidence == 0.85
        assert result.is_supported is True
        assert result.needs_translation is True

    @pytest.mark.asyncio
    async def test_detect_unsupported_language(self, mock_detect_langs):
        """Test detection of an unsupported language"""
        mock_detect_langs.return_value[0].lang = "ur"
        service = MultiLanguageSupport()
        result = await service.detect_language("یہ ایک امتحان ہے")
        assert result.detected_language == "ur"
        assert result.is_supported is False
        assert result.needs_translation is False

    @pytest.mark.asyncio
    async def test_detection_failure_fallback(self):
        """Test fallback to English on detection failure"""
        with patch(
            "query_intelligence.services.language_detector.detect_langs",
            side_effect=LangDetectException(0, "Detection failed"),
        ):
            service = MultiLanguageSupport()
            result = await service.detect_language("short")
            assert result.detected_language == "en"
            assert result.confidence == 0.5
            assert result.is_supported is True
            assert result.needs_translation is False

    @pytest.mark.asyncio
    async def test_empty_query_detection(self):
        """Test language detection with an empty query"""
        service = MultiLanguageSupport()
        result = await service.detect_language("")
        assert result.detected_language == "en"
        assert result.confidence == 0.5

    @pytest.mark.asyncio
    async def test_no_language_detected(self, mock_detect_langs):
        """Test the scenario where no language is detected"""
        mock_detect_langs.return_value = []
        service = MultiLanguageSupport()
        result = await service.detect_language("... --- ...")
        assert result.detected_language == "en"
        assert result.confidence == 0.5
        assert result.is_supported is True
        assert result.needs_translation is False


class TestQueryTranslation:
    """Test query translation functionality"""

    @pytest.mark.asyncio
    async def test_successful_translation(self, mock_translator):
        """Test successful query translation"""
        service = MultiLanguageSupport()
        service.translator = mock_translator
        query = "¿Cómo funciona la función de autenticación?"
        result = await service.translate_query(query, "es")
        assert result.translated_query == "translated text"
        assert result.source_language == "es"
        assert result.confidence == 0.95

    @pytest.mark.asyncio
    async def test_translation_failure(self):
        """Test fallback behavior on translation failure"""
        service = MultiLanguageSupport()
        service.translator.translate = Mock(side_effect=Exception("API error"))
        query = "Eine Testanfrage"
        result = await service.translate_query(query, "de")
        assert result.translated_query == "Eine Testanfrage"
        assert result.confidence == 0.0

    @pytest.mark.asyncio
    async def test_code_term_preservation_during_translation(self, mock_translator):
        """Test that code terms are preserved during translation"""
        service = MultiLanguageSupport()
        service.translator = mock_translator
        query = "Comment fonctionne la fonction getUserData?"
        
        # Mock the translation result to include the placeholder
        mock_translator.translate.return_value.text = "How does the __CODE0__ function work?"

        result = await service.translate_query(query, "fr")

        assert "getUserData" in result.translated_query
        # Ensure the mock was called with the modified query
        mock_translator.translate.assert_called_with(
            "Comment fonctionne la fonction __CODE0__?", src="fr", dest="en"
        )


class TestCodeTermPreservation:
    """Test code term preservation during translation"""

    def test_preserve_camel_case(self):
        """Test preservation of camelCase terms"""
        service = MultiLanguageSupport()
        query = "Explain processApiRequest method"
        modified_query, preserved = service._preserve_code_terms(query)
        assert modified_query == "Explain __CODE0__ method"
        assert preserved["__CODE0__"] == "processApiRequest"

    def test_preserve_snake_case(self):
        """Test preservation of snake_case terms"""
        service = MultiLanguageSupport()
        query = "Find the user_id variable"
        modified_query, preserved = service._preserve_code_terms(query)
        assert modified_query == "Find the __CODE0__ variable"
        assert preserved["__CODE0__"] == "user_id"

    def test_preserve_quoted_strings(self):
        """Test preservation of quoted strings"""
        service = MultiLanguageSupport()
        query = "Debug the 'authentication failed' error"
        modified_query, preserved = service._preserve_code_terms(query)
        assert "__STRING0__" in modified_query
        assert preserved["__STRING0__"] == "'authentication failed'"

    def test_restore_code_terms(self):
        """Test accurate restoration of preserved terms"""
        service = MultiLanguageSupport()
        text = "How does __CODE0__ work with __STRING0__?"
        preserved = {
            "__CODE0__": "myFunction",
            "__STRING0__": "'some_value'",
        }
        restored = service._restore_code_terms(text, preserved)
        assert restored == "How does myFunction work with 'some_value'?"


class TestQueryEnhancement:
    """Test query enhancement with translations"""

    def test_enhance_spanish_query(self):
        """Test enhancing a Spanish query"""
        service = MultiLanguageSupport()
        query = "Cómo funciona la función de autenticación"
        enhanced = service.enhance_query_with_translations(query, "es")
        assert "OR function" in enhanced

    def test_no_enhancement_for_english_query(self):
        """Test that English queries are not enhanced"""
        service = MultiLanguageSupport()
        query = "How does the authentication function work"
        enhanced = service.enhance_query_with_translations(query, "en")
        assert query == enhanced

    def test_multiple_keyword_enhancement(self):
        """Test enhancement with multiple keywords"""
        service = MultiLanguageSupport()
        query = "Solucionar error en la clase de variables"
        enhanced = service.enhance_query_with_translations(query, "es")
        expected = "Solucionar error en la clase de variables OR class OR error OR variable"
        assert enhanced == expected


class TestUtilityFunctions:
    """Test utility functions and edge cases"""

    def test_get_language_specific_tips(self):
        """Test getting language-specific tips"""
        service = MultiLanguageSupport()
        tips = service.get_language_specific_tips("es")
        assert len(tips) > 0
        assert "Usa 'función' para buscar funciones" in tips

    def test_get_tips_for_unsupported_language(self):
        """Test getting tips for a language with no specific tips"""
        service = MultiLanguageSupport()
        tips = service.get_language_specific_tips("ur")
        assert len(tips) == 0

    def test_singleton_instance(self):
        """Test that get_language_support returns a singleton"""
        instance1 = get_language_support()
        instance2 = get_language_support()
        assert instance1 is instance2
