"""
Unit tests for ResponseGenerator service
"""
import pytest
from unittest.mock import Mock

from query_intelligence.services.response_generator import ResponseGenerator
from query_intelligence.models import (
    CodeChunk,
    QueryIntent,
    GeneratedResponse,
)


class TestResponseGenerator:
    """Test cases for ResponseGenerator service"""

    def setup_method(self):
        """Set up test fixtures"""
        self.generator = ResponseGenerator()

    def test_format_response_with_references_no_references(self):
        """Test formatting response without references"""
        response = GeneratedResponse(
            text="This is a test response",
            confidence=0.8,
            model_used="test-model",
            generation_time_ms=100.0
        )
        
        result = self.generator.format_response_with_references(response, [])
        assert result == "This is a test response"

    def test_format_response_with_references_with_references(self):
        """Test formatting response with references"""
        response = GeneratedResponse(
            text="This is a test response",
            confidence=0.8,
            model_used="test-model",
            generation_time_ms=100.0
        )
        
        references = [
            {
                'file_path': 'src/test.py',
                'start_line': 10,
                'end_line': 20,
                'relevance_score': 0.85
            },
            {
                'file_path': 'src/another.py',
                'start_line': 5,
                'end_line': 15,
                'relevance_score': 0.75
            }
        ]
        
        result = self.generator.format_response_with_references(response, references)
        
        assert "This is a test response" in result
        assert "Code References:" in result
        assert "`src/test.py:10-20`" in result
        assert "(relevance: 85.00%)" in result
        assert "`src/another.py:5-15`" in result
        assert "(relevance: 75.00%)" in result

    def test_generate_error_response_no_results(self):
        """Test generating error response for no results"""
        result = self.generator.generate_error_response("no_results", "test query")
        
        assert "test query" in result
        assert "couldn't find any relevant code" in result
        assert "Try rephrasing" in result

    def test_generate_error_response_with_details(self):
        """Test generating error response with details"""
        result = self.generator.generate_error_response(
            "analysis_error", "test query", "Connection timeout"
        )
        
        assert "encountered an error" in result
        assert "Details: Connection timeout" in result

    def test_generate_error_response_unknown_type(self):
        """Test generating error response for unknown error type"""
        result = self.generator.generate_error_response("unknown_error", "test query")
        
        assert "An unexpected error occurred" in result

    def test_generate_clarification_request_multiple_matches(self):
        """Test generating clarification request for multiple matches"""
        result = self.generator.generate_clarification_request(
            "test query", "multiple_matches", ["Option 1", "Option 2"]
        )
        
        assert "test query" in result
        assert "multiple possibilities" in result
        assert "1. Option 1" in result
        assert "2. Option 2" in result

    def test_generate_clarification_request_no_suggestions(self):
        """Test generating clarification request without suggestions"""
        result = self.generator.generate_clarification_request(
            "test query", "vague_intent", []
        )
        
        assert "test query" in result
        assert "not sure what you'd like to know" in result

    def test_enhance_response_with_examples_explain_intent(self):
        """Test enhancing response with examples for EXPLAIN intent"""
        response = "This is the base response"
        
        code_chunk = CodeChunk(
            file_path="src/test.py",
            start_line=10,
            end_line=20,
            content="def test_function():\n    return 'test'",
            language="python",
            similarity_score=0.9,
            recency_score=0.8,
            combined_score=0.85
        )
        
        result = self.generator.enhance_response_with_examples(
            response, [code_chunk], QueryIntent.EXPLAIN
        )
        
        assert "This is the base response" in result
        assert "Example from your codebase:" in result
        assert "```python" in result
        assert "def test_function():" in result
        assert "From: src/test.py:10" in result

    def test_enhance_response_with_examples_low_score(self):
        """Test enhancing response with low scoring code chunk"""
        response = "This is the base response"
        
        code_chunk = CodeChunk(
            file_path="src/test.py",
            start_line=10,
            end_line=20,
            content="def test_function():\n    return 'test'",
            language="python",
            similarity_score=0.5,
            recency_score=0.5,
            combined_score=0.5  # Low score
        )
        
        result = self.generator.enhance_response_with_examples(
            response, [code_chunk], QueryIntent.EXPLAIN
        )
        
        # Should return original response since score is too low
        assert result == response

    def test_enhance_response_with_examples_wrong_intent(self):
        """Test enhancing response with wrong intent type"""
        response = "This is the base response"
        
        code_chunk = CodeChunk(
            file_path="src/test.py",
            start_line=10,
            end_line=20,
            content="def test_function():\n    return 'test'",
            language="python",
            similarity_score=0.9,
            recency_score=0.8,
            combined_score=0.85
        )
        
        result = self.generator.enhance_response_with_examples(
            response, [code_chunk], QueryIntent.DEBUG  # Wrong intent
        )
        
        # Should return original response since intent is not EXPLAIN or FIND
        assert result == response

    def test_enhance_response_with_examples_long_content(self):
        """Test enhancing response with long code content"""
        response = "This is the base response"
        
        # Create content longer than 300 characters
        long_content = "def test_function():\n    " + "x" * 400 + "\n    return 'test'"
        
        code_chunk = CodeChunk(
            file_path="src/test.py",
            start_line=10,
            end_line=20,
            content=long_content,
            language="python",
            similarity_score=0.9,
            recency_score=0.8,
            combined_score=0.85
        )
        
        result = self.generator.enhance_response_with_examples(
            response, [code_chunk], QueryIntent.EXPLAIN
        )
        
        assert "This is the base response" in result
        assert "Example from your codebase:" in result
        assert "```python" in result
        assert "..." in result  # Should truncate long content

    def test_generate_summary_card(self):
        """Test generating summary card"""
        result = self.generator.generate_summary_card(
            "test query",
            "This is the answer to your question",
            QueryIntent.EXPLAIN,
            0.85,
            3
        )
        
        assert result["query"] == "test query"
        assert result["answer_preview"] == "This is the answer to your question"
        assert result["intent"] == "explain"
        assert result["confidence"] == 0.85
        assert result["reference_count"] == 3
        assert result["confidence_label"] == "High"
        assert result["intent_icon"] == "📚"

    def test_generate_summary_card_long_answer(self):
        """Test generating summary card with long answer"""
        long_answer = "This is a very long answer that should be truncated " * 10
        
        result = self.generator.generate_summary_card(
            "test query",
            long_answer,
            QueryIntent.FIND,
            0.95,
            1
        )
        
        assert len(result["answer_preview"]) <= 203  # 200 + "..."
        assert result["answer_preview"].endswith("...")

    def test_get_confidence_label_very_high(self):
        """Test confidence label for very high confidence"""
        result = self.generator._get_confidence_label(0.95)
        assert result == "Very High"

    def test_get_confidence_label_high(self):
        """Test confidence label for high confidence"""
        result = self.generator._get_confidence_label(0.8)
        assert result == "High"

    def test_get_confidence_label_medium(self):
        """Test confidence label for medium confidence"""
        result = self.generator._get_confidence_label(0.6)
        assert result == "Medium"

    def test_get_confidence_label_low(self):
        """Test confidence label for low confidence"""
        result = self.generator._get_confidence_label(0.3)
        assert result == "Low"

    def test_get_intent_icon_all_intents(self):
        """Test getting icons for all intent types"""
        expected_icons = {
            QueryIntent.EXPLAIN: "📚",
            QueryIntent.FIND: "🔍",
            QueryIntent.DEBUG: "🐛",
            QueryIntent.REFACTOR: "♻️",
            QueryIntent.ANALYZE: "📊",
            QueryIntent.COMPARE: "⚖️",
            QueryIntent.UNKNOWN: "❓",
        }
        
        for intent, expected_icon in expected_icons.items():
            result = self.generator._get_intent_icon(intent)
            assert result == expected_icon