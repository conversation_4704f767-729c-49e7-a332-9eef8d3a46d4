import pytest
import pytest
from unittest.mock import Mock, patch, AsyncMock
from query_intelligence.services.query_processor import QueryProcessor
from query_intelligence.models import (
    QueryRequest,
    QueryResult,
    QueryIntent,
    CodeReference,
    QueryContext,
    IntentAnalysis,
    CodeChunk,
    GeneratedResponse,
)
from query_intelligence.services.language_detector import (
    LanguageDetectionResult,
    TranslationResult,
)
from query_intelligence.utils.circuit_breaker import CircuitBreakerError


@pytest.fixture
def mock_semantic_search():
    """Mock SemanticSearchService"""
    mock = AsyncMock()
    mock.generate_embedding.return_value = [0.1] * 768
    mock.search.return_value = Mock(chunks=[])
    return mock


@pytest.fixture
def mock_llm_service():
    """Mock LLMService"""
    mock = AsyncMock()
    mock.generate_response.return_value = GeneratedResponse(
        text="Test response", confidence=0.9, model_used="test_model"
    )
    mock.generate_json_response.return_value = {}
    return mock


@pytest.fixture
def mock_cache_manager():
    """Mock CacheManager"""
    mock = AsyncMock()
    mock.get_cached_query_result.return_value = None
    return mock


@pytest.fixture
def mock_language_support():
    """Mock MultiLanguageSupport"""
    mock = AsyncMock()
    mock.detect_language.return_value = LanguageDetectionResult(
        detected_language="en",
        confidence=0.99,
        is_supported=True,
        needs_translation=False,
    )
    mock.translate_query.return_value = TranslationResult(
        original_query="test query",
        translated_query="test query",
        source_language="en",
    )
    return mock


@pytest.fixture
def mock_pattern_mining_client():
    """Mock PatternMiningClient"""
    mock = AsyncMock()
    mock.detect_patterns.return_value = {"patterns": []}
    mock.get_recommendations.return_value = []
    mock.classify_patterns.return_value = {"classified_patterns": []}
    return mock


@pytest.fixture
def query_processor(
    mock_semantic_search,
    mock_llm_service,
    mock_cache_manager,
    mock_language_support,
    mock_pattern_mining_client,
):
    """Fixture for QueryProcessor with mocked dependencies"""
    with patch(
        "query_intelligence.services.query_processor.SemanticSearchService",
        return_value=mock_semantic_search,
    ), patch(
        "query_intelligence.services.query_processor.LLMService",
        return_value=mock_llm_service,
    ), patch(
        "query_intelligence.services.query_processor.get_cache_manager",
        return_value=mock_cache_manager,
    ), patch(
        "query_intelligence.services.query_processor.get_language_support",
        return_value=mock_language_support,
    ), patch(
        "query_intelligence.services.query_processor.get_pattern_mining_client",
        return_value=mock_pattern_mining_client,
    ):
        processor = QueryProcessor()
        # Mock redis client on the instance
        processor.redis_client = AsyncMock()
        return processor


class TestQueryProcessor:
    """Test suite for the QueryProcessor"""

    @pytest.mark.asyncio
    async def test_process_query_simple(self, query_processor, mock_llm_service):
        """Test a simple query processing flow"""
        request = QueryRequest(query="test query", repository_id="repo1")
        result = await query_processor.process_query(request)

        assert result.answer == "Test response"
        assert result.confidence == 0.9
        mock_llm_service.generate_response.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_query_with_translation(
        self, query_processor, mock_language_support
    ):
        """Test query processing with language translation"""
        mock_language_support.detect_language.return_value.needs_translation = True
        mock_language_support.translate_query.return_value.translated_query = (
            "translated query"
        )

        request = QueryRequest(query="consulta de prueba", repository_id="repo1")
        await query_processor.process_query(request)

        mock_language_support.detect_language.assert_called_with("consulta de prueba")
        mock_language_support.translate_query.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_query_cache_hit(self, query_processor, mock_cache_manager):
        """Test that a cached result is returned"""
        cached_result = QueryResult(
            answer="cached answer",
            confidence=0.99,
            intent=QueryIntent.EXPLAIN,
            execution_time_ms=123.0,
            references=[],
            follow_up_questions=[],
        )
        mock_cache_manager.get_cached_query_result.return_value = cached_result

        request = QueryRequest(query="cached query", repository_id="repo1")
        result = await query_processor.process_query(request)

        assert result.answer == "cached answer"
        mock_cache_manager.get_cached_query_result.assert_called_once()

    @pytest.mark.asyncio
    async def test_analyze_intent_fallback(self, query_processor, mock_llm_service):
        """Test the fallback intent analysis"""
        mock_llm_service.generate_json_response.side_effect = CircuitBreakerError(
            "LLM unavailable"
        )
        with patch.object(
            query_processor, "_generate_follow_ups", return_value=[]
        ) as mock_follow_ups:
            request = QueryRequest(
                query="how to fix this error?", repository_id="repo1"
            )
            result = await query_processor.process_query(request)

            assert result.intent == QueryIntent.DEBUG
            assert result.metadata["intent_confidence"] == 0.5
            mock_follow_ups.assert_called()

    @pytest.mark.asyncio
    async def test_rerank_chunks(self, query_processor, mock_semantic_search):
        """Test the reranking of code chunks"""
        chunks = [
            CodeChunk(
                content="class MyClass: pass",
                file_path="a.py",
                start_line=1,
                end_line=1,
                language="python",
                similarity_score=0.8,
                recency_score=0.5,
            ),
            CodeChunk(
                content="def my_func(): return 1",
                file_path="b.py",
                start_line=1,
                end_line=1,
                language="python",
                similarity_score=0.9,
                recency_score=0.6,
            ),
        ]
        mock_semantic_search.search.return_value = Mock(chunks=chunks)
        request = QueryRequest(query="explain my_func", repository_id="repo1")
        await query_processor.process_query(request)
        # Basic assertion to ensure reranking was called and didn't crash
        assert mock_semantic_search.search.called

    @pytest.mark.asyncio
    async def test_format_snippet(self, query_processor):
        """Test the _format_snippet method"""
        long_content = "\n".join([f"line {i}" for i in range(20)])
        snippet = query_processor._format_snippet(long_content)
        assert "..." in snippet
        assert len(snippet.split("\n")) == 11  # 5 head + 1 ... + 5 tail

        short_content = "line 1\nline 2"
        snippet = query_processor._format_snippet(short_content)
        assert snippet == short_content

    @pytest.mark.asyncio
    async def test_extract_chunk_features(self, query_processor):
        """Test the _extract_chunk_features method"""
        intent = IntentAnalysis(
            primary_intent=QueryIntent.DEBUG,
            code_elements=["MyClass"],
            confidence=0.9,
        )
        chunk = CodeChunk(
            content="error in MyClass",
            file_path="a.py",
            start_line=1,
            end_line=1,
            language="python",
            similarity_score=0.8,
            recency_score=0.5,
        )
        features = query_processor._extract_chunk_features(chunk, intent)
        assert len(features) == 6
        assert features[0] == 1.0  # "error" in content

    @pytest.mark.asyncio
    async def test_extract_query_features(self, query_processor):
        """Test the _extract_query_features method"""
        intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN,
            confidence=0.9,
            context_depth="deep",
        )
        features = query_processor._extract_query_features("how does it work?", intent)
        assert len(features) == 6
        assert features[0] == 1.0  # "how" in query

    @pytest.mark.asyncio
    async def test_pattern_analysis(
        self, query_processor, mock_pattern_mining_client
    ):
        """Test the pattern analysis logic"""
        intent = IntentAnalysis(
            primary_intent=QueryIntent.REFACTOR, confidence=0.9
        )
        with patch.object(
            query_processor, "_should_analyze_patterns", return_value=True
        ), patch.object(
            query_processor, "_analyze_intent", return_value=intent
        ):
            request = QueryRequest(query="refactor this code", repository_id="repo1")
            await query_processor.process_query(request)
            mock_pattern_mining_client.detect_patterns.assert_called_once()

    def test_build_search_filters(self, query_processor):
        """Test the _build_search_filters method"""
        intent = IntentAnalysis(
            primary_intent=QueryIntent.DEBUG,
            scope="file",
            code_elements=["a.py"],
            confidence=0.9,
        )
        context = QueryContext(repository_id="repo1", user_id="test_user")
        filters = query_processor._build_search_filters(intent, context)
        assert filters["file_pattern"] == "a.py"
        assert filters["prefer_error_handling"] is True

    def test_fallback_follow_ups(self, query_processor):
        """Test the _fallback_follow_ups method"""
        follow_ups = query_processor._fallback_follow_ups(
            "how to fix this error?", "This is a test response."
        )
        assert len(follow_ups) == 3
        assert "error" in follow_ups[0]
