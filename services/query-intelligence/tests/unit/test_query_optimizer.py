"""
Unit tests for QueryOptimizer service
"""
import pytest
from unittest.mock import Mock

from query_intelligence.services.query_optimizer import (
    QueryOptimizer,
    QueryOptimization,
    OptimizationHint,
    get_query_optimizer,
)
from query_intelligence.models import QueryIntent, IntentAnalysis


class TestQueryOptimizer:
    """Test cases for QueryOptimizer service"""

    def setup_method(self):
        """Set up test fixtures"""
        self.optimizer = QueryOptimizer()

    def test_init_default_values(self):
        """Test optimizer initialization with default values"""
        assert self.optimizer.min_query_length == 10
        assert self.optimizer.max_query_length == 500
        assert self.optimizer.optimal_length_range == (20, 200)

    def test_analyze_query_basic(self):
        """Test basic query analysis"""
        query = "How does authentication work?"
        intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN,
            code_elements=["authentication"],
            scope="repository",
            context_depth="normal",
            confidence=0.8
        )
        
        optimizations = self.optimizer.analyze_query(query, intent)
        
        assert isinstance(optimizations, list)
        assert all(isinstance(opt, QueryOptimization) for opt in optimizations)

    def test_analyze_query_with_previous_results(self):
        """Test query analysis with previous results"""
        query = "authentication"
        intent = IntentAnalysis(
            primary_intent=QueryIntent.FIND,
            code_elements=["authentication"],
            scope="repository",
            context_depth="normal",
            confidence=0.8
        )
        
        previous_results = {
            "confidence": 0.3,
            "total_results": 1
        }
        
        optimizations = self.optimizer.analyze_query(query, intent, previous_results)
        
        assert isinstance(optimizations, list)
        # Should have more optimizations due to low confidence
        assert len(optimizations) > 0

    def test_check_query_length_too_short(self):
        """Test query length check for short queries"""
        query = "auth"  # Too short
        
        optimizations = self.optimizer._check_query_length(query)
        
        assert len(optimizations) > 0
        assert any(opt.hint == OptimizationHint.ADD_CONTEXT for opt in optimizations)

    def test_check_query_length_too_long(self):
        """Test query length check for long queries"""
        query = "a" * 600  # Too long
        
        optimizations = self.optimizer._check_query_length(query)
        
        assert len(optimizations) > 0
        assert any(opt.hint == OptimizationHint.SPLIT_QUERY for opt in optimizations)

    def test_check_query_length_optimal(self):
        """Test query length check for optimal length"""
        query = "How does the authentication system work in this project?"
        
        optimizations = self.optimizer._check_query_length(query)
        
        assert len(optimizations) == 0  # No optimizations needed

    def test_check_specificity_vague_query(self):
        """Test specificity check for vague queries"""
        query = "How does this work?"
        intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN,
            code_elements=[],
            scope="repository",
            context_depth="normal",
            confidence=0.4
        )
        
        optimizations = self.optimizer._check_specificity(query, intent)
        
        assert len(optimizations) > 0
        assert any(opt.hint == OptimizationHint.BE_SPECIFIC for opt in optimizations)

    def test_check_specificity_specific_query(self):
        """Test specificity check for specific queries"""
        query = "How does the JWT authentication middleware validate tokens?"
        intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN,
            code_elements=["JWT", "authentication", "middleware", "tokens"],
            scope="repository",
            context_depth="normal",
            confidence=0.9
        )
        
        optimizations = self.optimizer._check_specificity(query, intent)
        
        assert len(optimizations) == 0  # No optimizations needed

    def test_check_context_explain_intent(self):
        """Test context check for explain intent"""
        query = "authentication"
        intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN,
            code_elements=["authentication"],
            scope="repository",
            context_depth="normal",
            confidence=0.8
        )
        
        optimizations = self.optimizer._check_context(query, intent)
        
        assert len(optimizations) > 0
        assert any(opt.hint == OptimizationHint.ADD_CONTEXT for opt in optimizations)

    def test_check_context_find_intent(self):
        """Test context check for find intent"""
        query = "functions"
        intent = IntentAnalysis(
            primary_intent=QueryIntent.FIND,
            code_elements=["functions"],
            scope="repository",
            context_depth="normal",
            confidence=0.8
        )
        
        optimizations = self.optimizer._check_context(query, intent)
        
        assert len(optimizations) > 0
        assert any(opt.hint == OptimizationHint.USE_KEYWORDS for opt in optimizations)

    def test_suggest_filters_file_types(self):
        """Test filter suggestions for file types"""
        query = "authentication functions"
        intent = IntentAnalysis(
            primary_intent=QueryIntent.FIND,
            code_elements=["authentication", "functions"],
            scope="repository",
            context_depth="normal",
            confidence=0.8
        )
        
        optimizations = self.optimizer._suggest_filters(query, intent)
        
        assert len(optimizations) > 0
        assert any(opt.hint == OptimizationHint.USE_FILTERS for opt in optimizations)

    def test_suggest_filters_language_specific(self):
        """Test filter suggestions for language-specific queries"""
        query = "python authentication classes"
        intent = IntentAnalysis(
            primary_intent=QueryIntent.FIND,
            code_elements=["python", "authentication", "classes"],
            scope="repository",
            context_depth="normal",
            confidence=0.8
        )
        
        optimizations = self.optimizer._suggest_filters(query, intent)
        
        assert len(optimizations) > 0
        filter_opts = [opt for opt in optimizations if opt.hint == OptimizationHint.USE_FILTERS]
        assert any("python" in opt.description.lower() for opt in filter_opts)

    def test_check_query_complexity_simple(self):
        """Test complexity check for simple queries"""
        query = "How does authentication work?"
        
        optimizations = self.optimizer._check_query_complexity(query)
        
        assert len(optimizations) == 0  # No split needed

    def test_check_query_complexity_complex(self):
        """Test complexity check for complex queries"""
        query = "How does authentication work and what are the security implications and how do I implement rate limiting?"
        
        optimizations = self.optimizer._check_query_complexity(query)
        
        assert len(optimizations) > 0
        assert any(opt.hint == OptimizationHint.SPLIT_QUERY for opt in optimizations)

    def test_optimize_from_results_low_confidence(self):
        """Test optimization from low confidence results"""
        query = "auth"
        previous_results = {
            "confidence": 0.2,
            "total_results": 5
        }
        
        optimizations = self.optimizer._optimize_from_results(query, previous_results)
        
        assert len(optimizations) > 0
        assert any(opt.hint in [OptimizationHint.BE_SPECIFIC, OptimizationHint.ADD_CONTEXT] for opt in optimizations)

    def test_optimize_from_results_too_many_results(self):
        """Test optimization from too many results"""
        query = "function"
        previous_results = {
            "confidence": 0.8,
            "total_results": 200
        }
        
        optimizations = self.optimizer._optimize_from_results(query, previous_results)
        
        assert len(optimizations) > 0
        assert any(opt.hint == OptimizationHint.USE_FILTERS for opt in optimizations)

    def test_optimize_from_results_no_results(self):
        """Test optimization from no results"""
        query = "nonexistent functionality"
        previous_results = {
            "confidence": 0.9,
            "total_results": 0
        }
        
        optimizations = self.optimizer._optimize_from_results(query, previous_results)
        
        assert len(optimizations) > 0
        assert any(opt.hint == OptimizationHint.USE_KEYWORDS for opt in optimizations)

    def test_optimize_from_results_good_results(self):
        """Test optimization from good results"""
        query = "authentication middleware"
        previous_results = {
            "confidence": 0.9,
            "total_results": 3
        }
        
        optimizations = self.optimizer._optimize_from_results(query, previous_results)
        
        assert len(optimizations) == 0  # No optimizations needed

    def test_get_query_quality_score_high(self):
        """Test quality score calculation for high quality query"""
        query = "How does the JWT authentication middleware validate tokens in the auth.py file?"
        intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN,
            code_elements=["JWT", "authentication", "middleware", "tokens"],
            scope="file",
            context_depth="normal",
            confidence=0.9
        )
        
        score = self.optimizer.get_query_quality_score(query, intent)
        
        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
        assert score > 0.7  # Should be high quality

    def test_get_query_quality_score_low(self):
        """Test quality score calculation for low quality query"""
        query = "this"
        intent = IntentAnalysis(
            primary_intent=QueryIntent.UNKNOWN,
            code_elements=[],
            scope="repository",
            context_depth="normal",
            confidence=0.2
        )
        
        score = self.optimizer.get_query_quality_score(query, intent)
        
        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
        assert score < 0.5  # Should be low quality

    def test_generate_optimized_query_be_specific(self):
        """Test optimized query generation for specificity"""
        query = "this function"
        optimizations = [
            QueryOptimization(
                hint=OptimizationHint.BE_SPECIFIC,
                description="Be more specific about what you're looking for",
                example="authentication function",
                confidence=0.8
            )
        ]
        
        optimized = self.optimizer.generate_optimized_query(query, optimizations)
        
        assert isinstance(optimized, str)
        assert len(optimized) > len(query)

    def test_generate_optimized_query_add_context(self):
        """Test optimized query generation for adding context"""
        query = "login"
        optimizations = [
            QueryOptimization(
                hint=OptimizationHint.ADD_CONTEXT,
                description="Add more context about what aspect you're interested in",
                example="How does the login process work?",
                confidence=0.8
            )
        ]
        
        optimized = self.optimizer.generate_optimized_query(query, optimizations)
        
        assert isinstance(optimized, str)
        assert "how" in optimized.lower() or "what" in optimized.lower()

    def test_generate_optimized_query_use_keywords(self):
        """Test optimized query generation for using keywords"""
        query = "the system"
        optimizations = [
            QueryOptimization(
                hint=OptimizationHint.USE_KEYWORDS,
                description="Use specific keywords related to your search",
                example="authentication system",
                confidence=0.8
            )
        ]
        
        optimized = self.optimizer.generate_optimized_query(query, optimizations)
        
        assert isinstance(optimized, str)
        assert optimized != query

    def test_generate_optimized_query_split_query(self):
        """Test optimized query generation for splitting complex queries"""
        query = "How does authentication work and what are the security implications?"
        optimizations = [
            QueryOptimization(
                hint=OptimizationHint.SPLIT_QUERY,
                description="Split complex queries into simpler parts",
                example="First ask: How does authentication work?",
                confidence=0.8
            )
        ]
        
        optimized = self.optimizer.generate_optimized_query(query, optimizations)
        
        assert isinstance(optimized, str)
        assert len(optimized) < len(query)

    def test_generate_optimized_query_no_optimizations(self):
        """Test optimized query generation with no optimizations"""
        query = "How does authentication work?"
        optimizations = []
        
        optimized = self.optimizer.generate_optimized_query(query, optimizations)
        
        assert optimized == query

    def test_calculate_length_score_optimal(self):
        """Test length score calculation for optimal length"""
        query = "How does the authentication system work?"
        
        score = self.optimizer._calculate_length_score(query)
        
        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
        assert score > 0.8  # Should be high for optimal length

    def test_calculate_length_score_too_short(self):
        """Test length score calculation for too short query"""
        query = "auth"
        
        score = self.optimizer._calculate_length_score(query)
        
        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
        assert score < 0.5  # Should be low for too short

    def test_calculate_length_score_too_long(self):
        """Test length score calculation for too long query"""
        query = "a" * 600
        
        score = self.optimizer._calculate_length_score(query)
        
        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
        assert score < 0.5  # Should be low for too long

    def test_calculate_specificity_score_high(self):
        """Test specificity score calculation for specific query"""
        query = "JWT authentication middleware validation"
        intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN,
            code_elements=["JWT", "authentication", "middleware", "validation"],
            scope="repository",
            context_depth="normal",
            confidence=0.9
        )
        
        score = self.optimizer._calculate_specificity_score(query, intent)
        
        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
        assert score > 0.7  # Should be high for specific query

    def test_calculate_specificity_score_low(self):
        """Test specificity score calculation for vague query"""
        query = "how does this work"
        intent = IntentAnalysis(
            primary_intent=QueryIntent.UNKNOWN,
            code_elements=[],
            scope="repository",
            context_depth="normal",
            confidence=0.3
        )
        
        score = self.optimizer._calculate_specificity_score(query, intent)
        
        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
        assert score < 0.5  # Should be low for vague query

    def test_calculate_complexity_score_simple(self):
        """Test complexity score calculation for simple query"""
        query = "How does authentication work?"
        
        score = self.optimizer._calculate_complexity_score(query)
        
        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
        assert score > 0.8  # Should be high for simple query

    def test_calculate_complexity_score_complex(self):
        """Test complexity score calculation for complex query"""
        query = "How does authentication work and what are the security implications and how do I implement rate limiting and what about caching?"
        
        score = self.optimizer._calculate_complexity_score(query)
        
        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
        assert score < 0.5  # Should be low for complex query

    def test_get_query_optimizer_factory(self):
        """Test query optimizer factory function"""
        optimizer = get_query_optimizer()
        
        assert isinstance(optimizer, QueryOptimizer)
        assert optimizer.min_query_length == 10
        assert optimizer.max_query_length == 500

    def test_optimization_hint_enum_values(self):
        """Test optimization hint enum values"""
        expected_hints = [
            "add_context", "be_specific", "use_filters", "split_query", 
            "use_keywords", "specify_language", "add_timeframe", "use_examples"
        ]
        
        actual_hints = [hint.value for hint in OptimizationHint]
        
        assert set(actual_hints) == set(expected_hints)

    def test_query_optimization_dataclass(self):
        """Test QueryOptimization dataclass"""
        opt = QueryOptimization(
            hint=OptimizationHint.BE_SPECIFIC,
            description="Be more specific",
            example="authentication function",
            confidence=0.8,
            impact="high"
        )
        
        assert opt.hint == OptimizationHint.BE_SPECIFIC
        assert opt.description == "Be more specific"
        assert opt.example == "authentication function"
        assert opt.confidence == 0.8
        assert opt.impact == "high"

    def test_query_optimization_dataclass_defaults(self):
        """Test QueryOptimization dataclass with defaults"""
        opt = QueryOptimization(
            hint=OptimizationHint.BE_SPECIFIC,
            description="Be more specific"
        )
        
        assert opt.hint == OptimizationHint.BE_SPECIFIC
        assert opt.description == "Be more specific"
        assert opt.example is None
        assert opt.confidence == 0.8
        assert opt.impact == "medium"