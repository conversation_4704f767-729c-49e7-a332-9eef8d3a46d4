"""
Unit tests for circuit breaker functionality
"""

import pytest
import asyncio

from query_intelligence.utils.circuit_breaker import (
    CircuitBreaker,
    CircuitState,
    CircuitBreakerError,
    circuit_breaker,
)


@pytest.mark.asyncio
async def test_circuit_breaker_closed_state():
    """Test circuit breaker in closed state allows calls"""
    cb = CircuitBreaker(name="test_cb", failure_threshold=3, recovery_timeout=60)

    # Successful call
    async def success_func():
        return "success"

    result = await cb.call(success_func)
    assert result == "success"
    assert cb.state == CircuitState.CLOSED
    assert cb._failure_count == 0


@pytest.mark.asyncio
async def test_circuit_breaker_opens_after_failures():
    """Test circuit breaker opens after threshold failures"""
    cb = CircuitBreaker(name="test_cb", failure_threshold=3, recovery_timeout=60)

    # Function that always fails
    async def failing_func():
        raise Exception("Test failure")

    # Should fail 3 times and open
    for i in range(3):
        with pytest.raises(Exception):
            await cb.call(failing_func)

    assert cb.state == CircuitState.OPEN
    assert cb._failure_count == 3

    # Next call should be rejected
    with pytest.raises(CircuitBreakerError):
        await cb.call(failing_func)


@pytest.mark.asyncio
async def test_circuit_breaker_half_open_recovery():
    """Test circuit breaker recovery through half-open state"""
    cb = CircuitBreaker(
        name="test_cb",
        failure_threshold=2,
        recovery_timeout=0.1,  # 100ms for fast testing
        success_threshold=2,
    )

    # Open the circuit
    async def failing_func():
        raise Exception("Test failure")

    for _ in range(2):
        with pytest.raises(Exception):
            await cb.call(failing_func)

    assert cb.state == CircuitState.OPEN

    # Wait for recovery timeout
    await asyncio.sleep(0.15)

    # Check if it transitions to half-open
    assert not cb.is_open  # This triggers transition
    assert cb.state == CircuitState.HALF_OPEN

    # Successful calls should close the circuit
    async def success_func():
        return "success"

    # Need 2 successes to close
    result1 = await cb.call(success_func)
    assert result1 == "success"
    assert cb.state == CircuitState.HALF_OPEN

    result2 = await cb.call(success_func)
    assert result2 == "success"
    assert cb.state == CircuitState.CLOSED


@pytest.mark.asyncio
async def test_circuit_breaker_half_open_failure():
    """Test circuit breaker returns to open on failure in half-open"""
    cb = CircuitBreaker(
        name="test_cb", failure_threshold=2, recovery_timeout=0.1, success_threshold=2
    )

    # Open the circuit
    async def failing_func():
        raise Exception("Test failure")

    for _ in range(2):
        with pytest.raises(Exception):
            await cb.call(failing_func)

    # Wait for recovery
    await asyncio.sleep(0.15)

    # Force transition to half-open
    _ = cb.is_open
    assert cb.state == CircuitState.HALF_OPEN

    # Failure in half-open should reopen
    with pytest.raises(Exception):
        await cb.call(failing_func)

    assert cb.state == CircuitState.OPEN


@pytest.mark.asyncio
async def test_circuit_breaker_decorator():
    """Test circuit breaker decorator"""
    call_count = 0

    @circuit_breaker(name="test_decorated", failure_threshold=2, recovery_timeout=60)
    async def decorated_func(should_fail=False):
        nonlocal call_count
        call_count += 1
        if should_fail:
            raise Exception("Decorated failure")
        return f"success_{call_count}"

    # Successful calls
    result = await decorated_func()
    assert result == "success_1"

    # Fail twice to open circuit
    for _ in range(2):
        with pytest.raises(Exception):
            await decorated_func(should_fail=True)

    # Circuit should be open
    with pytest.raises(CircuitBreakerError):
        await decorated_func()

    # Check that decorator attached the breaker
    assert hasattr(decorated_func, "circuit_breaker")
    assert decorated_func.circuit_breaker.state == CircuitState.OPEN


@pytest.mark.asyncio
async def test_circuit_breaker_specific_exception():
    """Test circuit breaker only catches specific exceptions"""
    cb = CircuitBreaker(
        name="test_cb", failure_threshold=2, expected_exception=ValueError
    )

    # Other exceptions should pass through
    async def other_exception():
        raise TypeError("Wrong type")

    with pytest.raises(TypeError):
        await cb.call(other_exception)

    # Failure count should not increase
    assert cb._failure_count == 0

    # Expected exception should count
    async def expected_exception():
        raise ValueError("Expected error")

    with pytest.raises(ValueError):
        await cb.call(expected_exception)

    assert cb._failure_count == 1


def test_circuit_breaker_status():
    """Test circuit breaker status reporting"""
    cb = CircuitBreaker(name="test_status", failure_threshold=3)

    status = cb.get_status()
    assert status["name"] == "test_status"
    assert status["state"] == "closed"
    assert status["failure_count"] == 0
    assert status["success_count"] == 0
    assert status["last_failure_time"] is None


@pytest.mark.asyncio
async def test_circuit_breaker_concurrent_calls():
    """Test circuit breaker handles concurrent calls safely"""
    cb = CircuitBreaker(
        name="test_concurrent", failure_threshold=5, recovery_timeout=60
    )

    async def concurrent_func(idx):
        await asyncio.sleep(0.01)  # Small delay
        return f"result_{idx}"

    # Launch multiple concurrent calls
    tasks = [cb.call(concurrent_func, i) for i in range(10)]
    results = await asyncio.gather(*tasks)

    assert len(results) == 10
    assert all(r.startswith("result_") for r in results)
    assert cb.state == CircuitState.CLOSED
