"""
Unit tests for pattern mining service integration
"""

import pytest
from unittest.mock import <PERSON><PERSON>ock, AsyncMock

from query_intelligence.services.query_processor import QueryProcessor
from query_intelligence.models import (
    QueryIntent,
    IntentAnalysis,
    CodeChunk,
    QueryContext,
)
from query_intelligence.clients.pattern_mining import PatternMining<PERSON>lient


@pytest.mark.asyncio
async def test_pattern_analysis_for_refactor_intent():
    """Test pattern analysis is triggered for refactor intent"""
    # Create query processor
    processor = QueryProcessor()

    # Mock the pattern mining client
    mock_pattern_client = MagicMock(spec=PatternMiningClient)
    processor.pattern_mining_client = mock_pattern_client

    # Mock pattern detection response
    mock_pattern_client.detect_patterns = AsyncMock(
        return_value={
            "patterns": [
                {"type": "singleton", "confidence": 0.9},
                {"type": "factory", "confidence": 0.85},
            ],
            "quality_score": 7.5,
            "anti_patterns": [],
        }
    )

    mock_pattern_client.get_recommendations = AsyncMock(
        return_value=[
            {
                "pattern_name": "Dependency Injection",
                "reason": "Improve testability and reduce coupling",
            }
        ]
    )

    mock_pattern_client.classify_patterns = AsyncMock(
        return_value={
            "classified_patterns": [
                {"type": "singleton", "category": "creational"},
                {"type": "factory", "category": "creational"},
            ]
        }
    )

    # Create test data
    intent = IntentAnalysis(
        primary_intent=QueryIntent.REFACTOR,
        confidence=0.9,
        code_elements=["UserService", "DatabaseConnection"],
    )

    code_chunks = [
        CodeChunk(
            chunk_id="1",
            file_path="services/user.py",
            start_line=10,
            end_line=50,
            content="class UserService:\n    def __init__(self):\n        self.db = DatabaseConnection()",
            language="python",
            semantic_score=0.9,
            keyword_score=0.8,
            combined_score=0.85,
        )
    ]

    context = QueryContext(
        session_id="test-session", repository_id="test-repo", user_id="test-user"
    )

    # Test pattern analysis
    assert processor._should_analyze_patterns(intent) is True

    # Analyze patterns
    pattern_insights = await processor._analyze_patterns(code_chunks, intent, context)

    # Verify results
    assert pattern_insights is not None
    assert pattern_insights["patterns_detected"] == 2
    assert pattern_insights["pattern_types"] == ["singleton", "factory"]
    assert pattern_insights["quality_score"] == 7.5
    assert len(pattern_insights["recommendations"]) == 1
    assert (
        pattern_insights["recommendations"][0]["pattern_name"] == "Dependency Injection"
    )

    # Verify API calls
    mock_pattern_client.detect_patterns.assert_called_once()
    mock_pattern_client.get_recommendations.assert_called_once()
    mock_pattern_client.classify_patterns.assert_called_once()


@pytest.mark.asyncio
async def test_pattern_analysis_not_triggered_for_find_intent():
    """Test pattern analysis is not triggered for find intent"""
    processor = QueryProcessor()

    intent = IntentAnalysis(
        primary_intent=QueryIntent.FIND, confidence=0.9, code_elements=["login"]
    )

    assert processor._should_analyze_patterns(intent) is False


@pytest.mark.asyncio
async def test_pattern_analysis_circuit_breaker_handling():
    """Test handling when pattern mining service is down"""
    from query_intelligence.utils.circuit_breaker import CircuitBreakerError

    processor = QueryProcessor()

    # Mock circuit breaker error
    mock_pattern_client = MagicMock(spec=PatternMiningClient)
    mock_pattern_client.detect_patterns = AsyncMock(
        side_effect=CircuitBreakerError("Pattern mining service unavailable")
    )
    processor.pattern_mining_client = mock_pattern_client

    intent = IntentAnalysis(primary_intent=QueryIntent.ANALYZE, confidence=0.9)

    code_chunks = [
        CodeChunk(
            chunk_id="1",
            file_path="test.py",
            start_line=1,
            end_line=10,
            content="def test(): pass",
            language="python",
            semantic_score=0.9,
            keyword_score=0.8,
            combined_score=0.85,
        )
    ]

    context = QueryContext(session_id="test", repository_id="test", user_id="test")

    # Should return None on circuit breaker error
    result = await processor._analyze_patterns(code_chunks, intent, context)
    assert result is None


@pytest.mark.asyncio
async def test_pattern_insights_in_llm_prompt():
    """Test pattern insights are included in LLM prompt"""
    from query_intelligence.services.llm_service_v2 import LLMServiceV2

    llm_service = LLMServiceV2()

    # Test pattern insights formatting
    pattern_insights = {
        "patterns_detected": 3,
        "pattern_types": ["singleton", "factory", "observer"],
        "quality_score": 8.2,
        "anti_patterns_found": ["god_object"],
        "recommendations": [
            {
                "pattern_name": "Strategy Pattern",
                "reason": "Replace conditional logic with polymorphism",
            }
        ],
    }

    formatted = llm_service._format_pattern_insights(pattern_insights)

    assert "Detected 3 coding patterns" in formatted
    assert "singleton, factory, observer" in formatted
    assert "Code quality score: 8.20/10" in formatted
    assert "Anti-patterns detected: god_object" in formatted
    assert "Strategy Pattern" in formatted
    assert "Replace conditional logic with polymorphism" in formatted


@pytest.mark.asyncio
async def test_empty_pattern_insights_handling():
    """Test handling when no patterns are detected"""
    processor = QueryProcessor()

    mock_pattern_client = MagicMock(spec=PatternMiningClient)
    mock_pattern_client.detect_patterns = AsyncMock(
        return_value={"patterns": [], "quality_score": None}
    )
    processor.pattern_mining_client = mock_pattern_client

    intent = IntentAnalysis(primary_intent=QueryIntent.ANALYZE, confidence=0.9)

    code_chunks = []
    context = QueryContext(session_id="test", repository_id="test", user_id="test")

    result = await processor._analyze_patterns(code_chunks, intent, context)
    assert result is None  # No patterns means None result
