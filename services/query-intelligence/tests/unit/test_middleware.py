import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta, timezone
from jose import jwt
from fastapi import HTTPException
from fastapi.security import HTTPAuthorizationCredentials

from query_intelligence.middleware.auth import J<PERSON><PERSON><PERSON>, get_current_user, require_role
from query_intelligence.middleware.rate_limit import RateLimiter


class TestJWTAuth:

    def test_create_access_token(self):
        auth = JWTAuth()
        data = {"sub": "user-123", "email": "<EMAIL>"}

        token = auth.create_access_token(data)

        # Verify token can be decoded
        decoded = jwt.decode(token, auth.secret_key, algorithms=[auth.algorithm])
        assert decoded["sub"] == "user-123"
        assert decoded["email"] == "<EMAIL>"
        assert "exp" in decoded

    def test_create_access_token_with_custom_expiry(self):
        auth = JWTAuth()
        data = {"sub": "user-123"}
        expires_delta = timedelta(hours=2)

        token = auth.create_access_token(data, expires_delta)

        decoded = jwt.decode(token, auth.secret_key, algorithms=[auth.algorithm])
        exp_time = datetime.fromtimestamp(decoded["exp"], tz=timezone.utc)
        now = datetime.now(timezone.utc)

        # Check expiry is approximately 2 hours from now
        time_diff_minutes = (exp_time - now).total_seconds() / 60
        assert 110 < time_diff_minutes < 130

    def test_verify_token_success(self):
        auth = JWTAuth()
        data = {"sub": "user-123", "roles": ["user", "admin"]}
        token = auth.create_access_token(data)

        payload = auth.verify_token(token)

        assert payload["sub"] == "user-123"
        assert payload["roles"] == ["user", "admin"]

    def test_verify_token_invalid(self):
        auth = JWTAuth()

        with pytest.raises(HTTPException) as exc_info:
            auth.verify_token("invalid.token.here")

        assert exc_info.value.status_code == 401
        assert "Invalid authentication credentials" in exc_info.value.detail

    def test_verify_token_expired(self):
        auth = JWTAuth()
        data = {"sub": "user-123"}
        # Create token that expires immediately
        token = auth.create_access_token(data, timedelta(seconds=-1))

        with pytest.raises(HTTPException) as exc_info:
            auth.verify_token(token)

        assert exc_info.value.status_code == 401


class TestAuthDependencies:

    @pytest.mark.asyncio
    async def test_get_current_user_success(self):
        auth = JWTAuth()
        data = {
            "sub": "user-123",
            "email": "<EMAIL>",
            "roles": ["user"],
            "exp": int((datetime.utcnow() + timedelta(hours=1)).timestamp()),
        }
        token = auth.create_access_token(data)

        credentials = HTTPAuthorizationCredentials(scheme="Bearer", credentials=token)

        with patch("query_intelligence.middleware.auth.jwt_auth", auth):
            user = await get_current_user(credentials)

        assert user["user_id"] == "user-123"
        assert user["email"] == "<EMAIL>"
        assert user["roles"] == ["user"]

    @pytest.mark.asyncio
    async def test_get_current_user_no_sub(self):
        auth = JWTAuth()
        # Token without 'sub' field
        data = {"email": "<EMAIL>"}
        token = auth.create_access_token(data)

        credentials = HTTPAuthorizationCredentials(scheme="Bearer", credentials=token)

        with patch("query_intelligence.middleware.auth.jwt_auth", auth):
            with pytest.raises(HTTPException) as exc_info:
                await get_current_user(credentials)

        assert exc_info.value.status_code == 401

    @pytest.mark.asyncio
    async def test_require_role_success(self):
        user = {"user_id": "user-123", "roles": ["user", "admin"]}

        role_checker = await require_role("admin")
        result = await role_checker(user)

        assert result == user

    @pytest.mark.asyncio
    async def test_require_role_forbidden(self):
        user = {"user_id": "user-123", "roles": ["user"]}

        role_checker = await require_role("admin")

        with pytest.raises(HTTPException) as exc_info:
            await role_checker(user)

        assert exc_info.value.status_code == 403
        assert "Role 'admin' required" in exc_info.value.detail


class TestRateLimiter:

    @pytest.fixture
    def mock_redis_client(self):
        mock = Mock()
        mock.pipeline = Mock()
        return mock

    @pytest.fixture
    def rate_limiter(self, mock_redis_client):
        with patch(
            "query_intelligence.middleware.rate_limit.get_redis_client",
            return_value=mock_redis_client,
        ):
            limiter = RateLimiter(requests_per_window=10, window_seconds=60)
            return limiter

    @pytest.mark.asyncio
    async def test_is_allowed_under_limit(self, rate_limiter, mock_redis_client):
        # Setup pipeline mock
        pipeline = Mock()
        pipeline.zremrangebyscore = Mock()
        pipeline.zcard = Mock()
        pipeline.zadd = Mock()
        pipeline.expire = Mock()
        pipeline.execute = AsyncMock(return_value=[None, 5, None, None])
        mock_redis_client.pipeline.return_value = pipeline

        # Test
        allowed, info = await rate_limiter.is_allowed("user:123")

        assert allowed is True
        assert info["limit"] == 10
        assert info["remaining"] == 4  # 10 - 5 - 1

    @pytest.mark.asyncio
    async def test_is_allowed_at_limit(self, rate_limiter, mock_redis_client):
        # Setup pipeline to return count at limit
        pipeline = Mock()
        pipeline.zremrangebyscore = Mock()
        pipeline.zcard = Mock()
        pipeline.zadd = Mock()
        pipeline.expire = Mock()
        pipeline.execute = AsyncMock(return_value=[None, 10, None, None])
        mock_redis_client.pipeline.return_value = pipeline

        # Test
        allowed, info = await rate_limiter.is_allowed("user:123")

        assert allowed is False
        assert info["remaining"] == 0

    @pytest.mark.asyncio
    async def test_is_allowed_redis_error(self, rate_limiter, mock_redis_client):
        # Setup Redis to fail
        mock_redis_client.pipeline.side_effect = Exception("Redis connection error")

        # Test - should allow on error
        allowed, info = await rate_limiter.is_allowed("user:123")

        assert allowed is True
        assert info["remaining"] == -1  # Indicates error state

    def test_get_identifier_with_user(self, rate_limiter):
        request = Mock()
        request.state.user = {"user_id": "user-123"}

        identifier = rate_limiter.get_identifier(request)

        assert identifier == "user:user-123"

    def test_get_identifier_with_ip(self, rate_limiter):
        request = Mock()
        request.state.user = None
        request.headers = {}
        request.client = Mock(host="***********")

        identifier = rate_limiter.get_identifier(request)

        assert identifier == "ip:***********"

    def test_get_identifier_with_forwarded_ip(self, rate_limiter):
        request = Mock()
        request.state.user = None
        request.headers = {"X-Forwarded-For": "********, ***********"}

        identifier = rate_limiter.get_identifier(request)

        assert identifier == "ip:********"
