"""
Comprehensive Performance Analysis Framework

Main orchestration tool for running complete memory usage benchmarking,
resource optimization analysis, and performance monitoring.
"""

import asyncio
import time
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
import os
import sys

# Import all benchmark modules
from .benchmarks.memory_profiling import <PERSON><PERSON><PERSON><PERSON><PERSON>, MemoryBenchmarkRunner
from .benchmarks.cpu_utilization_analysis import <PERSON><PERSON><PERSON><PERSON><PERSON>, CPUBenchmarkRunner
from .benchmarks.network_performance import NetworkAnalyzer, NetworkBenchmarkRunner
from .benchmarks.cache_efficiency_analysis import CacheEfficiencyAnalyzer, CacheBenchmarkRunner
from .benchmarks.resource_scaling_analysis import ResourceScalingAnalyzer, ScalingBenchmarkRunner


class ComprehensivePerformanceAnalyzer:
    """Main orchestrator for comprehensive performance analysis"""
    
    def __init__(self, 
                 results_dir: str = "performance_analysis_results",
                 enable_detailed_logging: bool = True):
        self.results_dir = Path(results_dir)
        self.enable_detailed_logging = enable_detailed_logging
        
        # Create results directory
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize all analyzers
        self.memory_profiler = MemoryProfiler(
            profile_interval=1.0,
            enable_tracemalloc=True
        )
        
        self.cpu_analyzer = CPUAnalyzer(
            analysis_interval=0.5
        )
        
        self.network_analyzer = NetworkAnalyzer(
            analysis_interval=2.0
        )
        
        self.cache_analyzer = CacheEfficiencyAnalyzer(
            monitoring_interval=1.0
        )
        
        self.scaling_analyzer = ResourceScalingAnalyzer(
            monitoring_interval=5.0
        )
        
        # Initialize benchmark runners
        self.memory_benchmark = MemoryBenchmarkRunner(self.memory_profiler)
        self.cpu_benchmark = CPUBenchmarkRunner(self.cpu_analyzer)
        self.network_benchmark = NetworkBenchmarkRunner(self.network_analyzer)
        self.cache_benchmark = CacheBenchmarkRunner(self.cache_analyzer)
        self.scaling_benchmark = ScalingBenchmarkRunner(self.scaling_analyzer)
        
        # Analysis results
        self.analysis_results = {}
        self.benchmark_results = {}
        
    def log(self, message: str, level: str = "INFO"):
        """Log message with timestamp"""
        if self.enable_detailed_logging:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"[{timestamp}] {level}: {message}")
            
    async def run_comprehensive_analysis(self, 
                                       analysis_duration_hours: float = 2.0,
                                       include_benchmarks: bool = True) -> Dict[str, Any]:
        """
        Run comprehensive performance analysis
        
        Args:
            analysis_duration_hours: Duration of the analysis in hours
            include_benchmarks: Whether to include specific benchmarks
            
        Returns:
            Complete analysis results
        """
        self.log(f"Starting comprehensive performance analysis for {analysis_duration_hours} hours")
        
        start_time = time.time()
        
        try:
            # Phase 1: Baseline Memory Analysis
            self.log("Phase 1: Running baseline memory analysis")
            baseline_results = await self._run_baseline_analysis()
            
            # Phase 2: Load Testing Analysis
            self.log("Phase 2: Running load testing analysis")
            load_results = await self._run_load_analysis(analysis_duration_hours * 0.4)
            
            # Phase 3: Sustained Load Analysis
            self.log("Phase 3: Running sustained load analysis")
            sustained_results = await self._run_sustained_analysis(analysis_duration_hours * 0.4)
            
            # Phase 4: Scaling Analysis
            self.log("Phase 4: Running scaling analysis")
            scaling_results = await self._run_scaling_analysis(analysis_duration_hours * 0.2)
            
            # Phase 5: Benchmark Testing (if enabled)
            if include_benchmarks:
                self.log("Phase 5: Running specific benchmarks")
                benchmark_results = await self._run_benchmark_suite()
            else:
                benchmark_results = {}
                
            # Phase 6: Generate Comprehensive Report
            self.log("Phase 6: Generating comprehensive analysis report")
            comprehensive_report = await self._generate_comprehensive_report(
                baseline_results,
                load_results,
                sustained_results,
                scaling_results,
                benchmark_results
            )
            
            # Save results
            await self._save_analysis_results(comprehensive_report)
            
            total_duration = time.time() - start_time
            self.log(f"Comprehensive analysis completed in {total_duration:.1f} seconds")
            
            return comprehensive_report
            
        except Exception as e:
            self.log(f"Error in comprehensive analysis: {str(e)}", "ERROR")
            raise
            
    async def _run_baseline_analysis(self) -> Dict[str, Any]:
        """Run baseline analysis with idle system"""
        self.log("Running idle baseline benchmark...")
        
        # Start all analyzers
        self.memory_profiler.start_profiling()
        self.cpu_analyzer.start_analysis()
        self.network_analyzer.start_analysis()
        self.cache_analyzer.start_monitoring()
        
        # Run idle for 60 seconds
        await asyncio.sleep(60)
        
        # Stop analyzers and collect results
        self.memory_profiler.stop_profiling()
        self.cpu_analyzer.stop_analysis()
        self.network_analyzer.stop_analysis()
        self.cache_analyzer.stop_monitoring()
        
        return {
            "memory_baseline": self.memory_profiler.get_profiling_report(),
            "cpu_baseline": self.cpu_analyzer.get_analysis_report(),
            "network_baseline": self.network_analyzer.get_analysis_report(),
            "cache_baseline": self.cache_analyzer.get_analysis_report(),
            "phase": "baseline",
            "duration_seconds": 60
        }
        
    async def _run_load_analysis(self, duration_hours: float) -> Dict[str, Any]:
        """Run analysis under simulated load"""
        duration_seconds = int(duration_hours * 3600)
        self.log(f"Running load analysis for {duration_hours} hours...")
        
        # Start all analyzers
        self.memory_profiler.start_profiling()
        self.cpu_analyzer.start_analysis()
        self.network_analyzer.start_analysis()
        self.cache_analyzer.start_monitoring()
        
        # Simulate load with concurrent operations
        await self._simulate_application_load(duration_seconds)
        
        # Stop analyzers and collect results
        self.memory_profiler.stop_profiling()
        self.cpu_analyzer.stop_analysis()
        self.network_analyzer.stop_analysis()
        self.cache_analyzer.stop_monitoring()
        
        return {
            "memory_load": self.memory_profiler.get_profiling_report(),
            "cpu_load": self.cpu_analyzer.get_analysis_report(),
            "network_load": self.network_analyzer.get_analysis_report(),
            "cache_load": self.cache_analyzer.get_analysis_report(),
            "phase": "load_testing",
            "duration_seconds": duration_seconds
        }
        
    async def _run_sustained_analysis(self, duration_hours: float) -> Dict[str, Any]:
        """Run analysis under sustained load"""
        duration_seconds = int(duration_hours * 3600)
        self.log(f"Running sustained load analysis for {duration_hours} hours...")
        
        # Start all analyzers
        self.memory_profiler.start_profiling()
        self.cpu_analyzer.start_analysis()
        self.network_analyzer.start_analysis()
        self.cache_analyzer.start_monitoring()
        
        # Simulate sustained load
        await self._simulate_sustained_load(duration_seconds)
        
        # Stop analyzers and collect results
        self.memory_profiler.stop_profiling()
        self.cpu_analyzer.stop_analysis()
        self.network_analyzer.stop_analysis()
        self.cache_analyzer.stop_monitoring()
        
        return {
            "memory_sustained": self.memory_profiler.get_profiling_report(),
            "cpu_sustained": self.cpu_analyzer.get_analysis_report(),
            "network_sustained": self.network_analyzer.get_analysis_report(),
            "cache_sustained": self.cache_analyzer.get_analysis_report(),
            "phase": "sustained_load",
            "duration_seconds": duration_seconds
        }
        
    async def _run_scaling_analysis(self, duration_hours: float) -> Dict[str, Any]:
        """Run scaling efficiency analysis"""
        self.log(f"Running scaling analysis for {duration_hours} hours...")
        
        # Run scaling benchmark
        scaling_results = await self.scaling_benchmark.run_load_scaling_benchmark(duration_hours)
        
        return {
            "scaling_analysis": scaling_results,
            "phase": "scaling_efficiency",
            "duration_hours": duration_hours
        }
        
    async def _run_benchmark_suite(self) -> Dict[str, Any]:
        """Run comprehensive benchmark suite"""
        self.log("Running benchmark suite...")
        
        benchmark_results = {}
        
        # Memory benchmarks
        self.log("Running memory benchmarks...")
        memory_idle = await self.memory_benchmark.run_idle_baseline_benchmark(60)
        memory_load = await self.memory_benchmark.run_load_memory_benchmark(
            self._create_memory_load_function(), 
            {"concurrent_operations": 25}, 
            300
        )
        
        benchmark_results["memory_benchmarks"] = {
            "idle_baseline": memory_idle,
            "load_test": memory_load,
            "comparison": self.memory_benchmark.get_benchmark_comparison()
        }
        
        # CPU benchmarks
        self.log("Running CPU benchmarks...")
        cpu_intensive = await self.cpu_benchmark.run_cpu_intensive_benchmark(120)
        cpu_mixed = await self.cpu_benchmark.run_mixed_workload_benchmark(120)
        
        benchmark_results["cpu_benchmarks"] = {
            "cpu_intensive": cpu_intensive,
            "mixed_workload": cpu_mixed,
            "comparison": self.cpu_benchmark.get_benchmark_comparison()
        }
        
        # Network benchmarks
        self.log("Running network benchmarks...")
        network_http = await self.network_benchmark.run_http_benchmark(
            "https://httpbin.org/json", 10, 120
        )
        
        benchmark_results["network_benchmarks"] = {
            "http_benchmark": network_http
        }
        
        # Cache benchmarks
        self.log("Running cache benchmarks...")
        cache_hit_rate = await self.cache_benchmark.run_hit_rate_benchmark(1000, 5000, 2000)
        cache_memory = await self.cache_benchmark.run_memory_efficiency_benchmark(50, 3000)
        
        benchmark_results["cache_benchmarks"] = {
            "hit_rate_test": cache_hit_rate,
            "memory_efficiency_test": cache_memory,
            "comparison": self.cache_benchmark.get_benchmark_comparison()
        }
        
        return benchmark_results
        
    def _create_memory_load_function(self):
        """Create a memory-intensive load function"""
        async def memory_load(concurrent_operations: int = 25):
            """Simulate memory-intensive operations"""
            tasks = []
            
            for i in range(concurrent_operations):
                task = asyncio.create_task(self._memory_intensive_task(i))
                tasks.append(task)
                
            await asyncio.gather(*tasks, return_exceptions=True)
            
        return memory_load
        
    async def _memory_intensive_task(self, task_id: int):
        """Single memory-intensive task"""
        # Simulate memory allocation and processing
        data = []
        
        for i in range(1000):
            # Allocate memory
            chunk = [f"data_{task_id}_{i}_{j}" for j in range(100)]
            data.extend(chunk)
            
            # Process data
            if i % 100 == 0:
                # Simulate processing
                processed = [item.upper() for item in data[-1000:]]
                
                # Brief async yield
                await asyncio.sleep(0.01)
                
                # Clean up some data
                data = data[-5000:]  # Keep only recent data
                
        return len(data)
        
    async def _simulate_application_load(self, duration_seconds: int):
        """Simulate realistic application load"""
        end_time = time.time() + duration_seconds
        
        while time.time() < end_time:
            # Simulate batch of operations
            tasks = []
            
            for i in range(10):  # 10 concurrent operations
                task = asyncio.create_task(self._simulate_query_processing(i))
                tasks.append(task)
                
            await asyncio.gather(*tasks, return_exceptions=True)
            
            # Record cache operations
            for i in range(5):
                cache_key = f"query_cache_{i % 50}"  # 50 different cache keys
                hit = (i % 3 == 0)  # 33% hit rate
                
                self.cache_analyzer.record_cache_operation(
                    "get" if hit else "set",
                    cache_key,
                    hit,
                    1024 + (i * 100),  # Variable value sizes
                    10 + (i * 2)  # Variable processing times
                )
                
            # Brief pause
            await asyncio.sleep(0.5)
            
    async def _simulate_sustained_load(self, duration_seconds: int):
        """Simulate sustained application load"""
        end_time = time.time() + duration_seconds
        operation_count = 0
        
        while time.time() < end_time:
            # Sustained operations with gradual increase
            concurrent_ops = min(25, 5 + (operation_count // 1000))
            
            tasks = []
            for i in range(concurrent_ops):
                task = asyncio.create_task(self._simulate_heavy_processing(i, operation_count))
                tasks.append(task)
                
            await asyncio.gather(*tasks, return_exceptions=True)
            
            operation_count += concurrent_ops
            
            # Periodic cache operations
            if operation_count % 10 == 0:
                cache_key = f"sustained_cache_{operation_count % 100}"
                
                self.cache_analyzer.record_cache_operation(
                    "set",
                    cache_key,
                    False,
                    2048,  # 2KB values
                    15.0  # 15ms processing time
                )
                
            await asyncio.sleep(0.2)
            
    async def _simulate_query_processing(self, query_id: int):
        """Simulate query processing"""
        # Simulate different types of processing
        processing_types = ["simple", "complex", "analytical"]
        processing_type = processing_types[query_id % 3]
        
        if processing_type == "simple":
            # Simple processing
            data = [f"simple_data_{i}" for i in range(100)]
            result = len(data)
            await asyncio.sleep(0.05)
            
        elif processing_type == "complex":
            # Complex processing
            data = {}
            for i in range(500):
                data[f"key_{i}"] = f"complex_value_{i * 2}"
            result = len(data)
            await asyncio.sleep(0.15)
            
        else:  # analytical
            # Analytical processing
            data = [[j for j in range(50)] for i in range(100)]
            result = sum(sum(row) for row in data)
            await asyncio.sleep(0.25)
            
        return result
        
    async def _simulate_heavy_processing(self, task_id: int, operation_count: int):
        """Simulate heavy processing for sustained load"""
        # Allocate memory
        data = []
        
        for i in range(200):
            chunk = {
                "id": f"{task_id}_{operation_count}_{i}",
                "data": [f"value_{j}" for j in range(20)],
                "metadata": {
                    "timestamp": time.time(),
                    "operation_count": operation_count,
                    "task_id": task_id
                }
            }
            data.append(chunk)
            
        # Process data
        processed_data = []
        for item in data:
            processed = {
                "processed_id": item["id"],
                "processed_data": [d.upper() for d in item["data"]],
                "processing_time": time.time()
            }
            processed_data.append(processed)
            
        await asyncio.sleep(0.1)
        
        return len(processed_data)
        
    async def _generate_comprehensive_report(self, 
                                           baseline_results: Dict[str, Any],
                                           load_results: Dict[str, Any],
                                           sustained_results: Dict[str, Any],
                                           scaling_results: Dict[str, Any],
                                           benchmark_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive analysis report"""
        self.log("Generating comprehensive performance report...")
        
        # Calculate overall performance scores
        performance_scores = self._calculate_performance_scores(
            baseline_results, load_results, sustained_results
        )
        
        # Generate optimization recommendations
        optimization_recommendations = self._generate_optimization_recommendations(
            baseline_results, load_results, sustained_results, scaling_results
        )
        
        # Calculate resource utilization efficiency
        resource_efficiency = self._calculate_resource_efficiency(
            baseline_results, load_results, sustained_results
        )
        
        # Generate cost analysis
        cost_analysis = self._generate_cost_analysis(scaling_results)
        
        # Create comprehensive report
        comprehensive_report = {
            "analysis_metadata": {
                "timestamp": datetime.now().isoformat(),
                "analysis_type": "comprehensive_performance_analysis",
                "service": "query-intelligence",
                "version": "1.0.0"
            },
            "executive_summary": {
                "overall_performance_score": performance_scores["overall_score"],
                "memory_efficiency_score": performance_scores["memory_score"],
                "cpu_efficiency_score": performance_scores["cpu_score"],
                "network_efficiency_score": performance_scores["network_score"],
                "cache_efficiency_score": performance_scores["cache_score"],
                "scaling_efficiency_score": performance_scores["scaling_score"],
                "target_4gb_memory_compliance": performance_scores["memory_compliance"],
                "production_readiness": performance_scores["production_readiness"]
            },
            "detailed_analysis": {
                "baseline_analysis": baseline_results,
                "load_analysis": load_results,
                "sustained_analysis": sustained_results,
                "scaling_analysis": scaling_results,
                "benchmark_results": benchmark_results
            },
            "optimization_recommendations": optimization_recommendations,
            "resource_efficiency": resource_efficiency,
            "cost_analysis": cost_analysis,
            "performance_targets": {
                "memory_usage_target": "<4GB under full load",
                "cpu_utilization_target": "<80% under sustained load",
                "cache_hit_rate_target": "75%+ cache hit rate",
                "response_time_target": "<500ms P95 response time",
                "scaling_efficiency_target": "70%+ resource utilization"
            },
            "deployment_recommendations": self._generate_deployment_recommendations(
                performance_scores, resource_efficiency
            )
        }
        
        return comprehensive_report
        
    def _calculate_performance_scores(self, 
                                    baseline: Dict[str, Any],
                                    load: Dict[str, Any],
                                    sustained: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall performance scores"""
        
        # Memory score (target: <4GB under full load)
        memory_score = 100.0
        if "memory_sustained" in sustained:
            peak_memory_gb = sustained["memory_sustained"].get("peak_memory", {}).get("peak_rss_mb", 0) / 1024
            if peak_memory_gb > 4:
                memory_score = max(0, 100 - (peak_memory_gb - 4) * 20)
                
        # CPU score (target: <80% under sustained load)
        cpu_score = 100.0
        if "cpu_sustained" in sustained:
            avg_cpu = sustained["cpu_sustained"].get("cpu_statistics", {}).get("overall_cpu", {}).get("mean", 0)
            if avg_cpu > 80:
                cpu_score = max(0, 100 - (avg_cpu - 80) * 2)
                
        # Network score
        network_score = 100.0
        if "network_sustained" in sustained:
            network_perf_score = sustained["network_sustained"].get("performance_score", 100)
            network_score = network_perf_score
            
        # Cache score (target: 75%+ hit rate)
        cache_score = 100.0
        if "cache_sustained" in sustained:
            hit_rate = sustained["cache_sustained"].get("cache_statistics", {}).get("hit_rate", {}).get("mean", 0)
            if hit_rate < 75:
                cache_score = max(0, hit_rate * 100 / 75)
                
        # Scaling score
        scaling_score = 100.0
        if "scaling_analysis" in sustained:
            scaling_score = sustained["scaling_analysis"].get("efficiency_score", 100)
            
        # Overall score
        overall_score = (memory_score * 0.3 + cpu_score * 0.25 + network_score * 0.2 + 
                        cache_score * 0.15 + scaling_score * 0.1)
        
        return {
            "overall_score": overall_score,
            "memory_score": memory_score,
            "cpu_score": cpu_score,
            "network_score": network_score,
            "cache_score": cache_score,
            "scaling_score": scaling_score,
            "memory_compliance": peak_memory_gb < 4 if "memory_sustained" in sustained else True,
            "production_readiness": overall_score >= 80
        }
        
    def _generate_optimization_recommendations(self, 
                                             baseline: Dict[str, Any],
                                             load: Dict[str, Any],
                                             sustained: Dict[str, Any],
                                             scaling: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate optimization recommendations"""
        recommendations = []
        
        # Memory optimization recommendations
        if "memory_sustained" in sustained:
            memory_report = sustained["memory_sustained"]
            peak_memory_gb = memory_report.get("peak_memory", {}).get("peak_rss_mb", 0) / 1024
            
            if peak_memory_gb > 3.5:
                recommendations.append({
                    "category": "memory_optimization",
                    "priority": "high",
                    "title": "Memory Usage Optimization Required",
                    "description": f"Peak memory usage: {peak_memory_gb:.1f}GB. Target: <4GB",
                    "actions": [
                        "Implement memory profiling and identify memory leaks",
                        "Optimize cache memory usage and implement eviction policies",
                        "Review object lifecycle management",
                        "Consider implementing memory-mapped files for large datasets",
                        "Add memory monitoring and alerting"
                    ],
                    "impact": "high",
                    "effort": "medium"
                })
                
        # CPU optimization recommendations
        if "cpu_sustained" in sustained:
            cpu_report = sustained["cpu_sustained"]
            avg_cpu = cpu_report.get("cpu_statistics", {}).get("overall_cpu", {}).get("mean", 0)
            
            if avg_cpu > 70:
                recommendations.append({
                    "category": "cpu_optimization",
                    "priority": "high",
                    "title": "CPU Utilization Optimization",
                    "description": f"Average CPU usage: {avg_cpu:.1f}%. Target: <80%",
                    "actions": [
                        "Implement async/await patterns for I/O-bound operations",
                        "Add caching for frequently computed results",
                        "Optimize database queries and connection pooling",
                        "Consider implementing multiprocessing for CPU-bound tasks",
                        "Profile and optimize CPU-intensive algorithms"
                    ],
                    "impact": "high",
                    "effort": "medium"
                })
                
        # Cache optimization recommendations
        if "cache_sustained" in sustained:
            cache_report = sustained["cache_sustained"]
            hit_rate = cache_report.get("cache_statistics", {}).get("hit_rate", {}).get("mean", 0)
            
            if hit_rate < 70:
                recommendations.append({
                    "category": "cache_optimization",
                    "priority": "medium",
                    "title": "Cache Hit Rate Improvement",
                    "description": f"Cache hit rate: {hit_rate:.1f}%. Target: >75%",
                    "actions": [
                        "Implement cache warming strategies",
                        "Review and optimize TTL settings",
                        "Add semantic caching for similar queries",
                        "Implement cache prefetching",
                        "Consider multi-level caching strategy"
                    ],
                    "impact": "medium",
                    "effort": "low"
                })
                
        # Scaling optimization recommendations
        if "scaling_analysis" in scaling:
            scaling_report = scaling["scaling_analysis"]
            efficiency_score = scaling_report.get("efficiency_score", 100)
            
            if efficiency_score < 80:
                recommendations.append({
                    "category": "scaling_optimization",
                    "priority": "medium",
                    "title": "Auto-scaling Efficiency Improvement",
                    "description": f"Scaling efficiency: {efficiency_score:.1f}%. Target: >80%",
                    "actions": [
                        "Implement predictive scaling based on historical patterns",
                        "Optimize container startup time",
                        "Review scaling thresholds and policies",
                        "Consider using custom scaling metrics",
                        "Implement connection pooling and warming"
                    ],
                    "impact": "medium",
                    "effort": "high"
                })
                
        return recommendations
        
    def _calculate_resource_efficiency(self, 
                                     baseline: Dict[str, Any],
                                     load: Dict[str, Any],
                                     sustained: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate resource utilization efficiency"""
        
        efficiency_metrics = {
            "memory_efficiency": {
                "baseline_mb": baseline.get("memory_baseline", {}).get("current_memory", {}).get("rss_mb", 0),
                "load_mb": load.get("memory_load", {}).get("current_memory", {}).get("rss_mb", 0),
                "sustained_mb": sustained.get("memory_sustained", {}).get("current_memory", {}).get("rss_mb", 0),
                "peak_mb": sustained.get("memory_sustained", {}).get("peak_memory", {}).get("peak_rss_mb", 0),
                "efficiency_score": 100.0
            },
            "cpu_efficiency": {
                "baseline_percent": baseline.get("cpu_baseline", {}).get("cpu_statistics", {}).get("overall_cpu", {}).get("mean", 0),
                "load_percent": load.get("cpu_load", {}).get("cpu_statistics", {}).get("overall_cpu", {}).get("mean", 0),
                "sustained_percent": sustained.get("cpu_sustained", {}).get("cpu_statistics", {}).get("overall_cpu", {}).get("mean", 0),
                "efficiency_score": 100.0
            },
            "network_efficiency": {
                "baseline_score": baseline.get("network_baseline", {}).get("performance_score", 100),
                "load_score": load.get("network_load", {}).get("performance_score", 100),
                "sustained_score": sustained.get("network_sustained", {}).get("performance_score", 100),
                "efficiency_score": 100.0
            },
            "cache_efficiency": {
                "baseline_hit_rate": baseline.get("cache_baseline", {}).get("cache_statistics", {}).get("hit_rate", {}).get("mean", 0),
                "load_hit_rate": load.get("cache_load", {}).get("cache_statistics", {}).get("hit_rate", {}).get("mean", 0),
                "sustained_hit_rate": sustained.get("cache_sustained", {}).get("cache_statistics", {}).get("hit_rate", {}).get("mean", 0),
                "efficiency_score": sustained.get("cache_sustained", {}).get("efficiency_score", 100)
            }
        }
        
        # Calculate overall efficiency score
        overall_efficiency = (
            efficiency_metrics["memory_efficiency"]["efficiency_score"] * 0.3 +
            efficiency_metrics["cpu_efficiency"]["efficiency_score"] * 0.25 +
            efficiency_metrics["network_efficiency"]["efficiency_score"] * 0.2 +
            efficiency_metrics["cache_efficiency"]["efficiency_score"] * 0.25
        )
        
        efficiency_metrics["overall_efficiency"] = overall_efficiency
        
        return efficiency_metrics
        
    def _generate_cost_analysis(self, scaling_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate cost analysis"""
        # Extract cost information from scaling analysis
        scaling_report = scaling_results.get("scaling_analysis", {})
        cost_info = scaling_report.get("cost_analysis", {})
        
        return {
            "estimated_monthly_cost": cost_info.get("potential_monthly_savings", 0) * -1,  # Convert savings to cost
            "cost_efficiency_score": cost_info.get("cost_efficiency_score", 100),
            "optimization_potential": cost_info.get("potential_monthly_savings", 0),
            "recommendations": [
                "Optimize resource allocation based on actual usage patterns",
                "Implement predictive scaling to reduce over-provisioning",
                "Consider reserved instances for baseline load",
                "Optimize container resource requests and limits"
            ]
        }
        
    def _generate_deployment_recommendations(self, 
                                           performance_scores: Dict[str, Any],
                                           resource_efficiency: Dict[str, Any]) -> Dict[str, Any]:
        """Generate deployment recommendations"""
        
        recommendations = {
            "cloud_run_configuration": {
                "memory_limit": "4Gi" if performance_scores["memory_compliance"] else "8Gi",
                "cpu_limit": "2000m",
                "min_instances": 5,
                "max_instances": 200,
                "concurrency": 20,
                "timeout": "300s"
            },
            "monitoring_setup": [
                "Set up memory usage alerts at 3.5GB threshold",
                "Configure CPU utilization alerts at 75% threshold",
                "Implement cache hit rate monitoring",
                "Set up auto-scaling efficiency monitoring",
                "Create performance regression detection"
            ],
            "optimization_priorities": [
                "Memory optimization (highest priority)",
                "Cache efficiency improvement",
                "CPU utilization optimization",
                "Auto-scaling tuning",
                "Network performance optimization"
            ],
            "production_readiness": {
                "ready_for_production": performance_scores["production_readiness"],
                "blocking_issues": self._identify_blocking_issues(performance_scores),
                "recommended_next_steps": self._get_next_steps(performance_scores)
            }
        }
        
        return recommendations
        
    def _identify_blocking_issues(self, performance_scores: Dict[str, Any]) -> List[str]:
        """Identify blocking issues for production deployment"""
        blocking_issues = []
        
        if not performance_scores["memory_compliance"]:
            blocking_issues.append("Memory usage exceeds 4GB target")
            
        if performance_scores["cpu_score"] < 70:
            blocking_issues.append("CPU utilization too high for sustained load")
            
        if performance_scores["cache_score"] < 60:
            blocking_issues.append("Cache hit rate too low")
            
        if performance_scores["overall_score"] < 80:
            blocking_issues.append("Overall performance score below production threshold")
            
        return blocking_issues
        
    def _get_next_steps(self, performance_scores: Dict[str, Any]) -> List[str]:
        """Get recommended next steps"""
        next_steps = []
        
        if performance_scores["production_readiness"]:
            next_steps.extend([
                "Proceed with production deployment",
                "Set up monitoring and alerting",
                "Implement gradual rollout strategy"
            ])
        else:
            next_steps.extend([
                "Address identified blocking issues",
                "Re-run performance analysis after optimizations",
                "Conduct load testing with production-like data",
                "Review and optimize resource allocation"
            ])
            
        return next_steps
        
    async def _save_analysis_results(self, results: Dict[str, Any]):
        """Save analysis results to files"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save comprehensive report
        report_file = self.results_dir / f"comprehensive_analysis_{timestamp}.json"
        with open(report_file, 'w') as f:
            json.dump(results, f, indent=2)
            
        # Save executive summary
        summary_file = self.results_dir / f"executive_summary_{timestamp}.json"
        with open(summary_file, 'w') as f:
            json.dump(results["executive_summary"], f, indent=2)
            
        # Save optimization recommendations
        recommendations_file = self.results_dir / f"optimization_recommendations_{timestamp}.json"
        with open(recommendations_file, 'w') as f:
            json.dump(results["optimization_recommendations"], f, indent=2)
            
        self.log(f"Analysis results saved to {self.results_dir}")
        
    async def run_quick_analysis(self) -> Dict[str, Any]:
        """Run quick performance analysis (30 minutes)"""
        return await self.run_comprehensive_analysis(
            analysis_duration_hours=0.5,
            include_benchmarks=False
        )
        
    async def run_production_validation(self) -> Dict[str, Any]:
        """Run production validation analysis (4 hours)"""
        return await self.run_comprehensive_analysis(
            analysis_duration_hours=4.0,
            include_benchmarks=True
        )


# Main execution function
async def main():
    """Main execution function for comprehensive analysis"""
    
    # Initialize analyzer
    analyzer = ComprehensivePerformanceAnalyzer(
        results_dir="query_intelligence_performance_results",
        enable_detailed_logging=True
    )
    
    # Run comprehensive analysis
    print("Starting comprehensive performance analysis...")
    print("This analysis will take approximately 2 hours to complete.")
    print("Results will be saved to query_intelligence_performance_results/")
    
    try:
        results = await analyzer.run_comprehensive_analysis(
            analysis_duration_hours=2.0,
            include_benchmarks=True
        )
        
        print("\n" + "="*80)
        print("COMPREHENSIVE PERFORMANCE ANALYSIS COMPLETE")
        print("="*80)
        
        # Print executive summary
        summary = results["executive_summary"]
        print(f"\nOverall Performance Score: {summary['overall_performance_score']:.1f}/100")
        print(f"Memory Efficiency Score: {summary['memory_efficiency_score']:.1f}/100")
        print(f"CPU Efficiency Score: {summary['cpu_efficiency_score']:.1f}/100")
        print(f"Network Efficiency Score: {summary['network_efficiency_score']:.1f}/100")
        print(f"Cache Efficiency Score: {summary['cache_efficiency_score']:.1f}/100")
        print(f"Scaling Efficiency Score: {summary['scaling_efficiency_score']:.1f}/100")
        
        print(f"\n4GB Memory Target Compliance: {'✓' if summary['target_4gb_memory_compliance'] else '✗'}")
        print(f"Production Readiness: {'✓' if summary['production_readiness'] else '✗'}")
        
        # Print top recommendations
        print("\nTop Optimization Recommendations:")
        for i, rec in enumerate(results["optimization_recommendations"][:3], 1):
            print(f"{i}. {rec['title']} ({rec['priority']} priority)")
            
        print(f"\nDetailed results saved to: {analyzer.results_dir}")
        
    except Exception as e:
        print(f"Error during analysis: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(main())