/**
 * K6 WebSocket Load Testing Script
 * 
 * Tests WebSocket concurrent connections, streaming performance, and real-time capabilities.
 * Supports concurrent connection stress testing and message throughput validation.
 */

import ws from 'k6/ws';
import { check, sleep } from 'k6';
import { Counter, Trend, Rate } from 'k6/metrics';
import { SharedArray } from 'k6/data';
import { randomString } from 'https://jslib.k6.io/k6-utils/1.4.0/index.js';

// Custom metrics
export let wsConnections = new Counter('ws_connections');
export let wsConnectionErrors = new Counter('ws_connection_errors');
export let wsMessagesSent = new Counter('ws_messages_sent');
export let wsMessagesReceived = new Counter('ws_messages_received');
export let wsConnectionTime = new Trend('ws_connection_time');
export let wsMessageLatency = new Trend('ws_message_latency');
export let wsConnectionDuration = new Trend('ws_connection_duration');
export let wsErrorRate = new Rate('ws_error_rate');

// Test configuration
const BASE_URL = __ENV.BASE_URL || 'ws://localhost:8000';
const AUTH_TOKEN = __ENV.AUTH_TOKEN || 'test-token';
const MAX_CONCURRENT_CONNECTIONS = parseInt(__ENV.MAX_CONCURRENT_CONNECTIONS || '100');
const MESSAGES_PER_CONNECTION = parseInt(__ENV.MESSAGES_PER_CONNECTION || '10');
const CONNECTION_DURATION = parseInt(__ENV.CONNECTION_DURATION || '30'); // seconds

// Test queries for WebSocket streaming
const streamingQueries = new SharedArray('streaming_queries', function() {
  return [
    "Explain the authentication flow step by step",
    "Find all security vulnerabilities in the codebase",
    "Analyze the performance bottlenecks in the system",
    "Generate a comprehensive code review report",
    "Find all API endpoints and their documentation",
    "Explain the database schema and relationships",
    "Find all error handling patterns in the code",
    "Analyze the testing coverage and quality",
    "Find all configuration management patterns",
    "Explain the deployment and infrastructure setup",
    "Find all logging and monitoring implementations",
    "Analyze the code quality and technical debt",
    "Find all third-party dependencies and licenses",
    "Explain the caching strategy and implementation",
    "Find all background job and queue implementations"
  ];
});

// Load test scenarios
export let options = {
  scenarios: {
    // Scenario 1: Concurrent connection stress test
    concurrent_connections: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '1m', target: 25 },    // Ramp to 25 concurrent connections
        { duration: '2m', target: 25 },    // Hold 25 connections
        { duration: '1m', target: 50 },    // Ramp to 50 connections
        { duration: '2m', target: 50 },    // Hold 50 connections
        { duration: '1m', target: 100 },   // Ramp to 100 connections
        { duration: '3m', target: 100 },   // Hold 100 connections
        { duration: '1m', target: 0 },     // Ramp down
      ],
      gracefulRampDown: '30s',
      tags: { scenario: 'concurrent_connections' },
    },
    
    // Scenario 2: High message throughput
    high_throughput: {
      executor: 'constant-vus',
      vus: 50,
      duration: '5m',
      tags: { scenario: 'high_throughput' },
    },
    
    // Scenario 3: Connection stability test
    stability_test: {
      executor: 'constant-vus',
      vus: 30,
      duration: '10m',
      tags: { scenario: 'stability_test' },
    },
    
    // Scenario 4: Burst connections
    burst_connections: {
      executor: 'ramping-arrival-rate',
      startRate: 5,
      stages: [
        { duration: '30s', target: 10 },   // Normal connection rate
        { duration: '15s', target: 100 },  // Burst to 100 connections/second
        { duration: '30s', target: 10 },   // Back to normal
        { duration: '15s', target: 200 },  // Bigger burst
        { duration: '30s', target: 10 },   // Back to normal
      ],
      preAllocatedVUs: 50,
      maxVUs: 300,
      tags: { scenario: 'burst_connections' },
    },
  },
  
  // Performance thresholds
  thresholds: {
    // WebSocket connection should be established within 2 seconds
    ws_connection_time: ['p(95)<2000'],
    
    // Message latency should be under 1 second for 95th percentile
    ws_message_latency: ['p(95)<1000', 'p(99)<2000'],
    
    // Error rate should be under 5%
    ws_error_rate: ['rate<0.05'],
    
    // Connection success rate should be above 95%
    ws_connection_errors: ['count<50'],
    
    // Scenario-specific thresholds
    'ws_connection_time{scenario:concurrent_connections}': ['p(95)<2000'],
    'ws_message_latency{scenario:high_throughput}': ['p(95)<1000'],
    'ws_connection_time{scenario:stability_test}': ['p(95)<1000'],
    'ws_connection_time{scenario:burst_connections}': ['p(95)<3000'],
  },
  
  // Test limits
  noConnectionReuse: true,
  userAgent: 'QueryIntelligence-WebSocketLoadTest/1.0',
};

export function setup() {
  console.log('Starting WebSocket load test');
  console.log(`Target: ${BASE_URL}/api/v1/ws/query`);
  console.log(`Max concurrent connections: ${MAX_CONCURRENT_CONNECTIONS}`);
  console.log(`Messages per connection: ${MESSAGES_PER_CONNECTION}`);
  
  return {
    startTime: new Date().toISOString(),
  };
}

export default function(data) {
  const connectionStart = new Date().getTime();
  let connectionEstablished = false;
  let messagesSent = 0;
  let messagesReceived = 0;
  let connectionErrors = 0;
  
  // WebSocket URL with authentication
  const wsUrl = `${BASE_URL}/api/v1/ws/query`;
  
  // Connection headers
  const params = {
    headers: {
      'Authorization': `Bearer ${AUTH_TOKEN}`,
      'X-Test-Type': 'websocket-load-test',
    },
  };
  
  const response = ws.connect(wsUrl, params, function(socket) {
    connectionEstablished = true;
    const connectionEnd = new Date().getTime();
    const connectionTime = connectionEnd - connectionStart;
    
    // Record connection metrics
    wsConnections.add(1);
    wsConnectionTime.add(connectionTime);
    
    console.log(`WebSocket connected in ${connectionTime}ms`);
    
    // Set up message handlers
    socket.on('open', function() {
      console.log('WebSocket connection opened');
    });
    
    socket.on('message', function(message) {
      messagesReceived++;
      wsMessagesReceived.add(1);
      
      try {
        const data = JSON.parse(message);
        
        // Calculate message latency if timestamp is available
        if (data.timestamp) {
          const messageLatency = new Date().getTime() - new Date(data.timestamp).getTime();
          wsMessageLatency.add(messageLatency);
        }
        
        // Log streaming events
        if (data.type === 'streaming') {
          console.log(`Streaming chunk received: ${data.content?.substring(0, 50)}...`);
        }
        
        // Log completion
        if (data.type === 'complete') {
          console.log(`Query completed with ${messagesReceived} messages`);
        }
        
      } catch (e) {
        console.log(`Invalid JSON message: ${message}`);
        connectionErrors++;
      }
    });
    
    socket.on('error', function(error) {
      console.log(`WebSocket error: ${error}`);
      connectionErrors++;
      wsConnectionErrors.add(1);
      wsErrorRate.add(1);
    });
    
    socket.on('close', function() {
      console.log('WebSocket connection closed');
      const connectionDuration = new Date().getTime() - connectionStart;
      wsConnectionDuration.add(connectionDuration);
    });
    
    // Send test messages
    const sendMessages = () => {
      const scenario = __ENV.SCENARIO || 'websocket_load_test';
      
      if (scenario === 'high_throughput') {
        // Send messages rapidly for throughput testing
        for (let i = 0; i < MESSAGES_PER_CONNECTION; i++) {
          const query = streamingQueries[Math.floor(Math.random() * streamingQueries.length)];
          const message = JSON.stringify({
            query: `${query} (ws-test-${randomString(8)})`,
            repository_id: 'websocket-load-test',
            stream: true,
            metadata: {
              test_scenario: scenario,
              message_index: i,
              virtual_user: __VU,
              timestamp: new Date().toISOString(),
            }
          });
          
          socket.send(message);
          messagesSent++;
          wsMessagesSent.add(1);
          
          // Small delay between messages
          sleep(0.1);
        }
      } else {
        // Send messages with realistic intervals
        for (let i = 0; i < MESSAGES_PER_CONNECTION; i++) {
          const query = streamingQueries[Math.floor(Math.random() * streamingQueries.length)];
          const message = JSON.stringify({
            query: `${query} (ws-test-${randomString(8)})`,
            repository_id: 'websocket-load-test',
            stream: true,
            metadata: {
              test_scenario: scenario,
              message_index: i,
              virtual_user: __VU,
              timestamp: new Date().toISOString(),
            }
          });
          
          socket.send(message);
          messagesSent++;
          wsMessagesSent.add(1);
          
          // Wait for response or timeout
          sleep(Math.random() * 2 + 1); // 1-3 seconds between messages
        }
      }
    };
    
    // Start sending messages after connection is established
    sleep(0.5); // Allow connection to stabilize
    sendMessages();
    
    // Keep connection alive for specified duration
    const remainingTime = CONNECTION_DURATION - (new Date().getTime() - connectionStart) / 1000;
    if (remainingTime > 0) {
      sleep(remainingTime);
    }
  });
  
  // Handle connection failures
  if (!connectionEstablished) {
    console.log('Failed to establish WebSocket connection');
    wsConnectionErrors.add(1);
    wsErrorRate.add(1);
  }
  
  // Check connection results
  check(response, {
    'WebSocket connected successfully': () => connectionEstablished,
    'Messages sent': () => messagesSent > 0,
    'Messages received': () => messagesReceived > 0,
    'No connection errors': () => connectionErrors === 0,
    'Response time acceptable': () => {
      const totalTime = new Date().getTime() - connectionStart;
      return totalTime < CONNECTION_DURATION * 1000 + 5000; // 5 second buffer
    },
  });
  
  // Log performance metrics
  console.log(`Connection summary: sent=${messagesSent}, received=${messagesReceived}, errors=${connectionErrors}`);
}

export function teardown(data) {
  const endTime = new Date().toISOString();
  
  console.log(`WebSocket load test completed`);
  console.log(`Started: ${data.startTime}`);
  console.log(`Ended: ${endTime}`);
}

// Custom summary function
export function handleSummary(data) {
  const summary = {
    testType: 'websocket_load_test',
    timestamp: new Date().toISOString(),
    configuration: {
      baseUrl: BASE_URL,
      maxConcurrentConnections: MAX_CONCURRENT_CONNECTIONS,
      messagesPerConnection: MESSAGES_PER_CONNECTION,
      connectionDuration: CONNECTION_DURATION,
      scenarios: Object.keys(options.scenarios),
    },
    metrics: {
      vus: data.metrics.vus,
      vus_max: data.metrics.vus_max,
      iterations: data.metrics.iterations,
      iteration_duration: data.metrics.iteration_duration,
    },
    websocketMetrics: {
      ws_connections: data.metrics.ws_connections,
      ws_connection_errors: data.metrics.ws_connection_errors,
      ws_messages_sent: data.metrics.ws_messages_sent,
      ws_messages_received: data.metrics.ws_messages_received,
      ws_connection_time: data.metrics.ws_connection_time,
      ws_message_latency: data.metrics.ws_message_latency,
      ws_connection_duration: data.metrics.ws_connection_duration,
      ws_error_rate: data.metrics.ws_error_rate,
    },
    thresholds: data.thresholds,
    performanceInsights: {
      averageConnectionTime: data.metrics.ws_connection_time ? data.metrics.ws_connection_time.avg : 0,
      averageMessageLatency: data.metrics.ws_message_latency ? data.metrics.ws_message_latency.avg : 0,
      totalConnections: data.metrics.ws_connections ? data.metrics.ws_connections.count : 0,
      totalErrors: data.metrics.ws_connection_errors ? data.metrics.ws_connection_errors.count : 0,
      messagesSent: data.metrics.ws_messages_sent ? data.metrics.ws_messages_sent.count : 0,
      messagesReceived: data.metrics.ws_messages_received ? data.metrics.ws_messages_received.count : 0,
      errorRate: data.metrics.ws_error_rate ? data.metrics.ws_error_rate.rate : 0,
    },
  };
  
  return {
    'stdout': JSON.stringify(summary, null, 2),
    'websocket-load-test-results.json': JSON.stringify(summary, null, 2),
  };
}