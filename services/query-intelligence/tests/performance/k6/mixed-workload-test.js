/**
 * K6 Mixed Workload Load Testing Script
 * 
 * Tests combined API and WebSocket workloads to simulate realistic user behavior.
 * Validates system performance under mixed traffic patterns.
 */

import http from 'k6/http';
import ws from 'k6/ws';
import { check, sleep } from 'k6';
import { Counter, Trend, Rate } from 'k6/metrics';
import { SharedArray } from 'k6/data';
import { randomString } from 'https://jslib.k6.io/k6-utils/1.4.0/index.js';

// Custom metrics
export let apiRequests = new Counter('api_requests');
export let apiErrors = new Counter('api_errors');
export let wsConnections = new Counter('ws_connections');
export let wsErrors = new Counter('ws_errors');
export let apiResponseTime = new Trend('api_response_time');
export let wsResponseTime = new Trend('ws_response_time');
export let mixedWorkloadErrors = new Rate('mixed_workload_errors');
export let overallThroughput = new Rate('overall_throughput');

// Test configuration
const BASE_URL = __ENV.BASE_URL || 'http://localhost:8000';
const WS_URL = __ENV.WS_URL || 'ws://localhost:8000';
const AUTH_TOKEN = __ENV.AUTH_TOKEN || 'test-token';
const API_RATIO = parseFloat(__ENV.API_RATIO || '0.7'); // 70% API, 30% WebSocket
const TARGET_RPS = parseInt(__ENV.TARGET_RPS || '500');

// Test data
const testQueries = new SharedArray('mixed_test_queries', function() {
  return [
    { type: 'simple', query: "What is authentication?", expectedTime: 1000 },
    { type: 'complex', query: "Explain the entire authentication flow including JWT validation, rate limiting, and error handling", expectedTime: 3000 },
    { type: 'code_search', query: "Find all functions that handle user authentication and authorization", expectedTime: 2000 },
    { type: 'analytical', query: "Analyze the performance bottlenecks in the query processing pipeline", expectedTime: 4000 },
    { type: 'streaming', query: "Generate a comprehensive security audit report", expectedTime: 5000 },
    { type: 'interactive', query: "Help me debug the authentication middleware", expectedTime: 3000 },
    { type: 'batch', query: "Process all API endpoints and generate documentation", expectedTime: 6000 },
    { type: 'realtime', query: "Monitor system health and alert on issues", expectedTime: 2000 },
  ];
});

// Load test scenarios
export let options = {
  scenarios: {
    // Scenario 1: Mixed API and WebSocket load
    mixed_workload: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '2m', target: 50 },    // Ramp to 50 users
        { duration: '5m', target: 100 },   // Ramp to 100 users
        { duration: '5m', target: 200 },   // Ramp to 200 users
        { duration: '5m', target: 300 },   // Ramp to 300 users
        { duration: '3m', target: 0 },     // Ramp down
      ],
      gracefulRampDown: '30s',
      tags: { scenario: 'mixed_workload' },
    },
    
    // Scenario 2: High-throughput mixed load
    high_throughput_mixed: {
      executor: 'constant-arrival-rate',
      rate: TARGET_RPS,
      duration: '10m',
      preAllocatedVUs: 100,
      maxVUs: 500,
      tags: { scenario: 'high_throughput_mixed' },
    },
    
    // Scenario 3: Realistic user behavior
    realistic_users: {
      executor: 'per-vu-iterations',
      vus: 100,
      iterations: 10,
      maxDuration: '15m',
      tags: { scenario: 'realistic_users' },
    },
    
    // Scenario 4: Peak hour simulation
    peak_hour: {
      executor: 'ramping-arrival-rate',
      startRate: 100,
      stages: [
        { duration: '5m', target: 200 },   // Morning ramp up
        { duration: '10m', target: 500 },  // Peak hour
        { duration: '5m', target: 800 },   // Super peak
        { duration: '10m', target: 500 },  // Sustained peak
        { duration: '5m', target: 200 },   // Evening ramp down
      ],
      preAllocatedVUs: 200,
      maxVUs: 800,
      tags: { scenario: 'peak_hour' },
    },
  },
  
  // Performance thresholds
  thresholds: {
    // API performance thresholds
    'api_response_time': ['p(95)<2000', 'p(99)<5000'],
    'api_errors': ['count<100'],
    
    // WebSocket performance thresholds
    'ws_response_time': ['p(95)<3000', 'p(99)<8000'],
    'ws_errors': ['count<50'],
    
    // Mixed workload thresholds
    'mixed_workload_errors': ['rate<0.05'],
    'overall_throughput': ['rate>10'],
    
    // Scenario-specific thresholds
    'api_response_time{scenario:mixed_workload}': ['p(95)<2000'],
    'api_response_time{scenario:high_throughput_mixed}': ['p(95)<3000'],
    'api_response_time{scenario:realistic_users}': ['p(95)<1500'],
    'api_response_time{scenario:peak_hour}': ['p(95)<4000'],
  },
  
  // Test limits
  noConnectionReuse: false,
  userAgent: 'QueryIntelligence-MixedWorkloadTest/1.0',
};

export function setup() {
  console.log('Starting Mixed Workload Load Test');
  console.log(`API URL: ${BASE_URL}`);
  console.log(`WebSocket URL: ${WS_URL}`);
  console.log(`API Ratio: ${API_RATIO * 100}%`);
  console.log(`Target RPS: ${TARGET_RPS}`);
  
  // Test both API and WebSocket connectivity
  const apiHealth = http.get(`${BASE_URL}/health`);
  if (apiHealth.status !== 200) {
    throw new Error(`API not healthy: ${apiHealth.status}`);
  }
  
  return {
    startTime: new Date().toISOString(),
    apiHealth: apiHealth.json(),
  };
}

export default function(data) {
  const userBehavior = Math.random();
  
  // Determine workload type based on ratio
  if (userBehavior < API_RATIO) {
    // Execute API workload
    executeAPIWorkload();
  } else {
    // Execute WebSocket workload
    executeWebSocketWorkload();
  }
  
  // Simulate user think time
  sleep(Math.random() * 2 + 0.5); // 0.5-2.5 seconds
}

function executeAPIWorkload() {
  const testData = testQueries[Math.floor(Math.random() * testQueries.length)];
  const query = `${testData.query} (api-mixed-${randomString(8)})`;
  
  const payload = JSON.stringify({
    query: query,
    repository_id: 'mixed-workload-test',
    metadata: {
      test_type: 'mixed_workload_api',
      query_type: testData.type,
      expected_time: testData.expectedTime,
      virtual_user: __VU,
      timestamp: new Date().toISOString(),
    }
  });
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${AUTH_TOKEN}`,
    'X-Test-Type': 'mixed-workload-api',
  };
  
  const startTime = new Date().getTime();
  const response = http.post(`${BASE_URL}/api/v1/query`, payload, {
    headers: headers,
    timeout: '30s',
  });
  const endTime = new Date().getTime();
  
  const responseTime = endTime - startTime;
  
  // Record metrics
  apiRequests.add(1);
  apiResponseTime.add(responseTime);
  overallThroughput.add(1);
  
  // Check response
  const success = check(response, {
    'API status 200': (r) => r.status === 200,
    'API response time reasonable': (r) => r.timings.duration < testData.expectedTime * 2,
    'API response has content': (r) => r.body && r.body.length > 0,
    'API response valid JSON': (r) => {
      try {
        const data = JSON.parse(r.body);
        return data.answer && data.answer.length > 0;
      } catch (e) {
        return false;
      }
    },
  });
  
  if (!success) {
    apiErrors.add(1);
    mixedWorkloadErrors.add(1);
    console.log(`API request failed: ${response.status} - ${query.substring(0, 50)}...`);
  }
  
  // Log slow responses
  if (responseTime > testData.expectedTime * 1.5) {
    console.log(`Slow API response: ${responseTime}ms (expected: ${testData.expectedTime}ms) for ${testData.type}`);
  }
}

function executeWebSocketWorkload() {
  const testData = testQueries[Math.floor(Math.random() * testQueries.length)];
  const query = `${testData.query} (ws-mixed-${randomString(8)})`;
  
  const wsUrl = `${WS_URL}/api/v1/ws/query`;
  const params = {
    headers: {
      'Authorization': `Bearer ${AUTH_TOKEN}`,
      'X-Test-Type': 'mixed-workload-ws',
    },
  };
  
  const startTime = new Date().getTime();
  let connectionSuccess = false;
  let messagesReceived = 0;
  let responseComplete = false;
  
  const response = ws.connect(wsUrl, params, function(socket) {
    connectionSuccess = true;
    wsConnections.add(1);
    
    socket.on('message', function(message) {
      messagesReceived++;
      
      try {
        const data = JSON.parse(message);
        
        if (data.type === 'complete') {
          responseComplete = true;
          const endTime = new Date().getTime();
          const totalTime = endTime - startTime;
          
          wsResponseTime.add(totalTime);
          overallThroughput.add(1);
          
          // Log performance
          if (totalTime > testData.expectedTime * 2) {
            console.log(`Slow WebSocket response: ${totalTime}ms (expected: ${testData.expectedTime}ms) for ${testData.type}`);
          }
        }
        
      } catch (e) {
        console.log(`Invalid WebSocket message: ${message}`);
      }
    });
    
    socket.on('error', function(error) {
      console.log(`WebSocket error: ${error}`);
      wsErrors.add(1);
      mixedWorkloadErrors.add(1);
    });
    
    // Send query
    const message = JSON.stringify({
      query: query,
      repository_id: 'mixed-workload-test',
      stream: true,
      metadata: {
        test_type: 'mixed_workload_ws',
        query_type: testData.type,
        expected_time: testData.expectedTime,
        virtual_user: __VU,
        timestamp: new Date().toISOString(),
      }
    });
    
    socket.send(message);
    
    // Wait for response or timeout
    const maxWaitTime = Math.max(testData.expectedTime * 3, 10000); // At least 10 seconds
    const waitStart = new Date().getTime();
    
    while (!responseComplete && (new Date().getTime() - waitStart) < maxWaitTime) {
      sleep(0.1);
    }
  });
  
  // Check WebSocket results
  const success = check(response, {
    'WebSocket connected': () => connectionSuccess,
    'WebSocket messages received': () => messagesReceived > 0,
    'WebSocket response complete': () => responseComplete,
  });
  
  if (!success) {
    wsErrors.add(1);
    mixedWorkloadErrors.add(1);
    console.log(`WebSocket request failed for ${testData.type}`);
  }
}

export function teardown(data) {
  const endTime = new Date().toISOString();
  
  console.log(`Mixed Workload Load Test completed`);
  console.log(`Started: ${data.startTime}`);
  console.log(`Ended: ${endTime}`);
  
  // Final health check
  const finalHealth = http.get(`${BASE_URL}/health`);
  console.log(`Final service health: ${finalHealth.status}`);
  
  // Check circuit breakers
  const circuitBreakers = http.get(`${BASE_URL}/circuit-breakers`);
  if (circuitBreakers.status === 200) {
    console.log(`Circuit breakers status: ${circuitBreakers.body}`);
  }
}

// Custom summary function
export function handleSummary(data) {
  const summary = {
    testType: 'mixed_workload_load_test',
    timestamp: new Date().toISOString(),
    configuration: {
      baseUrl: BASE_URL,
      wsUrl: WS_URL,
      apiRatio: API_RATIO,
      targetRPS: TARGET_RPS,
      scenarios: Object.keys(options.scenarios),
    },
    metrics: {
      vus: data.metrics.vus,
      vus_max: data.metrics.vus_max,
      iterations: data.metrics.iterations,
      iteration_duration: data.metrics.iteration_duration,
    },
    workloadMetrics: {
      api_requests: data.metrics.api_requests,
      api_errors: data.metrics.api_errors,
      api_response_time: data.metrics.api_response_time,
      ws_connections: data.metrics.ws_connections,
      ws_errors: data.metrics.ws_errors,
      ws_response_time: data.metrics.ws_response_time,
      mixed_workload_errors: data.metrics.mixed_workload_errors,
      overall_throughput: data.metrics.overall_throughput,
    },
    thresholds: data.thresholds,
    performanceInsights: {
      apiAverageResponseTime: data.metrics.api_response_time ? data.metrics.api_response_time.avg : 0,
      wsAverageResponseTime: data.metrics.ws_response_time ? data.metrics.ws_response_time.avg : 0,
      totalApiRequests: data.metrics.api_requests ? data.metrics.api_requests.count : 0,
      totalWsConnections: data.metrics.ws_connections ? data.metrics.ws_connections.count : 0,
      totalErrors: (data.metrics.api_errors ? data.metrics.api_errors.count : 0) + 
                   (data.metrics.ws_errors ? data.metrics.ws_errors.count : 0),
      overallErrorRate: data.metrics.mixed_workload_errors ? data.metrics.mixed_workload_errors.rate : 0,
      throughput: data.metrics.overall_throughput ? data.metrics.overall_throughput.rate : 0,
    },
  };
  
  return {
    'stdout': JSON.stringify(summary, null, 2),
    'mixed-workload-test-results.json': JSON.stringify(summary, null, 2),
  };
}