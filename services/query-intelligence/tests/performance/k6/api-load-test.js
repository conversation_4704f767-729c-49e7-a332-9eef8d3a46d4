/**
 * K6 API Load Testing Script
 * 
 * Tests API endpoints under various load conditions with graduated load patterns.
 * Supports concurrent users, sustained load, and performance validation.
 */

import http from 'k6/http';
import { check, sleep } from 'k6';
import { Counter, Trend, Rate } from 'k6/metrics';
import { SharedArray } from 'k6/data';
import { randomString } from 'https://jslib.k6.io/k6-utils/1.4.0/index.js';

// Custom metrics
export let errorRate = new Rate('error_rate');
export let responseTime = new Trend('response_time');
export let requestsPerSecond = new Rate('requests_per_second');
export let successfulRequests = new Counter('successful_requests');
export let failedRequests = new Counter('failed_requests');

// Test configuration
const BASE_URL = __ENV.BASE_URL || 'http://localhost:8000';
const AUTH_TOKEN = __ENV.AUTH_TOKEN || 'test-token';
const TEST_DURATION = __ENV.TEST_DURATION || '5m';
const TARGET_RPS = parseInt(__ENV.TARGET_RPS || '100');

// Test data
const testQueries = new SharedArray('test_queries', function() {
  return [
    "What is authentication?",
    "How does JWT validation work?",
    "Find all functions that handle user authentication",
    "Explain the rate limiting mechanism",
    "What are the circuit breaker patterns used?",
    "How is caching implemented in the system?",
    "Find all API endpoints for user management",
    "Explain the WebSocket implementation",
    "What security measures are in place?",
    "How is error handling implemented?",
    "Find all database connection patterns",
    "What is the performance optimization strategy?",
    "How are metrics collected and exposed?",
    "Find all middleware implementations",
    "What is the deployment configuration?",
    "How is logging structured in the application?",
    "Find all configuration management patterns",
    "What are the testing strategies used?",
    "How is the service discovery implemented?",
    "What are the monitoring and alerting patterns?"
  ];
});

// Load test scenarios
export let options = {
  scenarios: {
    // Scenario 1: Ramping load test (0 → 100 → 500 → 1000 users)
    ramping_load: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '2m', target: 100 },   // Ramp to 100 users over 2 minutes
        { duration: '5m', target: 100 },   // Stay at 100 users for 5 minutes
        { duration: '2m', target: 500 },   // Ramp to 500 users over 2 minutes
        { duration: '5m', target: 500 },   // Stay at 500 users for 5 minutes
        { duration: '2m', target: 1000 },  // Ramp to 1000 users over 2 minutes
        { duration: '5m', target: 1000 },  // Stay at 1000 users for 5 minutes
        { duration: '2m', target: 0 },     // Ramp down to 0 users over 2 minutes
      ],
      gracefulRampDown: '30s',
      tags: { scenario: 'ramping_load' },
    },
    
    // Scenario 2: Constant load test (sustained 1000+ QPS)
    constant_load: {
      executor: 'constant-arrival-rate',
      rate: TARGET_RPS,
      duration: TEST_DURATION,
      preAllocatedVUs: 50,
      maxVUs: 200,
      tags: { scenario: 'constant_load' },
    },
    
    // Scenario 3: Spike test (sudden traffic spike)
    spike_test: {
      executor: 'ramping-arrival-rate',
      startRate: 50,
      stages: [
        { duration: '1m', target: 100 },   // Normal load
        { duration: '30s', target: 2000 }, // Spike to 2000 RPS
        { duration: '1m', target: 100 },   // Back to normal
        { duration: '30s', target: 3000 }, // Bigger spike
        { duration: '1m', target: 100 },   // Back to normal
      ],
      preAllocatedVUs: 100,
      maxVUs: 500,
      tags: { scenario: 'spike_test' },
    },
    
    // Scenario 4: Stress test (beyond normal capacity)
    stress_test: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '2m', target: 1000 },  // Ramp to 1000 users
        { duration: '5m', target: 1000 },  // Stay at 1000 users
        { duration: '2m', target: 2000 },  // Ramp to 2000 users
        { duration: '5m', target: 2000 },  // Stay at 2000 users
        { duration: '2m', target: 3000 },  // Ramp to 3000 users
        { duration: '5m', target: 3000 },  // Stay at 3000 users
        { duration: '2m', target: 0 },     // Ramp down
      ],
      gracefulRampDown: '30s',
      tags: { scenario: 'stress_test' },
    },
  },
  
  // Performance thresholds
  thresholds: {
    // Overall HTTP request duration should be < 200ms for 95th percentile
    http_req_duration: ['p(95)<200', 'p(99)<500'],
    
    // 95% of requests should be successful
    http_req_failed: ['rate<0.05'],
    
    // Custom metrics thresholds
    error_rate: ['rate<0.01'],
    response_time: ['p(95)<200', 'p(99)<500'],
    
    // Scenario-specific thresholds
    'http_req_duration{scenario:ramping_load}': ['p(95)<200'],
    'http_req_duration{scenario:constant_load}': ['p(95)<200'],
    'http_req_duration{scenario:spike_test}': ['p(95)<500'],
    'http_req_duration{scenario:stress_test}': ['p(95)<1000'],
  },
  
  // Test limits
  noConnectionReuse: false,
  userAgent: 'QueryIntelligence-LoadTest/1.0',
};

export function setup() {
  // Test service health before starting load test
  const healthResponse = http.get(`${BASE_URL}/health`);
  
  if (healthResponse.status !== 200) {
    throw new Error(`Service not healthy: ${healthResponse.status}`);
  }
  
  console.log('Service health check passed - starting load test');
  
  return {
    startTime: new Date().toISOString(),
    serviceHealth: healthResponse.json(),
  };
}

export default function(data) {
  // Select random query from test data
  const query = testQueries[Math.floor(Math.random() * testQueries.length)];
  
  // Add some variation to queries
  const uniqueQuery = `${query} (test-${randomString(8)})`;
  
  // Prepare request
  const payload = JSON.stringify({
    query: uniqueQuery,
    repository_id: 'load-test-repo',
    metadata: {
      test_scenario: __ENV.SCENARIO || 'api_load_test',
      test_iteration: __ITER,
      virtual_user: __VU,
      timestamp: new Date().toISOString(),
    }
  });
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${AUTH_TOKEN}`,
    'X-Test-Type': 'load-test',
  };
  
  // Make API request
  const startTime = new Date().getTime();
  const response = http.post(`${BASE_URL}/api/v1/query`, payload, {
    headers: headers,
    timeout: '30s',
  });
  const endTime = new Date().getTime();
  
  // Record custom metrics
  const responseTimeMs = endTime - startTime;
  responseTime.add(responseTimeMs);
  
  // Check response
  const success = check(response, {
    'status code is 200': (r) => r.status === 200,
    'response time < 5000ms': (r) => r.timings.duration < 5000,
    'response has content': (r) => r.body && r.body.length > 0,
    'response is valid JSON': (r) => {
      try {
        JSON.parse(r.body);
        return true;
      } catch (e) {
        return false;
      }
    },
    'response contains answer': (r) => {
      try {
        const data = JSON.parse(r.body);
        return data.answer && data.answer.length > 0;
      } catch (e) {
        return false;
      }
    },
  });
  
  // Record success/failure
  if (success) {
    successfulRequests.add(1);
  } else {
    failedRequests.add(1);
    errorRate.add(1);
    console.log(`Request failed: ${response.status} - ${response.error}`);
  }
  
  // Log performance issues
  if (response.timings.duration > 1000) {
    console.log(`Slow response: ${response.timings.duration}ms for query: ${uniqueQuery.substring(0, 50)}...`);
  }
  
  // Variable sleep time (0.1 to 1 second)
  const sleepTime = Math.random() * 0.9 + 0.1;
  sleep(sleepTime);
}

export function teardown(data) {
  const endTime = new Date().toISOString();
  
  console.log(`Load test completed`);
  console.log(`Started: ${data.startTime}`);
  console.log(`Ended: ${endTime}`);
  
  // Final health check
  const healthResponse = http.get(`${BASE_URL}/health`);
  console.log(`Final service health: ${healthResponse.status}`);
  
  // Check circuit breakers
  const circuitBreakerResponse = http.get(`${BASE_URL}/circuit-breakers`);
  if (circuitBreakerResponse.status === 200) {
    console.log(`Circuit breakers status: ${circuitBreakerResponse.body}`);
  }
}

// Custom summary function
export function handleSummary(data) {
  const summary = {
    testType: 'api_load_test',
    timestamp: new Date().toISOString(),
    configuration: {
      baseUrl: BASE_URL,
      targetRPS: TARGET_RPS,
      duration: TEST_DURATION,
      scenarios: Object.keys(options.scenarios),
    },
    metrics: {
      http_reqs: data.metrics.http_reqs,
      http_req_duration: data.metrics.http_req_duration,
      http_req_failed: data.metrics.http_req_failed,
      http_req_blocked: data.metrics.http_req_blocked,
      http_req_connecting: data.metrics.http_req_connecting,
      http_req_receiving: data.metrics.http_req_receiving,
      http_req_sending: data.metrics.http_req_sending,
      http_req_waiting: data.metrics.http_req_waiting,
      vus: data.metrics.vus,
      vus_max: data.metrics.vus_max,
      iterations: data.metrics.iterations,
    },
    customMetrics: {
      error_rate: data.metrics.error_rate,
      response_time: data.metrics.response_time,
      successful_requests: data.metrics.successful_requests,
      failed_requests: data.metrics.failed_requests,
    },
    thresholds: data.thresholds,
  };
  
  return {
    'stdout': JSON.stringify(summary, null, 2),
    'api-load-test-results.json': JSON.stringify(summary, null, 2),
  };
}