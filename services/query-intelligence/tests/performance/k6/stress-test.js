/**
 * K6 Stress Testing Script
 * 
 * Tests system behavior beyond normal capacity to identify breaking points,
 * resource exhaustion thresholds, and recovery characteristics.
 */

import http from 'k6/http';
import ws from 'k6/ws';
import { check, sleep } from 'k6';
import { Counter, Trend, Rate, Gauge } from 'k6/metrics';
import { SharedArray } from 'k6/data';
import { randomString } from 'https://jslib.k6.io/k6-utils/1.4.0/index.js';

// Custom metrics
export let stressRequests = new Counter('stress_requests');
export let stressErrors = new Counter('stress_errors');
export let stressResponseTime = new Trend('stress_response_time');
export let stressErrorRate = new Rate('stress_error_rate');
export let concurrentUsers = new Gauge('concurrent_users');
export let systemBreakingPoint = new Gauge('system_breaking_point');
export let recoveryTime = new Trend('recovery_time');
export let resourceExhaustion = new Rate('resource_exhaustion');

// Test configuration
const BASE_URL = __ENV.BASE_URL || 'http://localhost:8000';
const WS_URL = __ENV.WS_URL || 'ws://localhost:8000';
const AUTH_TOKEN = __ENV.AUTH_TOKEN || 'test-token';
const MAX_STRESS_USERS = parseInt(__ENV.MAX_STRESS_USERS || '2000');
const STRESS_DURATION = __ENV.STRESS_DURATION || '10m';

// Stress test queries (designed to be resource intensive)
const stressQueries = new SharedArray('stress_test_queries', function() {
  return [
    {
      type: 'cpu_intensive',
      query: 'Analyze the entire codebase for performance bottlenecks, security vulnerabilities, and code quality issues. Generate a comprehensive report with detailed recommendations.',
      weight: 3,
      timeout: 60000,
    },
    {
      type: 'memory_intensive',
      query: 'Process all API endpoints, generate complete documentation, analyze dependencies, and create architectural diagrams for the entire system.',
      weight: 4,
      timeout: 90000,
    },
    {
      type: 'io_intensive',
      query: 'Scan all files in the repository, extract metadata, analyze patterns, and generate detailed statistics about code complexity and maintainability.',
      weight: 3,
      timeout: 45000,
    },
    {
      type: 'complex_analysis',
      query: 'Perform deep code analysis including cyclomatic complexity, dependency graphs, security audit, performance profiling, and technical debt assessment.',
      weight: 5,
      timeout: 120000,
    },
    {
      type: 'batch_processing',
      query: 'Process multiple queries simultaneously: security audit, performance analysis, code quality assessment, and dependency analysis.',
      weight: 4,
      timeout: 75000,
    },
    {
      type: 'streaming_heavy',
      query: 'Generate real-time analysis of the codebase with continuous updates, streaming results, and progressive enhancement of insights.',
      weight: 3,
      timeout: 60000,
    },
    {
      type: 'concurrent_load',
      query: 'Execute multiple concurrent analyses: static analysis, dynamic analysis, security scanning, and performance profiling.',
      weight: 5,
      timeout: 100000,
    },
  ];
});

// Stress test scenarios
export let options = {
  scenarios: {
    // Scenario 1: Gradual stress ramp to breaking point
    breaking_point_test: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '2m', target: 100 },   // Warm up
        { duration: '3m', target: 500 },   // Normal load
        { duration: '3m', target: 1000 },  // High load
        { duration: '3m', target: 1500 },  // Stress load
        { duration: '3m', target: 2000 },  // Breaking point
        { duration: '3m', target: 2500 },  // Beyond breaking point
        { duration: '2m', target: 0 },     // Recovery test
      ],
      gracefulRampDown: '1m',
      tags: { scenario: 'breaking_point_test' },
    },
    
    // Scenario 2: Spike stress test
    spike_stress: {
      executor: 'ramping-arrival-rate',
      startRate: 50,
      stages: [
        { duration: '1m', target: 100 },    // Baseline
        { duration: '30s', target: 1000 },  // Spike
        { duration: '2m', target: 100 },    // Recovery
        { duration: '30s', target: 2000 },  // Bigger spike
        { duration: '2m', target: 100 },    // Recovery
        { duration: '30s', target: 3000 },  // Extreme spike
        { duration: '3m', target: 100 },    // Extended recovery
      ],
      preAllocatedVUs: 500,
      maxVUs: 3000,
      tags: { scenario: 'spike_stress' },
    },
    
    // Scenario 3: Sustained stress test
    sustained_stress: {
      executor: 'constant-vus',
      vus: 1000,
      duration: STRESS_DURATION,
      tags: { scenario: 'sustained_stress' },
    },
    
    // Scenario 4: Resource exhaustion test
    resource_exhaustion: {
      executor: 'ramping-vus',
      startVUs: 0,
      stages: [
        { duration: '1m', target: 200 },   // Baseline
        { duration: '2m', target: 800 },   // Resource pressure
        { duration: '5m', target: 1200 },  // High resource usage
        { duration: '5m', target: 1600 },  // Resource exhaustion
        { duration: '5m', target: 2000 },  // Beyond exhaustion
        { duration: '2m', target: 0 },     // Recovery
      ],
      gracefulRampDown: '1m',
      tags: { scenario: 'resource_exhaustion' },
    },
  },
  
  // Stress test thresholds (more lenient than normal load tests)
  thresholds: {
    // Allow higher response times under stress
    'stress_response_time': ['p(95)<10000', 'p(99)<20000'],
    
    // Allow higher error rates under stress
    'stress_error_rate': ['rate<0.3'], // 30% error rate acceptable under extreme stress
    
    // Track system breaking point
    'system_breaking_point': ['value>0'],
    
    // Recovery should be within reasonable time
    'recovery_time': ['p(95)<30000'], // 30 seconds recovery time
    
    // Scenario-specific thresholds
    'stress_response_time{scenario:breaking_point_test}': ['p(95)<15000'],
    'stress_response_time{scenario:spike_stress}': ['p(95)<20000'],
    'stress_response_time{scenario:sustained_stress}': ['p(95)<8000'],
    'stress_response_time{scenario:resource_exhaustion}': ['p(95)<25000'],
  },
  
  // Extended timeouts for stress testing
  noConnectionReuse: false,
  userAgent: 'QueryIntelligence-StressTest/1.0',
  maxRedirects: 0,
  batch: 50,
  batchPerHost: 20,
};

export function setup() {
  console.log('Starting Stress Testing');
  console.log(`Target: ${BASE_URL}`);
  console.log(`Max stress users: ${MAX_STRESS_USERS}`);
  console.log(`Stress duration: ${STRESS_DURATION}`);
  
  // Verify service is healthy before stress testing
  const healthCheck = http.get(`${BASE_URL}/health`);
  if (healthCheck.status !== 200) {
    throw new Error(`Service not healthy before stress test: ${healthCheck.status}`);
  }
  
  console.log('Service is healthy - beginning stress test');
  
  return {
    startTime: new Date().toISOString(),
    initialHealth: healthCheck.json(),
  };
}

export default function(data) {
  const currentVUs = __VU;
  concurrentUsers.add(currentVUs);
  
  // Select stress query based on current load level
  const stressQuery = selectStressQuery(currentVUs);
  const query = `${stressQuery.query} (stress-${randomString(8)})`;
  
  const startTime = new Date().getTime();
  let success = false;
  let errorType = 'unknown';
  
  // Randomly choose between API and WebSocket stress
  if (Math.random() < 0.8) {
    // API stress test
    success = executeAPIStress(query, stressQuery);
  } else {
    // WebSocket stress test
    success = executeWebSocketStress(query, stressQuery);
  }
  
  const endTime = new Date().getTime();
  const responseTime = endTime - startTime;
  
  // Record metrics
  stressRequests.add(1);
  stressResponseTime.add(responseTime);
  
  if (!success) {
    stressErrors.add(1);
    stressErrorRate.add(1);
    
    // Check for resource exhaustion indicators
    if (responseTime > 30000 || errorType === 'timeout') {
      resourceExhaustion.add(1);
    }
    
    // Track breaking point
    if (currentVUs > 1000 && !success) {
      systemBreakingPoint.add(currentVUs);
    }
  }
  
  // Log stress indicators
  if (currentVUs > 1500) {
    console.log(`High stress level: ${currentVUs} users, response: ${responseTime}ms, success: ${success}`);
  }
  
  // Very short sleep under stress
  sleep(Math.random() * 0.5 + 0.1); // 0.1-0.6 seconds
}

function selectStressQuery(currentVUs) {
  // Select more intensive queries at higher load levels
  if (currentVUs > 2000) {
    return stressQueries.filter(q => q.weight >= 4)[Math.floor(Math.random() * stressQueries.filter(q => q.weight >= 4).length)];
  } else if (currentVUs > 1500) {
    return stressQueries.filter(q => q.weight >= 3)[Math.floor(Math.random() * stressQueries.filter(q => q.weight >= 3).length)];
  } else {
    return stressQueries[Math.floor(Math.random() * stressQueries.length)];
  }
}

function executeAPIStress(query, stressQuery) {
  const payload = JSON.stringify({
    query: query,
    repository_id: 'stress-test-repo',
    priority: 'high',
    metadata: {
      test_type: 'stress_test',
      query_type: stressQuery.type,
      stress_level: __VU,
      timestamp: new Date().toISOString(),
    }
  });
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${AUTH_TOKEN}`,
    'X-Test-Type': 'stress-test',
    'X-Stress-Level': __VU.toString(),
  };
  
  const response = http.post(`${BASE_URL}/api/v1/query`, payload, {
    headers: headers,
    timeout: `${stressQuery.timeout}ms`,
  });
  
  const success = check(response, {
    'stress API status acceptable': (r) => r.status === 200 || r.status === 429 || r.status === 503,
    'stress API response time under limit': (r) => r.timings.duration < stressQuery.timeout,
    'stress API has response': (r) => r.body && r.body.length > 0,
  });
  
  if (!success) {
    console.log(`API stress failure: ${response.status} - ${response.timings.duration}ms`);
  }
  
  return success;
}

function executeWebSocketStress(query, stressQuery) {
  const wsUrl = `${WS_URL}/api/v1/ws/query`;
  const params = {
    headers: {
      'Authorization': `Bearer ${AUTH_TOKEN}`,
      'X-Test-Type': 'stress-test-ws',
      'X-Stress-Level': __VU.toString(),
    },
  };
  
  let connectionSuccess = false;
  let responseReceived = false;
  
  const response = ws.connect(wsUrl, params, function(socket) {
    connectionSuccess = true;
    
    socket.on('message', function(message) {
      responseReceived = true;
      
      try {
        const data = JSON.parse(message);
        if (data.type === 'error') {
          console.log(`WebSocket stress error: ${data.message}`);
        }
      } catch (e) {
        // Ignore parsing errors under stress
      }
    });
    
    socket.on('error', function(error) {
      console.log(`WebSocket stress connection error: ${error}`);
    });
    
    // Send stress query
    const message = JSON.stringify({
      query: query,
      repository_id: 'stress-test-repo',
      stream: true,
      priority: 'high',
      metadata: {
        test_type: 'stress_test_ws',
        query_type: stressQuery.type,
        stress_level: __VU,
        timestamp: new Date().toISOString(),
      }
    });
    
    socket.send(message);
    
    // Wait for response with timeout
    const waitTime = Math.min(stressQuery.timeout / 1000, 30); // Max 30 seconds
    sleep(waitTime);
  });
  
  const success = check(response, {
    'stress WebSocket connected': () => connectionSuccess,
    'stress WebSocket response received': () => responseReceived,
  });
  
  if (!success) {
    console.log(`WebSocket stress failure: connected=${connectionSuccess}, received=${responseReceived}`);
  }
  
  return success;
}

export function teardown(data) {
  const endTime = new Date().toISOString();
  
  console.log(`Stress test completed`);
  console.log(`Started: ${data.startTime}`);
  console.log(`Ended: ${endTime}`);
  
  // Check system recovery
  console.log('Checking system recovery...');
  sleep(5); // Allow system to stabilize
  
  const recoveryStart = new Date().getTime();
  const recoveryHealth = http.get(`${BASE_URL}/health`);
  const recoveryEnd = new Date().getTime();
  
  if (recoveryHealth.status === 200) {
    const recoveryTimeMs = recoveryEnd - recoveryStart;
    recoveryTime.add(recoveryTimeMs);
    console.log(`System recovered in ${recoveryTimeMs}ms`);
  } else {
    console.log(`System not fully recovered: ${recoveryHealth.status}`);
  }
  
  // Check circuit breakers
  const circuitBreakers = http.get(`${BASE_URL}/circuit-breakers`);
  if (circuitBreakers.status === 200) {
    console.log(`Circuit breakers after stress test: ${circuitBreakers.body}`);
  }
}

// Custom summary function
export function handleSummary(data) {
  const summary = {
    testType: 'stress_test',
    timestamp: new Date().toISOString(),
    configuration: {
      baseUrl: BASE_URL,
      maxStressUsers: MAX_STRESS_USERS,
      stressDuration: STRESS_DURATION,
      scenarios: Object.keys(options.scenarios),
    },
    metrics: {
      vus: data.metrics.vus,
      vus_max: data.metrics.vus_max,
      iterations: data.metrics.iterations,
      iteration_duration: data.metrics.iteration_duration,
    },
    stressMetrics: {
      stress_requests: data.metrics.stress_requests,
      stress_errors: data.metrics.stress_errors,
      stress_response_time: data.metrics.stress_response_time,
      stress_error_rate: data.metrics.stress_error_rate,
      concurrent_users: data.metrics.concurrent_users,
      system_breaking_point: data.metrics.system_breaking_point,
      recovery_time: data.metrics.recovery_time,
      resource_exhaustion: data.metrics.resource_exhaustion,
    },
    thresholds: data.thresholds,
    performanceInsights: {
      totalStressRequests: data.metrics.stress_requests ? data.metrics.stress_requests.count : 0,
      totalStressErrors: data.metrics.stress_errors ? data.metrics.stress_errors.count : 0,
      stressErrorRate: data.metrics.stress_error_rate ? data.metrics.stress_error_rate.rate : 0,
      averageStressResponseTime: data.metrics.stress_response_time ? data.metrics.stress_response_time.avg : 0,
      maxConcurrentUsers: data.metrics.concurrent_users ? data.metrics.concurrent_users.max : 0,
      systemBreakingPoint: data.metrics.system_breaking_point ? data.metrics.system_breaking_point.max : 0,
      averageRecoveryTime: data.metrics.recovery_time ? data.metrics.recovery_time.avg : 0,
      resourceExhaustionRate: data.metrics.resource_exhaustion ? data.metrics.resource_exhaustion.rate : 0,
    },
  };
  
  return {
    'stdout': JSON.stringify(summary, null, 2),
    'stress-test-results.json': JSON.stringify(summary, null, 2),
  };
}