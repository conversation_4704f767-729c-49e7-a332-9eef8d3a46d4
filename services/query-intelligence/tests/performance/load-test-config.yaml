# Load Test Configuration
# Configuration for automated load testing of query-intelligence service

service:
  base_url: "http://localhost:8000"
  ws_url: "ws://localhost:8000"
  health_endpoint: "/health"
  auth_token: "test-token"
  circuit_breaker_endpoint: "/circuit-breakers"
  metrics_endpoint: "/metrics"

tests:
  k6_path: "k6"
  artillery_path: "artillery"
  output_dir: "./results"
  timeout: 3600  # 1 hour timeout for individual tests
  
  # Test environments
  environments:
    local:
      base_url: "http://localhost:8000"
      ws_url: "ws://localhost:8000"
      auth_token: "test-token"
    
    staging:
      base_url: "https://staging-api.example.com"
      ws_url: "wss://staging-api.example.com"
      auth_token: "${STAGING_AUTH_TOKEN}"
    
    production:
      base_url: "https://api.example.com"
      ws_url: "wss://api.example.com"
      auth_token: "${PRODUCTION_AUTH_TOKEN}"

# Load test scenarios with graduated load levels
load_scenarios:
  - name: "api_load_test"
    type: "k6"
    script: "k6/api-load-test.js"
    description: "API endpoint load testing with graduated RPS"
    target_rps: [100, 200, 500, 750, 1000, 1250, 1500]
    duration: "5m"
    ramp_up: "2m"
    success_criteria:
      max_response_time_p95: 2000  # 2 seconds
      min_success_rate: 0.95       # 95%
      max_error_rate: 0.05         # 5%
    
  - name: "websocket_load_test"
    type: "k6"
    script: "k6/websocket-load-test.js"
    description: "WebSocket concurrent connection stress testing"
    concurrent_connections: [25, 50, 100, 150, 200, 300, 500]
    duration: "5m"
    messages_per_connection: 10
    success_criteria:
      max_connection_time_p95: 2000  # 2 seconds
      min_success_rate: 0.90         # 90%
      max_error_rate: 0.10           # 10%
    
  - name: "mixed_workload_test"
    type: "k6"
    script: "k6/mixed-workload-test.js"
    description: "Mixed API and WebSocket workload testing"
    target_rps: [200, 400, 600, 800, 1000, 1200, 1500]
    duration: "10m"
    api_ratio: 0.7  # 70% API, 30% WebSocket
    success_criteria:
      max_response_time_p95: 3000  # 3 seconds
      min_success_rate: 0.92       # 92%
      max_error_rate: 0.08         # 8%
    
  - name: "stress_test"
    type: "k6"
    script: "k6/stress-test.js"
    description: "Stress testing beyond normal capacity"
    max_users: [500, 1000, 1500, 2000, 2500, 3000]
    duration: "10m"
    stress_duration: "5m"
    success_criteria:
      max_response_time_p95: 10000  # 10 seconds (lenient under stress)
      min_success_rate: 0.70        # 70% (lenient under stress)
      max_error_rate: 0.30          # 30% (lenient under stress)
    
  - name: "realtime_load_test"
    type: "artillery"
    script: "artillery/realtime-load.yml"
    description: "Real-time WebSocket streaming load testing"
    phases: "default"
    success_criteria:
      max_connection_time_p95: 2000  # 2 seconds
      max_message_latency_p95: 1000  # 1 second
      min_success_rate: 0.95         # 95%
    
  - name: "spike_test"
    type: "artillery"
    script: "artillery/spike-test.yml"
    description: "Spike load testing with sudden traffic increases"
    phases: "default"
    success_criteria:
      max_response_time_p95: 5000   # 5 seconds (lenient during spikes)
      min_success_rate: 0.85        # 85% (lenient during spikes)
      max_error_rate: 0.15          # 15% (lenient during spikes)

# Performance monitoring configuration
monitoring:
  collect_metrics: true
  metrics_interval: 5  # seconds
  real_time_monitoring: true
  
  # Performance alert thresholds
  alert_thresholds:
    response_time: 2000        # 2 seconds
    error_rate: 0.05           # 5%
    throughput: 10             # 10 requests per second
    concurrent_connections: 100
    cpu_usage: 80              # 80%
    memory_usage: 85           # 85%
    
  # Metrics to collect
  metrics:
    - name: "response_time"
      type: "histogram"
      percentiles: [50, 75, 90, 95, 99]
    
    - name: "throughput"
      type: "rate"
      window: "1m"
    
    - name: "error_rate"
      type: "rate"
      window: "1m"
    
    - name: "concurrent_connections"
      type: "gauge"
    
    - name: "cpu_usage"
      type: "gauge"
    
    - name: "memory_usage"
      type: "gauge"

# Reporting configuration
reporting:
  generate_html_report: true
  generate_json_report: true
  generate_csv_report: true
  
  # Report sections
  sections:
    - summary
    - performance_trends
    - error_analysis
    - resource_utilization
    - recommendations
    - detailed_results
  
  # Export formats
  exports:
    - format: "json"
      filename: "load_test_results.json"
    
    - format: "html"
      filename: "load_test_report.html"
      template: "performance_report_template.html"
    
    - format: "csv"
      filename: "load_test_metrics.csv"
      metrics: ["response_time", "throughput", "error_rate"]

# Test execution configuration
execution:
  # Sequential execution of scenarios
  sequential: true
  
  # Pause between scenarios
  pause_between_scenarios: 30  # seconds
  
  # Pre-test validations
  pre_test_checks:
    - service_health_check
    - dependency_health_check
    - resource_availability_check
  
  # Post-test validations
  post_test_checks:
    - service_recovery_check
    - resource_cleanup_check
    - data_integrity_check
  
  # Retry configuration
  retry:
    max_retries: 3
    retry_delay: 60  # seconds
    retry_on_failure: true
  
  # Failure handling
  failure_handling:
    stop_on_critical_failure: true
    continue_on_warning: true
    save_partial_results: true

# Performance baselines and targets
performance_targets:
  # Production readiness criteria
  production_ready:
    min_throughput: 1000          # 1000+ QPS
    max_response_time_p95: 200    # 200ms p95
    min_success_rate: 0.995       # 99.5%
    max_error_rate: 0.005         # 0.5%
    max_cpu_usage: 70             # 70%
    max_memory_usage: 80          # 80%
  
  # Performance regression thresholds
  regression_thresholds:
    response_time_increase: 0.20  # 20% increase
    throughput_decrease: 0.15     # 15% decrease
    error_rate_increase: 0.10     # 10% increase
  
  # Capacity planning targets
  capacity_targets:
    peak_throughput: 2000         # 2000 QPS peak
    concurrent_users: 1000        # 1000 concurrent users
    sustained_load_duration: 3600 # 1 hour sustained load

# Integration configuration
integrations:
  # Monitoring systems
  monitoring_systems:
    - name: "prometheus"
      enabled: false
      endpoint: "http://localhost:9090"
    
    - name: "grafana"
      enabled: false
      endpoint: "http://localhost:3000"
    
    - name: "cloudwatch"
      enabled: false
      region: "us-east-1"
      namespace: "QueryIntelligence/LoadTest"
  
  # Alerting systems
  alerting:
    - name: "slack"
      enabled: false
      webhook_url: "${SLACK_WEBHOOK_URL}"
    
    - name: "email"
      enabled: false
      smtp_server: "smtp.example.com"
      recipients: ["<EMAIL>"]
  
  # CI/CD integration
  ci_cd:
    - name: "github_actions"
      enabled: false
      report_pr_comment: true
    
    - name: "jenkins"
      enabled: false
      archive_artifacts: true

# Security configuration
security:
  # Authentication
  auth:
    token_rotation: true
    token_expiry: 3600  # 1 hour
  
  # Test data security
  test_data:
    anonymize_sensitive_data: true
    exclude_pii: true
    data_retention: 7  # days
  
  # Network security
  network:
    allowed_hosts: ["localhost", "127.0.0.1"]
    ssl_verification: true
    proxy_support: false