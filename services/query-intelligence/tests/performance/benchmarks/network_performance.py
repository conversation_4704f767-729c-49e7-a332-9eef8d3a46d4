"""
Network Performance Analysis Framework

Comprehensive network I/O performance analysis tools for monitoring
network usage patterns, latency, throughput, and connection efficiency.
"""

import asyncio
import time
import socket
import threading
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
from statistics import mean, median, stdev
import psutil
import json
from datetime import datetime
import aiohttp
import ssl
import urllib.parse


@dataclass
class NetworkSnapshot:
    """Network performance snapshot"""
    timestamp: float
    bytes_sent: int
    bytes_recv: int
    packets_sent: int
    packets_recv: int
    bytes_sent_per_sec: float
    bytes_recv_per_sec: float
    packets_sent_per_sec: float
    packets_recv_per_sec: float
    connections_established: int
    connections_active: int
    network_latency_ms: Optional[float] = None
    dns_resolution_time_ms: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "timestamp": self.timestamp,
            "bytes_sent": self.bytes_sent,
            "bytes_recv": self.bytes_recv,
            "packets_sent": self.packets_sent,
            "packets_recv": self.packets_recv,
            "bytes_sent_per_sec": self.bytes_sent_per_sec,
            "bytes_recv_per_sec": self.bytes_recv_per_sec,
            "packets_sent_per_sec": self.packets_sent_per_sec,
            "packets_recv_per_sec": self.packets_recv_per_sec,
            "connections_established": self.connections_established,
            "connections_active": self.connections_active,
            "network_latency_ms": self.network_latency_ms,
            "dns_resolution_time_ms": self.dns_resolution_time_ms
        }


@dataclass
class ConnectionMetrics:
    """Individual connection metrics"""
    connection_id: str
    local_address: str
    remote_address: str
    status: str
    family: str  # IPv4/IPv6
    type: str   # TCP/UDP
    established_time: float
    bytes_sent: int
    bytes_recv: int
    rtt_ms: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "connection_id": self.connection_id,
            "local_address": self.local_address,
            "remote_address": self.remote_address,
            "status": self.status,
            "family": self.family,
            "type": self.type,
            "established_time": self.established_time,
            "bytes_sent": self.bytes_sent,
            "bytes_recv": self.bytes_recv,
            "rtt_ms": self.rtt_ms
        }


@dataclass
class NetworkBottleneck:
    """Network bottleneck detection result"""
    detected: bool
    bottleneck_type: str  # "bandwidth", "latency", "connections", "dns"
    severity: str  # "low", "medium", "high", "critical"
    metrics: Dict[str, Any]
    recommendations: List[str]
    confidence: float  # 0-1
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "detected": self.detected,
            "bottleneck_type": self.bottleneck_type,
            "severity": self.severity,
            "metrics": self.metrics,
            "recommendations": self.recommendations,
            "confidence": self.confidence
        }


class NetworkAnalyzer:
    """Advanced network performance analyzer"""
    
    def __init__(self, analysis_interval: float = 1.0):
        self.analysis_interval = analysis_interval
        self.network_snapshots: List[NetworkSnapshot] = []
        self.connection_metrics: Dict[str, ConnectionMetrics] = {}
        self.analyzing = False
        self.analysis_thread = None
        self.start_time = None
        self.process = psutil.Process()
        
        # Network monitoring thresholds
        self.high_bandwidth_threshold_mbps = 100  # 100 Mbps
        self.high_latency_threshold_ms = 500  # 500ms
        self.dns_slow_threshold_ms = 200  # 200ms
        self.max_connections_threshold = 1000
        
        # Baseline metrics
        self.baseline_snapshots = 10
        self.baseline_network = None
        self.previous_snapshot = None
        
        # Bottleneck detection
        self.bottleneck_history = deque(maxlen=50)
        
        # External monitoring targets
        self.monitoring_targets = [
            "8.8.8.8",  # Google DNS
            "1.1.1.1",  # Cloudflare DNS
        ]
        
    def start_analysis(self):
        """Start network analysis"""
        if self.analyzing:
            return
            
        self.analyzing = True
        self.start_time = time.time()
        self.network_snapshots.clear()
        self.connection_metrics.clear()
        self.previous_snapshot = None
        
        # Start analysis thread
        self.analysis_thread = threading.Thread(target=self._analysis_loop)
        self.analysis_thread.daemon = True
        self.analysis_thread.start()
        
    def stop_analysis(self):
        """Stop network analysis"""
        self.analyzing = False
        
        if self.analysis_thread:
            self.analysis_thread.join(timeout=2.0)
            
    def _analysis_loop(self):
        """Main network analysis loop"""
        while self.analyzing:
            try:
                snapshot = self._take_network_snapshot()
                self.network_snapshots.append(snapshot)
                
                # Update connection metrics
                self._update_connection_metrics()
                
                # Establish baseline
                if len(self.network_snapshots) == self.baseline_snapshots:
                    self.baseline_network = self._calculate_baseline()
                
                # Detect bottlenecks
                if len(self.network_snapshots) > self.baseline_snapshots:
                    bottleneck = self._detect_bottlenecks(snapshot)
                    if bottleneck.detected:
                        self.bottleneck_history.append(bottleneck)
                        
                self.previous_snapshot = snapshot
                
            except Exception as e:
                print(f"Error in network analysis loop: {e}")
                
            time.sleep(self.analysis_interval)
            
    def _take_network_snapshot(self) -> NetworkSnapshot:
        """Take a network performance snapshot"""
        # System network I/O
        net_io = psutil.net_io_counters()
        
        # Process network connections
        try:
            connections = self.process.connections(kind='inet')
            connections_established = len([c for c in connections if c.status == 'ESTABLISHED'])
            connections_active = len(connections)
        except:
            connections_established = 0
            connections_active = 0
            
        # Calculate rates if we have a previous snapshot
        bytes_sent_per_sec = 0
        bytes_recv_per_sec = 0
        packets_sent_per_sec = 0
        packets_recv_per_sec = 0
        
        if self.previous_snapshot:
            time_diff = time.time() - self.previous_snapshot.timestamp
            if time_diff > 0:
                bytes_sent_per_sec = (net_io.bytes_sent - self.previous_snapshot.bytes_sent) / time_diff
                bytes_recv_per_sec = (net_io.bytes_recv - self.previous_snapshot.bytes_recv) / time_diff
                packets_sent_per_sec = (net_io.packets_sent - self.previous_snapshot.packets_sent) / time_diff
                packets_recv_per_sec = (net_io.packets_recv - self.previous_snapshot.packets_recv) / time_diff
                
        # Measure network latency
        latency_ms = self._measure_network_latency()
        
        # Measure DNS resolution time
        dns_time_ms = self._measure_dns_resolution_time()
        
        return NetworkSnapshot(
            timestamp=time.time(),
            bytes_sent=net_io.bytes_sent,
            bytes_recv=net_io.bytes_recv,
            packets_sent=net_io.packets_sent,
            packets_recv=net_io.packets_recv,
            bytes_sent_per_sec=bytes_sent_per_sec,
            bytes_recv_per_sec=bytes_recv_per_sec,
            packets_sent_per_sec=packets_sent_per_sec,
            packets_recv_per_sec=packets_recv_per_sec,
            connections_established=connections_established,
            connections_active=connections_active,
            network_latency_ms=latency_ms,
            dns_resolution_time_ms=dns_time_ms
        )
        
    def _measure_network_latency(self) -> Optional[float]:
        """Measure network latency to monitoring targets"""
        latencies = []
        
        for target in self.monitoring_targets:
            try:
                start_time = time.time()
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2.0)
                
                result = sock.connect_ex((target, 53))  # DNS port
                end_time = time.time()
                
                if result == 0:
                    latency_ms = (end_time - start_time) * 1000
                    latencies.append(latency_ms)
                    
                sock.close()
                
            except Exception:
                continue
                
        return mean(latencies) if latencies else None
        
    def _measure_dns_resolution_time(self) -> Optional[float]:
        """Measure DNS resolution time"""
        try:
            start_time = time.time()
            socket.gethostbyname("google.com")
            end_time = time.time()
            
            return (end_time - start_time) * 1000
            
        except Exception:
            return None
            
    def _update_connection_metrics(self):
        """Update connection metrics"""
        try:
            connections = self.process.connections(kind='inet')
            current_connections = {}
            
            for conn in connections:
                if conn.raddr:
                    conn_id = f"{conn.laddr[0]}:{conn.laddr[1]}-{conn.raddr[0]}:{conn.raddr[1]}"
                    
                    if conn_id not in self.connection_metrics:
                        # New connection
                        self.connection_metrics[conn_id] = ConnectionMetrics(
                            connection_id=conn_id,
                            local_address=f"{conn.laddr[0]}:{conn.laddr[1]}",
                            remote_address=f"{conn.raddr[0]}:{conn.raddr[1]}",
                            status=conn.status,
                            family="IPv4" if conn.family == socket.AF_INET else "IPv6",
                            type="TCP" if conn.type == socket.SOCK_STREAM else "UDP",
                            established_time=time.time(),
                            bytes_sent=0,
                            bytes_recv=0
                        )
                    
                    current_connections[conn_id] = True
                    
            # Remove closed connections
            closed_connections = [conn_id for conn_id in self.connection_metrics.keys() 
                                if conn_id not in current_connections]
            for conn_id in closed_connections:
                del self.connection_metrics[conn_id]
                
        except Exception as e:
            print(f"Error updating connection metrics: {e}")
            
    def _calculate_baseline(self) -> Dict[str, Any]:
        """Calculate network baseline from initial snapshots"""
        if len(self.network_snapshots) < self.baseline_snapshots:
            return {}
            
        baseline_snapshots = self.network_snapshots[:self.baseline_snapshots]
        
        bytes_sent_per_sec = [s.bytes_sent_per_sec for s in baseline_snapshots if s.bytes_sent_per_sec > 0]
        bytes_recv_per_sec = [s.bytes_recv_per_sec for s in baseline_snapshots if s.bytes_recv_per_sec > 0]
        latencies = [s.network_latency_ms for s in baseline_snapshots if s.network_latency_ms is not None]
        
        return {
            "avg_bytes_sent_per_sec": mean(bytes_sent_per_sec) if bytes_sent_per_sec else 0,
            "avg_bytes_recv_per_sec": mean(bytes_recv_per_sec) if bytes_recv_per_sec else 0,
            "avg_latency_ms": mean(latencies) if latencies else 0,
            "avg_connections": mean([s.connections_active for s in baseline_snapshots]),
            "snapshot_count": len(baseline_snapshots)
        }
        
    def _detect_bottlenecks(self, snapshot: NetworkSnapshot) -> NetworkBottleneck:
        """Detect network bottlenecks"""
        bottlenecks = []
        
        # High bandwidth usage detection
        total_bandwidth_mbps = (snapshot.bytes_sent_per_sec + snapshot.bytes_recv_per_sec) * 8 / 1_000_000
        if total_bandwidth_mbps > self.high_bandwidth_threshold_mbps:
            severity = "critical" if total_bandwidth_mbps > self.high_bandwidth_threshold_mbps * 2 else "high"
            bottlenecks.append(NetworkBottleneck(
                detected=True,
                bottleneck_type="bandwidth",
                severity=severity,
                metrics={"bandwidth_mbps": total_bandwidth_mbps},
                recommendations=self._get_bandwidth_recommendations(total_bandwidth_mbps),
                confidence=min(1.0, total_bandwidth_mbps / self.high_bandwidth_threshold_mbps)
            ))
            
        # High latency detection
        if snapshot.network_latency_ms and snapshot.network_latency_ms > self.high_latency_threshold_ms:
            severity = "critical" if snapshot.network_latency_ms > self.high_latency_threshold_ms * 2 else "high"
            bottlenecks.append(NetworkBottleneck(
                detected=True,
                bottleneck_type="latency",
                severity=severity,
                metrics={"latency_ms": snapshot.network_latency_ms},
                recommendations=self._get_latency_recommendations(snapshot.network_latency_ms),
                confidence=min(1.0, snapshot.network_latency_ms / self.high_latency_threshold_ms)
            ))
            
        # DNS resolution time detection
        if snapshot.dns_resolution_time_ms and snapshot.dns_resolution_time_ms > self.dns_slow_threshold_ms:
            severity = "medium" if snapshot.dns_resolution_time_ms < self.dns_slow_threshold_ms * 2 else "high"
            bottlenecks.append(NetworkBottleneck(
                detected=True,
                bottleneck_type="dns",
                severity=severity,
                metrics={"dns_resolution_time_ms": snapshot.dns_resolution_time_ms},
                recommendations=self._get_dns_recommendations(snapshot.dns_resolution_time_ms),
                confidence=min(1.0, snapshot.dns_resolution_time_ms / self.dns_slow_threshold_ms)
            ))
            
        # Too many connections detection
        if snapshot.connections_active > self.max_connections_threshold:
            severity = "high" if snapshot.connections_active > self.max_connections_threshold * 2 else "medium"
            bottlenecks.append(NetworkBottleneck(
                detected=True,
                bottleneck_type="connections",
                severity=severity,
                metrics={"active_connections": snapshot.connections_active},
                recommendations=self._get_connections_recommendations(snapshot.connections_active),
                confidence=min(1.0, snapshot.connections_active / self.max_connections_threshold)
            ))
            
        # Return most severe bottleneck or no bottleneck
        if bottlenecks:
            return max(bottlenecks, key=lambda b: {"critical": 4, "high": 3, "medium": 2, "low": 1}[b.severity])
        else:
            return NetworkBottleneck(
                detected=False,
                bottleneck_type="none",
                severity="low",
                metrics={},
                recommendations=[],
                confidence=0.0
            )
            
    def _get_bandwidth_recommendations(self, bandwidth_mbps: float) -> List[str]:
        """Get recommendations for high bandwidth usage"""
        recommendations = []
        
        if bandwidth_mbps > self.high_bandwidth_threshold_mbps * 2:
            recommendations.append("CRITICAL: Extremely high bandwidth usage detected")
            
        recommendations.extend([
            "Implement response compression (gzip, br)",
            "Add content caching to reduce repeated transfers",
            "Optimize payload sizes (remove unnecessary data)",
            "Implement pagination for large datasets",
            "Consider using HTTP/2 for multiplexing",
            "Add bandwidth monitoring and alerting",
            "Review data serialization formats (JSON vs. MessagePack)",
            "Implement connection pooling and reuse"
        ])
        
        return recommendations
        
    def _get_latency_recommendations(self, latency_ms: float) -> List[str]:
        """Get recommendations for high latency"""
        recommendations = []
        
        if latency_ms > self.high_latency_threshold_ms * 2:
            recommendations.append("CRITICAL: Very high network latency detected")
            
        recommendations.extend([
            "Check network infrastructure and routing",
            "Implement connection pooling",
            "Use CDN for static content",
            "Optimize DNS resolution",
            "Consider geographic load balancing",
            "Implement circuit breakers for external services",
            "Add timeout configurations",
            "Review firewall and proxy settings"
        ])
        
        return recommendations
        
    def _get_dns_recommendations(self, dns_time_ms: float) -> List[str]:
        """Get recommendations for slow DNS resolution"""
        return [
            "Implement DNS caching",
            "Use faster DNS servers (1.1.1.1, 8.8.8.8)",
            "Configure DNS connection pooling",
            "Add DNS resolution monitoring",
            "Consider DNS prefetching",
            "Review DNS server configuration",
            "Implement DNS failover mechanisms"
        ]
        
    def _get_connections_recommendations(self, connections: int) -> List[str]:
        """Get recommendations for high connection count"""
        return [
            "Implement connection pooling",
            "Add connection limits and throttling",
            "Review connection timeout settings",
            "Implement connection reuse",
            "Consider using HTTP/2 for multiplexing",
            "Add connection monitoring and alerting",
            "Review database connection management",
            "Implement graceful connection handling"
        ]
        
    def get_network_statistics(self) -> Dict[str, Any]:
        """Get comprehensive network statistics"""
        if not self.network_snapshots:
            return {}
            
        # Bandwidth statistics
        bytes_sent_per_sec = [s.bytes_sent_per_sec for s in self.network_snapshots if s.bytes_sent_per_sec > 0]
        bytes_recv_per_sec = [s.bytes_recv_per_sec for s in self.network_snapshots if s.bytes_recv_per_sec > 0]
        
        # Latency statistics
        latencies = [s.network_latency_ms for s in self.network_snapshots if s.network_latency_ms is not None]
        
        # DNS statistics
        dns_times = [s.dns_resolution_time_ms for s in self.network_snapshots if s.dns_resolution_time_ms is not None]
        
        # Connection statistics
        connections = [s.connections_active for s in self.network_snapshots]
        
        return {
            "bandwidth": {
                "bytes_sent_per_sec": {
                    "mean": mean(bytes_sent_per_sec) if bytes_sent_per_sec else 0,
                    "median": median(bytes_sent_per_sec) if bytes_sent_per_sec else 0,
                    "max": max(bytes_sent_per_sec) if bytes_sent_per_sec else 0,
                    "p95": sorted(bytes_sent_per_sec)[int(0.95 * len(bytes_sent_per_sec))] if bytes_sent_per_sec else 0
                },
                "bytes_recv_per_sec": {
                    "mean": mean(bytes_recv_per_sec) if bytes_recv_per_sec else 0,
                    "median": median(bytes_recv_per_sec) if bytes_recv_per_sec else 0,
                    "max": max(bytes_recv_per_sec) if bytes_recv_per_sec else 0,
                    "p95": sorted(bytes_recv_per_sec)[int(0.95 * len(bytes_recv_per_sec))] if bytes_recv_per_sec else 0
                },
                "total_mbps": {
                    "mean": mean([(s.bytes_sent_per_sec + s.bytes_recv_per_sec) * 8 / 1_000_000 for s in self.network_snapshots]),
                    "peak": max([(s.bytes_sent_per_sec + s.bytes_recv_per_sec) * 8 / 1_000_000 for s in self.network_snapshots])
                }
            },
            "latency": {
                "mean_ms": mean(latencies) if latencies else 0,
                "median_ms": median(latencies) if latencies else 0,
                "max_ms": max(latencies) if latencies else 0,
                "min_ms": min(latencies) if latencies else 0,
                "p95_ms": sorted(latencies)[int(0.95 * len(latencies))] if latencies else 0,
                "p99_ms": sorted(latencies)[int(0.99 * len(latencies))] if latencies else 0
            },
            "dns_resolution": {
                "mean_ms": mean(dns_times) if dns_times else 0,
                "median_ms": median(dns_times) if dns_times else 0,
                "max_ms": max(dns_times) if dns_times else 0,
                "p95_ms": sorted(dns_times)[int(0.95 * len(dns_times))] if dns_times else 0
            },
            "connections": {
                "mean_active": mean(connections),
                "median_active": median(connections),
                "max_active": max(connections),
                "min_active": min(connections),
                "current_active": connections[-1] if connections else 0
            },
            "analysis_duration": time.time() - self.start_time if self.start_time else 0,
            "snapshots_count": len(self.network_snapshots)
        }
        
    def get_connection_analysis(self) -> Dict[str, Any]:
        """Get connection analysis"""
        if not self.connection_metrics:
            return {}
            
        # Group connections by remote host
        host_connections = defaultdict(list)
        for conn in self.connection_metrics.values():
            host = conn.remote_address.split(':')[0]
            host_connections[host].append(conn)
            
        # Analyze connections per host
        host_analysis = {}
        for host, connections in host_connections.items():
            host_analysis[host] = {
                "connection_count": len(connections),
                "avg_duration": mean([time.time() - conn.established_time for conn in connections]),
                "status_distribution": self._get_status_distribution(connections),
                "total_bytes_sent": sum([conn.bytes_sent for conn in connections]),
                "total_bytes_recv": sum([conn.bytes_recv for conn in connections])
            }
            
        return {
            "total_connections": len(self.connection_metrics),
            "unique_hosts": len(host_connections),
            "host_analysis": host_analysis,
            "connection_types": self._get_connection_type_distribution(),
            "long_lived_connections": len([c for c in self.connection_metrics.values() 
                                         if time.time() - c.established_time > 300])  # 5 minutes
        }
        
    def _get_status_distribution(self, connections: List[ConnectionMetrics]) -> Dict[str, int]:
        """Get distribution of connection statuses"""
        status_count = defaultdict(int)
        for conn in connections:
            status_count[conn.status] += 1
        return dict(status_count)
        
    def _get_connection_type_distribution(self) -> Dict[str, int]:
        """Get distribution of connection types"""
        type_count = defaultdict(int)
        for conn in self.connection_metrics.values():
            type_count[conn.type] += 1
        return dict(type_count)
        
    def analyze_network_trends(self, window_size: int = 30) -> Dict[str, Any]:
        """Analyze network performance trends"""
        if len(self.network_snapshots) < window_size:
            return {"error": "Insufficient data for trend analysis"}
            
        recent_snapshots = self.network_snapshots[-window_size:]
        
        # Calculate trends
        bandwidth_values = [(s.bytes_sent_per_sec + s.bytes_recv_per_sec) * 8 / 1_000_000 for s in recent_snapshots]
        latency_values = [s.network_latency_ms for s in recent_snapshots if s.network_latency_ms is not None]
        connection_values = [s.connections_active for s in recent_snapshots]
        
        bandwidth_trend = self._calculate_trend(bandwidth_values)
        latency_trend = self._calculate_trend(latency_values) if latency_values else 0
        connection_trend = self._calculate_trend(connection_values)
        
        return {
            "bandwidth_trend": {
                "slope_mbps": bandwidth_trend,
                "direction": "increasing" if bandwidth_trend > 0.1 else "decreasing" if bandwidth_trend < -0.1 else "stable",
                "current_mbps": bandwidth_values[-1] if bandwidth_values else 0
            },
            "latency_trend": {
                "slope_ms": latency_trend,
                "direction": "increasing" if latency_trend > 1 else "decreasing" if latency_trend < -1 else "stable",
                "current_ms": latency_values[-1] if latency_values else 0
            },
            "connection_trend": {
                "slope": connection_trend,
                "direction": "increasing" if connection_trend > 0.1 else "decreasing" if connection_trend < -0.1 else "stable",
                "current_count": connection_values[-1] if connection_values else 0
            },
            "window_size": window_size,
            "analysis_confidence": min(1.0, len(recent_snapshots) / window_size)
        }
        
    def _calculate_trend(self, values: List[float]) -> float:
        """Calculate trend slope using linear regression"""
        if len(values) < 2:
            return 0.0
            
        n = len(values)
        x_values = list(range(n))
        x_mean = mean(x_values)
        y_mean = mean(values)
        
        numerator = sum((x - x_mean) * (y - y_mean) for x, y in zip(x_values, values))
        denominator = sum((x - x_mean) ** 2 for x in x_values)
        
        return numerator / denominator if denominator != 0 else 0.0
        
    def get_analysis_report(self) -> Dict[str, Any]:
        """Generate comprehensive network analysis report"""
        if not self.network_snapshots:
            return {"error": "No network analysis data available"}
            
        stats = self.get_network_statistics()
        trends = self.analyze_network_trends()
        connections = self.get_connection_analysis()
        
        # Recent bottlenecks
        recent_bottlenecks = [b.to_dict() for b in list(self.bottleneck_history)[-10:]]
        
        return {
            "analysis_duration_hours": (time.time() - self.start_time) / 3600 if self.start_time else 0,
            "snapshots_analyzed": len(self.network_snapshots),
            "network_statistics": stats,
            "trend_analysis": trends,
            "connection_analysis": connections,
            "recent_bottlenecks": recent_bottlenecks,
            "baseline_metrics": self.baseline_network or {},
            "performance_score": self._calculate_performance_score(stats, trends),
            "analysis_confidence": min(1.0, len(self.network_snapshots) / 100)
        }
        
    def _calculate_performance_score(self, stats: Dict[str, Any], trends: Dict[str, Any]) -> float:
        """Calculate overall network performance score (0-100)"""
        # Base score from latency
        mean_latency = stats["latency"]["mean_ms"]
        if mean_latency < 50:
            latency_score = 100
        elif mean_latency < 200:
            latency_score = 100 - (mean_latency - 50) * 0.5
        else:
            latency_score = 100 - (mean_latency - 50) * 0.3
            
        # Bandwidth efficiency score
        peak_bandwidth = stats["bandwidth"]["total_mbps"]["peak"]
        if peak_bandwidth < 10:
            bandwidth_score = 100
        elif peak_bandwidth < 50:
            bandwidth_score = 100 - (peak_bandwidth - 10) * 0.5
        else:
            bandwidth_score = 100 - (peak_bandwidth - 10) * 0.3
            
        # Connection efficiency score
        max_connections = stats["connections"]["max_active"]
        if max_connections < 100:
            connection_score = 100
        elif max_connections < 500:
            connection_score = 100 - (max_connections - 100) * 0.1
        else:
            connection_score = 100 - (max_connections - 100) * 0.05
            
        # Penalty for negative trends
        trend_penalty = 0
        if trends.get("latency_trend", {}).get("direction") == "increasing":
            trend_penalty += 10
        if trends.get("bandwidth_trend", {}).get("direction") == "increasing":
            trend_penalty += 5
            
        # Penalty for bottlenecks
        bottleneck_penalty = len([b for b in self.bottleneck_history if b.severity in ["high", "critical"]]) * 5
        
        final_score = (latency_score + bandwidth_score + connection_score) / 3 - trend_penalty - bottleneck_penalty
        return max(0, min(100, final_score))
        
    def export_analysis_data(self, filename: str):
        """Export network analysis data"""
        data = {
            "metadata": {
                "start_time": self.start_time,
                "end_time": time.time(),
                "analysis_interval": self.analysis_interval,
                "snapshots_count": len(self.network_snapshots)
            },
            "snapshots": [s.to_dict() for s in self.network_snapshots],
            "connections": [c.to_dict() for c in self.connection_metrics.values()],
            "bottlenecks": [b.to_dict() for b in self.bottleneck_history],
            "analysis_report": self.get_analysis_report()
        }
        
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)


class NetworkBenchmarkRunner:
    """Run network benchmarks and analysis"""
    
    def __init__(self, analyzer: NetworkAnalyzer):
        self.analyzer = analyzer
        self.benchmark_results = {}
        
    async def run_http_benchmark(self, 
                                url: str = "https://httpbin.org/json",
                                concurrent_requests: int = 10,
                                duration_seconds: int = 60) -> Dict[str, Any]:
        """Run HTTP benchmark"""
        print(f"Running HTTP benchmark for {duration_seconds} seconds...")
        
        self.analyzer.start_analysis()
        
        request_results = []
        
        async def make_request(session: aiohttp.ClientSession, request_id: int):
            """Make a single HTTP request"""
            try:
                start_time = time.time()
                async with session.get(url) as response:
                    await response.read()
                    end_time = time.time()
                    
                    return {
                        "request_id": request_id,
                        "response_time": end_time - start_time,
                        "status_code": response.status,
                        "success": response.status == 200,
                        "bytes_received": len(await response.read()) if response.status == 200 else 0
                    }
            except Exception as e:
                return {
                    "request_id": request_id,
                    "success": False,
                    "error": str(e),
                    "response_time": 0,
                    "bytes_received": 0
                }
                
        # Run concurrent requests
        end_time = time.time() + duration_seconds
        
        async with aiohttp.ClientSession(
            connector=aiohttp.TCPConnector(limit=concurrent_requests * 2),
            timeout=aiohttp.ClientTimeout(total=30)
        ) as session:
            
            while time.time() < end_time:
                # Create batch of concurrent requests
                tasks = [make_request(session, i) for i in range(concurrent_requests)]
                
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for result in batch_results:
                    if isinstance(result, dict):
                        request_results.append(result)
                        
                # Brief pause between batches
                await asyncio.sleep(0.1)
                
        self.analyzer.stop_analysis()
        
        # Analyze results
        successful_requests = [r for r in request_results if r.get("success", False)]
        failed_requests = [r for r in request_results if not r.get("success", False)]
        
        result = self.analyzer.get_analysis_report()
        result.update({
            "benchmark_type": "http_requests",
            "duration_seconds": duration_seconds,
            "concurrent_requests": concurrent_requests,
            "total_requests": len(request_results),
            "successful_requests": len(successful_requests),
            "failed_requests": len(failed_requests),
            "success_rate": len(successful_requests) / len(request_results) * 100 if request_results else 0,
            "avg_response_time": mean([r["response_time"] for r in successful_requests]) if successful_requests else 0,
            "total_bytes_received": sum([r["bytes_received"] for r in successful_requests]),
            "requests_per_second": len(request_results) / duration_seconds
        })
        
        self.benchmark_results["http_benchmark"] = result
        return result
        
    async def run_websocket_benchmark(self,
                                    ws_url: str = "ws://echo.websocket.org",
                                    concurrent_connections: int = 5,
                                    messages_per_connection: int = 10) -> Dict[str, Any]:
        """Run WebSocket benchmark"""
        print(f"Running WebSocket benchmark with {concurrent_connections} connections...")
        
        self.analyzer.start_analysis()
        
        connection_results = []
        
        async def websocket_test(connection_id: int):
            """Test a single WebSocket connection"""
            try:
                import aiohttp
                
                session = aiohttp.ClientSession()
                
                async with session.ws_connect(ws_url) as ws:
                    messages_sent = 0
                    messages_received = 0
                    start_time = time.time()
                    
                    for i in range(messages_per_connection):
                        # Send message
                        message = f"Test message {i} from connection {connection_id}"
                        await ws.send_str(message)
                        messages_sent += 1
                        
                        # Receive response
                        try:
                            response = await asyncio.wait_for(ws.receive(), timeout=5.0)
                            if response.type == aiohttp.WSMsgType.TEXT:
                                messages_received += 1
                        except asyncio.TimeoutError:
                            pass
                            
                        await asyncio.sleep(0.1)
                        
                    end_time = time.time()
                    
                    await session.close()
                    
                    return {
                        "connection_id": connection_id,
                        "success": True,
                        "messages_sent": messages_sent,
                        "messages_received": messages_received,
                        "duration": end_time - start_time,
                        "success_rate": messages_received / messages_sent * 100 if messages_sent > 0 else 0
                    }
                    
            except Exception as e:
                return {
                    "connection_id": connection_id,
                    "success": False,
                    "error": str(e),
                    "messages_sent": 0,
                    "messages_received": 0,
                    "duration": 0,
                    "success_rate": 0
                }
                
        # Run concurrent WebSocket connections
        tasks = [websocket_test(i) for i in range(concurrent_connections)]
        connection_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        self.analyzer.stop_analysis()
        
        # Analyze results
        successful_connections = [r for r in connection_results if isinstance(r, dict) and r.get("success", False)]
        failed_connections = [r for r in connection_results if isinstance(r, Exception) or not r.get("success", False)]
        
        result = self.analyzer.get_analysis_report()
        result.update({
            "benchmark_type": "websocket_connections",
            "concurrent_connections": concurrent_connections,
            "messages_per_connection": messages_per_connection,
            "successful_connections": len(successful_connections),
            "failed_connections": len(failed_connections),
            "connection_success_rate": len(successful_connections) / concurrent_connections * 100,
            "total_messages_sent": sum([r["messages_sent"] for r in successful_connections]),
            "total_messages_received": sum([r["messages_received"] for r in successful_connections]),
            "avg_message_success_rate": mean([r["success_rate"] for r in successful_connections]) if successful_connections else 0,
            "avg_connection_duration": mean([r["duration"] for r in successful_connections]) if successful_connections else 0
        })
        
        self.benchmark_results["websocket_benchmark"] = result
        return result
        
    def export_benchmark_results(self, filename: str):
        """Export benchmark results"""
        data = {
            "timestamp": datetime.now().isoformat(),
            "benchmark_results": self.benchmark_results
        }
        
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)