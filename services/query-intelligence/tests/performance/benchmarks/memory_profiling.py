"""
Memory Usage Profiling and Analysis Framework

Comprehensive memory profiling tools for analyzing memory usage patterns,
detecting memory leaks, and optimizing resource allocation.
"""

import asyncio
import time
import gc
import sys
import threading
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
from statistics import mean, median, stdev
import tracemalloc
import psutil
import os
import json
from datetime import datetime
import weakref
import resource
import subprocess


@dataclass
class MemorySnapshot:
    """Detailed memory snapshot at a specific point in time"""
    timestamp: float
    rss_mb: float  # Resident Set Size
    vms_mb: float  # Virtual Memory Size
    shared_mb: float  # Shared memory
    text_mb: float  # Text (code) memory
    lib_mb: float  # Library memory
    data_mb: float  # Data memory
    dirty_mb: float  # Dirty memory
    heap_mb: float  # Python heap memory
    gc_objects: int  # Number of GC objects
    gc_collections: Dict[int, int]  # GC collections per generation
    python_objects: int  # Python object count
    file_descriptors: int  # Open file descriptors
    threads: int  # Number of threads
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "timestamp": self.timestamp,
            "rss_mb": self.rss_mb,
            "vms_mb": self.vms_mb,
            "shared_mb": self.shared_mb,
            "text_mb": self.text_mb,
            "lib_mb": self.lib_mb,
            "data_mb": self.data_mb,
            "dirty_mb": self.dirty_mb,
            "heap_mb": self.heap_mb,
            "gc_objects": self.gc_objects,
            "gc_collections": self.gc_collections,
            "python_objects": self.python_objects,
            "file_descriptors": self.file_descriptors,
            "threads": self.threads
        }


@dataclass
class MemoryAllocation:
    """Memory allocation record from tracemalloc"""
    filename: str
    lineno: int
    size_mb: float
    count: int
    traceback: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "filename": self.filename,
            "lineno": self.lineno,
            "size_mb": self.size_mb,
            "count": self.count,
            "traceback": self.traceback
        }


@dataclass
class MemoryLeakDetection:
    """Memory leak detection result"""
    detected: bool
    growth_rate_mb_per_hour: float
    confidence: float  # 0-1 confidence level
    trend_analysis: Dict[str, Any]
    potential_sources: List[Dict[str, Any]]
    recommendations: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "detected": self.detected,
            "growth_rate_mb_per_hour": self.growth_rate_mb_per_hour,
            "confidence": self.confidence,
            "trend_analysis": self.trend_analysis,
            "potential_sources": self.potential_sources,
            "recommendations": self.recommendations
        }


class MemoryProfiler:
    """Advanced memory profiler with leak detection and optimization analysis"""
    
    def __init__(self, 
                 profile_interval: float = 0.5,
                 enable_tracemalloc: bool = True,
                 tracemalloc_limit: int = 25):
        self.profile_interval = profile_interval
        self.enable_tracemalloc = enable_tracemalloc
        self.tracemalloc_limit = tracemalloc_limit
        
        # Data storage
        self.memory_snapshots: List[MemorySnapshot] = []
        self.allocations_history: List[List[MemoryAllocation]] = []
        
        # Profiling state
        self.profiling = False
        self.profile_thread = None
        self.start_time = None
        self.process = psutil.Process()
        
        # Leak detection
        self.leak_detection_window = 50  # Number of snapshots for leak detection
        self.leak_threshold_mb_per_hour = 50.0  # MB per hour growth threshold
        
        # Object tracking
        self.object_refs = weakref.WeakSet()
        self.object_counts = defaultdict(int)
        
        # Performance tracking
        self.gc_stats = {"collections": defaultdict(int), "time": 0}
        
    def start_profiling(self):
        """Start memory profiling"""
        if self.profiling:
            return
            
        self.profiling = True
        self.start_time = time.time()
        
        # Clear previous data
        self.memory_snapshots.clear()
        self.allocations_history.clear()
        
        # Start tracemalloc if enabled
        if self.enable_tracemalloc:
            tracemalloc.start(self.tracemalloc_limit)
            
        # Start profiling thread
        self.profile_thread = threading.Thread(target=self._profile_loop)
        self.profile_thread.daemon = True
        self.profile_thread.start()
        
    def stop_profiling(self):
        """Stop memory profiling"""
        self.profiling = False
        
        if self.profile_thread:
            self.profile_thread.join(timeout=2.0)
            
        if self.enable_tracemalloc:
            tracemalloc.stop()
            
    def _profile_loop(self):
        """Main profiling loop"""
        while self.profiling:
            try:
                snapshot = self._take_memory_snapshot()
                self.memory_snapshots.append(snapshot)
                
                # Track allocations if tracemalloc is enabled
                if self.enable_tracemalloc:
                    allocations = self._get_current_allocations()
                    self.allocations_history.append(allocations)
                
                # Track object counts
                self._track_object_counts()
                
                # Trigger GC periodically and track stats
                if len(self.memory_snapshots) % 10 == 0:
                    self._track_gc_performance()
                    
            except Exception as e:
                print(f"Error in memory profiling loop: {e}")
                
            time.sleep(self.profile_interval)
            
    def _take_memory_snapshot(self) -> MemorySnapshot:
        """Take a detailed memory snapshot"""
        # Process memory info
        memory_info = self.process.memory_info()
        memory_full = self.process.memory_full_info()
        
        # GC statistics
        gc_counts = gc.get_count()
        gc_stats = {i: gc.get_stats()[i]['collections'] for i in range(len(gc.get_stats()))}
        
        # Object count
        python_objects = len(gc.get_objects())
        
        # File descriptors
        try:
            file_descriptors = self.process.num_fds()
        except:
            file_descriptors = 0
            
        # Thread count
        threads = self.process.num_threads()
        
        # Python heap size (approximate)
        heap_mb = sys.getsizeof(gc.get_objects()) / 1024 / 1024 if gc.get_objects() else 0
        
        return MemorySnapshot(
            timestamp=time.time(),
            rss_mb=memory_info.rss / 1024 / 1024,
            vms_mb=memory_info.vms / 1024 / 1024,
            shared_mb=getattr(memory_full, 'shared', 0) / 1024 / 1024,
            text_mb=getattr(memory_full, 'text', 0) / 1024 / 1024,
            lib_mb=getattr(memory_full, 'lib', 0) / 1024 / 1024,
            data_mb=getattr(memory_full, 'data', 0) / 1024 / 1024,
            dirty_mb=getattr(memory_full, 'dirty', 0) / 1024 / 1024,
            heap_mb=heap_mb,
            gc_objects=sum(gc_counts),
            gc_collections=gc_stats,
            python_objects=python_objects,
            file_descriptors=file_descriptors,
            threads=threads
        )
        
    def _get_current_allocations(self) -> List[MemoryAllocation]:
        """Get current memory allocations from tracemalloc"""
        if not tracemalloc.is_tracing():
            return []
            
        snapshot = tracemalloc.take_snapshot()
        top_stats = snapshot.statistics('lineno')
        
        allocations = []
        for stat in top_stats[:self.tracemalloc_limit]:
            allocations.append(MemoryAllocation(
                filename=stat.traceback.format()[0].split(', ')[0],
                lineno=stat.traceback.format()[0].split(', ')[1],
                size_mb=stat.size / 1024 / 1024,
                count=stat.count,
                traceback='\n'.join(stat.traceback.format()) if len(stat.traceback.format()) > 1 else None
            ))
            
        return allocations
        
    def _track_object_counts(self):
        """Track object counts by type"""
        objects = gc.get_objects()
        type_counts = defaultdict(int)
        
        for obj in objects:
            type_counts[type(obj).__name__] += 1
            
        self.object_counts = type_counts
        
    def _track_gc_performance(self):
        """Track garbage collection performance"""
        gc_start = time.time()
        collected = gc.collect()
        gc_time = time.time() - gc_start
        
        self.gc_stats["time"] += gc_time
        self.gc_stats["collections"][len(self.memory_snapshots)] = collected
        
    def get_memory_usage_baseline(self) -> Dict[str, Any]:
        """Get baseline memory usage metrics"""
        if not self.memory_snapshots:
            return {}
            
        first_snapshot = self.memory_snapshots[0]
        
        return {
            "rss_mb": first_snapshot.rss_mb,
            "vms_mb": first_snapshot.vms_mb,
            "heap_mb": first_snapshot.heap_mb,
            "gc_objects": first_snapshot.gc_objects,
            "python_objects": first_snapshot.python_objects,
            "file_descriptors": first_snapshot.file_descriptors,
            "threads": first_snapshot.threads,
            "timestamp": first_snapshot.timestamp
        }
        
    def get_memory_usage_current(self) -> Dict[str, Any]:
        """Get current memory usage metrics"""
        if not self.memory_snapshots:
            return {}
            
        latest_snapshot = self.memory_snapshots[-1]
        
        return {
            "rss_mb": latest_snapshot.rss_mb,
            "vms_mb": latest_snapshot.vms_mb,
            "heap_mb": latest_snapshot.heap_mb,
            "gc_objects": latest_snapshot.gc_objects,
            "python_objects": latest_snapshot.python_objects,
            "file_descriptors": latest_snapshot.file_descriptors,
            "threads": latest_snapshot.threads,
            "timestamp": latest_snapshot.timestamp
        }
        
    def get_memory_usage_peak(self) -> Dict[str, Any]:
        """Get peak memory usage metrics"""
        if not self.memory_snapshots:
            return {}
            
        peak_rss = max(s.rss_mb for s in self.memory_snapshots)
        peak_vms = max(s.vms_mb for s in self.memory_snapshots)
        peak_heap = max(s.heap_mb for s in self.memory_snapshots)
        peak_objects = max(s.python_objects for s in self.memory_snapshots)
        
        return {
            "peak_rss_mb": peak_rss,
            "peak_vms_mb": peak_vms,
            "peak_heap_mb": peak_heap,
            "peak_python_objects": peak_objects,
            "peak_gc_objects": max(s.gc_objects for s in self.memory_snapshots),
            "peak_file_descriptors": max(s.file_descriptors for s in self.memory_snapshots),
            "peak_threads": max(s.threads for s in self.memory_snapshots)
        }
        
    def analyze_memory_growth(self, window_size: int = 10) -> Dict[str, Any]:
        """Analyze memory growth patterns"""
        if len(self.memory_snapshots) < window_size:
            return {"error": "Insufficient data for growth analysis"}
            
        recent_snapshots = self.memory_snapshots[-window_size:]
        
        # Calculate growth rates
        time_span = recent_snapshots[-1].timestamp - recent_snapshots[0].timestamp
        
        rss_growth = (recent_snapshots[-1].rss_mb - recent_snapshots[0].rss_mb) / time_span * 3600  # MB/hour
        vms_growth = (recent_snapshots[-1].vms_mb - recent_snapshots[0].vms_mb) / time_span * 3600
        heap_growth = (recent_snapshots[-1].heap_mb - recent_snapshots[0].heap_mb) / time_span * 3600
        objects_growth = (recent_snapshots[-1].python_objects - recent_snapshots[0].python_objects) / time_span * 3600
        
        # Calculate trend (linear regression slope)
        rss_values = [s.rss_mb for s in recent_snapshots]
        time_values = [s.timestamp - recent_snapshots[0].timestamp for s in recent_snapshots]
        
        rss_trend = self._calculate_trend(time_values, rss_values)
        
        return {
            "rss_growth_mb_per_hour": rss_growth,
            "vms_growth_mb_per_hour": vms_growth,
            "heap_growth_mb_per_hour": heap_growth,
            "objects_growth_per_hour": objects_growth,
            "rss_trend_slope": rss_trend,
            "window_size": window_size,
            "analysis_window_hours": time_span / 3600
        }
        
    def detect_memory_leaks(self) -> MemoryLeakDetection:
        """Detect potential memory leaks"""
        if len(self.memory_snapshots) < self.leak_detection_window:
            return MemoryLeakDetection(
                detected=False,
                growth_rate_mb_per_hour=0.0,
                confidence=0.0,
                trend_analysis={},
                potential_sources=[],
                recommendations=["Insufficient data for leak detection"]
            )
            
        # Analyze recent memory growth
        growth_analysis = self.analyze_memory_growth(self.leak_detection_window)
        
        # Detect leak based on growth rate and trend
        rss_growth = growth_analysis.get("rss_growth_mb_per_hour", 0)
        trend_slope = growth_analysis.get("rss_trend_slope", 0)
        
        # Leak detection logic
        leak_detected = (
            rss_growth > self.leak_threshold_mb_per_hour and
            trend_slope > 0.1  # Positive trend
        )
        
        # Calculate confidence based on trend consistency
        rss_values = [s.rss_mb for s in self.memory_snapshots[-self.leak_detection_window:]]
        growth_consistency = self._calculate_trend_consistency(rss_values)
        
        confidence = min(1.0, growth_consistency * (rss_growth / self.leak_threshold_mb_per_hour))
        
        # Identify potential sources
        potential_sources = self._identify_leak_sources()
        
        # Generate recommendations
        recommendations = self._generate_leak_recommendations(leak_detected, rss_growth, potential_sources)
        
        return MemoryLeakDetection(
            detected=leak_detected,
            growth_rate_mb_per_hour=rss_growth,
            confidence=confidence,
            trend_analysis=growth_analysis,
            potential_sources=potential_sources,
            recommendations=recommendations
        )
        
    def _calculate_trend(self, x_values: List[float], y_values: List[float]) -> float:
        """Calculate linear trend (slope)"""
        if len(x_values) < 2:
            return 0.0
            
        n = len(x_values)
        x_mean = mean(x_values)
        y_mean = mean(y_values)
        
        numerator = sum((x - x_mean) * (y - y_mean) for x, y in zip(x_values, y_values))
        denominator = sum((x - x_mean) ** 2 for x in x_values)
        
        return numerator / denominator if denominator != 0 else 0.0
        
    def _calculate_trend_consistency(self, values: List[float]) -> float:
        """Calculate trend consistency (0-1)"""
        if len(values) < 3:
            return 0.0
            
        # Calculate how consistently the trend increases
        increases = sum(1 for i in range(1, len(values)) if values[i] > values[i-1])
        consistency = increases / (len(values) - 1)
        
        return consistency
        
    def _identify_leak_sources(self) -> List[Dict[str, Any]]:
        """Identify potential memory leak sources"""
        sources = []
        
        # Check for growing allocations
        if self.allocations_history and len(self.allocations_history) > 5:
            recent_allocations = self.allocations_history[-5:]
            
            # Track allocations that are consistently growing
            allocation_growth = defaultdict(list)
            
            for allocations in recent_allocations:
                for allocation in allocations:
                    key = f"{allocation.filename}:{allocation.lineno}"
                    allocation_growth[key].append(allocation.size_mb)
                    
            # Identify growing allocations
            for key, sizes in allocation_growth.items():
                if len(sizes) >= 3:
                    trend = self._calculate_trend(list(range(len(sizes))), sizes)
                    if trend > 0.1:  # Growing trend
                        sources.append({
                            "type": "allocation_growth",
                            "location": key,
                            "growth_trend": trend,
                            "current_size_mb": sizes[-1],
                            "confidence": min(1.0, trend * 5)
                        })
                        
        # Check for object count growth
        if len(self.memory_snapshots) > 10:
            recent_snapshots = self.memory_snapshots[-10:]
            object_growth = (recent_snapshots[-1].python_objects - recent_snapshots[0].python_objects) / len(recent_snapshots)
            
            if object_growth > 1000:  # More than 1000 objects per snapshot
                sources.append({
                    "type": "object_count_growth",
                    "growth_rate": object_growth,
                    "current_objects": recent_snapshots[-1].python_objects,
                    "confidence": min(1.0, object_growth / 10000)
                })
                
        return sources
        
    def _generate_leak_recommendations(self, leak_detected: bool, growth_rate: float, sources: List[Dict[str, Any]]) -> List[str]:
        """Generate memory leak recommendations"""
        recommendations = []
        
        if leak_detected:
            recommendations.append(f"Memory leak detected with growth rate of {growth_rate:.1f} MB/hour")
            
            if growth_rate > 100:
                recommendations.append("CRITICAL: High memory growth rate detected. Immediate investigation required.")
            elif growth_rate > 50:
                recommendations.append("WARNING: Moderate memory growth rate. Monitor closely.")
                
            # Specific recommendations based on sources
            for source in sources:
                if source["type"] == "allocation_growth":
                    recommendations.append(f"Check allocation at {source['location']} - growing at {source['growth_trend']:.3f} MB/snapshot")
                elif source["type"] == "object_count_growth":
                    recommendations.append(f"Object count growing by {source['growth_rate']:.0f} objects/snapshot - check for unreleased references")
                    
            # General recommendations
            recommendations.extend([
                "Run garbage collection more frequently",
                "Review object lifecycle management",
                "Check for circular references",
                "Consider using memory profiling tools like memory_profiler",
                "Monitor cache sizes and implement cache eviction policies"
            ])
        else:
            recommendations.append("No memory leaks detected")
            if growth_rate > 0:
                recommendations.append(f"Memory growth rate of {growth_rate:.1f} MB/hour is within normal limits")
                
        return recommendations
        
    def get_profiling_report(self) -> Dict[str, Any]:
        """Generate comprehensive profiling report"""
        if not self.memory_snapshots:
            return {"error": "No profiling data available"}
            
        baseline = self.get_memory_usage_baseline()
        current = self.get_memory_usage_current()
        peak = self.get_memory_usage_peak()
        growth = self.analyze_memory_growth()
        leak_detection = self.detect_memory_leaks()
        
        # Calculate summary statistics
        rss_values = [s.rss_mb for s in self.memory_snapshots]
        
        return {
            "profiling_duration_hours": (time.time() - self.start_time) / 3600 if self.start_time else 0,
            "snapshots_collected": len(self.memory_snapshots),
            "baseline_memory": baseline,
            "current_memory": current,
            "peak_memory": peak,
            "memory_statistics": {
                "rss_mean_mb": mean(rss_values),
                "rss_median_mb": median(rss_values),
                "rss_stddev_mb": stdev(rss_values) if len(rss_values) > 1 else 0,
                "rss_min_mb": min(rss_values),
                "rss_max_mb": max(rss_values)
            },
            "growth_analysis": growth,
            "leak_detection": leak_detection.to_dict(),
            "gc_performance": {
                "total_gc_time": self.gc_stats["time"],
                "total_collections": sum(self.gc_stats["collections"].values()),
                "avg_gc_time": self.gc_stats["time"] / max(1, sum(self.gc_stats["collections"].values()))
            },
            "object_counts": dict(self.object_counts),
            "allocations_tracked": len(self.allocations_history) if self.allocations_history else 0
        }
        
    def export_snapshots(self, filename: str):
        """Export memory snapshots to file"""
        data = {
            "metadata": {
                "start_time": self.start_time,
                "end_time": time.time(),
                "profile_interval": self.profile_interval,
                "snapshots_count": len(self.memory_snapshots)
            },
            "snapshots": [s.to_dict() for s in self.memory_snapshots]
        }
        
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)
            
    def get_memory_usage_per_connection(self, connection_count: int) -> Dict[str, Any]:
        """Calculate memory usage per connection"""
        if not self.memory_snapshots or connection_count <= 0:
            return {}
            
        current = self.get_memory_usage_current()
        baseline = self.get_memory_usage_baseline()
        
        connection_memory = {
            "rss_mb_per_connection": (current["rss_mb"] - baseline["rss_mb"]) / connection_count,
            "heap_mb_per_connection": (current["heap_mb"] - baseline["heap_mb"]) / connection_count,
            "objects_per_connection": (current["python_objects"] - baseline["python_objects"]) / connection_count,
            "total_connections": connection_count,
            "memory_efficiency_score": self._calculate_memory_efficiency(current, baseline, connection_count)
        }
        
        return connection_memory
        
    def _calculate_memory_efficiency(self, current: Dict[str, Any], baseline: Dict[str, Any], connection_count: int) -> float:
        """Calculate memory efficiency score (0-100)"""
        memory_per_connection = (current["rss_mb"] - baseline["rss_mb"]) / connection_count
        
        # Score based on memory usage per connection
        # Less than 1MB per connection = 100 points
        # 1-5MB per connection = 80-100 points
        # 5-10MB per connection = 60-80 points
        # More than 10MB per connection = 0-60 points
        
        if memory_per_connection < 1:
            return 100.0
        elif memory_per_connection < 5:
            return 100 - (memory_per_connection - 1) * 5
        elif memory_per_connection < 10:
            return 80 - (memory_per_connection - 5) * 4
        else:
            return max(0, 60 - (memory_per_connection - 10) * 2)


class MemoryBenchmarkRunner:
    """Run memory benchmarks under different scenarios"""
    
    def __init__(self, profiler: MemoryProfiler):
        self.profiler = profiler
        self.benchmark_results = {}
        
    async def run_idle_baseline_benchmark(self, duration_seconds: int = 60) -> Dict[str, Any]:
        """Run idle baseline memory benchmark"""
        print(f"Running idle baseline benchmark for {duration_seconds} seconds...")
        
        self.profiler.start_profiling()
        
        # Just wait without any activity
        await asyncio.sleep(duration_seconds)
        
        self.profiler.stop_profiling()
        
        result = self.profiler.get_profiling_report()
        result["benchmark_type"] = "idle_baseline"
        result["duration_seconds"] = duration_seconds
        
        self.benchmark_results["idle_baseline"] = result
        return result
        
    async def run_load_memory_benchmark(self, 
                                       load_function: Callable,
                                       load_params: Dict[str, Any],
                                       duration_seconds: int = 300) -> Dict[str, Any]:
        """Run memory benchmark under load"""
        print(f"Running load memory benchmark for {duration_seconds} seconds...")
        
        self.profiler.start_profiling()
        
        # Run load function
        start_time = time.time()
        
        try:
            result = await asyncio.wait_for(
                load_function(**load_params),
                timeout=duration_seconds
            )
            load_success = True
            load_result = result
        except asyncio.TimeoutError:
            load_success = True  # Timeout is expected for sustained load
            load_result = "Load test completed (timeout expected)"
        except Exception as e:
            load_success = False
            load_result = str(e)
            
        self.profiler.stop_profiling()
        
        benchmark_result = self.profiler.get_profiling_report()
        benchmark_result.update({
            "benchmark_type": "load_test",
            "duration_seconds": duration_seconds,
            "load_success": load_success,
            "load_result": load_result,
            "load_params": load_params
        })
        
        self.benchmark_results["load_test"] = benchmark_result
        return benchmark_result
        
    async def run_sustained_memory_benchmark(self, 
                                           load_function: Callable,
                                           load_params: Dict[str, Any],
                                           duration_hours: float = 1.0) -> Dict[str, Any]:
        """Run sustained load memory benchmark"""
        duration_seconds = int(duration_hours * 3600)
        print(f"Running sustained memory benchmark for {duration_hours} hours...")
        
        self.profiler.start_profiling()
        
        # Run sustained load with periodic GC
        end_time = time.time() + duration_seconds
        cycle_count = 0
        
        while time.time() < end_time:
            cycle_start = time.time()
            
            try:
                # Run load for 60 seconds
                await asyncio.wait_for(
                    load_function(**load_params),
                    timeout=60.0
                )
            except asyncio.TimeoutError:
                pass  # Expected for sustained load
            except Exception as e:
                print(f"Error in sustained load cycle {cycle_count}: {e}")
                
            # Force GC every 10 cycles
            if cycle_count % 10 == 0:
                gc.collect()
                
            cycle_count += 1
            
            # Brief pause between cycles
            await asyncio.sleep(1.0)
            
        self.profiler.stop_profiling()
        
        benchmark_result = self.profiler.get_profiling_report()
        benchmark_result.update({
            "benchmark_type": "sustained_load",
            "duration_hours": duration_hours,
            "cycles_completed": cycle_count,
            "load_params": load_params
        })
        
        self.benchmark_results["sustained_load"] = benchmark_result
        return benchmark_result
        
    def get_benchmark_comparison(self) -> Dict[str, Any]:
        """Compare benchmark results"""
        if len(self.benchmark_results) < 2:
            return {"error": "Need at least 2 benchmark results for comparison"}
            
        comparison = {}
        
        # Compare idle vs load if available
        if "idle_baseline" in self.benchmark_results and "load_test" in self.benchmark_results:
            idle = self.benchmark_results["idle_baseline"]
            load = self.benchmark_results["load_test"]
            
            comparison["idle_vs_load"] = {
                "memory_increase_mb": load["current_memory"]["rss_mb"] - idle["current_memory"]["rss_mb"],
                "peak_memory_increase_mb": load["peak_memory"]["peak_rss_mb"] - idle["peak_memory"]["peak_rss_mb"],
                "object_increase": load["current_memory"]["python_objects"] - idle["current_memory"]["python_objects"],
                "gc_performance_impact": load["gc_performance"]["avg_gc_time"] - idle["gc_performance"]["avg_gc_time"]
            }
            
        # Compare load vs sustained if available
        if "load_test" in self.benchmark_results and "sustained_load" in self.benchmark_results:
            load = self.benchmark_results["load_test"]
            sustained = self.benchmark_results["sustained_load"]
            
            comparison["load_vs_sustained"] = {
                "memory_growth_difference_mb": sustained["growth_analysis"]["rss_growth_mb_per_hour"] - load["growth_analysis"]["rss_growth_mb_per_hour"],
                "leak_detection_difference": sustained["leak_detection"]["detected"] != load["leak_detection"]["detected"],
                "stability_score": self._calculate_stability_score(sustained, load)
            }
            
        return comparison
        
    def _calculate_stability_score(self, sustained: Dict[str, Any], load: Dict[str, Any]) -> float:
        """Calculate memory stability score"""
        # Lower growth rate and consistent performance = higher stability
        sustained_growth = sustained["growth_analysis"]["rss_growth_mb_per_hour"]
        load_growth = load["growth_analysis"]["rss_growth_mb_per_hour"]
        
        # Score based on growth rate consistency
        growth_consistency = 1.0 - abs(sustained_growth - load_growth) / max(10.0, max(sustained_growth, load_growth))
        
        # Score based on absolute growth rate
        growth_score = max(0, 1.0 - sustained_growth / 100.0)  # 100 MB/hour = 0 score
        
        return (growth_consistency + growth_score) / 2 * 100
        
    def export_benchmark_results(self, filename: str):
        """Export all benchmark results"""
        data = {
            "timestamp": datetime.now().isoformat(),
            "benchmark_results": self.benchmark_results,
            "comparison": self.get_benchmark_comparison()
        }
        
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)