"""
CPU Utilization Analysis Framework

Comprehensive CPU analysis tools for monitoring CPU usage patterns,
identifying bottlenecks, and optimizing CPU-intensive operations.
"""

import asyncio
import time
import threading
import multiprocessing
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
from statistics import mean, median, stdev
import psutil
import os
import json
from datetime import datetime
import subprocess
import sys


@dataclass
class CPUSnapshot:
    """CPU utilization snapshot"""
    timestamp: float
    overall_cpu_percent: float
    per_core_cpu_percent: List[float]
    process_cpu_percent: float
    process_cpu_time: float
    system_load_avg: Tuple[float, float, float]  # 1min, 5min, 15min
    context_switches: int
    interrupts: int
    cpu_freq: float
    cpu_count: int
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "timestamp": self.timestamp,
            "overall_cpu_percent": self.overall_cpu_percent,
            "per_core_cpu_percent": self.per_core_cpu_percent,
            "process_cpu_percent": self.process_cpu_percent,
            "process_cpu_time": self.process_cpu_time,
            "system_load_avg": list(self.system_load_avg),
            "context_switches": self.context_switches,
            "interrupts": self.interrupts,
            "cpu_freq": self.cpu_freq,
            "cpu_count": self.cpu_count
        }


@dataclass
class CPUBottleneck:
    """CPU bottleneck detection result"""
    detected: bool
    bottleneck_type: str  # "high_usage", "load_imbalance", "context_switching", "frequency_scaling"
    severity: str  # "low", "medium", "high", "critical"
    affected_cores: List[int]
    metrics: Dict[str, Any]
    recommendations: List[str]
    confidence: float  # 0-1
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "detected": self.detected,
            "bottleneck_type": self.bottleneck_type,
            "severity": self.severity,
            "affected_cores": self.affected_cores,
            "metrics": self.metrics,
            "recommendations": self.recommendations,
            "confidence": self.confidence
        }


@dataclass
class CPUOptimizationSuggestion:
    """CPU optimization suggestion"""
    category: str  # "async_optimization", "multiprocessing", "caching", "algorithm"
    priority: str  # "high", "medium", "low"
    description: str
    implementation_difficulty: str  # "easy", "medium", "hard"
    expected_improvement: str  # "low", "medium", "high"
    code_example: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "category": self.category,
            "priority": self.priority,
            "description": self.description,
            "implementation_difficulty": self.implementation_difficulty,
            "expected_improvement": self.expected_improvement,
            "code_example": self.code_example
        }


class CPUAnalyzer:
    """Advanced CPU utilization analyzer"""
    
    def __init__(self, analysis_interval: float = 0.1):
        self.analysis_interval = analysis_interval
        self.cpu_snapshots: List[CPUSnapshot] = []
        self.analyzing = False
        self.analysis_thread = None
        self.start_time = None
        self.process = psutil.Process()
        
        # CPU monitoring thresholds
        self.high_cpu_threshold = 80.0
        self.critical_cpu_threshold = 95.0
        self.load_imbalance_threshold = 30.0  # % difference between cores
        
        # Performance baselines
        self.baseline_snapshots = 10
        self.baseline_cpu = None
        
        # Bottleneck detection
        self.bottleneck_history = deque(maxlen=100)
        
    def start_analysis(self):
        """Start CPU analysis"""
        if self.analyzing:
            return
            
        self.analyzing = True
        self.start_time = time.time()
        self.cpu_snapshots.clear()
        
        # Start analysis thread
        self.analysis_thread = threading.Thread(target=self._analysis_loop)
        self.analysis_thread.daemon = True
        self.analysis_thread.start()
        
    def stop_analysis(self):
        """Stop CPU analysis"""
        self.analyzing = False
        
        if self.analysis_thread:
            self.analysis_thread.join(timeout=2.0)
            
    def _analysis_loop(self):
        """Main CPU analysis loop"""
        while self.analyzing:
            try:
                snapshot = self._take_cpu_snapshot()
                self.cpu_snapshots.append(snapshot)
                
                # Establish baseline
                if len(self.cpu_snapshots) == self.baseline_snapshots:
                    self.baseline_cpu = self._calculate_baseline()
                
                # Detect bottlenecks
                if len(self.cpu_snapshots) > self.baseline_snapshots:
                    bottleneck = self._detect_bottlenecks(snapshot)
                    if bottleneck.detected:
                        self.bottleneck_history.append(bottleneck)
                        
            except Exception as e:
                print(f"Error in CPU analysis loop: {e}")
                
            time.sleep(self.analysis_interval)
            
    def _take_cpu_snapshot(self) -> CPUSnapshot:
        """Take a CPU utilization snapshot"""
        # Overall CPU usage
        overall_cpu = psutil.cpu_percent(interval=None)
        
        # Per-core CPU usage
        per_core_cpu = psutil.cpu_percent(interval=None, percpu=True)
        
        # Process CPU usage
        process_cpu = self.process.cpu_percent()
        
        # Process CPU time
        cpu_times = self.process.cpu_times()
        process_cpu_time = cpu_times.user + cpu_times.system
        
        # System load average
        try:
            load_avg = os.getloadavg()
        except:
            load_avg = (0.0, 0.0, 0.0)
            
        # Context switches and interrupts
        try:
            cpu_stats = psutil.cpu_stats()
            context_switches = cpu_stats.ctx_switches
            interrupts = cpu_stats.interrupts
        except:
            context_switches = 0
            interrupts = 0
            
        # CPU frequency
        try:
            cpu_freq = psutil.cpu_freq().current
        except:
            cpu_freq = 0.0
            
        # CPU count
        cpu_count = psutil.cpu_count()
        
        return CPUSnapshot(
            timestamp=time.time(),
            overall_cpu_percent=overall_cpu,
            per_core_cpu_percent=per_core_cpu,
            process_cpu_percent=process_cpu,
            process_cpu_time=process_cpu_time,
            system_load_avg=load_avg,
            context_switches=context_switches,
            interrupts=interrupts,
            cpu_freq=cpu_freq,
            cpu_count=cpu_count
        )
        
    def _calculate_baseline(self) -> Dict[str, Any]:
        """Calculate CPU baseline from initial snapshots"""
        if len(self.cpu_snapshots) < self.baseline_snapshots:
            return {}
            
        baseline_snapshots = self.cpu_snapshots[:self.baseline_snapshots]
        
        overall_cpu_values = [s.overall_cpu_percent for s in baseline_snapshots]
        process_cpu_values = [s.process_cpu_percent for s in baseline_snapshots]
        
        return {
            "overall_cpu_mean": mean(overall_cpu_values),
            "overall_cpu_stddev": stdev(overall_cpu_values) if len(overall_cpu_values) > 1 else 0,
            "process_cpu_mean": mean(process_cpu_values),
            "process_cpu_stddev": stdev(process_cpu_values) if len(process_cpu_values) > 1 else 0,
            "load_avg_mean": mean([s.system_load_avg[0] for s in baseline_snapshots]),
            "snapshot_count": len(baseline_snapshots)
        }
        
    def _detect_bottlenecks(self, snapshot: CPUSnapshot) -> CPUBottleneck:
        """Detect CPU bottlenecks"""
        bottlenecks = []
        
        # High CPU usage detection
        if snapshot.overall_cpu_percent > self.high_cpu_threshold:
            severity = "critical" if snapshot.overall_cpu_percent > self.critical_cpu_threshold else "high"
            bottlenecks.append(CPUBottleneck(
                detected=True,
                bottleneck_type="high_usage",
                severity=severity,
                affected_cores=list(range(snapshot.cpu_count)),
                metrics={"cpu_percent": snapshot.overall_cpu_percent},
                recommendations=self._get_high_cpu_recommendations(snapshot),
                confidence=min(1.0, snapshot.overall_cpu_percent / 100.0)
            ))
            
        # Load imbalance detection
        if len(snapshot.per_core_cpu_percent) > 1:
            cpu_min = min(snapshot.per_core_cpu_percent)
            cpu_max = max(snapshot.per_core_cpu_percent)
            imbalance = cpu_max - cpu_min
            
            if imbalance > self.load_imbalance_threshold:
                affected_cores = [i for i, cpu in enumerate(snapshot.per_core_cpu_percent) 
                                if cpu > cpu_min + imbalance * 0.7]
                
                bottlenecks.append(CPUBottleneck(
                    detected=True,
                    bottleneck_type="load_imbalance",
                    severity="medium" if imbalance < 50 else "high",
                    affected_cores=affected_cores,
                    metrics={"imbalance_percent": imbalance, "max_core_cpu": cpu_max, "min_core_cpu": cpu_min},
                    recommendations=self._get_load_imbalance_recommendations(snapshot),
                    confidence=min(1.0, imbalance / 100.0)
                ))
                
        # High context switching detection
        if self.baseline_cpu and len(self.cpu_snapshots) > self.baseline_snapshots:
            recent_snapshots = self.cpu_snapshots[-5:]
            if len(recent_snapshots) > 1:
                context_switches_per_sec = []
                for i in range(1, len(recent_snapshots)):
                    time_diff = recent_snapshots[i].timestamp - recent_snapshots[i-1].timestamp
                    ctx_diff = recent_snapshots[i].context_switches - recent_snapshots[i-1].context_switches
                    if time_diff > 0:
                        context_switches_per_sec.append(ctx_diff / time_diff)
                        
                if context_switches_per_sec:
                    avg_ctx_switches = mean(context_switches_per_sec)
                    if avg_ctx_switches > 10000:  # High context switching threshold
                        bottlenecks.append(CPUBottleneck(
                            detected=True,
                            bottleneck_type="context_switching",
                            severity="medium" if avg_ctx_switches < 50000 else "high",
                            affected_cores=list(range(snapshot.cpu_count)),
                            metrics={"context_switches_per_sec": avg_ctx_switches},
                            recommendations=self._get_context_switching_recommendations(),
                            confidence=min(1.0, avg_ctx_switches / 100000.0)
                        ))
                        
        # Return most severe bottleneck or no bottleneck
        if bottlenecks:
            return max(bottlenecks, key=lambda b: {"critical": 4, "high": 3, "medium": 2, "low": 1}[b.severity])
        else:
            return CPUBottleneck(
                detected=False,
                bottleneck_type="none",
                severity="low",
                affected_cores=[],
                metrics={},
                recommendations=[],
                confidence=0.0
            )
            
    def _get_high_cpu_recommendations(self, snapshot: CPUSnapshot) -> List[str]:
        """Get recommendations for high CPU usage"""
        recommendations = []
        
        if snapshot.overall_cpu_percent > self.critical_cpu_threshold:
            recommendations.append("CRITICAL: Implement immediate CPU usage reduction measures")
            
        recommendations.extend([
            "Profile CPU-intensive functions using cProfile or py-spy",
            "Implement async/await patterns for I/O-bound operations",
            "Add caching for frequently computed results",
            "Consider using multiprocessing for CPU-bound tasks",
            "Optimize database queries to reduce CPU load",
            "Review and optimize regular expressions",
            "Consider horizontal scaling if vertical optimization is insufficient"
        ])
        
        # Process-specific recommendations
        if snapshot.process_cpu_percent > 50:
            recommendations.append("Process CPU usage is high - focus on application-level optimizations")
            
        return recommendations
        
    def _get_load_imbalance_recommendations(self, snapshot: CPUSnapshot) -> List[str]:
        """Get recommendations for load imbalance"""
        return [
            "Implement better load balancing across CPU cores",
            "Use multiprocessing.Pool with appropriate worker count",
            "Consider async task queues (Celery, Redis Queue)",
            "Review thread pool configuration",
            "Implement work stealing algorithms",
            "Consider NUMA-aware scheduling",
            "Profile thread affinity settings"
        ]
        
    def _get_context_switching_recommendations(self) -> List[str]:
        """Get recommendations for high context switching"""
        return [
            "Reduce number of threads/processes",
            "Implement connection pooling",
            "Use async I/O instead of blocking operations",
            "Optimize thread synchronization",
            "Consider using green threads (gevent, eventlet)",
            "Review database connection management",
            "Implement batch processing for small tasks"
        ]
        
    def get_cpu_usage_statistics(self) -> Dict[str, Any]:
        """Get comprehensive CPU usage statistics"""
        if not self.cpu_snapshots:
            return {}
            
        overall_cpu_values = [s.overall_cpu_percent for s in self.cpu_snapshots]
        process_cpu_values = [s.process_cpu_percent for s in self.cpu_snapshots]
        load_avg_values = [s.system_load_avg[0] for s in self.cpu_snapshots]
        
        return {
            "overall_cpu": {
                "mean": mean(overall_cpu_values),
                "median": median(overall_cpu_values),
                "stddev": stdev(overall_cpu_values) if len(overall_cpu_values) > 1 else 0,
                "min": min(overall_cpu_values),
                "max": max(overall_cpu_values),
                "p95": sorted(overall_cpu_values)[int(0.95 * len(overall_cpu_values))],
                "p99": sorted(overall_cpu_values)[int(0.99 * len(overall_cpu_values))]
            },
            "process_cpu": {
                "mean": mean(process_cpu_values),
                "median": median(process_cpu_values),
                "stddev": stdev(process_cpu_values) if len(process_cpu_values) > 1 else 0,
                "min": min(process_cpu_values),
                "max": max(process_cpu_values),
                "p95": sorted(process_cpu_values)[int(0.95 * len(process_cpu_values))],
                "p99": sorted(process_cpu_values)[int(0.99 * len(process_cpu_values))]
            },
            "load_average": {
                "mean": mean(load_avg_values),
                "median": median(load_avg_values),
                "max": max(load_avg_values),
                "min": min(load_avg_values)
            },
            "cpu_count": self.cpu_snapshots[0].cpu_count if self.cpu_snapshots else 0,
            "analysis_duration": time.time() - self.start_time if self.start_time else 0,
            "snapshots_count": len(self.cpu_snapshots)
        }
        
    def get_per_core_analysis(self) -> Dict[str, Any]:
        """Get per-core CPU analysis"""
        if not self.cpu_snapshots:
            return {}
            
        cpu_count = self.cpu_snapshots[0].cpu_count
        per_core_stats = {}
        
        for core in range(cpu_count):
            core_values = [s.per_core_cpu_percent[core] for s in self.cpu_snapshots 
                          if core < len(s.per_core_cpu_percent)]
            
            if core_values:
                per_core_stats[f"core_{core}"] = {
                    "mean": mean(core_values),
                    "median": median(core_values),
                    "stddev": stdev(core_values) if len(core_values) > 1 else 0,
                    "min": min(core_values),
                    "max": max(core_values),
                    "utilization_efficiency": self._calculate_core_efficiency(core_values)
                }
                
        return per_core_stats
        
    def _calculate_core_efficiency(self, core_values: List[float]) -> float:
        """Calculate core utilization efficiency (0-100)"""
        if not core_values:
            return 0.0
            
        mean_usage = mean(core_values)
        stddev_usage = stdev(core_values) if len(core_values) > 1 else 0
        
        # Efficiency based on consistent moderate usage
        # Ideal: 40-70% with low variance
        if 40 <= mean_usage <= 70:
            base_score = 100
        elif mean_usage < 40:
            base_score = 80 + (mean_usage - 20) * 1.0  # Underutilized
        else:
            base_score = 100 - (mean_usage - 70) * 1.5  # Overutilized
            
        # Penalize high variance
        variance_penalty = min(20, stddev_usage * 0.5)
        
        return max(0, base_score - variance_penalty)
        
    def analyze_cpu_trends(self, window_size: int = 50) -> Dict[str, Any]:
        """Analyze CPU usage trends"""
        if len(self.cpu_snapshots) < window_size:
            return {"error": "Insufficient data for trend analysis"}
            
        recent_snapshots = self.cpu_snapshots[-window_size:]
        
        # Calculate trends
        overall_cpu_values = [s.overall_cpu_percent for s in recent_snapshots]
        process_cpu_values = [s.process_cpu_percent for s in recent_snapshots]
        load_avg_values = [s.system_load_avg[0] for s in recent_snapshots]
        
        overall_cpu_trend = self._calculate_trend(overall_cpu_values)
        process_cpu_trend = self._calculate_trend(process_cpu_values)
        load_avg_trend = self._calculate_trend(load_avg_values)
        
        return {
            "overall_cpu_trend": {
                "slope": overall_cpu_trend,
                "direction": "increasing" if overall_cpu_trend > 0.1 else "decreasing" if overall_cpu_trend < -0.1 else "stable",
                "current_value": overall_cpu_values[-1],
                "trend_strength": abs(overall_cpu_trend)
            },
            "process_cpu_trend": {
                "slope": process_cpu_trend,
                "direction": "increasing" if process_cpu_trend > 0.1 else "decreasing" if process_cpu_trend < -0.1 else "stable",
                "current_value": process_cpu_values[-1],
                "trend_strength": abs(process_cpu_trend)
            },
            "load_avg_trend": {
                "slope": load_avg_trend,
                "direction": "increasing" if load_avg_trend > 0.01 else "decreasing" if load_avg_trend < -0.01 else "stable",
                "current_value": load_avg_values[-1],
                "trend_strength": abs(load_avg_trend)
            },
            "window_size": window_size,
            "analysis_confidence": min(1.0, len(recent_snapshots) / window_size)
        }
        
    def _calculate_trend(self, values: List[float]) -> float:
        """Calculate trend slope using linear regression"""
        if len(values) < 2:
            return 0.0
            
        n = len(values)
        x_values = list(range(n))
        x_mean = mean(x_values)
        y_mean = mean(values)
        
        numerator = sum((x - x_mean) * (y - y_mean) for x, y in zip(x_values, values))
        denominator = sum((x - x_mean) ** 2 for x in x_values)
        
        return numerator / denominator if denominator != 0 else 0.0
        
    def get_optimization_suggestions(self) -> List[CPUOptimizationSuggestion]:
        """Get CPU optimization suggestions"""
        suggestions = []
        
        if not self.cpu_snapshots:
            return suggestions
            
        stats = self.get_cpu_usage_statistics()
        trends = self.analyze_cpu_trends()
        
        # High CPU usage suggestions
        if stats["overall_cpu"]["mean"] > 70:
            suggestions.append(CPUOptimizationSuggestion(
                category="async_optimization",
                priority="high",
                description="Implement async/await patterns for I/O-bound operations",
                implementation_difficulty="medium",
                expected_improvement="high",
                code_example="""
async def optimized_function():
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            return await response.json()
"""
            ))
            
        # Process CPU high usage
        if stats["process_cpu"]["mean"] > 50:
            suggestions.append(CPUOptimizationSuggestion(
                category="caching",
                priority="high",
                description="Implement caching for frequently computed results",
                implementation_difficulty="easy",
                expected_improvement="medium",
                code_example="""
from functools import lru_cache

@lru_cache(maxsize=1000)
def expensive_computation(input_data):
    # Expensive computation here
    return result
"""
            ))
            
        # Load imbalance suggestions
        per_core_stats = self.get_per_core_analysis()
        if per_core_stats:
            core_efficiencies = [stats["utilization_efficiency"] for stats in per_core_stats.values()]
            if core_efficiencies and min(core_efficiencies) < 60:
                suggestions.append(CPUOptimizationSuggestion(
                    category="multiprocessing",
                    priority="medium",
                    description="Implement multiprocessing for better core utilization",
                    implementation_difficulty="medium",
                    expected_improvement="high",
                    code_example="""
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor

async def parallel_processing(data_chunks):
    with ProcessPoolExecutor(max_workers=mp.cpu_count()) as executor:
        results = await asyncio.gather(*[
            loop.run_in_executor(executor, process_chunk, chunk)
            for chunk in data_chunks
        ])
    return results
"""
                ))
                
        # Trending CPU increase
        if trends.get("overall_cpu_trend", {}).get("direction") == "increasing":
            suggestions.append(CPUOptimizationSuggestion(
                category="algorithm",
                priority="high",
                description="Profile and optimize CPU-intensive algorithms",
                implementation_difficulty="hard",
                expected_improvement="high",
                code_example="""
import cProfile
import pstats

def profile_function():
    profiler = cProfile.Profile()
    profiler.enable()
    # Your function here
    profiler.disable()
    
    stats = pstats.Stats(profiler)
    stats.sort_stats('cumulative')
    stats.print_stats()
"""
            ))
            
        # Context switching optimization
        bottlenecks = list(self.bottleneck_history)
        if any(b.bottleneck_type == "context_switching" for b in bottlenecks):
            suggestions.append(CPUOptimizationSuggestion(
                category="async_optimization",
                priority="medium",
                description="Reduce context switching with async patterns",
                implementation_difficulty="medium",
                expected_improvement="medium",
                code_example="""
import asyncio
import aioredis

async def optimized_redis_operations():
    # Use connection pooling
    redis = aioredis.from_url("redis://localhost", max_connections=20)
    
    # Batch operations
    pipe = redis.pipeline()
    for key, value in data.items():
        pipe.set(key, value)
    await pipe.execute()
"""
            ))
            
        return suggestions
        
    def get_analysis_report(self) -> Dict[str, Any]:
        """Generate comprehensive CPU analysis report"""
        if not self.cpu_snapshots:
            return {"error": "No CPU analysis data available"}
            
        stats = self.get_cpu_usage_statistics()
        trends = self.analyze_cpu_trends()
        per_core = self.get_per_core_analysis()
        suggestions = self.get_optimization_suggestions()
        
        # Recent bottlenecks
        recent_bottlenecks = [b.to_dict() for b in list(self.bottleneck_history)[-10:]]
        
        return {
            "analysis_duration_hours": (time.time() - self.start_time) / 3600 if self.start_time else 0,
            "snapshots_analyzed": len(self.cpu_snapshots),
            "cpu_statistics": stats,
            "trend_analysis": trends,
            "per_core_analysis": per_core,
            "optimization_suggestions": [s.to_dict() for s in suggestions],
            "recent_bottlenecks": recent_bottlenecks,
            "baseline_metrics": self.baseline_cpu or {},
            "performance_score": self._calculate_performance_score(stats, trends),
            "analysis_confidence": min(1.0, len(self.cpu_snapshots) / 100)
        }
        
    def _calculate_performance_score(self, stats: Dict[str, Any], trends: Dict[str, Any]) -> float:
        """Calculate overall CPU performance score (0-100)"""
        # Base score from CPU utilization
        mean_cpu = stats["overall_cpu"]["mean"]
        if 30 <= mean_cpu <= 70:
            utilization_score = 100
        elif mean_cpu < 30:
            utilization_score = 80 + mean_cpu * 0.67  # Underutilized
        else:
            utilization_score = 100 - (mean_cpu - 70) * 1.5  # Overutilized
            
        # Penalty for high variance
        cpu_stddev = stats["overall_cpu"]["stddev"]
        variance_penalty = min(20, cpu_stddev * 0.3)
        
        # Penalty for negative trends
        trend_penalty = 0
        if trends.get("overall_cpu_trend", {}).get("direction") == "increasing":
            trend_penalty = 15
            
        # Penalty for bottlenecks
        bottleneck_penalty = len([b for b in self.bottleneck_history if b.severity in ["high", "critical"]]) * 5
        
        final_score = max(0, utilization_score - variance_penalty - trend_penalty - bottleneck_penalty)
        return min(100, final_score)
        
    def export_analysis_data(self, filename: str):
        """Export CPU analysis data"""
        data = {
            "metadata": {
                "start_time": self.start_time,
                "end_time": time.time(),
                "analysis_interval": self.analysis_interval,
                "snapshots_count": len(self.cpu_snapshots)
            },
            "snapshots": [s.to_dict() for s in self.cpu_snapshots],
            "bottlenecks": [b.to_dict() for b in self.bottleneck_history],
            "analysis_report": self.get_analysis_report()
        }
        
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)


class CPUBenchmarkRunner:
    """Run CPU benchmarks and analysis"""
    
    def __init__(self, analyzer: CPUAnalyzer):
        self.analyzer = analyzer
        self.benchmark_results = {}
        
    async def run_cpu_intensive_benchmark(self, duration_seconds: int = 60) -> Dict[str, Any]:
        """Run CPU-intensive benchmark"""
        print(f"Running CPU-intensive benchmark for {duration_seconds} seconds...")
        
        self.analyzer.start_analysis()
        
        async def cpu_intensive_task():
            """CPU-intensive task for benchmarking"""
            end_time = time.time() + duration_seconds
            iterations = 0
            
            while time.time() < end_time:
                # CPU-intensive operations
                for i in range(10000):
                    result = sum(j * j for j in range(100))
                    iterations += 1
                    
                # Small async yield
                await asyncio.sleep(0.001)
                
            return iterations
            
        try:
            iterations = await cpu_intensive_task()
            
            self.analyzer.stop_analysis()
            
            result = self.analyzer.get_analysis_report()
            result.update({
                "benchmark_type": "cpu_intensive",
                "duration_seconds": duration_seconds,
                "iterations_completed": iterations,
                "iterations_per_second": iterations / duration_seconds
            })
            
            self.benchmark_results["cpu_intensive"] = result
            return result
            
        except Exception as e:
            self.analyzer.stop_analysis()
            raise e
            
    async def run_mixed_workload_benchmark(self, duration_seconds: int = 60) -> Dict[str, Any]:
        """Run mixed CPU and I/O workload benchmark"""
        print(f"Running mixed workload benchmark for {duration_seconds} seconds...")
        
        self.analyzer.start_analysis()
        
        async def mixed_workload():
            """Mixed CPU and I/O workload"""
            end_time = time.time() + duration_seconds
            cpu_operations = 0
            io_operations = 0
            
            while time.time() < end_time:
                # CPU work
                for i in range(1000):
                    result = sum(j * j for j in range(50))
                    cpu_operations += 1
                    
                # I/O simulation
                await asyncio.sleep(0.01)
                io_operations += 1
                
            return cpu_operations, io_operations
            
        try:
            cpu_ops, io_ops = await mixed_workload()
            
            self.analyzer.stop_analysis()
            
            result = self.analyzer.get_analysis_report()
            result.update({
                "benchmark_type": "mixed_workload",
                "duration_seconds": duration_seconds,
                "cpu_operations": cpu_ops,
                "io_operations": io_ops,
                "cpu_ops_per_second": cpu_ops / duration_seconds,
                "io_ops_per_second": io_ops / duration_seconds
            })
            
            self.benchmark_results["mixed_workload"] = result
            return result
            
        except Exception as e:
            self.analyzer.stop_analysis()
            raise e
            
    def get_benchmark_comparison(self) -> Dict[str, Any]:
        """Compare benchmark results"""
        if len(self.benchmark_results) < 2:
            return {"error": "Need at least 2 benchmark results for comparison"}
            
        comparison = {}
        
        # Compare CPU intensive vs mixed workload
        if "cpu_intensive" in self.benchmark_results and "mixed_workload" in self.benchmark_results:
            cpu_intensive = self.benchmark_results["cpu_intensive"]
            mixed_workload = self.benchmark_results["mixed_workload"]
            
            comparison["cpu_intensive_vs_mixed"] = {
                "cpu_usage_difference": cpu_intensive["cpu_statistics"]["overall_cpu"]["mean"] - mixed_workload["cpu_statistics"]["overall_cpu"]["mean"],
                "performance_score_difference": cpu_intensive["performance_score"] - mixed_workload["performance_score"],
                "bottleneck_frequency_difference": len(cpu_intensive["recent_bottlenecks"]) - len(mixed_workload["recent_bottlenecks"]),
                "efficiency_comparison": self._calculate_efficiency_comparison(cpu_intensive, mixed_workload)
            }
            
        return comparison
        
    def _calculate_efficiency_comparison(self, benchmark1: Dict[str, Any], benchmark2: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate efficiency comparison between benchmarks"""
        b1_cpu = benchmark1["cpu_statistics"]["overall_cpu"]["mean"]
        b2_cpu = benchmark2["cpu_statistics"]["overall_cpu"]["mean"]
        
        b1_score = benchmark1["performance_score"]
        b2_score = benchmark2["performance_score"]
        
        return {
            "cpu_efficiency_ratio": b2_cpu / b1_cpu if b1_cpu > 0 else 0,
            "performance_efficiency_ratio": b2_score / b1_score if b1_score > 0 else 0,
            "recommendation": "mixed_workload" if b2_score > b1_score else "cpu_intensive"
        }
        
    def export_benchmark_results(self, filename: str):
        """Export benchmark results"""
        data = {
            "timestamp": datetime.now().isoformat(),
            "benchmark_results": self.benchmark_results,
            "comparison": self.get_benchmark_comparison()
        }
        
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)