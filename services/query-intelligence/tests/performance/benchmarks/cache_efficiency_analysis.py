"""
Cache Efficiency Analysis Framework

Comprehensive cache performance analysis tools for monitoring cache hit rates,
memory usage, eviction patterns, and optimization opportunities.
"""

import asyncio
import time
import threading
from typing import Dict, List, Any, Optional, Callable, Tuple, Union
from dataclasses import dataclass, field
from collections import defaultdict, deque, OrderedDict
from statistics import mean, median, stdev
import json
from datetime import datetime, timedelta
import hashlib
import weakref
import gc
import sys


@dataclass
class CacheOperation:
    """Individual cache operation record"""
    timestamp: float
    operation_type: str  # "get", "set", "delete", "evict"
    key: str
    key_hash: str
    hit: bool
    value_size_bytes: int
    processing_time_ms: float
    ttl_seconds: Optional[float] = None
    eviction_reason: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "timestamp": self.timestamp,
            "operation_type": self.operation_type,
            "key": self.key,
            "key_hash": self.key_hash,
            "hit": self.hit,
            "value_size_bytes": self.value_size_bytes,
            "processing_time_ms": self.processing_time_ms,
            "ttl_seconds": self.ttl_seconds,
            "eviction_reason": self.eviction_reason
        }


@dataclass
class CacheSnapshot:
    """Cache state snapshot"""
    timestamp: float
    total_keys: int
    total_memory_bytes: int
    memory_usage_percent: float
    hit_rate_percent: float
    miss_rate_percent: float
    eviction_rate_per_hour: float
    average_key_size_bytes: float
    average_value_size_bytes: float
    operations_per_second: float
    cache_levels: Dict[str, Dict[str, Any]]  # For multi-level caches
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "timestamp": self.timestamp,
            "total_keys": self.total_keys,
            "total_memory_bytes": self.total_memory_bytes,
            "memory_usage_percent": self.memory_usage_percent,
            "hit_rate_percent": self.hit_rate_percent,
            "miss_rate_percent": self.miss_rate_percent,
            "eviction_rate_per_hour": self.eviction_rate_per_hour,
            "average_key_size_bytes": self.average_key_size_bytes,
            "average_value_size_bytes": self.average_value_size_bytes,
            "operations_per_second": self.operations_per_second,
            "cache_levels": self.cache_levels
        }


@dataclass
class CacheEfficiencyIssue:
    """Cache efficiency issue detection"""
    issue_type: str  # "low_hit_rate", "high_eviction", "memory_waste", "hot_keys", "cold_keys"
    severity: str  # "low", "medium", "high", "critical"
    description: str
    metrics: Dict[str, Any]
    recommendations: List[str]
    confidence: float  # 0-1
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "issue_type": self.issue_type,
            "severity": self.severity,
            "description": self.description,
            "metrics": self.metrics,
            "recommendations": self.recommendations,
            "confidence": self.confidence
        }


class CacheEfficiencyAnalyzer:
    """Advanced cache efficiency analyzer"""
    
    def __init__(self, 
                 monitoring_interval: float = 1.0,
                 operation_history_size: int = 10000):
        self.monitoring_interval = monitoring_interval
        self.operation_history_size = operation_history_size
        
        # Data storage
        self.cache_operations: deque = deque(maxlen=operation_history_size)
        self.cache_snapshots: List[CacheSnapshot] = []
        self.efficiency_issues: List[CacheEfficiencyIssue] = []
        
        # Monitoring state
        self.monitoring = False
        self.monitoring_thread = None
        self.start_time = None
        
        # Cache analysis thresholds
        self.min_hit_rate_threshold = 70.0  # 70%
        self.max_eviction_rate_threshold = 100.0  # 100 evictions/hour
        self.max_memory_usage_threshold = 80.0  # 80%
        self.hot_key_threshold = 100  # 100 accesses/hour
        self.cold_key_threshold = 1   # 1 access/hour
        
        # Key pattern analysis
        self.key_access_patterns = defaultdict(list)
        self.key_sizes = defaultdict(list)
        self.key_ttls = defaultdict(list)
        
        # Performance baselines
        self.baseline_snapshots = 10
        self.baseline_cache = None
        
    def start_monitoring(self):
        """Start cache monitoring"""
        if self.monitoring:
            return
            
        self.monitoring = True
        self.start_time = time.time()
        
        # Clear previous data
        self.cache_operations.clear()
        self.cache_snapshots.clear()
        self.efficiency_issues.clear()
        self.key_access_patterns.clear()
        
        # Start monitoring thread
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
        
    def stop_monitoring(self):
        """Stop cache monitoring"""
        self.monitoring = False
        
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=2.0)
            
    def _monitoring_loop(self):
        """Main cache monitoring loop"""
        while self.monitoring:
            try:
                snapshot = self._take_cache_snapshot()
                self.cache_snapshots.append(snapshot)
                
                # Establish baseline
                if len(self.cache_snapshots) == self.baseline_snapshots:
                    self.baseline_cache = self._calculate_baseline()
                
                # Analyze efficiency issues
                if len(self.cache_snapshots) > self.baseline_snapshots:
                    issues = self._analyze_efficiency_issues(snapshot)
                    self.efficiency_issues.extend(issues)
                    
            except Exception as e:
                print(f"Error in cache monitoring loop: {e}")
                
            time.sleep(self.monitoring_interval)
            
    def _take_cache_snapshot(self) -> CacheSnapshot:
        """Take a cache state snapshot"""
        # Calculate recent statistics from operations
        recent_ops = [op for op in self.cache_operations 
                     if op.timestamp > time.time() - 60]  # Last 60 seconds
        
        if not recent_ops:
            return CacheSnapshot(
                timestamp=time.time(),
                total_keys=0,
                total_memory_bytes=0,
                memory_usage_percent=0.0,
                hit_rate_percent=0.0,
                miss_rate_percent=0.0,
                eviction_rate_per_hour=0.0,
                average_key_size_bytes=0.0,
                average_value_size_bytes=0.0,
                operations_per_second=0.0,
                cache_levels={}
            )
            
        # Calculate metrics
        total_ops = len(recent_ops)
        hit_ops = [op for op in recent_ops if op.hit]
        miss_ops = [op for op in recent_ops if not op.hit and op.operation_type == "get"]
        eviction_ops = [op for op in recent_ops if op.operation_type == "evict"]
        
        hit_rate = len(hit_ops) / max(1, len(hit_ops) + len(miss_ops)) * 100
        miss_rate = 100 - hit_rate
        
        # Eviction rate (extrapolated to per hour)
        eviction_rate = len(eviction_ops) * 60  # per hour
        
        # Memory usage estimation
        total_memory = sum(op.value_size_bytes for op in recent_ops if op.operation_type == "set")
        
        # Key and value sizes
        set_ops = [op for op in recent_ops if op.operation_type == "set"]
        avg_key_size = mean([len(op.key.encode()) for op in set_ops]) if set_ops else 0
        avg_value_size = mean([op.value_size_bytes for op in set_ops]) if set_ops else 0
        
        # Operations per second
        ops_per_second = total_ops / 60.0
        
        return CacheSnapshot(
            timestamp=time.time(),
            total_keys=len(set(op.key for op in recent_ops)),
            total_memory_bytes=total_memory,
            memory_usage_percent=min(100.0, total_memory / (100 * 1024 * 1024) * 100),  # Assume 100MB max
            hit_rate_percent=hit_rate,
            miss_rate_percent=miss_rate,
            eviction_rate_per_hour=eviction_rate,
            average_key_size_bytes=avg_key_size,
            average_value_size_bytes=avg_value_size,
            operations_per_second=ops_per_second,
            cache_levels={}
        )
        
    def _calculate_baseline(self) -> Dict[str, Any]:
        """Calculate cache baseline from initial snapshots"""
        if len(self.cache_snapshots) < self.baseline_snapshots:
            return {}
            
        baseline_snapshots = self.cache_snapshots[:self.baseline_snapshots]
        
        return {
            "avg_hit_rate": mean([s.hit_rate_percent for s in baseline_snapshots]),
            "avg_memory_usage": mean([s.memory_usage_percent for s in baseline_snapshots]),
            "avg_eviction_rate": mean([s.eviction_rate_per_hour for s in baseline_snapshots]),
            "avg_ops_per_second": mean([s.operations_per_second for s in baseline_snapshots]),
            "snapshot_count": len(baseline_snapshots)
        }
        
    def _analyze_efficiency_issues(self, snapshot: CacheSnapshot) -> List[CacheEfficiencyIssue]:
        """Analyze cache efficiency issues"""
        issues = []
        
        # Low hit rate detection
        if snapshot.hit_rate_percent < self.min_hit_rate_threshold:
            severity = "critical" if snapshot.hit_rate_percent < 50 else "high"
            issues.append(CacheEfficiencyIssue(
                issue_type="low_hit_rate",
                severity=severity,
                description=f"Cache hit rate is {snapshot.hit_rate_percent:.1f}%, below threshold of {self.min_hit_rate_threshold}%",
                metrics={"hit_rate": snapshot.hit_rate_percent, "threshold": self.min_hit_rate_threshold},
                recommendations=self._get_hit_rate_recommendations(snapshot),
                confidence=1.0 - (snapshot.hit_rate_percent / 100.0)
            ))
            
        # High eviction rate detection
        if snapshot.eviction_rate_per_hour > self.max_eviction_rate_threshold:
            severity = "high" if snapshot.eviction_rate_per_hour > self.max_eviction_rate_threshold * 2 else "medium"
            issues.append(CacheEfficiencyIssue(
                issue_type="high_eviction",
                severity=severity,
                description=f"Eviction rate is {snapshot.eviction_rate_per_hour:.1f}/hour, above threshold of {self.max_eviction_rate_threshold}/hour",
                metrics={"eviction_rate": snapshot.eviction_rate_per_hour, "threshold": self.max_eviction_rate_threshold},
                recommendations=self._get_eviction_recommendations(snapshot),
                confidence=min(1.0, snapshot.eviction_rate_per_hour / self.max_eviction_rate_threshold)
            ))
            
        # High memory usage detection
        if snapshot.memory_usage_percent > self.max_memory_usage_threshold:
            severity = "critical" if snapshot.memory_usage_percent > 95 else "high"
            issues.append(CacheEfficiencyIssue(
                issue_type="memory_waste",
                severity=severity,
                description=f"Memory usage is {snapshot.memory_usage_percent:.1f}%, above threshold of {self.max_memory_usage_threshold}%",
                metrics={"memory_usage": snapshot.memory_usage_percent, "threshold": self.max_memory_usage_threshold},
                recommendations=self._get_memory_recommendations(snapshot),
                confidence=min(1.0, snapshot.memory_usage_percent / 100.0)
            ))
            
        # Hot keys detection
        hot_keys = self._detect_hot_keys()
        if hot_keys:
            issues.append(CacheEfficiencyIssue(
                issue_type="hot_keys",
                severity="medium",
                description=f"Detected {len(hot_keys)} hot keys with high access frequency",
                metrics={"hot_keys_count": len(hot_keys), "hot_keys": hot_keys[:10]},  # Show top 10
                recommendations=self._get_hot_key_recommendations(hot_keys),
                confidence=min(1.0, len(hot_keys) / 100.0)
            ))
            
        # Cold keys detection
        cold_keys = self._detect_cold_keys()
        if cold_keys:
            issues.append(CacheEfficiencyIssue(
                issue_type="cold_keys",
                severity="low",
                description=f"Detected {len(cold_keys)} cold keys with low access frequency",
                metrics={"cold_keys_count": len(cold_keys), "cold_keys": cold_keys[:10]},  # Show top 10
                recommendations=self._get_cold_key_recommendations(cold_keys),
                confidence=min(1.0, len(cold_keys) / 1000.0)
            ))
            
        return issues
        
    def _get_hit_rate_recommendations(self, snapshot: CacheSnapshot) -> List[str]:
        """Get recommendations for improving hit rate"""
        recommendations = []
        
        if snapshot.hit_rate_percent < 30:
            recommendations.append("CRITICAL: Extremely low hit rate - review cache strategy")
            
        recommendations.extend([
            "Analyze cache key patterns and improve key design",
            "Implement cache warming strategies",
            "Review TTL settings - may be too short",
            "Consider implementing cache prefetching",
            "Add semantic caching for similar queries",
            "Review eviction policies (LRU vs LFU)",
            "Implement cache preloading for common queries",
            "Consider increasing cache size if memory permits"
        ])
        
        if snapshot.eviction_rate_per_hour > 50:
            recommendations.append("High eviction rate is contributing to low hit rate")
            
        return recommendations
        
    def _get_eviction_recommendations(self, snapshot: CacheSnapshot) -> List[str]:
        """Get recommendations for reducing eviction rate"""
        return [
            "Increase cache memory allocation",
            "Implement smarter eviction policies (LFU instead of LRU)",
            "Review key TTL settings - may be too long",
            "Implement cache tiering (L1/L2 caches)",
            "Add cache compression to store more data",
            "Implement cache partitioning by access patterns",
            "Consider using cache clustering for distribution",
            "Review data structure efficiency"
        ]
        
    def _get_memory_recommendations(self, snapshot: CacheSnapshot) -> List[str]:
        """Get recommendations for optimizing memory usage"""
        recommendations = []
        
        if snapshot.memory_usage_percent > 95:
            recommendations.append("CRITICAL: Cache memory nearly full")
            
        recommendations.extend([
            "Implement data compression for cached values",
            "Review value serialization efficiency",
            "Implement cache value deduplication",
            "Add memory-based eviction policies",
            "Consider using memory-mapped files for large caches",
            "Implement cache value streaming for large objects",
            "Review cache value sizes and optimize data structures",
            "Consider implementing cache sharding"
        ])
        
        return recommendations
        
    def _get_hot_key_recommendations(self, hot_keys: List[Dict[str, Any]]) -> List[str]:
        """Get recommendations for handling hot keys"""
        return [
            "Implement hot key detection and special handling",
            "Add dedicated cache instances for hot keys",
            "Implement cache replication for hot keys",
            "Consider local caching for frequently accessed keys",
            "Add cache warming for predicted hot keys",
            "Implement rate limiting for hot key access",
            "Consider read-through caching for hot keys",
            "Add monitoring and alerting for hot key patterns"
        ]
        
    def _get_cold_key_recommendations(self, cold_keys: List[Dict[str, Any]]) -> List[str]:
        """Get recommendations for handling cold keys"""
        return [
            "Implement cold key detection and removal",
            "Add TTL-based eviction for cold keys",
            "Consider archiving cold keys to slower storage",
            "Implement lazy loading for cold keys",
            "Add cold key cleanup jobs",
            "Consider implementing cache warming only for active keys",
            "Review cold key access patterns for optimization",
            "Implement cold key migration to cheaper storage"
        ]
        
    def _detect_hot_keys(self) -> List[Dict[str, Any]]:
        """Detect hot keys based on access patterns"""
        hot_keys = []
        current_time = time.time()
        
        for key, access_times in self.key_access_patterns.items():
            # Count accesses in the last hour
            recent_accesses = [t for t in access_times if t > current_time - 3600]
            
            if len(recent_accesses) > self.hot_key_threshold:
                hot_keys.append({
                    "key": key,
                    "access_count": len(recent_accesses),
                    "access_rate_per_hour": len(recent_accesses)
                })
                
        return sorted(hot_keys, key=lambda x: x["access_count"], reverse=True)
        
    def _detect_cold_keys(self) -> List[Dict[str, Any]]:
        """Detect cold keys based on access patterns"""
        cold_keys = []
        current_time = time.time()
        
        for key, access_times in self.key_access_patterns.items():
            # Count accesses in the last hour
            recent_accesses = [t for t in access_times if t > current_time - 3600]
            
            if len(recent_accesses) <= self.cold_key_threshold:
                cold_keys.append({
                    "key": key,
                    "access_count": len(recent_accesses),
                    "last_access": max(access_times) if access_times else 0
                })
                
        return sorted(cold_keys, key=lambda x: x["access_count"])
        
    def record_cache_operation(self, 
                             operation_type: str,
                             key: str,
                             hit: bool,
                             value_size_bytes: int,
                             processing_time_ms: float,
                             ttl_seconds: Optional[float] = None,
                             eviction_reason: Optional[str] = None):
        """Record a cache operation"""
        key_hash = hashlib.md5(key.encode()).hexdigest()
        
        operation = CacheOperation(
            timestamp=time.time(),
            operation_type=operation_type,
            key=key,
            key_hash=key_hash,
            hit=hit,
            value_size_bytes=value_size_bytes,
            processing_time_ms=processing_time_ms,
            ttl_seconds=ttl_seconds,
            eviction_reason=eviction_reason
        )
        
        self.cache_operations.append(operation)
        
        # Update key access patterns
        self.key_access_patterns[key].append(operation.timestamp)
        
        # Keep only recent access times
        cutoff_time = time.time() - 3600  # 1 hour
        self.key_access_patterns[key] = [
            t for t in self.key_access_patterns[key] if t > cutoff_time
        ]
        
        # Track key sizes and TTLs
        if operation_type == "set":
            self.key_sizes[key].append(value_size_bytes)
            if ttl_seconds:
                self.key_ttls[key].append(ttl_seconds)
                
    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        if not self.cache_snapshots:
            return {}
            
        # Hit rate statistics
        hit_rates = [s.hit_rate_percent for s in self.cache_snapshots]
        miss_rates = [s.miss_rate_percent for s in self.cache_snapshots]
        
        # Memory usage statistics
        memory_usage = [s.memory_usage_percent for s in self.cache_snapshots]
        
        # Eviction statistics
        eviction_rates = [s.eviction_rate_per_hour for s in self.cache_snapshots]
        
        # Operation statistics
        ops_per_second = [s.operations_per_second for s in self.cache_snapshots]
        
        return {
            "hit_rate": {
                "current": hit_rates[-1] if hit_rates else 0,
                "mean": mean(hit_rates) if hit_rates else 0,
                "median": median(hit_rates) if hit_rates else 0,
                "min": min(hit_rates) if hit_rates else 0,
                "max": max(hit_rates) if hit_rates else 0,
                "stddev": stdev(hit_rates) if len(hit_rates) > 1 else 0
            },
            "miss_rate": {
                "current": miss_rates[-1] if miss_rates else 0,
                "mean": mean(miss_rates) if miss_rates else 0,
                "median": median(miss_rates) if miss_rates else 0
            },
            "memory_usage": {
                "current_percent": memory_usage[-1] if memory_usage else 0,
                "mean_percent": mean(memory_usage) if memory_usage else 0,
                "peak_percent": max(memory_usage) if memory_usage else 0,
                "min_percent": min(memory_usage) if memory_usage else 0
            },
            "eviction_rate": {
                "current_per_hour": eviction_rates[-1] if eviction_rates else 0,
                "mean_per_hour": mean(eviction_rates) if eviction_rates else 0,
                "peak_per_hour": max(eviction_rates) if eviction_rates else 0
            },
            "operations": {
                "current_per_second": ops_per_second[-1] if ops_per_second else 0,
                "mean_per_second": mean(ops_per_second) if ops_per_second else 0,
                "peak_per_second": max(ops_per_second) if ops_per_second else 0
            },
            "key_statistics": {
                "total_unique_keys": len(self.key_access_patterns),
                "hot_keys_count": len(self._detect_hot_keys()),
                "cold_keys_count": len(self._detect_cold_keys()),
                "avg_key_size_bytes": mean([mean(sizes) for sizes in self.key_sizes.values()]) if self.key_sizes else 0
            },
            "monitoring_duration": time.time() - self.start_time if self.start_time else 0,
            "snapshots_count": len(self.cache_snapshots)
        }
        
    def analyze_cache_trends(self, window_size: int = 30) -> Dict[str, Any]:
        """Analyze cache performance trends"""
        if len(self.cache_snapshots) < window_size:
            return {"error": "Insufficient data for trend analysis"}
            
        recent_snapshots = self.cache_snapshots[-window_size:]
        
        # Calculate trends
        hit_rates = [s.hit_rate_percent for s in recent_snapshots]
        memory_usage = [s.memory_usage_percent for s in recent_snapshots]
        eviction_rates = [s.eviction_rate_per_hour for s in recent_snapshots]
        
        hit_rate_trend = self._calculate_trend(hit_rates)
        memory_trend = self._calculate_trend(memory_usage)
        eviction_trend = self._calculate_trend(eviction_rates)
        
        return {
            "hit_rate_trend": {
                "slope": hit_rate_trend,
                "direction": "improving" if hit_rate_trend > 0.1 else "degrading" if hit_rate_trend < -0.1 else "stable",
                "current_value": hit_rates[-1],
                "trend_strength": abs(hit_rate_trend)
            },
            "memory_usage_trend": {
                "slope": memory_trend,
                "direction": "increasing" if memory_trend > 0.1 else "decreasing" if memory_trend < -0.1 else "stable",
                "current_value": memory_usage[-1],
                "trend_strength": abs(memory_trend)
            },
            "eviction_rate_trend": {
                "slope": eviction_trend,
                "direction": "increasing" if eviction_trend > 1.0 else "decreasing" if eviction_trend < -1.0 else "stable",
                "current_value": eviction_rates[-1],
                "trend_strength": abs(eviction_trend)
            },
            "window_size": window_size,
            "analysis_confidence": min(1.0, len(recent_snapshots) / window_size)
        }
        
    def _calculate_trend(self, values: List[float]) -> float:
        """Calculate trend slope using linear regression"""
        if len(values) < 2:
            return 0.0
            
        n = len(values)
        x_values = list(range(n))
        x_mean = mean(x_values)
        y_mean = mean(values)
        
        numerator = sum((x - x_mean) * (y - y_mean) for x, y in zip(x_values, values))
        denominator = sum((x - x_mean) ** 2 for x in x_values)
        
        return numerator / denominator if denominator != 0 else 0.0
        
    def get_cache_efficiency_score(self) -> float:
        """Calculate overall cache efficiency score (0-100)"""
        if not self.cache_snapshots:
            return 0.0
            
        stats = self.get_cache_statistics()
        
        # Hit rate score (40% of total)
        hit_rate_score = stats["hit_rate"]["mean"] * 0.4
        
        # Memory efficiency score (30% of total)
        memory_efficiency = 100 - stats["memory_usage"]["mean_percent"]
        memory_score = max(0, memory_efficiency) * 0.3
        
        # Eviction rate score (20% of total)
        eviction_score = max(0, 100 - stats["eviction_rate"]["mean_per_hour"]) * 0.2
        
        # Operations efficiency score (10% of total)
        ops_score = min(100, stats["operations"]["mean_per_second"] * 2) * 0.1
        
        total_score = hit_rate_score + memory_score + eviction_score + ops_score
        
        # Apply penalties for issues
        issue_penalty = len([i for i in self.efficiency_issues if i.severity in ["high", "critical"]]) * 5
        
        return max(0, min(100, total_score - issue_penalty))
        
    def get_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """Get cache optimization recommendations"""
        recommendations = []
        
        if not self.cache_snapshots:
            return recommendations
            
        stats = self.get_cache_statistics()
        efficiency_score = self.get_cache_efficiency_score()
        
        # Hit rate optimization
        if stats["hit_rate"]["mean"] < 80:
            recommendations.append({
                "category": "hit_rate",
                "priority": "high" if stats["hit_rate"]["mean"] < 60 else "medium",
                "title": "Improve Cache Hit Rate",
                "description": f"Current hit rate is {stats['hit_rate']['mean']:.1f}%. Target: >80%",
                "actions": [
                    "Implement cache warming strategies",
                    "Review and optimize TTL settings",
                    "Add semantic caching for similar queries",
                    "Implement cache prefetching"
                ]
            })
            
        # Memory optimization
        if stats["memory_usage"]["mean_percent"] > 75:
            recommendations.append({
                "category": "memory",
                "priority": "high" if stats["memory_usage"]["mean_percent"] > 90 else "medium",
                "title": "Optimize Memory Usage",
                "description": f"Memory usage is {stats['memory_usage']['mean_percent']:.1f}%. Target: <75%",
                "actions": [
                    "Implement data compression",
                    "Review value serialization efficiency",
                    "Add memory-based eviction policies",
                    "Consider cache sharding"
                ]
            })
            
        # Eviction optimization
        if stats["eviction_rate"]["mean_per_hour"] > 50:
            recommendations.append({
                "category": "eviction",
                "priority": "medium",
                "title": "Reduce Eviction Rate",
                "description": f"Eviction rate is {stats['eviction_rate']['mean_per_hour']:.1f}/hour. Target: <50/hour",
                "actions": [
                    "Increase cache size if possible",
                    "Implement smarter eviction policies",
                    "Review TTL settings",
                    "Implement cache tiering"
                ]
            })
            
        # Hot key optimization
        hot_keys = self._detect_hot_keys()
        if len(hot_keys) > 10:
            recommendations.append({
                "category": "hot_keys",
                "priority": "medium",
                "title": "Handle Hot Keys",
                "description": f"Detected {len(hot_keys)} hot keys with high access frequency",
                "actions": [
                    "Implement hot key detection and special handling",
                    "Add dedicated cache instances for hot keys",
                    "Consider cache replication for hot keys",
                    "Add local caching for frequently accessed keys"
                ]
            })
            
        return recommendations
        
    def get_analysis_report(self) -> Dict[str, Any]:
        """Generate comprehensive cache analysis report"""
        if not self.cache_snapshots:
            return {"error": "No cache monitoring data available"}
            
        stats = self.get_cache_statistics()
        trends = self.analyze_cache_trends()
        efficiency_score = self.get_cache_efficiency_score()
        recommendations = self.get_optimization_recommendations()
        
        return {
            "monitoring_duration_hours": (time.time() - self.start_time) / 3600 if self.start_time else 0,
            "snapshots_analyzed": len(self.cache_snapshots),
            "operations_recorded": len(self.cache_operations),
            "cache_statistics": stats,
            "trend_analysis": trends,
            "efficiency_score": efficiency_score,
            "efficiency_issues": [i.to_dict() for i in self.efficiency_issues],
            "optimization_recommendations": recommendations,
            "baseline_metrics": self.baseline_cache or {},
            "key_patterns": {
                "hot_keys": self._detect_hot_keys()[:10],
                "cold_keys": self._detect_cold_keys()[:10]
            }
        }
        
    def export_analysis_data(self, filename: str):
        """Export cache analysis data"""
        data = {
            "metadata": {
                "start_time": self.start_time,
                "end_time": time.time(),
                "monitoring_interval": self.monitoring_interval,
                "snapshots_count": len(self.cache_snapshots),
                "operations_count": len(self.cache_operations)
            },
            "snapshots": [s.to_dict() for s in self.cache_snapshots],
            "operations": [op.to_dict() for op in self.cache_operations],
            "analysis_report": self.get_analysis_report()
        }
        
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)


class CacheBenchmarkRunner:
    """Run cache benchmarks and analysis"""
    
    def __init__(self, analyzer: CacheEfficiencyAnalyzer):
        self.analyzer = analyzer
        self.benchmark_results = {}
        
    async def run_hit_rate_benchmark(self, 
                                   cache_size: int = 1000,
                                   total_operations: int = 10000,
                                   key_space_size: int = 2000) -> Dict[str, Any]:
        """Run hit rate benchmark with different cache configurations"""
        print(f"Running hit rate benchmark with cache size {cache_size}...")
        
        # Simple LRU cache simulation
        cache = OrderedDict()
        
        self.analyzer.start_monitoring()
        
        # Generate operations
        import random
        
        for i in range(total_operations):
            # Generate key (Pareto distribution to simulate real-world access patterns)
            key = f"key_{random.paretovariate(1.16) % key_space_size}"
            
            start_time = time.time()
            
            # Check cache
            if key in cache:
                # Cache hit
                cache.move_to_end(key)
                hit = True
                value_size = len(cache[key])
            else:
                # Cache miss
                hit = False
                value = f"value_{i}_{'x' * random.randint(50, 500)}"
                value_size = len(value.encode())
                
                # Add to cache
                cache[key] = value
                
                # Evict if necessary
                if len(cache) > cache_size:
                    evicted_key, evicted_value = cache.popitem(last=False)
                    self.analyzer.record_cache_operation(
                        "evict", evicted_key, False, len(evicted_value.encode()),
                        0, eviction_reason="lru"
                    )
                    
            end_time = time.time()
            processing_time = (end_time - start_time) * 1000
            
            # Record operation
            self.analyzer.record_cache_operation(
                "get" if hit else "set", key, hit, value_size, processing_time
            )
            
            # Small delay to simulate real operations
            await asyncio.sleep(0.001)
            
        self.analyzer.stop_monitoring()
        
        result = self.analyzer.get_analysis_report()
        result.update({
            "benchmark_type": "hit_rate_test",
            "cache_size": cache_size,
            "total_operations": total_operations,
            "key_space_size": key_space_size,
            "final_cache_size": len(cache)
        })
        
        self.benchmark_results["hit_rate_benchmark"] = result
        return result
        
    async def run_memory_efficiency_benchmark(self, 
                                            max_memory_mb: int = 100,
                                            operation_count: int = 5000) -> Dict[str, Any]:
        """Run memory efficiency benchmark"""
        print(f"Running memory efficiency benchmark with {max_memory_mb}MB limit...")
        
        # Simple memory-aware cache
        cache = {}
        current_memory = 0
        max_memory_bytes = max_memory_mb * 1024 * 1024
        
        self.analyzer.start_monitoring()
        
        import random
        
        for i in range(operation_count):
            key = f"key_{random.randint(1, 1000)}"
            
            start_time = time.time()
            
            if key in cache:
                # Cache hit
                hit = True
                value_size = len(cache[key])
            else:
                # Cache miss - create new value
                hit = False
                value_size = random.randint(1024, 10240)  # 1KB to 10KB
                value = "x" * value_size
                
                # Check memory limit
                if current_memory + value_size > max_memory_bytes:
                    # Evict random items until we have space
                    while current_memory + value_size > max_memory_bytes and cache:
                        evict_key = random.choice(list(cache.keys()))
                        evicted_value = cache.pop(evict_key)
                        current_memory -= len(evicted_value)
                        
                        self.analyzer.record_cache_operation(
                            "evict", evict_key, False, len(evicted_value),
                            0, eviction_reason="memory_limit"
                        )
                        
                # Add to cache
                cache[key] = value
                current_memory += value_size
                
            end_time = time.time()
            processing_time = (end_time - start_time) * 1000
            
            # Record operation
            self.analyzer.record_cache_operation(
                "get" if hit else "set", key, hit, value_size, processing_time
            )
            
            await asyncio.sleep(0.001)
            
        self.analyzer.stop_monitoring()
        
        result = self.analyzer.get_analysis_report()
        result.update({
            "benchmark_type": "memory_efficiency_test",
            "max_memory_mb": max_memory_mb,
            "operation_count": operation_count,
            "final_memory_usage_mb": current_memory / 1024 / 1024,
            "final_cache_entries": len(cache)
        })
        
        self.benchmark_results["memory_efficiency_benchmark"] = result
        return result
        
    def get_benchmark_comparison(self) -> Dict[str, Any]:
        """Compare benchmark results"""
        if len(self.benchmark_results) < 2:
            return {"error": "Need at least 2 benchmark results for comparison"}
            
        comparison = {}
        
        # Compare hit rate vs memory efficiency
        if "hit_rate_benchmark" in self.benchmark_results and "memory_efficiency_benchmark" in self.benchmark_results:
            hit_rate = self.benchmark_results["hit_rate_benchmark"]
            memory_eff = self.benchmark_results["memory_efficiency_benchmark"]
            
            comparison["hit_rate_vs_memory"] = {
                "hit_rate_difference": hit_rate["cache_statistics"]["hit_rate"]["mean"] - memory_eff["cache_statistics"]["hit_rate"]["mean"],
                "memory_efficiency_difference": memory_eff["cache_statistics"]["memory_usage"]["mean_percent"] - hit_rate["cache_statistics"]["memory_usage"]["mean_percent"],
                "efficiency_score_difference": hit_rate["efficiency_score"] - memory_eff["efficiency_score"],
                "recommendation": "hit_rate_optimized" if hit_rate["efficiency_score"] > memory_eff["efficiency_score"] else "memory_optimized"
            }
            
        return comparison
        
    def export_benchmark_results(self, filename: str):
        """Export benchmark results"""
        data = {
            "timestamp": datetime.now().isoformat(),
            "benchmark_results": self.benchmark_results,
            "comparison": self.get_benchmark_comparison()
        }
        
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)