"""
Resource Scaling Analysis Framework

Comprehensive auto-scaling efficiency analysis for Cloud Run deployments,
including scaling behavior, resource utilization, and optimization recommendations.
"""

import asyncio
import time
import threading
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
from statistics import mean, median, stdev
import json
from datetime import datetime, timedelta
import math
import subprocess
import os


@dataclass
class ScalingEvent:
    """Scaling event record"""
    timestamp: float
    event_type: str  # "scale_up", "scale_down", "cold_start", "instance_ready"
    instance_count: int
    target_utilization: float
    actual_utilization: float
    scaling_reason: str
    scaling_duration_seconds: float
    resource_request: Dict[str, Any]  # CPU, memory requests
    resource_limits: Dict[str, Any]   # CPU, memory limits
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "timestamp": self.timestamp,
            "event_type": self.event_type,
            "instance_count": self.instance_count,
            "target_utilization": self.target_utilization,
            "actual_utilization": self.actual_utilization,
            "scaling_reason": self.scaling_reason,
            "scaling_duration_seconds": self.scaling_duration_seconds,
            "resource_request": self.resource_request,
            "resource_limits": self.resource_limits
        }


@dataclass
class InstanceMetrics:
    """Individual instance metrics"""
    instance_id: str
    timestamp: float
    cpu_utilization: float
    memory_utilization: float
    request_count: int
    response_time_ms: float
    concurrent_requests: int
    status: str  # "running", "starting", "stopping"
    startup_time_seconds: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "instance_id": self.instance_id,
            "timestamp": self.timestamp,
            "cpu_utilization": self.cpu_utilization,
            "memory_utilization": self.memory_utilization,
            "request_count": self.request_count,
            "response_time_ms": self.response_time_ms,
            "concurrent_requests": self.concurrent_requests,
            "status": self.status,
            "startup_time_seconds": self.startup_time_seconds
        }


@dataclass
class ScalingEfficiencyIssue:
    """Scaling efficiency issue"""
    issue_type: str  # "over_provisioning", "under_provisioning", "slow_scaling", "cold_starts"
    severity: str  # "low", "medium", "high", "critical"
    description: str
    metrics: Dict[str, Any]
    recommendations: List[str]
    confidence: float  # 0-1
    impact_score: float  # 0-100
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "issue_type": self.issue_type,
            "severity": self.severity,
            "description": self.description,
            "metrics": self.metrics,
            "recommendations": self.recommendations,
            "confidence": self.confidence,
            "impact_score": self.impact_score
        }


class ResourceScalingAnalyzer:
    """Advanced resource scaling analyzer for Cloud Run"""
    
    def __init__(self, monitoring_interval: float = 5.0):
        self.monitoring_interval = monitoring_interval
        
        # Data storage
        self.scaling_events: List[ScalingEvent] = []
        self.instance_metrics: Dict[str, List[InstanceMetrics]] = defaultdict(list)
        self.scaling_issues: List[ScalingEfficiencyIssue] = []
        
        # Monitoring state
        self.monitoring = False
        self.monitoring_thread = None
        self.start_time = None
        
        # Cloud Run configuration
        self.min_instances = 5
        self.max_instances = 200
        self.target_cpu_utilization = 70.0
        self.target_memory_utilization = 80.0
        self.max_concurrent_requests = 20
        
        # Scaling thresholds
        self.over_provisioning_threshold = 30.0  # < 30% utilization
        self.under_provisioning_threshold = 90.0  # > 90% utilization
        self.slow_scaling_threshold = 60.0  # > 60s scaling time
        self.cold_start_threshold = 5.0  # > 5s cold start time
        
        # Performance baselines
        self.baseline_snapshots = 10
        self.baseline_scaling = None
        
        # Simulated metrics (in real deployment, these would come from Cloud Monitoring)
        self.simulated_load = 0.0
        self.current_instances = self.min_instances
        self.instance_utilization = defaultdict(lambda: {"cpu": 0.0, "memory": 0.0})
        
    def start_monitoring(self):
        """Start scaling monitoring"""
        if self.monitoring:
            return
            
        self.monitoring = True
        self.start_time = time.time()
        
        # Clear previous data
        self.scaling_events.clear()
        self.instance_metrics.clear()
        self.scaling_issues.clear()
        
        # Start monitoring thread
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
        
    def stop_monitoring(self):
        """Stop scaling monitoring"""
        self.monitoring = False
        
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=2.0)
            
    def _monitoring_loop(self):
        """Main scaling monitoring loop"""
        while self.monitoring:
            try:
                # Update simulated metrics
                self._update_simulated_metrics()
                
                # Check for scaling events
                scaling_event = self._check_scaling_decisions()
                if scaling_event:
                    self.scaling_events.append(scaling_event)
                
                # Record instance metrics
                self._record_instance_metrics()
                
                # Analyze scaling efficiency
                if len(self.scaling_events) > 5:
                    issues = self._analyze_scaling_efficiency()
                    self.scaling_issues.extend(issues)
                    
            except Exception as e:
                print(f"Error in scaling monitoring loop: {e}")
                
            time.sleep(self.monitoring_interval)
            
    def _update_simulated_metrics(self):
        """Update simulated load and metrics"""
        # Simulate varying load patterns
        time_factor = (time.time() - self.start_time) / 3600  # Hours since start
        
        # Sine wave with random variations
        base_load = 50 + 40 * math.sin(time_factor * 2 * math.pi / 4)  # 4-hour cycle
        random_variation = (time.time() % 17) / 17 * 20 - 10  # -10 to +10
        
        self.simulated_load = max(0, base_load + random_variation)
        
        # Update instance utilization based on load
        requests_per_instance = self.simulated_load / max(1, self.current_instances)
        
        for i in range(self.current_instances):
            instance_id = f"instance_{i}"
            
            # CPU utilization based on requests
            cpu_util = min(100, requests_per_instance * 3 + (time.time() % 7) * 5)
            
            # Memory utilization (more stable)
            memory_util = min(100, 30 + requests_per_instance * 2 + (time.time() % 11) * 2)
            
            self.instance_utilization[instance_id] = {
                "cpu": cpu_util,
                "memory": memory_util
            }
            
    def _check_scaling_decisions(self) -> Optional[ScalingEvent]:
        """Check if scaling decisions are needed"""
        current_time = time.time()
        
        # Calculate average utilization
        if not self.instance_utilization:
            return None
            
        avg_cpu = mean([metrics["cpu"] for metrics in self.instance_utilization.values()])
        avg_memory = mean([metrics["memory"] for metrics in self.instance_utilization.values()])
        
        # Determine if scaling is needed
        scale_up_needed = (
            avg_cpu > self.target_cpu_utilization or 
            avg_memory > self.target_memory_utilization
        )
        
        scale_down_needed = (
            avg_cpu < self.target_cpu_utilization * 0.5 and 
            avg_memory < self.target_memory_utilization * 0.5 and
            self.current_instances > self.min_instances
        )
        
        scaling_event = None
        
        if scale_up_needed and self.current_instances < self.max_instances:
            # Scale up
            old_instances = self.current_instances
            self.current_instances = min(self.max_instances, self.current_instances + 1)
            
            scaling_event = ScalingEvent(
                timestamp=current_time,
                event_type="scale_up",
                instance_count=self.current_instances,
                target_utilization=self.target_cpu_utilization,
                actual_utilization=avg_cpu,
                scaling_reason="high_utilization",
                scaling_duration_seconds=self.monitoring_interval,
                resource_request={"cpu": "1000m", "memory": "4Gi"},
                resource_limits={"cpu": "2000m", "memory": "16Gi"}
            )
            
        elif scale_down_needed:
            # Scale down
            old_instances = self.current_instances
            self.current_instances = max(self.min_instances, self.current_instances - 1)
            
            scaling_event = ScalingEvent(
                timestamp=current_time,
                event_type="scale_down",
                instance_count=self.current_instances,
                target_utilization=self.target_cpu_utilization,
                actual_utilization=avg_cpu,
                scaling_reason="low_utilization",
                scaling_duration_seconds=self.monitoring_interval,
                resource_request={"cpu": "1000m", "memory": "4Gi"},
                resource_limits={"cpu": "2000m", "memory": "16Gi"}
            )
            
        return scaling_event
        
    def _record_instance_metrics(self):
        """Record metrics for all instances"""
        current_time = time.time()
        
        for i in range(self.current_instances):
            instance_id = f"instance_{i}"
            
            utilization = self.instance_utilization.get(instance_id, {"cpu": 0, "memory": 0})
            
            # Simulate request metrics
            request_count = int(self.simulated_load / self.current_instances)
            response_time = 100 + (utilization["cpu"] - 50) * 2  # 100ms base + utilization factor
            concurrent_requests = min(self.max_concurrent_requests, int(request_count * 0.1))
            
            # Determine startup time for new instances
            startup_time = None
            if len(self.scaling_events) > 0:
                last_event = self.scaling_events[-1]
                if (last_event.event_type == "scale_up" and 
                    current_time - last_event.timestamp < 30 and
                    i >= last_event.instance_count - 1):
                    startup_time = min(10.0, (current_time - last_event.timestamp))
                    
            metrics = InstanceMetrics(
                instance_id=instance_id,
                timestamp=current_time,
                cpu_utilization=utilization["cpu"],
                memory_utilization=utilization["memory"],
                request_count=request_count,
                response_time_ms=response_time,
                concurrent_requests=concurrent_requests,
                status="running",
                startup_time_seconds=startup_time
            )
            
            self.instance_metrics[instance_id].append(metrics)
            
            # Keep only recent metrics
            cutoff_time = current_time - 3600  # 1 hour
            self.instance_metrics[instance_id] = [
                m for m in self.instance_metrics[instance_id] 
                if m.timestamp > cutoff_time
            ]
            
    def _analyze_scaling_efficiency(self) -> List[ScalingEfficiencyIssue]:
        """Analyze scaling efficiency issues"""
        issues = []
        
        # Over-provisioning detection
        over_provisioning_issue = self._detect_over_provisioning()
        if over_provisioning_issue:
            issues.append(over_provisioning_issue)
            
        # Under-provisioning detection
        under_provisioning_issue = self._detect_under_provisioning()
        if under_provisioning_issue:
            issues.append(under_provisioning_issue)
            
        # Slow scaling detection
        slow_scaling_issue = self._detect_slow_scaling()
        if slow_scaling_issue:
            issues.append(slow_scaling_issue)
            
        # Cold start detection
        cold_start_issue = self._detect_cold_starts()
        if cold_start_issue:
            issues.append(cold_start_issue)
            
        return issues
        
    def _detect_over_provisioning(self) -> Optional[ScalingEfficiencyIssue]:
        """Detect over-provisioning issues"""
        if not self.instance_metrics:
            return None
            
        # Calculate average utilization across all instances
        all_cpu_utils = []
        all_memory_utils = []
        
        for instance_metrics in self.instance_metrics.values():
            recent_metrics = [m for m in instance_metrics if m.timestamp > time.time() - 1800]  # 30 minutes
            if recent_metrics:
                all_cpu_utils.extend([m.cpu_utilization for m in recent_metrics])
                all_memory_utils.extend([m.memory_utilization for m in recent_metrics])
                
        if not all_cpu_utils:
            return None
            
        avg_cpu = mean(all_cpu_utils)
        avg_memory = mean(all_memory_utils)
        
        # Check for over-provisioning
        if avg_cpu < self.over_provisioning_threshold and avg_memory < self.over_provisioning_threshold:
            severity = "high" if avg_cpu < 20 else "medium"
            
            return ScalingEfficiencyIssue(
                issue_type="over_provisioning",
                severity=severity,
                description=f"Resources are over-provisioned. CPU: {avg_cpu:.1f}%, Memory: {avg_memory:.1f}%",
                metrics={
                    "avg_cpu_utilization": avg_cpu,
                    "avg_memory_utilization": avg_memory,
                    "current_instances": self.current_instances,
                    "threshold": self.over_provisioning_threshold
                },
                recommendations=self._get_over_provisioning_recommendations(),
                confidence=1.0 - (avg_cpu / 100.0),
                impact_score=min(100, (50 - avg_cpu) * 2)
            )
            
        return None
        
    def _detect_under_provisioning(self) -> Optional[ScalingEfficiencyIssue]:
        """Detect under-provisioning issues"""
        if not self.instance_metrics:
            return None
            
        # Calculate peak utilization
        all_cpu_utils = []
        all_memory_utils = []
        
        for instance_metrics in self.instance_metrics.values():
            recent_metrics = [m for m in instance_metrics if m.timestamp > time.time() - 1800]  # 30 minutes
            if recent_metrics:
                all_cpu_utils.extend([m.cpu_utilization for m in recent_metrics])
                all_memory_utils.extend([m.memory_utilization for m in recent_metrics])
                
        if not all_cpu_utils:
            return None
            
        peak_cpu = max(all_cpu_utils)
        peak_memory = max(all_memory_utils)
        avg_cpu = mean(all_cpu_utils)
        
        # Check for under-provisioning
        if peak_cpu > self.under_provisioning_threshold or peak_memory > self.under_provisioning_threshold:
            severity = "critical" if peak_cpu > 95 or peak_memory > 95 else "high"
            
            return ScalingEfficiencyIssue(
                issue_type="under_provisioning",
                severity=severity,
                description=f"Resources are under-provisioned. Peak CPU: {peak_cpu:.1f}%, Peak Memory: {peak_memory:.1f}%",
                metrics={
                    "peak_cpu_utilization": peak_cpu,
                    "peak_memory_utilization": peak_memory,
                    "avg_cpu_utilization": avg_cpu,
                    "current_instances": self.current_instances,
                    "threshold": self.under_provisioning_threshold
                },
                recommendations=self._get_under_provisioning_recommendations(),
                confidence=min(1.0, (peak_cpu - 70) / 30.0),
                impact_score=min(100, (peak_cpu - 70) * 2)
            )
            
        return None
        
    def _detect_slow_scaling(self) -> Optional[ScalingEfficiencyIssue]:
        """Detect slow scaling issues"""
        if len(self.scaling_events) < 3:
            return None
            
        # Calculate average scaling time
        scaling_times = [event.scaling_duration_seconds for event in self.scaling_events]
        avg_scaling_time = mean(scaling_times)
        
        if avg_scaling_time > self.slow_scaling_threshold:
            severity = "high" if avg_scaling_time > 120 else "medium"
            
            return ScalingEfficiencyIssue(
                issue_type="slow_scaling",
                severity=severity,
                description=f"Scaling is slow. Average scaling time: {avg_scaling_time:.1f}s",
                metrics={
                    "avg_scaling_time_seconds": avg_scaling_time,
                    "max_scaling_time_seconds": max(scaling_times),
                    "scaling_events_count": len(self.scaling_events),
                    "threshold": self.slow_scaling_threshold
                },
                recommendations=self._get_slow_scaling_recommendations(),
                confidence=min(1.0, (avg_scaling_time - 30) / 90.0),
                impact_score=min(100, (avg_scaling_time - 30) * 1.5)
            )
            
        return None
        
    def _detect_cold_starts(self) -> Optional[ScalingEfficiencyIssue]:
        """Detect cold start issues"""
        # Collect startup times
        startup_times = []
        
        for instance_metrics in self.instance_metrics.values():
            for metrics in instance_metrics:
                if metrics.startup_time_seconds is not None:
                    startup_times.append(metrics.startup_time_seconds)
                    
        if not startup_times:
            return None
            
        avg_startup_time = mean(startup_times)
        
        if avg_startup_time > self.cold_start_threshold:
            severity = "high" if avg_startup_time > 10 else "medium"
            
            return ScalingEfficiencyIssue(
                issue_type="cold_starts",
                severity=severity,
                description=f"Cold starts are slow. Average startup time: {avg_startup_time:.1f}s",
                metrics={
                    "avg_startup_time_seconds": avg_startup_time,
                    "max_startup_time_seconds": max(startup_times),
                    "cold_starts_count": len(startup_times),
                    "threshold": self.cold_start_threshold
                },
                recommendations=self._get_cold_start_recommendations(),
                confidence=min(1.0, (avg_startup_time - 2) / 8.0),
                impact_score=min(100, (avg_startup_time - 2) * 10)
            )
            
        return None
        
    def _get_over_provisioning_recommendations(self) -> List[str]:
        """Get recommendations for over-provisioning"""
        return [
            "Consider reducing minimum instance count",
            "Adjust scaling thresholds to scale down more aggressively",
            "Implement request-based scaling instead of CPU-based",
            "Review resource requests and reduce if possible",
            "Consider using fractional CPU allocation",
            "Implement more aggressive idle timeout policies",
            "Review load balancing configuration",
            "Consider implementing predictive scaling"
        ]
        
    def _get_under_provisioning_recommendations(self) -> List[str]:
        """Get recommendations for under-provisioning"""
        return [
            "Increase minimum instance count",
            "Adjust scaling thresholds to scale up more aggressively",
            "Increase resource requests (CPU/memory)",
            "Implement proactive scaling based on request patterns",
            "Consider increasing maximum instance count",
            "Add horizontal pod autoscaling",
            "Implement circuit breakers to handle overload",
            "Consider using CPU boost for startup performance"
        ]
        
    def _get_slow_scaling_recommendations(self) -> List[str]:
        """Get recommendations for slow scaling"""
        return [
            "Implement predictive scaling based on historical patterns",
            "Reduce container image size for faster startup",
            "Use multi-stage Docker builds",
            "Implement health check optimizations",
            "Consider using custom scaling metrics",
            "Implement warm-up pools for instances",
            "Optimize application startup time",
            "Consider using regional persistent disks"
        ]
        
    def _get_cold_start_recommendations(self) -> List[str]:
        """Get recommendations for cold starts"""
        return [
            "Increase minimum instance count to reduce cold starts",
            "Implement keep-alive requests to maintain warm instances",
            "Optimize application initialization code",
            "Use smaller base images",
            "Implement lazy loading for dependencies",
            "Consider using Cloud Run CPU boost",
            "Implement application-level caching",
            "Pre-compile frequently used code paths"
        ]
        
    def get_scaling_statistics(self) -> Dict[str, Any]:
        """Get comprehensive scaling statistics"""
        if not self.scaling_events:
            return {}
            
        # Scaling event statistics
        scale_up_events = [e for e in self.scaling_events if e.event_type == "scale_up"]
        scale_down_events = [e for e in self.scaling_events if e.event_type == "scale_down"]
        
        scaling_times = [e.scaling_duration_seconds for e in self.scaling_events]
        
        # Instance utilization statistics
        all_cpu_utils = []
        all_memory_utils = []
        all_response_times = []
        
        for instance_metrics in self.instance_metrics.values():
            for metrics in instance_metrics:
                all_cpu_utils.append(metrics.cpu_utilization)
                all_memory_utils.append(metrics.memory_utilization)
                all_response_times.append(metrics.response_time_ms)
                
        return {
            "scaling_events": {
                "total_events": len(self.scaling_events),
                "scale_up_events": len(scale_up_events),
                "scale_down_events": len(scale_down_events),
                "avg_scaling_time": mean(scaling_times) if scaling_times else 0,
                "max_scaling_time": max(scaling_times) if scaling_times else 0,
                "scaling_frequency_per_hour": len(self.scaling_events) / max(1, (time.time() - self.start_time) / 3600)
            },
            "instance_utilization": {
                "cpu": {
                    "mean": mean(all_cpu_utils) if all_cpu_utils else 0,
                    "median": median(all_cpu_utils) if all_cpu_utils else 0,
                    "max": max(all_cpu_utils) if all_cpu_utils else 0,
                    "min": min(all_cpu_utils) if all_cpu_utils else 0,
                    "p95": sorted(all_cpu_utils)[int(0.95 * len(all_cpu_utils))] if all_cpu_utils else 0
                },
                "memory": {
                    "mean": mean(all_memory_utils) if all_memory_utils else 0,
                    "median": median(all_memory_utils) if all_memory_utils else 0,
                    "max": max(all_memory_utils) if all_memory_utils else 0,
                    "min": min(all_memory_utils) if all_memory_utils else 0,
                    "p95": sorted(all_memory_utils)[int(0.95 * len(all_memory_utils))] if all_memory_utils else 0
                }
            },
            "performance": {
                "avg_response_time_ms": mean(all_response_times) if all_response_times else 0,
                "p95_response_time_ms": sorted(all_response_times)[int(0.95 * len(all_response_times))] if all_response_times else 0,
                "max_response_time_ms": max(all_response_times) if all_response_times else 0
            },
            "instance_count": {
                "current": self.current_instances,
                "min_configured": self.min_instances,
                "max_configured": self.max_instances,
                "utilization_efficiency": self._calculate_utilization_efficiency()
            },
            "monitoring_duration": time.time() - self.start_time if self.start_time else 0
        }
        
    def _calculate_utilization_efficiency(self) -> float:
        """Calculate utilization efficiency score (0-100)"""
        if not self.instance_metrics:
            return 0.0
            
        # Calculate how well resources are utilized
        all_cpu_utils = []
        all_memory_utils = []
        
        for instance_metrics in self.instance_metrics.values():
            for metrics in instance_metrics:
                all_cpu_utils.append(metrics.cpu_utilization)
                all_memory_utils.append(metrics.memory_utilization)
                
        if not all_cpu_utils:
            return 0.0
            
        avg_cpu = mean(all_cpu_utils)
        avg_memory = mean(all_memory_utils)
        
        # Ideal utilization is around 70%
        cpu_efficiency = 100 - abs(avg_cpu - 70)
        memory_efficiency = 100 - abs(avg_memory - 70)
        
        return (cpu_efficiency + memory_efficiency) / 2
        
    def get_scaling_efficiency_score(self) -> float:
        """Calculate overall scaling efficiency score (0-100)"""
        stats = self.get_scaling_statistics()
        
        if not stats:
            return 0.0
            
        # Utilization efficiency (40% of score)
        utilization_score = stats["instance_count"]["utilization_efficiency"] * 0.4
        
        # Scaling responsiveness (30% of score)
        avg_scaling_time = stats["scaling_events"]["avg_scaling_time"]
        scaling_score = max(0, 100 - avg_scaling_time * 2) * 0.3
        
        # Performance impact (20% of score)
        avg_response_time = stats["performance"]["avg_response_time_ms"]
        performance_score = max(0, 100 - (avg_response_time - 100) * 0.1) * 0.2
        
        # Issue penalty (10% of score)
        issue_penalty = len([i for i in self.scaling_issues if i.severity in ["high", "critical"]]) * 5
        
        final_score = utilization_score + scaling_score + performance_score - issue_penalty
        return max(0, min(100, final_score))
        
    def get_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """Get scaling optimization recommendations"""
        recommendations = []
        
        stats = self.get_scaling_statistics()
        efficiency_score = self.get_scaling_efficiency_score()
        
        # Utilization optimization
        cpu_mean = stats["instance_utilization"]["cpu"]["mean"]
        memory_mean = stats["instance_utilization"]["memory"]["mean"]
        
        if cpu_mean < 40 or memory_mean < 40:
            recommendations.append({
                "category": "over_provisioning",
                "priority": "high",
                "title": "Reduce Over-Provisioning",
                "description": f"Average utilization: CPU {cpu_mean:.1f}%, Memory {memory_mean:.1f}%",
                "actions": [
                    "Reduce minimum instance count",
                    "Adjust scaling thresholds",
                    "Optimize resource requests",
                    "Implement more aggressive scaling policies"
                ]
            })
            
        if cpu_mean > 85 or memory_mean > 85:
            recommendations.append({
                "category": "under_provisioning",
                "priority": "critical",
                "title": "Increase Resource Provisioning",
                "description": f"High utilization: CPU {cpu_mean:.1f}%, Memory {memory_mean:.1f}%",
                "actions": [
                    "Increase minimum instance count",
                    "Increase resource requests",
                    "Implement proactive scaling",
                    "Add horizontal scaling rules"
                ]
            })
            
        # Scaling speed optimization
        avg_scaling_time = stats["scaling_events"]["avg_scaling_time"]
        if avg_scaling_time > 60:
            recommendations.append({
                "category": "scaling_speed",
                "priority": "medium",
                "title": "Improve Scaling Speed",
                "description": f"Average scaling time: {avg_scaling_time:.1f}s",
                "actions": [
                    "Implement predictive scaling",
                    "Optimize container startup",
                    "Use smaller base images",
                    "Implement warm-up pools"
                ]
            })
            
        # Performance optimization
        avg_response_time = stats["performance"]["avg_response_time_ms"]
        if avg_response_time > 200:
            recommendations.append({
                "category": "performance",
                "priority": "medium",
                "title": "Optimize Performance",
                "description": f"Average response time: {avg_response_time:.1f}ms",
                "actions": [
                    "Implement connection pooling",
                    "Add caching layers",
                    "Optimize database queries",
                    "Use async processing"
                ]
            })
            
        return recommendations
        
    def get_analysis_report(self) -> Dict[str, Any]:
        """Generate comprehensive scaling analysis report"""
        stats = self.get_scaling_statistics()
        efficiency_score = self.get_scaling_efficiency_score()
        recommendations = self.get_optimization_recommendations()
        
        return {
            "monitoring_duration_hours": (time.time() - self.start_time) / 3600 if self.start_time else 0,
            "scaling_configuration": {
                "min_instances": self.min_instances,
                "max_instances": self.max_instances,
                "target_cpu_utilization": self.target_cpu_utilization,
                "target_memory_utilization": self.target_memory_utilization,
                "max_concurrent_requests": self.max_concurrent_requests
            },
            "scaling_statistics": stats,
            "efficiency_score": efficiency_score,
            "scaling_issues": [i.to_dict() for i in self.scaling_issues],
            "optimization_recommendations": recommendations,
            "cost_analysis": self._calculate_cost_analysis(),
            "performance_impact": self._calculate_performance_impact()
        }
        
    def _calculate_cost_analysis(self) -> Dict[str, Any]:
        """Calculate cost analysis for scaling decisions"""
        if not self.scaling_events:
            return {}
            
        # Estimate costs based on instance hours
        total_instance_hours = 0
        for event in self.scaling_events:
            # Assume each scaling event represents the instance count for the monitoring interval
            total_instance_hours += event.instance_count * (self.monitoring_interval / 3600)
            
        # Estimated cost per instance hour (Cloud Run pricing)
        cost_per_instance_hour = 0.048  # $0.048 per vCPU-hour
        estimated_cost = total_instance_hours * cost_per_instance_hour
        
        # Calculate potential savings
        ideal_instances = self.min_instances + (self.max_instances - self.min_instances) * 0.5
        potential_savings = max(0, (self.current_instances - ideal_instances) * cost_per_instance_hour)
        
        return {
            "total_instance_hours": total_instance_hours,
            "estimated_cost_usd": estimated_cost,
            "cost_per_instance_hour": cost_per_instance_hour,
            "potential_monthly_savings": potential_savings * 24 * 30,  # Monthly savings
            "cost_efficiency_score": min(100, (ideal_instances / max(1, self.current_instances)) * 100)
        }
        
    def _calculate_performance_impact(self) -> Dict[str, Any]:
        """Calculate performance impact of scaling decisions"""
        if not self.instance_metrics:
            return {}
            
        # Calculate performance metrics during scaling events
        scaling_response_times = []
        normal_response_times = []
        
        for instance_metrics in self.instance_metrics.values():
            for metrics in instance_metrics:
                # Check if this metric was recorded during a scaling event
                is_scaling_period = any(
                    abs(metrics.timestamp - event.timestamp) < 60
                    for event in self.scaling_events
                )
                
                if is_scaling_period:
                    scaling_response_times.append(metrics.response_time_ms)
                else:
                    normal_response_times.append(metrics.response_time_ms)
                    
        return {
            "scaling_impact": {
                "avg_response_time_during_scaling": mean(scaling_response_times) if scaling_response_times else 0,
                "avg_response_time_normal": mean(normal_response_times) if normal_response_times else 0,
                "performance_degradation_percent": (
                    (mean(scaling_response_times) - mean(normal_response_times)) / mean(normal_response_times) * 100
                    if scaling_response_times and normal_response_times and mean(normal_response_times) > 0
                    else 0
                )
            },
            "availability_impact": {
                "scaling_events_per_hour": len(self.scaling_events) / max(1, (time.time() - self.start_time) / 3600),
                "estimated_downtime_minutes": len([e for e in self.scaling_events if e.event_type == "scale_up"]) * 0.1  # 0.1 min per scale up
            }
        }
        
    def export_analysis_data(self, filename: str):
        """Export scaling analysis data"""
        data = {
            "metadata": {
                "start_time": self.start_time,
                "end_time": time.time(),
                "monitoring_interval": self.monitoring_interval,
                "scaling_events_count": len(self.scaling_events),
                "instance_metrics_count": sum(len(metrics) for metrics in self.instance_metrics.values())
            },
            "scaling_events": [e.to_dict() for e in self.scaling_events],
            "instance_metrics": {
                instance_id: [m.to_dict() for m in metrics] 
                for instance_id, metrics in self.instance_metrics.items()
            },
            "scaling_issues": [i.to_dict() for i in self.scaling_issues],
            "analysis_report": self.get_analysis_report()
        }
        
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)


class ScalingBenchmarkRunner:
    """Run scaling benchmarks and analysis"""
    
    def __init__(self, analyzer: ResourceScalingAnalyzer):
        self.analyzer = analyzer
        self.benchmark_results = {}
        
    async def run_load_scaling_benchmark(self, duration_hours: float = 2.0) -> Dict[str, Any]:
        """Run load scaling benchmark"""
        duration_seconds = int(duration_hours * 3600)
        print(f"Running load scaling benchmark for {duration_hours} hours...")
        
        self.analyzer.start_monitoring()
        
        # Let the analyzer run and simulate scaling
        await asyncio.sleep(duration_seconds)
        
        self.analyzer.stop_monitoring()
        
        result = self.analyzer.get_analysis_report()
        result.update({
            "benchmark_type": "load_scaling",
            "duration_hours": duration_hours,
            "simulated_load_pattern": "sine_wave_with_random_variations"
        })
        
        self.benchmark_results["load_scaling_benchmark"] = result
        return result
        
    def export_benchmark_results(self, filename: str):
        """Export benchmark results"""
        data = {
            "timestamp": datetime.now().isoformat(),
            "benchmark_results": self.benchmark_results
        }
        
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)