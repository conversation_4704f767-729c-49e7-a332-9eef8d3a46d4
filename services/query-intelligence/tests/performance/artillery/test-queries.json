[{"query": "What is authentication and how is it implemented?", "type": "simple", "expectedResponseTime": 1000, "complexity": "low"}, {"query": "Explain the entire authentication flow including JWT validation, rate limiting, circuit breakers, and error handling mechanisms", "type": "complex", "expectedResponseTime": 3000, "complexity": "high"}, {"query": "Find all functions that handle user authentication and authorization", "type": "code_search", "expectedResponseTime": 2000, "complexity": "medium"}, {"query": "Analyze the performance bottlenecks in the query processing pipeline and suggest optimizations", "type": "analytical", "expectedResponseTime": 4000, "complexity": "high"}, {"query": "How does the rate limiting mechanism work?", "type": "simple", "expectedResponseTime": 1500, "complexity": "low"}, {"query": "What security measures are implemented in the WebSocket connections?", "type": "security", "expectedResponseTime": 2500, "complexity": "medium"}, {"query": "Find all error handling patterns and their effectiveness", "type": "code_search", "expectedResponseTime": 3000, "complexity": "medium"}, {"query": "Explain the caching strategy and its impact on performance", "type": "analytical", "expectedResponseTime": 2000, "complexity": "medium"}, {"query": "How is the circuit breaker pattern implemented?", "type": "simple", "expectedResponseTime": 1500, "complexity": "low"}, {"query": "Generate a comprehensive security audit report for the entire system", "type": "complex", "expectedResponseTime": 5000, "complexity": "high"}, {"query": "Find all API endpoints and their authentication requirements", "type": "code_search", "expectedResponseTime": 2500, "complexity": "medium"}, {"query": "What monitoring and logging mechanisms are in place?", "type": "analytical", "expectedResponseTime": 2000, "complexity": "medium"}, {"query": "How does the system handle concurrent requests?", "type": "simple", "expectedResponseTime": 1500, "complexity": "low"}, {"query": "Analyze the database connection pooling and management", "type": "analytical", "expectedResponseTime": 3000, "complexity": "medium"}, {"query": "Find all middleware implementations and their purposes", "type": "code_search", "expectedResponseTime": 2500, "complexity": "medium"}, {"query": "What is the deployment and infrastructure configuration?", "type": "simple", "expectedResponseTime": 2000, "complexity": "low"}, {"query": "Explain the WebSocket streaming implementation in detail", "type": "complex", "expectedResponseTime": 4000, "complexity": "high"}, {"query": "How are metrics collected and exposed for monitoring?", "type": "analytical", "expectedResponseTime": 2500, "complexity": "medium"}, {"query": "Find all configuration management patterns and best practices", "type": "code_search", "expectedResponseTime": 3000, "complexity": "medium"}, {"query": "What testing strategies are implemented for quality assurance?", "type": "analytical", "expectedResponseTime": 3500, "complexity": "high"}]