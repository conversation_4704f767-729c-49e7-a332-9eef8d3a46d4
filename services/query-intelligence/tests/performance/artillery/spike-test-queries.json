[{"query": "Quick authentication check", "type": "simple", "priority": "high", "expectedResponseTime": 500}, {"query": "What is the current system status?", "type": "simple", "priority": "high", "expectedResponseTime": 800}, {"query": "Find all critical security vulnerabilities", "type": "security", "priority": "high", "expectedResponseTime": 2000}, {"query": "How does the system handle high load?", "type": "simple", "priority": "medium", "expectedResponseTime": 1000}, {"query": "Analyze performance under stress conditions", "type": "analytical", "priority": "medium", "expectedResponseTime": 3000}, {"query": "Find all rate limiting implementations", "type": "code_search", "priority": "medium", "expectedResponseTime": 1500}, {"query": "What happens during circuit breaker activation?", "type": "simple", "priority": "high", "expectedResponseTime": 1200}, {"query": "Emergency system diagnostic report", "type": "complex", "priority": "high", "expectedResponseTime": 4000}, {"query": "How does auto-scaling work?", "type": "simple", "priority": "medium", "expectedResponseTime": 1500}, {"query": "Find all error recovery mechanisms", "type": "code_search", "priority": "medium", "expectedResponseTime": 2000}, {"query": "What is the current resource utilization?", "type": "simple", "priority": "high", "expectedResponseTime": 800}, {"query": "Analyze system bottlenecks during peak load", "type": "analytical", "priority": "high", "expectedResponseTime": 3500}, {"query": "How does load balancing work?", "type": "simple", "priority": "medium", "expectedResponseTime": 1200}, {"query": "Find all timeout configurations", "type": "code_search", "priority": "medium", "expectedResponseTime": 1800}, {"query": "What monitoring alerts are active?", "type": "simple", "priority": "high", "expectedResponseTime": 1000}, {"query": "Emergency performance optimization suggestions", "type": "complex", "priority": "high", "expectedResponseTime": 4500}, {"query": "How does the system recover from failures?", "type": "simple", "priority": "medium", "expectedResponseTime": 1500}, {"query": "Find all caching strategies during high load", "type": "code_search", "priority": "medium", "expectedResponseTime": 2200}, {"query": "What is the current error rate?", "type": "simple", "priority": "high", "expectedResponseTime": 600}, {"query": "Comprehensive spike recovery analysis", "type": "complex", "priority": "medium", "expectedResponseTime": 5000}]