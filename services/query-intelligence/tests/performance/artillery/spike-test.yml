# Artillery Spike Load Testing Configuration
# 
# Tests system behavior under sudden traffic spikes and rapid load changes.
# Focuses on system resilience, auto-scaling, and recovery capabilities.

config:
  target: 'http://localhost:8000'
  phases:
    # Phase 1: Baseline load
    - duration: 120
      arrivalRate: 10
      name: "Baseline Load"
    
    # Phase 2: First spike (5x increase)
    - duration: 60
      arrivalRate: 50
      name: "First Spike"
    
    # Phase 3: Recovery period
    - duration: 120
      arrivalRate: 10
      name: "Recovery Period 1"
    
    # Phase 4: Second spike (10x increase)
    - duration: 60
      arrivalRate: 100
      name: "Second Spike"
    
    # Phase 5: Recovery period
    - duration: 120
      arrivalRate: 10
      name: "Recovery Period 2"
    
    # Phase 6: Major spike (20x increase)
    - duration: 60
      arrivalRate: 200
      name: "Major Spike"
    
    # Phase 7: Extended recovery
    - duration: 180
      arrivalRate: 10
      name: "Extended Recovery"
    
    # Phase 8: Extreme spike (50x increase)
    - duration: 30
      arrivalRate: 500
      name: "Extreme Spike"
    
    # Phase 9: Final recovery
    - duration: 180
      arrivalRate: 10
      name: "Final Recovery"

  # HTTP-specific configuration
  http:
    timeout: 30
    pool: 50
    maxSockets: 100
    
  # Performance monitoring
  metrics:
    - name: 'spike_response_time'
      type: 'histogram'
      summary: 'Response time during spike events'
    
    - name: 'spike_error_rate'
      type: 'rate'
      summary: 'Error rate during spike events'
    
    - name: 'recovery_time'
      type: 'histogram'
      summary: 'Time to recover from spike'
    
    - name: 'throughput_spike'
      type: 'rate'
      summary: 'Throughput during spike events'
    
    - name: 'circuit_breaker_trips'
      type: 'counter'
      summary: 'Circuit breaker activations'
    
    - name: 'rate_limit_hits'
      type: 'counter'
      summary: 'Rate limiting activations'
  
  # Test configuration
  variables:
    auth_token: 'test-token'
    repository_id: 'artillery-spike-test'
    
  # Custom payload data
  payload:
    - path: './spike-test-queries.json'
      fields:
        - query
        - type
        - priority
        - expectedResponseTime
  
  # Environment-specific settings
  environments:
    local:
      target: 'http://localhost:8000'
      phases:
        - duration: 60
          arrivalRate: 5
        - duration: 30
          arrivalRate: 50
        - duration: 60
          arrivalRate: 5
    
    staging:
      target: 'https://staging.example.com'
      phases:
        - duration: 120
          arrivalRate: 20
        - duration: 60
          arrivalRate: 200
        - duration: 120
          arrivalRate: 20
    
    production:
      target: 'https://api.example.com'
      phases:
        - duration: 300
          arrivalRate: 50
        - duration: 120
          arrivalRate: 500
        - duration: 300
          arrivalRate: 50
      tls:
        rejectUnauthorized: false

# Test scenarios
scenarios:
  # Scenario 1: API endpoint spike testing
  - name: 'API Endpoint Spike Testing'
    weight: 60
    flow:
      - post:
          url: '/api/v1/query'
          headers:
            Authorization: 'Bearer {{ auth_token }}'
            Content-Type: 'application/json'
            X-Test-Type: 'artillery-spike'
            X-Spike-Phase: '{{ $phase }}'
          json:
            query: '{{ query }}'
            repository_id: '{{ repository_id }}'
            metadata:
              test_type: 'artillery_spike'
              query_type: '{{ type }}'
              priority: '{{ priority }}'
              expected_time: '{{ expectedResponseTime }}'
              spike_phase: '{{ $phase }}'
              timestamp: '{{ $timestamp }}'
              user_id: '{{ $uuid }}'
          
          capture:
            - json: '$.answer'
              as: 'query_answer'
            - json: '$.processing_time'
              as: 'processing_time'
            - json: '$.cache_hit'
              as: 'cache_hit'
            - json: '$.error'
              as: 'query_error'
          
          expect:
            - statusCode: [200, 429, 503] # Accept rate limiting and service unavailable
            - hasHeader: 'content-type'
            - contentType: 'application/json'
        
        # Log spike performance
      - log: 'Spike API: {{ response.statusCode }}, Time: {{ processing_time }}ms, Cache: {{ cache_hit }}, Error: {{ query_error }}'
      
      - think: 
          min: 0.1
          max: 0.5

  # Scenario 2: WebSocket connection spike
  - name: 'WebSocket Connection Spike'
    weight: 30
    flow:
      - connect:
          url: '/api/v1/ws/query'
          headers:
            Authorization: 'Bearer {{ auth_token }}'
            X-Test-Type: 'artillery-spike-ws'
            X-Spike-Phase: '{{ $phase }}'
          
      - send:
          data: |
            {
              "query": "{{ query }}",
              "repository_id": "{{ repository_id }}",
              "stream": true,
              "metadata": {
                "test_type": "artillery_spike_ws",
                "query_type": "{{ type }}",
                "priority": "{{ priority }}",
                "spike_phase": "{{ $phase }}",
                "timestamp": "{{ $timestamp }}",
                "connection_id": "{{ $uuid }}"
              }
            }
            
      - wait:
          milliseconds: 
            min: 1000
            max: 5000
      
      - capture:
          - json: '$.type'
            as: 'ws_message_type'
          - json: '$.status'
            as: 'ws_status'
          - json: '$.error'
            as: 'ws_error'
      
      - log: 'Spike WebSocket: {{ ws_message_type }}, Status: {{ ws_status }}, Error: {{ ws_error }}'
      
      - close

  # Scenario 3: Health check monitoring during spikes
  - name: 'Health Check Monitoring'
    weight: 10
    flow:
      - get:
          url: '/health'
          headers:
            X-Test-Type: 'artillery-spike-health'
            X-Spike-Phase: '{{ $phase }}'
          
          capture:
            - json: '$.status'
              as: 'health_status'
            - json: '$.checks'
              as: 'health_checks'
          
          expect:
            - statusCode: [200, 503] # Healthy or unhealthy
            - hasHeader: 'content-type'
            - contentType: 'application/json'
      
      - log: 'Health during spike: {{ health_status }}, Checks: {{ health_checks }}'
      
      - think: 1
      
      # Check circuit breakers
      - get:
          url: '/circuit-breakers'
          headers:
            X-Test-Type: 'artillery-spike-circuit'
          
          capture:
            - json: '$.circuit_breakers'
              as: 'circuit_breakers'
          
          expect:
            - statusCode: [200, 503]
      
      - log: 'Circuit breakers: {{ circuit_breakers }}'

# Performance expectations during spikes
expectations:
  # Response time should degrade gracefully
  - metric: 'spike_response_time'
    threshold: 5000 # 5 seconds max during spikes
    operator: 'lessThan'
    value: 90 # 90th percentile
    
  # Error rate should be manageable
  - metric: 'spike_error_rate'
    threshold: 0.3 # 30% error rate acceptable during extreme spikes
    operator: 'lessThan'
    value: 95 # 95th percentile
    
  # Recovery should be quick
  - metric: 'recovery_time'
    threshold: 10000 # 10 seconds recovery time
    operator: 'lessThan'
    value: 95 # 95th percentile
    
  # Throughput should be maintained
  - metric: 'throughput_spike'
    threshold: 50 # At least 50 requests per second
    operator: 'greaterThan'
    value: 50 # At least 50th percentile

# Plugins for enhanced testing
plugins:
  # Metrics collection and export
  metrics-by-endpoint:
    useOnlyRequestNames: true
    
  # Performance monitoring
  cloudwatch:
    enabled: false # Enable for AWS monitoring
    region: 'us-east-1'
    namespace: 'Artillery/Spike'
    
  # Custom reporting
  html-report:
    enabled: true
    output: './artillery-spike-report.html'
    
  json-report:
    enabled: true
    output: './artillery-spike-results.json'
    
  # Real-time monitoring
  statsd:
    enabled: false # Enable for real-time monitoring
    host: 'localhost'
    port: 8125
    prefix: 'artillery.spike'

# Before and after hooks
before:
  flow:
    - log: 'Starting Artillery Spike Load Test'
    - log: 'Target: {{ target }}'
    - log: 'Spike phases: {{ phases.length }}'
    
    # Pre-test health check
    - get:
        url: '/health'
        expect:
          - statusCode: 200
    
    - log: 'Service is healthy - beginning spike test'

after:
  flow:
    - log: 'Artillery Spike Load Test completed'
    
    # Post-test health check
    - get:
        url: '/health'
        capture:
          - json: '$.status'
            as: 'final_health_status'
    
    - log: 'Final health status: {{ final_health_status }}'
    
    # Check circuit breakers
    - get:
        url: '/circuit-breakers'
        capture:
          - json: '$.circuit_breakers'
            as: 'final_circuit_breakers'
    
    - log: 'Final circuit breakers: {{ final_circuit_breakers }}'