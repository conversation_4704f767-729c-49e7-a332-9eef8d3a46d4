# Artillery Realtime Load Testing Configuration
# 
# Specialized for WebSocket real-time streaming performance testing
# with focus on concurrent connections, message latency, and throughput.

config:
  target: 'ws://localhost:8000'
  phases:
    # Phase 1: Warm-up with gradual connection increase
    - duration: 120
      arrivalRate: 5
      name: "Warm-up Phase"
    
    # Phase 2: Normal load with sustained connections
    - duration: 300
      arrivalRate: 20
      name: "Normal Load Phase"
    
    # Phase 3: High load with increased concurrent connections
    - duration: 300
      arrivalRate: 50
      name: "High Load Phase"
    
    # Phase 4: Peak load testing
    - duration: 300
      arrivalRate: 100
      name: "Peak Load Phase"
    
    # Phase 5: Stress testing with maximum connections
    - duration: 300
      arrivalRate: 200
      name: "Stress Phase"
    
    # Phase 6: Recovery and cool-down
    - duration: 120
      arrivalRate: 10
      name: "Recovery Phase"

  # WebSocket-specific configuration
  ws:
    # Connection timeout
    timeout: 30000
    
    # Maximum concurrent connections per virtual user
    maxConcurrentConnections: 10
    
    # Message send rate limiting
    sendRate: 5 # messages per second per connection
    
    # Auto-reconnect on connection loss
    autoReconnect: true
    maxReconnectAttempts: 3
    reconnectInterval: 5000
  
  # Performance monitoring
  metrics:
    - name: 'connection_time'
      type: 'histogram'
      summary: 'WebSocket connection establishment time'
    
    - name: 'message_latency'
      type: 'histogram'
      summary: 'End-to-end message latency'
    
    - name: 'concurrent_connections'
      type: 'gauge'
      summary: 'Number of concurrent WebSocket connections'
    
    - name: 'message_throughput'
      type: 'rate'
      summary: 'Messages per second throughput'
    
    - name: 'connection_errors'
      type: 'counter'
      summary: 'WebSocket connection errors'
    
    - name: 'message_errors'
      type: 'counter'
      summary: 'Message processing errors'
  
  # Test configuration
  variables:
    auth_token: 'test-token'
    base_url: 'http://localhost:8000'
    repository_id: 'artillery-realtime-test'
    
  # Custom payload data
  payload:
    - path: './test-queries.json'
      fields:
        - query
        - type
        - expectedResponseTime
        - complexity
  
  # Environment-specific settings
  environments:
    local:
      target: 'ws://localhost:8000'
      phases:
        - duration: 60
          arrivalRate: 10
    
    staging:
      target: 'wss://staging.example.com'
      phases:
        - duration: 300
          arrivalRate: 50
    
    production:
      target: 'wss://api.example.com'
      phases:
        - duration: 600
          arrivalRate: 100
      tls:
        rejectUnauthorized: false

# Test scenarios
scenarios:
  # Scenario 1: Concurrent streaming queries
  - name: 'Concurrent Streaming Queries'
    weight: 40
    flow:
      - connect:
          url: '/api/v1/ws/query'
          headers:
            Authorization: 'Bearer {{ auth_token }}'
            X-Test-Type: 'artillery-realtime'
          subprotocols: []
          
      - think: 1
      
      - loop:
          count: 10
          over:
            - send:
                data: |
                  {
                    "query": "{{ query }}",
                    "repository_id": "{{ repository_id }}",
                    "stream": true,
                    "metadata": {
                      "test_type": "artillery_realtime",
                      "query_type": "{{ type }}",
                      "expected_time": {{ expectedResponseTime }},
                      "complexity": "{{ complexity }}",
                      "timestamp": "{{ $timestamp }}",
                      "session_id": "{{ $uuid }}"
                    }
                  }
                
            - wait:
                milliseconds: 100
                
            - capture:
                - json: '$.type'
                  as: 'message_type'
                - json: '$.timestamp'
                  as: 'message_timestamp'
                - json: '$.content'
                  as: 'message_content'
                  
            - log: 'Received {{ message_type }} message'
            
            - think: 
                min: 1
                max: 3
      
      - close

  # Scenario 2: High-frequency message bursts
  - name: 'High-Frequency Message Bursts'
    weight: 30
    flow:
      - connect:
          url: '/api/v1/ws/query'
          headers:
            Authorization: 'Bearer {{ auth_token }}'
            X-Test-Type: 'artillery-burst'
            
      - think: 0.5
      
      - loop:
          count: 20
          over:
            - send:
                data: |
                  {
                    "query": "Quick analysis: {{ query }}",
                    "repository_id": "{{ repository_id }}",
                    "stream": true,
                    "priority": "high",
                    "metadata": {
                      "test_type": "artillery_burst",
                      "burst_index": {{ $loopCount }},
                      "timestamp": "{{ $timestamp }}"
                    }
                  }
                  
            - wait:
                milliseconds: 50
                
            - capture:
                - json: '$.type'
                  as: 'burst_message_type'
                  
      - think: 2
      - close

  # Scenario 3: Long-running streaming connections
  - name: 'Long-Running Streaming Connections'
    weight: 20
    flow:
      - connect:
          url: '/api/v1/ws/query'
          headers:
            Authorization: 'Bearer {{ auth_token }}'
            X-Test-Type: 'artillery-longrun'
            
      - send:
          data: |
            {
              "query": "{{ query }}",
              "repository_id": "{{ repository_id }}",
              "stream": true,
              "streaming_mode": "continuous",
              "metadata": {
                "test_type": "artillery_longrun",
                "duration": "extended",
                "timestamp": "{{ $timestamp }}"
              }
            }
            
      - wait:
          milliseconds: 30000 # Wait 30 seconds for streaming
          
      - capture:
          - json: '$.status'
            as: 'final_status'
            
      - log: 'Long-running connection completed with status: {{ final_status }}'
      - close

  # Scenario 4: Connection stability testing
  - name: 'Connection Stability Testing'
    weight: 10
    flow:
      - connect:
          url: '/api/v1/ws/query'
          headers:
            Authorization: 'Bearer {{ auth_token }}'
            X-Test-Type: 'artillery-stability'
            
      - loop:
          count: 50
          over:
            - send:
                data: |
                  {
                    "query": "Stability test {{ $loopCount }}: {{ query }}",
                    "repository_id": "{{ repository_id }}",
                    "stream": true,
                    "metadata": {
                      "test_type": "artillery_stability",
                      "iteration": {{ $loopCount }},
                      "timestamp": "{{ $timestamp }}"
                    }
                  }
                  
            - wait:
                milliseconds: 200
                
            - capture:
                - json: '$.type'
                  as: 'stability_message_type'
                - json: '$.error'
                  as: 'stability_error'
                  
            - think: 1
            
            # Check for errors and log them
            - log: 'Message {{ $loopCount }}: {{ stability_message_type }}, Error: {{ stability_error }}'
            
      - close

# Performance expectations and SLAs
expectations:
  # Connection establishment should be fast
  - metric: 'connection_time'
    threshold: 2000 # 2 seconds
    operator: 'lessThan'
    value: 95 # 95th percentile
    
  # Message latency should be low
  - metric: 'message_latency'
    threshold: 1000 # 1 second
    operator: 'lessThan'
    value: 95 # 95th percentile
    
  # High message throughput
  - metric: 'message_throughput'
    threshold: 100 # 100 messages per second
    operator: 'greaterThan'
    value: 50 # At least 50th percentile
    
  # Low error rates
  - metric: 'connection_errors'
    threshold: 50 # Maximum 50 connection errors
    operator: 'lessThan'
    value: 100 # Total count
    
  - metric: 'message_errors'
    threshold: 100 # Maximum 100 message errors
    operator: 'lessThan'
    value: 100 # Total count
    
  # Concurrent connection capacity
  - metric: 'concurrent_connections'
    threshold: 100 # Should support at least 100 concurrent connections
    operator: 'greaterThan'
    value: 50 # At least 50th percentile

# Plugins for enhanced testing
plugins:
  # Metrics collection and export
  metrics-by-endpoint:
    useOnlyRequestNames: true
    
  # WebSocket-specific metrics
  websocket-metrics:
    enabled: true
    detailed: true
    
  # Performance monitoring
  cloudwatch:
    enabled: false # Enable for AWS monitoring
    region: 'us-east-1'
    namespace: 'Artillery/WebSocket'
    
  # Custom reporting
  html-report:
    enabled: true
    output: './artillery-realtime-report.html'
    
  json-report:
    enabled: true
    output: './artillery-realtime-results.json'

# Before and after hooks
before:
  flow:
    - log: 'Starting Artillery WebSocket Realtime Load Test'
    - log: 'Target: {{ target }}'
    - log: 'Test scenarios: {{ scenarios.length }}'

after:
  flow:
    - log: 'Artillery WebSocket Realtime Load Test completed'
    - log: 'Check reports for detailed results'