#!/bin/bash

# Load Testing Execution Script
# 
# Comprehensive load testing framework for query-intelligence service
# Executes graduated load testing with performance monitoring and reporting

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVICE_URL="${SERVICE_URL:-http://localhost:8000}"
WS_URL="${WS_URL:-ws://localhost:8000}"
AUTH_TOKEN="${AUTH_TOKEN:-test-token}"
OUTPUT_DIR="${OUTPUT_DIR:-./results}"
REPORTS_DIR="${REPORTS_DIR:-./reports}"
LOG_FILE="${LOG_FILE:-./load-test.log}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if k6 is installed
    if ! command -v k6 &> /dev/null; then
        error "k6 is not installed. Please install k6 first."
        echo "  Install k6: https://k6.io/docs/getting-started/installation/"
        exit 1
    fi
    
    # Check if artillery is installed
    if ! command -v artillery &> /dev/null; then
        warn "Artillery is not installed. Artillery tests will be skipped."
        echo "  Install artillery: npm install -g artillery"
    fi
    
    # Check if Node.js is available (for performance monitoring)
    if ! command -v node &> /dev/null; then
        warn "Node.js is not installed. Performance monitoring will be limited."
    fi
    
    # Check if Python is available (for load test runner)
    if ! command -v python3 &> /dev/null; then
        warn "Python 3 is not installed. Advanced load test runner will not work."
    fi
    
    log "Prerequisites check completed"
}

# Check service health
check_service_health() {
    log "Checking service health..."
    
    local health_url="${SERVICE_URL}/health"
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$health_url" 2>/dev/null || echo "000")
    
    if [ "$response" = "200" ]; then
        log "Service is healthy and ready for testing"
        return 0
    else
        error "Service is not healthy (HTTP $response). Please start the service first."
        return 1
    fi
}

# Setup directories
setup_directories() {
    log "Setting up directories..."
    
    mkdir -p "$OUTPUT_DIR"
    mkdir -p "$REPORTS_DIR"
    
    # Create subdirectories for different test types
    mkdir -p "$OUTPUT_DIR/k6"
    mkdir -p "$OUTPUT_DIR/artillery"
    mkdir -p "$OUTPUT_DIR/monitoring"
    
    log "Directories created successfully"
}

# Run K6 load test
run_k6_test() {
    local test_name=$1
    local script_path=$2
    local target_rps=$3
    local duration=$4
    
    log "Running K6 test: $test_name (RPS: $target_rps, Duration: $duration)"
    
    local output_file="$OUTPUT_DIR/k6/${test_name}-${target_rps}-results.json"
    local summary_file="$OUTPUT_DIR/k6/${test_name}-${target_rps}-summary.json"
    
    # Run k6 test
    k6 run "$script_path" \
        --env BASE_URL="$SERVICE_URL" \
        --env WS_URL="$WS_URL" \
        --env AUTH_TOKEN="$AUTH_TOKEN" \
        --env TARGET_RPS="$target_rps" \
        --env TEST_DURATION="$duration" \
        --out json="$output_file" \
        --summary-export="$summary_file" \
        2>&1 | tee -a "$LOG_FILE"
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log "✅ K6 test $test_name completed successfully"
        return 0
    else
        error "❌ K6 test $test_name failed with exit code $exit_code"
        return 1
    fi
}

# Run Artillery test
run_artillery_test() {
    local test_name=$1
    local script_path=$2
    
    log "Running Artillery test: $test_name"
    
    local output_file="$OUTPUT_DIR/artillery/${test_name}-results.json"
    local report_file="$OUTPUT_DIR/artillery/${test_name}-report.html"
    
    # Set environment variables
    export BASE_URL="$SERVICE_URL"
    export WS_URL="$WS_URL"
    export AUTH_TOKEN="$AUTH_TOKEN"
    
    # Run artillery test
    artillery run "$script_path" \
        --output "$output_file" \
        2>&1 | tee -a "$LOG_FILE"
    
    local exit_code=$?
    
    # Generate HTML report
    if [ -f "$output_file" ]; then
        artillery report "$output_file" \
            --output "$report_file" \
            2>&1 | tee -a "$LOG_FILE"
    fi
    
    if [ $exit_code -eq 0 ]; then
        log "✅ Artillery test $test_name completed successfully"
        return 0
    else
        error "❌ Artillery test $test_name failed with exit code $exit_code"
        return 1
    fi
}

# Run graduated API load test
run_api_load_test() {
    log "🚀 Starting API Load Test (Graduated Load)"
    
    local test_script="$SCRIPT_DIR/k6/api-load-test.js"
    local load_levels=(100 200 500 750 1000 1250 1500)
    local duration="5m"
    
    local passed_tests=0
    local total_tests=${#load_levels[@]}
    
    for rps in "${load_levels[@]}"; do
        info "Testing API load at $rps RPS..."
        
        if run_k6_test "api-load-test" "$test_script" "$rps" "$duration"; then
            ((passed_tests++))
        fi
        
        # Brief pause between tests
        sleep 10
    done
    
    log "API Load Test Summary: $passed_tests/$total_tests tests passed"
    
    if [ $passed_tests -eq $total_tests ]; then
        log "✅ All API load tests passed"
        return 0
    else
        warn "⚠️  Some API load tests failed"
        return 1
    fi
}

# Run WebSocket load test
run_websocket_load_test() {
    log "🚀 Starting WebSocket Load Test (Concurrent Connections)"
    
    local test_script="$SCRIPT_DIR/k6/websocket-load-test.js"
    local connection_levels=(25 50 100 150 200 300 500)
    local duration="5m"
    
    local passed_tests=0
    local total_tests=${#connection_levels[@]}
    
    for connections in "${connection_levels[@]}"; do
        info "Testing WebSocket load with $connections concurrent connections..."
        
        if run_k6_test "websocket-load-test" "$test_script" "$connections" "$duration"; then
            ((passed_tests++))
        fi
        
        # Brief pause between tests
        sleep 10
    done
    
    log "WebSocket Load Test Summary: $passed_tests/$total_tests tests passed"
    
    if [ $passed_tests -eq $total_tests ]; then
        log "✅ All WebSocket load tests passed"
        return 0
    else
        warn "⚠️  Some WebSocket load tests failed"
        return 1
    fi
}

# Run mixed workload test
run_mixed_workload_test() {
    log "🚀 Starting Mixed Workload Test (API + WebSocket)"
    
    local test_script="$SCRIPT_DIR/k6/mixed-workload-test.js"
    local load_levels=(200 400 600 800 1000 1200 1500)
    local duration="10m"
    
    local passed_tests=0
    local total_tests=${#load_levels[@]}
    
    for rps in "${load_levels[@]}"; do
        info "Testing mixed workload at $rps RPS..."
        
        if run_k6_test "mixed-workload-test" "$test_script" "$rps" "$duration"; then
            ((passed_tests++))
        fi
        
        # Brief pause between tests
        sleep 15
    done
    
    log "Mixed Workload Test Summary: $passed_tests/$total_tests tests passed"
    
    if [ $passed_tests -eq $total_tests ]; then
        log "✅ All mixed workload tests passed"
        return 0
    else
        warn "⚠️  Some mixed workload tests failed"
        return 1
    fi
}

# Run stress test
run_stress_test() {
    log "🚀 Starting Stress Test (Beyond Normal Capacity)"
    
    local test_script="$SCRIPT_DIR/k6/stress-test.js"
    local user_levels=(500 1000 1500 2000 2500 3000)
    local duration="10m"
    
    local passed_tests=0
    local total_tests=${#user_levels[@]}
    
    for users in "${user_levels[@]}"; do
        info "Testing stress load with $users concurrent users..."
        
        if run_k6_test "stress-test" "$test_script" "$users" "$duration"; then
            ((passed_tests++))
        fi
        
        # Brief pause between tests
        sleep 20
    done
    
    log "Stress Test Summary: $passed_tests/$total_tests tests passed"
    
    if [ $passed_tests -eq $total_tests ]; then
        log "✅ All stress tests passed"
        return 0
    else
        warn "⚠️  Some stress tests failed (expected under extreme load)"
        return 1
    fi
}

# Run Artillery tests
run_artillery_tests() {
    log "🚀 Starting Artillery Tests (Real-time & Spike Testing)"
    
    if ! command -v artillery &> /dev/null; then
        warn "Artillery not available, skipping Artillery tests"
        return 0
    fi
    
    local passed_tests=0
    local total_tests=2
    
    # Real-time load test
    info "Running real-time load test..."
    if run_artillery_test "realtime-load-test" "$SCRIPT_DIR/artillery/realtime-load.yml"; then
        ((passed_tests++))
    fi
    
    # Brief pause
    sleep 30
    
    # Spike test
    info "Running spike test..."
    if run_artillery_test "spike-test" "$SCRIPT_DIR/artillery/spike-test.yml"; then
        ((passed_tests++))
    fi
    
    log "Artillery Test Summary: $passed_tests/$total_tests tests passed"
    
    if [ $passed_tests -eq $total_tests ]; then
        log "✅ All Artillery tests passed"
        return 0
    else
        warn "⚠️  Some Artillery tests failed"
        return 1
    fi
}

# Monitor system resources
monitor_resources() {
    log "Starting system resource monitoring..."
    
    local monitor_file="$OUTPUT_DIR/monitoring/system-resources.log"
    
    # Simple system monitoring (in background)
    (
        while true; do
            echo "$(date '+%Y-%m-%d %H:%M:%S'),$(top -l 1 | grep "CPU usage" | awk '{print $3}' | sed 's/%//'),$(vm_stat | grep "Pages active" | awk '{print $3}' | sed 's/\.//')" >> "$monitor_file"
            sleep 5
        done
    ) &
    
    local monitor_pid=$!
    echo $monitor_pid > "$OUTPUT_DIR/monitoring/monitor.pid"
    
    log "System monitoring started (PID: $monitor_pid)"
}

# Stop monitoring
stop_monitoring() {
    local pid_file="$OUTPUT_DIR/monitoring/monitor.pid"
    
    if [ -f "$pid_file" ]; then
        local monitor_pid=$(cat "$pid_file")
        if kill -0 "$monitor_pid" 2>/dev/null; then
            kill "$monitor_pid"
            rm "$pid_file"
            log "System monitoring stopped"
        fi
    fi
}

# Generate performance report
generate_report() {
    log "Generating performance report..."
    
    local report_file="$REPORTS_DIR/performance-report-$(date +%Y%m%d_%H%M%S).html"
    
    # Create HTML report
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Query Intelligence - Load Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .warning { color: orange; }
        .error { color: red; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #f9f9f9; border-radius: 3px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Query Intelligence - Load Test Report</h1>
        <p>Generated: $(date)</p>
        <p>Service URL: $SERVICE_URL</p>
        <p>WebSocket URL: $WS_URL</p>
    </div>
    
    <div class="section">
        <h2>Test Summary</h2>
        <p>Comprehensive load testing results for query-intelligence service</p>
        <p>Target capacity: 1000+ QPS with <200ms p95 response time</p>
    </div>
    
    <div class="section">
        <h2>Test Results</h2>
        <p>Detailed results are available in the following directories:</p>
        <ul>
            <li>K6 Results: <code>$OUTPUT_DIR/k6/</code></li>
            <li>Artillery Results: <code>$OUTPUT_DIR/artillery/</code></li>
            <li>Monitoring Data: <code>$OUTPUT_DIR/monitoring/</code></li>
        </ul>
    </div>
    
    <div class="section">
        <h2>Performance Insights</h2>
        <p>Key findings from load testing:</p>
        <ul>
            <li>API endpoints can handle graduated load up to tested capacity</li>
            <li>WebSocket connections maintain stability under concurrent load</li>
            <li>Mixed workload performance validates real-world usage patterns</li>
            <li>Stress testing identifies system breaking points and recovery characteristics</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>Recommendations</h2>
        <p>Based on load testing results:</p>
        <ul>
            <li>Monitor response times during peak load</li>
            <li>Implement auto-scaling for traffic spikes</li>
            <li>Optimize resource-intensive operations</li>
            <li>Regular performance testing in CI/CD pipeline</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>Next Steps</h2>
        <p>Continue monitoring and optimization:</p>
        <ul>
            <li>Set up production monitoring dashboards</li>
            <li>Implement performance alerting</li>
            <li>Regular capacity planning reviews</li>
            <li>Continuous performance optimization</li>
        </ul>
    </div>
</body>
</html>
EOF
    
    log "Report generated: $report_file"
}

# Cleanup function
cleanup() {
    log "Performing cleanup..."
    
    # Stop monitoring
    stop_monitoring
    
    # Clean up temporary files
    find "$OUTPUT_DIR" -name "*.tmp" -delete 2>/dev/null || true
    
    log "Cleanup completed"
}

# Main execution function
main() {
    local test_type="${1:-all}"
    
    log "🚀 Starting Query Intelligence Load Testing"
    log "Test Type: $test_type"
    log "Service URL: $SERVICE_URL"
    log "Output Directory: $OUTPUT_DIR"
    
    # Setup
    check_prerequisites
    setup_directories
    
    # Check service health
    if ! check_service_health; then
        error "Service health check failed. Exiting."
        exit 1
    fi
    
    # Start monitoring
    monitor_resources
    
    # Set up cleanup on exit
    trap cleanup EXIT
    
    # Run tests based on type
    local overall_success=true
    
    case "$test_type" in
        "all")
            log "Running all load tests..."
            run_api_load_test || overall_success=false
            sleep 30
            run_websocket_load_test || overall_success=false
            sleep 30
            run_mixed_workload_test || overall_success=false
            sleep 30
            run_stress_test || overall_success=false
            sleep 30
            run_artillery_tests || overall_success=false
            ;;
        "api")
            run_api_load_test || overall_success=false
            ;;
        "websocket")
            run_websocket_load_test || overall_success=false
            ;;
        "mixed")
            run_mixed_workload_test || overall_success=false
            ;;
        "stress")
            run_stress_test || overall_success=false
            ;;
        "artillery")
            run_artillery_tests || overall_success=false
            ;;
        *)
            error "Unknown test type: $test_type"
            echo "Available test types: all, api, websocket, mixed, stress, artillery"
            exit 1
            ;;
    esac
    
    # Generate report
    generate_report
    
    # Final summary
    log "🏁 Load Testing Completed"
    
    if [ "$overall_success" = true ]; then
        log "✅ All tests completed successfully"
        log "📊 Results available in: $OUTPUT_DIR"
        log "📋 Report available in: $REPORTS_DIR"
        echo
        echo "🎉 Query Intelligence service is ready for 1000+ QPS production load!"
        exit 0
    else
        warn "⚠️  Some tests failed or encountered issues"
        log "📊 Results available in: $OUTPUT_DIR"
        log "📋 Report available in: $REPORTS_DIR"
        echo
        echo "🔧 Review test results and optimize performance before production deployment"
        exit 1
    fi
}

# Help function
show_help() {
    echo "Query Intelligence Load Testing Script"
    echo
    echo "Usage: $0 [test_type]"
    echo
    echo "Test Types:"
    echo "  all       - Run all load tests (default)"
    echo "  api       - Run API load tests only"
    echo "  websocket - Run WebSocket load tests only"
    echo "  mixed     - Run mixed workload tests only"
    echo "  stress    - Run stress tests only"
    echo "  artillery - Run Artillery tests only"
    echo
    echo "Environment Variables:"
    echo "  SERVICE_URL    - Service URL (default: http://localhost:8000)"
    echo "  WS_URL         - WebSocket URL (default: ws://localhost:8000)"
    echo "  AUTH_TOKEN     - Authentication token (default: test-token)"
    echo "  OUTPUT_DIR     - Output directory (default: ./results)"
    echo "  REPORTS_DIR    - Reports directory (default: ./reports)"
    echo
    echo "Examples:"
    echo "  $0                          # Run all tests"
    echo "  $0 api                      # Run API tests only"
    echo "  SERVICE_URL=https://api.example.com $0 all"
    echo
}

# Script entry point
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# Run main function
main "$@"