# Query Intelligence Performance Analysis Framework

Comprehensive memory usage benchmarking and resource optimization analysis framework for the query-intelligence service.

## Overview

This framework provides detailed performance analysis capabilities to ensure the query-intelligence service meets production requirements:
- **Memory Usage Target**: <4GB under full load
- **CPU Utilization Target**: <80% under sustained load  
- **Cache Hit Rate Target**: 75%+ cache hit rate
- **Auto-scaling Efficiency**: 70%+ resource utilization
- **Response Time Target**: <500ms P95 response time

## Framework Components

### 1. Memory Profiling (`benchmarks/memory_profiling.py`)
- **MemoryProfiler**: Advanced memory usage tracking with leak detection
- **MemoryBenchmarkRunner**: Comprehensive memory benchmarking scenarios
- **Features**:
  - Real-time memory usage monitoring
  - Memory leak detection with trend analysis
  - Memory usage per connection analysis
  - Object lifecycle tracking
  - Memory optimization recommendations

### 2. CPU Utilization Analysis (`benchmarks/cpu_utilization_analysis.py`)
- **CPUAnalyzer**: CPU usage patterns and bottleneck detection
- **CPUBenchmarkRunner**: CPU-intensive and mixed workload benchmarks
- **Features**:
  - Per-core CPU utilization analysis
  - CPU bottleneck detection (high usage, load imbalance, context switching)
  - CPU optimization suggestions
  - Performance trend analysis

### 3. Network Performance Analysis (`benchmarks/network_performance.py`)
- **NetworkAnalyzer**: Network I/O performance monitoring
- **NetworkBenchmarkRunner**: HTTP and WebSocket performance benchmarks
- **Features**:
  - Bandwidth utilization monitoring
  - Network latency measurement
  - Connection efficiency analysis
  - DNS resolution performance
  - Network optimization recommendations

### 4. Cache Efficiency Analysis (`benchmarks/cache_efficiency_analysis.py`)
- **CacheEfficiencyAnalyzer**: Cache performance monitoring
- **CacheBenchmarkRunner**: Cache hit rate and memory efficiency benchmarks
- **Features**:
  - Cache hit/miss rate analysis
  - Hot/cold key detection
  - Cache memory usage optimization
  - Eviction pattern analysis
  - Cache optimization recommendations

### 5. Resource Scaling Analysis (`benchmarks/resource_scaling_analysis.py`)
- **ResourceScalingAnalyzer**: Auto-scaling efficiency analysis
- **ScalingBenchmarkRunner**: Cloud Run scaling behavior benchmarks
- **Features**:
  - Scaling event monitoring
  - Instance utilization analysis
  - Over/under-provisioning detection
  - Cold start analysis
  - Scaling optimization recommendations

### 6. Comprehensive Analysis Orchestrator (`comprehensive_performance_analysis.py`)
- **ComprehensivePerformanceAnalyzer**: Main orchestration tool
- **Features**:
  - Multi-phase analysis execution
  - Integrated benchmark suite
  - Performance scoring and compliance checking
  - Optimization recommendations
  - Production readiness assessment

## Usage

### Quick Analysis (30 minutes)
```python
from tests.performance.comprehensive_performance_analysis import ComprehensivePerformanceAnalyzer

analyzer = ComprehensivePerformanceAnalyzer()
results = await analyzer.run_quick_analysis()
```

### Full Production Validation (4 hours)
```python
analyzer = ComprehensivePerformanceAnalyzer()
results = await analyzer.run_production_validation()
```

### Custom Analysis Duration
```python
analyzer = ComprehensivePerformanceAnalyzer()
results = await analyzer.run_comprehensive_analysis(
    analysis_duration_hours=2.0,
    include_benchmarks=True
)
```

### Command Line Execution
```bash
cd /Users/<USER>/Documents/GitHub/episteme/services/query-intelligence
python -m tests.performance.comprehensive_performance_analysis
```

## Analysis Phases

### Phase 1: Baseline Analysis (60 seconds)
- Idle system resource usage
- Memory, CPU, network, and cache baselines
- Performance scoring baseline

### Phase 2: Load Testing Analysis (40% of total time)
- Simulated concurrent operations
- Resource usage under load
- Performance degradation analysis

### Phase 3: Sustained Load Analysis (40% of total time)
- Long-running load simulation
- Memory leak detection
- Performance stability validation

### Phase 4: Scaling Analysis (20% of total time)
- Auto-scaling behavior simulation
- Instance utilization efficiency
- Scaling recommendation generation

### Phase 5: Benchmark Suite (Optional)
- Specific performance benchmarks
- Comparative analysis
- Detailed optimization recommendations

## Performance Targets

### Memory Usage
- **Target**: <4GB under full load
- **Monitoring**: Real-time memory usage tracking
- **Validation**: Memory leak detection over 24+ hours
- **Optimization**: Memory allocation pattern analysis

### CPU Utilization
- **Target**: <80% under sustained load
- **Monitoring**: Per-core CPU usage analysis
- **Validation**: CPU bottleneck detection
- **Optimization**: Async processing recommendations

### Cache Performance
- **Target**: 75%+ cache hit rate
- **Monitoring**: Hit/miss rate analysis
- **Validation**: Cache efficiency scoring
- **Optimization**: Cache warming and eviction strategies

### Network Performance
- **Target**: <500ms P95 response time
- **Monitoring**: Latency and bandwidth analysis
- **Validation**: Network bottleneck detection
- **Optimization**: Connection pooling recommendations

### Auto-scaling Efficiency
- **Target**: 70%+ resource utilization
- **Monitoring**: Scaling event analysis
- **Validation**: Instance efficiency scoring
- **Optimization**: Scaling policy recommendations

## Results and Reporting

### Executive Summary
- Overall performance score (0-100)
- Component-specific scores
- Production readiness assessment
- 4GB memory compliance status

### Detailed Analysis
- Phase-by-phase results
- Benchmark comparisons
- Trend analysis
- Issue identification

### Optimization Recommendations
- Priority-based recommendations
- Implementation difficulty assessment
- Expected impact analysis
- Code examples and best practices

### Resource Efficiency Metrics
- Memory efficiency scoring
- CPU utilization optimization
- Network performance analysis
- Cache optimization opportunities

### Cost Analysis
- Resource allocation efficiency
- Scaling cost optimization
- Monthly cost projections
- Savings opportunities

## Configuration

### Analysis Configuration
```python
analyzer = ComprehensivePerformanceAnalyzer(
    results_dir="performance_analysis_results",
    enable_detailed_logging=True
)
```

### Memory Profiler Configuration
```python
memory_profiler = MemoryProfiler(
    profile_interval=1.0,
    enable_tracemalloc=True,
    tracemalloc_limit=25
)
```

### CPU Analyzer Configuration
```python
cpu_analyzer = CPUAnalyzer(
    analysis_interval=0.5
)
```

### Network Analyzer Configuration
```python
network_analyzer = NetworkAnalyzer(
    analysis_interval=2.0
)
```

### Cache Analyzer Configuration
```python
cache_analyzer = CacheEfficiencyAnalyzer(
    monitoring_interval=1.0,
    operation_history_size=10000
)
```

### Scaling Analyzer Configuration
```python
scaling_analyzer = ResourceScalingAnalyzer(
    monitoring_interval=5.0
)
```

## Output Files

### Analysis Results
- `comprehensive_analysis_YYYYMMDD_HHMMSS.json`: Complete analysis results
- `executive_summary_YYYYMMDD_HHMMSS.json`: Executive summary
- `optimization_recommendations_YYYYMMDD_HHMMSS.json`: Optimization recommendations

### Performance Data
- Memory snapshots and allocation data
- CPU utilization trends
- Network performance metrics
- Cache operation logs
- Scaling event history

## Integration with Existing Tests

The framework integrates with existing performance tests:

```python
# Integration with existing performance fixtures
from tests.e2e.fixtures.performance_fixtures import performance_monitor

# Use with existing test infrastructure
@pytest.mark.asyncio
async def test_comprehensive_performance():
    analyzer = ComprehensivePerformanceAnalyzer()
    results = await analyzer.run_comprehensive_analysis(
        analysis_duration_hours=1.0,
        include_benchmarks=True
    )
    
    # Assert production readiness
    assert results["executive_summary"]["production_readiness"]
    assert results["executive_summary"]["target_4gb_memory_compliance"]
```

## Production Deployment Validation

### Pre-deployment Checklist
- [ ] Memory usage <4GB under full load
- [ ] CPU utilization <80% under sustained load
- [ ] Cache hit rate >75%
- [ ] No memory leaks detected over 24+ hours
- [ ] Auto-scaling efficiency >70%
- [ ] Overall performance score >80

### Monitoring Setup
- Memory usage alerts at 3.5GB threshold
- CPU utilization alerts at 75% threshold
- Cache hit rate monitoring
- Auto-scaling efficiency monitoring
- Performance regression detection

### Optimization Priorities
1. **Memory optimization** (highest priority)
2. **Cache efficiency improvement**
3. **CPU utilization optimization**
4. **Auto-scaling tuning**
5. **Network performance optimization**

## Troubleshooting

### Common Issues
1. **High memory usage**: Check for memory leaks, optimize cache sizes
2. **High CPU utilization**: Implement async patterns, optimize algorithms
3. **Low cache hit rate**: Implement cache warming, review TTL settings
4. **Slow scaling**: Optimize container startup, implement predictive scaling

### Performance Debugging
```python
# Debug memory issues
memory_profiler.export_snapshots("memory_debug.json")

# Debug CPU issues
cpu_analyzer.export_analysis_data("cpu_debug.json")

# Debug network issues
network_analyzer.export_analysis_data("network_debug.json")
```

## Best Practices

1. **Run comprehensive analysis before production deployment**
2. **Monitor key metrics continuously in production**
3. **Set up automated performance regression detection**
4. **Regularly review and update optimization recommendations**
5. **Use performance budgets to prevent degradation**

## Support

For issues or questions about the performance analysis framework:
1. Check the analysis logs for detailed error information
2. Review the optimization recommendations
3. Consult the production readiness assessment
4. Validate against performance targets