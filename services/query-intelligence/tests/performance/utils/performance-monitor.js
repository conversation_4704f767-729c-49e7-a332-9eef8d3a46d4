/**
 * Performance Monitoring Utilities
 * 
 * Real-time performance monitoring, metrics collection, and visualization
 * for load testing and production monitoring.
 */

const WebSocket = require('ws');
const EventEmitter = require('events');
const fs = require('fs').promises;
const path = require('path');

class PerformanceMonitor extends EventEmitter {
  constructor(config = {}) {
    super();
    
    this.config = {
      baseUrl: config.baseUrl || 'http://localhost:8000',
      wsUrl: config.wsUrl || 'ws://localhost:8000',
      monitoringInterval: config.monitoringInterval || 1000,
      metricsRetention: config.metricsRetention || 3600000, // 1 hour
      alertThresholds: config.alertThresholds || {
        responseTime: 2000,
        errorRate: 0.05,
        throughput: 10,
        concurrentConnections: 100
      },
      ...config
    };
    
    this.metrics = {
      requests: [],
      websockets: [],
      system: [],
      errors: [],
      alerts: []
    };
    
    this.isMonitoring = false;
    this.startTime = null;
    this.monitoringInterval = null;
  }

  /**
   * Start performance monitoring
   */
  async startMonitoring() {
    if (this.isMonitoring) {
      throw new Error('Monitoring already started');
    }
    
    this.isMonitoring = true;
    this.startTime = Date.now();
    
    console.log(`Starting performance monitoring at ${new Date().toISOString()}`);
    console.log(`Target: ${this.config.baseUrl}`);
    console.log(`WebSocket: ${this.config.wsUrl}`);
    
    // Start system monitoring
    this.monitoringInterval = setInterval(() => {
      this.collectSystemMetrics();
    }, this.config.monitoringInterval);
    
    // Start health monitoring
    setInterval(() => {
      this.monitorServiceHealth();
    }, 5000);
    
    this.emit('monitoring_started', {
      timestamp: new Date().toISOString(),
      config: this.config
    });
  }

  /**
   * Stop performance monitoring
   */
  async stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }
    
    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }
    
    console.log(`Stopping performance monitoring at ${new Date().toISOString()}`);
    
    // Generate final report
    const report = await this.generateReport();
    await this.saveReport(report);
    
    this.emit('monitoring_stopped', {
      timestamp: new Date().toISOString(),
      duration: Date.now() - this.startTime,
      report: report
    });
    
    return report;
  }

  /**
   * Record API request metrics
   */
  recordRequest(requestMetrics) {
    const timestamp = Date.now();
    
    const metrics = {
      timestamp,
      ...requestMetrics,
      responseTime: requestMetrics.responseTime || 0,
      statusCode: requestMetrics.statusCode || 0,
      success: requestMetrics.success || false,
      endpoint: requestMetrics.endpoint || 'unknown',
      method: requestMetrics.method || 'unknown',
      userAgent: requestMetrics.userAgent || 'unknown'
    };
    
    this.metrics.requests.push(metrics);
    
    // Check for performance alerts
    this.checkPerformanceAlerts(metrics);
    
    // Cleanup old metrics
    this.cleanupOldMetrics();
    
    this.emit('request_recorded', metrics);
  }

  /**
   * Record WebSocket connection metrics
   */
  recordWebSocketConnection(connectionMetrics) {
    const timestamp = Date.now();
    
    const metrics = {
      timestamp,
      ...connectionMetrics,
      connectionTime: connectionMetrics.connectionTime || 0,
      messagesSent: connectionMetrics.messagesSent || 0,
      messagesReceived: connectionMetrics.messagesReceived || 0,
      connectionDuration: connectionMetrics.connectionDuration || 0,
      errors: connectionMetrics.errors || 0
    };
    
    this.metrics.websockets.push(metrics);
    
    // Check for WebSocket alerts
    this.checkWebSocketAlerts(metrics);
    
    this.emit('websocket_recorded', metrics);
  }

  /**
   * Record error metrics
   */
  recordError(errorMetrics) {
    const timestamp = Date.now();
    
    const metrics = {
      timestamp,
      ...errorMetrics,
      errorType: errorMetrics.errorType || 'unknown',
      message: errorMetrics.message || '',
      stack: errorMetrics.stack || '',
      endpoint: errorMetrics.endpoint || 'unknown'
    };
    
    this.metrics.errors.push(metrics);
    
    this.emit('error_recorded', metrics);
  }

  /**
   * Collect system metrics
   */
  async collectSystemMetrics() {
    try {
      // Get system information (simplified for demo)
      const metrics = {
        timestamp: Date.now(),
        cpuUsage: Math.random() * 100, // In production, use actual CPU monitoring
        memoryUsage: Math.random() * 100,
        diskUsage: Math.random() * 100,
        networkIn: Math.random() * 1000,
        networkOut: Math.random() * 1000,
        activeConnections: this.getCurrentActiveConnections(),
        requestsPerSecond: this.getCurrentRequestsPerSecond(),
        errorRate: this.getCurrentErrorRate()
      };
      
      this.metrics.system.push(metrics);
      
      // Check for system alerts
      this.checkSystemAlerts(metrics);
      
      this.emit('system_metrics_collected', metrics);
      
    } catch (error) {
      console.error('Error collecting system metrics:', error);
    }
  }

  /**
   * Monitor service health
   */
  async monitorServiceHealth() {
    try {
      const healthCheckStart = Date.now();
      
      // In a real implementation, make actual HTTP requests
      const healthResponse = {
        status: 'healthy',
        responseTime: Math.random() * 100,
        timestamp: Date.now()
      };
      
      const healthMetrics = {
        timestamp: Date.now(),
        healthy: healthResponse.status === 'healthy',
        responseTime: Date.now() - healthCheckStart,
        checks: healthResponse.checks || {}
      };
      
      this.emit('health_check_completed', healthMetrics);
      
    } catch (error) {
      console.error('Health check failed:', error);
      this.emit('health_check_failed', { error: error.message, timestamp: Date.now() });
    }
  }

  /**
   * Check for performance alerts
   */
  checkPerformanceAlerts(metrics) {
    const alerts = [];
    
    // Response time alert
    if (metrics.responseTime > this.config.alertThresholds.responseTime) {
      alerts.push({
        type: 'high_response_time',
        severity: 'warning',
        message: `Response time ${metrics.responseTime}ms exceeds threshold ${this.config.alertThresholds.responseTime}ms`,
        timestamp: Date.now(),
        metrics: metrics
      });
    }
    
    // Error rate alert
    const currentErrorRate = this.getCurrentErrorRate();
    if (currentErrorRate > this.config.alertThresholds.errorRate) {
      alerts.push({
        type: 'high_error_rate',
        severity: 'critical',
        message: `Error rate ${(currentErrorRate * 100).toFixed(2)}% exceeds threshold ${(this.config.alertThresholds.errorRate * 100).toFixed(2)}%`,
        timestamp: Date.now(),
        metrics: { errorRate: currentErrorRate }
      });
    }
    
    // Process alerts
    alerts.forEach(alert => {
      this.metrics.alerts.push(alert);
      this.emit('alert_triggered', alert);
      console.warn(`ALERT [${alert.severity}]: ${alert.message}`);
    });
  }

  /**
   * Check for WebSocket alerts
   */
  checkWebSocketAlerts(metrics) {
    const alerts = [];
    
    // Connection time alert
    if (metrics.connectionTime > 2000) {
      alerts.push({
        type: 'slow_websocket_connection',
        severity: 'warning',
        message: `WebSocket connection time ${metrics.connectionTime}ms is slow`,
        timestamp: Date.now(),
        metrics: metrics
      });
    }
    
    // Connection error alert
    if (metrics.errors > 0) {
      alerts.push({
        type: 'websocket_connection_errors',
        severity: 'error',
        message: `WebSocket connection has ${metrics.errors} errors`,
        timestamp: Date.now(),
        metrics: metrics
      });
    }
    
    // Process alerts
    alerts.forEach(alert => {
      this.metrics.alerts.push(alert);
      this.emit('alert_triggered', alert);
      console.warn(`WEBSOCKET ALERT [${alert.severity}]: ${alert.message}`);
    });
  }

  /**
   * Check for system alerts
   */
  checkSystemAlerts(metrics) {
    const alerts = [];
    
    // CPU usage alert
    if (metrics.cpuUsage > 80) {
      alerts.push({
        type: 'high_cpu_usage',
        severity: 'warning',
        message: `CPU usage ${metrics.cpuUsage.toFixed(1)}% is high`,
        timestamp: Date.now(),
        metrics: metrics
      });
    }
    
    // Memory usage alert
    if (metrics.memoryUsage > 90) {
      alerts.push({
        type: 'high_memory_usage',
        severity: 'critical',
        message: `Memory usage ${metrics.memoryUsage.toFixed(1)}% is critical`,
        timestamp: Date.now(),
        metrics: metrics
      });
    }
    
    // Low throughput alert
    if (metrics.requestsPerSecond < this.config.alertThresholds.throughput) {
      alerts.push({
        type: 'low_throughput',
        severity: 'warning',
        message: `Throughput ${metrics.requestsPerSecond.toFixed(1)} RPS is below threshold ${this.config.alertThresholds.throughput}`,
        timestamp: Date.now(),
        metrics: metrics
      });
    }
    
    // Process alerts
    alerts.forEach(alert => {
      this.metrics.alerts.push(alert);
      this.emit('alert_triggered', alert);
      console.warn(`SYSTEM ALERT [${alert.severity}]: ${alert.message}`);
    });
  }

  /**
   * Get current active connections
   */
  getCurrentActiveConnections() {
    const now = Date.now();
    const activeWindow = 10000; // 10 seconds
    
    return this.metrics.websockets.filter(ws => 
      now - ws.timestamp < activeWindow && ws.connectionDuration > 0
    ).length;
  }

  /**
   * Get current requests per second
   */
  getCurrentRequestsPerSecond() {
    const now = Date.now();
    const timeWindow = 1000; // 1 second
    
    const recentRequests = this.metrics.requests.filter(req => 
      now - req.timestamp < timeWindow
    );
    
    return recentRequests.length;
  }

  /**
   * Get current error rate
   */
  getCurrentErrorRate() {
    const now = Date.now();
    const timeWindow = 60000; // 1 minute
    
    const recentRequests = this.metrics.requests.filter(req => 
      now - req.timestamp < timeWindow
    );
    
    if (recentRequests.length === 0) {
      return 0;
    }
    
    const errorCount = recentRequests.filter(req => !req.success).length;
    return errorCount / recentRequests.length;
  }

  /**
   * Clean up old metrics
   */
  cleanupOldMetrics() {
    const cutoffTime = Date.now() - this.config.metricsRetention;
    
    this.metrics.requests = this.metrics.requests.filter(m => m.timestamp > cutoffTime);
    this.metrics.websockets = this.metrics.websockets.filter(m => m.timestamp > cutoffTime);
    this.metrics.system = this.metrics.system.filter(m => m.timestamp > cutoffTime);
    this.metrics.errors = this.metrics.errors.filter(m => m.timestamp > cutoffTime);
    this.metrics.alerts = this.metrics.alerts.filter(m => m.timestamp > cutoffTime);
  }

  /**
   * Generate performance report
   */
  async generateReport() {
    const now = Date.now();
    const duration = now - this.startTime;
    
    // Calculate aggregated metrics
    const requestMetrics = this.calculateRequestMetrics();
    const websocketMetrics = this.calculateWebSocketMetrics();
    const systemMetrics = this.calculateSystemMetrics();
    const errorMetrics = this.calculateErrorMetrics();
    
    return {
      testInfo: {
        startTime: new Date(this.startTime).toISOString(),
        endTime: new Date(now).toISOString(),
        duration: duration,
        durationFormatted: this.formatDuration(duration)
      },
      summary: {
        totalRequests: this.metrics.requests.length,
        totalWebSocketConnections: this.metrics.websockets.length,
        totalErrors: this.metrics.errors.length,
        totalAlerts: this.metrics.alerts.length,
        overallSuccessRate: this.calculateOverallSuccessRate(),
        averageResponseTime: requestMetrics.averageResponseTime,
        averageThroughput: requestMetrics.averageThroughput
      },
      performance: {
        requests: requestMetrics,
        websockets: websocketMetrics,
        system: systemMetrics,
        errors: errorMetrics
      },
      alerts: this.metrics.alerts,
      recommendations: this.generateRecommendations()
    };
  }

  /**
   * Calculate request metrics
   */
  calculateRequestMetrics() {
    const requests = this.metrics.requests;
    
    if (requests.length === 0) {
      return {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        successRate: 0,
        averageResponseTime: 0,
        medianResponseTime: 0,
        p95ResponseTime: 0,
        p99ResponseTime: 0,
        minResponseTime: 0,
        maxResponseTime: 0,
        averageThroughput: 0
      };
    }
    
    const successful = requests.filter(r => r.success);
    const failed = requests.filter(r => !r.success);
    const responseTimes = requests.map(r => r.responseTime).sort((a, b) => a - b);
    
    const duration = (Date.now() - this.startTime) / 1000; // seconds
    
    return {
      totalRequests: requests.length,
      successfulRequests: successful.length,
      failedRequests: failed.length,
      successRate: successful.length / requests.length,
      averageResponseTime: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length,
      medianResponseTime: responseTimes[Math.floor(responseTimes.length / 2)],
      p95ResponseTime: responseTimes[Math.floor(responseTimes.length * 0.95)],
      p99ResponseTime: responseTimes[Math.floor(responseTimes.length * 0.99)],
      minResponseTime: Math.min(...responseTimes),
      maxResponseTime: Math.max(...responseTimes),
      averageThroughput: requests.length / duration
    };
  }

  /**
   * Calculate WebSocket metrics
   */
  calculateWebSocketMetrics() {
    const websockets = this.metrics.websockets;
    
    if (websockets.length === 0) {
      return {
        totalConnections: 0,
        averageConnectionTime: 0,
        totalMessagesSent: 0,
        totalMessagesReceived: 0,
        averageConnectionDuration: 0,
        totalErrors: 0
      };
    }
    
    return {
      totalConnections: websockets.length,
      averageConnectionTime: websockets.reduce((a, b) => a + b.connectionTime, 0) / websockets.length,
      totalMessagesSent: websockets.reduce((a, b) => a + b.messagesSent, 0),
      totalMessagesReceived: websockets.reduce((a, b) => a + b.messagesReceived, 0),
      averageConnectionDuration: websockets.reduce((a, b) => a + b.connectionDuration, 0) / websockets.length,
      totalErrors: websockets.reduce((a, b) => a + b.errors, 0)
    };
  }

  /**
   * Calculate system metrics
   */
  calculateSystemMetrics() {
    const system = this.metrics.system;
    
    if (system.length === 0) {
      return {
        averageCpuUsage: 0,
        averageMemoryUsage: 0,
        averageDiskUsage: 0,
        maxCpuUsage: 0,
        maxMemoryUsage: 0,
        maxActiveConnections: 0,
        averageRequestsPerSecond: 0
      };
    }
    
    return {
      averageCpuUsage: system.reduce((a, b) => a + b.cpuUsage, 0) / system.length,
      averageMemoryUsage: system.reduce((a, b) => a + b.memoryUsage, 0) / system.length,
      averageDiskUsage: system.reduce((a, b) => a + b.diskUsage, 0) / system.length,
      maxCpuUsage: Math.max(...system.map(s => s.cpuUsage)),
      maxMemoryUsage: Math.max(...system.map(s => s.memoryUsage)),
      maxActiveConnections: Math.max(...system.map(s => s.activeConnections)),
      averageRequestsPerSecond: system.reduce((a, b) => a + b.requestsPerSecond, 0) / system.length
    };
  }

  /**
   * Calculate error metrics
   */
  calculateErrorMetrics() {
    const errors = this.metrics.errors;
    
    const errorsByType = {};
    const errorsByEndpoint = {};
    
    errors.forEach(error => {
      errorsByType[error.errorType] = (errorsByType[error.errorType] || 0) + 1;
      errorsByEndpoint[error.endpoint] = (errorsByEndpoint[error.endpoint] || 0) + 1;
    });
    
    return {
      totalErrors: errors.length,
      errorsByType,
      errorsByEndpoint,
      errorRate: this.getCurrentErrorRate()
    };
  }

  /**
   * Calculate overall success rate
   */
  calculateOverallSuccessRate() {
    const requests = this.metrics.requests;
    
    if (requests.length === 0) {
      return 1.0;
    }
    
    const successful = requests.filter(r => r.success).length;
    return successful / requests.length;
  }

  /**
   * Generate performance recommendations
   */
  generateRecommendations() {
    const recommendations = [];
    
    const requestMetrics = this.calculateRequestMetrics();
    const systemMetrics = this.calculateSystemMetrics();
    const errorMetrics = this.calculateErrorMetrics();
    
    // Response time recommendations
    if (requestMetrics.averageResponseTime > 2000) {
      recommendations.push({
        type: 'performance',
        priority: 'high',
        message: 'Average response time is high. Consider optimizing query processing or adding caching.',
        metric: `${requestMetrics.averageResponseTime.toFixed(0)}ms average response time`
      });
    }
    
    // Error rate recommendations
    if (errorMetrics.errorRate > 0.05) {
      recommendations.push({
        type: 'reliability',
        priority: 'critical',
        message: 'Error rate is high. Review error logs and improve error handling.',
        metric: `${(errorMetrics.errorRate * 100).toFixed(2)}% error rate`
      });
    }
    
    // Throughput recommendations
    if (requestMetrics.averageThroughput < 10) {
      recommendations.push({
        type: 'capacity',
        priority: 'medium',
        message: 'Low throughput detected. Consider scaling up resources or optimizing performance.',
        metric: `${requestMetrics.averageThroughput.toFixed(1)} requests/second`
      });
    }
    
    // System resource recommendations
    if (systemMetrics.maxCpuUsage > 80) {
      recommendations.push({
        type: 'resources',
        priority: 'high',
        message: 'High CPU usage detected. Consider optimizing CPU-intensive operations or scaling.',
        metric: `${systemMetrics.maxCpuUsage.toFixed(1)}% max CPU usage`
      });
    }
    
    if (systemMetrics.maxMemoryUsage > 90) {
      recommendations.push({
        type: 'resources',
        priority: 'critical',
        message: 'High memory usage detected. Check for memory leaks or increase available memory.',
        metric: `${systemMetrics.maxMemoryUsage.toFixed(1)}% max memory usage`
      });
    }
    
    if (recommendations.length === 0) {
      recommendations.push({
        type: 'success',
        priority: 'low',
        message: 'Performance is within acceptable limits.',
        metric: 'All metrics are healthy'
      });
    }
    
    return recommendations;
  }

  /**
   * Save report to file
   */
  async saveReport(report) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `performance-report-${timestamp}.json`;
    const filepath = path.join(process.cwd(), 'reports', filename);
    
    try {
      await fs.mkdir(path.dirname(filepath), { recursive: true });
      await fs.writeFile(filepath, JSON.stringify(report, null, 2));
      
      console.log(`Performance report saved to: ${filepath}`);
      return filepath;
      
    } catch (error) {
      console.error('Error saving performance report:', error);
      throw error;
    }
  }

  /**
   * Format duration in human-readable format
   */
  formatDuration(milliseconds) {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Get real-time metrics
   */
  getRealTimeMetrics() {
    return {
      timestamp: Date.now(),
      activeConnections: this.getCurrentActiveConnections(),
      requestsPerSecond: this.getCurrentRequestsPerSecond(),
      errorRate: this.getCurrentErrorRate(),
      totalRequests: this.metrics.requests.length,
      totalWebSocketConnections: this.metrics.websockets.length,
      totalErrors: this.metrics.errors.length,
      totalAlerts: this.metrics.alerts.length,
      recentAlerts: this.metrics.alerts.slice(-5) // Last 5 alerts
    };
  }
}

module.exports = PerformanceMonitor;