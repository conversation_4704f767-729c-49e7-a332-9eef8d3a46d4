<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Query Intelligence - Performance Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .dashboard {
            max-width: 1400px;
            margin: 20px auto;
            padding: 0 20px;
        }
        
        .metrics-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .metric-label {
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: #666;
        }
        
        .metric-change {
            font-size: 0.8rem;
            margin-top: 5px;
        }
        
        .metric-good { color: #27ae60; }
        .metric-warning { color: #f39c12; }
        .metric-critical { color: #e74c3c; }
        
        .charts-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .chart-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .chart-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        .controls {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .controls h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .control-group {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .control-group label {
            font-weight: bold;
            min-width: 100px;
        }
        
        .control-group select,
        .control-group input,
        .control-group button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .control-group button {
            background: #667eea;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .control-group button:hover {
            background: #5a67d8;
        }
        
        .alerts {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .alerts h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .alert {
            display: flex;
            align-items: center;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .alert-critical {
            background: #fee;
            border-left: 4px solid #e74c3c;
        }
        
        .alert-warning {
            background: #fff8e1;
            border-left: 4px solid #f39c12;
        }
        
        .alert-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        
        .alert-icon {
            margin-right: 10px;
            font-size: 16px;
        }
        
        .test-results {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .test-results h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .test-result {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 6px;
            background: #f8f9fa;
        }
        
        .test-name {
            font-weight: bold;
        }
        
        .test-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-passed { background: #d4edda; color: #155724; }
        .status-failed { background: #f8d7da; color: #721c24; }
        .status-running { background: #fff3cd; color: #856404; }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            font-size: 14px;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .loading::after {
            content: "...";
            animation: dots 1.5s steps(5, end) infinite;
        }
        
        @keyframes dots {
            0%, 20% { content: "."; }
            40% { content: ".."; }
            60% { content: "..."; }
            80%, 100% { content: ""; }
        }
        
        @media (max-width: 768px) {
            .metrics-overview {
                grid-template-columns: 1fr;
            }
            
            .charts-container {
                grid-template-columns: 1fr;
            }
            
            .control-group {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .control-group label {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Query Intelligence Performance Dashboard</h1>
        <p>Real-time Load Testing and Performance Monitoring</p>
    </div>
    
    <div class="dashboard">
        <div class="controls">
            <h3>Test Controls</h3>
            <div class="control-group">
                <label for="testType">Test Type:</label>
                <select id="testType">
                    <option value="all">All Tests</option>
                    <option value="api_load_test">API Load Test</option>
                    <option value="websocket_load_test">WebSocket Load Test</option>
                    <option value="mixed_workload_test">Mixed Workload Test</option>
                    <option value="stress_test">Stress Test</option>
                    <option value="realtime_load_test">Real-time Load Test</option>
                    <option value="spike_test">Spike Test</option>
                </select>
                <button onclick="startTest()">Start Test</button>
                <button onclick="stopTest()">Stop Test</button>
            </div>
            
            <div class="control-group">
                <label for="targetLoad">Target Load:</label>
                <input type="number" id="targetLoad" value="100" min="1" max="5000">
                <label for="duration">Duration (min):</label>
                <input type="number" id="duration" value="5" min="1" max="60">
                <button onclick="refreshDashboard()">Refresh</button>
            </div>
        </div>
        
        <div class="metrics-overview">
            <div class="metric-card">
                <div class="metric-value metric-good" id="throughput">0</div>
                <div class="metric-label">Requests/sec</div>
                <div class="metric-change metric-good" id="throughputChange">-</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value metric-warning" id="responseTime">0</div>
                <div class="metric-label">Response Time (ms)</div>
                <div class="metric-change metric-warning" id="responseTimeChange">-</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value metric-good" id="successRate">0</div>
                <div class="metric-label">Success Rate (%)</div>
                <div class="metric-change metric-good" id="successRateChange">-</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value metric-critical" id="errorRate">0</div>
                <div class="metric-label">Error Rate (%)</div>
                <div class="metric-change metric-critical" id="errorRateChange">-</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value metric-good" id="activeConnections">0</div>
                <div class="metric-label">Active Connections</div>
                <div class="metric-change metric-good" id="activeConnectionsChange">-</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value metric-warning" id="cpuUsage">0</div>
                <div class="metric-label">CPU Usage (%)</div>
                <div class="metric-change metric-warning" id="cpuUsageChange">-</div>
            </div>
        </div>
        
        <div class="charts-container">
            <div class="chart-card">
                <div class="chart-title">Response Time Trends</div>
                <div class="chart-container">
                    <canvas id="responseTimeChart"></canvas>
                </div>
            </div>
            
            <div class="chart-card">
                <div class="chart-title">Throughput and Error Rate</div>
                <div class="chart-container">
                    <canvas id="throughputChart"></canvas>
                </div>
            </div>
            
            <div class="chart-card">
                <div class="chart-title">System Resources</div>
                <div class="chart-container">
                    <canvas id="resourceChart"></canvas>
                </div>
            </div>
            
            <div class="chart-card">
                <div class="chart-title">Connection Metrics</div>
                <div class="chart-container">
                    <canvas id="connectionChart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="alerts">
            <h3>Performance Alerts</h3>
            <div id="alertsList">
                <div class="loading">Loading alerts</div>
            </div>
        </div>
        
        <div class="test-results">
            <h3>Test Results</h3>
            <div id="testResultsList">
                <div class="loading">Loading test results</div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>Query Intelligence Performance Dashboard - Real-time monitoring and load testing</p>
        <p>Last updated: <span id="lastUpdated">-</span></p>
    </div>
    
    <script>
        // Chart configurations
        const chartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'minute'
                    }
                },
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            }
        };
        
        // Initialize charts
        const responseTimeChart = new Chart(document.getElementById('responseTimeChart'), {
            type: 'line',
            data: {
                datasets: [{
                    label: 'Average Response Time',
                    data: [],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }, {
                    label: 'P95 Response Time',
                    data: [],
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    tension: 0.1
                }]
            },
            options: chartOptions
        });
        
        const throughputChart = new Chart(document.getElementById('throughputChart'), {
            type: 'line',
            data: {
                datasets: [{
                    label: 'Requests/sec',
                    data: [],
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    yAxisID: 'y'
                }, {
                    label: 'Error Rate (%)',
                    data: [],
                    borderColor: 'rgb(255, 205, 86)',
                    backgroundColor: 'rgba(255, 205, 86, 0.2)',
                    yAxisID: 'y1'
                }]
            },
            options: {
                ...chartOptions,
                scales: {
                    ...chartOptions.scales,
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        beginAtZero: true,
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
        
        const resourceChart = new Chart(document.getElementById('resourceChart'), {
            type: 'line',
            data: {
                datasets: [{
                    label: 'CPU Usage (%)',
                    data: [],
                    borderColor: 'rgb(153, 102, 255)',
                    backgroundColor: 'rgba(153, 102, 255, 0.2)',
                    tension: 0.1
                }, {
                    label: 'Memory Usage (%)',
                    data: [],
                    borderColor: 'rgb(255, 159, 64)',
                    backgroundColor: 'rgba(255, 159, 64, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                ...chartOptions,
                scales: {
                    ...chartOptions.scales,
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
        
        const connectionChart = new Chart(document.getElementById('connectionChart'), {
            type: 'line',
            data: {
                datasets: [{
                    label: 'Active Connections',
                    data: [],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }, {
                    label: 'WebSocket Connections',
                    data: [],
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    tension: 0.1
                }]
            },
            options: chartOptions
        });
        
        // Dashboard state
        let isTestRunning = false;
        let currentTestType = 'all';
        let refreshInterval;
        let metricsHistory = [];
        
        // Mock data for demonstration
        function generateMockData() {
            const now = new Date();
            return {
                timestamp: now,
                throughput: Math.floor(Math.random() * 100) + 50,
                responseTime: Math.floor(Math.random() * 500) + 100,
                successRate: Math.floor(Math.random() * 10) + 90,
                errorRate: Math.floor(Math.random() * 5) + 1,
                activeConnections: Math.floor(Math.random() * 50) + 20,
                cpuUsage: Math.floor(Math.random() * 30) + 40,
                memoryUsage: Math.floor(Math.random() * 20) + 50,
                websocketConnections: Math.floor(Math.random() * 20) + 10
            };
        }
        
        // Update metrics display
        function updateMetrics(data) {
            document.getElementById('throughput').textContent = data.throughput;
            document.getElementById('responseTime').textContent = data.responseTime;
            document.getElementById('successRate').textContent = data.successRate;
            document.getElementById('errorRate').textContent = data.errorRate;
            document.getElementById('activeConnections').textContent = data.activeConnections;
            document.getElementById('cpuUsage').textContent = data.cpuUsage;
            
            // Update metric styles based on values
            updateMetricStyle('throughput', data.throughput, 80, 50);
            updateMetricStyle('responseTime', data.responseTime, 200, 500, true);
            updateMetricStyle('successRate', data.successRate, 95, 90);
            updateMetricStyle('errorRate', data.errorRate, 5, 10, true);
            updateMetricStyle('activeConnections', data.activeConnections, 30, 60);
            updateMetricStyle('cpuUsage', data.cpuUsage, 70, 85, true);
        }
        
        // Update metric style based on thresholds
        function updateMetricStyle(elementId, value, goodThreshold, badThreshold, reverse = false) {
            const element = document.getElementById(elementId);
            element.classList.remove('metric-good', 'metric-warning', 'metric-critical');
            
            if (reverse) {
                if (value <= goodThreshold) {
                    element.classList.add('metric-good');
                } else if (value <= badThreshold) {
                    element.classList.add('metric-warning');
                } else {
                    element.classList.add('metric-critical');
                }
            } else {
                if (value >= goodThreshold) {
                    element.classList.add('metric-good');
                } else if (value >= badThreshold) {
                    element.classList.add('metric-warning');
                } else {
                    element.classList.add('metric-critical');
                }
            }
        }
        
        // Update charts
        function updateCharts(data) {
            // Response time chart
            responseTimeChart.data.datasets[0].data.push({
                x: data.timestamp,
                y: data.responseTime
            });
            responseTimeChart.data.datasets[1].data.push({
                x: data.timestamp,
                y: data.responseTime * 1.2 // Mock P95
            });
            
            // Throughput chart
            throughputChart.data.datasets[0].data.push({
                x: data.timestamp,
                y: data.throughput
            });
            throughputChart.data.datasets[1].data.push({
                x: data.timestamp,
                y: data.errorRate
            });
            
            // Resource chart
            resourceChart.data.datasets[0].data.push({
                x: data.timestamp,
                y: data.cpuUsage
            });
            resourceChart.data.datasets[1].data.push({
                x: data.timestamp,
                y: data.memoryUsage
            });
            
            // Connection chart
            connectionChart.data.datasets[0].data.push({
                x: data.timestamp,
                y: data.activeConnections
            });
            connectionChart.data.datasets[1].data.push({
                x: data.timestamp,
                y: data.websocketConnections
            });
            
            // Limit data points to last 100
            [responseTimeChart, throughputChart, resourceChart, connectionChart].forEach(chart => {
                chart.data.datasets.forEach(dataset => {
                    if (dataset.data.length > 100) {
                        dataset.data.shift();
                    }
                });
                chart.update('none');
            });
        }
        
        // Update alerts
        function updateAlerts(data) {
            const alertsList = document.getElementById('alertsList');
            const alerts = [];
            
            // Check for alerts based on thresholds
            if (data.responseTime > 500) {
                alerts.push({
                    level: 'critical',
                    message: `High response time: ${data.responseTime}ms`,
                    timestamp: data.timestamp
                });
            }
            
            if (data.errorRate > 5) {
                alerts.push({
                    level: 'warning',
                    message: `High error rate: ${data.errorRate}%`,
                    timestamp: data.timestamp
                });
            }
            
            if (data.cpuUsage > 80) {
                alerts.push({
                    level: 'warning',
                    message: `High CPU usage: ${data.cpuUsage}%`,
                    timestamp: data.timestamp
                });
            }
            
            if (alerts.length === 0) {
                alertsList.innerHTML = '<div class="alert alert-info"><span class="alert-icon">ℹ️</span>No active alerts</div>';
            } else {
                alertsList.innerHTML = alerts.map(alert => `
                    <div class="alert alert-${alert.level}">
                        <span class="alert-icon">${alert.level === 'critical' ? '🚨' : '⚠️'}</span>
                        ${alert.message}
                        <span style="margin-left: auto; font-size: 12px; opacity: 0.7;">
                            ${alert.timestamp.toLocaleTimeString()}
                        </span>
                    </div>
                `).join('');
            }
        }
        
        // Update test results
        function updateTestResults() {
            const testResultsList = document.getElementById('testResultsList');
            
            // Mock test results
            const testResults = [
                { name: 'API Load Test (100 RPS)', status: 'passed', timestamp: new Date() },
                { name: 'WebSocket Load Test (50 connections)', status: 'passed', timestamp: new Date() },
                { name: 'Mixed Workload Test (200 RPS)', status: 'running', timestamp: new Date() },
                { name: 'Stress Test (1000 users)', status: 'failed', timestamp: new Date() },
                { name: 'Spike Test', status: 'passed', timestamp: new Date() }
            ];
            
            testResultsList.innerHTML = testResults.map(result => `
                <div class="test-result">
                    <div class="test-name">${result.name}</div>
                    <div class="test-status status-${result.status}">${result.status}</div>
                </div>
            `).join('');
        }
        
        // Start test
        function startTest() {
            const testType = document.getElementById('testType').value;
            const targetLoad = document.getElementById('targetLoad').value;
            const duration = document.getElementById('duration').value;
            
            console.log(`Starting test: ${testType} with load ${targetLoad} for ${duration} minutes`);
            
            isTestRunning = true;
            currentTestType = testType;
            
            // Update UI
            document.querySelector('button[onclick="startTest()"]').disabled = true;
            document.querySelector('button[onclick="stopTest()"]').disabled = false;
            
            // Start monitoring
            startMonitoring();
        }
        
        // Stop test
        function stopTest() {
            console.log('Stopping test');
            
            isTestRunning = false;
            
            // Update UI
            document.querySelector('button[onclick="startTest()"]').disabled = false;
            document.querySelector('button[onclick="stopTest()"]').disabled = true;
            
            // Stop monitoring
            stopMonitoring();
        }
        
        // Start monitoring
        function startMonitoring() {
            refreshInterval = setInterval(() => {
                const data = generateMockData();
                metricsHistory.push(data);
                
                updateMetrics(data);
                updateCharts(data);
                updateAlerts(data);
                updateTestResults();
                
                document.getElementById('lastUpdated').textContent = new Date().toLocaleTimeString();
            }, 2000);
        }
        
        // Stop monitoring
        function stopMonitoring() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        }
        
        // Refresh dashboard
        function refreshDashboard() {
            console.log('Refreshing dashboard');
            
            // Clear charts
            [responseTimeChart, throughputChart, resourceChart, connectionChart].forEach(chart => {
                chart.data.datasets.forEach(dataset => {
                    dataset.data = [];
                });
                chart.update();
            });
            
            // Clear metrics history
            metricsHistory = [];
            
            // Update alerts and test results
            updateAlerts(generateMockData());
            updateTestResults();
        }
        
        // Initialize dashboard
        function initializeDashboard() {
            console.log('Initializing dashboard');
            
            // Set initial state
            document.querySelector('button[onclick="stopTest()"]').disabled = true;
            
            // Load initial data
            updateTestResults();
            
            // Start monitoring if not already running
            if (!isTestRunning) {
                startMonitoring();
            }
        }
        
        // Initialize when page loads
        window.addEventListener('load', initializeDashboard);
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            stopMonitoring();
        });
    </script>
</body>
</html>