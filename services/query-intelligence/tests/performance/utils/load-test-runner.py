#!/usr/bin/env python3
"""
Load Test Runner
 
Automated load test execution framework with graduated load testing,
performance monitoring, and result analysis.
"""

import asyncio
import json
import logging
import os
import subprocess
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import aiohttp
import click
import yaml

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LoadTestRunner:
    """Automated load test execution framework"""
    
    def __init__(self, config_path: str = "load-test-config.yaml"):
        self.config_path = config_path
        self.config = self._load_config()
        self.results = []
        self.test_start_time = None
        self.test_end_time = None
        
    def _load_config(self) -> Dict:
        """Load configuration from YAML file"""
        try:
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.warning(f"Config file {self.config_path} not found, using defaults")
            return self._default_config()
    
    def _default_config(self) -> Dict:
        """Default configuration"""
        return {
            'service': {
                'base_url': 'http://localhost:8000',
                'ws_url': 'ws://localhost:8000',
                'health_endpoint': '/health',
                'auth_token': 'test-token'
            },
            'tests': {
                'k6_path': 'k6',
                'artillery_path': 'artillery',
                'output_dir': './results',
                'timeout': 3600  # 1 hour
            },
            'load_scenarios': [
                {
                    'name': 'api_load_test',
                    'type': 'k6',
                    'script': 'k6/api-load-test.js',
                    'target_rps': [100, 500, 1000],
                    'duration': '5m',
                    'ramp_up': '2m'
                },
                {
                    'name': 'websocket_load_test',
                    'type': 'k6',
                    'script': 'k6/websocket-load-test.js',
                    'concurrent_connections': [50, 100, 200],
                    'duration': '5m'
                },
                {
                    'name': 'mixed_workload_test',
                    'type': 'k6',
                    'script': 'k6/mixed-workload-test.js',
                    'target_rps': [200, 500, 1000],
                    'duration': '10m'
                },
                {
                    'name': 'stress_test',
                    'type': 'k6',
                    'script': 'k6/stress-test.js',
                    'max_users': [1000, 2000, 3000],
                    'duration': '10m'
                },
                {
                    'name': 'realtime_load_test',
                    'type': 'artillery',
                    'script': 'artillery/realtime-load.yml',
                    'phases': 'default'
                },
                {
                    'name': 'spike_test',
                    'type': 'artillery',
                    'script': 'artillery/spike-test.yml',
                    'phases': 'default'
                }
            ],
            'monitoring': {
                'collect_metrics': True,
                'metrics_interval': 5,
                'alert_thresholds': {
                    'response_time': 2000,
                    'error_rate': 0.05,
                    'throughput': 10
                }
            }
        }
    
    async def run_graduated_load_test(self, scenario: str = "all") -> Dict:
        """Run graduated load testing with increasing loads"""
        logger.info(f"Starting graduated load test for scenario: {scenario}")
        
        self.test_start_time = datetime.now()
        
        # Check service health before testing
        if not await self._check_service_health():
            raise Exception("Service is not healthy - aborting load test")
        
        # Run load test scenarios
        if scenario == "all":
            scenarios = self.config['load_scenarios']
        else:
            scenarios = [s for s in self.config['load_scenarios'] if s['name'] == scenario]
        
        for scenario_config in scenarios:
            logger.info(f"Running scenario: {scenario_config['name']}")
            
            try:
                result = await self._run_scenario(scenario_config)
                self.results.append(result)
                
                # Wait between scenarios
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error(f"Scenario {scenario_config['name']} failed: {e}")
                self.results.append({
                    'scenario': scenario_config['name'],
                    'status': 'failed',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                })
        
        self.test_end_time = datetime.now()
        
        # Generate comprehensive report
        report = await self._generate_final_report()
        
        return report
    
    async def _run_scenario(self, scenario_config: Dict) -> Dict:
        """Run a single load test scenario"""
        scenario_name = scenario_config['name']
        test_type = scenario_config['type']
        
        logger.info(f"Executing {test_type} scenario: {scenario_name}")
        
        if test_type == 'k6':
            return await self._run_k6_scenario(scenario_config)
        elif test_type == 'artillery':
            return await self._run_artillery_scenario(scenario_config)
        else:
            raise ValueError(f"Unknown test type: {test_type}")
    
    async def _run_k6_scenario(self, scenario_config: Dict) -> Dict:
        """Run K6 load test scenario"""
        script_path = scenario_config['script']
        scenario_name = scenario_config['name']
        
        # Determine load levels based on scenario type
        if 'target_rps' in scenario_config:
            load_levels = scenario_config['target_rps']
            load_param = 'TARGET_RPS'
        elif 'concurrent_connections' in scenario_config:
            load_levels = scenario_config['concurrent_connections']
            load_param = 'MAX_CONCURRENT_CONNECTIONS'
        elif 'max_users' in scenario_config:
            load_levels = scenario_config['max_users']
            load_param = 'MAX_STRESS_USERS'
        else:
            load_levels = [100]  # Default
            load_param = 'TARGET_RPS'
        
        scenario_results = []
        
        for load_level in load_levels:
            logger.info(f"Running {scenario_name} with {load_param}={load_level}")
            
            # Prepare K6 command
            cmd = [
                self.config['tests']['k6_path'],
                'run',
                script_path,
                '--env', f'BASE_URL={self.config["service"]["base_url"]}',
                '--env', f'AUTH_TOKEN={self.config["service"]["auth_token"]}',
                '--env', f'{load_param}={load_level}',
                '--env', f'TEST_DURATION={scenario_config.get("duration", "5m")}',
                '--out', f'json=./results/{scenario_name}-{load_level}-results.json',
                '--summary-export', f'./results/{scenario_name}-{load_level}-summary.json'
            ]
            
            # Execute K6 test
            start_time = time.time()
            
            try:
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=Path(__file__).parent
                )
                
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=self.config['tests']['timeout']
                )
                
                end_time = time.time()
                
                # Parse results
                result = {
                    'scenario': scenario_name,
                    'load_level': load_level,
                    'load_param': load_param,
                    'duration': end_time - start_time,
                    'exit_code': process.returncode,
                    'stdout': stdout.decode(),
                    'stderr': stderr.decode(),
                    'timestamp': datetime.now().isoformat()
                }
                
                # Load detailed results if available
                try:
                    results_file = f'./results/{scenario_name}-{load_level}-results.json'
                    if os.path.exists(results_file):
                        with open(results_file, 'r') as f:
                            detailed_results = json.load(f)
                            result['detailed_results'] = detailed_results
                except Exception as e:
                    logger.warning(f"Could not load detailed results: {e}")
                
                scenario_results.append(result)
                
                # Log key metrics
                if process.returncode == 0:
                    logger.info(f"✓ {scenario_name} with {load_param}={load_level} completed successfully")
                    await self._log_k6_metrics(result)
                else:
                    logger.error(f"✗ {scenario_name} with {load_param}={load_level} failed")
                
                # Brief pause between load levels
                await asyncio.sleep(10)
                
            except asyncio.TimeoutError:
                logger.error(f"Timeout running {scenario_name} with {load_param}={load_level}")
                scenario_results.append({
                    'scenario': scenario_name,
                    'load_level': load_level,
                    'load_param': load_param,
                    'status': 'timeout',
                    'timestamp': datetime.now().isoformat()
                })
            
            except Exception as e:
                logger.error(f"Error running {scenario_name} with {load_param}={load_level}: {e}")
                scenario_results.append({
                    'scenario': scenario_name,
                    'load_level': load_level,
                    'load_param': load_param,
                    'status': 'error',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                })
        
        return {
            'scenario': scenario_name,
            'type': 'k6',
            'results': scenario_results,
            'summary': self._analyze_scenario_results(scenario_results)
        }
    
    async def _run_artillery_scenario(self, scenario_config: Dict) -> Dict:
        """Run Artillery load test scenario"""
        script_path = scenario_config['script']
        scenario_name = scenario_config['name']
        
        logger.info(f"Running Artillery scenario: {scenario_name}")
        
        # Prepare Artillery command
        cmd = [
            self.config['tests']['artillery_path'],
            'run',
            script_path,
            '--output', f'./results/{scenario_name}-artillery-results.json'
        ]
        
        # Add environment variables
        env = os.environ.copy()
        env.update({
            'BASE_URL': self.config['service']['base_url'],
            'AUTH_TOKEN': self.config['service']['auth_token']
        })
        
        start_time = time.time()
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env=env,
                cwd=Path(__file__).parent
            )
            
            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=self.config['tests']['timeout']
            )
            
            end_time = time.time()
            
            result = {
                'scenario': scenario_name,
                'type': 'artillery',
                'duration': end_time - start_time,
                'exit_code': process.returncode,
                'stdout': stdout.decode(),
                'stderr': stderr.decode(),
                'timestamp': datetime.now().isoformat()
            }
            
            # Load detailed results if available
            try:
                results_file = f'./results/{scenario_name}-artillery-results.json'
                if os.path.exists(results_file):
                    with open(results_file, 'r') as f:
                        detailed_results = json.load(f)
                        result['detailed_results'] = detailed_results
            except Exception as e:
                logger.warning(f"Could not load Artillery detailed results: {e}")
            
            # Log results
            if process.returncode == 0:
                logger.info(f"✓ Artillery scenario {scenario_name} completed successfully")
                await self._log_artillery_metrics(result)
            else:
                logger.error(f"✗ Artillery scenario {scenario_name} failed")
            
            return result
            
        except asyncio.TimeoutError:
            logger.error(f"Timeout running Artillery scenario {scenario_name}")
            return {
                'scenario': scenario_name,
                'type': 'artillery',
                'status': 'timeout',
                'timestamp': datetime.now().isoformat()
            }
        
        except Exception as e:
            logger.error(f"Error running Artillery scenario {scenario_name}: {e}")
            return {
                'scenario': scenario_name,
                'type': 'artillery',
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _check_service_health(self) -> bool:
        """Check if service is healthy before testing"""
        health_url = f"{self.config['service']['base_url']}{self.config['service']['health_endpoint']}"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(health_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        health_data = await response.json()
                        logger.info(f"Service health check passed: {health_data.get('status', 'unknown')}")
                        return True
                    else:
                        logger.error(f"Service health check failed: HTTP {response.status}")
                        return False
        
        except Exception as e:
            logger.error(f"Service health check error: {e}")
            return False
    
    async def _log_k6_metrics(self, result: Dict):
        """Log key K6 metrics"""
        if 'detailed_results' in result:
            metrics = result['detailed_results'].get('metrics', {})
            
            # Log key metrics
            if 'http_req_duration' in metrics:
                duration = metrics['http_req_duration']
                logger.info(f"Response time - avg: {duration.get('avg', 0):.2f}ms, p95: {duration.get('p(95)', 0):.2f}ms")
            
            if 'http_reqs' in metrics:
                total_reqs = metrics['http_reqs'].get('count', 0)
                logger.info(f"Total requests: {total_reqs}")
            
            if 'http_req_failed' in metrics:
                failed_rate = metrics['http_req_failed'].get('rate', 0)
                logger.info(f"Error rate: {failed_rate*100:.2f}%")
    
    async def _log_artillery_metrics(self, result: Dict):
        """Log key Artillery metrics"""
        if 'detailed_results' in result:
            # Artillery results structure varies, adapt as needed
            logger.info("Artillery test completed - check detailed results for metrics")
    
    def _analyze_scenario_results(self, results: List[Dict]) -> Dict:
        """Analyze results from a scenario"""
        successful_tests = [r for r in results if r.get('exit_code') == 0]
        failed_tests = [r for r in results if r.get('exit_code') != 0]
        
        return {
            'total_tests': len(results),
            'successful_tests': len(successful_tests),
            'failed_tests': len(failed_tests),
            'success_rate': len(successful_tests) / len(results) if results else 0,
            'load_levels_tested': [r.get('load_level') for r in results],
            'performance_trend': self._analyze_performance_trend(results)
        }
    
    def _analyze_performance_trend(self, results: List[Dict]) -> Dict:
        """Analyze performance trend across load levels"""
        trend_data = []
        
        for result in results:
            if 'detailed_results' in result:
                metrics = result['detailed_results'].get('metrics', {})
                
                if 'http_req_duration' in metrics:
                    trend_data.append({
                        'load_level': result.get('load_level'),
                        'avg_response_time': metrics['http_req_duration'].get('avg', 0),
                        'p95_response_time': metrics['http_req_duration'].get('p(95)', 0),
                        'error_rate': metrics.get('http_req_failed', {}).get('rate', 0)
                    })
        
        if not trend_data:
            return {'status': 'no_data'}
        
        # Analyze trend
        response_times = [d['avg_response_time'] for d in trend_data]
        error_rates = [d['error_rate'] for d in trend_data]
        
        return {
            'status': 'analyzed',
            'response_time_trend': 'increasing' if response_times[-1] > response_times[0] else 'stable',
            'error_rate_trend': 'increasing' if error_rates[-1] > error_rates[0] else 'stable',
            'breaking_point': self._find_breaking_point(trend_data),
            'optimal_load': self._find_optimal_load(trend_data)
        }
    
    def _find_breaking_point(self, trend_data: List[Dict]) -> Optional[int]:
        """Find the load level where performance starts to degrade significantly"""
        for i, data in enumerate(trend_data):
            if data['error_rate'] > 0.1 or data['p95_response_time'] > 5000:  # 10% error rate or 5s response time
                return data['load_level']
        return None
    
    def _find_optimal_load(self, trend_data: List[Dict]) -> Optional[int]:
        """Find optimal load level with good performance"""
        for data in reversed(trend_data):
            if data['error_rate'] < 0.05 and data['p95_response_time'] < 2000:  # 5% error rate and 2s response time
                return data['load_level']
        return None
    
    async def _generate_final_report(self) -> Dict:
        """Generate comprehensive final report"""
        report = {
            'test_info': {
                'start_time': self.test_start_time.isoformat() if self.test_start_time else None,
                'end_time': self.test_end_time.isoformat() if self.test_end_time else None,
                'duration': str(self.test_end_time - self.test_start_time) if self.test_start_time and self.test_end_time else None,
                'scenarios_tested': len(self.results),
                'config': self.config
            },
            'results': self.results,
            'summary': {
                'total_scenarios': len(self.results),
                'successful_scenarios': len([r for r in self.results if r.get('type') in ['k6', 'artillery']]),
                'failed_scenarios': len([r for r in self.results if r.get('status') == 'failed']),
                'overall_success_rate': self._calculate_overall_success_rate()
            },
            'performance_insights': self._generate_performance_insights(),
            'recommendations': self._generate_recommendations()
        }
        
        # Save report
        await self._save_report(report)
        
        return report
    
    def _calculate_overall_success_rate(self) -> float:
        """Calculate overall success rate across all scenarios"""
        total_tests = 0
        successful_tests = 0
        
        for result in self.results:
            if 'summary' in result:
                total_tests += result['summary']['total_tests']
                successful_tests += result['summary']['successful_tests']
        
        return successful_tests / total_tests if total_tests > 0 else 0
    
    def _generate_performance_insights(self) -> Dict:
        """Generate performance insights from all test results"""
        insights = {
            'throughput_capacity': None,
            'response_time_performance': None,
            'error_rate_analysis': None,
            'breaking_points': [],
            'optimal_loads': []
        }
        
        for result in self.results:
            if 'summary' in result and 'performance_trend' in result['summary']:
                trend = result['summary']['performance_trend']
                
                if trend.get('breaking_point'):
                    insights['breaking_points'].append({
                        'scenario': result['scenario'],
                        'breaking_point': trend['breaking_point']
                    })
                
                if trend.get('optimal_load'):
                    insights['optimal_loads'].append({
                        'scenario': result['scenario'],
                        'optimal_load': trend['optimal_load']
                    })
        
        return insights
    
    def _generate_recommendations(self) -> List[Dict]:
        """Generate performance recommendations"""
        recommendations = []
        
        # Analyze results and generate recommendations
        overall_success_rate = self._calculate_overall_success_rate()
        
        if overall_success_rate < 0.95:
            recommendations.append({
                'type': 'reliability',
                'priority': 'high',
                'message': f'Overall success rate is {overall_success_rate*100:.1f}%. Investigate error causes and improve error handling.',
                'action': 'Review error logs and optimize error handling mechanisms'
            })
        
        # Check for breaking points
        insights = self._generate_performance_insights()
        if insights['breaking_points']:
            lowest_breaking_point = min(bp['breaking_point'] for bp in insights['breaking_points'])
            recommendations.append({
                'type': 'capacity',
                'priority': 'medium',
                'message': f'System breaking point detected at {lowest_breaking_point} load level. Consider scaling before reaching this capacity.',
                'action': 'Implement auto-scaling or increase resource capacity'
            })
        
        # Check for optimal loads
        if insights['optimal_loads']:
            highest_optimal = max(ol['optimal_load'] for ol in insights['optimal_loads'])
            recommendations.append({
                'type': 'performance',
                'priority': 'low',
                'message': f'System performs optimally up to {highest_optimal} load level. This can be used as a baseline for capacity planning.',
                'action': 'Use this baseline for production capacity planning'
            })
        
        if not recommendations:
            recommendations.append({
                'type': 'success',
                'priority': 'low',
                'message': 'All load tests passed successfully. System is performing within acceptable limits.',
                'action': 'Continue monitoring and consider periodic load testing'
            })
        
        return recommendations
    
    async def _save_report(self, report: Dict):
        """Save final report to file"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'load_test_report_{timestamp}.json'
        
        # Ensure output directory exists
        output_dir = Path(self.config['tests']['output_dir'])
        output_dir.mkdir(parents=True, exist_ok=True)
        
        filepath = output_dir / filename
        
        try:
            with open(filepath, 'w') as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"Final report saved to: {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving report: {e}")

# CLI Interface
@click.command()
@click.option('--scenario', default='all', help='Scenario to run (default: all)')
@click.option('--config', default='load-test-config.yaml', help='Configuration file path')
@click.option('--output-dir', default='./results', help='Output directory for results')
@click.option('--verbose', is_flag=True, help='Enable verbose logging')
def main(scenario: str, config: str, output_dir: str, verbose: bool):
    """Run graduated load testing for query-intelligence service"""
    
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create output directory
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # Initialize and run load test
    runner = LoadTestRunner(config)
    runner.config['tests']['output_dir'] = output_dir
    
    try:
        report = asyncio.run(runner.run_graduated_load_test(scenario))
        
        # Print summary
        print("\n" + "="*80)
        print("LOAD TEST SUMMARY")
        print("="*80)
        print(f"Total scenarios: {report['summary']['total_scenarios']}")
        print(f"Successful scenarios: {report['summary']['successful_scenarios']}")
        print(f"Failed scenarios: {report['summary']['failed_scenarios']}")
        print(f"Overall success rate: {report['summary']['overall_success_rate']*100:.1f}%")
        
        print("\nRECOMMENDATIONS:")
        for rec in report['recommendations']:
            print(f"- [{rec['priority'].upper()}] {rec['message']}")
        
        print(f"\nDetailed report saved to: {output_dir}")
        
    except Exception as e:
        logger.error(f"Load test failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()