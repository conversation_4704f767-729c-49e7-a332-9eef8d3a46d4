#!/usr/bin/env python3
"""
Real-time communication validation between query-intelligence and analysis-engine.
This script tests the actual communication patterns and validates the integration.
"""

import asyncio
import json
import sys
import time
from typing import Dict, Any, List, Optional
import traceback
import httpx
from dataclasses import dataclass
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from query_intelligence.clients.analysis_engine import AnalysisEngineClient
from query_intelligence.config.settings import get_settings

@dataclass
class TestResult:
    """Test result data structure."""
    test_name: str
    success: bool
    duration_ms: float
    error: Optional[str] = None
    details: Optional[Dict[str, Any]] = None

class AnalysisEngineCommunicationValidator:
    """Validates communication between query-intelligence and analysis-engine."""
    
    def __init__(self, analysis_engine_url: str = None):
        self.analysis_engine_url = analysis_engine_url or "https://analysis-engine-l3nxty7oka-uc.a.run.app"
        self.client = None
        self.test_results: List[TestResult] = []
        self.verbose = True
        
    async def initialize(self):
        """Initialize the client."""
        print(f"🔧 Initializing Analysis Engine Communication Validator")
        print(f"🌐 Analysis Engine URL: {self.analysis_engine_url}")
        
        self.client = AnalysisEngineClient(base_url=self.analysis_engine_url)
        
        # Verify basic connectivity
        print("🔍 Checking basic connectivity...")
        try:
            is_healthy = await self.client.health_check()
            if not is_healthy:
                print("❌ Analysis Engine is not healthy")
                return False
            print("✅ Analysis Engine is healthy")
            return True
        except Exception as e:
            print(f"❌ Failed to connect to Analysis Engine: {e}")
            return False
    
    async def cleanup(self):
        """Cleanup resources."""
        if self.client:
            await self.client.close()
    
    def log_test_result(self, result: TestResult):
        """Log and store test result."""
        self.test_results.append(result)
        
        status_icon = "✅" if result.success else "❌"
        print(f"{status_icon} {result.test_name}: {result.duration_ms:.1f}ms")
        
        if not result.success and result.error:
            print(f"   Error: {result.error}")
        
        if self.verbose and result.details:
            print(f"   Details: {json.dumps(result.details, indent=2)}")
    
    async def test_health_check_performance(self) -> TestResult:
        """Test health check performance and reliability."""
        test_name = "Health Check Performance"
        start_time = time.time()
        
        try:
            # Test multiple health checks
            tasks = [self.client.health_check() for _ in range(10)]
            results = await asyncio.gather(*tasks)
            
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            success_count = sum(1 for r in results if r is True)
            success_rate = success_count / len(results)
            
            success = success_rate >= 0.9  # 90% success rate
            
            return TestResult(
                test_name=test_name,
                success=success,
                duration_ms=duration_ms,
                details={
                    "total_requests": len(results),
                    "successful_requests": success_count,
                    "success_rate": success_rate,
                    "avg_response_time_ms": duration_ms / len(results)
                }
            )
            
        except Exception as e:
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            return TestResult(
                test_name=test_name,
                success=False,
                duration_ms=duration_ms,
                error=str(e)
            )
    
    async def test_health_endpoint_details(self) -> TestResult:
        """Test health endpoint with detailed response validation."""
        test_name = "Health Endpoint Details"
        start_time = time.time()
        
        try:
            # Make direct HTTP request to get detailed response
            async with httpx.AsyncClient() as http_client:
                response = await http_client.get(
                    f"{self.analysis_engine_url}/health",
                    timeout=30.0
                )
                
                end_time = time.time()
                duration_ms = (end_time - start_time) * 1000
                
                success = response.status_code == 200
                
                details = {
                    "status_code": response.status_code,
                    "headers": dict(response.headers),
                    "response_time_ms": duration_ms
                }
                
                try:
                    response_json = response.json()
                    details["response_body"] = response_json
                except:
                    details["response_text"] = response.text
                
                return TestResult(
                    test_name=test_name,
                    success=success,
                    duration_ms=duration_ms,
                    details=details
                )
                
        except Exception as e:
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            return TestResult(
                test_name=test_name,
                success=False,
                duration_ms=duration_ms,
                error=str(e)
            )
    
    async def test_available_endpoints(self) -> TestResult:
        """Test all available endpoints for basic connectivity."""
        test_name = "Available Endpoints"
        start_time = time.time()
        
        endpoints_to_test = [
            ("/health", "GET"),
            ("/health/live", "GET"),
            ("/health/ready", "GET"),
            ("/api/v1/version", "GET"),
            ("/api/v1/languages", "GET"),
            ("/metrics", "GET"),
        ]
        
        try:
            async with httpx.AsyncClient() as http_client:
                endpoint_results = {}
                
                for endpoint, method in endpoints_to_test:
                    try:
                        if method == "GET":
                            response = await http_client.get(
                                f"{self.analysis_engine_url}{endpoint}",
                                timeout=10.0
                            )
                        else:
                            continue  # Skip non-GET for now
                        
                        endpoint_results[endpoint] = {
                            "status_code": response.status_code,
                            "success": response.status_code < 400,
                            "response_time_ms": response.elapsed.total_seconds() * 1000 if response.elapsed else 0
                        }
                        
                        try:
                            response_json = response.json()
                            endpoint_results[endpoint]["response_size"] = len(json.dumps(response_json))
                        except:
                            endpoint_results[endpoint]["response_size"] = len(response.text)
                            
                    except Exception as e:
                        endpoint_results[endpoint] = {
                            "status_code": 0,
                            "success": False,
                            "error": str(e)
                        }
                
                end_time = time.time()
                duration_ms = (end_time - start_time) * 1000
                
                successful_endpoints = sum(1 for r in endpoint_results.values() if r.get("success", False))
                success_rate = successful_endpoints / len(endpoints_to_test)
                
                return TestResult(
                    test_name=test_name,
                    success=success_rate >= 0.7,  # 70% of endpoints should work
                    duration_ms=duration_ms,
                    details={
                        "endpoint_results": endpoint_results,
                        "successful_endpoints": successful_endpoints,
                        "total_endpoints": len(endpoints_to_test),
                        "success_rate": success_rate
                    }
                )
                
        except Exception as e:
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            return TestResult(
                test_name=test_name,
                success=False,
                duration_ms=duration_ms,
                error=str(e)
            )
    
    async def test_circuit_breaker_behavior(self) -> TestResult:
        """Test circuit breaker behavior under failure conditions."""
        test_name = "Circuit Breaker Behavior"
        start_time = time.time()
        
        try:
            # Create a client that will fail
            failing_client = AnalysisEngineClient(base_url="http://non-existent-service:8000")
            
            # Trigger circuit breaker with failures
            failure_count = 0
            for i in range(5):
                try:
                    await failing_client.get_analysis("test-repo")
                except:
                    failure_count += 1
            
            await failing_client.close()
            
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            # Circuit breaker should be triggered after 3 failures
            success = failure_count >= 3
            
            return TestResult(
                test_name=test_name,
                success=success,
                duration_ms=duration_ms,
                details={
                    "failure_count": failure_count,
                    "circuit_breaker_triggered": success,
                    "expected_failures": 3
                }
            )
            
        except Exception as e:
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            return TestResult(
                test_name=test_name,
                success=False,
                duration_ms=duration_ms,
                error=str(e)
            )
    
    async def test_concurrent_requests(self) -> TestResult:
        """Test concurrent request handling."""
        test_name = "Concurrent Requests"
        start_time = time.time()
        
        try:
            # Test concurrent health checks
            concurrent_requests = 25
            tasks = [self.client.health_check() for _ in range(concurrent_requests)]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            successful_requests = sum(1 for r in results if r is True)
            error_count = sum(1 for r in results if isinstance(r, Exception))
            
            success_rate = successful_requests / len(results)
            success = success_rate >= 0.8  # 80% success rate
            
            return TestResult(
                test_name=test_name,
                success=success,
                duration_ms=duration_ms,
                details={
                    "concurrent_requests": concurrent_requests,
                    "successful_requests": successful_requests,
                    "error_count": error_count,
                    "success_rate": success_rate,
                    "avg_response_time_ms": duration_ms / concurrent_requests
                }
            )
            
        except Exception as e:
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            return TestResult(
                test_name=test_name,
                success=False,
                duration_ms=duration_ms,
                error=str(e)
            )
    
    async def test_request_timeout_handling(self) -> TestResult:
        """Test request timeout handling."""
        test_name = "Request Timeout Handling"
        start_time = time.time()
        
        try:
            # Create client with very short timeout
            timeout_client = AnalysisEngineClient(base_url=self.analysis_engine_url)
            timeout_client.client.timeout = httpx.Timeout(0.001)  # 1ms timeout
            
            # This should timeout
            try:
                await timeout_client.health_check()
                success = False  # Should have timed out
                error = "Request did not timeout as expected"
            except (httpx.TimeoutException, asyncio.TimeoutError):
                success = True
                error = None
            except Exception as e:
                success = False
                error = f"Unexpected error: {e}"
            
            await timeout_client.close()
            
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            return TestResult(
                test_name=test_name,
                success=success,
                duration_ms=duration_ms,
                error=error,
                details={
                    "timeout_ms": 1,
                    "timeout_handled_correctly": success
                }
            )
            
        except Exception as e:
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            return TestResult(
                test_name=test_name,
                success=False,
                duration_ms=duration_ms,
                error=str(e)
            )
    
    async def test_error_response_handling(self) -> TestResult:
        """Test error response handling."""
        test_name = "Error Response Handling"
        start_time = time.time()
        
        try:
            # Test 404 error handling
            try:
                await self.client.get_analysis("non-existent-repo-12345")
                success = False  # Should have raised an error
                error = "Expected 404 error was not raised"
            except httpx.HTTPStatusError as e:
                success = e.response.status_code == 404
                error = None if success else f"Unexpected status code: {e.response.status_code}"
            except Exception as e:
                success = False
                error = f"Unexpected error type: {type(e).__name__}: {e}"
            
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            return TestResult(
                test_name=test_name,
                success=success,
                duration_ms=duration_ms,
                error=error,
                details={
                    "expected_status_code": 404,
                    "error_handled_correctly": success
                }
            )
            
        except Exception as e:
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            return TestResult(
                test_name=test_name,
                success=False,
                duration_ms=duration_ms,
                error=str(e)
            )
    
    async def test_response_data_validation(self) -> TestResult:
        """Test response data structure validation."""
        test_name = "Response Data Validation"
        start_time = time.time()
        
        try:
            # Test version endpoint response structure
            async with httpx.AsyncClient() as http_client:
                try:
                    response = await http_client.get(
                        f"{self.analysis_engine_url}/api/v1/version",
                        timeout=10.0
                    )
                    
                    if response.status_code == 200:
                        version_data = response.json()
                        
                        # Validate response structure
                        required_fields = ["version", "service", "timestamp"]
                        missing_fields = [field for field in required_fields if field not in version_data]
                        
                        success = len(missing_fields) == 0
                        error = f"Missing fields: {missing_fields}" if missing_fields else None
                        
                        details = {
                            "response_data": version_data,
                            "required_fields": required_fields,
                            "missing_fields": missing_fields,
                            "has_all_fields": success
                        }
                    else:
                        success = False
                        error = f"Unexpected status code: {response.status_code}"
                        details = {"status_code": response.status_code}
                        
                except httpx.HTTPStatusError as e:
                    if e.response.status_code == 404:
                        # Version endpoint might not exist, that's OK
                        success = True
                        error = None
                        details = {"endpoint_not_found": True, "note": "Version endpoint not implemented"}
                    else:
                        success = False
                        error = f"HTTP error: {e.response.status_code}"
                        details = {"status_code": e.response.status_code}
                
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            return TestResult(
                test_name=test_name,
                success=success,
                duration_ms=duration_ms,
                error=error,
                details=details
            )
            
        except Exception as e:
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            return TestResult(
                test_name=test_name,
                success=False,
                duration_ms=duration_ms,
                error=str(e)
            )
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all communication validation tests."""
        print("\n🧪 Running Analysis Engine Communication Validation Tests")
        print("=" * 60)
        
        # Initialize
        if not await self.initialize():
            return {"overall_success": False, "error": "Failed to initialize", "total_tests": 0, "passed_tests": 0, "failed_tests": 0, "success_rate": 0, "average_duration_ms": 0, "test_results": []}
        
        # Test cases
        test_cases = [
            self.test_health_check_performance,
            self.test_health_endpoint_details,
            self.test_available_endpoints,
            self.test_circuit_breaker_behavior,
            self.test_concurrent_requests,
            self.test_request_timeout_handling,
            self.test_error_response_handling,
            self.test_response_data_validation,
        ]
        
        # Run tests
        for test_case in test_cases:
            try:
                result = await test_case()
                self.log_test_result(result)
            except Exception as e:
                error_result = TestResult(
                    test_name=test_case.__name__,
                    success=False,
                    duration_ms=0,
                    error=f"Test execution failed: {e}"
                )
                self.log_test_result(error_result)
        
        # Cleanup
        await self.cleanup()
        
        # Generate summary
        return self.generate_summary()
    
    def generate_summary(self) -> Dict[str, Any]:
        """Generate test summary."""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r.success)
        failed_tests = total_tests - passed_tests
        
        success_rate = passed_tests / total_tests if total_tests > 0 else 0
        avg_duration = sum(r.duration_ms for r in self.test_results) / total_tests if total_tests > 0 else 0
        
        overall_success = success_rate >= 0.7  # 70% success rate
        
        summary = {
            "overall_success": overall_success,
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "success_rate": success_rate,
            "average_duration_ms": avg_duration,
            "test_results": [
                {
                    "test_name": r.test_name,
                    "success": r.success,
                    "duration_ms": r.duration_ms,
                    "error": r.error,
                    "details": r.details
                }
                for r in self.test_results
            ]
        }
        
        return summary
    
    def print_summary(self, summary: Dict[str, Any]):
        """Print test summary."""
        print("\n" + "=" * 60)
        print("📊 ANALYSIS ENGINE COMMUNICATION VALIDATION SUMMARY")
        print("=" * 60)
        
        status_icon = "✅" if summary["overall_success"] else "❌"
        print(f"{status_icon} Overall Status: {'PASSED' if summary['overall_success'] else 'FAILED'}")
        print(f"📈 Success Rate: {summary['success_rate']:.1%}")
        print(f"⏱️  Average Duration: {summary['average_duration_ms']:.1f}ms")
        print(f"🧪 Tests: {summary['passed_tests']}/{summary['total_tests']} passed")
        
        if summary["failed_tests"] > 0:
            print(f"\n❌ Failed Tests:")
            for result in summary["test_results"]:
                if not result["success"]:
                    print(f"  - {result['test_name']}: {result['error']}")
        
        print("\n💡 Recommendations:")
        if summary["overall_success"]:
            print("  ✅ Analysis Engine communication is working correctly")
            print("  ✅ Ready for production integration")
            print("  ✅ Consider implementing monitoring and alerting")
        else:
            print("  ⚠️  Some tests failed - review failed tests")
            print("  ⚠️  Check network connectivity and service status")
            print("  ⚠️  Verify service configuration and deployment")
        
        print("=" * 60)

async def main():
    """Main function to run communication validation."""
    print("🚀 Analysis Engine Communication Validation")
    
    # Use the deployed analysis engine URL
    analysis_engine_url = "https://analysis-engine-l3nxty7oka-uc.a.run.app"
    
    # Run validation
    validator = AnalysisEngineCommunicationValidator(analysis_engine_url)
    
    try:
        summary = await validator.run_all_tests()
        validator.print_summary(summary)
        
        # Return appropriate exit code
        return 0 if summary["overall_success"] else 1
        
    except KeyboardInterrupt:
        print("\n⏹️  Validation cancelled by user")
        return 130
    except Exception as e:
        print(f"\n❌ Validation failed with error: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))