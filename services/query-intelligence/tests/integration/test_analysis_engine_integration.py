"""
Integration tests for Analysis Engine client and service communication.
Tests the integration between query-intelligence and the deployed analysis-engine service.
"""

import pytest
import pytest_asyncio
import httpx
import json
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, List

from query_intelligence.clients.analysis_engine import AnalysisEngineClient, get_analysis_engine_client
from query_intelligence.config.settings import get_settings

# Test configuration
TEST_ANALYSIS_ENGINE_URL = "https://analysis-engine-l3nxty7oka-uc.a.run.app"
TEST_REPO_ID = "test-repo-123"
TEST_EMBEDDING_VECTOR = [0.1, 0.2, 0.3, 0.4, 0.5] * 768  # 3840 dimensions for typical embedding

class TestAnalysisEngineClientIntegration:
    """Integration tests for the AnalysisEngineClient with the deployed service."""

    @pytest_asyncio.fixture
    async def client(self):
        """Create test client instance."""
        client = AnalysisEngineClient(base_url=TEST_ANALYSIS_ENGINE_URL)
        yield client
        await client.close()

    @pytest_asyncio.fixture
    async def mock_client(self):
        """Create mock client for testing without hitting real service."""
        with patch('httpx.AsyncClient') as mock_httpx:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"status": "healthy"}
            mock_response.raise_for_status.return_value = None
            
            mock_httpx.return_value.__aenter__.return_value.get.return_value = mock_response
            mock_httpx.return_value.__aenter__.return_value.post.return_value = mock_response
            
            client = AnalysisEngineClient(base_url=TEST_ANALYSIS_ENGINE_URL)
            yield client
            await client.close()

    @pytest.mark.asyncio
    async def test_health_check_real_service(self, client):
        """Test health check against the real deployed service."""
        is_healthy = await client.health_check()
        assert is_healthy is True, "Analysis Engine service should be healthy"

    @pytest.mark.asyncio
    async def test_health_check_connection_error(self):
        """Test health check with connection error."""
        client = AnalysisEngineClient(base_url="http://non-existent-service:8000")
        is_healthy = await client.health_check()
        assert is_healthy is False
        await client.close()

    @pytest.mark.asyncio
    async def test_health_check_timeout(self):
        """Test health check with timeout."""
        client = AnalysisEngineClient(base_url="http://httpbin.org/delay/10")
        is_healthy = await client.health_check()
        assert is_healthy is False
        await client.close()

    @pytest.mark.asyncio
    async def test_get_analysis_success(self, mock_client):
        """Test successful analysis retrieval."""
        expected_response = {
            "repository_id": TEST_REPO_ID,
            "analysis_results": {
                "code_quality": 0.85,
                "security_score": 0.92,
                "complexity_metrics": {
                    "cyclomatic_complexity": 12,
                    "maintainability_index": 78
                },
                "languages": ["python", "javascript", "rust"],
                "total_lines": 12500,
                "test_coverage": 0.87
            },
            "metadata": {
                "analyzed_at": "2025-07-14T10:30:00Z",
                "version": "1.0.0",
                "engine_version": "2.0.1"
            }
        }
        
        with patch.object(mock_client.client, 'get') as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = expected_response
            mock_response.raise_for_status.return_value = None
            mock_get.return_value = mock_response
            
            result = await mock_client.get_analysis(TEST_REPO_ID)
            
            assert result == expected_response
            mock_get.assert_called_once_with(f"/analysis/{TEST_REPO_ID}")

    @pytest.mark.asyncio
    async def test_get_analysis_not_found(self, mock_client):
        """Test analysis retrieval for non-existent repository."""
        with patch.object(mock_client.client, 'get') as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 404
            mock_response.raise_for_status.side_effect = httpx.HTTPStatusError(
                "Not Found", request=MagicMock(), response=mock_response
            )
            mock_get.return_value = mock_response
            
            with pytest.raises(httpx.HTTPStatusError):
                await mock_client.get_analysis("non-existent-repo")

    @pytest.mark.asyncio
    async def test_get_analysis_circuit_breaker_activation(self, mock_client):
        """Test circuit breaker activation on repeated failures."""
        with patch.object(mock_client.client, 'get') as mock_get:
            mock_get.side_effect = httpx.ConnectError("Connection refused")
            
            # Test multiple failures to trigger circuit breaker
            for i in range(4):  # Threshold is 3, so 4th should trigger circuit breaker
                with pytest.raises(httpx.ConnectError):
                    await mock_client.get_analysis(TEST_REPO_ID)
            
            assert mock_get.call_count == 4

    @pytest.mark.asyncio
    async def test_search_embeddings_success(self, mock_client):
        """Test successful embeddings search."""
        expected_response = {
            "repository_id": TEST_REPO_ID,
            "results": [
                {
                    "chunk_id": "chunk_001",
                    "file_path": "src/main.py",
                    "line_start": 45,
                    "line_end": 67,
                    "similarity_score": 0.94,
                    "content": "def process_query(query: str) -> Dict[str, Any]:\n    # Query processing logic\n    return result",
                    "language": "python",
                    "metadata": {
                        "function_name": "process_query",
                        "complexity": 3,
                        "test_coverage": 0.85
                    }
                },
                {
                    "chunk_id": "chunk_002",
                    "file_path": "src/utils.py",
                    "line_start": 12,
                    "line_end": 28,
                    "similarity_score": 0.87,
                    "content": "class QueryProcessor:\n    def __init__(self, config):\n        self.config = config",
                    "language": "python",
                    "metadata": {
                        "class_name": "QueryProcessor",
                        "complexity": 2,
                        "test_coverage": 0.92
                    }
                }
            ],
            "total_results": 2,
            "search_metadata": {
                "query_time_ms": 45,
                "embedding_model": "text-embedding-ada-002",
                "search_algorithm": "cosine_similarity"
            }
        }
        
        with patch.object(mock_client.client, 'post') as mock_post:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = expected_response
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response
            
            result = await mock_client.search_embeddings(
                repository_id=TEST_REPO_ID,
                query_embedding=TEST_EMBEDDING_VECTOR,
                limit=20,
                filters={"language": "python", "min_similarity": 0.7}
            )
            
            assert result == expected_response
            
            # Verify the request payload
            call_args = mock_post.call_args
            assert call_args[0][0] == "/embeddings/search"
            payload = call_args[1]['json']
            assert payload['repository_id'] == TEST_REPO_ID
            assert payload['query_embedding'] == TEST_EMBEDDING_VECTOR
            assert payload['limit'] == 20
            assert payload['filters'] == {"language": "python", "min_similarity": 0.7}

    @pytest.mark.asyncio
    async def test_search_embeddings_empty_results(self, mock_client):
        """Test embeddings search with no results."""
        expected_response = {
            "repository_id": TEST_REPO_ID,
            "results": [],
            "total_results": 0,
            "search_metadata": {
                "query_time_ms": 12,
                "embedding_model": "text-embedding-ada-002",
                "search_algorithm": "cosine_similarity"
            }
        }
        
        with patch.object(mock_client.client, 'post') as mock_post:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = expected_response
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response
            
            result = await mock_client.search_embeddings(
                repository_id=TEST_REPO_ID,
                query_embedding=TEST_EMBEDDING_VECTOR,
                limit=20
            )
            
            assert result == expected_response
            assert len(result['results']) == 0

    @pytest.mark.asyncio
    async def test_search_embeddings_with_filters(self, mock_client):
        """Test embeddings search with various filters."""
        filters = {
            "language": "python",
            "min_similarity": 0.8,
            "file_patterns": ["*.py", "*.pyx"],
            "exclude_test_files": True,
            "complexity_range": [1, 5]
        }
        
        with patch.object(mock_client.client, 'post') as mock_post:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"results": [], "total_results": 0}
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response
            
            await mock_client.search_embeddings(
                repository_id=TEST_REPO_ID,
                query_embedding=TEST_EMBEDDING_VECTOR,
                limit=50,
                filters=filters
            )
            
            # Verify filters were passed correctly
            call_args = mock_post.call_args
            payload = call_args[1]['json']
            assert payload['filters'] == filters
            assert payload['limit'] == 50

    @pytest.mark.asyncio
    async def test_search_embeddings_error_handling(self, mock_client):
        """Test comprehensive error handling for embeddings search."""
        test_cases = [
            (httpx.ConnectError("Connection refused"), "Connection refused"),
            (httpx.TimeoutException("Request timeout"), "Request timeout"),
            (httpx.HTTPStatusError("Bad Request", request=MagicMock(), response=MagicMock(status_code=400)), "HTTP 400"),
        ]
        
        for exception, expected_error in test_cases:
            with patch.object(mock_client.client, 'post') as mock_post:
                mock_post.side_effect = exception
                
                with pytest.raises(type(exception)):
                    await mock_client.search_embeddings(
                        repository_id=TEST_REPO_ID,
                        query_embedding=TEST_EMBEDDING_VECTOR
                    )

    @pytest.mark.asyncio
    async def test_get_repository_metadata_success(self, mock_client):
        """Test successful repository metadata retrieval."""
        expected_response = {
            "repository_id": TEST_REPO_ID,
            "metadata": {
                "name": "episteme",
                "description": "AI-powered code analysis and query system",
                "languages": {
                    "python": {"files": 145, "lines": 12500, "percentage": 65.2},
                    "javascript": {"files": 67, "lines": 8900, "percentage": 23.4},
                    "rust": {"files": 23, "lines": 4200, "percentage": 11.4}
                },
                "structure": {
                    "total_files": 235,
                    "total_lines": 25600,
                    "directories": 45,
                    "average_file_size": 109
                },
                "analysis_status": {
                    "status": "completed",
                    "analyzed_at": "2025-07-14T10:30:00Z",
                    "analysis_version": "2.0.1",
                    "next_analysis_due": "2025-07-15T10:30:00Z"
                },
                "quality_metrics": {
                    "code_quality": 0.85,
                    "security_score": 0.92,
                    "test_coverage": 0.87,
                    "documentation_coverage": 0.73
                },
                "dependencies": {
                    "total_dependencies": 127,
                    "outdated_dependencies": 12,
                    "security_vulnerabilities": 2,
                    "license_issues": 0
                }
            }
        }
        
        with patch.object(mock_client.client, 'get') as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = expected_response
            mock_response.raise_for_status.return_value = None
            mock_get.return_value = mock_response
            
            result = await mock_client.get_repository_metadata(TEST_REPO_ID)
            
            assert result == expected_response
            mock_get.assert_called_once_with(f"/repositories/{TEST_REPO_ID}/metadata")

    @pytest.mark.asyncio
    async def test_get_repository_metadata_not_found(self, mock_client):
        """Test metadata retrieval for non-existent repository."""
        with patch.object(mock_client.client, 'get') as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 404
            mock_response.raise_for_status.side_effect = httpx.HTTPStatusError(
                "Repository not found", request=MagicMock(), response=mock_response
            )
            mock_get.return_value = mock_response
            
            with pytest.raises(httpx.HTTPStatusError):
                await mock_client.get_repository_metadata("non-existent-repo")

    @pytest.mark.asyncio
    async def test_client_context_manager(self):
        """Test client as async context manager."""
        async with AnalysisEngineClient(base_url=TEST_ANALYSIS_ENGINE_URL) as client:
            # Test that client is properly initialized
            assert client.base_url == TEST_ANALYSIS_ENGINE_URL
            assert client.client is not None
            
            # Test health check within context
            is_healthy = await client.health_check()
            assert isinstance(is_healthy, bool)
        
        # Client should be closed after context exit
        # Note: We can't easily test this without accessing private members

    @pytest.mark.asyncio
    async def test_singleton_client_factory(self):
        """Test singleton client factory function."""
        client1 = get_analysis_engine_client()
        client2 = get_analysis_engine_client()
        
        # Should return the same instance
        assert client1 is client2
        
        # Should have circuit breakers registered
        assert hasattr(client1.get_analysis, 'circuit_breaker')
        assert hasattr(client1.search_embeddings, 'circuit_breaker')
        assert hasattr(client1.get_repository_metadata, 'circuit_breaker')

    @pytest.mark.asyncio
    async def test_concurrent_requests(self, mock_client):
        """Test concurrent requests to different endpoints."""
        with patch.object(mock_client.client, 'get') as mock_get, \
             patch.object(mock_client.client, 'post') as mock_post:
            
            # Setup mock responses
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"status": "success"}
            mock_response.raise_for_status.return_value = None
            mock_get.return_value = mock_response
            mock_post.return_value = mock_response
            
            # Run concurrent requests
            tasks = [
                mock_client.get_analysis(TEST_REPO_ID),
                mock_client.get_repository_metadata(TEST_REPO_ID),
                mock_client.search_embeddings(TEST_REPO_ID, TEST_EMBEDDING_VECTOR),
                mock_client.health_check()
            ]
            
            results = await asyncio.gather(*tasks)
            
            # Verify all requests completed successfully
            assert len(results) == 4
            assert all(result is not None for result in results[:-1])  # All except health check
            assert isinstance(results[-1], bool)  # Health check returns boolean

    @pytest.mark.asyncio
    async def test_request_timeout_handling(self, mock_client):
        """Test request timeout handling."""
        with patch.object(mock_client.client, 'get') as mock_get:
            mock_get.side_effect = httpx.TimeoutException("Request timeout")
            
            with pytest.raises(httpx.TimeoutException):
                await mock_client.get_analysis(TEST_REPO_ID)

    @pytest.mark.asyncio
    async def test_large_embedding_vector(self, mock_client):
        """Test handling of large embedding vectors."""
        large_embedding = [0.1] * 4096  # Large embedding vector
        
        with patch.object(mock_client.client, 'post') as mock_post:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"results": [], "total_results": 0}
            mock_response.raise_for_status.return_value = None
            mock_post.return_value = mock_response
            
            result = await mock_client.search_embeddings(
                repository_id=TEST_REPO_ID,
                query_embedding=large_embedding
            )
            
            assert result is not None
            call_args = mock_post.call_args
            payload = call_args[1]['json']
            assert len(payload['query_embedding']) == 4096

    @pytest.mark.asyncio
    async def test_malformed_responses(self, mock_client):
        """Test handling of malformed JSON responses."""
        with patch.object(mock_client.client, 'get') as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.side_effect = json.JSONDecodeError("Invalid JSON", "", 0)
            mock_response.raise_for_status.return_value = None
            mock_get.return_value = mock_response
            
            with pytest.raises(json.JSONDecodeError):
                await mock_client.get_analysis(TEST_REPO_ID)

    @pytest.mark.asyncio
    async def test_circuit_breaker_recovery(self, mock_client):
        """Test circuit breaker recovery after failures."""
        with patch.object(mock_client.client, 'get') as mock_get:
            # First, trigger circuit breaker with failures
            mock_get.side_effect = httpx.ConnectError("Connection refused")
            
            for i in range(3):  # Trigger circuit breaker
                with pytest.raises(httpx.ConnectError):
                    await mock_client.get_analysis(TEST_REPO_ID)
            
            # Now test recovery (this would normally require waiting for recovery timeout)
            # For testing purposes, we'll just verify the circuit breaker was triggered
            assert mock_get.call_count == 3

class TestAnalysisEngineIntegrationEndToEnd:
    """End-to-end integration tests with real service scenarios."""

    @pytest.mark.asyncio
    async def test_full_analysis_workflow(self, mock_client):
        """Test complete analysis workflow."""
        # Setup mock responses for full workflow
        with patch.object(mock_client, 'health_check') as mock_health, \
             patch.object(mock_client, 'get_repository_metadata') as mock_metadata, \
             patch.object(mock_client, 'search_embeddings') as mock_search, \
             patch.object(mock_client, 'get_analysis') as mock_analysis:
            
            mock_health.return_value = True
            mock_metadata.return_value = {
                "repository_id": TEST_REPO_ID,
                "metadata": {"languages": {"python": {"files": 100}}}
            }
            mock_search.return_value = {
                "results": [{"chunk_id": "chunk_001", "similarity_score": 0.9}],
                "total_results": 1
            }
            mock_analysis.return_value = {
                "repository_id": TEST_REPO_ID,
                "analysis_results": {"code_quality": 0.85}
            }
            
            # Execute full workflow
            assert await mock_client.health_check()
            
            metadata = await mock_client.get_repository_metadata(TEST_REPO_ID)
            assert metadata['repository_id'] == TEST_REPO_ID
            
            search_results = await mock_client.search_embeddings(
                TEST_REPO_ID, TEST_EMBEDDING_VECTOR
            )
            assert search_results['total_results'] == 1
            
            analysis = await mock_client.get_analysis(TEST_REPO_ID)
            assert analysis['analysis_results']['code_quality'] == 0.85

    @pytest.mark.asyncio
    async def test_error_propagation_in_workflow(self, mock_client):
        """Test error propagation in analysis workflow."""
        with patch.object(mock_client, 'health_check') as mock_health, \
             patch.object(mock_client, 'get_repository_metadata') as mock_metadata:
            
            mock_health.return_value = False
            mock_metadata.side_effect = httpx.HTTPStatusError(
                "Service unavailable", request=MagicMock(), response=MagicMock(status_code=503)
            )
            
            # Health check should fail
            assert not await mock_client.health_check()
            
            # Metadata request should raise error
            with pytest.raises(httpx.HTTPStatusError):
                await mock_client.get_repository_metadata(TEST_REPO_ID)

    @pytest.mark.asyncio
    async def test_performance_under_load(self, mock_client):
        """Test client performance under concurrent load."""
        import time
        
        with patch.object(mock_client, 'health_check') as mock_health:
            mock_health.return_value = True
            
            # Measure performance of concurrent health checks
            start_time = time.time()
            
            tasks = [mock_client.health_check() for _ in range(100)]
            results = await asyncio.gather(*tasks)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # All requests should succeed
            assert all(result is True for result in results)
            
            # Should complete within reasonable time (adjust based on actual performance)
            assert duration < 5.0  # 5 seconds for 100 concurrent requests
            
            # Verify all calls were made
            assert mock_health.call_count == 100

# Performance and load testing fixtures
@pytest.fixture
def performance_config():
    """Configuration for performance testing."""
    return {
        "concurrent_requests": 50,
        "request_timeout": 30.0,
        "max_retries": 3,
        "retry_delay": 1.0,
        "test_duration": 60.0
    }

@pytest.mark.asyncio
async def test_analysis_engine_load_testing(performance_config):
    """Load testing for analysis engine integration."""
    async with AnalysisEngineClient(base_url=TEST_ANALYSIS_ENGINE_URL) as client:
        # Verify service is available
        is_healthy = await client.health_check()
        if not is_healthy:
            pytest.skip("Analysis Engine service is not available")
        
        # Run concurrent health checks
        tasks = [
            client.health_check() 
            for _ in range(performance_config["concurrent_requests"])
        ]
        
        import time
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # Analyze results
        successful_requests = sum(1 for r in results if r is True)
        failed_requests = len(results) - successful_requests
        duration = end_time - start_time
        
        # Performance assertions
        assert successful_requests > 0, "At least some requests should succeed"
        assert failed_requests < len(results) * 0.1, "Less than 10% failure rate"
        assert duration < performance_config["test_duration"], "Should complete within timeout"
        
        # Performance metrics
        requests_per_second = len(results) / duration
        assert requests_per_second > 1, "Should handle at least 1 request per second"

if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])