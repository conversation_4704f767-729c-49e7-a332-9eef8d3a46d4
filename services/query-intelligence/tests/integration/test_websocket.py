import pytest
import json
import asyncio
import time
from unittest.mock import patch, Async<PERSON>ock, Mock
from fastapi.testclient import Test<PERSON>lient
import websockets
from websockets.exceptions import ConnectionClosed
import threading
from concurrent.futures import ThreadPoolExecutor

from query_intelligence.main import app
from query_intelligence.models import (
    QueryIntent,
    IntentAnalysis,
    CodeChunk,
    SearchResult,
    CodeReference,
)
from query_intelligence.middleware.auth import jwt_auth


@pytest.fixture
def test_client():
    return TestClient(app)


@pytest.fixture
def mock_query_processor():
    mock = Mock()
    mock._create_context = Mock()
    mock._analyze_intent = AsyncMock()
    mock.semantic_search = Mock()
    mock.semantic_search.generate_embedding = AsyncMock()
    mock.semantic_search.search = AsyncMock()
    mock._build_search_filters = Mock()
    mock._rerank_chunks = AsyncMock()
    mock._extract_references = Mock()
    mock.llm_service = Mock()
    mock.llm_service.stream_response = AsyncMock()
    mock._generate_follow_ups = AsyncMock()
    return mock


@pytest.fixture
def valid_jwt_token():
    """Create a valid JWT token for testing"""
    return jwt_auth.create_access_token({"sub": "test-user-123", "email": "<EMAIL>"})


class TestWebSocketAPI:

    def test_websocket_connection_no_auth(self, test_client):
        """Test WebSocket connection without authentication should fail"""
        with test_client.websocket_connect("/api/v1/ws/query") as websocket:
            # Should receive authentication required error
            data = websocket.receive_json()
            assert data["type"] == "error"
            assert data["code"] == "AUTH_REQUIRED"
            assert "Authentication required" in data["message"]

    def test_websocket_connection_with_invalid_token(self, test_client):
        """Test WebSocket connection with invalid token should fail"""
        with test_client.websocket_connect(
            "/api/v1/ws/query", 
            headers={"Authorization": "Bearer invalid-token"}
        ) as websocket:
            # Should receive authentication required error
            data = websocket.receive_json()
            assert data["type"] == "error"
            assert data["code"] == "AUTH_REQUIRED"
            assert "Authentication required" in data["message"]

    def test_websocket_connection_with_valid_token(self, test_client, valid_jwt_token):
        """Test WebSocket connection with valid token should succeed"""
        with test_client.websocket_connect(
            "/api/v1/ws/query", 
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket:
            # Connection should be established and ready for messages
            assert websocket is not None

    def test_websocket_query_success(self, test_client, valid_jwt_token, mock_query_processor):
        # Setup mocks
        mock_context = Mock()
        mock_query_processor._create_context.return_value = mock_context

        mock_intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN,
            code_elements=["auth"],
            scope="repository",
            context_depth="normal",
            confidence=0.9,
        )
        mock_query_processor._analyze_intent.return_value = mock_intent

        mock_query_processor.semantic_search.generate_embedding.return_value = [
            0.1,
            0.2,
            0.3,
        ]

        mock_chunks = [
            CodeChunk(
                file_path="src/auth.py",
                start_line=10,
                end_line=20,
                content="auth code",
                language="python",
                similarity_score=0.9,
                recency_score=0.8,
                combined_score=0.85,
            )
        ]
        mock_query_processor.semantic_search.search.return_value = SearchResult(
            chunks=mock_chunks, total_results=1, search_time_ms=50.0
        )

        mock_query_processor._build_search_filters.return_value = {}
        mock_query_processor._rerank_chunks.return_value = mock_chunks

        mock_references = [
            CodeReference(
                file_path="src/auth.py",
                start_line=10,
                end_line=20,
                snippet="auth code",
                relevance_score=0.85,
            )
        ]
        mock_query_processor._extract_references.return_value = mock_references

        # Setup streaming response
        async def mock_stream():
            yield "Authentication works by "
            yield "validating JWT tokens..."

        mock_query_processor.llm_service.stream_response.return_value = mock_stream()
        mock_query_processor._generate_follow_ups.return_value = [
            "How are tokens validated?"
        ]

        with patch(
            "query_intelligence.api.websocket.get_query_processor",
            return_value=mock_query_processor,
        ):
            with test_client.websocket_connect(
                "/api/v1/ws/query",
                headers={"Authorization": f"Bearer {valid_jwt_token}"}
            ) as websocket:
                # Send query
                websocket.send_text(
                    json.dumps(
                        {
                            "query": "How does authentication work?",
                            "repository_id": "test-repo",
                        }
                    )
                )

                # Receive acknowledgment
                data = websocket.receive_json()
                assert data["type"] == "acknowledged"
                assert data["query"] == "How does authentication work?"

                # Receive processing started
                data = websocket.receive_json()
                assert data["type"] == "processing_started"

                # Receive intent analysis
                data = websocket.receive_json()
                assert data["type"] == "intent_analyzed"
                assert data["intent"] == "explain"
                assert data["confidence"] == 0.9

                # Receive search status
                data = websocket.receive_json()
                assert data["type"] == "status"
                assert "Searching" in data["message"]

                # Receive search complete
                data = websocket.receive_json()
                assert data["type"] == "search_complete"
                assert data["results_found"] == 1

                # Receive reference
                data = websocket.receive_json()
                assert data["type"] == "reference"
                assert data["reference"]["file_path"] == "src/auth.py"

                # Receive generation status
                data = websocket.receive_json()
                assert data["type"] == "status"
                assert "Generating" in data["message"]

                # Receive text chunks
                data = websocket.receive_json()
                assert data["type"] == "text"
                assert data["content"] == "Authentication works by "

                data = websocket.receive_json()
                assert data["type"] == "text"
                assert data["content"] == "validating JWT tokens..."

                # Receive completion
                data = websocket.receive_json()
                assert data["type"] == "done"
                assert data["done"] is True
                assert "follow_up_questions" in data["metadata"]

    def test_websocket_invalid_json(self, test_client, valid_jwt_token):
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket:
            # Send invalid JSON
            websocket.send_text("invalid json")

            # Should receive error
            data = websocket.receive_json()
            assert data["type"] == "error"
            assert "Invalid JSON" in data["message"]

    def test_websocket_missing_fields(self, test_client, valid_jwt_token):
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket:
            # Send incomplete request
            websocket.send_text(
                json.dumps(
                    {
                        "query": "Test query"
                        # Missing repository_id
                    }
                )
            )

            # Should receive error
            data = websocket.receive_json()
            assert data["type"] == "error"
            assert "Query processing error" in data["message"]

    def test_websocket_processing_error(self, test_client, valid_jwt_token, mock_query_processor):
        # Setup error
        mock_query_processor._analyze_intent.side_effect = Exception(
            "Processing failed"
        )

        with patch(
            "query_intelligence.api.websocket.get_query_processor",
            return_value=mock_query_processor,
        ):
            with test_client.websocket_connect(
                "/api/v1/ws/query",
                headers={"Authorization": f"Bearer {valid_jwt_token}"}
            ) as websocket:
                # Send query
                websocket.send_text(
                    json.dumps({"query": "Test query", "repository_id": "test-repo"})
                )

                # Skip acknowledgment
                websocket.receive_json()

                # Should receive error
                data = websocket.receive_json()
                assert data["type"] == "error"
                assert "Streaming error" in data["message"]

    def test_websocket_auth_header_formats(self, test_client, valid_jwt_token):
        """Test various authorization header formats"""
        # Test missing Bearer prefix
        with test_client.websocket_connect(
            "/api/v1/ws/query", 
            headers={"Authorization": valid_jwt_token}
        ) as websocket:
            data = websocket.receive_json()
            assert data["type"] == "error"
            assert data["code"] == "AUTH_REQUIRED"

        # Test empty authorization header
        with test_client.websocket_connect(
            "/api/v1/ws/query", 
            headers={"Authorization": ""}
        ) as websocket:
            data = websocket.receive_json()
            assert data["type"] == "error"
            assert data["code"] == "AUTH_REQUIRED"

        # Test Bearer with no token
        with test_client.websocket_connect(
            "/api/v1/ws/query", 
            headers={"Authorization": "Bearer "}
        ) as websocket:
            data = websocket.receive_json()
            assert data["type"] == "error"
            assert data["code"] == "AUTH_REQUIRED"

    def test_websocket_rate_limiting_headers(self, test_client, valid_jwt_token):
        """Test that authentication uses headers and doesn't expose tokens in URL"""
        # This test ensures tokens are not logged in access logs
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket:
            # Connection should succeed with header-based auth
            assert websocket is not None
            # No token should be in URL or query parameters
            
    def test_websocket_connection_security_headers(self, test_client, valid_jwt_token):
        """Test WebSocket connection handles security headers properly"""
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={
                "Authorization": f"Bearer {valid_jwt_token}",
                "Origin": "https://example.com"
            }
        ) as websocket:
            assert websocket is not None

    def test_websocket_message_size_limits(self, test_client, valid_jwt_token):
        """Test WebSocket handles large messages appropriately"""
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket:
            # Send extremely large query (should be handled gracefully)
            large_query = "a" * 10000  # 10KB query
            websocket.send_text(json.dumps({
                "query": large_query,
                "repository_id": "test-repo"
            }))
            
            # Should receive appropriate error or processing response
            data = websocket.receive_json()
            assert data["type"] in ["error", "acknowledged"]

    def test_websocket_concurrent_connections(self, test_client, valid_jwt_token):
        """Test multiple WebSocket connections with same user"""
        connections = []
        
        try:
            # Create multiple connections
            for i in range(3):
                conn = test_client.websocket_connect(
                    "/api/v1/ws/query",
                    headers={"Authorization": f"Bearer {valid_jwt_token}"}
                )
                connections.append(conn.__enter__())
            
            # All connections should be valid
            assert len(connections) == 3
            for conn in connections:
                assert conn is not None
                
        finally:
            # Clean up connections
            for conn in connections:
                try:
                    conn.__exit__(None, None, None)
                except:
                    pass

    def test_websocket_malformed_json_messages(self, test_client, valid_jwt_token):
        """Test WebSocket handling of various malformed JSON"""
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket:
            
            # Test completely invalid JSON
            websocket.send_text("{invalid json")
            data = websocket.receive_json()
            assert data["type"] == "error"
            assert "Invalid JSON" in data["message"]
            
            # Test empty message
            websocket.send_text("")
            data = websocket.receive_json()
            assert data["type"] == "error"
            
            # Test null message
            websocket.send_text("null")
            data = websocket.receive_json()
            assert data["type"] == "error"

    def test_websocket_user_context_isolation(self, test_client):
        """Test that different users have isolated contexts"""
        # Create tokens for different users
        token1 = jwt_auth.create_access_token({"sub": "user1", "email": "<EMAIL>"})
        token2 = jwt_auth.create_access_token({"sub": "user2", "email": "<EMAIL>"})
        
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {token1}"}
        ) as ws1:
            with test_client.websocket_connect(
                "/api/v1/ws/query",
                headers={"Authorization": f"Bearer {token2}"}
            ) as ws2:
                # Both connections should be isolated
                assert ws1 is not None
                assert ws2 is not None

    def test_websocket_reconnection_scenarios(self, test_client, valid_jwt_token):
        """Test WebSocket reconnection behavior"""
        # Test normal connection followed by reconnection
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket:
            assert websocket is not None
            
        # Reconnect should work
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket2:
            assert websocket2 is not None

    def test_websocket_query_validation(self, test_client, valid_jwt_token):
        """Test WebSocket query validation"""
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket:
            
            # Test query without repository_id
            websocket.send_text(json.dumps({"query": "test query"}))
            data = websocket.receive_json()
            assert data["type"] == "error"
            
            # Test empty query
            websocket.send_text(json.dumps({
                "query": "",
                "repository_id": "test-repo"
            }))
            data = websocket.receive_json()
            assert data["type"] == "error"


class TestWebSocketE2E:
    """End-to-end WebSocket tests with real connection scenarios"""
    
    @pytest.fixture
    def mock_services(self):
        """Mock all external services for E2E tests"""
        with patch('query_intelligence.api.websocket.get_query_processor') as mock_processor:
            # Setup comprehensive mock
            mock_processor_instance = Mock()
            
            # Mock context creation
            mock_context = Mock()
            mock_context.repository_id = "test-repo"
            mock_context.user_id = "test-user-123"
            mock_processor_instance._create_context.return_value = mock_context
            
            # Mock intent analysis
            mock_intent = IntentAnalysis(
                primary_intent=QueryIntent.EXPLAIN,
                code_elements=["authentication"],
                scope="repository",
                context_depth="normal",
                confidence=0.9,
            )
            mock_processor_instance._analyze_intent = AsyncMock(return_value=mock_intent)
            
            # Mock semantic search
            mock_search = Mock()
            mock_search.generate_embedding = AsyncMock(return_value=[0.1, 0.2, 0.3])
            
            mock_chunks = [
                CodeChunk(
                    file_path="src/auth.py",
                    start_line=10,
                    end_line=20,
                    content="def authenticate_user(token):",
                    language="python",
                    similarity_score=0.9,
                    recency_score=0.8,
                    combined_score=0.85,
                )
            ]
            
            mock_search.search = AsyncMock(return_value=SearchResult(
                chunks=mock_chunks,
                total_results=1,
                search_time_ms=25.5
            ))
            
            mock_processor_instance.semantic_search = mock_search
            
            # Mock other methods
            mock_processor_instance._build_search_filters = Mock(return_value={})
            mock_processor_instance._rerank_chunks = AsyncMock(return_value=mock_chunks)
            
            mock_references = [
                CodeReference(
                    file_path="src/auth.py",
                    start_line=10,
                    end_line=20,
                    snippet="def authenticate_user(token):",
                    relevance_score=0.85,
                )
            ]
            mock_processor_instance._extract_references = Mock(return_value=mock_references)
            
            # Mock LLM streaming
            async def mock_stream():
                chunks = [
                    "Authentication in this system works by ",
                    "validating JWT tokens. ",
                    "The authenticate_user function takes a token ",
                    "and verifies its signature and expiration."
                ]
                for chunk in chunks:
                    yield chunk
                    await asyncio.sleep(0.1)  # Simulate streaming delay
            
            mock_llm = Mock()
            mock_llm.stream_response = AsyncMock(side_effect=mock_stream)
            mock_processor_instance.llm_service = mock_llm
            
            # Mock follow-up generation
            mock_processor_instance._generate_follow_ups = AsyncMock(return_value=[
                "How are JWT tokens generated?",
                "What happens if a token expires?"
            ])
            
            mock_processor.return_value = mock_processor_instance
            yield mock_processor_instance
    
    def test_websocket_full_streaming_flow(self, test_client, valid_jwt_token, mock_services):
        """Test complete WebSocket streaming flow with all message types"""
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket:
            # Send query
            query_request = {
                "query": "How does authentication work?",
                "repository_id": "test-repo"
            }
            websocket.send_text(json.dumps(query_request))
            
            # Collect all messages
            messages = []
            while True:
                try:
                    data = websocket.receive_json()
                    messages.append(data)
                    if data.get("type") == "done" and data.get("done"):
                        break
                except Exception:
                    break
            
            # Verify message sequence
            message_types = [msg["type"] for msg in messages]
            
            # Should have these message types in order
            assert "acknowledged" in message_types
            assert "processing_started" in message_types
            assert "intent_analyzed" in message_types
            assert "status" in message_types  # "Searching codebase..."
            assert "search_complete" in message_types
            # Note: "reference" might not always be sent due to search failures
            # assert "reference" in message_types
            assert "text" in message_types  # Multiple streaming text chunks
            assert "done" in message_types
            
            # Verify specific message content
            ack_msg = next(msg for msg in messages if msg["type"] == "acknowledged")
            assert ack_msg["query"] == "How does authentication work?"
            
            intent_msg = next(msg for msg in messages if msg["type"] == "intent_analyzed")
            assert intent_msg["intent"] == "explain"
            assert intent_msg["confidence"] == 0.9
            
            search_msg = next(msg for msg in messages if msg["type"] == "search_complete")
            assert search_msg["results_found"] == 1
            assert "search_time_ms" in search_msg
            
            # Verify streaming text chunks
            text_messages = [msg for msg in messages if msg["type"] == "text"]
            assert len(text_messages) >= 2  # Should have multiple chunks
            
            # Verify completion message
            done_msg = next(msg for msg in messages if msg["type"] == "done")
            assert done_msg["done"] is True
            assert "metadata" in done_msg
            assert "follow_up_questions" in done_msg["metadata"]
            assert len(done_msg["metadata"]["follow_up_questions"]) == 2
    
    def test_websocket_concurrent_user_connections(self, test_client, mock_services):
        """Test multiple concurrent connections from different users"""
        # Create tokens for different users
        user_tokens = [
            jwt_auth.create_access_token({"sub": f"user-{i}", "email": f"user{i}@example.com"})
            for i in range(5)
        ]
        
        connections = []
        
        try:
            # Establish multiple connections
            for i, token in enumerate(user_tokens):
                conn = test_client.websocket_connect(
                    "/api/v1/ws/query",
                    headers={"Authorization": f"Bearer {token}"}
                )
                connections.append(conn.__enter__())
            
            # Send queries from all connections simultaneously
            for i, conn in enumerate(connections):
                query = {
                    "query": f"User {i} query about authentication",
                    "repository_id": "test-repo"
                }
                conn.send_text(json.dumps(query))
            
            # Verify all connections receive responses
            for i, conn in enumerate(connections):
                # Should receive acknowledgment
                data = conn.receive_json()
                assert data["type"] == "acknowledged"
                assert f"User {i} query" in data["query"]
                
        finally:
            # Clean up connections
            for conn in connections:
                try:
                    conn.__exit__(None, None, None)
                except:
                    pass
    
    def test_websocket_connection_limits(self, test_client, valid_jwt_token, mock_services):
        """Test connection limits enforcement (simulated)"""
        # This test simulates connection limit behavior
        # In a real implementation, you would test the actual limit enforcement
        
        connections = []
        max_connections = 15  # Attempt more than typical limit
        
        successful_connections = 0
        
        try:
            for i in range(max_connections):
                try:
                    conn = test_client.websocket_connect(
                        "/api/v1/ws/query",
                        headers={"Authorization": f"Bearer {valid_jwt_token}"}
                    )
                    connections.append(conn.__enter__())
                    successful_connections += 1
                except Exception as e:
                    # Connection limit reached or other error
                    break
            
            # Should be able to establish reasonable number of connections
            assert successful_connections >= 3  # At least some connections should work
            
        finally:
            # Clean up all connections
            for conn in connections:
                try:
                    conn.__exit__(None, None, None)
                except:
                    pass
    
    def test_websocket_message_rate_limiting(self, test_client, valid_jwt_token, mock_services):
        """Test message rate limiting behavior"""
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket:
            # Send rapid messages
            messages_sent = 0
            errors_received = 0
            
            for i in range(10):
                try:
                    query = {
                        "query": f"Rapid query {i}",
                        "repository_id": "test-repo"
                    }
                    websocket.send_text(json.dumps(query))
                    messages_sent += 1
                    
                    # Try to receive response quickly
                    try:
                        data = websocket.receive_json()
                        if data["type"] == "error":
                            errors_received += 1
                    except:
                        pass
                        
                except Exception:
                    break
            
            # Should have sent some messages
            assert messages_sent >= 5
    
    def test_websocket_error_recovery(self, test_client, valid_jwt_token):
        """Test WebSocket error recovery scenarios"""
        with patch('query_intelligence.api.websocket.get_query_processor') as mock_processor:
            # Setup processor that fails on first call, succeeds on second
            mock_processor_instance = Mock()
            mock_processor_instance._create_context = Mock(return_value=Mock(repository_id="test-repo"))
            
            # Fail on first call, succeed on second
            call_count = 0
            async def failing_analyze_intent(*args, **kwargs):
                nonlocal call_count
                call_count += 1
                if call_count == 1:
                    raise Exception("Temporary failure")
                return IntentAnalysis(
                    primary_intent=QueryIntent.EXPLAIN,
                    code_elements=["test"],
                    scope="repository",
                    context_depth="normal",
                    confidence=0.8,
                )
            
            mock_processor_instance._analyze_intent = AsyncMock(side_effect=failing_analyze_intent)
            mock_processor.return_value = mock_processor_instance
            
            with test_client.websocket_connect(
                "/api/v1/ws/query",
                headers={"Authorization": f"Bearer {valid_jwt_token}"}
            ) as websocket:
                # Send first query (should fail)
                websocket.send_text(json.dumps({
                    "query": "First query",
                    "repository_id": "test-repo"
                }))
                
                # Should receive acknowledgment then error
                ack_data = websocket.receive_json()
                assert ack_data["type"] == "acknowledged"
                
                error_data = websocket.receive_json()
                assert error_data["type"] == "error"
                assert "Streaming error" in error_data["message"]
                
                # Send second query (should succeed)
                websocket.send_text(json.dumps({
                    "query": "Second query",
                    "repository_id": "test-repo"
                }))
                
                # Should receive acknowledgment (recovery)
                ack_data2 = websocket.receive_json()
                assert ack_data2["type"] == "acknowledged"
                assert ack_data2["query"] == "Second query"
    
    def test_websocket_timeout_handling(self, test_client, valid_jwt_token):
        """Test WebSocket timeout scenarios"""
        with patch('query_intelligence.api.websocket.get_query_processor') as mock_processor:
            # Setup processor with very slow response
            mock_processor_instance = Mock()
            mock_processor_instance._create_context = Mock(return_value=Mock(repository_id="test-repo"))
            
            async def slow_analyze_intent(*args, **kwargs):
                await asyncio.sleep(0.5)  # Simulate slow processing
                return IntentAnalysis(
                    primary_intent=QueryIntent.EXPLAIN,
                    code_elements=["test"],
                    scope="repository",
                    context_depth="normal",
                    confidence=0.8,
                )
            
            mock_processor_instance._analyze_intent = AsyncMock(side_effect=slow_analyze_intent)
            mock_processor.return_value = mock_processor_instance
            
            with test_client.websocket_connect(
                "/api/v1/ws/query",
                headers={"Authorization": f"Bearer {valid_jwt_token}"}
            ) as websocket:
                # Send query
                websocket.send_text(json.dumps({
                    "query": "Slow query",
                    "repository_id": "test-repo"
                }))
                
                # Should receive acknowledgment
                ack_data = websocket.receive_json()
                assert ack_data["type"] == "acknowledged"
                
                # Should receive processing started (may timeout in slow processing)
                try:
                    processing_data = websocket.receive_json()
                    assert processing_data["type"] in ["processing_started", "error"]
                except:
                    # Timeout is acceptable in this test
                    pass
    
    def test_websocket_malformed_auth_tokens(self, test_client, mock_services):
        """Test various malformed authentication tokens"""
        malformed_tokens = [
            "not.a.jwt",
            "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.malformed",
            "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.malformed",
            "expired.token.here",
            "",
            "Bearer",
            "Basic dXNlcjpwYXNz",  # Basic auth instead of Bearer
        ]
        
        for token in malformed_tokens:
            with test_client.websocket_connect(
                "/api/v1/ws/query",
                headers={"Authorization": token}
            ) as websocket:
                # Should receive authentication error
                data = websocket.receive_json()
                assert data["type"] == "error"
                assert data["code"] == "AUTH_REQUIRED"
                assert "Authentication required" in data["message"]
    
    def test_websocket_large_payload_handling(self, test_client, valid_jwt_token, mock_services):
        """Test WebSocket handling of large payloads"""
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket:
            # Test progressively larger payloads
            sizes = [100, 1000, 5000, 10000]  # bytes
            
            for size in sizes:
                large_query = "a" * size
                payload = {
                    "query": large_query,
                    "repository_id": "test-repo"
                }
                
                try:
                    websocket.send_text(json.dumps(payload))
                    
                    # Should receive acknowledgment or error
                    data = websocket.receive_json()
                    assert data["type"] in ["acknowledged", "error"]
                    
                    if data["type"] == "acknowledged":
                        # If acknowledged, query should be truncated or handled
                        assert len(data["query"]) <= size + 100  # Allow some overhead
                    
                except Exception as e:
                    # Large payloads might be rejected
                    assert size >= 5000  # Only very large payloads should fail
    
    def test_websocket_binary_message_rejection(self, test_client, valid_jwt_token):
        """Test WebSocket rejection of binary messages"""
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket:
            # Try to send binary data
            try:
                websocket.send_bytes(b"binary data")
                
                # Should receive error or connection should close
                try:
                    data = websocket.receive_json()
                    if data["type"] == "error":
                        assert "text" in data["message"].lower() or "json" in data["message"].lower()
                except:
                    # Connection might close, which is acceptable
                    pass
                    
            except Exception:
                # Binary messages might be rejected at protocol level
                pass


class TestWebSocketPerformance:
    """Performance-focused WebSocket tests"""
    
    def test_websocket_message_throughput(self, test_client, valid_jwt_token):
        """Test WebSocket message throughput"""
        with patch('query_intelligence.api.websocket.get_query_processor') as mock_processor:
            # Setup fast mock processor
            mock_processor_instance = Mock()
            mock_processor_instance._create_context = Mock(return_value=Mock(repository_id="test-repo"))
            
            # Fast mock responses
            mock_processor_instance._analyze_intent = AsyncMock(return_value=IntentAnalysis(
                primary_intent=QueryIntent.EXPLAIN,
                code_elements=["test"],
                scope="repository",
                context_depth="normal",
                confidence=0.8,
            ))
            
            mock_processor.return_value = mock_processor_instance
            
            with test_client.websocket_connect(
                "/api/v1/ws/query",
                headers={"Authorization": f"Bearer {valid_jwt_token}"}
            ) as websocket:
                # Send multiple messages and measure response time
                start_time = time.time()
                message_count = 5
                
                for i in range(message_count):
                    websocket.send_text(json.dumps({
                        "query": f"Performance test query {i}",
                        "repository_id": "test-repo"
                    }))
                    
                    # Receive acknowledgment
                    data = websocket.receive_json()
                    assert data["type"] == "acknowledged"
                
                end_time = time.time()
                total_time = end_time - start_time
                
                # Should handle messages reasonably fast
                avg_time_per_message = total_time / message_count
                assert avg_time_per_message < 2.0  # Less than 2 seconds per message
    
    def test_websocket_concurrent_message_handling(self, test_client, valid_jwt_token):
        """Test handling of concurrent messages from single connection"""
        with patch('query_intelligence.api.websocket.get_query_processor') as mock_processor:
            # Setup mock processor
            mock_processor_instance = Mock()
            mock_processor_instance._create_context = Mock(return_value=Mock(repository_id="test-repo"))
            
            mock_processor_instance._analyze_intent = AsyncMock(return_value=IntentAnalysis(
                primary_intent=QueryIntent.EXPLAIN,
                code_elements=["test"],
                scope="repository",
                context_depth="normal",
                confidence=0.8,
            ))
            
            mock_processor.return_value = mock_processor_instance
            
            with test_client.websocket_connect(
                "/api/v1/ws/query",
                headers={"Authorization": f"Bearer {valid_jwt_token}"}
            ) as websocket:
                # Send multiple messages rapidly
                messages_sent = 0
                for i in range(3):
                    try:
                        websocket.send_text(json.dumps({
                            "query": f"Concurrent query {i}",
                            "repository_id": "test-repo"
                        }))
                        messages_sent += 1
                    except Exception:
                        break
                
                # Should have sent at least some messages
                assert messages_sent >= 2
                
                # Receive responses (may be out of order)
                responses_received = 0
                for _ in range(messages_sent):
                    try:
                        data = websocket.receive_json()
                        if data["type"] == "acknowledged":
                            responses_received += 1
                    except Exception:
                        break
                
                # Should receive most acknowledgments
                assert responses_received >= 1


class TestWebSocketConnectionManagement:
    """Test WebSocket connection management and cleanup"""
    
    def test_websocket_graceful_disconnect(self, test_client, valid_jwt_token):
        """Test graceful WebSocket disconnection"""
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket:
            # Send a query
            websocket.send_text(json.dumps({
                "query": "Test query before disconnect",
                "repository_id": "test-repo"
            }))
            
            # Receive acknowledgment
            data = websocket.receive_json()
            assert data["type"] == "acknowledged"
            
            # Connection should close gracefully when exiting context
    
    def test_websocket_forced_disconnect(self, test_client, valid_jwt_token):
        """Test forced WebSocket disconnection handling"""
        conn = test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        )
        
        websocket = conn.__enter__()
        
        try:
            # Send a query
            websocket.send_text(json.dumps({
                "query": "Test query before forced disconnect",
                "repository_id": "test-repo"
            }))
            
            # Receive acknowledgment
            data = websocket.receive_json()
            assert data["type"] == "acknowledged"
            
        finally:
            # Force disconnect
            conn.__exit__(None, None, None)
    
    def test_websocket_connection_state_cleanup(self, test_client, valid_jwt_token):
        """Test WebSocket connection state cleanup"""
        # Create multiple connections sequentially
        for i in range(3):
            with test_client.websocket_connect(
                "/api/v1/ws/query",
                headers={"Authorization": f"Bearer {valid_jwt_token}"}
            ) as websocket:
                # Send query
                websocket.send_text(json.dumps({
                    "query": f"Connection {i} query",
                    "repository_id": "test-repo"
                }))
                
                # Receive acknowledgment
                data = websocket.receive_json()
                assert data["type"] == "acknowledged"
                
                # Each connection should work independently
                assert f"Connection {i} query" == data["query"]
    
    def test_websocket_expired_token_handling(self, test_client):
        """Test WebSocket handling of expired tokens"""
        # Create an expired token (simulate by creating with past timestamp)
        import time
        from datetime import datetime, timedelta
        
        # Create token that expires immediately
        expired_payload = {
            "sub": "test-user",
            "email": "<EMAIL>",
            "exp": int(time.time()) - 3600  # Expired 1 hour ago
        }
        
        # Note: This test depends on JWT implementation details
        # In a real scenario, you'd create a properly expired token
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": "Bearer expired.token.here"}
        ) as websocket:
            # Should receive authentication error
            data = websocket.receive_json()
            assert data["type"] == "error"
            assert data["code"] == "AUTH_REQUIRED"
            assert "Authentication required" in data["message"]
    
    def test_websocket_user_session_isolation(self, test_client):
        """Test user session isolation across WebSocket connections"""
        # Create tokens for different users
        user1_token = jwt_auth.create_access_token({
            "sub": "user1",
            "email": "<EMAIL>"
        })
        user2_token = jwt_auth.create_access_token({
            "sub": "user2",
            "email": "<EMAIL>"
        })
        
        # Test simultaneous connections from different users
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {user1_token}"}
        ) as ws1:
            with test_client.websocket_connect(
                "/api/v1/ws/query",
                headers={"Authorization": f"Bearer {user2_token}"}
            ) as ws2:
                # Send queries from both users
                ws1.send_text(json.dumps({
                    "query": "User 1 query",
                    "repository_id": "test-repo"
                }))
                
                ws2.send_text(json.dumps({
                    "query": "User 2 query",
                    "repository_id": "test-repo"
                }))
                
                # Receive responses
                data1 = ws1.receive_json()
                data2 = ws2.receive_json()
                
                # Both should be acknowledged with correct queries
                assert data1["type"] == "acknowledged"
                assert data2["type"] == "acknowledged"
                assert data1["query"] == "User 1 query"
                assert data2["query"] == "User 2 query"
