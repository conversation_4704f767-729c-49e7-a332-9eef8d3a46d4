"""
Distributed Tracing Configuration for Query Intelligence Service
Implements OpenTelemetry distributed tracing for comprehensive observability
"""

import os
from typing import Optional
from opentelemetry import trace
from opentelemetry.exporter.jaeger.thrift import J<PERSON>gerExporter
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.httpx import HTTPXClientInstrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from opentelemetry.instrumentation.logging import LoggingInstrumentor
from opentelemetry.propagators.b3 import B3MultiFormat
from opentelemetry.propagators.jaeger import JaegerPropagator
from opentelemetry.propagators.composite import CompositePropagator
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor, ConsoleSpanExporter
from opentelemetry.sdk.resources import Resource
from opentelemetry.semconv.resource import ResourceAttributes
import structlog

logger = structlog.get_logger()

class TracingConfig:
    """Configuration for distributed tracing"""
    
    def __init__(self):
        self.service_name = os.getenv("SERVICE_NAME", "query-intelligence")
        self.service_version = os.getenv("SERVICE_VERSION", "1.0.0")
        self.environment = os.getenv("ENVIRONMENT", "production")
        self.jaeger_endpoint = os.getenv("JAEGER_ENDPOINT", "http://localhost:14268/api/traces")
        self.otlp_endpoint = os.getenv("OTLP_ENDPOINT", "http://localhost:4317")
        self.tracing_enabled = os.getenv("TRACING_ENABLED", "true").lower() == "true"
        self.console_exporter = os.getenv("CONSOLE_EXPORTER", "false").lower() == "true"
        self.sampling_rate = float(os.getenv("SAMPLING_RATE", "1.0"))
        
    def setup_tracing(self) -> Optional[trace.Tracer]:
        """Setup distributed tracing with OpenTelemetry"""
        if not self.tracing_enabled:
            logger.info("tracing_disabled")
            return None
            
        # Create resource
        resource = Resource.create({
            ResourceAttributes.SERVICE_NAME: self.service_name,
            ResourceAttributes.SERVICE_VERSION: self.service_version,
            ResourceAttributes.DEPLOYMENT_ENVIRONMENT: self.environment,
            ResourceAttributes.SERVICE_NAMESPACE: "episteme",
        })
        
        # Create tracer provider
        provider = TracerProvider(resource=resource)
        trace.set_tracer_provider(provider)
        
        # Setup exporters
        self._setup_exporters(provider)
        
        # Setup propagators
        self._setup_propagators()
        
        # Setup instrumentations
        self._setup_instrumentations()
        
        tracer = trace.get_tracer(__name__)
        logger.info(
            "tracing_initialized",
            service_name=self.service_name,
            jaeger_endpoint=self.jaeger_endpoint,
            otlp_endpoint=self.otlp_endpoint,
            sampling_rate=self.sampling_rate
        )
        
        return tracer
        
    def _setup_exporters(self, provider: TracerProvider):
        """Setup trace exporters"""
        # Jaeger exporter
        try:
            jaeger_exporter = JaegerExporter(
                endpoint=self.jaeger_endpoint,
                collector_endpoint=self.jaeger_endpoint,
                insecure=True,
            )
            provider.add_span_processor(BatchSpanProcessor(jaeger_exporter))
            logger.info("jaeger_exporter_configured", endpoint=self.jaeger_endpoint)
        except Exception as e:
            logger.warning("jaeger_exporter_failed", error=str(e))
            
        # OTLP exporter
        try:
            otlp_exporter = OTLPSpanExporter(
                endpoint=self.otlp_endpoint,
                insecure=True,
            )
            provider.add_span_processor(BatchSpanProcessor(otlp_exporter))
            logger.info("otlp_exporter_configured", endpoint=self.otlp_endpoint)
        except Exception as e:
            logger.warning("otlp_exporter_failed", error=str(e))
            
        # Console exporter for debugging
        if self.console_exporter:
            console_exporter = ConsoleSpanExporter()
            provider.add_span_processor(BatchSpanProcessor(console_exporter))
            logger.info("console_exporter_configured")
            
    def _setup_propagators(self):
        """Setup trace propagators"""
        # Support multiple propagation formats
        propagators = [
            B3MultiFormat(),
            JaegerPropagator(),
        ]
        
        composite_propagator = CompositePropagator(propagators)
        trace.set_global_textmap(composite_propagator)
        logger.info("propagators_configured")
        
    def _setup_instrumentations(self):
        """Setup automatic instrumentation"""
        # FastAPI instrumentation
        FastAPIInstrumentor.instrument()
        logger.info("fastapi_instrumentation_enabled")
        
        # HTTP client instrumentation
        HTTPXClientInstrumentor.instrument()
        logger.info("httpx_instrumentation_enabled")
        
        # Redis instrumentation
        try:
            RedisInstrumentor.instrument()
            logger.info("redis_instrumentation_enabled")
        except Exception as e:
            logger.warning("redis_instrumentation_failed", error=str(e))
            
        # Logging instrumentation
        LoggingInstrumentor.instrument()
        logger.info("logging_instrumentation_enabled")

# Custom trace decorators
def trace_function(span_name: Optional[str] = None):
    """Decorator to trace function calls"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            tracer = trace.get_tracer(__name__)
            name = span_name or f"{func.__module__}.{func.__name__}"
            
            with tracer.start_as_current_span(name) as span:
                # Add function attributes
                span.set_attribute("function.name", func.__name__)
                span.set_attribute("function.module", func.__module__)
                
                # Add arguments as attributes (be careful with sensitive data)
                if args:
                    span.set_attribute("function.args_count", len(args))
                if kwargs:
                    span.set_attribute("function.kwargs_count", len(kwargs))
                    
                try:
                    result = func(*args, **kwargs)
                    span.set_attribute("function.success", True)
                    return result
                except Exception as e:
                    span.set_attribute("function.success", False)
                    span.set_attribute("function.error", str(e))
                    span.record_exception(e)
                    raise
                    
        return wrapper
    return decorator

async def trace_async_function(span_name: Optional[str] = None):
    """Decorator to trace async function calls"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            tracer = trace.get_tracer(__name__)
            name = span_name or f"{func.__module__}.{func.__name__}"
            
            with tracer.start_as_current_span(name) as span:
                # Add function attributes
                span.set_attribute("function.name", func.__name__)
                span.set_attribute("function.module", func.__module__)
                span.set_attribute("function.type", "async")
                
                # Add arguments as attributes
                if args:
                    span.set_attribute("function.args_count", len(args))
                if kwargs:
                    span.set_attribute("function.kwargs_count", len(kwargs))
                    
                try:
                    result = await func(*args, **kwargs)
                    span.set_attribute("function.success", True)
                    return result
                except Exception as e:
                    span.set_attribute("function.success", False)
                    span.set_attribute("function.error", str(e))
                    span.record_exception(e)
                    raise
                    
        return wrapper
    return decorator

# Trace context utilities
class TraceContext:
    """Utilities for working with trace context"""
    
    @staticmethod
    def get_current_trace_id() -> Optional[str]:
        """Get current trace ID"""
        span = trace.get_current_span()
        if span:
            return format(span.get_span_context().trace_id, '032x')
        return None
        
    @staticmethod
    def get_current_span_id() -> Optional[str]:
        """Get current span ID"""
        span = trace.get_current_span()
        if span:
            return format(span.get_span_context().span_id, '016x')
        return None
        
    @staticmethod
    def add_span_attribute(key: str, value: str):
        """Add attribute to current span"""
        span = trace.get_current_span()
        if span:
            span.set_attribute(key, value)
            
    @staticmethod
    def add_span_event(name: str, attributes: dict = None):
        """Add event to current span"""
        span = trace.get_current_span()
        if span:
            span.add_event(name, attributes or {})
            
    @staticmethod
    def record_exception(exception: Exception):
        """Record exception in current span"""
        span = trace.get_current_span()
        if span:
            span.record_exception(exception)

# Business logic tracing
class BusinessTracing:
    """Business logic specific tracing utilities"""
    
    @staticmethod
    def trace_query_processing(query_id: str, intent: str, user_id: str = None):
        """Trace query processing with business context"""
        tracer = trace.get_tracer(__name__)
        
        with tracer.start_as_current_span("query_processing") as span:
            span.set_attribute("query.id", query_id)
            span.set_attribute("query.intent", intent)
            if user_id:
                span.set_attribute("user.id", user_id)
            
            return span
            
    @staticmethod
    def trace_llm_request(model: str, prompt_tokens: int, completion_tokens: int = None):
        """Trace LLM request with token usage"""
        tracer = trace.get_tracer(__name__)
        
        with tracer.start_as_current_span("llm_request") as span:
            span.set_attribute("llm.model", model)
            span.set_attribute("llm.prompt_tokens", prompt_tokens)
            if completion_tokens:
                span.set_attribute("llm.completion_tokens", completion_tokens)
                span.set_attribute("llm.total_tokens", prompt_tokens + completion_tokens)
            
            return span
            
    @staticmethod
    def trace_cache_operation(operation: str, key: str, hit: bool = None):
        """Trace cache operations"""
        tracer = trace.get_tracer(__name__)
        
        with tracer.start_as_current_span("cache_operation") as span:
            span.set_attribute("cache.operation", operation)
            span.set_attribute("cache.key", key)
            if hit is not None:
                span.set_attribute("cache.hit", hit)
            
            return span
            
    @staticmethod
    def trace_database_operation(operation: str, table: str, query: str = None):
        """Trace database operations"""
        tracer = trace.get_tracer(__name__)
        
        with tracer.start_as_current_span("database_operation") as span:
            span.set_attribute("db.operation", operation)
            span.set_attribute("db.table", table)
            if query:
                span.set_attribute("db.query", query)
            
            return span

# Global tracing configuration
tracing_config = TracingConfig()
tracer = tracing_config.setup_tracing()

# Export commonly used utilities
__all__ = [
    'TracingConfig',
    'trace_function',
    'trace_async_function',
    'TraceContext',
    'BusinessTracing',
    'tracing_config',
    'tracer'
]