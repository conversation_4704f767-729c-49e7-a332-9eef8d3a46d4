"""
Enhanced Metrics Collection for Query Intelligence Service
Extends the basic metrics with advanced observability features
"""

import time
import psutil
import asyncio
from typing import Dict, List, Optional, Any
from functools import wraps
from prometheus_client import Counter, Histogram, Gauge, Info, Enum, Summary
from prometheus_client.metrics import MetricWrapperBase
import structlog

logger = structlog.get_logger()

# Advanced performance metrics
query_latency_by_complexity = Histogram(
    "query_intelligence_latency_by_complexity_seconds",
    "Query latency by complexity level",
    ["complexity_level", "intent"],
    buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0, float('inf')]
)

query_queue_size = Gauge(
    "query_intelligence_queue_size",
    "Number of queries in processing queue"
)

query_processing_stages = Histogram(
    "query_intelligence_processing_stage_duration_seconds",
    "Duration of each processing stage",
    ["stage", "intent"],
    buckets=[0.01, 0.05, 0.1, 0.5, 1.0, 2.0, float('inf')]
)

concurrent_queries_by_user = Gauge(
    "query_intelligence_concurrent_queries_by_user",
    "Number of concurrent queries by user",
    ["user_id"]
)

# Resource utilization metrics
system_memory_usage = Gauge(
    "query_intelligence_system_memory_usage_bytes",
    "System memory usage in bytes",
    ["type"]
)

system_cpu_usage = Gauge(
    "query_intelligence_system_cpu_usage_percent",
    "System CPU usage percentage",
    ["core"]
)

system_disk_usage = Gauge(
    "query_intelligence_system_disk_usage_bytes",
    "System disk usage in bytes",
    ["device", "type"]
)

# Application-specific metrics
model_performance = Histogram(
    "query_intelligence_model_performance_seconds",
    "Model inference performance",
    ["model", "task", "complexity"],
    buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0, float('inf')]
)

embedding_cache_efficiency = Gauge(
    "query_intelligence_embedding_cache_efficiency_percent",
    "Embedding cache efficiency percentage",
    ["model"]
)

semantic_search_precision = Histogram(
    "query_intelligence_semantic_search_precision",
    "Semantic search precision scores",
    ["repository_id", "query_type"],
    buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
)

# Business metrics
user_satisfaction_score = Histogram(
    "query_intelligence_user_satisfaction_score",
    "User satisfaction scores",
    ["intent", "response_type"],
    buckets=[1.0, 2.0, 3.0, 4.0, 5.0]
)

query_success_rate_by_intent = Gauge(
    "query_intelligence_success_rate_by_intent_percent",
    "Success rate by query intent",
    ["intent"]
)

response_quality_metrics = Histogram(
    "query_intelligence_response_quality_score",
    "Response quality scores",
    ["metric_type", "intent"],
    buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
)

# Circuit breaker metrics
circuit_breaker_state_changes = Counter(
    "query_intelligence_circuit_breaker_state_changes_total",
    "Total circuit breaker state changes",
    ["breaker_name", "from_state", "to_state"]
)

circuit_breaker_failure_rate = Gauge(
    "query_intelligence_circuit_breaker_failure_rate_percent",
    "Circuit breaker failure rate",
    ["breaker_name"]
)

# Error tracking metrics
error_classification = Counter(
    "query_intelligence_errors_by_classification_total",
    "Total errors by classification",
    ["error_type", "severity", "component"]
)

error_resolution_time = Histogram(
    "query_intelligence_error_resolution_time_seconds",
    "Time to resolve errors",
    ["error_type", "severity"],
    buckets=[1.0, 5.0, 10.0, 30.0, 60.0, 300.0, float('inf')]
)

# Security metrics
security_events = Counter(
    "query_intelligence_security_events_total",
    "Total security events",
    ["event_type", "severity", "source"]
)

auth_attempts = Counter(
    "query_intelligence_auth_attempts_total",
    "Total authentication attempts",
    ["method", "result", "user_type"]
)

rate_limit_violations = Counter(
    "query_intelligence_rate_limit_violations_total",
    "Total rate limit violations",
    ["user_id", "endpoint", "limit_type"]
)

# Dependency metrics
dependency_response_time = Histogram(
    "query_intelligence_dependency_response_time_seconds",
    "Response time for external dependencies",
    ["dependency", "operation", "status"],
    buckets=[0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0, float('inf')]
)

dependency_availability = Gauge(
    "query_intelligence_dependency_availability_percent",
    "Dependency availability percentage",
    ["dependency"]
)

# Custom metrics collector
class EnhancedMetricsCollector:
    """Enhanced metrics collector with advanced features"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.last_cpu_times = self.process.cpu_times()
        self.last_check_time = time.time()
        
    def collect_system_metrics(self):
        """Collect system-level metrics"""
        try:
            # Memory metrics
            memory = psutil.virtual_memory()
            system_memory_usage.labels(type="total").set(memory.total)
            system_memory_usage.labels(type="available").set(memory.available)
            system_memory_usage.labels(type="used").set(memory.used)
            system_memory_usage.labels(type="cached").set(memory.cached)
            
            # CPU metrics
            cpu_percentages = psutil.cpu_percent(percpu=True)
            for i, cpu_percent in enumerate(cpu_percentages):
                system_cpu_usage.labels(core=str(i)).set(cpu_percent)
                
            # Disk metrics
            disk_usage = psutil.disk_usage('/')
            system_disk_usage.labels(device="root", type="total").set(disk_usage.total)
            system_disk_usage.labels(device="root", type="used").set(disk_usage.used)
            system_disk_usage.labels(device="root", type="free").set(disk_usage.free)
            
        except Exception as e:
            logger.error("system_metrics_collection_failed", error=str(e))
            
    def record_query_complexity(self, complexity_level: str, intent: str, duration: float):
        """Record query metrics by complexity"""
        query_latency_by_complexity.labels(
            complexity_level=complexity_level,
            intent=intent
        ).observe(duration)
        
    def record_processing_stage(self, stage: str, intent: str, duration: float):
        """Record processing stage duration"""
        query_processing_stages.labels(
            stage=stage,
            intent=intent
        ).observe(duration)
        
    def record_model_performance(self, model: str, task: str, complexity: str, duration: float):
        """Record model performance metrics"""
        model_performance.labels(
            model=model,
            task=task,
            complexity=complexity
        ).observe(duration)
        
    def record_user_satisfaction(self, intent: str, response_type: str, score: float):
        """Record user satisfaction score"""
        user_satisfaction_score.labels(
            intent=intent,
            response_type=response_type
        ).observe(score)
        
    def record_response_quality(self, metric_type: str, intent: str, score: float):
        """Record response quality metrics"""
        response_quality_metrics.labels(
            metric_type=metric_type,
            intent=intent
        ).observe(score)
        
    def record_security_event(self, event_type: str, severity: str, source: str):
        """Record security event"""
        security_events.labels(
            event_type=event_type,
            severity=severity,
            source=source
        ).inc()
        
    def record_dependency_call(self, dependency: str, operation: str, status: str, duration: float):
        """Record dependency call metrics"""
        dependency_response_time.labels(
            dependency=dependency,
            operation=operation,
            status=status
        ).observe(duration)
        
    def update_dependency_availability(self, dependency: str, availability: float):
        """Update dependency availability"""
        dependency_availability.labels(dependency=dependency).set(availability)
        
    def record_circuit_breaker_state_change(self, breaker_name: str, from_state: str, to_state: str):
        """Record circuit breaker state change"""
        circuit_breaker_state_changes.labels(
            breaker_name=breaker_name,
            from_state=from_state,
            to_state=to_state
        ).inc()
        
    def update_circuit_breaker_failure_rate(self, breaker_name: str, failure_rate: float):
        """Update circuit breaker failure rate"""
        circuit_breaker_failure_rate.labels(breaker_name=breaker_name).set(failure_rate)
        
    def record_error_classification(self, error_type: str, severity: str, component: str):
        """Record error classification"""
        error_classification.labels(
            error_type=error_type,
            severity=severity,
            component=component
        ).inc()

# Performance monitoring decorators
def monitor_performance(complexity_level: str = "medium"):
    """Decorator to monitor function performance"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            intent = getattr(args[0], 'intent', 'unknown') if args else 'unknown'
            
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                
                metrics_collector.record_query_complexity(
                    complexity_level=complexity_level,
                    intent=intent,
                    duration=duration
                )
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                metrics_collector.record_error_classification(
                    error_type=type(e).__name__,
                    severity="error",
                    component=func.__module__
                )
                raise
                
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            intent = getattr(args[0], 'intent', 'unknown') if args else 'unknown'
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                metrics_collector.record_query_complexity(
                    complexity_level=complexity_level,
                    intent=intent,
                    duration=duration
                )
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                metrics_collector.record_error_classification(
                    error_type=type(e).__name__,
                    severity="error",
                    component=func.__module__
                )
                raise
                
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator

def monitor_processing_stage(stage: str):
    """Decorator to monitor processing stage performance"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            intent = getattr(args[0], 'intent', 'unknown') if args else 'unknown'
            
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                
                metrics_collector.record_processing_stage(
                    stage=stage,
                    intent=intent,
                    duration=duration
                )
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                metrics_collector.record_error_classification(
                    error_type=type(e).__name__,
                    severity="error",
                    component=stage
                )
                raise
                
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            intent = getattr(args[0], 'intent', 'unknown') if args else 'unknown'
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                metrics_collector.record_processing_stage(
                    stage=stage,
                    intent=intent,
                    duration=duration
                )
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                metrics_collector.record_error_classification(
                    error_type=type(e).__name__,
                    severity="error",
                    component=stage
                )
                raise
                
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator

# Background metrics collection
class BackgroundMetricsCollector:
    """Background task for collecting system metrics"""
    
    def __init__(self, interval: int = 30):
        self.interval = interval
        self.running = False
        self.collector = EnhancedMetricsCollector()
        
    async def start(self):
        """Start background metrics collection"""
        self.running = True
        while self.running:
            try:
                self.collector.collect_system_metrics()
                await asyncio.sleep(self.interval)
            except Exception as e:
                logger.error("background_metrics_collection_failed", error=str(e))
                await asyncio.sleep(self.interval)
                
    def stop(self):
        """Stop background metrics collection"""
        self.running = False

# Global metrics collector instance
metrics_collector = EnhancedMetricsCollector()
background_collector = BackgroundMetricsCollector()

# Export commonly used utilities
__all__ = [
    'EnhancedMetricsCollector',
    'BackgroundMetricsCollector',
    'monitor_performance',
    'monitor_processing_stage',
    'metrics_collector',
    'background_collector'
]