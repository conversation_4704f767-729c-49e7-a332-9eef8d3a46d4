# SLI/SLO Configuration for Query Intelligence Service
# Defines Service Level Indicators and Service Level Objectives

sli_definitions:
  # Availability SLI
  availability:
    name: "Service Availability"
    description: "Percentage of time the service is available and responding to requests"
    query: "up{job='query-intelligence'}"
    type: "availability"
    unit: "percentage"
    measurement_window: "1m"
    
  # Request Success Rate SLI
  request_success_rate:
    name: "Request Success Rate"
    description: "Percentage of successful requests out of total requests"
    query: |
      sum(rate(query_intelligence_queries_total{status="success"}[1m])) /
      sum(rate(query_intelligence_queries_total[1m])) * 100
    type: "success_rate"
    unit: "percentage"
    measurement_window: "1m"
    
  # Response Latency SLI
  response_latency_p95:
    name: "Response Latency P95"
    description: "95th percentile response latency"
    query: |
      histogram_quantile(0.95, 
        rate(query_intelligence_query_duration_seconds_bucket[1m])
      )
    type: "latency"
    unit: "seconds"
    measurement_window: "1m"
    
  response_latency_p99:
    name: "Response Latency P99"
    description: "99th percentile response latency"
    query: |
      histogram_quantile(0.99, 
        rate(query_intelligence_query_duration_seconds_bucket[1m])
      )
    type: "latency"
    unit: "seconds"
    measurement_window: "1m"
    
  # Throughput SLI
  throughput:
    name: "Request Throughput"
    description: "Number of requests per second"
    query: "sum(rate(query_intelligence_queries_total[1m]))"
    type: "throughput"
    unit: "requests_per_second"
    measurement_window: "1m"
    
  # Cache Hit Rate SLI
  cache_hit_rate:
    name: "Cache Hit Rate"
    description: "Percentage of cache hits out of total cache requests"
    query: |
      sum(rate(query_intelligence_cache_hits_total[1m])) /
      (sum(rate(query_intelligence_cache_hits_total[1m])) + 
       sum(rate(query_intelligence_cache_misses_total[1m]))) * 100
    type: "efficiency"
    unit: "percentage"
    measurement_window: "1m"
    
  # Error Budget SLI
  error_rate:
    name: "Error Rate"
    description: "Percentage of failed requests out of total requests"
    query: |
      sum(rate(query_intelligence_queries_total{status="error"}[1m])) /
      sum(rate(query_intelligence_queries_total[1m])) * 100
    type: "error_rate"
    unit: "percentage"
    measurement_window: "1m"
    
  # Dependency Health SLI
  dependency_availability:
    name: "Dependency Availability"
    description: "Percentage of healthy dependencies"
    query: |
      (sum(up{job=~"redis|analysis-engine|pattern-mining"}) /
       count(up{job=~"redis|analysis-engine|pattern-mining"})) * 100
    type: "availability"
    unit: "percentage"
    measurement_window: "1m"
    
  # Quality SLI
  response_confidence:
    name: "Response Confidence"
    description: "Average confidence score of responses"
    query: |
      avg(query_intelligence_response_confidence)
    type: "quality"
    unit: "score"
    measurement_window: "5m"

# SLO Definitions
slo_definitions:
  # Availability SLO
  availability_slo:
    name: "Service Availability SLO"
    description: "Service should be available 99.9% of the time"
    sli: "availability"
    target: 99.9
    operator: ">="
    time_window: "30d"
    error_budget: 0.1
    alerts:
      - name: "availability_slo_burn_rate_fast"
        condition: "burn_rate > 14.4"
        window: "1h"
        severity: "critical"
      - name: "availability_slo_burn_rate_slow"
        condition: "burn_rate > 1"
        window: "1d"
        severity: "warning"
        
  # Request Success Rate SLO
  success_rate_slo:
    name: "Request Success Rate SLO"
    description: "99.5% of requests should succeed"
    sli: "request_success_rate"
    target: 99.5
    operator: ">="
    time_window: "30d"
    error_budget: 0.5
    alerts:
      - name: "success_rate_slo_burn_rate_fast"
        condition: "burn_rate > 14.4"
        window: "1h"
        severity: "critical"
      - name: "success_rate_slo_burn_rate_slow"
        condition: "burn_rate > 1"
        window: "1d"
        severity: "warning"
        
  # Latency SLO
  latency_p95_slo:
    name: "Response Latency P95 SLO"
    description: "95% of requests should complete within 2 seconds"
    sli: "response_latency_p95"
    target: 2.0
    operator: "<="
    time_window: "30d"
    error_budget: 5.0
    alerts:
      - name: "latency_p95_slo_burn_rate_fast"
        condition: "burn_rate > 14.4"
        window: "1h"
        severity: "critical"
      - name: "latency_p95_slo_burn_rate_slow"
        condition: "burn_rate > 1"
        window: "1d"
        severity: "warning"
        
  latency_p99_slo:
    name: "Response Latency P99 SLO"
    description: "99% of requests should complete within 5 seconds"
    sli: "response_latency_p99"
    target: 5.0
    operator: "<="
    time_window: "30d"
    error_budget: 1.0
    alerts:
      - name: "latency_p99_slo_burn_rate_fast"
        condition: "burn_rate > 14.4"
        window: "1h"
        severity: "critical"
      - name: "latency_p99_slo_burn_rate_slow"
        condition: "burn_rate > 1"
        window: "1d"
        severity: "warning"
        
  # Throughput SLO
  throughput_slo:
    name: "Minimum Throughput SLO"
    description: "Service should handle at least 10 requests per second"
    sli: "throughput"
    target: 10.0
    operator: ">="
    time_window: "30d"
    error_budget: 2.0
    alerts:
      - name: "throughput_slo_burn_rate_fast"
        condition: "burn_rate > 14.4"
        window: "1h"
        severity: "warning"
      - name: "throughput_slo_burn_rate_slow"
        condition: "burn_rate > 1"
        window: "1d"
        severity: "info"
        
  # Cache Hit Rate SLO
  cache_hit_rate_slo:
    name: "Cache Hit Rate SLO"
    description: "Cache hit rate should be at least 80%"
    sli: "cache_hit_rate"
    target: 80.0
    operator: ">="
    time_window: "30d"
    error_budget: 10.0
    alerts:
      - name: "cache_hit_rate_slo_burn_rate_fast"
        condition: "burn_rate > 14.4"
        window: "1h"
        severity: "warning"
      - name: "cache_hit_rate_slo_burn_rate_slow"
        condition: "burn_rate > 1"
        window: "1d"
        severity: "info"
        
  # Error Rate SLO
  error_rate_slo:
    name: "Error Rate SLO"
    description: "Error rate should be less than 0.5%"
    sli: "error_rate"
    target: 0.5
    operator: "<="
    time_window: "30d"
    error_budget: 0.5
    alerts:
      - name: "error_rate_slo_burn_rate_fast"
        condition: "burn_rate > 14.4"
        window: "1h"
        severity: "critical"
      - name: "error_rate_slo_burn_rate_slow"
        condition: "burn_rate > 1"
        window: "1d"
        severity: "warning"
        
  # Dependency Health SLO
  dependency_availability_slo:
    name: "Dependency Availability SLO"
    description: "Dependencies should be available 99% of the time"
    sli: "dependency_availability"
    target: 99.0
    operator: ">="
    time_window: "30d"
    error_budget: 1.0
    alerts:
      - name: "dependency_availability_slo_burn_rate_fast"
        condition: "burn_rate > 14.4"
        window: "1h"
        severity: "critical"
      - name: "dependency_availability_slo_burn_rate_slow"
        condition: "burn_rate > 1"
        window: "1d"
        severity: "warning"
        
  # Quality SLO
  response_confidence_slo:
    name: "Response Confidence SLO"
    description: "Response confidence should be at least 0.8"
    sli: "response_confidence"
    target: 0.8
    operator: ">="
    time_window: "30d"
    error_budget: 0.1
    alerts:
      - name: "response_confidence_slo_burn_rate_fast"
        condition: "burn_rate > 14.4"
        window: "1h"
        severity: "warning"
      - name: "response_confidence_slo_burn_rate_slow"
        condition: "burn_rate > 1"
        window: "1d"
        severity: "info"

# Error Budget Policies
error_budget_policies:
  # Fast burn rate policy
  fast_burn_rate:
    name: "Fast Burn Rate Policy"
    description: "Triggered when error budget is consumed rapidly"
    burn_rate_threshold: 14.4
    window: "1h"
    actions:
      - stop_deployments
      - page_oncall
      - create_incident
      - escalate_to_manager
      
  # Slow burn rate policy
  slow_burn_rate:
    name: "Slow Burn Rate Policy"
    description: "Triggered when error budget is consumed slowly"
    burn_rate_threshold: 1.0
    window: "1d"
    actions:
      - alert_team
      - create_tracking_issue
      - schedule_postmortem
      
  # Error budget exhaustion policy
  budget_exhaustion:
    name: "Error Budget Exhaustion Policy"
    description: "Triggered when error budget is exhausted"
    remaining_budget_threshold: 0.0
    actions:
      - stop_deployments
      - emergency_response
      - executive_notification
      - implement_circuit_breaker

# SLO Reporting Configuration
reporting:
  # Dashboard configuration
  dashboards:
    - name: "SLO Overview Dashboard"
      url: "/dashboards/slo-overview"
      panels:
        - sli_current_values
        - slo_status
        - error_budget_remaining
        - burn_rate_trends
        
    - name: "SLO Detail Dashboard"
      url: "/dashboards/slo-detail"
      panels:
        - sli_time_series
        - slo_compliance_history
        - error_budget_consumption
        - alert_history
        
  # Report generation
  reports:
    - name: "Weekly SLO Report"
      frequency: "weekly"
      recipients: ["<EMAIL>", "<EMAIL>"]
      format: "html"
      
    - name: "Monthly SLO Report"
      frequency: "monthly"
      recipients: ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
      format: "pdf"
      
    - name: "Quarterly SLO Review"
      frequency: "quarterly"
      recipients: ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
      format: "presentation"

# SLO Monitoring Rules
monitoring_rules:
  # SLI calculation rules
  sli_calculation:
    - name: "availability_sli"
      expr: "up{job='query-intelligence'}"
      interval: "1m"
      
    - name: "success_rate_sli"
      expr: |
        sum(rate(query_intelligence_queries_total{status="success"}[1m])) /
        sum(rate(query_intelligence_queries_total[1m])) * 100
      interval: "1m"
      
    - name: "latency_p95_sli"
      expr: |
        histogram_quantile(0.95, 
          rate(query_intelligence_query_duration_seconds_bucket[1m])
        )
      interval: "1m"
      
  # Error budget burn rate calculation
  burn_rate_calculation:
    - name: "availability_burn_rate_1h"
      expr: |
        (1 - avg_over_time(availability_sli[1h])) / 
        (1 - 0.999) * 24 * 30
      interval: "1m"
      
    - name: "success_rate_burn_rate_1h"
      expr: |
        (1 - avg_over_time(success_rate_sli[1h]) / 100) / 
        (1 - 0.995) * 24 * 30
      interval: "1m"
      
    - name: "latency_p95_burn_rate_1h"
      expr: |
        (avg_over_time(latency_p95_sli[1h]) > 2.0) * 
        24 * 30
      interval: "1m"

# Integration Configuration
integrations:
  # Prometheus configuration
  prometheus:
    enabled: true
    recording_rules_file: "/etc/prometheus/slo-recording-rules.yml"
    alert_rules_file: "/etc/prometheus/slo-alert-rules.yml"
    
  # Grafana configuration
  grafana:
    enabled: true
    dashboard_folder: "SLO Dashboards"
    auto_import: true
    
  # Alertmanager configuration
  alertmanager:
    enabled: true
    slo_alerts_routing: "slo-alerts"
    
  # Custom webhook configuration
  webhooks:
    - name: "slo_webhook"
      url: "https://api.example.com/slo-webhook"
      events: ["slo_violation", "error_budget_exhaustion"]