groups:
  - name: query-intelligence-critical
    rules:
      - alert: QueryIntelligenceServiceDown
        expr: up{job="query-intelligence"} == 0
        for: 30s
        labels:
          severity: critical
          service: query-intelligence
          component: main
        annotations:
          summary: "Query Intelligence service is down"
          description: "Query Intelligence service has been down for more than 30 seconds"
          runbook_url: "https://docs.example.com/runbooks/service-down"
          
      - alert: QueryIntelligenceHighErrorRate
        expr: rate(query_intelligence_queries_total{status="error"}[5m]) / rate(query_intelligence_queries_total[5m]) * 100 > 10
        for: 2m
        labels:
          severity: critical
          service: query-intelligence
          component: api
        annotations:
          summary: "High error rate in Query Intelligence service"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"
          runbook_url: "https://docs.example.com/runbooks/high-error-rate"
          
      - alert: QueryIntelligenceHighLatency
        expr: histogram_quantile(0.95, rate(query_intelligence_query_duration_seconds_bucket[5m])) > 5
        for: 2m
        labels:
          severity: critical
          service: query-intelligence
          component: api
        annotations:
          summary: "High latency in Query Intelligence service"
          description: "95th percentile latency is {{ $value | humanizeDuration }} for the last 5 minutes"
          runbook_url: "https://docs.example.com/runbooks/high-latency"
          
      - alert: QueryIntelligenceCircuitBreakerOpen
        expr: circuit_breaker_state == 2
        for: 0s
        labels:
          severity: critical
          service: query-intelligence
          component: circuit-breaker
        annotations:
          summary: "Circuit breaker is open for {{ $labels.name }}"
          description: "Circuit breaker {{ $labels.name }} has been open, indicating service degradation"
          runbook_url: "https://docs.example.com/runbooks/circuit-breaker-open"
          
      - alert: QueryIntelligenceRedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
          service: query-intelligence
          component: redis
        annotations:
          summary: "Redis instance is down"
          description: "Redis instance used by Query Intelligence has been down for more than 1 minute"
          runbook_url: "https://docs.example.com/runbooks/redis-down"
          
      - alert: QueryIntelligenceAnalysisEngineDown
        expr: up{job="analysis-engine"} == 0
        for: 1m
        labels:
          severity: critical
          service: query-intelligence
          component: analysis-engine
        annotations:
          summary: "Analysis Engine is down"
          description: "Analysis Engine dependency has been down for more than 1 minute"
          runbook_url: "https://docs.example.com/runbooks/analysis-engine-down"

  - name: query-intelligence-warning
    rules:
      - alert: QueryIntelligenceHighLoad
        expr: rate(query_intelligence_queries_total[1m]) > 40
        for: 5m
        labels:
          severity: warning
          service: query-intelligence
          component: api
        annotations:
          summary: "High load on Query Intelligence service"
          description: "Request rate is {{ $value | humanize }} req/sec for the last 5 minutes"
          runbook_url: "https://docs.example.com/runbooks/high-load"
          
      - alert: QueryIntelligenceModerateLatency
        expr: histogram_quantile(0.95, rate(query_intelligence_query_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: query-intelligence
          component: api
        annotations:
          summary: "Moderate latency increase in Query Intelligence service"
          description: "95th percentile latency is {{ $value | humanizeDuration }} for the last 5 minutes"
          runbook_url: "https://docs.example.com/runbooks/moderate-latency"
          
      - alert: QueryIntelligenceLowCacheHitRate
        expr: rate(query_intelligence_cache_hits_total[5m]) / (rate(query_intelligence_cache_hits_total[5m]) + rate(query_intelligence_cache_misses_total[5m])) * 100 < 70
        for: 10m
        labels:
          severity: warning
          service: query-intelligence
          component: cache
        annotations:
          summary: "Low cache hit rate in Query Intelligence service"
          description: "Cache hit rate is {{ $value | humanizePercentage }} for the last 10 minutes"
          runbook_url: "https://docs.example.com/runbooks/low-cache-hit-rate"
          
      - alert: QueryIntelligenceHighLLMLatency
        expr: histogram_quantile(0.95, rate(query_intelligence_llm_request_duration_seconds_bucket[5m])) > 10
        for: 3m
        labels:
          severity: warning
          service: query-intelligence
          component: llm
        annotations:
          summary: "High LLM response latency"
          description: "95th percentile LLM latency is {{ $value | humanizeDuration }} for the last 5 minutes"
          runbook_url: "https://docs.example.com/runbooks/high-llm-latency"
          
      - alert: QueryIntelligenceHighActiveQueries
        expr: query_intelligence_active_queries > 30
        for: 5m
        labels:
          severity: warning
          service: query-intelligence
          component: api
        annotations:
          summary: "High number of active queries"
          description: "{{ $value }} active queries have been running for more than 5 minutes"
          runbook_url: "https://docs.example.com/runbooks/high-active-queries"
          
      - alert: QueryIntelligenceHighMemoryUsage
        expr: process_resident_memory_bytes{job="query-intelligence"} / 1024 / 1024 / 1024 > 1.5
        for: 10m
        labels:
          severity: warning
          service: query-intelligence
          component: memory
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value | humanize }}GB for the last 10 minutes"
          runbook_url: "https://docs.example.com/runbooks/high-memory-usage"
          
      - alert: QueryIntelligenceHighCPUUsage
        expr: rate(process_cpu_seconds_total{job="query-intelligence"}[5m]) * 100 > 80
        for: 10m
        labels:
          severity: warning
          service: query-intelligence
          component: cpu
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value | humanizePercentage }} for the last 10 minutes"
          runbook_url: "https://docs.example.com/runbooks/high-cpu-usage"

  - name: query-intelligence-info
    rules:
      - alert: QueryIntelligenceSlowResponses
        expr: histogram_quantile(0.95, rate(query_intelligence_query_duration_seconds_bucket[5m])) > 1
        for: 15m
        labels:
          severity: info
          service: query-intelligence
          component: api
        annotations:
          summary: "Slow responses detected"
          description: "95th percentile latency is {{ $value | humanizeDuration }} for the last 15 minutes"
          runbook_url: "https://docs.example.com/runbooks/slow-responses"
          
      - alert: QueryIntelligencePatternMiningDown
        expr: up{job="pattern-mining"} == 0
        for: 5m
        labels:
          severity: info
          service: query-intelligence
          component: pattern-mining
        annotations:
          summary: "Pattern Mining service is down"
          description: "Pattern Mining dependency has been down for more than 5 minutes"
          runbook_url: "https://docs.example.com/runbooks/pattern-mining-down"
          
      - alert: QueryIntelligenceLowThroughput
        expr: rate(query_intelligence_queries_total[5m]) < 1
        for: 30m
        labels:
          severity: info
          service: query-intelligence
          component: api
        annotations:
          summary: "Low throughput detected"
          description: "Request rate is {{ $value | humanize }} req/sec for the last 30 minutes"
          runbook_url: "https://docs.example.com/runbooks/low-throughput"
          
      - alert: QueryIntelligenceLowConfidenceScores
        expr: histogram_quantile(0.50, rate(query_intelligence_response_confidence_bucket[10m])) < 0.7
        for: 20m
        labels:
          severity: info
          service: query-intelligence
          component: ml
        annotations:
          summary: "Low confidence scores detected"
          description: "Median confidence score is {{ $value | humanizePercentage }} for the last 20 minutes"
          runbook_url: "https://docs.example.com/runbooks/low-confidence-scores"

  - name: query-intelligence-security
    rules:
      - alert: QueryIntelligenceHighRateLimitViolations
        expr: rate(http_requests_total{status="429"}[5m]) > 5
        for: 2m
        labels:
          severity: warning
          service: query-intelligence
          component: security
        annotations:
          summary: "High rate limit violations"
          description: "Rate limit violations are {{ $value | humanize }} per second for the last 5 minutes"
          runbook_url: "https://docs.example.com/runbooks/high-rate-limit-violations"
          
      - alert: QueryIntelligenceHighAuthFailures
        expr: rate(http_requests_total{path=~"/api/v1/auth.*", status=~"4.."}[5m]) > 2
        for: 3m
        labels:
          severity: warning
          service: query-intelligence
          component: security
        annotations:
          summary: "High authentication failures"
          description: "Authentication failures are {{ $value | humanize }} per second for the last 5 minutes"
          runbook_url: "https://docs.example.com/runbooks/high-auth-failures"
          
      - alert: QueryIntelligenceSuspiciousActivity
        expr: rate(query_intelligence_queries_total{intent="malicious"}[5m]) > 0.1
        for: 1m
        labels:
          severity: critical
          service: query-intelligence
          component: security
        annotations:
          summary: "Suspicious query activity detected"
          description: "Potential malicious queries detected at {{ $value | humanize }} per second"
          runbook_url: "https://docs.example.com/runbooks/suspicious-activity"

  - name: query-intelligence-business
    rules:
      - alert: QueryIntelligenceBusinessHourDowntime
        expr: up{job="query-intelligence"} == 0 and ON() hour() >= 9 and ON() hour() <= 17
        for: 1m
        labels:
          severity: critical
          service: query-intelligence
          component: business
        annotations:
          summary: "Service down during business hours"
          description: "Query Intelligence service is down during business hours"
          runbook_url: "https://docs.example.com/runbooks/business-hour-downtime"
          
      - alert: QueryIntelligenceWeekendHighUsage
        expr: rate(query_intelligence_queries_total[1h]) > 20 and ON() day_of_week() == 0 or ON() day_of_week() == 6
        for: 30m
        labels:
          severity: info
          service: query-intelligence
          component: business
        annotations:
          summary: "High usage during weekend"
          description: "Unusual high usage ({{ $value | humanize }} req/sec) detected during weekend"
          runbook_url: "https://docs.example.com/runbooks/weekend-high-usage"