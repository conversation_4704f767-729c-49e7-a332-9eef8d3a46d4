global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'password'
  
  # Slack configuration
  slack_api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
  
  # PagerDuty configuration
  pagerduty_url: 'https://events.pagerduty.com/v2/enqueue'

# Templates for notifications
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# Routing tree for alerts
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
    # Critical alerts - immediate notification
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      group_interval: 5m
      repeat_interval: 15m
      routes:
        # Business hours critical alerts
        - match:
            component: business
        - receiver: 'business-critical'
          group_wait: 0s
          repeat_interval: 5m
        # Security critical alerts
        - match:
            component: security
          receiver: 'security-critical'
          group_wait: 0s
          repeat_interval: 10m
    
    # Warning alerts - escalated notification
    - match:
        severity: warning
      receiver: 'warning-alerts'
      group_wait: 5m
      group_interval: 15m
      repeat_interval: 1h
      routes:
        # Security warnings
        - match:
            component: security
          receiver: 'security-warnings'
          repeat_interval: 30m
        # Performance warnings
        - match:
            component: api
          receiver: 'performance-warnings'
          repeat_interval: 45m
    
    # Info alerts - low priority notification
    - match:
        severity: info
      receiver: 'info-alerts'
      group_wait: 15m
      group_interval: 1h
      repeat_interval: 4h

# Inhibition rules to prevent alert spam
inhibit_rules:
  # If service is down, don't send component alerts
  - source_match:
      alertname: QueryIntelligenceServiceDown
    target_match:
      service: query-intelligence
    equal: ['service']
  
  # If circuit breaker is open, don't send high latency alerts
  - source_match:
      alertname: QueryIntelligenceCircuitBreakerOpen
    target_match:
      alertname: QueryIntelligenceHighLatency
    equal: ['service']
  
  # If Redis is down, don't send cache-related alerts
  - source_match:
      alertname: QueryIntelligenceRedisDown
    target_match:
      component: cache
    equal: ['service']

# Notification receivers
receivers:
  - name: 'web.hook'
    webhook_configs:
      - url: 'http://127.0.0.1:5001/'

  - name: 'critical-alerts'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#alerts-critical'
        title: 'CRITICAL: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        text: '{{ range .Alerts }}{{ .Annotations.description }}{{ end }}'
        color: 'danger'
        send_resolved: true
        actions:
          - type: button
            text: 'Runbook'
            url: '{{ range .Alerts }}{{ .Annotations.runbook_url }}{{ end }}'
          - type: button
            text: 'Silence'
            url: '{{ template "slack.default.silence_url" . }}'
    email_configs:
      - to: '<EMAIL>'
        subject: 'CRITICAL: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Runbook: {{ .Annotations.runbook_url }}
          {{ end }}
    pagerduty_configs:
      - routing_key: 'YOUR_PAGERDUTY_INTEGRATION_KEY'
        description: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        
  - name: 'business-critical'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#alerts-business'
        title: 'BUSINESS CRITICAL: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        text: '{{ range .Alerts }}{{ .Annotations.description }}{{ end }}'
        color: 'danger'
        send_resolved: true
    email_configs:
      - to: '<EMAIL>'
        subject: 'BUSINESS CRITICAL: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
    pagerduty_configs:
      - routing_key: 'YOUR_BUSINESS_PAGERDUTY_KEY'
        description: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        
  - name: 'security-critical'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#security-alerts'
        title: 'SECURITY CRITICAL: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        text: '{{ range .Alerts }}{{ .Annotations.description }}{{ end }}'
        color: 'danger'
        send_resolved: true
    email_configs:
      - to: '<EMAIL>'
        subject: 'SECURITY CRITICAL: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
    pagerduty_configs:
      - routing_key: 'YOUR_SECURITY_PAGERDUTY_KEY'
        description: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

  - name: 'warning-alerts'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#alerts-warnings'
        title: 'WARNING: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        text: '{{ range .Alerts }}{{ .Annotations.description }}{{ end }}'
        color: 'warning'
        send_resolved: true
    email_configs:
      - to: '<EMAIL>'
        subject: 'WARNING: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        
  - name: 'security-warnings'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#security-warnings'
        title: 'SECURITY WARNING: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        text: '{{ range .Alerts }}{{ .Annotations.description }}{{ end }}'
        color: 'warning'
        send_resolved: true
    email_configs:
      - to: '<EMAIL>'
        subject: 'SECURITY WARNING: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        
  - name: 'performance-warnings'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#performance-alerts'
        title: 'PERFORMANCE WARNING: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        text: '{{ range .Alerts }}{{ .Annotations.description }}{{ end }}'
        color: 'warning'
        send_resolved: true

  - name: 'info-alerts'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#alerts-info'
        title: 'INFO: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        text: '{{ range .Alerts }}{{ .Annotations.description }}{{ end }}'
        color: 'good'
        send_resolved: true