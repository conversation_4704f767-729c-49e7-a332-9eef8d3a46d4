# Escalation Procedures for Query Intelligence Service
# This file defines the escalation matrix and procedures for different alert types

escalation_matrix:
  critical:
    # Immediate escalation for critical alerts
    level_1:
      timeout: 5m
      contacts:
        - type: pagerduty
          integration_key: "PRIMARY_ONCALL_KEY"
        - type: slack
          channel: "#alerts-critical"
        - type: email
          recipients: ["<EMAIL>"]
    
    level_2:
      timeout: 15m
      contacts:
        - type: pagerduty
          integration_key: "SECONDARY_ONCALL_KEY"
        - type: slack
          channel: "#alerts-critical"
          mention: "@channel"
        - type: email
          recipients: ["<EMAIL>", "<EMAIL>"]
    
    level_3:
      timeout: 30m
      contacts:
        - type: pagerduty
          integration_key: "MANAGER_ONCALL_KEY"
        - type: slack
          channel: "#alerts-critical"
          mention: "@engineering-managers"
        - type: email
          recipients: ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
    
    level_4:
      timeout: 60m
      contacts:
        - type: phone
          numbers: ["******-0123", "******-0124"]
        - type: slack
          channel: "#alerts-critical"
          mention: "@here"
        - type: email
          recipients: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]

  warning:
    # Escalation for warning alerts
    level_1:
      timeout: 30m
      contacts:
        - type: slack
          channel: "#alerts-warnings"
        - type: email
          recipients: ["<EMAIL>"]
    
    level_2:
      timeout: 2h
      contacts:
        - type: slack
          channel: "#alerts-warnings"
          mention: "@team"
        - type: email
          recipients: ["<EMAIL>", "<EMAIL>"]
    
    level_3:
      timeout: 4h
      contacts:
        - type: pagerduty
          integration_key: "SECONDARY_ONCALL_KEY"
        - type: slack
          channel: "#alerts-warnings"
          mention: "@team-lead"
        - type: email
          recipients: ["<EMAIL>", "<EMAIL>", "<EMAIL>"]

  info:
    # Escalation for info alerts
    level_1:
      timeout: 4h
      contacts:
        - type: slack
          channel: "#alerts-info"
        - type: email
          recipients: ["<EMAIL>"]
    
    level_2:
      timeout: 24h
      contacts:
        - type: slack
          channel: "#alerts-info"
          mention: "@team"
        - type: email
          recipients: ["<EMAIL>", "<EMAIL>"]

# Special escalation procedures for specific alert types
special_procedures:
  security:
    # Security alerts bypass normal escalation
    immediate_contacts:
      - type: pagerduty
        integration_key: "SECURITY_TEAM_KEY"
      - type: slack
        channel: "#security-alerts"
        mention: "@security-team"
      - type: email
        recipients: ["<EMAIL>", "<EMAIL>"]
    
    escalation_override: true
    notification_delay: 0s
    
  business_critical:
    # Business critical alerts during business hours
    business_hours:
      timezone: "America/New_York"
      start: "09:00"
      end: "17:00"
      days: ["monday", "tuesday", "wednesday", "thursday", "friday"]
    
    business_hours_escalation:
      level_1:
        timeout: 2m
        contacts:
          - type: pagerduty
            integration_key: "BUSINESS_ONCALL_KEY"
          - type: slack
            channel: "#business-critical"
            mention: "@business-oncall"
      
      level_2:
        timeout: 5m
        contacts:
          - type: phone
            numbers: ["******-0125"]
          - type: slack
            channel: "#business-critical"
            mention: "@business-managers"
    
    after_hours_escalation:
      level_1:
        timeout: 15m
        contacts:
          - type: slack
            channel: "#business-critical"
          - type: email
            recipients: ["<EMAIL>"]

# Suppression rules to prevent alert fatigue
suppression_rules:
  - name: "service_down_suppression"
    description: "Suppress component alerts when service is down"
    conditions:
      - alert: "QueryIntelligenceServiceDown"
        state: "firing"
    suppress_alerts:
      - pattern: "QueryIntelligence.*"
        exceptions: ["QueryIntelligenceServiceDown"]
    
  - name: "circuit_breaker_suppression"
    description: "Suppress latency alerts when circuit breaker is open"
    conditions:
      - alert: "QueryIntelligenceCircuitBreakerOpen"
        state: "firing"
    suppress_alerts:
      - pattern: "QueryIntelligence.*Latency.*"
    
  - name: "dependency_down_suppression"
    description: "Suppress related alerts when dependencies are down"
    conditions:
      - alert: "QueryIntelligenceRedisDown"
        state: "firing"
    suppress_alerts:
      - pattern: "QueryIntelligence.*Cache.*"
    
  - name: "maintenance_window_suppression"
    description: "Suppress alerts during maintenance windows"
    conditions:
      - annotation: "maintenance_window"
        value: "true"
    suppress_alerts:
      - pattern: "QueryIntelligence.*"
        exceptions: ["QueryIntelligenceSuspiciousActivity"]

# Auto-resolution rules
auto_resolution:
  - name: "transient_issues"
    description: "Auto-resolve transient issues that self-correct"
    conditions:
      - alert_duration: "< 5m"
      - resolution_time: "< 2m"
    auto_resolve_after: "10m"
    
  - name: "dependency_recovery"
    description: "Auto-resolve dependency alerts when service recovers"
    conditions:
      - alert: "QueryIntelligenceRedisDown"
        state: "resolved"
    auto_resolve_alerts:
      - pattern: "QueryIntelligence.*Cache.*"
    
# Notification templates
notification_templates:
  critical_template: |
    🚨 **CRITICAL ALERT** 🚨
    
    **Service**: {{ .CommonLabels.service }}
    **Component**: {{ .CommonLabels.component }}
    **Summary**: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}
    **Description**: {{ range .Alerts }}{{ .Annotations.description }}{{ end }}
    
    **Runbook**: {{ range .Alerts }}{{ .Annotations.runbook_url }}{{ end }}
    **Silence**: {{ template "slack.default.silence_url" . }}
    
    **Escalation**: This alert will escalate in 5 minutes if not acknowledged.
    
  warning_template: |
    ⚠️ **WARNING ALERT** ⚠️
    
    **Service**: {{ .CommonLabels.service }}
    **Component**: {{ .CommonLabels.component }}
    **Summary**: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}
    **Description**: {{ range .Alerts }}{{ .Annotations.description }}{{ end }}
    
    **Runbook**: {{ range .Alerts }}{{ .Annotations.runbook_url }}{{ end }}
    
    **Escalation**: This alert will escalate in 30 minutes if not resolved.
    
  info_template: |
    ℹ️ **INFO ALERT** ℹ️
    
    **Service**: {{ .CommonLabels.service }}
    **Component**: {{ .CommonLabels.component }}
    **Summary**: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}
    **Description**: {{ range .Alerts }}{{ .Annotations.description }}{{ end }}
    
    **Note**: This is an informational alert that requires attention but is not urgent.

# Escalation configuration
escalation_config:
  enabled: true
  check_interval: "30s"
  ack_timeout: "5m"
  
  # Escalation conditions
  conditions:
    critical_unacknowledged: "5m"
    warning_unacknowledged: "30m"
    info_unacknowledged: "4h"
    
  # De-escalation conditions
  de_escalation:
    enabled: true
    resolved_timeout: "10m"
    acknowledged_timeout: "1h"