# Monitoring System Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the comprehensive monitoring and alerting system for the Query Intelligence Service.

## Prerequisites

### Required Tools
- `kubectl` configured for your cluster
- `helm` v3.x
- `prometheus-operator` (if using Kubernetes)
- `grafana` deployment access
- `alertmanager` deployment access

### Required Permissions
- Cluster admin access for Kubernetes deployments
- Grafana admin access for dashboard imports
- Alertmanager configuration access
- Service account creation permissions

### Infrastructure Requirements
- Kubernetes cluster with adequate resources
- Persistent storage for Prometheus data
- Load balancer for external access
- DNS configuration for service discovery

## Step 1: Deploy Core Infrastructure

### 1.1 Deploy Prometheus

```bash
# Create namespace
kubectl create namespace monitoring

# Deploy Prometheus using Helm
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update

# Install Prometheus
helm install prometheus prometheus-community/kube-prometheus-stack \
  --namespace monitoring \
  --values monitoring/infrastructure/prometheus/values.yaml \
  --wait
```

### 1.2 Deploy <PERSON>ana

```bash
# Deploy <PERSON>ana (if not included in kube-prometheus-stack)
helm install grafana grafana/grafana \
  --namespace monitoring \
  --values monitoring/infrastructure/grafana/values.yaml \
  --wait
```

### 1.3 Deploy Alertmanager

```bash
# Apply Alertmanager configuration
kubectl apply -f monitoring/alerting/alertmanager-config.yml -n monitoring

# Restart Alertmanager to pick up configuration
kubectl rollout restart deployment/alertmanager -n monitoring
```

## Step 2: Configure Prometheus

### 2.1 Apply Recording Rules

```bash
# Apply SLI/SLO recording rules
kubectl apply -f monitoring/observability/sli-slo-recording-rules.yml -n monitoring

# Apply custom recording rules
kubectl apply -f monitoring/infrastructure/prometheus/recording-rules.yml -n monitoring
```

### 2.2 Apply Alert Rules

```bash
# Apply alert rules
kubectl apply -f monitoring/alerting/prometheus-alerts.yml -n monitoring

# Verify rules are loaded
kubectl exec -n monitoring prometheus-0 -- promtool rules
```

### 2.3 Configure Service Discovery

```bash
# Apply service monitor configurations
kubectl apply -f monitoring/infrastructure/prometheus/service-monitors.yml -n monitoring

# Verify service discovery
kubectl get servicemonitors -n monitoring
```

## Step 3: Import Dashboards

### 3.1 Import Grafana Dashboards

```bash
# Make the import script executable
chmod +x scripts/import-dashboards.sh

# Run the import script
./scripts/import-dashboards.sh

# Verify dashboard import
curl -H "Authorization: Bearer $GRAFANA_API_KEY" \
  http://grafana.monitoring.svc.cluster.local:3000/api/dashboards
```

### 3.2 Configure Dashboard Permissions

```bash
# Create dashboard folders
curl -X POST \
  -H "Authorization: Bearer $GRAFANA_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"title":"Query Intelligence","uid":"query-intelligence"}' \
  http://grafana.monitoring.svc.cluster.local:3000/api/folders

# Set folder permissions
curl -X POST \
  -H "Authorization: Bearer $GRAFANA_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"items":[{"role":"Editor","permission":2}]}' \
  http://grafana.monitoring.svc.cluster.local:3000/api/folders/query-intelligence/permissions
```

## Step 4: Configure Alerting

### 4.1 Setup Notification Channels

```bash
# Create Slack notification channel
curl -X POST \
  -H "Authorization: Bearer $GRAFANA_API_KEY" \
  -H "Content-Type: application/json" \
  -d @monitoring/alerting/slack-channel.json \
  http://grafana.monitoring.svc.cluster.local:3000/api/alert-notifications

# Create email notification channel
curl -X POST \
  -H "Authorization: Bearer $GRAFANA_API_KEY" \
  -H "Content-Type: application/json" \
  -d @monitoring/alerting/email-channel.json \
  http://grafana.monitoring.svc.cluster.local:3000/api/alert-notifications
```

### 4.2 Configure PagerDuty Integration

```bash
# Create PagerDuty integration
kubectl create secret generic pagerduty-config \
  --from-literal=integration-key=$PAGERDUTY_INTEGRATION_KEY \
  -n monitoring

# Apply PagerDuty webhook configuration
kubectl apply -f monitoring/alerting/pagerduty-webhook.yml -n monitoring
```

### 4.3 Test Alerting

```bash
# Test alert firing
kubectl apply -f monitoring/testing/test-alerts.yml -n monitoring

# Verify alert delivery
kubectl logs -n monitoring alertmanager-0 | grep "notification sent"
```

## Step 5: Enable Distributed Tracing

### 5.1 Deploy Jaeger

```bash
# Deploy Jaeger operator
kubectl apply -f monitoring/infrastructure/jaeger/jaeger-operator.yml -n monitoring

# Deploy Jaeger instance
kubectl apply -f monitoring/infrastructure/jaeger/jaeger-instance.yml -n monitoring
```

### 5.2 Configure OpenTelemetry

```bash
# Deploy OpenTelemetry Collector
kubectl apply -f monitoring/infrastructure/otel/otel-collector.yml -n monitoring

# Configure application tracing
kubectl apply -f monitoring/observability/tracing-config.yml -n monitoring
```

### 5.3 Enable Application Tracing

```python
# Add to your application
from monitoring.observability.tracing_config import tracing_config

# Initialize tracing in your main application
tracer = tracing_config.setup_tracing()
```

## Step 6: Configure SLI/SLO Monitoring

### 6.1 Deploy SLO Configuration

```bash
# Apply SLI/SLO configuration
kubectl apply -f monitoring/observability/sli-slo-config.yml -n monitoring

# Create SLO dashboards
./scripts/setup-slo-monitoring.sh
```

### 6.2 Configure Error Budget Alerting

```bash
# Apply error budget alert rules
kubectl apply -f monitoring/observability/error-budget-alerts.yml -n monitoring

# Configure error budget webhook
kubectl apply -f monitoring/observability/error-budget-webhook.yml -n monitoring
```

## Step 7: Configure External Integrations

### 7.1 Slack Integration

```bash
# Create Slack webhook secret
kubectl create secret generic slack-webhook \
  --from-literal=webhook-url=$SLACK_WEBHOOK_URL \
  -n monitoring

# Apply Slack integration configuration
kubectl apply -f monitoring/integrations/slack-integration.yml -n monitoring
```

### 7.2 Email Integration

```bash
# Create email configuration
kubectl create secret generic email-config \
  --from-literal=smtp-host=$SMTP_HOST \
  --from-literal=smtp-port=$SMTP_PORT \
  --from-literal=smtp-username=$SMTP_USERNAME \
  --from-literal=smtp-password=$SMTP_PASSWORD \
  -n monitoring

# Apply email integration
kubectl apply -f monitoring/integrations/email-integration.yml -n monitoring
```

## Step 8: Enable Enhanced Metrics

### 8.1 Deploy Enhanced Metrics Collector

```bash
# Deploy enhanced metrics collector
kubectl apply -f monitoring/observability/enhanced-metrics-collector.yml -n monitoring

# Configure custom metrics
kubectl apply -f monitoring/observability/custom-metrics.yml -n monitoring
```

### 8.2 Configure Background Metrics Collection

```python
# Add to your application startup
from monitoring.observability.enhanced_metrics import background_collector

# Start background collection
await background_collector.start()
```

## Step 9: Validation and Testing

### 9.1 Verify Prometheus Targets

```bash
# Check Prometheus targets
kubectl port-forward -n monitoring svc/prometheus-operated 9090:9090

# Open browser to http://localhost:9090/targets
# Verify all targets are "UP"
```

### 9.2 Verify Grafana Dashboards

```bash
# Get Grafana admin password
kubectl get secret -n monitoring grafana-admin -o jsonpath="{.data.admin-password}" | base64 -d

# Access Grafana
kubectl port-forward -n monitoring svc/grafana 3000:3000

# Open browser to http://localhost:3000
# Verify all dashboards are accessible
```

### 9.3 Test Alerting

```bash
# Create test alert
kubectl apply -f monitoring/testing/test-alert.yml -n monitoring

# Verify alert fires
kubectl logs -n monitoring alertmanager-0 | grep "test-alert"

# Check notification delivery
# Verify Slack/email/PagerDuty notifications
```

### 9.4 Verify Tracing

```bash
# Access Jaeger UI
kubectl port-forward -n monitoring svc/jaeger-query 16686:16686

# Open browser to http://localhost:16686
# Verify traces are being collected
```

## Step 10: Configure Backup and Recovery

### 10.1 Setup Prometheus Backup

```bash
# Deploy Prometheus backup
kubectl apply -f monitoring/infrastructure/prometheus/backup.yml -n monitoring

# Configure backup schedule
kubectl apply -f monitoring/infrastructure/prometheus/backup-schedule.yml -n monitoring
```

### 10.2 Setup Grafana Backup

```bash
# Deploy Grafana backup
kubectl apply -f monitoring/infrastructure/grafana/backup.yml -n monitoring

# Configure dashboard backup
kubectl apply -f monitoring/infrastructure/grafana/dashboard-backup.yml -n monitoring
```

## Step 11: Configure Monitoring for Monitoring

### 11.1 Monitor Prometheus

```bash
# Apply Prometheus self-monitoring
kubectl apply -f monitoring/infrastructure/prometheus/self-monitoring.yml -n monitoring

# Configure Prometheus alerts
kubectl apply -f monitoring/infrastructure/prometheus/meta-alerts.yml -n monitoring
```

### 11.2 Monitor Grafana

```bash
# Apply Grafana monitoring
kubectl apply -f monitoring/infrastructure/grafana/monitoring.yml -n monitoring

# Configure Grafana health checks
kubectl apply -f monitoring/infrastructure/grafana/health-checks.yml -n monitoring
```

## Post-Deployment Configuration

### 1. Configure Data Retention

```bash
# Configure Prometheus retention
kubectl patch prometheus prometheus-operated -n monitoring --type='merge' -p='{"spec":{"retention":"30d"}}'

# Configure Jaeger retention
kubectl patch jaeger jaeger -n monitoring --type='merge' -p='{"spec":{"storage":{"options":{"es":{"index-cleaner":{"enabled":true,"number-of-days":7}}}}}}'
```

### 2. Configure Resource Limits

```bash
# Apply resource limits
kubectl apply -f monitoring/infrastructure/resource-limits.yml -n monitoring

# Configure auto-scaling
kubectl apply -f monitoring/infrastructure/hpa.yml -n monitoring
```

### 3. Configure Security

```bash
# Apply network policies
kubectl apply -f monitoring/infrastructure/network-policies.yml -n monitoring

# Configure RBAC
kubectl apply -f monitoring/infrastructure/rbac.yml -n monitoring
```

## Troubleshooting Deployment

### Common Issues

#### Prometheus Not Starting
```bash
# Check Prometheus logs
kubectl logs -n monitoring prometheus-0

# Check configuration
kubectl exec -n monitoring prometheus-0 -- promtool check config /etc/prometheus/prometheus.yml
```

#### Grafana Dashboards Not Loading
```bash
# Check Grafana logs
kubectl logs -n monitoring grafana-0

# Verify data source connection
kubectl exec -n monitoring grafana-0 -- grafana-cli admin data-sources list
```

#### Alerts Not Firing
```bash
# Check Alertmanager logs
kubectl logs -n monitoring alertmanager-0

# Verify alert rules
kubectl exec -n monitoring prometheus-0 -- promtool rules
```

#### Tracing Not Working
```bash
# Check Jaeger logs
kubectl logs -n monitoring jaeger-collector-0

# Verify OpenTelemetry collector
kubectl logs -n monitoring otel-collector-0
```

## Verification Checklist

- [ ] Prometheus is collecting metrics from all targets
- [ ] Grafana dashboards are accessible and displaying data
- [ ] Alert rules are loaded and can fire
- [ ] Alertmanager is routing notifications correctly
- [ ] Tracing is collecting spans from the application
- [ ] SLI/SLO metrics are being calculated
- [ ] External integrations are working
- [ ] Backup and monitoring are configured
- [ ] Resource limits and security are in place

## Next Steps

After successful deployment:
1. Configure user access and permissions
2. Set up operational procedures
3. Train team on monitoring tools
4. Establish incident response procedures
5. Schedule regular review and maintenance

## Support

For deployment issues:
- Check the troubleshooting guide
- Review logs for error messages
- Contact the platform engineering team
- Create an issue in the monitoring repository

---

**Version**: 1.0.0  
**Last Updated**: 2025-07-14  
**Maintained by**: Platform Engineering Team