{"dashboard": {"id": null, "title": "Query Intelligence - Technical Performance Dashboard", "description": "Detailed technical performance metrics for engineering teams", "tags": ["query-intelligence", "technical", "performance"], "style": "dark", "timezone": "browser", "editable": true, "graphTooltip": 0, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m"]}, "refresh": "10s", "panels": [{"id": 1, "title": "Request Rate", "type": "timeseries", "targets": [{"expr": "rate(query_intelligence_queries_total[1m])", "legendFormat": "Total Requests/sec", "refId": "A"}, {"expr": "rate(query_intelligence_queries_total{status=\"success\"}[1m])", "legendFormat": "Successful Requests/sec", "refId": "B"}, {"expr": "rate(query_intelligence_queries_total{status=\"error\"}[1m])", "legendFormat": "Failed Requests/sec", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Response Time Percentiles", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.50, rate(query_intelligence_query_duration_seconds_bucket[1m]))", "legendFormat": "50th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(query_intelligence_query_duration_seconds_bucket[1m]))", "legendFormat": "95th percentile", "refId": "B"}, {"expr": "histogram_quantile(0.99, rate(query_intelligence_query_duration_seconds_bucket[1m]))", "legendFormat": "99th percentile", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "s"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Active Queries", "type": "timeseries", "targets": [{"expr": "query_intelligence_active_queries", "legendFormat": "Active Queries", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "LLM Performance", "type": "timeseries", "targets": [{"expr": "rate(query_intelligence_llm_requests_total[1m]) by (model)", "legendFormat": "{{model}} requests/sec", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "Token Usage", "type": "timeseries", "targets": [{"expr": "rate(query_intelligence_llm_tokens_total{token_type=\"prompt\"}[1m])", "legendFormat": "Prompt tokens/sec", "refId": "A"}, {"expr": "rate(query_intelligence_llm_tokens_total{token_type=\"completion\"}[1m])", "legendFormat": "Completion tokens/sec", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 6, "title": "Cache Performance Details", "type": "timeseries", "targets": [{"expr": "rate(query_intelligence_cache_hits_total[1m])", "legendFormat": "Cache hits/sec", "refId": "A"}, {"expr": "rate(query_intelligence_cache_misses_total[1m])", "legendFormat": "<PERSON><PERSON> misses/sec", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 7, "title": "Embedding Generation Performance", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.50, rate(query_intelligence_embedding_duration_seconds_bucket[1m]))", "legendFormat": "50th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(query_intelligence_embedding_duration_seconds_bucket[1m]))", "legendFormat": "95th percentile", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "s"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 8, "title": "Search Results Distribution", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.50, rate(query_intelligence_search_results_count_bucket[1m]))", "legendFormat": "50th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(query_intelligence_search_results_count_bucket[1m]))", "legendFormat": "95th percentile", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}, {"id": 9, "title": "Response Confidence Scores", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.50, rate(query_intelligence_response_confidence_bucket[1m])) by (intent)", "legendFormat": "{{intent}} (50th percentile)", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(query_intelligence_response_confidence_bucket[1m])) by (intent)", "legendFormat": "{{intent}} (95th percentile)", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}}, {"id": 10, "title": "Circuit Breaker Status", "type": "table", "targets": [{"expr": "circuit_breaker_state", "legendFormat": "Circuit Breaker Status", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 2}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}}]}}