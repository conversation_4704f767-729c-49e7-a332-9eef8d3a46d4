{"dashboard": {"id": null, "title": "Query Intelligence - Security Monitoring Dashboard", "description": "Security-focused monitoring for threat detection and compliance", "tags": ["query-intelligence", "security", "compliance"], "style": "dark", "timezone": "browser", "editable": true, "graphTooltip": 0, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m"]}, "refresh": "10s", "panels": [{"id": 1, "title": "Authentication Attempts", "type": "timeseries", "targets": [{"expr": "rate(http_requests_total{path=~\"/api/v1/auth.*\", status=~\"2..\"}[1m])", "legendFormat": "Successful <PERSON><PERSON>", "refId": "A"}, {"expr": "rate(http_requests_total{path=~\"/api/v1/auth.*\", status=~\"4..\"}[1m])", "legendFormat": "Failed Auth", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Rate Limiting Violations", "type": "timeseries", "targets": [{"expr": "rate(http_requests_total{status=\"429\"}[1m])", "legendFormat": "Rate Limited Requests", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "HTTP Error Rates by Status", "type": "timeseries", "targets": [{"expr": "rate(http_requests_total{status=~\"4..\"}[1m]) by (status)", "legendFormat": "{{status}} errors/sec", "refId": "A"}, {"expr": "rate(http_requests_total{status=~\"5..\"}[1m]) by (status)", "legendFormat": "{{status}} errors/sec", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Suspicious Request Patterns", "type": "timeseries", "targets": [{"expr": "rate(http_requests_total{method=\"POST\", path=~\"/api/v1/query\", status=~\"4..\"}[1m])", "legendFormat": "Failed Query Requests", "refId": "A"}, {"expr": "rate(http_requests_total{method=\"GET\", path=~\"/api/v1/admin.*\", status=~\"4..\"}[1m])", "legendFormat": "Failed Admin Access", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "Request Size Distribution", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.50, rate(http_request_size_bytes_bucket[1m]))", "legendFormat": "50th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(http_request_size_bytes_bucket[1m]))", "legendFormat": "95th percentile", "refId": "B"}, {"expr": "histogram_quantile(0.99, rate(http_request_size_bytes_bucket[1m]))", "legendFormat": "99th percentile", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "bytes"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 6, "title": "Response Size Distribution", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.50, rate(http_response_size_bytes_bucket[1m]))", "legendFormat": "50th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(http_response_size_bytes_bucket[1m]))", "legendFormat": "95th percentile", "refId": "B"}, {"expr": "histogram_quantile(0.99, rate(http_response_size_bytes_bucket[1m]))", "legendFormat": "99th percentile", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "bytes"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 7, "title": "Geographic Request Distribution", "type": "table", "targets": [{"expr": "sum(increase(http_requests_total[1h])) by (country)", "legendFormat": "Requests by Country", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 8, "title": "Top User Agents", "type": "table", "targets": [{"expr": "topk(10, sum(increase(http_requests_total[1h])) by (user_agent))", "legendFormat": "Top User Agents", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}, {"id": 9, "title": "Query Content Analysis", "type": "timeseries", "targets": [{"expr": "rate(query_intelligence_queries_total{intent=\"malicious\"}[1m])", "legendFormat": "Potential Malicious Queries", "refId": "A"}, {"expr": "rate(query_intelligence_queries_total{intent=\"injection\"}[1m])", "legendFormat": "Potential Injection Attempts", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}}, {"id": 10, "title": "SSL/TLS Certificate Status", "type": "stat", "targets": [{"expr": "probe_ssl_earliest_cert_expiry", "legendFormat": "Certificate Expiry", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "dtdurations", "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 7}, {"color": "green", "value": 30}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}}, {"id": 11, "title": "Security Alerts <PERSON>", "type": "table", "targets": [{"expr": "ALERTS{alertstate=\"firing\", severity=~\"warning|critical\"}", "legendFormat": "Active Security Alerts", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 40}}]}}