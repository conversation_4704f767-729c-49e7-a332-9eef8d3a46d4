{"dashboard": {"id": null, "title": "Query Intelligence - Capacity Planning Dashboard", "description": "Resource usage and scaling metrics for capacity planning", "tags": ["query-intelligence", "capacity", "scaling", "resources"], "style": "dark", "timezone": "browser", "editable": true, "graphTooltip": 0, "time": {"from": "now-24h", "to": "now"}, "timepicker": {"refresh_intervals": ["1m", "5m", "15m", "30m", "1h", "2h", "6h", "12h", "1d"]}, "refresh": "1m", "panels": [{"id": 1, "title": "CPU Usage", "type": "timeseries", "targets": [{"expr": "rate(process_cpu_seconds_total{job=\"query-intelligence\"}[1m]) * 100", "legendFormat": "CPU Usage %", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "percent", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 70}, {"color": "red", "value": 90}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Memory Usage", "type": "timeseries", "targets": [{"expr": "process_resident_memory_bytes{job=\"query-intelligence\"} / 1024 / 1024", "legendFormat": "Memory Usage (MB)", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "decbytes", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1073741824}, {"color": "red", "value": 2147483648}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Request Queue Length", "type": "timeseries", "targets": [{"expr": "query_intelligence_active_queries", "legendFormat": "Active Queries", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Throughput Capacity", "type": "timeseries", "targets": [{"expr": "rate(query_intelligence_queries_total[1m])", "legendFormat": "Current Throughput", "refId": "A"}, {"expr": "scalar(50)", "legendFormat": "Target Capacity", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "Response Time vs Load", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.95, rate(query_intelligence_query_duration_seconds_bucket[1m]))", "legendFormat": "95th percentile response time", "refId": "A"}, {"expr": "rate(query_intelligence_queries_total[1m])", "legendFormat": "Request rate", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 6, "title": "Error Rate vs Load", "type": "timeseries", "targets": [{"expr": "rate(query_intelligence_queries_total{status=\"error\"}[1m]) / rate(query_intelligence_queries_total[1m]) * 100", "legendFormat": "Error Rate %", "refId": "A"}, {"expr": "rate(query_intelligence_queries_total[1m])", "legendFormat": "Request rate", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 7, "title": "Cache Efficiency vs Load", "type": "timeseries", "targets": [{"expr": "rate(query_intelligence_cache_hits_total[1m]) / (rate(query_intelligence_cache_hits_total[1m]) + rate(query_intelligence_cache_misses_total[1m])) * 100", "legendFormat": "Cache Hit Rate %", "refId": "A"}, {"expr": "rate(query_intelligence_queries_total[1m])", "legendFormat": "Request rate", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 8, "title": "Resource Utilization Trends", "type": "timeseries", "targets": [{"expr": "rate(process_cpu_seconds_total{job=\"query-intelligence\"}[1m]) * 100", "legendFormat": "CPU Usage %", "refId": "A"}, {"expr": "process_resident_memory_bytes{job=\"query-intelligence\"} / process_virtual_memory_max_bytes{job=\"query-intelligence\"} * 100", "legendFormat": "Memory Usage %", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}, {"id": 9, "title": "Daily Usage Patterns", "type": "timeseries", "targets": [{"expr": "rate(query_intelligence_queries_total[1h])", "legendFormat": "Hourly Query Rate", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}}, {"id": 10, "title": "Scaling Recommendations", "type": "table", "targets": [{"expr": "rate(query_intelligence_queries_total[1m])", "legendFormat": "Current Load", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(query_intelligence_query_duration_seconds_bucket[1m]))", "legendFormat": "P95 Response Time", "refId": "B"}, {"expr": "rate(query_intelligence_queries_total{status=\"error\"}[1m]) / rate(query_intelligence_queries_total[1m]) * 100", "legendFormat": "Error Rate %", "refId": "C"}], "transformations": [{"id": "organize", "options": {"columns": [{"text": "Current Load (req/s)", "value": "Current Load"}, {"text": "P95 Response Time (s)", "value": "P95 Response Time"}, {"text": "Error Rate (%)", "value": "Error Rate %"}]}}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}}, {"id": 11, "title": "Resource Forecast", "type": "timeseries", "targets": [{"expr": "predict_linear(rate(query_intelligence_queries_total[1h])[4h:1h], 86400)", "legendFormat": "Predicted <PERSON><PERSON> (24h)", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 40}}, {"id": 12, "title": "External Dependencies Load", "type": "timeseries", "targets": [{"expr": "rate(query_intelligence_llm_requests_total[1m])", "legendFormat": "LLM Requests/sec", "refId": "A"}, {"expr": "rate(query_intelligence_cache_hits_total[1m]) + rate(query_intelligence_cache_misses_total[1m])", "legendFormat": "Cache Requests/sec", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 40}}]}}