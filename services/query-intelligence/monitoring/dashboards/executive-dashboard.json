{"dashboard": {"id": null, "title": "Query Intelligence - Executive Dashboard", "description": "High-level KPIs and business metrics for executive visibility", "tags": ["query-intelligence", "executive", "kpi"], "style": "dark", "timezone": "browser", "editable": true, "graphTooltip": 0, "time": {"from": "now-24h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "refresh": "30s", "panels": [{"id": 1, "title": "Service Health Status", "type": "stat", "targets": [{"expr": "up{job=\"query-intelligence\"}", "legendFormat": "Service Status", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "red", "text": "DOWN"}, "1": {"color": "green", "text": "UP"}}, "type": "value"}], "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Total Queries (24h)", "type": "stat", "targets": [{"expr": "increase(query_intelligence_queries_total[24h])", "legendFormat": "Total Queries", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Success Rate (24h)", "type": "stat", "targets": [{"expr": "rate(query_intelligence_queries_total{status=\"success\"}[24h]) / rate(query_intelligence_queries_total[24h]) * 100", "legendFormat": "Success Rate", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "percent", "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 95}, {"color": "green", "value": 99}]}}}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "Average Response Time", "type": "stat", "targets": [{"expr": "avg(query_intelligence_query_duration_seconds_bucket)", "legendFormat": "Avg Response Time", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "s", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "Query Volume Trend", "type": "timeseries", "targets": [{"expr": "rate(query_intelligence_queries_total[5m])", "legendFormat": "Queries/sec", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}}, {"id": 6, "title": "Response Time Distribution", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.50, rate(query_intelligence_query_duration_seconds_bucket[5m]))", "legendFormat": "50th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(query_intelligence_query_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile", "refId": "B"}, {"expr": "histogram_quantile(0.99, rate(query_intelligence_query_duration_seconds_bucket[5m]))", "legendFormat": "99th percentile", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "s"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}}, {"id": 7, "title": "Error Rate by Intent", "type": "timeseries", "targets": [{"expr": "rate(query_intelligence_queries_total{status=\"error\"}[5m]) by (intent)", "legendFormat": "{{intent}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}}, {"id": 8, "title": "<PERSON><PERSON>", "type": "stat", "targets": [{"expr": "rate(query_intelligence_cache_hits_total[5m]) / (rate(query_intelligence_cache_hits_total[5m]) + rate(query_intelligence_cache_misses_total[5m])) * 100", "legendFormat": "<PERSON><PERSON> Hit Rate", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "percent", "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 70}, {"color": "green", "value": 85}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}}, {"id": 9, "title": "Top Query Intents", "type": "piechart", "targets": [{"expr": "sum(increase(query_intelligence_queries_total[24h])) by (intent)", "legendFormat": "{{intent}}", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}}, {"id": 10, "title": "System Dependencies Health", "type": "stat", "targets": [{"expr": "up{job=\"redis\"} or on() vector(0)", "legendFormat": "Redis", "refId": "A"}, {"expr": "up{job=\"analysis-engine\"} or on() vector(0)", "legendFormat": "Analysis Engine", "refId": "B"}, {"expr": "up{job=\"pattern-mining\"} or on() vector(0)", "legendFormat": "Pattern Mining", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "red", "text": "DOWN"}, "1": {"color": "green", "text": "UP"}}, "type": "value"}], "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}}]}}