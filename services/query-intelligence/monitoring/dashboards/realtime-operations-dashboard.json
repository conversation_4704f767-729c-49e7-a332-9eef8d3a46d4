{"dashboard": {"id": null, "title": "Query Intelligence - Real-Time Operations Dashboard", "description": "Live monitoring and operational status for real-time incident response", "tags": ["query-intelligence", "operations", "real-time", "incident"], "style": "dark", "timezone": "browser", "editable": true, "graphTooltip": 0, "time": {"from": "now-15m", "to": "now"}, "timepicker": {"refresh_intervals": ["1s", "5s", "10s", "30s", "1m", "5m"]}, "refresh": "5s", "panels": [{"id": 1, "title": "Service Health", "type": "stat", "targets": [{"expr": "up{job=\"query-intelligence\"}", "legendFormat": "Service Status", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "red", "text": "DOWN"}, "1": {"color": "green", "text": "UP"}}, "type": "value"}], "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 4, "w": 4, "x": 0, "y": 0}}, {"id": 2, "title": "Current RPS", "type": "stat", "targets": [{"expr": "rate(query_intelligence_queries_total[30s])", "legendFormat": "Requests/sec", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "reqps", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 30}, {"color": "red", "value": 45}]}}}, "gridPos": {"h": 4, "w": 4, "x": 4, "y": 0}}, {"id": 3, "title": "P95 Response Time", "type": "stat", "targets": [{"expr": "histogram_quantile(0.95, rate(query_intelligence_query_duration_seconds_bucket[30s]))", "legendFormat": "P95 Response Time", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "s", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 3}]}}}, "gridPos": {"h": 4, "w": 4, "x": 8, "y": 0}}, {"id": 4, "title": "Error Rate", "type": "stat", "targets": [{"expr": "rate(query_intelligence_queries_total{status=\"error\"}[30s]) / rate(query_intelligence_queries_total[30s]) * 100", "legendFormat": "Error Rate", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "percent", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}}, "gridPos": {"h": 4, "w": 4, "x": 12, "y": 0}}, {"id": 5, "title": "Active Queries", "type": "stat", "targets": [{"expr": "query_intelligence_active_queries", "legendFormat": "Active Queries", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "short", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 20}, {"color": "red", "value": 40}]}}}, "gridPos": {"h": 4, "w": 4, "x": 16, "y": 0}}, {"id": 6, "title": "<PERSON><PERSON> Hit Rate", "type": "stat", "targets": [{"expr": "rate(query_intelligence_cache_hits_total[30s]) / (rate(query_intelligence_cache_hits_total[30s]) + rate(query_intelligence_cache_misses_total[30s])) * 100", "legendFormat": "<PERSON><PERSON> Hit Rate", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "unit": "percent", "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 70}, {"color": "green", "value": 85}]}}}, "gridPos": {"h": 4, "w": 4, "x": 20, "y": 0}}, {"id": 7, "title": "Real-Time Request Rate", "type": "timeseries", "targets": [{"expr": "rate(query_intelligence_queries_total[30s])", "legendFormat": "Total Requests", "refId": "A"}, {"expr": "rate(query_intelligence_queries_total{status=\"success\"}[30s])", "legendFormat": "Successful Requests", "refId": "B"}, {"expr": "rate(query_intelligence_queries_total{status=\"error\"}[30s])", "legendFormat": "Failed Requests", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}}, {"id": 8, "title": "Real-Time Response Times", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.50, rate(query_intelligence_query_duration_seconds_bucket[30s]))", "legendFormat": "P50", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(query_intelligence_query_duration_seconds_bucket[30s]))", "legendFormat": "P95", "refId": "B"}, {"expr": "histogram_quantile(0.99, rate(query_intelligence_query_duration_seconds_bucket[30s]))", "legendFormat": "P99", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "s"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}}, {"id": 9, "title": "Circuit Breaker Status", "type": "timeseries", "targets": [{"expr": "circuit_breaker_state", "legendFormat": "{{name}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "mappings": [{"options": {"0": {"color": "green", "text": "CLOSED"}, "1": {"color": "yellow", "text": "HALF_OPEN"}, "2": {"color": "red", "text": "OPEN"}}, "type": "value"}]}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}}, {"id": 10, "title": "Dependency Health", "type": "stat", "targets": [{"expr": "up{job=\"redis\"} or on() vector(0)", "legendFormat": "Redis", "refId": "A"}, {"expr": "up{job=\"analysis-engine\"} or on() vector(0)", "legendFormat": "Analysis Engine", "refId": "B"}, {"expr": "up{job=\"pattern-mining\"} or on() vector(0)", "legendFormat": "Pattern Mining", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"color": "red", "text": "DOWN"}, "1": {"color": "green", "text": "UP"}}, "type": "value"}], "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}}, {"id": 11, "title": "Active Alerts", "type": "table", "targets": [{"expr": "ALERTS{alertstate=\"firing\"}", "legendFormat": "Active Alerts", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 3}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}}, {"id": 12, "title": "Real-Time Logs", "type": "logs", "targets": [{"expr": "{job=\"query-intelligence\"}", "legendFormat": "Service Logs", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}}]}}