# Query Intelligence Service Environment Configuration
# Updated July 2025 for Google GenAI SDK

# Service Configuration
SERVICE_NAME=query-intelligence
PORT=8002
ENVIRONMENT=development  # development/staging/production
LOG_LEVEL=INFO

# External Services
REDIS_URL=redis://localhost:6379
ANALYSIS_ENGINE_URL=http://localhost:8001
PATTERN_MINING_URL=http://localhost:8003
COLLABORATION_URL=http://localhost:8004

# ===== CRITICAL: Google GenAI SDK Configuration =====
# Choose your backend:
USE_VERTEX_AI=true  # true for production (Vertex AI), false for dev (Gemini API)

# For Vertex AI Backend (Production)
GCP_PROJECT_ID=vibe-match-463114
GCP_REGION=us-central1
SERVICE_ACCOUNT_PATH=/secrets/service-account.json
# Note: Can also use Application Default Credentials if SERVICE_ACCOUNT_PATH is not set

# For Gemini API Backend (Development Only - NEVER use in production)
# GOOGLE_API_KEY=your-api-key

# Model Configuration
GEMINI_MODEL_NAME=gemini-2.5-flash  # Primary model
USE_MODEL_ROUTING=true  # Enable intelligent routing
SIMPLE_QUERY_MODEL=gemini-2.5-flash  # Flash-lite not available in all regions
COMPLEX_QUERY_MODEL=gemini-2.5-pro

# Pinecone Vector Database
PINECONE_API_KEY=your-pinecone-key
PINECONE_INDEX_NAME=ccl-code-embeddings
PINECONE_CLOUD=aws
PINECONE_REGION=us-west-2

# Security - MUST use Secret Manager in production!
USE_SECRET_MANAGER=false  # Set to true for production
SECRET_PROJECT_ID=vibe-match-463114
JWT_SECRET_KEY=CHANGE-THIS-USE-SECRET-MANAGER  # Will fail in production if not changed
JWT_ALGORITHM=HS256
JWT_EXPIRATION_MINUTES=60

# Performance Optimization
MIN_INSTANCES=0  # Set to 5+ for production to eliminate cold starts
MAX_INSTANCES=5  # Set to 200 for production
CONCURRENCY=20  # Per-instance limit
CPU_BOOST_ENABLED=true  # 30-40% faster cold starts
SEMANTIC_CACHE_ENABLED=true

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW_SECONDS=60
RATE_LIMIT_PER_USER=true  # Apply per user, not per IP

# Security Features
ENABLE_INPUT_VALIDATION=true
ENABLE_PROMPT_INJECTION_DETECTION=true
ENABLE_PII_DETECTION=true

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_TRACING=true