{"gates": [{"gate_id": "api_performance_gate", "name": "API Performance Gate", "description": "Primary performance gate for API endpoints", "max_response_time_ms": 2000, "min_throughput_rps": 10.0, "max_error_rate_percent": 1.0, "max_cpu_percent": 80.0, "max_memory_percent": 80.0, "max_response_time_regression": 20.0, "max_throughput_regression": 15.0, "max_error_rate_regression": 0.5, "min_confidence_score": 0.7, "min_statistical_significance": 0.05, "test_duration": 60.0, "concurrent_users": 10, "requests_per_user": 5, "action": "block", "baseline_id": "main_branch", "max_retries": 2, "retry_delay": 30.0, "environment": "ci"}, {"gate_id": "resource_usage_gate", "name": "Resource Usage Gate", "description": "Monitor system resource usage during load testing", "max_response_time_ms": 3000, "min_throughput_rps": 5.0, "max_error_rate_percent": 2.0, "max_cpu_percent": 90.0, "max_memory_percent": 85.0, "max_response_time_regression": 30.0, "max_throughput_regression": 20.0, "max_error_rate_regression": 1.0, "min_confidence_score": 0.6, "test_duration": 45.0, "concurrent_users": 8, "requests_per_user": 3, "action": "warn", "baseline_id": "resource_baseline", "max_retries": 1, "retry_delay": 15.0, "environment": "ci"}, {"gate_id": "stress_test_gate", "name": "Stress Test Gate", "description": "Validate performance under stress conditions", "max_response_time_ms": 5000, "min_throughput_rps": 3.0, "max_error_rate_percent": 5.0, "max_cpu_percent": 95.0, "max_memory_percent": 90.0, "max_response_time_regression": 50.0, "max_throughput_regression": 30.0, "max_error_rate_regression": 2.0, "min_confidence_score": 0.5, "test_duration": 30.0, "concurrent_users": 20, "requests_per_user": 3, "action": "advisory", "baseline_id": "stress_baseline", "max_retries": 1, "retry_delay": 10.0, "environment": "ci"}], "global_settings": {"default_timeout": 30.0, "max_concurrent_gates": 1, "results_retention_days": 30, "enable_detailed_logging": true, "notification_settings": {"slack_webhook": null, "email_recipients": [], "github_issues": true}}, "thresholds": {"response_time": {"excellent": 500, "good": 1000, "acceptable": 2000, "poor": 5000}, "throughput": {"excellent": 50, "good": 20, "acceptable": 10, "poor": 5}, "error_rate": {"excellent": 0.1, "good": 0.5, "acceptable": 1.0, "poor": 5.0}, "cpu_usage": {"excellent": 40, "good": 60, "acceptable": 80, "poor": 95}, "memory_usage": {"excellent": 50, "good": 70, "acceptable": 85, "poor": 95}}}