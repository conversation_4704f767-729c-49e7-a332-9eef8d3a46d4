# Configuration Directory

This directory contains configuration files and templates for the Query Intelligence Service.

## Files

### Environment Configuration
- **`.env.example`** - Template for environment variables with documentation
- **`performance_gates.json`** - Performance testing thresholds and gates

## Environment Setup

### Local Development
```bash
# Copy the example file and customize for local development
cp config/.env.example .env.local

# Edit .env.local with your local settings
vim .env.local
```

### Production Deployment
For production environments, use Google Cloud Secret Manager instead of environment files:

```bash
# Store secrets in Secret Manager
echo "production-jwt-secret" | gcloud secrets create jwt-secret --data-file=-
echo "production-api-key" | gcloud secrets create google-api-key --data-file=-
```

## Security Notes

- **Never commit actual environment files** (.env.local, .env.development, .env.production)
- **Use Secret Manager for production** - Store sensitive values in Google Cloud Secret Manager
- **Rotate secrets regularly** - Implement secret rotation policies
- **Validate environment variables** - Use the service's configuration validation

## Configuration Validation

The service includes automatic configuration validation:
```python
# Configuration is validated on startup
poetry run python -c "from query_intelligence.config.settings import get_settings; print('Config valid')"
```

## Related Documentation

- [Deployment Guide](../../../../docs/query-intelligence/deployment/production-deployment.md)
- [Security Guide](../../../../docs/query-intelligence/troubleshooting/README.md)
- [Developer Guide](../../../../docs/query-intelligence/guides/developer-guide.md)