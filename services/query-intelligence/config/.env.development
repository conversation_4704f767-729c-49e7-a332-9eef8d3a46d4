# Development Environment Configuration
# Query Intelligence Service - July 2025

# Service Configuration
SERVICE_NAME=query-intelligence
PORT=8002
ENVIRONMENT=development
LOG_LEVEL=DEBUG

# External Services (Local Development)
REDIS_URL=redis://localhost:6379
ANALYSIS_ENGINE_URL=http://localhost:8001
PATTERN_MINING_URL=http://localhost:8003
COLLABORATION_URL=http://localhost:8004

# Google GenAI SDK Configuration
# Option 1: Use Gemini API with API key (easiest for local dev)
USE_VERTEX_AI=false
GOOGLE_API_KEY=your-gemini-api-key-here

# Option 2: Use Vertex AI with Application Default Credentials
# USE_VERTEX_AI=true
# GCP_PROJECT_ID=your-dev-project
# GCP_REGION=us-central1
# Note: Run 'gcloud auth application-default login' first

# Model Configuration
GEMINI_MODEL_NAME=gemini-2.5-flash
USE_MODEL_ROUTING=false  # Simplified for dev
SIMPLE_QUERY_MODEL=gemini-2.5-flash
COMPLEX_QUERY_MODEL=gemini-2.5-flash  # Use same model for dev

# Pinecone Vector Database (Development)
PINECONE_API_KEY=your-dev-pinecone-key
PINECONE_INDEX_NAME=ccl-code-embeddings-dev
PINECONE_CLOUD=aws
PINECONE_REGION=us-west-2

# Security (Simplified for Development)
USE_SECRET_MANAGER=false
JWT_SECRET_KEY=dev-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRATION_MINUTES=1440  # 24 hours for dev

# Performance (Relaxed for Development)
MIN_INSTANCES=0
MAX_INSTANCES=5
CONCURRENCY=10
CPU_BOOST_ENABLED=false
SEMANTIC_CACHE_ENABLED=false  # Disable cache for testing

# Cache Configuration
CACHE_TTL_HOURS=1  # Short TTL for dev
CACHE_MAX_SIZE=100

# Rate Limiting (Relaxed for Development)
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW_SECONDS=60
RATE_LIMIT_PER_USER=false  # Simplified for dev

# Security Features (Can disable for faster dev)
ENABLE_INPUT_VALIDATION=true
ENABLE_PROMPT_INJECTION_DETECTION=false
ENABLE_PII_DETECTION=false

# Monitoring (Simplified for Development)
ENABLE_METRICS=false
METRICS_PORT=9090
ENABLE_TRACING=false

# Performance Settings
MAX_QUERY_LENGTH=10000
MAX_CODE_CHUNKS=10
MAX_RESPONSE_TOKENS=1024
QUERY_TIMEOUT_SECONDS=60  # Longer timeout for debugging