# Query Intelligence Service - Environment Configuration Example

# Environment
ENVIRONMENT=development
PORT=8002
HOST=0.0.0.0

# Google Cloud Configuration
GCP_PROJECT_ID=your-project-id
GCP_REGION=us-central1
GOOGLE_API_KEY=your-google-api-key

# Authentication
JWT_SECRET=your-jwt-secret
FIREBASE_PROJECT_ID=your-firebase-project

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_MAX_CONNECTIONS=100

# Service URLs
ANALYSIS_ENGINE_URL=https://analysis-engine-service-url
PATTERN_MINING_URL=https://pattern-mining-service-url

# Performance Configuration
MAX_QUERY_LENGTH=10000
RATE_LIMIT_PER_HOUR=1000
SEMANTIC_CACHE_ENABLED=true
CACHE_TTL_SECONDS=3600

# Security Configuration
ENABLE_WEBSOCKET_AUTH=true
ENABLE_SECURITY_CONTROLS=true
ENABLE_RATE_LIMITING=true

# Monitoring and Logging
ENABLE_METRICS=true
LOG_LEVEL=INFO
STRUCTURED_LOGGING=true

# Feature Flags
ENABLE_DEBUG_ROUTES=false
FALLBACK_MODE=false

# Copy this file to:
# - .env.local for local development
# - .env.development for development environment
# - .env.production for production environment (use secrets manager)
EOF < /dev/null