[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
asyncio_mode = auto
addopts = -v --tb=short

# E2E Test Configuration
markers =
    e2e: End-to-end integration tests
    user_journey: Complete user journey tests
    service_integration: Multi-service integration tests
    performance: Performance and load testing
    error_recovery: Error recovery and fallback tests
    cache_behavior: Cache behavior validation tests
    production: Production-like scenarios
    slow: Slow running tests
    websocket: WebSocket functionality tests
    concurrent: Concurrent execution tests

# Timeout configuration
timeout = 300
timeout_method = thread

# Logging configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning