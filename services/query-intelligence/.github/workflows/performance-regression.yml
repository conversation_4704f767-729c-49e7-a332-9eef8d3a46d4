name: Performance Regression Tests

on:
  pull_request:
    branches: [main]
    paths:
      - 'services/query-intelligence/**'
  push:
    branches: [main]
    paths:
      - 'services/query-intelligence/**'
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  SERVICE_NAME: query-intelligence
  SERVICE_PORT: 8000
  SERVICE_URL: http://localhost:8000

jobs:
  performance-regression:
    runs-on: ubuntu-latest
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
        cache: 'pip'
    
    - name: Install dependencies
      run: |
        cd services/query-intelligence
        pip install -r requirements.txt
        pip install scipy numpy pytest-asyncio
    
    - name: Set up environment variables
      run: |
        echo "REDIS_URL=redis://localhost:6379" >> $GITHUB_ENV
        echo "ENVIRONMENT=test" >> $GITHUB_ENV
        echo "LOG_LEVEL=INFO" >> $GITHUB_ENV
    
    - name: Start query-intelligence service
      run: |
        cd services/query-intelligence
        python -m uvicorn src.query_intelligence.main:app \
          --host 0.0.0.0 \
          --port ${{ env.SERVICE_PORT }} \
          --log-level info &
        
        # Wait for service to start
        timeout 60 bash -c 'until curl -f ${{ env.SERVICE_URL }}/health; do sleep 2; done'
        
        # Verify service is responding
        curl -f ${{ env.SERVICE_URL }}/health || exit 1
    
    - name: Run unit tests for regression framework
      run: |
        cd services/query-intelligence
        python -m pytest tests/regression/test_regression_suite.py -v --tb=short
    
    - name: Create performance baselines (if needed)
      run: |
        cd services/query-intelligence
        
        # Check if baseline exists, create if not
        if ! python scripts/run_performance_regression_tests.py list-baselines | grep -q "main_branch"; then
          echo "Creating main branch baseline..."
          python scripts/run_performance_regression_tests.py create-baseline \
            --service-url ${{ env.SERVICE_URL }} \
            --baseline-id main_branch \
            --version "1.0.0" \
            --service-version ${{ github.sha }} \
            --environment ci \
            --auth-token ${{ secrets.API_TOKEN }}
        else
          echo "Baseline already exists, skipping creation"
        fi
    
    - name: Run performance regression tests
      run: |
        cd services/query-intelligence
        python scripts/run_performance_regression_tests.py regression-test \
          --service-url ${{ env.SERVICE_URL }} \
          --baseline-id main_branch \
          --duration 30.0 \
          --concurrent-users 5 \
          --requests-per-user 10 \
          --auth-token ${{ secrets.API_TOKEN }}
    
    - name: Run quick benchmark suite
      run: |
        cd services/query-intelligence
        python scripts/run_performance_regression_tests.py benchmark \
          --service-url ${{ env.SERVICE_URL }} \
          --suite-type quick \
          --auth-token ${{ secrets.API_TOKEN }}
    
    - name: Run CI/CD performance gates
      id: performance-gates
      run: |
        cd services/query-intelligence
        python scripts/run_performance_regression_tests.py ci-gates \
          --service-url ${{ env.SERVICE_URL }} \
          --gates-config performance_gates.json \
          --auth-token ${{ secrets.API_TOKEN }} \
          --github-output
      env:
        API_TOKEN: ${{ secrets.API_TOKEN }}
        GITHUB_OUTPUT: ${{ github.env }}
    
    - name: Analyze performance trends
      if: github.event_name == 'schedule' || github.ref == 'refs/heads/main'
      run: |
        cd services/query-intelligence
        python scripts/run_performance_regression_tests.py analyze-trends \
          --baseline-id main_branch \
          --days 30
    
    - name: Generate performance report
      if: always()
      run: |
        cd services/query-intelligence
        
        # Generate summary report
        echo "## Performance Test Results" > performance_report.md
        echo "" >> performance_report.md
        echo "**Test Run:** $(date)" >> performance_report.md
        echo "**Commit:** ${{ github.sha }}" >> performance_report.md
        echo "**Branch:** ${{ github.ref_name }}" >> performance_report.md
        echo "" >> performance_report.md
        
        # Add gate results if available
        if [ -f performance_gate_results.json ]; then
          echo "### Performance Gates" >> performance_report.md
          python -c "
import json
with open('performance_gate_results.json', 'r') as f:
    data = json.load(f)
    summary = data['summary']
    print(f'- Total Gates: {summary[\"total_gates\"]}')
    print(f'- Passed: {summary[\"passed\"]}')
    print(f'- Failed: {summary[\"failed\"]}')
    print(f'- Warnings: {summary[\"warned\"]}')
    print(f'- Should Block: {\"Yes\" if summary[\"should_block_deployment\"] else \"No\"}')
" >> performance_report.md
        fi
        
        # Add baseline information
        echo "" >> performance_report.md
        echo "### Available Baselines" >> performance_report.md
        python scripts/run_performance_regression_tests.py list-baselines >> performance_report.md
    
    - name: Upload performance artifacts
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: performance-results-${{ github.run_number }}
        path: |
          services/query-intelligence/performance_gate_results.json
          services/query-intelligence/performance_report.md
          services/query-intelligence/performance_baselines/**
        retention-days: 30
    
    - name: Comment PR with performance results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const path = 'services/query-intelligence/performance_report.md';
          
          if (fs.existsSync(path)) {
            const report = fs.readFileSync(path, 'utf8');
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `## 🚀 Performance Test Results\n\n${report}`
            });
          }
    
    - name: Fail if performance gates block deployment
      if: steps.performance-gates.outcome == 'failure'
      run: |
        echo "❌ Performance gates failed - blocking deployment"
        exit 1
    
    - name: Update baseline on main branch
      if: github.ref == 'refs/heads/main' && github.event_name == 'push'
      run: |
        cd services/query-intelligence
        
        # Create new baseline version for main branch
        BASELINE_VERSION=$(date +%Y%m%d_%H%M%S)
        
        python scripts/run_performance_regression_tests.py create-baseline \
          --service-url ${{ env.SERVICE_URL }} \
          --baseline-id main_branch \
          --version $BASELINE_VERSION \
          --service-version ${{ github.sha }} \
          --environment production \
          --auth-token ${{ secrets.API_TOKEN }}
        
        echo "✅ Updated main branch baseline to version $BASELINE_VERSION"

  performance-comparison:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    needs: performance-regression
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Download performance artifacts
      uses: actions/download-artifact@v4
      with:
        name: performance-results-${{ github.run_number }}
        path: ./performance-results
    
    - name: Compare with main branch performance
      run: |
        cd services/query-intelligence
        
        # This would implement comparison logic
        # For now, just report the results
        echo "Performance comparison with main branch:"
        if [ -f ./performance-results/performance_gate_results.json ]; then
          cat ./performance-results/performance_gate_results.json | jq '.summary'
        fi
    
    - name: Performance regression summary
      run: |
        echo "## Performance Regression Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "Performance tests completed for PR #${{ github.event.pull_request.number }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        if [ -f ./performance-results/performance_gate_results.json ]; then
          echo "### Gate Results" >> $GITHUB_STEP_SUMMARY
          python -c "
import json
with open('./performance-results/performance_gate_results.json', 'r') as f:
    data = json.load(f)
    summary = data['summary']
    if summary['should_block_deployment']:
        print('❌ **BLOCKED** - Performance regression detected')
    else:
        print('✅ **PASSED** - Performance within acceptable limits')
    print(f'- Gates run: {summary[\"total_gates\"]}')
    print(f'- Passed: {summary[\"passed\"]}')
    print(f'- Failed: {summary[\"failed\"]}')
    print(f'- Warnings: {summary[\"warned\"]}')
" >> $GITHUB_STEP_SUMMARY
        fi

  cleanup:
    runs-on: ubuntu-latest
    if: always()
    needs: [performance-regression, performance-comparison]
    
    steps:
    - name: Cleanup old baselines
      run: |
        cd services/query-intelligence
        
        # This would implement cleanup logic
        # Remove baselines older than 30 days
        echo "Cleaning up old performance baselines..."
        
        # In a real implementation, this would:
        # 1. Connect to baseline storage
        # 2. Remove expired baselines
        # 3. Clean up old performance results
        
        echo "✅ Cleanup completed"