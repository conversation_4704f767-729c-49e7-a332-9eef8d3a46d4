#!/usr/bin/env python3
"""
Performance Regression Testing CLI

Comprehensive CLI tool for running performance regression tests,
managing baselines, and integrating with CI/CD pipelines.
"""

import asyncio
import argparse
import sys
import os
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
import httpx
from datetime import datetime

# Add the parent directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from tests.regression.performance_baselines import BaselineManager, BaselineMetrics
from tests.regression.regression_tests import RegressionTest, RegressionTestConfig
from tests.regression.benchmark_suite import BenchmarkSuite, BenchmarkRunner, BenchmarkConfig
from tests.regression.ci_performance_gates import CIPipelineIntegration
from tests.regression.trend_analysis import TrendAnalyzer


class PerformanceTestingCLI:
    """Main CLI class for performance regression testing"""
    
    def __init__(self):
        self.baseline_manager = None
        self.service_url = None
        self.auth_headers = {}
    
    def setup(self, baseline_storage: str, service_url: str, auth_token: Optional[str] = None):
        """Setup CLI with configuration"""
        self.baseline_manager = BaselineManager(baseline_storage)
        self.service_url = service_url
        
        if auth_token:
            self.auth_headers = {"Authorization": f"Bearer {auth_token}"}
        
        print(f"✅ Setup complete - Service: {service_url}")
        print(f"📁 Baseline storage: {baseline_storage}")
    
    async def create_baseline(self, baseline_id: str, version: str, service_version: str, 
                            test_config: Dict[str, Any], environment: str = "test"):
        """Create a new performance baseline"""
        
        print(f"🔄 Creating baseline: {baseline_id} v{version}")
        
        # Run benchmark to collect baseline metrics
        benchmark_config = BenchmarkConfig(
            benchmark_id=f"baseline_{baseline_id}",
            benchmark_type=BenchmarkType.SINGLE_ENDPOINT,
            duration=60.0,
            concurrent_users=10,
            requests_per_user=5,
            endpoints=["/api/v1/query"],
            base_url=self.service_url,
            create_baseline=False
        )
        
        benchmark_runner = BenchmarkRunner(self.baseline_manager)
        result = await benchmark_runner.run_benchmark(benchmark_config, self.auth_headers)
        
        if result.status.value != "completed":
            print(f"❌ Benchmark failed: {result.status.value}")
            return False
        
        # Create baseline from benchmark results
        baseline = self.baseline_manager.create_baseline(
            baseline_id=baseline_id,
            version=version,
            service_version=service_version,
            metrics=result.overall_metrics,
            test_config=test_config,
            environment=environment,
            tags=["cli-created", environment]
        )
        
        print(f"✅ Baseline created successfully")
        print(f"   📊 Avg Response Time: {baseline.metrics.avg_response_time:.3f}s")
        print(f"   🚀 Throughput: {baseline.metrics.requests_per_second:.1f} RPS")
        print(f"   ✅ Success Rate: {baseline.metrics.success_rate:.1f}%")
        print(f"   💾 Total Requests: {baseline.metrics.total_requests}")
        
        return True
    
    async def run_regression_test(self, baseline_id: str, baseline_version: Optional[str] = None,
                                config_overrides: Optional[Dict[str, Any]] = None):
        """Run regression test against baseline"""
        
        print(f"🔄 Running regression test against baseline: {baseline_id}")
        if baseline_version:
            print(f"   Version: {baseline_version}")
        
        # Get baseline
        baseline = self.baseline_manager.get_baseline(baseline_id, baseline_version)
        if not baseline:
            print(f"❌ Baseline not found: {baseline_id}")
            return False
        
        print(f"📊 Baseline metrics:")
        print(f"   Response Time: {baseline.metrics.avg_response_time:.3f}s")
        print(f"   Throughput: {baseline.metrics.requests_per_second:.1f} RPS")
        print(f"   Success Rate: {baseline.metrics.success_rate:.1f}%")
        
        # Create test configuration
        config = RegressionTestConfig(
            test_duration=60.0,
            concurrent_users=10,
            requests_per_user=5,
            **(config_overrides or {})
        )
        
        # Create test function
        async def test_func(user_id: int = 0, request_num: int = 0, **kwargs):
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.service_url}/api/v1/query",
                    json={
                        "query": f"Regression test query {user_id}-{request_num}",
                        "repository_id": "regression_test_repo"
                    },
                    headers=self.auth_headers
                )
                
                return {
                    "success": response.status_code == 200,
                    "response_time": response.elapsed.total_seconds() if hasattr(response, 'elapsed') else 0,
                    "status_code": response.status_code
                }
        
        # Run regression test
        regression_test = RegressionTest(self.baseline_manager)
        result = await regression_test.run_regression_test(baseline_id, test_func, config, baseline_version)
        
        # Display results
        print(f"\n📈 Regression Test Results:")
        print(f"   Test ID: {result.test_id}")
        print(f"   Duration: {result.duration:.1f}s")
        print(f"   Status: {'✅ PASSED' if not result.is_regression else '❌ REGRESSION DETECTED'}")
        print(f"   Confidence: {result.confidence_score:.1%}")
        print(f"   Severity: {result.severity.value.upper()}")
        
        if result.is_regression:
            print(f"\n🚨 Regression Details:")
            for factor in result.regression_factors:
                print(f"   • {factor}")
            
            print(f"\n📊 Performance Changes:")
            for metric, change in result.performance_changes.items():
                direction = "↑" if change > 0 else "↓"
                print(f"   {metric}: {direction} {abs(change):.1f}%")
        
        if result.recommendations:
            print(f"\n💡 Recommendations:")
            for rec in result.recommendations:
                print(f"   • {rec}")
        
        return not result.is_regression
    
    async def run_benchmark_suite(self, suite_type: str = "comprehensive"):
        """Run benchmark suite"""
        
        print(f"🔄 Running {suite_type} benchmark suite")
        
        # Create benchmark suite
        if suite_type == "comprehensive":
            configs = BenchmarkSuite.create_comprehensive_suite(self.service_url)
        elif suite_type == "quick":
            configs = BenchmarkSuite.create_quick_suite(self.service_url)
        else:
            print(f"❌ Unknown suite type: {suite_type}")
            return False
        
        print(f"📋 Running {len(configs)} benchmark tests")
        
        # Run benchmarks
        benchmark_runner = BenchmarkRunner(self.baseline_manager)
        results = await benchmark_runner.run_benchmark_suite(configs, self.auth_headers)
        
        # Display results
        print(f"\n📊 Benchmark Suite Results:")
        
        passed = 0
        failed = 0
        
        for result in results:
            status_symbol = "✅" if result.status.value == "completed" else "❌"
            print(f"   {status_symbol} {result.benchmark_id}")
            print(f"      Grade: {result.performance_grade}")
            print(f"      Score: {result.performance_score:.1f}/100")
            print(f"      Response Time: {result.overall_metrics.avg_response_time:.3f}s")
            print(f"      Throughput: {result.overall_metrics.requests_per_second:.1f} RPS")
            print(f"      Success Rate: {result.overall_metrics.success_rate:.1f}%")
            
            if result.status.value == "completed":
                passed += 1
            else:
                failed += 1
        
        print(f"\n📈 Summary: {passed} passed, {failed} failed")
        
        return failed == 0
    
    async def run_ci_gates(self, gates_config: str = "performance_gates.json"):
        """Run CI/CD performance gates"""
        
        print(f"🔄 Running CI/CD performance gates")
        print(f"📋 Configuration: {gates_config}")
        
        # Initialize CI integration
        ci_integration = CIPipelineIntegration(self.baseline_manager, gates_config)
        
        # Run all gates
        results = await ci_integration.run_all_gates(self.service_url, self.auth_headers)
        
        # Display results
        print(f"\n🛡️ Performance Gates Results:")
        
        for result in results:
            status_symbols = {
                "passed": "✅",
                "warning": "⚠️",
                "failed": "❌",
                "error": "💥"
            }
            
            symbol = status_symbols.get(result.status.value, "❓")
            print(f"   {symbol} {result.gate_name}: {result.status.value.upper()}")
            
            if result.messages:
                for message in result.messages:
                    print(f"      📝 {message}")
            
            if result.threshold_violations:
                print(f"      🚨 Violations:")
                for violation in result.threshold_violations:
                    print(f"         • {violation}")
        
        # Summary
        summary = ci_integration.generate_summary_report()
        print(f"\n📊 Gates Summary:")
        print(f"   Total: {summary['summary']['total_gates']}")
        print(f"   Passed: {summary['summary']['passed']}")
        print(f"   Warned: {summary['summary']['warned']}")
        print(f"   Failed: {summary['summary']['failed']}")
        print(f"   Should Block: {'YES' if summary['summary']['should_block_deployment'] else 'NO'}")
        
        return not summary['summary']['should_block_deployment']
    
    def analyze_trends(self, baseline_id: str, days: int = 30):
        """Analyze performance trends"""
        
        print(f"📈 Analyzing trends for baseline: {baseline_id}")
        print(f"📅 Time period: {days} days")
        
        # Initialize trend analyzer
        trend_analyzer = TrendAnalyzer(self.baseline_manager)
        
        # Analyze trends
        trends = trend_analyzer.analyze_baseline_trends(baseline_id, days)
        
        if not trends:
            print(f"⚠️ No trend data available for baseline: {baseline_id}")
            return
        
        print(f"\n📊 Trend Analysis Results:")
        
        for metric_name, trend in trends.items():
            direction_symbols = {
                "improving": "📈",
                "stable": "➡️",
                "degrading": "📉",
                "volatile": "📊"
            }
            
            symbol = direction_symbols.get(trend.direction.value, "❓")
            print(f"   {symbol} {metric_name}:")
            print(f"      Direction: {trend.direction.value.upper()}")
            print(f"      Confidence: {trend.confidence:.1%}")
            print(f"      Current: {trend.current_value:.3f}")
            print(f"      Predicted: {trend.predicted_value:.3f}")
            print(f"      Strength: {trend.trend_strength}")
        
        # Generate and display alerts
        alerts = trend_analyzer.generate_trend_alerts(trends)
        
        if alerts:
            print(f"\n🚨 Trend Alerts:")
            
            for alert in alerts:
                severity_symbols = {
                    "info": "ℹ️",
                    "warning": "⚠️",
                    "critical": "🚨"
                }
                
                symbol = severity_symbols.get(alert.severity.value, "❓")
                print(f"   {symbol} {alert.title}")
                print(f"      Severity: {alert.severity.value.upper()}")
                print(f"      Metric: {alert.metric_name}")
                print(f"      Current: {alert.current_value:.3f}")
                print(f"      Threshold: {alert.threshold_value:.3f}")
                print(f"      Recommendation: {alert.recommendation}")
        else:
            print(f"\n✅ No trend alerts generated")
    
    def list_baselines(self, environment: Optional[str] = None):
        """List all available baselines"""
        
        print(f"📋 Available Baselines:")
        if environment:
            print(f"   Environment filter: {environment}")
        
        baselines = self.baseline_manager.list_baselines(environment=environment)
        
        if not baselines:
            print(f"   No baselines found")
            return
        
        for baseline_info in baselines:
            print(f"   📊 {baseline_info['baseline_id']} v{baseline_info['version']}")
            print(f"      Service: {baseline_info['service_version']}")
            print(f"      Environment: {baseline_info['environment']}")
            print(f"      Created: {baseline_info['created_at']}")
            print(f"      Status: {baseline_info['status']}")
    
    def get_baseline_details(self, baseline_id: str, version: Optional[str] = None):
        """Get detailed information about a baseline"""
        
        print(f"📊 Baseline Details: {baseline_id}")
        if version:
            print(f"   Version: {version}")
        
        baseline = self.baseline_manager.get_baseline(baseline_id, version)
        
        if not baseline:
            print(f"❌ Baseline not found: {baseline_id}")
            return
        
        print(f"\n📋 Baseline Information:")
        print(f"   ID: {baseline.baseline_id}")
        print(f"   Version: {baseline.version}")
        print(f"   Service Version: {baseline.service_version}")
        print(f"   Environment: {baseline.environment}")
        print(f"   Created: {baseline.created_at}")
        print(f"   Status: {baseline.status.value}")
        print(f"   Git Commit: {baseline.git_commit or 'N/A'}")
        
        print(f"\n📊 Performance Metrics:")
        metrics = baseline.metrics
        print(f"   Response Time (avg): {metrics.avg_response_time:.3f}s")
        print(f"   Response Time (p95): {metrics.p95_response_time:.3f}s")
        print(f"   Response Time (p99): {metrics.p99_response_time:.3f}s")
        print(f"   Throughput: {metrics.requests_per_second:.1f} RPS")
        print(f"   Success Rate: {metrics.success_rate:.1f}%")
        print(f"   Error Rate: {metrics.error_rate:.1f}%")
        print(f"   CPU Usage (avg): {metrics.avg_cpu_percent:.1f}%")
        print(f"   Memory Usage (avg): {metrics.avg_memory_percent:.1f}%")
        print(f"   Total Requests: {metrics.total_requests}")
        
        if baseline.tags:
            print(f"   Tags: {', '.join(baseline.tags)}")
        
        if baseline.test_config:
            print(f"\n⚙️ Test Configuration:")
            for key, value in baseline.test_config.items():
                print(f"   {key}: {value}")


def main():
    """Main CLI entry point"""
    
    parser = argparse.ArgumentParser(
        description="Performance Regression Testing CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Create a new baseline
  python run_performance_regression_tests.py create-baseline \\
    --service-url http://localhost:8000 \\
    --baseline-id main_branch \\
    --version 1.0.0 \\
    --service-version v1.2.3

  # Run regression test
  python run_performance_regression_tests.py regression-test \\
    --service-url http://localhost:8000 \\
    --baseline-id main_branch

  # Run benchmark suite
  python run_performance_regression_tests.py benchmark \\
    --service-url http://localhost:8000 \\
    --suite-type comprehensive

  # Run CI/CD performance gates
  python run_performance_regression_tests.py ci-gates \\
    --service-url http://localhost:8000 \\
    --gates-config performance_gates.json

  # Analyze trends
  python run_performance_regression_tests.py analyze-trends \\
    --baseline-id main_branch \\
    --days 30
        """
    )
    
    # Global options
    parser.add_argument("--baseline-storage", default="performance_baselines",
                       help="Baseline storage directory")
    parser.add_argument("--service-url", help="Service URL to test")
    parser.add_argument("--auth-token", help="Authentication token")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Verbose output")
    
    # Subcommands
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Create baseline command
    create_parser = subparsers.add_parser("create-baseline", help="Create performance baseline")
    create_parser.add_argument("--baseline-id", required=True, help="Baseline ID")
    create_parser.add_argument("--version", required=True, help="Baseline version")
    create_parser.add_argument("--service-version", required=True, help="Service version")
    create_parser.add_argument("--environment", default="test", help="Environment")
    create_parser.add_argument("--test-config", help="Test configuration JSON")
    
    # Regression test command
    regression_parser = subparsers.add_parser("regression-test", help="Run regression test")
    regression_parser.add_argument("--baseline-id", required=True, help="Baseline ID")
    regression_parser.add_argument("--baseline-version", help="Baseline version")
    regression_parser.add_argument("--duration", type=float, default=60.0, help="Test duration")
    regression_parser.add_argument("--concurrent-users", type=int, default=10, help="Concurrent users")
    regression_parser.add_argument("--requests-per-user", type=int, default=5, help="Requests per user")
    
    # Benchmark command
    benchmark_parser = subparsers.add_parser("benchmark", help="Run benchmark suite")
    benchmark_parser.add_argument("--suite-type", choices=["comprehensive", "quick"],
                                 default="comprehensive", help="Benchmark suite type")
    
    # CI gates command
    ci_parser = subparsers.add_parser("ci-gates", help="Run CI/CD performance gates")
    ci_parser.add_argument("--gates-config", default="performance_gates.json",
                          help="Gates configuration file")
    
    # Trend analysis command
    trend_parser = subparsers.add_parser("analyze-trends", help="Analyze performance trends")
    trend_parser.add_argument("--baseline-id", required=True, help="Baseline ID")
    trend_parser.add_argument("--days", type=int, default=30, help="Days to analyze")
    
    # List baselines command
    list_parser = subparsers.add_parser("list-baselines", help="List available baselines")
    list_parser.add_argument("--environment", help="Filter by environment")
    
    # Baseline details command
    details_parser = subparsers.add_parser("baseline-details", help="Get baseline details")
    details_parser.add_argument("--baseline-id", required=True, help="Baseline ID")
    details_parser.add_argument("--version", help="Baseline version")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    # Initialize CLI
    cli = PerformanceTestingCLI()
    
    # Commands that don't require service URL
    if args.command in ["list-baselines", "baseline-details", "analyze-trends"]:
        cli.baseline_manager = BaselineManager(args.baseline_storage)
        
        if args.command == "list-baselines":
            cli.list_baselines(args.environment)
        elif args.command == "baseline-details":
            cli.get_baseline_details(args.baseline_id, args.version)
        elif args.command == "analyze-trends":
            cli.analyze_trends(args.baseline_id, args.days)
        
        return 0
    
    # Commands that require service URL
    if not args.service_url:
        print("❌ --service-url is required for this command")
        return 1
    
    # Get auth token from environment if not provided
    auth_token = args.auth_token or os.getenv("API_TOKEN") or os.getenv("GITHUB_TOKEN")
    
    # Setup CLI
    cli.setup(args.baseline_storage, args.service_url, auth_token)
    
    # Run async commands
    async def run_async_command():
        try:
            if args.command == "create-baseline":
                test_config = {}
                if args.test_config:
                    test_config = json.loads(args.test_config)
                
                success = await cli.create_baseline(
                    args.baseline_id, args.version, args.service_version,
                    test_config, args.environment
                )
                return 0 if success else 1
            
            elif args.command == "regression-test":
                config_overrides = {
                    "test_duration": args.duration,
                    "concurrent_users": args.concurrent_users,
                    "requests_per_user": args.requests_per_user
                }
                
                success = await cli.run_regression_test(
                    args.baseline_id, args.baseline_version, config_overrides
                )
                return 0 if success else 1
            
            elif args.command == "benchmark":
                success = await cli.run_benchmark_suite(args.suite_type)
                return 0 if success else 1
            
            elif args.command == "ci-gates":
                success = await cli.run_ci_gates(args.gates_config)
                return 0 if success else 1
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            if args.verbose:
                import traceback
                traceback.print_exc()
            return 1
    
    # Run async command
    return asyncio.run(run_async_command())


if __name__ == "__main__":
    sys.exit(main())