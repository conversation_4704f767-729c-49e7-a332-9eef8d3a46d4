#!/usr/bin/env python3
"""
E2E Test Runner Script

Comprehensive test runner for End-to-End testing with different test suites,
environments, and reporting options.
"""

import os
import sys
import argparse
import subprocess
import time
import json
from typing import Dict, List, Optional
from pathlib import Path


class E2ETestRunner:
    """E2E Test Runner with comprehensive options"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.test_dir = self.project_root / "tests" / "e2e"
        self.reports_dir = self.project_root / "test_reports"
        self.reports_dir.mkdir(exist_ok=True)
        
        # Test suites configuration
        self.test_suites = {
            "smoke": {
                "description": "Quick smoke tests for basic functionality",
                "markers": ["user_journey", "not slow"],
                "timeout": 300
            },
            "integration": {
                "description": "Service integration tests",
                "markers": ["service_integration"],
                "timeout": 600
            },
            "performance": {
                "description": "Performance and load testing",
                "markers": ["performance"],
                "timeout": 1800
            },
            "resilience": {
                "description": "Error recovery and fallback tests",
                "markers": ["error_recovery"],
                "timeout": 900
            },
            "cache": {
                "description": "Cache behavior validation tests",
                "markers": ["cache_behavior"],
                "timeout": 600
            },
            "production": {
                "description": "Production-like scenario tests",
                "markers": ["production"],
                "timeout": 3600
            },
            "websocket": {
                "description": "WebSocket functionality tests",
                "markers": ["websocket"],
                "timeout": 600
            },
            "concurrent": {
                "description": "Concurrent execution tests",
                "markers": ["concurrent"],
                "timeout": 900
            },
            "full": {
                "description": "Complete E2E test suite",
                "markers": ["e2e"],
                "timeout": 7200
            }
        }
        
        # Environment configurations
        self.environments = {
            "local": {
                "service_url": "http://localhost:8000",
                "websocket_url": "ws://localhost:8000",
                "redis_url": "redis://localhost:6379",
                "enable_mocks": True
            },
            "dev": {
                "service_url": "http://dev.example.com",
                "websocket_url": "ws://dev.example.com",
                "redis_url": "redis://dev-redis.example.com:6379",
                "enable_mocks": False
            },
            "staging": {
                "service_url": "http://staging.example.com",
                "websocket_url": "ws://staging.example.com",
                "redis_url": "redis://staging-redis.example.com:6379",
                "enable_mocks": False
            },
            "production": {
                "service_url": "http://api.example.com",
                "websocket_url": "ws://api.example.com",
                "redis_url": "redis://prod-redis.example.com:6379",
                "enable_mocks": False
            }
        }
    
    def setup_environment(self, env_name: str, custom_config: Optional[Dict] = None):
        """Setup test environment variables"""
        if env_name not in self.environments:
            raise ValueError(f"Unknown environment: {env_name}")
        
        env_config = self.environments[env_name].copy()
        
        # Override with custom config if provided
        if custom_config:
            env_config.update(custom_config)
        
        # Set environment variables
        os.environ["E2E_SERVICE_URL"] = env_config["service_url"]
        os.environ["E2E_WEBSOCKET_URL"] = env_config["websocket_url"]
        os.environ["E2E_REDIS_URL"] = env_config["redis_url"]
        os.environ["E2E_ENABLE_MOCKS"] = str(env_config["enable_mocks"]).lower()
        
        # Additional environment variables
        os.environ["E2E_TEST_TIMEOUT"] = "300"
        os.environ["E2E_CONCURRENT_USERS"] = "10"
        os.environ["E2E_JWT_SECRET"] = "test_secret_key_for_e2e_testing"
        
        return env_config
    
    def build_pytest_command(self, suite: str, options: Dict) -> List[str]:
        """Build pytest command with appropriate options"""
        if suite not in self.test_suites:
            raise ValueError(f"Unknown test suite: {suite}")
        
        suite_config = self.test_suites[suite]
        
        # Base command
        cmd = [
            sys.executable, "-m", "pytest",
            str(self.test_dir)
        ]
        
        # Add markers
        for marker in suite_config["markers"]:
            cmd.extend(["-m", marker])
        
        # Add timeout
        cmd.extend(["--timeout", str(suite_config["timeout"])])
        
        # Add verbosity
        if options.get("verbose", False):
            cmd.append("-v")
        
        if options.get("very_verbose", False):
            cmd.append("-vv")
        
        # Add coverage
        if options.get("coverage", False):
            cmd.extend([
                "--cov=src/query_intelligence",
                "--cov-report=html",
                "--cov-report=term-missing"
            ])
        
        # Add parallel execution
        if options.get("parallel", False):
            cmd.extend(["-n", str(options.get("parallel_workers", 4))])
        
        # Add specific tests
        if options.get("test_pattern"):
            cmd.extend(["-k", options["test_pattern"]])
        
        # Add output options
        if options.get("json_report", False):
            json_report_path = self.reports_dir / f"e2e_results_{suite}_{int(time.time())}.json"
            cmd.extend(["--json-report", "--json-report-file", str(json_report_path)])
        
        if options.get("html_report", False):
            html_report_path = self.reports_dir / f"e2e_report_{suite}_{int(time.time())}.html"
            cmd.extend(["--html", str(html_report_path), "--self-contained-html"])
        
        # Add JUnit XML
        if options.get("junit_xml", False):
            xml_report_path = self.reports_dir / f"e2e_junit_{suite}_{int(time.time())}.xml"
            cmd.extend(["--junit-xml", str(xml_report_path)])
        
        # Add failure options
        if options.get("fail_fast", False):
            cmd.append("-x")
        
        if options.get("last_failed", False):
            cmd.append("--lf")
        
        if options.get("failed_first", False):
            cmd.append("--ff")
        
        # Add capture options
        if options.get("capture") == "no":
            cmd.append("-s")
        elif options.get("capture") == "sys":
            cmd.append("--capture=sys")
        
        # Add reruns for flaky tests
        if options.get("reruns", 0) > 0:
            cmd.extend(["--reruns", str(options["reruns"])])
        
        return cmd
    
    def run_health_check(self, env_config: Dict) -> bool:
        """Run health check on services"""
        import httpx
        import asyncio
        
        async def check_service_health():
            try:
                async with httpx.AsyncClient(timeout=10.0) as client:
                    response = await client.get(f"{env_config['service_url']}/health")
                    return response.status_code == 200
            except Exception as e:
                print(f"❌ Health check failed: {e}")
                return False
        
        print("🔍 Running health check...")
        try:
            healthy = asyncio.run(check_service_health())
            if healthy:
                print("✅ Service is healthy")
                return True
            else:
                print("❌ Service is not healthy")
                return False
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False
    
    def run_pre_test_validation(self, env_config: Dict) -> bool:
        """Run pre-test validation"""
        print("🔍 Running pre-test validation...")
        
        # Check if service is running
        if not self.run_health_check(env_config):
            return False
        
        # Check if test dependencies are installed
        try:
            import pytest
            import httpx
            import websockets
            import psutil
            print("✅ All dependencies are available")
        except ImportError as e:
            print(f"❌ Missing dependency: {e}")
            return False
        
        # Check if test directory exists
        if not self.test_dir.exists():
            print(f"❌ Test directory not found: {self.test_dir}")
            return False
        
        print("✅ Pre-test validation passed")
        return True
    
    def run_test_suite(self, suite: str, environment: str, options: Dict) -> int:
        """Run a specific test suite"""
        print(f"🚀 Starting E2E Test Suite: {suite}")
        print(f"📍 Environment: {environment}")
        print(f"🎯 Description: {self.test_suites[suite]['description']}")
        print("-" * 60)
        
        # Setup environment
        env_config = self.setup_environment(environment, options.get("custom_env"))
        
        # Run pre-test validation
        if not options.get("skip_validation", False):
            if not self.run_pre_test_validation(env_config):
                print("❌ Pre-test validation failed")
                return 1
        
        # Build pytest command
        cmd = self.build_pytest_command(suite, options)
        
        # Print command if verbose
        if options.get("verbose", False):
            print(f"🔧 Running command: {' '.join(cmd)}")
            print("-" * 60)
        
        # Run tests
        start_time = time.time()
        
        try:
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=options.get("capture_output", False),
                text=True
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            # Print results
            print("\\n" + "=" * 60)
            print(f"🏁 Test Suite Complete: {suite}")
            print(f"⏱️  Duration: {duration:.2f} seconds")
            print(f"📊 Exit Code: {result.returncode}")
            
            if result.returncode == 0:
                print("✅ All tests passed!")
            else:
                print("❌ Some tests failed")
                
                if options.get("capture_output", False):
                    print("\\n📋 STDOUT:")
                    print(result.stdout)
                    print("\\n📋 STDERR:")
                    print(result.stderr)
            
            print("=" * 60)
            
            return result.returncode
            
        except KeyboardInterrupt:
            print("\\n🛑 Test execution interrupted by user")
            return 130
        except Exception as e:
            print(f"\\n❌ Test execution error: {e}")
            return 1
    
    def run_multiple_suites(self, suites: List[str], environment: str, options: Dict) -> int:
        """Run multiple test suites"""
        print(f"🚀 Starting Multiple E2E Test Suites: {', '.join(suites)}")
        print(f"📍 Environment: {environment}")
        print("-" * 60)
        
        overall_result = 0
        suite_results = {}
        
        for suite in suites:
            print(f"\\n🎯 Running suite: {suite}")
            result = self.run_test_suite(suite, environment, options)
            suite_results[suite] = result
            
            if result != 0:
                overall_result = result
                
                if options.get("fail_fast", False):
                    print(f"❌ Stopping execution due to failure in {suite}")
                    break
        
        # Print summary
        print("\\n" + "=" * 60)
        print("📊 Multiple Test Suites Summary")
        print("=" * 60)
        
        for suite, result in suite_results.items():
            status = "✅ PASSED" if result == 0 else "❌ FAILED"
            print(f"{suite}: {status}")
        
        overall_status = "✅ ALL PASSED" if overall_result == 0 else "❌ SOME FAILED"
        print(f"\\nOverall: {overall_status}")
        print("=" * 60)
        
        return overall_result
    
    def list_test_suites(self):
        """List available test suites"""
        print("📋 Available E2E Test Suites:")
        print("=" * 60)
        
        for suite_name, suite_config in self.test_suites.items():
            print(f"🎯 {suite_name}")
            print(f"   Description: {suite_config['description']}")
            print(f"   Markers: {', '.join(suite_config['markers'])}")
            print(f"   Timeout: {suite_config['timeout']}s")
            print()
    
    def list_environments(self):
        """List available environments"""
        print("🌍 Available Environments:")
        print("=" * 60)
        
        for env_name, env_config in self.environments.items():
            print(f"📍 {env_name}")
            print(f"   Service URL: {env_config['service_url']}")
            print(f"   WebSocket URL: {env_config['websocket_url']}")
            print(f"   Redis URL: {env_config['redis_url']}")
            print(f"   Mocks Enabled: {env_config['enable_mocks']}")
            print()


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="E2E Test Runner for Query Intelligence Service",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # Test suite selection
    parser.add_argument(
        "suite",
        nargs="*",
        help="Test suite(s) to run (default: smoke)"
    )
    
    # Environment selection
    parser.add_argument(
        "--environment", "-e",
        default="local",
        help="Environment to test against (default: local)"
    )
    
    # Listing options
    parser.add_argument(
        "--list-suites",
        action="store_true",
        help="List available test suites"
    )
    
    parser.add_argument(
        "--list-environments",
        action="store_true",
        help="List available environments"
    )
    
    # Test execution options
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    
    parser.add_argument(
        "--very-verbose", "-vv",
        action="store_true",
        help="Very verbose output"
    )
    
    parser.add_argument(
        "--coverage",
        action="store_true",
        help="Generate coverage report"
    )
    
    parser.add_argument(
        "--parallel",
        action="store_true",
        help="Run tests in parallel"
    )
    
    parser.add_argument(
        "--parallel-workers",
        type=int,
        default=4,
        help="Number of parallel workers (default: 4)"
    )
    
    parser.add_argument(
        "--test-pattern", "-k",
        help="Run tests matching pattern"
    )
    
    # Reporting options
    parser.add_argument(
        "--json-report",
        action="store_true",
        help="Generate JSON report"
    )
    
    parser.add_argument(
        "--html-report",
        action="store_true",
        help="Generate HTML report"
    )
    
    parser.add_argument(
        "--junit-xml",
        action="store_true",
        help="Generate JUnit XML report"
    )
    
    # Failure handling
    parser.add_argument(
        "--fail-fast", "-x",
        action="store_true",
        help="Stop on first failure"
    )
    
    parser.add_argument(
        "--last-failed",
        action="store_true",
        help="Run only last failed tests"
    )
    
    parser.add_argument(
        "--failed-first",
        action="store_true",
        help="Run failed tests first"
    )
    
    parser.add_argument(
        "--reruns",
        type=int,
        default=0,
        help="Number of reruns for flaky tests"
    )
    
    # Output options
    parser.add_argument(
        "--capture",
        choices=["no", "sys", "fd"],
        help="Capture output mode"
    )
    
    parser.add_argument(
        "--capture-output",
        action="store_true",
        help="Capture subprocess output"
    )
    
    # Validation options
    parser.add_argument(
        "--skip-validation",
        action="store_true",
        help="Skip pre-test validation"
    )
    
    args = parser.parse_args()
    
    # Create runner
    runner = E2ETestRunner()
    
    # Handle listing options
    if args.list_suites:
        runner.list_test_suites()
        return 0
    
    if args.list_environments:
        runner.list_environments()
        return 0
    
    # Determine test suites to run
    suites = args.suite if args.suite else ["smoke"]
    
    # Validate suites
    invalid_suites = [s for s in suites if s not in runner.test_suites]
    if invalid_suites:
        print(f"❌ Invalid test suites: {', '.join(invalid_suites)}")
        print("Use --list-suites to see available suites")
        return 1
    
    # Validate environment
    if args.environment not in runner.environments:
        print(f"❌ Invalid environment: {args.environment}")
        print("Use --list-environments to see available environments")
        return 1
    
    # Build options
    options = {
        "verbose": args.verbose,
        "very_verbose": args.very_verbose,
        "coverage": args.coverage,
        "parallel": args.parallel,
        "parallel_workers": args.parallel_workers,
        "test_pattern": args.test_pattern,
        "json_report": args.json_report,
        "html_report": args.html_report,
        "junit_xml": args.junit_xml,
        "fail_fast": args.fail_fast,
        "last_failed": args.last_failed,
        "failed_first": args.failed_first,
        "reruns": args.reruns,
        "capture": args.capture,
        "capture_output": args.capture_output,
        "skip_validation": args.skip_validation
    }
    
    # Run tests
    if len(suites) == 1:
        return runner.run_test_suite(suites[0], args.environment, options)
    else:
        return runner.run_multiple_suites(suites, args.environment, options)


if __name__ == "__main__":
    sys.exit(main())