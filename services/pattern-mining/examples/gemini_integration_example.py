"""
Gemini API Integration Example

This example demonstrates how to use the Gemini API integration for complex pattern
reasoning, code analysis, and embedding generation.
"""

import async<PERSON>
import json
from typing import List, Dict, Any
from pathlib import Path

# Add the src directory to the path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from pattern_mining.ml.gemini_client import GeminiClient
from pattern_mining.ml.gemini_analyzer import GeminiAnalyzer, AnalysisType, CodeContext
from pattern_mining.ml.gemini_embeddings import GeminiEmbeddingService, EmbeddingType
from pattern_mining.ml.gemini_integration import GeminiIntegration, IntegrationMode
from pattern_mining.config.gemini import GeminiConfig


# Sample code for analysis
SAMPLE_CODE = """
class UserManager:
    def __init__(self, database):
        self.db = database
        self.users = []
    
    def create_user(self, name, email, password):
        # Validate input
        if not name or not email or not password:
            raise ValueError("All fields are required")
        
        # Check if user already exists
        existing_user = self.db.find_user_by_email(email)
        if existing_user:
            raise ValueError("User already exists")
        
        # Create new user
        user = {
            'id': len(self.users) + 1,
            'name': name,
            'email': email,
            'password': password,  # Should be hashed!
            'created_at': datetime.now()
        }
        
        self.users.append(user)
        self.db.save_user(user)
        return user
    
    def get_user(self, user_id):
        for user in self.users:
            if user['id'] == user_id:
                return user
        return None
    
    def update_user(self, user_id, **kwargs):
        user = self.get_user(user_id)
        if not user:
            raise ValueError("User not found")
        
        for key, value in kwargs.items():
            if key in user:
                user[key] = value
        
        self.db.save_user(user)
        return user
    
    def delete_user(self, user_id):
        user = self.get_user(user_id)
        if not user:
            raise ValueError("User not found")
        
        self.users.remove(user)
        self.db.delete_user(user_id)
        return True
"""

SAMPLE_SECURITY_CODE = """
import hashlib
import secrets
import sqlite3

def authenticate_user(username, password):
    # Vulnerable: SQL injection possible
    query = f"SELECT * FROM users WHERE username = '{username}' AND password = '{password}'"
    conn = sqlite3.connect('users.db')
    cursor = conn.cursor()
    cursor.execute(query)
    result = cursor.fetchone()
    conn.close()
    
    if result:
        return {"user_id": result[0], "username": result[1]}
    return None

def hash_password(password):
    # Weak: Using MD5 for password hashing
    return hashlib.md5(password.encode()).hexdigest()

def generate_session_token():
    # Predictable: Using simple timestamp
    import time
    return str(int(time.time()))

def store_user_data(user_data):
    # Insecure: Storing sensitive data without encryption
    with open('user_data.txt', 'w') as f:
        f.write(str(user_data))
"""


async def demonstrate_gemini_client():
    """Demonstrate basic Gemini client usage."""
    print("=== Gemini Client Demo ===")
    
    async with GeminiClient() as client:
        # Basic content generation
        response = await client.generate_content(
            prompt="Explain the Factory design pattern in Python with an example.",
            system_prompt="You are a software engineering expert."
        )
        
        print(f"Generated response: {response['text'][:200]}...")
        print(f"Tokens used: {response['usage']['total_tokens']}")
        
        # Code analysis
        analysis = await client.analyze_code(
            code=SAMPLE_CODE,
            language="python",
            analysis_type="pattern"
        )
        
        print(f"Code analysis: {json.dumps(analysis, indent=2)}")
        
        # Client metrics
        metrics = client.get_metrics()
        print(f"Client metrics: {json.dumps(metrics, indent=2)}")


async def demonstrate_gemini_analyzer():
    """Demonstrate advanced pattern analysis."""
    print("\n=== Gemini Analyzer Demo ===")
    
    async with GeminiAnalyzer() as analyzer:
        # Create code context
        context = CodeContext(
            code=SAMPLE_CODE,
            language="python",
            file_path="user_manager.py",
            project_context="User management system"
        )
        
        # Pattern detection
        print("1. Pattern Detection:")
        pattern_results = await analyzer.analyze_patterns(
            context, [AnalysisType.PATTERN_DETECTION]
        )
        
        for analysis_type, result in pattern_results.items():
            print(f"   {analysis_type}: Confidence {result.confidence:.2f}")
            print(f"   Findings: {len(result.findings)}")
            for finding in result.findings[:2]:  # Show first 2 findings
                print(f"     - {finding}")
        
        # Code explanation
        print("\n2. Code Explanation:")
        explanation = await analyzer.explain_code(
            context, audience="senior", detail_level="comprehensive"
        )
        print(f"   Explanation: {explanation['explanation'][:200]}...")
        
        # Anti-pattern detection
        print("\n3. Anti-pattern Detection:")
        anti_patterns = await analyzer.detect_anti_patterns(context)
        print(f"   Anti-patterns found: {anti_patterns['total_found']}")
        
        # Security analysis
        print("\n4. Security Analysis:")
        security_context = CodeContext(
            code=SAMPLE_SECURITY_CODE,
            language="python",
            file_path="auth.py"
        )
        security_results = await analyzer.analyze_security(security_context)
        print(f"   Vulnerabilities: {security_results['total_found']}")
        print(f"   High severity: {security_results['high_severity']}")


async def demonstrate_gemini_embeddings():
    """Demonstrate embedding generation and similarity search."""
    print("\n=== Gemini Embeddings Demo ===")
    
    async with GeminiEmbeddingService() as embeddings:
        # Generate embeddings
        print("1. Embedding Generation:")
        embedding, metadata = await embeddings.generate_embedding(
            content=SAMPLE_CODE,
            embedding_type=EmbeddingType.CODE,
            language="python"
        )
        
        print(f"   Embedding shape: {embedding.shape}")
        print(f"   Metadata: {metadata.embedding_id}")
        
        # Batch embedding generation
        print("\n2. Batch Embedding Generation:")
        code_samples = [
            "def factorial(n): return 1 if n <= 1 else n * factorial(n-1)",
            "class Calculator: def add(self, a, b): return a + b",
            "import math\ndef sqrt(x): return math.sqrt(x)"
        ]
        
        batch_results = await embeddings.batch_generate_embeddings(
            contents=code_samples,
            embedding_types=[EmbeddingType.FUNCTION] * 3,
            languages=["python"] * 3
        )
        
        print(f"   Generated {len(batch_results)} embeddings")
        
        # Add to index
        embeddings_list = [result[0] for result in batch_results]
        metadata_list = [result[1] for result in batch_results]
        await embeddings.add_to_index(embeddings_list, metadata_list)
        
        # Similarity search
        print("\n3. Similarity Search:")
        query = "recursive function implementation"
        results = await embeddings.search_similar(
            query_content=query,
            query_type=EmbeddingType.QUERY,
            k=2
        )
        
        print(f"   Found {len(results)} similar embeddings:")
        for result in results:
            print(f"     - Similarity: {result.similarity_score:.3f}")
            print(f"       Code: {result.metadata.source_code[:50]}...")
        
        # Clustering
        print("\n4. Clustering:")
        embedding_ids = [meta.embedding_id for meta in metadata_list]
        clusters = await embeddings.cluster_embeddings(embedding_ids, n_clusters=2)
        
        print(f"   Created {len(clusters)} clusters")
        for cluster in clusters:
            print(f"     - Cluster {cluster.cluster_id}: {cluster.cluster_size} items")
        
        # Service metrics
        metrics = embeddings.get_metrics()
        print(f"\n   Service metrics: {json.dumps(metrics, indent=2)}")


async def demonstrate_gemini_integration():
    """Demonstrate hybrid integration with local models."""
    print("\n=== Gemini Integration Demo ===")
    
    async with GeminiIntegration() as integration:
        # Hybrid analysis
        print("1. Hybrid Analysis:")
        context = CodeContext(
            code=SAMPLE_CODE,
            language="python",
            file_path="user_manager.py"
        )
        
        # Try different integration modes
        modes = [
            IntegrationMode.GEMINI_ONLY,
            IntegrationMode.HYBRID_PARALLEL,
            IntegrationMode.ENSEMBLE
        ]
        
        for mode in modes:
            print(f"\n   Mode: {mode.value}")
            result = await integration.analyze_code_hybrid(
                code=SAMPLE_CODE,
                language="python",
                integration_mode=mode
            )
            
            print(f"     Confidence: {result.combined_confidence:.3f}")
            print(f"     Processing time: {result.processing_time:.3f}s")
            print(f"     Tokens used: {result.tokens_used}")
            
            if result.confidence_breakdown:
                print(f"     Confidence breakdown: {result.confidence_breakdown}")
        
        # Hybrid embeddings
        print("\n2. Hybrid Embeddings:")
        embedding_result = await integration.generate_hybrid_embeddings(
            code="def fibonacci(n): return n if n <= 1 else fibonacci(n-1) + fibonacci(n-2)",
            language="python"
        )
        
        print(f"   Embedding shape: {embedding_result['embedding'].shape}")
        print(f"   Hybrid: {embedding_result['hybrid']}")
        print(f"   Confidence: {embedding_result['confidence']:.3f}")
        
        # Hybrid similarity search
        print("\n3. Hybrid Similarity Search:")
        search_results = await integration.search_similar_hybrid(
            query_code="recursive function",
            language="python",
            k=3
        )
        
        print(f"   Found {len(search_results)} results")
        for result in search_results:
            print(f"     - Source: {result['source']}")
            print(f"       Similarity: {result['similarity_score']:.3f}")
        
        # Integration metrics
        metrics = integration.get_metrics()
        print(f"\n   Integration metrics: {json.dumps(metrics, indent=2)}")


async def demonstrate_performance_comparison():
    """Compare performance between different approaches."""
    print("\n=== Performance Comparison ===")
    
    import time
    
    # Test code
    test_code = """
    def bubble_sort(arr):
        n = len(arr)
        for i in range(n):
            for j in range(0, n - i - 1):
                if arr[j] > arr[j + 1]:
                    arr[j], arr[j + 1] = arr[j + 1], arr[j]
        return arr
    
    def quick_sort(arr):
        if len(arr) <= 1:
            return arr
        
        pivot = arr[len(arr) // 2]
        left = [x for x in arr if x < pivot]
        middle = [x for x in arr if x == pivot]
        right = [x for x in arr if x > pivot]
        
        return quick_sort(left) + middle + quick_sort(right)
    """
    
    async with GeminiIntegration() as integration:
        # Performance tests
        methods = [
            ("Gemini Only", IntegrationMode.GEMINI_ONLY),
            ("Hybrid Parallel", IntegrationMode.HYBRID_PARALLEL),
            ("Ensemble", IntegrationMode.ENSEMBLE)
        ]
        
        results = {}
        
        for method_name, mode in methods:
            print(f"\n   Testing {method_name}:")
            start_time = time.time()
            
            result = await integration.analyze_code_hybrid(
                code=test_code,
                language="python",
                integration_mode=mode,
                analysis_types=[AnalysisType.PATTERN_DETECTION, AnalysisType.PERFORMANCE_ANALYSIS]
            )
            
            end_time = time.time()
            
            results[method_name] = {
                "processing_time": end_time - start_time,
                "confidence": result.combined_confidence,
                "tokens_used": result.tokens_used,
                "gemini_time": result.gemini_processing_time,
                "local_time": result.local_processing_time
            }
            
            print(f"     Total time: {results[method_name]['processing_time']:.3f}s")
            print(f"     Confidence: {results[method_name]['confidence']:.3f}")
            print(f"     Tokens used: {results[method_name]['tokens_used']}")
        
        # Compare results
        print("\n   Performance Summary:")
        for method_name, metrics in results.items():
            print(f"     {method_name}:")
            print(f"       Time: {metrics['processing_time']:.3f}s")
            print(f"       Confidence: {metrics['confidence']:.3f}")
            print(f"       Efficiency: {metrics['confidence'] / metrics['processing_time']:.3f}")


async def main():
    """Run all demonstrations."""
    print("Gemini API Integration Demonstration")
    print("=" * 50)
    
    try:
        await demonstrate_gemini_client()
        await demonstrate_gemini_analyzer()
        await demonstrate_gemini_embeddings()
        await demonstrate_gemini_integration()
        await demonstrate_performance_comparison()
        
        print("\n" + "=" * 50)
        print("All demonstrations completed successfully!")
        
    except Exception as e:
        print(f"\nError during demonstration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Set up environment
    import os
    
    # Check for API key
    if not os.getenv("GEMINI_API_KEY") and not os.getenv("GOOGLE_API_KEY"):
        print("Warning: GEMINI_API_KEY or GOOGLE_API_KEY environment variable not set")
        print("Some features may not work without proper authentication")
    
    # Run the demonstration
    asyncio.run(main())