# Pattern Mining Service .gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
.venv/

# IDE
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store

# Environment
.env
.env.*
!.env.example

# Testing
.pytest_cache/
.coverage
.coverage.*
coverage.xml
htmlcov/
.tox/
.mypy_cache/
.ruff_cache/
.hypothesis/

# Logs
logs/
*.log

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/
*.tmp

# Build artifacts
*.pyc
*.pyo

# Docker
docker-compose.override.yml

# Ray
ray_results/
ray_temp/

# ML Models
models/
*.pkl
*.joblib
*.h5
*.pt
*.pth

# Cache
.cache/
redis-data/

# OS
.DS_Store
Thumbs.db

# Jupyter
.ipynb_checkpoints/
*.ipynb

# Archives (keep in repo for history)
# archive/