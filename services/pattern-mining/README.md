# Pattern Mining Service

ML/AI-powered code pattern detection service for the CCL (Codebase Context Layer) platform. This service analyzes code repositories to identify patterns, best practices, and potential improvements using advanced machine learning models.

## ✅ Production Status - July 2025

**The Pattern Mining service is production-ready with 85% security audit completion.**

### Quick Status Overview
- ✅ **Gemini 2.5 Integration**: Flash model with thinking mode enabled
- ✅ **Security Phase 1.1**: Secret management with 24-hour rotation completed
- ✅ **Security Phase 1.2**: Configuration access control (165+ parameters) completed  
- ✅ **Test Coverage**: 95% for security components
- ✅ **Performance**: Processes 1M+ LOC/minute with Ray cluster
- 🔄 **In Progress**: Phase 1.3 - Ray authentication & Redis encryption (15% remaining)

## 📚 Complete Documentation

For comprehensive documentation, please refer to our centralized documentation structure:

### 🚀 Quick Start
- **[Central Documentation Hub](/docs/pattern-mining/index.md)** - Complete overview and navigation
- **[Developer Guide](/docs/pattern-mining/guides/developer-guide.md)** - Development setup and contribution guidelines
- **[API Reference](/docs/pattern-mining/api/README.md)** - Complete REST API documentation

### 🔧 Operations & Deployment
- **[Operations Runbook](/docs/pattern-mining/operations-runbook.md)** - Production operations and monitoring
- **[Architecture Documentation](/docs/pattern-mining/architecture/README.md)** - System design and ML models
- **[Troubleshooting Guide](/docs/pattern-mining/troubleshooting/README.md)** - Common issues and solutions

### 📋 Security & Compliance
- **[Security Architecture](/docs/pattern-mining/architecture/security-architecture.md)** - Security implementation details
- **[Security Audit Report](SECURITY_AUDIT_REPORT.md)** - Latest audit findings (85/100 score)

## Service Overview

The Pattern Mining service is a production-ready Python microservice that:
- Detects code patterns using Google Gemini 2.5 Flash with thinking mode
- Analyzes 18+ programming languages with AST parsing
- Provides real-time and batch processing capabilities
- Implements enterprise-grade security with RBAC
- Scales horizontally with Ray distributed computing

## Quick Architecture Overview

### Technology Stack
- **Language**: Python 3.11+ with FastAPI
- **AI/ML**: Google Gemini 2.5 Flash, Vertex AI
- **Processing**: Ray cluster for distributed computing
- **Storage**: Redis (cache), BigQuery (analytics), Spanner (transactional)
- **Runtime**: Cloud Run with auto-scaling (2-50 instances)

### Service Integration
- **Dependencies**: analysis-engine (AST data)
- **Consumers**: marketplace, web-ui
- **API Types**: REST (sync) with batch processing
- **Security**: JWT auth, 7-role RBAC, audit logging
- **Performance**: <5ms cached, 95% cache hit rate

> **📖 For detailed architecture information, see [Architecture Documentation](/docs/pattern-mining/architecture/README.md)**

## Key Features

### Production Capabilities ✅
- **Pattern Detection**: 50+ pattern types with 90%+ accuracy
- **Multi-Language**: AST-based analysis for 18+ languages
- **AI Security**: Prompt injection protection, response validation
- **Distributed Processing**: Ray cluster with 5-100 workers
- **Enterprise Security**: Secret rotation, parameter-level permissions
- **High Performance**: 1M+ LOC/minute processing capacity

> **📖 For complete feature documentation and examples, see [API Reference](/docs/pattern-mining/api/README.md)**

## API Quick Reference

### Pattern Analysis Example
```http
POST /api/v1/patterns/analyze
Authorization: Bearer <token>
Content-Type: application/json

{
  "repository_url": "https://github.com/org/repo",
  "languages": ["python", "javascript"],
  "analysis_types": ["patterns", "anomalies"]
}
```

### Health Endpoints
- `GET /health` - Service health check
- `GET /security/status` - Security status
- `GET /metrics` - Prometheus metrics

> **📖 For complete API documentation, see [API Reference](/docs/pattern-mining/api/README.md)**

## Configuration

### Key Environment Variables

```bash
# Core Configuration
ENVIRONMENT=production|development
GCP_PROJECT_ID=your-project-id
GEMINI_API_KEY=your-api-key  # Rotated every 24h

# Processing
RAY_CLUSTER_ADDRESS=ray://ray-head:10001
REDIS_URL=redis://redis:6379
BIGQUERY_DATASET=pattern_mining

# Security (Phase 1.2 Complete)
CONFIG_ACCESS_CONTROL_ENABLED=true
CONFIG_AUDIT_RETENTION_DAYS=90
SECRET_ROTATION_ENABLED=true
```

### Quick Setup

```bash
# Copy secure configuration template
cp .env.example .env

# Edit with your settings (ALL values must be changed)
nano .env
```

> **⚠️ SECURITY WARNING: See [SECURITY_WARNING.md](SECURITY_WARNING.md) for critical security configuration**

## Local Development

### Quick Start

```bash
# Clone and setup
git clone https://github.com/episteme/ccl.git
cd ccl/services/pattern-mining

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Configure environment
cp .env.example .env
# Edit .env - MUST change all INSECURE_DEFAULT values

# Start services
docker-compose up -d
uvicorn src.pattern_mining.main:app --reload
```

> **📖 For complete development setup, see [Developer Guide](/docs/pattern-mining/guides/developer-guide.md)**

## Security & Monitoring

### Security Status
- **Score**: 85/100 (July 2025 audit)
- **Secret Management**: ✅ Automated rotation
- **Access Control**: ✅ 7-role RBAC implemented
- **Audit Logging**: ✅ 90-day retention
- **Remaining**: Ray auth, Redis encryption

### Monitoring
- **Metrics**: `/metrics` (Prometheus format)
- **Dashboard**: `/security/dashboard`
- **Alerts**: Automated via Cloud Monitoring

> **📖 For security procedures, see [Security Architecture](/docs/pattern-mining/architecture/security-architecture.md)**

## Support

- **Operations**: See [Operations Runbook](/docs/pattern-mining/operations-runbook.md)
- **Troubleshooting**: See [Troubleshooting Guide](/docs/pattern-mining/troubleshooting/README.md)
- **On-Call**: Check PagerDuty for current on-call
- **Team Channel**: #pattern-mining-support
