# 🔧 Pattern Mining Operations Guide

This comprehensive guide covers deployment, database migrations, monitoring, and operational procedures for the Pattern Mining service.

## 📑 Table of Contents

1. [Deployment](#deployment)
2. [Database & Migrations](#database--migrations)
3. [Monitoring & Observability](#monitoring--observability)
4. [GCP Permissions & IAM](#gcp-permissions--iam)
5. [Security Operations](#security-operations)
6. [Troubleshooting](#troubleshooting)

---

## 🚀 Deployment

### Overview

The Pattern Mining service supports multiple deployment strategies:
- **Standard deployment** for development/testing
- **Cloud Run deployment** for production (auto-scaling 2-50 instances)
- **Canary deployments** with traffic splitting
- **Multi-region deployment** for disaster recovery

### Docker Build & Deploy

#### Development Build
```bash
# Build for development (with debugging tools)
docker build -t pattern-mining:dev -f Dockerfile.dev .

# Run locally with all services
docker-compose up -d

# Or run standalone
docker run -d \
  --name pattern-mining \
  -p 8000:8000 \
  -e GEMINI_API_KEY="your-key" \
  -e REDIS_URL="redis://localhost:6379" \
  pattern-mining:dev
```

#### Production Build
```bash
# Build optimized production image
docker build -t pattern-mining:prod \
  --build-arg BUILD_ENV=production \
  --target production .

# Tag for GCR
docker tag pattern-mining:prod gcr.io/${PROJECT_ID}/pattern-mining:${VERSION}

# Push to registry
docker push gcr.io/${PROJECT_ID}/pattern-mining:${VERSION}
```

### Cloud Run Deployment

```bash
# Deploy to Cloud Run
gcloud run deploy pattern-mining \
  --image gcr.io/${PROJECT_ID}/pattern-mining:${VERSION} \
  --platform managed \
  --region us-central1 \
  --service-account pattern-mining-sa@${PROJECT_ID}.iam.gserviceaccount.com \
  --set-secrets="GEMINI_API_KEY=gemini-api-key:latest" \
  --set-secrets="JWT_SECRET=jwt-secret:latest" \
  --vpc-connector=pattern-mining-connector \
  --min-instances=2 \
  --max-instances=50 \
  --cpu=2 \
  --memory=4Gi \
  --timeout=300 \
  --concurrency=100
```

### Deployment Scripts

```bash
# One-time setup
./scripts/setup/initialize_gcp.sh

# Deploy to production with canary
./scripts/deployment/deploy_canary.sh v2.0.0

# Complete rollout after validation
./scripts/deployment/promote_canary.sh

# Emergency rollback
./scripts/deployment/rollback.sh
```

### Environment Variables

```bash
# Core Configuration
export ENVIRONMENT="production"
export GCP_PROJECT_ID="episteme-prod"
export GCP_REGION="us-central1"

# AI/ML Configuration
export GEMINI_API_KEY="<from-secret-manager>"
export GEMINI_MODEL="gemini-2.5-flash"
export VERTEX_AI_LOCATION="us-central1"
export ENABLE_PROMPT_INJECTION_PROTECTION=true

# Data Storage
export REDIS_URL="redis://********:6379"
export BIGQUERY_DATASET="pattern_mining"
export SPANNER_INSTANCE="pattern-mining-prod"
export SPANNER_DATABASE="pattern-mining"

# Processing Configuration
export RAY_CLUSTER_ADDRESS="ray://ray-head:10001"
export MAX_WORKERS=100
export BATCH_SIZE=1000
export PROCESSING_TIMEOUT=300

# Security Configuration
export SECRET_ROTATION_ENABLED=true
export CONFIG_ACCESS_CONTROL_ENABLED=true
export CONFIG_AUDIT_RETENTION_DAYS=90
export SECURITY_MONITORING_ENABLED=true

# Performance Tuning
export CACHE_TTL_PATTERNS=3600
export CACHE_TTL_EMBEDDINGS=86400
export DATABASE_POOL_SIZE=20
export DATABASE_MAX_OVERFLOW=40
```

---

## 🗄️ Database & Migrations

### PostgreSQL Configuration

```bash
# Connection settings
DATABASE_URL="postgresql://pattern_mining:${DB_PASSWORD}@${DB_HOST}:5432/pattern_mining"

# Run migrations
alembic upgrade head

# Create new migration
alembic revision --autogenerate -m "Add pattern confidence index"

# Rollback one revision
alembic downgrade -1
```

### BigQuery Setup

```bash
# Create dataset
bq mk --dataset \
  --location=US \
  --description="Pattern Mining cache and analytics" \
  ${PROJECT_ID}:pattern_mining

# Create tables
bq mk --table \
  ${PROJECT_ID}:pattern_mining.pattern_cache \
  schemas/bigquery/pattern_cache.json

bq mk --table \
  ${PROJECT_ID}:pattern_mining.analysis_metrics \
  schemas/bigquery/analysis_metrics.json
```

### Spanner Configuration

```bash
# Create instance
gcloud spanner instances create pattern-mining-prod \
  --config=regional-us-central1 \
  --description="Pattern Mining production database" \
  --nodes=3

# Create database
gcloud spanner databases create pattern-mining \
  --instance=pattern-mining-prod

# Apply schema
gcloud spanner databases ddl update pattern-mining \
  --instance=pattern-mining-prod \
  --ddl-file=schemas/spanner/schema.sql
```

### Data Migration Scripts

```bash
# Backup production data
./scripts/database/backup_production.sh

# Migrate data between environments
./scripts/database/migrate_data.sh staging production

# Verify data integrity
./scripts/database/verify_integrity.sh
```

---

## 📊 Monitoring & Observability

### Key Metrics

| Metric | Target | Alert Threshold |
|--------|--------|-----------------|
| Request Latency (p95) | <200ms | >500ms |
| Pattern Detection Accuracy | >90% | <85% |
| Cache Hit Rate | >90% | <80% |
| Error Rate | <0.1% | >1% |
| Ray Cluster Utilization | 60-80% | >90% |
| Gemini API Success Rate | >99.9% | <99% |

### Monitoring Setup

```bash
# Create uptime check
gcloud monitoring uptime create pattern-mining-health \
  --display-name="Pattern Mining Health Check" \
  --uri="https://pattern-mining.ccl-platform.com/health" \
  --check-interval=60s

# Create alert policies
./scripts/monitoring/create_alerts.sh

# View dashboards
echo "https://console.cloud.google.com/monitoring/dashboards/custom/pattern-mining"
```

### Log Queries

```bash
# View recent errors
gcloud logging read \
  'resource.labels.service_name="pattern-mining" AND severity>=ERROR' \
  --limit=50 \
  --format=json

# Pattern detection performance
gcloud logging read \
  'jsonPayload.event="pattern_detection_complete"' \
  --format="table(timestamp,jsonPayload.duration_ms,jsonPayload.patterns_found)"

# Security events
gcloud logging read \
  'jsonPayload.event_type="security_event"' \
  --format="table(timestamp,jsonPayload.event,jsonPayload.user_id)"
```

### Custom Metrics

```python
# Prometheus metrics exposed at /metrics
pattern_detection_duration_seconds
pattern_detection_total
cache_hit_rate
gemini_api_calls_total
security_events_total
configuration_changes_total
```

---

## 🔐 GCP Permissions & IAM

### Service Account Setup

```bash
# Create service account
gcloud iam service-accounts create pattern-mining-sa \
  --display-name="Pattern Mining Service Account"

# Required roles
gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:pattern-mining-sa@${PROJECT_ID}.iam.gserviceaccount.com" \
  --role="roles/bigquery.dataEditor"

gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:pattern-mining-sa@${PROJECT_ID}.iam.gserviceaccount.com" \
  --role="roles/spanner.databaseUser"

gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:pattern-mining-sa@${PROJECT_ID}.iam.gserviceaccount.com" \
  --role="roles/secretmanager.secretAccessor"

gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:pattern-mining-sa@${PROJECT_ID}.iam.gserviceaccount.com" \
  --role="roles/aiplatform.user"
```

### Required APIs

```bash
# Enable required APIs
gcloud services enable \
  cloudrun.googleapis.com \
  cloudbuild.googleapis.com \
  secretmanager.googleapis.com \
  bigquery.googleapis.com \
  spanner.googleapis.com \
  redis.googleapis.com \
  aiplatform.googleapis.com \
  generativelanguage.googleapis.com
```

---

## 🛡️ Security Operations

### Secret Rotation

```bash
# Rotate Gemini API key (automatic every 24h)
./scripts/security/rotate_gemini_key.sh

# Force immediate rotation
curl -X POST http://localhost:8000/security/rotate-keys \
  -H "Authorization: Bearer ${ADMIN_TOKEN}"

# Verify rotation status
gcloud secrets versions list gemini-api-key
```

### Security Monitoring

```bash
# Check security status
curl https://pattern-mining.ccl-platform.com/security/status

# View audit logs
gcloud logging read \
  'protoPayload.serviceName="secretmanager.googleapis.com" AND 
   protoPayload.resourceName=~"gemini-api-key"' \
  --limit=20

# Monitor configuration changes
curl https://pattern-mining.ccl-platform.com/api/v1/config/audit \
  -H "Authorization: Bearer ${SECURITY_ADMIN_TOKEN}"
```

### Access Control

```bash
# List user permissions
curl https://pattern-mining.ccl-platform.com/api/v1/users/me/permissions \
  -H "Authorization: Bearer ${USER_TOKEN}"

# Grant role to user
./scripts/security/grant_role.sh <EMAIL> OPERATOR

# Revoke access
./scripts/security/revoke_access.sh <EMAIL>
```

---

## 🔧 Troubleshooting

### Common Issues

#### Service Won't Start
```bash
# Check startup logs
gcloud logging read \
  'resource.labels.service_name="pattern-mining" AND 
   textPayload:"Starting Pattern Mining Service"' \
  --limit=50

# Verify environment variables
gcloud run services describe pattern-mining \
  --format="yaml(spec.template.spec.containers[0].env)"

# Test database connectivity
./scripts/test/test_connections.sh
```

#### High Memory Usage
```bash
# Check memory metrics
gcloud monitoring read \
  --filter='metric.type="run.googleapis.com/container/memory/utilization"
           AND resource.labels.service_name="pattern-mining"' \
  --format=table

# Force garbage collection
curl -X POST http://localhost:8000/admin/gc \
  -H "Authorization: Bearer ${ADMIN_TOKEN}"
```

#### Ray Cluster Issues
```bash
# Check Ray status
ray status --address=${RAY_ADDRESS}

# View Ray dashboard
kubectl port-forward service/ray-head 8265:8265
# Open http://localhost:8265

# Restart Ray cluster
ray stop --force
ray start --head --dashboard-host=0.0.0.0
```

### Emergency Procedures

```bash
# Complete service restart
./scripts/emergency/restart_all.sh

# Switch to DR region
./scripts/emergency/failover_dr.sh

# Restore from backup
./scripts/emergency/restore_backup.sh 2025-01-10

# Enable read-only mode
curl -X POST http://localhost:8000/admin/readonly \
  -H "Authorization: Bearer ${ADMIN_TOKEN}"
```

### Performance Tuning

```bash
# Analyze slow queries
./scripts/performance/analyze_slow_queries.sh

# Optimize cache settings
./scripts/performance/tune_cache.sh

# Scale Ray workers
ray submit --address=${RAY_ADDRESS} \
  scripts/performance/scale_workers.py --num-workers=50
```

---

## 📞 Support Contacts

- **On-Call Engineer**: Check PagerDuty
- **Team Lead**: <EMAIL>
- **Security Team**: <EMAIL>
- **GCP Support**: 1-877-355-5787 (Priority support)

For detailed troubleshooting procedures, see the [Troubleshooting Guide](/docs/pattern-mining/troubleshooting/README.md).