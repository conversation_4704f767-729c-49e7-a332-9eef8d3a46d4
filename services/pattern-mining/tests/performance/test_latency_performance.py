"""
Performance Tests for Latency Requirements

Tests to ensure the pattern-mining service meets latency requirements:
- <50ms inference latency
- <100ms API response time
- <200ms batch processing per item
"""

import pytest
import asyncio
import time
from typing import Dict, Any, List
from unittest.mock import AsyncMock, patch
from datetime import datetime, timedelta
import statistics

from httpx import AsyncClient
from tests.utils.performance import (
    PerformanceTestRunner, LatencyMeasurer, benchmark, performance_test
)
from tests.utils.generators import CodeGenerator, PatternGenerator


@pytest.mark.performance
class TestInferenceLatency:
    """Test ML inference latency requirements."""
    
    @pytest.mark.asyncio
    @performance_test(sla_config={"max_duration_ms": 50})
    async def test_ml_inference_latency(
        self,
        async_test_client: AsyncClient,
        latency_measurer: LatencyMeasurer
    ):
        """Test ML inference latency is under 50ms."""
        code_generator = CodeGenerator()
        
        # Generate test code
        test_code = code_generator.generate_function()
        
        # Prepare request
        request_data = {
            "repository_id": "latency-test-repo",
            "ast_data": {"type": "Module", "children": []},
            "code_content": test_code,
            "file_path": "test.py",
            "language": "python",
            "detection_config": {
                "confidence_threshold": 0.7,
                "enable_ml_models": True,
                "enable_heuristic_detection": False  # Only ML for latency test
            }
        }
        
        # Mock ML inference with realistic processing time
        with patch("pattern_mining.api.routes.patterns.get_pattern_detector_manager") as mock_detector:
            mock_detector_instance = AsyncMock()
            
            async def simulate_ml_inference():
                # Simulate ML inference processing
                await asyncio.sleep(0.03)  # 30ms - within 50ms limit
                return [
                    {
                        "pattern_id": "inference-pattern",
                        "pattern_name": "Test Pattern",
                        "pattern_type": "DESIGN_PATTERN",
                        "severity": "LOW",
                        "confidence": 0.85,
                        "location": {"file": "test.py", "line": 1, "column": 1},
                        "description": "ML inference test pattern",
                        "detection_method": "ML_INFERENCE",
                        "context": {},
                        "metadata": {"model_version": "1.0.0", "inference_time_ms": 30}
                    }
                ]
            
            mock_detector_instance.detect_patterns.side_effect = simulate_ml_inference
            mock_detector_instance.get_model_versions.return_value = {"ml_inference": "1.0.0"}
            mock_detector.return_value = mock_detector_instance
            
            # Measure inference latency
            with latency_measurer.measure("ml_inference"):
                response = await async_test_client.post("/api/v1/patterns/detect", json=request_data)
            
            # Verify response
            assert response.status_code == 200
            data = response.json()
            assert "patterns" in data
            assert len(data["patterns"]) == 1
            assert data["patterns"][0]["detection_method"] == "ML_INFERENCE"
            
            # Verify latency requirements
            latency_analysis = latency_measurer.get_latency_analysis()
            assert latency_analysis["avg_latency_ms"] < 50
            assert latency_analysis["max_latency_ms"] < 50
            assert latency_analysis["meets_target_percentage"] == 100
    
    @pytest.mark.asyncio
    @benchmark(iterations=100)
    async def test_ml_inference_latency_benchmark(
        self,
        async_test_client: AsyncClient
    ):
        """Benchmark ML inference latency over multiple iterations."""
        code_generator = CodeGenerator()
        
        # Generate test code
        test_code = code_generator.generate_function()
        
        # Prepare request
        request_data = {
            "repository_id": "benchmark-test-repo",
            "ast_data": {"type": "Module", "children": []},
            "code_content": test_code,
            "file_path": "test.py",
            "language": "python",
            "detection_config": {
                "confidence_threshold": 0.7,
                "enable_ml_models": True,
                "enable_heuristic_detection": False
            }
        }
        
        # Mock ML inference with consistent timing
        with patch("pattern_mining.api.routes.patterns.get_pattern_detector_manager") as mock_detector:
            mock_detector_instance = AsyncMock()
            
            async def simulate_consistent_inference():
                # Simulate consistent ML inference
                await asyncio.sleep(0.025)  # 25ms - well within limit
                return [
                    {
                        "pattern_id": "benchmark-pattern",
                        "pattern_name": "Benchmark Pattern",
                        "pattern_type": "DESIGN_PATTERN",
                        "severity": "LOW",
                        "confidence": 0.8,
                        "location": {"file": "test.py", "line": 1, "column": 1},
                        "description": "Benchmark test pattern",
                        "detection_method": "ML_INFERENCE",
                        "context": {},
                        "metadata": {"model_version": "1.0.0"}
                    }
                ]
            
            mock_detector_instance.detect_patterns.side_effect = simulate_consistent_inference
            mock_detector_instance.get_model_versions.return_value = {"ml_inference": "1.0.0"}
            mock_detector.return_value = mock_detector_instance
            
            # Run benchmark
            response = await async_test_client.post("/api/v1/patterns/detect", json=request_data)
            
            # Verify response
            assert response.status_code == 200
            data = response.json()
            assert "patterns" in data
            
            # Return response for benchmark analysis
            return response
    
    @pytest.mark.asyncio
    async def test_ml_inference_under_load(
        self,
        async_test_client: AsyncClient,
        latency_measurer: LatencyMeasurer
    ):
        """Test ML inference latency under concurrent load."""
        code_generator = CodeGenerator()
        
        # Generate multiple test cases
        test_cases = []
        for i in range(20):
            test_code = code_generator.generate_function()
            test_cases.append({
                "repository_id": f"load-test-repo-{i}",
                "ast_data": {"type": "Module", "children": []},
                "code_content": test_code,
                "file_path": f"test_{i}.py",
                "language": "python",
                "detection_config": {
                    "confidence_threshold": 0.7,
                    "enable_ml_models": True,
                    "enable_heuristic_detection": False
                }
            })
        
        # Mock ML inference
        with patch("pattern_mining.api.routes.patterns.get_pattern_detector_manager") as mock_detector:
            mock_detector_instance = AsyncMock()
            
            async def simulate_loaded_inference():
                # Simulate slightly slower inference under load
                await asyncio.sleep(0.04)  # 40ms - still within limit
                return [
                    {
                        "pattern_id": "load-pattern",
                        "pattern_name": "Load Test Pattern",
                        "pattern_type": "DESIGN_PATTERN",
                        "severity": "LOW",
                        "confidence": 0.8,
                        "location": {"file": "test.py", "line": 1, "column": 1},
                        "description": "Load test pattern",
                        "detection_method": "ML_INFERENCE",
                        "context": {},
                        "metadata": {"model_version": "1.0.0"}
                    }
                ]
            
            mock_detector_instance.detect_patterns.side_effect = simulate_loaded_inference
            mock_detector_instance.get_model_versions.return_value = {"ml_inference": "1.0.0"}
            mock_detector.return_value = mock_detector_instance
            
            # Run concurrent requests
            tasks = []
            for test_case in test_cases:
                with latency_measurer.measure(f"concurrent_inference_{len(tasks)}"):
                    task = async_test_client.post("/api/v1/patterns/detect", json=test_case)
                    tasks.append(task)
            
            # Wait for all requests to complete
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Verify all requests succeeded
            success_count = 0
            for response in responses:
                if not isinstance(response, Exception) and response.status_code == 200:
                    success_count += 1
            
            assert success_count >= 18  # At least 90% success rate
            
            # Verify latency requirements under load
            latency_analysis = latency_measurer.get_latency_analysis()
            assert latency_analysis["avg_latency_ms"] < 60  # Slightly higher under load
            assert latency_analysis["p95_latency_ms"] < 80  # P95 should still be reasonable
            assert latency_analysis["meets_target_percentage"] >= 90  # At least 90% meet target
    
    @pytest.mark.asyncio
    async def test_ml_inference_different_code_sizes(
        self,
        async_test_client: AsyncClient,
        latency_measurer: LatencyMeasurer
    ):
        """Test ML inference latency with different code sizes."""
        code_generator = CodeGenerator()
        
        # Generate test cases with different code sizes
        test_cases = [
            {
                "name": "small_code",
                "code": code_generator.generate_function(),
                "expected_latency": 30  # ms
            },
            {
                "name": "medium_code",
                "code": code_generator.generate_file_content(file_type="module", pattern_count=5),
                "expected_latency": 40  # ms
            },
            {
                "name": "large_code",
                "code": code_generator.generate_file_content(file_type="module", pattern_count=10),
                "expected_latency": 50  # ms
            }
        ]
        
        # Mock ML inference with size-dependent timing
        with patch("pattern_mining.api.routes.patterns.get_pattern_detector_manager") as mock_detector:
            mock_detector_instance = AsyncMock()
            
            async def simulate_size_dependent_inference():
                # Simulate processing time based on code size
                code_content = mock_detector_instance.detect_patterns.call_args[0][0].get("code_content", "")
                code_size = len(code_content)
                
                if code_size < 500:
                    await asyncio.sleep(0.025)  # 25ms for small code
                elif code_size < 2000:
                    await asyncio.sleep(0.035)  # 35ms for medium code
                else:
                    await asyncio.sleep(0.045)  # 45ms for large code
                
                return [
                    {
                        "pattern_id": "size-test-pattern",
                        "pattern_name": "Size Test Pattern",
                        "pattern_type": "DESIGN_PATTERN",
                        "severity": "LOW",
                        "confidence": 0.8,
                        "location": {"file": "test.py", "line": 1, "column": 1},
                        "description": "Size test pattern",
                        "detection_method": "ML_INFERENCE",
                        "context": {"code_size": code_size},
                        "metadata": {"model_version": "1.0.0"}
                    }
                ]
            
            mock_detector_instance.detect_patterns.side_effect = simulate_size_dependent_inference
            mock_detector_instance.get_model_versions.return_value = {"ml_inference": "1.0.0"}
            mock_detector.return_value = mock_detector_instance
            
            # Test each code size
            for test_case in test_cases:
                request_data = {
                    "repository_id": "size-test-repo",
                    "ast_data": {"type": "Module", "children": []},
                    "code_content": test_case["code"],
                    "file_path": "test.py",
                    "language": "python",
                    "detection_config": {
                        "confidence_threshold": 0.7,
                        "enable_ml_models": True,
                        "enable_heuristic_detection": False
                    }
                }
                
                with latency_measurer.measure(test_case["name"]):
                    response = await async_test_client.post("/api/v1/patterns/detect", json=request_data)
                
                # Verify response
                assert response.status_code == 200
                data = response.json()
                assert "patterns" in data
                
                # Verify latency is within expected range
                latency_analysis = latency_measurer.get_latency_analysis()
                measurements = [m for m in latency_measurer.measurements if m["operation"] == test_case["name"]]
                if measurements:
                    latest_measurement = measurements[-1]
                    assert latest_measurement["latency_ms"] < test_case["expected_latency"] + 10  # 10ms tolerance
                    assert latest_measurement["latency_ms"] > test_case["expected_latency"] - 10  # 10ms tolerance


@pytest.mark.performance
class TestAPIResponseTime:
    """Test API response time requirements."""
    
    @pytest.mark.asyncio
    @performance_test(sla_config={"max_duration_ms": 100})
    async def test_api_response_time(
        self,
        async_test_client: AsyncClient,
        latency_measurer: LatencyMeasurer
    ):
        """Test API response time is under 100ms."""
        # Test single pattern detection
        request_data = {
            "repository_id": "api-test-repo",
            "ast_data": {"type": "Module", "children": []},
            "code_content": "def test_function():\n    pass",
            "file_path": "test.py",
            "language": "python",
            "detection_config": {
                "confidence_threshold": 0.7,
                "enable_ml_models": True,
                "enable_heuristic_detection": True
            }
        }
        
        # Mock fast pattern detection
        with patch("pattern_mining.api.routes.patterns.get_pattern_detector_manager") as mock_detector:
            mock_detector_instance = AsyncMock()
            mock_detector_instance.detect_patterns.return_value = [
                {
                    "pattern_id": "api-pattern",
                    "pattern_name": "API Test Pattern",
                    "pattern_type": "DESIGN_PATTERN",
                    "severity": "LOW",
                    "confidence": 0.8,
                    "location": {"file": "test.py", "line": 1, "column": 1},
                    "description": "API test pattern",
                    "detection_method": "ML_INFERENCE",
                    "context": {},
                    "metadata": {"model_version": "1.0.0"}
                }
            ]
            mock_detector_instance.get_model_versions.return_value = {"ml_inference": "1.0.0"}
            mock_detector.return_value = mock_detector_instance
            
            # Measure API response time
            with latency_measurer.measure("api_response"):
                response = await async_test_client.post("/api/v1/patterns/detect", json=request_data)
            
            # Verify response
            assert response.status_code == 200
            data = response.json()
            assert "patterns" in data
            assert "processing_time_ms" in data
            
            # Verify API response time
            latency_analysis = latency_measurer.get_latency_analysis()
            assert latency_analysis["avg_latency_ms"] < 100
            assert latency_analysis["p95_latency_ms"] < 150
            assert latency_analysis["meets_target_percentage"] == 100
    
    @pytest.mark.asyncio
    async def test_api_response_time_health_endpoints(
        self,
        async_test_client: AsyncClient,
        latency_measurer: LatencyMeasurer
    ):
        """Test health endpoints response time."""
        health_endpoints = [
            "/health",
            "/health/ready",
            "/health/live",
            "/health/metrics"
        ]
        
        for endpoint in health_endpoints:
            with latency_measurer.measure(f"health_{endpoint.replace('/', '_')}"):
                response = await async_test_client.get(endpoint)
            
            # Verify response
            assert response.status_code in [200, 503]  # 503 is acceptable for readiness
            
            # Health endpoints should be very fast
            measurements = [m for m in latency_measurer.measurements if endpoint.replace('/', '_') in m["operation"]]
            if measurements:
                latest_measurement = measurements[-1]
                assert latest_measurement["latency_ms"] < 50  # Health endpoints should be under 50ms
    
    @pytest.mark.asyncio
    async def test_api_response_time_model_endpoints(
        self,
        async_test_client: AsyncClient,
        latency_measurer: LatencyMeasurer
    ):
        """Test ML model endpoints response time."""
        # Mock ML manager
        with patch("pattern_mining.api.routes.ml.get_ml_manager") as mock_manager:
            mock_ml_manager = AsyncMock()
            mock_ml_manager.list_models.return_value = [
                {
                    "id": "model-1",
                    "name": "Test Model",
                    "version": "1.0.0",
                    "status": "active",
                    "accuracy": 0.95
                }
            ]
            mock_ml_manager.get_model_info.return_value = {
                "id": "model-1",
                "name": "Test Model",
                "version": "1.0.0",
                "status": "active",
                "accuracy": 0.95
            }
            mock_ml_manager.predict.return_value = {
                "prediction": "DESIGN_PATTERN",
                "confidence": 0.8,
                "processing_time_ms": 25
            }
            mock_manager.return_value = mock_ml_manager
            
            # Test model listing
            with latency_measurer.measure("list_models"):
                response = await async_test_client.get("/api/v1/ml/models")
            assert response.status_code == 200
            
            # Test model info
            with latency_measurer.measure("get_model_info"):
                response = await async_test_client.get("/api/v1/ml/models/model-1")
            assert response.status_code == 200
            
            # Test model prediction
            prediction_data = {
                "code": "def test():\n    pass",
                "language": "python"
            }
            with latency_measurer.measure("model_prediction"):
                response = await async_test_client.post("/api/v1/ml/models/model-1/predict", json=prediction_data)
            assert response.status_code == 200
            
            # Verify all endpoints are under 100ms
            latency_analysis = latency_measurer.get_latency_analysis()
            assert latency_analysis["avg_latency_ms"] < 100
            assert latency_analysis["max_latency_ms"] < 150
    
    @pytest.mark.asyncio
    async def test_api_response_time_concurrent_requests(
        self,
        async_test_client: AsyncClient,
        latency_measurer: LatencyMeasurer
    ):
        """Test API response time under concurrent load."""
        # Prepare concurrent requests
        request_data = {
            "repository_id": "concurrent-test-repo",
            "ast_data": {"type": "Module", "children": []},
            "code_content": "def concurrent_test():\n    pass",
            "file_path": "test.py",
            "language": "python",
            "detection_config": {
                "confidence_threshold": 0.7,
                "enable_ml_models": True
            }
        }
        
        # Mock pattern detection
        with patch("pattern_mining.api.routes.patterns.get_pattern_detector_manager") as mock_detector:
            mock_detector_instance = AsyncMock()
            mock_detector_instance.detect_patterns.return_value = [
                {
                    "pattern_id": "concurrent-pattern",
                    "pattern_name": "Concurrent Test Pattern",
                    "pattern_type": "DESIGN_PATTERN",
                    "severity": "LOW",
                    "confidence": 0.8,
                    "location": {"file": "test.py", "line": 1, "column": 1},
                    "description": "Concurrent test pattern",
                    "detection_method": "ML_INFERENCE",
                    "context": {},
                    "metadata": {"model_version": "1.0.0"}
                }
            ]
            mock_detector_instance.get_model_versions.return_value = {"ml_inference": "1.0.0"}
            mock_detector.return_value = mock_detector_instance
            
            # Send concurrent requests
            tasks = []
            for i in range(10):
                with latency_measurer.measure(f"concurrent_request_{i}"):
                    task = async_test_client.post("/api/v1/patterns/detect", json=request_data)
                    tasks.append(task)
            
            # Wait for all requests
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Verify responses
            success_count = 0
            for response in responses:
                if not isinstance(response, Exception) and response.status_code == 200:
                    success_count += 1
            
            assert success_count >= 9  # At least 90% success rate
            
            # Verify response times are still acceptable under load
            latency_analysis = latency_measurer.get_latency_analysis()
            assert latency_analysis["avg_latency_ms"] < 200  # Slightly higher under load
            assert latency_analysis["p95_latency_ms"] < 300  # P95 should still be reasonable


@pytest.mark.performance
class TestBatchProcessingLatency:
    """Test batch processing latency requirements."""
    
    @pytest.mark.asyncio
    async def test_batch_processing_per_item_latency(
        self,
        async_test_client: AsyncClient,
        latency_measurer: LatencyMeasurer
    ):
        """Test batch processing latency per item is under 200ms."""
        # Mock batch processing
        with patch("pattern_mining.api.routes.patterns.start_batch_detection") as mock_batch:
            mock_batch.return_value = {
                "job_id": "latency-batch-job",
                "status": "queued",
                "estimated_duration": 120  # 2 minutes for 100 files = 1.2s per file
            }
            
            # Start batch job
            request_data = {
                "repository_id": "batch-latency-test",
                "repository_url": "https://github.com/test/latency-repo",
                "file_patterns": ["*.py"],
                "detection_config": {
                    "confidence_threshold": 0.7,
                    "enable_ml_models": True
                },
                "parallel_jobs": 10,
                "chunk_size": 5
            }
            
            with latency_measurer.measure("batch_start"):
                response = await async_test_client.post("/api/v1/patterns/batch", json=request_data)
            
            assert response.status_code == 202
            data = response.json()
            job_id = data["job_id"]
            
            # Mock batch progress with per-item timing
            with patch("pattern_mining.api.routes.patterns.get_batch_job_status") as mock_status:
                # Simulate processing 100 files in 2 minutes = 1.2s per file (well under 200ms target)
                mock_status.return_value = {
                    "job_id": job_id,
                    "status": "processing",
                    "progress": 50.0,
                    "files_processed": 50,
                    "files_total": 100,
                    "patterns_detected": 234,
                    "average_time_per_file_ms": 120,  # 120ms per file - within target
                    "estimated_completion": (datetime.now() + timedelta(minutes=1)).isoformat()
                }
                
                with latency_measurer.measure("batch_status"):
                    response = await async_test_client.get(f"/api/v1/patterns/batch/{job_id}")
                
                assert response.status_code == 200
                data = response.json()
                assert data["average_time_per_file_ms"] < 200  # Within latency target
                
                # Verify batch status response time
                latency_analysis = latency_measurer.get_latency_analysis()
                status_measurements = [m for m in latency_measurer.measurements if m["operation"] == "batch_status"]
                if status_measurements:
                    assert status_measurements[-1]["latency_ms"] < 50  # Status endpoint should be fast
    
    @pytest.mark.asyncio
    async def test_batch_processing_throughput(
        self,
        async_test_client: AsyncClient,
        latency_measurer: LatencyMeasurer
    ):
        """Test batch processing throughput meets requirements."""
        # Mock batch results with throughput metrics
        with patch("pattern_mining.api.routes.patterns.get_batch_results") as mock_results:
            mock_results.return_value = {
                "job_id": "throughput-batch-job",
                "status": "completed",
                "summary": {
                    "files_processed": 500,
                    "patterns_detected": 1234,
                    "processing_time_ms": 60000,  # 1 minute total
                    "average_time_per_file_ms": 120,  # 120ms per file
                    "throughput_files_per_second": 8.33,  # 500 files / 60 seconds
                    "throughput_patterns_per_second": 20.57  # 1234 patterns / 60 seconds
                }
            }
            
            with latency_measurer.measure("batch_results"):
                response = await async_test_client.get("/api/v1/patterns/batch/throughput-batch-job/results")
            
            assert response.status_code == 200
            data = response.json()
            summary = data["summary"]
            
            # Verify throughput requirements
            assert summary["average_time_per_file_ms"] < 200  # Per-file latency target
            assert summary["throughput_files_per_second"] > 5  # Minimum throughput
            assert summary["throughput_patterns_per_second"] > 10  # Pattern detection throughput
            
            # Verify results retrieval is fast
            latency_analysis = latency_measurer.get_latency_analysis()
            results_measurements = [m for m in latency_measurer.measurements if m["operation"] == "batch_results"]
            if results_measurements:
                assert results_measurements[-1]["latency_ms"] < 100  # Results endpoint should be fast
    
    @pytest.mark.asyncio
    async def test_batch_processing_scalability(
        self,
        async_test_client: AsyncClient,
        latency_measurer: LatencyMeasurer
    ):
        """Test batch processing scalability with different job sizes."""
        job_sizes = [
            {"files": 10, "expected_time_per_file": 100},    # Small job
            {"files": 100, "expected_time_per_file": 120},   # Medium job
            {"files": 1000, "expected_time_per_file": 150}   # Large job
        ]
        
        for job_config in job_sizes:
            # Mock batch processing for different sizes
            with patch("pattern_mining.api.routes.patterns.start_batch_detection") as mock_batch:
                mock_batch.return_value = {
                    "job_id": f"scale-job-{job_config['files']}",
                    "status": "queued",
                    "estimated_duration": job_config["files"] * job_config["expected_time_per_file"] / 1000
                }
                
                request_data = {
                    "repository_id": f"scale-test-{job_config['files']}",
                    "repository_url": "https://github.com/test/scale-repo",
                    "file_patterns": ["*.py"],
                    "detection_config": {
                        "confidence_threshold": 0.7,
                        "enable_ml_models": True
                    },
                    "parallel_jobs": min(job_config["files"] // 10, 20),  # Scale parallel jobs
                    "chunk_size": min(job_config["files"] // 20, 50)      # Scale chunk size
                }
                
                with latency_measurer.measure(f"batch_scale_{job_config['files']}"):
                    response = await async_test_client.post("/api/v1/patterns/batch", json=request_data)
                
                assert response.status_code == 202
                data = response.json()
                
                # Verify estimated duration is reasonable
                expected_duration = job_config["files"] * job_config["expected_time_per_file"] / 1000
                assert data["estimated_duration"] <= expected_duration * 1.2  # 20% tolerance
                
                # Mock completion status
                with patch("pattern_mining.api.routes.patterns.get_batch_job_status") as mock_status:
                    mock_status.return_value = {
                        "job_id": data["job_id"],
                        "status": "completed",
                        "progress": 100.0,
                        "files_processed": job_config["files"],
                        "files_total": job_config["files"],
                        "patterns_detected": job_config["files"] * 5,  # Average 5 patterns per file
                        "average_time_per_file_ms": job_config["expected_time_per_file"],
                        "processing_time_ms": job_config["files"] * job_config["expected_time_per_file"]
                    }
                    
                    response = await async_test_client.get(f"/api/v1/patterns/batch/{data['job_id']}")
                    assert response.status_code == 200
                    
                    status_data = response.json()
                    assert status_data["average_time_per_file_ms"] < 200  # Within latency target
                    
                    # Verify scalability: larger jobs shouldn't have proportionally worse per-file performance
                    if job_config["files"] > 100:
                        assert status_data["average_time_per_file_ms"] < 180  # Large jobs should still be efficient
        
        # Verify all batch operations are within latency targets
        latency_analysis = latency_measurer.get_latency_analysis()
        assert latency_analysis["avg_latency_ms"] < 1000  # Batch operations should be under 1 second
        assert latency_analysis["max_latency_ms"] < 2000   # Max should be under 2 seconds


@pytest.mark.performance
class TestMemoryAndCPULatency:
    """Test memory usage and CPU performance impact on latency."""
    
    @pytest.mark.asyncio
    async def test_memory_usage_impact_on_latency(
        self,
        async_test_client: AsyncClient,
        latency_measurer: LatencyMeasurer,
        memory_profiler
    ):
        """Test that memory usage doesn't significantly impact latency."""
        # Start memory profiling
        memory_profiler.start_profiling()
        memory_profiler.take_snapshot("test_start")
        
        # Generate large code samples to test memory usage
        code_generator = CodeGenerator()
        large_code_samples = []
        for i in range(5):
            large_code = code_generator.generate_file_content(file_type="module", pattern_count=20)
            large_code_samples.append(large_code)
        
        # Mock pattern detection
        with patch("pattern_mining.api.routes.patterns.get_pattern_detector_manager") as mock_detector:
            mock_detector_instance = AsyncMock()
            mock_detector_instance.detect_patterns.return_value = [
                {
                    "pattern_id": "memory-pattern",
                    "pattern_name": "Memory Test Pattern",
                    "pattern_type": "DESIGN_PATTERN",
                    "severity": "LOW",
                    "confidence": 0.8,
                    "location": {"file": "test.py", "line": 1, "column": 1},
                    "description": "Memory test pattern",
                    "detection_method": "ML_INFERENCE",
                    "context": {},
                    "metadata": {"model_version": "1.0.0"}
                }
            ]
            mock_detector_instance.get_model_versions.return_value = {"ml_inference": "1.0.0"}
            mock_detector.return_value = mock_detector_instance
            
            # Test with increasing memory usage
            for i, code_sample in enumerate(large_code_samples):
                memory_profiler.take_snapshot(f"before_request_{i}")
                
                request_data = {
                    "repository_id": f"memory-test-repo-{i}",
                    "ast_data": {"type": "Module", "children": []},
                    "code_content": code_sample,
                    "file_path": f"test_{i}.py",
                    "language": "python",
                    "detection_config": {
                        "confidence_threshold": 0.7,
                        "enable_ml_models": True
                    }
                }
                
                with latency_measurer.measure(f"memory_test_{i}"):
                    response = await async_test_client.post("/api/v1/patterns/detect", json=request_data)
                
                assert response.status_code == 200
                memory_profiler.take_snapshot(f"after_request_{i}")
        
        # Analyze memory usage impact
        memory_analysis = memory_profiler.get_memory_analysis()
        latency_analysis = latency_measurer.get_latency_analysis()
        
        # Verify memory usage doesn't cause latency degradation
        assert memory_analysis["peak_memory_mb"] < 500  # Memory usage should be reasonable
        assert not memory_analysis["memory_leak_detected"]  # No memory leaks
        assert latency_analysis["avg_latency_ms"] < 100  # Latency should remain good
        
        # Check that latency doesn't increase significantly with memory usage
        measurements = latency_measurer.measurements
        first_request_latency = next(m["latency_ms"] for m in measurements if m["operation"] == "memory_test_0")
        last_request_latency = next(m["latency_ms"] for m in measurements if m["operation"] == "memory_test_4")
        
        # Latency shouldn't increase by more than 50% due to memory usage
        assert last_request_latency < first_request_latency * 1.5
    
    @pytest.mark.asyncio
    async def test_cpu_usage_impact_on_latency(
        self,
        async_test_client: AsyncClient,
        latency_measurer: LatencyMeasurer
    ):
        """Test that CPU usage doesn't significantly impact latency."""
        # Mock CPU-intensive pattern detection
        with patch("pattern_mining.api.routes.patterns.get_pattern_detector_manager") as mock_detector:
            mock_detector_instance = AsyncMock()
            
            async def simulate_cpu_intensive_inference():
                # Simulate CPU-intensive work
                await asyncio.sleep(0.02)  # 20ms base time
                
                # Simulate additional CPU work that might increase with load
                # In real scenario, this would be actual ML inference
                for _ in range(1000):
                    _ = sum(range(100))  # Simple CPU work
                
                return [
                    {
                        "pattern_id": "cpu-pattern",
                        "pattern_name": "CPU Test Pattern",
                        "pattern_type": "DESIGN_PATTERN",
                        "severity": "LOW",
                        "confidence": 0.8,
                        "location": {"file": "test.py", "line": 1, "column": 1},
                        "description": "CPU test pattern",
                        "detection_method": "ML_INFERENCE",
                        "context": {},
                        "metadata": {"model_version": "1.0.0"}
                    }
                ]
            
            mock_detector_instance.detect_patterns.side_effect = simulate_cpu_intensive_inference
            mock_detector_instance.get_model_versions.return_value = {"ml_inference": "1.0.0"}
            mock_detector.return_value = mock_detector_instance
            
            # Test with sequential requests to measure CPU impact
            request_data = {
                "repository_id": "cpu-test-repo",
                "ast_data": {"type": "Module", "children": []},
                "code_content": "def cpu_test():\n    pass",
                "file_path": "test.py",
                "language": "python",
                "detection_config": {
                    "confidence_threshold": 0.7,
                    "enable_ml_models": True
                }
            }
            
            # Send multiple sequential requests
            for i in range(10):
                with latency_measurer.measure(f"cpu_test_{i}"):
                    response = await async_test_client.post("/api/v1/patterns/detect", json=request_data)
                
                assert response.status_code == 200
        
        # Analyze CPU impact on latency
        latency_analysis = latency_measurer.get_latency_analysis()
        
        # Verify CPU usage doesn't cause significant latency issues
        assert latency_analysis["avg_latency_ms"] < 100  # Average should still be good
        assert latency_analysis["max_latency_ms"] < 200  # Max should be reasonable
        
        # Check latency consistency across requests
        measurements = latency_measurer.measurements
        cpu_measurements = [m["latency_ms"] for m in measurements if m["operation"].startswith("cpu_test_")]
        
        if len(cpu_measurements) > 1:
            latency_variance = statistics.variance(cpu_measurements)
            assert latency_variance < 1000  # Latency should be consistent (variance < 1000ms²)
            
            # First and last requests shouldn't differ by more than 100%
            assert cpu_measurements[-1] < cpu_measurements[0] * 2