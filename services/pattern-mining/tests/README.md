# Pattern Mining Test Suite

Comprehensive test suite for the Pattern Mining service with 95% coverage on security components.

## Test Organization

```
tests/
├── conftest.py          # Shared fixtures and test configuration
├── unit/                # Unit tests for individual components
├── integration/         # Integration tests with external services
├── e2e/                 # End-to-end tests
├── distributed/         # Tests for Ray cluster and distributed processing
├── performance/         # Performance and load tests
├── security/            # Security-specific tests
└── utils/               # Test utilities and helpers
```

## Running Tests

### All Tests
```bash
pytest -v
```

### Unit Tests Only
```bash
pytest tests/unit/ -v
```

### Integration Tests
```bash
pytest tests/integration/ -v --integration
```

### Security Tests
```bash
pytest tests/security/ -v
```

### Performance Tests
```bash
pytest tests/performance/ -v --benchmark-only
```

### With Coverage
```bash
pytest --cov=pattern_mining --cov-report=html --cov-report=term
```

## Test Categories

### Unit Tests (`tests/unit/`)
- Fast, isolated tests
- Mock external dependencies
- Test individual functions and classes
- Run on every commit

### Integration Tests (`tests/integration/`)
- Test interactions with external services
- Require test database and Redis
- Test API endpoints with real dependencies
- Run in CI/CD pipeline

### End-to-End Tests (`tests/e2e/`)
- Full workflow tests
- Test complete user scenarios
- Require all services running
- Run before releases

### Distributed Tests (`tests/distributed/`)
- Test Ray cluster functionality
- Test distributed processing
- Test scaling behaviors
- Require Ray cluster

### Performance Tests (`tests/performance/`)
- Benchmark critical paths
- Test response times
- Load testing
- Memory profiling

### Security Tests (`tests/security/`)
- Test authentication and authorization
- Test configuration access control
- Test prompt injection protection
- Validate security policies

## Test Utilities (`tests/utils/`)

### Factories
```python
from tests.utils.factories import PatternFactory, AnalysisJobFactory

# Create test data
pattern = PatternFactory.create(confidence=0.95)
job = AnalysisJobFactory.create(status="completed")
```

### Generators
```python
from tests.utils.generators import generate_code_sample, generate_repository

# Generate test data
code = generate_code_sample("python", "singleton")
repo = generate_repository(files=100)
```

### Assertions
```python
from tests.utils.assertions import assert_pattern_valid, assert_api_response

# Custom assertions
assert_pattern_valid(pattern)
assert_api_response(response, status=200)
```

## Environment Setup

### Test Environment Variables
```bash
# Copy test environment
cp .env.test.example .env.test

# Required for integration tests
TEST_DATABASE_URL=postgresql://test:test@localhost:5432/test
TEST_REDIS_URL=redis://localhost:6379/1
TEST_GEMINI_API_KEY=test-key
```

### Docker Compose for Tests
```bash
# Start test dependencies
docker-compose -f docker-compose.test.yml up -d

# Run tests
pytest

# Cleanup
docker-compose -f docker-compose.test.yml down
```

## Writing Tests

### Unit Test Example
```python
# tests/unit/test_pattern_detector.py
import pytest
from unittest.mock import Mock, patch
from pattern_mining.detectors import PatternDetector

class TestPatternDetector:
    @pytest.fixture
    def detector(self):
        return PatternDetector()
    
    def test_detect_singleton_pattern(self, detector):
        code = "class Singleton: _instance = None"
        patterns = detector.detect(code, language="python")
        
        assert len(patterns) == 1
        assert patterns[0].type == "singleton"
        assert patterns[0].confidence > 0.8
```

### Integration Test Example
```python
# tests/integration/test_pattern_api.py
import pytest
from httpx import AsyncClient
from pattern_mining.main import app

@pytest.mark.integration
class TestPatternAPI:
    @pytest.fixture
    async def client(self):
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client
    
    async def test_analyze_repository(self, client, test_auth_token):
        response = await client.post(
            "/api/v1/patterns/analyze",
            headers={"Authorization": f"Bearer {test_auth_token}"},
            json={
                "repository_url": "https://github.com/test/repo",
                "languages": ["python"]
            }
        )
        
        assert response.status_code == 200
        assert "analysis_id" in response.json()
```

## CI/CD Integration

### GitHub Actions
```yaml
- name: Run Tests
  run: |
    pytest tests/unit/ -v
    pytest tests/integration/ -v --integration
    pytest tests/security/ -v
```

### Coverage Requirements
- Overall: 80% minimum
- Security components: 95% minimum
- New code: 90% minimum

## Debugging Tests

### Run specific test
```bash
pytest tests/unit/test_api.py::TestAPI::test_health_check -v
```

### Debug with pdb
```bash
pytest tests/unit/test_cache.py -v --pdb
```

### Show print statements
```bash
pytest tests/unit/test_models.py -v -s
```

### Parallel execution
```bash
pytest -n auto
```

## Test Data

Test fixtures and data are managed in `conftest.py`:
- Database fixtures with automatic cleanup
- Redis fixtures with namespace isolation
- Mock Gemini responses
- Authentication tokens
- Sample code snippets

## Maintenance

- Update tests when adding new features
- Maintain test coverage above thresholds
- Review and update test data regularly
- Remove obsolete tests
- Keep tests fast and reliable