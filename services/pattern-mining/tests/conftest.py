"""
Test Configuration and Fixtures

Global pytest configuration and fixtures for the pattern-mining service test suite.
Provides database setup, ML model mocking, API client fixtures, and performance utilities.
"""

import asyncio
import os
import pytest
import pytest_asyncio
from unittest.mock import Mock, AsyncMock, MagicMock
from typing import Dict, Any, List, AsyncGenerator, Generator
from datetime import datetime, timedelta
import json
import tempfile
import logging
from pathlib import Path

# FastAPI and HTTP testing
from fastapi.testclient import TestClient
from httpx import AsyncClient
import redis.asyncio as redis

# Database and testing utilities
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from google.cloud import bigquery
from google.cloud.bigquery import Client as BigQueryClient
import pandas as pd

# Pattern mining imports
from pattern_mining.api.main import create_app
from pattern_mining.config.settings import Settings, get_settings
from pattern_mining.database.connection import get_database_session
from pattern_mining.database.models import Base
from pattern_mining.ml.manager import MLManager
from pattern_mining.ml.gemini_client import Gemini<PERSON><PERSON>
from pattern_mining.detectors.manager import PatternDetectorManager
from pattern_mining.features.extractor import FeatureExtractor
from pattern_mining.models.patterns import DetectedPattern, PatternType, SeverityLevel
from pattern_mining.models.database import PatternRecord, AnalysisRecord
from pattern_mining.utils.validation import DataValidator

# Performance testing
import time
import psutil
import tracemalloc
from functools import wraps

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Test configuration
@pytest.fixture(scope="session")
def test_settings() -> Settings:
    """Create test settings configuration."""
    return Settings(
        # Database settings
        database_url="sqlite+aiosqlite:///:memory:",
        bigquery_project_id="test-project",
        bigquery_dataset_id="test_dataset",
        
        # Redis settings
        redis_url="redis://localhost:6379/15",  # Use test database
        
        # ML settings
        gemini_api_key="test-key",
        gemini_model_name="gemini-pro",
        
        # API settings
        environment="test",
        secret_key="test-secret-key",
        cors_origins=["http://localhost:3000"],
        
        # Performance settings
        max_concurrent_requests=10,
        request_timeout=30,
        
        # Feature flags
        enable_ml_models=True,
        enable_bigquery_ml=False,
        enable_gpu_acceleration=False,
        enable_clustering=True,
        enable_metrics=False,
        enable_tracing=False,
        
        # Test-specific settings
        test_mode=True,
        debug=True,
    )


@pytest.fixture(scope="session")
def event_loop():
    """Create event loop for async tests."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


# Database fixtures
@pytest_asyncio.fixture(scope="function")
async def async_db_engine(test_settings):
    """Create async database engine for testing."""
    engine = create_async_engine(
        test_settings.database_url,
        echo=False,
        pool_pre_ping=True
    )
    
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    # Cleanup
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    
    await engine.dispose()


@pytest_asyncio.fixture(scope="function")
async def async_db_session(async_db_engine):
    """Create async database session for testing."""
    async_session = sessionmaker(
        async_db_engine,
        class_=AsyncSession,
        expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session


@pytest.fixture(scope="function")
def mock_bigquery_client():
    """Create mock BigQuery client."""
    mock_client = Mock(spec=BigQueryClient)
    mock_client.project = "test-project"
    
    # Mock dataset
    mock_dataset = Mock()
    mock_dataset.dataset_id = "test_dataset"
    mock_client.dataset.return_value = mock_dataset
    
    # Mock table
    mock_table = Mock()
    mock_table.table_id = "test_table"
    mock_client.get_table.return_value = mock_table
    
    # Mock query results
    mock_job = Mock()
    mock_job.result.return_value = []
    mock_client.query.return_value = mock_job
    
    return mock_client


@pytest_asyncio.fixture(scope="function")
async def mock_redis_client():
    """Create mock Redis client."""
    mock_redis = AsyncMock()
    mock_redis.ping.return_value = True
    mock_redis.get.return_value = None
    mock_redis.set.return_value = True
    mock_redis.delete.return_value = 1
    mock_redis.expire.return_value = True
    mock_redis.keys.return_value = []
    
    return mock_redis


# ML and AI fixtures
@pytest.fixture(scope="function")
def mock_gemini_client():
    """Create mock Gemini client."""
    mock_client = AsyncMock(spec=GeminiClient)
    
    # Mock analyze_code method
    mock_client.analyze_code.return_value = {
        "patterns": [],
        "analysis": {
            "quality_score": 85.0,
            "complexity_score": 3.2,
            "maintainability_score": 78.5
        },
        "recommendations": []
    }
    
    # Mock generate_embeddings method
    mock_client.generate_embeddings.return_value = [0.1] * 768  # 768-dimensional vector
    
    # Mock batch_analyze method
    mock_client.batch_analyze.return_value = {
        "results": [],
        "summary": {
            "total_files": 0,
            "patterns_detected": 0,
            "average_quality": 85.0
        }
    }
    
    return mock_client


@pytest.fixture(scope="function")
def mock_ml_manager(mock_gemini_client):
    """Create mock ML manager."""
    mock_manager = AsyncMock(spec=MLManager)
    mock_manager.gemini_client = mock_gemini_client
    
    # Mock model management methods
    mock_manager.list_models.return_value = [
        {
            "id": "pattern-detector-v1",
            "name": "Pattern Detector",
            "version": "1.0.0",
            "status": "active"
        }
    ]
    
    mock_manager.get_model_info.return_value = {
        "id": "pattern-detector-v1",
        "name": "Pattern Detector",
        "version": "1.0.0",
        "status": "active",
        "accuracy": 0.95,
        "precision": 0.93,
        "recall": 0.97
    }
    
    # Mock prediction methods
    mock_manager.predict.return_value = {
        "prediction": "positive",
        "confidence": 0.92,
        "probabilities": {"positive": 0.92, "negative": 0.08}
    }
    
    # Mock training methods
    mock_manager.train_model.return_value = {
        "job_id": "train-job-123",
        "status": "queued",
        "estimated_time": 3600
    }
    
    return mock_manager


@pytest.fixture(scope="function")
def mock_pattern_detector_manager():
    """Create mock pattern detector manager."""
    mock_manager = AsyncMock(spec=PatternDetectorManager)
    
    # Mock detection methods
    mock_manager.detect_patterns.return_value = [
        DetectedPattern(
            pattern_id="test-pattern-1",
            pattern_name="Test Pattern",
            pattern_type=PatternType.DESIGN_PATTERN,
            severity=SeverityLevel.MEDIUM,
            confidence=0.85,
            location={
                "file": "test.py",
                "line": 10,
                "column": 5
            },
            description="Test pattern description",
            detection_method="ml_inference",
            context={},
            metadata={}
        )
    ]
    
    mock_manager.get_model_versions.return_value = {
        "ml_inference": "1.0.0",
        "heuristic": "1.0.0"
    }
    
    return mock_manager


@pytest.fixture(scope="function")
def mock_feature_extractor():
    """Create mock feature extractor."""
    mock_extractor = AsyncMock(spec=FeatureExtractor)
    
    # Mock feature extraction
    mock_extractor.extract_features.return_value = {
        "ast": {
            "node_count": 50,
            "depth": 5,
            "complexity": 3.2
        },
        "semantic": {
            "imports": ["os", "sys"],
            "functions": ["main", "helper"],
            "classes": ["TestClass"]
        },
        "text": {
            "lines": 100,
            "comments": 20,
            "docstrings": 5
        }
    }
    
    return mock_extractor


# API fixtures
@pytest.fixture(scope="function")
def test_app(test_settings, mock_ml_manager, mock_pattern_detector_manager, mock_feature_extractor):
    """Create test FastAPI application."""
    app = create_app()
    
    # Override dependencies
    app.dependency_overrides[get_settings] = lambda: test_settings
    
    return app


@pytest.fixture(scope="function")
def test_client(test_app):
    """Create test client."""
    return TestClient(test_app)


@pytest_asyncio.fixture(scope="function")
async def async_test_client(test_app):
    """Create async test client."""
    async with AsyncClient(app=test_app, base_url="http://test") as client:
        yield client


# Test data fixtures
@pytest.fixture(scope="function")
def sample_code_data():
    """Sample code data for testing."""
    return {
        "python": {
            "code": """
def calculate_total(items):
    total = 0
    for item in items:
        total += item.price
    return total

class ShoppingCart:
    def __init__(self):
        self.items = []
    
    def add_item(self, item):
        self.items.append(item)
    
    def get_total(self):
        return calculate_total(self.items)
""",
            "ast": {
                "type": "Module",
                "children": [
                    {
                        "type": "FunctionDef",
                        "name": "calculate_total",
                        "lineno": 1
                    },
                    {
                        "type": "ClassDef",
                        "name": "ShoppingCart",
                        "lineno": 7
                    }
                ]
            }
        },
        "javascript": {
            "code": """
function calculateTotal(items) {
    let total = 0;
    for (const item of items) {
        total += item.price;
    }
    return total;
}

class ShoppingCart {
    constructor() {
        this.items = [];
    }
    
    addItem(item) {
        this.items.push(item);
    }
    
    getTotal() {
        return calculateTotal(this.items);
    }
}
""",
            "ast": {
                "type": "Program",
                "children": [
                    {
                        "type": "FunctionDeclaration",
                        "name": "calculateTotal",
                        "loc": {"start": {"line": 1}}
                    },
                    {
                        "type": "ClassDeclaration",
                        "name": "ShoppingCart",
                        "loc": {"start": {"line": 9}}
                    }
                ]
            }
        }
    }


@pytest.fixture(scope="function")
def sample_patterns():
    """Sample detected patterns for testing."""
    return [
        DetectedPattern(
            pattern_id="pattern-1",
            pattern_name="Iterator Pattern",
            pattern_type=PatternType.DESIGN_PATTERN,
            severity=SeverityLevel.LOW,
            confidence=0.92,
            location={
                "file": "shopping_cart.py",
                "line": 1,
                "column": 1,
                "end_line": 5,
                "end_column": 15
            },
            description="Iterator pattern implementation for processing items",
            detection_method="ml_inference",
            context={
                "function_name": "calculate_total",
                "loop_type": "for",
                "collection": "items"
            },
            metadata={
                "model_version": "1.0.0",
                "processing_time_ms": 15
            }
        ),
        DetectedPattern(
            pattern_id="pattern-2",
            pattern_name="Data Class Pattern",
            pattern_type=PatternType.DESIGN_PATTERN,
            severity=SeverityLevel.LOW,
            confidence=0.88,
            location={
                "file": "shopping_cart.py",
                "line": 7,
                "column": 1,
                "end_line": 20,
                "end_column": 30
            },
            description="Simple data class with basic methods",
            detection_method="heuristic",
            context={
                "class_name": "ShoppingCart",
                "method_count": 3,
                "has_init": True
            },
            metadata={
                "processing_time_ms": 8
            }
        ),
        DetectedPattern(
            pattern_id="pattern-3",
            pattern_name="SQL Injection Risk",
            pattern_type=PatternType.SECURITY_ISSUE,
            severity=SeverityLevel.HIGH,
            confidence=0.95,
            location={
                "file": "database.py",
                "line": 42,
                "column": 8,
                "end_line": 42,
                "end_column": 50
            },
            description="Potential SQL injection vulnerability in query construction",
            detection_method="ml_inference",
            context={
                "function_name": "execute_query",
                "vulnerability_type": "sql_injection",
                "risk_level": "high"
            },
            metadata={
                "model_version": "1.0.0",
                "processing_time_ms": 25
            }
        )
    ]


@pytest.fixture(scope="function")
def sample_repository_data():
    """Sample repository data for testing."""
    return {
        "repository_id": "test-repo-123",
        "repository_url": "https://github.com/test/repo",
        "branch": "main",
        "commit_sha": "abc123def456",
        "files": [
            {
                "path": "src/main.py",
                "language": "python",
                "lines": 150,
                "size": 4096
            },
            {
                "path": "src/utils.py",
                "language": "python",
                "lines": 75,
                "size": 2048
            },
            {
                "path": "tests/test_main.py",
                "language": "python",
                "lines": 200,
                "size": 5120
            }
        ]
    }


# Performance testing fixtures
@pytest.fixture(scope="function")
def performance_monitor():
    """Performance monitoring fixture."""
    class PerformanceMonitor:
        def __init__(self):
            self.start_time = None
            self.end_time = None
            self.memory_start = None
            self.memory_end = None
            self.tracemalloc_snapshot = None
        
        def start(self):
            """Start performance monitoring."""
            self.start_time = time.perf_counter()
            self.memory_start = psutil.Process().memory_info().rss
            tracemalloc.start()
            
        def stop(self):
            """Stop performance monitoring."""
            self.end_time = time.perf_counter()
            self.memory_end = psutil.Process().memory_info().rss
            self.tracemalloc_snapshot = tracemalloc.take_snapshot()
            tracemalloc.stop()
            
        @property
        def duration_ms(self) -> float:
            """Get duration in milliseconds."""
            if self.start_time is None or self.end_time is None:
                return 0.0
            return (self.end_time - self.start_time) * 1000
        
        @property
        def memory_usage_mb(self) -> float:
            """Get memory usage in MB."""
            if self.memory_start is None or self.memory_end is None:
                return 0.0
            return (self.memory_end - self.memory_start) / 1024 / 1024
        
        def get_top_memory_usage(self, limit=10):
            """Get top memory usage by line."""
            if self.tracemalloc_snapshot is None:
                return []
            
            top_stats = self.tracemalloc_snapshot.statistics('lineno')
            return [
                {
                    'filename': stat.traceback.format()[0],
                    'size_mb': stat.size / 1024 / 1024,
                    'count': stat.count
                }
                for stat in top_stats[:limit]
            ]
    
    return PerformanceMonitor()


@pytest.fixture(scope="function")
def benchmark_decorator():
    """Decorator for benchmarking test functions."""
    def benchmark(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.perf_counter()
            result = await func(*args, **kwargs)
            end_time = time.perf_counter()
            duration_ms = (end_time - start_time) * 1000
            
            # Add benchmark info to result if it's a dict
            if isinstance(result, dict):
                result['_benchmark'] = {
                    'duration_ms': duration_ms,
                    'function': func.__name__
                }
            
            return result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.perf_counter()
            result = func(*args, **kwargs)
            end_time = time.perf_counter()
            duration_ms = (end_time - start_time) * 1000
            
            # Add benchmark info to result if it's a dict
            if isinstance(result, dict):
                result['_benchmark'] = {
                    'duration_ms': duration_ms,
                    'function': func.__name__
                }
            
            return result
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return benchmark


# Test utilities
@pytest.fixture(scope="function")
def temp_directory():
    """Create temporary directory for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture(scope="function")
def mock_file_system():
    """Mock file system for testing."""
    files = {
        "src/main.py": "print('Hello, World!')",
        "src/utils.py": "def helper(): pass",
        "tests/test_main.py": "def test_main(): assert True",
        "README.md": "# Test Repository",
        "requirements.txt": "pytest>=6.0.0"
    }
    
    def read_file(path: str) -> str:
        return files.get(path, "")
    
    def list_files(pattern: str = "*") -> List[str]:
        return list(files.keys())
    
    def file_exists(path: str) -> bool:
        return path in files
    
    mock_fs = Mock()
    mock_fs.read_file = read_file
    mock_fs.list_files = list_files
    mock_fs.file_exists = file_exists
    mock_fs.files = files
    
    return mock_fs


# Database utilities
@pytest.fixture(scope="function")
def db_test_data():
    """Test data for database operations."""
    return {
        "pattern_records": [
            {
                "id": "pattern-1",
                "repository_id": "repo-123",
                "pattern_name": "Iterator Pattern",
                "pattern_type": "DESIGN_PATTERN",
                "severity": "LOW",
                "confidence": 0.92,
                "file_path": "src/main.py",
                "line_number": 10,
                "created_at": datetime.utcnow()
            },
            {
                "id": "pattern-2",
                "repository_id": "repo-123",
                "pattern_name": "SQL Injection",
                "pattern_type": "SECURITY_ISSUE",
                "severity": "HIGH",
                "confidence": 0.95,
                "file_path": "src/db.py",
                "line_number": 42,
                "created_at": datetime.utcnow()
            }
        ],
        "analysis_records": [
            {
                "id": "analysis-1",
                "repository_id": "repo-123",
                "status": "completed",
                "patterns_detected": 2,
                "quality_score": 85.5,
                "processing_time_ms": 1500,
                "created_at": datetime.utcnow()
            }
        ]
    }


# WebSocket testing fixtures
@pytest.fixture(scope="function")
def websocket_mock():
    """Mock WebSocket connection for testing."""
    class MockWebSocket:
        def __init__(self):
            self.messages = []
            self.closed = False
        
        async def send_text(self, data: str):
            self.messages.append({"type": "text", "data": data})
        
        async def send_json(self, data: dict):
            self.messages.append({"type": "json", "data": data})
        
        async def receive_text(self):
            return "test message"
        
        async def receive_json(self):
            return {"type": "test", "data": "test"}
        
        async def close(self):
            self.closed = True
    
    return MockWebSocket()


# Configuration for pytest
def pytest_configure(config):
    """Configure pytest."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )
    config.addinivalue_line(
        "markers", "gpu: marks tests that require GPU"
    )
    config.addinivalue_line(
        "markers", "performance: marks tests as performance tests"
    )


# Pytest plugins
pytest_plugins = ["pytest_asyncio"]

# Test collection settings
collect_ignore = [
    "build",
    "dist",
    ".tox",
    ".git",
    "__pycache__",
    "*.egg-info"
]