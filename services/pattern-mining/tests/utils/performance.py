"""
Performance Testing Utilities

Tools for measuring and validating performance characteristics of the pattern-mining service.
"""

import time
import asyncio
import threading
from typing import Dict, List, Any, Optional, Callable, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import psutil
import tracemalloc
import gc
import statistics
from functools import wraps
from contextlib import contextmanager

import pytest
import numpy as np
from httpx import AsyncClient
from fastapi.testclient import TestClient


@dataclass
class PerformanceMetrics:
    """Performance metrics container."""
    
    # Timing metrics
    duration_ms: float
    min_time_ms: float
    max_time_ms: float
    avg_time_ms: float
    median_time_ms: float
    p95_time_ms: float
    p99_time_ms: float
    
    # Memory metrics
    memory_usage_mb: float
    peak_memory_mb: float
    memory_leak_mb: float
    
    # CPU metrics
    cpu_usage_percent: float
    cpu_time_seconds: float
    
    # Throughput metrics
    requests_per_second: float
    total_requests: int
    successful_requests: int
    failed_requests: int
    
    # Error metrics
    error_rate_percent: float
    timeout_count: int
    
    # Custom metrics
    custom_metrics: Dict[str, Any]
    
    def __post_init__(self):
        """Calculate derived metrics."""
        if self.custom_metrics is None:
            self.custom_metrics = {}
        
        # Calculate error rate
        if self.total_requests > 0:
            self.error_rate_percent = (self.failed_requests / self.total_requests) * 100
        else:
            self.error_rate_percent = 0.0
    
    def passes_sla(self, sla_config: Dict[str, Any]) -> bool:
        """Check if metrics pass SLA requirements."""
        checks = [
            self.avg_time_ms <= sla_config.get("max_avg_response_time_ms", 100),
            self.p95_time_ms <= sla_config.get("max_p95_response_time_ms", 200),
            self.p99_time_ms <= sla_config.get("max_p99_response_time_ms", 500),
            self.memory_usage_mb <= sla_config.get("max_memory_usage_mb", 512),
            self.cpu_usage_percent <= sla_config.get("max_cpu_usage_percent", 80),
            self.requests_per_second >= sla_config.get("min_throughput_rps", 50),
            self.error_rate_percent <= sla_config.get("max_error_rate_percent", 1.0),
        ]
        
        return all(checks)


class PerformanceBenchmark:
    """Performance benchmarking utility."""
    
    def __init__(self, name: str = "benchmark"):
        self.name = name
        self.start_time = None
        self.end_time = None
        self.measurements = []
        self.memory_snapshots = []
        self.cpu_measurements = []
        self.process = psutil.Process()
        self.initial_memory = None
        self.peak_memory = 0
        
    def start(self):
        """Start benchmarking."""
        self.start_time = time.perf_counter()
        self.initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        self.peak_memory = self.initial_memory
        
        # Start memory tracking
        tracemalloc.start()
        
        # Force garbage collection
        gc.collect()
    
    def stop(self):
        """Stop benchmarking."""
        self.end_time = time.perf_counter()
        
        # Stop memory tracking
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        # Record final memory
        final_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        self.peak_memory = max(self.peak_memory, final_memory)
        
        # Calculate metrics
        duration_ms = (self.end_time - self.start_time) * 1000
        memory_usage_mb = final_memory
        memory_leak_mb = final_memory - self.initial_memory
        
        return {
            "duration_ms": duration_ms,
            "memory_usage_mb": memory_usage_mb,
            "memory_leak_mb": memory_leak_mb,
            "peak_memory_mb": self.peak_memory
        }
    
    def measure_operation(self, operation: Callable, *args, **kwargs) -> Tuple[Any, Dict[str, Any]]:
        """Measure a single operation."""
        start_time = time.perf_counter()
        start_memory = self.process.memory_info().rss / 1024 / 1024
        
        try:
            result = operation(*args, **kwargs)
            success = True
        except Exception as e:
            result = e
            success = False
        
        end_time = time.perf_counter()
        end_memory = self.process.memory_info().rss / 1024 / 1024
        
        metrics = {
            "duration_ms": (end_time - start_time) * 1000,
            "memory_delta_mb": end_memory - start_memory,
            "success": success,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        self.measurements.append(metrics)
        return result, metrics
    
    async def measure_async_operation(self, operation: Callable, *args, **kwargs) -> Tuple[Any, Dict[str, Any]]:
        """Measure an async operation."""
        start_time = time.perf_counter()
        start_memory = self.process.memory_info().rss / 1024 / 1024
        
        try:
            result = await operation(*args, **kwargs)
            success = True
        except Exception as e:
            result = e
            success = False
        
        end_time = time.perf_counter()
        end_memory = self.process.memory_info().rss / 1024 / 1024
        
        metrics = {
            "duration_ms": (end_time - start_time) * 1000,
            "memory_delta_mb": end_memory - start_memory,
            "success": success,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        self.measurements.append(metrics)
        return result, metrics
    
    def get_summary(self) -> Dict[str, Any]:
        """Get benchmark summary."""
        if not self.measurements:
            return {"error": "No measurements taken"}
        
        durations = [m["duration_ms"] for m in self.measurements if m["success"]]
        memory_deltas = [m["memory_delta_mb"] for m in self.measurements]
        
        if not durations:
            return {"error": "No successful operations"}
        
        successful_count = sum(1 for m in self.measurements if m["success"])
        failed_count = len(self.measurements) - successful_count
        
        return {
            "total_operations": len(self.measurements),
            "successful_operations": successful_count,
            "failed_operations": failed_count,
            "avg_duration_ms": statistics.mean(durations),
            "median_duration_ms": statistics.median(durations),
            "min_duration_ms": min(durations),
            "max_duration_ms": max(durations),
            "p95_duration_ms": np.percentile(durations, 95),
            "p99_duration_ms": np.percentile(durations, 99),
            "avg_memory_delta_mb": statistics.mean(memory_deltas),
            "total_memory_delta_mb": sum(memory_deltas),
            "error_rate": (failed_count / len(self.measurements)) * 100
        }


class LatencyMeasurer:
    """Measure and analyze latency patterns."""
    
    def __init__(self, target_latency_ms: float = 50.0):
        self.target_latency_ms = target_latency_ms
        self.measurements = []
    
    @contextmanager
    def measure(self, operation_name: str = "operation"):
        """Context manager for measuring latency."""
        start_time = time.perf_counter()
        
        try:
            yield
        finally:
            end_time = time.perf_counter()
            latency_ms = (end_time - start_time) * 1000
            
            self.measurements.append({
                "operation": operation_name,
                "latency_ms": latency_ms,
                "timestamp": datetime.utcnow(),
                "meets_target": latency_ms <= self.target_latency_ms
            })
    
    def get_latency_analysis(self) -> Dict[str, Any]:
        """Get detailed latency analysis."""
        if not self.measurements:
            return {"error": "No measurements taken"}
        
        latencies = [m["latency_ms"] for m in self.measurements]
        meets_target_count = sum(1 for m in self.measurements if m["meets_target"])
        
        return {
            "total_measurements": len(self.measurements),
            "target_latency_ms": self.target_latency_ms,
            "meets_target_count": meets_target_count,
            "meets_target_percentage": (meets_target_count / len(self.measurements)) * 100,
            "avg_latency_ms": statistics.mean(latencies),
            "median_latency_ms": statistics.median(latencies),
            "min_latency_ms": min(latencies),
            "max_latency_ms": max(latencies),
            "p50_latency_ms": np.percentile(latencies, 50),
            "p95_latency_ms": np.percentile(latencies, 95),
            "p99_latency_ms": np.percentile(latencies, 99),
            "p999_latency_ms": np.percentile(latencies, 99.9),
            "latency_variance": statistics.variance(latencies),
            "latency_stddev": statistics.stdev(latencies)
        }
    
    def get_latency_distribution(self, buckets: int = 10) -> Dict[str, Any]:
        """Get latency distribution histogram."""
        if not self.measurements:
            return {"error": "No measurements taken"}
        
        latencies = [m["latency_ms"] for m in self.measurements]
        hist, bin_edges = np.histogram(latencies, bins=buckets)
        
        distribution = []
        for i in range(len(hist)):
            distribution.append({
                "range": f"{bin_edges[i]:.1f}-{bin_edges[i+1]:.1f}ms",
                "count": int(hist[i]),
                "percentage": (hist[i] / len(latencies)) * 100
            })
        
        return {
            "buckets": buckets,
            "distribution": distribution,
            "total_measurements": len(latencies)
        }


class MemoryProfiler:
    """Memory usage profiler."""
    
    def __init__(self):
        self.snapshots = []
        self.baseline_memory = None
    
    def start_profiling(self):
        """Start memory profiling."""
        tracemalloc.start()
        self.baseline_memory = self._get_memory_usage()
    
    def stop_profiling(self):
        """Stop memory profiling."""
        tracemalloc.stop()
    
    def take_snapshot(self, label: str = "snapshot"):
        """Take a memory snapshot."""
        if not tracemalloc.is_tracing():
            tracemalloc.start()
        
        snapshot = tracemalloc.take_snapshot()
        memory_usage = self._get_memory_usage()
        
        self.snapshots.append({
            "label": label,
            "timestamp": datetime.utcnow(),
            "snapshot": snapshot,
            "memory_usage_mb": memory_usage,
            "memory_delta_mb": memory_usage - (self.baseline_memory or 0)
        })
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    
    def get_memory_analysis(self) -> Dict[str, Any]:
        """Get memory usage analysis."""
        if not self.snapshots:
            return {"error": "No snapshots taken"}
        
        memory_usages = [s["memory_usage_mb"] for s in self.snapshots]
        memory_deltas = [s["memory_delta_mb"] for s in self.snapshots]
        
        return {
            "total_snapshots": len(self.snapshots),
            "baseline_memory_mb": self.baseline_memory,
            "current_memory_mb": memory_usages[-1],
            "peak_memory_mb": max(memory_usages),
            "min_memory_mb": min(memory_usages),
            "avg_memory_mb": statistics.mean(memory_usages),
            "total_memory_growth_mb": memory_deltas[-1] if memory_deltas else 0,
            "max_memory_growth_mb": max(memory_deltas) if memory_deltas else 0,
            "memory_leak_detected": memory_deltas[-1] > 50 if memory_deltas else False
        }
    
    def get_top_memory_usage(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top memory usage by line."""
        if not self.snapshots:
            return []
        
        latest_snapshot = self.snapshots[-1]["snapshot"]
        top_stats = latest_snapshot.statistics('lineno')
        
        return [
            {
                "filename": stat.traceback.format()[0],
                "line_number": stat.traceback.lineno,
                "size_mb": stat.size / 1024 / 1024,
                "count": stat.count,
                "average_size_bytes": stat.size / stat.count if stat.count > 0 else 0
            }
            for stat in top_stats[:limit]
        ]


class ThroughputTester:
    """Test throughput and concurrent request handling."""
    
    def __init__(self, client: AsyncClient, target_rps: float = 100.0):
        self.client = client
        self.target_rps = target_rps
        self.results = []
    
    async def test_throughput(
        self,
        endpoint: str,
        method: str = "GET",
        payload: Optional[Dict[str, Any]] = None,
        duration_seconds: int = 10,
        concurrent_requests: int = 10
    ) -> Dict[str, Any]:
        """Test throughput over a duration."""
        start_time = time.perf_counter()
        end_time = start_time + duration_seconds
        
        results = []
        
        async def make_request():
            """Make a single request."""
            request_start = time.perf_counter()
            try:
                if method.upper() == "GET":
                    response = await self.client.get(endpoint)
                elif method.upper() == "POST":
                    response = await self.client.post(endpoint, json=payload)
                else:
                    raise ValueError(f"Unsupported method: {method}")
                
                request_end = time.perf_counter()
                
                return {
                    "success": response.status_code < 400,
                    "status_code": response.status_code,
                    "duration_ms": (request_end - request_start) * 1000,
                    "timestamp": datetime.utcnow()
                }
            except Exception as e:
                request_end = time.perf_counter()
                return {
                    "success": False,
                    "error": str(e),
                    "duration_ms": (request_end - request_start) * 1000,
                    "timestamp": datetime.utcnow()
                }
        
        # Run concurrent requests for the duration
        tasks = []
        while time.perf_counter() < end_time:
            # Create batch of concurrent requests
            batch_tasks = [make_request() for _ in range(concurrent_requests)]
            batch_results = await asyncio.gather(*batch_tasks)
            results.extend(batch_results)
            
            # Small delay to prevent overwhelming the server
            await asyncio.sleep(0.01)
        
        # Calculate metrics
        total_duration = time.perf_counter() - start_time
        successful_requests = sum(1 for r in results if r["success"])
        failed_requests = len(results) - successful_requests
        
        if results:
            durations = [r["duration_ms"] for r in results if r["success"]]
            avg_duration = statistics.mean(durations) if durations else 0
            p95_duration = np.percentile(durations, 95) if durations else 0
        else:
            avg_duration = 0
            p95_duration = 0
        
        actual_rps = len(results) / total_duration
        
        return {
            "total_requests": len(results),
            "successful_requests": successful_requests,
            "failed_requests": failed_requests,
            "duration_seconds": total_duration,
            "requests_per_second": actual_rps,
            "target_rps": self.target_rps,
            "meets_target": actual_rps >= self.target_rps,
            "avg_response_time_ms": avg_duration,
            "p95_response_time_ms": p95_duration,
            "error_rate_percent": (failed_requests / len(results)) * 100 if results else 0,
            "concurrent_requests": concurrent_requests
        }
    
    async def test_load_spike(
        self,
        endpoint: str,
        method: str = "GET",
        payload: Optional[Dict[str, Any]] = None,
        spike_requests: int = 100,
        spike_duration_seconds: int = 1
    ) -> Dict[str, Any]:
        """Test response to sudden load spike."""
        start_time = time.perf_counter()
        
        # Create all requests at once
        tasks = []
        for _ in range(spike_requests):
            if method.upper() == "GET":
                task = self.client.get(endpoint)
            elif method.upper() == "POST":
                task = self.client.post(endpoint, json=payload)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            tasks.append(task)
        
        # Execute all requests concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.perf_counter()
        total_duration = end_time - start_time
        
        # Analyze results
        successful_requests = 0
        failed_requests = 0
        response_times = []
        
        for result in results:
            if isinstance(result, Exception):
                failed_requests += 1
            else:
                if result.status_code < 400:
                    successful_requests += 1
                else:
                    failed_requests += 1
        
        return {
            "spike_requests": spike_requests,
            "successful_requests": successful_requests,
            "failed_requests": failed_requests,
            "duration_seconds": total_duration,
            "requests_per_second": spike_requests / total_duration,
            "success_rate_percent": (successful_requests / spike_requests) * 100,
            "failure_rate_percent": (failed_requests / spike_requests) * 100,
            "avg_response_time_ms": statistics.mean(response_times) if response_times else 0
        }


class PerformanceTestRunner:
    """Comprehensive performance test runner."""
    
    def __init__(self, client: AsyncClient, sla_config: Dict[str, Any] = None):
        self.client = client
        self.sla_config = sla_config or self._default_sla_config()
        self.benchmark = PerformanceBenchmark()
        self.latency_measurer = LatencyMeasurer()
        self.memory_profiler = MemoryProfiler()
        self.throughput_tester = ThroughputTester(client)
    
    def _default_sla_config(self) -> Dict[str, Any]:
        """Default SLA configuration."""
        return {
            "max_avg_response_time_ms": 50,
            "max_p95_response_time_ms": 100,
            "max_p99_response_time_ms": 200,
            "max_memory_usage_mb": 256,
            "max_cpu_usage_percent": 80,
            "min_throughput_rps": 100,
            "max_error_rate_percent": 1.0
        }
    
    async def run_performance_test(
        self,
        endpoint: str,
        method: str = "GET",
        payload: Optional[Dict[str, Any]] = None,
        test_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Run comprehensive performance test."""
        config = test_config or {}
        
        # Start profiling
        self.memory_profiler.start_profiling()
        self.benchmark.start()
        
        results = {}
        
        try:
            # Test single request latency
            with self.latency_measurer.measure("single_request"):
                if method.upper() == "GET":
                    response = await self.client.get(endpoint)
                elif method.upper() == "POST":
                    response = await self.client.post(endpoint, json=payload)
                else:
                    raise ValueError(f"Unsupported method: {method}")
            
            results["single_request"] = {
                "status_code": response.status_code,
                "success": response.status_code < 400
            }
            
            # Test throughput
            throughput_results = await self.throughput_tester.test_throughput(
                endpoint, method, payload,
                duration_seconds=config.get("throughput_duration", 10),
                concurrent_requests=config.get("concurrent_requests", 10)
            )
            results["throughput"] = throughput_results
            
            # Test load spike
            spike_results = await self.throughput_tester.test_load_spike(
                endpoint, method, payload,
                spike_requests=config.get("spike_requests", 50)
            )
            results["load_spike"] = spike_results
            
            # Take memory snapshot
            self.memory_profiler.take_snapshot("after_tests")
            
        finally:
            # Stop profiling
            benchmark_results = self.benchmark.stop()
            self.memory_profiler.stop_profiling()
        
        # Compile final results
        results.update({
            "latency_analysis": self.latency_measurer.get_latency_analysis(),
            "memory_analysis": self.memory_profiler.get_memory_analysis(),
            "benchmark_summary": self.benchmark.get_summary(),
            "sla_compliance": self._check_sla_compliance(results)
        })
        
        return results
    
    def _check_sla_compliance(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Check SLA compliance."""
        compliance = {}
        
        # Check latency SLA
        latency_analysis = results.get("latency_analysis", {})
        compliance["avg_response_time"] = {
            "value": latency_analysis.get("avg_latency_ms", 0),
            "limit": self.sla_config["max_avg_response_time_ms"],
            "compliant": latency_analysis.get("avg_latency_ms", 0) <= self.sla_config["max_avg_response_time_ms"]
        }
        
        compliance["p95_response_time"] = {
            "value": latency_analysis.get("p95_latency_ms", 0),
            "limit": self.sla_config["max_p95_response_time_ms"],
            "compliant": latency_analysis.get("p95_latency_ms", 0) <= self.sla_config["max_p95_response_time_ms"]
        }
        
        # Check memory SLA
        memory_analysis = results.get("memory_analysis", {})
        compliance["memory_usage"] = {
            "value": memory_analysis.get("peak_memory_mb", 0),
            "limit": self.sla_config["max_memory_usage_mb"],
            "compliant": memory_analysis.get("peak_memory_mb", 0) <= self.sla_config["max_memory_usage_mb"]
        }
        
        # Check throughput SLA
        throughput_results = results.get("throughput", {})
        compliance["throughput"] = {
            "value": throughput_results.get("requests_per_second", 0),
            "limit": self.sla_config["min_throughput_rps"],
            "compliant": throughput_results.get("requests_per_second", 0) >= self.sla_config["min_throughput_rps"]
        }
        
        # Check error rate SLA
        compliance["error_rate"] = {
            "value": throughput_results.get("error_rate_percent", 0),
            "limit": self.sla_config["max_error_rate_percent"],
            "compliant": throughput_results.get("error_rate_percent", 0) <= self.sla_config["max_error_rate_percent"]
        }
        
        # Overall compliance
        compliance["overall_compliant"] = all(
            check["compliant"] for check in compliance.values() if isinstance(check, dict)
        )
        
        return compliance


# Decorators for performance testing
def performance_test(sla_config: Optional[Dict[str, Any]] = None):
    """Decorator for performance testing."""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            benchmark = PerformanceBenchmark(func.__name__)
            benchmark.start()
            
            try:
                result = await func(*args, **kwargs)
                success = True
            except Exception as e:
                result = e
                success = False
            
            metrics = benchmark.stop()
            
            # Check SLA compliance if configured
            if sla_config:
                sla_passed = metrics["duration_ms"] <= sla_config.get("max_duration_ms", 1000)
                sla_passed = sla_passed and metrics["memory_usage_mb"] <= sla_config.get("max_memory_mb", 256)
                
                if not sla_passed:
                    pytest.fail(f"SLA violation in {func.__name__}: {metrics}")
            
            return result if success else pytest.fail(f"Performance test failed: {result}")
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            benchmark = PerformanceBenchmark(func.__name__)
            benchmark.start()
            
            try:
                result = func(*args, **kwargs)
                success = True
            except Exception as e:
                result = e
                success = False
            
            metrics = benchmark.stop()
            
            # Check SLA compliance if configured
            if sla_config:
                sla_passed = metrics["duration_ms"] <= sla_config.get("max_duration_ms", 1000)
                sla_passed = sla_passed and metrics["memory_usage_mb"] <= sla_config.get("max_memory_mb", 256)
                
                if not sla_passed:
                    pytest.fail(f"SLA violation in {func.__name__}: {metrics}")
            
            return result if success else pytest.fail(f"Performance test failed: {result}")
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator


def benchmark(iterations: int = 100):
    """Decorator for benchmarking functions."""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            measurements = []
            
            for i in range(iterations):
                start_time = time.perf_counter()
                try:
                    result = await func(*args, **kwargs)
                    success = True
                except Exception as e:
                    result = e
                    success = False
                
                end_time = time.perf_counter()
                duration_ms = (end_time - start_time) * 1000
                
                measurements.append({
                    "iteration": i,
                    "duration_ms": duration_ms,
                    "success": success
                })
            
            # Calculate statistics
            successful_measurements = [m for m in measurements if m["success"]]
            durations = [m["duration_ms"] for m in successful_measurements]
            
            if durations:
                benchmark_results = {
                    "function": func.__name__,
                    "iterations": iterations,
                    "successful_iterations": len(successful_measurements),
                    "failed_iterations": iterations - len(successful_measurements),
                    "avg_duration_ms": statistics.mean(durations),
                    "median_duration_ms": statistics.median(durations),
                    "min_duration_ms": min(durations),
                    "max_duration_ms": max(durations),
                    "std_dev_ms": statistics.stdev(durations) if len(durations) > 1 else 0,
                    "p95_duration_ms": np.percentile(durations, 95),
                    "p99_duration_ms": np.percentile(durations, 99)
                }
                
                print(f"Benchmark results for {func.__name__}:")
                print(f"  Average: {benchmark_results['avg_duration_ms']:.2f}ms")
                print(f"  Median: {benchmark_results['median_duration_ms']:.2f}ms")
                print(f"  P95: {benchmark_results['p95_duration_ms']:.2f}ms")
                print(f"  P99: {benchmark_results['p99_duration_ms']:.2f}ms")
            
            return result if successful_measurements else pytest.fail("All benchmark iterations failed")
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            measurements = []
            
            for i in range(iterations):
                start_time = time.perf_counter()
                try:
                    result = func(*args, **kwargs)
                    success = True
                except Exception as e:
                    result = e
                    success = False
                
                end_time = time.perf_counter()
                duration_ms = (end_time - start_time) * 1000
                
                measurements.append({
                    "iteration": i,
                    "duration_ms": duration_ms,
                    "success": success
                })
            
            # Calculate statistics
            successful_measurements = [m for m in measurements if m["success"]]
            durations = [m["duration_ms"] for m in successful_measurements]
            
            if durations:
                benchmark_results = {
                    "function": func.__name__,
                    "iterations": iterations,
                    "successful_iterations": len(successful_measurements),
                    "failed_iterations": iterations - len(successful_measurements),
                    "avg_duration_ms": statistics.mean(durations),
                    "median_duration_ms": statistics.median(durations),
                    "min_duration_ms": min(durations),
                    "max_duration_ms": max(durations),
                    "std_dev_ms": statistics.stdev(durations) if len(durations) > 1 else 0,
                    "p95_duration_ms": np.percentile(durations, 95),
                    "p99_duration_ms": np.percentile(durations, 99)
                }
                
                print(f"Benchmark results for {func.__name__}:")
                print(f"  Average: {benchmark_results['avg_duration_ms']:.2f}ms")
                print(f"  Median: {benchmark_results['median_duration_ms']:.2f}ms")
                print(f"  P95: {benchmark_results['p95_duration_ms']:.2f}ms")
                print(f"  P99: {benchmark_results['p99_duration_ms']:.2f}ms")
            
            return result if successful_measurements else pytest.fail("All benchmark iterations failed")
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator


# Pytest fixtures for performance testing
@pytest.fixture
def performance_benchmark():
    """Performance benchmark fixture."""
    return PerformanceBenchmark()


@pytest.fixture
def latency_measurer():
    """Latency measurer fixture."""
    return LatencyMeasurer()


@pytest.fixture
def memory_profiler():
    """Memory profiler fixture."""
    return MemoryProfiler()


@pytest.fixture
def throughput_tester(async_test_client):
    """Throughput tester fixture."""
    return ThroughputTester(async_test_client)


@pytest.fixture
def performance_test_runner(async_test_client):
    """Performance test runner fixture."""
    return PerformanceTestRunner(async_test_client)