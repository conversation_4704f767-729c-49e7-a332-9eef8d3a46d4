"""
Test Data Factories

Factory classes for creating test data objects with realistic values.
"""

import random
import string
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from uuid import uuid4
from faker import Faker
import factory
from factory import fuzzy

from pattern_mining.models.patterns import (
    DetectedPattern, PatternType, SeverityLevel, DetectionType
)
from pattern_mining.models.api import (
    PatternDetectionRequest, BatchDetectionRequest, DetectionConfig,
    PatternDetectionResponse, PatternSummary
)
from pattern_mining.models.database import (
    PatternRecord, AnalysisRecord, RepositoryRecord, ModelRecord
)

fake = Faker()


class PatternFactory(factory.Factory):
    """Factory for creating DetectedPattern instances."""
    
    class Meta:
        model = DetectedPattern
    
    pattern_id = factory.LazyFunction(lambda: f"pattern-{uuid4().hex[:8]}")
    pattern_name = factory.LazyFunction(lambda: fake.catch_phrase())
    pattern_type = factory.LazyFunction(lambda: random.choice(list(PatternType)))
    severity = factory.LazyFunction(lambda: random.choice(list(SeverityLevel)))
    confidence = factory.LazyFunction(lambda: round(random.uniform(0.5, 1.0), 2))
    
    location = factory.LazyFunction(lambda: {
        "file": fake.file_path(depth=2, category="text", extension="py"),
        "line": fake.random_int(min=1, max=500),
        "column": fake.random_int(min=1, max=80),
        "end_line": fake.random_int(min=1, max=500),
        "end_column": fake.random_int(min=1, max=80)
    })
    
    description = factory.LazyFunction(lambda: fake.text(max_nb_chars=200))
    detection_method = factory.LazyFunction(lambda: random.choice(list(DetectionType)))
    
    context = factory.LazyFunction(lambda: {
        "function_name": fake.pystr(min_chars=5, max_chars=20),
        "class_name": fake.pystr(min_chars=5, max_chars=20),
        "complexity": round(random.uniform(1.0, 10.0), 1),
        "lines_of_code": fake.random_int(min=5, max=100)
    })
    
    metadata = factory.LazyFunction(lambda: {
        "model_version": f"{fake.random_int(min=1, max=3)}.{fake.random_int(min=0, max=9)}.0",
        "processing_time_ms": fake.random_int(min=5, max=100),
        "timestamp": datetime.utcnow().isoformat()
    })
    
    created_at = factory.LazyFunction(lambda: datetime.utcnow())
    updated_at = factory.LazyFunction(lambda: datetime.utcnow())


class HighSeverityPatternFactory(PatternFactory):
    """Factory for high severity patterns."""
    
    severity = SeverityLevel.HIGH
    confidence = factory.LazyFunction(lambda: round(random.uniform(0.8, 1.0), 2))
    pattern_type = factory.LazyFunction(lambda: random.choice([
        PatternType.SECURITY_ISSUE,
        PatternType.PERFORMANCE_ISSUE,
        PatternType.BUG_PATTERN
    ]))


class SecurityPatternFactory(PatternFactory):
    """Factory for security-related patterns."""
    
    pattern_type = PatternType.SECURITY_ISSUE
    severity = factory.LazyFunction(lambda: random.choice([
        SeverityLevel.HIGH, SeverityLevel.CRITICAL
    ]))
    confidence = factory.LazyFunction(lambda: round(random.uniform(0.85, 1.0), 2))
    
    context = factory.LazyFunction(lambda: {
        "vulnerability_type": random.choice([
            "sql_injection", "xss", "csrf", "authentication_bypass",
            "authorization_flaw", "sensitive_data_exposure"
        ]),
        "risk_level": random.choice(["high", "critical"]),
        "cwe_id": fake.random_int(min=1, max=999),
        "owasp_category": fake.random_element(elements=[
            "A1", "A2", "A3", "A4", "A5", "A6", "A7", "A8", "A9", "A10"
        ])
    })


class PerformancePatternFactory(PatternFactory):
    """Factory for performance-related patterns."""
    
    pattern_type = PatternType.PERFORMANCE_ISSUE
    severity = factory.LazyFunction(lambda: random.choice([
        SeverityLevel.MEDIUM, SeverityLevel.HIGH
    ]))
    
    context = factory.LazyFunction(lambda: {
        "performance_impact": random.choice(["low", "medium", "high"]),
        "optimization_potential": round(random.uniform(10.0, 80.0), 1),
        "resource_usage": random.choice(["cpu", "memory", "io", "network"]),
        "complexity_class": random.choice(["O(1)", "O(log n)", "O(n)", "O(n²)", "O(n³)"])
    })


class DetectionConfigFactory(factory.Factory):
    """Factory for DetectionConfig instances."""
    
    class Meta:
        model = DetectionConfig
    
    pattern_types = factory.LazyFunction(lambda: random.sample(
        list(PatternType), random.randint(1, 3)
    ))
    exclude_patterns = factory.LazyFunction(lambda: [
        f"pattern-{uuid4().hex[:8]}" for _ in range(random.randint(0, 3))
    ])
    confidence_threshold = factory.LazyFunction(lambda: round(random.uniform(0.5, 0.9), 2))
    max_patterns_per_file = factory.LazyFunction(lambda: random.randint(10, 100))
    enable_ml_models = factory.LazyFunction(lambda: random.choice([True, False]))
    enable_heuristic_detection = factory.LazyFunction(lambda: random.choice([True, False]))
    enable_clustering = factory.LazyFunction(lambda: random.choice([True, False]))
    enable_deep_learning = factory.LazyFunction(lambda: random.choice([True, False]))
    use_bigquery_ml = factory.LazyFunction(lambda: random.choice([True, False]))
    language_hints = factory.LazyFunction(lambda: random.sample([
        "django", "flask", "fastapi", "react", "vue", "angular", "spring", "express"
    ], random.randint(0, 3)))
    enable_cross_file_analysis = factory.LazyFunction(lambda: random.choice([True, False]))
    enable_evolutionary_patterns = factory.LazyFunction(lambda: random.choice([True, False]))


class PatternDetectionRequestFactory(factory.Factory):
    """Factory for PatternDetectionRequest instances."""
    
    class Meta:
        model = PatternDetectionRequest
    
    repository_id = factory.LazyFunction(lambda: f"repo-{uuid4().hex[:8]}")
    code_content = factory.LazyFunction(lambda: fake.text(max_nb_chars=1000))
    file_path = factory.LazyFunction(lambda: fake.file_path(depth=3, extension="py"))
    language = factory.LazyFunction(lambda: random.choice([
        "python", "javascript", "typescript", "java", "go", "rust"
    ]))
    
    ast_data = factory.LazyFunction(lambda: {
        "type": "Module",
        "children": [
            {
                "type": "FunctionDef",
                "name": fake.pystr(min_chars=5, max_chars=15),
                "lineno": fake.random_int(min=1, max=50)
            }
        ]
    })
    
    detection_config = factory.SubFactory(DetectionConfigFactory)
    
    context = factory.LazyFunction(lambda: {
        "project_type": random.choice(["web", "api", "cli", "library"]),
        "framework": random.choice(["django", "flask", "fastapi", "express"]),
        "testing_framework": random.choice(["pytest", "unittest", "jest", "mocha"])
    })
    
    metadata = factory.LazyFunction(lambda: {
        "client_version": f"{fake.random_int(min=1, max=3)}.{fake.random_int(min=0, max=9)}.0",
        "user_agent": fake.user_agent(),
        "timestamp": datetime.utcnow().isoformat()
    })
    
    priority = factory.LazyFunction(lambda: random.randint(1, 10))
    timeout_seconds = factory.LazyFunction(lambda: random.randint(10, 300))


class BatchDetectionRequestFactory(factory.Factory):
    """Factory for BatchDetectionRequest instances."""
    
    class Meta:
        model = BatchDetectionRequest
    
    repository_id = factory.LazyFunction(lambda: f"repo-{uuid4().hex[:8]}")
    repository_url = factory.LazyFunction(lambda: fake.url())
    
    file_patterns = factory.LazyFunction(lambda: [
        "*.py", "*.js", "*.ts", "*.java", "*.go"
    ][:random.randint(1, 3)])
    
    exclude_patterns = factory.LazyFunction(lambda: [
        "__pycache__/*", "*.pyc", "node_modules/*", "*.min.js"
    ][:random.randint(0, 2)])
    
    detection_config = factory.SubFactory(DetectionConfigFactory)
    
    parallel_jobs = factory.LazyFunction(lambda: random.randint(1, 20))
    timeout_seconds = factory.LazyFunction(lambda: random.randint(60, 3600))
    chunk_size = factory.LazyFunction(lambda: random.randint(10, 100))
    
    callback_url = factory.LazyFunction(lambda: fake.url() if random.choice([True, False]) else None)
    callback_headers = factory.LazyFunction(lambda: {
        "Authorization": f"Bearer {fake.uuid4()}",
        "Content-Type": "application/json"
    })


class PatternSummaryFactory(factory.Factory):
    """Factory for PatternSummary instances."""
    
    class Meta:
        model = PatternSummary
    
    total_patterns = factory.LazyFunction(lambda: random.randint(0, 100))
    unique_patterns = factory.LazyFunction(lambda: random.randint(0, 50))
    
    by_type = factory.LazyFunction(lambda: {
        pattern_type: random.randint(0, 10)
        for pattern_type in random.sample(list(PatternType), random.randint(1, 5))
    })
    
    by_severity = factory.LazyFunction(lambda: {
        severity: random.randint(0, 20)
        for severity in random.sample(list(SeverityLevel), random.randint(1, 4))
    })
    
    by_detection_method = factory.LazyFunction(lambda: {
        method: random.randint(0, 30)
        for method in random.sample(list(DetectionType), random.randint(1, 3))
    })
    
    quality_score = factory.LazyFunction(lambda: round(random.uniform(60.0, 100.0), 1))
    security_score = factory.LazyFunction(lambda: round(random.uniform(70.0, 100.0), 1))
    performance_score = factory.LazyFunction(lambda: round(random.uniform(65.0, 95.0), 1))
    maintainability_score = factory.LazyFunction(lambda: round(random.uniform(60.0, 90.0), 1))
    
    top_patterns = factory.LazyFunction(lambda: [
        (fake.catch_phrase(), random.randint(1, 10))
        for _ in range(random.randint(1, 5))
    ])
    
    average_confidence = factory.LazyFunction(lambda: round(random.uniform(0.6, 0.95), 2))
    confidence_distribution = factory.LazyFunction(lambda: {
        "high (>0.8)": random.randint(0, 30),
        "medium (0.6-0.8)": random.randint(0, 40),
        "low (<0.6)": random.randint(0, 20)
    })


class PatternDetectionResponseFactory(factory.Factory):
    """Factory for PatternDetectionResponse instances."""
    
    class Meta:
        model = PatternDetectionResponse
    
    request_id = factory.LazyFunction(lambda: f"req-{uuid4().hex[:8]}")
    repository_id = factory.LazyFunction(lambda: f"repo-{uuid4().hex[:8]}")
    
    patterns = factory.LazyFunction(lambda: [
        PatternFactory() for _ in range(random.randint(0, 10))
    ])
    
    summary = factory.SubFactory(PatternSummaryFactory)
    
    processing_time_ms = factory.LazyFunction(lambda: random.randint(10, 5000))
    model_versions = factory.LazyFunction(lambda: {
        "ml_inference": f"{fake.random_int(min=1, max=3)}.{fake.random_int(min=0, max=9)}.0",
        "heuristic": f"{fake.random_int(min=1, max=3)}.{fake.random_int(min=0, max=9)}.0"
    })
    
    metadata = factory.LazyFunction(lambda: {
        "server_version": f"{fake.random_int(min=1, max=3)}.{fake.random_int(min=0, max=9)}.0",
        "processing_node": fake.hostname(),
        "timestamp": datetime.utcnow().isoformat()
    })
    
    timestamp = factory.LazyFunction(lambda: datetime.utcnow())


# Database model factories
class PatternRecordFactory(factory.Factory):
    """Factory for PatternRecord database model."""
    
    class Meta:
        model = PatternRecord
    
    id = factory.LazyFunction(lambda: f"pattern-{uuid4().hex[:8]}")
    repository_id = factory.LazyFunction(lambda: f"repo-{uuid4().hex[:8]}")
    pattern_name = factory.LazyFunction(lambda: fake.catch_phrase())
    pattern_type = factory.LazyFunction(lambda: random.choice(list(PatternType)).value)
    severity = factory.LazyFunction(lambda: random.choice(list(SeverityLevel)).value)
    confidence = factory.LazyFunction(lambda: round(random.uniform(0.5, 1.0), 2))
    file_path = factory.LazyFunction(lambda: fake.file_path(depth=2, extension="py"))
    line_number = factory.LazyFunction(lambda: fake.random_int(min=1, max=500))
    description = factory.LazyFunction(lambda: fake.text(max_nb_chars=200))
    detection_method = factory.LazyFunction(lambda: random.choice(list(DetectionType)).value)
    context = factory.LazyFunction(lambda: {
        "function_name": fake.pystr(min_chars=5, max_chars=20),
        "complexity": round(random.uniform(1.0, 10.0), 1)
    })
    metadata = factory.LazyFunction(lambda: {
        "model_version": f"{fake.random_int(min=1, max=3)}.{fake.random_int(min=0, max=9)}.0",
        "processing_time_ms": fake.random_int(min=5, max=100)
    })
    created_at = factory.LazyFunction(lambda: datetime.utcnow())
    updated_at = factory.LazyFunction(lambda: datetime.utcnow())


class AnalysisRecordFactory(factory.Factory):
    """Factory for AnalysisRecord database model."""
    
    class Meta:
        model = AnalysisRecord
    
    id = factory.LazyFunction(lambda: f"analysis-{uuid4().hex[:8]}")
    repository_id = factory.LazyFunction(lambda: f"repo-{uuid4().hex[:8]}")
    status = factory.LazyFunction(lambda: random.choice([
        "pending", "processing", "completed", "failed", "cancelled"
    ]))
    patterns_detected = factory.LazyFunction(lambda: random.randint(0, 50))
    quality_score = factory.LazyFunction(lambda: round(random.uniform(60.0, 100.0), 1))
    security_score = factory.LazyFunction(lambda: round(random.uniform(70.0, 100.0), 1))
    performance_score = factory.LazyFunction(lambda: round(random.uniform(65.0, 95.0), 1))
    processing_time_ms = factory.LazyFunction(lambda: random.randint(100, 30000))
    error_message = factory.LazyFunction(lambda: fake.text(max_nb_chars=100) if random.choice([True, False]) else None)
    metadata = factory.LazyFunction(lambda: {
        "files_processed": random.randint(1, 100),
        "lines_analyzed": random.randint(100, 10000),
        "languages": random.sample(["python", "javascript", "java", "go"], random.randint(1, 3))
    })
    created_at = factory.LazyFunction(lambda: datetime.utcnow())
    updated_at = factory.LazyFunction(lambda: datetime.utcnow())


class RepositoryRecordFactory(factory.Factory):
    """Factory for RepositoryRecord database model."""
    
    class Meta:
        model = RepositoryRecord
    
    id = factory.LazyFunction(lambda: f"repo-{uuid4().hex[:8]}")
    repository_url = factory.LazyFunction(lambda: fake.url())
    name = factory.LazyFunction(lambda: fake.slug())
    description = factory.LazyFunction(lambda: fake.text(max_nb_chars=200))
    language = factory.LazyFunction(lambda: random.choice([
        "python", "javascript", "typescript", "java", "go", "rust"
    ]))
    stars = factory.LazyFunction(lambda: random.randint(0, 10000))
    forks = factory.LazyFunction(lambda: random.randint(0, 1000))
    last_commit = factory.LazyFunction(lambda: fake.date_time_between(
        start_date="-1y", end_date="now"
    ))
    metadata = factory.LazyFunction(lambda: {
        "owner": fake.user_name(),
        "license": random.choice(["MIT", "Apache-2.0", "GPL-3.0", "BSD-3-Clause"]),
        "topics": random.sample([
            "machine-learning", "web-development", "data-science", "api", "frontend"
        ], random.randint(1, 3))
    })
    created_at = factory.LazyFunction(lambda: datetime.utcnow())
    updated_at = factory.LazyFunction(lambda: datetime.utcnow())


# Utility functions for creating test data
def create_test_patterns(count: int = 5, **kwargs) -> List[DetectedPattern]:
    """Create a list of test patterns."""
    return [PatternFactory(**kwargs) for _ in range(count)]


def create_security_patterns(count: int = 3) -> List[DetectedPattern]:
    """Create security-related test patterns."""
    return [SecurityPatternFactory() for _ in range(count)]


def create_performance_patterns(count: int = 3) -> List[DetectedPattern]:
    """Create performance-related test patterns."""
    return [PerformancePatternFactory() for _ in range(count)]


def create_mixed_severity_patterns(count: int = 10) -> List[DetectedPattern]:
    """Create patterns with mixed severity levels."""
    patterns = []
    severities = list(SeverityLevel)
    
    for i in range(count):
        severity = severities[i % len(severities)]
        pattern = PatternFactory(severity=severity)
        patterns.append(pattern)
    
    return patterns


def create_test_repository_with_patterns(pattern_count: int = 20) -> Dict[str, Any]:
    """Create a test repository with associated patterns."""
    repo_id = f"repo-{uuid4().hex[:8]}"
    
    return {
        "repository": RepositoryRecordFactory(id=repo_id),
        "patterns": [PatternFactory() for _ in range(pattern_count)],
        "analysis": AnalysisRecordFactory(
            repository_id=repo_id,
            patterns_detected=pattern_count
        )
    }


# Builder pattern for complex test scenarios
class TestScenarioBuilder:
    """Builder for creating complex test scenarios."""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """Reset the builder state."""
        self._repository_id = None
        self._patterns = []
        self._analysis = None
        self._requests = []
        self._responses = []
        return self
    
    def with_repository(self, repository_id: str = None) -> 'TestScenarioBuilder':
        """Set repository for the scenario."""
        self._repository_id = repository_id or f"repo-{uuid4().hex[:8]}"
        return self
    
    def with_patterns(self, count: int = 10, **kwargs) -> 'TestScenarioBuilder':
        """Add patterns to the scenario."""
        self._patterns.extend([PatternFactory(**kwargs) for _ in range(count)])
        return self
    
    def with_security_patterns(self, count: int = 5) -> 'TestScenarioBuilder':
        """Add security patterns to the scenario."""
        self._patterns.extend([SecurityPatternFactory() for _ in range(count)])
        return self
    
    def with_performance_patterns(self, count: int = 3) -> 'TestScenarioBuilder':
        """Add performance patterns to the scenario."""
        self._patterns.extend([PerformancePatternFactory() for _ in range(count)])
        return self
    
    def with_analysis(self, **kwargs) -> 'TestScenarioBuilder':
        """Add analysis record to the scenario."""
        self._analysis = AnalysisRecordFactory(
            repository_id=self._repository_id,
            patterns_detected=len(self._patterns),
            **kwargs
        )
        return self
    
    def with_request(self, **kwargs) -> 'TestScenarioBuilder':
        """Add detection request to the scenario."""
        self._requests.append(PatternDetectionRequestFactory(
            repository_id=self._repository_id,
            **kwargs
        ))
        return self
    
    def with_batch_request(self, **kwargs) -> 'TestScenarioBuilder':
        """Add batch detection request to the scenario."""
        self._requests.append(BatchDetectionRequestFactory(
            repository_id=self._repository_id,
            **kwargs
        ))
        return self
    
    def with_response(self, **kwargs) -> 'TestScenarioBuilder':
        """Add detection response to the scenario."""
        self._responses.append(PatternDetectionResponseFactory(
            repository_id=self._repository_id,
            patterns=self._patterns,
            **kwargs
        ))
        return self
    
    def build(self) -> Dict[str, Any]:
        """Build the test scenario."""
        scenario = {
            "repository_id": self._repository_id,
            "patterns": self._patterns,
            "analysis": self._analysis,
            "requests": self._requests,
            "responses": self._responses
        }
        
        self.reset()
        return scenario