"""
Test Data Generators

Generate realistic test data for code samples, patterns, and repositories.
"""

import random
import string
from typing import Dict, List, Any, Optional, Tu<PERSON>
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
import json

from pattern_mining.models.patterns import PatternType, SeverityLevel, DetectionType


class CodeGenerator:
    """Generate realistic code samples for testing."""
    
    PYTHON_TEMPLATES = {
        "function": """
def {function_name}({parameters}):
    \"\"\"{docstring}\"\"\"
    {body}
    return {return_value}
""",
        "class": """
class {class_name}:
    \"\"\"{docstring}\"\"\"
    
    def __init__(self{init_params}):
        {init_body}
    
    {methods}
""",
        "loop": """
for {variable} in {iterable}:
    {body}
""",
        "condition": """
if {condition}:
    {body}
elif {elif_condition}:
    {elif_body}
else:
    {else_body}
""",
        "try_catch": """
try:
    {try_body}
except {exception} as e:
    {except_body}
finally:
    {finally_body}
""",
        "async_function": """
async def {function_name}({parameters}):
    \"\"\"{docstring}\"\"\"
    {body}
    return {return_value}
""",
        "context_manager": """
with {context_manager} as {variable}:
    {body}
""",
        "lambda": """
{variable} = lambda {parameters}: {expression}
""",
        "list_comprehension": """
{variable} = [{expression} for {item} in {iterable} if {condition}]
""",
        "decorator": """
@{decorator_name}
def {function_name}({parameters}):
    {body}
""",
        "generator": """
def {function_name}({parameters}):
    for {variable} in {iterable}:
        yield {expression}
""",
        "dataclass": """
@dataclass
class {class_name}:
    {fields}
    
    def {method_name}(self):
        {method_body}
""",
        "property": """
@property
def {property_name}(self):
    return self._{property_name}

@{property_name}.setter
def {property_name}(self, value):
    self._{property_name} = value
""",
        "abstract_class": """
from abc import ABC, abstractmethod

class {class_name}(ABC):
    @abstractmethod
    def {method_name}(self):
        pass
""",
        "singleton": """
class {class_name}:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
""",
        "factory": """
class {class_name}Factory:
    @staticmethod
    def create_{type}({parameters}):
        return {class_name}({arguments})
""",
        "observer": """
class {class_name}:
    def __init__(self):
        self._observers = []
    
    def attach(self, observer):
        self._observers.append(observer)
    
    def notify(self, event):
        for observer in self._observers:
            observer.update(event)
""",
        "strategy": """
class {class_name}Strategy:
    def execute(self, data):
        raise NotImplementedError

class {concrete_class}({class_name}Strategy):
    def execute(self, data):
        {implementation}
""",
        "api_endpoint": """
@app.route('/{endpoint}', methods=['{method}'])
def {function_name}():
    {body}
    return jsonify({response})
""",
        "database_model": """
class {class_name}(db.Model):
    __tablename__ = '{table_name}'
    
    id = db.Column(db.Integer, primary_key=True)
    {fields}
    
    def to_dict(self):
        return {dict_body}
""",
        "unit_test": """
class Test{class_name}(unittest.TestCase):
    def setUp(self):
        {setup_body}
    
    def test_{test_name}(self):
        {test_body}
        self.assertEqual({expected}, {actual})
""",
        "async_context_manager": """
class {class_name}:
    async def __aenter__(self):
        {enter_body}
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        {exit_body}
""",
        "metaclass": """
class {class_name}Meta(type):
    def __new__(cls, name, bases, attrs):
        {new_body}
        return super().__new__(cls, name, bases, attrs)

class {class_name}(metaclass={class_name}Meta):
    {body}
""",
        "enum": """
from enum import Enum

class {enum_name}(Enum):
    {enum_values}
""",
        "namedtuple": """
from collections import namedtuple

{class_name} = namedtuple('{class_name}', [{fields}])
""",
        "type_hints": """
from typing import List, Dict, Optional, Union, Any

def {function_name}(
    {typed_parameters}
) -> {return_type}:
    {body}
""",
        "generic_class": """
from typing import TypeVar, Generic

T = TypeVar('T')

class {class_name}(Generic[T]):
    def __init__(self, item: T):
        self.item = item
    
    def get_item(self) -> T:
        return self.item
""",
        "protocol": """
from typing import Protocol

class {protocol_name}(Protocol):
    def {method_name}(self, {parameters}) -> {return_type}:
        ...
""",
        "coroutine": """
import asyncio

async def {function_name}({parameters}):
    {body}
    await asyncio.sleep(0.1)
    return {return_value}
""",
        "performance_issue": """
def {function_name}({parameters}):
    # Performance issue: nested loops
    result = []
    for i in range(len({list1})):
        for j in range(len({list2})):
            for k in range(len({list3})):
                result.append({computation})
    return result
""",
        "security_issue": """
def {function_name}(user_input):
    # Security issue: SQL injection vulnerability
    query = f"SELECT * FROM users WHERE name = '{user_input}'"
    return execute_query(query)
""",
        "code_smell": """
def {function_name}():
    # Code smell: long method with many parameters
    {long_body}
    
    # Code smell: nested conditionals
    if condition1:
        if condition2:
            if condition3:
                if condition4:
                    {nested_body}
""",
        "memory_leak": """
class {class_name}:
    def __init__(self):
        self.data = []
    
    def add_data(self, item):
        # Memory leak: references never cleared
        self.data.append(item)
        # Missing: cleanup mechanism
""",
        "thread_safety": """
import threading

class {class_name}:
    def __init__(self):
        self._lock = threading.Lock()
        self._data = []
    
    def add_item(self, item):
        with self._lock:
            self._data.append(item)
""",
        "bad_practice": """
def {function_name}():
    # Bad practice: catching all exceptions
    try:
        {risky_code}
    except:
        pass  # Silent failure
    
    # Bad practice: mutable default argument
    def helper(items=[]):
        items.append("default")
        return items
""",
        "optimization_opportunity": """
def {function_name}(data):
    # Optimization opportunity: use dict for faster lookups
    result = []
    for item in data:
        for reference in reference_list:  # O(n*m)
            if item == reference:
                result.append(item)
    return result
""",
        "architectural_issue": """
class {class_name}:
    # Architectural issue: God class
    def __init__(self):
        self.database = Database()
        self.cache = Cache()
        self.logger = Logger()
        self.validator = Validator()
        self.serializer = Serializer()
    
    def process_data(self, data):
        # Too many responsibilities
        {processing_logic}
""",
        "dependency_issue": """
# Dependency issue: tight coupling
class {class_name}:
    def __init__(self):
        self.service = ConcreteService()  # Should be injected
    
    def process(self, data):
        return self.service.process(data)
""",
        "testability_issue": """
def {function_name}():
    # Testability issue: hard to mock
    current_time = datetime.now()  # Direct dependency
    file_content = open('config.txt').read()  # File system dependency
    
    {processing_logic}
""",
    }
    
    JAVASCRIPT_TEMPLATES = {
        "function": """
function {function_name}({parameters}) {{
    {body}
    return {return_value};
}}
""",
        "arrow_function": """
const {function_name} = ({parameters}) => {{
    {body}
    return {return_value};
}};
""",
        "class": """
class {class_name} {{
    constructor({constructor_params}) {{
        {constructor_body}
    }}
    
    {methods}
}}
""",
        "async_function": """
async function {function_name}({parameters}) {{
    {body}
    return {return_value};
}}
""",
        "promise": """
const {function_name} = ({parameters}) => {{
    return new Promise((resolve, reject) => {{
        {body}
        resolve({return_value});
    }});
}};
""",
        "callback": """
function {function_name}({parameters}, callback) {{
    {body}
    callback(null, {return_value});
}}
""",
        "closure": """
function {function_name}() {{
    const {variable} = {value};
    
    return function({parameters}) {{
        {body}
        return {return_value};
    }};
}}
""",
        "prototype": """
function {constructor_name}({parameters}) {{
    {constructor_body}
}}

{constructor_name}.prototype.{method_name} = function({parameters}) {{
    {method_body}
}};
""",
        "module": """
const {module_name} = (function() {{
    let {private_variable} = {value};
    
    return {{
        {public_method}: function({parameters}) {{
            {method_body}
        }}
    }};
}})();
""",
        "event_handler": """
document.addEventListener('{event}', function(event) {{
    {body}
}});
""",
        "xhr_security_issue": """
function {function_name}(userInput) {{
    // Security issue: XSS vulnerability
    document.getElementById('output').innerHTML = userInput;
}}
""",
        "performance_issue": """
function {function_name}(data) {{
    // Performance issue: inefficient DOM manipulation
    for (let i = 0; i < data.length; i++) {{
        document.getElementById('list').innerHTML += '<li>' + data[i] + '</li>';
    }}
}}
""",
        "memory_leak": """
function {function_name}() {{
    // Memory leak: event listeners not removed
    const element = document.getElementById('button');
    element.addEventListener('click', function() {{
        {handler_body}
    }});
    // Missing: removeEventListener
}}
""",
        "callback_hell": """
function {function_name}() {{
    // Anti-pattern: callback hell
    getData(function(a) {{
        getMoreData(a, function(b) {{
            getEvenMoreData(b, function(c) {{
                getFinalData(c, function(d) {{
                    {final_logic}
                }});
            }});
        }});
    }});
}}
""",
        "global_variable": """
// Code smell: global variables
var {variable_name} = {value};

function {function_name}() {{
    // Modifying global state
    {variable_name} = {new_value};
}}
""",
        "type_coercion": """
function {function_name}(value) {{
    // Code smell: implicit type coercion
    if (value == null) {{  // Should use ===
        return {default_value};
    }}
    return value + '';  // Implicit string conversion
}}
""",
    }
    
    JAVA_TEMPLATES = {
        "class": """
public class {class_name} {{
    {fields}
    
    public {class_name}({constructor_params}) {{
        {constructor_body}
    }}
    
    {methods}
}}
""",
        "interface": """
public interface {interface_name} {{
    {method_signatures}
}}
""",
        "abstract_class": """
public abstract class {class_name} {{
    {fields}
    
    public abstract {return_type} {method_name}({parameters});
    
    {concrete_methods}
}}
""",
        "singleton": """
public class {class_name} {{
    private static {class_name} instance;
    
    private {class_name}() {{}}
    
    public static {class_name} getInstance() {{
        if (instance == null) {{
            instance = new {class_name}();
        }}
        return instance;
    }}
}}
""",
        "factory": """
public class {class_name}Factory {{
    public static {class_name} create{type}({parameters}) {{
        return new {concrete_class}({arguments});
    }}
}}
""",
        "builder": """
public class {class_name} {{
    {fields}
    
    public static class Builder {{
        {builder_fields}
        
        public Builder {setter_name}({type} {parameter}) {{
            this.{parameter} = {parameter};
            return this;
        }}
        
        public {class_name} build() {{
            return new {class_name}(this);
        }}
    }}
}}
""",
        "observer": """
public class {class_name} extends Observable {{
    {fields}
    
    public void {method_name}({parameters}) {{
        {body}
        setChanged();
        notifyObservers({argument});
    }}
}}
""",
        "strategy": """
public interface {strategy_interface} {{
    {return_type} execute({parameters});
}}

public class {concrete_strategy} implements {strategy_interface} {{
    @Override
    public {return_type} execute({parameters}) {{
        {implementation}
    }}
}}
""",
        "exception_handling": """
public {return_type} {method_name}({parameters}) throws {exception} {{
    try {{
        {try_body}
    }} catch ({exception} e) {{
        {catch_body}
    }} finally {{
        {finally_body}
    }}
}}
""",
        "thread_safety": """
public class {class_name} {{
    private final Object lock = new Object();
    private {type} {field_name};
    
    public {type} get{field_name}() {{
        synchronized (lock) {{
            return {field_name};
        }}
    }}
    
    public void set{field_name}({type} {field_name}) {{
        synchronized (lock) {{
            this.{field_name} = {field_name};
        }}
    }}
}}
""",
        "security_issue": """
public class {class_name} {{
    // Security issue: SQL injection vulnerability
    public List<User> getUsers(String name) {{
        String query = "SELECT * FROM users WHERE name = '" + name + "'";
        return executeQuery(query);
    }}
}}
""",
        "performance_issue": """
public class {class_name} {{
    // Performance issue: inefficient string concatenation
    public String buildString(List<String> items) {{
        String result = "";
        for (String item : items) {{
            result += item + ", ";  // Creates new string each time
        }}
        return result;
    }}
}}
""",
        "memory_leak": """
public class {class_name} {{
    private static List<{type}> cache = new ArrayList<>();
    
    public void addToCache({type} item) {{
        // Memory leak: cache never cleared
        cache.add(item);
    }}
}}
""",
        "null_pointer": """
public class {class_name} {{
    // Null pointer risk
    public {return_type} {method_name}({type} input) {{
        return input.{method_call}();  // No null check
    }}
}}
""",
        "resource_leak": """
public class {class_name} {{
    // Resource leak: connection not closed
    public void {method_name}() {{
        Connection conn = DriverManager.getConnection(url);
        // Missing: conn.close() in finally block
        {body}
    }}
}}
""",
        "god_class": """
public class {class_name} {{
    // Architectural issue: God class
    private Database database;
    private Cache cache;
    private Logger logger;
    private Validator validator;
    private Serializer serializer;
    private EmailService emailService;
    private PaymentService paymentService;
    
    // Too many responsibilities
    {many_methods}
}}
""",
        "tight_coupling": """
public class {class_name} {{
    // Tight coupling: direct dependency
    private ConcreteService service = new ConcreteService();
    
    public void process() {{
        service.execute();  // Should use dependency injection
    }}
}}
""",
        "violation_srp": """
public class {class_name} {{
    // SRP violation: multiple responsibilities
    public void saveUser(User user) {{
        validateUser(user);
        encryptPassword(user);
        saveToDatabase(user);
        sendWelcomeEmail(user);
        logActivity(user);
    }}
}}
""",
        "violation_dip": """
public class {class_name} {{
    // DIP violation: depends on concrete class
    private MySQLDatabase database = new MySQLDatabase();
    
    public void save(Object data) {{
        database.save(data);  // Should depend on abstraction
    }}
}}
""",
    }
    
    def __init__(self, language: str = "python"):
        """Initialize code generator for specific language."""
        self.language = language.lower()
        self.templates = self._get_templates()
        self.names = self._get_names()
        self.values = self._get_values()
    
    def _get_templates(self) -> Dict[str, str]:
        """Get templates for the specified language."""
        templates = {
            "python": self.PYTHON_TEMPLATES,
            "javascript": self.JAVASCRIPT_TEMPLATES,
            "java": self.JAVA_TEMPLATES,
        }
        return templates.get(self.language, self.PYTHON_TEMPLATES)
    
    def _get_names(self) -> Dict[str, List[str]]:
        """Get realistic names for different code elements."""
        return {
            "functions": [
                "calculate_total", "process_data", "validate_input", "format_output",
                "fetch_user", "save_record", "update_status", "delete_item",
                "generate_report", "send_notification", "parse_response",
                "authenticate_user", "authorize_access", "encrypt_data",
                "compress_file", "backup_database", "restore_backup",
                "optimize_query", "cache_result", "log_event", "handle_error",
                "transform_data", "filter_results", "sort_items", "merge_lists",
                "create_session", "close_connection", "execute_command",
                "render_template", "serialize_object", "deserialize_json"
            ],
            "classes": [
                "UserManager", "DatabaseConnection", "FileProcessor", "DataValidator",
                "EmailService", "PaymentProcessor", "SecurityManager", "CacheManager",
                "ConfigLoader", "LoggingHandler", "ErrorHandler", "ResponseBuilder",
                "RequestParser", "SessionManager", "AuthenticationService",
                "NotificationService", "ReportGenerator", "DataTransformer",
                "FileUploader", "ImageProcessor", "PDFGenerator", "CSVParser",
                "XMLProcessor", "JSONSerializer", "TemplateEngine", "SearchEngine",
                "ShoppingCart", "OrderProcessor", "InventoryManager", "ProductCatalog"
            ],
            "variables": [
                "user_data", "config_settings", "database_url", "api_key",
                "request_headers", "response_data", "error_message", "status_code",
                "file_path", "directory_name", "temp_file", "log_level",
                "max_retries", "timeout_seconds", "buffer_size", "chunk_size",
                "page_number", "page_size", "total_count", "current_index",
                "start_time", "end_time", "elapsed_time", "processing_time",
                "memory_usage", "cpu_usage", "disk_space", "network_latency",
                "connection_pool", "thread_pool", "queue_size", "batch_size"
            ],
            "parameters": [
                "request", "response", "data", "config", "options", "settings",
                "context", "session", "user", "item", "record", "entity",
                "model", "view", "controller", "service", "repository",
                "factory", "builder", "handler", "processor", "manager",
                "validator", "serializer", "parser", "formatter", "transformer",
                "filter", "sorter", "mapper", "reducer", "aggregator"
            ]
        }
    
    def _get_values(self) -> Dict[str, List[str]]:
        """Get realistic values for different data types."""
        return {
            "strings": [
                '"Hello, World!"', '"success"', '"error"', '"pending"',
                '"active"', '"inactive"', '"processing"', '"completed"',
                '"failed"', '"cancelled"', '"approved"', '"rejected"',
                '"published"', '"draft"', '"archived"', '"deleted"'
            ],
            "numbers": [
                "0", "1", "10", "100", "1000", "5000", "10000",
                "0.5", "1.0", "3.14", "2.718", "0.001", "99.99"
            ],
            "booleans": ["True", "False"],
            "none": ["None", "null", "undefined"],
            "lists": [
                "[]", "[1, 2, 3]", "['a', 'b', 'c']", "[user1, user2, user3]",
                "[item for item in items]", "list(range(10))"
            ],
            "dicts": [
                "{}", "{'key': 'value'}", "{'status': 'success', 'data': result}",
                "{'id': 1, 'name': 'test', 'active': True}"
            ]
        }
    
    def generate_function(self, pattern_type: Optional[PatternType] = None) -> str:
        """Generate a function with optional pattern type."""
        if pattern_type in [PatternType.SECURITY_ISSUE, PatternType.VULNERABILITY]:
            template_key = "security_issue"
        elif pattern_type == PatternType.PERFORMANCE_ISSUE:
            template_key = "performance_issue"
        elif pattern_type == PatternType.CODE_SMELL:
            template_key = "code_smell"
        elif pattern_type == PatternType.MEMORY_LEAK:
            template_key = "memory_leak"
        else:
            template_key = random.choice(["function", "async_function", "lambda"])
        
        template = self.templates.get(template_key, self.templates["function"])
        
        return template.format(
            function_name=random.choice(self.names["functions"]),
            parameters=", ".join(random.sample(self.names["parameters"], random.randint(1, 3))),
            docstring=f"Process {random.choice(['data', 'request', 'input', 'information'])}",
            body=self._generate_body(),
            return_value=random.choice(self.values["strings"] + self.values["numbers"]),
            variable=random.choice(self.names["variables"]),
            iterable=random.choice(["items", "data", "records", "users"]),
            condition=self._generate_condition(),
            elif_condition=self._generate_condition(),
            elif_body=self._generate_body(),
            else_body=self._generate_body(),
            exception="Exception",
            try_body=self._generate_body(),
            except_body='logger.error("Error occurred: %s", e)',
            finally_body='cleanup_resources()',
            context_manager=random.choice(["open(file_path)", "get_connection()", "session_scope()"]),
            expression=random.choice(["item.value", "item * 2", "item.process()"]),
            decorator_name=random.choice(["@property", "@staticmethod", "@classmethod", "@lru_cache"]),
            risky_code=self._generate_body(),
            processing_logic=self._generate_body(),
            list1=random.choice(self.names["variables"]),
            list2=random.choice(self.names["variables"]),
            list3=random.choice(self.names["variables"]),
            computation="i + j + k",
            long_body=self._generate_long_body(),
            nested_body=self._generate_body(),
            condition1=self._generate_condition(),
            condition2=self._generate_condition(),
            condition3=self._generate_condition(),
            condition4=self._generate_condition()
        )
    
    def generate_class(self, pattern_type: Optional[PatternType] = None) -> str:
        """Generate a class with optional pattern type."""
        if pattern_type == PatternType.DESIGN_PATTERN:
            template_key = random.choice(["singleton", "factory", "observer", "strategy"])
        elif pattern_type in [PatternType.ARCHITECTURAL_SMELL, PatternType.ANTI_PATTERN]:
            template_key = "architectural_issue"
        else:
            template_key = "class"
        
        template = self.templates.get(template_key, self.templates["class"])
        
        return template.format(
            class_name=random.choice(self.names["classes"]),
            docstring=f"Manages {random.choice(['users', 'data', 'files', 'connections'])}",
            init_params=", ".join(random.sample(self.names["parameters"], random.randint(1, 3))),
            init_body=self._generate_body(),
            methods=self._generate_methods(),
            fields=self._generate_fields(),
            method_name=random.choice(self.names["functions"]),
            method_body=self._generate_body(),
            decorator_name=random.choice(["@property", "@staticmethod", "@classmethod"]),
            property_name=random.choice(self.names["variables"]),
            type=random.choice(["Concrete", "Advanced", "Simple"]),
            parameters=", ".join(random.sample(self.names["parameters"], random.randint(1, 2))),
            arguments=", ".join(random.sample(self.names["variables"], random.randint(1, 2))),
            concrete_class=f"Concrete{random.choice(self.names['classes'])}",
            implementation=self._generate_body(),
            processing_logic=self._generate_body()
        )
    
    def generate_pattern_example(self, pattern_type: PatternType) -> str:
        """Generate code example for specific pattern type."""
        pattern_generators = {
            PatternType.DESIGN_PATTERN: self._generate_design_pattern,
            PatternType.ANTI_PATTERN: self._generate_anti_pattern,
            PatternType.CODE_SMELL: self._generate_code_smell,
            PatternType.SECURITY_ISSUE: self._generate_security_issue,
            PatternType.PERFORMANCE_ISSUE: self._generate_performance_issue,
            PatternType.BUG_PATTERN: self._generate_bug_pattern,
            PatternType.BEST_PRACTICE: self._generate_best_practice,
            PatternType.ARCHITECTURAL_SMELL: self._generate_architectural_smell,
            PatternType.TESTING_PATTERN: self._generate_testing_pattern,
            PatternType.CONCURRENCY_ISSUE: self._generate_concurrency_issue,
            PatternType.MEMORY_LEAK: self._generate_memory_leak,
            PatternType.RESOURCE_LEAK: self._generate_resource_leak,
        }
        
        generator = pattern_generators.get(pattern_type, self.generate_function)
        return generator()
    
    def _generate_design_pattern(self) -> str:
        """Generate design pattern example."""
        patterns = ["singleton", "factory", "observer", "strategy", "builder", "decorator"]
        pattern = random.choice(patterns)
        template = self.templates.get(pattern, self.templates["class"])
        
        return template.format(
            class_name=random.choice(self.names["classes"]),
            type=random.choice(["Basic", "Advanced", "Enhanced"]),
            strategy_interface=f"{random.choice(self.names['classes'])}Strategy",
            concrete_strategy=f"Concrete{random.choice(self.names['classes'])}Strategy",
            decorator_name=random.choice(["@property", "@staticmethod", "@classmethod"]),
            parameters=", ".join(random.sample(self.names["parameters"], random.randint(1, 2))),
            return_type=random.choice(["String", "int", "boolean", "Object"]),
            method_name=random.choice(self.names["functions"]),
            arguments=", ".join(random.sample(self.names["variables"], random.randint(1, 2))),
            implementation=self._generate_body(),
            body=self._generate_body(),
            fields=self._generate_fields(),
            builder_fields=self._generate_fields(),
            setter_name=f"set{random.choice(self.names['variables']).title()}",
            parameter=random.choice(self.names["parameters"]),
            argument=random.choice(self.names["variables"])
        )
    
    def _generate_anti_pattern(self) -> str:
        """Generate anti-pattern example."""
        return self.templates["bad_practice"].format(
            function_name=random.choice(self.names["functions"]),
            risky_code=self._generate_body(),
            class_name=random.choice(self.names["classes"]),
            processing_logic=self._generate_body()
        )
    
    def _generate_code_smell(self) -> str:
        """Generate code smell example."""
        return self.templates["code_smell"].format(
            function_name=random.choice(self.names["functions"]),
            long_body=self._generate_long_body(),
            nested_body=self._generate_body(),
            condition1=self._generate_condition(),
            condition2=self._generate_condition(),
            condition3=self._generate_condition(),
            condition4=self._generate_condition()
        )
    
    def _generate_security_issue(self) -> str:
        """Generate security issue example."""
        return self.templates["security_issue"].format(
            function_name=random.choice(self.names["functions"])
        )
    
    def _generate_performance_issue(self) -> str:
        """Generate performance issue example."""
        return self.templates["performance_issue"].format(
            function_name=random.choice(self.names["functions"]),
            parameters=", ".join(random.sample(self.names["parameters"], random.randint(1, 2))),
            list1=random.choice(self.names["variables"]),
            list2=random.choice(self.names["variables"]),
            list3=random.choice(self.names["variables"]),
            computation="complex_calculation(i, j, k)"
        )
    
    def _generate_bug_pattern(self) -> str:
        """Generate bug pattern example."""
        return self.templates["bad_practice"].format(
            function_name=random.choice(self.names["functions"]),
            risky_code="division = numerator / denominator  # No zero check",
            class_name=random.choice(self.names["classes"]),
            processing_logic="return items[index]  # No bounds check"
        )
    
    def _generate_best_practice(self) -> str:
        """Generate best practice example."""
        return self.templates["thread_safety"].format(
            class_name=random.choice(self.names["classes"]),
            variable=random.choice(self.names["variables"])
        )
    
    def _generate_architectural_smell(self) -> str:
        """Generate architectural smell example."""
        return self.templates["architectural_issue"].format(
            class_name=random.choice(self.names["classes"]),
            processing_logic=self._generate_body()
        )
    
    def _generate_testing_pattern(self) -> str:
        """Generate testing pattern example."""
        return self.templates["unit_test"].format(
            class_name=random.choice(self.names["classes"]),
            setup_body="self.instance = TestClass()",
            test_name=random.choice(self.names["functions"]),
            test_body="result = self.instance.process_data(test_data)",
            expected="expected_result",
            actual="result"
        )
    
    def _generate_concurrency_issue(self) -> str:
        """Generate concurrency issue example."""
        return self.templates["thread_safety"].format(
            class_name=random.choice(self.names["classes"]),
            variable=random.choice(self.names["variables"])
        )
    
    def _generate_memory_leak(self) -> str:
        """Generate memory leak example."""
        return self.templates["memory_leak"].format(
            class_name=random.choice(self.names["classes"])
        )
    
    def _generate_resource_leak(self) -> str:
        """Generate resource leak example."""
        return self.templates["testability_issue"].format(
            function_name=random.choice(self.names["functions"]),
            processing_logic=self._generate_body()
        )
    
    def _generate_body(self) -> str:
        """Generate function body."""
        statements = [
            f"{random.choice(self.names['variables'])} = {random.choice(self.values['strings'])}",
            f"logger.info('Processing {random.choice(['data', 'request', 'input'])}')",
            f"validate_{random.choice(['input', 'data', 'parameters'])}()",
            f"result = process_{random.choice(['data', 'input', 'request'])}()",
            f"if {self._generate_condition()}:",
            f"    {random.choice(['return', 'raise', 'continue', 'break'])} {random.choice(self.values['strings'])}",
            f"for {random.choice(['item', 'record', 'element'])} in {random.choice(['items', 'records', 'data'])}:",
            f"    {random.choice(['process', 'validate', 'transform'])}({random.choice(['item', 'record', 'element'])})",
        ]
        
        return "\n    ".join(random.sample(statements, random.randint(2, 4)))
    
    def _generate_long_body(self) -> str:
        """Generate long function body for code smell examples."""
        statements = [
            f"{random.choice(self.names['variables'])} = {random.choice(self.values['strings'])}",
            f"logger.info('Processing step {i}')" for i in range(1, 6)
        ] + [
            f"validate_{random.choice(['input', 'data', 'parameters'])}()",
            f"result = process_{random.choice(['data', 'input', 'request'])}()",
            f"transform_{random.choice(['data', 'input', 'output'])}()",
            f"serialize_{random.choice(['data', 'result', 'output'])}()",
            f"save_{random.choice(['data', 'result', 'output'])}()",
            f"send_{random.choice(['notification', 'email', 'message'])}()",
            f"log_{random.choice(['activity', 'event', 'action'])}()",
            f"cleanup_{random.choice(['resources', 'temporary', 'cache'])}()",
        ]
        
        return "\n    ".join(statements)
    
    def _generate_condition(self) -> str:
        """Generate condition for if statements."""
        conditions = [
            f"{random.choice(self.names['variables'])} is not None",
            f"{random.choice(self.names['variables'])} == {random.choice(self.values['strings'])}",
            f"len({random.choice(self.names['variables'])}) > 0",
            f"{random.choice(self.names['variables'])}.{random.choice(['is_valid', 'is_active', 'is_ready'])}()",
            f"hasattr({random.choice(self.names['variables'])}, '{random.choice(self.names['functions'])}')",
        ]
        
        return random.choice(conditions)
    
    def _generate_methods(self) -> str:
        """Generate class methods."""
        methods = []
        for _ in range(random.randint(2, 4)):
            method = f"""
    def {random.choice(self.names["functions"])}(self, {random.choice(self.names["parameters"])}):
        {self._generate_body()}
        return {random.choice(self.values["strings"])}
"""
            methods.append(method)
        
        return "\n".join(methods)
    
    def _generate_fields(self) -> str:
        """Generate class fields."""
        fields = []
        for _ in range(random.randint(2, 4)):
            field = f"self.{random.choice(self.names['variables'])} = {random.choice(self.values['strings'])}"
            fields.append(field)
        
        return "\n        ".join(fields)
    
    def generate_file_content(self, file_type: str = "module", pattern_count: int = 3) -> str:
        """Generate complete file content with multiple patterns."""
        content = []
        
        if self.language == "python":
            content.append('"""')
            content.append(f"Generated {file_type} for testing pattern detection.")
            content.append('"""')
            content.append("")
            content.append("import os")
            content.append("import sys")
            content.append("import logging")
            content.append("from typing import List, Dict, Any, Optional")
            content.append("")
        
        # Generate patterns
        pattern_types = list(PatternType)
        for i in range(pattern_count):
            pattern_type = random.choice(pattern_types)
            
            if i % 2 == 0:
                code = self.generate_class(pattern_type)
            else:
                code = self.generate_function(pattern_type)
            
            content.append(code)
            content.append("")
        
        return "\n".join(content)


class PatternGenerator:
    """Generate realistic pattern detection results."""
    
    def __init__(self):
        self.code_generator = CodeGenerator()
    
    def generate_pattern(
        self,
        pattern_type: PatternType,
        severity: SeverityLevel,
        file_path: str = "test.py",
        line_number: int = None
    ) -> Dict[str, Any]:
        """Generate a single pattern detection result."""
        return {
            "pattern_id": f"pattern-{random.randint(1000, 9999)}",
            "pattern_name": self._get_pattern_name(pattern_type),
            "pattern_type": pattern_type.value,
            "severity": severity.value,
            "confidence": round(random.uniform(0.7, 0.98), 2),
            "location": {
                "file": file_path,
                "line": line_number or random.randint(1, 100),
                "column": random.randint(1, 80),
                "end_line": (line_number or random.randint(1, 100)) + random.randint(1, 10),
                "end_column": random.randint(1, 80)
            },
            "description": self._get_pattern_description(pattern_type),
            "detection_method": random.choice(list(DetectionType)).value,
            "context": self._get_pattern_context(pattern_type),
            "metadata": {
                "model_version": f"1.{random.randint(0, 9)}.0",
                "processing_time_ms": random.randint(5, 50),
                "timestamp": datetime.utcnow().isoformat()
            }
        }
    
    def _get_pattern_name(self, pattern_type: PatternType) -> str:
        """Get realistic pattern name for type."""
        names = {
            PatternType.DESIGN_PATTERN: [
                "Singleton Pattern", "Factory Pattern", "Observer Pattern",
                "Strategy Pattern", "Builder Pattern", "Decorator Pattern",
                "Command Pattern", "Adapter Pattern", "Facade Pattern"
            ],
            PatternType.ANTI_PATTERN: [
                "God Object", "Spaghetti Code", "Copy-Paste Programming",
                "Magic Numbers", "Hard Coding", "Shotgun Surgery",
                "Feature Envy", "Inappropriate Intimacy"
            ],
            PatternType.CODE_SMELL: [
                "Long Method", "Large Class", "Duplicate Code",
                "Long Parameter List", "Divergent Change", "Data Clumps",
                "Primitive Obsession", "Switch Statements"
            ],
            PatternType.SECURITY_ISSUE: [
                "SQL Injection", "XSS Vulnerability", "CSRF Vulnerability",
                "Authentication Bypass", "Authorization Flaw", "Path Traversal",
                "Command Injection", "Buffer Overflow"
            ],
            PatternType.PERFORMANCE_ISSUE: [
                "Inefficient Loop", "N+1 Query Problem", "Memory Leak",
                "Inefficient Algorithm", "Unnecessary Object Creation",
                "Blocking I/O", "Synchronous Processing"
            ],
            PatternType.BUG_PATTERN: [
                "Null Pointer Dereference", "Array Index Out of Bounds",
                "Division by Zero", "Resource Leak", "Infinite Loop",
                "Race Condition", "Deadlock"
            ],
            PatternType.BEST_PRACTICE: [
                "Dependency Injection", "Error Handling", "Logging",
                "Input Validation", "Resource Management", "Thread Safety",
                "Exception Handling", "Configuration Management"
            ],
            PatternType.ARCHITECTURAL_SMELL: [
                "Circular Dependencies", "Tight Coupling", "God Component",
                "Layering Violation", "Unstable Interface", "Ambiguous Interface",
                "Scattered Functionality", "Dense Structure"
            ],
            PatternType.TESTING_PATTERN: [
                "Test Fixture", "Test Double", "Mock Object",
                "Test Data Builder", "Page Object", "Test Utility",
                "Parameterized Test", "Test Suite"
            ],
            PatternType.CONCURRENCY_ISSUE: [
                "Race Condition", "Deadlock", "Livelock",
                "Thread Starvation", "Priority Inversion", "Atomic Violation",
                "Lock Contention", "Thread Safety Violation"
            ],
            PatternType.MEMORY_LEAK: [
                "Unclosed Resource", "Circular Reference", "Event Listener Leak",
                "Cache Without TTL", "Static Collection Growth", "Thread Local Leak",
                "Connection Pool Leak", "Observer Pattern Leak"
            ],
            PatternType.RESOURCE_LEAK: [
                "File Handle Leak", "Database Connection Leak", "Socket Leak",
                "Memory Leak", "Thread Leak", "GPU Memory Leak",
                "Network Resource Leak", "Temporary File Leak"
            ]
        }
        
        return random.choice(names.get(pattern_type, ["Unknown Pattern"]))
    
    def _get_pattern_description(self, pattern_type: PatternType) -> str:
        """Get realistic pattern description."""
        descriptions = {
            PatternType.DESIGN_PATTERN: [
                "Implementation follows the {} design pattern for better code organization",
                "Uses {} pattern to promote code reusability and maintainability",
                "Implements {} pattern for flexible and extensible architecture"
            ],
            PatternType.ANTI_PATTERN: [
                "Code exhibits {} anti-pattern that makes it hard to maintain",
                "Implementation shows {} which violates software design principles",
                "Pattern indicates {} that should be refactored"
            ],
            PatternType.CODE_SMELL: [
                "Code contains {} that suggests potential design issues",
                "Implementation shows {} indicating need for refactoring",
                "Pattern exhibits {} that impacts code quality"
            ],
            PatternType.SECURITY_ISSUE: [
                "Potential {} vulnerability detected in user input handling",
                "Security issue: {} risk in data processing logic",
                "Critical security flaw: {} vulnerability in authentication"
            ],
            PatternType.PERFORMANCE_ISSUE: [
                "Performance bottleneck: {} detected in critical path",
                "Inefficient implementation: {} causing performance degradation",
                "Optimization opportunity: {} can be improved for better performance"
            ],
            PatternType.BUG_PATTERN: [
                "Potential bug: {} may cause runtime errors",
                "Bug pattern detected: {} in error handling logic",
                "Critical issue: {} that can lead to system failure"
            ]
        }
        
        templates = descriptions.get(pattern_type, ["Pattern detected: {}"])
        return random.choice(templates).format(self._get_pattern_name(pattern_type))
    
    def _get_pattern_context(self, pattern_type: PatternType) -> Dict[str, Any]:
        """Get realistic pattern context."""
        contexts = {
            PatternType.DESIGN_PATTERN: {
                "pattern_family": random.choice(["Creational", "Structural", "Behavioral"]),
                "complexity": random.choice(["Low", "Medium", "High"]),
                "implementation_quality": random.choice(["Good", "Excellent", "Perfect"])
            },
            PatternType.SECURITY_ISSUE: {
                "vulnerability_type": random.choice(["Input Validation", "Authentication", "Authorization"]),
                "risk_level": random.choice(["Medium", "High", "Critical"]),
                "cwe_id": random.randint(1, 999),
                "owasp_category": random.choice(["A1", "A2", "A3", "A4", "A5"])
            },
            PatternType.PERFORMANCE_ISSUE: {
                "performance_impact": random.choice(["Low", "Medium", "High"]),
                "optimization_potential": f"{random.randint(10, 80)}%",
                "resource_type": random.choice(["CPU", "Memory", "I/O", "Network"])
            },
            PatternType.CODE_SMELL: {
                "smell_category": random.choice(["Bloaters", "Object-Orientation Abusers", "Change Preventers"]),
                "severity": random.choice(["Minor", "Major", "Critical"]),
                "refactoring_suggestion": random.choice(["Extract Method", "Move Method", "Replace Method"])
            }
        }
        
        return contexts.get(pattern_type, {"category": "General"})
    
    def generate_patterns_for_file(
        self,
        file_path: str,
        pattern_count: int = 5,
        severity_distribution: Dict[SeverityLevel, float] = None
    ) -> List[Dict[str, Any]]:
        """Generate multiple patterns for a file."""
        if severity_distribution is None:
            severity_distribution = {
                SeverityLevel.LOW: 0.4,
                SeverityLevel.MEDIUM: 0.3,
                SeverityLevel.HIGH: 0.2,
                SeverityLevel.CRITICAL: 0.1
            }
        
        patterns = []
        pattern_types = list(PatternType)
        
        for i in range(pattern_count):
            pattern_type = random.choice(pattern_types)
            severity = self._choose_severity(severity_distribution)
            line_number = (i + 1) * 10  # Spread patterns across file
            
            pattern = self.generate_pattern(pattern_type, severity, file_path, line_number)
            patterns.append(pattern)
        
        return patterns
    
    def _choose_severity(self, distribution: Dict[SeverityLevel, float]) -> SeverityLevel:
        """Choose severity based on distribution."""
        rand = random.random()
        cumulative = 0
        
        for severity, probability in distribution.items():
            cumulative += probability
            if rand <= cumulative:
                return severity
        
        return SeverityLevel.LOW  # Fallback


class RepositoryGenerator:
    """Generate realistic repository data for testing."""
    
    def __init__(self):
        self.pattern_generator = PatternGenerator()
        self.code_generator = CodeGenerator()
    
    def generate_repository(
        self,
        repository_id: str = None,
        file_count: int = 10,
        pattern_count: int = 50
    ) -> Dict[str, Any]:
        """Generate complete repository with files and patterns."""
        repo_id = repository_id or f"repo-{random.randint(1000, 9999)}"
        
        # Generate files
        files = []
        languages = ["python", "javascript", "java", "go", "rust"]
        extensions = {
            "python": ".py",
            "javascript": ".js",
            "java": ".java",
            "go": ".go",
            "rust": ".rs"
        }
        
        for i in range(file_count):
            language = random.choice(languages)
            extension = extensions[language]
            file_path = f"src/{random.choice(['main', 'utils', 'models', 'services', 'controllers'])}{i}{extension}"
            
            files.append({
                "path": file_path,
                "language": language,
                "lines": random.randint(50, 500),
                "size": random.randint(1024, 51200)
            })
        
        # Generate patterns distributed across files
        all_patterns = []
        patterns_per_file = pattern_count // file_count
        
        for file_info in files:
            file_patterns = self.pattern_generator.generate_patterns_for_file(
                file_info["path"],
                patterns_per_file + random.randint(0, 5)
            )
            all_patterns.extend(file_patterns)
        
        # Generate repository metadata
        repository = {
            "id": repo_id,
            "name": f"test-repo-{random.randint(1000, 9999)}",
            "url": f"https://github.com/test-org/test-repo-{random.randint(1000, 9999)}",
            "description": "Test repository for pattern detection",
            "language": random.choice(languages),
            "stars": random.randint(0, 5000),
            "forks": random.randint(0, 500),
            "files": files,
            "patterns": all_patterns,
            "statistics": {
                "total_files": len(files),
                "total_lines": sum(f["lines"] for f in files),
                "total_patterns": len(all_patterns),
                "pattern_density": len(all_patterns) / sum(f["lines"] for f in files),
                "languages": list(set(f["language"] for f in files))
            }
        }
        
        return repository
    
    def generate_batch_repositories(
        self,
        count: int = 5,
        files_per_repo: int = 10,
        patterns_per_repo: int = 50
    ) -> List[Dict[str, Any]]:
        """Generate multiple repositories for batch testing."""
        repositories = []
        
        for i in range(count):
            repo = self.generate_repository(
                repository_id=f"batch-repo-{i}",
                file_count=files_per_repo,
                pattern_count=patterns_per_repo
            )
            repositories.append(repo)
        
        return repositories


class TestDataGenerator:
    """High-level test data generator combining all generators."""
    
    def __init__(self):
        self.code_generator = CodeGenerator()
        self.pattern_generator = PatternGenerator()
        self.repository_generator = RepositoryGenerator()
    
    def generate_test_scenario(
        self,
        scenario_type: str = "basic",
        complexity: str = "medium"
    ) -> Dict[str, Any]:
        """Generate complete test scenario with all necessary data."""
        complexity_config = {
            "low": {"files": 5, "patterns": 20, "requests": 3},
            "medium": {"files": 10, "patterns": 50, "requests": 5},
            "high": {"files": 20, "patterns": 100, "requests": 10}
        }
        
        config = complexity_config.get(complexity, complexity_config["medium"])
        
        scenario = {
            "scenario_type": scenario_type,
            "complexity": complexity,
            "repository": self.repository_generator.generate_repository(
                file_count=config["files"],
                pattern_count=config["patterns"]
            ),
            "test_requests": self._generate_test_requests(config["requests"]),
            "expected_responses": self._generate_expected_responses(config["requests"]),
            "performance_benchmarks": self._generate_performance_benchmarks(),
            "validation_rules": self._generate_validation_rules()
        }
        
        return scenario
    
    def _generate_test_requests(self, count: int) -> List[Dict[str, Any]]:
        """Generate test requests."""
        requests = []
        
        for i in range(count):
            request = {
                "request_id": f"test-request-{i}",
                "repository_id": f"test-repo-{i}",
                "code_content": self.code_generator.generate_file_content(),
                "file_path": f"test/file_{i}.py",
                "language": "python",
                "detection_config": {
                    "confidence_threshold": 0.7,
                    "max_patterns_per_file": 50,
                    "enable_ml_models": True,
                    "enable_heuristic_detection": True
                }
            }
            requests.append(request)
        
        return requests
    
    def _generate_expected_responses(self, count: int) -> List[Dict[str, Any]]:
        """Generate expected responses for validation."""
        responses = []
        
        for i in range(count):
            patterns = self.pattern_generator.generate_patterns_for_file(
                f"test/file_{i}.py",
                random.randint(3, 10)
            )
            
            response = {
                "request_id": f"test-request-{i}",
                "repository_id": f"test-repo-{i}",
                "patterns": patterns,
                "summary": {
                    "total_patterns": len(patterns),
                    "by_severity": self._count_by_severity(patterns),
                    "by_type": self._count_by_type(patterns)
                },
                "processing_time_ms": random.randint(100, 2000)
            }
            responses.append(response)
        
        return responses
    
    def _generate_performance_benchmarks(self) -> Dict[str, Any]:
        """Generate performance benchmarks."""
        return {
            "max_response_time_ms": 50,
            "max_memory_usage_mb": 256,
            "min_throughput_rps": 100,
            "max_cpu_usage_percent": 80,
            "max_error_rate_percent": 1.0
        }
    
    def _generate_validation_rules(self) -> Dict[str, Any]:
        """Generate validation rules for testing."""
        return {
            "required_fields": [
                "request_id", "repository_id", "patterns", "summary"
            ],
            "pattern_required_fields": [
                "pattern_id", "pattern_name", "pattern_type", "severity",
                "confidence", "location", "description"
            ],
            "confidence_range": [0.0, 1.0],
            "severity_levels": [s.value for s in SeverityLevel],
            "pattern_types": [p.value for p in PatternType]
        }
    
    def _count_by_severity(self, patterns: List[Dict[str, Any]]) -> Dict[str, int]:
        """Count patterns by severity."""
        counts = {}
        for pattern in patterns:
            severity = pattern["severity"]
            counts[severity] = counts.get(severity, 0) + 1
        return counts
    
    def _count_by_type(self, patterns: List[Dict[str, Any]]) -> Dict[str, int]:
        """Count patterns by type."""
        counts = {}
        for pattern in patterns:
            pattern_type = pattern["pattern_type"]
            counts[pattern_type] = counts.get(pattern_type, 0) + 1
        return counts