"""
Custom Assertions and Test Utilities

Custom assertion functions for pattern-mining service tests.
"""

import pytest
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import json
import re

from pattern_mining.models.patterns import DetectedPattern, PatternType, SeverityLevel, DetectionType
from pattern_mining.models.api import PatternDetectionResponse, PatternSummary
from pattern_mining.models.ml import ModelMetrics, PredictionResponse


def assert_pattern_detected(
    patterns: List[DetectedPattern],
    pattern_name: str,
    pattern_type: PatternType,
    min_confidence: float = 0.0,
    max_confidence: float = 1.0,
    severity: Optional[SeverityLevel] = None,
    detection_method: Optional[DetectionType] = None
) -> DetectedPattern:
    """
    Assert that a specific pattern is detected in the results.
    
    Args:
        patterns: List of detected patterns
        pattern_name: Expected pattern name
        pattern_type: Expected pattern type
        min_confidence: Minimum confidence threshold
        max_confidence: Maximum confidence threshold
        severity: Expected severity level (optional)
        detection_method: Expected detection method (optional)
    
    Returns:
        The matched pattern
    
    Raises:
        AssertionError: If pattern is not found or doesn't match criteria
    """
    matching_patterns = [
        p for p in patterns
        if p.pattern_name == pattern_name and p.pattern_type == pattern_type
    ]
    
    assert len(matching_patterns) > 0, (
        f"Pattern '{pattern_name}' of type '{pattern_type}' not found in results. "
        f"Available patterns: {[(p.pattern_name, p.pattern_type) for p in patterns]}"
    )
    
    pattern = matching_patterns[0]
    
    # Check confidence range
    assert min_confidence <= pattern.confidence <= max_confidence, (
        f"Pattern confidence {pattern.confidence} is not within range "
        f"[{min_confidence}, {max_confidence}]"
    )
    
    # Check severity if specified
    if severity is not None:
        assert pattern.severity == severity, (
            f"Pattern severity {pattern.severity} does not match expected {severity}"
        )
    
    # Check detection method if specified
    if detection_method is not None:
        assert pattern.detection_method == detection_method, (
            f"Pattern detection method {pattern.detection_method} does not match expected {detection_method}"
        )
    
    return pattern


def assert_pattern_not_detected(
    patterns: List[DetectedPattern],
    pattern_name: str,
    pattern_type: PatternType
):
    """
    Assert that a specific pattern is NOT detected in the results.
    
    Args:
        patterns: List of detected patterns
        pattern_name: Pattern name that should not be found
        pattern_type: Pattern type that should not be found
    
    Raises:
        AssertionError: If pattern is found
    """
    matching_patterns = [
        p for p in patterns
        if p.pattern_name == pattern_name and p.pattern_type == pattern_type
    ]
    
    assert len(matching_patterns) == 0, (
        f"Pattern '{pattern_name}' of type '{pattern_type}' was unexpectedly found in results"
    )


def assert_response_performance(
    response_data: Dict[str, Any],
    max_processing_time_ms: int = 1000,
    min_patterns_detected: int = 0,
    max_patterns_detected: int = 1000
):
    """
    Assert that response meets performance criteria.
    
    Args:
        response_data: API response data
        max_processing_time_ms: Maximum allowed processing time
        min_patterns_detected: Minimum expected patterns
        max_patterns_detected: Maximum expected patterns
    
    Raises:
        AssertionError: If performance criteria are not met
    """
    # Check processing time
    assert "processing_time_ms" in response_data, "Response missing processing_time_ms"
    processing_time = response_data["processing_time_ms"]
    assert processing_time <= max_processing_time_ms, (
        f"Processing time {processing_time}ms exceeds maximum {max_processing_time_ms}ms"
    )
    
    # Check pattern count
    assert "patterns" in response_data, "Response missing patterns"
    pattern_count = len(response_data["patterns"])
    assert min_patterns_detected <= pattern_count <= max_patterns_detected, (
        f"Pattern count {pattern_count} is not within range "
        f"[{min_patterns_detected}, {max_patterns_detected}]"
    )
    
    # Check response structure
    assert "summary" in response_data, "Response missing summary"
    assert "request_id" in response_data, "Response missing request_id"


def assert_api_contract(
    response_data: Dict[str, Any],
    expected_fields: List[str],
    optional_fields: List[str] = None
):
    """
    Assert that API response follows the expected contract.
    
    Args:
        response_data: API response data
        expected_fields: List of required fields
        optional_fields: List of optional fields
    
    Raises:
        AssertionError: If contract is not followed
    """
    # Check required fields
    for field in expected_fields:
        assert field in response_data, f"Required field '{field}' missing from response"
    
    # Check field types based on common patterns
    type_expectations = {
        "request_id": str,
        "repository_id": str,
        "patterns": list,
        "summary": dict,
        "processing_time_ms": (int, float),
        "model_versions": dict,
        "timestamp": str,
        "status": str,
        "progress": (int, float),
        "total": int,
        "page": int,
        "page_size": int,
        "has_next": bool,
        "has_previous": bool
    }
    
    for field, expected_type in type_expectations.items():
        if field in response_data:
            actual_value = response_data[field]
            if isinstance(expected_type, tuple):
                assert isinstance(actual_value, expected_type), (
                    f"Field '{field}' should be of type {expected_type}, got {type(actual_value)}"
                )
            else:
                assert isinstance(actual_value, expected_type), (
                    f"Field '{field}' should be of type {expected_type}, got {type(actual_value)}"
                )


def assert_ml_accuracy(
    metrics: ModelMetrics,
    min_accuracy: float = 0.8,
    min_precision: float = 0.8,
    min_recall: float = 0.8,
    min_f1_score: float = 0.8
):
    """
    Assert that ML model metrics meet accuracy requirements.
    
    Args:
        metrics: Model metrics object
        min_accuracy: Minimum required accuracy
        min_precision: Minimum required precision
        min_recall: Minimum required recall
        min_f1_score: Minimum required F1 score
    
    Raises:
        AssertionError: If metrics don't meet requirements
    """
    assert metrics.accuracy >= min_accuracy, (
        f"Model accuracy {metrics.accuracy} below minimum {min_accuracy}"
    )
    
    assert metrics.precision >= min_precision, (
        f"Model precision {metrics.precision} below minimum {min_precision}"
    )
    
    assert metrics.recall >= min_recall, (
        f"Model recall {metrics.recall} below minimum {min_recall}"
    )
    
    assert metrics.f1_score >= min_f1_score, (
        f"Model F1 score {metrics.f1_score} below minimum {min_f1_score}"
    )


def assert_prediction_quality(
    prediction: PredictionResponse,
    expected_label: str,
    min_confidence: float = 0.7
):
    """
    Assert that prediction meets quality requirements.
    
    Args:
        prediction: Prediction response object
        expected_label: Expected prediction label
        min_confidence: Minimum required confidence
    
    Raises:
        AssertionError: If prediction doesn't meet requirements
    """
    assert prediction.predictions, "Prediction response contains no predictions"
    
    top_prediction = prediction.predictions[0]
    assert top_prediction["label"] == expected_label, (
        f"Top prediction label '{top_prediction['label']}' does not match expected '{expected_label}'"
    )
    
    assert top_prediction["confidence"] >= min_confidence, (
        f"Prediction confidence {top_prediction['confidence']} below minimum {min_confidence}"
    )


def assert_pattern_summary_valid(
    summary: PatternSummary,
    expected_total: Optional[int] = None,
    min_quality_score: float = 0.0,
    max_quality_score: float = 100.0
):
    """
    Assert that pattern summary is valid and meets expectations.
    
    Args:
        summary: Pattern summary object
        expected_total: Expected total pattern count (optional)
        min_quality_score: Minimum quality score
        max_quality_score: Maximum quality score
    
    Raises:
        AssertionError: If summary is invalid
    """
    # Check total patterns
    assert summary.total_patterns >= 0, "Total patterns cannot be negative"
    
    if expected_total is not None:
        assert summary.total_patterns == expected_total, (
            f"Total patterns {summary.total_patterns} does not match expected {expected_total}"
        )
    
    # Check unique patterns
    assert summary.unique_patterns >= 0, "Unique patterns cannot be negative"
    assert summary.unique_patterns <= summary.total_patterns, (
        "Unique patterns cannot exceed total patterns"
    )
    
    # Check quality scores
    assert min_quality_score <= summary.quality_score <= max_quality_score, (
        f"Quality score {summary.quality_score} is not within range "
        f"[{min_quality_score}, {max_quality_score}]"
    )
    
    assert min_quality_score <= summary.security_score <= max_quality_score, (
        f"Security score {summary.security_score} is not within range "
        f"[{min_quality_score}, {max_quality_score}]"
    )
    
    assert min_quality_score <= summary.performance_score <= max_quality_score, (
        f"Performance score {summary.performance_score} is not within range "
        f"[{min_quality_score}, {max_quality_score}]"
    )
    
    assert min_quality_score <= summary.maintainability_score <= max_quality_score, (
        f"Maintainability score {summary.maintainability_score} is not within range "
        f"[{min_quality_score}, {max_quality_score}]"
    )
    
    # Check confidence values
    assert 0.0 <= summary.average_confidence <= 1.0, (
        f"Average confidence {summary.average_confidence} is not within range [0.0, 1.0]"
    )
    
    # Check count consistency
    total_by_type = sum(summary.by_type.values())
    assert total_by_type == summary.total_patterns, (
        f"Total patterns by type {total_by_type} does not match total patterns {summary.total_patterns}"
    )
    
    total_by_severity = sum(summary.by_severity.values())
    assert total_by_severity == summary.total_patterns, (
        f"Total patterns by severity {total_by_severity} does not match total patterns {summary.total_patterns}"
    )


def assert_database_integrity(
    records: List[Dict[str, Any]],
    required_fields: List[str],
    unique_fields: List[str] = None
):
    """
    Assert database records maintain integrity constraints.
    
    Args:
        records: List of database records
        required_fields: Fields that must be present and non-null
        unique_fields: Fields that must be unique across records
    
    Raises:
        AssertionError: If integrity constraints are violated
    """
    if not records:
        return
    
    # Check required fields
    for record in records:
        for field in required_fields:
            assert field in record, f"Required field '{field}' missing from record"
            assert record[field] is not None, f"Required field '{field}' is null in record"
    
    # Check unique fields
    if unique_fields:
        for field in unique_fields:
            values = [record[field] for record in records if field in record]
            assert len(values) == len(set(values)), (
                f"Field '{field}' contains duplicate values: {values}"
            )


def assert_time_range_valid(
    start_time: Union[str, datetime],
    end_time: Union[str, datetime],
    max_duration: Optional[timedelta] = None
):
    """
    Assert that time range is valid.
    
    Args:
        start_time: Start time (string or datetime)
        end_time: End time (string or datetime)
        max_duration: Maximum allowed duration (optional)
    
    Raises:
        AssertionError: If time range is invalid
    """
    # Convert strings to datetime if needed
    if isinstance(start_time, str):
        start_time = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
    if isinstance(end_time, str):
        end_time = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
    
    # Check that end time is after start time
    assert end_time >= start_time, (
        f"End time {end_time} is before start time {start_time}"
    )
    
    # Check maximum duration if specified
    if max_duration is not None:
        duration = end_time - start_time
        assert duration <= max_duration, (
            f"Duration {duration} exceeds maximum {max_duration}"
        )


def assert_json_structure(
    data: Dict[str, Any],
    schema: Dict[str, Any],
    strict: bool = False
):
    """
    Assert that JSON data follows expected structure.
    
    Args:
        data: JSON data to validate
        schema: Expected schema structure
        strict: Whether to enforce strict validation
    
    Raises:
        AssertionError: If structure doesn't match schema
    """
    def validate_field(field_name: str, field_value: Any, field_schema: Any):
        if isinstance(field_schema, dict):
            if "type" in field_schema:
                expected_type = field_schema["type"]
                if expected_type == "string":
                    assert isinstance(field_value, str), (
                        f"Field '{field_name}' should be string, got {type(field_value)}"
                    )
                elif expected_type == "number":
                    assert isinstance(field_value, (int, float)), (
                        f"Field '{field_name}' should be number, got {type(field_value)}"
                    )
                elif expected_type == "boolean":
                    assert isinstance(field_value, bool), (
                        f"Field '{field_name}' should be boolean, got {type(field_value)}"
                    )
                elif expected_type == "array":
                    assert isinstance(field_value, list), (
                        f"Field '{field_name}' should be array, got {type(field_value)}"
                    )
                elif expected_type == "object":
                    assert isinstance(field_value, dict), (
                        f"Field '{field_name}' should be object, got {type(field_value)}"
                    )
            
            if "required" in field_schema and field_schema["required"]:
                assert field_value is not None, f"Required field '{field_name}' is null"
            
            if "min" in field_schema and isinstance(field_value, (int, float)):
                assert field_value >= field_schema["min"], (
                    f"Field '{field_name}' value {field_value} is below minimum {field_schema['min']}"
                )
            
            if "max" in field_schema and isinstance(field_value, (int, float)):
                assert field_value <= field_schema["max"], (
                    f"Field '{field_name}' value {field_value} is above maximum {field_schema['max']}"
                )
            
            if "pattern" in field_schema and isinstance(field_value, str):
                pattern = field_schema["pattern"]
                assert re.match(pattern, field_value), (
                    f"Field '{field_name}' value '{field_value}' does not match pattern '{pattern}'"
                )
    
    # Validate all schema fields
    for field_name, field_schema in schema.items():
        if field_name in data:
            validate_field(field_name, data[field_name], field_schema)
        elif isinstance(field_schema, dict) and field_schema.get("required", False):
            assert False, f"Required field '{field_name}' missing from data"
    
    # In strict mode, ensure no extra fields
    if strict:
        extra_fields = set(data.keys()) - set(schema.keys())
        assert not extra_fields, f"Extra fields found in data: {extra_fields}"


def assert_error_response(
    response: Dict[str, Any],
    expected_status_code: int,
    expected_error_type: str,
    expected_message_contains: str = None
):
    """
    Assert that error response follows expected format.
    
    Args:
        response: Error response data
        expected_status_code: Expected HTTP status code
        expected_error_type: Expected error type
        expected_message_contains: Substring that should be in error message
    
    Raises:
        AssertionError: If error response doesn't match expectations
    """
    # Check status code
    assert "status_code" in response or "error" in response, (
        "Error response missing status_code or error field"
    )
    
    if "status_code" in response:
        assert response["status_code"] == expected_status_code, (
            f"Status code {response['status_code']} does not match expected {expected_status_code}"
        )
    
    # Check error type
    assert "error" in response, "Error response missing error field"
    if expected_error_type:
        error_field = response["error"]
        assert expected_error_type.lower() in error_field.lower(), (
            f"Error type '{expected_error_type}' not found in error message: {error_field}"
        )
    
    # Check message content
    if expected_message_contains:
        message_fields = ["message", "detail", "details"]
        message_found = False
        
        for field in message_fields:
            if field in response:
                message = str(response[field])
                if expected_message_contains.lower() in message.lower():
                    message_found = True
                    break
        
        assert message_found, (
            f"Expected message containing '{expected_message_contains}' not found in error response"
        )


def assert_pagination_response(
    response: Dict[str, Any],
    expected_page: int,
    expected_page_size: int,
    expected_total: Optional[int] = None
):
    """
    Assert that pagination response is valid.
    
    Args:
        response: Paginated response data
        expected_page: Expected page number
        expected_page_size: Expected page size
        expected_total: Expected total count (optional)
    
    Raises:
        AssertionError: If pagination response is invalid
    """
    # Check required pagination fields
    required_fields = ["page", "page_size", "total", "has_next", "has_previous"]
    for field in required_fields:
        assert field in response, f"Pagination response missing field: {field}"
    
    # Check page number
    assert response["page"] == expected_page, (
        f"Page number {response['page']} does not match expected {expected_page}"
    )
    
    # Check page size
    assert response["page_size"] == expected_page_size, (
        f"Page size {response['page_size']} does not match expected {expected_page_size}"
    )
    
    # Check total if specified
    if expected_total is not None:
        assert response["total"] == expected_total, (
            f"Total count {response['total']} does not match expected {expected_total}"
        )
    
    # Check has_previous logic
    if expected_page == 1:
        assert response["has_previous"] is False, (
            "First page should not have previous page"
        )
    else:
        assert response["has_previous"] is True, (
            "Non-first page should have previous page"
        )
    
    # Check has_next logic
    total = response["total"]
    max_page = (total + expected_page_size - 1) // expected_page_size
    
    if expected_page >= max_page:
        assert response["has_next"] is False, (
            "Last page should not have next page"
        )
    else:
        assert response["has_next"] is True, (
            "Non-last page should have next page"
        )


def assert_websocket_message(
    message: Dict[str, Any],
    expected_type: str,
    expected_fields: List[str] = None
):
    """
    Assert that WebSocket message follows expected format.
    
    Args:
        message: WebSocket message data
        expected_type: Expected message type
        expected_fields: Expected fields in message
    
    Raises:
        AssertionError: If message doesn't match expectations
    """
    # Check message type
    assert "type" in message, "WebSocket message missing type field"
    assert message["type"] == expected_type, (
        f"Message type '{message['type']}' does not match expected '{expected_type}'"
    )
    
    # Check expected fields
    if expected_fields:
        for field in expected_fields:
            assert field in message, (
                f"WebSocket message missing expected field: {field}"
            )
    
    # Check timestamp if present
    if "timestamp" in message:
        timestamp = message["timestamp"]
        if isinstance(timestamp, str):
            try:
                datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            except ValueError:
                assert False, f"Invalid timestamp format: {timestamp}"


def assert_batch_processing_status(
    status: Dict[str, Any],
    expected_status: str,
    expected_progress_range: tuple = None
):
    """
    Assert that batch processing status is valid.
    
    Args:
        status: Batch processing status data
        expected_status: Expected processing status
        expected_progress_range: Expected progress range (min, max)
    
    Raises:
        AssertionError: If status is invalid
    """
    # Check required fields
    required_fields = ["job_id", "status", "progress"]
    for field in required_fields:
        assert field in status, f"Batch status missing field: {field}"
    
    # Check status
    assert status["status"] == expected_status, (
        f"Status '{status['status']}' does not match expected '{expected_status}'"
    )
    
    # Check progress range
    progress = status["progress"]
    assert 0.0 <= progress <= 100.0, (
        f"Progress {progress} is not within range [0.0, 100.0]"
    )
    
    if expected_progress_range:
        min_progress, max_progress = expected_progress_range
        assert min_progress <= progress <= max_progress, (
            f"Progress {progress} is not within expected range [{min_progress}, {max_progress}]"
        )
    
    # Check status-specific fields
    if expected_status == "processing":
        assert "current_stage" in status or "files_processed" in status, (
            "Processing status should include progress indicators"
        )
    
    if expected_status == "completed":
        assert progress == 100.0, "Completed status should have 100% progress"
        assert "completed_at" in status, "Completed status should include completion time"
    
    if expected_status == "failed":
        assert "error_message" in status, "Failed status should include error message"


# Custom pytest fixtures for assertions
@pytest.fixture
def assert_pattern_detected():
    """Fixture for pattern detection assertion."""
    return assert_pattern_detected


@pytest.fixture
def assert_response_performance():
    """Fixture for response performance assertion."""
    return assert_response_performance


@pytest.fixture
def assert_api_contract():
    """Fixture for API contract assertion."""
    return assert_api_contract


@pytest.fixture
def assert_ml_accuracy():
    """Fixture for ML accuracy assertion."""
    return assert_ml_accuracy