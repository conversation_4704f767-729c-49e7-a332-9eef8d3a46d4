"""
Test Utilities Package

Common utilities and helpers for the pattern-mining service test suite.
"""

from .fixtures import *
from .factories import *
from .mocks import *
from .generators import *
from .database import *
from .performance import *
from .assertions import *

__all__ = [
    # Fixtures
    "TestDataFixture",
    "DatabaseFixture",
    "MLModelFixture",
    "APIFixture",
    
    # Factories
    "PatternFactory",
    "RepositoryFactory",
    "AnalysisFactory",
    "RequestFactory",
    
    # Mock utilities
    "MockBuilder",
    "AsyncMockBuilder",
    "MockResponse",
    "MockWebSocket",
    
    # Data generators
    "CodeGenerator",
    "PatternGenerator",
    "RepositoryGenerator",
    "TestDataGenerator",
    
    # Database utilities
    "DatabaseTestManager",
    "TransactionManager",
    "DataSeeder",
    
    # Performance utilities
    "PerformanceBenchmark",
    "LatencyMeasurer",
    "MemoryProfiler",
    "ThroughputTester",
    
    # Custom assertions
    "assert_pattern_detected",
    "assert_response_performance",
    "assert_api_contract",
    "assert_ml_accuracy",
]