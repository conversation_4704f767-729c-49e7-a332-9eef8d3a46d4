"""
Tests for distributed ML pipeline functionality.
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock, MagicMock
import torch
import numpy as np
import ray
from ray.train import TrainingResult
from ray.tune import ResultGrid

from src.pattern_mining.distributed.ml_pipeline import (
    DistributedMLPipeline,
    DistributedTrainingConfig,
    HyperparameterConfig,
    ServingConfig,
    PipelineStatus,
    create_training_config,
    create_hyperparameter_config,
    create_serving_config
)


class TestDistributedMLPipeline:
    """Test suite for distributed ML pipeline."""
    
    @pytest.fixture
    def pipeline(self):
        """Create test ML pipeline."""
        return DistributedMLPipeline()
    
    @pytest.fixture
    def training_config(self):
        """Create test training configuration."""
        return DistributedTrainingConfig(
            model_name="test_model",
            dataset_path="/test/data.parquet",
            output_path="/test/output",
            batch_size=32,
            num_epochs=5,
            num_workers=2
        )
    
    @pytest.fixture
    def hyperparameter_config(self):
        """Create test hyperparameter configuration."""
        return HyperparameterConfig(
            search_space={"learning_rate": [0.001, 0.01, 0.1]},
            num_samples=3,
            max_concurrent_trials=2
        )
    
    @pytest.fixture
    def serving_config(self):
        """Create test serving configuration."""
        return ServingConfig(
            model_name="test_model",
            model_path="/test/model.pt",
            num_replicas=2
        )
    
    @pytest.fixture
    def mock_dataset(self):
        """Create mock Ray dataset."""
        dataset = Mock()
        dataset.random_shuffle.return_value = dataset
        dataset.train_test_split.return_value = (dataset, dataset)
        dataset.iter_batches.return_value = iter([{"input": torch.randn(32, 10), "label": torch.randint(0, 2, (32,))}])
        return dataset
    
    @pytest.fixture
    def mock_model_class(self):
        """Create mock model class."""
        class MockModel(torch.nn.Module):
            def __init__(self):
                super().__init__()
                self.linear = torch.nn.Linear(10, 2)
            
            def forward(self, x):
                return self.linear(x)
        
        return MockModel
    
    @pytest.mark.asyncio
    async def test_train_model_distributed(self, pipeline, training_config, mock_dataset, mock_model_class):
        """Test distributed model training."""
        # Mock Ray Data operations
        with patch('ray.data.read_parquet', return_value=mock_dataset):
            with patch.object(pipeline, '_create_trainer') as mock_create_trainer:
                
                # Mock trainer
                mock_trainer_class = Mock()
                mock_trainer_instance = Mock()
                mock_trainer_class.return_value = mock_trainer_instance
                mock_create_trainer.return_value = mock_trainer_class
                
                # Mock training result
                mock_result = Mock(spec=TrainingResult)
                mock_result.checkpoint.path = "/test/checkpoint"
                mock_result.metrics = {"accuracy": 0.85}
                mock_trainer_instance.fit.return_value = mock_result
                
                # Mock other methods
                with patch.object(pipeline, '_should_use_wandb', return_value=False), \
                     patch.object(pipeline, '_update_training_metrics'):
                    
                    result = await pipeline.train_model_distributed(
                        training_config, mock_model_class, mock_dataset
                    )
                    
                    assert result == mock_result
                    assert pipeline.status == PipelineStatus.IDLE
                    assert training_config.model_name in pipeline.model_registry
    
    @pytest.mark.asyncio
    async def test_train_model_distributed_failure(self, pipeline, training_config, mock_model_class):
        """Test distributed training failure."""
        with patch('ray.data.read_parquet', side_effect=Exception("Data loading failed")):
            
            with pytest.raises(Exception, match="Data loading failed"):
                await pipeline.train_model_distributed(training_config, mock_model_class)
            
            assert pipeline.status == PipelineStatus.FAILED
    
    @pytest.mark.asyncio
    async def test_tune_hyperparameters(self, pipeline, hyperparameter_config, training_config, mock_model_class):
        """Test hyperparameter tuning."""
        with patch('ray.tune.Tuner') as mock_tuner_class:
            
            # Mock tuner
            mock_tuner = Mock()
            mock_tuner_class.return_value = mock_tuner
            
            # Mock result grid
            mock_result_grid = Mock(spec=ResultGrid)
            mock_best_result = Mock()
            mock_best_result.metrics = {"accuracy": 0.90}
            mock_best_result.config = {"learning_rate": 0.01}
            mock_result_grid.get_best_result.return_value = mock_best_result
            mock_tuner.fit.return_value = mock_result_grid
            
            with patch.object(pipeline, '_get_search_algorithm'), \
                 patch.object(pipeline, '_get_scheduler'), \
                 patch.object(pipeline, '_get_trainable_function'), \
                 patch.object(pipeline, '_update_tuning_metrics'):
                
                result = await pipeline.tune_hyperparameters(
                    hyperparameter_config, training_config, mock_model_class
                )
                
                assert result == mock_result_grid
                assert pipeline.status == PipelineStatus.IDLE
    
    @pytest.mark.asyncio
    async def test_serve_model(self, pipeline, serving_config):
        """Test model serving deployment."""
        with patch('ray.serve.deployment') as mock_deployment:
            
            # Mock deployment
            mock_deployment_instance = Mock()
            mock_handle = Mock()
            mock_deployment_instance.deploy.return_value = mock_handle
            mock_deployment.return_value = lambda func: mock_deployment_instance
            
            with patch.object(pipeline, '_create_deployment_function'), \
                 patch.object(pipeline, '_wait_for_deployment_ready', return_value=True):
                
                result = await pipeline.serve_model(serving_config)
                
                assert result == mock_handle
                assert serving_config.model_name in pipeline.deployed_models
                assert pipeline.status == PipelineStatus.IDLE
    
    @pytest.mark.asyncio
    async def test_batch_inference(self, pipeline, mock_dataset):
        """Test batch inference."""
        model_name = "test_model"
        
        # Mock deployed model
        mock_handle = Mock()
        mock_handle.predict.remote.return_value = {"prediction": 1, "confidence": 0.9}
        pipeline.deployed_models[model_name] = mock_handle
        
        with patch('ray.get', return_value={"prediction": 1, "confidence": 0.9}):
            
            # Mock dataset operations
            mock_result_dataset = Mock()
            mock_dataset.map_batches.return_value = mock_result_dataset
            
            result = await pipeline.batch_inference(model_name, mock_dataset)
            
            assert result == mock_result_dataset
            mock_dataset.map_batches.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_batch_inference_no_model(self, pipeline, mock_dataset):
        """Test batch inference with no deployed model."""
        model_name = "nonexistent_model"
        
        with pytest.raises(ValueError, match="Model nonexistent_model not deployed"):
            await pipeline.batch_inference(model_name, mock_dataset)
    
    @pytest.mark.asyncio
    async def test_evaluate_model(self, pipeline, mock_dataset):
        """Test model evaluation."""
        model_name = "test_model"
        
        # Mock deployed model
        mock_handle = Mock()
        pipeline.deployed_models[model_name] = mock_handle
        
        # Mock batch inference results
        mock_results = [
            {"label": 0, "prediction": 0},
            {"label": 1, "prediction": 1},
            {"label": 0, "prediction": 1},
            {"label": 1, "prediction": 1}
        ]
        
        with patch.object(pipeline, 'batch_inference') as mock_batch_inference:
            mock_result_dataset = Mock()
            mock_result_dataset.take_all.return_value = mock_results
            mock_batch_inference.return_value = mock_result_dataset
            
            with patch.object(pipeline, '_store_evaluation_metrics'):
                
                metrics = await pipeline.evaluate_model(model_name, mock_dataset)
                
                assert "accuracy" in metrics
                assert "precision" in metrics
                assert "recall" in metrics
                assert "f1" in metrics
                assert metrics["accuracy"] == 0.75  # 3/4 correct predictions
    
    @pytest.mark.asyncio
    async def test_get_pipeline_status(self, pipeline):
        """Test getting pipeline status."""
        # Set up some state
        pipeline.status = PipelineStatus.TRAINING
        pipeline.deployed_models["model1"] = Mock()
        pipeline.model_registry["model1"] = "/path/to/model"
        pipeline.metrics_history = [{"type": "training", "accuracy": 0.85}]
        
        status = await pipeline.get_pipeline_status()
        
        assert status["status"] == PipelineStatus.TRAINING.value
        assert "model1" in status["deployed_models"]
        assert status["model_registry"]["model1"] == "/path/to/model"
        assert len(status["metrics_history"]) == 1
    
    @pytest.mark.asyncio
    async def test_stop_training(self, pipeline):
        """Test stopping training."""
        pipeline.current_training_job = Mock()
        
        result = await pipeline.stop_training()
        
        assert result is True
        assert pipeline.current_training_job is None
        assert pipeline.status == PipelineStatus.IDLE
    
    @pytest.mark.asyncio
    async def test_undeploy_model(self, pipeline):
        """Test model undeployment."""
        model_name = "test_model"
        mock_handle = Mock()
        pipeline.deployed_models[model_name] = mock_handle
        
        result = await pipeline.undeploy_model(model_name)
        
        assert result is True
        assert model_name not in pipeline.deployed_models
    
    @pytest.mark.asyncio
    async def test_load_dataset_parquet(self, pipeline):
        """Test loading parquet dataset."""
        dataset_path = "/test/data.parquet"
        
        with patch('ray.data.read_parquet') as mock_read_parquet:
            mock_dataset = Mock()
            mock_read_parquet.return_value = mock_dataset
            
            result = await pipeline._load_dataset(dataset_path)
            
            assert result == mock_dataset
            mock_read_parquet.assert_called_once_with(dataset_path)
    
    @pytest.mark.asyncio
    async def test_load_dataset_csv(self, pipeline):
        """Test loading CSV dataset."""
        dataset_path = "/test/data.csv"
        
        with patch('ray.data.read_csv') as mock_read_csv:
            mock_dataset = Mock()
            mock_read_csv.return_value = mock_dataset
            
            result = await pipeline._load_dataset(dataset_path)
            
            assert result == mock_dataset
            mock_read_csv.assert_called_once_with(dataset_path)
    
    @pytest.mark.asyncio
    async def test_load_dataset_unsupported_format(self, pipeline):
        """Test loading unsupported dataset format."""
        dataset_path = "/test/data.txt"
        
        with pytest.raises(ValueError, match="Unsupported dataset format"):
            await pipeline._load_dataset(dataset_path)
    
    @pytest.mark.asyncio
    async def test_split_dataset(self, pipeline, mock_dataset):
        """Test dataset splitting."""
        validation_split = 0.2
        
        train_dataset = Mock()
        val_dataset = Mock()
        mock_dataset.train_test_split.return_value = (train_dataset, val_dataset)
        
        result_train, result_val = await pipeline._split_dataset(mock_dataset, validation_split)
        
        assert result_train == train_dataset
        assert result_val == val_dataset
        mock_dataset.random_shuffle.assert_called_once()
        mock_dataset.train_test_split.assert_called_once_with(test_size=validation_split)
    
    @pytest.mark.asyncio
    async def test_create_trainer_torch(self, pipeline, training_config):
        """Test creating PyTorch trainer."""
        from ray.train.torch import TorchTrainer
        
        class MockTorchModel(torch.nn.Module):
            def __init__(self):
                super().__init__()
                self.linear = torch.nn.Linear(10, 2)
        
        trainer_class = await pipeline._create_trainer(training_config, MockTorchModel)
        
        assert trainer_class == TorchTrainer
    
    @pytest.mark.asyncio
    async def test_create_trainer_sklearn(self, pipeline, training_config):
        """Test creating sklearn trainer."""
        from ray.train.sklearn import SklearnTrainer
        
        class MockSklearnModel:
            def fit(self, X, y):
                pass
        
        trainer_class = await pipeline._create_trainer(training_config, MockSklearnModel)
        
        assert trainer_class == SklearnTrainer
    
    @pytest.mark.asyncio
    async def test_create_trainer_unsupported(self, pipeline, training_config):
        """Test creating trainer for unsupported model type."""
        class UnsupportedModel:
            pass
        
        with pytest.raises(ValueError, match="Unsupported model type"):
            await pipeline._create_trainer(training_config, UnsupportedModel)
    
    def test_get_search_algorithm(self, pipeline):
        """Test getting search algorithm."""
        config = HyperparameterConfig(
            search_space={},
            search_algorithm="hyperopt"
        )
        
        algorithm = pipeline._get_search_algorithm(config)
        
        # Should return HyperOptSearch or None
        assert algorithm is not None or algorithm is None
    
    def test_get_scheduler(self, pipeline):
        """Test getting scheduler."""
        config = HyperparameterConfig(
            search_space={},
            scheduler="asha"
        )
        
        scheduler = pipeline._get_scheduler(config)
        
        # Should return ASHAScheduler or None
        assert scheduler is not None or scheduler is None
    
    def test_should_use_wandb(self, pipeline):
        """Test Weights & Biases usage check."""
        with patch.dict('os.environ', {'WANDB_API_KEY': 'test_key'}):
            assert pipeline._should_use_wandb() is True
        
        with patch.dict('os.environ', {}, clear=True):
            assert pipeline._should_use_wandb() is False
    
    @pytest.mark.asyncio
    async def test_update_training_metrics(self, pipeline):
        """Test updating training metrics."""
        mock_result = Mock(spec=TrainingResult)
        mock_result.metrics = {"accuracy": 0.85}
        mock_result.checkpoint.path = "/test/checkpoint"
        
        await pipeline._update_training_metrics(mock_result)
        
        assert len(pipeline.metrics_history) == 1
        assert pipeline.metrics_history[0]["type"] == "training"
        assert pipeline.metrics_history[0]["metrics"] == {"accuracy": 0.85}
    
    @pytest.mark.asyncio
    async def test_update_tuning_metrics(self, pipeline):
        """Test updating tuning metrics."""
        mock_result_grid = Mock(spec=ResultGrid)
        mock_result_grid.__len__ = Mock(return_value=5)
        
        mock_best_result = Mock()
        mock_best_result.metrics = {"accuracy": 0.90}
        mock_best_result.config = {"learning_rate": 0.01}
        
        await pipeline._update_tuning_metrics(mock_result_grid, mock_best_result)
        
        assert len(pipeline.metrics_history) == 1
        assert pipeline.metrics_history[0]["type"] == "tuning"
        assert pipeline.metrics_history[0]["num_trials"] == 5
        assert pipeline.metrics_history[0]["best_result"] == {"accuracy": 0.90}
    
    @pytest.mark.asyncio
    async def test_store_evaluation_metrics(self, pipeline):
        """Test storing evaluation metrics."""
        model_name = "test_model"
        metrics = {"accuracy": 0.85, "precision": 0.87}
        
        await pipeline._store_evaluation_metrics(model_name, metrics)
        
        assert len(pipeline.metrics_history) == 1
        assert pipeline.metrics_history[0]["type"] == "evaluation"
        assert pipeline.metrics_history[0]["model_name"] == model_name
        assert pipeline.metrics_history[0]["metrics"] == metrics


class TestMLPipelineConfigurations:
    """Test ML pipeline configuration classes."""
    
    def test_distributed_training_config(self):
        """Test distributed training configuration."""
        config = DistributedTrainingConfig(
            model_name="test_model",
            dataset_path="/test/data.parquet",
            output_path="/test/output"
        )
        
        assert config.model_name == "test_model"
        assert config.dataset_path == "/test/data.parquet"
        assert config.output_path == "/test/output"
        assert config.batch_size == 32
        assert config.num_epochs == 10
        assert config.use_gpu is True
        assert config.data_parallel is True
    
    def test_hyperparameter_config(self):
        """Test hyperparameter configuration."""
        search_space = {"learning_rate": [0.001, 0.01, 0.1]}
        config = HyperparameterConfig(search_space=search_space)
        
        assert config.search_space == search_space
        assert config.num_samples == 20
        assert config.scheduler == "asha"
        assert config.search_algorithm == "hyperopt"
        assert config.metric == "accuracy"
        assert config.mode == "max"
    
    def test_serving_config(self):
        """Test serving configuration."""
        config = ServingConfig(
            model_name="test_model",
            model_path="/test/model.pt"
        )
        
        assert config.model_name == "test_model"
        assert config.model_path == "/test/model.pt"
        assert config.num_replicas == 2
        assert config.max_concurrent_queries == 100
        assert config.batch_size == 32
    
    @pytest.mark.asyncio
    async def test_create_training_config(self):
        """Test creating example training configuration."""
        config = await create_training_config()
        
        assert isinstance(config, DistributedTrainingConfig)
        assert config.model_name == "pattern_classifier"
        assert config.num_workers == 4
        assert config.use_gpu is True
        assert config.mixed_precision is True
    
    @pytest.mark.asyncio
    async def test_create_hyperparameter_config(self):
        """Test creating example hyperparameter configuration."""
        config = await create_hyperparameter_config()
        
        assert isinstance(config, HyperparameterConfig)
        assert "learning_rate" in config.search_space
        assert "batch_size" in config.search_space
        assert config.num_samples == 20
        assert config.metric == "accuracy"
    
    @pytest.mark.asyncio
    async def test_create_serving_config(self):
        """Test creating example serving configuration."""
        config = await create_serving_config()
        
        assert isinstance(config, ServingConfig)
        assert config.model_name == "pattern_classifier"
        assert config.num_replicas == 2
        assert config.max_concurrent_queries == 100


class TestMLPipelineIntegration:
    """Integration tests for ML pipeline."""
    
    @pytest.mark.asyncio
    async def test_full_training_pipeline(self):
        """Test complete training pipeline."""
        pipeline = DistributedMLPipeline()
        
        # Mock all dependencies
        with patch('ray.data.read_parquet') as mock_read_parquet, \
             patch('ray.train.torch.TorchTrainer') as mock_trainer_class:
            
            # Mock dataset
            mock_dataset = Mock()
            mock_dataset.random_shuffle.return_value = mock_dataset
            mock_dataset.train_test_split.return_value = (mock_dataset, mock_dataset)
            mock_read_parquet.return_value = mock_dataset
            
            # Mock trainer
            mock_trainer_instance = Mock()
            mock_trainer_class.return_value = mock_trainer_instance
            
            # Mock training result
            mock_result = Mock(spec=TrainingResult)
            mock_result.checkpoint.path = "/test/checkpoint"
            mock_result.metrics = {"accuracy": 0.85}
            mock_trainer_instance.fit.return_value = mock_result
            
            # Mock model class
            class MockModel(torch.nn.Module):
                def __init__(self):
                    super().__init__()
                    self.linear = torch.nn.Linear(10, 2)
            
            config = DistributedTrainingConfig(
                model_name="integration_test",
                dataset_path="/test/data.parquet",
                output_path="/test/output",
                num_workers=2
            )
            
            with patch.object(pipeline, '_should_use_wandb', return_value=False):
                result = await pipeline.train_model_distributed(config, MockModel)
                
                assert result == mock_result
                assert pipeline.status == PipelineStatus.IDLE
                assert config.model_name in pipeline.model_registry
    
    @pytest.mark.asyncio
    async def test_training_and_serving_pipeline(self):
        """Test training followed by serving."""
        pipeline = DistributedMLPipeline()
        
        # First train a model
        with patch('ray.data.read_parquet') as mock_read_parquet, \
             patch('ray.train.torch.TorchTrainer') as mock_trainer_class:
            
            # Mock dataset and trainer (similar to above)
            mock_dataset = Mock()
            mock_dataset.random_shuffle.return_value = mock_dataset
            mock_dataset.train_test_split.return_value = (mock_dataset, mock_dataset)
            mock_read_parquet.return_value = mock_dataset
            
            mock_trainer_instance = Mock()
            mock_trainer_class.return_value = mock_trainer_instance
            
            mock_result = Mock(spec=TrainingResult)
            mock_result.checkpoint.path = "/test/checkpoint"
            mock_result.metrics = {"accuracy": 0.85}
            mock_trainer_instance.fit.return_value = mock_result
            
            class MockModel(torch.nn.Module):
                def __init__(self):
                    super().__init__()
                    self.linear = torch.nn.Linear(10, 2)
            
            training_config = DistributedTrainingConfig(
                model_name="test_model",
                dataset_path="/test/data.parquet",
                output_path="/test/output"
            )
            
            with patch.object(pipeline, '_should_use_wandb', return_value=False):
                training_result = await pipeline.train_model_distributed(training_config, MockModel)
                
                assert training_result == mock_result
                assert "test_model" in pipeline.model_registry
        
        # Then serve the model
        with patch('ray.serve.deployment') as mock_deployment:
            mock_deployment_instance = Mock()
            mock_handle = Mock()
            mock_deployment_instance.deploy.return_value = mock_handle
            mock_deployment.return_value = lambda func: mock_deployment_instance
            
            serving_config = ServingConfig(
                model_name="test_model",
                model_path="/test/checkpoint"
            )
            
            with patch.object(pipeline, '_wait_for_deployment_ready', return_value=True):
                serving_result = await pipeline.serve_model(serving_config)
                
                assert serving_result == mock_handle
                assert "test_model" in pipeline.deployed_models
    
    @pytest.mark.asyncio
    async def test_error_handling_and_recovery(self):
        """Test error handling and recovery."""
        pipeline = DistributedMLPipeline()
        
        # Test training failure
        with patch('ray.data.read_parquet', side_effect=Exception("Data loading failed")):
            config = DistributedTrainingConfig(
                model_name="test_model",
                dataset_path="/test/data.parquet",
                output_path="/test/output"
            )
            
            class MockModel(torch.nn.Module):
                def __init__(self):
                    super().__init__()
                    self.linear = torch.nn.Linear(10, 2)
            
            with pytest.raises(Exception, match="Data loading failed"):
                await pipeline.train_model_distributed(config, MockModel)
            
            assert pipeline.status == PipelineStatus.FAILED
        
        # Test recovery by checking status
        status = await pipeline.get_pipeline_status()
        assert status["status"] == PipelineStatus.FAILED.value


if __name__ == "__main__":
    pytest.main([__file__, "-v"])