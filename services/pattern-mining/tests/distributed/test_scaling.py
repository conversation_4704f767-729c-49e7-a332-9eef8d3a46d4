"""
Tests for auto-scaling and resource management functionality.
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock
from collections import deque

from src.pattern_mining.distributed.scaling import (
    AutoScalingManager,
    ScalingConfig,
    ScalingMetrics,
    ScalingDecision,
    ScalingDirection,
    LoadBalancer,
    WorkloadPredictor,
    CostOptimizer,
    create_scaling_config
)
from src.pattern_mining.distributed.cluster import RayClusterManager, ClusterStatus
from src.pattern_mining.distributed.task_manager import DistributedTaskManager


class TestAutoScalingManager:
    """Test suite for auto-scaling manager."""
    
    @pytest.fixture
    def scaling_config(self):
        """Create test scaling configuration."""
        return ScalingConfig(
            min_workers=2,
            max_workers=10,
            target_cpu_utilization=70.0,
            scale_up_threshold=80.0,
            scale_down_threshold=30.0
        )
    
    @pytest.fixture
    def mock_cluster_manager(self):
        """Create mock cluster manager."""
        manager = Mock(spec=RayClusterManager)
        manager.get_cluster_status.return_value = asyncio.Future()
        manager.get_cluster_status.return_value.set_result({
            "status": ClusterStatus.RUNNING.value,
            "nodes": {"total": 3, "workers": 2},
            "resources": {"CPU": 8, "memory": 16000000000},
            "metrics": {"cpu_utilization": 50.0, "memory_utilization": 60.0}
        })
        manager.scale_cluster.return_value = asyncio.Future()
        manager.scale_cluster.return_value.set_result(True)
        return manager
    
    @pytest.fixture
    def mock_task_manager(self):
        """Create mock task manager."""
        manager = Mock(spec=DistributedTaskManager)
        manager.get_manager_status.return_value = asyncio.Future()
        manager.get_manager_status.return_value.set_result({
            "tasks": {"total": 100, "completed": 80, "failed": 5},
            "queues": {"task_queue_size": 10}
        })
        return manager
    
    @pytest.fixture
    def mock_gpu_manager(self):
        """Create mock GPU manager."""
        manager = Mock()
        manager.get_gpu_metrics.return_value = asyncio.Future()
        manager.get_gpu_metrics.return_value.set_result({
            "average_utilization": 45.0,
            "total_memory": 8000000000,
            "used_memory": 4000000000
        })
        return manager
    
    @pytest.fixture
    def scaling_manager(self, scaling_config, mock_cluster_manager, mock_task_manager, mock_gpu_manager):
        """Create test scaling manager."""
        return AutoScalingManager(
            scaling_config,
            mock_cluster_manager,
            mock_task_manager,
            mock_gpu_manager
        )
    
    @pytest.fixture
    def sample_metrics(self):
        """Create sample scaling metrics."""
        return ScalingMetrics(
            cpu_utilization=75.0,
            memory_utilization=70.0,
            gpu_utilization=60.0,
            queue_length=50,
            task_completion_rate=0.8,
            error_rate=0.05,
            response_time=1.2,
            throughput=100.0,
            cost_per_hour=5.0
        )
    
    @pytest.mark.asyncio
    async def test_start_autoscaling(self, scaling_manager):
        """Test starting auto-scaling."""
        with patch.object(scaling_manager, '_scale_to_target', return_value=True):
            result = await scaling_manager.start_autoscaling()
            
            assert result is True
            assert scaling_manager.scaling_active is True
            assert scaling_manager.target_workers == scaling_manager.config.min_workers
    
    @pytest.mark.asyncio
    async def test_stop_autoscaling(self, scaling_manager):
        """Test stopping auto-scaling."""
        scaling_manager.scaling_active = True
        
        with patch.object(scaling_manager, '_scale_to_target', return_value=True):
            result = await scaling_manager.stop_autoscaling()
            
            assert result is True
            assert scaling_manager.scaling_active is False
    
    @pytest.mark.asyncio
    async def test_manual_scale(self, scaling_manager):
        """Test manual scaling."""
        target_workers = 5
        
        with patch.object(scaling_manager, '_scale_to_target', return_value=True):
            result = await scaling_manager.manual_scale(target_workers)
            
            assert result is True
            assert scaling_manager.target_workers == target_workers
    
    @pytest.mark.asyncio
    async def test_manual_scale_bounds_checking(self, scaling_manager):
        """Test manual scaling with bounds checking."""
        # Test scaling above maximum
        with patch.object(scaling_manager, '_scale_to_target', return_value=True):
            result = await scaling_manager.manual_scale(15)  # Above max_workers=10
            
            assert result is True
            assert scaling_manager.target_workers == scaling_manager.config.max_workers
        
        # Test scaling below minimum
        with patch.object(scaling_manager, '_scale_to_target', return_value=True):
            result = await scaling_manager.manual_scale(1)  # Below min_workers=2
            
            assert result is True
            assert scaling_manager.target_workers == scaling_manager.config.min_workers
    
    @pytest.mark.asyncio
    async def test_get_scaling_status(self, scaling_manager, sample_metrics):
        """Test getting scaling status."""
        scaling_manager.scaling_active = True
        scaling_manager.current_workers = 5
        scaling_manager.target_workers = 6
        
        # Add some history
        scaling_manager.scaling_history = [
            ScalingDecision(
                direction=ScalingDirection.UP,
                reason="High CPU utilization",
                confidence=0.8,
                metrics=sample_metrics
            )
        ]
        
        with patch.object(scaling_manager, '_collect_metrics', return_value=sample_metrics):
            status = await scaling_manager.get_scaling_status()
            
            assert status["scaling_active"] is True
            assert status["current_workers"] == 5
            assert status["target_workers"] == 6
            assert status["min_workers"] == 2
            assert status["max_workers"] == 10
            assert "current_metrics" in status
            assert "scaling_history" in status
            assert len(status["scaling_history"]) == 1
    
    @pytest.mark.asyncio
    async def test_get_scaling_recommendations(self, scaling_manager, sample_metrics):
        """Test getting scaling recommendations."""
        with patch.object(scaling_manager, '_collect_metrics', return_value=sample_metrics), \
             patch.object(scaling_manager, '_make_scaling_decision') as mock_decision:
            
            mock_decision.return_value = ScalingDecision(
                direction=ScalingDirection.UP,
                reason="High resource utilization",
                confidence=0.85,
                estimated_cost=2.5,
                estimated_completion_time=300.0
            )
            
            recommendations = await scaling_manager.get_scaling_recommendations()
            
            assert "scaling_decision" in recommendations
            assert recommendations["scaling_decision"]["direction"] == ScalingDirection.UP.value
            assert recommendations["scaling_decision"]["confidence"] == 0.85
            assert "cost_optimization" in recommendations
            assert "load_balancing" in recommendations
            assert "metrics" in recommendations
    
    @pytest.mark.asyncio
    async def test_collect_metrics(self, scaling_manager, mock_cluster_manager, mock_task_manager, mock_gpu_manager):
        """Test collecting scaling metrics."""
        metrics = await scaling_manager._collect_metrics()
        
        assert isinstance(metrics, ScalingMetrics)
        assert metrics.cpu_utilization >= 0
        assert metrics.memory_utilization >= 0
        assert metrics.gpu_utilization >= 0
        assert metrics.queue_length >= 0
        assert metrics.timestamp > 0
    
    @pytest.mark.asyncio
    async def test_make_scaling_decision_scale_up(self, scaling_manager, sample_metrics):
        """Test making scale up decision."""
        # Set high utilization to trigger scale up
        high_utilization_metrics = ScalingMetrics(
            cpu_utilization=85.0,  # Above threshold
            memory_utilization=75.0,
            gpu_utilization=70.0,
            queue_length=100,
            task_completion_rate=0.8,
            error_rate=0.02
        )
        
        scaling_manager.current_workers = 5
        scaling_manager.last_scale_up_time = time.time() - 600  # 10 minutes ago
        
        decision = await scaling_manager._make_scaling_decision(high_utilization_metrics)
        
        assert decision.direction == ScalingDirection.UP
        assert decision.confidence > 0
        assert decision.resource_request is not None
        assert decision.resource_request.num_workers > 0
        assert "High resource utilization" in decision.reason
    
    @pytest.mark.asyncio
    async def test_make_scaling_decision_scale_down(self, scaling_manager, sample_metrics):
        """Test making scale down decision."""
        # Set low utilization to trigger scale down
        low_utilization_metrics = ScalingMetrics(
            cpu_utilization=25.0,  # Below threshold
            memory_utilization=20.0,
            gpu_utilization=15.0,
            queue_length=5,
            task_completion_rate=0.9,
            error_rate=0.01
        )
        
        scaling_manager.current_workers = 8
        scaling_manager.last_scale_down_time = time.time() - 700  # 11+ minutes ago
        
        decision = await scaling_manager._make_scaling_decision(low_utilization_metrics)
        
        assert decision.direction == ScalingDirection.DOWN
        assert decision.confidence > 0
        assert decision.resource_request is not None
        assert decision.resource_request.num_workers > 0
        assert "Low resource utilization" in decision.reason
    
    @pytest.mark.asyncio
    async def test_make_scaling_decision_stable(self, scaling_manager, sample_metrics):
        """Test making stable decision."""
        # Set moderate utilization
        moderate_utilization_metrics = ScalingMetrics(
            cpu_utilization=60.0,  # Within target range
            memory_utilization=65.0,
            gpu_utilization=55.0,
            queue_length=25,
            task_completion_rate=0.85,
            error_rate=0.03
        )
        
        scaling_manager.current_workers = 5
        
        decision = await scaling_manager._make_scaling_decision(moderate_utilization_metrics)
        
        assert decision.direction == ScalingDirection.STABLE
        assert decision.confidence > 0
        assert "within target range" in decision.reason
    
    @pytest.mark.asyncio
    async def test_make_scaling_decision_cooldown(self, scaling_manager, sample_metrics):
        """Test scaling decision during cooldown period."""
        # Set high utilization but recent scale up
        high_utilization_metrics = ScalingMetrics(
            cpu_utilization=90.0,
            memory_utilization=85.0,
            gpu_utilization=80.0,
            queue_length=200
        )
        
        scaling_manager.current_workers = 5
        scaling_manager.last_scale_up_time = time.time() - 60  # 1 minute ago (within cooldown)
        
        decision = await scaling_manager._make_scaling_decision(high_utilization_metrics)
        
        assert decision.direction == ScalingDirection.STABLE
        assert "Cooldown period active" in decision.reason
    
    @pytest.mark.asyncio
    async def test_execute_scaling_decision_up(self, scaling_manager, sample_metrics):
        """Test executing scale up decision."""
        from src.pattern_mining.distributed.scaling import ResourceRequest
        
        decision = ScalingDecision(
            direction=ScalingDirection.UP,
            resource_request=ResourceRequest(
                worker_type="standard",
                num_workers=2,
                cpu_per_worker=4,
                memory_per_worker=8000000000
            ),
            reason="High utilization",
            confidence=0.8,
            metrics=sample_metrics
        )
        
        scaling_manager.current_workers = 5
        
        with patch.object(scaling_manager, '_scale_to_target', return_value=True):
            await scaling_manager._execute_scaling_decision(decision)
            
            assert scaling_manager.last_scale_up_time > 0
    
    @pytest.mark.asyncio
    async def test_execute_scaling_decision_down(self, scaling_manager, sample_metrics):
        """Test executing scale down decision."""
        from src.pattern_mining.distributed.scaling import ResourceRequest
        
        decision = ScalingDecision(
            direction=ScalingDirection.DOWN,
            resource_request=ResourceRequest(
                worker_type="standard",
                num_workers=1,
                cpu_per_worker=4,
                memory_per_worker=8000000000
            ),
            reason="Low utilization",
            confidence=0.7,
            metrics=sample_metrics
        )
        
        scaling_manager.current_workers = 8
        
        with patch.object(scaling_manager, '_scale_to_target', return_value=True):
            await scaling_manager._execute_scaling_decision(decision)
            
            assert scaling_manager.last_scale_down_time > 0
    
    @pytest.mark.asyncio
    async def test_scale_to_target(self, scaling_manager, mock_cluster_manager):
        """Test scaling to target workers."""
        target_workers = 6
        
        result = await scaling_manager._scale_to_target(target_workers)
        
        assert result is True
        assert scaling_manager.current_workers == target_workers
        assert scaling_manager.target_workers == target_workers
        mock_cluster_manager.scale_cluster.assert_called_once_with(target_workers)
    
    @pytest.mark.asyncio
    async def test_scale_to_target_bounds(self, scaling_manager, mock_cluster_manager):
        """Test scaling to target with bounds checking."""
        # Test scaling above maximum
        result = await scaling_manager._scale_to_target(15)  # Above max_workers=10
        
        assert result is True
        assert scaling_manager.current_workers == scaling_manager.config.max_workers
        
        # Test scaling below minimum
        result = await scaling_manager._scale_to_target(1)  # Below min_workers=2
        
        assert result is True
        assert scaling_manager.current_workers == scaling_manager.config.min_workers
    
    def test_calculate_cpu_utilization(self, scaling_manager):
        """Test CPU utilization calculation."""
        cluster_status = {
            "metrics": {"cpu_utilization": 75.0}
        }
        
        utilization = scaling_manager._calculate_cpu_utilization(cluster_status)
        
        assert utilization == 75.0
    
    def test_calculate_memory_utilization(self, scaling_manager):
        """Test memory utilization calculation."""
        cluster_status = {
            "metrics": {"memory_utilization": 65.0}
        }
        
        utilization = scaling_manager._calculate_memory_utilization(cluster_status)
        
        assert utilization == 65.0
    
    def test_calculate_task_completion_rate(self, scaling_manager):
        """Test task completion rate calculation."""
        task_status = {
            "tasks": {"completed": 80, "total": 100}
        }
        
        rate = scaling_manager._calculate_task_completion_rate(task_status)
        
        assert rate == 0.8
    
    def test_calculate_error_rate(self, scaling_manager):
        """Test error rate calculation."""
        task_status = {
            "tasks": {"failed": 5, "total": 100}
        }
        
        rate = scaling_manager._calculate_error_rate(task_status)
        
        assert rate == 0.05
    
    def test_calculate_cost_per_hour(self, scaling_manager):
        """Test cost per hour calculation."""
        scaling_manager.current_workers = 10
        
        cost = scaling_manager._calculate_cost_per_hour()
        
        assert cost == 5.0  # 10 workers * $0.50/hour
    
    def test_calculate_workers_to_add(self, scaling_manager):
        """Test calculating workers to add."""
        # High utilization
        high_metrics = ScalingMetrics(cpu_utilization=95.0, memory_utilization=90.0, gpu_utilization=85.0)
        scaling_manager.current_workers = 5
        
        workers_to_add = scaling_manager._calculate_workers_to_add(high_metrics)
        
        assert workers_to_add > 0
        assert workers_to_add <= 4  # Min of 4 or max_workers - current_workers
    
    def test_calculate_workers_to_remove(self, scaling_manager):
        """Test calculating workers to remove."""
        # Low utilization
        low_metrics = ScalingMetrics(cpu_utilization=15.0, memory_utilization=10.0, gpu_utilization=5.0)
        scaling_manager.current_workers = 8
        
        workers_to_remove = scaling_manager._calculate_workers_to_remove(low_metrics)
        
        assert workers_to_remove > 0
        assert workers_to_remove <= 2  # Min of 2 or current_workers - min_workers
    
    def test_estimate_scaling_cost(self, scaling_manager):
        """Test estimating scaling cost."""
        # Scale up cost
        scale_up_cost = scaling_manager._estimate_scaling_cost(3, True)
        assert scale_up_cost > 0
        
        # Scale down cost (should be negative)
        scale_down_cost = scaling_manager._estimate_scaling_cost(2, False)
        assert scale_down_cost < 0
    
    def test_estimate_completion_time(self, scaling_manager):
        """Test estimating completion time."""
        metrics = ScalingMetrics(
            queue_length=100,
            task_completion_rate=0.5  # 50% completion rate
        )
        
        completion_time = scaling_manager._estimate_completion_time(metrics)
        
        assert completion_time == 200.0  # 100 / 0.5


class TestLoadBalancer:
    """Test suite for load balancer."""
    
    @pytest.fixture
    def load_balancer(self):
        """Create test load balancer."""
        config = ScalingConfig()
        return LoadBalancer(config)
    
    @pytest.mark.asyncio
    async def test_get_balancing_status(self, load_balancer):
        """Test getting balancing status."""
        status = await load_balancer.get_balancing_status()
        
        assert "strategy" in status
        assert "worker_loads" in status
        assert isinstance(status["worker_loads"], dict)
    
    @pytest.mark.asyncio
    async def test_get_recommendations(self, load_balancer):
        """Test getting recommendations."""
        metrics = ScalingMetrics()
        
        recommendations = await load_balancer.get_recommendations(metrics)
        
        assert "strategy" in recommendations
        assert "recommendations" in recommendations
        assert isinstance(recommendations["recommendations"], list)


class TestWorkloadPredictor:
    """Test suite for workload predictor."""
    
    @pytest.fixture
    def workload_predictor(self):
        """Create test workload predictor."""
        config = ScalingConfig()
        return WorkloadPredictor(config)
    
    @pytest.mark.asyncio
    async def test_predict_workload_increasing(self, workload_predictor):
        """Test predicting increasing workload."""
        # Create metrics with increasing trend
        metrics_history = [
            ScalingMetrics(cpu_utilization=50.0),
            ScalingMetrics(cpu_utilization=60.0),
            ScalingMetrics(cpu_utilization=70.0),
            ScalingMetrics(cpu_utilization=80.0),
            ScalingMetrics(cpu_utilization=90.0)
        ]
        
        prediction = await workload_predictor.predict_workload(metrics_history)
        
        assert prediction["prediction"] == "increasing"
        assert prediction["confidence"] > 0.5
    
    @pytest.mark.asyncio
    async def test_predict_workload_decreasing(self, workload_predictor):
        """Test predicting decreasing workload."""
        # Create metrics with decreasing trend
        metrics_history = [
            ScalingMetrics(cpu_utilization=90.0),
            ScalingMetrics(cpu_utilization=80.0),
            ScalingMetrics(cpu_utilization=70.0),
            ScalingMetrics(cpu_utilization=60.0),
            ScalingMetrics(cpu_utilization=50.0)
        ]
        
        prediction = await workload_predictor.predict_workload(metrics_history)
        
        assert prediction["prediction"] == "decreasing"
        assert prediction["confidence"] > 0.5
    
    @pytest.mark.asyncio
    async def test_predict_workload_stable(self, workload_predictor):
        """Test predicting stable workload."""
        # Create metrics with stable trend
        metrics_history = [
            ScalingMetrics(cpu_utilization=60.0),
            ScalingMetrics(cpu_utilization=62.0),
            ScalingMetrics(cpu_utilization=58.0),
            ScalingMetrics(cpu_utilization=61.0),
            ScalingMetrics(cpu_utilization=59.0)
        ]
        
        prediction = await workload_predictor.predict_workload(metrics_history)
        
        assert prediction["prediction"] == "stable"
        assert prediction["confidence"] > 0.5
    
    @pytest.mark.asyncio
    async def test_predict_workload_insufficient_data(self, workload_predictor):
        """Test predicting with insufficient data."""
        metrics_history = [ScalingMetrics(cpu_utilization=50.0)]
        
        prediction = await workload_predictor.predict_workload(metrics_history)
        
        assert prediction["prediction"] == "stable"
        assert prediction["confidence"] == 0.5


class TestCostOptimizer:
    """Test suite for cost optimizer."""
    
    @pytest.fixture
    def cost_optimizer(self):
        """Create test cost optimizer."""
        config = ScalingConfig(cost_threshold=10.0, enable_cost_optimization=True)
        return CostOptimizer(config)
    
    @pytest.mark.asyncio
    async def test_optimize_costs_within_threshold(self, cost_optimizer):
        """Test cost optimization within threshold."""
        metrics = ScalingMetrics(cost_per_hour=5.0)  # Below threshold
        
        # Should not raise any issues
        await cost_optimizer.optimize_costs(metrics)
    
    @pytest.mark.asyncio
    async def test_optimize_costs_above_threshold(self, cost_optimizer):
        """Test cost optimization above threshold."""
        metrics = ScalingMetrics(cost_per_hour=15.0)  # Above threshold
        
        # Should log optimization message
        with patch('src.pattern_mining.distributed.scaling.logger') as mock_logger:
            await cost_optimizer.optimize_costs(metrics)
            
            mock_logger.info.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_optimization_status(self, cost_optimizer):
        """Test getting optimization status."""
        status = await cost_optimizer.get_optimization_status()
        
        assert "cost_threshold" in status
        assert "optimization_enabled" in status
        assert status["cost_threshold"] == 10.0
        assert status["optimization_enabled"] is True
    
    @pytest.mark.asyncio
    async def test_get_recommendations_within_threshold(self, cost_optimizer):
        """Test getting recommendations within threshold."""
        metrics = ScalingMetrics(cost_per_hour=5.0)
        
        recommendations = await cost_optimizer.get_recommendations(metrics)
        
        assert "recommendations" in recommendations
        assert len(recommendations["recommendations"]) == 0
    
    @pytest.mark.asyncio
    async def test_get_recommendations_above_threshold(self, cost_optimizer):
        """Test getting recommendations above threshold."""
        metrics = ScalingMetrics(cost_per_hour=15.0)
        
        recommendations = await cost_optimizer.get_recommendations(metrics)
        
        assert "recommendations" in recommendations
        assert len(recommendations["recommendations"]) > 0
        assert recommendations["recommendations"][0]["type"] == "cost_reduction"
        assert recommendations["recommendations"][0]["priority"] == "high"


class TestScalingConfigurations:
    """Test scaling configuration classes."""
    
    def test_scaling_config_defaults(self):
        """Test scaling configuration defaults."""
        config = ScalingConfig()
        
        assert config.min_workers == 1
        assert config.max_workers == 100
        assert config.target_cpu_utilization == 70.0
        assert config.scale_up_threshold == 80.0
        assert config.scale_down_threshold == 30.0
        assert config.scale_up_cooldown == 300
        assert config.scale_down_cooldown == 600
        assert config.enable_cost_optimization is True
        assert config.enable_gpu_scaling is True
    
    def test_scaling_config_custom_values(self):
        """Test scaling configuration with custom values."""
        config = ScalingConfig(
            min_workers=3,
            max_workers=50,
            target_cpu_utilization=75.0,
            scale_up_threshold=85.0,
            scale_down_threshold=25.0,
            enable_cost_optimization=False
        )
        
        assert config.min_workers == 3
        assert config.max_workers == 50
        assert config.target_cpu_utilization == 75.0
        assert config.scale_up_threshold == 85.0
        assert config.scale_down_threshold == 25.0
        assert config.enable_cost_optimization is False
    
    def test_scaling_metrics_defaults(self):
        """Test scaling metrics defaults."""
        metrics = ScalingMetrics()
        
        assert metrics.cpu_utilization == 0.0
        assert metrics.memory_utilization == 0.0
        assert metrics.gpu_utilization == 0.0
        assert metrics.queue_length == 0
        assert metrics.task_completion_rate == 0.0
        assert metrics.error_rate == 0.0
        assert metrics.response_time == 0.0
        assert metrics.throughput == 0.0
        assert metrics.cost_per_hour == 0.0
        assert metrics.timestamp > 0
    
    def test_scaling_metrics_custom_values(self):
        """Test scaling metrics with custom values."""
        metrics = ScalingMetrics(
            cpu_utilization=75.0,
            memory_utilization=80.0,
            gpu_utilization=65.0,
            queue_length=50,
            task_completion_rate=0.85,
            error_rate=0.02,
            response_time=1.5,
            throughput=120.0,
            cost_per_hour=8.5
        )
        
        assert metrics.cpu_utilization == 75.0
        assert metrics.memory_utilization == 80.0
        assert metrics.gpu_utilization == 65.0
        assert metrics.queue_length == 50
        assert metrics.task_completion_rate == 0.85
        assert metrics.error_rate == 0.02
        assert metrics.response_time == 1.5
        assert metrics.throughput == 120.0
        assert metrics.cost_per_hour == 8.5
    
    def test_create_scaling_config(self):
        """Test creating example scaling configuration."""
        config = create_scaling_config()
        
        assert isinstance(config, ScalingConfig)
        assert config.min_workers == 2
        assert config.max_workers == 20
        assert config.target_cpu_utilization == 70.0
        assert config.enable_cost_optimization is True
        assert config.enable_gpu_scaling is True


class TestScalingIntegration:
    """Integration tests for scaling functionality."""
    
    @pytest.mark.asyncio
    async def test_full_scaling_lifecycle(self):
        """Test complete scaling lifecycle."""
        config = ScalingConfig(min_workers=2, max_workers=10)
        
        # Create mock managers
        mock_cluster_manager = Mock(spec=RayClusterManager)
        mock_cluster_manager.get_cluster_status.return_value = asyncio.Future()
        mock_cluster_manager.get_cluster_status.return_value.set_result({
            "status": ClusterStatus.RUNNING.value,
            "metrics": {"cpu_utilization": 60.0, "memory_utilization": 70.0}
        })
        mock_cluster_manager.scale_cluster.return_value = asyncio.Future()
        mock_cluster_manager.scale_cluster.return_value.set_result(True)
        
        mock_task_manager = Mock(spec=DistributedTaskManager)
        mock_task_manager.get_manager_status.return_value = asyncio.Future()
        mock_task_manager.get_manager_status.return_value.set_result({
            "tasks": {"total": 100, "completed": 80, "failed": 5},
            "queues": {"task_queue_size": 25}
        })
        
        # Create scaling manager
        scaling_manager = AutoScalingManager(config, mock_cluster_manager, mock_task_manager)
        
        # Start auto-scaling
        with patch.object(scaling_manager, '_scale_to_target', return_value=True):
            start_result = await scaling_manager.start_autoscaling()
            assert start_result is True
            assert scaling_manager.scaling_active is True
        
        # Test metrics collection
        metrics = await scaling_manager._collect_metrics()
        assert isinstance(metrics, ScalingMetrics)
        assert metrics.cpu_utilization >= 0
        
        # Test scaling decision
        decision = await scaling_manager._make_scaling_decision(metrics)
        assert isinstance(decision, ScalingDecision)
        assert decision.direction in [ScalingDirection.UP, ScalingDirection.DOWN, ScalingDirection.STABLE]
        
        # Test manual scaling
        with patch.object(scaling_manager, '_scale_to_target', return_value=True):
            manual_result = await scaling_manager.manual_scale(5)
            assert manual_result is True
            assert scaling_manager.target_workers == 5
        
        # Test status retrieval
        status = await scaling_manager.get_scaling_status()
        assert isinstance(status, dict)
        assert "scaling_active" in status
        assert "current_workers" in status
        
        # Stop auto-scaling
        with patch.object(scaling_manager, '_scale_to_target', return_value=True):
            stop_result = await scaling_manager.stop_autoscaling()
            assert stop_result is True
            assert scaling_manager.scaling_active is False
    
    @pytest.mark.asyncio
    async def test_scaling_under_load(self):
        """Test scaling behavior under high load."""
        config = ScalingConfig(min_workers=2, max_workers=10, scale_up_threshold=80.0)
        
        # Create mock managers with high load
        mock_cluster_manager = Mock(spec=RayClusterManager)
        mock_cluster_manager.get_cluster_status.return_value = asyncio.Future()
        mock_cluster_manager.get_cluster_status.return_value.set_result({
            "status": ClusterStatus.RUNNING.value,
            "metrics": {"cpu_utilization": 90.0, "memory_utilization": 85.0}
        })
        mock_cluster_manager.scale_cluster.return_value = asyncio.Future()
        mock_cluster_manager.scale_cluster.return_value.set_result(True)
        
        mock_task_manager = Mock(spec=DistributedTaskManager)
        mock_task_manager.get_manager_status.return_value = asyncio.Future()
        mock_task_manager.get_manager_status.return_value.set_result({
            "tasks": {"total": 200, "completed": 150, "failed": 10},
            "queues": {"task_queue_size": 100}  # High queue length
        })
        
        scaling_manager = AutoScalingManager(config, mock_cluster_manager, mock_task_manager)
        scaling_manager.current_workers = 5
        scaling_manager.last_scale_up_time = time.time() - 600  # Outside cooldown
        
        # Test that high load triggers scale up
        metrics = await scaling_manager._collect_metrics()
        decision = await scaling_manager._make_scaling_decision(metrics)
        
        assert decision.direction == ScalingDirection.UP
        assert decision.resource_request.num_workers > 0
        assert "High resource utilization" in decision.reason
    
    @pytest.mark.asyncio
    async def test_scaling_under_low_load(self):
        """Test scaling behavior under low load."""
        config = ScalingConfig(min_workers=2, max_workers=10, scale_down_threshold=30.0)
        
        # Create mock managers with low load
        mock_cluster_manager = Mock(spec=RayClusterManager)
        mock_cluster_manager.get_cluster_status.return_value = asyncio.Future()
        mock_cluster_manager.get_cluster_status.return_value.set_result({
            "status": ClusterStatus.RUNNING.value,
            "metrics": {"cpu_utilization": 20.0, "memory_utilization": 25.0}
        })
        mock_cluster_manager.scale_cluster.return_value = asyncio.Future()
        mock_cluster_manager.scale_cluster.return_value.set_result(True)
        
        mock_task_manager = Mock(spec=DistributedTaskManager)
        mock_task_manager.get_manager_status.return_value = asyncio.Future()
        mock_task_manager.get_manager_status.return_value.set_result({
            "tasks": {"total": 50, "completed": 45, "failed": 2},
            "queues": {"task_queue_size": 5}  # Low queue length
        })
        
        scaling_manager = AutoScalingManager(config, mock_cluster_manager, mock_task_manager)
        scaling_manager.current_workers = 8
        scaling_manager.last_scale_down_time = time.time() - 700  # Outside cooldown
        
        # Test that low load triggers scale down
        metrics = await scaling_manager._collect_metrics()
        decision = await scaling_manager._make_scaling_decision(metrics)
        
        assert decision.direction == ScalingDirection.DOWN
        assert decision.resource_request.num_workers > 0
        assert "Low resource utilization" in decision.reason


if __name__ == "__main__":
    pytest.main([__file__, "-v"])