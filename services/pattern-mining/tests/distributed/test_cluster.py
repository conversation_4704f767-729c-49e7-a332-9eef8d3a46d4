"""
Tests for Ray cluster management functionality.
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock
import ray

from src.pattern_mining.distributed.cluster import (
    RayClusterManager,
    ClusterConfig,
    NodeConfig,
    ClusterStatus,
    create_default_cluster_config
)


class TestRayClusterManager:
    """Test suite for Ray cluster manager."""
    
    @pytest.fixture
    def cluster_config(self):
        """Create test cluster configuration."""
        return create_default_cluster_config("test_cluster")
    
    @pytest.fixture
    def cluster_manager(self, cluster_config):
        """Create test cluster manager."""
        return RayClusterManager(cluster_config)
    
    @pytest.mark.asyncio
    async def test_initialize_cluster(self, cluster_manager):
        """Test cluster initialization."""
        with patch('ray.init') as mock_init, \
             patch('ray.is_initialized', return_value=False):
            
            # Mock Ray initialization
            mock_init.return_value = None
            
            with patch.object(cluster_manager, '_wait_for_cluster_ready', return_value=True), \
                 patch.object(cluster_manager, '_initialize_cluster_resources'), \
                 patch.object(cluster_manager, '_start_monitoring'):
                
                result = await cluster_manager.initialize_cluster()
                
                assert result is True
                assert cluster_manager.status == ClusterStatus.RUNNING
                mock_init.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_initialize_cluster_failure(self, cluster_manager):
        """Test cluster initialization failure."""
        with patch('ray.init', side_effect=Exception("Init failed")):
            
            result = await cluster_manager.initialize_cluster()
            
            assert result is False
            assert cluster_manager.status == ClusterStatus.FAILED
            assert cluster_manager.failure_count == 1
    
    @pytest.mark.asyncio
    async def test_shutdown_cluster(self, cluster_manager):
        """Test cluster shutdown."""
        with patch('ray.is_initialized', return_value=True), \
             patch('ray.shutdown') as mock_shutdown:
            
            with patch.object(cluster_manager, '_stop_monitoring'):
                
                result = await cluster_manager.shutdown_cluster()
                
                assert result is True
                assert cluster_manager.status == ClusterStatus.SHUTDOWN
                mock_shutdown.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_scale_cluster(self, cluster_manager):
        """Test cluster scaling."""
        target_workers = 5
        
        with patch.object(cluster_manager, '_get_worker_nodes', return_value=[1, 2, 3]), \
             patch.object(cluster_manager, '_scale_up') as mock_scale_up, \
             patch.object(cluster_manager, '_wait_for_scaling_complete', return_value=True):
            
            cluster_manager.status = ClusterStatus.RUNNING
            
            result = await cluster_manager.scale_cluster(target_workers)
            
            assert result is True
            assert cluster_manager.status == ClusterStatus.RUNNING
            mock_scale_up.assert_called_once_with(2)  # 5 - 3 = 2
    
    @pytest.mark.asyncio
    async def test_scale_cluster_down(self, cluster_manager):
        """Test cluster scaling down."""
        target_workers = 2
        
        with patch.object(cluster_manager, '_get_worker_nodes', return_value=[1, 2, 3, 4, 5]), \
             patch.object(cluster_manager, '_scale_down') as mock_scale_down, \
             patch.object(cluster_manager, '_wait_for_scaling_complete', return_value=True):
            
            cluster_manager.status = ClusterStatus.RUNNING
            
            result = await cluster_manager.scale_cluster(target_workers)
            
            assert result is True
            assert cluster_manager.status == ClusterStatus.RUNNING
            mock_scale_down.assert_called_once_with(3)  # 5 - 2 = 3
    
    @pytest.mark.asyncio
    async def test_health_check(self, cluster_manager):
        """Test cluster health check."""
        with patch('ray.is_initialized', return_value=True), \
             patch.object(cluster_manager, '_check_cluster_connectivity', return_value=True), \
             patch.object(cluster_manager, '_check_node_health', return_value=True), \
             patch.object(cluster_manager, '_check_resource_availability', return_value=True), \
             patch.object(cluster_manager, '_update_health_metrics'):
            
            result = await cluster_manager.health_check()
            
            assert result is True
            assert cluster_manager.last_health_check > 0
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self, cluster_manager):
        """Test cluster health check failure."""
        with patch('ray.is_initialized', return_value=False):
            
            result = await cluster_manager.health_check()
            
            assert result is False
    
    @pytest.mark.asyncio
    async def test_get_cluster_status(self, cluster_manager):
        """Test get cluster status."""
        with patch.object(cluster_manager, '_get_cluster_nodes', return_value=[
            {"node_type": "head", "is_alive": True},
            {"node_type": "worker", "is_alive": True},
            {"node_type": "worker", "is_alive": False}
        ]), \
             patch.object(cluster_manager, '_get_cluster_resources', return_value={"CPU": 8, "memory": 16000000000}):
            
            status = await cluster_manager.get_cluster_status()
            
            assert status["cluster_name"] == "test_cluster"
            assert status["nodes"]["total"] == 3
            assert status["nodes"]["head"] == 1
            assert status["nodes"]["workers"] == 2
            assert status["nodes"]["alive"] == 2
            assert status["nodes"]["dead"] == 1
            assert status["resources"]["CPU"] == 8
    
    @pytest.mark.asyncio
    async def test_recover_from_failure(self, cluster_manager):
        """Test recovery from failure."""
        cluster_manager.failure_count = 1
        
        with patch.object(cluster_manager, '_restart_failed_components'), \
             patch.object(cluster_manager, 'health_check', return_value=True):
            
            result = await cluster_manager.recover_from_failure()
            
            assert result is True
            assert cluster_manager.failure_count == 0
            assert cluster_manager.status == ClusterStatus.RUNNING
    
    @pytest.mark.asyncio
    async def test_recover_from_failure_max_failures(self, cluster_manager):
        """Test recovery failure when max failures exceeded."""
        cluster_manager.failure_count = cluster_manager.max_failures
        
        result = await cluster_manager.recover_from_failure()
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_check_cluster_connectivity(self, cluster_manager):
        """Test cluster connectivity check."""
        with patch('ray.remote') as mock_remote:
            # Mock remote task
            mock_task = Mock()
            mock_task.remote.return_value = "ok"
            mock_remote.return_value = mock_task
            
            result = await cluster_manager._check_cluster_connectivity()
            
            assert result is True
    
    @pytest.mark.asyncio
    async def test_check_node_health(self, cluster_manager):
        """Test node health check."""
        with patch.object(cluster_manager, '_get_cluster_nodes', return_value=[
            {"is_alive": True},
            {"is_alive": True}
        ]):
            
            result = await cluster_manager._check_node_health()
            
            assert result is True
    
    @pytest.mark.asyncio
    async def test_check_resource_availability(self, cluster_manager):
        """Test resource availability check."""
        with patch.object(cluster_manager, '_get_cluster_resources', return_value={
            "CPU": 8,
            "memory": 16000000000
        }):
            
            result = await cluster_manager._check_resource_availability()
            
            assert result is True
    
    @pytest.mark.asyncio
    async def test_check_resource_availability_insufficient(self, cluster_manager):
        """Test resource availability check with insufficient resources."""
        with patch.object(cluster_manager, '_get_cluster_resources', return_value={
            "CPU": 0,
            "memory": 100000000  # 100MB, below minimum
        }):
            
            result = await cluster_manager._check_resource_availability()
            
            assert result is False
    
    def test_create_default_cluster_config(self):
        """Test creating default cluster configuration."""
        config = create_default_cluster_config("test_cluster")
        
        assert config.cluster_name == "test_cluster"
        assert config.head_node.node_type == "head"
        assert len(config.worker_nodes) == 2
        assert config.max_workers == 20
        assert config.upscaling_speed == 1.0
    
    def test_cluster_config_validation(self):
        """Test cluster configuration validation."""
        # Test minimum required fields
        config = ClusterConfig(cluster_name="test")
        assert config.cluster_name == "test"
        assert config.provider == "local"
        assert config.max_workers == 100
        
        # Test with custom values
        config = ClusterConfig(
            cluster_name="custom",
            max_workers=50,
            upscaling_speed=2.0
        )
        assert config.max_workers == 50
        assert config.upscaling_speed == 2.0
    
    def test_node_config_validation(self):
        """Test node configuration validation."""
        # Test minimum required fields
        config = NodeConfig(node_type="worker")
        assert config.node_type == "worker"
        assert config.min_workers == 0
        assert config.max_workers == 100
        
        # Test with custom values
        config = NodeConfig(
            node_type="gpu_worker",
            min_workers=1,
            max_workers=10,
            use_gpu=True,
            gpu_count=2
        )
        assert config.node_type == "gpu_worker"
        assert config.min_workers == 1
        assert config.max_workers == 10
        assert config.use_gpu is True
        assert config.gpu_count == 2


class TestClusterIntegration:
    """Integration tests for cluster management."""
    
    @pytest.mark.asyncio
    async def test_full_cluster_lifecycle(self):
        """Test complete cluster lifecycle."""
        config = create_default_cluster_config("integration_test")
        manager = RayClusterManager(config)
        
        # Mock Ray operations
        with patch('ray.init'), \
             patch('ray.is_initialized', return_value=True), \
             patch('ray.shutdown'), \
             patch('ray.cluster_resources', return_value={"CPU": 8, "memory": 16000000000}), \
             patch('ray.nodes', return_value=[{"NodeID": "1", "is_alive": True}]):
            
            with patch.object(manager, '_wait_for_cluster_ready', return_value=True), \
                 patch.object(manager, '_start_monitoring'), \
                 patch.object(manager, '_stop_monitoring'):
                
                # Initialize cluster
                init_result = await manager.initialize_cluster()
                assert init_result is True
                assert manager.status == ClusterStatus.RUNNING
                
                # Scale cluster
                scale_result = await manager.scale_cluster(5)
                assert scale_result is True
                
                # Check health
                health_result = await manager.health_check()
                assert health_result is True
                
                # Get status
                status = await manager.get_cluster_status()
                assert status["cluster_name"] == "integration_test"
                assert status["status"] == ClusterStatus.RUNNING.value
                
                # Shutdown cluster
                shutdown_result = await manager.shutdown_cluster()
                assert shutdown_result is True
                assert manager.status == ClusterStatus.SHUTDOWN
    
    @pytest.mark.asyncio
    async def test_cluster_failure_and_recovery(self):
        """Test cluster failure and recovery scenarios."""
        config = create_default_cluster_config("failure_test")
        manager = RayClusterManager(config)
        
        # Simulate initialization failure
        with patch('ray.init', side_effect=Exception("Connection failed")):
            init_result = await manager.initialize_cluster()
            assert init_result is False
            assert manager.status == ClusterStatus.FAILED
            assert manager.failure_count == 1
        
        # Test recovery
        with patch('ray.init'), \
             patch('ray.is_initialized', return_value=True), \
             patch.object(manager, '_wait_for_cluster_ready', return_value=True), \
             patch.object(manager, '_initialize_cluster_resources'), \
             patch.object(manager, '_start_monitoring'):
            
            recovery_result = await manager.recover_from_failure()
            assert recovery_result is True
            assert manager.status == ClusterStatus.RUNNING
            assert manager.failure_count == 0
    
    @pytest.mark.asyncio
    async def test_concurrent_operations(self):
        """Test concurrent cluster operations."""
        config = create_default_cluster_config("concurrent_test")
        manager = RayClusterManager(config)
        
        with patch('ray.init'), \
             patch('ray.is_initialized', return_value=True), \
             patch('ray.cluster_resources', return_value={"CPU": 8, "memory": 16000000000}), \
             patch('ray.nodes', return_value=[{"NodeID": "1", "is_alive": True}]):
            
            with patch.object(manager, '_wait_for_cluster_ready', return_value=True), \
                 patch.object(manager, '_start_monitoring'), \
                 patch.object(manager, '_scale_up'), \
                 patch.object(manager, '_wait_for_scaling_complete', return_value=True):
                
                # Initialize cluster
                await manager.initialize_cluster()
                
                # Run concurrent operations
                tasks = [
                    manager.scale_cluster(3),
                    manager.health_check(),
                    manager.get_cluster_status()
                ]
                
                results = await asyncio.gather(*tasks)
                
                # All operations should complete successfully
                assert results[0] is True  # Scale result
                assert results[1] is True  # Health check result
                assert isinstance(results[2], dict)  # Status result
    
    @pytest.mark.asyncio
    async def test_monitoring_lifecycle(self):
        """Test monitoring lifecycle."""
        config = create_default_cluster_config("monitoring_test")
        manager = RayClusterManager(config)
        
        with patch('ray.init'), \
             patch('ray.is_initialized', return_value=True), \
             patch('ray.cluster_resources', return_value={"CPU": 8, "memory": 16000000000}), \
             patch('ray.nodes', return_value=[{"NodeID": "1", "is_alive": True}]):
            
            with patch.object(manager, '_wait_for_cluster_ready', return_value=True), \
                 patch.object(manager, '_initialize_cluster_resources'):
                
                # Initialize cluster (should start monitoring)
                await manager.initialize_cluster()
                
                # Verify monitoring started
                assert manager.status == ClusterStatus.RUNNING
                
                # Test health check updates
                with patch.object(manager, '_check_cluster_connectivity', return_value=True), \
                     patch.object(manager, '_check_node_health', return_value=True), \
                     patch.object(manager, '_check_resource_availability', return_value=True), \
                     patch.object(manager, '_update_health_metrics'):
                    
                    health_result = await manager.health_check()
                    assert health_result is True
                    assert manager.last_health_check > 0


@pytest.mark.integration
class TestClusterRealIntegration:
    """Real integration tests with Ray (requires Ray to be installed)."""
    
    @pytest.mark.skipif(not pytest.importorskip("ray"), reason="Ray not available")
    @pytest.mark.asyncio
    async def test_real_ray_cluster(self):
        """Test with actual Ray cluster (local mode)."""
        config = create_default_cluster_config("real_test")
        manager = RayClusterManager(config)
        
        try:
            # This test requires Ray to be properly installed
            if not ray.is_initialized():
                ray.init(local_mode=True)
            
            # Test basic cluster operations
            status = await manager.get_cluster_status()
            assert isinstance(status, dict)
            assert "cluster_name" in status
            
            # Test health check
            health = await manager.health_check()
            assert isinstance(health, bool)
            
        finally:
            # Clean up
            if ray.is_initialized():
                ray.shutdown()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])