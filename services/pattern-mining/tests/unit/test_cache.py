"""
Cache Tests

Comprehensive tests for the caching layer including Redis client,
strategies, pattern caches, distributed caching, and monitoring.
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Any, Dict, List

from src.pattern_mining.cache.redis_client import (
    RedisClientManager,
    RedisClusterManager,
    RedisConfig,
    RedisMode,
    RedisMetrics
)
from src.pattern_mining.cache.strategies import (
    LRUCache,
    TTLCache,
    MultiLevelCache,
    CacheInvalidationStrategy,
    CacheWarmupStrategy,
    create_lru_cache,
    create_ttl_cache,
    create_multi_level_cache
)
from src.pattern_mining.cache.pattern_cache import (
    Pattern<PERSON>ache,
    FeatureCache,
    InferenceCache,
    Similarity<PERSON>ache,
    <PERSON>chResultCache,
    <PERSON>acheKey,
    CacheKeyType
)
from src.pattern_mining.cache.distributed_cache import (
    DistributedCache,
    ConsistentHashRing,
    CacheNode,
    ConsistentHashingCache,
    ShardedCache,
    ConsistencyLevel
)
from src.pattern_mining.cache.monitoring import (
    CacheMetrics,
    CacheMonitor,
    CacheAlerting,
    AlertType,
    AlertSeverity,
    AlertRule
)


class TestRedisClientManager:
    """Test Redis client manager."""
    
    @pytest.fixture
    def redis_config(self):
        """Test Redis configuration."""
        return RedisConfig(
            url="redis://localhost:6379",
            max_connections=10,
            connection_timeout=5.0,
            socket_timeout=5.0,
            health_check_interval=30.0
        )
    
    @pytest.fixture
    def redis_client(self, redis_config):
        """Test Redis client."""
        return RedisClientManager(redis_config)
    
    @pytest.mark.asyncio
    async def test_redis_client_initialization(self, redis_client):
        """Test Redis client initialization."""
        with patch('redis.asyncio.from_url') as mock_redis:
            mock_redis.return_value = AsyncMock()
            
            await redis_client.initialize()
            
            assert redis_client.client is not None
            mock_redis.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_redis_client_get_set(self, redis_client):
        """Test Redis client get/set operations."""
        with patch('redis.asyncio.from_url') as mock_redis:
            mock_client = AsyncMock()
            mock_redis.return_value = mock_client
            
            await redis_client.initialize()
            
            # Test set
            mock_client.set.return_value = True
            result = await redis_client.set("test_key", "test_value")
            assert result is True
            
            # Test get
            mock_client.get.return_value = '"test_value"'
            result = await redis_client.get("test_key")
            assert result == "test_value"
    
    @pytest.mark.asyncio
    async def test_redis_client_retry_logic(self, redis_client):
        """Test Redis client retry logic."""
        with patch('redis.asyncio.from_url') as mock_redis:
            mock_client = AsyncMock()
            mock_redis.return_value = mock_client
            
            await redis_client.initialize()
            
            # Simulate timeout then success
            mock_client.get.side_effect = [
                Exception("Connection timeout"),
                Exception("Connection timeout"),
                '"test_value"'
            ]
            
            result = await redis_client.get("test_key")
            assert result == "test_value"
            assert mock_client.get.call_count == 3
    
    @pytest.mark.asyncio
    async def test_redis_client_health_check(self, redis_client):
        """Test Redis client health check."""
        with patch('redis.asyncio.from_url') as mock_redis:
            mock_client = AsyncMock()
            mock_redis.return_value = mock_client
            
            await redis_client.initialize()
            
            # Mock info command response
            mock_client.info.return_value = {
                'used_memory': 1024,
                'connected_clients': 5,
                'keyspace_hits': 100,
                'keyspace_misses': 20
            }
            
            await redis_client._perform_health_check()
            
            assert redis_client.is_healthy is True
            assert redis_client.metrics.memory_usage == 1024
            assert redis_client.metrics.connected_clients == 5
    
    @pytest.mark.asyncio
    async def test_redis_client_batch_operations(self, redis_client):
        """Test Redis client batch operations."""
        with patch('redis.asyncio.from_url') as mock_redis:
            mock_client = AsyncMock()
            mock_redis.return_value = mock_client
            
            await redis_client.initialize()
            
            # Test mget
            mock_client.mget.return_value = ['"value1"', '"value2"', None]
            result = await redis_client.mget("key1", "key2", "key3")
            assert result == ["value1", "value2", None]
            
            # Test mset
            mock_client.mset.return_value = True
            result = await redis_client.mset({"key1": "value1", "key2": "value2"})
            assert result is True


class TestCacheStrategies:
    """Test cache strategies."""
    
    @pytest.fixture
    def lru_cache(self):
        """Test LRU cache."""
        return LRUCache(max_size=3, ttl=60)
    
    @pytest.fixture
    def ttl_cache(self):
        """Test TTL cache."""
        return TTLCache(max_size=3, ttl=1)  # 1 second TTL for testing
    
    @pytest.mark.asyncio
    async def test_lru_cache_basic_operations(self, lru_cache):
        """Test LRU cache basic operations."""
        # Test set and get
        await lru_cache.set("key1", "value1")
        result = await lru_cache.get("key1")
        assert result == "value1"
        
        # Test non-existent key
        result = await lru_cache.get("nonexistent")
        assert result is None
        
        # Test delete
        await lru_cache.delete("key1")
        result = await lru_cache.get("key1")
        assert result is None
    
    @pytest.mark.asyncio
    async def test_lru_cache_eviction(self, lru_cache):
        """Test LRU cache eviction."""
        # Fill cache to capacity
        await lru_cache.set("key1", "value1")
        await lru_cache.set("key2", "value2")
        await lru_cache.set("key3", "value3")
        
        # Add one more item to trigger eviction
        await lru_cache.set("key4", "value4")
        
        # key1 should be evicted (least recently used)
        result = await lru_cache.get("key1")
        assert result is None
        
        # Other keys should still exist
        assert await lru_cache.get("key2") == "value2"
        assert await lru_cache.get("key3") == "value3"
        assert await lru_cache.get("key4") == "value4"
    
    @pytest.mark.asyncio
    async def test_lru_cache_access_order(self, lru_cache):
        """Test LRU cache access order."""
        # Fill cache
        await lru_cache.set("key1", "value1")
        await lru_cache.set("key2", "value2")
        await lru_cache.set("key3", "value3")
        
        # Access key1 to make it recently used
        await lru_cache.get("key1")
        
        # Add new item
        await lru_cache.set("key4", "value4")
        
        # key2 should be evicted (least recently used)
        result = await lru_cache.get("key2")
        assert result is None
        
        # key1 should still exist
        assert await lru_cache.get("key1") == "value1"
    
    @pytest.mark.asyncio
    async def test_ttl_cache_expiration(self, ttl_cache):
        """Test TTL cache expiration."""
        # Set item with TTL
        await ttl_cache.set("key1", "value1")
        
        # Should be available immediately
        result = await ttl_cache.get("key1")
        assert result == "value1"
        
        # Wait for expiration
        await asyncio.sleep(1.1)
        
        # Should be expired
        result = await ttl_cache.get("key1")
        assert result is None
    
    @pytest.mark.asyncio
    async def test_ttl_cache_custom_ttl(self, ttl_cache):
        """Test TTL cache with custom TTL."""
        # Set item with custom TTL
        await ttl_cache.set("key1", "value1", ttl=2.0)
        
        # Should be available after 1 second
        await asyncio.sleep(1.1)
        result = await ttl_cache.get("key1")
        assert result == "value1"
        
        # Should be expired after 2 seconds
        await asyncio.sleep(1.1)
        result = await ttl_cache.get("key1")
        assert result is None
    
    @pytest.mark.asyncio
    async def test_cache_statistics(self, lru_cache):
        """Test cache statistics."""
        # Perform operations
        await lru_cache.set("key1", "value1")
        await lru_cache.get("key1")  # hit
        await lru_cache.get("key2")  # miss
        
        stats = await lru_cache.get_stats()
        
        assert stats['hits'] == 1
        assert stats['misses'] == 1
        assert stats['size'] == 1
        assert stats['hit_rate'] == 0.5


class TestMultiLevelCache:
    """Test multi-level cache."""
    
    @pytest.fixture
    def multi_level_cache(self):
        """Test multi-level cache."""
        l1_cache = LRUCache(max_size=2, ttl=60)
        
        with patch('src.pattern_mining.cache.redis_client.get_redis_client') as mock_get_client:
            mock_redis = AsyncMock()
            mock_get_client.return_value = mock_redis
            
            return MultiLevelCache(
                l1_strategy=l1_cache,
                redis_client=mock_redis,
                l2_ttl=3600,
                promote_threshold=2
            )
    
    @pytest.mark.asyncio
    async def test_multi_level_cache_l1_hit(self, multi_level_cache):
        """Test L1 cache hit."""
        # Set item in L1
        await multi_level_cache.l1_cache.set("key1", "value1")
        
        # Should hit L1
        result = await multi_level_cache.get("key1")
        assert result == "value1"
    
    @pytest.mark.asyncio
    async def test_multi_level_cache_l2_hit(self, multi_level_cache):
        """Test L2 cache hit with promotion."""
        # Mock L2 cache hit
        multi_level_cache.l2_cache.get.return_value = "value1"
        
        # First access - should hit L2
        result = await multi_level_cache.get("key1")
        assert result == "value1"
        
        # Second access - should promote to L1
        result = await multi_level_cache.get("key1")
        assert result == "value1"
        
        # Third access - should hit L1
        result = await multi_level_cache.get("key1")
        assert result == "value1"
    
    @pytest.mark.asyncio
    async def test_multi_level_cache_set(self, multi_level_cache):
        """Test multi-level cache set."""
        multi_level_cache.l2_cache.set.return_value = True
        
        result = await multi_level_cache.set("key1", "value1", ttl=60)
        assert result is True
        
        # Should be in L1
        l1_result = await multi_level_cache.l1_cache.get("key1")
        assert l1_result == "value1"
        
        # Should also be sent to L2
        multi_level_cache.l2_cache.set.assert_called_once()


class TestCacheInvalidation:
    """Test cache invalidation strategies."""
    
    @pytest.fixture
    def cache_invalidation(self):
        """Test cache invalidation."""
        cache = LRUCache(max_size=10)
        return CacheInvalidationStrategy(cache)
    
    @pytest.mark.asyncio
    async def test_invalidate_by_tag(self, cache_invalidation):
        """Test invalidation by tag."""
        # Set items with tags
        await cache_invalidation.cache.set("key1", "value1", tags={"tag1", "tag2"})
        await cache_invalidation.cache.set("key2", "value2", tags={"tag1"})
        await cache_invalidation.cache.set("key3", "value3", tags={"tag2"})
        
        # Register tags
        await cache_invalidation.register_tag("key1", "tag1")
        await cache_invalidation.register_tag("key1", "tag2")
        await cache_invalidation.register_tag("key2", "tag1")
        await cache_invalidation.register_tag("key3", "tag2")
        
        # Invalidate by tag1
        count = await cache_invalidation.invalidate_by_tag("tag1")
        assert count == 2
        
        # key1 and key2 should be invalidated
        assert await cache_invalidation.cache.get("key1") is None
        assert await cache_invalidation.cache.get("key2") is None
        
        # key3 should still exist
        assert await cache_invalidation.cache.get("key3") == "value3"
    
    @pytest.mark.asyncio
    async def test_invalidate_by_dependency(self, cache_invalidation):
        """Test invalidation by dependency."""
        # Set items with dependencies
        await cache_invalidation.cache.set("key1", "value1")
        await cache_invalidation.cache.set("key2", "value2")
        await cache_invalidation.cache.set("dep1", "dep_value")
        
        # Register dependencies
        await cache_invalidation.register_dependency("key1", "dep1")
        await cache_invalidation.register_dependency("key2", "dep1")
        
        # Invalidate by dependency
        count = await cache_invalidation.invalidate_by_dependency("dep1")
        assert count == 2
        
        # Dependent keys should be invalidated
        assert await cache_invalidation.cache.get("key1") is None
        assert await cache_invalidation.cache.get("key2") is None
        
        # Dependency key should still exist
        assert await cache_invalidation.cache.get("dep1") == "dep_value"


class TestCacheWarmup:
    """Test cache warmup strategies."""
    
    @pytest.fixture
    def cache_warmup(self):
        """Test cache warmup."""
        cache = LRUCache(max_size=10)
        return CacheWarmupStrategy(cache)
    
    @pytest.mark.asyncio
    async def test_warmup_batch(self, cache_warmup):
        """Test batch warmup."""
        # Data loader function
        def data_loader():
            return [("key1", "value1"), ("key2", "value2"), ("key3", "value3")]
        
        # Warmup cache
        count = await cache_warmup.warmup_batch(data_loader, batch_size=2)
        assert count == 3
        
        # Items should be in cache
        assert await cache_warmup.cache.get("key1") == "value1"
        assert await cache_warmup.cache.get("key2") == "value2"
        assert await cache_warmup.cache.get("key3") == "value3"
    
    @pytest.mark.asyncio
    async def test_warmup_async(self, cache_warmup):
        """Test async warmup."""
        # Key generator
        def key_generator():
            return ["key1", "key2", "key3"]
        
        # Value loader
        def value_loader(key):
            return f"value_{key}"
        
        # Warmup cache
        count = await cache_warmup.warmup_async(
            key_generator, 
            value_loader, 
            concurrency=2
        )
        assert count == 3
        
        # Items should be in cache
        assert await cache_warmup.cache.get("key1") == "value_key1"
        assert await cache_warmup.cache.get("key2") == "value_key2"
        assert await cache_warmup.cache.get("key3") == "value_key3"


class TestPatternCache:
    """Test pattern-specific caches."""
    
    @pytest.fixture
    def pattern_cache(self):
        """Test pattern cache."""
        with patch('src.pattern_mining.cache.redis_client.get_redis_client') as mock_get_client:
            mock_redis = AsyncMock()
            mock_get_client.return_value = mock_redis
            
            return PatternCache(
                redis_client=mock_redis,
                l1_max_size=100,
                l2_ttl=3600
            )
    
    @pytest.mark.asyncio
    async def test_pattern_cache_key_generation(self, pattern_cache):
        """Test pattern cache key generation."""
        cache_key = CacheKey(
            key_type=CacheKeyType.PATTERN,
            primary_id="code_hash_123",
            secondary_id="python",
            params_hash="abc123"
        )
        
        key_str = str(cache_key)
        assert key_str == "pattern:v1:code_hash_123:python:abc123"
        
        # Test parsing
        parsed_key = CacheKey.from_string(key_str)
        assert parsed_key.key_type == CacheKeyType.PATTERN
        assert parsed_key.primary_id == "code_hash_123"
        assert parsed_key.secondary_id == "python"
        assert parsed_key.params_hash == "abc123"
    
    @pytest.mark.asyncio
    async def test_pattern_detection_cache(self, pattern_cache):
        """Test pattern detection result caching."""
        # Mock pattern detection result
        pattern_result = {"patterns": ["pattern1", "pattern2"], "confidence": 0.85}
        
        # Mock cache operations
        pattern_cache.cache.get.return_value = None
        pattern_cache.cache.set.return_value = True
        
        # Test cache miss
        result = await pattern_cache.get_pattern_detection_result(
            "code_hash_123",
            "python",
            {"detector_type": "ml"}
        )
        assert result is None
        
        # Test cache set
        success = await pattern_cache.set_pattern_detection_result(
            "code_hash_123",
            "python",
            {"detector_type": "ml"},
            pattern_result
        )
        assert success is True
        
        # Test cache hit
        pattern_cache.cache.get.return_value = {
            "data": "eyJwYXR0ZXJucyI6WyJwYXR0ZXJuMSIsInBhdHRlcm4yIl0sImNvbmZpZGVuY2UiOjAuODV9",
            "compressed": False,
            "timestamp": time.time(),
            "size": 100
        }
        
        result = await pattern_cache.get_pattern_detection_result(
            "code_hash_123",
            "python",
            {"detector_type": "ml"}
        )
        assert result == pattern_result


class TestDistributedCache:
    """Test distributed cache."""
    
    @pytest.fixture
    def cache_nodes(self):
        """Test cache nodes."""
        nodes = []
        for i in range(3):
            node = CacheNode(
                node_id=f"node_{i}",
                host="localhost",
                port=6379 + i,
                weight=1
            )
            node.redis_client = AsyncMock()
            nodes.append(node)
        return nodes
    
    @pytest.fixture
    def distributed_cache(self, cache_nodes):
        """Test distributed cache."""
        return DistributedCache(
            nodes=cache_nodes,
            replication_factor=2,
            consistency_level=ConsistencyLevel.EVENTUAL
        )
    
    def test_consistent_hash_ring(self, cache_nodes):
        """Test consistent hash ring."""
        ring = ConsistentHashRing(cache_nodes)
        
        # Test node assignment
        node1 = ring.get_node("test_key_1")
        node2 = ring.get_node("test_key_2")
        
        assert node1 is not None
        assert node2 is not None
        assert node1.node_id in ["node_0", "node_1", "node_2"]
        assert node2.node_id in ["node_0", "node_1", "node_2"]
        
        # Test multiple nodes for replication
        nodes = ring.get_nodes("test_key_1", 2)
        assert len(nodes) == 2
        assert nodes[0] != nodes[1]
    
    @pytest.mark.asyncio
    async def test_distributed_cache_get(self, distributed_cache):
        """Test distributed cache get operation."""
        # Mock node responses
        for node in distributed_cache.nodes:
            node.redis_client.get.return_value = "test_value"
        
        result = await distributed_cache.get("test_key")
        assert result == "test_value"
    
    @pytest.mark.asyncio
    async def test_distributed_cache_set(self, distributed_cache):
        """Test distributed cache set operation."""
        # Mock node responses
        for node in distributed_cache.nodes:
            node.redis_client.set.return_value = True
        
        result = await distributed_cache.set("test_key", "test_value")
        assert result is True
    
    @pytest.mark.asyncio
    async def test_distributed_cache_node_failure(self, distributed_cache):
        """Test distributed cache with node failure."""
        # Mock one node failure
        distributed_cache.nodes[0].redis_client.get.side_effect = Exception("Node down")
        distributed_cache.nodes[1].redis_client.get.return_value = "test_value"
        distributed_cache.nodes[2].redis_client.get.return_value = "test_value"
        
        result = await distributed_cache.get("test_key")
        assert result == "test_value"


class TestCacheMonitoring:
    """Test cache monitoring."""
    
    @pytest.fixture
    def cache_metrics(self):
        """Test cache metrics."""
        return CacheMetrics()
    
    @pytest.fixture
    def cache_monitor(self, cache_metrics):
        """Test cache monitor."""
        with patch('src.pattern_mining.cache.redis_client.get_redis_client') as mock_get_client:
            mock_redis = AsyncMock()
            mock_get_client.return_value = mock_redis
            
            return CacheMonitor(
                metrics=cache_metrics,
                redis_client=mock_redis,
                monitoring_interval=0.1  # Fast interval for testing
            )
    
    def test_cache_metrics_recording(self, cache_metrics):
        """Test cache metrics recording."""
        # Record operations
        cache_metrics.record_operation("get", "pattern", 0.1, "success")
        cache_metrics.record_operation("set", "pattern", 0.2, "success")
        cache_metrics.record_operation("get", "pattern", 0.15, "error")
        
        # Record hits/misses
        cache_metrics.record_hit("pattern", "l1")
        cache_metrics.record_hit("pattern", "l2")
        cache_metrics.record_miss("pattern", "l1")
        
        # Record evictions
        cache_metrics.record_eviction("pattern", "lru")
        
        # Get summary
        summary = cache_metrics.get_metrics_summary()
        
        assert summary['total_operations'] == 3
        assert summary['total_errors'] == 1
        assert "get_pattern" in summary['operation_stats']
        assert "set_pattern" in summary['operation_stats']
    
    @pytest.mark.asyncio
    async def test_cache_monitor_lifecycle(self, cache_monitor):
        """Test cache monitor lifecycle."""
        # Start monitoring
        await cache_monitor.start_monitoring()
        
        # Register a cache
        mock_cache = AsyncMock()
        mock_cache.get_stats.return_value = {
            "hits": 10,
            "misses": 5,
            "size": 100
        }
        
        cache_monitor.register_cache("test_cache", mock_cache)
        
        # Wait for monitoring cycle
        await asyncio.sleep(0.2)
        
        # Stop monitoring
        await cache_monitor.stop_monitoring()
        
        # Get monitoring report
        report = await cache_monitor.get_monitoring_report()
        
        assert "test_cache" in report['monitored_caches']
        assert report['metrics_summary'] is not None


class TestCacheAlerting:
    """Test cache alerting."""
    
    @pytest.fixture
    def cache_alerting(self):
        """Test cache alerting."""
        metrics = CacheMetrics()
        return CacheAlerting(metrics)
    
    def test_alert_rule_creation(self, cache_alerting):
        """Test alert rule creation."""
        from src.pattern_mining.cache.monitoring import AlertRule
        
        rule = AlertRule(
            alert_type=AlertType.CACHE_HIT_RATIO_LOW,
            threshold=0.5,
            duration=60,
            severity=AlertSeverity.MEDIUM,
            message="Hit ratio is low"
        )
        
        cache_alerting.add_alert_rule(rule)
        
        assert AlertType.CACHE_HIT_RATIO_LOW in cache_alerting.alert_rules
        assert cache_alerting.alert_rules[AlertType.CACHE_HIT_RATIO_LOW].threshold == 0.5
    
    @pytest.mark.asyncio
    async def test_alert_triggering(self, cache_alerting):
        """Test alert triggering."""
        # Mock low hit ratio
        cache_alerting.metrics.cache_hit_ratio.labels(cache_type="test").set(0.6)
        
        # Mock alert handler
        triggered_alerts = []
        
        def alert_handler(alert):
            triggered_alerts.append(alert)
        
        cache_alerting.add_alert_handler(alert_handler)
        
        # Start alerting
        await cache_alerting.start_alerting()
        
        # Wait for alert check
        await asyncio.sleep(0.1)
        
        # Stop alerting
        await cache_alerting.stop_alerting()
        
        # Check if alert was triggered
        status = cache_alerting.get_alert_status()
        assert len(status['alert_rules']) > 0


class TestCacheFactories:
    """Test cache factory functions."""
    
    def test_create_lru_cache(self):
        """Test LRU cache factory."""
        cache = create_lru_cache(max_size=100, ttl=3600)
        assert isinstance(cache, LRUCache)
        assert cache.max_size == 100
        assert cache.default_ttl == 3600
    
    def test_create_ttl_cache(self):
        """Test TTL cache factory."""
        cache = create_ttl_cache(max_size=100, ttl=3600)
        assert isinstance(cache, TTLCache)
        assert cache.max_size == 100
        assert cache.default_ttl == 3600
    
    def test_create_multi_level_cache(self):
        """Test multi-level cache factory."""
        with patch('src.pattern_mining.cache.redis_client.get_redis_client') as mock_get_client:
            mock_redis = AsyncMock()
            mock_get_client.return_value = mock_redis
            
            cache = create_multi_level_cache(
                l1_max_size=100,
                l1_ttl=1800,
                l2_ttl=3600,
                promote_threshold=3
            )
            
            assert isinstance(cache, MultiLevelCache)
            assert cache.l1_cache.max_size == 100
            assert cache.l2_ttl == 3600
            assert cache.promote_threshold == 3


# Integration tests
class TestCacheIntegration:
    """Integration tests for cache system."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_pattern_caching(self):
        """Test end-to-end pattern caching workflow."""
        with patch('src.pattern_mining.cache.redis_client.get_redis_client') as mock_get_client:
            mock_redis = AsyncMock()
            mock_get_client.return_value = mock_redis
            
            # Create pattern cache
            pattern_cache = PatternCache(redis_client=mock_redis)
            
            # Mock cache operations
            mock_redis.get.return_value = None  # Cache miss
            mock_redis.set.return_value = True  # Cache set success
            
            # Test pattern detection caching
            code_hash = "test_code_hash"
            language = "python"
            config = {"detector_type": "ml", "threshold": 0.8}
            
            # Cache miss
            result = await pattern_cache.get_pattern_detection_result(
                code_hash, language, config
            )
            assert result is None
            
            # Cache pattern result
            pattern_result = {
                "patterns": ["singleton", "factory"],
                "confidence": 0.85,
                "metadata": {"version": "1.0"}
            }
            
            success = await pattern_cache.set_pattern_detection_result(
                code_hash, language, config, pattern_result
            )
            assert success is True
            
            # Verify cache was called
            mock_redis.set.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_cache_monitoring_integration(self):
        """Test cache monitoring integration."""
        with patch('src.pattern_mining.cache.redis_client.get_redis_client') as mock_get_client:
            mock_redis = AsyncMock()
            mock_get_client.return_value = mock_redis
            
            # Create monitoring components
            metrics = CacheMetrics()
            monitor = CacheMonitor(metrics, mock_redis, monitoring_interval=0.1)
            alerting = CacheAlerting(metrics)
            
            # Start monitoring
            await monitor.start_monitoring()
            await alerting.start_alerting()
            
            # Create and register cache
            cache = LRUCache(max_size=10)
            monitor.register_cache("test_cache", cache)
            
            # Perform cache operations
            await cache.set("key1", "value1")
            await cache.get("key1")  # hit
            await cache.get("key2")  # miss
            
            # Wait for monitoring cycle
            await asyncio.sleep(0.2)
            
            # Stop monitoring
            await monitor.stop_monitoring()
            await alerting.stop_alerting()
            
            # Get monitoring report
            report = await monitor.get_monitoring_report()
            
            assert "test_cache" in report['monitored_caches']
            assert report['metrics_summary']['total_operations'] > 0
    
    @pytest.mark.asyncio
    async def test_distributed_cache_integration(self):
        """Test distributed cache integration."""
        # Create mock nodes
        nodes = []
        for i in range(3):
            node = CacheNode(
                node_id=f"node_{i}",
                host="localhost",
                port=6379 + i,
                weight=1
            )
            node.redis_client = AsyncMock()
            nodes.append(node)
        
        # Create distributed cache
        distributed_cache = DistributedCache(
            nodes=nodes,
            replication_factor=2,
            consistency_level=ConsistencyLevel.EVENTUAL
        )
        
        # Mock node responses
        for node in nodes:
            node.redis_client.set.return_value = True
            node.redis_client.get.return_value = "test_value"
            node.redis_client.delete.return_value = 1
        
        # Test operations
        set_result = await distributed_cache.set("test_key", "test_value")
        assert set_result is True
        
        get_result = await distributed_cache.get("test_key")
        assert get_result == "test_value"
        
        delete_result = await distributed_cache.delete("test_key")
        assert delete_result is True
        
        # Get cluster info
        cluster_info = await distributed_cache.get_cluster_info()
        assert cluster_info['total_nodes'] == 3
        assert cluster_info['replication_factor'] == 2


if __name__ == "__main__":
    pytest.main([__file__, "-v"])