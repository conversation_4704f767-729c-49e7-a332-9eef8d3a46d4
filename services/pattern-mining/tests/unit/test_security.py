"""
Comprehensive Test Suite for Security Module

Tests all security components including:
- Authentication
- Authorization
- Rate limiting
- Encryption
- Middleware
"""

import pytest
import asyncio
import time
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
from fastapi import Request, HTTPException
from fastapi.testclient import TestClient
import redis.asyncio as redis
from src.pattern_mining.security.authentication import (
    JWTAuthenticator, 
    MFAAuthenticator, 
    SessionManager, 
    User, 
    <PERSON>r<PERSON><PERSON>,
    AuthenticationError,
    TokenError
)
from src.pattern_mining.security.authorization import (
    RBACAuthorizer,
    RoleManager,
    PermissionManager,
    ResourceAuthorizer,
    AccessRequest,
    Resource,
    Action,
    Role,
    Permission
)
from src.pattern_mining.security.rate_limiting import (
    TokenBucketRateLimiter,
    SlidingWindowRateLimiter,
    RateLimitManager,
    RateLimitType,
    RateLimitResult
)
from src.pattern_mining.security.encryption import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    EncryptionAlgorithm,
    KeyType
)
from src.pattern_mining.security.middleware import (
    SecurityMiddleware,
    AuthenticationMiddleware,
    ValidationMiddleware,
    SecurityHeadersMiddleware
)
from src.pattern_mining.security.integration import SecurityIntegration
from src.pattern_mining.config.settings import Settings


class TestJWTAuthenticator:
    """Test JWT authentication."""
    
    @pytest.fixture
    def jwt_authenticator(self):
        """Create JWT authenticator for testing."""
        return JWTAuthenticator(
            secret_key="test_secret_key_12345",
            access_token_expire_minutes=15,
            refresh_token_expire_days=7
        )
    
    @pytest.fixture
    def test_user(self):
        """Create test user."""
        return User(
            id="test_user_123",
            email="<EMAIL>",
            username="testuser",
            role=UserRole.USER,
            permissions=["pattern:read", "pattern:create"],
            is_active=True,
            mfa_enabled=False
        )
    
    def test_create_access_token(self, jwt_authenticator, test_user):
        """Test access token creation."""
        token = jwt_authenticator.create_access_token(test_user)
        assert isinstance(token, str)
        assert len(token) > 0
    
    def test_create_refresh_token(self, jwt_authenticator, test_user):
        """Test refresh token creation."""
        token = jwt_authenticator.create_refresh_token(test_user)
        assert isinstance(token, str)
        assert len(token) > 0
    
    @pytest.mark.asyncio
    async def test_verify_token_success(self, jwt_authenticator, test_user):
        """Test successful token verification."""
        token = jwt_authenticator.create_access_token(test_user)
        payload = await jwt_authenticator.verify_token(token)
        
        assert payload["sub"] == test_user.id
        assert payload["email"] == test_user.email
        assert payload["username"] == test_user.username
        assert payload["role"] == test_user.role.value
        assert payload["permissions"] == test_user.permissions
    
    @pytest.mark.asyncio
    async def test_verify_token_expired(self, jwt_authenticator, test_user):
        """Test expired token verification."""
        # Create token with very short expiry
        expires_delta = timedelta(microseconds=1)
        token = jwt_authenticator.create_access_token(test_user, expires_delta)
        
        # Wait for token to expire
        await asyncio.sleep(0.001)
        
        with pytest.raises(TokenError, match="Token has expired"):
            await jwt_authenticator.verify_token(token)
    
    @pytest.mark.asyncio
    async def test_verify_token_invalid(self, jwt_authenticator):
        """Test invalid token verification."""
        invalid_token = "invalid_token_123"
        
        with pytest.raises(TokenError, match="Invalid token"):
            await jwt_authenticator.verify_token(invalid_token)
    
    @pytest.mark.asyncio
    async def test_revoke_token(self, jwt_authenticator, test_user):
        """Test token revocation."""
        # Mock Redis client
        mock_redis = AsyncMock()
        jwt_authenticator.redis_client = mock_redis
        
        token = jwt_authenticator.create_access_token(test_user)
        await jwt_authenticator.revoke_token(token)
        
        # Verify Redis delete was called
        mock_redis.delete.assert_called_once()


class TestMFAAuthenticator:
    """Test MFA authentication."""
    
    @pytest.fixture
    def mfa_authenticator(self):
        """Create MFA authenticator for testing."""
        return MFAAuthenticator(app_name="Pattern Mining Test")
    
    def test_generate_secret(self, mfa_authenticator):
        """Test MFA secret generation."""
        secret = mfa_authenticator.generate_secret()
        assert isinstance(secret, str)
        assert len(secret) == 32  # Base32 encoded
    
    def test_generate_qr_code_url(self, mfa_authenticator):
        """Test QR code URL generation."""
        secret = mfa_authenticator.generate_secret()
        url = mfa_authenticator.generate_qr_code_url("<EMAIL>", secret)
        
        assert "otpauth://totp/" in url
        assert "<EMAIL>" in url
        assert "Pattern Mining Test" in url
    
    def test_verify_totp_valid(self, mfa_authenticator):
        """Test valid TOTP verification."""
        secret = mfa_authenticator.generate_secret()
        
        # Generate current TOTP token
        import pyotp
        totp = pyotp.TOTP(secret)
        current_token = totp.now()
        
        # Verify token
        is_valid = mfa_authenticator.verify_totp(secret, current_token)
        assert is_valid is True
    
    def test_verify_totp_invalid(self, mfa_authenticator):
        """Test invalid TOTP verification."""
        secret = mfa_authenticator.generate_secret()
        invalid_token = "123456"
        
        is_valid = mfa_authenticator.verify_totp(secret, invalid_token)
        assert is_valid is False
    
    def test_generate_backup_codes(self, mfa_authenticator):
        """Test backup code generation."""
        codes = mfa_authenticator.generate_backup_codes(count=10)
        
        assert len(codes) == 10
        assert all(isinstance(code, str) for code in codes)
        assert all(len(code) == 8 for code in codes)  # 4 bytes hex = 8 chars


class TestSessionManager:
    """Test session management."""
    
    @pytest.fixture
    def session_manager(self):
        """Create session manager for testing."""
        mock_redis = AsyncMock()
        return SessionManager(
            redis_client=mock_redis,
            session_timeout_minutes=30,
            max_sessions_per_user=5
        )
    
    @pytest.mark.asyncio
    async def test_create_session(self, session_manager):
        """Test session creation."""
        session = await session_manager.create_session(
            user_id="test_user_123",
            ip_address="***********",
            user_agent="Test Browser"
        )
        
        assert session.user_id == "test_user_123"
        assert session.ip_address == "***********"
        assert session.user_agent == "Test Browser"
        assert session.is_active is True
        assert len(session.session_id) > 0
    
    @pytest.mark.asyncio
    async def test_get_session(self, session_manager):
        """Test session retrieval."""
        # Mock Redis response
        session_data = {
            "session_id": "test_session_123",
            "user_id": "test_user_123",
            "created_at": "2023-01-01T00:00:00",
            "expires_at": "2023-01-01T01:00:00",
            "last_activity": "2023-01-01T00:30:00",
            "ip_address": "***********",
            "user_agent": "Test Browser",
            "is_active": True
        }
        
        session_manager.redis_client.hgetall.return_value = session_data
        
        session = await session_manager.get_session("test_session_123")
        
        assert session is not None
        assert session.session_id == "test_session_123"
        assert session.user_id == "test_user_123"
    
    @pytest.mark.asyncio
    async def test_revoke_session(self, session_manager):
        """Test session revocation."""
        # Mock session data
        session_data = {
            "session_id": "test_session_123",
            "user_id": "test_user_123",
            "created_at": "2023-01-01T00:00:00",
            "expires_at": "2023-01-01T01:00:00",
            "last_activity": "2023-01-01T00:30:00",
            "ip_address": "***********",
            "user_agent": "Test Browser",
            "is_active": True
        }
        
        session_manager.redis_client.hgetall.return_value = session_data
        
        await session_manager.revoke_session("test_session_123")
        
        # Verify Redis operations
        session_manager.redis_client.srem.assert_called_once()
        session_manager.redis_client.delete.assert_called_once()


class TestRoleManager:
    """Test role management."""
    
    @pytest.fixture
    def role_manager(self):
        """Create role manager for testing."""
        mock_redis = AsyncMock()
        return RoleManager(mock_redis)
    
    @pytest.mark.asyncio
    async def test_assign_role_to_user(self, role_manager):
        """Test role assignment."""
        await role_manager.assign_role_to_user("test_user_123", "admin")
        
        # Verify Redis operation
        role_manager.redis_client.sadd.assert_called_once_with(
            "user_roles:test_user_123", 
            "admin"
        )
    
    @pytest.mark.asyncio
    async def test_get_user_roles(self, role_manager):
        """Test user role retrieval."""
        # Mock Redis response
        role_manager.redis_client.smembers.return_value = ["admin", "user"]
        
        roles = await role_manager.get_user_roles("test_user_123")
        
        assert "admin" in roles
        assert "user" in roles
    
    @pytest.mark.asyncio
    async def test_get_role_permissions(self, role_manager):
        """Test role permission retrieval."""
        # Mock role data
        role_data = {
            "name": "admin",
            "permissions": json.dumps(["system:admin", "user:admin"]),
            "description": "Administrator",
            "is_system_role": "True",
            "created_at": "2023-01-01T00:00:00",
            "updated_at": "2023-01-01T00:00:00"
        }
        
        role_manager.redis_client.hgetall.return_value = role_data
        
        permissions = await role_manager.get_role_permissions("admin")
        
        assert "system:admin" in permissions
        assert "user:admin" in permissions


class TestRateLimitManager:
    """Test rate limiting."""
    
    @pytest.fixture
    def rate_limit_manager(self):
        """Create rate limit manager for testing."""
        mock_redis = AsyncMock()
        settings = Settings()
        settings.api_rate_limit = 100
        settings.rate_limit_per_minute = 60
        settings.rate_limit_burst = 10
        
        return RateLimitManager(
            redis_client=mock_redis,
            settings=settings
        )
    
    @pytest.mark.asyncio
    async def test_check_rate_limit_allowed(self, rate_limit_manager):
        """Test rate limit check when allowed."""
        # Mock successful rate limit check
        with patch.object(
            rate_limit_manager.limiters["sliding_window"], 
            'check_rate_limit'
        ) as mock_check:
            mock_check.return_value = RateLimitResult(
                allowed=True,
                remaining=50,
                reset_time=60,
                limit=100,
                window_seconds=60
            )
            
            result = await rate_limit_manager.check_rate_limit(
                key="test_key",
                limit_type=RateLimitType.USER
            )
            
            assert result.allowed is True
            assert result.remaining == 50
            assert result.limit == 100
    
    @pytest.mark.asyncio
    async def test_check_rate_limit_exceeded(self, rate_limit_manager):
        """Test rate limit check when exceeded."""
        # Mock exceeded rate limit
        with patch.object(
            rate_limit_manager.limiters["sliding_window"], 
            'check_rate_limit'
        ) as mock_check:
            mock_check.return_value = RateLimitResult(
                allowed=False,
                remaining=0,
                reset_time=60,
                limit=100,
                window_seconds=60,
                retry_after=60
            )
            
            result = await rate_limit_manager.check_rate_limit(
                key="test_key",
                limit_type=RateLimitType.USER
            )
            
            assert result.allowed is False
            assert result.remaining == 0
            assert result.retry_after == 60


class TestEncryptionManager:
    """Test encryption management."""
    
    @pytest.fixture
    def encryption_manager(self):
        """Create encryption manager for testing."""
        mock_redis = AsyncMock()
        settings = Settings()
        settings.secret_key = "test_secret_key_12345"
        
        return EncryptionManager(
            settings=settings,
            redis_client=mock_redis
        )
    
    @pytest.mark.asyncio
    async def test_encrypt_decrypt_data(self, encryption_manager):
        """Test data encryption and decryption."""
        # Mock active key
        test_key = encryption_manager.generate_key(
            key_type=KeyType.DATA,
            algorithm=EncryptionAlgorithm.FERNET
        )
        
        encryption_manager.keys[test_key.key_id] = test_key
        
        with patch.object(encryption_manager, 'get_active_key') as mock_get_key:
            mock_get_key.return_value = test_key
            
            # Test encryption
            original_data = "sensitive_data_123"
            encrypted_data = await encryption_manager.encrypt_data(original_data)
            
            assert encrypted_data != original_data
            assert isinstance(encrypted_data, str)
            
            # Test decryption
            decrypted_data = await encryption_manager.decrypt_data(encrypted_data)
            
            assert decrypted_data == original_data
    
    def test_generate_key(self, encryption_manager):
        """Test key generation."""
        key = encryption_manager.generate_key(
            key_type=KeyType.DATA,
            algorithm=EncryptionAlgorithm.AES_256_GCM
        )
        
        assert key.key_type == KeyType.DATA
        assert key.algorithm == EncryptionAlgorithm.AES_256_GCM
        assert len(key.key_material) == 32  # 256 bits
        assert key.is_active is True


class TestSecretManager:
    """Test secret management."""
    
    @pytest.fixture
    def secret_manager(self):
        """Create secret manager for testing."""
        mock_redis = AsyncMock()
        settings = Settings()
        settings.gcp_project_id = "test-project"
        
        # Mock encryption manager
        mock_encryption = AsyncMock()
        mock_encryption.encrypt_data.return_value = "encrypted_value"
        mock_encryption.decrypt_data.return_value = "decrypted_value"
        
        return SecretManager(
            settings=settings,
            redis_client=mock_redis,
            encryption_manager=mock_encryption,
            use_gcp_secret_manager=False  # Use local storage for testing
        )
    
    @pytest.mark.asyncio
    async def test_store_secret(self, secret_manager):
        """Test secret storage."""
        version_id = await secret_manager.store_secret(
            secret_name="test_secret",
            secret_value="secret_value_123"
        )
        
        assert isinstance(version_id, str)
        assert len(version_id) > 0
        
        # Verify Redis operations
        secret_manager.redis_client.hset.assert_called_once()
        secret_manager.redis_client.set.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_secret(self, secret_manager):
        """Test secret retrieval."""
        # Mock Redis responses
        secret_manager.redis_client.get.return_value = "version_123"
        secret_manager.redis_client.hgetall.return_value = {
            "version_id": "version_123",
            "secret_name": "test_secret",
            "secret_value": "encrypted_value",
            "created_at": "2023-01-01T00:00:00",
            "expires_at": None,
            "is_active": True,
            "metadata": "{}"
        }
        
        secret_value = await secret_manager.get_secret("test_secret")
        
        assert secret_value == "decrypted_value"


class TestValidationMiddleware:
    """Test validation middleware."""
    
    @pytest.fixture
    def validation_middleware(self):
        """Create validation middleware for testing."""
        settings = Settings()
        settings.max_request_size = 1024 * 1024  # 1MB
        
        return ValidationMiddleware(settings)
    
    @pytest.mark.asyncio
    async def test_validate_parameter_safe(self, validation_middleware):
        """Test parameter validation with safe content."""
        # This should not raise an exception
        await validation_middleware._validate_parameter(
            "test_param",
            "safe_value_123",
            "query"
        )
    
    @pytest.mark.asyncio
    async def test_validate_parameter_sql_injection(self, validation_middleware):
        """Test parameter validation with SQL injection."""
        with pytest.raises(HTTPException) as exc_info:
            await validation_middleware._validate_parameter(
                "test_param",
                "'; DROP TABLE users; --",
                "query"
            )
        
        assert exc_info.value.status_code == 400
        assert "Malicious content detected" in str(exc_info.value.detail)
    
    @pytest.mark.asyncio
    async def test_validate_parameter_xss(self, validation_middleware):
        """Test parameter validation with XSS."""
        with pytest.raises(HTTPException) as exc_info:
            await validation_middleware._validate_parameter(
                "test_param",
                "<script>alert('xss')</script>",
                "query"
            )
        
        assert exc_info.value.status_code == 400
        assert "Malicious content detected" in str(exc_info.value.detail)


class TestSecurityIntegration:
    """Test security integration."""
    
    @pytest.fixture
    def security_integration(self):
        """Create security integration for testing."""
        settings = Settings()
        settings.redis_url = "redis://localhost:6379"
        settings.secret_key = "test_secret_key_12345"
        settings.api_rate_limit = 100
        
        return SecurityIntegration(settings)
    
    @pytest.mark.asyncio
    async def test_initialize(self, security_integration):
        """Test security integration initialization."""
        # Mock Redis client
        with patch('redis.asyncio.Redis.from_url') as mock_redis:
            mock_redis.return_value.ping = AsyncMock()
            
            await security_integration.initialize()
            
            assert security_integration._initialized is True
            assert security_integration.jwt_authenticator is not None
            assert security_integration.rbac_authorizer is not None
            assert security_integration.rate_limit_manager is not None
    
    @pytest.mark.asyncio
    async def test_get_current_user(self, security_integration):
        """Test getting current user from request."""
        # Mock request with JWT token
        mock_request = Mock()
        mock_request.headers = {
            "authorization": "Bearer valid_token"
        }
        
        # Mock JWT verification
        with patch.object(
            security_integration, 
            'jwt_authenticator'
        ) as mock_jwt:
            mock_jwt.verify_token.return_value = {
                "sub": "user_123",
                "email": "<EMAIL>"
            }
            
            # Initialize first
            security_integration._initialized = True
            
            user = await security_integration.get_current_user(mock_request)
            
            assert user is not None
            assert user["sub"] == "user_123"
            assert user["email"] == "<EMAIL>"


# Integration tests
class TestSecurityIntegrationFlow:
    """Test complete security integration flow."""
    
    @pytest.mark.asyncio
    async def test_full_authentication_flow(self):
        """Test complete authentication flow."""
        # This would test the full flow from login to authenticated request
        # Implementation would depend on the full application setup
        pass
    
    @pytest.mark.asyncio
    async def test_full_authorization_flow(self):
        """Test complete authorization flow."""
        # This would test the full flow from role assignment to permission check
        # Implementation would depend on the full application setup
        pass
    
    @pytest.mark.asyncio
    async def test_rate_limiting_integration(self):
        """Test rate limiting integration."""
        # This would test rate limiting in the context of the full middleware stack
        # Implementation would depend on the full application setup
        pass


# Performance tests
class TestSecurityPerformance:
    """Test security performance."""
    
    @pytest.mark.asyncio
    async def test_jwt_verification_performance(self):
        """Test JWT verification performance."""
        jwt_authenticator = JWTAuthenticator(
            secret_key="test_secret_key_12345"
        )
        
        test_user = User(
            id="test_user_123",
            email="<EMAIL>",
            username="testuser",
            role=UserRole.USER,
            permissions=["pattern:read"]
        )
        
        token = jwt_authenticator.create_access_token(test_user)
        
        # Measure verification time
        start_time = time.time()
        
        for _ in range(100):
            await jwt_authenticator.verify_token(token)
        
        end_time = time.time()
        avg_time = (end_time - start_time) / 100
        
        # Should be fast (under 1ms per verification)
        assert avg_time < 0.001
    
    @pytest.mark.asyncio
    async def test_encryption_performance(self):
        """Test encryption performance."""
        mock_redis = AsyncMock()
        settings = Settings()
        settings.secret_key = "test_secret_key_12345"
        
        encryption_manager = EncryptionManager(
            settings=settings,
            redis_client=mock_redis
        )
        
        # Generate test key
        test_key = encryption_manager.generate_key(
            key_type=KeyType.DATA,
            algorithm=EncryptionAlgorithm.FERNET
        )
        
        encryption_manager.keys[test_key.key_id] = test_key
        
        with patch.object(encryption_manager, 'get_active_key') as mock_get_key:
            mock_get_key.return_value = test_key
            
            test_data = "test_data_123" * 100  # Larger data
            
            # Measure encryption time
            start_time = time.time()
            
            for _ in range(10):
                encrypted = await encryption_manager.encrypt_data(test_data)
                await encryption_manager.decrypt_data(encrypted)
            
            end_time = time.time()
            avg_time = (end_time - start_time) / 10
            
            # Should be reasonable (under 10ms per encrypt/decrypt cycle)
            assert avg_time < 0.01


if __name__ == "__main__":
    pytest.main([__file__, "-v"])