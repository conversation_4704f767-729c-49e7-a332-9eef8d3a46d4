"""
Unit Tests for Database Integration

Comprehensive tests for database operations including BigQuery, repositories, and data models.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import json
from uuid import uuid4

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, insert, update, delete
from google.cloud import bigquery
from google.cloud.bigquery import Client as BigQueryClient, Table, Dataset

from pattern_mining.database.connection import DatabaseConnection, get_database_session
from pattern_mining.database.bigquery import BigQueryManager, BigQueryMLManager
from pattern_mining.database.models import (
    Base, PatternRecord, AnalysisRecord, RepositoryRecord, ModelRecord,
    TrainingJobRecord, BatchJobRecord
)
from pattern_mining.database.repositories.pattern_repository import PatternRepository
from pattern_mining.database.repositories.analysis_repository import AnalysisRepository
from pattern_mining.database.repositories.model_repository import ModelRepository
from pattern_mining.database.query_builder import QueryBuilder
from pattern_mining.models.patterns import PatternType, SeverityLevel, DetectionType
from pattern_mining.models.database import (
    PatternFilter, AnalysisFilter, TimeRangeFilter, PaginationConfig
)
from tests.utils.factories import (
    PatternRecordFactory, AnalysisRecordFactory, RepositoryRecordFactory,
    ModelRecordFactory
)


class TestDatabaseConnection:
    """Test database connection functionality."""
    
    @pytest.fixture
    def db_connection(self):
        """Create database connection instance."""
        return DatabaseConnection(
            database_url="sqlite+aiosqlite:///:memory:",
            pool_size=10,
            max_overflow=20,
            pool_timeout=30,
            pool_recycle=3600
        )
    
    def test_database_connection_initialization(self, db_connection):
        """Test database connection initialization."""
        assert db_connection is not None
        assert hasattr(db_connection, 'database_url')
        assert hasattr(db_connection, 'engine')
        assert hasattr(db_connection, 'session_factory')
        assert db_connection.database_url == "sqlite+aiosqlite:///:memory:"
    
    @pytest.mark.asyncio
    async def test_create_engine_success(self, db_connection):
        """Test successful engine creation."""
        engine = await db_connection.create_engine()
        
        assert engine is not None
        assert hasattr(engine, 'connect')
        assert hasattr(engine, 'execute')
    
    @pytest.mark.asyncio
    async def test_create_tables_success(self, db_connection):
        """Test successful table creation."""
        engine = await db_connection.create_engine()
        
        result = await db_connection.create_tables(engine)
        
        assert result is True
        
        # Verify tables were created
        async with engine.connect() as conn:
            # Check if pattern_records table exists
            result = await conn.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='pattern_records'"
            )
            tables = result.fetchall()
            assert len(tables) == 1
    
    @pytest.mark.asyncio
    async def test_get_session_success(self, db_connection):
        """Test successful session creation."""
        engine = await db_connection.create_engine()
        await db_connection.create_tables(engine)
        
        session_factory = db_connection.create_session_factory(engine)
        
        async with session_factory() as session:
            assert session is not None
            assert isinstance(session, AsyncSession)
            assert hasattr(session, 'execute')
            assert hasattr(session, 'commit')
            assert hasattr(session, 'rollback')
    
    @pytest.mark.asyncio
    async def test_connection_health_check(self, db_connection):
        """Test database connection health check."""
        engine = await db_connection.create_engine()
        
        health_status = await db_connection.health_check(engine)
        
        assert health_status is not None
        assert health_status["status"] == "healthy"
        assert "response_time_ms" in health_status
        assert "connection_pool_size" in health_status
    
    @pytest.mark.asyncio
    async def test_connection_error_handling(self, db_connection):
        """Test connection error handling."""
        # Test with invalid database URL
        db_connection.database_url = "invalid://invalid"
        
        with pytest.raises(Exception):
            await db_connection.create_engine()
    
    @pytest.mark.asyncio
    async def test_transaction_handling(self, db_connection):
        """Test transaction handling."""
        engine = await db_connection.create_engine()
        await db_connection.create_tables(engine)
        session_factory = db_connection.create_session_factory(engine)
        
        async with session_factory() as session:
            # Test successful transaction
            try:
                pattern_record = PatternRecord(
                    id="test-pattern",
                    repository_id="test-repo",
                    pattern_name="Test Pattern",
                    pattern_type=PatternType.DESIGN_PATTERN.value,
                    severity=SeverityLevel.LOW.value,
                    confidence=0.8,
                    file_path="test.py",
                    line_number=10,
                    description="Test pattern",
                    detection_method=DetectionType.ML_INFERENCE.value
                )
                
                session.add(pattern_record)
                await session.commit()
                
                # Verify record was created
                result = await session.execute(
                    select(PatternRecord).where(PatternRecord.id == "test-pattern")
                )
                retrieved_record = result.scalar_one_or_none()
                assert retrieved_record is not None
                assert retrieved_record.pattern_name == "Test Pattern"
                
            except Exception as e:
                await session.rollback()
                raise e
    
    @pytest.mark.asyncio
    async def test_connection_pooling(self, db_connection):
        """Test connection pooling functionality."""
        engine = await db_connection.create_engine()
        await db_connection.create_tables(engine)
        session_factory = db_connection.create_session_factory(engine)
        
        # Test multiple concurrent connections
        async def test_concurrent_session():
            async with session_factory() as session:
                result = await session.execute("SELECT 1")
                return result.scalar()
        
        # Run multiple concurrent sessions
        tasks = [test_concurrent_session() for _ in range(5)]
        results = await asyncio.gather(*tasks)
        
        assert all(result == 1 for result in results)


class TestBigQueryManager:
    """Test BigQuery manager functionality."""
    
    @pytest.fixture
    def bigquery_manager(self):
        """Create BigQuery manager instance."""
        return BigQueryManager(
            project_id="test-project",
            dataset_id="test_dataset",
            location="US"
        )
    
    @pytest.fixture
    def mock_bigquery_client(self):
        """Mock BigQuery client."""
        client = Mock(spec=BigQueryClient)
        client.project = "test-project"
        return client
    
    def test_bigquery_manager_initialization(self, bigquery_manager):
        """Test BigQuery manager initialization."""
        assert bigquery_manager is not None
        assert hasattr(bigquery_manager, 'project_id')
        assert hasattr(bigquery_manager, 'dataset_id')
        assert hasattr(bigquery_manager, 'location')
        assert hasattr(bigquery_manager, 'client')
        assert bigquery_manager.project_id == "test-project"
        assert bigquery_manager.dataset_id == "test_dataset"
    
    @pytest.mark.asyncio
    async def test_create_dataset_success(self, bigquery_manager, mock_bigquery_client):
        """Test successful dataset creation."""
        bigquery_manager.client = mock_bigquery_client
        
        mock_dataset = Mock(spec=Dataset)
        mock_bigquery_client.create_dataset.return_value = mock_dataset
        
        result = await bigquery_manager.create_dataset()
        
        assert result is True
        mock_bigquery_client.create_dataset.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_table_success(self, bigquery_manager, mock_bigquery_client):
        """Test successful table creation."""
        bigquery_manager.client = mock_bigquery_client
        
        mock_table = Mock(spec=Table)
        mock_bigquery_client.create_table.return_value = mock_table
        
        table_schema = [
            {"name": "pattern_id", "type": "STRING", "mode": "REQUIRED"},
            {"name": "pattern_name", "type": "STRING", "mode": "REQUIRED"},
            {"name": "confidence", "type": "FLOAT", "mode": "REQUIRED"},
            {"name": "created_at", "type": "TIMESTAMP", "mode": "REQUIRED"}
        ]
        
        result = await bigquery_manager.create_table("patterns", table_schema)
        
        assert result is True
        mock_bigquery_client.create_table.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_insert_data_success(self, bigquery_manager, mock_bigquery_client):
        """Test successful data insertion."""
        bigquery_manager.client = mock_bigquery_client
        
        mock_job = Mock()
        mock_job.result.return_value = None
        mock_bigquery_client.load_table_from_json.return_value = mock_job
        
        data = [
            {
                "pattern_id": "pattern-1",
                "pattern_name": "Test Pattern",
                "confidence": 0.85,
                "created_at": datetime.utcnow().isoformat()
            },
            {
                "pattern_id": "pattern-2",
                "pattern_name": "Another Pattern",
                "confidence": 0.92,
                "created_at": datetime.utcnow().isoformat()
            }
        ]
        
        result = await bigquery_manager.insert_data("patterns", data)
        
        assert result is True
        mock_bigquery_client.load_table_from_json.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_query_data_success(self, bigquery_manager, mock_bigquery_client):
        """Test successful data querying."""
        bigquery_manager.client = mock_bigquery_client
        
        mock_job = Mock()
        mock_job.result.return_value = [
            {"pattern_id": "pattern-1", "pattern_name": "Test Pattern", "confidence": 0.85},
            {"pattern_id": "pattern-2", "pattern_name": "Another Pattern", "confidence": 0.92}
        ]
        mock_bigquery_client.query.return_value = mock_job
        
        query = "SELECT * FROM `test-project.test_dataset.patterns` WHERE confidence > 0.8"
        
        result = await bigquery_manager.query_data(query)
        
        assert result is not None
        assert len(result) == 2
        assert result[0]["pattern_id"] == "pattern-1"
        assert result[1]["pattern_id"] == "pattern-2"
        mock_bigquery_client.query.assert_called_once_with(query)
    
    @pytest.mark.asyncio
    async def test_batch_insert_success(self, bigquery_manager, mock_bigquery_client):
        """Test successful batch data insertion."""
        bigquery_manager.client = mock_bigquery_client
        
        mock_job = Mock()
        mock_job.result.return_value = None
        mock_bigquery_client.load_table_from_json.return_value = mock_job
        
        # Large batch of data
        data = [
            {
                "pattern_id": f"pattern-{i}",
                "pattern_name": f"Pattern {i}",
                "confidence": 0.8 + (i % 20) * 0.01,
                "created_at": datetime.utcnow().isoformat()
            }
            for i in range(1000)
        ]
        
        result = await bigquery_manager.batch_insert("patterns", data, batch_size=100)
        
        assert result is True
        # Should have made 10 calls (1000 records / 100 batch size)
        assert mock_bigquery_client.load_table_from_json.call_count == 10
    
    @pytest.mark.asyncio
    async def test_streaming_insert_success(self, bigquery_manager, mock_bigquery_client):
        """Test successful streaming data insertion."""
        bigquery_manager.client = mock_bigquery_client
        
        mock_table = Mock()
        mock_table.insert_rows_json.return_value = []  # Empty list means success
        mock_bigquery_client.get_table.return_value = mock_table
        
        data = [
            {
                "pattern_id": "pattern-1",
                "pattern_name": "Streaming Pattern",
                "confidence": 0.88,
                "created_at": datetime.utcnow().isoformat()
            }
        ]
        
        result = await bigquery_manager.streaming_insert("patterns", data)
        
        assert result is True
        mock_table.insert_rows_json.assert_called_once_with(data)
    
    @pytest.mark.asyncio
    async def test_error_handling(self, bigquery_manager, mock_bigquery_client):
        """Test error handling in BigQuery operations."""
        bigquery_manager.client = mock_bigquery_client
        
        # Test query error
        mock_bigquery_client.query.side_effect = Exception("Query failed")
        
        with pytest.raises(Exception, match="Query failed"):
            await bigquery_manager.query_data("SELECT * FROM invalid_table")
        
        # Test insert error
        mock_bigquery_client.load_table_from_json.side_effect = Exception("Insert failed")
        
        with pytest.raises(Exception, match="Insert failed"):
            await bigquery_manager.insert_data("patterns", [{"test": "data"}])


class TestPatternRepository:
    """Test pattern repository functionality."""
    
    @pytest.fixture
    def pattern_repository(self, async_db_session):
        """Create pattern repository instance."""
        return PatternRepository(async_db_session)
    
    @pytest.mark.asyncio
    async def test_create_pattern_success(self, pattern_repository):
        """Test successful pattern creation."""
        pattern_data = {
            "id": "test-pattern-1",
            "repository_id": "test-repo",
            "pattern_name": "Test Pattern",
            "pattern_type": PatternType.DESIGN_PATTERN.value,
            "severity": SeverityLevel.MEDIUM.value,
            "confidence": 0.85,
            "file_path": "src/main.py",
            "line_number": 15,
            "description": "Test pattern description",
            "detection_method": DetectionType.ML_INFERENCE.value,
            "context": {"function_name": "test_func"},
            "metadata": {"model_version": "1.0.0"}
        }
        
        result = await pattern_repository.create_pattern(pattern_data)
        
        assert result is not None
        assert result.id == "test-pattern-1"
        assert result.pattern_name == "Test Pattern"
        assert result.pattern_type == PatternType.DESIGN_PATTERN.value
        assert result.confidence == 0.85
        assert result.context["function_name"] == "test_func"
    
    @pytest.mark.asyncio
    async def test_get_pattern_by_id_success(self, pattern_repository):
        """Test successful pattern retrieval by ID."""
        # Create a pattern first
        pattern_data = {
            "id": "test-pattern-2",
            "repository_id": "test-repo",
            "pattern_name": "Another Pattern",
            "pattern_type": PatternType.SECURITY_ISSUE.value,
            "severity": SeverityLevel.HIGH.value,
            "confidence": 0.92,
            "file_path": "src/security.py",
            "line_number": 25,
            "description": "Security issue pattern",
            "detection_method": DetectionType.HEURISTIC.value
        }
        
        created_pattern = await pattern_repository.create_pattern(pattern_data)
        
        # Retrieve the pattern
        retrieved_pattern = await pattern_repository.get_pattern_by_id("test-pattern-2")
        
        assert retrieved_pattern is not None
        assert retrieved_pattern.id == "test-pattern-2"
        assert retrieved_pattern.pattern_name == "Another Pattern"
        assert retrieved_pattern.pattern_type == PatternType.SECURITY_ISSUE.value
        assert retrieved_pattern.confidence == 0.92
    
    @pytest.mark.asyncio
    async def test_get_pattern_by_id_not_found(self, pattern_repository):
        """Test pattern retrieval for non-existent ID."""
        result = await pattern_repository.get_pattern_by_id("non-existent-pattern")
        assert result is None
    
    @pytest.mark.asyncio
    async def test_get_patterns_by_repository_success(self, pattern_repository):
        """Test successful pattern retrieval by repository."""
        # Create multiple patterns for the same repository
        patterns_data = [
            {
                "id": f"repo-pattern-{i}",
                "repository_id": "test-repo-patterns",
                "pattern_name": f"Pattern {i}",
                "pattern_type": PatternType.DESIGN_PATTERN.value,
                "severity": SeverityLevel.LOW.value,
                "confidence": 0.8 + i * 0.05,
                "file_path": f"src/file_{i}.py",
                "line_number": 10 + i,
                "description": f"Pattern {i} description",
                "detection_method": DetectionType.ML_INFERENCE.value
            }
            for i in range(5)
        ]
        
        # Create patterns
        for pattern_data in patterns_data:
            await pattern_repository.create_pattern(pattern_data)
        
        # Retrieve patterns
        patterns = await pattern_repository.get_patterns_by_repository("test-repo-patterns")
        
        assert patterns is not None
        assert len(patterns) == 5
        assert all(p.repository_id == "test-repo-patterns" for p in patterns)
        assert [p.pattern_name for p in patterns] == [f"Pattern {i}" for i in range(5)]
    
    @pytest.mark.asyncio
    async def test_get_patterns_with_filter_success(self, pattern_repository):
        """Test pattern retrieval with filters."""
        # Create patterns with different attributes
        patterns_data = [
            {
                "id": "filter-pattern-1",
                "repository_id": "filter-repo",
                "pattern_name": "High Confidence Pattern",
                "pattern_type": PatternType.DESIGN_PATTERN.value,
                "severity": SeverityLevel.HIGH.value,
                "confidence": 0.95,
                "file_path": "src/high.py",
                "line_number": 10,
                "description": "High confidence pattern",
                "detection_method": DetectionType.ML_INFERENCE.value
            },
            {
                "id": "filter-pattern-2",
                "repository_id": "filter-repo",
                "pattern_name": "Low Confidence Pattern",
                "pattern_type": PatternType.CODE_SMELL.value,
                "severity": SeverityLevel.LOW.value,
                "confidence": 0.65,
                "file_path": "src/low.py",
                "line_number": 20,
                "description": "Low confidence pattern",
                "detection_method": DetectionType.HEURISTIC.value
            },
            {
                "id": "filter-pattern-3",
                "repository_id": "filter-repo",
                "pattern_name": "Security Pattern",
                "pattern_type": PatternType.SECURITY_ISSUE.value,
                "severity": SeverityLevel.CRITICAL.value,
                "confidence": 0.88,
                "file_path": "src/security.py",
                "line_number": 30,
                "description": "Security issue pattern",
                "detection_method": DetectionType.ML_INFERENCE.value
            }
        ]
        
        # Create patterns
        for pattern_data in patterns_data:
            await pattern_repository.create_pattern(pattern_data)
        
        # Test filter by confidence
        pattern_filter = PatternFilter(
            confidence_min=0.8,
            confidence_max=1.0
        )
        
        filtered_patterns = await pattern_repository.get_patterns_with_filter("filter-repo", pattern_filter)
        
        assert len(filtered_patterns) == 2  # Should get patterns with confidence >= 0.8
        assert all(p.confidence >= 0.8 for p in filtered_patterns)
        
        # Test filter by pattern type
        pattern_filter = PatternFilter(
            pattern_types=[PatternType.SECURITY_ISSUE]
        )
        
        filtered_patterns = await pattern_repository.get_patterns_with_filter("filter-repo", pattern_filter)
        
        assert len(filtered_patterns) == 1
        assert filtered_patterns[0].pattern_type == PatternType.SECURITY_ISSUE.value
        
        # Test filter by severity
        pattern_filter = PatternFilter(
            severity_levels=[SeverityLevel.HIGH, SeverityLevel.CRITICAL]
        )
        
        filtered_patterns = await pattern_repository.get_patterns_with_filter("filter-repo", pattern_filter)
        
        assert len(filtered_patterns) == 2
        assert all(p.severity in [SeverityLevel.HIGH.value, SeverityLevel.CRITICAL.value] for p in filtered_patterns)
    
    @pytest.mark.asyncio
    async def test_get_patterns_with_pagination(self, pattern_repository):
        """Test pattern retrieval with pagination."""
        # Create many patterns
        patterns_data = [
            {
                "id": f"page-pattern-{i}",
                "repository_id": "page-repo",
                "pattern_name": f"Pattern {i}",
                "pattern_type": PatternType.DESIGN_PATTERN.value,
                "severity": SeverityLevel.LOW.value,
                "confidence": 0.8,
                "file_path": f"src/file_{i}.py",
                "line_number": 10,
                "description": f"Pattern {i} description",
                "detection_method": DetectionType.ML_INFERENCE.value
            }
            for i in range(25)
        ]
        
        # Create patterns
        for pattern_data in patterns_data:
            await pattern_repository.create_pattern(pattern_data)
        
        # Test pagination
        pagination_config = PaginationConfig(
            page=1,
            page_size=10,
            sort_by="created_at",
            sort_order="desc"
        )
        
        result = await pattern_repository.get_patterns_paginated("page-repo", pagination_config)
        
        assert result is not None
        assert "patterns" in result
        assert "total" in result
        assert "page" in result
        assert "page_size" in result
        assert "has_next" in result
        assert "has_previous" in result
        
        assert len(result["patterns"]) == 10
        assert result["total"] == 25
        assert result["page"] == 1
        assert result["page_size"] == 10
        assert result["has_next"] is True
        assert result["has_previous"] is False
        
        # Test second page
        pagination_config.page = 2
        result = await pattern_repository.get_patterns_paginated("page-repo", pagination_config)
        
        assert len(result["patterns"]) == 10
        assert result["page"] == 2
        assert result["has_next"] is True
        assert result["has_previous"] is True
        
        # Test last page
        pagination_config.page = 3
        result = await pattern_repository.get_patterns_paginated("page-repo", pagination_config)
        
        assert len(result["patterns"]) == 5  # Remaining patterns
        assert result["page"] == 3
        assert result["has_next"] is False
        assert result["has_previous"] is True
    
    @pytest.mark.asyncio
    async def test_update_pattern_success(self, pattern_repository):
        """Test successful pattern update."""
        # Create a pattern
        pattern_data = {
            "id": "update-pattern",
            "repository_id": "update-repo",
            "pattern_name": "Original Pattern",
            "pattern_type": PatternType.DESIGN_PATTERN.value,
            "severity": SeverityLevel.LOW.value,
            "confidence": 0.8,
            "file_path": "src/original.py",
            "line_number": 10,
            "description": "Original description",
            "detection_method": DetectionType.ML_INFERENCE.value
        }
        
        created_pattern = await pattern_repository.create_pattern(pattern_data)
        
        # Update the pattern
        updates = {
            "pattern_name": "Updated Pattern",
            "confidence": 0.92,
            "description": "Updated description"
        }
        
        updated_pattern = await pattern_repository.update_pattern("update-pattern", updates)
        
        assert updated_pattern is not None
        assert updated_pattern.pattern_name == "Updated Pattern"
        assert updated_pattern.confidence == 0.92
        assert updated_pattern.description == "Updated description"
        assert updated_pattern.pattern_type == PatternType.DESIGN_PATTERN.value  # Unchanged
    
    @pytest.mark.asyncio
    async def test_delete_pattern_success(self, pattern_repository):
        """Test successful pattern deletion."""
        # Create a pattern
        pattern_data = {
            "id": "delete-pattern",
            "repository_id": "delete-repo",
            "pattern_name": "Delete Pattern",
            "pattern_type": PatternType.DESIGN_PATTERN.value,
            "severity": SeverityLevel.LOW.value,
            "confidence": 0.8,
            "file_path": "src/delete.py",
            "line_number": 10,
            "description": "Pattern to delete",
            "detection_method": DetectionType.ML_INFERENCE.value
        }
        
        created_pattern = await pattern_repository.create_pattern(pattern_data)
        
        # Delete the pattern
        result = await pattern_repository.delete_pattern("delete-pattern")
        
        assert result is True
        
        # Verify pattern is deleted
        deleted_pattern = await pattern_repository.get_pattern_by_id("delete-pattern")
        assert deleted_pattern is None
    
    @pytest.mark.asyncio
    async def test_get_pattern_statistics_success(self, pattern_repository):
        """Test pattern statistics retrieval."""
        # Create patterns with different attributes
        patterns_data = [
            {
                "id": f"stats-pattern-{i}",
                "repository_id": "stats-repo",
                "pattern_name": f"Pattern {i}",
                "pattern_type": [PatternType.DESIGN_PATTERN, PatternType.CODE_SMELL, PatternType.SECURITY_ISSUE][i % 3].value,
                "severity": [SeverityLevel.LOW, SeverityLevel.MEDIUM, SeverityLevel.HIGH][i % 3].value,
                "confidence": 0.7 + (i % 3) * 0.1,
                "file_path": f"src/file_{i}.py",
                "line_number": 10 + i,
                "description": f"Pattern {i} description",
                "detection_method": [DetectionType.ML_INFERENCE, DetectionType.HEURISTIC][i % 2].value
            }
            for i in range(9)  # 3 of each type, severity, etc.
        ]
        
        # Create patterns
        for pattern_data in patterns_data:
            await pattern_repository.create_pattern(pattern_data)
        
        # Get statistics
        stats = await pattern_repository.get_pattern_statistics("stats-repo")
        
        assert stats is not None
        assert "total_patterns" in stats
        assert "by_type" in stats
        assert "by_severity" in stats
        assert "by_detection_method" in stats
        assert "average_confidence" in stats
        
        assert stats["total_patterns"] == 9
        assert stats["by_type"][PatternType.DESIGN_PATTERN.value] == 3
        assert stats["by_type"][PatternType.CODE_SMELL.value] == 3
        assert stats["by_type"][PatternType.SECURITY_ISSUE.value] == 3
        assert stats["by_severity"][SeverityLevel.LOW.value] == 3
        assert stats["by_severity"][SeverityLevel.MEDIUM.value] == 3
        assert stats["by_severity"][SeverityLevel.HIGH.value] == 3
        assert stats["by_detection_method"][DetectionType.ML_INFERENCE.value] == 5
        assert stats["by_detection_method"][DetectionType.HEURISTIC.value] == 4
        assert abs(stats["average_confidence"] - 0.8) < 0.01  # Should be approximately 0.8


class TestQueryBuilder:
    """Test query builder functionality."""
    
    @pytest.fixture
    def query_builder(self):
        """Create query builder instance."""
        return QueryBuilder()
    
    def test_query_builder_initialization(self, query_builder):
        """Test query builder initialization."""
        assert query_builder is not None
        assert hasattr(query_builder, 'query')
        assert hasattr(query_builder, 'conditions')
        assert hasattr(query_builder, 'joins')
        assert hasattr(query_builder, 'order_by')
        assert hasattr(query_builder, 'limit_value')
        assert hasattr(query_builder, 'offset_value')
    
    def test_select_query_building(self, query_builder):
        """Test SELECT query building."""
        query = (query_builder
                .select(["id", "pattern_name", "confidence"])
                .from_table("pattern_records")
                .where("confidence > :confidence")
                .order_by("confidence", "desc")
                .limit(10)
                .offset(20)
                .build())
        
        assert "SELECT id, pattern_name, confidence" in query
        assert "FROM pattern_records" in query
        assert "WHERE confidence > :confidence" in query
        assert "ORDER BY confidence desc" in query
        assert "LIMIT 10" in query
        assert "OFFSET 20" in query
    
    def test_insert_query_building(self, query_builder):
        """Test INSERT query building."""
        query = (query_builder
                .insert_into("pattern_records")
                .values({
                    "id": ":id",
                    "pattern_name": ":pattern_name",
                    "confidence": ":confidence"
                })
                .build())
        
        assert "INSERT INTO pattern_records" in query
        assert "VALUES (:id, :pattern_name, :confidence)" in query
    
    def test_update_query_building(self, query_builder):
        """Test UPDATE query building."""
        query = (query_builder
                .update("pattern_records")
                .set({
                    "pattern_name": ":pattern_name",
                    "confidence": ":confidence"
                })
                .where("id = :id")
                .build())
        
        assert "UPDATE pattern_records" in query
        assert "SET pattern_name = :pattern_name, confidence = :confidence" in query
        assert "WHERE id = :id" in query
    
    def test_delete_query_building(self, query_builder):
        """Test DELETE query building."""
        query = (query_builder
                .delete_from("pattern_records")
                .where("id = :id")
                .build())
        
        assert "DELETE FROM pattern_records" in query
        assert "WHERE id = :id" in query
    
    def test_complex_query_building(self, query_builder):
        """Test complex query building with joins."""
        query = (query_builder
                .select([
                    "pr.id",
                    "pr.pattern_name",
                    "pr.confidence",
                    "ar.quality_score"
                ])
                .from_table("pattern_records pr")
                .join("analysis_records ar", "pr.repository_id = ar.repository_id")
                .where("pr.confidence > :confidence")
                .where("ar.quality_score > :quality_score")
                .order_by("pr.confidence", "desc")
                .limit(50)
                .build())
        
        assert "SELECT pr.id, pr.pattern_name, pr.confidence, ar.quality_score" in query
        assert "FROM pattern_records pr" in query
        assert "JOIN analysis_records ar ON pr.repository_id = ar.repository_id" in query
        assert "WHERE pr.confidence > :confidence AND ar.quality_score > :quality_score" in query
        assert "ORDER BY pr.confidence desc" in query
        assert "LIMIT 50" in query
    
    def test_subquery_building(self, query_builder):
        """Test subquery building."""
        subquery = (QueryBuilder()
                    .select(["repository_id"])
                    .from_table("analysis_records")
                    .where("quality_score > :quality_threshold")
                    .build())
        
        query = (query_builder
                .select(["*"])
                .from_table("pattern_records")
                .where(f"repository_id IN ({subquery})")
                .build())
        
        assert "SELECT *" in query
        assert "FROM pattern_records" in query
        assert "WHERE repository_id IN" in query
        assert "SELECT repository_id FROM analysis_records WHERE quality_score > :quality_threshold" in query
    
    def test_aggregation_query_building(self, query_builder):
        """Test aggregation query building."""
        query = (query_builder
                .select([
                    "pattern_type",
                    "COUNT(*) as count",
                    "AVG(confidence) as avg_confidence",
                    "MAX(confidence) as max_confidence"
                ])
                .from_table("pattern_records")
                .where("repository_id = :repository_id")
                .group_by(["pattern_type"])
                .having("COUNT(*) > :min_count")
                .order_by("count", "desc")
                .build())
        
        assert "SELECT pattern_type, COUNT(*) as count, AVG(confidence) as avg_confidence, MAX(confidence) as max_confidence" in query
        assert "FROM pattern_records" in query
        assert "WHERE repository_id = :repository_id" in query
        assert "GROUP BY pattern_type" in query
        assert "HAVING COUNT(*) > :min_count" in query
        assert "ORDER BY count desc" in query
    
    def test_query_parameters_handling(self, query_builder):
        """Test query parameters handling."""
        query = (query_builder
                .select(["*"])
                .from_table("pattern_records")
                .where("confidence BETWEEN :min_confidence AND :max_confidence")
                .where("pattern_type IN :pattern_types")
                .where("created_at >= :start_date")
                .build())
        
        parameters = {
            "min_confidence": 0.7,
            "max_confidence": 1.0,
            "pattern_types": ["DESIGN_PATTERN", "SECURITY_ISSUE"],
            "start_date": "2025-01-01"
        }
        
        assert "WHERE confidence BETWEEN :min_confidence AND :max_confidence" in query
        assert "AND pattern_type IN :pattern_types" in query
        assert "AND created_at >= :start_date" in query
        
        # Test parameter binding
        bound_query = query_builder.bind_parameters(parameters)
        assert bound_query is not None
    
    def test_query_validation(self, query_builder):
        """Test query validation."""
        # Test valid query
        valid_query = (query_builder
                      .select(["id", "pattern_name"])
                      .from_table("pattern_records")
                      .where("confidence > 0.5")
                      .build())
        
        assert query_builder.validate_query(valid_query) is True
        
        # Test invalid query (missing FROM clause)
        with pytest.raises(ValueError):
            query_builder.select(["id"]).where("confidence > 0.5").build()
    
    def test_query_optimization(self, query_builder):
        """Test query optimization."""
        # Test index hint
        query = (query_builder
                .select(["*"])
                .from_table("pattern_records")
                .where("confidence > :confidence")
                .use_index("idx_confidence")
                .build())
        
        assert "USE INDEX (idx_confidence)" in query
        
        # Test query caching
        query_builder.enable_caching = True
        query1 = query_builder.select(["*"]).from_table("pattern_records").build()
        query2 = query_builder.select(["*"]).from_table("pattern_records").build()
        
        # Should return same query from cache
        assert query1 == query2