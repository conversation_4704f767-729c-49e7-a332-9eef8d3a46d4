"""
Unit Tests for Pydantic Models

Comprehensive tests for all Pydantic models including validation, serialization, and edge cases.
"""

import pytest
from datetime import datetime, timedelta
from typing import Dict, Any, List
from uuid import uuid4, UUID
from decimal import Decimal
import json

from pydantic import ValidationError

from pattern_mining.models.api import (
    PatternDetectionRequest, BatchDetectionRequest, DetectionConfig,
    PatternDetectionResponse, PatternSummary, StreamingDetectionRequest,
    RepositoryAnalysisRequest, RepositoryAnalysisResponse, FileAnalysis,
    QualityMetrics, Recommendation, RepositoryComparison, AnalyticsReport,
    TrendAnalysis, PerformanceMetrics, AnalyticsQuery
)
from pattern_mining.models.patterns import (
    DetectedPattern, PatternType, SeverityLevel, DetectionType, PatternLocation,
    PatternContext, PatternMetadata, PatternStatistics
)
from pattern_mining.models.database import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>, AnalysisRecord, RepositoryRecord, ModelRecord,
    TrainingJobRecord, BatchJobRecord
)
from pattern_mining.models.ml import (
    ModelConfig, TrainingConfig, PredictionRequest, PredictionResponse,
    ModelMetrics, TrainingMetrics, ModelInfo, InferenceConfig
)
from pattern_mining.models.health import (
    HealthStatus, HealthCheck, ComponentHealth, SystemMetrics
)
from pattern_mining.models.common import (
    PaginatedResponse, TimeRange, SortOrder, FilterConfig
)


class TestPatternModels:
    """Test pattern-related models."""
    
    def test_detected_pattern_valid(self):
        """Test valid DetectedPattern creation."""
        pattern = DetectedPattern(
            pattern_id="test-pattern-123",
            pattern_name="Test Pattern",
            pattern_type=PatternType.DESIGN_PATTERN,
            severity=SeverityLevel.MEDIUM,
            confidence=0.85,
            location={
                "file": "test.py",
                "line": 10,
                "column": 5,
                "end_line": 15,
                "end_column": 20
            },
            description="Test pattern description",
            detection_method=DetectionType.ML_INFERENCE,
            context={"function_name": "test_func"},
            metadata={"model_version": "1.0.0"}
        )
        
        assert pattern.pattern_id == "test-pattern-123"
        assert pattern.pattern_name == "Test Pattern"
        assert pattern.pattern_type == PatternType.DESIGN_PATTERN
        assert pattern.severity == SeverityLevel.MEDIUM
        assert pattern.confidence == 0.85
        assert pattern.location["file"] == "test.py"
        assert pattern.location["line"] == 10
        assert pattern.detection_method == DetectionType.ML_INFERENCE
        assert pattern.context["function_name"] == "test_func"
        assert pattern.metadata["model_version"] == "1.0.0"
    
    def test_detected_pattern_invalid_confidence(self):
        """Test DetectedPattern with invalid confidence."""
        with pytest.raises(ValidationError) as exc_info:
            DetectedPattern(
                pattern_id="test-pattern-123",
                pattern_name="Test Pattern",
                pattern_type=PatternType.DESIGN_PATTERN,
                severity=SeverityLevel.MEDIUM,
                confidence=1.5,  # Invalid: > 1.0
                location={"file": "test.py", "line": 10, "column": 5},
                description="Test pattern description",
                detection_method=DetectionType.ML_INFERENCE
            )
        
        assert "confidence" in str(exc_info.value)
    
    def test_detected_pattern_missing_required_fields(self):
        """Test DetectedPattern with missing required fields."""
        with pytest.raises(ValidationError) as exc_info:
            DetectedPattern(
                pattern_id="test-pattern-123",
                # Missing pattern_name
                pattern_type=PatternType.DESIGN_PATTERN,
                severity=SeverityLevel.MEDIUM,
                confidence=0.85,
                location={"file": "test.py", "line": 10, "column": 5},
                description="Test pattern description",
                detection_method=DetectionType.ML_INFERENCE
            )
        
        assert "pattern_name" in str(exc_info.value)
    
    def test_detected_pattern_serialization(self):
        """Test DetectedPattern serialization/deserialization."""
        pattern = DetectedPattern(
            pattern_id="test-pattern-123",
            pattern_name="Test Pattern",
            pattern_type=PatternType.DESIGN_PATTERN,
            severity=SeverityLevel.MEDIUM,
            confidence=0.85,
            location={"file": "test.py", "line": 10, "column": 5},
            description="Test pattern description",
            detection_method=DetectionType.ML_INFERENCE,
            context={"function_name": "test_func"},
            metadata={"model_version": "1.0.0"}
        )
        
        # Serialize to dict
        pattern_dict = pattern.model_dump()
        assert pattern_dict["pattern_id"] == "test-pattern-123"
        assert pattern_dict["pattern_type"] == "DESIGN_PATTERN"
        assert pattern_dict["severity"] == "MEDIUM"
        
        # Deserialize from dict
        pattern_restored = DetectedPattern.model_validate(pattern_dict)
        assert pattern_restored.pattern_id == pattern.pattern_id
        assert pattern_restored.pattern_type == pattern.pattern_type
        assert pattern_restored.severity == pattern.severity
        assert pattern_restored.confidence == pattern.confidence
    
    def test_detected_pattern_json_serialization(self):
        """Test DetectedPattern JSON serialization."""
        pattern = DetectedPattern(
            pattern_id="test-pattern-123",
            pattern_name="Test Pattern",
            pattern_type=PatternType.DESIGN_PATTERN,
            severity=SeverityLevel.MEDIUM,
            confidence=0.85,
            location={"file": "test.py", "line": 10, "column": 5},
            description="Test pattern description",
            detection_method=DetectionType.ML_INFERENCE,
            created_at=datetime.utcnow()
        )
        
        # Serialize to JSON
        json_str = pattern.model_dump_json()
        assert isinstance(json_str, str)
        
        # Deserialize from JSON
        json_data = json.loads(json_str)
        pattern_restored = DetectedPattern.model_validate(json_data)
        assert pattern_restored.pattern_id == pattern.pattern_id
        assert pattern_restored.pattern_type == pattern.pattern_type
        assert pattern_restored.severity == pattern.severity
    
    def test_pattern_location_validation(self):
        """Test PatternLocation validation."""
        # Valid location
        location = PatternLocation(
            file="test.py",
            line=10,
            column=5,
            end_line=15,
            end_column=20
        )
        assert location.file == "test.py"
        assert location.line == 10
        assert location.column == 5
        assert location.end_line == 15
        assert location.end_column == 20
        
        # Invalid location (negative line)
        with pytest.raises(ValidationError):
            PatternLocation(
                file="test.py",
                line=-1,  # Invalid
                column=5
            )
        
        # Invalid location (end_line < line)
        with pytest.raises(ValidationError):
            PatternLocation(
                file="test.py",
                line=15,
                column=5,
                end_line=10,  # Invalid: end_line < line
                end_column=20
            )
    
    def test_pattern_statistics_creation(self):
        """Test PatternStatistics creation and calculation."""
        patterns = [
            DetectedPattern(
                pattern_id="pattern-1",
                pattern_name="Pattern 1",
                pattern_type=PatternType.DESIGN_PATTERN,
                severity=SeverityLevel.LOW,
                confidence=0.8,
                location={"file": "test.py", "line": 10, "column": 5},
                description="Pattern 1",
                detection_method=DetectionType.ML_INFERENCE
            ),
            DetectedPattern(
                pattern_id="pattern-2",
                pattern_name="Pattern 2",
                pattern_type=PatternType.SECURITY_ISSUE,
                severity=SeverityLevel.HIGH,
                confidence=0.95,
                location={"file": "test.py", "line": 20, "column": 5},
                description="Pattern 2",
                detection_method=DetectionType.HEURISTIC
            )
        ]
        
        stats = PatternStatistics.from_patterns(patterns)
        
        assert stats.total_patterns == 2
        assert stats.unique_pattern_types == 2
        assert stats.by_severity[SeverityLevel.LOW] == 1
        assert stats.by_severity[SeverityLevel.HIGH] == 1
        assert stats.by_type[PatternType.DESIGN_PATTERN] == 1
        assert stats.by_type[PatternType.SECURITY_ISSUE] == 1
        assert stats.by_detection_method[DetectionType.ML_INFERENCE] == 1
        assert stats.by_detection_method[DetectionType.HEURISTIC] == 1
        assert stats.average_confidence == 0.875  # (0.8 + 0.95) / 2


class TestAPIModels:
    """Test API request/response models."""
    
    def test_pattern_detection_request_valid(self):
        """Test valid PatternDetectionRequest creation."""
        request = PatternDetectionRequest(
            repository_id="repo-123",
            ast_data={"type": "Module", "children": []},
            code_content="print('hello')",
            file_path="test.py",
            language="python",
            detection_config=DetectionConfig(
                confidence_threshold=0.7,
                enable_ml_models=True,
                enable_heuristic_detection=True
            )
        )
        
        assert request.repository_id == "repo-123"
        assert request.ast_data["type"] == "Module"
        assert request.code_content == "print('hello')"
        assert request.file_path == "test.py"
        assert request.language == "python"
        assert request.detection_config.confidence_threshold == 0.7
        assert request.detection_config.enable_ml_models is True
    
    def test_pattern_detection_request_invalid_language(self):
        """Test PatternDetectionRequest with invalid language."""
        with pytest.raises(ValidationError) as exc_info:
            PatternDetectionRequest(
                repository_id="repo-123",
                ast_data={"type": "Module", "children": []},
                code_content="print('hello')",
                file_path="test.py",
                language="invalid_language",  # Invalid
                detection_config=DetectionConfig(confidence_threshold=0.7)
            )
        
        assert "language" in str(exc_info.value)
        assert "not supported" in str(exc_info.value)
    
    def test_pattern_detection_request_empty_code(self):
        """Test PatternDetectionRequest with empty code."""
        with pytest.raises(ValidationError) as exc_info:
            PatternDetectionRequest(
                repository_id="repo-123",
                ast_data={"type": "Module", "children": []},
                code_content="",  # Empty
                file_path="test.py",
                language="python",
                detection_config=DetectionConfig(confidence_threshold=0.7)
            )
        
        assert "code_content" in str(exc_info.value)
        assert "empty" in str(exc_info.value)
    
    def test_pattern_detection_request_invalid_ast(self):
        """Test PatternDetectionRequest with invalid AST."""
        with pytest.raises(ValidationError) as exc_info:
            PatternDetectionRequest(
                repository_id="repo-123",
                ast_data={"invalid": "structure"},  # Missing required fields
                code_content="print('hello')",
                file_path="test.py",
                language="python",
                detection_config=DetectionConfig(confidence_threshold=0.7)
            )
        
        assert "ast_data" in str(exc_info.value)
        assert "required fields" in str(exc_info.value)
    
    def test_detection_config_validation(self):
        """Test DetectionConfig validation."""
        # Valid config
        config = DetectionConfig(
            confidence_threshold=0.7,
            max_patterns_per_file=50,
            enable_ml_models=True,
            enable_heuristic_detection=True,
            pattern_types=[PatternType.DESIGN_PATTERN, PatternType.SECURITY_ISSUE]
        )
        
        assert config.confidence_threshold == 0.7
        assert config.max_patterns_per_file == 50
        assert config.enable_ml_models is True
        assert len(config.pattern_types) == 2
        
        # Invalid confidence threshold
        with pytest.raises(ValidationError):
            DetectionConfig(
                confidence_threshold=1.5,  # Invalid: > 1.0
                enable_ml_models=True
            )
        
        # Invalid max patterns
        with pytest.raises(ValidationError):
            DetectionConfig(
                confidence_threshold=0.7,
                max_patterns_per_file=0,  # Invalid: < 1
                enable_ml_models=True
            )
        
        # Empty pattern types list
        with pytest.raises(ValidationError):
            DetectionConfig(
                confidence_threshold=0.7,
                enable_ml_models=True,
                pattern_types=[]  # Invalid: empty list
            )
    
    def test_batch_detection_request_valid(self):
        """Test valid BatchDetectionRequest creation."""
        request = BatchDetectionRequest(
            repository_id="repo-123",
            repository_url="https://github.com/test/repo",
            file_patterns=["*.py", "*.js"],
            detection_config=DetectionConfig(
                confidence_threshold=0.7,
                enable_ml_models=True
            ),
            parallel_jobs=5,
            timeout_seconds=300
        )
        
        assert request.repository_id == "repo-123"
        assert request.repository_url == "https://github.com/test/repo"
        assert request.file_patterns == ["*.py", "*.js"]
        assert request.parallel_jobs == 5
        assert request.timeout_seconds == 300
    
    def test_batch_detection_request_invalid_url(self):
        """Test BatchDetectionRequest with invalid URL."""
        with pytest.raises(ValidationError) as exc_info:
            BatchDetectionRequest(
                repository_id="repo-123",
                repository_url="invalid-url",  # Invalid
                file_patterns=["*.py"],
                detection_config=DetectionConfig(confidence_threshold=0.7)
            )
        
        assert "repository_url" in str(exc_info.value)
    
    def test_batch_detection_request_empty_patterns(self):
        """Test BatchDetectionRequest with empty file patterns."""
        with pytest.raises(ValidationError) as exc_info:
            BatchDetectionRequest(
                repository_id="repo-123",
                repository_url="https://github.com/test/repo",
                file_patterns=[],  # Empty
                detection_config=DetectionConfig(confidence_threshold=0.7)
            )
        
        assert "file_patterns" in str(exc_info.value)
        assert "empty" in str(exc_info.value)
    
    def test_pattern_detection_response_creation(self):
        """Test PatternDetectionResponse creation."""
        patterns = [
            DetectedPattern(
                pattern_id="pattern-1",
                pattern_name="Pattern 1",
                pattern_type=PatternType.DESIGN_PATTERN,
                severity=SeverityLevel.LOW,
                confidence=0.8,
                location={"file": "test.py", "line": 10, "column": 5},
                description="Pattern 1",
                detection_method=DetectionType.ML_INFERENCE
            )
        ]
        
        summary = PatternSummary.from_patterns(patterns)
        
        response = PatternDetectionResponse(
            request_id="req-123",
            repository_id="repo-123",
            patterns=patterns,
            summary=summary,
            processing_time_ms=150,
            model_versions={"ml_inference": "1.0.0"}
        )
        
        assert response.request_id == "req-123"
        assert response.repository_id == "repo-123"
        assert len(response.patterns) == 1
        assert response.pattern_count == 1
        assert response.processing_time_ms == 150
        assert response.model_versions["ml_inference"] == "1.0.0"
    
    def test_pattern_summary_from_patterns(self):
        """Test PatternSummary creation from patterns."""
        patterns = [
            DetectedPattern(
                pattern_id="pattern-1",
                pattern_name="Pattern 1",
                pattern_type=PatternType.DESIGN_PATTERN,
                severity=SeverityLevel.LOW,
                confidence=0.8,
                location={"file": "test.py", "line": 10, "column": 5},
                description="Pattern 1",
                detection_method=DetectionType.ML_INFERENCE
            ),
            DetectedPattern(
                pattern_id="pattern-2",
                pattern_name="Pattern 2",
                pattern_type=PatternType.SECURITY_ISSUE,
                severity=SeverityLevel.CRITICAL,
                confidence=0.95,
                location={"file": "test.py", "line": 20, "column": 5},
                description="Pattern 2",
                detection_method=DetectionType.HEURISTIC
            )
        ]
        
        summary = PatternSummary.from_patterns(patterns)
        
        assert summary.total_patterns == 2
        assert summary.unique_patterns == 2
        assert summary.by_severity[SeverityLevel.LOW] == 1
        assert summary.by_severity[SeverityLevel.CRITICAL] == 1
        assert summary.by_type[PatternType.DESIGN_PATTERN] == 1
        assert summary.by_type[PatternType.SECURITY_ISSUE] == 1
        assert summary.average_confidence == 0.875
        assert summary.quality_score < 100  # Should be reduced due to critical pattern
        assert summary.security_score < 100  # Should be reduced due to security issue
    
    def test_repository_analysis_request_valid(self):
        """Test valid RepositoryAnalysisRequest creation."""
        request = RepositoryAnalysisRequest(
            repository_id="repo-123",
            repository_url="https://github.com/test/repo",
            branch="main",
            commit_sha="abc123",
            analysis_type="comprehensive",
            detection_config=DetectionConfig(
                confidence_threshold=0.7,
                enable_ml_models=True
            ),
            include_tests=True,
            generate_report=True
        )
        
        assert request.repository_id == "repo-123"
        assert request.repository_url == "https://github.com/test/repo"
        assert request.branch == "main"
        assert request.commit_sha == "abc123"
        assert request.analysis_type == "comprehensive"
        assert request.include_tests is True
        assert request.generate_report is True
    
    def test_repository_analysis_request_invalid_url(self):
        """Test RepositoryAnalysisRequest with invalid URL."""
        with pytest.raises(ValidationError) as exc_info:
            RepositoryAnalysisRequest(
                repository_id="repo-123",
                repository_url="invalid-url",  # Invalid
                detection_config=DetectionConfig(confidence_threshold=0.7)
            )
        
        assert "repository_url" in str(exc_info.value)
        assert "invalid" in str(exc_info.value).lower()


class TestDatabaseModels:
    """Test database models."""
    
    def test_pattern_record_creation(self):
        """Test PatternRecord creation."""
        record = PatternRecord(
            id="pattern-123",
            repository_id="repo-123",
            pattern_name="Test Pattern",
            pattern_type=PatternType.DESIGN_PATTERN.value,
            severity=SeverityLevel.MEDIUM.value,
            confidence=0.85,
            file_path="test.py",
            line_number=10,
            description="Test pattern description",
            detection_method=DetectionType.ML_INFERENCE.value,
            context={"function_name": "test_func"},
            metadata={"model_version": "1.0.0"}
        )
        
        assert record.id == "pattern-123"
        assert record.repository_id == "repo-123"
        assert record.pattern_name == "Test Pattern"
        assert record.pattern_type == PatternType.DESIGN_PATTERN.value
        assert record.severity == SeverityLevel.MEDIUM.value
        assert record.confidence == 0.85
        assert record.file_path == "test.py"
        assert record.line_number == 10
        assert record.context["function_name"] == "test_func"
        assert record.metadata["model_version"] == "1.0.0"
    
    def test_analysis_record_creation(self):
        """Test AnalysisRecord creation."""
        record = AnalysisRecord(
            id="analysis-123",
            repository_id="repo-123",
            status="completed",
            patterns_detected=45,
            quality_score=85.5,
            security_score=92.3,
            performance_score=78.9,
            processing_time_ms=15000,
            metadata={
                "files_processed": 50,
                "languages": ["python", "javascript"]
            }
        )
        
        assert record.id == "analysis-123"
        assert record.repository_id == "repo-123"
        assert record.status == "completed"
        assert record.patterns_detected == 45
        assert record.quality_score == 85.5
        assert record.security_score == 92.3
        assert record.performance_score == 78.9
        assert record.processing_time_ms == 15000
        assert record.metadata["files_processed"] == 50
        assert record.metadata["languages"] == ["python", "javascript"]
    
    def test_repository_record_creation(self):
        """Test RepositoryRecord creation."""
        record = RepositoryRecord(
            id="repo-123",
            repository_url="https://github.com/test/repo",
            name="test-repo",
            description="Test repository",
            language="python",
            stars=150,
            forks=25,
            last_commit=datetime.utcnow(),
            metadata={
                "owner": "test-owner",
                "license": "MIT",
                "topics": ["testing", "python"]
            }
        )
        
        assert record.id == "repo-123"
        assert record.repository_url == "https://github.com/test/repo"
        assert record.name == "test-repo"
        assert record.description == "Test repository"
        assert record.language == "python"
        assert record.stars == 150
        assert record.forks == 25
        assert record.metadata["owner"] == "test-owner"
        assert record.metadata["license"] == "MIT"
        assert record.metadata["topics"] == ["testing", "python"]
    
    def test_model_record_creation(self):
        """Test ModelRecord creation."""
        record = ModelRecord(
            id="model-123",
            name="Pattern Detector",
            version="1.0.0",
            model_type="classification",
            status="active",
            accuracy=0.95,
            precision=0.93,
            recall=0.97,
            f1_score=0.95,
            training_data_size=10000,
            model_path="gs://models/pattern-detector-v1",
            metadata={
                "framework": "tensorflow",
                "hyperparameters": {
                    "learning_rate": 0.001,
                    "batch_size": 32
                }
            }
        )
        
        assert record.id == "model-123"
        assert record.name == "Pattern Detector"
        assert record.version == "1.0.0"
        assert record.model_type == "classification"
        assert record.status == "active"
        assert record.accuracy == 0.95
        assert record.precision == 0.93
        assert record.recall == 0.97
        assert record.f1_score == 0.95
        assert record.training_data_size == 10000
        assert record.model_path == "gs://models/pattern-detector-v1"
        assert record.metadata["framework"] == "tensorflow"
        assert record.metadata["hyperparameters"]["learning_rate"] == 0.001


class TestMLModels:
    """Test ML-related models."""
    
    def test_prediction_request_creation(self):
        """Test PredictionRequest creation."""
        request = PredictionRequest(
            model_id="model-123",
            input_data={
                "code": "print('hello')",
                "language": "python",
                "context": {"file_path": "test.py"}
            },
            config=InferenceConfig(
                confidence_threshold=0.7,
                max_predictions=10,
                include_probabilities=True
            )
        )
        
        assert request.model_id == "model-123"
        assert request.input_data["code"] == "print('hello')"
        assert request.input_data["language"] == "python"
        assert request.config.confidence_threshold == 0.7
        assert request.config.max_predictions == 10
        assert request.config.include_probabilities is True
    
    def test_prediction_response_creation(self):
        """Test PredictionResponse creation."""
        response = PredictionResponse(
            request_id="req-123",
            model_id="model-123",
            predictions=[
                {
                    "label": "DESIGN_PATTERN",
                    "confidence": 0.92,
                    "probability": 0.92
                },
                {
                    "label": "CODE_SMELL",
                    "confidence": 0.15,
                    "probability": 0.08
                }
            ],
            processing_time_ms=25,
            model_version="1.0.0"
        )
        
        assert response.request_id == "req-123"
        assert response.model_id == "model-123"
        assert len(response.predictions) == 2
        assert response.predictions[0]["label"] == "DESIGN_PATTERN"
        assert response.predictions[0]["confidence"] == 0.92
        assert response.processing_time_ms == 25
        assert response.model_version == "1.0.0"
    
    def test_training_config_creation(self):
        """Test TrainingConfig creation."""
        config = TrainingConfig(
            model_id="model-123",
            training_data_config={
                "data_source": "bigquery",
                "dataset": "patterns_dataset",
                "table": "training_patterns"
            },
            hyperparameters={
                "learning_rate": 0.001,
                "batch_size": 32,
                "epochs": 10,
                "dropout_rate": 0.2
            },
            validation_config={
                "validation_split": 0.2,
                "validation_source": "same",
                "cross_validation_folds": 5
            },
            early_stopping_config={
                "patience": 5,
                "min_delta": 0.001,
                "restore_best_weights": True
            }
        )
        
        assert config.model_id == "model-123"
        assert config.training_data_config["data_source"] == "bigquery"
        assert config.hyperparameters["learning_rate"] == 0.001
        assert config.hyperparameters["batch_size"] == 32
        assert config.validation_config["validation_split"] == 0.2
        assert config.early_stopping_config["patience"] == 5
    
    def test_model_metrics_creation(self):
        """Test ModelMetrics creation."""
        metrics = ModelMetrics(
            model_id="model-123",
            accuracy=0.95,
            precision=0.93,
            recall=0.97,
            f1_score=0.95,
            confusion_matrix=[[85, 3], [2, 10]],
            classification_report={
                "DESIGN_PATTERN": {
                    "precision": 0.97,
                    "recall": 0.96,
                    "f1-score": 0.97,
                    "support": 88
                },
                "ANTI_PATTERN": {
                    "precision": 0.77,
                    "recall": 0.83,
                    "f1-score": 0.80,
                    "support": 12
                }
            },
            evaluation_time_ms=1500
        )
        
        assert metrics.model_id == "model-123"
        assert metrics.accuracy == 0.95
        assert metrics.precision == 0.93
        assert metrics.recall == 0.97
        assert metrics.f1_score == 0.95
        assert metrics.confusion_matrix == [[85, 3], [2, 10]]
        assert metrics.classification_report["DESIGN_PATTERN"]["precision"] == 0.97
        assert metrics.evaluation_time_ms == 1500


class TestHealthModels:
    """Test health check models."""
    
    def test_health_status_creation(self):
        """Test HealthStatus creation."""
        status = HealthStatus(
            status="healthy",
            service="pattern-mining",
            version="1.0.0",
            timestamp=datetime.utcnow(),
            uptime_seconds=3600,
            checks={
                "database": ComponentHealth(
                    status="healthy",
                    response_time_ms=15,
                    details={"connection_pool": "available"}
                ),
                "cache": ComponentHealth(
                    status="healthy",
                    response_time_ms=5,
                    details={"redis_version": "6.2.0"}
                )
            }
        )
        
        assert status.status == "healthy"
        assert status.service == "pattern-mining"
        assert status.version == "1.0.0"
        assert status.uptime_seconds == 3600
        assert status.checks["database"].status == "healthy"
        assert status.checks["database"].response_time_ms == 15
        assert status.checks["cache"].status == "healthy"
        assert status.checks["cache"].response_time_ms == 5
    
    def test_component_health_creation(self):
        """Test ComponentHealth creation."""
        health = ComponentHealth(
            status="healthy",
            response_time_ms=25,
            details={
                "connection_pool_size": 10,
                "active_connections": 3,
                "idle_connections": 7
            },
            last_check=datetime.utcnow()
        )
        
        assert health.status == "healthy"
        assert health.response_time_ms == 25
        assert health.details["connection_pool_size"] == 10
        assert health.details["active_connections"] == 3
        assert health.details["idle_connections"] == 7
        assert health.last_check is not None
    
    def test_system_metrics_creation(self):
        """Test SystemMetrics creation."""
        metrics = SystemMetrics(
            cpu_usage_percent=65.5,
            memory_usage_percent=78.2,
            disk_usage_percent=45.8,
            network_io_bytes_per_second=1024000,
            process_count=156,
            thread_count=2048,
            open_file_descriptors=128,
            load_average=[1.2, 1.5, 1.8],
            timestamp=datetime.utcnow()
        )
        
        assert metrics.cpu_usage_percent == 65.5
        assert metrics.memory_usage_percent == 78.2
        assert metrics.disk_usage_percent == 45.8
        assert metrics.network_io_bytes_per_second == 1024000
        assert metrics.process_count == 156
        assert metrics.thread_count == 2048
        assert metrics.open_file_descriptors == 128
        assert metrics.load_average == [1.2, 1.5, 1.8]
        assert metrics.timestamp is not None


class TestCommonModels:
    """Test common utility models."""
    
    def test_paginated_response_creation(self):
        """Test PaginatedResponse creation."""
        data = [{"id": i, "name": f"item-{i}"} for i in range(10)]
        
        response = PaginatedResponse(
            items=data,
            total=100,
            page=2,
            page_size=10,
            has_next=True,
            has_previous=True
        )
        
        assert len(response.items) == 10
        assert response.total == 100
        assert response.page == 2
        assert response.page_size == 10
        assert response.has_next is True
        assert response.has_previous is True
        assert response.total_pages == 10
        assert response.start_index == 11
        assert response.end_index == 20
    
    def test_time_range_creation(self):
        """Test TimeRange creation."""
        start_time = datetime.utcnow()
        end_time = start_time + timedelta(days=7)
        
        time_range = TimeRange(
            start_time=start_time,
            end_time=end_time,
            timezone="UTC"
        )
        
        assert time_range.start_time == start_time
        assert time_range.end_time == end_time
        assert time_range.timezone == "UTC"
        assert time_range.duration == timedelta(days=7)
    
    def test_time_range_validation(self):
        """Test TimeRange validation."""
        start_time = datetime.utcnow()
        end_time = start_time - timedelta(days=1)  # End before start
        
        with pytest.raises(ValidationError) as exc_info:
            TimeRange(
                start_time=start_time,
                end_time=end_time,
                timezone="UTC"
            )
        
        assert "end_time" in str(exc_info.value)
        assert "start_time" in str(exc_info.value)
    
    def test_filter_config_creation(self):
        """Test FilterConfig creation."""
        config = FilterConfig(
            pattern_types=[PatternType.DESIGN_PATTERN, PatternType.SECURITY_ISSUE],
            severity_levels=[SeverityLevel.HIGH, SeverityLevel.CRITICAL],
            confidence_range=[0.8, 1.0],
            file_patterns=["*.py", "*.js"],
            exclude_patterns=["*/test/*", "*/tests/*"],
            date_range=TimeRange(
                start_time=datetime.utcnow() - timedelta(days=30),
                end_time=datetime.utcnow(),
                timezone="UTC"
            ),
            repositories=["repo-1", "repo-2"],
            languages=["python", "javascript"]
        )
        
        assert len(config.pattern_types) == 2
        assert len(config.severity_levels) == 2
        assert config.confidence_range == [0.8, 1.0]
        assert config.file_patterns == ["*.py", "*.js"]
        assert config.exclude_patterns == ["*/test/*", "*/tests/*"]
        assert config.date_range is not None
        assert config.repositories == ["repo-1", "repo-2"]
        assert config.languages == ["python", "javascript"]
    
    def test_filter_config_validation(self):
        """Test FilterConfig validation."""
        # Invalid confidence range
        with pytest.raises(ValidationError) as exc_info:
            FilterConfig(
                confidence_range=[1.5, 0.5],  # Invalid: values > 1.0 and reversed
                pattern_types=[PatternType.DESIGN_PATTERN]
            )
        
        assert "confidence_range" in str(exc_info.value)
        
        # Empty pattern types
        with pytest.raises(ValidationError) as exc_info:
            FilterConfig(
                pattern_types=[],  # Empty list
                confidence_range=[0.7, 1.0]
            )
        
        assert "pattern_types" in str(exc_info.value)


class TestModelEdgeCases:
    """Test edge cases and error conditions."""
    
    def test_model_with_none_optional_fields(self):
        """Test model with None optional fields."""
        pattern = DetectedPattern(
            pattern_id="test-pattern-123",
            pattern_name="Test Pattern",
            pattern_type=PatternType.DESIGN_PATTERN,
            severity=SeverityLevel.MEDIUM,
            confidence=0.85,
            location={"file": "test.py", "line": 10, "column": 5},
            description="Test pattern description",
            detection_method=DetectionType.ML_INFERENCE,
            context=None,  # Optional field
            metadata=None  # Optional field
        )
        
        assert pattern.context is None
        assert pattern.metadata is None
    
    def test_model_with_extra_fields(self):
        """Test model behavior with extra fields."""
        data = {
            "pattern_id": "test-pattern-123",
            "pattern_name": "Test Pattern",
            "pattern_type": "DESIGN_PATTERN",
            "severity": "MEDIUM",
            "confidence": 0.85,
            "location": {"file": "test.py", "line": 10, "column": 5},
            "description": "Test pattern description",
            "detection_method": "ML_INFERENCE",
            "extra_field": "should_be_ignored"  # Extra field
        }
        
        # Should not raise error due to extra fields
        pattern = DetectedPattern.model_validate(data)
        assert pattern.pattern_id == "test-pattern-123"
        assert pattern.pattern_name == "Test Pattern"
        # Extra field should be ignored
        assert not hasattr(pattern, 'extra_field')
    
    def test_model_with_empty_collections(self):
        """Test model with empty collections."""
        summary = PatternSummary(
            total_patterns=0,
            unique_patterns=0,
            by_type={},  # Empty dict
            by_severity={},  # Empty dict
            by_detection_method={},  # Empty dict
            quality_score=100.0,
            security_score=100.0,
            performance_score=100.0,
            maintainability_score=100.0,
            top_patterns=[],  # Empty list
            average_confidence=0.0,
            confidence_distribution={}  # Empty dict
        )
        
        assert summary.total_patterns == 0
        assert summary.unique_patterns == 0
        assert summary.by_type == {}
        assert summary.by_severity == {}
        assert summary.by_detection_method == {}
        assert summary.top_patterns == []
        assert summary.confidence_distribution == {}
    
    def test_model_with_extreme_values(self):
        """Test model with extreme values."""
        # Very long string
        long_description = "x" * 10000
        
        pattern = DetectedPattern(
            pattern_id="test-pattern-123",
            pattern_name="Test Pattern",
            pattern_type=PatternType.DESIGN_PATTERN,
            severity=SeverityLevel.MEDIUM,
            confidence=0.85,
            location={"file": "test.py", "line": 10, "column": 5},
            description=long_description,
            detection_method=DetectionType.ML_INFERENCE
        )
        
        assert len(pattern.description) == 10000
        
        # Very large numbers
        analysis = AnalysisRecord(
            id="analysis-123",
            repository_id="repo-123",
            status="completed",
            patterns_detected=999999,
            quality_score=100.0,
            processing_time_ms=3600000  # 1 hour
        )
        
        assert analysis.patterns_detected == 999999
        assert analysis.processing_time_ms == 3600000
    
    def test_model_datetime_handling(self):
        """Test model datetime handling."""
        now = datetime.utcnow()
        
        pattern = DetectedPattern(
            pattern_id="test-pattern-123",
            pattern_name="Test Pattern",
            pattern_type=PatternType.DESIGN_PATTERN,
            severity=SeverityLevel.MEDIUM,
            confidence=0.85,
            location={"file": "test.py", "line": 10, "column": 5},
            description="Test pattern description",
            detection_method=DetectionType.ML_INFERENCE,
            created_at=now,
            updated_at=now
        )
        
        assert pattern.created_at == now
        assert pattern.updated_at == now
        
        # Test JSON serialization/deserialization of datetime
        json_data = pattern.model_dump_json()
        pattern_restored = DetectedPattern.model_validate_json(json_data)
        
        # Should preserve datetime (within reasonable precision)
        assert abs((pattern_restored.created_at - now).total_seconds()) < 1
        assert abs((pattern_restored.updated_at - now).total_seconds()) < 1
    
    def test_model_unicode_handling(self):
        """Test model Unicode handling."""
        unicode_text = "测试模式 🚀 Тест パターン"
        
        pattern = DetectedPattern(
            pattern_id="test-pattern-123",
            pattern_name=unicode_text,
            pattern_type=PatternType.DESIGN_PATTERN,
            severity=SeverityLevel.MEDIUM,
            confidence=0.85,
            location={"file": f"{unicode_text}.py", "line": 10, "column": 5},
            description=f"Pattern with {unicode_text}",
            detection_method=DetectionType.ML_INFERENCE
        )
        
        assert pattern.pattern_name == unicode_text
        assert pattern.location["file"] == f"{unicode_text}.py"
        assert pattern.description == f"Pattern with {unicode_text}"
        
        # Test JSON serialization with Unicode
        json_data = pattern.model_dump_json()
        pattern_restored = DetectedPattern.model_validate_json(json_data)
        
        assert pattern_restored.pattern_name == unicode_text
        assert pattern_restored.location["file"] == f"{unicode_text}.py"
        assert pattern_restored.description == f"Pattern with {unicode_text}"