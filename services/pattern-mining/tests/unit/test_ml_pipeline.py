"""
Unit Tests for ML Pipeline Components

Comprehensive tests for ML pipeline including model management, inference, training,
and Gemini integration.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import numpy as np
import json

from pattern_mining.ml.manager import M<PERSON><PERSON>ger
from pattern_mining.ml.inference import InferenceEngine
from pattern_mining.ml.trainer import ModelTrainer
from pattern_mining.ml.gemini_client import Gemini<PERSON><PERSON>
from pattern_mining.ml.gemini_analyzer import <PERSON><PERSON>nal<PERSON><PERSON>
from pattern_mining.ml.gemini_embeddings import GeminiEmbeddings
from pattern_mining.ml.components.base_model import BaseModel
from pattern_mining.ml.components.transformer_models import TransformerModel
from pattern_mining.models.ml import (
    ModelConfig, TrainingConfig, PredictionRequest, PredictionResponse,
    ModelMetrics, TrainingMetrics, ModelInfo, InferenceConfig
)
from pattern_mining.models.patterns import PatternType, SeverityLevel, DetectionType
from tests.utils.generators import CodeGenerator, PatternGenerator


class TestMLManager:
    """Test ML Manager functionality."""
    
    @pytest.fixture
    def ml_manager(self):
        """Create ML manager instance."""
        return MLManager()
    
    @pytest.fixture
    def mock_model_config(self):
        """Mock model configuration."""
        return ModelConfig(
            model_id="test-model",
            model_name="Test Model",
            model_type="classification",
            model_path="gs://models/test-model",
            input_shape=[768],
            output_shape=[10],
            preprocessing_config={
                "tokenizer": "bert-base-uncased",
                "max_length": 512
            },
            hyperparameters={
                "learning_rate": 0.001,
                "batch_size": 32,
                "dropout_rate": 0.1
            }
        )
    
    def test_ml_manager_initialization(self, ml_manager):
        """Test ML manager initialization."""
        assert ml_manager is not None
        assert hasattr(ml_manager, 'models')
        assert hasattr(ml_manager, 'inference_engine')
        assert hasattr(ml_manager, 'trainer')
        assert hasattr(ml_manager, 'gemini_client')
    
    @pytest.mark.asyncio
    async def test_load_model_success(self, ml_manager, mock_model_config):
        """Test successful model loading."""
        with patch.object(ml_manager, '_load_model_from_path') as mock_load:
            mock_model = Mock()
            mock_model.config = mock_model_config
            mock_load.return_value = mock_model
            
            result = await ml_manager.load_model(mock_model_config)
            
            assert result is True
            assert mock_model_config.model_id in ml_manager.models
            assert ml_manager.models[mock_model_config.model_id] == mock_model
            mock_load.assert_called_once_with(mock_model_config.model_path)
    
    @pytest.mark.asyncio
    async def test_load_model_failure(self, ml_manager, mock_model_config):
        """Test model loading failure."""
        with patch.object(ml_manager, '_load_model_from_path') as mock_load:
            mock_load.side_effect = Exception("Model loading failed")
            
            result = await ml_manager.load_model(mock_model_config)
            
            assert result is False
            assert mock_model_config.model_id not in ml_manager.models
    
    @pytest.mark.asyncio
    async def test_list_models(self, ml_manager):
        """Test listing available models."""
        # Mock some loaded models
        mock_models = {
            "model-1": Mock(config=ModelConfig(
                model_id="model-1",
                model_name="Model 1",
                model_type="classification",
                model_path="gs://models/model-1",
                input_shape=[768],
                output_shape=[5]
            )),
            "model-2": Mock(config=ModelConfig(
                model_id="model-2",
                model_name="Model 2",
                model_type="regression",
                model_path="gs://models/model-2",
                input_shape=[512],
                output_shape=[1]
            ))
        }
        
        ml_manager.models = mock_models
        
        models = await ml_manager.list_models()
        
        assert len(models) == 2
        assert models[0]["id"] == "model-1"
        assert models[0]["name"] == "Model 1"
        assert models[0]["type"] == "classification"
        assert models[1]["id"] == "model-2"
        assert models[1]["name"] == "Model 2"
        assert models[1]["type"] == "regression"
    
    @pytest.mark.asyncio
    async def test_get_model_info_exists(self, ml_manager):
        """Test getting model info for existing model."""
        mock_model = Mock()
        mock_model.config = ModelConfig(
            model_id="test-model",
            model_name="Test Model",
            model_type="classification",
            model_path="gs://models/test-model",
            input_shape=[768],
            output_shape=[10]
        )
        mock_model.get_metrics.return_value = ModelMetrics(
            model_id="test-model",
            accuracy=0.95,
            precision=0.93,
            recall=0.97,
            f1_score=0.95
        )
        
        ml_manager.models = {"test-model": mock_model}
        
        info = await ml_manager.get_model_info("test-model")
        
        assert info is not None
        assert info["id"] == "test-model"
        assert info["name"] == "Test Model"
        assert info["type"] == "classification"
        assert info["accuracy"] == 0.95
        assert info["precision"] == 0.93
        assert info["recall"] == 0.97
        assert info["f1_score"] == 0.95
    
    @pytest.mark.asyncio
    async def test_get_model_info_not_exists(self, ml_manager):
        """Test getting model info for non-existent model."""
        info = await ml_manager.get_model_info("non-existent-model")
        assert info is None
    
    @pytest.mark.asyncio
    async def test_predict_success(self, ml_manager):
        """Test successful prediction."""
        mock_model = Mock()
        mock_model.predict.return_value = {
            "predictions": [
                {"label": "DESIGN_PATTERN", "confidence": 0.92},
                {"label": "ANTI_PATTERN", "confidence": 0.08}
            ],
            "processing_time_ms": 25
        }
        
        ml_manager.models = {"test-model": mock_model}
        
        prediction_request = PredictionRequest(
            model_id="test-model",
            input_data={
                "code": "def test_function():\n    pass",
                "language": "python"
            }
        )
        
        result = await ml_manager.predict(prediction_request)
        
        assert result is not None
        assert result["model_id"] == "test-model"
        assert "predictions" in result
        assert len(result["predictions"]) == 2
        assert result["predictions"][0]["label"] == "DESIGN_PATTERN"
        assert result["predictions"][0]["confidence"] == 0.92
        assert result["processing_time_ms"] == 25
    
    @pytest.mark.asyncio
    async def test_predict_model_not_found(self, ml_manager):
        """Test prediction with non-existent model."""
        prediction_request = PredictionRequest(
            model_id="non-existent-model",
            input_data={"code": "test"}
        )
        
        with pytest.raises(ValueError, match="Model non-existent-model not found"):
            await ml_manager.predict(prediction_request)
    
    @pytest.mark.asyncio
    async def test_evaluate_model_success(self, ml_manager):
        """Test successful model evaluation."""
        mock_model = Mock()
        mock_model.evaluate.return_value = ModelMetrics(
            model_id="test-model",
            accuracy=0.95,
            precision=0.93,
            recall=0.97,
            f1_score=0.95,
            confusion_matrix=[[85, 3], [2, 10]],
            classification_report={
                "DESIGN_PATTERN": {"precision": 0.97, "recall": 0.96, "f1-score": 0.97},
                "ANTI_PATTERN": {"precision": 0.77, "recall": 0.83, "f1-score": 0.80}
            }
        )
        
        ml_manager.models = {"test-model": mock_model}
        
        test_data = {
            "data_source": "test_dataset",
            "samples": [
                {"code": "def test1():\n    pass", "label": "DESIGN_PATTERN"},
                {"code": "def test2():\n    pass", "label": "ANTI_PATTERN"}
            ]
        }
        
        result = await ml_manager.evaluate_model("test-model", test_data)
        
        assert result is not None
        assert result.accuracy == 0.95
        assert result.precision == 0.93
        assert result.recall == 0.97
        assert result.f1_score == 0.95
        assert result.confusion_matrix == [[85, 3], [2, 10]]
        assert "DESIGN_PATTERN" in result.classification_report
        assert "ANTI_PATTERN" in result.classification_report
    
    @pytest.mark.asyncio
    async def test_delete_model_success(self, ml_manager):
        """Test successful model deletion."""
        mock_model = Mock()
        ml_manager.models = {"test-model": mock_model}
        
        with patch.object(ml_manager, '_delete_model_files') as mock_delete:
            mock_delete.return_value = True
            
            result = await ml_manager.delete_model("test-model")
            
            assert result is True
            assert "test-model" not in ml_manager.models
            mock_delete.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_delete_model_not_found(self, ml_manager):
        """Test deletion of non-existent model."""
        result = await ml_manager.delete_model("non-existent-model")
        assert result is False


class TestInferenceEngine:
    """Test inference engine functionality."""
    
    @pytest.fixture
    def inference_engine(self):
        """Create inference engine instance."""
        return InferenceEngine()
    
    @pytest.fixture
    def mock_model(self):
        """Mock model for testing."""
        model = Mock()
        model.config = ModelConfig(
            model_id="test-model",
            model_name="Test Model",
            model_type="classification",
            model_path="gs://models/test-model",
            input_shape=[768],
            output_shape=[10]
        )
        return model
    
    def test_inference_engine_initialization(self, inference_engine):
        """Test inference engine initialization."""
        assert inference_engine is not None
        assert hasattr(inference_engine, 'models')
        assert hasattr(inference_engine, 'preprocessors')
        assert hasattr(inference_engine, 'postprocessors')
    
    @pytest.mark.asyncio
    async def test_preprocess_input_success(self, inference_engine):
        """Test successful input preprocessing."""
        with patch.object(inference_engine, '_tokenize_code') as mock_tokenize:
            mock_tokenize.return_value = {
                "input_ids": [101, 1045, 2572, 102],
                "attention_mask": [1, 1, 1, 1],
                "token_type_ids": [0, 0, 0, 0]
            }
            
            input_data = {
                "code": "def test():\n    pass",
                "language": "python"
            }
            
            result = await inference_engine.preprocess_input(input_data, "test-model")
            
            assert result is not None
            assert "input_ids" in result
            assert "attention_mask" in result
            assert "token_type_ids" in result
            mock_tokenize.assert_called_once_with("def test():\n    pass", "python")
    
    @pytest.mark.asyncio
    async def test_run_inference_success(self, inference_engine, mock_model):
        """Test successful inference execution."""
        inference_engine.models = {"test-model": mock_model}
        
        # Mock model prediction
        mock_model.predict.return_value = np.array([[0.92, 0.08]])
        
        preprocessed_input = {
            "input_ids": [101, 1045, 2572, 102],
            "attention_mask": [1, 1, 1, 1]
        }
        
        result = await inference_engine.run_inference(preprocessed_input, "test-model")
        
        assert result is not None
        assert isinstance(result, np.ndarray)
        assert result.shape == (1, 2)
        assert result[0][0] == 0.92
        assert result[0][1] == 0.08
    
    @pytest.mark.asyncio
    async def test_postprocess_output_success(self, inference_engine):
        """Test successful output postprocessing."""
        raw_output = np.array([[0.92, 0.08]])
        
        config = InferenceConfig(
            confidence_threshold=0.7,
            max_predictions=5,
            include_probabilities=True
        )
        
        with patch.object(inference_engine, '_get_class_labels') as mock_labels:
            mock_labels.return_value = ["DESIGN_PATTERN", "ANTI_PATTERN"]
            
            result = await inference_engine.postprocess_output(raw_output, "test-model", config)
            
            assert result is not None
            assert "predictions" in result
            assert len(result["predictions"]) == 2
            assert result["predictions"][0]["label"] == "DESIGN_PATTERN"
            assert result["predictions"][0]["confidence"] == 0.92
            assert result["predictions"][0]["probability"] == 0.92
            assert result["predictions"][1]["label"] == "ANTI_PATTERN"
            assert result["predictions"][1]["confidence"] == 0.08
            assert result["predictions"][1]["probability"] == 0.08
    
    @pytest.mark.asyncio
    async def test_batch_inference_success(self, inference_engine, mock_model):
        """Test successful batch inference."""
        inference_engine.models = {"test-model": mock_model}
        
        # Mock batch prediction
        mock_model.predict_batch.return_value = np.array([
            [0.92, 0.08],
            [0.15, 0.85],
            [0.75, 0.25]
        ])
        
        batch_input = [
            {"code": "def test1():\n    pass", "language": "python"},
            {"code": "def test2():\n    pass", "language": "python"},
            {"code": "def test3():\n    pass", "language": "python"}
        ]
        
        with patch.object(inference_engine, 'preprocess_input') as mock_preprocess:
            mock_preprocess.return_value = {
                "input_ids": [101, 1045, 2572, 102],
                "attention_mask": [1, 1, 1, 1]
            }
            
            with patch.object(inference_engine, 'postprocess_output') as mock_postprocess:
                mock_postprocess.side_effect = [
                    {"predictions": [{"label": "DESIGN_PATTERN", "confidence": 0.92}]},
                    {"predictions": [{"label": "ANTI_PATTERN", "confidence": 0.85}]},
                    {"predictions": [{"label": "DESIGN_PATTERN", "confidence": 0.75}]}
                ]
                
                results = await inference_engine.batch_inference(batch_input, "test-model")
                
                assert results is not None
                assert len(results) == 3
                assert results[0]["predictions"][0]["label"] == "DESIGN_PATTERN"
                assert results[1]["predictions"][0]["label"] == "ANTI_PATTERN"
                assert results[2]["predictions"][0]["label"] == "DESIGN_PATTERN"
    
    @pytest.mark.asyncio
    async def test_inference_with_caching(self, inference_engine, mock_model):
        """Test inference with result caching."""
        inference_engine.models = {"test-model": mock_model}
        inference_engine.enable_caching = True
        
        # Mock model prediction
        mock_model.predict.return_value = np.array([[0.92, 0.08]])
        
        input_data = {
            "code": "def test():\n    pass",
            "language": "python"
        }
        
        with patch.object(inference_engine, 'preprocess_input') as mock_preprocess:
            mock_preprocess.return_value = {
                "input_ids": [101, 1045, 2572, 102],
                "attention_mask": [1, 1, 1, 1]
            }
            
            with patch.object(inference_engine, 'postprocess_output') as mock_postprocess:
                mock_postprocess.return_value = {
                    "predictions": [{"label": "DESIGN_PATTERN", "confidence": 0.92}]
                }
                
                # First call - should run inference
                result1 = await inference_engine.predict(input_data, "test-model")
                
                # Second call - should use cache
                result2 = await inference_engine.predict(input_data, "test-model")
                
                assert result1 == result2
                assert mock_model.predict.call_count == 1  # Should only be called once due to caching
    
    @pytest.mark.asyncio
    async def test_inference_error_handling(self, inference_engine, mock_model):
        """Test inference error handling."""
        inference_engine.models = {"test-model": mock_model}
        
        # Mock model prediction failure
        mock_model.predict.side_effect = Exception("Model prediction failed")
        
        input_data = {
            "code": "def test():\n    pass",
            "language": "python"
        }
        
        with patch.object(inference_engine, 'preprocess_input') as mock_preprocess:
            mock_preprocess.return_value = {
                "input_ids": [101, 1045, 2572, 102],
                "attention_mask": [1, 1, 1, 1]
            }
            
            with pytest.raises(Exception, match="Model prediction failed"):
                await inference_engine.predict(input_data, "test-model")


class TestModelTrainer:
    """Test model trainer functionality."""
    
    @pytest.fixture
    def model_trainer(self):
        """Create model trainer instance."""
        return ModelTrainer()
    
    @pytest.fixture
    def mock_training_config(self):
        """Mock training configuration."""
        return TrainingConfig(
            model_id="test-model",
            training_data_config={
                "data_source": "bigquery",
                "dataset": "patterns_dataset",
                "table": "training_patterns"
            },
            hyperparameters={
                "learning_rate": 0.001,
                "batch_size": 32,
                "epochs": 10,
                "dropout_rate": 0.1
            },
            validation_config={
                "validation_split": 0.2,
                "validation_source": "same"
            }
        )
    
    def test_model_trainer_initialization(self, model_trainer):
        """Test model trainer initialization."""
        assert model_trainer is not None
        assert hasattr(model_trainer, 'training_jobs')
        assert hasattr(model_trainer, 'data_loaders')
        assert hasattr(model_trainer, 'optimizers')
    
    @pytest.mark.asyncio
    async def test_prepare_training_data_success(self, model_trainer, mock_training_config):
        """Test successful training data preparation."""
        with patch.object(model_trainer, '_load_data_from_source') as mock_load:
            mock_load.return_value = {
                "train_data": [
                    {"code": "def test1():\n    pass", "label": "DESIGN_PATTERN"},
                    {"code": "def test2():\n    pass", "label": "ANTI_PATTERN"}
                ],
                "validation_data": [
                    {"code": "def test3():\n    pass", "label": "DESIGN_PATTERN"}
                ]
            }
            
            result = await model_trainer.prepare_training_data(mock_training_config)
            
            assert result is not None
            assert "train_data" in result
            assert "validation_data" in result
            assert len(result["train_data"]) == 2
            assert len(result["validation_data"]) == 1
    
    @pytest.mark.asyncio
    async def test_start_training_success(self, model_trainer, mock_training_config):
        """Test successful training start."""
        with patch.object(model_trainer, 'prepare_training_data') as mock_prepare:
            mock_prepare.return_value = {
                "train_data": [{"code": "test", "label": "DESIGN_PATTERN"}],
                "validation_data": [{"code": "test", "label": "DESIGN_PATTERN"}]
            }
            
            with patch.object(model_trainer, '_create_training_job') as mock_create:
                mock_create.return_value = {
                    "job_id": "train-job-123",
                    "status": "queued",
                    "estimated_duration": 3600
                }
                
                result = await model_trainer.start_training(mock_training_config)
                
                assert result is not None
                assert result["job_id"] == "train-job-123"
                assert result["status"] == "queued"
                assert result["estimated_duration"] == 3600
    
    @pytest.mark.asyncio
    async def test_get_training_status_success(self, model_trainer):
        """Test getting training status."""
        job_id = "train-job-123"
        
        # Mock training job
        mock_job = {
            "job_id": job_id,
            "status": "running",
            "progress": 65.5,
            "epoch": 7,
            "total_epochs": 10,
            "current_loss": 0.15,
            "validation_accuracy": 0.89
        }
        
        model_trainer.training_jobs = {job_id: mock_job}
        
        result = await model_trainer.get_training_status(job_id)
        
        assert result is not None
        assert result["job_id"] == job_id
        assert result["status"] == "running"
        assert result["progress"] == 65.5
        assert result["epoch"] == 7
        assert result["current_loss"] == 0.15
        assert result["validation_accuracy"] == 0.89
    
    @pytest.mark.asyncio
    async def test_get_training_status_not_found(self, model_trainer):
        """Test getting status for non-existent training job."""
        result = await model_trainer.get_training_status("non-existent-job")
        assert result is None
    
    @pytest.mark.asyncio
    async def test_training_progress_callback(self, model_trainer):
        """Test training progress callback."""
        job_id = "train-job-123"
        
        # Initialize training job
        model_trainer.training_jobs = {
            job_id: {
                "job_id": job_id,
                "status": "running",
                "progress": 0.0,
                "epoch": 0,
                "total_epochs": 10
            }
        }
        
        # Simulate progress updates
        progress_updates = [
            {"epoch": 1, "loss": 0.5, "accuracy": 0.7, "val_loss": 0.6, "val_accuracy": 0.65},
            {"epoch": 2, "loss": 0.4, "accuracy": 0.75, "val_loss": 0.55, "val_accuracy": 0.7},
            {"epoch": 3, "loss": 0.35, "accuracy": 0.8, "val_loss": 0.5, "val_accuracy": 0.75}
        ]
        
        for update in progress_updates:
            await model_trainer.update_training_progress(job_id, update)
            
            job = model_trainer.training_jobs[job_id]
            assert job["epoch"] == update["epoch"]
            assert job["current_loss"] == update["loss"]
            assert job["validation_accuracy"] == update["val_accuracy"]
            assert job["progress"] == (update["epoch"] / 10) * 100
    
    @pytest.mark.asyncio
    async def test_training_completion(self, model_trainer):
        """Test training completion handling."""
        job_id = "train-job-123"
        
        # Initialize training job
        model_trainer.training_jobs = {
            job_id: {
                "job_id": job_id,
                "status": "running",
                "progress": 90.0,
                "epoch": 9,
                "total_epochs": 10
            }
        }
        
        # Simulate training completion
        completion_data = {
            "final_loss": 0.25,
            "final_accuracy": 0.95,
            "final_val_loss": 0.3,
            "final_val_accuracy": 0.92,
            "model_path": "gs://models/test-model-final",
            "training_time": 3456.78
        }
        
        await model_trainer.complete_training(job_id, completion_data)
        
        job = model_trainer.training_jobs[job_id]
        assert job["status"] == "completed"
        assert job["progress"] == 100.0
        assert job["final_accuracy"] == 0.95
        assert job["model_path"] == "gs://models/test-model-final"
        assert job["training_time"] == 3456.78
    
    @pytest.mark.asyncio
    async def test_training_failure_handling(self, model_trainer):
        """Test training failure handling."""
        job_id = "train-job-123"
        
        # Initialize training job
        model_trainer.training_jobs = {
            job_id: {
                "job_id": job_id,
                "status": "running",
                "progress": 45.0,
                "epoch": 4,
                "total_epochs": 10
            }
        }
        
        # Simulate training failure
        error_data = {
            "error_message": "Training failed due to memory error",
            "error_type": "OutOfMemoryError",
            "failed_at_epoch": 4
        }
        
        await model_trainer.fail_training(job_id, error_data)
        
        job = model_trainer.training_jobs[job_id]
        assert job["status"] == "failed"
        assert job["error_message"] == "Training failed due to memory error"
        assert job["error_type"] == "OutOfMemoryError"
        assert job["failed_at_epoch"] == 4


class TestGeminiClient:
    """Test Gemini client functionality."""
    
    @pytest.fixture
    def gemini_client(self):
        """Create Gemini client instance."""
        return GeminiClient(api_key="test-key")
    
    @pytest.fixture
    def mock_code_sample(self):
        """Mock code sample for testing."""
        return """
def calculate_total(items):
    total = 0
    for item in items:
        total += item.price
    return total

class ShoppingCart:
    def __init__(self):
        self.items = []
    
    def add_item(self, item):
        self.items.append(item)
    
    def get_total(self):
        return calculate_total(self.items)
"""
    
    def test_gemini_client_initialization(self, gemini_client):
        """Test Gemini client initialization."""
        assert gemini_client is not None
        assert hasattr(gemini_client, 'api_key')
        assert hasattr(gemini_client, 'model_name')
        assert hasattr(gemini_client, 'client')
        assert gemini_client.api_key == "test-key"
    
    @pytest.mark.asyncio
    async def test_analyze_code_success(self, gemini_client, mock_code_sample):
        """Test successful code analysis."""
        mock_response = {
            "patterns": [
                {
                    "pattern_name": "Iterator Pattern",
                    "pattern_type": "DESIGN_PATTERN",
                    "confidence": 0.92,
                    "location": {"line": 2, "column": 5},
                    "description": "Iterator pattern in calculate_total function"
                },
                {
                    "pattern_name": "Data Class Pattern",
                    "pattern_type": "DESIGN_PATTERN",
                    "confidence": 0.88,
                    "location": {"line": 8, "column": 1},
                    "description": "Simple data class pattern in ShoppingCart"
                }
            ],
            "analysis": {
                "quality_score": 85.0,
                "complexity_score": 3.2,
                "maintainability_score": 78.5,
                "security_score": 95.0
            },
            "recommendations": [
                {
                    "type": "improvement",
                    "description": "Consider adding type hints for better code clarity",
                    "priority": "medium"
                }
            ]
        }
        
        with patch.object(gemini_client, '_make_api_request') as mock_request:
            mock_request.return_value = mock_response
            
            result = await gemini_client.analyze_code(mock_code_sample, "python")
            
            assert result is not None
            assert "patterns" in result
            assert len(result["patterns"]) == 2
            assert result["patterns"][0]["pattern_name"] == "Iterator Pattern"
            assert result["patterns"][1]["pattern_name"] == "Data Class Pattern"
            assert result["analysis"]["quality_score"] == 85.0
            assert len(result["recommendations"]) == 1
    
    @pytest.mark.asyncio
    async def test_generate_embeddings_success(self, gemini_client, mock_code_sample):
        """Test successful embedding generation."""
        mock_embeddings = [0.1] * 768  # 768-dimensional vector
        
        with patch.object(gemini_client, '_make_embedding_request') as mock_request:
            mock_request.return_value = mock_embeddings
            
            result = await gemini_client.generate_embeddings(mock_code_sample)
            
            assert result is not None
            assert len(result) == 768
            assert all(isinstance(x, (int, float)) for x in result)
    
    @pytest.mark.asyncio
    async def test_batch_analyze_success(self, gemini_client):
        """Test successful batch analysis."""
        code_samples = [
            {"code": "def func1():\n    pass", "language": "python"},
            {"code": "def func2():\n    pass", "language": "python"},
            {"code": "def func3():\n    pass", "language": "python"}
        ]
        
        mock_responses = [
            {
                "patterns": [{"pattern_name": "Function Pattern", "confidence": 0.8}],
                "analysis": {"quality_score": 80.0}
            },
            {
                "patterns": [{"pattern_name": "Function Pattern", "confidence": 0.85}],
                "analysis": {"quality_score": 85.0}
            },
            {
                "patterns": [{"pattern_name": "Function Pattern", "confidence": 0.78}],
                "analysis": {"quality_score": 78.0}
            }
        ]
        
        with patch.object(gemini_client, 'analyze_code') as mock_analyze:
            mock_analyze.side_effect = mock_responses
            
            result = await gemini_client.batch_analyze(code_samples)
            
            assert result is not None
            assert "results" in result
            assert len(result["results"]) == 3
            assert "summary" in result
            assert result["summary"]["total_files"] == 3
            assert result["summary"]["patterns_detected"] == 3
            assert result["summary"]["average_quality"] == 81.0  # (80 + 85 + 78) / 3
    
    @pytest.mark.asyncio
    async def test_api_error_handling(self, gemini_client, mock_code_sample):
        """Test API error handling."""
        with patch.object(gemini_client, '_make_api_request') as mock_request:
            mock_request.side_effect = Exception("API request failed")
            
            with pytest.raises(Exception, match="API request failed"):
                await gemini_client.analyze_code(mock_code_sample, "python")
    
    @pytest.mark.asyncio
    async def test_rate_limiting_handling(self, gemini_client, mock_code_sample):
        """Test rate limiting handling."""
        with patch.object(gemini_client, '_make_api_request') as mock_request:
            # First call fails with rate limit
            mock_request.side_effect = [
                Exception("Rate limit exceeded"),
                {"patterns": [], "analysis": {"quality_score": 80.0}}
            ]
            
            with patch('asyncio.sleep') as mock_sleep:
                result = await gemini_client.analyze_code(mock_code_sample, "python", retry_on_rate_limit=True)
                
                assert result is not None
                assert result["analysis"]["quality_score"] == 80.0
                mock_sleep.assert_called_once()  # Should have slept before retry
    
    @pytest.mark.asyncio
    async def test_context_aware_analysis(self, gemini_client, mock_code_sample):
        """Test context-aware analysis."""
        context = {
            "file_path": "src/shopping/cart.py",
            "project_type": "e-commerce",
            "framework": "django",
            "related_files": ["src/shopping/models.py", "src/shopping/views.py"]
        }
        
        mock_response = {
            "patterns": [
                {
                    "pattern_name": "Django Model Pattern",
                    "pattern_type": "FRAMEWORK_PATTERN",
                    "confidence": 0.95,
                    "context_relevance": 0.92,
                    "description": "Django-specific model pattern detected"
                }
            ],
            "analysis": {
                "quality_score": 88.0,
                "framework_compliance": 0.94,
                "context_appropriateness": 0.91
            }
        }
        
        with patch.object(gemini_client, '_make_api_request') as mock_request:
            mock_request.return_value = mock_response
            
            result = await gemini_client.analyze_code(mock_code_sample, "python", context=context)
            
            assert result is not None
            assert result["patterns"][0]["pattern_name"] == "Django Model Pattern"
            assert result["patterns"][0]["context_relevance"] == 0.92
            assert result["analysis"]["framework_compliance"] == 0.94
            assert result["analysis"]["context_appropriateness"] == 0.91


class TestTransformerModel:
    """Test transformer model functionality."""
    
    @pytest.fixture
    def transformer_model(self):
        """Create transformer model instance."""
        config = ModelConfig(
            model_id="transformer-test",
            model_name="Transformer Test Model",
            model_type="classification",
            model_path="gs://models/transformer-test",
            input_shape=[512],
            output_shape=[10],
            preprocessing_config={
                "tokenizer": "bert-base-uncased",
                "max_length": 512
            }
        )
        return TransformerModel(config)
    
    def test_transformer_model_initialization(self, transformer_model):
        """Test transformer model initialization."""
        assert transformer_model is not None
        assert hasattr(transformer_model, 'config')
        assert hasattr(transformer_model, 'tokenizer')
        assert hasattr(transformer_model, 'model')
        assert transformer_model.config.model_id == "transformer-test"
    
    @pytest.mark.asyncio
    async def test_preprocess_text_success(self, transformer_model):
        """Test successful text preprocessing."""
        text = "def calculate_total(items):\n    return sum(item.price for item in items)"
        
        with patch.object(transformer_model, 'tokenizer') as mock_tokenizer:
            mock_tokenizer.return_value = {
                "input_ids": [101, 1045, 2572, 102],
                "attention_mask": [1, 1, 1, 1],
                "token_type_ids": [0, 0, 0, 0]
            }
            
            result = await transformer_model.preprocess_text(text)
            
            assert result is not None
            assert "input_ids" in result
            assert "attention_mask" in result
            assert "token_type_ids" in result
            assert len(result["input_ids"]) == 4
            assert len(result["attention_mask"]) == 4
    
    @pytest.mark.asyncio
    async def test_predict_success(self, transformer_model):
        """Test successful prediction."""
        input_data = {
            "input_ids": [101, 1045, 2572, 102],
            "attention_mask": [1, 1, 1, 1]
        }
        
        with patch.object(transformer_model, 'model') as mock_model:
            mock_model.return_value = Mock(logits=np.array([[0.8, 0.2, 0.1, 0.05, 0.03, 0.02, 0.01, 0.01, 0.01, 0.01]]))
            
            result = await transformer_model.predict(input_data)
            
            assert result is not None
            assert isinstance(result, np.ndarray)
            assert result.shape == (1, 10)
            assert np.argmax(result[0]) == 0  # Highest probability should be first class
    
    @pytest.mark.asyncio
    async def test_predict_batch_success(self, transformer_model):
        """Test successful batch prediction."""
        batch_input = [
            {"input_ids": [101, 1045, 2572, 102], "attention_mask": [1, 1, 1, 1]},
            {"input_ids": [101, 1046, 2573, 102], "attention_mask": [1, 1, 1, 1]},
            {"input_ids": [101, 1047, 2574, 102], "attention_mask": [1, 1, 1, 1]}
        ]
        
        with patch.object(transformer_model, 'model') as mock_model:
            mock_model.return_value = Mock(logits=np.array([
                [0.8, 0.2, 0.1, 0.05, 0.03, 0.02, 0.01, 0.01, 0.01, 0.01],
                [0.1, 0.8, 0.2, 0.05, 0.03, 0.02, 0.01, 0.01, 0.01, 0.01],
                [0.1, 0.1, 0.8, 0.05, 0.03, 0.02, 0.01, 0.01, 0.01, 0.01]
            ]))
            
            result = await transformer_model.predict_batch(batch_input)
            
            assert result is not None
            assert isinstance(result, np.ndarray)
            assert result.shape == (3, 10)
            assert np.argmax(result[0]) == 0  # First sample: class 0
            assert np.argmax(result[1]) == 1  # Second sample: class 1
            assert np.argmax(result[2]) == 2  # Third sample: class 2
    
    @pytest.mark.asyncio
    async def test_fine_tune_success(self, transformer_model):
        """Test successful fine-tuning."""
        training_data = [
            {"code": "def test1():\n    pass", "label": "DESIGN_PATTERN"},
            {"code": "def test2():\n    pass", "label": "ANTI_PATTERN"}
        ]
        
        hyperparameters = {
            "learning_rate": 0.001,
            "batch_size": 16,
            "epochs": 3
        }
        
        with patch.object(transformer_model, '_prepare_training_data') as mock_prepare:
            mock_prepare.return_value = {
                "input_ids": [[101, 1045, 102], [101, 1046, 102]],
                "attention_mask": [[1, 1, 1], [1, 1, 1]],
                "labels": [0, 1]
            }
            
            with patch.object(transformer_model, '_train_model') as mock_train:
                mock_train.return_value = {
                    "final_loss": 0.25,
                    "final_accuracy": 0.95,
                    "training_time": 123.45
                }
                
                result = await transformer_model.fine_tune(training_data, hyperparameters)
                
                assert result is not None
                assert result["final_loss"] == 0.25
                assert result["final_accuracy"] == 0.95
                assert result["training_time"] == 123.45
    
    @pytest.mark.asyncio
    async def test_evaluate_success(self, transformer_model):
        """Test successful model evaluation."""
        test_data = [
            {"code": "def test1():\n    pass", "label": "DESIGN_PATTERN"},
            {"code": "def test2():\n    pass", "label": "ANTI_PATTERN"}
        ]
        
        with patch.object(transformer_model, 'predict_batch') as mock_predict:
            mock_predict.return_value = np.array([
                [0.95, 0.05],  # Correct prediction for first sample
                [0.1, 0.9]     # Correct prediction for second sample
            ])
            
            result = await transformer_model.evaluate(test_data)
            
            assert result is not None
            assert isinstance(result, ModelMetrics)
            assert result.accuracy == 1.0  # Both predictions correct
            assert result.precision > 0.9
            assert result.recall > 0.9
            assert result.f1_score > 0.9
    
    @pytest.mark.asyncio
    async def test_model_serialization(self, transformer_model):
        """Test model serialization and deserialization."""
        model_path = "gs://models/transformer-test-saved"
        
        with patch.object(transformer_model, '_save_model_to_path') as mock_save:
            mock_save.return_value = True
            
            result = await transformer_model.save(model_path)
            
            assert result is True
            mock_save.assert_called_once_with(model_path)
        
        with patch.object(transformer_model, '_load_model_from_path') as mock_load:
            mock_load.return_value = True
            
            result = await transformer_model.load(model_path)
            
            assert result is True
            mock_load.assert_called_once_with(model_path)