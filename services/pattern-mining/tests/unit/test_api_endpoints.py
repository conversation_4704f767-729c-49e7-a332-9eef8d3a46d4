"""
Unit Tests for API Endpoints

Comprehensive tests for all API endpoints including health, patterns, ML, repositories, and analytics.
"""

import pytest
import json
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from typing import Dict, Any, List

from fastapi.testclient import TestClient
from fastapi import status
from httpx import AsyncClient

from pattern_mining.models.patterns import PatternType, SeverityLevel, DetectionType
from pattern_mining.models.api import (
    PatternDetectionRequest, BatchDetectionRequest, DetectionConfig,
    PatternDetectionResponse, PatternSummary
)
from tests.utils.factories import (
    PatternFactory, PatternDetectionRequestFactory, BatchDetectionRequestFactory,
    PatternDetectionResponseFactory, DetectionConfigFactory
)
from tests.utils.generators import TestDataGenerator


class TestHealthEndpoints:
    """Test health check endpoints."""
    
    def test_health_check_success(self, test_client):
        """Test successful health check."""
        response = test_client.get("/health")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "pattern-mining"
        assert "version" in data
        assert "timestamp" in data
    
    @patch("pattern_mining.api.routes.health.get_database_status")
    def test_readiness_check_healthy(self, mock_db_status, test_client):
        """Test readiness check when all dependencies are healthy."""
        mock_db_status.return_value = True
        
        response = test_client.get("/health/ready")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "ready"
        assert "checks" in data
        assert data["checks"]["database"]["status"] == "healthy"
        assert data["checks"]["cache"]["status"] == "healthy"
        assert data["checks"]["ml_models"]["status"] == "healthy"
    
    @patch("pattern_mining.api.routes.health.get_database_status")
    @patch("pattern_mining.api.routes.health.get_cache_status")
    def test_readiness_check_unhealthy(self, mock_cache_status, mock_db_status, test_client):
        """Test readiness check when dependencies are unhealthy."""
        mock_db_status.return_value = False
        mock_cache_status.return_value = False
        
        response = test_client.get("/health/ready")
        
        assert response.status_code == status.HTTP_503_SERVICE_UNAVAILABLE
        data = response.json()
        assert "not ready" in data["detail"].lower()
    
    def test_liveness_check(self, test_client):
        """Test liveness check endpoint."""
        response = test_client.get("/health/live")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["status"] == "alive"
        assert "uptime" in data
        assert "memory_usage" in data
        assert "cpu_usage" in data
    
    def test_metrics_endpoint(self, test_client):
        """Test metrics endpoint."""
        response = test_client.get("/health/metrics")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "requests_total" in data
        assert "response_time_avg" in data
        assert "response_time_p95" in data
        assert "response_time_p99" in data
        assert "error_rate" in data
        assert "memory_usage" in data
        assert "cpu_usage" in data
    
    def test_deep_health_check(self, test_client):
        """Test deep health check endpoint."""
        response = test_client.get("/health/deep")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "database" in data
        assert "cache" in data
        assert "ml_models" in data
        assert "external_services" in data
        assert "system_resources" in data


class TestPatternEndpoints:
    """Test pattern detection endpoints."""
    
    def test_detect_patterns_success(self, test_client, sample_code_data):
        """Test successful pattern detection."""
        request_data = {
            "repository_id": "test-repo-123",
            "ast_data": sample_code_data["python"]["ast"],
            "code_content": sample_code_data["python"]["code"],
            "file_path": "test.py",
            "language": "python",
            "detection_config": {
                "confidence_threshold": 0.7,
                "max_patterns_per_file": 50,
                "enable_ml_models": True,
                "enable_heuristic_detection": True
            }
        }
        
        with patch("pattern_mining.api.routes.patterns.get_pattern_detector_manager") as mock_manager:
            mock_detector = AsyncMock()
            mock_detector.detect_patterns.return_value = [
                PatternFactory(pattern_type=PatternType.DESIGN_PATTERN)
            ]
            mock_detector.get_model_versions.return_value = {"ml_inference": "1.0.0"}
            mock_manager.return_value = mock_detector
            
            response = test_client.post("/api/v1/patterns/detect", json=request_data)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "request_id" in data
        assert "patterns" in data
        assert "summary" in data
        assert "processing_time_ms" in data
        assert "model_versions" in data
        
        # Validate pattern structure
        if data["patterns"]:
            pattern = data["patterns"][0]
            assert "pattern_id" in pattern
            assert "pattern_name" in pattern
            assert "pattern_type" in pattern
            assert "severity" in pattern
            assert "confidence" in pattern
            assert "location" in pattern
    
    def test_detect_patterns_invalid_language(self, test_client):
        """Test pattern detection with invalid language."""
        request_data = {
            "repository_id": "test-repo-123",
            "ast_data": {"type": "Module", "children": []},
            "code_content": "print('hello')",
            "file_path": "test.py",
            "language": "invalid_language",
            "detection_config": {
                "confidence_threshold": 0.7,
                "enable_ml_models": True
            }
        }
        
        response = test_client.post("/api/v1/patterns/detect", json=request_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "validation" in data["error"].lower()
    
    def test_detect_patterns_empty_code(self, test_client):
        """Test pattern detection with empty code."""
        request_data = {
            "repository_id": "test-repo-123",
            "ast_data": {"type": "Module", "children": []},
            "code_content": "",
            "file_path": "test.py",
            "language": "python",
            "detection_config": {
                "confidence_threshold": 0.7,
                "enable_ml_models": True
            }
        }
        
        response = test_client.post("/api/v1/patterns/detect", json=request_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "empty" in data["details"][0]["msg"].lower()
    
    def test_detect_patterns_invalid_ast(self, test_client):
        """Test pattern detection with invalid AST data."""
        request_data = {
            "repository_id": "test-repo-123",
            "ast_data": {"invalid": "structure"},
            "code_content": "print('hello')",
            "file_path": "test.py",
            "language": "python",
            "detection_config": {
                "confidence_threshold": 0.7,
                "enable_ml_models": True
            }
        }
        
        response = test_client.post("/api/v1/patterns/detect", json=request_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "ast" in data["details"][0]["msg"].lower()
    
    def test_batch_detect_patterns_success(self, test_client):
        """Test successful batch pattern detection."""
        request_data = {
            "repository_id": "test-repo-123",
            "repository_url": "https://github.com/test/repo",
            "file_patterns": ["*.py", "*.js"],
            "detection_config": {
                "confidence_threshold": 0.7,
                "enable_ml_models": True,
                "enable_heuristic_detection": True
            },
            "parallel_jobs": 5,
            "timeout_seconds": 300
        }
        
        with patch("pattern_mining.api.routes.patterns.start_batch_detection") as mock_batch:
            mock_batch.return_value = {
                "job_id": "batch-job-123",
                "status": "queued",
                "estimated_duration": 300
            }
            
            response = test_client.post("/api/v1/patterns/batch", json=request_data)
        
        assert response.status_code == status.HTTP_202_ACCEPTED
        data = response.json()
        assert "job_id" in data
        assert data["status"] == "queued"
        assert "estimated_duration" in data
    
    def test_batch_detect_patterns_invalid_patterns(self, test_client):
        """Test batch detection with invalid file patterns."""
        request_data = {
            "repository_id": "test-repo-123",
            "repository_url": "https://github.com/test/repo",
            "file_patterns": [],  # Empty patterns
            "detection_config": {
                "confidence_threshold": 0.7,
                "enable_ml_models": True
            }
        }
        
        response = test_client.post("/api/v1/patterns/batch", json=request_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "empty" in data["details"][0]["msg"].lower()
    
    def test_get_batch_job_status(self, test_client):
        """Test getting batch job status."""
        job_id = "batch-job-123"
        
        with patch("pattern_mining.api.routes.patterns.get_batch_job_status") as mock_status:
            mock_status.return_value = {
                "job_id": job_id,
                "status": "processing",
                "progress": 45.5,
                "files_processed": 23,
                "files_total": 50,
                "patterns_detected": 156,
                "estimated_completion": "2025-01-01T12:00:00Z"
            }
            
            response = test_client.get(f"/api/v1/patterns/batch/{job_id}")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["job_id"] == job_id
        assert data["status"] == "processing"
        assert data["progress"] == 45.5
        assert data["files_processed"] == 23
        assert data["files_total"] == 50
        assert data["patterns_detected"] == 156
    
    def test_get_batch_job_not_found(self, test_client):
        """Test getting non-existent batch job."""
        job_id = "non-existent-job"
        
        with patch("pattern_mining.api.routes.patterns.get_batch_job_status") as mock_status:
            mock_status.return_value = None
            
            response = test_client.get(f"/api/v1/patterns/batch/{job_id}")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    def test_get_patterns_by_repository(self, test_client, sample_patterns):
        """Test getting patterns by repository."""
        repo_id = "test-repo-123"
        
        with patch("pattern_mining.api.routes.patterns.get_repository_patterns") as mock_patterns:
            mock_patterns.return_value = {
                "patterns": sample_patterns,
                "total": len(sample_patterns),
                "page": 1,
                "page_size": 50
            }
            
            response = test_client.get(f"/api/v1/patterns/repository/{repo_id}")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "patterns" in data
        assert data["total"] == len(sample_patterns)
        assert data["page"] == 1
        assert data["page_size"] == 50
    
    def test_get_patterns_by_repository_with_filters(self, test_client, sample_patterns):
        """Test getting patterns by repository with filters."""
        repo_id = "test-repo-123"
        
        with patch("pattern_mining.api.routes.patterns.get_repository_patterns") as mock_patterns:
            mock_patterns.return_value = {
                "patterns": [p for p in sample_patterns if p.severity == SeverityLevel.HIGH],
                "total": 1,
                "page": 1,
                "page_size": 50
            }
            
            response = test_client.get(
                f"/api/v1/patterns/repository/{repo_id}",
                params={
                    "severity": "HIGH",
                    "pattern_type": "SECURITY_ISSUE",
                    "confidence_min": 0.8
                }
            )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "patterns" in data
        assert data["total"] >= 0
    
    def test_get_pattern_by_id(self, test_client, sample_patterns):
        """Test getting single pattern by ID."""
        pattern = sample_patterns[0]
        pattern_id = pattern.pattern_id
        
        with patch("pattern_mining.api.routes.patterns.get_pattern_by_id") as mock_pattern:
            mock_pattern.return_value = pattern
            
            response = test_client.get(f"/api/v1/patterns/{pattern_id}")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["pattern_id"] == pattern_id
        assert data["pattern_name"] == pattern.pattern_name
        assert data["pattern_type"] == pattern.pattern_type
        assert data["severity"] == pattern.severity
    
    def test_get_pattern_by_id_not_found(self, test_client):
        """Test getting non-existent pattern."""
        pattern_id = "non-existent-pattern"
        
        with patch("pattern_mining.api.routes.patterns.get_pattern_by_id") as mock_pattern:
            mock_pattern.return_value = None
            
            response = test_client.get(f"/api/v1/patterns/{pattern_id}")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    def test_find_similar_patterns(self, test_client, sample_patterns):
        """Test finding similar patterns."""
        pattern_id = "test-pattern-123"
        
        with patch("pattern_mining.api.routes.patterns.find_similar_patterns") as mock_similar:
            mock_similar.return_value = {
                "similar_patterns": sample_patterns[:2],
                "similarity_scores": [0.95, 0.87],
                "total_found": 2
            }
            
            response = test_client.get(f"/api/v1/patterns/similar/{pattern_id}")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "similar_patterns" in data
        assert "similarity_scores" in data
        assert data["total_found"] == 2
        assert len(data["similar_patterns"]) == 2
        assert len(data["similarity_scores"]) == 2
    
    def test_find_similar_patterns_with_threshold(self, test_client, sample_patterns):
        """Test finding similar patterns with custom threshold."""
        pattern_id = "test-pattern-123"
        
        with patch("pattern_mining.api.routes.patterns.find_similar_patterns") as mock_similar:
            mock_similar.return_value = {
                "similar_patterns": sample_patterns[:1],
                "similarity_scores": [0.95],
                "total_found": 1
            }
            
            response = test_client.get(
                f"/api/v1/patterns/similar/{pattern_id}",
                params={"threshold": 0.9, "limit": 5}
            )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["total_found"] == 1
        assert data["similarity_scores"][0] >= 0.9
    
    def test_delete_pattern(self, test_client):
        """Test deleting a pattern."""
        pattern_id = "test-pattern-123"
        
        with patch("pattern_mining.api.routes.patterns.delete_pattern") as mock_delete:
            mock_delete.return_value = True
            
            response = test_client.delete(f"/api/v1/patterns/{pattern_id}")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "deleted" in data["message"].lower()
    
    def test_delete_pattern_not_found(self, test_client):
        """Test deleting non-existent pattern."""
        pattern_id = "non-existent-pattern"
        
        with patch("pattern_mining.api.routes.patterns.delete_pattern") as mock_delete:
            mock_delete.return_value = False
            
            response = test_client.delete(f"/api/v1/patterns/{pattern_id}")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    def test_export_patterns(self, test_client, sample_patterns):
        """Test exporting patterns."""
        repo_id = "test-repo-123"
        
        with patch("pattern_mining.api.routes.patterns.export_patterns") as mock_export:
            mock_export.return_value = {
                "export_id": "export-123",
                "download_url": "https://example.com/download/export-123",
                "format": "json",
                "expires_at": "2025-01-01T12:00:00Z"
            }
            
            response = test_client.post(
                f"/api/v1/patterns/repository/{repo_id}/export",
                json={"format": "json", "include_metadata": True}
            )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "export_id" in data
        assert "download_url" in data
        assert data["format"] == "json"
        assert "expires_at" in data


class TestMLEndpoints:
    """Test ML model endpoints."""
    
    def test_list_models(self, test_client):
        """Test listing ML models."""
        with patch("pattern_mining.api.routes.ml.get_ml_manager") as mock_manager:
            mock_ml_manager = AsyncMock()
            mock_ml_manager.list_models.return_value = [
                {
                    "id": "pattern-detector-v1",
                    "name": "Pattern Detector",
                    "version": "1.0.0",
                    "status": "active",
                    "accuracy": 0.95,
                    "created_at": "2025-01-01T10:00:00Z"
                },
                {
                    "id": "security-analyzer-v1",
                    "name": "Security Analyzer",
                    "version": "1.0.0",
                    "status": "active",
                    "accuracy": 0.92,
                    "created_at": "2025-01-01T11:00:00Z"
                }
            ]
            mock_manager.return_value = mock_ml_manager
            
            response = test_client.get("/api/v1/ml/models")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 2
        assert data[0]["id"] == "pattern-detector-v1"
        assert data[0]["status"] == "active"
        assert data[0]["accuracy"] == 0.95
    
    def test_get_model_info(self, test_client):
        """Test getting model information."""
        model_id = "pattern-detector-v1"
        
        with patch("pattern_mining.api.routes.ml.get_ml_manager") as mock_manager:
            mock_ml_manager = AsyncMock()
            mock_ml_manager.get_model_info.return_value = {
                "id": model_id,
                "name": "Pattern Detector",
                "version": "1.0.0",
                "status": "active",
                "accuracy": 0.95,
                "precision": 0.93,
                "recall": 0.97,
                "f1_score": 0.95,
                "training_data_size": 10000,
                "created_at": "2025-01-01T10:00:00Z",
                "updated_at": "2025-01-01T10:00:00Z"
            }
            mock_manager.return_value = mock_ml_manager
            
            response = test_client.get(f"/api/v1/ml/models/{model_id}")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["id"] == model_id
        assert data["name"] == "Pattern Detector"
        assert data["accuracy"] == 0.95
        assert data["precision"] == 0.93
        assert data["recall"] == 0.97
        assert data["f1_score"] == 0.95
    
    def test_get_model_info_not_found(self, test_client):
        """Test getting non-existent model."""
        model_id = "non-existent-model"
        
        with patch("pattern_mining.api.routes.ml.get_ml_manager") as mock_manager:
            mock_ml_manager = AsyncMock()
            mock_ml_manager.get_model_info.return_value = None
            mock_manager.return_value = mock_ml_manager
            
            response = test_client.get(f"/api/v1/ml/models/{model_id}")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    def test_predict_with_model(self, test_client, sample_code_data):
        """Test making prediction with model."""
        model_id = "pattern-detector-v1"
        
        with patch("pattern_mining.api.routes.ml.get_ml_manager") as mock_manager:
            mock_ml_manager = AsyncMock()
            mock_ml_manager.predict.return_value = {
                "prediction": "DESIGN_PATTERN",
                "confidence": 0.92,
                "probabilities": {
                    "DESIGN_PATTERN": 0.92,
                    "ANTI_PATTERN": 0.05,
                    "CODE_SMELL": 0.03
                },
                "processing_time_ms": 25
            }
            mock_manager.return_value = mock_ml_manager
            
            response = test_client.post(
                f"/api/v1/ml/models/{model_id}/predict",
                json={
                    "code": sample_code_data["python"]["code"],
                    "language": "python",
                    "context": {"file_path": "test.py"}
                }
            )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["model_id"] == model_id
        assert data["prediction"] == "DESIGN_PATTERN"
        assert data["confidence"] == 0.92
        assert "probabilities" in data
        assert data["processing_time_ms"] == 25
    
    def test_predict_with_invalid_input(self, test_client):
        """Test prediction with invalid input."""
        model_id = "pattern-detector-v1"
        
        response = test_client.post(
            f"/api/v1/ml/models/{model_id}/predict",
            json={
                "code": "",  # Empty code
                "language": "python"
            }
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_train_model(self, test_client):
        """Test training a model."""
        model_id = "pattern-detector-v1"
        
        with patch("pattern_mining.api.routes.ml.get_model_trainer") as mock_trainer:
            mock_trainer_instance = AsyncMock()
            mock_trainer_instance.train.return_value = {
                "job_id": "train-job-123",
                "status": "queued",
                "estimated_duration": 3600,
                "created_at": "2025-01-01T10:00:00Z"
            }
            mock_trainer.return_value = mock_trainer_instance
            
            response = test_client.post(
                f"/api/v1/ml/models/{model_id}/train",
                json={
                    "training_data": {
                        "data_source": "bigquery",
                        "dataset": "patterns_dataset",
                        "table": "training_patterns"
                    },
                    "hyperparameters": {
                        "learning_rate": 0.001,
                        "batch_size": 32,
                        "epochs": 10
                    },
                    "validation_split": 0.2
                }
            )
        
        assert response.status_code == status.HTTP_202_ACCEPTED
        data = response.json()
        assert data["model_id"] == model_id
        assert data["job_id"] == "train-job-123"
        assert data["status"] == "queued"
        assert data["estimated_duration"] == 3600
    
    def test_get_training_job_status(self, test_client):
        """Test getting training job status."""
        job_id = "train-job-123"
        
        with patch("pattern_mining.api.routes.ml.get_training_job_status") as mock_status:
            mock_status.return_value = {
                "job_id": job_id,
                "model_id": "pattern-detector-v1",
                "status": "running",
                "progress": 65.5,
                "epoch": 7,
                "total_epochs": 10,
                "current_loss": 0.15,
                "validation_accuracy": 0.89,
                "estimated_completion": "2025-01-01T12:00:00Z",
                "created_at": "2025-01-01T10:00:00Z"
            }
            
            response = test_client.get(f"/api/v1/ml/training/{job_id}")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["job_id"] == job_id
        assert data["model_id"] == "pattern-detector-v1"
        assert data["status"] == "running"
        assert data["progress"] == 65.5
        assert data["epoch"] == 7
        assert data["validation_accuracy"] == 0.89
    
    def test_get_training_job_not_found(self, test_client):
        """Test getting non-existent training job."""
        job_id = "non-existent-job"
        
        with patch("pattern_mining.api.routes.ml.get_training_job_status") as mock_status:
            mock_status.return_value = None
            
            response = test_client.get(f"/api/v1/ml/training/{job_id}")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    def test_evaluate_model(self, test_client):
        """Test evaluating a model."""
        model_id = "pattern-detector-v1"
        
        with patch("pattern_mining.api.routes.ml.get_ml_manager") as mock_manager:
            mock_ml_manager = AsyncMock()
            mock_ml_manager.evaluate_model.return_value = {
                "accuracy": 0.95,
                "precision": 0.93,
                "recall": 0.97,
                "f1_score": 0.95,
                "confusion_matrix": [[85, 3], [2, 10]],
                "classification_report": {
                    "DESIGN_PATTERN": {"precision": 0.97, "recall": 0.96, "f1-score": 0.97},
                    "ANTI_PATTERN": {"precision": 0.77, "recall": 0.83, "f1-score": 0.80}
                },
                "evaluation_time_ms": 1500
            }
            mock_manager.return_value = mock_ml_manager
            
            response = test_client.post(
                f"/api/v1/ml/models/{model_id}/evaluate",
                json={
                    "test_data": {
                        "data_source": "bigquery",
                        "dataset": "patterns_dataset",
                        "table": "test_patterns"
                    }
                }
            )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["model_id"] == model_id
        assert data["accuracy"] == 0.95
        assert data["precision"] == 0.93
        assert data["recall"] == 0.97
        assert data["f1_score"] == 0.95
        assert "confusion_matrix" in data
        assert "classification_report" in data
    
    def test_delete_model(self, test_client):
        """Test deleting a model."""
        model_id = "pattern-detector-v1"
        
        with patch("pattern_mining.api.routes.ml.get_ml_manager") as mock_manager:
            mock_ml_manager = AsyncMock()
            mock_ml_manager.delete_model.return_value = True
            mock_manager.return_value = mock_ml_manager
            
            response = test_client.delete(f"/api/v1/ml/models/{model_id}")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "deleted" in data["message"].lower()
        assert model_id in data["message"]
    
    def test_delete_model_not_found(self, test_client):
        """Test deleting non-existent model."""
        model_id = "non-existent-model"
        
        with patch("pattern_mining.api.routes.ml.get_ml_manager") as mock_manager:
            mock_ml_manager = AsyncMock()
            mock_ml_manager.delete_model.return_value = False
            mock_manager.return_value = mock_ml_manager
            
            response = test_client.delete(f"/api/v1/ml/models/{model_id}")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND


class TestRepositoryEndpoints:
    """Test repository analysis endpoints."""
    
    def test_analyze_repository(self, test_client):
        """Test repository analysis."""
        request_data = {
            "repository_url": "https://github.com/test/repo",
            "branch": "main",
            "analysis_type": "comprehensive",
            "detection_config": {
                "confidence_threshold": 0.7,
                "enable_ml_models": True,
                "enable_heuristic_detection": True
            },
            "include_tests": True,
            "generate_report": True
        }
        
        with patch("pattern_mining.api.routes.repositories.start_repository_analysis") as mock_analysis:
            mock_analysis.return_value = {
                "analysis_id": "analysis-123",
                "status": "queued",
                "estimated_duration": 1800,
                "created_at": "2025-01-01T10:00:00Z"
            }
            
            response = test_client.post("/api/v1/repositories/analyze", json=request_data)
        
        assert response.status_code == status.HTTP_202_ACCEPTED
        data = response.json()
        assert "analysis_id" in data
        assert data["status"] == "queued"
        assert data["estimated_duration"] == 1800
    
    def test_analyze_repository_invalid_url(self, test_client):
        """Test repository analysis with invalid URL."""
        request_data = {
            "repository_url": "invalid-url",
            "analysis_type": "comprehensive",
            "detection_config": {
                "confidence_threshold": 0.7,
                "enable_ml_models": True
            }
        }
        
        response = test_client.post("/api/v1/repositories/analyze", json=request_data)
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "url" in data["details"][0]["msg"].lower()
    
    def test_get_repository_analysis_status(self, test_client):
        """Test getting repository analysis status."""
        analysis_id = "analysis-123"
        
        with patch("pattern_mining.api.routes.repositories.get_analysis_status") as mock_status:
            mock_status.return_value = {
                "analysis_id": analysis_id,
                "repository_url": "https://github.com/test/repo",
                "status": "processing",
                "progress": 65.5,
                "files_processed": 45,
                "files_total": 70,
                "patterns_detected": 234,
                "current_stage": "pattern_detection",
                "estimated_completion": "2025-01-01T12:00:00Z"
            }
            
            response = test_client.get(f"/api/v1/repositories/analysis/{analysis_id}")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["analysis_id"] == analysis_id
        assert data["status"] == "processing"
        assert data["progress"] == 65.5
        assert data["files_processed"] == 45
        assert data["patterns_detected"] == 234
    
    def test_get_repository_analysis_results(self, test_client, sample_patterns):
        """Test getting repository analysis results."""
        analysis_id = "analysis-123"
        
        with patch("pattern_mining.api.routes.repositories.get_analysis_results") as mock_results:
            mock_results.return_value = {
                "analysis_id": analysis_id,
                "repository_url": "https://github.com/test/repo",
                "status": "completed",
                "summary": {
                    "files_analyzed": 70,
                    "lines_of_code": 15000,
                    "total_patterns": 234,
                    "critical_patterns": 5,
                    "high_patterns": 23,
                    "quality_score": 85.5,
                    "security_score": 92.3,
                    "maintainability_score": 78.9
                },
                "patterns": sample_patterns,
                "report_url": "https://example.com/reports/analysis-123",
                "completed_at": "2025-01-01T12:00:00Z"
            }
            
            response = test_client.get(f"/api/v1/repositories/analysis/{analysis_id}/results")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["analysis_id"] == analysis_id
        assert data["status"] == "completed"
        assert "summary" in data
        assert data["summary"]["files_analyzed"] == 70
        assert data["summary"]["total_patterns"] == 234
        assert "patterns" in data
        assert "report_url" in data
    
    def test_get_repository_summary(self, test_client):
        """Test getting repository summary."""
        repo_id = "test-repo-123"
        
        with patch("pattern_mining.api.routes.repositories.get_repository_summary") as mock_summary:
            mock_summary.return_value = {
                "repository_id": repo_id,
                "name": "test-repo",
                "url": "https://github.com/test/repo",
                "language": "python",
                "total_files": 70,
                "total_lines": 15000,
                "total_patterns": 234,
                "critical_patterns": 5,
                "high_patterns": 23,
                "quality_score": 85.5,
                "security_score": 92.3,
                "maintainability_score": 78.9,
                "last_analysis": "2025-01-01T12:00:00Z",
                "analysis_count": 15
            }
            
            response = test_client.get(f"/api/v1/repositories/{repo_id}/summary")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["repository_id"] == repo_id
        assert data["name"] == "test-repo"
        assert data["total_files"] == 70
        assert data["total_patterns"] == 234
        assert data["quality_score"] == 85.5
    
    def test_compare_repositories(self, test_client):
        """Test comparing repositories."""
        request_data = {
            "repository_ids": ["repo-1", "repo-2", "repo-3"],
            "comparison_type": "quality",
            "include_patterns": True,
            "similarity_threshold": 0.8
        }
        
        with patch("pattern_mining.api.routes.repositories.compare_repositories") as mock_compare:
            mock_compare.return_value = {
                "comparison_id": "comp-123",
                "repository_ids": ["repo-1", "repo-2", "repo-3"],
                "comparison_type": "quality",
                "results": {
                    "repo-1": {"quality_score": 85.5, "patterns": 45},
                    "repo-2": {"quality_score": 78.2, "patterns": 67},
                    "repo-3": {"quality_score": 92.1, "patterns": 23}
                },
                "similarity_matrix": {
                    "repo-1": {"repo-2": 0.65, "repo-3": 0.82},
                    "repo-2": {"repo-1": 0.65, "repo-3": 0.58},
                    "repo-3": {"repo-1": 0.82, "repo-2": 0.58}
                },
                "recommendations": [
                    "repo-3 shows best practices that could be applied to repo-2",
                    "repo-1 and repo-3 have similar architectural patterns"
                ]
            }
            
            response = test_client.post("/api/v1/repositories/compare", json=request_data)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "comparison_id" in data
        assert data["comparison_type"] == "quality"
        assert "results" in data
        assert "similarity_matrix" in data
        assert "recommendations" in data
        assert len(data["results"]) == 3


class TestAnalyticsEndpoints:
    """Test analytics endpoints."""
    
    def test_get_analytics_report(self, test_client):
        """Test getting analytics report."""
        with patch("pattern_mining.api.routes.analytics.generate_analytics_report") as mock_report:
            mock_report.return_value = {
                "report_id": "report-123",
                "time_range": "last_30_days",
                "start_date": "2024-12-01T00:00:00Z",
                "end_date": "2024-12-31T23:59:59Z",
                "summary": {
                    "total_analyses": 1234,
                    "total_patterns": 45678,
                    "avg_quality_score": 82.5,
                    "top_pattern_types": {
                        "DESIGN_PATTERN": 15678,
                        "CODE_SMELL": 12345,
                        "SECURITY_ISSUE": 8901
                    }
                },
                "trends": {
                    "quality_trend": "improving",
                    "pattern_detection_trend": "stable",
                    "security_trend": "improving"
                },
                "performance_metrics": {
                    "avg_analysis_time": 45.6,
                    "avg_patterns_per_analysis": 37.0,
                    "success_rate": 99.2
                }
            }
            
            response = test_client.get(
                "/api/v1/analytics/report",
                params={
                    "time_range": "last_30_days",
                    "granularity": "daily",
                    "include_trends": True
                }
            )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "report_id" in data
        assert data["time_range"] == "last_30_days"
        assert "summary" in data
        assert "trends" in data
        assert "performance_metrics" in data
    
    def test_get_pattern_trends(self, test_client):
        """Test getting pattern trends."""
        with patch("pattern_mining.api.routes.analytics.get_pattern_trends") as mock_trends:
            mock_trends.return_value = {
                "trend_type": "pattern_detection",
                "time_range": "last_90_days",
                "data_points": [
                    {"date": "2024-12-01", "value": 123, "trend": "up"},
                    {"date": "2024-12-02", "value": 134, "trend": "up"},
                    {"date": "2024-12-03", "value": 128, "trend": "down"}
                ],
                "growth_rate": 15.6,
                "trend_direction": "increasing",
                "seasonal_patterns": [
                    {"period": "weekday", "multiplier": 1.2},
                    {"period": "weekend", "multiplier": 0.8}
                ]
            }
            
            response = test_client.get(
                "/api/v1/analytics/trends",
                params={
                    "metric": "pattern_detection",
                    "time_range": "last_90_days",
                    "granularity": "daily"
                }
            )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["trend_type"] == "pattern_detection"
        assert data["time_range"] == "last_90_days"
        assert "data_points" in data
        assert data["growth_rate"] == 15.6
        assert data["trend_direction"] == "increasing"
    
    def test_get_performance_metrics(self, test_client):
        """Test getting performance metrics."""
        with patch("pattern_mining.api.routes.analytics.get_performance_metrics") as mock_metrics:
            mock_metrics.return_value = {
                "time_range": "last_24_hours",
                "average_response_time": 45.6,
                "p95_response_time": 89.2,
                "p99_response_time": 156.8,
                "throughput": 125.5,
                "error_rate": 0.8,
                "cpu_usage": 65.2,
                "memory_usage": 78.9,
                "request_distribution": {
                    "pattern_detection": 78.5,
                    "batch_analysis": 15.2,
                    "model_training": 6.3
                }
            }
            
            response = test_client.get(
                "/api/v1/analytics/performance",
                params={"time_range": "last_24_hours"}
            )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["time_range"] == "last_24_hours"
        assert data["average_response_time"] == 45.6
        assert data["p95_response_time"] == 89.2
        assert data["throughput"] == 125.5
        assert data["error_rate"] == 0.8
        assert "request_distribution" in data
    
    def test_custom_analytics_query(self, test_client):
        """Test custom analytics query."""
        query_data = {
            "query_type": "pattern_analysis",
            "metrics": ["count", "avg_confidence", "severity_distribution"],
            "filters": {
                "pattern_type": ["SECURITY_ISSUE", "PERFORMANCE_ISSUE"],
                "severity": ["HIGH", "CRITICAL"],
                "date_range": {
                    "start": "2024-12-01T00:00:00Z",
                    "end": "2024-12-31T23:59:59Z"
                }
            },
            "group_by": ["pattern_type", "severity"],
            "limit": 1000
        }
        
        with patch("pattern_mining.api.routes.analytics.execute_custom_query") as mock_query:
            mock_query.return_value = {
                "query_id": "query-123",
                "results": [
                    {
                        "pattern_type": "SECURITY_ISSUE",
                        "severity": "HIGH",
                        "count": 234,
                        "avg_confidence": 0.89,
                        "severity_distribution": {"HIGH": 234}
                    },
                    {
                        "pattern_type": "PERFORMANCE_ISSUE",
                        "severity": "CRITICAL",
                        "count": 45,
                        "avg_confidence": 0.95,
                        "severity_distribution": {"CRITICAL": 45}
                    }
                ],
                "total_results": 2,
                "execution_time_ms": 234
            }
            
            response = test_client.post("/api/v1/analytics/query", json=query_data)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "query_id" in data
        assert "results" in data
        assert data["total_results"] == 2
        assert data["execution_time_ms"] == 234
        assert len(data["results"]) == 2