"""
Unit tests for Gemini API integration components.

Tests cover configuration, client functionality, analysis services,
embeddings, and integration layer.
"""

import asyncio
import pytest
import numpy as np
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
import json

# Import the components to test
from pattern_mining.config.gemini import GeminiConfig, get_gemini_config
from pattern_mining.ml.gemini_client import GeminiClient, GeminiRateLimiter
from pattern_mining.ml.gemini_analyzer import <PERSON>Anal<PERSON><PERSON>, AnalysisType, CodeContext
from pattern_mining.ml.gemini_embeddings import GeminiEmbeddingService, EmbeddingType
from pattern_mining.ml.gemini_integration import GeminiIntegration, IntegrationMode


class TestGeminiConfig:
    """Test Gemini configuration."""
    
    def test_default_config(self):
        """Test default configuration values."""
        config = GeminiConfig()
        
        assert config.temperature == 0.1
        assert config.max_output_tokens == 8192
        assert config.context_window == 1000000
        assert config.requests_per_minute == 60
        assert config.enable_caching is True
    
    def test_config_validation(self):
        """Test configuration validation."""
        # Test invalid temperature
        with pytest.raises(ValueError, match="Temperature must be between 0.0 and 1.0"):
            GeminiConfig(temperature=1.5)
        
        # Test invalid max_output_tokens
        with pytest.raises(ValueError, match="Max output tokens cannot exceed 1M"):
            GeminiConfig(max_output_tokens=2000000)
    
    def test_config_properties(self):
        """Test configuration properties."""
        config = GeminiConfig()
        
        gen_config = config.generation_config
        assert gen_config["temperature"] == 0.1
        assert gen_config["max_output_tokens"] == 8192
        
        safety_config = config.safety_config
        assert isinstance(safety_config, list)
        assert len(safety_config) > 0
    
    def test_singleton_config(self):
        """Test singleton configuration."""
        config1 = get_gemini_config()
        config2 = get_gemini_config()
        
        assert config1 is config2


class TestGeminiRateLimiter:
    """Test rate limiter functionality."""
    
    def test_rate_limiter_initialization(self):
        """Test rate limiter initialization."""
        limiter = GeminiRateLimiter(requests_per_minute=60, tokens_per_minute=60000)
        
        assert limiter.requests_bucket.capacity == 60
        assert limiter.tokens_bucket.capacity == 60000
        assert limiter.requests_bucket.tokens == 60
        assert limiter.tokens_bucket.tokens == 60000
    
    @pytest.mark.asyncio
    async def test_rate_limiter_acquire(self):
        """Test rate limiter token acquisition."""
        limiter = GeminiRateLimiter(requests_per_minute=60, tokens_per_minute=60000)
        
        # Should be able to acquire initially
        result = await limiter.acquire(1000)
        assert result is True
        
        # Tokens should be reduced
        assert limiter.requests_bucket.tokens == 59
        assert limiter.tokens_bucket.tokens == 59000
    
    def test_rate_limiter_wait_time(self):
        """Test wait time calculation."""
        limiter = GeminiRateLimiter(requests_per_minute=60, tokens_per_minute=60000)
        
        # Initially should be 0 wait time
        wait_time = limiter.get_wait_time(1000)
        assert wait_time == 0
        
        # After using all tokens
        limiter.tokens_bucket.tokens = 0
        wait_time = limiter.get_wait_time(1000)
        assert wait_time > 0


class TestGeminiClient:
    """Test Gemini client functionality."""
    
    @pytest.fixture
    def mock_config(self):
        """Create mock configuration."""
        config = MagicMock()
        config.api_key = MagicMock()
        config.api_key.get_secret_value.return_value = "test_key"
        config.requests_per_minute = 60
        config.tokens_per_minute = 60000
        config.concurrent_requests = 10
        config.generation_config = {"temperature": 0.1}
        config.safety_config = []
        config.enable_caching = True
        config.cache_max_size = 1000
        config.cache_ttl = 3600
        config.max_retries = 3
        config.retry_delay = 1.0
        config.retry_exponential_base = 2.0
        return config
    
    @pytest.fixture
    def client(self, mock_config):
        """Create test client."""
        with patch('pattern_mining.ml.gemini_client.get_gemini_config', return_value=mock_config):
            with patch('pattern_mining.ml.gemini_client.genai.configure'):
                return GeminiClient(mock_config)
    
    def test_client_initialization(self, client):
        """Test client initialization."""
        assert client.config is not None
        assert client.rate_limiter is not None
        assert client.cache == {}
        assert client.request_metrics == {}
    
    def test_generate_request_id(self, client):
        """Test request ID generation."""
        prompt = "test prompt"
        model = "test-model"
        
        id1 = client._generate_request_id(prompt, model)
        id2 = client._generate_request_id(prompt, model)
        
        assert isinstance(id1, str)
        assert len(id1) == 32  # MD5 hash length
        assert id1 != id2  # Should be different due to timestamp
    
    def test_cache_key_generation(self, client):
        """Test cache key generation."""
        prompt = "test prompt"
        model = "test-model"
        
        key1 = client._get_cache_key(prompt, model)
        key2 = client._get_cache_key(prompt, model)
        
        assert key1 == key2
        assert isinstance(key1, str)
        assert len(key1) == 64  # SHA256 hash length
    
    def test_cache_operations(self, client):
        """Test cache operations."""
        cache_key = "test_key"
        response = {"text": "test response"}
        
        # Test cache miss
        assert not client._is_cache_valid(cache_key)
        
        # Test cache set
        client._cache_response(cache_key, response)
        assert client._is_cache_valid(cache_key)
        assert client.cache[cache_key] == response
        
        # Test cache cleanup
        client.cache = {f"key_{i}": f"value_{i}" for i in range(2000)}
        client._cleanup_cache()
        assert len(client.cache) <= client.config.cache_max_size
    
    @pytest.mark.asyncio
    async def test_client_context_manager(self, mock_config):
        """Test client async context manager."""
        with patch('pattern_mining.ml.gemini_client.get_gemini_config', return_value=mock_config):
            with patch('pattern_mining.ml.gemini_client.genai.configure'):
                async with GeminiClient(mock_config) as client:
                    assert client.session is not None
                
                # Session should be closed after exit
                assert client.session is None
    
    def test_client_metrics(self, client):
        """Test client metrics."""
        metrics = client.get_metrics()
        
        assert isinstance(metrics, dict)
        assert "total_requests" in metrics
        assert "completed_requests" in metrics
        assert "failed_requests" in metrics
        assert "success_rate" in metrics
        assert "avg_response_time" in metrics


class TestGeminiAnalyzer:
    """Test Gemini analyzer functionality."""
    
    @pytest.fixture
    def mock_client(self):
        """Create mock Gemini client."""
        client = AsyncMock()
        client.generate_content = AsyncMock(return_value={
            "text": json.dumps({
                "confidence": 85,
                "findings": [{"pattern_type": "singleton", "confidence": 90}],
                "explanation": "Test explanation",
                "recommendations": ["Use dependency injection"]
            }),
            "usage": {"total_tokens": 1000}
        })
        return client
    
    @pytest.fixture
    def analyzer(self, mock_client):
        """Create test analyzer."""
        return GeminiAnalyzer(mock_client)
    
    def test_analyzer_initialization(self, analyzer):
        """Test analyzer initialization."""
        assert analyzer.client is not None
        assert analyzer.config is not None
        assert analyzer._analysis_prompts is not None
        assert analyzer._pattern_definitions is not None
    
    def test_load_analysis_prompts(self, analyzer):
        """Test analysis prompts loading."""
        prompts = analyzer._analysis_prompts
        
        assert AnalysisType.PATTERN_DETECTION in prompts
        assert AnalysisType.CODE_EXPLANATION in prompts
        assert AnalysisType.ANTI_PATTERN_DETECTION in prompts
        
        for prompt in prompts.values():
            assert isinstance(prompt, str)
            assert len(prompt) > 100  # Should be substantial prompts
    
    def test_create_analysis_prompt(self, analyzer):
        """Test analysis prompt creation."""
        context = CodeContext(
            code="def test(): pass",
            language="python",
            file_path="test.py"
        )
        
        prompt = analyzer._create_analysis_prompt(context, AnalysisType.PATTERN_DETECTION)
        
        assert "python" in prompt
        assert "def test(): pass" in prompt
        assert "test.py" in prompt
        assert "pattern_detection" in prompt
    
    def test_model_selection(self, analyzer):
        """Test model selection for different analysis types."""
        pattern_model = analyzer._select_model(AnalysisType.PATTERN_DETECTION)
        explanation_model = analyzer._select_model(AnalysisType.CODE_EXPLANATION)
        
        assert pattern_model == analyzer.config.pattern_analysis_model
        assert explanation_model == analyzer.config.code_explanation_model
    
    @pytest.mark.asyncio
    async def test_perform_analysis(self, analyzer):
        """Test analysis performance."""
        context = CodeContext(
            code="class Singleton: pass",
            language="python"
        )
        
        result = await analyzer._perform_analysis(context, AnalysisType.PATTERN_DETECTION)
        
        assert result.analysis_type == AnalysisType.PATTERN_DETECTION
        assert result.confidence == 85
        assert len(result.findings) == 1
        assert result.explanation == "Test explanation"
        assert len(result.recommendations) == 1
    
    def test_process_text_response(self, analyzer):
        """Test text response processing."""
        context = CodeContext(code="test", language="python")
        
        result = analyzer._process_text_response(
            "This is a text response",
            AnalysisType.PATTERN_DETECTION,
            context
        )
        
        assert result["confidence"] == 50
        assert len(result["findings"]) == 1
        assert result["findings"][0]["type"] == "text_analysis"
        assert result["metadata"]["format"] == "text"
    
    def test_create_error_result(self, analyzer):
        """Test error result creation."""
        result = analyzer._create_error_result(
            AnalysisType.PATTERN_DETECTION,
            "Test error"
        )
        
        assert result.analysis_type == AnalysisType.PATTERN_DETECTION
        assert result.confidence == 0
        assert len(result.findings) == 0
        assert "Test error" in result.explanation
        assert "error" in result.metadata


class TestGeminiEmbeddingService:
    """Test Gemini embedding service."""
    
    @pytest.fixture
    def mock_client(self):
        """Create mock Gemini client."""
        client = AsyncMock()
        client.generate_embeddings = AsyncMock(return_value=[
            np.random.rand(768).tolist()
        ])
        return client
    
    @pytest.fixture
    def embedding_service(self, mock_client):
        """Create test embedding service."""
        return GeminiEmbeddingService(mock_client)
    
    def test_embedding_service_initialization(self, embedding_service):
        """Test embedding service initialization."""
        assert embedding_service.client is not None
        assert embedding_service.config is not None
        assert embedding_service.cache is not None
        assert embedding_service.vector_index is not None
    
    def test_generate_embedding_id(self, embedding_service):
        """Test embedding ID generation."""
        content = "test content"
        embedding_type = EmbeddingType.CODE
        
        id1 = embedding_service._generate_embedding_id(content, embedding_type)
        id2 = embedding_service._generate_embedding_id(content, embedding_type)
        
        assert id1 == id2  # Should be deterministic
        assert id1.startswith("code:")
        assert len(id1) == 21  # "code:" + 16 chars
    
    def test_preprocess_code(self, embedding_service):
        """Test code preprocessing."""
        code = """
        # This is a comment
        def test():
            # Another comment
            return 42
        """
        
        processed = embedding_service._preprocess_code(code, "python")
        
        assert "# This is a comment" not in processed
        assert "def test():" in processed
        assert "return 42" in processed
    
    @pytest.mark.asyncio
    async def test_generate_embedding(self, embedding_service):
        """Test embedding generation."""
        content = "def test(): pass"
        
        embedding, metadata = await embedding_service.generate_embedding(
            content=content,
            embedding_type=EmbeddingType.CODE,
            language="python"
        )
        
        assert isinstance(embedding, np.ndarray)
        assert embedding.shape == (768,)  # Default embedding dimension
        assert metadata.embedding_type == EmbeddingType.CODE
        assert metadata.source_code == content
        assert metadata.language == "python"
    
    @pytest.mark.asyncio
    async def test_batch_generate_embeddings(self, embedding_service):
        """Test batch embedding generation."""
        contents = ["def test1(): pass", "def test2(): pass"]
        types = [EmbeddingType.FUNCTION, EmbeddingType.FUNCTION]
        languages = ["python", "python"]
        
        results = await embedding_service.batch_generate_embeddings(
            contents, types, languages
        )
        
        assert len(results) == 2
        for embedding, metadata in results:
            assert isinstance(embedding, np.ndarray)
            assert embedding.shape == (768,)
            assert metadata.embedding_type == EmbeddingType.FUNCTION
    
    def test_embedding_cache(self, embedding_service):
        """Test embedding cache functionality."""
        cache = embedding_service.cache
        
        # Test cache operations
        key = "test_key"
        embedding = np.random.rand(768)
        metadata = MagicMock()
        
        # Test cache miss
        assert cache.get(key) is None
        
        # Test cache put and get
        cache.put(key, embedding, metadata)
        result = cache.get(key)
        
        assert result is not None
        cached_embedding, cached_metadata = result
        assert np.array_equal(cached_embedding, embedding)
        assert cached_metadata == metadata
    
    def test_vector_index(self, embedding_service):
        """Test vector index functionality."""
        index = embedding_service.vector_index
        
        # Test empty index
        assert index.get_size() == 0
        
        # Test adding embeddings
        embeddings = np.random.rand(5, 768)
        embedding_ids = [f"test_{i}" for i in range(5)]
        metadata = [MagicMock() for _ in range(5)]
        
        index.add_embeddings(embeddings, embedding_ids, metadata)
        
        assert index.get_size() == 5
        assert len(index.embedding_ids) == 5
        assert len(index.metadata_store) == 5
    
    def test_similarity_search(self, embedding_service):
        """Test similarity search."""
        index = embedding_service.vector_index
        
        # Add some embeddings
        embeddings = np.random.rand(10, 768)
        embedding_ids = [f"test_{i}" for i in range(10)]
        metadata = [MagicMock() for _ in range(10)]
        
        index.add_embeddings(embeddings, embedding_ids, metadata)
        
        # Search
        query = np.random.rand(768)
        results = index.search(query, k=5)
        
        assert len(results) <= 5
        for result in results:
            assert hasattr(result, 'similarity_score')
            assert hasattr(result, 'embedding_id')
            assert hasattr(result, 'metadata')


class TestGeminiIntegration:
    """Test Gemini integration layer."""
    
    @pytest.fixture
    def mock_gemini_client(self):
        """Create mock Gemini client."""
        client = AsyncMock()
        client.generate_content = AsyncMock(return_value={
            "text": json.dumps({
                "confidence": 80,
                "findings": [{"pattern_type": "factory"}],
                "explanation": "Factory pattern detected",
                "recommendations": ["Use abstract factory"]
            }),
            "usage": {"total_tokens": 500}
        })
        return client
    
    @pytest.fixture
    def mock_ml_manager(self):
        """Create mock ML manager."""
        manager = AsyncMock()
        manager.analyze_patterns = AsyncMock(return_value=MagicMock(
            confidence=0.75,
            detected_patterns=[MagicMock(pattern_type="factory")]
        ))
        return manager
    
    @pytest.fixture
    def integration(self, mock_gemini_client, mock_ml_manager):
        """Create test integration."""
        return GeminiIntegration(mock_gemini_client, mock_ml_manager)
    
    def test_integration_initialization(self, integration):
        """Test integration initialization."""
        assert integration.config is not None
        assert integration.confidence_aggregator is not None
        assert integration.ensemble_weights is not None
        assert integration.metrics is not None
    
    def test_confidence_aggregator(self, integration):
        """Test confidence aggregation."""
        aggregator = integration.confidence_aggregator
        
        # Test combined confidence calculation
        combined = aggregator.calculate_combined_confidence(
            gemini_confidence=0.8,
            local_confidence=0.7,
            agreement_score=0.9,
            weights=integration.ensemble_weights
        )
        
        assert 0.0 <= combined <= 1.0
        assert combined > 0.7  # Should be higher than individual scores due to agreement
    
    def test_ensemble_weights_update(self, integration):
        """Test ensemble weights update."""
        original_gemini_weight = integration.ensemble_weights.gemini_weight
        
        integration.update_ensemble_weights(0.7, 0.3)
        
        assert integration.ensemble_weights.gemini_weight == 0.7
        assert integration.ensemble_weights.local_weight == 0.3
        assert integration.ensemble_weights.gemini_weight != original_gemini_weight
    
    def test_metrics_collection(self, integration):
        """Test metrics collection."""
        metrics = integration.get_metrics()
        
        assert isinstance(metrics, dict)
        assert "hybrid_analyses" in metrics
        assert "average_confidence" in metrics
        assert "total_processing_time" in metrics
        assert "errors" in metrics
    
    def test_metrics_clearing(self, integration):
        """Test metrics clearing."""
        # Add some metrics
        integration.metrics["hybrid_analyses"] = 10
        integration.metrics["errors"] = 2
        
        # Clear metrics
        integration.clear_metrics()
        
        assert integration.metrics["hybrid_analyses"] == 0
        assert integration.metrics["errors"] == 0


class TestIntegrationModes:
    """Test different integration modes."""
    
    @pytest.fixture
    def mock_integration(self):
        """Create mock integration with proper async methods."""
        integration = AsyncMock()
        
        # Mock Gemini result
        gemini_result = MagicMock()
        gemini_result.confidence = 0.85
        gemini_result.findings = [{"pattern": "singleton"}]
        gemini_result.tokens_used = 1000
        
        # Mock local result
        local_result = MagicMock()
        local_result.confidence = 0.75
        
        integration._analyze_gemini_only = AsyncMock(return_value=MagicMock(
            gemini_result=gemini_result,
            local_result=None,
            combined_confidence=0.85
        ))
        
        integration._analyze_local_only = AsyncMock(return_value=MagicMock(
            gemini_result=None,
            local_result=local_result,
            combined_confidence=0.75
        ))
        
        integration._analyze_hybrid_parallel = AsyncMock(return_value=MagicMock(
            gemini_result=gemini_result,
            local_result=local_result,
            combined_confidence=0.80
        ))
        
        return integration
    
    @pytest.mark.asyncio
    async def test_gemini_only_mode(self, mock_integration):
        """Test Gemini-only analysis mode."""
        result = await mock_integration._analyze_gemini_only(
            MagicMock(), [AnalysisType.PATTERN_DETECTION]
        )
        
        assert result.gemini_result is not None
        assert result.local_result is None
        assert result.combined_confidence == 0.85
    
    @pytest.mark.asyncio
    async def test_local_only_mode(self, mock_integration):
        """Test local-only analysis mode."""
        result = await mock_integration._analyze_local_only(
            MagicMock(), [AnalysisType.PATTERN_DETECTION]
        )
        
        assert result.gemini_result is None
        assert result.local_result is not None
        assert result.combined_confidence == 0.75
    
    @pytest.mark.asyncio
    async def test_hybrid_parallel_mode(self, mock_integration):
        """Test hybrid parallel analysis mode."""
        result = await mock_integration._analyze_hybrid_parallel(
            MagicMock(), [AnalysisType.PATTERN_DETECTION]
        )
        
        assert result.gemini_result is not None
        assert result.local_result is not None
        assert result.combined_confidence == 0.80


# Performance tests
class TestPerformance:
    """Performance tests for Gemini integration."""
    
    @pytest.mark.asyncio
    async def test_rate_limiting_performance(self):
        """Test rate limiting doesn't significantly impact performance."""
        import time
        
        limiter = GeminiRateLimiter(requests_per_minute=1000, tokens_per_minute=100000)
        
        start_time = time.time()
        
        # Try to acquire 100 tokens quickly
        for _ in range(100):
            result = await limiter.acquire(100)
            assert result is True
        
        end_time = time.time()
        
        # Should complete quickly (less than 1 second)
        assert end_time - start_time < 1.0
    
    def test_cache_performance(self):
        """Test cache operations performance."""
        from pattern_mining.ml.gemini_embeddings import EmbeddingCache
        
        cache = EmbeddingCache(max_size=1000)
        
        # Add many items
        for i in range(1000):
            embedding = np.random.rand(768)
            metadata = MagicMock()
            cache.put(f"key_{i}", embedding, metadata)
        
        # Test retrieval performance
        import time
        start_time = time.time()
        
        for i in range(100):
            result = cache.get(f"key_{i}")
            assert result is not None
        
        end_time = time.time()
        
        # Should be very fast (less than 0.1 seconds)
        assert end_time - start_time < 0.1
    
    def test_vector_index_performance(self):
        """Test vector index performance."""
        from pattern_mining.ml.gemini_embeddings import VectorIndex
        
        index = VectorIndex(dimension=768)
        
        # Add many embeddings
        embeddings = np.random.rand(1000, 768)
        embedding_ids = [f"id_{i}" for i in range(1000)]
        metadata = [MagicMock() for _ in range(1000)]
        
        import time
        start_time = time.time()
        
        index.add_embeddings(embeddings, embedding_ids, metadata)
        
        end_time = time.time()
        
        # Should complete reasonably quickly (less than 5 seconds)
        assert end_time - start_time < 5.0
        
        # Test search performance
        query = np.random.rand(768)
        
        start_time = time.time()
        results = index.search(query, k=10)
        end_time = time.time()
        
        assert len(results) <= 10
        # Search should be very fast (less than 0.5 seconds)
        assert end_time - start_time < 0.5


# Integration tests
class TestEndToEnd:
    """End-to-end integration tests."""
    
    @pytest.mark.asyncio
    async def test_full_analysis_pipeline(self):
        """Test complete analysis pipeline."""
        # Mock all external dependencies
        with patch('pattern_mining.ml.gemini_client.genai.configure'):
            with patch('pattern_mining.ml.gemini_client.genai.GenerativeModel') as mock_model:
                # Mock the model response
                mock_response = MagicMock()
                mock_response.text = json.dumps({
                    "confidence": 90,
                    "findings": [{"pattern_type": "factory", "confidence": 95}],
                    "explanation": "Factory pattern implementation",
                    "recommendations": ["Consider using abstract factory"]
                })
                mock_response.candidates = [MagicMock(safety_ratings=[])]
                
                mock_model_instance = MagicMock()
                mock_model_instance.generate_content_async = AsyncMock(return_value=mock_response)
                mock_model.return_value = mock_model_instance
                
                # Create and test the full pipeline
                config = GeminiConfig(
                    temperature=0.1,
                    max_output_tokens=1000,
                    enable_caching=False  # Disable caching for testing
                )
                
                async with GeminiClient(config) as client:
                    analyzer = GeminiAnalyzer(client)
                    
                    context = CodeContext(
                        code="class Factory: def create(self): pass",
                        language="python"
                    )
                    
                    results = await analyzer.analyze_patterns(
                        context, [AnalysisType.PATTERN_DETECTION]
                    )
                    
                    assert len(results) == 1
                    result = results[AnalysisType.PATTERN_DETECTION]
                    assert result.confidence == 90
                    assert len(result.findings) == 1
                    assert result.findings[0]["pattern_type"] == "factory"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])