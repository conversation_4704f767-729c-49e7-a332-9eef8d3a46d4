"""
Unit Tests for API

Test cases for the FastAPI application.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

from pattern_mining.api.main import app


@pytest.fixture
def client():
    """Test client fixture."""
    return TestClient(app)


def test_health_check(client):
    """Test health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "healthy"
    assert data["service"] == "pattern-mining"
    assert data["version"] == "1.0.0"


@patch("pattern_mining.api.routes.health.get_database_status")
def test_readiness_check_healthy(mock_db_status, client):
    """Test readiness check when all dependencies are healthy."""
    mock_db_status.return_value = True
    
    response = client.get("/health/ready")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "healthy"
    assert "checks" in data
    assert data["checks"]["database"]["status"] == "healthy"


@patch("pattern_mining.api.routes.health.get_database_status")
def test_readiness_check_unhealthy(mock_db_status, client):
    """Test readiness check when database is unhealthy."""
    mock_db_status.return_value = False
    
    response = client.get("/health/ready")
    assert response.status_code == 503
    
    data = response.json()
    assert "Service not ready" in data["detail"]


def test_metrics_endpoint(client):
    """Test metrics endpoint."""
    response = client.get("/health/metrics")
    assert response.status_code == 200
    
    data = response.json()
    assert "requests_total" in data
    assert "response_time_avg" in data
    assert "error_rate" in data


@patch("pattern_mining.api.routes.patterns.get_pattern_detector_manager")
@patch("pattern_mining.api.routes.patterns.get_feature_extractor")
def test_pattern_detection_valid_request(mock_feature_extractor, mock_detector_manager, client):
    """Test pattern detection with valid request."""
    # Mock feature extractor
    mock_extractor = Mock()
    mock_extractor.extract_features.return_value = {"ast": {}, "semantic": {}, "text": {}}
    mock_feature_extractor.return_value = mock_extractor
    
    # Mock detector manager
    mock_manager = Mock()
    mock_manager.detect_patterns.return_value = []
    mock_manager.get_model_versions.return_value = {"ml_inference": "1.0.0"}
    mock_detector_manager.return_value = mock_manager
    
    request_data = {
        "code": "def test_function():\n    pass",
        "language": "python",
        "detection_types": ["ml_inference"]
    }
    
    response = client.post("/api/v1/patterns/detect", json=request_data)
    assert response.status_code == 200
    
    data = response.json()
    assert "job_id" in data
    assert "patterns" in data
    assert "metadata" in data


def test_pattern_detection_invalid_code(client):
    """Test pattern detection with invalid code."""
    request_data = {
        "code": "",  # Empty code
        "language": "python",
        "detection_types": ["ml_inference"]
    }
    
    response = client.post("/api/v1/patterns/detect", json=request_data)
    assert response.status_code == 400
    assert "Invalid code input" in response.json()["detail"]


def test_pattern_detection_unsupported_language(client):
    """Test pattern detection with unsupported language."""
    request_data = {
        "code": "def test_function():\n    pass",
        "language": "unsupported_language",
        "detection_types": ["ml_inference"]
    }
    
    response = client.post("/api/v1/patterns/detect", json=request_data)
    assert response.status_code == 400


def test_get_analysis_job(client):
    """Test get analysis job endpoint."""
    job_id = "test-job-123"
    
    response = client.get(f"/api/v1/patterns/jobs/{job_id}")
    assert response.status_code == 200
    
    data = response.json()
    assert data["job_id"] == job_id
    assert "status" in data
    assert "progress" in data


def test_find_similar_patterns(client):
    """Test find similar patterns endpoint."""
    pattern_id = "test-pattern-123"
    
    response = client.get(f"/api/v1/patterns/similar/{pattern_id}")
    assert response.status_code == 200
    
    data = response.json()
    assert isinstance(data, list)


def test_batch_pattern_detection(client):
    """Test batch pattern detection endpoint."""
    request_data = [
        {
            "code": "def test1():\n    pass",
            "language": "python",
            "detection_types": ["ml_inference"]
        },
        {
            "code": "def test2():\n    pass",
            "language": "python",
            "detection_types": ["heuristic"]
        }
    ]
    
    response = client.post("/api/v1/patterns/batch", json=request_data)
    assert response.status_code == 200
    
    data = response.json()
    assert "batch_id" in data
    assert "job_ids" in data
    assert len(data["job_ids"]) == 2
    assert data["status"] == "queued"


@patch("pattern_mining.api.routes.ml.get_ml_manager")
def test_list_models(mock_ml_manager, client):
    """Test list models endpoint."""
    mock_manager = Mock()
    mock_manager.list_models.return_value = []
    mock_ml_manager.return_value = mock_manager
    
    response = client.get("/api/v1/ml/models")
    assert response.status_code == 200
    
    data = response.json()
    assert isinstance(data, list)


@patch("pattern_mining.api.routes.ml.get_ml_manager")
def test_get_model_info(mock_ml_manager, client):
    """Test get model info endpoint."""
    mock_manager = Mock()
    mock_manager.get_model_info.return_value = None
    mock_ml_manager.return_value = mock_manager
    
    model_id = "test-model"
    response = client.get(f"/api/v1/ml/models/{model_id}")
    assert response.status_code == 404


@patch("pattern_mining.api.routes.ml.get_model_trainer")
def test_train_model(mock_trainer, client):
    """Test train model endpoint."""
    mock_trainer_instance = Mock()
    mock_trainer.return_value = mock_trainer_instance
    
    model_id = "test-model"
    request_data = {
        "model_id": model_id,
        "training_data": {"data_source": "test"},
        "hyperparameters": {"learning_rate": 0.001}
    }
    
    response = client.post(f"/api/v1/ml/models/{model_id}/train", json=request_data)
    assert response.status_code == 200
    
    data = response.json()
    assert data["model_id"] == model_id
    assert data["status"] == "queued"
    assert "job_id" in data


def test_get_training_job(client):
    """Test get training job endpoint."""
    job_id = "test-job-123"
    
    response = client.get(f"/api/v1/ml/training/{job_id}")
    assert response.status_code == 200
    
    data = response.json()
    assert data["job_id"] == job_id
    assert data["model_id"] == "pattern-detector-v1"
    assert data["status"] == "running"


@patch("pattern_mining.api.routes.ml.get_ml_manager")
def test_evaluate_model(mock_ml_manager, client):
    """Test evaluate model endpoint."""
    mock_manager = Mock()
    mock_manager.evaluate_model.return_value = Mock(
        accuracy=0.95,
        precision=0.93,
        recall=0.97,
        f1_score=0.95
    )
    mock_ml_manager.return_value = mock_manager
    
    model_id = "test-model"
    test_data = {"test": "data"}
    
    response = client.post(f"/api/v1/ml/models/{model_id}/evaluate", json=test_data)
    assert response.status_code == 200
    
    data = response.json()
    assert data["accuracy"] == 0.95
    assert data["precision"] == 0.93


@patch("pattern_mining.api.routes.ml.get_ml_manager")
def test_predict_with_model(mock_ml_manager, client):
    """Test predict with model endpoint."""
    mock_manager = Mock()
    mock_manager.predict.return_value = {"prediction": "positive"}
    mock_ml_manager.return_value = mock_manager
    
    model_id = "test-model"
    input_data = {"input": "test"}
    
    response = client.post(f"/api/v1/ml/models/{model_id}/predict", json=input_data)
    assert response.status_code == 200
    
    data = response.json()
    assert data["model_id"] == model_id
    assert data["prediction"] == {"prediction": "positive"}
    assert data["confidence"] == 0.92


@patch("pattern_mining.api.routes.ml.get_ml_manager")
def test_delete_model(mock_ml_manager, client):
    """Test delete model endpoint."""
    mock_manager = Mock()
    mock_manager.delete_model.return_value = True
    mock_ml_manager.return_value = mock_manager
    
    model_id = "test-model"
    
    response = client.delete(f"/api/v1/ml/models/{model_id}")
    assert response.status_code == 200
    
    data = response.json()
    assert f"Model {model_id} deleted successfully" in data["message"]