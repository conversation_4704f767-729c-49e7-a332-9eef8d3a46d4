"""
Integration Tests for Full Workflow

End-to-end integration tests for the complete pattern detection workflow.
Tests the integration between API, ML services, database, and external services.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List
from unittest.mock import Mock, AsyncMock, patch
import json
import uuid

from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from pattern_mining.models.patterns import PatternType, SeverityLevel, DetectionType
from pattern_mining.models.api import PatternDetectionRequest, BatchDetectionRequest
from pattern_mining.models.database import PatternRecord, AnalysisRecord
from tests.utils.generators import <PERSON><PERSON>ata<PERSON>enerator, CodeGenerator, PatternGenerator
from tests.utils.performance import PerformanceTestRunner, LatencyMeasurer


@pytest.mark.integration
class TestPatternDetectionWorkflow:
    """Integration tests for pattern detection workflow."""
    
    @pytest.mark.asyncio
    async def test_complete_pattern_detection_workflow(
        self, 
        async_test_client: AsyncClient,
        async_db_session: AsyncSession,
        sample_code_data: Dict[str, Any]
    ):
        """Test complete pattern detection workflow from request to response."""
        # Prepare test data
        request_data = {
            "repository_id": "integration-test-repo",
            "ast_data": sample_code_data["python"]["ast"],
            "code_content": sample_code_data["python"]["code"],
            "file_path": "src/main.py",
            "language": "python",
            "detection_config": {
                "confidence_threshold": 0.7,
                "max_patterns_per_file": 50,
                "enable_ml_models": True,
                "enable_heuristic_detection": True,
                "enable_clustering": True
            },
            "metadata": {
                "test_run": "integration_test",
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        
        # Mock the pattern detection pipeline
        with patch("pattern_mining.api.routes.patterns.get_pattern_detector_manager") as mock_detector:
            mock_detector_instance = AsyncMock()
            mock_detector_instance.detect_patterns.return_value = [
                {
                    "pattern_id": "pattern-1",
                    "pattern_name": "Iterator Pattern",
                    "pattern_type": PatternType.DESIGN_PATTERN,
                    "severity": SeverityLevel.LOW,
                    "confidence": 0.92,
                    "location": {
                        "file": "src/main.py",
                        "line": 1,
                        "column": 1,
                        "end_line": 5,
                        "end_column": 15
                    },
                    "description": "Iterator pattern implementation",
                    "detection_method": DetectionType.ML_INFERENCE,
                    "context": {"function_name": "calculate_total"},
                    "metadata": {"model_version": "1.0.0"}
                },
                {
                    "pattern_id": "pattern-2",
                    "pattern_name": "SQL Injection Risk",
                    "pattern_type": PatternType.SECURITY_ISSUE,
                    "severity": SeverityLevel.HIGH,
                    "confidence": 0.95,
                    "location": {
                        "file": "src/main.py",
                        "line": 10,
                        "column": 8,
                        "end_line": 10,
                        "end_column": 50
                    },
                    "description": "Potential SQL injection vulnerability",
                    "detection_method": DetectionType.ML_INFERENCE,
                    "context": {"vulnerability_type": "sql_injection"},
                    "metadata": {"model_version": "1.0.0"}
                }
            ]
            mock_detector_instance.get_model_versions.return_value = {
                "ml_inference": "1.0.0",
                "heuristic": "1.0.0"
            }
            mock_detector.return_value = mock_detector_instance
            
            # Send request
            response = await async_test_client.post("/api/v1/patterns/detect", json=request_data)
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        
        # Verify response structure
        assert "request_id" in data
        assert "repository_id" in data
        assert "patterns" in data
        assert "summary" in data
        assert "processing_time_ms" in data
        assert "model_versions" in data
        
        # Verify patterns
        patterns = data["patterns"]
        assert len(patterns) == 2
        
        # Verify first pattern (design pattern)
        design_pattern = patterns[0]
        assert design_pattern["pattern_type"] == "DESIGN_PATTERN"
        assert design_pattern["severity"] == "LOW"
        assert design_pattern["confidence"] == 0.92
        assert design_pattern["location"]["file"] == "src/main.py"
        assert design_pattern["location"]["line"] == 1
        
        # Verify second pattern (security issue)
        security_pattern = patterns[1]
        assert security_pattern["pattern_type"] == "SECURITY_ISSUE"
        assert security_pattern["severity"] == "HIGH"
        assert security_pattern["confidence"] == 0.95
        assert security_pattern["context"]["vulnerability_type"] == "sql_injection"
        
        # Verify summary
        summary = data["summary"]
        assert summary["total_patterns"] == 2
        assert summary["unique_patterns"] == 2
        assert summary["by_type"]["DESIGN_PATTERN"] == 1
        assert summary["by_type"]["SECURITY_ISSUE"] == 1
        assert summary["by_severity"]["LOW"] == 1
        assert summary["by_severity"]["HIGH"] == 1
        assert summary["security_score"] < 100  # Should be reduced due to security issue
        
        # Verify model versions
        model_versions = data["model_versions"]
        assert "ml_inference" in model_versions
        assert "heuristic" in model_versions
        
        # Verify performance metrics
        assert data["processing_time_ms"] > 0
        assert data["processing_time_ms"] < 10000  # Should be under 10 seconds
    
    @pytest.mark.asyncio
    async def test_batch_detection_workflow(
        self,
        async_test_client: AsyncClient,
        async_db_session: AsyncSession
    ):
        """Test batch pattern detection workflow."""
        # Prepare batch request
        request_data = {
            "repository_id": "batch-test-repo",
            "repository_url": "https://github.com/test/batch-repo",
            "file_patterns": ["*.py", "*.js", "*.ts"],
            "exclude_patterns": ["*/test/*", "*/tests/*", "__pycache__/*"],
            "detection_config": {
                "confidence_threshold": 0.7,
                "max_patterns_per_file": 100,
                "enable_ml_models": True,
                "enable_heuristic_detection": True,
                "enable_clustering": True
            },
            "parallel_jobs": 5,
            "timeout_seconds": 300,
            "chunk_size": 25
        }
        
        # Mock batch processing
        with patch("pattern_mining.api.routes.patterns.start_batch_detection") as mock_batch:
            mock_batch.return_value = {
                "job_id": "batch-job-123",
                "repository_id": "batch-test-repo",
                "status": "queued",
                "estimated_duration": 180,
                "created_at": datetime.utcnow().isoformat()
            }
            
            # Start batch detection
            response = await async_test_client.post("/api/v1/patterns/batch", json=request_data)
        
        # Verify batch job creation
        assert response.status_code == 202
        data = response.json()
        assert "job_id" in data
        assert data["status"] == "queued"
        assert data["estimated_duration"] == 180
        
        job_id = data["job_id"]
        
        # Mock batch job status progression
        status_progression = [
            {
                "job_id": job_id,
                "status": "processing",
                "progress": 25.0,
                "files_processed": 15,
                "files_total": 60,
                "patterns_detected": 87,
                "current_file": "src/models/user.py",
                "estimated_completion": (datetime.utcnow() + timedelta(minutes=5)).isoformat()
            },
            {
                "job_id": job_id,
                "status": "processing",
                "progress": 65.0,
                "files_processed": 39,
                "files_total": 60,
                "patterns_detected": 234,
                "current_file": "src/controllers/api.py",
                "estimated_completion": (datetime.utcnow() + timedelta(minutes=2)).isoformat()
            },
            {
                "job_id": job_id,
                "status": "completed",
                "progress": 100.0,
                "files_processed": 60,
                "files_total": 60,
                "patterns_detected": 356,
                "processing_time_ms": 165000,
                "completed_at": datetime.utcnow().isoformat()
            }
        ]
        
        # Test status polling
        with patch("pattern_mining.api.routes.patterns.get_batch_job_status") as mock_status:
            for status_data in status_progression:
                mock_status.return_value = status_data
                
                response = await async_test_client.get(f"/api/v1/patterns/batch/{job_id}")
                assert response.status_code == 200
                
                data = response.json()
                assert data["job_id"] == job_id
                assert data["status"] == status_data["status"]
                assert data["progress"] == status_data["progress"]
                assert data["files_processed"] == status_data["files_processed"]
                assert data["files_total"] == status_data["files_total"]
                assert data["patterns_detected"] == status_data["patterns_detected"]
                
                # Check completion
                if status_data["status"] == "completed":
                    assert data["progress"] == 100.0
                    assert data["files_processed"] == data["files_total"]
                    assert "processing_time_ms" in data
                    assert "completed_at" in data
        
        # Test batch results retrieval
        with patch("pattern_mining.api.routes.patterns.get_batch_results") as mock_results:
            mock_results.return_value = {
                "job_id": job_id,
                "repository_id": "batch-test-repo",
                "status": "completed",
                "summary": {
                    "files_processed": 60,
                    "patterns_detected": 356,
                    "critical_patterns": 8,
                    "high_patterns": 45,
                    "medium_patterns": 123,
                    "low_patterns": 180,
                    "quality_score": 78.5,
                    "security_score": 85.2
                },
                "patterns_by_file": {
                    "src/models/user.py": 15,
                    "src/controllers/api.py": 23,
                    "src/utils/helpers.py": 8
                },
                "results_url": f"https://storage.googleapis.com/results/{job_id}.json",
                "expires_at": (datetime.utcnow() + timedelta(days=7)).isoformat()
            }
            
            response = await async_test_client.get(f"/api/v1/patterns/batch/{job_id}/results")
            assert response.status_code == 200
            
            data = response.json()
            assert data["job_id"] == job_id
            assert data["status"] == "completed"
            assert data["summary"]["files_processed"] == 60
            assert data["summary"]["patterns_detected"] == 356
            assert data["summary"]["quality_score"] == 78.5
            assert "results_url" in data
            assert "expires_at" in data
    
    @pytest.mark.asyncio
    async def test_repository_analysis_workflow(
        self,
        async_test_client: AsyncClient,
        async_db_session: AsyncSession
    ):
        """Test complete repository analysis workflow."""
        # Prepare repository analysis request
        request_data = {
            "repository_id": "analysis-test-repo",
            "repository_url": "https://github.com/test/analysis-repo",
            "branch": "main",
            "commit_sha": "abc123def456",
            "analysis_type": "comprehensive",
            "detection_config": {
                "confidence_threshold": 0.7,
                "enable_ml_models": True,
                "enable_heuristic_detection": True,
                "enable_clustering": True,
                "enable_cross_file_analysis": True
            },
            "include_tests": True,
            "include_documentation": False,
            "generate_report": True,
            "report_format": "json"
        }
        
        # Mock repository analysis start
        with patch("pattern_mining.api.routes.repositories.start_repository_analysis") as mock_analysis:
            mock_analysis.return_value = {
                "analysis_id": "analysis-123",
                "repository_id": "analysis-test-repo",
                "repository_url": "https://github.com/test/analysis-repo",
                "status": "queued",
                "estimated_duration": 900,
                "created_at": datetime.utcnow().isoformat()
            }
            
            # Start repository analysis
            response = await async_test_client.post("/api/v1/repositories/analyze", json=request_data)
        
        # Verify analysis job creation
        assert response.status_code == 202
        data = response.json()
        assert "analysis_id" in data
        assert data["status"] == "queued"
        assert data["estimated_duration"] == 900
        
        analysis_id = data["analysis_id"]
        
        # Mock analysis status progression
        status_progression = [
            {
                "analysis_id": analysis_id,
                "status": "cloning",
                "progress": 5.0,
                "current_stage": "repository_cloning",
                "stage_details": "Cloning repository from GitHub"
            },
            {
                "analysis_id": analysis_id,
                "status": "processing",
                "progress": 25.0,
                "current_stage": "file_discovery",
                "stage_details": "Discovering and categorizing files",
                "files_discovered": 145
            },
            {
                "analysis_id": analysis_id,
                "status": "processing",
                "progress": 45.0,
                "current_stage": "ast_generation",
                "stage_details": "Generating AST for source files",
                "files_processed": 65,
                "files_total": 145
            },
            {
                "analysis_id": analysis_id,
                "status": "processing",
                "progress": 75.0,
                "current_stage": "pattern_detection",
                "stage_details": "Detecting patterns in source files",
                "files_processed": 108,
                "files_total": 145,
                "patterns_detected": 567
            },
            {
                "analysis_id": analysis_id,
                "status": "processing",
                "progress": 90.0,
                "current_stage": "report_generation",
                "stage_details": "Generating analysis report",
                "patterns_detected": 723,
                "quality_metrics_calculated": True
            },
            {
                "analysis_id": analysis_id,
                "status": "completed",
                "progress": 100.0,
                "current_stage": "completed",
                "stage_details": "Analysis completed successfully",
                "files_processed": 145,
                "files_total": 145,
                "patterns_detected": 723,
                "processing_time_ms": 847000,
                "completed_at": datetime.utcnow().isoformat()
            }
        ]
        
        # Test status polling
        with patch("pattern_mining.api.routes.repositories.get_analysis_status") as mock_status:
            for status_data in status_progression:
                mock_status.return_value = status_data
                
                response = await async_test_client.get(f"/api/v1/repositories/analysis/{analysis_id}")
                assert response.status_code == 200
                
                data = response.json()
                assert data["analysis_id"] == analysis_id
                assert data["status"] == status_data["status"]
                assert data["progress"] == status_data["progress"]
                assert data["current_stage"] == status_data["current_stage"]
                
                # Check completion
                if status_data["status"] == "completed":
                    assert data["progress"] == 100.0
                    assert data["patterns_detected"] == 723
                    assert "processing_time_ms" in data
                    assert "completed_at" in data
        
        # Test analysis results retrieval
        with patch("pattern_mining.api.routes.repositories.get_analysis_results") as mock_results:
            mock_results.return_value = {
                "analysis_id": analysis_id,
                "repository_id": "analysis-test-repo",
                "repository_url": "https://github.com/test/analysis-repo",
                "status": "completed",
                "summary": {
                    "files_analyzed": 145,
                    "lines_of_code": 25000,
                    "total_patterns": 723,
                    "critical_patterns": 12,
                    "high_patterns": 87,
                    "medium_patterns": 234,
                    "low_patterns": 390,
                    "quality_score": 82.5,
                    "security_score": 78.9,
                    "maintainability_score": 85.3,
                    "performance_score": 79.2,
                    "languages": ["python", "javascript", "typescript"]
                },
                "quality_metrics": {
                    "cyclomatic_complexity": 3.2,
                    "cognitive_complexity": 2.8,
                    "maintainability_index": 65.4,
                    "duplication_ratio": 0.08,
                    "test_coverage": 78.5,
                    "comment_ratio": 0.15
                },
                "recommendations": [
                    {
                        "id": "rec-1",
                        "title": "Reduce Cyclomatic Complexity",
                        "description": "Several functions have high cyclomatic complexity",
                        "category": "maintainability",
                        "priority": "high",
                        "impact": "medium",
                        "effort": "medium"
                    },
                    {
                        "id": "rec-2",
                        "title": "Fix Security Issues",
                        "description": "Address SQL injection vulnerabilities",
                        "category": "security",
                        "priority": "critical",
                        "impact": "high",
                        "effort": "low"
                    }
                ],
                "report_url": f"https://storage.googleapis.com/reports/{analysis_id}.json",
                "completed_at": datetime.utcnow().isoformat()
            }
            
            response = await async_test_client.get(f"/api/v1/repositories/analysis/{analysis_id}/results")
            assert response.status_code == 200
            
            data = response.json()
            assert data["analysis_id"] == analysis_id
            assert data["status"] == "completed"
            assert data["summary"]["files_analyzed"] == 145
            assert data["summary"]["total_patterns"] == 723
            assert data["summary"]["quality_score"] == 82.5
            assert data["quality_metrics"]["cyclomatic_complexity"] == 3.2
            assert len(data["recommendations"]) == 2
            assert data["recommendations"][0]["priority"] == "high"
            assert data["recommendations"][1]["priority"] == "critical"
            assert "report_url" in data
    
    @pytest.mark.asyncio
    async def test_ml_model_integration_workflow(
        self,
        async_test_client: AsyncClient,
        sample_code_data: Dict[str, Any]
    ):
        """Test ML model integration workflow."""
        # Test model listing
        with patch("pattern_mining.api.routes.ml.get_ml_manager") as mock_manager:
            mock_ml_manager = AsyncMock()
            mock_ml_manager.list_models.return_value = [
                {
                    "id": "pattern-detector-v1",
                    "name": "Pattern Detector",
                    "version": "1.0.0",
                    "status": "active",
                    "accuracy": 0.95,
                    "created_at": "2025-01-01T10:00:00Z"
                },
                {
                    "id": "security-analyzer-v1",
                    "name": "Security Analyzer",
                    "version": "1.0.0",
                    "status": "active",
                    "accuracy": 0.92,
                    "created_at": "2025-01-01T11:00:00Z"
                }
            ]
            mock_manager.return_value = mock_ml_manager
            
            response = await async_test_client.get("/api/v1/ml/models")
            assert response.status_code == 200
            
            models = response.json()
            assert len(models) == 2
            assert models[0]["id"] == "pattern-detector-v1"
            assert models[1]["id"] == "security-analyzer-v1"
        
        # Test model prediction
        model_id = "pattern-detector-v1"
        prediction_data = {
            "code": sample_code_data["python"]["code"],
            "language": "python",
            "context": {"file_path": "test.py"}
        }
        
        with patch("pattern_mining.api.routes.ml.get_ml_manager") as mock_manager:
            mock_ml_manager = AsyncMock()
            mock_ml_manager.predict.return_value = {
                "prediction": "DESIGN_PATTERN",
                "confidence": 0.92,
                "probabilities": {
                    "DESIGN_PATTERN": 0.92,
                    "ANTI_PATTERN": 0.05,
                    "CODE_SMELL": 0.03
                },
                "processing_time_ms": 25
            }
            mock_manager.return_value = mock_ml_manager
            
            response = await async_test_client.post(
                f"/api/v1/ml/models/{model_id}/predict",
                json=prediction_data
            )
            assert response.status_code == 200
            
            result = response.json()
            assert result["model_id"] == model_id
            assert result["prediction"] == "DESIGN_PATTERN"
            assert result["confidence"] == 0.92
            assert result["processing_time_ms"] == 25
        
        # Test model training
        training_data = {
            "training_data": {
                "data_source": "bigquery",
                "dataset": "patterns_dataset",
                "table": "training_patterns"
            },
            "hyperparameters": {
                "learning_rate": 0.001,
                "batch_size": 32,
                "epochs": 10
            },
            "validation_split": 0.2
        }
        
        with patch("pattern_mining.api.routes.ml.get_model_trainer") as mock_trainer:
            mock_trainer_instance = AsyncMock()
            mock_trainer_instance.train.return_value = {
                "job_id": "train-job-123",
                "model_id": model_id,
                "status": "queued",
                "estimated_duration": 3600,
                "created_at": datetime.utcnow().isoformat()
            }
            mock_trainer.return_value = mock_trainer_instance
            
            response = await async_test_client.post(
                f"/api/v1/ml/models/{model_id}/train",
                json=training_data
            )
            assert response.status_code == 202
            
            result = response.json()
            assert result["model_id"] == model_id
            assert result["job_id"] == "train-job-123"
            assert result["status"] == "queued"
            assert result["estimated_duration"] == 3600
        
        # Test training job status
        job_id = "train-job-123"
        with patch("pattern_mining.api.routes.ml.get_training_job_status") as mock_status:
            mock_status.return_value = {
                "job_id": job_id,
                "model_id": model_id,
                "status": "running",
                "progress": 65.5,
                "epoch": 7,
                "total_epochs": 10,
                "current_loss": 0.15,
                "validation_accuracy": 0.89,
                "estimated_completion": (datetime.utcnow() + timedelta(minutes=30)).isoformat()
            }
            
            response = await async_test_client.get(f"/api/v1/ml/training/{job_id}")
            assert response.status_code == 200
            
            result = response.json()
            assert result["job_id"] == job_id
            assert result["model_id"] == model_id
            assert result["status"] == "running"
            assert result["progress"] == 65.5
            assert result["epoch"] == 7
            assert result["validation_accuracy"] == 0.89
    
    @pytest.mark.asyncio
    async def test_database_integration_workflow(
        self,
        async_test_client: AsyncClient,
        async_db_session: AsyncSession
    ):
        """Test database integration workflow."""
        # Create test repository record
        repo_record = {
            "id": "db-test-repo",
            "repository_url": "https://github.com/test/db-repo",
            "name": "db-test-repo",
            "description": "Test repository for database integration",
            "language": "python",
            "stars": 100,
            "forks": 20,
            "last_commit": datetime.utcnow(),
            "metadata": {
                "owner": "test-user",
                "license": "MIT"
            }
        }
        
        # Create test analysis record
        analysis_record = {
            "id": "db-test-analysis",
            "repository_id": "db-test-repo",
            "status": "processing",
            "patterns_detected": 0,
            "quality_score": 0.0,
            "processing_time_ms": 0,
            "metadata": {
                "analysis_type": "comprehensive",
                "files_to_process": 45
            }
        }
        
        # Create test pattern records
        pattern_records = [
            {
                "id": "db-pattern-1",
                "repository_id": "db-test-repo",
                "pattern_name": "Iterator Pattern",
                "pattern_type": PatternType.DESIGN_PATTERN.value,
                "severity": SeverityLevel.LOW.value,
                "confidence": 0.92,
                "file_path": "src/main.py",
                "line_number": 10,
                "description": "Iterator pattern implementation",
                "detection_method": DetectionType.ML_INFERENCE.value,
                "context": {"function_name": "process_items"},
                "metadata": {"model_version": "1.0.0"}
            },
            {
                "id": "db-pattern-2",
                "repository_id": "db-test-repo",
                "pattern_name": "SQL Injection",
                "pattern_type": PatternType.SECURITY_ISSUE.value,
                "severity": SeverityLevel.HIGH.value,
                "confidence": 0.95,
                "file_path": "src/database.py",
                "line_number": 25,
                "description": "SQL injection vulnerability",
                "detection_method": DetectionType.ML_INFERENCE.value,
                "context": {"vulnerability_type": "sql_injection"},
                "metadata": {"model_version": "1.0.0"}
            }
        ]
        
        # Mock database operations
        with patch("pattern_mining.database.repositories.pattern_repository.PatternRepository") as mock_repo:
            mock_repo_instance = AsyncMock()
            
            # Mock pattern creation
            mock_repo_instance.create_patterns.return_value = pattern_records
            
            # Mock pattern retrieval
            mock_repo_instance.get_patterns_by_repository.return_value = {
                "patterns": pattern_records,
                "total": 2,
                "page": 1,
                "page_size": 50
            }
            
            # Mock pattern statistics
            mock_repo_instance.get_pattern_statistics.return_value = {
                "total_patterns": 2,
                "by_type": {
                    PatternType.DESIGN_PATTERN.value: 1,
                    PatternType.SECURITY_ISSUE.value: 1
                },
                "by_severity": {
                    SeverityLevel.LOW.value: 1,
                    SeverityLevel.HIGH.value: 1
                },
                "average_confidence": 0.935
            }
            
            mock_repo.return_value = mock_repo_instance
            
            # Test pattern retrieval
            response = await async_test_client.get(f"/api/v1/patterns/repository/db-test-repo")
            assert response.status_code == 200
            
            data = response.json()
            assert data["total"] == 2
            assert len(data["patterns"]) == 2
            assert data["patterns"][0]["pattern_type"] == PatternType.DESIGN_PATTERN.value
            assert data["patterns"][1]["pattern_type"] == PatternType.SECURITY_ISSUE.value
    
    @pytest.mark.asyncio
    async def test_error_handling_workflow(
        self,
        async_test_client: AsyncClient
    ):
        """Test error handling throughout the workflow."""
        # Test invalid request data
        invalid_request = {
            "repository_id": "",  # Empty repository ID
            "code_content": "",   # Empty code
            "file_path": "test.py",
            "language": "invalid_language",  # Invalid language
            "detection_config": {
                "confidence_threshold": 1.5,  # Invalid threshold
                "enable_ml_models": True
            }
        }
        
        response = await async_test_client.post("/api/v1/patterns/detect", json=invalid_request)
        assert response.status_code == 422
        
        data = response.json()
        assert "error" in data
        assert "validation" in data["error"].lower()
        assert "details" in data
        
        # Test service unavailable
        with patch("pattern_mining.api.routes.patterns.get_pattern_detector_manager") as mock_detector:
            mock_detector.side_effect = Exception("Service unavailable")
            
            valid_request = {
                "repository_id": "test-repo",
                "ast_data": {"type": "Module", "children": []},
                "code_content": "print('hello')",
                "file_path": "test.py",
                "language": "python",
                "detection_config": {
                    "confidence_threshold": 0.7,
                    "enable_ml_models": True
                }
            }
            
            response = await async_test_client.post("/api/v1/patterns/detect", json=valid_request)
            assert response.status_code == 500
            
            data = response.json()
            assert "error" in data
            assert "internal server error" in data["error"].lower()
        
        # Test timeout handling
        with patch("pattern_mining.api.routes.patterns.get_pattern_detector_manager") as mock_detector:
            mock_detector_instance = AsyncMock()
            mock_detector_instance.detect_patterns.side_effect = asyncio.TimeoutError("Request timeout")
            mock_detector.return_value = mock_detector_instance
            
            valid_request = {
                "repository_id": "test-repo",
                "ast_data": {"type": "Module", "children": []},
                "code_content": "print('hello')",
                "file_path": "test.py",
                "language": "python",
                "detection_config": {
                    "confidence_threshold": 0.7,
                    "enable_ml_models": True
                },
                "timeout_seconds": 1  # Very short timeout
            }
            
            response = await async_test_client.post("/api/v1/patterns/detect", json=valid_request)
            assert response.status_code == 408
            
            data = response.json()
            assert "timeout" in data["error"].lower()
    
    @pytest.mark.asyncio
    async def test_concurrent_requests_workflow(
        self,
        async_test_client: AsyncClient
    ):
        """Test handling of concurrent requests."""
        # Prepare multiple concurrent requests
        requests = []
        for i in range(10):
            request_data = {
                "repository_id": f"concurrent-repo-{i}",
                "ast_data": {"type": "Module", "children": []},
                "code_content": f"def function_{i}():\n    print('hello {i}')",
                "file_path": f"test_{i}.py",
                "language": "python",
                "detection_config": {
                    "confidence_threshold": 0.7,
                    "enable_ml_models": True
                }
            }
            requests.append(request_data)
        
        # Mock pattern detector for concurrent requests
        with patch("pattern_mining.api.routes.patterns.get_pattern_detector_manager") as mock_detector:
            mock_detector_instance = AsyncMock()
            mock_detector_instance.detect_patterns.return_value = [
                {
                    "pattern_id": "concurrent-pattern",
                    "pattern_name": "Test Pattern",
                    "pattern_type": PatternType.DESIGN_PATTERN,
                    "severity": SeverityLevel.LOW,
                    "confidence": 0.8,
                    "location": {"file": "test.py", "line": 1, "column": 1},
                    "description": "Test pattern",
                    "detection_method": DetectionType.ML_INFERENCE,
                    "context": {},
                    "metadata": {"model_version": "1.0.0"}
                }
            ]
            mock_detector_instance.get_model_versions.return_value = {"ml_inference": "1.0.0"}
            mock_detector.return_value = mock_detector_instance
            
            # Send concurrent requests
            tasks = []
            for request_data in requests:
                task = async_test_client.post("/api/v1/patterns/detect", json=request_data)
                tasks.append(task)
            
            # Wait for all requests to complete
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Verify all requests succeeded
            success_count = 0
            for response in responses:
                if not isinstance(response, Exception):
                    assert response.status_code == 200
                    success_count += 1
            
            # Should have high success rate (at least 80%)
            assert success_count >= 8
    
    @pytest.mark.asyncio
    async def test_performance_monitoring_workflow(
        self,
        async_test_client: AsyncClient,
        performance_test_runner: PerformanceTestRunner
    ):
        """Test performance monitoring throughout the workflow."""
        # Mock pattern detector with simulated processing time
        with patch("pattern_mining.api.routes.patterns.get_pattern_detector_manager") as mock_detector:
            mock_detector_instance = AsyncMock()
            
            async def simulate_processing():
                # Simulate processing time
                await asyncio.sleep(0.01)  # 10ms
                return [
                    {
                        "pattern_id": "perf-pattern",
                        "pattern_name": "Performance Pattern",
                        "pattern_type": PatternType.DESIGN_PATTERN,
                        "severity": SeverityLevel.LOW,
                        "confidence": 0.8,
                        "location": {"file": "test.py", "line": 1, "column": 1},
                        "description": "Performance test pattern",
                        "detection_method": DetectionType.ML_INFERENCE,
                        "context": {},
                        "metadata": {"model_version": "1.0.0"}
                    }
                ]
            
            mock_detector_instance.detect_patterns.side_effect = simulate_processing
            mock_detector_instance.get_model_versions.return_value = {"ml_inference": "1.0.0"}
            mock_detector.return_value = mock_detector_instance
            
            # Run performance test
            test_config = {
                "throughput_duration": 5,
                "concurrent_requests": 5,
                "spike_requests": 20
            }
            
            request_data = {
                "repository_id": "perf-test-repo",
                "ast_data": {"type": "Module", "children": []},
                "code_content": "print('performance test')",
                "file_path": "test.py",
                "language": "python",
                "detection_config": {
                    "confidence_threshold": 0.7,
                    "enable_ml_models": True
                }
            }
            
            results = await performance_test_runner.run_performance_test(
                "/api/v1/patterns/detect",
                method="POST",
                payload=request_data,
                test_config=test_config
            )
            
            # Verify performance metrics
            assert "latency_analysis" in results
            assert "throughput" in results
            assert "load_spike" in results
            assert "memory_analysis" in results
            assert "sla_compliance" in results
            
            # Check latency requirements
            latency_analysis = results["latency_analysis"]
            assert latency_analysis["avg_latency_ms"] < 100  # Should be under 100ms
            assert latency_analysis["p95_latency_ms"] < 200  # P95 should be under 200ms
            
            # Check throughput requirements
            throughput_results = results["throughput"]
            assert throughput_results["requests_per_second"] > 10  # Should handle at least 10 RPS
            assert throughput_results["error_rate_percent"] < 5.0  # Error rate should be under 5%
            
            # Check memory usage
            memory_analysis = results["memory_analysis"]
            assert memory_analysis["peak_memory_mb"] < 500  # Should use less than 500MB
            assert not memory_analysis["memory_leak_detected"]  # No memory leaks
            
            # Check SLA compliance
            sla_compliance = results["sla_compliance"]
            assert sla_compliance["overall_compliant"] is True