# Cloud Build configuration for Pattern Mining Service
# Supports multi-stage deployment: staging → production

substitutions:
  _SERVICE_NAME: pattern-mining
  _REGION: us-central1
  _IMAGE_NAME: gcr.io/${PROJECT_ID}/${_SERVICE_NAME}
  _STAGING_URL: https://pattern-mining-staging.ccl-platform.com
  _PROD_URL: https://pattern-mining.ccl-platform.com

options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'
  substitution_option: 'ALLOW_LOOSE'

steps:
  # Step 1: Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-image'
    args:
      - 'build'
      - '--target=production'
      - '--build-arg=PYTHON_VERSION=3.13'
      - '-t=${_IMAGE_NAME}:${SHORT_SHA}'
      - '-t=${_IMAGE_NAME}:latest'
      - '-t=${_IMAGE_NAME}:${BRANCH_NAME}'
      - '-f=Dockerfile'
      - '.'
    waitFor: ['-']

  # Step 2: Push the image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-image'
    args:
      - 'push'
      - '--all-tags'
      - '${_IMAGE_NAME}'
    waitFor: ['build-image']

  # Step 3: Run security scanning
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'scan-image'
    args:
      - 'container'
      - 'images'
      - 'scan'
      - '${_IMAGE_NAME}:${SHORT_SHA}'
      - '--severity-threshold=MEDIUM'
    waitFor: ['push-image']

  # Step 4: Deploy to staging (all branches)
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-staging'
    args:
      - 'run'
      - 'deploy'
      - '${_SERVICE_NAME}-staging'
      - '--image=${_IMAGE_NAME}:${SHORT_SHA}'
      - '--platform=managed'
      - '--region=${_REGION}'
      - '--tag=commit-${SHORT_SHA}'
      - '--no-traffic'
      - '--max-instances=10'
      - '--min-instances=1'
      - '--memory=4Gi'
      - '--cpu=2'
      - '--timeout=300'
      - '--concurrency=100'
      - '--service-account=${_SERVICE_NAME}-sa@${PROJECT_ID}.iam.gserviceaccount.com'
      - '--set-env-vars=ENVIRONMENT=staging'
      - '--set-secrets=GEMINI_API_KEY=gemini-api-key:latest'
      - '--set-secrets=JWT_SECRET=jwt-secret:latest'
      - '--set-secrets=DATABASE_PASSWORD=database-password:latest'
      - '--vpc-connector=${_SERVICE_NAME}-connector'
    waitFor: ['scan-image']

  # Step 5: Run integration tests against staging
  - name: 'python:3.13-slim'
    id: 'integration-tests'
    env:
      - 'STAGING_URL=${_STAGING_URL}'
      - 'TEST_ENV=staging'
    script: |
      #!/bin/bash
      pip install pytest httpx
      pytest tests/integration/ -v --tb=short
    waitFor: ['deploy-staging']

  # Step 6: Gradual traffic migration to staging
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'route-staging-traffic'
    args:
      - 'run'
      - 'services'
      - 'update-traffic'
      - '${_SERVICE_NAME}-staging'
      - '--to-tags=commit-${SHORT_SHA}=10'
      - '--region=${_REGION}'
    waitFor: ['integration-tests']

  # Step 7: Deploy to production (main branch only)
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-production'
    args:
      - 'run'
      - 'deploy'
      - '${_SERVICE_NAME}'
      - '--image=${_IMAGE_NAME}:${SHORT_SHA}'
      - '--platform=managed'
      - '--region=${_REGION}'
      - '--tag=canary-${SHORT_SHA}'
      - '--no-traffic'
      - '--max-instances=50'
      - '--min-instances=2'
      - '--memory=4Gi'
      - '--cpu=2'
      - '--timeout=300'
      - '--concurrency=100'
      - '--service-account=${_SERVICE_NAME}-sa@${PROJECT_ID}.iam.gserviceaccount.com'
      - '--set-env-vars=ENVIRONMENT=production'
      - '--set-secrets=GEMINI_API_KEY=gemini-api-key:latest'
      - '--set-secrets=JWT_SECRET=jwt-secret:latest'
      - '--set-secrets=DATABASE_PASSWORD=database-password:latest'
      - '--vpc-connector=${_SERVICE_NAME}-connector'
    waitFor: ['route-staging-traffic']
    # Only run on main branch
    options:
      substitution_option: 'ALLOW_LOOSE'
    # Use conditional execution
    script: |
      if [ "${BRANCH_NAME}" = "main" ]; then
        gcloud run deploy ${_SERVICE_NAME} \
          --image=${_IMAGE_NAME}:${SHORT_SHA} \
          --platform=managed \
          --region=${_REGION} \
          --tag=canary-${SHORT_SHA} \
          --no-traffic \
          --max-instances=50 \
          --min-instances=2 \
          --memory=4Gi \
          --cpu=2 \
          --timeout=300 \
          --concurrency=100 \
          --service-account=${_SERVICE_NAME}-sa@${PROJECT_ID}.iam.gserviceaccount.com \
          --set-env-vars=ENVIRONMENT=production \
          --set-secrets=GEMINI_API_KEY=gemini-api-key:latest \
          --set-secrets=JWT_SECRET=jwt-secret:latest \
          --set-secrets=DATABASE_PASSWORD=database-password:latest \
          --vpc-connector=${_SERVICE_NAME}-connector
      fi

  # Step 8: Canary deployment (10% traffic)
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'canary-traffic'
    args:
      - 'run'
      - 'services'
      - 'update-traffic'
      - '${_SERVICE_NAME}'
      - '--to-tags=canary-${SHORT_SHA}=10'
      - '--region=${_REGION}'
    waitFor: ['deploy-production']
    # Only run on main branch
    script: |
      if [ "${BRANCH_NAME}" = "main" ]; then
        gcloud run services update-traffic ${_SERVICE_NAME} \
          --to-tags=canary-${SHORT_SHA}=10 \
          --region=${_REGION}
      fi

  # Step 9: Smoke tests on production
  - name: 'python:3.13-slim'
    id: 'smoke-tests'
    env:
      - 'PROD_URL=${_PROD_URL}'
      - 'TEST_ENV=production'
    script: |
      #!/bin/bash
      if [ "${BRANCH_NAME}" = "main" ]; then
        pip install pytest httpx
        pytest tests/smoke/ -v --tb=short
      fi
    waitFor: ['canary-traffic']

# Build artifacts
artifacts:
  images:
    - '${_IMAGE_NAME}:${SHORT_SHA}'
    - '${_IMAGE_NAME}:latest'
    - '${_IMAGE_NAME}:${BRANCH_NAME}'

# Timeout for the entire build
timeout: '1800s'

# Notifications (requires setup in GCP)
availableSecrets:
  secretManager:
    - versionName: projects/${PROJECT_ID}/secrets/slack-webhook/versions/latest
      env: 'SLACK_WEBHOOK'