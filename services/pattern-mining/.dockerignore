# Docker ignore file for Pattern Mining Service

# Version control
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
*.so
*.egg-info
dist
build
eggs
.eggs
wheels
*.egg

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store

# Testing
.pytest_cache/
.coverage
.coverage.*
coverage.xml
htmlcov/
.tox/
.mypy_cache/
.ruff_cache/

# Documentation
docs/
*.md
!README.md

# Archives
archive/

# Development files
Dockerfile.dev
Dockerfile.prod
docker-compose.override.yml
.env
.env.*
!.env.example

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
*.tmp

# Build artifacts
cloudbuild.yaml
k8s/
scripts/deployment/
scripts/setup/