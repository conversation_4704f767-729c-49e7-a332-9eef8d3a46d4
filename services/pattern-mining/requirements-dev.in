# Top-level development dependencies for the Pattern Mining Service
# This file is used by pip-compile to generate requirements-dev.txt
# To update, run: pip-compile services/pattern-mining/requirements-dev.in

# Base requirements file (for production dependencies)
-r requirements.txt

# Testing Framework
pytest
pytest-asyncio
pytest-cov
pytest-benchmark
pytest-mock
pytest-timeout
pytest-xdist
hypothesis
faker

# Code Quality & Linting
black
ruff
mypy
pyright
isort
pre-commit
flake8
pylint

# Documentation
mkdocs
mkdocs-material
mkdocstrings[python]
mkdocs-mermaid2-plugin
mkdocs-git-revision-date-localized-plugin

# Type Stubs
types-redis
types-requests
types-PyYAML
types-aiofiles
types-python-dateutil

# Development Tools
ipython
jupyter
notebook
jupyterlab
ipdb
watchdog
python-dotenv[cli]

# Profiling & Performance
py-spy
memory-profiler
line-profiler
scalene

# Security Testing
bandit[toml]
safety
pip-audit
semgrep

# API Testing
httpx[cli]
locust
responses

# Database Tools
alembic[tz]
sqlalchemy-utils
faker-sqlalchemy

# Debugging & Monitoring
rich
icecream
loguru
python-json-logger

# Build & Packaging
build
wheel
setuptools
twine

# Git Hooks
commitizen
gitlint

# Additional Development Utilities
python-decouple
environs
dynaconf
pydantic-extra-types
