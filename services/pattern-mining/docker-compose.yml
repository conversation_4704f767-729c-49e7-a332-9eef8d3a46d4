# Docker Compose for Pattern Mining Service
# Complete local development environment with all dependencies

version: '3.8'

services:
  # Pattern Mining Service - Main Application
  pattern-mining:
    build:
      context: .
      dockerfile: Dockerfile.prod
      target: development
      args:
        RUNTIME_BASE: cpu-runtime
    image: pattern-mining:dev
    container_name: pattern-mining-service
    ports:
      - "8000:8000"
    environment:
      # Application Configuration
      ENVIRONMENT: development
      LOG_LEVEL: DEBUG
      DEBUG: "true"
      
      # Database Configuration (loaded from .env file)
      DATABASE_URL: ${DATABASE_URL:-postgresql://postgres:${POSTGRES_PASSWORD:-INSECURE_PASSWORD}@postgres:5432/pattern_mining}
      REDIS_URL: ${REDIS_URL:-redis://:${REDIS_PASSWORD:-}@redis:6379/0}
      
      # Google Cloud Configuration (using emulators)
      GOOGLE_CLOUD_PROJECT: episteme-dev
      BIGQUERY_EMULATOR_HOST: bigquery-emulator:9050
      PUBSUB_EMULATOR_HOST: pubsub-emulator:8085
      
      # ML Configuration
      PRELOAD_MODELS: "false"
      GPU_ENABLED: "false"
      
      # Security Configuration (loaded from .env file)
      SECRET_KEY: ${SECRET_KEY:-INSECURE_DEV_KEY_CHANGE_ME}
      JWT_SECRET: ${JWT_SECRET:-INSECURE_DEV_JWT_CHANGE_ME}
      CORS_ORIGINS: ${CORS_ORIGINS:-http://localhost:3000,http://localhost:8080}
      
      # Performance Configuration
      WORKERS: 1
      MAX_WORKERS: 4
      WORKER_CONNECTIONS: 1000
      
      # Monitoring Configuration
      PROMETHEUS_ENABLED: "true"
      TRACING_ENABLED: "true"
      
      # Cache Configuration
      CACHE_TTL: 3600
      CACHE_SIZE: 1000
      
      # Development Configuration
      HOT_RELOAD: "true"
      SKIP_MIGRATIONS: "false"
    volumes:
      - ./src:/app/src:ro
      - ./tests:/app/tests:ro
      - ./config:/app/config:ro
      - ./logs:/app/logs
      - ./data:/app/data
      - ./cache:/app/cache
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      bigquery-emulator:
        condition: service_started
    networks:
      - pattern-mining-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Pattern Mining Service - GPU Enabled (optional)
  pattern-mining-gpu:
    build:
      context: .
      dockerfile: Dockerfile.prod
      target: development
      args:
        RUNTIME_BASE: cuda-runtime
    image: pattern-mining:gpu-dev
    container_name: pattern-mining-gpu-service
    ports:
      - "8001:8000"
    environment:
      # Application Configuration
      ENVIRONMENT: development
      LOG_LEVEL: DEBUG
      DEBUG: "true"
      
      # Database Configuration (loaded from .env file)
      DATABASE_URL: ${DATABASE_URL:-postgresql://postgres:${POSTGRES_PASSWORD:-INSECURE_PASSWORD}@postgres:5432/pattern_mining}
      REDIS_URL: ${REDIS_URL:-redis://:${REDIS_PASSWORD:-}@redis:6379/0}
      
      # Google Cloud Configuration (using emulators)
      GOOGLE_CLOUD_PROJECT: episteme-dev
      BIGQUERY_EMULATOR_HOST: bigquery-emulator:9050
      PUBSUB_EMULATOR_HOST: pubsub-emulator:8085
      
      # ML Configuration
      PRELOAD_MODELS: "false"
      GPU_ENABLED: "true"
      CUDA_VISIBLE_DEVICES: "0"
    volumes:
      - ./src:/app/src:ro
      - ./tests:/app/tests:ro
      - ./config:/app/config:ro
      - ./logs:/app/logs
      - ./data:/app/data
      - ./cache:/app/cache
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - pattern-mining-network
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    profiles:
      - gpu
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s

  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: pattern-mining-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-pattern_mining}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-INSECURE_PASSWORD}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - pattern-mining-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d pattern_mining"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.max=10000
      -c pg_stat_statements.track=all
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c work_mem=4MB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c log_statement=all
      -c log_duration=on
      -c log_min_duration_statement=100ms

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: pattern-mining-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - pattern-mining-network
    restart: unless-stopped
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  # BigQuery Emulator
  bigquery-emulator:
    image: ghcr.io/goccy/bigquery-emulator:latest
    container_name: pattern-mining-bigquery-emulator
    ports:
      - "9050:9050"
    environment:
      PROJECT_ID: episteme-dev
      DATASET_ID: pattern_mining
    volumes:
      - bigquery_data:/data
      - ./config/bigquery:/config:ro
    networks:
      - pattern-mining-network
    restart: unless-stopped
    command: >
      bigquery-emulator
      --project=episteme-dev
      --dataset=pattern_mining
      --port=9050
      --data-from-yaml=/config/schema.yaml
    profiles:
      - bigquery

  # Pub/Sub Emulator
  pubsub-emulator:
    image: gcr.io/google.com/cloudsdktool/cloud-sdk:emulators
    container_name: pattern-mining-pubsub-emulator
    ports:
      - "8085:8085"
    environment:
      PUBSUB_PROJECT_ID: episteme-dev
    networks:
      - pattern-mining-network
    restart: unless-stopped
    command: >
      gcloud beta emulators pubsub start
      --project=episteme-dev
      --host-port=0.0.0.0:8085
      --data-dir=/data
    volumes:
      - pubsub_data:/data
    profiles:
      - pubsub

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: pattern-mining-prometheus
    ports:
      - "9090:9090"
    volumes:
      - prometheus_data:/prometheus
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    networks:
      - pattern-mining-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    profiles:
      - monitoring

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: pattern-mining-grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_ADMIN_PASSWORD:-INSECURE_ADMIN_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: "false"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - pattern-mining-network
    restart: unless-stopped
    depends_on:
      - prometheus
    profiles:
      - monitoring

  # Jaeger Tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: pattern-mining-jaeger
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      COLLECTOR_OTLP_ENABLED: "true"
    networks:
      - pattern-mining-network
    restart: unless-stopped
    profiles:
      - tracing

  # MinIO Object Storage (S3 Compatible)
  minio:
    image: minio/minio:latest
    container_name: pattern-mining-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-INSECURE_MINIO_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-INSECURE_MINIO_PASSWORD}
    volumes:
      - minio_data:/data
    networks:
      - pattern-mining-network
    restart: unless-stopped
    command: server /data --console-address ":9001"
    profiles:
      - storage

  # Nginx Load Balancer
  nginx:
    image: nginx:alpine
    container_name: pattern-mining-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/nginx/conf.d:/etc/nginx/conf.d:ro
      - ./config/ssl:/etc/ssl:ro
    networks:
      - pattern-mining-network
    restart: unless-stopped
    depends_on:
      - pattern-mining
    profiles:
      - loadbalancer

  # Test Runner
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile.prod
      target: development
    image: pattern-mining:test
    container_name: pattern-mining-test-runner
    environment:
      ENVIRONMENT: test
      LOG_LEVEL: DEBUG
      DATABASE_URL: ********************************************/pattern_mining_test
      REDIS_URL: redis://redis:6379/1
    volumes:
      - ./src:/app/src:ro
      - ./tests:/app/tests:ro
      - ./coverage:/app/coverage
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - pattern-mining-network
    profiles:
      - test
    command: >
      sh -c "
        pip install pytest pytest-cov pytest-asyncio pytest-mock &&
        pytest tests/ -v --cov=pattern_mining --cov-report=html:/app/coverage/html --cov-report=term
      "

networks:
  pattern-mining-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  bigquery_data:
    driver: local
  pubsub_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  minio_data:
    driver: local

# Environment variable anchors for reuse
x-pattern-mining-env: &pattern-mining-env
  ENVIRONMENT: ${ENVIRONMENT:-development}
  LOG_LEVEL: ${LOG_LEVEL:-DEBUG}
  DEBUG: ${DEBUG:-true}
  DATABASE_URL: ${DATABASE_URL:-postgresql://postgres:${POSTGRES_PASSWORD:-INSECURE_PASSWORD}@postgres:5432/pattern_mining}
  REDIS_URL: ${REDIS_URL:-redis://:${REDIS_PASSWORD:-}@redis:6379/0}
  GOOGLE_CLOUD_PROJECT: ${GOOGLE_CLOUD_PROJECT:-episteme-dev}
  BIGQUERY_EMULATOR_HOST: bigquery-emulator:9050
  PUBSUB_EMULATOR_HOST: pubsub-emulator:8085
  PRELOAD_MODELS: ${PRELOAD_MODELS:-false}
  GPU_ENABLED: ${GPU_ENABLED:-false}
  SECRET_KEY: ${SECRET_KEY:-INSECURE_DEV_KEY_CHANGE_ME}
  JWT_SECRET: ${JWT_SECRET:-INSECURE_DEV_JWT_CHANGE_ME}
  CORS_ORIGINS: ${CORS_ORIGINS:-http://localhost:3000,http://localhost:8080}
  WORKERS: ${WORKERS:-1}
  MAX_WORKERS: ${MAX_WORKERS:-4}
  WORKER_CONNECTIONS: ${WORKER_CONNECTIONS:-1000}
  PROMETHEUS_ENABLED: ${PROMETHEUS_ENABLED:-true}
  TRACING_ENABLED: ${TRACING_ENABLED:-true}
  CACHE_TTL: ${CACHE_TTL:-3600}
  CACHE_SIZE: ${CACHE_MAX_SIZE:-1000}
  HOT_RELOAD: ${HOT_RELOAD:-true}
  SKIP_MIGRATIONS: ${SKIP_MIGRATIONS:-false}
  BIND: "0.0.0.0:8000"
  TIMEOUT: ${TIMEOUT:-120}
  KEEPALIVE: ${KEEPALIVE:-5}
  GRACEFUL_TIMEOUT: ${GRACEFUL_TIMEOUT:-30}