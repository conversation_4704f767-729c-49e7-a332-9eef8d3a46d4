# Distributed Computing Configuration for Pattern Mining Service
# This configuration defines the setup for Ray-based distributed computing
# with GPU acceleration and auto-scaling capabilities

# Environment-specific configurations
environments:
  development:
    cluster:
      name: "pattern-mining-dev"
      provider: "local"
      max_workers: 4
      upscaling_speed: 1.0
      idle_timeout_minutes: 2
      
    gpu:
      use_gpu: false
      memory_fraction: 0.5
      allow_growth: true
      mixed_precision: false
      cuda_streams: 2
      
    scaling:
      min_workers: 1
      max_workers: 4
      target_cpu_utilization: 60.0
      scale_up_threshold: 70.0
      scale_down_threshold: 40.0
      scale_up_cooldown: 120  # 2 minutes
      scale_down_cooldown: 180  # 3 minutes
      enable_cost_optimization: false
      enable_gpu_scaling: false
      
    workers:
      - worker_id: "dev_pattern_detector"
        worker_type: "pattern_detector"
        num_cpus: 1
        num_gpus: 0
        memory: **********  # 2GB
        max_concurrent_tasks: 2
        
      - worker_id: "dev_feature_extractor"
        worker_type: "feature_extractor"
        num_cpus: 2
        num_gpus: 0
        memory: **********  # 4GB
        max_concurrent_tasks: 2

  staging:
    cluster:
      name: "pattern-mining-staging"
      provider: "local"
      max_workers: 8
      upscaling_speed: 1.0
      idle_timeout_minutes: 5
      
    gpu:
      use_gpu: true
      memory_fraction: 0.7
      allow_growth: true
      mixed_precision: true
      cuda_streams: 4
      use_rapids: true
      
    scaling:
      min_workers: 2
      max_workers: 8
      target_cpu_utilization: 70.0
      scale_up_threshold: 80.0
      scale_down_threshold: 30.0
      scale_up_cooldown: 300  # 5 minutes
      scale_down_cooldown: 600  # 10 minutes
      enable_cost_optimization: true
      enable_gpu_scaling: true
      
    workers:
      - worker_id: "staging_pattern_detector_1"
        worker_type: "pattern_detector"
        num_cpus: 2
        num_gpus: 0
        memory: **********  # 4GB
        max_concurrent_tasks: 4
        
      - worker_id: "staging_pattern_detector_2"
        worker_type: "pattern_detector"
        num_cpus: 2
        num_gpus: 0
        memory: **********  # 4GB
        max_concurrent_tasks: 4
        
      - worker_id: "staging_feature_extractor"
        worker_type: "feature_extractor"
        num_cpus: 4
        num_gpus: 0
        memory: **********  # 8GB
        max_concurrent_tasks: 4
        
      - worker_id: "staging_ml_trainer"
        worker_type: "ml_trainer"
        num_cpus: 4
        num_gpus: 1
        memory: 16000000000  # 16GB
        max_concurrent_tasks: 2

  production:
    cluster:
      name: "pattern-mining-prod"
      provider: "gcp"  # Google Cloud Platform
      max_workers: 50
      upscaling_speed: 2.0
      idle_timeout_minutes: 5
      
    gpu:
      use_gpu: true
      memory_fraction: 0.8
      allow_growth: true
      mixed_precision: true
      cuda_streams: 8
      use_rapids: true
      use_tensorrt: true
      
    scaling:
      min_workers: 5
      max_workers: 50
      target_cpu_utilization: 75.0
      target_memory_utilization: 80.0
      target_gpu_utilization: 70.0
      scale_up_threshold: 85.0
      scale_down_threshold: 25.0
      scale_up_cooldown: 300  # 5 minutes
      scale_down_cooldown: 600  # 10 minutes
      min_queue_length: 20
      max_queue_length: 200
      metrics_window: 600  # 10 minutes
      prediction_horizon: 1800  # 30 minutes
      cost_threshold: 100.0  # $100/hour
      enable_cost_optimization: true
      enable_gpu_scaling: true
      enable_preemptible_instances: true
      
    workers:
      - worker_id: "prod_pattern_detector"
        worker_type: "pattern_detector"
        num_cpus: 4
        num_gpus: 0
        memory: **********  # 8GB
        max_concurrent_tasks: 8
        min_instances: 2
        max_instances: 10
        
      - worker_id: "prod_feature_extractor"
        worker_type: "feature_extractor"
        num_cpus: 8
        num_gpus: 0
        memory: 16000000000  # 16GB
        max_concurrent_tasks: 6
        min_instances: 1
        max_instances: 5
        
      - worker_id: "prod_ml_trainer"
        worker_type: "ml_trainer"
        num_cpus: 16
        num_gpus: 2
        memory: 3**********  # 32GB
        max_concurrent_tasks: 4
        min_instances: 1
        max_instances: 8
        
      - worker_id: "prod_repository_analyzer"
        worker_type: "repository_analyzer"
        num_cpus: 8
        num_gpus: 0
        memory: 16000000000  # 16GB
        max_concurrent_tasks: 4
        min_instances: 1
        max_instances: 6

# ML Pipeline Configuration
ml_pipeline:
  default_training:
    batch_size: 64
    learning_rate: 0.001
    num_epochs: 20
    validation_split: 0.2
    early_stopping_patience: 5
    mixed_precision: true
    gradient_clipping: 1.0
    save_checkpoints: true
    checkpoint_freq: 5
    
  hyperparameter_tuning:
    num_samples: 50
    max_concurrent_trials: 8
    scheduler: "asha"
    search_algorithm: "hyperopt"
    metric: "accuracy"
    mode: "max"
    grace_period: 10
    reduction_factor: 2
    max_t: 100
    
  model_serving:
    default_replicas: 3
    max_concurrent_queries: 200
    batch_size: 32
    max_batch_wait_time: 0.1
    health_check_period: 30
    
  search_spaces:
    transformer:
      learning_rate: [0.0001, 0.001, 0.01]
      batch_size: [32, 64, 128]
      num_layers: [4, 6, 8, 12]
      hidden_size: [256, 512, 1024]
      num_heads: [8, 12, 16]
      dropout_rate: [0.1, 0.2, 0.3]
      
    classifier:
      learning_rate: [0.001, 0.01, 0.1]
      batch_size: [64, 128, 256]
      hidden_layers: [2, 3, 4]
      hidden_size: [128, 256, 512]
      dropout_rate: [0.2, 0.3, 0.4]

# Task Management Configuration
task_management:
  queue_config:
    max_size: 10000
    priority_levels: 4
    default_priority: 2
    
  retry_config:
    max_retries: 3
    retry_delay: 60  # seconds
    exponential_backoff: true
    
  timeout_config:
    pattern_detection: 300  # 5 minutes
    feature_extraction: 600  # 10 minutes
    repository_analysis: 1800  # 30 minutes
    model_training: 7200  # 2 hours
    batch_inference: 3600  # 1 hour
    
  resource_limits:
    pattern_detection:
      cpu: 2
      memory: **********  # 4GB
      gpu: 0
      
    feature_extraction:
      cpu: 4
      memory: **********  # 8GB
      gpu: 0
      
    repository_analysis:
      cpu: 6
      memory: 1**********  # 12GB
      gpu: 0
      
    model_training:
      cpu: 8
      memory: 16000000000  # 16GB
      gpu: 1
      
    batch_inference:
      cpu: 4
      memory: **********  # 8GB
      gpu: 1

# Monitoring and Observability
monitoring:
  metrics_collection:
    interval: 60  # seconds
    retention: 86400  # 24 hours
    
  health_checks:
    interval: 30  # seconds
    timeout: 10  # seconds
    
  logging:
    level: "INFO"
    format: "json"
    include_metrics: true
    
  alerting:
    enabled: true
    channels:
      - slack
      - email
      - pagerduty
      
    rules:
      - name: "high_error_rate"
        condition: "error_rate > 0.05"
        severity: "warning"
        
      - name: "cluster_down"
        condition: "cluster_health == false"
        severity: "critical"
        
      - name: "high_resource_usage"
        condition: "cpu_utilization > 0.9"
        severity: "warning"
        
      - name: "scaling_failure"
        condition: "scaling_errors > 3"
        severity: "warning"

# Security Configuration
security:
  authentication:
    enabled: true
    method: "service_account"
    
  authorization:
    enabled: true
    rbac: true
    
  network:
    encryption_in_transit: true
    encryption_at_rest: true
    
  secrets:
    provider: "gcp_secret_manager"
    rotation_enabled: true
    rotation_interval: 2592000  # 30 days

# Performance Optimization
performance:
  caching:
    enabled: true
    ttl: 3600  # 1 hour
    max_size: 10000
    
  compression:
    enabled: true
    algorithm: "gzip"
    
  batching:
    enabled: true
    max_batch_size: 100
    max_wait_time: 1.0  # seconds
    
  connection_pooling:
    enabled: true
    max_connections: 100
    connection_timeout: 30  # seconds

# Cost Optimization
cost_optimization:
  enabled: true
  
  preemptible_instances:
    enabled: true
    percentage: 70  # 70% of workers can be preemptible
    
  spot_instances:
    enabled: true
    max_price: 0.10  # $0.10/hour
    
  scheduled_scaling:
    enabled: true
    scale_down_schedule: "0 2 * * *"  # Scale down at 2 AM daily
    scale_up_schedule: "0 8 * * *"   # Scale up at 8 AM daily
    
  idle_resource_cleanup:
    enabled: true
    idle_timeout: 300  # 5 minutes
    
  budget_alerts:
    enabled: true
    daily_budget: 500  # $500/day
    monthly_budget: 15000  # $15,000/month