"""
Enhanced Secret Management with Automatic Rotation for Pattern Mining Service

This module provides automatic secret rotation capabilities specifically
designed for Gemini API keys and other sensitive credentials.
"""

import asyncio
import json
import secrets
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from enum import Enum
import structlog
from google.cloud import secretmanager
from google.auth import default
import redis.asyncio as redis

from .encryption import Secret<PERSON>anager, SecretError
from ..config.settings import Settings

logger = structlog.get_logger()


class RotationStatus(str, Enum):
    """Secret rotation status."""
    ACTIVE = "active"
    PENDING_ROTATION = "pending_rotation"
    ROTATING = "rotating"
    FAILED = "failed"
    DEPRECATED = "deprecated"


class SecretType(str, Enum):
    """Types of secrets for rotation."""
    GEMINI_API_KEY = "gemini_api_key"
    DATABASE_PASSWORD = "database_password"
    REDIS_PASSWORD = "redis_password"
    JWT_SECRET = "jwt_secret"
    ENCRYPTION_KEY = "encryption_key"


@dataclass
class RotationPolicy:
    """Secret rotation policy configuration."""
    secret_type: SecretType
    rotation_interval_hours: int = 24  # Default 24 hours
    grace_period_hours: int = 2  # Grace period for old secrets
    max_versions: int = 5  # Maximum versions to keep
    auto_rotate: bool = True
    notification_webhook: Optional[str] = None
    require_approval: bool = False
    
    
@dataclass
class RotationRecord:
    """Record of secret rotation attempt."""
    rotation_id: str
    secret_name: str
    secret_type: SecretType
    old_version: str
    new_version: Optional[str]
    status: RotationStatus
    started_at: datetime
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class SecretRotationManager:
    """
    Enhanced secret manager with automatic rotation capabilities.
    
    Features:
    - Automatic rotation based on policies
    - Zero-downtime rotation with grace periods
    - Multi-version secret management
    - Integration with Google Secret Manager
    - Rotation monitoring and alerting
    """
    
    def __init__(
        self,
        settings: Settings,
        redis_client: redis.Redis,
        base_secret_manager: SecretManager,
        project_id: Optional[str] = None
    ):
        self.settings = settings
        self.redis_client = redis_client
        self.base_secret_manager = base_secret_manager
        self.project_id = project_id or settings.gcp_project_id
        
        # Initialize GCP Secret Manager client
        try:
            self.gcp_client = secretmanager.SecretManagerServiceClient()
            self.use_gcp = True
        except Exception as e:
            logger.warning("Failed to initialize GCP Secret Manager", error=str(e))
            self.use_gcp = False
        
        # Rotation policies by secret type
        self.rotation_policies: Dict[SecretType, RotationPolicy] = {
            SecretType.GEMINI_API_KEY: RotationPolicy(
                secret_type=SecretType.GEMINI_API_KEY,
                rotation_interval_hours=24,  # Daily rotation for API keys
                grace_period_hours=2,
                max_versions=3,
                auto_rotate=True
            ),
            SecretType.JWT_SECRET: RotationPolicy(
                secret_type=SecretType.JWT_SECRET,
                rotation_interval_hours=168,  # Weekly rotation
                grace_period_hours=4,
                max_versions=2,
                auto_rotate=True
            ),
            SecretType.DATABASE_PASSWORD: RotationPolicy(
                secret_type=SecretType.DATABASE_PASSWORD,
                rotation_interval_hours=720,  # Monthly rotation
                grace_period_hours=24,
                max_versions=2,
                auto_rotate=False,  # Manual approval required
                require_approval=True
            )
        }
        
        # Active rotation tasks
        self.rotation_tasks: Set[str] = set()
        
        # Monitoring
        self.rotation_metrics = {
            "successful_rotations": 0,
            "failed_rotations": 0,
            "last_rotation_check": None,
            "active_secrets": 0
        }
    
    async def initialize(self) -> None:
        """Initialize the rotation manager."""
        try:
            # Start background rotation monitoring
            asyncio.create_task(self._rotation_monitor())
            
            # Initialize metrics
            await self._update_metrics()
            
            logger.info("Secret rotation manager initialized")
            
        except Exception as e:
            logger.error("Failed to initialize rotation manager", error=str(e))
            raise SecretError(f"Rotation manager initialization failed: {e}")
    
    async def register_secret_for_rotation(
        self,
        secret_name: str,
        secret_type: SecretType,
        initial_value: str,
        policy_override: Optional[RotationPolicy] = None
    ) -> str:
        """Register a secret for automatic rotation."""
        try:
            # Use custom policy or default
            policy = policy_override or self.rotation_policies.get(secret_type)
            if not policy:
                raise SecretError(f"No rotation policy defined for type: {secret_type}")
            
            # Store the secret
            version_id = await self.base_secret_manager.store_secret(
                secret_name,
                initial_value,
                metadata={
                    "secret_type": secret_type.value,
                    "rotation_policy": self._serialize_policy(policy),
                    "next_rotation": self._calculate_next_rotation(policy).isoformat(),
                    "created_at": datetime.utcnow().isoformat()
                }
            )
            
            # Create rotation tracking record
            await self._create_rotation_tracking(secret_name, secret_type, policy)
            
            logger.info(
                "Secret registered for rotation",
                secret_name=secret_name,
                secret_type=secret_type.value,
                version_id=version_id
            )
            
            return version_id
            
        except Exception as e:
            logger.error("Failed to register secret for rotation", error=str(e))
            raise SecretError(f"Secret registration failed: {e}")
    
    async def rotate_secret(
        self,
        secret_name: str,
        force: bool = False,
        new_value: Optional[str] = None
    ) -> RotationRecord:
        """
        Rotate a specific secret.
        
        Args:
            secret_name: Name of the secret to rotate
            force: Force rotation even if not due
            new_value: Optional new value (if None, will be generated)
        """
        rotation_id = secrets.token_hex(8)
        
        try:
            # Check if rotation is already in progress
            if secret_name in self.rotation_tasks:
                raise SecretError(f"Rotation already in progress for {secret_name}")
            
            self.rotation_tasks.add(secret_name)
            
            # Get current secret metadata
            metadata = await self._get_secret_metadata(secret_name)
            if not metadata:
                raise SecretError(f"Secret not found: {secret_name}")
            
            secret_type = SecretType(metadata.get("secret_type"))
            policy = self._deserialize_policy(metadata.get("rotation_policy"))
            
            # Check if rotation is due (unless forced)
            if not force and not await self._is_rotation_due(secret_name, policy):
                raise SecretError(f"Rotation not due for {secret_name}")
            
            # Create rotation record
            rotation_record = RotationRecord(
                rotation_id=rotation_id,
                secret_name=secret_name,
                secret_type=secret_type,
                old_version=metadata.get("current_version"),
                new_version=None,
                status=RotationStatus.ROTATING,
                started_at=datetime.utcnow()
            )
            
            await self._save_rotation_record(rotation_record)
            
            # Generate new secret value if not provided
            if new_value is None:
                new_value = await self._generate_new_secret_value(secret_type)
            
            # Validate new secret value
            await self._validate_secret_value(secret_type, new_value)
            
            # Store new secret version
            new_version_id = await self.base_secret_manager.store_secret(
                secret_name,
                new_value,
                metadata={
                    **metadata,
                    "rotation_id": rotation_id,
                    "rotated_at": datetime.utcnow().isoformat(),
                    "next_rotation": self._calculate_next_rotation(policy).isoformat()
                }
            )
            
            # Update rotation record
            rotation_record.new_version = new_version_id
            rotation_record.status = RotationStatus.ACTIVE
            rotation_record.completed_at = datetime.utcnow()
            
            await self._save_rotation_record(rotation_record)
            
            # Clean up old versions
            await self._cleanup_old_versions(secret_name, policy.max_versions)
            
            # Update metrics
            self.rotation_metrics["successful_rotations"] += 1
            
            logger.info(
                "Secret rotation completed",
                secret_name=secret_name,
                rotation_id=rotation_id,
                old_version=rotation_record.old_version,
                new_version=new_version_id
            )
            
            return rotation_record
            
        except Exception as e:
            # Mark rotation as failed
            rotation_record = RotationRecord(
                rotation_id=rotation_id,
                secret_name=secret_name,
                secret_type=SecretType.GEMINI_API_KEY,  # Default, will be updated
                old_version="unknown",
                new_version=None,
                status=RotationStatus.FAILED,
                started_at=datetime.utcnow(),
                completed_at=datetime.utcnow(),
                error_message=str(e)
            )
            
            await self._save_rotation_record(rotation_record)
            self.rotation_metrics["failed_rotations"] += 1
            
            logger.error(
                "Secret rotation failed",
                secret_name=secret_name,
                rotation_id=rotation_id,
                error=str(e)
            )
            
            raise SecretError(f"Secret rotation failed: {e}")
            
        finally:
            self.rotation_tasks.discard(secret_name)
    
    async def get_active_secret(self, secret_name: str) -> Optional[str]:
        """Get the currently active secret value."""
        return await self.base_secret_manager.get_secret(secret_name)
    
    async def get_secret_versions(self, secret_name: str) -> List[Dict[str, Any]]:
        """Get all versions of a secret."""
        try:
            versions = []
            
            if self.use_gcp:
                # Get versions from GCP Secret Manager
                parent = f"projects/{self.project_id}/secrets/{secret_name}"
                try:
                    for version in self.gcp_client.list_secret_versions(parent=parent):
                        version_info = {
                            "version_id": version.name.split("/")[-1],
                            "state": version.state.name,
                            "created_at": version.create_time.isoformat(),
                            "is_active": version.state.name == "ENABLED"
                        }
                        versions.append(version_info)
                except Exception as e:
                    logger.warning("Failed to list GCP secret versions", error=str(e))
            
            # Fallback to Redis-based versions
            keys = await self.redis_client.keys(f"secret:{secret_name}:*")
            for key in keys:
                version_data = await self.redis_client.hgetall(key)
                if version_data:
                    version_info = {
                        "version_id": version_data.get("version_id"),
                        "created_at": version_data.get("created_at"),
                        "is_active": version_data.get("is_active", "true") == "true"
                    }
                    versions.append(version_info)
            
            return sorted(versions, key=lambda x: x["created_at"], reverse=True)
            
        except Exception as e:
            logger.error("Failed to get secret versions", error=str(e))
            return []
    
    async def force_rotate_all_gemini_keys(self) -> Dict[str, RotationRecord]:
        """Force rotation of all Gemini API keys (emergency use)."""
        results = {}
        
        # Find all Gemini API key secrets
        gemini_secrets = await self._find_secrets_by_type(SecretType.GEMINI_API_KEY)
        
        for secret_name in gemini_secrets:
            try:
                record = await self.rotate_secret(secret_name, force=True)
                results[secret_name] = record
            except Exception as e:
                logger.error(f"Failed to rotate Gemini key {secret_name}", error=str(e))
                results[secret_name] = RotationRecord(
                    rotation_id=secrets.token_hex(8),
                    secret_name=secret_name,
                    secret_type=SecretType.GEMINI_API_KEY,
                    old_version="unknown",
                    new_version=None,
                    status=RotationStatus.FAILED,
                    started_at=datetime.utcnow(),
                    error_message=str(e)
                )
        
        return results
    
    def get_rotation_metrics(self) -> Dict[str, Any]:
        """Get rotation metrics and status."""
        return {
            **self.rotation_metrics,
            "active_rotation_tasks": len(self.rotation_tasks),
            "last_check": datetime.utcnow().isoformat()
        }
    
    # Private helper methods
    
    async def _rotation_monitor(self) -> None:
        """Background task to monitor and perform scheduled rotations."""
        while True:
            try:
                await self._check_rotations_due()
                await self._update_metrics()
                
                # Wait 1 hour before next check
                await asyncio.sleep(3600)
                
            except Exception as e:
                logger.error("Rotation monitor error", error=str(e))
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def _check_rotations_due(self) -> None:
        """Check for secrets that need rotation."""
        try:
            # Get all tracked secrets
            keys = await self.redis_client.keys("rotation_tracking:*")
            
            for key in keys:
                tracking_data = await self.redis_client.hgetall(key)
                if not tracking_data:
                    continue
                
                secret_name = tracking_data.get("secret_name")
                next_rotation = datetime.fromisoformat(tracking_data.get("next_rotation", ""))
                auto_rotate = tracking_data.get("auto_rotate", "true") == "true"
                
                if auto_rotate and datetime.utcnow() >= next_rotation:
                    if secret_name not in self.rotation_tasks:
                        logger.info(f"Automatic rotation due for {secret_name}")
                        asyncio.create_task(self.rotate_secret(secret_name))
                        
        except Exception as e:
            logger.error("Failed to check rotations due", error=str(e))
    
    async def _generate_new_secret_value(self, secret_type: SecretType) -> str:
        """Generate a new secret value based on type."""
        if secret_type == SecretType.GEMINI_API_KEY:
            # Note: In production, this would require actual API key generation from Google
            # This is a placeholder for the security audit
            return f"gai-{secrets.token_hex(32)}"
        elif secret_type == SecretType.JWT_SECRET:
            return secrets.token_urlsafe(64)
        elif secret_type in [SecretType.DATABASE_PASSWORD, SecretType.REDIS_PASSWORD]:
            # Generate strong password
            chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
            return ''.join(secrets.choice(chars) for _ in range(32))
        else:
            return secrets.token_urlsafe(32)
    
    async def _validate_secret_value(self, secret_type: SecretType, value: str) -> None:
        """Validate a secret value."""
        if secret_type == SecretType.GEMINI_API_KEY:
            if not value.startswith(("AIza", "gai-")):
                raise SecretError("Invalid Gemini API key format")
        elif secret_type == SecretType.JWT_SECRET:
            if len(value) < 32:
                raise SecretError("JWT secret too short")
        elif secret_type in [SecretType.DATABASE_PASSWORD, SecretType.REDIS_PASSWORD]:
            if len(value) < 16:
                raise SecretError("Password too short")
    
    def _calculate_next_rotation(self, policy: RotationPolicy) -> datetime:
        """Calculate next rotation time."""
        return datetime.utcnow() + timedelta(hours=policy.rotation_interval_hours)
    
    def _serialize_policy(self, policy: RotationPolicy) -> str:
        """Serialize rotation policy to JSON."""
        return json.dumps({
            "secret_type": policy.secret_type.value,
            "rotation_interval_hours": policy.rotation_interval_hours,
            "grace_period_hours": policy.grace_period_hours,
            "max_versions": policy.max_versions,
            "auto_rotate": policy.auto_rotate,
            "require_approval": policy.require_approval
        })
    
    def _deserialize_policy(self, policy_json: str) -> RotationPolicy:
        """Deserialize rotation policy from JSON."""
        data = json.loads(policy_json)
        return RotationPolicy(
            secret_type=SecretType(data["secret_type"]),
            rotation_interval_hours=data["rotation_interval_hours"],
            grace_period_hours=data["grace_period_hours"],
            max_versions=data["max_versions"],
            auto_rotate=data["auto_rotate"],
            require_approval=data.get("require_approval", False)
        )
    
    async def _get_secret_metadata(self, secret_name: str) -> Optional[Dict[str, Any]]:
        """Get secret metadata."""
        key = f"rotation_tracking:{secret_name}"
        return await self.redis_client.hgetall(key)
    
    async def _is_rotation_due(self, secret_name: str, policy: RotationPolicy) -> bool:
        """Check if rotation is due for a secret."""
        metadata = await self._get_secret_metadata(secret_name)
        if not metadata:
            return True  # Rotate if no metadata
        
        next_rotation_str = metadata.get("next_rotation")
        if not next_rotation_str:
            return True
        
        next_rotation = datetime.fromisoformat(next_rotation_str)
        return datetime.utcnow() >= next_rotation
    
    async def _create_rotation_tracking(
        self,
        secret_name: str,
        secret_type: SecretType,
        policy: RotationPolicy
    ) -> None:
        """Create rotation tracking record."""
        key = f"rotation_tracking:{secret_name}"
        tracking_data = {
            "secret_name": secret_name,
            "secret_type": secret_type.value,
            "rotation_policy": self._serialize_policy(policy),
            "next_rotation": self._calculate_next_rotation(policy).isoformat(),
            "auto_rotate": str(policy.auto_rotate),
            "created_at": datetime.utcnow().isoformat()
        }
        
        await self.redis_client.hset(key, mapping=tracking_data)
    
    async def _save_rotation_record(self, record: RotationRecord) -> None:
        """Save rotation record."""
        key = f"rotation_record:{record.rotation_id}"
        record_data = {
            "rotation_id": record.rotation_id,
            "secret_name": record.secret_name,
            "secret_type": record.secret_type.value,
            "old_version": record.old_version,
            "new_version": record.new_version or "",
            "status": record.status.value,
            "started_at": record.started_at.isoformat(),
            "completed_at": record.completed_at.isoformat() if record.completed_at else "",
            "error_message": record.error_message or "",
            "metadata": json.dumps(record.metadata)
        }
        
        await self.redis_client.hset(key, mapping=record_data)
        
        # Set expiration (keep records for 30 days)
        await self.redis_client.expire(key, 30 * 24 * 3600)
    
    async def _cleanup_old_versions(self, secret_name: str, max_versions: int) -> None:
        """Clean up old secret versions."""
        versions = await self.get_secret_versions(secret_name)
        
        if len(versions) > max_versions:
            # Keep the most recent versions
            versions_to_delete = versions[max_versions:]
            
            for version in versions_to_delete:
                version_id = version["version_id"]
                
                if self.use_gcp:
                    try:
                        version_path = f"projects/{self.project_id}/secrets/{secret_name}/versions/{version_id}"
                        self.gcp_client.destroy_secret_version(name=version_path)
                    except Exception as e:
                        logger.warning(f"Failed to delete GCP version {version_id}", error=str(e))
                
                # Delete from Redis
                redis_key = f"secret:{secret_name}:{version_id}"
                await self.redis_client.delete(redis_key)
    
    async def _find_secrets_by_type(self, secret_type: SecretType) -> List[str]:
        """Find all secrets of a specific type."""
        secrets = []
        keys = await self.redis_client.keys("rotation_tracking:*")
        
        for key in keys:
            tracking_data = await self.redis_client.hgetall(key)
            if tracking_data.get("secret_type") == secret_type.value:
                secrets.append(tracking_data.get("secret_name"))
        
        return secrets
    
    async def _update_metrics(self) -> None:
        """Update rotation metrics."""
        self.rotation_metrics["last_rotation_check"] = datetime.utcnow().isoformat()
        
        # Count active secrets
        keys = await self.redis_client.keys("rotation_tracking:*")
        self.rotation_metrics["active_secrets"] = len(keys)