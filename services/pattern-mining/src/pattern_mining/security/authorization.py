"""
Authorization System for Pattern Mining Service

Provides comprehensive authorization including:
- Role-based access control (RBAC)
- Resource-based permissions
- API endpoint authorization
- Data access controls
- Privilege escalation prevention
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Any, Union
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
import redis.asyncio as redis
import structlog
from fastapi import HTT<PERSON>Ex<PERSON>, Request
from ..models.api import User

logger = structlog.get_logger()


class AuthorizationError(Exception):
    """Base authorization error."""
    pass


class PermissionError(AuthorizationError):
    """Permission-related errors."""
    pass


class RoleError(AuthorizationError):
    """Role-related errors."""
    pass


class Action(str, Enum):
    """Available actions."""
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"
    EXECUTE = "execute"
    ADMIN = "admin"


class Resource(str, Enum):
    """Available resources."""
    PATTERN = "pattern"
    MODEL = "model"
    ANALYSIS = "analysis"
    REPOSITORY = "repository"
    USER = "user"
    SYSTEM = "system"
    API = "api"


class Permission(str, Enum):
    """System permissions."""
    # Pattern permissions
    PATTERN_CREATE = "pattern:create"
    PATTERN_READ = "pattern:read"
    PATTERN_UPDATE = "pattern:update"
    PATTERN_DELETE = "pattern:delete"
    
    # Model permissions
    MODEL_CREATE = "model:create"
    MODEL_READ = "model:read"
    MODEL_UPDATE = "model:update"
    MODEL_DELETE = "model:delete"
    MODEL_TRAIN = "model:train"
    MODEL_DEPLOY = "model:deploy"
    
    # Analysis permissions
    ANALYSIS_CREATE = "analysis:create"
    ANALYSIS_READ = "analysis:read"
    ANALYSIS_UPDATE = "analysis:update"
    ANALYSIS_DELETE = "analysis:delete"
    ANALYSIS_EXECUTE = "analysis:execute"
    
    # Repository permissions
    REPOSITORY_CREATE = "repository:create"
    REPOSITORY_READ = "repository:read"
    REPOSITORY_UPDATE = "repository:update"
    REPOSITORY_DELETE = "repository:delete"
    REPOSITORY_ADMIN = "repository:admin"
    
    # User permissions
    USER_CREATE = "user:create"
    USER_READ = "user:read"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    USER_ADMIN = "user:admin"
    
    # System permissions
    SYSTEM_ADMIN = "system:admin"
    SYSTEM_MONITOR = "system:monitor"
    SYSTEM_CONFIG = "system:config"
    
    # API permissions
    API_READ = "api:read"
    API_WRITE = "api:write"
    API_ADMIN = "api:admin"


class Role(str, Enum):
    """System roles."""
    ADMIN = "admin"
    MODERATOR = "moderator"
    USER = "user"
    READONLY = "readonly"
    SERVICE = "service"
    ANALYST = "analyst"
    DEVELOPER = "developer"


@dataclass
class RoleDefinition:
    """Role definition with permissions."""
    name: str
    permissions: Set[Permission]
    description: str
    is_system_role: bool = False
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)


@dataclass
class ResourcePermission:
    """Resource-specific permission."""
    resource_type: Resource
    resource_id: str
    action: Action
    granted_by: str
    granted_at: datetime = field(default_factory=datetime.utcnow)
    expires_at: Optional[datetime] = None


@dataclass
class AccessRequest:
    """Access request for authorization."""
    user_id: str
    resource_type: Resource
    resource_id: str
    action: Action
    context: Dict[str, Any] = field(default_factory=dict)


class AuthorizationPolicy(ABC):
    """Abstract authorization policy."""
    
    @abstractmethod
    async def check_access(self, request: AccessRequest) -> bool:
        """Check if access should be granted."""
        pass


class RoleBasedPolicy(AuthorizationPolicy):
    """Role-based authorization policy."""
    
    def __init__(self, role_manager: 'RoleManager'):
        self.role_manager = role_manager
    
    async def check_access(self, request: AccessRequest) -> bool:
        """Check access based on user roles."""
        user_roles = await self.role_manager.get_user_roles(request.user_id)
        required_permission = f"{request.resource_type.value}:{request.action.value}"
        
        for role in user_roles:
            role_permissions = await self.role_manager.get_role_permissions(role)
            if required_permission in role_permissions:
                return True
        
        return False


class ResourceBasedPolicy(AuthorizationPolicy):
    """Resource-based authorization policy."""
    
    def __init__(self, resource_manager: 'ResourceManager'):
        self.resource_manager = resource_manager
    
    async def check_access(self, request: AccessRequest) -> bool:
        """Check access based on resource ownership."""
        return await self.resource_manager.check_resource_access(
            request.user_id,
            request.resource_type,
            request.resource_id,
            request.action
        )


class ContextBasedPolicy(AuthorizationPolicy):
    """Context-based authorization policy."""
    
    async def check_access(self, request: AccessRequest) -> bool:
        """Check access based on context (IP, time, etc.)."""
        # Check IP whitelist
        if "ip_address" in request.context:
            allowed_ips = request.context.get("allowed_ips", [])
            if allowed_ips and request.context["ip_address"] not in allowed_ips:
                return False
        
        # Check time-based access
        if "time_restrictions" in request.context:
            current_time = datetime.utcnow().time()
            restrictions = request.context["time_restrictions"]
            
            if "start_time" in restrictions and current_time < restrictions["start_time"]:
                return False
            if "end_time" in restrictions and current_time > restrictions["end_time"]:
                return False
        
        return True


class RoleManager:
    """Role management service."""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis_client = redis_client
        self._default_roles = self._create_default_roles()
    
    def _create_default_roles(self) -> Dict[Role, RoleDefinition]:
        """Create default system roles."""
        return {
            Role.ADMIN: RoleDefinition(
                name="admin",
                permissions={
                    Permission.SYSTEM_ADMIN,
                    Permission.USER_ADMIN,
                    Permission.REPOSITORY_ADMIN,
                    Permission.MODEL_CREATE,
                    Permission.MODEL_READ,
                    Permission.MODEL_UPDATE,
                    Permission.MODEL_DELETE,
                    Permission.MODEL_TRAIN,
                    Permission.MODEL_DEPLOY,
                    Permission.PATTERN_CREATE,
                    Permission.PATTERN_READ,
                    Permission.PATTERN_UPDATE,
                    Permission.PATTERN_DELETE,
                    Permission.ANALYSIS_CREATE,
                    Permission.ANALYSIS_READ,
                    Permission.ANALYSIS_UPDATE,
                    Permission.ANALYSIS_DELETE,
                    Permission.ANALYSIS_EXECUTE,
                    Permission.API_ADMIN,
                },
                description="Full system administrator",
                is_system_role=True
            ),
            Role.MODERATOR: RoleDefinition(
                name="moderator",
                permissions={
                    Permission.USER_READ,
                    Permission.USER_UPDATE,
                    Permission.REPOSITORY_READ,
                    Permission.REPOSITORY_UPDATE,
                    Permission.MODEL_READ,
                    Permission.MODEL_UPDATE,
                    Permission.PATTERN_READ,
                    Permission.PATTERN_UPDATE,
                    Permission.ANALYSIS_READ,
                    Permission.ANALYSIS_EXECUTE,
                    Permission.API_WRITE,
                },
                description="Content moderator",
                is_system_role=True
            ),
            Role.USER: RoleDefinition(
                name="user",
                permissions={
                    Permission.PATTERN_CREATE,
                    Permission.PATTERN_READ,
                    Permission.PATTERN_UPDATE,
                    Permission.ANALYSIS_CREATE,
                    Permission.ANALYSIS_READ,
                    Permission.ANALYSIS_EXECUTE,
                    Permission.MODEL_READ,
                    Permission.REPOSITORY_READ,
                    Permission.API_READ,
                    Permission.API_WRITE,
                },
                description="Regular user",
                is_system_role=True
            ),
            Role.READONLY: RoleDefinition(
                name="readonly",
                permissions={
                    Permission.PATTERN_READ,
                    Permission.ANALYSIS_READ,
                    Permission.MODEL_READ,
                    Permission.REPOSITORY_READ,
                    Permission.API_READ,
                },
                description="Read-only access",
                is_system_role=True
            ),
            Role.SERVICE: RoleDefinition(
                name="service",
                permissions={
                    Permission.PATTERN_CREATE,
                    Permission.PATTERN_READ,
                    Permission.PATTERN_UPDATE,
                    Permission.ANALYSIS_CREATE,
                    Permission.ANALYSIS_READ,
                    Permission.ANALYSIS_EXECUTE,
                    Permission.MODEL_READ,
                    Permission.MODEL_TRAIN,
                    Permission.API_READ,
                    Permission.API_WRITE,
                },
                description="Service account",
                is_system_role=True
            ),
            Role.ANALYST: RoleDefinition(
                name="analyst",
                permissions={
                    Permission.PATTERN_READ,
                    Permission.ANALYSIS_CREATE,
                    Permission.ANALYSIS_READ,
                    Permission.ANALYSIS_UPDATE,
                    Permission.ANALYSIS_EXECUTE,
                    Permission.MODEL_READ,
                    Permission.REPOSITORY_READ,
                    Permission.API_READ,
                    Permission.API_WRITE,
                },
                description="Data analyst",
                is_system_role=True
            ),
            Role.DEVELOPER: RoleDefinition(
                name="developer",
                permissions={
                    Permission.PATTERN_CREATE,
                    Permission.PATTERN_READ,
                    Permission.PATTERN_UPDATE,
                    Permission.MODEL_CREATE,
                    Permission.MODEL_READ,
                    Permission.MODEL_UPDATE,
                    Permission.MODEL_TRAIN,
                    Permission.ANALYSIS_CREATE,
                    Permission.ANALYSIS_READ,
                    Permission.ANALYSIS_EXECUTE,
                    Permission.REPOSITORY_READ,
                    Permission.REPOSITORY_UPDATE,
                    Permission.API_READ,
                    Permission.API_WRITE,
                },
                description="Developer",
                is_system_role=True
            )
        }
    
    async def create_role(self, role: RoleDefinition) -> None:
        """Create a new role."""
        role_key = f"role:{role.name}"
        role_data = {
            "name": role.name,
            "permissions": [p.value for p in role.permissions],
            "description": role.description,
            "is_system_role": role.is_system_role,
            "created_at": role.created_at.isoformat(),
            "updated_at": role.updated_at.isoformat()
        }
        
        await self.redis_client.hset(role_key, mapping=role_data)
        
        logger.info(
            "Role created",
            role_name=role.name,
            permission_count=len(role.permissions)
        )
    
    async def get_role(self, role_name: str) -> Optional[RoleDefinition]:
        """Get role by name."""
        role_key = f"role:{role_name}"
        role_data = await self.redis_client.hgetall(role_key)
        
        if not role_data:
            return None
        
        return RoleDefinition(
            name=role_data["name"],
            permissions={Permission(p) for p in json.loads(role_data["permissions"])},
            description=role_data["description"],
            is_system_role=role_data["is_system_role"] == "True",
            created_at=datetime.fromisoformat(role_data["created_at"]),
            updated_at=datetime.fromisoformat(role_data["updated_at"])
        )
    
    async def assign_role_to_user(self, user_id: str, role_name: str) -> None:
        """Assign role to user."""
        user_roles_key = f"user_roles:{user_id}"
        await self.redis_client.sadd(user_roles_key, role_name)
        
        logger.info(
            "Role assigned to user",
            user_id=user_id,
            role_name=role_name
        )
    
    async def remove_role_from_user(self, user_id: str, role_name: str) -> None:
        """Remove role from user."""
        user_roles_key = f"user_roles:{user_id}"
        await self.redis_client.srem(user_roles_key, role_name)
        
        logger.info(
            "Role removed from user",
            user_id=user_id,
            role_name=role_name
        )
    
    async def get_user_roles(self, user_id: str) -> List[str]:
        """Get user roles."""
        user_roles_key = f"user_roles:{user_id}"
        roles = await self.redis_client.smembers(user_roles_key)
        return list(roles)
    
    async def get_role_permissions(self, role_name: str) -> Set[str]:
        """Get permissions for a role."""
        role = await self.get_role(role_name)
        if not role:
            return set()
        
        return {p.value for p in role.permissions}
    
    async def initialize_default_roles(self) -> None:
        """Initialize default system roles."""
        for role_def in self._default_roles.values():
            await self.create_role(role_def)
        
        logger.info("Default roles initialized")


class PermissionManager:
    """Permission management service."""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis_client = redis_client
    
    async def grant_permission(
        self,
        user_id: str,
        resource_type: Resource,
        resource_id: str,
        action: Action,
        granted_by: str,
        expires_at: Optional[datetime] = None
    ) -> None:
        """Grant specific permission to user."""
        permission = ResourcePermission(
            resource_type=resource_type,
            resource_id=resource_id,
            action=action,
            granted_by=granted_by,
            expires_at=expires_at
        )
        
        permission_key = f"permission:{user_id}:{resource_type.value}:{resource_id}:{action.value}"
        permission_data = {
            "resource_type": resource_type.value,
            "resource_id": resource_id,
            "action": action.value,
            "granted_by": granted_by,
            "granted_at": permission.granted_at.isoformat(),
            "expires_at": permission.expires_at.isoformat() if permission.expires_at else None
        }
        
        await self.redis_client.hset(permission_key, mapping=permission_data)
        
        # Set expiration if specified
        if expires_at:
            ttl = int((expires_at - datetime.utcnow()).total_seconds())
            await self.redis_client.expire(permission_key, ttl)
        
        logger.info(
            "Permission granted",
            user_id=user_id,
            resource_type=resource_type.value,
            resource_id=resource_id,
            action=action.value,
            granted_by=granted_by
        )
    
    async def revoke_permission(
        self,
        user_id: str,
        resource_type: Resource,
        resource_id: str,
        action: Action
    ) -> None:
        """Revoke specific permission from user."""
        permission_key = f"permission:{user_id}:{resource_type.value}:{resource_id}:{action.value}"
        await self.redis_client.delete(permission_key)
        
        logger.info(
            "Permission revoked",
            user_id=user_id,
            resource_type=resource_type.value,
            resource_id=resource_id,
            action=action.value
        )
    
    async def check_permission(
        self,
        user_id: str,
        resource_type: Resource,
        resource_id: str,
        action: Action
    ) -> bool:
        """Check if user has specific permission."""
        permission_key = f"permission:{user_id}:{resource_type.value}:{resource_id}:{action.value}"
        permission_data = await self.redis_client.hgetall(permission_key)
        
        if not permission_data:
            return False
        
        # Check if permission has expired
        if permission_data.get("expires_at"):
            expires_at = datetime.fromisoformat(permission_data["expires_at"])
            if expires_at < datetime.utcnow():
                await self.redis_client.delete(permission_key)
                return False
        
        return True
    
    async def get_user_permissions(self, user_id: str) -> List[ResourcePermission]:
        """Get all permissions for a user."""
        pattern = f"permission:{user_id}:*"
        keys = await self.redis_client.keys(pattern)
        permissions = []
        
        for key in keys:
            permission_data = await self.redis_client.hgetall(key)
            if permission_data:
                # Check if expired
                if permission_data.get("expires_at"):
                    expires_at = datetime.fromisoformat(permission_data["expires_at"])
                    if expires_at < datetime.utcnow():
                        await self.redis_client.delete(key)
                        continue
                
                permissions.append(ResourcePermission(
                    resource_type=Resource(permission_data["resource_type"]),
                    resource_id=permission_data["resource_id"],
                    action=Action(permission_data["action"]),
                    granted_by=permission_data["granted_by"],
                    granted_at=datetime.fromisoformat(permission_data["granted_at"]),
                    expires_at=datetime.fromisoformat(permission_data["expires_at"]) if permission_data.get("expires_at") else None
                ))
        
        return permissions


class RBACAuthorizer:
    """Role-Based Access Control authorizer."""
    
    def __init__(
        self,
        role_manager: RoleManager,
        permission_manager: PermissionManager,
        redis_client: redis.Redis
    ):
        self.role_manager = role_manager
        self.permission_manager = permission_manager
        self.redis_client = redis_client
        self.policies = [
            RoleBasedPolicy(role_manager),
            ResourceBasedPolicy(self),
            ContextBasedPolicy()
        ]
    
    async def authorize(self, request: AccessRequest) -> bool:
        """Authorize access request."""
        # Check cache first
        cache_key = f"auth_cache:{request.user_id}:{request.resource_type.value}:{request.resource_id}:{request.action.value}"
        cached_result = await self.redis_client.get(cache_key)
        
        if cached_result is not None:
            return cached_result == "true"
        
        # Check all policies
        for policy in self.policies:
            if await policy.check_access(request):
                # Cache positive result for 5 minutes
                await self.redis_client.setex(cache_key, 300, "true")
                
                logger.info(
                    "Access granted",
                    user_id=request.user_id,
                    resource_type=request.resource_type.value,
                    resource_id=request.resource_id,
                    action=request.action.value
                )
                
                return True
        
        # Cache negative result for 1 minute
        await self.redis_client.setex(cache_key, 60, "false")
        
        logger.warning(
            "Access denied",
            user_id=request.user_id,
            resource_type=request.resource_type.value,
            resource_id=request.resource_id,
            action=request.action.value
        )
        
        return False
    
    async def check_resource_access(
        self,
        user_id: str,
        resource_type: Resource,
        resource_id: str,
        action: Action
    ) -> bool:
        """Check resource-specific access."""
        # Check specific permission
        if await self.permission_manager.check_permission(user_id, resource_type, resource_id, action):
            return True
        
        # Check ownership (for user-owned resources)
        if resource_type == Resource.PATTERN:
            owner_key = f"pattern_owner:{resource_id}"
            owner_id = await self.redis_client.get(owner_key)
            if owner_id == user_id:
                return True
        
        return False


class ResourceAuthorizer:
    """Resource-based authorization."""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis_client = redis_client
    
    async def set_resource_owner(self, resource_type: Resource, resource_id: str, user_id: str) -> None:
        """Set resource owner."""
        owner_key = f"{resource_type.value}_owner:{resource_id}"
        await self.redis_client.set(owner_key, user_id)
        
        logger.info(
            "Resource owner set",
            resource_type=resource_type.value,
            resource_id=resource_id,
            user_id=user_id
        )
    
    async def get_resource_owner(self, resource_type: Resource, resource_id: str) -> Optional[str]:
        """Get resource owner."""
        owner_key = f"{resource_type.value}_owner:{resource_id}"
        return await self.redis_client.get(owner_key)
    
    async def check_resource_ownership(
        self,
        user_id: str,
        resource_type: Resource,
        resource_id: str
    ) -> bool:
        """Check if user owns resource."""
        owner = await self.get_resource_owner(resource_type, resource_id)
        return owner == user_id