"""
Security Integration Module for Pattern Mining Service

This module provides a unified interface for all security components
and integrates them with the main application.
"""

import asyncio
from typing import Dict, List, Optional, Any
from fastapi import Fast<PERSON><PERSON>, Request, HTTPException
from fastapi.security import HTTPBearer
import redis.asyncio as redis
import structlog
from ..config.settings import Settings
from .authentication import (
    JWTAuthenticator, 
    OAuth2Authenticator, 
    ServiceAccountAuthenticator,
    MFAAuthenticator,
    SessionManager
)
from .authorization import (
    RBACAuthorizer,
    RoleManager,
    PermissionManager,
    ResourceAuthorizer
)
from .rate_limiting import RateLimitManager
from .middleware import SecurityMiddleware
from .encryption import (
    EncryptionManager,
    SecretManager,
    KeyManager,
    CertificateManager
)

logger = structlog.get_logger()


class SecurityIntegration:
    """Main security integration service."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.redis_client: Optional[redis.Redis] = None
        
        # Security components
        self.jwt_authenticator: Optional[JWTAuthenticator] = None
        self.oauth2_authenticator: Optional[OAuth2Authenticator] = None
        self.service_account_authenticator: Optional[ServiceAccountAuthenticator] = None
        self.mfa_authenticator: Optional[MFAAuthenticator] = None
        self.session_manager: Optional[SessionManager] = None
        
        self.rbac_authorizer: Optional[RBACAuthorizer] = None
        self.role_manager: Optional[RoleManager] = None
        self.permission_manager: Optional[PermissionManager] = None
        self.resource_authorizer: Optional[ResourceAuthorizer] = None
        
        self.rate_limit_manager: Optional[RateLimitManager] = None
        self.encryption_manager: Optional[EncryptionManager] = None
        self.secret_manager: Optional[SecretManager] = None
        self.key_manager: Optional[KeyManager] = None
        self.certificate_manager: Optional[CertificateManager] = None
        
        self.security_middleware: Optional[SecurityMiddleware] = None
        
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize all security components."""
        if self._initialized:
            return
        
        try:
            # Initialize Redis client
            self.redis_client = redis.Redis.from_url(
                self.settings.redis_url,
                decode_responses=True
            )
            
            # Test Redis connection
            await self.redis_client.ping()
            logger.info("Redis connection established")
            
            # Initialize encryption manager first (others depend on it)
            self.encryption_manager = EncryptionManager(
                settings=self.settings,
                redis_client=self.redis_client
            )
            
            # Initialize authentication components
            self.jwt_authenticator = JWTAuthenticator(
                secret_key=self.settings.secret_key,
                redis_client=self.redis_client
            )
            
            self.mfa_authenticator = MFAAuthenticator(
                app_name=self.settings.app_name
            )
            
            self.session_manager = SessionManager(
                redis_client=self.redis_client
            )
            
            # Initialize authorization components
            self.role_manager = RoleManager(self.redis_client)
            await self.role_manager.initialize_default_roles()
            
            self.permission_manager = PermissionManager(self.redis_client)
            
            self.resource_authorizer = ResourceAuthorizer(self.redis_client)
            
            self.rbac_authorizer = RBACAuthorizer(
                role_manager=self.role_manager,
                permission_manager=self.permission_manager,
                redis_client=self.redis_client
            )
            
            # Initialize rate limiting
            self.rate_limit_manager = RateLimitManager(
                redis_client=self.redis_client,
                settings=self.settings
            )
            
            # Initialize secret management
            self.secret_manager = SecretManager(
                settings=self.settings,
                redis_client=self.redis_client,
                encryption_manager=self.encryption_manager
            )
            
            # Initialize key management
            self.key_manager = KeyManager(
                redis_client=self.redis_client,
                encryption_manager=self.encryption_manager
            )
            
            # Initialize certificate management
            self.certificate_manager = CertificateManager(
                redis_client=self.redis_client,
                encryption_manager=self.encryption_manager
            )
            
            # Initialize OAuth2 if configured
            if hasattr(self.settings, 'oauth2_client_id') and self.settings.oauth2_client_id:
                self.oauth2_authenticator = OAuth2Authenticator(
                    client_id=self.settings.oauth2_client_id,
                    client_secret=await self.secret_manager.get_secret("oauth2_client_secret"),
                    redirect_uri=self.settings.oauth2_redirect_uri,
                    scopes=self.settings.oauth2_scopes,
                    redis_client=self.redis_client
                )
            
            # Initialize service account authentication if configured
            if hasattr(self.settings, 'service_account_key') and self.settings.service_account_key:
                self.service_account_authenticator = ServiceAccountAuthenticator(
                    service_account_key=self.settings.service_account_key,
                    scopes=self.settings.service_account_scopes,
                    redis_client=self.redis_client
                )
            
            self._initialized = True
            logger.info("Security integration initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize security integration", error=str(e))
            raise
    
    async def setup_middleware(self, app: FastAPI) -> None:
        """Setup security middleware on FastAPI app."""
        if not self._initialized:
            await self.initialize()
        
        # Create security middleware
        self.security_middleware = SecurityMiddleware(
            app=app,
            settings=self.settings,
            redis_client=self.redis_client,
            jwt_authenticator=self.jwt_authenticator,
            rbac_authorizer=self.rbac_authorizer,
            rate_limit_manager=self.rate_limit_manager
        )
        
        # Add middleware to app
        app.add_middleware(
            type(self.security_middleware),
            settings=self.settings,
            redis_client=self.redis_client,
            jwt_authenticator=self.jwt_authenticator,
            rbac_authorizer=self.rbac_authorizer,
            rate_limit_manager=self.rate_limit_manager
        )
        
        logger.info("Security middleware setup complete")
    
    async def get_current_user(self, request: Request) -> Optional[Dict[str, Any]]:
        """Get current authenticated user from request."""
        if not self._initialized:
            await self.initialize()
        
        try:
            # Try JWT authentication first
            auth_header = request.headers.get("authorization")
            if auth_header and auth_header.startswith("Bearer "):
                token = auth_header.split(" ", 1)[1]
                payload = await self.jwt_authenticator.verify_token(token)
                return payload
            
            # Try API key authentication
            api_key = request.headers.get("x-api-key")
            if api_key:
                key_data = await self.key_manager.validate_api_key(api_key)
                if key_data:
                    return {
                        "sub": key_data["user_id"],
                        "type": "api_key",
                        "permissions": key_data["permissions"]
                    }
            
            # Try session authentication
            session_id = request.headers.get("x-session-id")
            if session_id:
                session = await self.session_manager.get_session(session_id)
                if session and session.is_active:
                    return {
                        "sub": session.user_id,
                        "type": "session",
                        "session_id": session_id
                    }
            
            return None
            
        except Exception as e:
            logger.error("Failed to get current user", error=str(e))
            return None
    
    async def create_user_session(
        self,
        user_id: str,
        request: Request
    ) -> str:
        """Create new user session."""
        if not self._initialized:
            await self.initialize()
        
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent", "")
        
        session = await self.session_manager.create_session(
            user_id=user_id,
            ip_address=client_ip,
            user_agent=user_agent
        )
        
        return session.session_id
    
    async def generate_api_key(
        self,
        user_id: str,
        name: str,
        permissions: List[str]
    ) -> str:
        """Generate API key for user."""
        if not self._initialized:
            await self.initialize()
        
        return await self.key_manager.generate_api_key(
            user_id=user_id,
            name=name,
            permissions=permissions
        )
    
    async def encrypt_sensitive_data(self, data: str) -> str:
        """Encrypt sensitive data."""
        if not self._initialized:
            await self.initialize()
        
        return await self.encryption_manager.encrypt_data(data)
    
    async def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data."""
        if not self._initialized:
            await self.initialize()
        
        return await self.encryption_manager.decrypt_data(encrypted_data)
    
    async def store_secret(self, name: str, value: str) -> str:
        """Store secret securely."""
        if not self._initialized:
            await self.initialize()
        
        return await self.secret_manager.store_secret(name, value)
    
    async def get_secret(self, name: str) -> Optional[str]:
        """Get secret value."""
        if not self._initialized:
            await self.initialize()
        
        return await self.secret_manager.get_secret(name)
    
    async def assign_role_to_user(self, user_id: str, role_name: str) -> None:
        """Assign role to user."""
        if not self._initialized:
            await self.initialize()
        
        await self.role_manager.assign_role_to_user(user_id, role_name)
    
    async def check_user_permission(
        self,
        user_id: str,
        resource_type: str,
        resource_id: str,
        action: str
    ) -> bool:
        """Check if user has permission."""
        if not self._initialized:
            await self.initialize()
        
        from .authorization import AccessRequest, Resource, Action
        
        # Convert string parameters to enums
        try:
            resource_enum = Resource(resource_type)
            action_enum = Action(action)
        except ValueError:
            logger.warning(
                "Invalid resource type or action",
                resource_type=resource_type,
                action=action
            )
            return False
        
        access_request = AccessRequest(
            user_id=user_id,
            resource_type=resource_enum,
            resource_id=resource_id,
            action=action_enum
        )
        
        return await self.rbac_authorizer.authorize(access_request)
    
    async def get_security_metrics(self) -> Dict[str, Any]:
        """Get security metrics."""
        if not self._initialized:
            await self.initialize()
        
        # Get rate limiting metrics
        rate_limit_metrics = {}
        
        # Get authentication metrics
        auth_metrics = {}
        
        # Get key metrics
        key_metrics = {}
        
        # Get certificate metrics
        cert_metrics = {}
        
        return {
            "rate_limiting": rate_limit_metrics,
            "authentication": auth_metrics,
            "key_management": key_metrics,
            "certificate_management": cert_metrics,
            "timestamp": asyncio.get_event_loop().time()
        }
    
    async def perform_security_audit(self) -> Dict[str, Any]:
        """Perform security audit."""
        if not self._initialized:
            await self.initialize()
        
        audit_results = {
            "timestamp": asyncio.get_event_loop().time(),
            "checks": {}
        }
        
        # Check for expiring certificates
        expiring_certs = await self.certificate_manager.check_certificate_expiry()
        audit_results["checks"]["expiring_certificates"] = {
            "status": "warning" if expiring_certs else "ok",
            "count": len(expiring_certs),
            "certificates": expiring_certs
        }
        
        # Check for inactive API keys
        # This would require iterating through all users - simplified for now
        audit_results["checks"]["api_keys"] = {
            "status": "ok",
            "message": "API key audit completed"
        }
        
        # Check Redis connection
        try:
            await self.redis_client.ping()
            audit_results["checks"]["redis_connection"] = {
                "status": "ok",
                "message": "Redis connection healthy"
            }
        except Exception as e:
            audit_results["checks"]["redis_connection"] = {
                "status": "error",
                "message": f"Redis connection failed: {str(e)}"
            }
        
        return audit_results
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address."""
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    async def cleanup(self) -> None:
        """Cleanup security resources."""
        if self.redis_client:
            await self.redis_client.close()
        
        logger.info("Security integration cleanup complete")


# Global security integration instance
security_integration = SecurityIntegration(Settings())


async def get_security_integration() -> SecurityIntegration:
    """Get security integration instance."""
    if not security_integration._initialized:
        await security_integration.initialize()
    return security_integration


async def get_current_user_dependency(request: Request) -> Optional[Dict[str, Any]]:
    """FastAPI dependency to get current user."""
    integration = await get_security_integration()
    return await integration.get_current_user(request)


async def require_authentication(request: Request) -> Dict[str, Any]:
    """FastAPI dependency that requires authentication."""
    user = await get_current_user_dependency(request)
    if not user:
        raise HTTPException(
            status_code=401,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"}
        )
    return user


async def require_permission(
    resource_type: str,
    action: str,
    resource_id: str = "all"
):
    """FastAPI dependency factory that requires specific permission."""
    async def permission_checker(
        request: Request,
        user: Dict[str, Any] = Depends(require_authentication)
    ) -> Dict[str, Any]:
        integration = await get_security_integration()
        
        has_permission = await integration.check_user_permission(
            user_id=user["sub"],
            resource_type=resource_type,
            resource_id=resource_id,
            action=action
        )
        
        if not has_permission:
            raise HTTPException(
                status_code=403,
                detail="Insufficient permissions"
            )
        
        return user
    
    return permission_checker