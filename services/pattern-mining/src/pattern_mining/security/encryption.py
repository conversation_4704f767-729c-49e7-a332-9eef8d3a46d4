"""
Encryption and Secret Management for Pattern Mining Service

Provides comprehensive encryption and secret management including:
- Data encryption at rest
- Data encryption in transit
- Secret management
- API key management
- Certificate management
- Key rotation
"""

import os
import json
import time
import secrets
import hashlib
import hmac
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
import base64
from pathlib import Path
from cryptography.fernet import <PERSON><PERSON>t, MultiFernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.kdf.scrypt import Scrypt
from cryptography.hazmat.backends import default_backend
from cryptography.x509 import load_pem_x509_certificate
from cryptography.hazmat.primitives.serialization import load_pem_private_key
from google.cloud import secretmanager
from google.cloud.secretmanager_v1 import SecretManagerServiceClient
import redis.asyncio as redis
import structlog
from ..config.settings import Settings

logger = structlog.get_logger()


class EncryptionError(Exception):
    """Base encryption error."""
    pass


class SecretError(Exception):
    """Secret management error."""
    pass


class CertificateError(Exception):
    """Certificate management error."""
    pass


class KeyError(Exception):
    """Key management error."""
    pass


class EncryptionAlgorithm(str, Enum):
    """Encryption algorithms."""
    AES_256_GCM = "aes_256_gcm"
    AES_256_CBC = "aes_256_cbc"
    CHACHA20_POLY1305 = "chacha20_poly1305"
    FERNET = "fernet"


class KeyType(str, Enum):
    """Key types."""
    MASTER = "master"
    DATA = "data"
    API = "api"
    SESSION = "session"
    BACKUP = "backup"


@dataclass
class EncryptionKey:
    """Encryption key model."""
    key_id: str
    key_type: KeyType
    algorithm: EncryptionAlgorithm
    key_material: bytes
    created_at: datetime
    expires_at: Optional[datetime] = None
    is_active: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SecretVersion:
    """Secret version model."""
    version_id: str
    secret_name: str
    secret_value: str
    created_at: datetime
    expires_at: Optional[datetime] = None
    is_active: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Certificate:
    """Certificate model."""
    cert_id: str
    name: str
    certificate_pem: str
    private_key_pem: str
    chain_pem: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.utcnow)
    expires_at: Optional[datetime] = None
    is_active: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)


class EncryptionManager:
    """Comprehensive encryption manager."""
    
    def __init__(
        self,
        settings: Settings,
        redis_client: redis.Redis,
        master_key: Optional[bytes] = None
    ):
        self.settings = settings
        self.redis_client = redis_client
        self.backend = default_backend()
        
        # Initialize master key
        if master_key:
            self.master_key = master_key
        else:
            self.master_key = self._derive_master_key()
        
        # Initialize Fernet cipher
        self.fernet = Fernet(base64.urlsafe_b64encode(self.master_key))
        
        # Key storage
        self.keys: Dict[str, EncryptionKey] = {}
        
        # Initialize default keys
        asyncio.create_task(self._initialize_default_keys())
    
    def _derive_master_key(self) -> bytes:
        """Derive master key from settings."""
        # In production, this should come from a secure key management service
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'stable_salt',  # Use a stable salt for consistency
            iterations=100000,
            backend=self.backend
        )
        
        return kdf.derive(self.settings.secret_key.encode())
    
    async def _initialize_default_keys(self) -> None:
        """Initialize default encryption keys."""
        # Create data encryption key
        data_key = self.generate_key(
            key_type=KeyType.DATA,
            algorithm=EncryptionAlgorithm.AES_256_GCM
        )
        await self.store_key(data_key)
        
        # Create API key encryption key
        api_key = self.generate_key(
            key_type=KeyType.API,
            algorithm=EncryptionAlgorithm.FERNET
        )
        await self.store_key(api_key)
        
        # Create session encryption key
        session_key = self.generate_key(
            key_type=KeyType.SESSION,
            algorithm=EncryptionAlgorithm.AES_256_GCM
        )
        await self.store_key(session_key)
        
        logger.info("Default encryption keys initialized")
    
    def generate_key(
        self,
        key_type: KeyType,
        algorithm: EncryptionAlgorithm,
        key_size: int = 32
    ) -> EncryptionKey:
        """Generate new encryption key."""
        key_id = f"{key_type.value}_{secrets.token_hex(8)}"
        
        if algorithm == EncryptionAlgorithm.FERNET:
            key_material = Fernet.generate_key()
        else:
            key_material = secrets.token_bytes(key_size)
        
        key = EncryptionKey(
            key_id=key_id,
            key_type=key_type,
            algorithm=algorithm,
            key_material=key_material,
            created_at=datetime.utcnow()
        )
        
        logger.info(
            "Encryption key generated",
            key_id=key_id,
            key_type=key_type.value,
            algorithm=algorithm.value
        )
        
        return key
    
    async def store_key(self, key: EncryptionKey) -> None:
        """Store encryption key securely."""
        # Encrypt key material with master key
        encrypted_key_material = self.fernet.encrypt(key.key_material)
        
        key_data = {
            "key_id": key.key_id,
            "key_type": key.key_type.value,
            "algorithm": key.algorithm.value,
            "encrypted_key_material": base64.b64encode(encrypted_key_material).decode(),
            "created_at": key.created_at.isoformat(),
            "expires_at": key.expires_at.isoformat() if key.expires_at else None,
            "is_active": key.is_active,
            "metadata": json.dumps(key.metadata)
        }
        
        # Store in Redis
        key_storage_key = f"encryption_key:{key.key_id}"
        await self.redis_client.hset(key_storage_key, mapping=key_data)
        
        # Store in memory cache
        self.keys[key.key_id] = key
        
        logger.info(
            "Encryption key stored",
            key_id=key.key_id,
            key_type=key.key_type.value
        )
    
    async def get_key(self, key_id: str) -> Optional[EncryptionKey]:
        """Get encryption key by ID."""
        # Check memory cache first
        if key_id in self.keys:
            return self.keys[key_id]
        
        # Load from Redis
        key_storage_key = f"encryption_key:{key_id}"
        key_data = await self.redis_client.hgetall(key_storage_key)
        
        if not key_data:
            return None
        
        # Decrypt key material
        encrypted_key_material = base64.b64decode(key_data["encrypted_key_material"])
        key_material = self.fernet.decrypt(encrypted_key_material)
        
        key = EncryptionKey(
            key_id=key_data["key_id"],
            key_type=KeyType(key_data["key_type"]),
            algorithm=EncryptionAlgorithm(key_data["algorithm"]),
            key_material=key_material,
            created_at=datetime.fromisoformat(key_data["created_at"]),
            expires_at=datetime.fromisoformat(key_data["expires_at"]) if key_data.get("expires_at") else None,
            is_active=key_data["is_active"] == "True",
            metadata=json.loads(key_data["metadata"])
        )
        
        # Cache in memory
        self.keys[key_id] = key
        
        return key
    
    async def get_active_key(self, key_type: KeyType) -> Optional[EncryptionKey]:
        """Get active key by type."""
        # Find active key of specified type
        for key in self.keys.values():
            if key.key_type == key_type and key.is_active:
                if key.expires_at and key.expires_at < datetime.utcnow():
                    continue
                return key
        
        # Search in Redis if not in cache
        pattern = f"encryption_key:*"
        keys = await self.redis_client.keys(pattern)
        
        for key_storage_key in keys:
            key_data = await self.redis_client.hgetall(key_storage_key)
            
            if (key_data.get("key_type") == key_type.value and 
                key_data.get("is_active") == "True"):
                
                # Check expiration
                if key_data.get("expires_at"):
                    expires_at = datetime.fromisoformat(key_data["expires_at"])
                    if expires_at < datetime.utcnow():
                        continue
                
                return await self.get_key(key_data["key_id"])
        
        return None
    
    async def encrypt_data(
        self,
        data: Union[str, bytes],
        key_type: KeyType = KeyType.DATA,
        algorithm: Optional[EncryptionAlgorithm] = None
    ) -> str:
        """Encrypt data."""
        if isinstance(data, str):
            data = data.encode()
        
        # Get encryption key
        key = await self.get_active_key(key_type)
        if not key:
            raise EncryptionError(f"No active encryption key found for type: {key_type}")
        
        # Use key's algorithm if not specified
        if not algorithm:
            algorithm = key.algorithm
        
        try:
            if algorithm == EncryptionAlgorithm.FERNET:
                fernet = Fernet(key.key_material)
                encrypted_data = fernet.encrypt(data)
                
            elif algorithm == EncryptionAlgorithm.AES_256_GCM:
                # Generate random IV
                iv = secrets.token_bytes(12)  # 96-bit IV for GCM
                
                # Create cipher
                cipher = Cipher(
                    algorithms.AES(key.key_material),
                    modes.GCM(iv),
                    backend=self.backend
                )
                encryptor = cipher.encryptor()
                
                # Encrypt data
                ciphertext = encryptor.update(data) + encryptor.finalize()
                
                # Combine IV + auth_tag + ciphertext
                encrypted_data = iv + encryptor.tag + ciphertext
                
            elif algorithm == EncryptionAlgorithm.AES_256_CBC:
                # Generate random IV
                iv = secrets.token_bytes(16)  # 128-bit IV for CBC
                
                # Pad data to block size
                padded_data = self._pad_data(data, 16)
                
                # Create cipher
                cipher = Cipher(
                    algorithms.AES(key.key_material),
                    modes.CBC(iv),
                    backend=self.backend
                )
                encryptor = cipher.encryptor()
                
                # Encrypt data
                ciphertext = encryptor.update(padded_data) + encryptor.finalize()
                
                # Combine IV + ciphertext
                encrypted_data = iv + ciphertext
                
            else:
                raise EncryptionError(f"Unsupported algorithm: {algorithm}")
            
            # Encode as base64 for storage
            return base64.b64encode(encrypted_data).decode()
            
        except Exception as e:
            logger.error("Data encryption failed", error=str(e), algorithm=algorithm.value)
            raise EncryptionError(f"Data encryption failed: {str(e)}")
    
    async def decrypt_data(
        self,
        encrypted_data: str,
        key_type: KeyType = KeyType.DATA,
        algorithm: Optional[EncryptionAlgorithm] = None
    ) -> str:
        """Decrypt data."""
        # Get encryption key
        key = await self.get_active_key(key_type)
        if not key:
            raise EncryptionError(f"No active encryption key found for type: {key_type}")
        
        # Use key's algorithm if not specified
        if not algorithm:
            algorithm = key.algorithm
        
        try:
            # Decode from base64
            encrypted_bytes = base64.b64decode(encrypted_data)
            
            if algorithm == EncryptionAlgorithm.FERNET:
                fernet = Fernet(key.key_material)
                decrypted_data = fernet.decrypt(encrypted_bytes)
                
            elif algorithm == EncryptionAlgorithm.AES_256_GCM:
                # Extract IV, auth_tag, and ciphertext
                iv = encrypted_bytes[:12]
                auth_tag = encrypted_bytes[12:28]
                ciphertext = encrypted_bytes[28:]
                
                # Create cipher
                cipher = Cipher(
                    algorithms.AES(key.key_material),
                    modes.GCM(iv, auth_tag),
                    backend=self.backend
                )
                decryptor = cipher.decryptor()
                
                # Decrypt data
                decrypted_data = decryptor.update(ciphertext) + decryptor.finalize()
                
            elif algorithm == EncryptionAlgorithm.AES_256_CBC:
                # Extract IV and ciphertext
                iv = encrypted_bytes[:16]
                ciphertext = encrypted_bytes[16:]
                
                # Create cipher
                cipher = Cipher(
                    algorithms.AES(key.key_material),
                    modes.CBC(iv),
                    backend=self.backend
                )
                decryptor = cipher.decryptor()
                
                # Decrypt data
                padded_data = decryptor.update(ciphertext) + decryptor.finalize()
                
                # Remove padding
                decrypted_data = self._unpad_data(padded_data)
                
            else:
                raise EncryptionError(f"Unsupported algorithm: {algorithm}")
            
            return decrypted_data.decode()
            
        except Exception as e:
            logger.error("Data decryption failed", error=str(e), algorithm=algorithm.value)
            raise EncryptionError(f"Data decryption failed: {str(e)}")
    
    def _pad_data(self, data: bytes, block_size: int) -> bytes:
        """Pad data to block size using PKCS7 padding."""
        padding_length = block_size - (len(data) % block_size)
        padding = bytes([padding_length] * padding_length)
        return data + padding
    
    def _unpad_data(self, data: bytes) -> bytes:
        """Remove PKCS7 padding."""
        padding_length = data[-1]
        return data[:-padding_length]
    
    async def rotate_key(self, key_type: KeyType) -> EncryptionKey:
        """Rotate encryption key."""
        # Get current key
        current_key = await self.get_active_key(key_type)
        if not current_key:
            raise EncryptionError(f"No active key found for type: {key_type}")
        
        # Generate new key
        new_key = self.generate_key(key_type, current_key.algorithm)
        
        # Deactivate old key
        current_key.is_active = False
        await self.store_key(current_key)
        
        # Store new key
        await self.store_key(new_key)
        
        logger.info(
            "Key rotated",
            key_type=key_type.value,
            old_key_id=current_key.key_id,
            new_key_id=new_key.key_id
        )
        
        return new_key


class SecretManager:
    """Secret management service."""
    
    def __init__(
        self,
        settings: Settings,
        redis_client: redis.Redis,
        encryption_manager: EncryptionManager,
        use_gcp_secret_manager: bool = True
    ):
        self.settings = settings
        self.redis_client = redis_client
        self.encryption_manager = encryption_manager
        self.use_gcp_secret_manager = use_gcp_secret_manager
        
        # Initialize GCP Secret Manager client if enabled
        if self.use_gcp_secret_manager:
            try:
                self.gcp_client = SecretManagerServiceClient()
                self.project_id = settings.gcp_project_id
            except Exception as e:
                logger.warning("Failed to initialize GCP Secret Manager", error=str(e))
                self.use_gcp_secret_manager = False
    
    async def store_secret(
        self,
        secret_name: str,
        secret_value: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Store secret securely."""
        version_id = secrets.token_hex(8)
        
        if self.use_gcp_secret_manager:
            # Store in GCP Secret Manager
            try:
                parent = f"projects/{self.project_id}"
                
                # Create secret if it doesn't exist
                try:
                    self.gcp_client.create_secret(
                        parent=parent,
                        secret_id=secret_name,
                        secret={"replication": {"automatic": {}}}
                    )
                except Exception:
                    # Secret already exists
                    pass
                
                # Add secret version
                secret_path = f"projects/{self.project_id}/secrets/{secret_name}"
                response = self.gcp_client.add_secret_version(
                    parent=secret_path,
                    payload={"data": secret_value.encode()}
                )
                
                version_id = response.name.split("/")[-1]
                
            except Exception as e:
                logger.error("Failed to store secret in GCP", error=str(e))
                # Fall back to local storage
                await self._store_secret_local(secret_name, secret_value, version_id, metadata)
        else:
            # Store locally
            await self._store_secret_local(secret_name, secret_value, version_id, metadata)
        
        logger.info(
            "Secret stored",
            secret_name=secret_name,
            version_id=version_id
        )
        
        return version_id
    
    async def _store_secret_local(
        self,
        secret_name: str,
        secret_value: str,
        version_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Store secret locally with encryption."""
        # Encrypt secret value
        encrypted_value = await self.encryption_manager.encrypt_data(
            secret_value,
            key_type=KeyType.DATA
        )
        
        secret_version = SecretVersion(
            version_id=version_id,
            secret_name=secret_name,
            secret_value=encrypted_value,
            created_at=datetime.utcnow(),
            metadata=metadata or {}
        )
        
        # Store in Redis
        secret_key = f"secret:{secret_name}:{version_id}"
        secret_data = {
            "version_id": secret_version.version_id,
            "secret_name": secret_version.secret_name,
            "secret_value": secret_version.secret_value,
            "created_at": secret_version.created_at.isoformat(),
            "expires_at": secret_version.expires_at.isoformat() if secret_version.expires_at else None,
            "is_active": secret_version.is_active,
            "metadata": json.dumps(secret_version.metadata)
        }
        
        await self.redis_client.hset(secret_key, mapping=secret_data)
        
        # Update latest version pointer
        latest_key = f"secret_latest:{secret_name}"
        await self.redis_client.set(latest_key, version_id)
    
    async def get_secret(
        self,
        secret_name: str,
        version_id: Optional[str] = None
    ) -> Optional[str]:
        """Get secret value."""
        if not version_id:
            # Get latest version
            latest_key = f"secret_latest:{secret_name}"
            version_id = await self.redis_client.get(latest_key)
            
            if not version_id:
                return None
        
        if self.use_gcp_secret_manager:
            # Try GCP Secret Manager first
            try:
                secret_path = f"projects/{self.project_id}/secrets/{secret_name}/versions/{version_id}"
                response = self.gcp_client.access_secret_version(name=secret_path)
                return response.payload.data.decode()
            except Exception as e:
                logger.warning("Failed to get secret from GCP", error=str(e))
                # Fall back to local storage
                pass
        
        # Get from local storage
        secret_key = f"secret:{secret_name}:{version_id}"
        secret_data = await self.redis_client.hgetall(secret_key)
        
        if not secret_data:
            return None
        
        # Decrypt secret value
        encrypted_value = secret_data["secret_value"]
        secret_value = await self.encryption_manager.decrypt_data(
            encrypted_value,
            key_type=KeyType.DATA
        )
        
        return secret_value
    
    async def delete_secret(
        self,
        secret_name: str,
        version_id: Optional[str] = None
    ) -> None:
        """Delete secret."""
        if self.use_gcp_secret_manager:
            try:
                if version_id:
                    secret_path = f"projects/{self.project_id}/secrets/{secret_name}/versions/{version_id}"
                    self.gcp_client.destroy_secret_version(name=secret_path)
                else:
                    secret_path = f"projects/{self.project_id}/secrets/{secret_name}"
                    self.gcp_client.delete_secret(name=secret_path)
            except Exception as e:
                logger.error("Failed to delete secret from GCP", error=str(e))
        
        # Delete from local storage
        if version_id:
            secret_key = f"secret:{secret_name}:{version_id}"
            await self.redis_client.delete(secret_key)
        else:
            # Delete all versions
            pattern = f"secret:{secret_name}:*"
            keys = await self.redis_client.keys(pattern)
            if keys:
                await self.redis_client.delete(*keys)
            
            # Delete latest version pointer
            latest_key = f"secret_latest:{secret_name}"
            await self.redis_client.delete(latest_key)
        
        logger.info(
            "Secret deleted",
            secret_name=secret_name,
            version_id=version_id
        )


class KeyManager:
    """API key management service."""
    
    def __init__(
        self,
        redis_client: redis.Redis,
        encryption_manager: EncryptionManager
    ):
        self.redis_client = redis_client
        self.encryption_manager = encryption_manager
    
    async def generate_api_key(
        self,
        user_id: str,
        name: str,
        permissions: List[str],
        expires_at: Optional[datetime] = None
    ) -> str:
        """Generate API key."""
        # Generate key
        key_id = secrets.token_hex(16)
        api_key = f"pm_{key_id}"
        
        # Hash key for storage
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        # Store key metadata
        key_data = {
            "key_id": key_id,
            "user_id": user_id,
            "name": name,
            "permissions": json.dumps(permissions),
            "created_at": datetime.utcnow().isoformat(),
            "expires_at": expires_at.isoformat() if expires_at else None,
            "is_active": True,
            "last_used": None
        }
        
        key_storage_key = f"api_key:{key_hash}"
        await self.redis_client.hset(key_storage_key, mapping=key_data)
        
        # Set expiration if specified
        if expires_at:
            ttl = int((expires_at - datetime.utcnow()).total_seconds())
            await self.redis_client.expire(key_storage_key, ttl)
        
        # Track user keys
        user_keys_key = f"user_api_keys:{user_id}"
        await self.redis_client.sadd(user_keys_key, key_hash)
        
        logger.info(
            "API key generated",
            key_id=key_id,
            user_id=user_id,
            name=name,
            permissions=permissions
        )
        
        return api_key
    
    async def validate_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """Validate API key."""
        # Hash key
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        # Get key data
        key_storage_key = f"api_key:{key_hash}"
        key_data = await self.redis_client.hgetall(key_storage_key)
        
        if not key_data:
            return None
        
        # Check if key is active
        if key_data.get("is_active") != "True":
            return None
        
        # Check expiration
        if key_data.get("expires_at"):
            expires_at = datetime.fromisoformat(key_data["expires_at"])
            if expires_at < datetime.utcnow():
                return None
        
        # Update last used timestamp
        await self.redis_client.hset(
            key_storage_key,
            "last_used",
            datetime.utcnow().isoformat()
        )
        
        return {
            "key_id": key_data["key_id"],
            "user_id": key_data["user_id"],
            "name": key_data["name"],
            "permissions": json.loads(key_data["permissions"]),
            "created_at": key_data["created_at"],
            "expires_at": key_data.get("expires_at"),
            "last_used": key_data.get("last_used")
        }
    
    async def revoke_api_key(self, api_key: str) -> None:
        """Revoke API key."""
        # Hash key
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        # Get key data
        key_storage_key = f"api_key:{key_hash}"
        key_data = await self.redis_client.hgetall(key_storage_key)
        
        if key_data:
            # Remove from user keys
            user_keys_key = f"user_api_keys:{key_data['user_id']}"
            await self.redis_client.srem(user_keys_key, key_hash)
            
            # Delete key
            await self.redis_client.delete(key_storage_key)
            
            logger.info(
                "API key revoked",
                key_id=key_data["key_id"],
                user_id=key_data["user_id"]
            )
    
    async def list_user_api_keys(self, user_id: str) -> List[Dict[str, Any]]:
        """List API keys for a user."""
        user_keys_key = f"user_api_keys:{user_id}"
        key_hashes = await self.redis_client.smembers(user_keys_key)
        
        keys = []
        for key_hash in key_hashes:
            key_storage_key = f"api_key:{key_hash}"
            key_data = await self.redis_client.hgetall(key_storage_key)
            
            if key_data:
                keys.append({
                    "key_id": key_data["key_id"],
                    "name": key_data["name"],
                    "permissions": json.loads(key_data["permissions"]),
                    "created_at": key_data["created_at"],
                    "expires_at": key_data.get("expires_at"),
                    "last_used": key_data.get("last_used"),
                    "is_active": key_data.get("is_active") == "True"
                })
        
        return keys


class CertificateManager:
    """Certificate management service."""
    
    def __init__(
        self,
        redis_client: redis.Redis,
        encryption_manager: EncryptionManager
    ):
        self.redis_client = redis_client
        self.encryption_manager = encryption_manager
    
    async def store_certificate(
        self,
        name: str,
        certificate_pem: str,
        private_key_pem: str,
        chain_pem: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Store certificate."""
        cert_id = secrets.token_hex(16)
        
        # Parse certificate to get expiration
        cert = load_pem_x509_certificate(certificate_pem.encode())
        expires_at = cert.not_valid_after
        
        # Encrypt private key
        encrypted_private_key = await self.encryption_manager.encrypt_data(
            private_key_pem,
            key_type=KeyType.DATA
        )
        
        certificate = Certificate(
            cert_id=cert_id,
            name=name,
            certificate_pem=certificate_pem,
            private_key_pem=encrypted_private_key,
            chain_pem=chain_pem,
            expires_at=expires_at,
            metadata=metadata or {}
        )
        
        # Store in Redis
        cert_key = f"certificate:{cert_id}"
        cert_data = {
            "cert_id": certificate.cert_id,
            "name": certificate.name,
            "certificate_pem": certificate.certificate_pem,
            "private_key_pem": certificate.private_key_pem,
            "chain_pem": certificate.chain_pem or "",
            "created_at": certificate.created_at.isoformat(),
            "expires_at": certificate.expires_at.isoformat() if certificate.expires_at else None,
            "is_active": certificate.is_active,
            "metadata": json.dumps(certificate.metadata)
        }
        
        await self.redis_client.hset(cert_key, mapping=cert_data)
        
        # Index by name
        name_key = f"certificate_name:{name}"
        await self.redis_client.set(name_key, cert_id)
        
        logger.info(
            "Certificate stored",
            cert_id=cert_id,
            name=name,
            expires_at=expires_at.isoformat() if expires_at else None
        )
        
        return cert_id
    
    async def get_certificate(self, cert_id: str) -> Optional[Certificate]:
        """Get certificate by ID."""
        cert_key = f"certificate:{cert_id}"
        cert_data = await self.redis_client.hgetall(cert_key)
        
        if not cert_data:
            return None
        
        # Decrypt private key
        decrypted_private_key = await self.encryption_manager.decrypt_data(
            cert_data["private_key_pem"],
            key_type=KeyType.DATA
        )
        
        return Certificate(
            cert_id=cert_data["cert_id"],
            name=cert_data["name"],
            certificate_pem=cert_data["certificate_pem"],
            private_key_pem=decrypted_private_key,
            chain_pem=cert_data["chain_pem"] if cert_data["chain_pem"] else None,
            created_at=datetime.fromisoformat(cert_data["created_at"]),
            expires_at=datetime.fromisoformat(cert_data["expires_at"]) if cert_data.get("expires_at") else None,
            is_active=cert_data["is_active"] == "True",
            metadata=json.loads(cert_data["metadata"])
        )
    
    async def get_certificate_by_name(self, name: str) -> Optional[Certificate]:
        """Get certificate by name."""
        name_key = f"certificate_name:{name}"
        cert_id = await self.redis_client.get(name_key)
        
        if not cert_id:
            return None
        
        return await self.get_certificate(cert_id)
    
    async def list_certificates(self) -> List[Dict[str, Any]]:
        """List all certificates."""
        pattern = "certificate:*"
        keys = await self.redis_client.keys(pattern)
        
        certificates = []
        for key in keys:
            if key.startswith("certificate_name:"):
                continue
            
            cert_data = await self.redis_client.hgetall(key)
            if cert_data:
                certificates.append({
                    "cert_id": cert_data["cert_id"],
                    "name": cert_data["name"],
                    "created_at": cert_data["created_at"],
                    "expires_at": cert_data.get("expires_at"),
                    "is_active": cert_data["is_active"] == "True"
                })
        
        return certificates
    
    async def delete_certificate(self, cert_id: str) -> None:
        """Delete certificate."""
        # Get certificate data
        cert_key = f"certificate:{cert_id}"
        cert_data = await self.redis_client.hgetall(cert_key)
        
        if cert_data:
            # Remove name index
            name_key = f"certificate_name:{cert_data['name']}"
            await self.redis_client.delete(name_key)
            
            # Remove certificate
            await self.redis_client.delete(cert_key)
            
            logger.info(
                "Certificate deleted",
                cert_id=cert_id,
                name=cert_data["name"]
            )
    
    async def check_certificate_expiry(self, days_ahead: int = 30) -> List[Dict[str, Any]]:
        """Check for certificates expiring soon."""
        cutoff_date = datetime.utcnow() + timedelta(days=days_ahead)
        
        pattern = "certificate:*"
        keys = await self.redis_client.keys(pattern)
        
        expiring_certificates = []
        for key in keys:
            if key.startswith("certificate_name:"):
                continue
            
            cert_data = await self.redis_client.hgetall(key)
            if cert_data and cert_data.get("expires_at"):
                expires_at = datetime.fromisoformat(cert_data["expires_at"])
                
                if expires_at < cutoff_date:
                    expiring_certificates.append({
                        "cert_id": cert_data["cert_id"],
                        "name": cert_data["name"],
                        "expires_at": cert_data["expires_at"],
                        "days_until_expiry": (expires_at - datetime.utcnow()).days
                    })
        
        return expiring_certificates