"""
Authentication and Authorization Module

Provides authentication utilities for the configuration management API.
In production, this should integrate with your actual authentication system.
"""

import jwt
from typing import Optional
from datetime import datetime, timedelta
import structlog

from ..config.access_control import ConfigRole
from ..config.settings import get_settings

logger = structlog.get_logger()


async def get_current_user_role(token: str) -> ConfigRole:
    """
    Get user role from JWT token.
    
    In production, this should decode the actual JWT and extract user roles.
    For development, we'll return a default role based on token pattern.
    """
    try:
        settings = get_settings()
        
        # In development mode, use simple token-to-role mapping
        if settings.is_development:
            # Simple development mapping
            if token.startswith("admin-"):
                return ConfigRole.ADMIN
            elif token.startswith("security-"):
                return ConfigRole.SECURITY_ADMIN
            elif token.startswith("operator-"):
                return ConfigRole.OPERATOR
            elif token.startswith("developer-"):
                return ConfigRole.DEVELOPER
            elif token.startswith("monitor-"):
                return ConfigRole.MONITOR
            elif token.startswith("service-"):
                return ConfigRole.SERVICE
            else:
                return ConfigRole.READONLY
        
        # Production JWT decoding
        try:
            payload = jwt.decode(
                token,
                settings.jwt_secret,
                algorithms=["HS256"]
            )
            
            # Extract role from JWT claims
            role_claim = payload.get("role", "readonly")
            return ConfigRole(role_claim)
            
        except jwt.InvalidTokenError as e:
            logger.warning("Invalid JWT token", error=str(e))
            return ConfigRole.READONLY
            
    except Exception as e:
        logger.error("Failed to determine user role", error=str(e))
        return ConfigRole.READONLY


async def verify_session(token: str) -> Optional[str]:
    """
    Verify user session and return session ID.
    
    In production, this should validate the session with your session store.
    """
    try:
        settings = get_settings()
        
        # In development mode, generate a simple session ID
        if settings.is_development:
            import hashlib
            session_id = hashlib.md5(f"{token}:{datetime.utcnow().date()}".encode()).hexdigest()
            return f"dev-session-{session_id[:8]}"
        
        # Production session validation
        try:
            payload = jwt.decode(
                token,
                settings.jwt_secret,
                algorithms=["HS256"]
            )
            
            session_id = payload.get("session_id")
            if not session_id:
                return None
            
            # Validate session is still active
            # This would typically check against Redis or your session store
            return session_id
            
        except jwt.InvalidTokenError:
            return None
            
    except Exception as e:
        logger.error("Session verification failed", error=str(e))
        return None


def generate_dev_token(user_id: str, role: ConfigRole) -> str:
    """
    Generate a development JWT token.
    Only for development/testing purposes.
    """
    settings = get_settings()
    
    if not settings.is_development:
        raise ValueError("Dev token generation only allowed in development mode")
    
    payload = {
        "user_id": user_id,
        "role": role.value,
        "session_id": f"dev-session-{user_id}",
        "exp": datetime.utcnow() + timedelta(hours=24),
        "iat": datetime.utcnow()
    }
    
    return jwt.encode(payload, settings.jwt_secret, algorithm="HS256")


# Development utility functions
def get_dev_tokens() -> dict[str, str]:
    """Get development tokens for testing different roles."""
    settings = get_settings()
    
    if not settings.is_development:
        return {}
    
    tokens = {}
    roles = [
        (ConfigRole.ADMIN, "admin-user"),
        (ConfigRole.SECURITY_ADMIN, "security-user"),
        (ConfigRole.OPERATOR, "operator-user"),
        (ConfigRole.DEVELOPER, "developer-user"),
        (ConfigRole.MONITOR, "monitor-user"),
        (ConfigRole.SERVICE, "service-user"),
        (ConfigRole.READONLY, "readonly-user"),
    ]
    
    for role, user_id in roles:
        tokens[role.value] = generate_dev_token(user_id, role)
    
    return tokens