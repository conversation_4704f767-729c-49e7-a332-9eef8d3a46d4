"""
Advanced Rate Limiting System for Pattern Mining Service

Provides comprehensive rate limiting including:
- Token bucket algorithm
- Sliding window rate limiting
- Per-user and per-IP rate limiting
- API-specific rate limits
- Burst protection
- Distributed rate limiting
"""

import time
import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
import redis.asyncio as redis
import structlog
from fastapi import Request, HTTPException
from ..config.settings import Settings

logger = structlog.get_logger()


class RateLimitError(Exception):
    """Rate limit exceeded error."""
    pass


class BurstError(Exception):
    """Burst limit exceeded error."""
    pass


class RateLimitType(str, Enum):
    """Rate limit types."""
    USER = "user"
    IP = "ip"
    API_KEY = "api_key"
    ENDPOINT = "endpoint"
    GLOBAL = "global"


@dataclass
class RateLimitConfig:
    """Rate limit configuration."""
    limit: int
    window_seconds: int
    burst_limit: Optional[int] = None
    burst_window_seconds: Optional[int] = None
    enabled: bool = True


@dataclass
class RateLimitResult:
    """Rate limit check result."""
    allowed: bool
    remaining: int
    reset_time: int
    limit: int
    window_seconds: int
    retry_after: Optional[int] = None


class RateLimiter(ABC):
    """Abstract rate limiter."""
    
    @abstractmethod
    async def check_rate_limit(
        self,
        key: str,
        limit: int,
        window_seconds: int
    ) -> RateLimitResult:
        """Check rate limit for a key."""
        pass
    
    @abstractmethod
    async def reset_rate_limit(self, key: str) -> None:
        """Reset rate limit for a key."""
        pass


class TokenBucketRateLimiter(RateLimiter):
    """Token bucket rate limiter implementation."""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis_client = redis_client
    
    async def check_rate_limit(
        self,
        key: str,
        limit: int,
        window_seconds: int
    ) -> RateLimitResult:
        """Check rate limit using token bucket algorithm."""
        now = time.time()
        bucket_key = f"rate_limit:token_bucket:{key}"
        
        # Token bucket parameters
        refill_rate = limit / window_seconds  # tokens per second
        
        try:
            # Get current bucket state
            bucket_data = await self.redis_client.hgetall(bucket_key)
            
            if bucket_data:
                last_refill = float(bucket_data.get("last_refill", now))
                tokens = float(bucket_data.get("tokens", limit))
            else:
                last_refill = now
                tokens = limit
            
            # Calculate tokens to add
            time_passed = now - last_refill
            tokens_to_add = time_passed * refill_rate
            tokens = min(limit, tokens + tokens_to_add)
            
            # Check if request can be processed
            if tokens >= 1:
                tokens -= 1
                allowed = True
                remaining = int(tokens)
                
                # Update bucket
                await self.redis_client.hset(bucket_key, mapping={
                    "tokens": tokens,
                    "last_refill": now
                })
                await self.redis_client.expire(bucket_key, window_seconds * 2)
                
            else:
                allowed = False
                remaining = 0
            
            # Calculate reset time
            if tokens < limit:
                time_to_refill = (1 - tokens) / refill_rate
                reset_time = int(time_to_refill)
            else:
                reset_time = 0
            
            return RateLimitResult(
                allowed=allowed,
                remaining=remaining,
                reset_time=reset_time,
                limit=limit,
                window_seconds=window_seconds,
                retry_after=reset_time if not allowed else None
            )
            
        except Exception as e:
            logger.error("Token bucket rate limit check failed", error=str(e), key=key)
            # Allow request on error
            return RateLimitResult(
                allowed=True,
                remaining=limit,
                reset_time=0,
                limit=limit,
                window_seconds=window_seconds
            )
    
    async def reset_rate_limit(self, key: str) -> None:
        """Reset token bucket for a key."""
        bucket_key = f"rate_limit:token_bucket:{key}"
        await self.redis_client.delete(bucket_key)


class SlidingWindowRateLimiter(RateLimiter):
    """Sliding window rate limiter implementation."""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis_client = redis_client
    
    async def check_rate_limit(
        self,
        key: str,
        limit: int,
        window_seconds: int
    ) -> RateLimitResult:
        """Check rate limit using sliding window algorithm."""
        now = time.time()
        window_start = now - window_seconds
        window_key = f"rate_limit:sliding_window:{key}"
        
        try:
            # Use Redis pipeline for atomic operations
            async with self.redis_client.pipeline(transaction=True) as pipe:
                # Remove old entries
                pipe.zremrangebyscore(window_key, 0, window_start)
                
                # Count current requests
                pipe.zcard(window_key)
                
                # Execute pipeline
                results = await pipe.execute()
                current_count = results[1]
                
                # Check if under limit
                if current_count >= limit:
                    remaining = 0
                    allowed = False
                    
                    # Calculate reset time (time until oldest entry expires)
                    oldest_entries = await self.redis_client.zrange(
                        window_key, 0, 0, withscores=True
                    )
                    if oldest_entries:
                        oldest_time = oldest_entries[0][1]
                        reset_time = int((oldest_time + window_seconds) - now)
                    else:
                        reset_time = window_seconds
                    
                else:
                    # Add current request
                    request_id = f"{now}:{id(object())}"
                    await self.redis_client.zadd(window_key, {request_id: now})
                    await self.redis_client.expire(window_key, window_seconds)
                    
                    remaining = limit - current_count - 1
                    allowed = True
                    reset_time = window_seconds
                
                return RateLimitResult(
                    allowed=allowed,
                    remaining=remaining,
                    reset_time=reset_time,
                    limit=limit,
                    window_seconds=window_seconds,
                    retry_after=reset_time if not allowed else None
                )
                
        except Exception as e:
            logger.error("Sliding window rate limit check failed", error=str(e), key=key)
            # Allow request on error
            return RateLimitResult(
                allowed=True,
                remaining=limit,
                reset_time=0,
                limit=limit,
                window_seconds=window_seconds
            )
    
    async def reset_rate_limit(self, key: str) -> None:
        """Reset sliding window for a key."""
        window_key = f"rate_limit:sliding_window:{key}"
        await self.redis_client.delete(window_key)


class FixedWindowRateLimiter(RateLimiter):
    """Fixed window rate limiter implementation."""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis_client = redis_client
    
    async def check_rate_limit(
        self,
        key: str,
        limit: int,
        window_seconds: int
    ) -> RateLimitResult:
        """Check rate limit using fixed window algorithm."""
        now = time.time()
        window_start = int(now // window_seconds) * window_seconds
        window_key = f"rate_limit:fixed_window:{key}:{window_start}"
        
        try:
            # Increment counter
            current_count = await self.redis_client.incr(window_key)
            
            # Set expiration on first increment
            if current_count == 1:
                await self.redis_client.expire(window_key, window_seconds)
            
            # Check if under limit
            if current_count <= limit:
                allowed = True
                remaining = limit - current_count
            else:
                allowed = False
                remaining = 0
            
            # Calculate reset time
            reset_time = int(window_start + window_seconds - now)
            
            return RateLimitResult(
                allowed=allowed,
                remaining=remaining,
                reset_time=reset_time,
                limit=limit,
                window_seconds=window_seconds,
                retry_after=reset_time if not allowed else None
            )
            
        except Exception as e:
            logger.error("Fixed window rate limit check failed", error=str(e), key=key)
            # Allow request on error
            return RateLimitResult(
                allowed=True,
                remaining=limit,
                reset_time=0,
                limit=limit,
                window_seconds=window_seconds
            )
    
    async def reset_rate_limit(self, key: str) -> None:
        """Reset fixed window for a key."""
        now = time.time()
        window_start = int(now // 60) * 60  # Assume 60 second window
        window_key = f"rate_limit:fixed_window:{key}:{window_start}"
        await self.redis_client.delete(window_key)


class RateLimitManager:
    """Comprehensive rate limit manager."""
    
    def __init__(
        self,
        redis_client: redis.Redis,
        settings: Settings,
        default_algorithm: str = "sliding_window"
    ):
        self.redis_client = redis_client
        self.settings = settings
        
        # Initialize rate limiters
        self.limiters = {
            "token_bucket": TokenBucketRateLimiter(redis_client),
            "sliding_window": SlidingWindowRateLimiter(redis_client),
            "fixed_window": FixedWindowRateLimiter(redis_client)
        }
        
        self.default_algorithm = default_algorithm
        
        # Rate limit configurations
        self.rate_limits = {
            "global": RateLimitConfig(
                limit=settings.api_rate_limit,
                window_seconds=60,
                burst_limit=settings.rate_limit_burst,
                burst_window_seconds=1
            ),
            "user": RateLimitConfig(
                limit=settings.rate_limit_per_minute,
                window_seconds=60,
                burst_limit=settings.rate_limit_burst,
                burst_window_seconds=1
            ),
            "ip": RateLimitConfig(
                limit=settings.rate_limit_per_minute * 2,
                window_seconds=60,
                burst_limit=settings.rate_limit_burst * 2,
                burst_window_seconds=1
            ),
            "api_key": RateLimitConfig(
                limit=settings.rate_limit_per_minute * 5,
                window_seconds=60,
                burst_limit=settings.rate_limit_burst * 3,
                burst_window_seconds=1
            )
        }
        
        # Endpoint-specific rate limits
        self.endpoint_limits = {
            "/api/v1/patterns/detect": RateLimitConfig(limit=50, window_seconds=60),
            "/api/v1/patterns/batch": RateLimitConfig(limit=10, window_seconds=60),
            "/api/v1/ml/models": RateLimitConfig(limit=200, window_seconds=60),
            "/api/v1/ml/train": RateLimitConfig(limit=5, window_seconds=3600),
            "/api/v1/analysis/execute": RateLimitConfig(limit=30, window_seconds=60),
            "/api/v2/patterns/stream": RateLimitConfig(limit=30, window_seconds=60),
        }
    
    async def check_rate_limit(
        self,
        key: str,
        limit_type: RateLimitType,
        endpoint: Optional[str] = None,
        algorithm: Optional[str] = None
    ) -> RateLimitResult:
        """Check rate limit for a key."""
        # Get configuration
        config = self._get_rate_limit_config(limit_type, endpoint)
        if not config.enabled:
            return RateLimitResult(
                allowed=True,
                remaining=config.limit,
                reset_time=0,
                limit=config.limit,
                window_seconds=config.window_seconds
            )
        
        # Get rate limiter
        limiter = self.limiters.get(algorithm or self.default_algorithm)
        if not limiter:
            raise ValueError(f"Unknown rate limiting algorithm: {algorithm}")
        
        # Check rate limit
        result = await limiter.check_rate_limit(
            key=key,
            limit=config.limit,
            window_seconds=config.window_seconds
        )
        
        # Log if rate limit exceeded
        if not result.allowed:
            logger.warning(
                "Rate limit exceeded",
                key=key,
                limit_type=limit_type.value,
                endpoint=endpoint,
                limit=config.limit,
                window_seconds=config.window_seconds
            )
        
        return result
    
    async def check_burst_limit(
        self,
        key: str,
        limit_type: RateLimitType,
        endpoint: Optional[str] = None
    ) -> RateLimitResult:
        """Check burst limit for a key."""
        config = self._get_rate_limit_config(limit_type, endpoint)
        
        if not config.burst_limit or not config.burst_window_seconds:
            return RateLimitResult(
                allowed=True,
                remaining=0,
                reset_time=0,
                limit=0,
                window_seconds=0
            )
        
        # Use fixed window for burst protection
        limiter = self.limiters["fixed_window"]
        
        # Create burst key
        burst_key = f"burst:{key}"
        
        result = await limiter.check_rate_limit(
            key=burst_key,
            limit=config.burst_limit,
            window_seconds=config.burst_window_seconds
        )
        
        if not result.allowed:
            logger.warning(
                "Burst limit exceeded",
                key=key,
                limit_type=limit_type.value,
                endpoint=endpoint,
                burst_limit=config.burst_limit,
                burst_window_seconds=config.burst_window_seconds
            )
        
        return result
    
    def _get_rate_limit_config(
        self,
        limit_type: RateLimitType,
        endpoint: Optional[str] = None
    ) -> RateLimitConfig:
        """Get rate limit configuration."""
        # Check endpoint-specific limits first
        if endpoint and endpoint in self.endpoint_limits:
            return self.endpoint_limits[endpoint]
        
        # Fall back to type-specific limits
        if limit_type.value in self.rate_limits:
            return self.rate_limits[limit_type.value]
        
        # Default to global limits
        return self.rate_limits["global"]
    
    async def reset_rate_limit(
        self,
        key: str,
        algorithm: Optional[str] = None
    ) -> None:
        """Reset rate limit for a key."""
        limiter = self.limiters.get(algorithm or self.default_algorithm)
        if limiter:
            await limiter.reset_rate_limit(key)
    
    async def get_rate_limit_status(
        self,
        key: str,
        limit_type: RateLimitType,
        endpoint: Optional[str] = None,
        algorithm: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get current rate limit status."""
        config = self._get_rate_limit_config(limit_type, endpoint)
        
        # Get current count (implementation depends on algorithm)
        if algorithm == "sliding_window" or self.default_algorithm == "sliding_window":
            window_key = f"rate_limit:sliding_window:{key}"
            now = time.time()
            window_start = now - config.window_seconds
            
            # Remove old entries and count current
            await self.redis_client.zremrangebyscore(window_key, 0, window_start)
            current_count = await self.redis_client.zcard(window_key)
            
        elif algorithm == "token_bucket":
            bucket_key = f"rate_limit:token_bucket:{key}"
            bucket_data = await self.redis_client.hgetall(bucket_key)
            
            if bucket_data:
                now = time.time()
                last_refill = float(bucket_data.get("last_refill", now))
                tokens = float(bucket_data.get("tokens", config.limit))
                
                # Calculate current tokens
                time_passed = now - last_refill
                refill_rate = config.limit / config.window_seconds
                tokens = min(config.limit, tokens + (time_passed * refill_rate))
                current_count = config.limit - int(tokens)
            else:
                current_count = 0
                
        else:  # fixed_window
            now = time.time()
            window_start = int(now // config.window_seconds) * config.window_seconds
            window_key = f"rate_limit:fixed_window:{key}:{window_start}"
            current_count = await self.redis_client.get(window_key) or 0
            current_count = int(current_count)
        
        return {
            "limit": config.limit,
            "window_seconds": config.window_seconds,
            "current_count": current_count,
            "remaining": max(0, config.limit - current_count),
            "reset_time": config.window_seconds
        }


class BurstProtector:
    """Burst protection service."""
    
    def __init__(
        self,
        redis_client: redis.Redis,
        burst_limit: int = 10,
        burst_window_seconds: int = 1,
        penalty_seconds: int = 60
    ):
        self.redis_client = redis_client
        self.burst_limit = burst_limit
        self.burst_window_seconds = burst_window_seconds
        self.penalty_seconds = penalty_seconds
    
    async def check_burst_protection(self, key: str) -> Tuple[bool, int]:
        """Check burst protection for a key."""
        # Check if already in penalty
        penalty_key = f"burst_penalty:{key}"
        penalty_ttl = await self.redis_client.ttl(penalty_key)
        
        if penalty_ttl > 0:
            return False, penalty_ttl
        
        # Check burst limit
        now = time.time()
        window_start = int(now // self.burst_window_seconds) * self.burst_window_seconds
        burst_key = f"burst:{key}:{window_start}"
        
        # Increment burst counter
        burst_count = await self.redis_client.incr(burst_key)
        
        # Set expiration on first increment
        if burst_count == 1:
            await self.redis_client.expire(burst_key, self.burst_window_seconds)
        
        # Check if burst limit exceeded
        if burst_count > self.burst_limit:
            # Apply penalty
            await self.redis_client.setex(penalty_key, self.penalty_seconds, "1")
            
            logger.warning(
                "Burst limit exceeded, penalty applied",
                key=key,
                burst_count=burst_count,
                burst_limit=self.burst_limit,
                penalty_seconds=self.penalty_seconds
            )
            
            return False, self.penalty_seconds
        
        return True, 0
    
    async def reset_burst_protection(self, key: str) -> None:
        """Reset burst protection for a key."""
        # Remove penalty
        penalty_key = f"burst_penalty:{key}"
        await self.redis_client.delete(penalty_key)
        
        # Remove burst counters
        pattern = f"burst:{key}:*"
        keys = await self.redis_client.keys(pattern)
        if keys:
            await self.redis_client.delete(*keys)


class AdaptiveRateLimiter:
    """Adaptive rate limiter that adjusts based on system load."""
    
    def __init__(
        self,
        redis_client: redis.Redis,
        base_rate_limiter: RateLimiter,
        adjustment_factor: float = 0.1
    ):
        self.redis_client = redis_client
        self.base_rate_limiter = base_rate_limiter
        self.adjustment_factor = adjustment_factor
    
    async def check_rate_limit(
        self,
        key: str,
        limit: int,
        window_seconds: int
    ) -> RateLimitResult:
        """Check rate limit with adaptive adjustment."""
        # Get system load metrics
        load_factor = await self._get_system_load_factor()
        
        # Adjust limit based on load
        adjusted_limit = int(limit * (1 - (load_factor * self.adjustment_factor)))
        adjusted_limit = max(1, adjusted_limit)  # Ensure at least 1 request allowed
        
        # Use base rate limiter with adjusted limit
        result = await self.base_rate_limiter.check_rate_limit(
            key=key,
            limit=adjusted_limit,
            window_seconds=window_seconds
        )
        
        # Update original limit in result
        result.limit = limit
        
        return result
    
    async def _get_system_load_factor(self) -> float:
        """Get system load factor (0.0 to 1.0)."""
        # This would typically check CPU, memory, response times, etc.
        # For now, return a simple metric based on active connections
        
        connections_key = "system:active_connections"
        active_connections = await self.redis_client.get(connections_key) or 0
        active_connections = int(active_connections)
        
        # Normalize to 0.0 - 1.0 range
        max_connections = 1000  # Configure based on your system
        load_factor = min(1.0, active_connections / max_connections)
        
        return load_factor
    
    async def reset_rate_limit(self, key: str) -> None:
        """Reset rate limit for a key."""
        await self.base_rate_limiter.reset_rate_limit(key)