"""
Detection Pipeline

Main orchestrator for pattern detection with <50ms latency target.
Coordinates feature extraction, pattern detection, and result aggregation
with comprehensive error handling and performance optimization.
"""

from typing import Dict, Any, List, Optional, Tuple, Union
import asyncio
import logging
import time
from datetime import datetime
from dataclasses import dataclass
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from ..features.text_features import TextFeatureExtractor
from ..features.ast_features import ASTFeatureExtractor
from ..detectors.ml_detector import MLPatternDetector
from ..detectors.heuristic_detector import HeuristicPatternDetector
from ..models.patterns import (
    PatternDetectionRequest, PatternDetectionResponse, DetectedPattern,
    PatternStatistics, DetectionType, PatternType, SeverityLevel
)
from ..config.ml import get_ml_config
from ..evaluation.metrics import ConfidenceScorer, PerformanceMetrics

logger = logging.getLogger(__name__)


@dataclass
class PipelineConfig:
    """Configuration for the detection pipeline."""
    max_parallel_extractors: int = 3
    feature_extraction_timeout: float = 0.03  # 30ms for feature extraction
    detection_timeout: float = 0.015  # 15ms for pattern detection
    total_timeout: float = 0.05  # 50ms total target
    enable_caching: bool = True
    cache_size_limit: int = 1000
    min_confidence_threshold: float = 0.1
    enable_ensemble: bool = True
    performance_monitoring: bool = True


class DetectionPipeline:
    """
    Main detection pipeline for coordinating pattern detection.
    
    Orchestrates feature extraction, pattern detection, and result aggregation
    with comprehensive performance optimization and error handling.
    """
    
    def __init__(self, config: Optional[PipelineConfig] = None):
        self.config = config or PipelineConfig()
        self.ml_config = get_ml_config()
        
        # Initialize extractors
        self.text_extractor = TextFeatureExtractor()
        self.ast_extractor = ASTFeatureExtractor()
        
        # Initialize detectors
        self.ml_detector = MLPatternDetector()
        self.heuristic_detector = HeuristicPatternDetector()
        
        # Initialize evaluation components
        self.confidence_scorer = ConfidenceScorer()
        self.performance_metrics = PerformanceMetrics()
        
        # Performance optimization
        self.thread_pool = ThreadPoolExecutor(max_workers=self.config.max_parallel_extractors)
        self.detection_cache = {}
        self.cache_access_times = {}
        
        # Pipeline statistics
        self.stats = {
            "total_requests": 0,
            "successful_detections": 0,
            "cache_hits": 0,
            "average_latency_ms": 0.0,
            "latency_samples": [],
            "feature_extraction_times": [],
            "detection_times": [],
            "error_count": 0
        }
        
        # Feature pipeline weights
        self.feature_weights = {
            "text": 0.3,
            "ast": 0.7
        }
        
        # Detection method weights for ensemble
        self.detection_weights = {
            DetectionType.ML_INFERENCE: 0.7,
            DetectionType.HEURISTIC: 0.3,
            DetectionType.ENSEMBLE: 1.0
        }
        
        logger.info("Detection pipeline initialized with <50ms latency target")
    
    async def detect_patterns(self, request: PatternDetectionRequest) -> PatternDetectionResponse:
        """
        Main entry point for pattern detection.
        
        Args:
            request: Pattern detection request
            
        Returns:
            PatternDetectionResponse with detected patterns and metadata
        """
        start_time = time.perf_counter()
        job_id = f"detection_{int(time.time() * 1000)}"
        
        try:
            # Update request statistics
            self.stats["total_requests"] += 1
            
            # Check cache first
            cache_key = self._generate_cache_key(request)
            if self.config.enable_caching and cache_key in self.detection_cache:
                cached_response = self._get_cached_response(cache_key, job_id)
                if cached_response:
                    self.stats["cache_hits"] += 1
                    return cached_response
            
            # Validate request
            if not await self._validate_request(request):
                return self._create_error_response(job_id, "Invalid request parameters")
            
            # Phase 1: Feature Extraction (target: 30ms)
            extraction_start = time.perf_counter()
            features = await self._extract_features_parallel(request.code, request.language)
            extraction_time = (time.perf_counter() - extraction_start) * 1000
            self.stats["feature_extraction_times"].append(extraction_time)
            
            if not features:
                return self._create_error_response(job_id, "Feature extraction failed")
            
            # Phase 2: Pattern Detection (target: 15ms)
            detection_start = time.perf_counter()
            detected_patterns = await self._detect_patterns_parallel(
                request.code, 
                request.language, 
                features, 
                request
            )
            detection_time = (time.perf_counter() - detection_start) * 1000
            self.stats["detection_times"].append(detection_time)
            
            # Phase 3: Post-processing and Aggregation (target: 5ms)
            aggregation_start = time.perf_counter()
            final_patterns = await self._aggregate_and_score_patterns(
                detected_patterns, 
                features, 
                request
            )
            aggregation_time = (time.perf_counter() - aggregation_start) * 1000
            
            # Calculate total processing time
            total_time = (time.perf_counter() - start_time) * 1000
            target_met = total_time <= self.config.total_timeout * 1000
            
            # Update performance statistics
            self._update_performance_stats(total_time, target_met)
            
            # Create response
            response = PatternDetectionResponse(
                job_id=job_id,
                patterns=final_patterns,
                summary=self._create_detection_summary(final_patterns),
                metadata={
                    "processing_time_ms": total_time,
                    "feature_extraction_ms": extraction_time,
                    "detection_ms": detection_time,
                    "aggregation_ms": aggregation_time,
                    "target_met": target_met,
                    "features_extracted": list(features.keys()),
                    "detection_methods_used": list(set(p.detection_method for p in final_patterns)),
                    "cache_hit": False,
                    "pipeline_version": "1.0.0"
                },
                processing_time=total_time / 1000,
                model_performance=self._get_model_performance_metrics(),
                quality_metrics=self._calculate_quality_metrics(final_patterns)
            )
            
            # Cache successful results
            if self.config.enable_caching and target_met and len(final_patterns) > 0:
                self._cache_response(cache_key, response)
            
            self.stats["successful_detections"] += 1
            return response
            
        except asyncio.TimeoutError:
            total_time = (time.perf_counter() - start_time) * 1000
            logger.warning(f"Detection pipeline timeout after {total_time:.2f}ms")
            self.stats["error_count"] += 1
            return self._create_error_response(job_id, f"Pipeline timeout after {total_time:.2f}ms")
            
        except Exception as e:
            total_time = (time.perf_counter() - start_time) * 1000
            logger.error(f"Detection pipeline error after {total_time:.2f}ms: {e}")
            self.stats["error_count"] += 1
            return self._create_error_response(job_id, f"Pipeline error: {str(e)}")
    
    async def _extract_features_parallel(self, code: str, language: str) -> Dict[str, Any]:
        """Extract features in parallel with timeout control."""
        try:
            # Create feature extraction tasks
            tasks = []
            
            # Text feature extraction
            if self.feature_weights.get("text", 0) > 0:
                tasks.append(("text", self.text_extractor.extract_features(code, language)))
            
            # AST feature extraction
            if self.feature_weights.get("ast", 0) > 0:
                tasks.append(("ast", self.ast_extractor.extract_features(code, language)))
            
            if not tasks:
                logger.warning("No feature extractors enabled")
                return {}
            
            # Execute with timeout
            timeout = self.config.feature_extraction_timeout
            results = await asyncio.wait_for(
                asyncio.gather(*[task for _, task in tasks], return_exceptions=True),
                timeout=timeout
            )
            
            # Process results
            features = {}
            for i, (feature_type, result) in enumerate(zip([name for name, _ in tasks], results)):
                if isinstance(result, Exception):
                    logger.warning(f"Feature extraction failed for {feature_type}: {result}")
                    continue
                
                if result and not result.get("error"):
                    features[feature_type] = result
                else:
                    logger.warning(f"Empty or error result for {feature_type} features")
            
            return features
            
        except asyncio.TimeoutError:
            logger.warning(f"Feature extraction timeout after {timeout*1000:.1f}ms")
            return {}
        except Exception as e:
            logger.error(f"Feature extraction error: {e}")
            return {}
    
    async def _detect_patterns_parallel(
        self, 
        code: str, 
        language: str, 
        features: Dict[str, Any], 
        request: PatternDetectionRequest
    ) -> List[DetectedPattern]:
        """Detect patterns using multiple methods in parallel."""
        try:
            patterns = []
            detection_tasks = []
            
            # Determine which detection methods to use
            detection_types = request.detection_types or [DetectionType.ML_INFERENCE]
            
            # ML detection
            if DetectionType.ML_INFERENCE in detection_types:
                detection_tasks.append(("ml", self.ml_detector.detect_patterns(code, language, features)))
            
            # Heuristic detection
            if DetectionType.HEURISTIC in detection_types:
                detection_tasks.append(("heuristic", self.heuristic_detector.detect_patterns(code, language, features)))
            
            if not detection_tasks:
                logger.warning("No detection methods specified")
                return []
            
            # Execute with timeout
            timeout = self.config.detection_timeout
            results = await asyncio.wait_for(
                asyncio.gather(*[task for _, task in detection_tasks], return_exceptions=True),
                timeout=timeout
            )
            
            # Process results
            for i, (method_name, result) in enumerate(zip([name for name, _ in detection_tasks], results)):
                if isinstance(result, Exception):
                    logger.warning(f"Pattern detection failed for {method_name}: {result}")
                    continue
                
                if result and isinstance(result, list):
                    # Filter patterns by confidence threshold
                    filtered_patterns = [
                        p for p in result 
                        if p.confidence_score >= max(
                            request.confidence_threshold, 
                            self.config.min_confidence_threshold
                        )
                    ]
                    patterns.extend(filtered_patterns)
                    logger.debug(f"Added {len(filtered_patterns)} patterns from {method_name}")
            
            return patterns
            
        except asyncio.TimeoutError:
            logger.warning(f"Pattern detection timeout after {timeout*1000:.1f}ms")
            return []
        except Exception as e:
            logger.error(f"Pattern detection error: {e}")
            return []
    
    async def _aggregate_and_score_patterns(
        self, 
        patterns: List[DetectedPattern], 
        features: Dict[str, Any], 
        request: PatternDetectionRequest
    ) -> List[DetectedPattern]:
        """Aggregate patterns and enhance confidence scoring."""
        if not patterns:
            return []
        
        try:
            # Remove duplicates based on pattern type and location
            unique_patterns = self._deduplicate_patterns(patterns)
            
            # Enhance confidence scores
            enhanced_patterns = []
            for pattern in unique_patterns:
                enhanced_pattern = await self._enhance_pattern_confidence(pattern, features)
                
                # Apply ensemble scoring if multiple detection methods were used
                if self.config.enable_ensemble and len(request.detection_types) > 1:
                    enhanced_pattern = self._apply_ensemble_scoring(enhanced_pattern, patterns)
                
                enhanced_patterns.append(enhanced_pattern)
            
            # Sort by severity and confidence
            enhanced_patterns.sort(
                key=lambda p: (
                    self._severity_weight(p.severity),
                    p.confidence_score
                ),
                reverse=True
            )
            
            # Apply final filters
            final_patterns = self._apply_final_filters(enhanced_patterns, request)
            
            return final_patterns
            
        except Exception as e:
            logger.error(f"Pattern aggregation error: {e}")
            return patterns  # Return original patterns if aggregation fails
    
    def _deduplicate_patterns(self, patterns: List[DetectedPattern]) -> List[DetectedPattern]:
        """Remove duplicate patterns."""
        seen = set()
        unique_patterns = []
        
        for pattern in patterns:
            # Create a signature for the pattern
            location = pattern.primary_location
            signature = (
                pattern.pattern_type,
                pattern.pattern_name,
                location.line_start if location else 0,
                location.line_end if location else 0
            )
            
            if signature not in seen:
                seen.add(signature)
                unique_patterns.append(pattern)
            else:
                # If we see a duplicate, keep the one with higher confidence
                for i, existing in enumerate(unique_patterns):
                    existing_location = existing.primary_location
                    existing_signature = (
                        existing.pattern_type,
                        existing.pattern_name,
                        existing_location.line_start if existing_location else 0,
                        existing_location.line_end if existing_location else 0
                    )
                    if existing_signature == signature:
                        if pattern.confidence_score > existing.confidence_score:
                            unique_patterns[i] = pattern
                        break
        
        return unique_patterns
    
    async def _enhance_pattern_confidence(
        self, 
        pattern: DetectedPattern, 
        features: Dict[str, Any]
    ) -> DetectedPattern:
        """Enhance pattern confidence using additional context."""
        try:
            # Use the confidence scorer to get enhanced metrics
            enhanced_confidence = await self.confidence_scorer.calculate_confidence(
                pattern, features
            )
            
            # Update the pattern's confidence metrics
            pattern.confidence_metrics = enhanced_confidence
            
            return pattern
            
        except Exception as e:
            logger.warning(f"Confidence enhancement failed: {e}")
            return pattern
    
    def _apply_ensemble_scoring(
        self, 
        pattern: DetectedPattern, 
        all_patterns: List[DetectedPattern]
    ) -> DetectedPattern:
        """Apply ensemble scoring when multiple detection methods agree."""
        # Find similar patterns from different detection methods
        similar_patterns = [
            p for p in all_patterns
            if (p.pattern_type == pattern.pattern_type and 
                p.pattern_name == pattern.pattern_name and
                p.detection_method != pattern.detection_method)
        ]
        
        if similar_patterns:
            # Weight confidence scores by detection method reliability
            total_weight = self.detection_weights.get(pattern.detection_method, 1.0)
            weighted_confidence = pattern.confidence_score * total_weight
            
            for similar in similar_patterns:
                method_weight = self.detection_weights.get(similar.detection_method, 1.0)
                weighted_confidence += similar.confidence_score * method_weight
                total_weight += method_weight
            
            # Calculate ensemble confidence
            ensemble_confidence = weighted_confidence / total_weight
            
            # Update confidence metrics
            pattern.confidence_metrics.overall_confidence = min(1.0, ensemble_confidence)
            pattern.detection_method = DetectionType.ENSEMBLE
            
            # Add ensemble metadata
            pattern.metadata["ensemble_methods"] = [p.detection_method for p in [pattern] + similar_patterns]
            pattern.metadata["ensemble_confidence"] = ensemble_confidence
        
        return pattern
    
    def _apply_final_filters(
        self, 
        patterns: List[DetectedPattern], 
        request: PatternDetectionRequest
    ) -> List[DetectedPattern]:
        """Apply final filtering based on request parameters."""
        filtered = patterns
        
        # Filter by pattern types if specified
        if request.pattern_types:
            filtered = [p for p in filtered if p.pattern_type in request.pattern_types]
        
        # Filter by confidence threshold
        filtered = [p for p in filtered if p.confidence_score >= request.confidence_threshold]
        
        # Limit results for performance (top 50 patterns)
        if len(filtered) > 50:
            filtered = filtered[:50]
            logger.info(f"Limited results to top 50 patterns from {len(patterns)} detected")
        
        return filtered
    
    def _severity_weight(self, severity: SeverityLevel) -> int:
        """Get numerical weight for severity level."""
        weights = {
            SeverityLevel.CRITICAL: 5,
            SeverityLevel.HIGH: 4,
            SeverityLevel.MEDIUM: 3,
            SeverityLevel.LOW: 2,
            SeverityLevel.INFO: 1
        }
        return weights.get(severity, 0)
    
    def _create_detection_summary(self, patterns: List[DetectedPattern]) -> Dict[str, Any]:
        """Create summary of detection results."""
        if not patterns:
            return {
                "total_patterns": 0,
                "by_severity": {},
                "by_type": {},
                "by_category": {},
                "recommendations": []
            }
        
        # Count by severity
        by_severity = {}
        for severity in SeverityLevel:
            count = sum(1 for p in patterns if p.severity == severity)
            if count > 0:
                by_severity[severity.value] = count
        
        # Count by type
        by_type = {}
        for pattern in patterns:
            type_name = pattern.pattern_type.value
            by_type[type_name] = by_type.get(type_name, 0) + 1
        
        # Count by category
        by_category = {}
        for pattern in patterns:
            category_name = pattern.pattern_category.value
            by_category[category_name] = by_category.get(category_name, 0) + 1
        
        # Top recommendations
        high_priority_patterns = [
            p for p in patterns 
            if p.severity in [SeverityLevel.CRITICAL, SeverityLevel.HIGH]
        ]
        recommendations = []
        for pattern in high_priority_patterns[:5]:  # Top 5 critical/high severity
            if pattern.recommendations:
                recommendations.append({
                    "pattern": pattern.pattern_name,
                    "recommendation": pattern.recommendations[0].title
                })
        
        return {
            "total_patterns": len(patterns),
            "by_severity": by_severity,
            "by_type": by_type,
            "by_category": by_category,
            "critical_count": sum(1 for p in patterns if p.severity == SeverityLevel.CRITICAL),
            "high_confidence_count": sum(1 for p in patterns if p.is_high_confidence),
            "recommendations": recommendations
        }
    
    def _get_model_performance_metrics(self) -> Dict[str, Any]:
        """Get model performance metrics."""
        return {
            "total_requests": self.stats["total_requests"],
            "success_rate": (
                self.stats["successful_detections"] / max(1, self.stats["total_requests"])
            ),
            "cache_hit_rate": (
                self.stats["cache_hits"] / max(1, self.stats["total_requests"])
            ),
            "average_latency_ms": self.stats["average_latency_ms"],
            "target_achievement_rate": self._calculate_target_achievement_rate(),
            "error_rate": (
                self.stats["error_count"] / max(1, self.stats["total_requests"])
            )
        }
    
    def _calculate_quality_metrics(self, patterns: List[DetectedPattern]) -> Dict[str, float]:
        """Calculate quality metrics for detected patterns."""
        if not patterns:
            return {}
        
        confidences = [p.confidence_score for p in patterns]
        
        return {
            "average_confidence": sum(confidences) / len(confidences),
            "median_confidence": sorted(confidences)[len(confidences) // 2],
            "min_confidence": min(confidences),
            "max_confidence": max(confidences),
            "confidence_std": (
                sum((c - sum(confidences) / len(confidences)) ** 2 for c in confidences) / len(confidences)
            ) ** 0.5,
            "high_confidence_ratio": sum(1 for c in confidences if c >= 0.8) / len(confidences),
            "pattern_diversity": len(set(p.pattern_type for p in patterns)) / len(patterns)
        }
    
    def _calculate_target_achievement_rate(self) -> float:
        """Calculate what percentage of requests met the latency target."""
        if not self.stats["latency_samples"]:
            return 0.0
        
        target_ms = self.config.total_timeout * 1000
        met_target = sum(1 for latency in self.stats["latency_samples"] if latency <= target_ms)
        return met_target / len(self.stats["latency_samples"])
    
    def _update_performance_stats(self, total_time: float, target_met: bool):
        """Update performance statistics."""
        self.stats["latency_samples"].append(total_time)
        
        # Keep only recent samples (last 1000)
        if len(self.stats["latency_samples"]) > 1000:
            self.stats["latency_samples"] = self.stats["latency_samples"][-1000:]
        
        # Update average latency
        self.stats["average_latency_ms"] = sum(self.stats["latency_samples"]) / len(self.stats["latency_samples"])
    
    def _generate_cache_key(self, request: PatternDetectionRequest) -> str:
        """Generate cache key for request."""
        import hashlib
        key_data = f"{request.code}:{request.language}:{request.confidence_threshold}:{sorted(request.detection_types)}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _get_cached_response(self, cache_key: str, job_id: str) -> Optional[PatternDetectionResponse]:
        """Get cached response if available."""
        if cache_key in self.detection_cache:
            cached_data = self.detection_cache[cache_key]
            cached_data["job_id"] = job_id
            cached_data["metadata"]["cache_hit"] = True
            cached_data["metadata"]["cached_at"] = datetime.utcnow().isoformat()
            
            # Update access time
            self.cache_access_times[cache_key] = time.time()
            
            return PatternDetectionResponse(**cached_data)
        return None
    
    def _cache_response(self, cache_key: str, response: PatternDetectionResponse):
        """Cache successful response."""
        if len(self.detection_cache) >= self.config.cache_size_limit:
            # Remove oldest entries (LRU)
            oldest_keys = sorted(
                self.cache_access_times.items(), 
                key=lambda x: x[1]
            )[:self.config.cache_size_limit // 4]  # Remove 25%
            
            for key, _ in oldest_keys:
                self.detection_cache.pop(key, None)
                self.cache_access_times.pop(key, None)
        
        # Store in cache
        cache_data = response.dict()
        cache_data.pop("job_id", None)  # Remove job_id as it's unique per request
        self.detection_cache[cache_key] = cache_data
        self.cache_access_times[cache_key] = time.time()
    
    def _create_error_response(self, job_id: str, error_message: str) -> PatternDetectionResponse:
        """Create error response."""
        return PatternDetectionResponse(
            job_id=job_id,
            patterns=[],
            summary={"error": error_message},
            metadata={
                "error": True,
                "error_message": error_message,
                "timestamp": datetime.utcnow().isoformat()
            }
        )
    
    async def _validate_request(self, request: PatternDetectionRequest) -> bool:
        """Validate incoming request."""
        if not request.code or not request.code.strip():
            return False
        
        if not request.language:
            return False
        
        if len(request.code) > 500000:  # 500KB limit
            logger.warning(f"Code too large: {len(request.code)} characters")
            return False
        
        if request.confidence_threshold < 0 or request.confidence_threshold > 1:
            return False
        
        return True
    
    async def get_pipeline_statistics(self) -> Dict[str, Any]:
        """Get pipeline performance statistics."""
        return {
            "requests": {
                "total": self.stats["total_requests"],
                "successful": self.stats["successful_detections"],
                "errors": self.stats["error_count"],
                "success_rate": self.stats["successful_detections"] / max(1, self.stats["total_requests"])
            },
            "performance": {
                "average_latency_ms": self.stats["average_latency_ms"],
                "target_achievement_rate": self._calculate_target_achievement_rate(),
                "cache_hit_rate": self.stats["cache_hits"] / max(1, self.stats["total_requests"])
            },
            "timing_breakdown": {
                "avg_feature_extraction_ms": (
                    sum(self.stats["feature_extraction_times"]) / 
                    max(1, len(self.stats["feature_extraction_times"]))
                ),
                "avg_detection_ms": (
                    sum(self.stats["detection_times"]) / 
                    max(1, len(self.stats["detection_times"]))
                )
            },
            "cache": {
                "size": len(self.detection_cache),
                "hit_rate": self.stats["cache_hits"] / max(1, self.stats["total_requests"]),
                "limit": self.config.cache_size_limit
            }
        }
    
    async def cleanup(self):
        """Cleanup pipeline resources."""
        try:
            # Cleanup extractors
            await self.text_extractor.cleanup()
            await self.ast_extractor.cleanup()
            
            # Cleanup detectors
            await self.ml_detector.cleanup()
            
            # Cleanup thread pool
            self.thread_pool.shutdown(wait=True)
            
            # Clear caches
            self.detection_cache.clear()
            self.cache_access_times.clear()
            
            logger.info("Detection pipeline cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Error during pipeline cleanup: {e}")


# Factory function for creating pipeline instances
def create_detection_pipeline(config: Optional[PipelineConfig] = None) -> DetectionPipeline:
    """Create a new detection pipeline instance."""
    return DetectionPipeline(config)


# Global pipeline instance for reuse
_global_pipeline = None
_pipeline_lock = threading.Lock()


def get_detection_pipeline() -> DetectionPipeline:
    """Get global detection pipeline instance (singleton)."""
    global _global_pipeline
    
    if _global_pipeline is None:
        with _pipeline_lock:
            if _global_pipeline is None:
                _global_pipeline = DetectionPipeline()
    
    return _global_pipeline