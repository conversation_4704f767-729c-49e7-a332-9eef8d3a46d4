"""
Validation Utilities

Input validation functions for the pattern mining service.
"""

import re
from typing import Any, Dict, List, Optional
import logging

logger = logging.getLogger(__name__)


def validate_code_input(code: str) -> bool:
    """
    Validate code input.
    
    Args:
        code: Source code to validate
        
    Returns:
        True if code is valid
    """
    if not code or not isinstance(code, str):
        return False
    
    # Check for minimum length
    if len(code.strip()) < 1:
        return False
    
    # Check for maximum length (10MB)
    if len(code) > 10 * 1024 * 1024:
        return False
    
    # Check for null bytes
    if '\x00' in code:
        return False
    
    return True


def validate_language(language: str) -> bool:
    """
    Validate programming language.
    
    Args:
        language: Programming language name
        
    Returns:
        True if language is supported
    """
    if not language or not isinstance(language, str):
        return False
    
    supported_languages = {
        'python', 'javascript', 'typescript', 'java', 'cpp', 'c',
        'go', 'rust', 'ruby', 'php', 'swift', 'kotlin', 'scala',
        'clojure', 'haskell', 'erlang', 'elixir', 'lua', 'perl',
        'r', 'matlab', 'sql', 'html', 'css', 'xml', 'json', 'yaml'
    }
    
    return language.lower() in supported_languages


def validate_features(features: Dict[str, Any]) -> bool:
    """
    Validate extracted features.
    
    Args:
        features: Feature dictionary
        
    Returns:
        True if features are valid
    """
    if not features or not isinstance(features, dict):
        return False
    
    # Check for required metadata
    if 'metadata' not in features:
        return False
    
    metadata = features['metadata']
    if not isinstance(metadata, dict):
        return False
    
    # Check required metadata fields
    required_fields = ['language', 'feature_types', 'extraction_time']
    for field in required_fields:
        if field not in metadata:
            return False
    
    # Validate feature types
    valid_feature_types = {'ast', 'semantic', 'text', 'ml'}
    feature_types = metadata.get('feature_types', [])
    
    if not isinstance(feature_types, list):
        return False
    
    for feature_type in feature_types:
        if feature_type not in valid_feature_types:
            return False
        
        # Check if feature data exists
        if feature_type not in features:
            return False
    
    return True


def validate_pattern_detection_request(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate pattern detection request.
    
    Args:
        request_data: Request data dictionary
        
    Returns:
        Dictionary with validation results
    """
    validation_result = {
        'valid': True,
        'errors': [],
        'warnings': []
    }
    
    # Check required fields
    required_fields = ['code', 'language']
    for field in required_fields:
        if field not in request_data:
            validation_result['valid'] = False
            validation_result['errors'].append(f"Missing required field: {field}")
    
    # Validate code
    if 'code' in request_data:
        if not validate_code_input(request_data['code']):
            validation_result['valid'] = False
            validation_result['errors'].append("Invalid code input")
    
    # Validate language
    if 'language' in request_data:
        if not validate_language(request_data['language']):
            validation_result['valid'] = False
            validation_result['errors'].append("Unsupported language")
    
    # Validate detection types
    if 'detection_types' in request_data:
        detection_types = request_data['detection_types']
        if not isinstance(detection_types, list):
            validation_result['valid'] = False
            validation_result['errors'].append("Detection types must be a list")
        else:
            valid_types = {'static_analysis', 'ml_inference', 'heuristic', 'hybrid'}
            for dt in detection_types:
                if dt not in valid_types:
                    validation_result['valid'] = False
                    validation_result['errors'].append(f"Invalid detection type: {dt}")
    
    # Validate confidence threshold
    if 'confidence_threshold' in request_data:
        threshold = request_data['confidence_threshold']
        if not isinstance(threshold, (int, float)) or threshold < 0 or threshold > 1:
            validation_result['valid'] = False
            validation_result['errors'].append("Confidence threshold must be between 0 and 1")
    
    # Validate pattern types
    if 'pattern_types' in request_data:
        pattern_types = request_data['pattern_types']
        if not isinstance(pattern_types, list):
            validation_result['valid'] = False
            validation_result['errors'].append("Pattern types must be a list")
        else:
            valid_types = {
                'anti_pattern', 'design_pattern', 'code_smell', 
                'security_issue', 'performance_issue', 'architectural_pattern'
            }
            for pt in pattern_types:
                if pt not in valid_types:
                    validation_result['valid'] = False
                    validation_result['errors'].append(f"Invalid pattern type: {pt}")
    
    return validation_result


def validate_model_training_request(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate model training request.
    
    Args:
        request_data: Request data dictionary
        
    Returns:
        Dictionary with validation results
    """
    validation_result = {
        'valid': True,
        'errors': [],
        'warnings': []
    }
    
    # Check required fields
    required_fields = ['model_id', 'training_data']
    for field in required_fields:
        if field not in request_data:
            validation_result['valid'] = False
            validation_result['errors'].append(f"Missing required field: {field}")
    
    # Validate model_id
    if 'model_id' in request_data:
        model_id = request_data['model_id']
        if not isinstance(model_id, str) or not model_id.strip():
            validation_result['valid'] = False
            validation_result['errors'].append("Model ID must be a non-empty string")
    
    # Validate training_data
    if 'training_data' in request_data:
        training_data = request_data['training_data']
        if not isinstance(training_data, dict):
            validation_result['valid'] = False
            validation_result['errors'].append("Training data must be a dictionary")
    
    # Validate hyperparameters
    if 'hyperparameters' in request_data:
        hyperparameters = request_data['hyperparameters']
        if not isinstance(hyperparameters, dict):
            validation_result['valid'] = False
            validation_result['errors'].append("Hyperparameters must be a dictionary")
    
    # Validate numeric parameters
    numeric_params = {
        'max_epochs': (1, 10000),
        'batch_size': (1, 10000),
        'learning_rate': (0.0001, 1.0)
    }
    
    for param, (min_val, max_val) in numeric_params.items():
        if param in request_data:
            value = request_data[param]
            if not isinstance(value, (int, float)) or value < min_val or value > max_val:
                validation_result['valid'] = False
                validation_result['errors'].append(f"{param} must be between {min_val} and {max_val}")
    
    return validation_result


def validate_file_path(file_path: str) -> bool:
    """
    Validate file path.
    
    Args:
        file_path: File path to validate
        
    Returns:
        True if path is valid
    """
    if not file_path or not isinstance(file_path, str):
        return False
    
    # Check for path traversal attempts
    if '..' in file_path or file_path.startswith('/'):
        return False
    
    # Check for valid characters
    if not re.match(r'^[a-zA-Z0-9_\-./]+$', file_path):
        return False
    
    # Check length
    if len(file_path) > 1000:
        return False
    
    return True


def validate_email(email: str) -> bool:
    """
    Validate email address.
    
    Args:
        email: Email address to validate
        
    Returns:
        True if email is valid
    """
    if not email or not isinstance(email, str):
        return False
    
    # Simple email regex
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def validate_uuid(uuid_string: str) -> bool:
    """
    Validate UUID string.
    
    Args:
        uuid_string: UUID string to validate
        
    Returns:
        True if UUID is valid
    """
    if not uuid_string or not isinstance(uuid_string, str):
        return False
    
    uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    return re.match(uuid_pattern, uuid_string.lower()) is not None


def sanitize_string(input_string: str, max_length: int = 1000) -> str:
    """
    Sanitize input string.
    
    Args:
        input_string: String to sanitize
        max_length: Maximum allowed length
        
    Returns:
        Sanitized string
    """
    if not input_string or not isinstance(input_string, str):
        return ""
    
    # Remove null bytes
    sanitized = input_string.replace('\x00', '')
    
    # Remove control characters except newlines and tabs
    sanitized = re.sub(r'[\x01-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', sanitized)
    
    # Limit length
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length]
    
    return sanitized


def validate_json_schema(data: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate data against JSON schema.
    
    Args:
        data: Data to validate
        schema: JSON schema
        
    Returns:
        Validation results
    """
    validation_result = {
        'valid': True,
        'errors': [],
        'warnings': []
    }
    
    try:
        # This is a simplified validation
        # In a real implementation, you'd use jsonschema library
        
        # Check required fields
        required_fields = schema.get('required', [])
        for field in required_fields:
            if field not in data:
                validation_result['valid'] = False
                validation_result['errors'].append(f"Missing required field: {field}")
        
        # Check field types
        properties = schema.get('properties', {})
        for field, field_schema in properties.items():
            if field in data:
                expected_type = field_schema.get('type')
                actual_value = data[field]
                
                if expected_type == 'string' and not isinstance(actual_value, str):
                    validation_result['valid'] = False
                    validation_result['errors'].append(f"Field {field} must be a string")
                elif expected_type == 'integer' and not isinstance(actual_value, int):
                    validation_result['valid'] = False
                    validation_result['errors'].append(f"Field {field} must be an integer")
                elif expected_type == 'number' and not isinstance(actual_value, (int, float)):
                    validation_result['valid'] = False
                    validation_result['errors'].append(f"Field {field} must be a number")
                elif expected_type == 'boolean' and not isinstance(actual_value, bool):
                    validation_result['valid'] = False
                    validation_result['errors'].append(f"Field {field} must be a boolean")
                elif expected_type == 'array' and not isinstance(actual_value, list):
                    validation_result['valid'] = False
                    validation_result['errors'].append(f"Field {field} must be an array")
                elif expected_type == 'object' and not isinstance(actual_value, dict):
                    validation_result['valid'] = False
                    validation_result['errors'].append(f"Field {field} must be an object")
    
    except Exception as e:
        validation_result['valid'] = False
        validation_result['errors'].append(f"Validation error: {str(e)}")
    
    return validation_result


def validate_pagination_params(page: int, page_size: int) -> Dict[str, Any]:
    """
    Validate pagination parameters.
    
    Args:
        page: Page number
        page_size: Number of items per page
        
    Returns:
        Validation results
    """
    validation_result = {
        'valid': True,
        'errors': [],
        'warnings': []
    }
    
    # Validate page number
    if not isinstance(page, int) or page < 1:
        validation_result['valid'] = False
        validation_result['errors'].append("Page number must be a positive integer")
    
    # Validate page size
    if not isinstance(page_size, int) or page_size < 1:
        validation_result['valid'] = False
        validation_result['errors'].append("Page size must be a positive integer")
    elif page_size > 1000:
        validation_result['valid'] = False
        validation_result['errors'].append("Page size cannot exceed 1000")
    
    return validation_result


def validate_repository_input(request: Any) -> bool:
    """
    Validate repository analysis request.
    
    Args:
        request: Repository analysis request
        
    Returns:
        bool: True if valid, False otherwise
    """
    if not hasattr(request, 'repository_url') or not request.repository_url:
        return False
    
    # Validate repository URL format
    try:
        from urllib.parse import urlparse
        parsed = urlparse(request.repository_url)
        if not parsed.scheme or not parsed.netloc:
            return False
        
        # Check for supported schemes
        if parsed.scheme not in ['http', 'https', 'git', 'ssh']:
            return False
        
        # Check for common repository hosts
        supported_hosts = [
            'github.com',
            'gitlab.com',
            'bitbucket.org',
            'git.sr.ht',
            'codeberg.org',
            'gitea.io'
        ]
        
        # Allow localhost and IP addresses for development
        if parsed.netloc not in supported_hosts:
            if not (parsed.netloc == 'localhost' or 
                   re.match(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}', parsed.netloc)):
                logger.warning(f"Unsupported repository host: {parsed.netloc}")
                return False
    
    except Exception as e:
        logger.error(f"Error validating repository URL: {e}")
        return False
    
    # Validate branch name
    if hasattr(request, 'branch') and request.branch:
        if not re.match(r'^[a-zA-Z0-9._/-]+$', request.branch):
            logger.warning(f"Invalid branch name: {request.branch}")
            return False
    
    return True