"""
Text Feature Extractor

Advanced text-based feature extraction with statistical analysis and embeddings.

This module extracts comprehensive text features from code including statistical metrics,
lexical analysis, n-grams, readability metrics, and text embeddings.
"""

from typing import Dict, Any, List, Optional, Tuple, Set
import asyncio
import logging
import re
import numpy as np
from collections import Counter, defaultdict
import string
import math
from dataclasses import dataclass, field
from datetime import datetime
import hashlib
import time
import pickle
from concurrent.futures import ThreadPoolExecutor
import threading

# Performance optimizations
try:
    import numba
    from numba import jit, njit
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    # Fallback decorators
    def jit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    njit = jit

# Advanced text analysis libraries
try:
    from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logging.warning("scikit-learn not available, falling back to basic text analysis")

try:
    import scipy.stats as stats
    from scipy.spatial.distance import hamming, jaccard
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    logging.warning("scipy not available, falling back to basic statistics")

from ..config.ml import get_ml_config

logger = logging.getLogger(__name__)


@dataclass
class TextFeatures:
    """Enhanced text feature set for production pattern detection."""
    statistics: Dict[str, Any] = field(default_factory=dict)  # Basic text statistics
    lexical: Dict[str, Any] = field(default_factory=dict)  # Lexical analysis features
    ngrams: Dict[str, Any] = field(default_factory=dict)  # N-gram features
    readability: Dict[str, Any] = field(default_factory=dict)  # Readability metrics
    style: Dict[str, Any] = field(default_factory=dict)  # Code style features
    comments: Dict[str, Any] = field(default_factory=dict)  # Comment analysis
    strings: Dict[str, Any] = field(default_factory=dict)  # String analysis
    keywords: Dict[str, Any] = field(default_factory=dict)  # Keyword analysis
    statistical: Dict[str, Any] = field(default_factory=dict)  # Statistical analysis
    embeddings: Dict[str, np.ndarray] = field(default_factory=dict)  # Text embeddings
    metadata: Dict[str, Any] = field(default_factory=dict)  # Additional metadata
    
    # Enhanced production features
    performance_metrics: Dict[str, float] = field(default_factory=dict)  # Performance tracking
    pattern_indicators: Dict[str, float] = field(default_factory=dict)  # ML pattern indicators
    code_quality_metrics: Dict[str, float] = field(default_factory=dict)  # Quality scores
    maintainability_scores: Dict[str, float] = field(default_factory=dict)  # Maintainability metrics
    security_indicators: Dict[str, int] = field(default_factory=dict)  # Security pattern counts
    ml_specific_features: Dict[str, Any] = field(default_factory=dict)  # ML-specific patterns


class TextFeatureExtractor:
    """Production-ready text feature extractor with <50ms latency target."""
    
    def __init__(self):
        self.config = get_ml_config()
        
        # Performance optimization settings
        self.max_workers = min(4, (threading.active_count() or 1) + 4)
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        self.cache_size_limit = 10000
        self.latency_target_ms = 50
        
        # Enhanced stop words for code analysis
        self.stop_words = set([
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
            'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does',
            'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that',
            'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us',
            'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their', 'get', 'set', 'var', 'let',
            'const', 'function', 'method', 'class', 'object', 'array', 'list', 'dict', 'string'
        ])
        
        # Enhanced caching with LRU-like behavior
        self.embedding_cache = {}
        self.feature_cache = {}
        self.cache_access_times = {}
        
        # Load enhanced patterns and configurations
        self.statistical_patterns = self._load_statistical_patterns()
        self.language_keywords = self._load_language_keywords()
        self.pattern_vocabularies = self._load_pattern_vocabularies()
        self.ml_pattern_indicators = self._load_ml_pattern_indicators()
        self.security_patterns = self._load_security_patterns()
        
        # ML models for advanced features
        self._embedding_model = None
        self._tfidf_vectorizer = None
        self._pattern_classifier_cache = {}
        
        # Compiled regex patterns for performance
        self._compile_regex_patterns()
        
        # Performance metrics tracking
        self.performance_stats = {
            "total_extractions": 0,
            "cache_hits": 0,
            "avg_latency_ms": 0.0,
            "latency_samples": [],
            "feature_extraction_times": defaultdict(list)
        }
    
    def _load_statistical_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Load enhanced statistical patterns for production analysis."""
        return {
            "entropy_thresholds": {
                "very_low": 1.0,
                "low": 2.5,
                "medium": 4.5,
                "high": 6.5,
                "very_high": 8.0
            },
            "complexity_indicators": {
                "nesting_keywords": ["if", "for", "while", "try", "with", "def", "class", "async", "await"],
                "branching_keywords": ["if", "elif", "else", "switch", "case", "match", "when"],
                "loop_keywords": ["for", "while", "do", "foreach", "map", "filter", "reduce"],
                "async_keywords": ["async", "await", "Promise", "Future", "Task", "Coroutine"]
            },
            "code_smells": {
                "long_lines": 120,
                "very_long_lines": 200,
                "deep_nesting": 4,
                "very_deep_nesting": 6,
                "many_parameters": 6,
                "too_many_parameters": 10,
                "long_methods": 50,
                "very_long_methods": 100,
                "god_class_methods": 20,
                "duplicate_threshold": 0.8
            },
            "performance_thresholds": {
                "latency_target_ms": 50,
                "cache_hit_ratio_target": 0.7,
                "memory_usage_mb_limit": 100,
                "max_code_length": 100000
            },
            "quality_metrics": {
                "min_comment_ratio": 0.1,
                "max_comment_ratio": 0.5,
                "ideal_line_length": 80,
                "max_cognitive_complexity": 15,
                "min_test_coverage": 0.8
            }
        }
    
    def _load_language_keywords(self) -> Dict[str, Set[str]]:
        """Load language-specific keywords."""
        return {
            "python": {
                'and', 'as', 'assert', 'break', 'class', 'continue', 'def', 'del', 'elif',
                'else', 'except', 'exec', 'finally', 'for', 'from', 'global', 'if', 'import',
                'in', 'is', 'lambda', 'not', 'or', 'pass', 'print', 'raise', 'return', 'try',
                'while', 'with', 'yield', 'True', 'False', 'None', 'async', 'await', 'nonlocal'
            },
            "javascript": {
                'break', 'case', 'catch', 'class', 'const', 'continue', 'debugger', 'default',
                'delete', 'do', 'else', 'export', 'extends', 'finally', 'for', 'function',
                'if', 'import', 'in', 'instanceof', 'let', 'new', 'return', 'super', 'switch',
                'this', 'throw', 'try', 'typeof', 'var', 'void', 'while', 'with', 'yield',
                'true', 'false', 'null', 'undefined', 'async', 'await'
            },
            "java": {
                'abstract', 'assert', 'boolean', 'break', 'byte', 'case', 'catch', 'char',
                'class', 'const', 'continue', 'default', 'do', 'double', 'else', 'enum',
                'extends', 'final', 'finally', 'float', 'for', 'goto', 'if', 'implements',
                'import', 'instanceof', 'int', 'interface', 'long', 'native', 'new', 'package',
                'private', 'protected', 'public', 'return', 'short', 'static', 'strictfp',
                'super', 'switch', 'synchronized', 'this', 'throw', 'throws', 'transient',
                'try', 'void', 'volatile', 'while', 'true', 'false', 'null'
            }
        }
    
    async def extract_features(self, code: str, language: str) -> TextFeatures:
        """Extract comprehensive text-based features from code."""
        try:
            # Extract all feature types
            statistics = await self._extract_text_statistics(code)
            lexical = await self._extract_lexical_features(code)
            ngrams = await self._extract_ngram_features(code)
            readability = await self._extract_readability_metrics(code)
            style = await self._extract_style_features(code)
            comments = await self._analyze_comments(code)
            strings = await self._analyze_strings(code)
            keywords = await self._analyze_keywords(code, language)
            
            # New enhanced features
            statistical = await self._extract_statistical_features(code)
            embeddings = await self._extract_text_embeddings(code, language)
            
            # Create metadata
            metadata = {
                "language": language,
                "extraction_time": datetime.utcnow().isoformat(),
                "code_length": len(code),
                "line_count": len(code.split('\n')),
                "feature_version": "2.0",
                "extractor_type": "text"
            }
            
            return TextFeatures(
                statistics=statistics,
                lexical=lexical,
                ngrams=ngrams,
                readability=readability,
                style=style,
                comments=comments,
                strings=strings,
                keywords=keywords,
                statistical=statistical,
                embeddings=embeddings,
                metadata=metadata
            )
        
        except Exception as e:
            logger.error(f"Error extracting text features: {str(e)}")
            return TextFeatures(
                statistics={}, lexical={}, ngrams={}, readability={}, style={},
                comments={}, strings={}, keywords={}, statistical={}, embeddings={},
                metadata={"error": str(e), "extraction_time": datetime.utcnow().isoformat()}
            )
    
    async def _extract_text_statistics(self, code: str) -> Dict[str, Any]:
        """Extract basic text statistics."""
        try:
            lines = code.split('\n')
            words = code.split()
            
            stats = {
                "total_characters": len(code),
                "total_lines": len(lines),
                "total_words": len(words),
                "avg_line_length": sum(len(line) for line in lines) / len(lines) if lines else 0,
                "avg_word_length": sum(len(word) for word in words) / len(words) if words else 0,
                "blank_lines": sum(1 for line in lines if not line.strip()),
                "max_line_length": max(len(line) for line in lines) if lines else 0,
                "min_line_length": min(len(line) for line in lines if line.strip()) if lines else 0,
                "character_distribution": self._calculate_character_distribution(code)
            }
            
            return stats
        
        except Exception as e:
            logger.error(f"Error extracting text statistics: {str(e)}")
            return {}
    
    def _calculate_character_distribution(self, code: str) -> Dict[str, int]:
        """Calculate character distribution."""
        char_counts = Counter(code)
        
        distribution = {
            "letters": sum(count for char, count in char_counts.items() if char.isalpha()),
            "digits": sum(count for char, count in char_counts.items() if char.isdigit()),
            "punctuation": sum(count for char, count in char_counts.items() if char in string.punctuation),
            "whitespace": sum(count for char, count in char_counts.items() if char.isspace()),
            "special": sum(count for char, count in char_counts.items() 
                         if not (char.isalnum() or char in string.punctuation or char.isspace()))
        }
        
        return distribution
    
    async def _extract_lexical_features(self, code: str) -> Dict[str, Any]:
        """Extract lexical features."""
        try:
            # Tokenize code
            tokens = self._tokenize_code(code)
            
            # Calculate lexical diversity
            unique_tokens = set(tokens)
            lexical_diversity = len(unique_tokens) / len(tokens) if tokens else 0
            
            # Token frequency
            token_freq = Counter(tokens)
            
            lexical = {
                "total_tokens": len(tokens),
                "unique_tokens": len(unique_tokens),
                "lexical_diversity": lexical_diversity,
                "most_common_tokens": token_freq.most_common(10),
                "token_length_distribution": self._calculate_token_length_distribution(tokens),
                "vocabulary_richness": self._calculate_vocabulary_richness(tokens)
            }
            
            return lexical
        
        except Exception as e:
            logger.error(f"Error extracting lexical features: {str(e)}")
            return {}
    
    def _tokenize_code(self, code: str) -> List[str]:
        """Tokenize code into meaningful tokens."""
        # Simple tokenization - would be more sophisticated in real implementation
        # Remove comments and strings for tokenization
        code_cleaned = re.sub(r'#.*$', '', code, flags=re.MULTILINE)
        code_cleaned = re.sub(r'""".*?"""', '', code_cleaned, flags=re.DOTALL)
        code_cleaned = re.sub(r"'''.*?'''", '', code_cleaned, flags=re.DOTALL)
        code_cleaned = re.sub(r'".*?"', '', code_cleaned)
        code_cleaned = re.sub(r"'.*?'", '', code_cleaned)
        
        # Extract tokens
        tokens = re.findall(r'\b\w+\b', code_cleaned)
        
        # Filter out stop words and short tokens
        tokens = [token.lower() for token in tokens if len(token) > 1 and token.lower() not in self.stop_words]
        
        return tokens
    
    def _calculate_token_length_distribution(self, tokens: List[str]) -> Dict[str, int]:
        """Calculate token length distribution."""
        length_counts = Counter(len(token) for token in tokens)
        
        distribution = {
            "avg_length": sum(len(token) for token in tokens) / len(tokens) if tokens else 0,
            "max_length": max(len(token) for token in tokens) if tokens else 0,
            "min_length": min(len(token) for token in tokens) if tokens else 0,
            "length_counts": dict(length_counts)
        }
        
        return distribution
    
    def _calculate_vocabulary_richness(self, tokens: List[str]) -> Dict[str, float]:
        """Calculate vocabulary richness metrics."""
        if not tokens:
            return {"ttr": 0.0, "hapax_legomena": 0.0}
        
        token_freq = Counter(tokens)
        
        # Type-Token Ratio
        ttr = len(set(tokens)) / len(tokens)
        
        # Hapax Legomena (words that appear only once)
        hapax_count = sum(1 for count in token_freq.values() if count == 1)
        hapax_ratio = hapax_count / len(tokens)
        
        return {
            "ttr": ttr,
            "hapax_legomena": hapax_ratio
        }
    
    async def _extract_ngram_features(self, code: str) -> Dict[str, Any]:
        """Extract n-gram features."""
        try:
            tokens = self._tokenize_code(code)
            
            ngrams = {
                "unigrams": self._extract_unigrams(tokens),
                "bigrams": self._extract_bigrams(tokens),
                "trigrams": self._extract_trigrams(tokens),
                "character_ngrams": self._extract_character_ngrams(code)
            }
            
            return ngrams
        
        except Exception as e:
            logger.error(f"Error extracting n-gram features: {str(e)}")
            return {}
    
    def _extract_unigrams(self, tokens: List[str]) -> Dict[str, int]:
        """Extract unigram features."""
        return dict(Counter(tokens).most_common(20))
    
    def _extract_bigrams(self, tokens: List[str]) -> Dict[str, int]:
        """Extract bigram features."""
        bigrams = [(tokens[i], tokens[i+1]) for i in range(len(tokens)-1)]
        bigram_strings = [f"{w1} {w2}" for w1, w2 in bigrams]
        return dict(Counter(bigram_strings).most_common(20))
    
    def _extract_trigrams(self, tokens: List[str]) -> Dict[str, int]:
        """Extract trigram features."""
        trigrams = [(tokens[i], tokens[i+1], tokens[i+2]) for i in range(len(tokens)-2)]
        trigram_strings = [f"{w1} {w2} {w3}" for w1, w2, w3 in trigrams]
        return dict(Counter(trigram_strings).most_common(20))
    
    def _extract_character_ngrams(self, code: str) -> Dict[str, int]:
        """Extract character n-grams."""
        # Extract 3-character n-grams
        code_cleaned = re.sub(r'\s+', ' ', code)
        char_trigrams = [code_cleaned[i:i+3] for i in range(len(code_cleaned)-2)]
        return dict(Counter(char_trigrams).most_common(20))
    
    async def _extract_readability_metrics(self, code: str) -> Dict[str, Any]:
        """Extract code readability metrics."""
        try:
            lines = code.split('\n')
            words = code.split()
            sentences = re.split(r'[.!?]+', code)
            
            readability = {
                "avg_words_per_line": len(words) / len(lines) if lines else 0,
                "avg_sentences_per_line": len(sentences) / len(lines) if lines else 0,
                "code_complexity_index": self._calculate_code_complexity_index(code),
                "maintainability_index": self._calculate_maintainability_index(code),
                "cognitive_complexity": self._calculate_cognitive_complexity(code)
            }
            
            return readability
        
        except Exception as e:
            logger.error(f"Error extracting readability metrics: {str(e)}")
            return {}
    
    def _calculate_code_complexity_index(self, code: str) -> float:
        """Calculate a simple code complexity index."""
        # This is a simplified metric
        nesting_level = 0
        max_nesting = 0
        
        for line in code.split('\n'):
            stripped = line.strip()
            if stripped.startswith(('if', 'for', 'while', 'try', 'with')):
                nesting_level += 1
                max_nesting = max(max_nesting, nesting_level)
            elif stripped.startswith(('else', 'elif', 'except', 'finally')):
                # These don't increase nesting but indicate complexity
                pass
            elif stripped and not stripped.startswith((' ', '\t')):
                # Top-level statement, reset nesting
                nesting_level = 0
        
        return max_nesting
    
    def _calculate_maintainability_index(self, code: str) -> float:
        """Calculate a simple maintainability index."""
        lines = code.split('\n')
        code_lines = [line for line in lines if line.strip() and not line.strip().startswith('#')]
        comment_lines = [line for line in lines if line.strip().startswith('#')]
        
        if not code_lines:
            return 0.0
        
        # Simple maintainability score based on comments ratio and line length
        comment_ratio = len(comment_lines) / len(code_lines)
        avg_line_length = sum(len(line) for line in code_lines) / len(code_lines)
        
        # Lower score for longer lines, higher score for more comments
        maintainability = (comment_ratio * 50) + max(0, 100 - avg_line_length)
        return min(100, max(0, maintainability))
    
    def _calculate_cognitive_complexity(self, code: str) -> int:
        """Calculate cognitive complexity."""
        complexity = 0
        nesting_level = 0
        
        for line in code.split('\n'):
            stripped = line.strip()
            
            # Count complexity-increasing constructs
            if stripped.startswith(('if', 'elif')):
                complexity += 1 + nesting_level
            elif stripped.startswith(('for', 'while')):
                complexity += 1 + nesting_level
            elif stripped.startswith('try'):
                complexity += 1 + nesting_level
            elif stripped.startswith('except'):
                complexity += 1 + nesting_level
            elif 'and' in stripped or 'or' in stripped:
                complexity += stripped.count('and') + stripped.count('or')
            
            # Track nesting level
            if stripped.endswith(':') and stripped.startswith(('if', 'for', 'while', 'try', 'with', 'def', 'class')):
                nesting_level += 1
            elif stripped and not stripped.startswith((' ', '\t')):
                nesting_level = 0
        
        return complexity
    
    async def _extract_style_features(self, code: str) -> Dict[str, Any]:
        """Extract code style features."""
        try:
            style = {
                "indentation_style": self._detect_indentation_style(code),
                "line_ending_style": self._detect_line_ending_style(code),
                "naming_conventions": self._analyze_naming_conventions(code),
                "whitespace_usage": self._analyze_whitespace_usage(code),
                "bracket_style": self._analyze_bracket_style(code)
            }
            
            return style
        
        except Exception as e:
            logger.error(f"Error extracting style features: {str(e)}")
            return {}
    
    def _detect_indentation_style(self, code: str) -> Dict[str, Any]:
        """Detect indentation style."""
        lines = code.split('\n')
        space_indents = 0
        tab_indents = 0
        
        for line in lines:
            if line.startswith('    '):
                space_indents += 1
            elif line.startswith('\t'):
                tab_indents += 1
        
        return {
            "type": "spaces" if space_indents > tab_indents else "tabs",
            "space_count": space_indents,
            "tab_count": tab_indents
        }
    
    def _detect_line_ending_style(self, code: str) -> str:
        """Detect line ending style."""
        if '\r\n' in code:
            return "CRLF"
        elif '\n' in code:
            return "LF"
        elif '\r' in code:
            return "CR"
        else:
            return "unknown"
    
    def _analyze_naming_conventions(self, code: str) -> Dict[str, int]:
        """Analyze naming conventions."""
        # Extract identifiers
        identifiers = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', code)
        
        conventions = {
            "snake_case": 0,
            "camelCase": 0,
            "PascalCase": 0,
            "UPPER_CASE": 0
        }
        
        for identifier in identifiers:
            if identifier.islower() and '_' in identifier:
                conventions["snake_case"] += 1
            elif identifier[0].islower() and any(c.isupper() for c in identifier):
                conventions["camelCase"] += 1
            elif identifier[0].isupper() and any(c.isupper() for c in identifier[1:]):
                conventions["PascalCase"] += 1
            elif identifier.isupper():
                conventions["UPPER_CASE"] += 1
        
        return conventions
    
    def _analyze_whitespace_usage(self, code: str) -> Dict[str, int]:
        """Analyze whitespace usage."""
        return {
            "spaces": code.count(' '),
            "tabs": code.count('\t'),
            "newlines": code.count('\n'),
            "multiple_spaces": len(re.findall(r'  +', code)),
            "trailing_whitespace": len(re.findall(r' +$', code, re.MULTILINE))
        }
    
    def _analyze_bracket_style(self, code: str) -> Dict[str, int]:
        """Analyze bracket style."""
        return {
            "round_brackets": code.count('('),
            "square_brackets": code.count('['),
            "curly_brackets": code.count('{'),
            "angle_brackets": code.count('<')
        }
    
    async def _analyze_comments(self, code: str) -> Dict[str, Any]:
        """Analyze comments in the code."""
        try:
            lines = code.split('\n')
            
            comments = {
                "total_comments": 0,
                "comment_lines": [],
                "comment_density": 0.0,
                "comment_types": {
                    "single_line": 0,
                    "multi_line": 0,
                    "docstrings": 0
                },
                "avg_comment_length": 0.0
            }
            
            comment_lengths = []
            
            for i, line in enumerate(lines):
                stripped = line.strip()
                if stripped.startswith('#'):
                    comments["total_comments"] += 1
                    comments["comment_types"]["single_line"] += 1
                    comments["comment_lines"].append(i + 1)
                    comment_lengths.append(len(stripped))
            
            # Find multi-line comments and docstrings
            multi_line_comments = re.findall(r'""".*?"""', code, re.DOTALL)
            docstring_comments = re.findall(r"'''.*?'''", code, re.DOTALL)
            
            comments["comment_types"]["multi_line"] = len(multi_line_comments)
            comments["comment_types"]["docstrings"] = len(docstring_comments)
            
            # Calculate comment density
            code_lines = len([line for line in lines if line.strip() and not line.strip().startswith('#')])
            if code_lines > 0:
                comments["comment_density"] = comments["total_comments"] / code_lines
            
            # Calculate average comment length
            if comment_lengths:
                comments["avg_comment_length"] = sum(comment_lengths) / len(comment_lengths)
            
            return comments
        
        except Exception as e:
            logger.error(f"Error analyzing comments: {str(e)}")
            return {}
    
    async def _analyze_strings(self, code: str) -> Dict[str, Any]:
        """Analyze string literals in the code."""
        try:
            # Find string literals
            single_quoted = re.findall(r"'([^']*)'", code)
            double_quoted = re.findall(r'"([^"]*)"', code)
            triple_quoted = re.findall(r'"""([^"]*)"""', code, re.DOTALL)
            
            all_strings = single_quoted + double_quoted + triple_quoted
            
            strings = {
                "total_strings": len(all_strings),
                "single_quoted": len(single_quoted),
                "double_quoted": len(double_quoted),
                "triple_quoted": len(triple_quoted),
                "avg_string_length": sum(len(s) for s in all_strings) / len(all_strings) if all_strings else 0,
                "max_string_length": max(len(s) for s in all_strings) if all_strings else 0,
                "empty_strings": sum(1 for s in all_strings if not s.strip())
            }
            
            return strings
        
        except Exception as e:
            logger.error(f"Error analyzing strings: {str(e)}")
            return {}
    
    async def _analyze_keywords(self, code: str, language: str) -> Dict[str, Any]:
        """Analyze language keywords in the code."""
        try:
            # Define keywords for different languages
            python_keywords = {
                'and', 'as', 'assert', 'break', 'class', 'continue', 'def', 'del', 'elif',
                'else', 'except', 'exec', 'finally', 'for', 'from', 'global', 'if', 'import',
                'in', 'is', 'lambda', 'not', 'or', 'pass', 'print', 'raise', 'return', 'try',
                'while', 'with', 'yield', 'True', 'False', 'None'
            }
            
            # Use Python keywords for now
            keywords = python_keywords if language == 'python' else python_keywords
            
            # Count keyword occurrences
            keyword_counts = {}
            for keyword in keywords:
                pattern = r'\b' + re.escape(keyword) + r'\b'
                count = len(re.findall(pattern, code))
                if count > 0:
                    keyword_counts[keyword] = count
            
            keyword_analysis = {
                "total_keywords": sum(keyword_counts.values()),
                "unique_keywords": len(keyword_counts),
                "keyword_counts": keyword_counts,
                "most_common_keywords": sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)[:10]
            }
            
            return keyword_analysis
        
        except Exception as e:
            logger.error(f"Error analyzing keywords: {str(e)}")
            return {}
    
    async def _extract_statistical_features(self, code: str) -> Dict[str, Any]:
        """Extract advanced statistical features from code."""
        try:
            lines = code.split('\n')
            non_empty_lines = [line for line in lines if line.strip()]
            
            # Information theory metrics
            entropy = self._calculate_entropy(code)
            
            # Distribution analysis
            char_distribution = self._analyze_character_distribution(code)
            line_length_distribution = self._analyze_line_length_distribution(lines)
            
            # Complexity metrics
            halstead_metrics = self._calculate_halstead_metrics(code)
            
            # Pattern detection
            code_patterns = self._detect_code_patterns(code)
            
            # Similarity metrics
            repetition_metrics = self._calculate_repetition_metrics(lines)
            
            statistical = {
                "entropy": entropy,
                "character_distribution": char_distribution,
                "line_length_distribution": line_length_distribution,
                "halstead_metrics": halstead_metrics,
                "code_patterns": code_patterns,
                "repetition_metrics": repetition_metrics,
                "variability_metrics": self._calculate_variability_metrics(lines),
                "complexity_indicators": self._extract_complexity_indicators(code),
                "information_density": self._calculate_information_density(code)
            }
            
            return statistical
        
        except Exception as e:
            logger.error(f"Error extracting statistical features: {str(e)}")
            return {}
    
    def _calculate_entropy(self, text: str) -> Dict[str, float]:
        """Calculate information entropy of the text."""
        if not text:
            return {"entropy": 0.0, "normalized_entropy": 0.0}
        
        # Character-level entropy
        char_counts = Counter(text)
        text_length = len(text)
        char_entropy = 0.0
        
        for count in char_counts.values():
            probability = count / text_length
            if probability > 0:
                char_entropy -= probability * math.log2(probability)
        
        # Normalized entropy (0-1 scale)
        max_entropy = math.log2(len(char_counts)) if len(char_counts) > 1 else 1
        normalized_entropy = char_entropy / max_entropy if max_entropy > 0 else 0
        
        # Token-level entropy
        tokens = self._tokenize_code(text)
        if tokens:
            token_counts = Counter(tokens)
            token_entropy = 0.0
            total_tokens = len(tokens)
            
            for count in token_counts.values():
                probability = count / total_tokens
                if probability > 0:
                    token_entropy -= probability * math.log2(probability)
        else:
            token_entropy = 0.0
        
        return {
            "character_entropy": char_entropy,
            "normalized_entropy": normalized_entropy,
            "token_entropy": token_entropy,
            "entropy_classification": self._classify_entropy(char_entropy)
        }
    
    def _classify_entropy(self, entropy: float) -> str:
        """Classify entropy level."""
        thresholds = self.statistical_patterns["entropy_thresholds"]
        if entropy < thresholds["low"]:
            return "low"
        elif entropy < thresholds["medium"]:
            return "medium"
        elif entropy < thresholds["high"]:
            return "high"
        else:
            return "very_high"
    
    def _analyze_character_distribution(self, code: str) -> Dict[str, Any]:
        """Analyze character distribution patterns."""
        char_counts = Counter(code)
        total_chars = len(code)
        
        if total_chars == 0:
            return {}
        
        # Character frequency analysis
        frequencies = {char: count / total_chars for char, count in char_counts.items()}
        
        # Character categories
        categories = {
            "alphabetic": sum(count for char, count in char_counts.items() if char.isalpha()),
            "numeric": sum(count for char, count in char_counts.items() if char.isdigit()),
            "punctuation": sum(count for char, count in char_counts.items() if char in string.punctuation),
            "whitespace": sum(count for char, count in char_counts.items() if char.isspace()),
            "special": sum(count for char, count in char_counts.items() 
                         if not (char.isalnum() or char in string.punctuation or char.isspace()))
        }
        
        # Normalize categories
        category_ratios = {cat: count / total_chars for cat, count in categories.items()}
        
        # Most/least common characters
        sorted_chars = sorted(char_counts.items(), key=lambda x: x[1], reverse=True)
        
        return {
            "frequency_distribution": dict(sorted_chars[:20]),  # Top 20 characters
            "category_ratios": category_ratios,
            "unique_characters": len(char_counts),
            "most_common_char": sorted_chars[0] if sorted_chars else ("", 0),
            "least_common_chars": sorted_chars[-5:] if len(sorted_chars) >= 5 else sorted_chars,
            "uniformity_score": self._calculate_uniformity_score(list(char_counts.values()))
        }
    
    def _calculate_uniformity_score(self, values: List[int]) -> float:
        """Calculate uniformity score (0-1, where 1 is perfectly uniform)."""
        if not values or len(values) == 1:
            return 1.0
        
        mean_val = np.mean(values)
        if mean_val == 0:
            return 1.0
        
        variance = np.var(values)
        coefficient_of_variation = np.sqrt(variance) / mean_val
        
        # Convert to uniformity score (inverse of coefficient of variation)
        uniformity = 1 / (1 + coefficient_of_variation)
        return uniformity
    
    def _analyze_line_length_distribution(self, lines: List[str]) -> Dict[str, Any]:
        """Analyze line length distribution patterns."""
        if not lines:
            return {}
        
        line_lengths = [len(line) for line in lines]
        non_empty_lengths = [length for length in line_lengths if length > 0]
        
        if not non_empty_lengths:
            return {"all_empty_lines": True}
        
        distribution = {
            "mean": np.mean(line_lengths),
            "median": np.median(line_lengths),
            "std_dev": np.std(line_lengths),
            "min": min(line_lengths),
            "max": max(line_lengths),
            "range": max(line_lengths) - min(line_lengths),
            "percentiles": {
                "25th": np.percentile(line_lengths, 25),
                "75th": np.percentile(line_lengths, 75),
                "90th": np.percentile(line_lengths, 90),
                "95th": np.percentile(line_lengths, 95)
            },
            "length_histogram": self._create_length_histogram(line_lengths),
            "outlier_count": self._count_outliers(line_lengths),
            "consistency_score": self._calculate_line_consistency(line_lengths)
        }
        
        return distribution
    
    def _create_length_histogram(self, lengths: List[int]) -> Dict[str, int]:
        """Create histogram of line lengths."""
        bins = [0, 20, 40, 60, 80, 100, 120, 150, float('inf')]
        bin_labels = ["0-20", "21-40", "41-60", "61-80", "81-100", "101-120", "121-150", "150+"]
        
        histogram = {label: 0 for label in bin_labels}
        
        for length in lengths:
            for i, (bin_start, bin_end) in enumerate(zip(bins[:-1], bins[1:])):
                if bin_start <= length < bin_end:
                    histogram[bin_labels[i]] += 1
                    break
        
        return histogram
    
    def _count_outliers(self, values: List[int]) -> Dict[str, int]:
        """Count outliers using IQR method."""
        if len(values) < 4:
            return {"lower": 0, "upper": 0}
        
        q1 = np.percentile(values, 25)
        q3 = np.percentile(values, 75)
        iqr = q3 - q1
        
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        lower_outliers = sum(1 for v in values if v < lower_bound)
        upper_outliers = sum(1 for v in values if v > upper_bound)
        
        return {"lower": lower_outliers, "upper": upper_outliers}
    
    def _calculate_line_consistency(self, lengths: List[int]) -> float:
        """Calculate line length consistency score (0-1)."""
        if len(lengths) <= 1:
            return 1.0
        
        mean_length = np.mean(lengths)
        if mean_length == 0:
            return 1.0
        
        std_dev = np.std(lengths)
        coefficient_of_variation = std_dev / mean_length
        
        # Convert to consistency score
        consistency = 1 / (1 + coefficient_of_variation)
        return consistency
    
    def _calculate_halstead_metrics(self, code: str) -> Dict[str, Any]:
        """Calculate Halstead complexity metrics."""
        try:
            # Extract operators and operands
            operators, operands = self._extract_operators_operands(code)
            
            if not operators and not operands:
                return {"error": "No operators or operands found"}
            
            # Count unique and total
            unique_operators = len(set(operators))
            unique_operands = len(set(operands))
            total_operators = len(operators)
            total_operands = len(operands)
            
            # Calculate Halstead metrics
            vocabulary = unique_operators + unique_operands
            length = total_operators + total_operands
            
            if vocabulary == 0 or length == 0:
                return {"error": "Invalid vocabulary or length"}
            
            volume = length * math.log2(vocabulary) if vocabulary > 1 else 0
            difficulty = (unique_operators / 2) * (total_operands / unique_operands) if unique_operands > 0 else 0
            effort = difficulty * volume
            time_to_program = effort / 18  # Stroud number
            bugs = volume / 3000  # Halstead's bug prediction
            
            return {
                "vocabulary": vocabulary,
                "length": length,
                "volume": volume,
                "difficulty": difficulty,
                "effort": effort,
                "time_to_program": time_to_program,
                "predicted_bugs": bugs,
                "unique_operators": unique_operators,
                "unique_operands": unique_operands,
                "total_operators": total_operators,
                "total_operands": total_operands
            }
        
        except Exception as e:
            logger.error(f"Error calculating Halstead metrics: {str(e)}")
            return {"error": str(e)}
    
    def _extract_operators_operands(self, code: str) -> Tuple[List[str], List[str]]:
        """Extract operators and operands from code."""
        # Simple extraction - would be more sophisticated with AST
        operators = []
        operands = []
        
        # Common operators
        operator_patterns = [
            r'\+\+', r'--', r'\+=', r'-=', r'\*=', r'/=', r'%=',
            r'==', r'!=', r'<=', r'>=', r'&&', r'\|\|',
            r'\+', r'-', r'\*', r'/', r'%', r'=', r'<', r'>',
            r'!', r'&', r'\|', r'\^', r'~', r'<<', r'>>',
            r'\.', r'->', r'::', r',', r';', r'\(', r'\)',
            r'\[', r'\]', r'\{', r'\}'
        ]
        
        for pattern in operator_patterns:
            matches = re.findall(pattern, code)
            operators.extend(matches)
        
        # Extract identifiers as operands
        identifier_pattern = r'\b[a-zA-Z_][a-zA-Z0-9_]*\b'
        identifiers = re.findall(identifier_pattern, code)
        
        # Filter out keywords
        keywords = self.language_keywords.get("python", set())  # Default to Python
        operands.extend([id for id in identifiers if id not in keywords])
        
        # Extract literals as operands
        number_pattern = r'\b\d+\.?\d*\b'
        numbers = re.findall(number_pattern, code)
        operands.extend(numbers)
        
        string_pattern = r'"[^"]*"|\'[^\']*\''
        strings = re.findall(string_pattern, code)
        operands.extend(strings)
        
        return operators, operands
    
    def _detect_code_patterns(self, code: str) -> Dict[str, Any]:
        """Detect various code patterns."""
        patterns = {
            "code_smells": self._detect_code_smells(code),
            "design_indicators": self._detect_design_indicators(code),
            "complexity_patterns": self._detect_complexity_patterns(code),
            "naming_patterns": self._detect_naming_patterns(code),
            "structure_patterns": self._detect_structure_patterns(code)
        }
        
        return patterns
    
    def _detect_code_smells(self, code: str) -> Dict[str, int]:
        """Detect code smells in the text."""
        lines = code.split('\n')
        smells = defaultdict(int)
        
        thresholds = self.statistical_patterns["code_smells"]
        
        for line in lines:
            # Long lines
            if len(line) > thresholds["long_lines"]:
                smells["long_lines"] += 1
            
            # Deep nesting (simplified check)
            leading_spaces = len(line) - len(line.lstrip())
            if leading_spaces > thresholds["deep_nesting"] * 4:  # Assuming 4 spaces per indent
                smells["deep_nesting"] += 1
        
        # Many parameters (simplified check)
        function_pattern = r'def\s+\w+\s*\([^)]*\)'
        for match in re.finditer(function_pattern, code):
            param_count = match.group().count(',') + 1
            if param_count > thresholds["many_parameters"]:
                smells["many_parameters"] += 1
        
        # Long methods (simplified check)
        methods = re.split(r'\ndef\s+', code)
        for method in methods:
            method_lines = len(method.split('\n'))
            if method_lines > thresholds["long_methods"]:
                smells["long_methods"] += 1
        
        return dict(smells)
    
    def _detect_design_indicators(self, code: str) -> Dict[str, int]:
        """Detect design pattern indicators."""
        indicators = defaultdict(int)
        
        # Singleton indicators
        if re.search(r'__new__.*instance', code, re.IGNORECASE):
            indicators["singleton"] += 1
        
        # Factory indicators
        if re.search(r'def\s+(create|make|build|factory)', code, re.IGNORECASE):
            indicators["factory"] += 1
        
        # Observer indicators
        if re.search(r'(notify|update|subscribe|attach|detach)', code, re.IGNORECASE):
            indicators["observer"] += 1
        
        # Strategy indicators
        if re.search(r'class.*Strategy|def\s+execute', code, re.IGNORECASE):
            indicators["strategy"] += 1
        
        return dict(indicators)
    
    def _detect_complexity_patterns(self, code: str) -> Dict[str, int]:
        """Detect complexity patterns."""
        patterns = defaultdict(int)
        
        complexity_indicators = self.statistical_patterns["complexity_indicators"]
        
        # Count nesting keywords
        for keyword in complexity_indicators["nesting_keywords"]:
            pattern = r'\b' + keyword + r'\b'
            patterns[f"nesting_{keyword}"] = len(re.findall(pattern, code))
        
        # Count branching keywords
        for keyword in complexity_indicators["branching_keywords"]:
            pattern = r'\b' + keyword + r'\b'
            patterns[f"branching_{keyword}"] = len(re.findall(pattern, code))
        
        # Count loop keywords
        for keyword in complexity_indicators["loop_keywords"]:
            pattern = r'\b' + keyword + r'\b'
            patterns[f"loop_{keyword}"] = len(re.findall(pattern, code))
        
        return dict(patterns)
    
    def _detect_naming_patterns(self, code: str) -> Dict[str, int]:
        """Detect naming convention patterns."""
        identifiers = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', code)
        
        patterns = {
            "snake_case": 0,
            "camelCase": 0,
            "PascalCase": 0,
            "UPPER_CASE": 0,
            "mixed_case": 0,
            "single_char": 0,
            "very_long": 0
        }
        
        for identifier in identifiers:
            if len(identifier) == 1:
                patterns["single_char"] += 1
            elif len(identifier) > 30:
                patterns["very_long"] += 1
            elif identifier.islower() and '_' in identifier:
                patterns["snake_case"] += 1
            elif identifier[0].islower() and any(c.isupper() for c in identifier[1:]):
                patterns["camelCase"] += 1
            elif identifier[0].isupper() and any(c.isupper() for c in identifier[1:]):
                patterns["PascalCase"] += 1
            elif identifier.isupper():
                patterns["UPPER_CASE"] += 1
            else:
                patterns["mixed_case"] += 1
        
        return patterns
    
    def _detect_structure_patterns(self, code: str) -> Dict[str, int]:
        """Detect structural patterns."""
        patterns = {
            "classes": len(re.findall(r'\bclass\s+\w+', code)),
            "functions": len(re.findall(r'\bdef\s+\w+', code)),
            "imports": len(re.findall(r'\bimport\s+|from\s+.*\s+import', code)),
            "decorators": len(re.findall(r'@\w+', code)),
            "comprehensions": len(re.findall(r'\[.*for.*in.*\]|\{.*for.*in.*\}', code)),
            "lambda_functions": len(re.findall(r'\blambda\b', code)),
            "exception_handling": len(re.findall(r'\btry\b|\bexcept\b|\bfinally\b', code)),
            "context_managers": len(re.findall(r'\bwith\s+', code))
        }
        
        return patterns
    
    def _calculate_repetition_metrics(self, lines: List[str]) -> Dict[str, Any]:
        """Calculate code repetition metrics."""
        if not lines:
            return {}
        
        # Line-level repetition
        line_counts = Counter(line.strip() for line in lines if line.strip())
        duplicate_lines = sum(count - 1 for count in line_counts.values() if count > 1)
        
        # Token-level repetition
        all_tokens = []
        for line in lines:
            tokens = self._tokenize_code(line)
            all_tokens.extend(tokens)
        
        token_counts = Counter(all_tokens)
        unique_tokens = len(token_counts)
        total_tokens = len(all_tokens)
        
        # N-gram repetition
        trigrams = []
        for i in range(len(all_tokens) - 2):
            trigram = ' '.join(all_tokens[i:i+3])
            trigrams.append(trigram)
        
        trigram_counts = Counter(trigrams)
        duplicate_trigrams = sum(count - 1 for count in trigram_counts.values() if count > 1)
        
        return {
            "duplicate_lines": duplicate_lines,
            "total_lines": len([line for line in lines if line.strip()]),
            "line_duplication_ratio": duplicate_lines / max(1, len(lines)),
            "unique_tokens": unique_tokens,
            "total_tokens": total_tokens,
            "token_diversity": unique_tokens / max(1, total_tokens),
            "duplicate_trigrams": duplicate_trigrams,
            "trigram_repetition_ratio": duplicate_trigrams / max(1, len(trigrams)),
            "most_repeated_lines": [line for line, count in line_counts.most_common(5) if count > 1],
            "most_repeated_tokens": token_counts.most_common(10)
        }
    
    def _calculate_variability_metrics(self, lines: List[str]) -> Dict[str, float]:
        """Calculate code variability metrics."""
        if not lines:
            return {}
        
        # Line length variability
        line_lengths = [len(line) for line in lines]
        length_variance = np.var(line_lengths) if line_lengths else 0
        
        # Token count variability per line
        token_counts = []
        for line in lines:
            tokens = self._tokenize_code(line)
            token_counts.append(len(tokens))
        
        token_variance = np.var(token_counts) if token_counts else 0
        
        # Indentation variability
        indentations = []
        for line in lines:
            if line.strip():
                indent = len(line) - len(line.lstrip())
                indentations.append(indent)
        
        indent_variance = np.var(indentations) if indentations else 0
        
        return {
            "line_length_variance": length_variance,
            "token_count_variance": token_variance,
            "indentation_variance": indent_variance,
            "complexity_variance": self._calculate_complexity_variance(lines)
        }
    
    def _calculate_complexity_variance(self, lines: List[str]) -> float:
        """Calculate complexity variance across lines."""
        complexities = []
        
        for line in lines:
            complexity = 0
            # Simple complexity calculation per line
            complexity += line.count('if') + line.count('for') + line.count('while')
            complexity += line.count('and') + line.count('or')
            complexity += line.count('try') + line.count('except')
            complexities.append(complexity)
        
        return np.var(complexities) if complexities else 0
    
    def _extract_complexity_indicators(self, code: str) -> Dict[str, Any]:
        """Extract complexity indicators from code."""
        indicators = {
            "cyclomatic_complexity": self._calculate_cyclomatic_complexity(code),
            "cognitive_complexity": self._calculate_cognitive_complexity(code),
            "nesting_depth": self._calculate_max_nesting_depth(code),
            "branching_factor": self._calculate_branching_factor(code),
            "loop_complexity": self._calculate_loop_complexity(code)
        }
        
        return indicators
    
    def _calculate_cyclomatic_complexity(self, code: str) -> int:
        """Calculate cyclomatic complexity."""
        # Count decision points
        decision_points = 0
        decision_points += len(re.findall(r'\bif\b', code))
        decision_points += len(re.findall(r'\belif\b', code))
        decision_points += len(re.findall(r'\bwhile\b', code))
        decision_points += len(re.findall(r'\bfor\b', code))
        decision_points += len(re.findall(r'\bexcept\b', code))
        decision_points += len(re.findall(r'\band\b', code))
        decision_points += len(re.findall(r'\bor\b', code))
        
        return decision_points + 1  # +1 for the main path
    
    def _calculate_max_nesting_depth(self, code: str) -> int:
        """Calculate maximum nesting depth."""
        lines = code.split('\n')
        max_depth = 0
        current_depth = 0
        
        for line in lines:
            stripped = line.strip()
            if stripped.endswith(':') and any(stripped.startswith(kw) for kw in ['if', 'for', 'while', 'try', 'with', 'def', 'class']):
                current_depth += 1
                max_depth = max(max_depth, current_depth)
            elif stripped and not line.startswith(' ') and not line.startswith('\t'):
                current_depth = 0
        
        return max_depth
    
    def _calculate_branching_factor(self, code: str) -> float:
        """Calculate average branching factor."""
        if_statements = len(re.findall(r'\bif\b', code))
        elif_statements = len(re.findall(r'\belif\b', code))
        else_statements = len(re.findall(r'\belse\b', code))
        
        total_conditionals = if_statements
        total_branches = if_statements + elif_statements + else_statements
        
        return total_branches / max(1, total_conditionals)
    
    def _calculate_loop_complexity(self, code: str) -> Dict[str, int]:
        """Calculate loop complexity metrics."""
        return {
            "for_loops": len(re.findall(r'\bfor\b', code)),
            "while_loops": len(re.findall(r'\bwhile\b', code)),
            "nested_loops": self._count_nested_loops(code),
            "loop_depth": self._calculate_max_loop_depth(code)
        }
    
    def _count_nested_loops(self, code: str) -> int:
        """Count nested loops."""
        lines = code.split('\n')
        nested_count = 0
        loop_depth = 0
        
        for line in lines:
            stripped = line.strip()
            if stripped.startswith(('for ', 'while ')):
                if loop_depth > 0:
                    nested_count += 1
                loop_depth += 1
            elif stripped and not line.startswith(' ') and not line.startswith('\t'):
                loop_depth = 0
        
        return nested_count
    
    def _calculate_max_loop_depth(self, code: str) -> int:
        """Calculate maximum loop nesting depth."""
        lines = code.split('\n')
        max_depth = 0
        current_depth = 0
        
        for line in lines:
            stripped = line.strip()
            if stripped.startswith(('for ', 'while ')):
                current_depth += 1
                max_depth = max(max_depth, current_depth)
            elif stripped and not line.startswith(' ') and not line.startswith('\t'):
                current_depth = 0
        
        return max_depth
    
    def _calculate_information_density(self, code: str) -> Dict[str, float]:
        """Calculate information density metrics."""
        lines = code.split('\n')
        non_empty_lines = [line for line in lines if line.strip()]
        
        if not non_empty_lines:
            return {"density": 0.0, "normalized_density": 0.0}
        
        # Information per line
        total_information = 0
        for line in non_empty_lines:
            # Simple information calculation based on unique characters and complexity
            unique_chars = len(set(line))
            complexity = line.count('(') + line.count('[') + line.count('{')
            total_information += unique_chars + complexity
        
        density = total_information / len(non_empty_lines)
        
        # Normalize by maximum possible density (rough estimate)
        max_density = 100  # Rough estimate of maximum information per line
        normalized_density = min(1.0, density / max_density)
        
        return {
            "density": density,
            "normalized_density": normalized_density,
            "total_information": total_information,
            "information_per_character": total_information / max(1, len(code))
        }
    
    async def _extract_text_embeddings(self, code: str, language: str) -> Dict[str, np.ndarray]:
        """Extract text-based embeddings from code."""
        try:
            embeddings = {}
            
            # Create cache key
            code_hash = hashlib.md5(code.encode()).hexdigest()
            cache_key = f"{language}_{code_hash}"
            
            if cache_key in self.embedding_cache:
                return self.embedding_cache[cache_key]
            
            # Simple bag-of-words embedding (placeholder for more sophisticated methods)
            tokens = self._tokenize_code(code)
            if tokens:
                # Create vocabulary-based embedding
                vocab_size = 1000  # Configurable vocabulary size
                embedding_dim = 256  # Configurable embedding dimension
                
                # Simple frequency-based embedding
                token_counts = Counter(tokens)
                most_common = token_counts.most_common(vocab_size)
                
                # Create feature vector
                feature_vector = np.zeros(embedding_dim)
                for i, (token, count) in enumerate(most_common[:embedding_dim]):
                    feature_vector[i] = count
                
                # Normalize
                if np.linalg.norm(feature_vector) > 0:
                    feature_vector = feature_vector / np.linalg.norm(feature_vector)
                
                embeddings["token_frequency"] = feature_vector
            
            # Character-level n-gram embeddings
            char_ngrams = self._extract_character_ngrams(code, n=3)
            if char_ngrams:
                ngram_counts = Counter(char_ngrams)
                ngram_vector = np.array([count for _, count in ngram_counts.most_common(256)])
                if len(ngram_vector) < 256:
                    ngram_vector = np.pad(ngram_vector, (0, 256 - len(ngram_vector)))
                
                # Normalize
                if np.linalg.norm(ngram_vector) > 0:
                    ngram_vector = ngram_vector / np.linalg.norm(ngram_vector)
                
                embeddings["character_ngrams"] = ngram_vector
            
            # Statistical feature embedding
            stats = await self._extract_statistical_features(code)
            if stats:
                stat_features = []
                
                # Extract numeric features
                if "entropy" in stats:
                    entropy_data = stats["entropy"]
                    stat_features.extend([
                        entropy_data.get("character_entropy", 0),
                        entropy_data.get("normalized_entropy", 0),
                        entropy_data.get("token_entropy", 0)
                    ])
                
                if "halstead_metrics" in stats and "error" not in stats["halstead_metrics"]:
                    halstead = stats["halstead_metrics"]
                    stat_features.extend([
                        halstead.get("vocabulary", 0),
                        halstead.get("length", 0),
                        halstead.get("volume", 0),
                        halstead.get("difficulty", 0),
                        halstead.get("effort", 0)
                    ])
                
                if stat_features:
                    stat_vector = np.array(stat_features[:128])  # Limit to 128 features
                    if len(stat_vector) < 128:
                        stat_vector = np.pad(stat_vector, (0, 128 - len(stat_vector)))
                    
                    # Normalize
                    if np.linalg.norm(stat_vector) > 0:
                        stat_vector = stat_vector / np.linalg.norm(stat_vector)
                    
                    embeddings["statistical"] = stat_vector
            
            # Cache results
            self.embedding_cache[cache_key] = embeddings
            
            return embeddings
        
        except Exception as e:
            logger.error(f"Error extracting text embeddings: {str(e)}")
            return {}
    
    async def _extract_basic_features_only(self, code: str, language: str) -> TextFeatures:
        """Extract only basic features for large code files to meet latency targets."""
        start_time = time.perf_counter()
        
        # Basic statistics only
        lines = code.split('\n')
        words = code.split()
        
        basic_stats = {
            "total_characters": len(code),
            "total_lines": len(lines),
            "total_words": len(words),
            "avg_line_length": sum(len(line) for line in lines) / len(lines) if lines else 0
        }
        
        extraction_time = (time.perf_counter() - start_time) * 1000
        
        return TextFeatures(
            statistics=basic_stats,
            metadata={
                "language": language,
                "extraction_time": datetime.utcnow().isoformat(),
                "extraction_latency_ms": extraction_time,
                "feature_version": "3.0_basic",
                "basic_mode": True
            },
            performance_metrics={
                "extraction_time_ms": extraction_time,
                "basic_mode": True
            }
        )
    
    async def _extract_pattern_indicators(self, code: str, language: str) -> Dict[str, float]:
        """Extract pattern indicators for ML-based pattern detection."""
        try:
            indicators = {}
            
            # Design pattern indicators
            for pattern_name in ["singleton", "factory", "observer", "strategy"]:
                pattern_count = len(re.findall(rf'\b{pattern_name}\b', code, re.IGNORECASE))
                indicators[f"design_pattern_{pattern_name}"] = pattern_count / max(1, len(code.split()))
            
            # Anti-pattern indicators
            for keyword in ["god", "blob", "spaghetti", "duplicate", "dead"]:
                count = len(re.findall(rf'\b{keyword}\b', code, re.IGNORECASE))
                indicators[f"anti_pattern_{keyword}"] = count / max(1, len(code.split()))
            
            # Complexity indicators
            indicators["nesting_depth_ratio"] = self._calculate_nesting_depth_ratio(code)
            indicators["method_length_variance"] = self._calculate_method_length_variance(code)
            indicators["parameter_complexity"] = self._calculate_parameter_complexity(code)
            
            return indicators
        
        except Exception as e:
            logger.error(f"Error extracting pattern indicators: {str(e)}")
            return {}
    
    async def _extract_quality_metrics(self, code: str) -> Dict[str, float]:
        """Extract code quality metrics."""
        try:
            lines = code.split('\n')
            
            quality_metrics = {
                "comment_density": self._calculate_comment_density(code),
                "documentation_ratio": self._calculate_documentation_ratio(code),
                "naming_consistency": self._calculate_naming_consistency(code),
                "line_length_consistency": self._calculate_line_length_consistency_score(lines),
                "whitespace_consistency": self._calculate_whitespace_consistency(code),
                "import_organization": self._calculate_import_organization_score(code),
                "error_handling_coverage": self._calculate_error_handling_coverage(code),
                "test_coverage_indicators": self._calculate_test_indicators(code)
            }
            
            # Overall quality score (weighted average)
            weights = {
                "comment_density": 0.15,
                "documentation_ratio": 0.15,
                "naming_consistency": 0.20,
                "line_length_consistency": 0.10,
                "whitespace_consistency": 0.10,
                "import_organization": 0.10,
                "error_handling_coverage": 0.15,
                "test_coverage_indicators": 0.05
            }
            
            overall_score = sum(
                quality_metrics.get(metric, 0) * weight 
                for metric, weight in weights.items()
            )
            
            quality_metrics["overall_quality_score"] = min(1.0, max(0.0, overall_score))
            
            return quality_metrics
        
        except Exception as e:
            logger.error(f"Error extracting quality metrics: {str(e)}")
            return {}
    
    async def _extract_security_indicators(self, code: str) -> Dict[str, int]:
        """Extract security vulnerability indicators."""
        try:
            security_counts = defaultdict(int)
            
            # Check for common security issues
            security_counts["potential_secrets"] = self._count_potential_secrets(code)
            security_counts["unsafe_functions"] = self._count_unsafe_functions(code)
            security_counts["input_validation_missing"] = self._check_input_validation(code)
            security_counts["authentication_issues"] = self._check_authentication_patterns(code)
            
            return dict(security_counts)
        
        except Exception as e:
            logger.error(f"Error extracting security indicators: {str(e)}")
            return {}
    
    async def _extract_ml_specific_features(self, code: str) -> Dict[str, Any]:
        """Extract ML-specific pattern features."""
        try:
            ml_features = {}
            
            # Data leakage indicators
            ml_features["data_leakage_risk"] = self._detect_data_leakage_patterns(code)
            
            # Model validation issues
            ml_features["validation_issues"] = self._detect_validation_issues(code)
            
            # Feature engineering problems
            ml_features["feature_engineering_issues"] = self._detect_feature_issues(code)
            
            return ml_features
        
        except Exception as e:
            logger.error(f"Error extracting ML-specific features: {str(e)}")
            return {}
    
    # Helper methods for enhanced features
    def _calculate_nesting_depth_ratio(self, code: str) -> float:
        """Calculate the ratio of deeply nested code."""
        lines = code.split('\n')
        deep_nesting_threshold = 4
        deep_lines = 0
        
        for line in lines:
            if line.strip():
                indent_level = (len(line) - len(line.lstrip())) // 4
                if indent_level >= deep_nesting_threshold:
                    deep_lines += 1
        
        return deep_lines / max(1, len([l for l in lines if l.strip()]))
    
    def _calculate_method_length_variance(self, code: str) -> float:
        """Calculate variance in method lengths."""
        method_pattern = re.compile(r'def\s+\w+.*?(?=def\s+|class\s+|$)', re.DOTALL)
        methods = method_pattern.findall(code)
        
        if len(methods) < 2:
            return 0.0
        
        method_lengths = [len(method.split('\n')) for method in methods]
        return float(np.var(method_lengths))
    
    def _calculate_parameter_complexity(self, code: str) -> float:
        """Calculate average parameter complexity."""
        function_pattern = self.compiled_patterns["function_def"]
        matches = function_pattern.findall(code)
        
        if not matches:
            return 0.0
        
        param_counts = []
        for _, params in matches:
            param_count = len([p.strip() for p in params.split(',') if p.strip()])
            param_counts.append(param_count)
        
        return float(np.mean(param_counts)) if param_counts else 0.0
    
    def _calculate_comment_density(self, code: str) -> float:
        """Calculate comment density score."""
        lines = code.split('\n')
        code_lines = [l for l in lines if l.strip() and not l.strip().startswith('#')]
        comment_lines = [l for l in lines if l.strip().startswith('#')]
        
        if not code_lines:
            return 0.0
        
        density = len(comment_lines) / len(code_lines)
        # Normalize to 0-1 scale with ideal range 0.1-0.3
        if density < 0.1:
            return density / 0.1 * 0.5  # Scale to 0-0.5
        elif density <= 0.3:
            return 0.5 + (density - 0.1) / 0.2 * 0.5  # Scale to 0.5-1.0
        else:
            return max(0.1, 1.0 - (density - 0.3) * 2)  # Penalize excessive comments
    
    def _calculate_documentation_ratio(self, code: str) -> float:
        """Calculate documentation string ratio."""
        docstring_pattern = self.compiled_patterns["docstring"]
        docstrings = docstring_pattern.findall(code)
        
        function_pattern = self.compiled_patterns["function_def"]
        functions = function_pattern.findall(code)
        
        class_pattern = self.compiled_patterns["class_def"]
        classes = class_pattern.findall(code)
        
        total_definitions = len(functions) + len(classes)
        if total_definitions == 0:
            return 1.0
        
        return min(1.0, len(docstrings) / total_definitions)
    
    def _calculate_naming_consistency(self, code: str) -> float:
        """Calculate naming convention consistency."""
        identifiers = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', code)
        if not identifiers:
            return 1.0
        
        conventions = {
            "snake_case": 0,
            "camelCase": 0,
            "PascalCase": 0,
            "UPPER_CASE": 0
        }
        
        for identifier in identifiers:
            if identifier.islower() and '_' in identifier:
                conventions["snake_case"] += 1
            elif identifier[0].islower() and any(c.isupper() for c in identifier[1:]):
                conventions["camelCase"] += 1
            elif identifier[0].isupper():
                conventions["PascalCase"] += 1
            elif identifier.isupper() and len(identifier) > 1:
                conventions["UPPER_CASE"] += 1
        
        # Calculate consistency as the ratio of the most common convention
        max_convention = max(conventions.values())
        return max_convention / max(1, len(identifiers))
    
    def _calculate_line_length_consistency_score(self, lines: List[str]) -> float:
        """Calculate line length consistency score."""
        if not lines:
            return 1.0
        
        non_empty_lines = [line for line in lines if line.strip()]
        if len(non_empty_lines) < 2:
            return 1.0
        
        lengths = [len(line) for line in non_empty_lines]
        mean_length = np.mean(lengths)
        std_length = np.std(lengths)
        
        if mean_length == 0:
            return 1.0
        
        # Coefficient of variation (lower is more consistent)
        cv = std_length / mean_length
        # Convert to consistency score (0-1, where 1 is most consistent)
        return max(0.0, 1.0 - cv)
    
    def _calculate_whitespace_consistency(self, code: str) -> float:
        """Calculate whitespace usage consistency."""
        lines = code.split('\n')
        indented_lines = [line for line in lines if line.startswith((' ', '\t'))]
        
        if not indented_lines:
            return 1.0
        
        space_indents = sum(1 for line in indented_lines if line.startswith(' '))
        tab_indents = sum(1 for line in indented_lines if line.startswith('\t'))
        
        total_indents = space_indents + tab_indents
        if total_indents == 0:
            return 1.0
        
        # Consistency score based on predominant style
        consistency = max(space_indents, tab_indents) / total_indents
        return consistency
    
    def _calculate_import_organization_score(self, code: str) -> float:
        """Calculate import organization quality score."""
        import_pattern = self.compiled_patterns["import_stmt"]
        imports = import_pattern.findall(code)
        
        if not imports:
            return 1.0
        
        # Check if imports are at the top of the file
        lines = code.split('\n')
        first_import_line = None
        
        for i, line in enumerate(lines):
            if import_pattern.match(line.strip()):
                if first_import_line is None:
                    first_import_line = i
                break
        
        if first_import_line is None:
            return 1.0
        
        # Score based on how early imports appear
        early_import_score = max(0.0, 1.0 - first_import_line / max(1, len(lines) * 0.1))
        return early_import_score
    
    def _calculate_error_handling_coverage(self, code: str) -> float:
        """Calculate error handling coverage score."""
        exception_pattern = self.compiled_patterns["exception_handling"]
        exception_blocks = len(exception_pattern.findall(code))
        
        # Count risky operations that should have error handling
        risky_patterns = [
            r'open\s*\(',  # File operations
            r'int\s*\(',   # Type conversions
            r'float\s*\(',
            r'\[.*\]',     # List access
        ]
        
        risky_operations = 0
        for pattern in risky_patterns:
            risky_operations += len(re.findall(pattern, code))
        
        if risky_operations == 0:
            return 1.0
        
        # Coverage ratio
        coverage = min(1.0, exception_blocks / risky_operations)
        return coverage
    
    def _calculate_test_indicators(self, code: str) -> float:
        """Calculate test presence indicators."""
        test_indicators = [
            r'def\s+test_',  # Test functions
            r'assert\s+',    # Assertions
            r'@pytest\.',    # Pytest decorators
            r'unittest\.',   # Unittest usage
        ]
        
        test_count = 0
        for pattern in test_indicators:
            test_count += len(re.findall(pattern, code, re.IGNORECASE))
        
        # Normalize by code size
        code_lines = len([line for line in code.split('\n') if line.strip()])
        if code_lines == 0:
            return 0.0
        
        return min(1.0, test_count / max(1, code_lines * 0.1))
    
    # Security helper methods
    def _count_potential_secrets(self, code: str) -> int:
        """Count potential hardcoded secrets."""
        secret_patterns = [
            r'["\'][A-Za-z0-9+/]{20,}={0,2}["\']',  # Base64-like strings
            r'["\'][0-9a-fA-F]{32,}["\']',          # Hex strings
            r'["\'][A-Za-z0-9]{40,}["\']',         # Long random strings
        ]
        
        count = 0
        for pattern in secret_patterns:
            matches = re.findall(pattern, code)
            count += len(matches)
        
        return count
    
    def _count_unsafe_functions(self, code: str) -> int:
        """Count usage of unsafe functions."""
        unsafe_patterns = [
            r'eval\s*\(',
            r'exec\s*\(',
            r'pickle\.loads',
            r'subprocess\.call',
            r'os\.system',
        ]
        
        count = 0
        for pattern in unsafe_patterns:
            count += len(re.findall(pattern, code))
        
        return count
    
    def _check_input_validation(self, code: str) -> int:
        """Check for missing input validation."""
        # This is a simplified check
        input_sources = re.findall(r'input\s*\(|request\.|params\[|args\[', code)
        validation_patterns = re.findall(r'validate|sanitize|escape|clean', code, re.IGNORECASE)
        
        if len(input_sources) == 0:
            return 0
        
        # Return count of inputs without validation
        return max(0, len(input_sources) - len(validation_patterns))
    
    def _check_authentication_patterns(self, code: str) -> int:
        """Check for authentication-related issues."""
        auth_issues = 0
        
        # Check for hardcoded credentials
        if re.search(r'password\s*=\s*["\']w+["\']', code, re.IGNORECASE):
            auth_issues += 1
        
        # Check for weak authentication
        if re.search(r'auth\s*=\s*False|authenticate\s*=\s*False', code, re.IGNORECASE):
            auth_issues += 1
        
        return auth_issues
    
    # ML-specific helper methods
    def _detect_data_leakage_patterns(self, code: str) -> Dict[str, int]:
        """Detect data leakage patterns in ML code."""
        leakage_indicators = {
            "future_data_access": 0,
            "target_leakage": 0,
            "temporal_leakage": 0,
            "preprocessing_leakage": 0
        }
        
        # Check for future data access
        future_patterns = [r'test.*train', r'future.*past', r'leak.*data']
        for pattern in future_patterns:
            leakage_indicators["future_data_access"] += len(re.findall(pattern, code, re.IGNORECASE))
        
        # Check for target leakage
        target_patterns = [r'target.*feature', r'label.*train', r'y.*X']
        for pattern in target_patterns:
            leakage_indicators["target_leakage"] += len(re.findall(pattern, code, re.IGNORECASE))
        
        return leakage_indicators
    
    def _detect_validation_issues(self, code: str) -> Dict[str, int]:
        """Detect model validation issues."""
        validation_issues = {
            "no_train_test_split": 0,
            "no_cross_validation": 0,
            "data_preprocessing_on_test": 0
        }
        
        # Check for train/test split
        if not re.search(r'train_test_split|split.*test', code, re.IGNORECASE):
            validation_issues["no_train_test_split"] = 1
        
        # Check for cross-validation
        if not re.search(r'cross_val|cv|fold', code, re.IGNORECASE):
            validation_issues["no_cross_validation"] = 1
        
        return validation_issues
    
    def _detect_feature_issues(self, code: str) -> Dict[str, int]:
        """Detect feature engineering issues."""
        feature_issues = {
            "missing_scaling": 0,
            "feature_selection_issues": 0,
            "encoding_problems": 0
        }
        
        # Check for scaling
        if not re.search(r'scale|standard|normal|minmax', code, re.IGNORECASE):
            feature_issues["missing_scaling"] = 1
        
        return feature_issues
    
    def _extract_character_ngrams(self, text: str, n: int = 3) -> List[str]:
        """Extract character n-grams from text."""
        # Remove excessive whitespace
        cleaned_text = re.sub(r'\s+', ' ', text)
        return [cleaned_text[i:i+n] for i in range(len(cleaned_text) - n + 1)]
    
    async def cleanup(self):
        """Cleanup text extractor resources and save performance stats."""
        try:
            # Log final performance statistics
            stats = self.cache_stats
            logger.info(f"Text feature extractor performance: {stats}")
            
            # Clean up caches
            self.embedding_cache.clear()
            self.feature_cache.clear()
            self.cache_access_times.clear()
            
            # Close thread pool
            if hasattr(self, 'executor'):
                self.executor.shutdown(wait=True)
            
            # Clear models
            self._embedding_model = None
            self._tfidf_vectorizer = None
            self._pattern_classifier_cache.clear()
            
        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")