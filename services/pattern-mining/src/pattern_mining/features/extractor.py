"""
Feature Extractor

Main feature extraction coordinator.
"""

from typing import Dict, Any, List, Optional
import asyncio
import logging
from datetime import datetime

from ..config.ml import get_ml_config
from .ast_features import ASTFeatureExtractor
from .semantic_features import SemanticFeatureExtractor
from .text_features import TextFeatureExtractor

logger = logging.getLogger(__name__)


class FeatureExtractor:
    """Main feature extractor coordinator."""
    
    def __init__(self):
        self.config = get_ml_config()
        self.ast_extractor = ASTFeatureExtractor()
        self.semantic_extractor = SemanticFeatureExtractor()
        self.text_extractor = TextFeatureExtractor()
    
    async def extract_features(
        self,
        code: str,
        language: str,
        feature_types: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Extract features from code."""
        try:
            features = {}
            
            # Default feature types
            if feature_types is None:
                feature_types = ["ast", "semantic", "text"]
            
            # Extract features concurrently
            tasks = []
            
            if "ast" in feature_types:
                tasks.append(self._extract_ast_features(code, language))
            
            if "semantic" in feature_types:
                tasks.append(self._extract_semantic_features(code, language))
            
            if "text" in feature_types:
                tasks.append(self._extract_text_features(code, language))
            
            # Wait for all extractions to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Combine results
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Feature extraction failed: {str(result)}")
                    continue
                
                feature_type = feature_types[i]
                features[feature_type] = result
            
            # Add metadata
            features["metadata"] = {
                "language": language,
                "feature_types": feature_types,
                "extraction_time": datetime.utcnow().isoformat(),
                "code_length": len(code),
                "line_count": len(code.split('\n'))
            }
            
            return features
        
        except Exception as e:
            logger.error(f"Error extracting features: {str(e)}")
            raise
    
    async def _extract_ast_features(self, code: str, language: str) -> Dict[str, Any]:
        """Extract AST features."""
        try:
            return await self.ast_extractor.extract_features(code, language)
        except Exception as e:
            logger.error(f"AST feature extraction failed: {str(e)}")
            return {}
    
    async def _extract_semantic_features(self, code: str, language: str) -> Dict[str, Any]:
        """Extract semantic features."""
        try:
            return await self.semantic_extractor.extract_features(code, language)
        except Exception as e:
            logger.error(f"Semantic feature extraction failed: {str(e)}")
            return {}
    
    async def _extract_text_features(self, code: str, language: str) -> Dict[str, Any]:
        """Extract text features."""
        try:
            return await self.text_extractor.extract_features(code, language)
        except Exception as e:
            logger.error(f"Text feature extraction failed: {str(e)}")
            return {}
    
    async def extract_embeddings(self, code: str, language: str) -> Dict[str, Any]:
        """Extract code embeddings."""
        try:
            # Extract semantic embeddings
            semantic_features = await self.semantic_extractor.extract_features(code, language)
            embeddings = semantic_features.get("embeddings", {})
            
            # Add text embeddings
            text_features = await self.text_extractor.extract_features(code, language)
            embeddings.update(text_features.get("embeddings", {}))
            
            return embeddings
        
        except Exception as e:
            logger.error(f"Error extracting embeddings: {str(e)}")
            raise
    
    async def batch_extract_features(
        self,
        code_samples: List[Dict[str, str]],
        feature_types: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """Extract features from multiple code samples."""
        try:
            tasks = []
            
            for sample in code_samples:
                code = sample.get("code", "")
                language = sample.get("language", "")
                
                task = self.extract_features(code, language, feature_types)
                tasks.append(task)
            
            # Process in batches to avoid overwhelming the system
            batch_size = 10
            results = []
            
            for i in range(0, len(tasks), batch_size):
                batch = tasks[i:i + batch_size]
                batch_results = await asyncio.gather(*batch, return_exceptions=True)
                results.extend(batch_results)
            
            # Handle exceptions
            processed_results = []
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"Batch feature extraction failed: {str(result)}")
                    processed_results.append({})
                else:
                    processed_results.append(result)
            
            return processed_results
        
        except Exception as e:
            logger.error(f"Error in batch feature extraction: {str(e)}")
            raise
    
    async def validate_features(self, features: Dict[str, Any]) -> bool:
        """Validate extracted features."""
        try:
            # Check required fields
            if "metadata" not in features:
                return False
            
            # Check feature types
            feature_types = features["metadata"].get("feature_types", [])
            for feature_type in feature_types:
                if feature_type not in features:
                    return False
            
            # Validate individual feature types
            for feature_type, feature_data in features.items():
                if feature_type == "metadata":
                    continue
                
                if not isinstance(feature_data, dict):
                    return False
            
            return True
        
        except Exception as e:
            logger.error(f"Error validating features: {str(e)}")
            return False
    
    async def cleanup(self):
        """Cleanup feature extraction resources."""
        await self.ast_extractor.cleanup()
        await self.semantic_extractor.cleanup()
        await self.text_extractor.cleanup()
    
    @property
    def supported_languages(self) -> List[str]:
        """Get list of supported languages."""
        return [
            "python", "javascript", "typescript", "java", "cpp", "c",
            "go", "rust", "ruby", "php", "swift", "kotlin", "scala"
        ]
    
    @property
    def available_feature_types(self) -> List[str]:
        """Get list of available feature types."""
        return ["ast", "semantic", "text"]


# Global feature extractor instance
_feature_extractor: Optional[FeatureExtractor] = None


def get_feature_extractor() -> FeatureExtractor:
    """Get feature extractor instance."""
    global _feature_extractor
    if _feature_extractor is None:
        _feature_extractor = FeatureExtractor()
    return _feature_extractor