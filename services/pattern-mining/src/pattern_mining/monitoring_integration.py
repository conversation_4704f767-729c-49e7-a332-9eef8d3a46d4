"""
Monitoring System Integration

Integrates all monitoring, logging, health checks, alerting, and observability
components with the existing FastAPI application.
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from fastapi import FastAPI, Request, Response, HTTPException, Depends
from fastapi.middleware.base import BaseHTTPMiddleware
from fastapi.responses import JSONResponse
from starlette.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import time
import uuid

# Monitoring components
from .monitoring import (
    PrometheusMetrics, PatternMetrics, PerformanceMetrics, 
    BusinessMetrics, ResourceMetrics, AlertManager,
    get_metrics, get_pattern_metrics, get_performance_metrics,
    get_business_metrics, get_resource_metrics, get_alert_manager
)

# Logging components
from .logging import (
    StructuredLogger, PerformanceLogger, get_logger, get_performance_logger,
    set_correlation_id, set_request_id, set_user_id, set_session_id,
    generate_correlation_id, generate_request_id, logging_context
)

# Health check components
from .health import (
    HealthChecker, HealthStatus, <PERSON>T<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ExternalService<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    get_health_checker, create_database_health_checker, create_ml_model_health_checker,
    create_external_service_health_checker
)

# Observability components
from .observability import (
    TracingManager, get_tracing_manager, init_tracing_manager
)

# Configuration
from .config.settings import get_settings

logger = get_logger(__name__)


class MonitoringMiddleware(BaseHTTPMiddleware):
    """
    Middleware for monitoring HTTP requests with comprehensive
    metrics, logging, and tracing.
    """
    
    def __init__(self, app: FastAPI):
        super().__init__(app)
        self.metrics = get_metrics()
        self.performance_metrics = get_performance_metrics()
        self.business_metrics = get_business_metrics()
        self.tracing_manager = get_tracing_manager()
        self.performance_logger = get_performance_logger()
        
    async def dispatch(self, request: Request, call_next):
        """Process request with monitoring."""
        start_time = time.time()
        
        # Generate correlation and request IDs
        correlation_id = request.headers.get('X-Correlation-ID', generate_correlation_id())
        request_id = generate_request_id()
        
        # Set context
        set_correlation_id(correlation_id)
        set_request_id(request_id)
        
        # Extract user information if available
        user_id = request.headers.get('X-User-ID')
        session_id = request.headers.get('X-Session-ID')
        
        if user_id:
            set_user_id(user_id)
        if session_id:
            set_session_id(session_id)
        
        # Get request information
        method = request.method
        url = str(request.url)
        endpoint = request.url.path
        user_agent = request.headers.get('User-Agent', '')
        
        # Get request size
        request_size = int(request.headers.get('Content-Length', 0))
        
        # Start performance tracking
        operation_id = self.performance_logger.start_operation(
            f"http_request_{method}_{endpoint}",
            {
                'method': method,
                'endpoint': endpoint,
                'user_agent': user_agent,
                'correlation_id': correlation_id,
                'request_id': request_id
            }
        )
        
        # Update concurrent requests metric
        self.metrics.increment_gauge('http_concurrent_requests')
        
        # Process request
        try:
            # Trace the request
            with self.tracing_manager.trace_operation(
                f"HTTP {method} {endpoint}",
                attributes={
                    'http.method': method,
                    'http.url': url,
                    'http.route': endpoint,
                    'http.user_agent': user_agent,
                    'http.request_size': request_size,
                    'correlation_id': correlation_id,
                    'request_id': request_id
                }
            ) as span:
                response = await call_next(request)
                
                # Calculate duration
                duration = time.time() - start_time
                status_code = response.status_code
                
                # Get response size
                response_size = int(response.headers.get('Content-Length', 0))
                
                # Update tracing attributes
                span.set_attribute('http.status_code', status_code)
                span.set_attribute('http.response_size', response_size)
                span.set_attribute('http.duration_ms', duration * 1000)
                
                # Log request
                logger.info(
                    f"HTTP {method} {endpoint} - {status_code}",
                    method=method,
                    endpoint=endpoint,
                    status_code=status_code,
                    duration_ms=duration * 1000,
                    request_size_bytes=request_size,
                    response_size_bytes=response_size,
                    user_agent=user_agent,
                    correlation_id=correlation_id,
                    request_id=request_id,
                    user_id=user_id,
                    session_id=session_id
                )
                
                # Record metrics
                self.metrics.record_http_request(
                    method, endpoint, status_code, duration,
                    request_size, response_size
                )
                
                self.performance_metrics.record_request_metrics(
                    method, endpoint, status_code, duration,
                    request_size, response_size, user_agent
                )
                
                # Record business metrics
                if user_id:
                    from .monitoring.business_metrics import UserTier
                    user_tier = UserTier.FREE  # Default - would need to be determined from user data
                    
                    if session_id:
                        # Check if session exists, if not create it
                        if session_id not in self.business_metrics._user_sessions:
                            self.business_metrics.start_user_session(session_id, user_id, user_tier)
                        
                        # Record user action
                        self.business_metrics.record_user_action(
                            session_id, 
                            f"http_request_{method}_{endpoint}",
                            {
                                'status_code': status_code,
                                'duration_ms': duration * 1000,
                                'endpoint': endpoint
                            }
                        )
                
                # End performance tracking
                self.performance_logger.end_operation(operation_id, "completed")
                
                # Add response headers
                response.headers['X-Correlation-ID'] = correlation_id
                response.headers['X-Request-ID'] = request_id
                response.headers['X-Response-Time'] = str(duration)
                
                return response
                
        except Exception as e:
            # Calculate duration
            duration = time.time() - start_time
            
            # Record error metrics
            self.metrics.increment_counter('http_requests_total', {
                'method': method,
                'endpoint': endpoint,
                'status_code': '500'
            })
            
            # Log error
            logger.error(
                f"HTTP {method} {endpoint} - ERROR",
                method=method,
                endpoint=endpoint,
                duration_ms=duration * 1000,
                error=str(e),
                correlation_id=correlation_id,
                request_id=request_id,
                exception=e
            )
            
            # Record exception in tracing
            self.tracing_manager.record_exception(e)
            
            # End performance tracking with error
            self.performance_logger.end_operation(operation_id, "failed", {'error': str(e)})
            
            raise
            
        finally:
            # Decrement concurrent requests
            self.metrics.set_gauge('http_concurrent_requests', 
                                 self.metrics.get_metric('http_concurrent_requests')._value._value - 1)


class MonitoringIntegration:
    """
    Main monitoring integration class that sets up and manages
    all monitoring components.
    """
    
    def __init__(self, app: FastAPI):
        self.app = app
        self.settings = get_settings()
        
        # Initialize components
        self.metrics = None
        self.pattern_metrics = None
        self.performance_metrics = None
        self.business_metrics = None
        self.resource_metrics = None
        self.alert_manager = None
        self.health_checker = None
        self.tracing_manager = None
        self.performance_logger = None
        
        # Background tasks
        self._monitoring_tasks = []
        self._shutdown_event = asyncio.Event()
        
    async def initialize(self):
        """Initialize all monitoring components."""
        logger.info("Initializing monitoring system")
        
        # Initialize metrics
        self.metrics = get_metrics()
        self.pattern_metrics = get_pattern_metrics()
        self.performance_metrics = get_performance_metrics()
        self.business_metrics = get_business_metrics()
        self.resource_metrics = get_resource_metrics()
        
        # Initialize alert manager
        self.alert_manager = get_alert_manager()
        
        # Initialize health checker
        self.health_checker = get_health_checker()
        
        # Initialize tracing
        self.tracing_manager = init_tracing_manager(
            service_name=self.settings.app_name,
            service_version=self.settings.app_version,
            environment=self.settings.environment,
            enable_auto_instrumentation=True
        )
        
        # Initialize performance logger
        self.performance_logger = get_performance_logger()
        
        # Setup health checks
        await self._setup_health_checks()
        
        # Setup alert handlers
        self._setup_alert_handlers()
        
        # Start background tasks
        await self._start_background_tasks()
        
        # Add middleware
        self.app.add_middleware(MonitoringMiddleware)
        
        # Add monitoring routes
        self._add_monitoring_routes()
        
        logger.info("Monitoring system initialized successfully")
    
    async def _setup_health_checks(self):
        """Setup health checks."""
        # Database health check
        # Note: You'd need to pass actual database connections here
        db_health_checker = create_database_health_checker(
            # db_connection_pool=your_db_pool,
            # bigquery_client=your_bigquery_client,
            interval=30
        )
        self.health_checker.add_health_check(db_health_checker)
        
        # ML model health check
        ml_health_checker = create_ml_model_health_checker(
            # model_manager=your_model_manager,
            interval=60
        )
        self.health_checker.add_health_check(ml_health_checker)
        
        # External service health check
        external_service_checker = create_external_service_health_checker(
            interval=60
        )
        self.health_checker.add_health_check(external_service_checker)
    
    def _setup_alert_handlers(self):
        """Setup alert notification handlers."""
        def console_alert_handler(alert, action):
            """Handle console alerts."""
            if action == "fired":
                logger.warning(
                    f"ALERT FIRED: {alert.rule.name}",
                    alert_id=alert.id,
                    severity=alert.rule.severity.value,
                    value=alert.value,
                    threshold=alert.rule.threshold,
                    message=alert.rule.annotations.get('summary', '')
                )
            elif action == "resolved":
                logger.info(
                    f"ALERT RESOLVED: {alert.rule.name}",
                    alert_id=alert.id,
                    duration_seconds=(alert.resolved_at - alert.created_at).total_seconds()
                )
        
        # Register console handler
        from .monitoring.alert_manager import NotificationChannel
        self.alert_manager.register_notification_channel(
            NotificationChannel.CONSOLE,
            console_alert_handler
        )
    
    async def _start_background_tasks(self):
        """Start background monitoring tasks."""
        # Start metrics collection
        task = asyncio.create_task(self.metrics.start_background_collection())
        self._monitoring_tasks.append(task)
        
        # Start performance monitoring
        task = asyncio.create_task(self.performance_metrics.start_monitoring())
        self._monitoring_tasks.append(task)
        
        # Start resource monitoring
        task = asyncio.create_task(self.resource_metrics.start_collection())
        self._monitoring_tasks.append(task)
        
        # Start alert processing
        task = asyncio.create_task(self.alert_manager.start_processing())
        self._monitoring_tasks.append(task)
        
        # Start health checks
        task = asyncio.create_task(self.health_checker.start_background_checks())
        self._monitoring_tasks.append(task)
        
        # Start cleanup tasks
        task = asyncio.create_task(self._cleanup_loop())
        self._monitoring_tasks.append(task)
    
    async def _cleanup_loop(self):
        """Periodic cleanup of old monitoring data."""
        while not self._shutdown_event.is_set():
            try:
                # Clean up old data every hour
                await asyncio.sleep(3600)
                
                self.pattern_metrics.cleanup_old_data()
                self.performance_metrics.cleanup_old_data()
                self.business_metrics.cleanup_old_events()
                self.resource_metrics.cleanup_old_snapshots()
                self.health_checker.cleanup_old_results()
                
                logger.info("Completed periodic cleanup of monitoring data")
                
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    def _add_monitoring_routes(self):
        """Add monitoring and health check routes."""
        @self.app.get("/health")
        async def health():
            """Health check endpoint."""
            health_result = self.health_checker.get_overall_health()
            
            status_code = 200
            if health_result.status == HealthStatus.DEGRADED:
                status_code = 200  # Still healthy enough
            elif health_result.status == HealthStatus.UNHEALTHY:
                status_code = 503  # Service unavailable
            
            return JSONResponse(
                content=health_result.to_dict(),
                status_code=status_code
            )
        
        @self.app.get("/health/ready")
        async def readiness():
            """Readiness probe endpoint."""
            readiness_result = self.health_checker.get_readiness_status()
            
            status_code = 200 if readiness_result.status == HealthStatus.HEALTHY else 503
            
            return JSONResponse(
                content=readiness_result.to_dict(),
                status_code=status_code
            )
        
        @self.app.get("/health/live")
        async def liveness():
            """Liveness probe endpoint."""
            liveness_result = self.health_checker.get_liveness_status()
            
            status_code = 200 if liveness_result.status == HealthStatus.HEALTHY else 503
            
            return JSONResponse(
                content=liveness_result.to_dict(),
                status_code=status_code
            )
        
        @self.app.get("/health/startup")
        async def startup():
            """Startup probe endpoint."""
            startup_result = self.health_checker.get_startup_status()
            
            status_code = 200 if startup_result.status == HealthStatus.HEALTHY else 503
            
            return JSONResponse(
                content=startup_result.to_dict(),
                status_code=status_code
            )
        
        @self.app.get("/health/detail")
        async def health_detail():
            """Detailed health check endpoint."""
            return self.health_checker.get_health_summary()
        
        @self.app.get("/metrics")
        async def metrics():
            """Prometheus metrics endpoint."""
            return Response(
                content=self.metrics.get_metrics(),
                media_type="text/plain"
            )
        
        @self.app.get("/metrics/pattern")
        async def pattern_metrics():
            """Pattern detection metrics."""
            return self.pattern_metrics.get_global_stats()
        
        @self.app.get("/metrics/performance")
        async def performance_metrics():
            """Performance metrics."""
            return self.performance_metrics.get_performance_summary()
        
        @self.app.get("/metrics/business")
        async def business_metrics():
            """Business metrics."""
            return self.business_metrics.get_user_engagement_stats()
        
        @self.app.get("/metrics/resources")
        async def resource_metrics():
            """Resource metrics."""
            return self.resource_metrics.get_resource_summary()
        
        @self.app.get("/alerts")
        async def alerts():
            """Active alerts."""
            return {
                'active_alerts': [alert.to_dict() for alert in self.alert_manager.get_active_alerts()],
                'statistics': self.alert_manager.get_alert_statistics()
            }
        
        @self.app.get("/tracing/stats")
        async def tracing_stats():
            """Tracing statistics."""
            return self.tracing_manager.get_tracing_stats()
    
    async def shutdown(self):
        """Shutdown monitoring system."""
        logger.info("Shutting down monitoring system")
        
        # Signal shutdown
        self._shutdown_event.set()
        
        # Stop background tasks
        for task in self._monitoring_tasks:
            task.cancel()
        
        if self._monitoring_tasks:
            await asyncio.gather(*self._monitoring_tasks, return_exceptions=True)
        
        # Stop monitoring components
        if self.metrics:
            await self.metrics.stop_background_collection()
        
        if self.performance_metrics:
            await self.performance_metrics.stop_monitoring()
        
        if self.resource_metrics:
            await self.resource_metrics.stop_collection()
        
        if self.alert_manager:
            await self.alert_manager.stop_processing()
        
        if self.health_checker:
            await self.health_checker.stop_background_checks()
        
        if self.tracing_manager:
            self.tracing_manager.shutdown()
        
        logger.info("Monitoring system shutdown completed")


# Global monitoring integration instance
_monitoring_integration: Optional[MonitoringIntegration] = None


async def setup_monitoring(app: FastAPI) -> MonitoringIntegration:
    """Setup monitoring integration for FastAPI app."""
    global _monitoring_integration
    
    if _monitoring_integration is None:
        _monitoring_integration = MonitoringIntegration(app)
        await _monitoring_integration.initialize()
    
    return _monitoring_integration


async def get_monitoring_integration() -> Optional[MonitoringIntegration]:
    """Get the monitoring integration instance."""
    return _monitoring_integration


@asynccontextmanager
async def monitoring_lifespan(app: FastAPI):
    """Lifespan context manager for monitoring integration."""
    # Startup
    monitoring = await setup_monitoring(app)
    
    try:
        yield
    finally:
        # Shutdown
        if monitoring:
            await monitoring.shutdown()


# Utility functions for easy integration
def add_monitoring_to_app(app: FastAPI):
    """Add monitoring to FastAPI app with lifespan management."""
    app.router.lifespan_context = monitoring_lifespan
    
    # Add CORS middleware for monitoring endpoints
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )


def get_monitoring_context() -> Dict[str, Any]:
    """Get current monitoring context."""
    tracing_manager = get_tracing_manager()
    
    return {
        'trace_id': tracing_manager.get_current_trace_id(),
        'span_id': tracing_manager.get_current_span_id(),
        'correlation_id': generate_correlation_id(),
        'request_id': generate_request_id()
    }