"""
Main Entry Point

FastAPI application entry point for the pattern mining service.
"""

import asyncio
import signal
import sys
from typing import Optional

import uvicorn
import structlog
from .api.main import app
from .config.settings import get_settings

# Configure structured logging
logger = structlog.get_logger(__name__)

# Global server instance for graceful shutdown
server: Optional[uvicorn.Server] = None


def setup_signal_handlers() -> None:
    """Setup signal handlers for graceful shutdown."""
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, initiating graceful shutdown")
        if server:
            server.should_exit = True
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


async def run_server() -> None:
    """Run the server with proper async context management."""
    global server
    
    settings = get_settings()
    
    # Configure uvicorn
    config = uvicorn.Config(
        app=app,
        host=settings.host,
        port=settings.port,
        log_level=settings.log_level.lower(),
        access_log=True,
        server_header=False,
        date_header=False,
        reload=settings.is_development,
        workers=1,  # Use 1 worker for async operation
        loop="asyncio",
        http="auto",
        ws="auto",
        interface="asgi3",
        timeout_keep_alive=30,
        timeout_graceful_shutdown=30,
    )
    
    server = uvicorn.Server(config)
    
    try:
        logger.info(
            "Starting Pattern Mining Service",
            host=settings.host,
            port=settings.port,
            environment=settings.environment,
            workers=1,
            log_level=settings.log_level,
        )
        
        # Run server
        await server.serve()
        
    except Exception as e:
        logger.error("Server error occurred", error=str(e), exc_info=True)
        raise
    finally:
        logger.info("Server shutdown completed")


def main() -> None:
    """Main entry point with proper async handling."""
    try:
        # Setup signal handlers
        setup_signal_handlers()
        
        # Run the server
        asyncio.run(run_server())
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down")
    except Exception as e:
        logger.error("Fatal error occurred", error=str(e), exc_info=True)
        sys.exit(1)
    finally:
        logger.info("Application terminated")


if __name__ == "__main__":
    main()