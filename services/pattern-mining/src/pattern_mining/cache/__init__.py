"""
Cache Module

High-performance Redis-based caching layer for the pattern-mining service.
Provides comprehensive caching strategies, distributed caching, and monitoring.
"""

from .redis_client import RedisClientManager, RedisClusterManager
from .strategies import (
    LRUCache, 
    TTLCache, 
    MultiLevelCache, 
    CacheStrategy,
    CacheInvalidationStrategy
)
from .pattern_cache import (
    Pattern<PERSON>ache,
    FeatureCache,
    Inference<PERSON>ache,
    Similarity<PERSON>ache,
    BatchResultCache
)
from .distributed_cache import (
    Distributed<PERSON>ache,
    ConsistentHashingCache,
    ShardedCache
)
from .monitoring import (
    CacheMetrics,
    CacheMonitor,
    CacheAlerting
)

__all__ = [
    # Redis Client Management
    "RedisClientManager",
    "RedisClusterManager",
    
    # Caching Strategies
    "LRUCache",
    "TTLCache", 
    "MultiLevelCache",
    "CacheStrategy",
    "CacheInvalidationStrategy",
    
    # Pattern-specific Caches
    "PatternCache",
    "FeatureCache",
    "InferenceCache",
    "SimilarityCache",
    "BatchResultCache",
    
    # Distributed Caching
    "DistributedCache",
    "ConsistentHashingCache",
    "ShardedCache",
    
    # Monitoring
    "CacheMetrics",
    "CacheMonitor",
    "CacheAlerting",
]