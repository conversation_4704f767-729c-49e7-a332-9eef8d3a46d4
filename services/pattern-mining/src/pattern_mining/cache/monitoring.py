"""
Cache Monitoring

Comprehensive monitoring, metrics collection, and alerting for cache operations.
Includes performance analytics, health monitoring, and alert management.
"""

import asyncio
import time
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import json
from collections import defaultdict, deque
import statistics

import structlog
from prometheus_client import (
    Counter, Histogram, Gauge, Summary, Info,
    CollectorRegistry, generate_latest, CONTENT_TYPE_LATEST
)

from .redis_client import RedisClientManager, get_redis_client
from .strategies import CacheStrategy, MultiLevelCache

logger = structlog.get_logger()


class AlertSeverity(Enum):
    """Alert severity levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class AlertType(Enum):
    """Alert types."""
    CACHE_HIT_RATIO_LOW = "cache_hit_ratio_low"
    CACHE_MISS_RATIO_HIGH = "cache_miss_ratio_high"
    CACHE_EVICTION_RATE_HIGH = "cache_eviction_rate_high"
    CACHE_MEMORY_USAGE_HIGH = "cache_memory_usage_high"
    CACHE_LATENCY_HIGH = "cache_latency_high"
    CACHE_NODE_DOWN = "cache_node_down"
    CACHE_REPLICATION_LAG = "cache_replication_lag"
    CACHE_ERROR_RATE_HIGH = "cache_error_rate_high"
    CACHE_CONNECTION_POOL_EXHAUSTED = "cache_connection_pool_exhausted"


@dataclass
class AlertRule:
    """Alert rule configuration."""
    alert_type: AlertType
    threshold: float
    duration: float  # seconds
    severity: AlertSeverity
    message: str
    enabled: bool = True
    cooldown: float = 300  # 5 minutes
    last_triggered: float = 0.0
    
    def should_trigger(self, current_time: float) -> bool:
        """Check if alert should trigger based on cooldown."""
        return current_time - self.last_triggered >= self.cooldown


@dataclass
class Alert:
    """Alert instance."""
    alert_type: AlertType
    severity: AlertSeverity
    message: str
    value: float
    threshold: float
    timestamp: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert alert to dictionary."""
        return {
            'alert_type': self.alert_type.value,
            'severity': self.severity.value,
            'message': self.message,
            'value': self.value,
            'threshold': self.threshold,
            'timestamp': self.timestamp,
            'metadata': self.metadata
        }


class CacheMetrics:
    """
    Comprehensive cache metrics collection and analysis.
    Tracks performance, health, and operational metrics.
    """
    
    def __init__(self, registry: Optional[CollectorRegistry] = None):
        self.registry = registry or CollectorRegistry()
        self.metrics_history = defaultdict(lambda: deque(maxlen=1000))
        self.last_metrics_calculation = 0.0
        
        # Initialize Prometheus metrics
        self._init_prometheus_metrics()
        
        # Performance tracking
        self.operation_times = defaultdict(list)
        self.error_counts = defaultdict(int)
        self.hit_miss_ratios = deque(maxlen=100)
        
        # Health tracking
        self.node_health_history = defaultdict(lambda: deque(maxlen=100))
        self.connection_pool_usage = deque(maxlen=100)
        
        logger.info("Cache metrics initialized")
    
    def _init_prometheus_metrics(self) -> None:
        """Initialize Prometheus metrics."""
        # Cache operation counters
        self.cache_operations_total = Counter(
            'cache_operations_total',
            'Total cache operations',
            ['operation', 'cache_type', 'status'],
            registry=self.registry
        )
        
        # Cache hit/miss counters
        self.cache_hits_total = Counter(
            'cache_hits_total',
            'Total cache hits',
            ['cache_type', 'cache_level'],
            registry=self.registry
        )
        
        self.cache_misses_total = Counter(
            'cache_misses_total',
            'Total cache misses',
            ['cache_type', 'cache_level'],
            registry=self.registry
        )
        
        # Cache performance metrics
        self.cache_operation_duration = Histogram(
            'cache_operation_duration_seconds',
            'Cache operation duration',
            ['operation', 'cache_type'],
            registry=self.registry
        )
        
        self.cache_hit_ratio = Gauge(
            'cache_hit_ratio',
            'Cache hit ratio',
            ['cache_type'],
            registry=self.registry
        )
        
        self.cache_miss_ratio = Gauge(
            'cache_miss_ratio',
            'Cache miss ratio',
            ['cache_type'],
            registry=self.registry
        )
        
        # Cache size and memory metrics
        self.cache_size_bytes = Gauge(
            'cache_size_bytes',
            'Cache size in bytes',
            ['cache_type', 'cache_level'],
            registry=self.registry
        )
        
        self.cache_entries_total = Gauge(
            'cache_entries_total',
            'Total number of cache entries',
            ['cache_type', 'cache_level'],
            registry=self.registry
        )
        
        self.cache_memory_usage_bytes = Gauge(
            'cache_memory_usage_bytes',
            'Cache memory usage in bytes',
            ['cache_type', 'node_id'],
            registry=self.registry
        )
        
        # Cache eviction metrics
        self.cache_evictions_total = Counter(
            'cache_evictions_total',
            'Total cache evictions',
            ['cache_type', 'eviction_reason'],
            registry=self.registry
        )
        
        self.cache_eviction_rate = Gauge(
            'cache_eviction_rate',
            'Cache eviction rate per second',
            ['cache_type'],
            registry=self.registry
        )
        
        # Connection pool metrics
        self.cache_connection_pool_size = Gauge(
            'cache_connection_pool_size',
            'Cache connection pool size',
            ['node_id'],
            registry=self.registry
        )
        
        self.cache_connection_pool_active = Gauge(
            'cache_connection_pool_active',
            'Active connections in pool',
            ['node_id'],
            registry=self.registry
        )
        
        self.cache_connection_pool_available = Gauge(
            'cache_connection_pool_available',
            'Available connections in pool',
            ['node_id'],
            registry=self.registry
        )
        
        # Error metrics
        self.cache_errors_total = Counter(
            'cache_errors_total',
            'Total cache errors',
            ['cache_type', 'error_type'],
            registry=self.registry
        )
        
        # Node health metrics
        self.cache_node_health = Gauge(
            'cache_node_health',
            'Cache node health status (1=healthy, 0=unhealthy)',
            ['node_id', 'node_address'],
            registry=self.registry
        )
        
        self.cache_node_response_time = Histogram(
            'cache_node_response_time_seconds',
            'Cache node response time',
            ['node_id'],
            registry=self.registry
        )
        
        # Replication metrics
        self.cache_replication_lag = Histogram(
            'cache_replication_lag_seconds',
            'Cache replication lag',
            ['source_node', 'target_node'],
            registry=self.registry
        )
        
        # Cache warming metrics
        self.cache_warmup_operations_total = Counter(
            'cache_warmup_operations_total',
            'Total cache warmup operations',
            ['cache_type', 'status'],
            registry=self.registry
        )
        
        self.cache_warmup_duration = Histogram(
            'cache_warmup_duration_seconds',
            'Cache warmup duration',
            ['cache_type'],
            registry=self.registry
        )
        
        # Cache info
        self.cache_info = Info(
            'cache_info',
            'Cache configuration information',
            registry=self.registry
        )
    
    def record_operation(
        self,
        operation: str,
        cache_type: str,
        duration: float,
        status: str = "success"
    ) -> None:
        """Record cache operation metrics."""
        self.cache_operations_total.labels(
            operation=operation,
            cache_type=cache_type,
            status=status
        ).inc()
        
        self.cache_operation_duration.labels(
            operation=operation,
            cache_type=cache_type
        ).observe(duration)
        
        # Store for analysis
        self.operation_times[f"{operation}_{cache_type}"].append(duration)
        
        # Keep only recent data
        if len(self.operation_times[f"{operation}_{cache_type}"]) > 1000:
            self.operation_times[f"{operation}_{cache_type}"] = \
                self.operation_times[f"{operation}_{cache_type}"][-1000:]
    
    def record_hit(self, cache_type: str, cache_level: str = "unknown") -> None:
        """Record cache hit."""
        self.cache_hits_total.labels(
            cache_type=cache_type,
            cache_level=cache_level
        ).inc()
        
        self._update_hit_miss_ratio(cache_type, is_hit=True)
    
    def record_miss(self, cache_type: str, cache_level: str = "unknown") -> None:
        """Record cache miss."""
        self.cache_misses_total.labels(
            cache_type=cache_type,
            cache_level=cache_level
        ).inc()
        
        self._update_hit_miss_ratio(cache_type, is_hit=False)
    
    def _update_hit_miss_ratio(self, cache_type: str, is_hit: bool) -> None:
        """Update hit/miss ratio metrics."""
        # Get current counters
        hit_counter = self.cache_hits_total.labels(cache_type=cache_type, cache_level="unknown")
        miss_counter = self.cache_misses_total.labels(cache_type=cache_type, cache_level="unknown")
        
        # Calculate ratios
        total_ops = hit_counter._value.get() + miss_counter._value.get()
        
        if total_ops > 0:
            hit_ratio = hit_counter._value.get() / total_ops
            miss_ratio = miss_counter._value.get() / total_ops
            
            self.cache_hit_ratio.labels(cache_type=cache_type).set(hit_ratio)
            self.cache_miss_ratio.labels(cache_type=cache_type).set(miss_ratio)
            
            # Store for historical analysis
            self.hit_miss_ratios.append({
                'timestamp': time.time(),
                'cache_type': cache_type,
                'hit_ratio': hit_ratio,
                'miss_ratio': miss_ratio
            })
    
    def record_eviction(self, cache_type: str, reason: str = "lru") -> None:
        """Record cache eviction."""
        self.cache_evictions_total.labels(
            cache_type=cache_type,
            eviction_reason=reason
        ).inc()
    
    def record_error(self, cache_type: str, error_type: str) -> None:
        """Record cache error."""
        self.cache_errors_total.labels(
            cache_type=cache_type,
            error_type=error_type
        ).inc()
        
        self.error_counts[f"{cache_type}_{error_type}"] += 1
    
    def update_cache_size(self, cache_type: str, cache_level: str, size_bytes: int) -> None:
        """Update cache size metrics."""
        self.cache_size_bytes.labels(
            cache_type=cache_type,
            cache_level=cache_level
        ).set(size_bytes)
    
    def update_cache_entries(self, cache_type: str, cache_level: str, count: int) -> None:
        """Update cache entries count."""
        self.cache_entries_total.labels(
            cache_type=cache_type,
            cache_level=cache_level
        ).set(count)
    
    def update_node_health(self, node_id: str, node_address: str, is_healthy: bool) -> None:
        """Update node health status."""
        self.cache_node_health.labels(
            node_id=node_id,
            node_address=node_address
        ).set(1 if is_healthy else 0)
        
        # Store health history
        self.node_health_history[node_id].append({
            'timestamp': time.time(),
            'is_healthy': is_healthy
        })
    
    def record_node_response_time(self, node_id: str, response_time: float) -> None:
        """Record node response time."""
        self.cache_node_response_time.labels(node_id=node_id).observe(response_time)
    
    def update_connection_pool(self, node_id: str, total: int, active: int, available: int) -> None:
        """Update connection pool metrics."""
        self.cache_connection_pool_size.labels(node_id=node_id).set(total)
        self.cache_connection_pool_active.labels(node_id=node_id).set(active)
        self.cache_connection_pool_available.labels(node_id=node_id).set(available)
        
        # Store usage history
        self.connection_pool_usage.append({
            'timestamp': time.time(),
            'node_id': node_id,
            'usage_ratio': active / total if total > 0 else 0
        })
    
    def record_replication_lag(self, source_node: str, target_node: str, lag_seconds: float) -> None:
        """Record replication lag."""
        self.cache_replication_lag.labels(
            source_node=source_node,
            target_node=target_node
        ).observe(lag_seconds)
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get comprehensive metrics summary."""
        current_time = time.time()
        
        # Calculate averages and percentiles
        operation_stats = {}
        for op_type, times in self.operation_times.items():
            if times:
                operation_stats[op_type] = {
                    'avg': statistics.mean(times),
                    'p50': statistics.median(times),
                    'p95': statistics.quantiles(times, n=20)[18] if len(times) > 20 else max(times),
                    'p99': statistics.quantiles(times, n=100)[98] if len(times) > 100 else max(times),
                    'count': len(times)
                }
        
        # Calculate error rates
        error_rates = {}
        for error_type, count in self.error_counts.items():
            error_rates[error_type] = count
        
        # Calculate hit/miss ratios
        recent_ratios = list(self.hit_miss_ratios)[-10:]  # Last 10 samples
        avg_hit_ratio = statistics.mean([r['hit_ratio'] for r in recent_ratios]) if recent_ratios else 0
        
        return {
            'timestamp': current_time,
            'operation_stats': operation_stats,
            'error_rates': error_rates,
            'average_hit_ratio': avg_hit_ratio,
            'total_operations': sum(len(times) for times in self.operation_times.values()),
            'total_errors': sum(self.error_counts.values()),
            'node_health_summary': self._get_node_health_summary(),
            'connection_pool_summary': self._get_connection_pool_summary()
        }
    
    def _get_node_health_summary(self) -> Dict[str, Any]:
        """Get node health summary."""
        summary = {}
        
        for node_id, health_history in self.node_health_history.items():
            if health_history:
                recent_health = list(health_history)[-10:]  # Last 10 samples
                uptime_ratio = sum(h['is_healthy'] for h in recent_health) / len(recent_health)
                
                summary[node_id] = {
                    'uptime_ratio': uptime_ratio,
                    'current_status': recent_health[-1]['is_healthy'],
                    'sample_count': len(recent_health)
                }
        
        return summary
    
    def _get_connection_pool_summary(self) -> Dict[str, Any]:
        """Get connection pool usage summary."""
        if not self.connection_pool_usage:
            return {}
        
        recent_usage = list(self.connection_pool_usage)[-50:]  # Last 50 samples
        usage_by_node = defaultdict(list)
        
        for usage in recent_usage:
            usage_by_node[usage['node_id']].append(usage['usage_ratio'])
        
        summary = {}
        for node_id, ratios in usage_by_node.items():
            if ratios:
                summary[node_id] = {
                    'avg_usage': statistics.mean(ratios),
                    'max_usage': max(ratios),
                    'min_usage': min(ratios)
                }
        
        return summary
    
    def export_prometheus_metrics(self) -> str:
        """Export metrics in Prometheus format."""
        return generate_latest(self.registry)


class CacheMonitor:
    """
    Advanced cache monitoring with health checks, performance analysis,
    and automated issue detection.
    """
    
    def __init__(
        self,
        metrics: CacheMetrics,
        redis_client: Optional[RedisClientManager] = None,
        monitoring_interval: float = 30.0
    ):
        self.metrics = metrics
        self.redis_client = redis_client or get_redis_client()
        self.monitoring_interval = monitoring_interval
        
        # Monitoring tasks
        self._monitoring_task: Optional[asyncio.Task] = None
        self._health_check_task: Optional[asyncio.Task] = None
        self._performance_analysis_task: Optional[asyncio.Task] = None
        
        # Monitored caches
        self.monitored_caches: Dict[str, Union[CacheStrategy, MultiLevelCache]] = {}
        
        # Performance analysis
        self.performance_baselines: Dict[str, float] = {}
        self.performance_alerts: List[Dict[str, Any]] = []
        
        logger.info("Cache monitor initialized")
    
    async def start_monitoring(self) -> None:
        """Start cache monitoring tasks."""
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        self._health_check_task = asyncio.create_task(self._health_check_loop())
        self._performance_analysis_task = asyncio.create_task(self._performance_analysis_loop())
        
        logger.info("Cache monitoring started")
    
    async def stop_monitoring(self) -> None:
        """Stop cache monitoring tasks."""
        tasks = [
            self._monitoring_task,
            self._health_check_task,
            self._performance_analysis_task
        ]
        
        for task in tasks:
            if task:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        logger.info("Cache monitoring stopped")
    
    def register_cache(self, name: str, cache: Union[CacheStrategy, MultiLevelCache]) -> None:
        """Register cache for monitoring."""
        self.monitored_caches[name] = cache
        logger.info("Cache registered for monitoring", cache_name=name)
    
    def unregister_cache(self, name: str) -> None:
        """Unregister cache from monitoring."""
        if name in self.monitored_caches:
            del self.monitored_caches[name]
            logger.info("Cache unregistered from monitoring", cache_name=name)
    
    async def _monitoring_loop(self) -> None:
        """Main monitoring loop."""
        while True:
            try:
                await asyncio.sleep(self.monitoring_interval)
                await self._collect_metrics()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Monitoring loop error", error=str(e))
    
    async def _health_check_loop(self) -> None:
        """Health check loop."""
        while True:
            try:
                await asyncio.sleep(self.monitoring_interval * 2)  # Less frequent
                await self._perform_health_checks()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Health check loop error", error=str(e))
    
    async def _performance_analysis_loop(self) -> None:
        """Performance analysis loop."""
        while True:
            try:
                await asyncio.sleep(self.monitoring_interval * 4)  # Even less frequent
                await self._analyze_performance()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Performance analysis loop error", error=str(e))
    
    async def _collect_metrics(self) -> None:
        """Collect metrics from all monitored caches."""
        for cache_name, cache in self.monitored_caches.items():
            try:
                # Get cache statistics
                if hasattr(cache, 'get_stats'):
                    stats = await cache.get_stats()
                    
                    # Update metrics
                    if 'hits' in stats:
                        self.metrics.cache_hits_total.labels(
                            cache_type=cache_name,
                            cache_level="unknown"
                        ).inc(stats['hits'])
                    
                    if 'misses' in stats:
                        self.metrics.cache_misses_total.labels(
                            cache_type=cache_name,
                            cache_level="unknown"
                        ).inc(stats['misses'])
                    
                    if 'size' in stats:
                        self.metrics.cache_entries_total.labels(
                            cache_type=cache_name,
                            cache_level="l1"
                        ).set(stats['size'])
                
                # Multi-level cache specific metrics
                if isinstance(cache, MultiLevelCache):
                    multilevel_stats = await cache.get_stats()
                    
                    if 'l1' in multilevel_stats:
                        l1_stats = multilevel_stats['l1']
                        self.metrics.cache_entries_total.labels(
                            cache_type=cache_name,
                            cache_level="l1"
                        ).set(l1_stats.get('size', 0))
                    
                    if 'l2' in multilevel_stats:
                        l2_stats = multilevel_stats['l2']
                        self.metrics.cache_memory_usage_bytes.labels(
                            cache_type=cache_name,
                            node_id="redis"
                        ).set(l2_stats.get('memory_usage', 0))
            
            except Exception as e:
                logger.error("Failed to collect metrics", cache_name=cache_name, error=str(e))
                self.metrics.record_error(cache_name, "metrics_collection_failed")
    
    async def _perform_health_checks(self) -> None:
        """Perform health checks on cache infrastructure."""
        try:
            # Check Redis health
            redis_metrics = await self.redis_client.get_metrics()
            
            self.metrics.update_connection_pool(
                node_id="redis",
                total=redis_metrics.connection_pool_size,
                active=redis_metrics.active_connections,
                available=redis_metrics.available_connections
            )
            
            self.metrics.update_node_health(
                node_id="redis",
                node_address="localhost",
                is_healthy=self.redis_client.is_healthy
            )
            
            # Check cache responsiveness
            start_time = time.time()
            await self.redis_client.execute_command('ping')
            response_time = time.time() - start_time
            
            self.metrics.record_node_response_time("redis", response_time)
            
        except Exception as e:
            logger.error("Health check failed", error=str(e))
            self.metrics.record_error("redis", "health_check_failed")
    
    async def _analyze_performance(self) -> None:
        """Analyze cache performance and detect issues."""
        try:
            summary = self.metrics.get_metrics_summary()
            
            # Analyze operation performance
            for op_type, stats in summary['operation_stats'].items():
                # Check for performance degradation
                if op_type in self.performance_baselines:
                    baseline = self.performance_baselines[op_type]
                    current_avg = stats['avg']
                    
                    if current_avg > baseline * 1.5:  # 50% degradation
                        self.performance_alerts.append({
                            'type': 'performance_degradation',
                            'operation': op_type,
                            'baseline': baseline,
                            'current': current_avg,
                            'degradation': (current_avg - baseline) / baseline,
                            'timestamp': time.time()
                        })
                        
                        logger.warning(
                            "Performance degradation detected",
                            operation=op_type,
                            baseline=baseline,
                            current=current_avg
                        )
                else:
                    # Set baseline
                    self.performance_baselines[op_type] = stats['avg']
            
            # Analyze error rates
            total_ops = summary['total_operations']
            total_errors = summary['total_errors']
            
            if total_ops > 0:
                error_rate = total_errors / total_ops
                
                if error_rate > 0.05:  # 5% error rate
                    logger.warning(
                        "High error rate detected",
                        error_rate=error_rate,
                        total_operations=total_ops,
                        total_errors=total_errors
                    )
            
            # Clean up old alerts
            current_time = time.time()
            self.performance_alerts = [
                alert for alert in self.performance_alerts
                if current_time - alert['timestamp'] < 3600  # Keep for 1 hour
            ]
            
        except Exception as e:
            logger.error("Performance analysis failed", error=str(e))
    
    async def get_monitoring_report(self) -> Dict[str, Any]:
        """Get comprehensive monitoring report."""
        metrics_summary = self.metrics.get_metrics_summary()
        
        return {
            'timestamp': time.time(),
            'monitored_caches': list(self.monitored_caches.keys()),
            'metrics_summary': metrics_summary,
            'performance_alerts': self.performance_alerts,
            'performance_baselines': self.performance_baselines,
            'redis_health': self.redis_client.is_healthy,
            'monitoring_interval': self.monitoring_interval
        }


class CacheAlerting:
    """
    Advanced cache alerting system with configurable rules,
    severity levels, and notification channels.
    """
    
    def __init__(
        self,
        metrics: CacheMetrics,
        alert_handlers: Optional[List[Callable[[Alert], None]]] = None
    ):
        self.metrics = metrics
        self.alert_handlers = alert_handlers or []
        self.alert_rules: Dict[AlertType, AlertRule] = {}
        self.active_alerts: Dict[AlertType, Alert] = {}
        self.alert_history: List[Alert] = []
        self.alert_suppression: Dict[AlertType, float] = {}
        
        # Setup default alert rules
        self._setup_default_rules()
        
        # Alert checking task
        self._alert_task: Optional[asyncio.Task] = None
        
        logger.info("Cache alerting initialized")
    
    def _setup_default_rules(self) -> None:
        """Setup default alert rules."""
        self.alert_rules = {
            AlertType.CACHE_HIT_RATIO_LOW: AlertRule(
                alert_type=AlertType.CACHE_HIT_RATIO_LOW,
                threshold=0.7,  # 70% hit ratio
                duration=300,   # 5 minutes
                severity=AlertSeverity.MEDIUM,
                message="Cache hit ratio is below {threshold}%",
                cooldown=600    # 10 minutes
            ),
            
            AlertType.CACHE_LATENCY_HIGH: AlertRule(
                alert_type=AlertType.CACHE_LATENCY_HIGH,
                threshold=0.1,  # 100ms
                duration=180,   # 3 minutes
                severity=AlertSeverity.HIGH,
                message="Cache latency is above {threshold}ms",
                cooldown=300    # 5 minutes
            ),
            
            AlertType.CACHE_MEMORY_USAGE_HIGH: AlertRule(
                alert_type=AlertType.CACHE_MEMORY_USAGE_HIGH,
                threshold=0.85,  # 85% memory usage
                duration=300,    # 5 minutes
                severity=AlertSeverity.HIGH,
                message="Cache memory usage is above {threshold}%",
                cooldown=600     # 10 minutes
            ),
            
            AlertType.CACHE_ERROR_RATE_HIGH: AlertRule(
                alert_type=AlertType.CACHE_ERROR_RATE_HIGH,
                threshold=0.05,  # 5% error rate
                duration=120,    # 2 minutes
                severity=AlertSeverity.CRITICAL,
                message="Cache error rate is above {threshold}%",
                cooldown=300     # 5 minutes
            ),
            
            AlertType.CACHE_NODE_DOWN: AlertRule(
                alert_type=AlertType.CACHE_NODE_DOWN,
                threshold=0,     # Any node down
                duration=60,     # 1 minute
                severity=AlertSeverity.CRITICAL,
                message="Cache node is down",
                cooldown=300     # 5 minutes
            ),
            
            AlertType.CACHE_CONNECTION_POOL_EXHAUSTED: AlertRule(
                alert_type=AlertType.CACHE_CONNECTION_POOL_EXHAUSTED,
                threshold=0.95,  # 95% pool usage
                duration=60,     # 1 minute
                severity=AlertSeverity.HIGH,
                message="Cache connection pool is near exhaustion",
                cooldown=300     # 5 minutes
            )
        }
    
    def add_alert_handler(self, handler: Callable[[Alert], None]) -> None:
        """Add alert handler."""
        self.alert_handlers.append(handler)
    
    def add_alert_rule(self, rule: AlertRule) -> None:
        """Add custom alert rule."""
        self.alert_rules[rule.alert_type] = rule
    
    async def start_alerting(self) -> None:
        """Start alert checking task."""
        self._alert_task = asyncio.create_task(self._alert_checking_loop())
        logger.info("Cache alerting started")
    
    async def stop_alerting(self) -> None:
        """Stop alert checking task."""
        if self._alert_task:
            self._alert_task.cancel()
            try:
                await self._alert_task
            except asyncio.CancelledError:
                pass
        logger.info("Cache alerting stopped")
    
    async def _alert_checking_loop(self) -> None:
        """Main alert checking loop."""
        while True:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds
                await self._check_alerts()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Alert checking error", error=str(e))
    
    async def _check_alerts(self) -> None:
        """Check all alert rules and trigger alerts."""
        current_time = time.time()
        metrics_summary = self.metrics.get_metrics_summary()
        
        for alert_type, rule in self.alert_rules.items():
            if not rule.enabled:
                continue
            
            try:
                should_alert, value = await self._evaluate_alert_rule(rule, metrics_summary)
                
                if should_alert and rule.should_trigger(current_time):
                    alert = Alert(
                        alert_type=alert_type,
                        severity=rule.severity,
                        message=rule.message.format(threshold=rule.threshold),
                        value=value,
                        threshold=rule.threshold,
                        timestamp=current_time,
                        metadata=self._get_alert_metadata(alert_type, metrics_summary)
                    )
                    
                    await self._trigger_alert(alert)
                    rule.last_triggered = current_time
                    self.active_alerts[alert_type] = alert
                    
                elif not should_alert and alert_type in self.active_alerts:
                    # Clear alert
                    await self._clear_alert(alert_type)
                    
            except Exception as e:
                logger.error("Alert rule evaluation failed", alert_type=alert_type.value, error=str(e))
    
    async def _evaluate_alert_rule(self, rule: AlertRule, metrics_summary: Dict[str, Any]) -> Tuple[bool, float]:
        """Evaluate alert rule against current metrics."""
        if rule.alert_type == AlertType.CACHE_HIT_RATIO_LOW:
            hit_ratio = metrics_summary.get('average_hit_ratio', 1.0)
            return hit_ratio < rule.threshold, hit_ratio
        
        elif rule.alert_type == AlertType.CACHE_LATENCY_HIGH:
            # Check average latency across all operations
            max_latency = 0
            for op_stats in metrics_summary.get('operation_stats', {}).values():
                max_latency = max(max_latency, op_stats.get('avg', 0))
            return max_latency > rule.threshold, max_latency
        
        elif rule.alert_type == AlertType.CACHE_ERROR_RATE_HIGH:
            total_ops = metrics_summary.get('total_operations', 0)
            total_errors = metrics_summary.get('total_errors', 0)
            error_rate = total_errors / total_ops if total_ops > 0 else 0
            return error_rate > rule.threshold, error_rate
        
        elif rule.alert_type == AlertType.CACHE_NODE_DOWN:
            node_health = metrics_summary.get('node_health_summary', {})
            for node_id, health in node_health.items():
                if not health.get('current_status', True):
                    return True, 0
            return False, 1
        
        elif rule.alert_type == AlertType.CACHE_CONNECTION_POOL_EXHAUSTED:
            pool_summary = metrics_summary.get('connection_pool_summary', {})
            for node_id, pool_stats in pool_summary.items():
                if pool_stats.get('max_usage', 0) > rule.threshold:
                    return True, pool_stats['max_usage']
            return False, 0
        
        return False, 0
    
    def _get_alert_metadata(self, alert_type: AlertType, metrics_summary: Dict[str, Any]) -> Dict[str, Any]:
        """Get additional metadata for alert."""
        metadata = {
            'total_operations': metrics_summary.get('total_operations', 0),
            'total_errors': metrics_summary.get('total_errors', 0),
            'timestamp': time.time()
        }
        
        if alert_type == AlertType.CACHE_HIT_RATIO_LOW:
            metadata['hit_ratio'] = metrics_summary.get('average_hit_ratio', 0)
        
        elif alert_type == AlertType.CACHE_NODE_DOWN:
            metadata['node_health'] = metrics_summary.get('node_health_summary', {})
        
        elif alert_type == AlertType.CACHE_CONNECTION_POOL_EXHAUSTED:
            metadata['pool_usage'] = metrics_summary.get('connection_pool_summary', {})
        
        return metadata
    
    async def _trigger_alert(self, alert: Alert) -> None:
        """Trigger alert and notify handlers."""
        logger.warning(
            "Cache alert triggered",
            alert_type=alert.alert_type.value,
            severity=alert.severity.value,
            message=alert.message,
            value=alert.value,
            threshold=alert.threshold
        )
        
        # Store in history
        self.alert_history.append(alert)
        
        # Keep only recent alerts
        if len(self.alert_history) > 1000:
            self.alert_history = self.alert_history[-1000:]
        
        # Notify handlers
        for handler in self.alert_handlers:
            try:
                handler(alert)
            except Exception as e:
                logger.error("Alert handler failed", error=str(e))
    
    async def _clear_alert(self, alert_type: AlertType) -> None:
        """Clear active alert."""
        if alert_type in self.active_alerts:
            del self.active_alerts[alert_type]
            logger.info("Cache alert cleared", alert_type=alert_type.value)
    
    def get_alert_status(self) -> Dict[str, Any]:
        """Get current alert status."""
        return {
            'active_alerts': {
                alert_type.value: alert.to_dict()
                for alert_type, alert in self.active_alerts.items()
            },
            'alert_rules': {
                alert_type.value: {
                    'threshold': rule.threshold,
                    'duration': rule.duration,
                    'severity': rule.severity.value,
                    'enabled': rule.enabled,
                    'last_triggered': rule.last_triggered
                }
                for alert_type, rule in self.alert_rules.items()
            },
            'recent_alerts': [
                alert.to_dict() for alert in self.alert_history[-10:]
            ]
        }


# Global monitoring instances
_cache_metrics: Optional[CacheMetrics] = None
_cache_monitor: Optional[CacheMonitor] = None
_cache_alerting: Optional[CacheAlerting] = None


def get_cache_metrics() -> CacheMetrics:
    """Get global cache metrics instance."""
    global _cache_metrics
    if _cache_metrics is None:
        _cache_metrics = CacheMetrics()
    return _cache_metrics


def get_cache_monitor() -> CacheMonitor:
    """Get global cache monitor instance."""
    global _cache_monitor
    if _cache_monitor is None:
        _cache_monitor = CacheMonitor(get_cache_metrics())
    return _cache_monitor


def get_cache_alerting() -> CacheAlerting:
    """Get global cache alerting instance."""
    global _cache_alerting
    if _cache_alerting is None:
        _cache_alerting = CacheAlerting(get_cache_metrics())
    return _cache_alerting


async def initialize_cache_monitoring() -> None:
    """Initialize cache monitoring system."""
    monitor = get_cache_monitor()
    alerting = get_cache_alerting()
    
    await monitor.start_monitoring()
    await alerting.start_alerting()
    
    logger.info("Cache monitoring system initialized")


async def shutdown_cache_monitoring() -> None:
    """Shutdown cache monitoring system."""
    global _cache_monitor, _cache_alerting
    
    if _cache_monitor:
        await _cache_monitor.stop_monitoring()
    
    if _cache_alerting:
        await _cache_alerting.stop_alerting()
    
    logger.info("Cache monitoring system shutdown")