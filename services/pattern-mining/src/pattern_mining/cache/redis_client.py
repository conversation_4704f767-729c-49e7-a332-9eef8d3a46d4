"""
Redis Client Manager

High-performance Redis client with connection pooling, monitoring, and cluster support.
Optimized for Redis 7.0+ with clustering and persistence features.
"""

import asyncio
import hashlib
import time
from typing import Any, Dict, List, Optional, Union, Tuple, Set
from functools import lru_cache
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from enum import Enum
import json
import ssl

import redis.asyncio as redis
from redis.exceptions import (
    ConnectionError,
    TimeoutError,
    RedisError,
    ClusterError,
    ResponseError
)
import structlog
from prometheus_client import Counter, Histogram, Gauge

from ..config.settings import get_settings
from ..monitoring.prometheus_metrics import PrometheusMetrics

logger = structlog.get_logger()


class RedisMode(Enum):
    """Redis deployment modes."""
    STANDALONE = "standalone"
    CLUSTER = "cluster"
    SENTINEL = "sentinel"


@dataclass
class RedisMetrics:
    """Redis client metrics."""
    connection_pool_size: int = 0
    active_connections: int = 0
    available_connections: int = 0
    total_commands: int = 0
    failed_commands: int = 0
    avg_response_time: float = 0.0
    cache_hit_ratio: float = 0.0
    memory_usage: int = 0
    connected_clients: int = 0
    keyspace_hits: int = 0
    keyspace_misses: int = 0
    expired_keys: int = 0
    evicted_keys: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary."""
        return {
            "connection_pool_size": self.connection_pool_size,
            "active_connections": self.active_connections,
            "available_connections": self.available_connections,
            "total_commands": self.total_commands,
            "failed_commands": self.failed_commands,
            "avg_response_time": self.avg_response_time,
            "cache_hit_ratio": self.cache_hit_ratio,
            "memory_usage": self.memory_usage,
            "connected_clients": self.connected_clients,
            "keyspace_hits": self.keyspace_hits,
            "keyspace_misses": self.keyspace_misses,
            "expired_keys": self.expired_keys,
            "evicted_keys": self.evicted_keys,
        }


@dataclass
class RedisConfig:
    """Redis configuration."""
    url: str
    mode: RedisMode = RedisMode.STANDALONE
    max_connections: int = 50
    max_connections_per_node: int = 10
    connection_timeout: float = 5.0
    socket_timeout: float = 5.0
    socket_keepalive: bool = True
    socket_keepalive_options: Dict[str, int] = field(default_factory=dict)
    health_check_interval: float = 30.0
    retry_on_timeout: bool = True
    retry_on_error: List[type] = field(default_factory=lambda: [ConnectionError, TimeoutError])
    max_retries: int = 3
    retry_backoff_factor: float = 1.0
    decode_responses: bool = True
    skip_full_coverage_check: bool = False
    readonly_mode: bool = False
    cluster_nodes: List[str] = field(default_factory=list)
    sentinel_service_name: str = "mymaster"
    sentinel_password: Optional[str] = None
    password: Optional[str] = None
    db: int = 0
    ssl: bool = False
    ssl_ca_certs: Optional[str] = None
    ssl_cert_reqs: str = "required"
    compression: bool = False
    connection_pool_class: Optional[type] = None
    
    def to_redis_kwargs(self) -> Dict[str, Any]:
        """Convert config to Redis client kwargs."""
        kwargs = {
            "socket_timeout": self.socket_timeout,
            "socket_keepalive": self.socket_keepalive,
            "socket_keepalive_options": self.socket_keepalive_options,
            "health_check_interval": self.health_check_interval,
            "retry_on_timeout": self.retry_on_timeout,
            "retry_on_error": self.retry_on_error,
            "max_connections": self.max_connections,
            "decode_responses": self.decode_responses,
            "password": self.password,
            "db": self.db,
        }
        
        if self.ssl:
            kwargs.update({
                "ssl": True,
                "ssl_ca_certs": self.ssl_ca_certs,
                "ssl_cert_reqs": getattr(ssl, self.ssl_cert_reqs.upper()),
            })
        
        if self.connection_pool_class:
            kwargs["connection_pool_class"] = self.connection_pool_class
            
        return kwargs


class RedisClientManager:
    """
    Advanced Redis client manager with connection pooling, monitoring, and failover.
    Supports standalone, cluster, and sentinel modes.
    """
    
    def __init__(self, config: Optional[RedisConfig] = None):
        self.config = config or self._get_default_config()
        self.client: Optional[redis.Redis] = None
        self.metrics = RedisMetrics()
        self.prometheus_metrics = PrometheusMetrics()
        self.is_healthy = False
        self.last_health_check = 0.0
        self._health_check_task: Optional[asyncio.Task] = None
        self._connection_lock = asyncio.Lock()
        
        # Prometheus metrics
        self.redis_commands_total = Counter(
            'redis_commands_total',
            'Total number of Redis commands executed',
            ['command', 'status']
        )
        
        self.redis_command_duration = Histogram(
            'redis_command_duration_seconds',
            'Time spent on Redis commands',
            ['command']
        )
        
        self.redis_connections_active = Gauge(
            'redis_connections_active',
            'Number of active Redis connections'
        )
        
        self.redis_pool_size = Gauge(
            'redis_pool_size',
            'Redis connection pool size'
        )
        
        self.redis_cache_hit_ratio = Gauge(
            'redis_cache_hit_ratio',
            'Redis cache hit ratio'
        )
    
    @classmethod
    def _get_default_config(cls) -> RedisConfig:
        """Get default Redis configuration from settings."""
        settings = get_settings()
        return RedisConfig(
            url=settings.redis_url,
            max_connections=settings.redis_max_connections,
            mode=RedisMode.STANDALONE,
        )
    
    async def initialize(self) -> None:
        """Initialize Redis client and start health monitoring."""
        async with self._connection_lock:
            if self.client is None:
                await self._create_client()
                await self._start_health_monitoring()
                logger.info("Redis client initialized", mode=self.config.mode.value)
    
    async def _create_client(self) -> None:
        """Create Redis client based on configuration."""
        try:
            if self.config.mode == RedisMode.STANDALONE:
                self.client = redis.from_url(
                    self.config.url,
                    **self.config.to_redis_kwargs()
                )
            elif self.config.mode == RedisMode.CLUSTER:
                # Redis Cluster client
                from redis.asyncio.cluster import RedisCluster
                self.client = RedisCluster(
                    startup_nodes=self.config.cluster_nodes or [self.config.url],
                    skip_full_coverage_check=self.config.skip_full_coverage_check,
                    readonly_mode=self.config.readonly_mode,
                    max_connections_per_node=self.config.max_connections_per_node,
                    **self.config.to_redis_kwargs()
                )
            elif self.config.mode == RedisMode.SENTINEL:
                # Redis Sentinel client
                from redis.asyncio.sentinel import Sentinel
                sentinels = [(host, int(port)) for host, port in 
                           [node.split(':') for node in self.config.cluster_nodes]]
                sentinel = Sentinel(
                    sentinels,
                    sentinel_kwargs={'password': self.config.sentinel_password}
                )
                self.client = sentinel.master_for(
                    self.config.sentinel_service_name,
                    **self.config.to_redis_kwargs()
                )
            else:
                raise ValueError(f"Unsupported Redis mode: {self.config.mode}")
            
            # Test connection
            await self.client.ping()
            self.is_healthy = True
            
        except Exception as e:
            logger.error("Failed to create Redis client", error=str(e))
            raise
    
    async def _start_health_monitoring(self) -> None:
        """Start background health monitoring."""
        if self._health_check_task is None:
            self._health_check_task = asyncio.create_task(self._health_check_loop())
    
    async def _health_check_loop(self) -> None:
        """Background health check loop."""
        while True:
            try:
                await asyncio.sleep(self.config.health_check_interval)
                await self._perform_health_check()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Health check failed", error=str(e))
                self.is_healthy = False
    
    async def _perform_health_check(self) -> None:
        """Perform health check and update metrics."""
        try:
            if not self.client:
                self.is_healthy = False
                return
            
            start_time = time.time()
            
            # Basic ping test
            await self.client.ping()
            
            # Get Redis info
            info = await self.client.info()
            
            # Update metrics
            self.metrics.memory_usage = info.get('used_memory', 0)
            self.metrics.connected_clients = info.get('connected_clients', 0)
            self.metrics.keyspace_hits = info.get('keyspace_hits', 0)
            self.metrics.keyspace_misses = info.get('keyspace_misses', 0)
            self.metrics.expired_keys = info.get('expired_keys', 0)
            self.metrics.evicted_keys = info.get('evicted_keys', 0)
            
            # Calculate cache hit ratio
            total_lookups = self.metrics.keyspace_hits + self.metrics.keyspace_misses
            if total_lookups > 0:
                self.metrics.cache_hit_ratio = self.metrics.keyspace_hits / total_lookups
            
            # Get connection pool stats
            if hasattr(self.client, 'connection_pool'):
                pool = self.client.connection_pool
                self.metrics.connection_pool_size = pool.max_connections
                self.metrics.active_connections = len(pool._available_connections)
                self.metrics.available_connections = len(pool._available_connections)
            
            # Update Prometheus metrics
            self.redis_connections_active.set(self.metrics.active_connections)
            self.redis_pool_size.set(self.metrics.connection_pool_size)
            self.redis_cache_hit_ratio.set(self.metrics.cache_hit_ratio)
            
            self.is_healthy = True
            self.last_health_check = time.time()
            
        except Exception as e:
            logger.error("Health check failed", error=str(e))
            self.is_healthy = False
    
    async def execute_command(self, command: str, *args, **kwargs) -> Any:
        """Execute Redis command with monitoring and error handling."""
        if not self.client:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            # Execute command with retries
            result = await self._execute_with_retries(command, *args, **kwargs)
            
            # Update metrics
            self.metrics.total_commands += 1
            execution_time = time.time() - start_time
            self.metrics.avg_response_time = (
                (self.metrics.avg_response_time * (self.metrics.total_commands - 1) + execution_time) /
                self.metrics.total_commands
            )
            
            # Update Prometheus metrics
            self.redis_commands_total.labels(command=command, status='success').inc()
            self.redis_command_duration.labels(command=command).observe(execution_time)
            
            return result
            
        except Exception as e:
            self.metrics.failed_commands += 1
            self.redis_commands_total.labels(command=command, status='error').inc()
            logger.error("Redis command failed", command=command, error=str(e))
            raise
    
    async def _execute_with_retries(self, command: str, *args, **kwargs) -> Any:
        """Execute Redis command with retry logic."""
        for attempt in range(self.config.max_retries + 1):
            try:
                cmd_func = getattr(self.client, command)
                return await cmd_func(*args, **kwargs)
                
            except Exception as e:
                if attempt == self.config.max_retries:
                    raise
                
                if not any(isinstance(e, error_type) for error_type in self.config.retry_on_error):
                    raise
                
                backoff_time = self.config.retry_backoff_factor * (2 ** attempt)
                logger.warning(
                    "Redis command failed, retrying",
                    command=command,
                    attempt=attempt + 1,
                    backoff=backoff_time,
                    error=str(e)
                )
                await asyncio.sleep(backoff_time)
    
    async def get(self, key: str, default: Any = None) -> Any:
        """Get value from Redis."""
        try:
            value = await self.execute_command('get', key)
            if value is None:
                return default
            return json.loads(value) if isinstance(value, str) else value
        except (json.JSONDecodeError, TypeError):
            return value
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ex: Optional[int] = None,
        px: Optional[int] = None,
        nx: bool = False,
        xx: bool = False
    ) -> bool:
        """Set value in Redis."""
        try:
            serialized_value = json.dumps(value) if not isinstance(value, (str, bytes)) else value
            return await self.execute_command('set', key, serialized_value, ex=ex, px=px, nx=nx, xx=xx)
        except Exception:
            return False
    
    async def delete(self, *keys: str) -> int:
        """Delete keys from Redis."""
        return await self.execute_command('delete', *keys)
    
    async def exists(self, *keys: str) -> int:
        """Check if keys exist in Redis."""
        return await self.execute_command('exists', *keys)
    
    async def expire(self, key: str, time: int) -> bool:
        """Set expiration time for key."""
        return await self.execute_command('expire', key, time)
    
    async def ttl(self, key: str) -> int:
        """Get time to live for key."""
        return await self.execute_command('ttl', key)
    
    async def incr(self, key: str, amount: int = 1) -> int:
        """Increment value in Redis."""
        return await self.execute_command('incrby', key, amount)
    
    async def decr(self, key: str, amount: int = 1) -> int:
        """Decrement value in Redis."""
        return await self.execute_command('decrby', key, amount)
    
    async def mget(self, *keys: str) -> List[Any]:
        """Get multiple values from Redis."""
        values = await self.execute_command('mget', *keys)
        result = []
        for value in values:
            if value is None:
                result.append(None)
            else:
                try:
                    result.append(json.loads(value))
                except (json.JSONDecodeError, TypeError):
                    result.append(value)
        return result
    
    async def mset(self, mapping: Dict[str, Any]) -> bool:
        """Set multiple values in Redis."""
        serialized_mapping = {}
        for key, value in mapping.items():
            serialized_mapping[key] = json.dumps(value) if not isinstance(value, (str, bytes)) else value
        return await self.execute_command('mset', serialized_mapping)
    
    async def hget(self, name: str, key: str) -> Any:
        """Get hash field value."""
        value = await self.execute_command('hget', name, key)
        if value is None:
            return None
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            return value
    
    async def hset(self, name: str, key: str, value: Any) -> int:
        """Set hash field value."""
        serialized_value = json.dumps(value) if not isinstance(value, (str, bytes)) else value
        return await self.execute_command('hset', name, key, serialized_value)
    
    async def hgetall(self, name: str) -> Dict[str, Any]:
        """Get all hash fields."""
        result = await self.execute_command('hgetall', name)
        parsed_result = {}
        for key, value in result.items():
            try:
                parsed_result[key] = json.loads(value)
            except (json.JSONDecodeError, TypeError):
                parsed_result[key] = value
        return parsed_result
    
    async def hmset(self, name: str, mapping: Dict[str, Any]) -> bool:
        """Set multiple hash fields."""
        serialized_mapping = {}
        for key, value in mapping.items():
            serialized_mapping[key] = json.dumps(value) if not isinstance(value, (str, bytes)) else value
        return await self.execute_command('hmset', name, serialized_mapping)
    
    async def sadd(self, name: str, *values: Any) -> int:
        """Add values to set."""
        serialized_values = []
        for value in values:
            serialized_values.append(json.dumps(value) if not isinstance(value, (str, bytes)) else value)
        return await self.execute_command('sadd', name, *serialized_values)
    
    async def smembers(self, name: str) -> Set[Any]:
        """Get all set members."""
        members = await self.execute_command('smembers', name)
        result = set()
        for member in members:
            try:
                result.add(json.loads(member))
            except (json.JSONDecodeError, TypeError):
                result.add(member)
        return result
    
    async def sismember(self, name: str, value: Any) -> bool:
        """Check if value is in set."""
        serialized_value = json.dumps(value) if not isinstance(value, (str, bytes)) else value
        return await self.execute_command('sismember', name, serialized_value)
    
    async def lpush(self, name: str, *values: Any) -> int:
        """Push values to list head."""
        serialized_values = []
        for value in values:
            serialized_values.append(json.dumps(value) if not isinstance(value, (str, bytes)) else value)
        return await self.execute_command('lpush', name, *serialized_values)
    
    async def rpush(self, name: str, *values: Any) -> int:
        """Push values to list tail."""
        serialized_values = []
        for value in values:
            serialized_values.append(json.dumps(value) if not isinstance(value, (str, bytes)) else value)
        return await self.execute_command('rpush', name, *serialized_values)
    
    async def lpop(self, name: str) -> Any:
        """Pop value from list head."""
        value = await self.execute_command('lpop', name)
        if value is None:
            return None
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            return value
    
    async def rpop(self, name: str) -> Any:
        """Pop value from list tail."""
        value = await self.execute_command('rpop', name)
        if value is None:
            return None
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            return value
    
    async def lrange(self, name: str, start: int, end: int) -> List[Any]:
        """Get list range."""
        values = await self.execute_command('lrange', name, start, end)
        result = []
        for value in values:
            try:
                result.append(json.loads(value))
            except (json.JSONDecodeError, TypeError):
                result.append(value)
        return result
    
    @asynccontextmanager
    async def pipeline(self, transaction: bool = True):
        """Get Redis pipeline for batch operations."""
        if not self.client:
            await self.initialize()
        
        pipeline = self.client.pipeline(transaction=transaction)
        try:
            yield pipeline
        finally:
            await pipeline.reset()
    
    async def get_metrics(self) -> RedisMetrics:
        """Get current Redis metrics."""
        await self._perform_health_check()
        return self.metrics
    
    async def flush_all(self) -> bool:
        """Flush all Redis data (use with caution)."""
        return await self.execute_command('flushall')
    
    async def close(self) -> None:
        """Close Redis client and cleanup resources."""
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        if self.client:
            await self.client.close()
            self.client = None
        
        logger.info("Redis client closed")


class RedisClusterManager(RedisClientManager):
    """Redis Cluster-specific client manager."""
    
    def __init__(self, cluster_nodes: List[str], **kwargs):
        config = RedisConfig(
            url="",  # Not used in cluster mode
            mode=RedisMode.CLUSTER,
            cluster_nodes=cluster_nodes,
            **kwargs
        )
        super().__init__(config)
    
    async def get_cluster_info(self) -> Dict[str, Any]:
        """Get Redis cluster information."""
        return await self.execute_command('cluster_info')
    
    async def get_cluster_nodes(self) -> Dict[str, Any]:
        """Get Redis cluster nodes information."""
        return await self.execute_command('cluster_nodes')
    
    async def get_cluster_slots(self) -> List[List[Any]]:
        """Get Redis cluster slots information."""
        return await self.execute_command('cluster_slots')


# Global Redis client instance
_redis_client: Optional[RedisClientManager] = None


@lru_cache()
def get_redis_client() -> RedisClientManager:
    """Get global Redis client instance."""
    global _redis_client
    if _redis_client is None:
        _redis_client = RedisClientManager()
    return _redis_client


async def init_redis_client(config: Optional[RedisConfig] = None) -> RedisClientManager:
    """Initialize global Redis client."""
    global _redis_client
    _redis_client = RedisClientManager(config)
    await _redis_client.initialize()
    return _redis_client


async def close_redis_client() -> None:
    """Close global Redis client."""
    global _redis_client
    if _redis_client:
        await _redis_client.close()
        _redis_client = None