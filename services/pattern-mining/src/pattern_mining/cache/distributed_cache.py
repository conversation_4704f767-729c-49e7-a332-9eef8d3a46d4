"""
Distributed Cache

Advanced distributed caching with consistent hashing, sharding, 
cache synchronization, and distributed consistency management.
"""

import asyncio
import hashlib
import json
import time
from typing import Any, Dict, List, Optional, Set, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict
import random
import bisect
from contextlib import asynccontextmanager

import structlog
from prometheus_client import Counter, Histogram, Gauge

from .redis_client import RedisClientManager, RedisConfig, RedisMode
from .strategies import CacheStrategy

logger = structlog.get_logger()


class ConsistencyLevel(Enum):
    """Distributed cache consistency levels."""
    EVENTUAL = "eventual"
    STRONG = "strong"
    WEAK = "weak"
    CAUSAL = "causal"


class ReplicationStrategy(Enum):
    """Cache replication strategies."""
    NONE = "none"
    MASTER_SLAVE = "master_slave"
    MASTER_MASTER = "master_master"
    RING = "ring"


@dataclass
class CacheNode:
    """Cache node configuration."""
    node_id: str
    host: str
    port: int
    weight: int = 1
    is_master: bool = False
    is_healthy: bool = True
    last_health_check: float = 0.0
    redis_client: Optional[RedisClientManager] = None
    
    def __post_init__(self):
        """Initialize Redis client after creation."""
        if self.redis_client is None:
            config = RedisConfig(
                url=f"redis://{self.host}:{self.port}",
                mode=RedisMode.STANDALONE,
                health_check_interval=30.0
            )
            self.redis_client = RedisClientManager(config)
    
    @property
    def address(self) -> str:
        """Get node address."""
        return f"{self.host}:{self.port}"
    
    async def initialize(self) -> None:
        """Initialize node connection."""
        if self.redis_client:
            await self.redis_client.initialize()
    
    async def health_check(self) -> bool:
        """Perform health check on node."""
        try:
            if self.redis_client:
                await self.redis_client.execute_command('ping')
                self.is_healthy = True
                self.last_health_check = time.time()
                return True
        except Exception as e:
            logger.error("Node health check failed", node_id=self.node_id, error=str(e))
            self.is_healthy = False
        return False
    
    async def close(self) -> None:
        """Close node connection."""
        if self.redis_client:
            await self.redis_client.close()


class ConsistentHashRing:
    """Consistent hash ring for distributed caching."""
    
    def __init__(self, nodes: List[CacheNode], virtual_nodes: int = 150):
        self.nodes = {node.node_id: node for node in nodes}
        self.virtual_nodes = virtual_nodes
        self.ring: Dict[int, str] = {}
        self.sorted_keys: List[int] = []
        self._build_ring()
    
    def _hash(self, key: str) -> int:
        """Hash function for consistent hashing."""
        return int(hashlib.md5(key.encode()).hexdigest(), 16)
    
    def _build_ring(self) -> None:
        """Build the consistent hash ring."""
        self.ring.clear()
        self.sorted_keys.clear()
        
        for node_id, node in self.nodes.items():
            for i in range(self.virtual_nodes * node.weight):
                virtual_key = f"{node_id}:{i}"
                hash_value = self._hash(virtual_key)
                self.ring[hash_value] = node_id
                self.sorted_keys.append(hash_value)
        
        self.sorted_keys.sort()
    
    def get_node(self, key: str) -> Optional[CacheNode]:
        """Get node responsible for key."""
        if not self.ring:
            return None
        
        hash_value = self._hash(key)
        
        # Find the first node with hash >= hash_value
        idx = bisect.bisect_right(self.sorted_keys, hash_value)
        
        if idx == len(self.sorted_keys):
            idx = 0
        
        node_id = self.ring[self.sorted_keys[idx]]
        return self.nodes.get(node_id)
    
    def get_nodes(self, key: str, count: int) -> List[CacheNode]:
        """Get multiple nodes for replication."""
        if not self.ring or count <= 0:
            return []
        
        hash_value = self._hash(key)
        idx = bisect.bisect_right(self.sorted_keys, hash_value)
        
        nodes = []
        seen_nodes = set()
        
        for i in range(len(self.sorted_keys)):
            current_idx = (idx + i) % len(self.sorted_keys)
            node_id = self.ring[self.sorted_keys[current_idx]]
            
            if node_id not in seen_nodes:
                node = self.nodes.get(node_id)
                if node and node.is_healthy:
                    nodes.append(node)
                    seen_nodes.add(node_id)
                    
                    if len(nodes) >= count:
                        break
        
        return nodes
    
    def add_node(self, node: CacheNode) -> None:
        """Add node to ring."""
        self.nodes[node.node_id] = node
        self._build_ring()
    
    def remove_node(self, node_id: str) -> None:
        """Remove node from ring."""
        if node_id in self.nodes:
            del self.nodes[node_id]
            self._build_ring()
    
    def get_all_nodes(self) -> List[CacheNode]:
        """Get all nodes in ring."""
        return list(self.nodes.values())
    
    def get_healthy_nodes(self) -> List[CacheNode]:
        """Get all healthy nodes."""
        return [node for node in self.nodes.values() if node.is_healthy]


class DistributedCache:
    """
    Distributed cache implementation with consistent hashing,
    replication, and configurable consistency levels.
    """
    
    def __init__(
        self,
        nodes: List[CacheNode],
        replication_factor: int = 2,
        consistency_level: ConsistencyLevel = ConsistencyLevel.EVENTUAL,
        replication_strategy: ReplicationStrategy = ReplicationStrategy.RING,
        read_repair: bool = True,
        write_timeout: float = 5.0,
        read_timeout: float = 3.0
    ):
        self.nodes = nodes
        self.replication_factor = replication_factor
        self.consistency_level = consistency_level
        self.replication_strategy = replication_strategy
        self.read_repair = read_repair
        self.write_timeout = write_timeout
        self.read_timeout = read_timeout
        
        self.hash_ring = ConsistentHashRing(nodes)
        self.node_failure_detector = NodeFailureDetector(nodes)
        
        # Prometheus metrics
        self.distributed_cache_operations = Counter(
            'distributed_cache_operations_total',
            'Total distributed cache operations',
            ['operation', 'status', 'consistency_level']
        )
        
        self.distributed_cache_latency = Histogram(
            'distributed_cache_latency_seconds',
            'Distributed cache operation latency',
            ['operation', 'consistency_level']
        )
        
        self.cache_replication_lag = Histogram(
            'cache_replication_lag_seconds',
            'Cache replication lag between nodes'
        )
        
        self.healthy_nodes_count = Gauge(
            'distributed_cache_healthy_nodes',
            'Number of healthy cache nodes'
        )
        
        # Start background tasks
        self._health_check_task: Optional[asyncio.Task] = None
        self._start_health_monitoring()
    
    async def initialize(self) -> None:
        """Initialize all cache nodes."""
        tasks = [node.initialize() for node in self.nodes]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # Initial health check
        await self._perform_health_checks()
        
        logger.info(
            "Distributed cache initialized",
            node_count=len(self.nodes),
            healthy_nodes=len(self.hash_ring.get_healthy_nodes()),
            replication_factor=self.replication_factor,
            consistency_level=self.consistency_level.value
        )
    
    def _start_health_monitoring(self) -> None:
        """Start background health monitoring."""
        if self._health_check_task is None:
            self._health_check_task = asyncio.create_task(self._health_monitoring_loop())
    
    async def _health_monitoring_loop(self) -> None:
        """Background health monitoring loop."""
        while True:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds
                await self._perform_health_checks()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Health monitoring failed", error=str(e))
    
    async def _perform_health_checks(self) -> None:
        """Perform health checks on all nodes."""
        tasks = [node.health_check() for node in self.nodes]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        healthy_count = sum(1 for node in self.nodes if node.is_healthy)
        self.healthy_nodes_count.set(healthy_count)
        
        # Log unhealthy nodes
        for node, result in zip(self.nodes, results):
            if not node.is_healthy:
                logger.warning("Node unhealthy", node_id=node.node_id, address=node.address)
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from distributed cache."""
        start_time = time.time()
        
        try:
            if self.consistency_level == ConsistencyLevel.STRONG:
                result = await self._get_strong_consistency(key)
            elif self.consistency_level == ConsistencyLevel.EVENTUAL:
                result = await self._get_eventual_consistency(key)
            else:
                result = await self._get_weak_consistency(key)
            
            # Record metrics
            status = 'hit' if result is not None else 'miss'
            self.distributed_cache_operations.labels(
                operation='get',
                status=status,
                consistency_level=self.consistency_level.value
            ).inc()
            
            self.distributed_cache_latency.labels(
                operation='get',
                consistency_level=self.consistency_level.value
            ).observe(time.time() - start_time)
            
            return result
            
        except Exception as e:
            self.distributed_cache_operations.labels(
                operation='get',
                status='error',
                consistency_level=self.consistency_level.value
            ).inc()
            logger.error("Distributed cache get failed", key=key, error=str(e))
            return None
    
    async def _get_strong_consistency(self, key: str) -> Optional[Any]:
        """Get with strong consistency (read from majority)."""
        nodes = self.hash_ring.get_nodes(key, self.replication_factor)
        
        if not nodes:
            return None
        
        # Read from majority of nodes
        required_reads = (len(nodes) // 2) + 1
        
        tasks = [
            self._read_from_node(node, key)
            for node in nodes
        ]
        
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=self.read_timeout
            )
            
            # Count successful reads
            successful_results = [
                result for result in results
                if not isinstance(result, Exception) and result is not None
            ]
            
            if len(successful_results) >= required_reads:
                # Return most common value (consensus)
                return self._get_consensus_value(successful_results)
            
            return None
            
        except asyncio.TimeoutError:
            logger.warning("Strong consistency read timeout", key=key)
            return None
    
    async def _get_eventual_consistency(self, key: str) -> Optional[Any]:
        """Get with eventual consistency (read from any available node)."""
        nodes = self.hash_ring.get_nodes(key, self.replication_factor)
        
        if not nodes:
            return None
        
        # Try nodes in order until we get a result
        for node in nodes:
            try:
                result = await asyncio.wait_for(
                    self._read_from_node(node, key),
                    timeout=self.read_timeout
                )
                
                if result is not None:
                    # Perform read repair if enabled
                    if self.read_repair:
                        asyncio.create_task(self._perform_read_repair(key, result, nodes))
                    
                    return result
                    
            except Exception as e:
                logger.debug("Node read failed", node_id=node.node_id, key=key, error=str(e))
                continue
        
        return None
    
    async def _get_weak_consistency(self, key: str) -> Optional[Any]:
        """Get with weak consistency (read from primary node only)."""
        node = self.hash_ring.get_node(key)
        
        if not node or not node.is_healthy:
            return None
        
        try:
            return await asyncio.wait_for(
                self._read_from_node(node, key),
                timeout=self.read_timeout
            )
        except Exception as e:
            logger.debug("Weak consistency read failed", node_id=node.node_id, key=key, error=str(e))
            return None
    
    async def _read_from_node(self, node: CacheNode, key: str) -> Optional[Any]:
        """Read value from specific node."""
        if not node.redis_client:
            return None
        
        return await node.redis_client.get(key)
    
    async def _perform_read_repair(self, key: str, value: Any, nodes: List[CacheNode]) -> None:
        """Perform read repair to synchronize nodes."""
        repair_tasks = []
        
        for node in nodes:
            if node.redis_client:
                repair_tasks.append(
                    self._repair_node_value(node, key, value)
                )
        
        if repair_tasks:
            await asyncio.gather(*repair_tasks, return_exceptions=True)
    
    async def _repair_node_value(self, node: CacheNode, key: str, value: Any) -> None:
        """Repair value on specific node."""
        try:
            if node.redis_client:
                await node.redis_client.set(key, value)
        except Exception as e:
            logger.debug("Read repair failed", node_id=node.node_id, key=key, error=str(e))
    
    def _get_consensus_value(self, values: List[Any]) -> Any:
        """Get consensus value from multiple results."""
        if not values:
            return None
        
        # Count occurrences of each value
        value_counts = defaultdict(int)
        for value in values:
            value_key = json.dumps(value, sort_keys=True, default=str)
            value_counts[value_key] += 1
        
        # Return most common value
        most_common = max(value_counts.items(), key=lambda x: x[1])
        return json.loads(most_common[0])
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None
    ) -> bool:
        """Set value in distributed cache."""
        start_time = time.time()
        
        try:
            if self.consistency_level == ConsistencyLevel.STRONG:
                result = await self._set_strong_consistency(key, value, ttl)
            elif self.consistency_level == ConsistencyLevel.EVENTUAL:
                result = await self._set_eventual_consistency(key, value, ttl)
            else:
                result = await self._set_weak_consistency(key, value, ttl)
            
            # Record metrics
            status = 'success' if result else 'failure'
            self.distributed_cache_operations.labels(
                operation='set',
                status=status,
                consistency_level=self.consistency_level.value
            ).inc()
            
            self.distributed_cache_latency.labels(
                operation='set',
                consistency_level=self.consistency_level.value
            ).observe(time.time() - start_time)
            
            return result
            
        except Exception as e:
            self.distributed_cache_operations.labels(
                operation='set',
                status='error',
                consistency_level=self.consistency_level.value
            ).inc()
            logger.error("Distributed cache set failed", key=key, error=str(e))
            return False
    
    async def _set_strong_consistency(self, key: str, value: Any, ttl: Optional[int]) -> bool:
        """Set with strong consistency (write to majority)."""
        nodes = self.hash_ring.get_nodes(key, self.replication_factor)
        
        if not nodes:
            return False
        
        # Write to majority of nodes
        required_writes = (len(nodes) // 2) + 1
        
        tasks = [
            self._write_to_node(node, key, value, ttl)
            for node in nodes
        ]
        
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=self.write_timeout
            )
            
            # Count successful writes
            successful_writes = sum(
                1 for result in results
                if not isinstance(result, Exception) and result is True
            )
            
            return successful_writes >= required_writes
            
        except asyncio.TimeoutError:
            logger.warning("Strong consistency write timeout", key=key)
            return False
    
    async def _set_eventual_consistency(self, key: str, value: Any, ttl: Optional[int]) -> bool:
        """Set with eventual consistency (write to all available nodes)."""
        nodes = self.hash_ring.get_nodes(key, self.replication_factor)
        
        if not nodes:
            return False
        
        tasks = [
            self._write_to_node(node, key, value, ttl)
            for node in nodes
        ]
        
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=self.write_timeout
            )
            
            # Return success if at least one write succeeded
            return any(
                not isinstance(result, Exception) and result is True
                for result in results
            )
            
        except asyncio.TimeoutError:
            logger.warning("Eventual consistency write timeout", key=key)
            return False
    
    async def _set_weak_consistency(self, key: str, value: Any, ttl: Optional[int]) -> bool:
        """Set with weak consistency (write to primary node only)."""
        node = self.hash_ring.get_node(key)
        
        if not node or not node.is_healthy:
            return False
        
        try:
            return await asyncio.wait_for(
                self._write_to_node(node, key, value, ttl),
                timeout=self.write_timeout
            )
        except Exception as e:
            logger.debug("Weak consistency write failed", node_id=node.node_id, key=key, error=str(e))
            return False
    
    async def _write_to_node(
        self,
        node: CacheNode,
        key: str,
        value: Any,
        ttl: Optional[int]
    ) -> bool:
        """Write value to specific node."""
        if not node.redis_client:
            return False
        
        return await node.redis_client.set(key, value, ex=ttl)
    
    async def delete(self, key: str) -> bool:
        """Delete value from distributed cache."""
        start_time = time.time()
        
        try:
            nodes = self.hash_ring.get_nodes(key, self.replication_factor)
            
            if not nodes:
                return False
            
            tasks = [
                self._delete_from_node(node, key)
                for node in nodes
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Return success if at least one delete succeeded
            success = any(
                not isinstance(result, Exception) and result is True
                for result in results
            )
            
            # Record metrics
            status = 'success' if success else 'failure'
            self.distributed_cache_operations.labels(
                operation='delete',
                status=status,
                consistency_level=self.consistency_level.value
            ).inc()
            
            self.distributed_cache_latency.labels(
                operation='delete',
                consistency_level=self.consistency_level.value
            ).observe(time.time() - start_time)
            
            return success
            
        except Exception as e:
            self.distributed_cache_operations.labels(
                operation='delete',
                status='error',
                consistency_level=self.consistency_level.value
            ).inc()
            logger.error("Distributed cache delete failed", key=key, error=str(e))
            return False
    
    async def _delete_from_node(self, node: CacheNode, key: str) -> bool:
        """Delete value from specific node."""
        if not node.redis_client:
            return False
        
        result = await node.redis_client.delete(key)
        return result > 0
    
    async def clear(self) -> bool:
        """Clear all cache data."""
        tasks = [
            self._clear_node(node)
            for node in self.nodes
            if node.is_healthy
        ]
        
        if not tasks:
            return False
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        return any(
            not isinstance(result, Exception) and result is True
            for result in results
        )
    
    async def _clear_node(self, node: CacheNode) -> bool:
        """Clear specific node."""
        if not node.redis_client:
            return False
        
        return await node.redis_client.flush_all()
    
    async def get_cluster_info(self) -> Dict[str, Any]:
        """Get distributed cache cluster information."""
        healthy_nodes = self.hash_ring.get_healthy_nodes()
        
        return {
            'total_nodes': len(self.nodes),
            'healthy_nodes': len(healthy_nodes),
            'replication_factor': self.replication_factor,
            'consistency_level': self.consistency_level.value,
            'replication_strategy': self.replication_strategy.value,
            'nodes': [
                {
                    'node_id': node.node_id,
                    'address': node.address,
                    'is_healthy': node.is_healthy,
                    'is_master': node.is_master,
                    'weight': node.weight
                }
                for node in self.nodes
            ]
        }
    
    async def close(self) -> None:
        """Close distributed cache and cleanup resources."""
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        # Close all node connections
        tasks = [node.close() for node in self.nodes]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        logger.info("Distributed cache closed")


class NodeFailureDetector:
    """Detects and handles node failures in distributed cache."""
    
    def __init__(self, nodes: List[CacheNode]):
        self.nodes = nodes
        self.failure_threshold = 3
        self.failure_counts: Dict[str, int] = defaultdict(int)
        self.recovery_threshold = 2
        self.recovery_counts: Dict[str, int] = defaultdict(int)
    
    async def check_node_failure(self, node: CacheNode) -> bool:
        """Check if node has failed."""
        if not await node.health_check():
            self.failure_counts[node.node_id] += 1
            
            if self.failure_counts[node.node_id] >= self.failure_threshold:
                logger.warning("Node marked as failed", node_id=node.node_id)
                return True
        else:
            # Reset failure count on successful health check
            self.failure_counts[node.node_id] = 0
        
        return False
    
    async def check_node_recovery(self, node: CacheNode) -> bool:
        """Check if failed node has recovered."""
        if not node.is_healthy:
            if await node.health_check():
                self.recovery_counts[node.node_id] += 1
                
                if self.recovery_counts[node.node_id] >= self.recovery_threshold:
                    logger.info("Node recovered", node_id=node.node_id)
                    self.recovery_counts[node.node_id] = 0
                    self.failure_counts[node.node_id] = 0
                    return True
        
        return False


class ConsistentHashingCache(CacheStrategy):
    """Cache strategy using consistent hashing."""
    
    def __init__(self, nodes: List[CacheNode], **kwargs):
        super().__init__(**kwargs)
        self.distributed_cache = DistributedCache(nodes, **kwargs)
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value using consistent hashing."""
        return await self.distributed_cache.get(key)
    
    async def set(self, key: str, value: Any, ttl: Optional[float] = None, tags: Optional[Set[str]] = None) -> bool:
        """Set value using consistent hashing."""
        return await self.distributed_cache.set(key, value, int(ttl) if ttl else None)
    
    async def delete(self, key: str) -> bool:
        """Delete value using consistent hashing."""
        return await self.distributed_cache.delete(key)
    
    async def clear(self) -> None:
        """Clear all cache data."""
        await self.distributed_cache.clear()
    
    async def evict(self) -> bool:
        """Eviction not applicable for distributed cache."""
        return False


class ShardedCache(CacheStrategy):
    """Simple sharded cache implementation."""
    
    def __init__(self, nodes: List[CacheNode], **kwargs):
        super().__init__(**kwargs)
        self.nodes = nodes
        self.shard_count = len(nodes)
        
        # Initialize node clients
        self.node_clients = {
            node.node_id: node.redis_client
            for node in nodes
        }
    
    def _get_shard(self, key: str) -> CacheNode:
        """Get shard for key using simple hash modulo."""
        hash_value = hash(key)
        shard_index = hash_value % self.shard_count
        return self.nodes[shard_index]
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from sharded cache."""
        node = self._get_shard(key)
        
        if not node.is_healthy or not node.redis_client:
            return None
        
        return await node.redis_client.get(key)
    
    async def set(self, key: str, value: Any, ttl: Optional[float] = None, tags: Optional[Set[str]] = None) -> bool:
        """Set value in sharded cache."""
        node = self._get_shard(key)
        
        if not node.is_healthy or not node.redis_client:
            return False
        
        return await node.redis_client.set(key, value, ex=int(ttl) if ttl else None)
    
    async def delete(self, key: str) -> bool:
        """Delete value from sharded cache."""
        node = self._get_shard(key)
        
        if not node.is_healthy or not node.redis_client:
            return False
        
        result = await node.redis_client.delete(key)
        return result > 0
    
    async def clear(self) -> None:
        """Clear all sharded cache data."""
        tasks = [
            node.redis_client.flush_all()
            for node in self.nodes
            if node.is_healthy and node.redis_client
        ]
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def evict(self) -> bool:
        """Eviction not applicable for sharded cache."""
        return False


# Factory functions
def create_distributed_cache(
    node_configs: List[Dict[str, Any]],
    replication_factor: int = 2,
    consistency_level: ConsistencyLevel = ConsistencyLevel.EVENTUAL
) -> DistributedCache:
    """Create distributed cache with node configurations."""
    nodes = []
    
    for config in node_configs:
        node = CacheNode(
            node_id=config['node_id'],
            host=config['host'],
            port=config['port'],
            weight=config.get('weight', 1),
            is_master=config.get('is_master', False)
        )
        nodes.append(node)
    
    return DistributedCache(
        nodes=nodes,
        replication_factor=replication_factor,
        consistency_level=consistency_level
    )


def create_sharded_cache(node_configs: List[Dict[str, Any]]) -> ShardedCache:
    """Create sharded cache with node configurations."""
    nodes = []
    
    for config in node_configs:
        node = CacheNode(
            node_id=config['node_id'],
            host=config['host'],
            port=config['port'],
            weight=config.get('weight', 1)
        )
        nodes.append(node)
    
    return ShardedCache(nodes=nodes)