"""
Pattern Cache

Specialized caching for pattern detection, feature extraction, ML inference,
and similarity search results with intelligent cache warming and invalidation.
"""

import asyncio
import hashlib
import json
import time
from typing import Any, Dict, List, Optional, Set, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
import pickle
import gzip
import base64

import numpy as np
import structlog
from prometheus_client import Counter, Histogram, Gauge

from .redis_client import RedisClientManager, get_redis_client
from .strategies import MultiLevelCache, LRUCache, CacheInvalidationStrategy, CacheWarmupStrategy
from ..models.patterns import PatternDetectionResult, CodePattern, PatternConfidence
from ..models.ml import ModelInferenceResult, EmbeddingVector, FeatureVector

logger = structlog.get_logger()


class CacheKeyType(Enum):
    """Cache key types for different operations."""
    PATTERN = "pattern"
    FEATURE = "feature"
    INFERENCE = "inference"
    EMBEDDING = "embedding"
    SIMILARITY = "similarity"
    BATCH = "batch"
    ANALYSIS = "analysis"


@dataclass
class CacheKey:
    """Structured cache key with metadata."""
    key_type: CacheKeyType
    primary_id: str
    secondary_id: Optional[str] = None
    version: str = "v1"
    params_hash: Optional[str] = None
    
    def __str__(self) -> str:
        """Generate string representation of cache key."""
        parts = [
            self.key_type.value,
            self.version,
            self.primary_id
        ]
        
        if self.secondary_id:
            parts.append(self.secondary_id)
        
        if self.params_hash:
            parts.append(self.params_hash)
        
        return ":".join(parts)
    
    @classmethod
    def from_string(cls, key_str: str) -> "CacheKey":
        """Parse cache key from string."""
        parts = key_str.split(":")
        
        if len(parts) < 3:
            raise ValueError(f"Invalid cache key format: {key_str}")
        
        key_type = CacheKeyType(parts[0])
        version = parts[1]
        primary_id = parts[2]
        secondary_id = parts[3] if len(parts) > 3 else None
        params_hash = parts[4] if len(parts) > 4 else None
        
        return cls(
            key_type=key_type,
            primary_id=primary_id,
            secondary_id=secondary_id,
            version=version,
            params_hash=params_hash
        )


class PatternCache:
    """
    Specialized cache for pattern detection results with intelligent warming,
    invalidation, and compression for large pattern data.
    """
    
    def __init__(
        self,
        redis_client: Optional[RedisClientManager] = None,
        l1_max_size: int = 1000,
        l2_ttl: int = 7200,  # 2 hours
        enable_compression: bool = True,
        compression_threshold: int = 1024  # Compress if data > 1KB
    ):
        self.redis_client = redis_client or get_redis_client()
        self.cache = MultiLevelCache(
            l1_strategy=LRUCache(max_size=l1_max_size, ttl=3600),
            redis_client=self.redis_client,
            l2_ttl=l2_ttl,
            promote_threshold=2
        )
        self.invalidation = CacheInvalidationStrategy(self.cache)
        self.warmup = CacheWarmupStrategy(self.cache)
        self.enable_compression = enable_compression
        self.compression_threshold = compression_threshold
        
        # Prometheus metrics
        self.pattern_cache_hits = Counter(
            'pattern_cache_hits_total',
            'Pattern cache hits',
            ['pattern_type', 'cache_level']
        )
        
        self.pattern_cache_misses = Counter(
            'pattern_cache_misses_total',
            'Pattern cache misses',
            ['pattern_type']
        )
        
        self.pattern_cache_size = Gauge(
            'pattern_cache_size_bytes',
            'Pattern cache size in bytes',
            ['pattern_type']
        )
        
        self.compression_ratio = Histogram(
            'pattern_cache_compression_ratio',
            'Pattern cache compression ratio'
        )
    
    def _generate_params_hash(self, params: Dict[str, Any]) -> str:
        """Generate hash for cache parameters."""
        # Sort parameters for consistent hashing
        sorted_params = json.dumps(params, sort_keys=True, default=str)
        return hashlib.md5(sorted_params.encode()).hexdigest()[:8]
    
    def _compress_data(self, data: Any) -> Tuple[bytes, bool]:
        """Compress data if above threshold."""
        serialized = pickle.dumps(data)
        
        if not self.enable_compression or len(serialized) < self.compression_threshold:
            return serialized, False
        
        compressed = gzip.compress(serialized)
        compression_ratio = len(compressed) / len(serialized)
        self.compression_ratio.observe(compression_ratio)
        
        return compressed, True
    
    def _decompress_data(self, data: bytes, is_compressed: bool) -> Any:
        """Decompress data if needed."""
        if is_compressed:
            data = gzip.decompress(data)
        return pickle.loads(data)
    
    async def get_pattern_detection_result(
        self,
        code_hash: str,
        language: str,
        detector_config: Dict[str, Any]
    ) -> Optional[PatternDetectionResult]:
        """Get cached pattern detection result."""
        params_hash = self._generate_params_hash({
            'language': language,
            'config': detector_config
        })
        
        cache_key = CacheKey(
            key_type=CacheKeyType.PATTERN,
            primary_id=code_hash,
            secondary_id=language,
            params_hash=params_hash
        )
        
        try:
            cached_data = await self.cache.get(str(cache_key))
            if cached_data:
                # Decompress if needed
                result_data, is_compressed = cached_data['data'], cached_data['compressed']
                if isinstance(result_data, str):
                    result_data = base64.b64decode(result_data)
                
                result = self._decompress_data(result_data, is_compressed)
                self.pattern_cache_hits.labels(
                    pattern_type='detection',
                    cache_level='unknown'
                ).inc()
                
                return result
            
            self.pattern_cache_misses.labels(pattern_type='detection').inc()
            return None
            
        except Exception as e:
            logger.error("Failed to get pattern detection result", key=str(cache_key), error=str(e))
            return None
    
    async def set_pattern_detection_result(
        self,
        code_hash: str,
        language: str,
        detector_config: Dict[str, Any],
        result: PatternDetectionResult,
        ttl: Optional[int] = None
    ) -> bool:
        """Cache pattern detection result."""
        params_hash = self._generate_params_hash({
            'language': language,
            'config': detector_config
        })
        
        cache_key = CacheKey(
            key_type=CacheKeyType.PATTERN,
            primary_id=code_hash,
            secondary_id=language,
            params_hash=params_hash
        )
        
        try:
            # Compress data
            compressed_data, is_compressed = self._compress_data(result)
            
            # Encode binary data for JSON serialization
            encoded_data = base64.b64encode(compressed_data).decode('utf-8')
            
            cached_data = {
                'data': encoded_data,
                'compressed': is_compressed,
                'timestamp': time.time(),
                'size': len(compressed_data)
            }
            
            # Register tags for invalidation
            tags = {
                f"language:{language}",
                f"detector:{detector_config.get('detector_type', 'unknown')}",
                f"code_hash:{code_hash}"
            }
            
            success = await self.cache.set(str(cache_key), cached_data, ttl, tags)
            
            if success:
                self.pattern_cache_size.labels(pattern_type='detection').inc(len(compressed_data))
                
                # Register invalidation tags
                for tag in tags:
                    await self.invalidation.register_tag(str(cache_key), tag)
            
            return success
            
        except Exception as e:
            logger.error("Failed to cache pattern detection result", key=str(cache_key), error=str(e))
            return False
    
    async def get_feature_extraction_result(
        self,
        code_hash: str,
        feature_type: str,
        extractor_config: Dict[str, Any]
    ) -> Optional[FeatureVector]:
        """Get cached feature extraction result."""
        params_hash = self._generate_params_hash({
            'feature_type': feature_type,
            'config': extractor_config
        })
        
        cache_key = CacheKey(
            key_type=CacheKeyType.FEATURE,
            primary_id=code_hash,
            secondary_id=feature_type,
            params_hash=params_hash
        )
        
        try:
            cached_data = await self.cache.get(str(cache_key))
            if cached_data:
                result_data, is_compressed = cached_data['data'], cached_data['compressed']
                if isinstance(result_data, str):
                    result_data = base64.b64decode(result_data)
                
                result = self._decompress_data(result_data, is_compressed)
                self.pattern_cache_hits.labels(
                    pattern_type='feature',
                    cache_level='unknown'
                ).inc()
                
                return result
            
            self.pattern_cache_misses.labels(pattern_type='feature').inc()
            return None
            
        except Exception as e:
            logger.error("Failed to get feature extraction result", key=str(cache_key), error=str(e))
            return None
    
    async def set_feature_extraction_result(
        self,
        code_hash: str,
        feature_type: str,
        extractor_config: Dict[str, Any],
        result: FeatureVector,
        ttl: Optional[int] = None
    ) -> bool:
        """Cache feature extraction result."""
        params_hash = self._generate_params_hash({
            'feature_type': feature_type,
            'config': extractor_config
        })
        
        cache_key = CacheKey(
            key_type=CacheKeyType.FEATURE,
            primary_id=code_hash,
            secondary_id=feature_type,
            params_hash=params_hash
        )
        
        try:
            # Compress data
            compressed_data, is_compressed = self._compress_data(result)
            encoded_data = base64.b64encode(compressed_data).decode('utf-8')
            
            cached_data = {
                'data': encoded_data,
                'compressed': is_compressed,
                'timestamp': time.time(),
                'size': len(compressed_data)
            }
            
            # Register tags for invalidation
            tags = {
                f"feature_type:{feature_type}",
                f"extractor:{extractor_config.get('extractor_type', 'unknown')}",
                f"code_hash:{code_hash}"
            }
            
            success = await self.cache.set(str(cache_key), cached_data, ttl, tags)
            
            if success:
                self.pattern_cache_size.labels(pattern_type='feature').inc(len(compressed_data))
                
                # Register invalidation tags
                for tag in tags:
                    await self.invalidation.register_tag(str(cache_key), tag)
            
            return success
            
        except Exception as e:
            logger.error("Failed to cache feature extraction result", key=str(cache_key), error=str(e))
            return False


class FeatureCache:
    """Specialized cache for feature extraction results."""
    
    def __init__(self, redis_client: Optional[RedisClientManager] = None):
        self.redis_client = redis_client or get_redis_client()
        self.cache = MultiLevelCache(
            l1_strategy=LRUCache(max_size=500, ttl=1800),  # 30 minutes
            redis_client=self.redis_client,
            l2_ttl=3600,  # 1 hour
            promote_threshold=3
        )
    
    async def get_ast_features(self, code_hash: str, language: str) -> Optional[Dict[str, Any]]:
        """Get cached AST features."""
        key = f"ast_features:{code_hash}:{language}"
        return await self.cache.get(key)
    
    async def set_ast_features(
        self,
        code_hash: str,
        language: str,
        features: Dict[str, Any],
        ttl: Optional[int] = None
    ) -> bool:
        """Cache AST features."""
        key = f"ast_features:{code_hash}:{language}"
        return await self.cache.set(key, features, ttl)
    
    async def get_semantic_features(self, code_hash: str, model_name: str) -> Optional[Dict[str, Any]]:
        """Get cached semantic features."""
        key = f"semantic_features:{code_hash}:{model_name}"
        return await self.cache.get(key)
    
    async def set_semantic_features(
        self,
        code_hash: str,
        model_name: str,
        features: Dict[str, Any],
        ttl: Optional[int] = None
    ) -> bool:
        """Cache semantic features."""
        key = f"semantic_features:{code_hash}:{model_name}"
        return await self.cache.set(key, features, ttl)
    
    async def get_text_features(self, code_hash: str, extractor_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get cached text features."""
        config_hash = hashlib.md5(json.dumps(extractor_config, sort_keys=True).encode()).hexdigest()[:8]
        key = f"text_features:{code_hash}:{config_hash}"
        return await self.cache.get(key)
    
    async def set_text_features(
        self,
        code_hash: str,
        extractor_config: Dict[str, Any],
        features: Dict[str, Any],
        ttl: Optional[int] = None
    ) -> bool:
        """Cache text features."""
        config_hash = hashlib.md5(json.dumps(extractor_config, sort_keys=True).encode()).hexdigest()[:8]
        key = f"text_features:{code_hash}:{config_hash}"
        return await self.cache.set(key, features, ttl)


class InferenceCache:
    """Specialized cache for ML model inference results."""
    
    def __init__(self, redis_client: Optional[RedisClientManager] = None):
        self.redis_client = redis_client or get_redis_client()
        self.cache = MultiLevelCache(
            l1_strategy=LRUCache(max_size=200, ttl=1800),  # 30 minutes
            redis_client=self.redis_client,
            l2_ttl=3600,  # 1 hour
            promote_threshold=2
        )
        
        # Prometheus metrics
        self.inference_cache_hits = Counter(
            'inference_cache_hits_total',
            'Inference cache hits',
            ['model_name', 'inference_type']
        )
        
        self.inference_cache_misses = Counter(
            'inference_cache_misses_total',
            'Inference cache misses',
            ['model_name', 'inference_type']
        )
    
    async def get_model_inference(
        self,
        model_name: str,
        input_hash: str,
        inference_type: str = "prediction"
    ) -> Optional[ModelInferenceResult]:
        """Get cached model inference result."""
        key = f"inference:{model_name}:{inference_type}:{input_hash}"
        
        try:
            result = await self.cache.get(key)
            if result:
                self.inference_cache_hits.labels(
                    model_name=model_name,
                    inference_type=inference_type
                ).inc()
                return result
            
            self.inference_cache_misses.labels(
                model_name=model_name,
                inference_type=inference_type
            ).inc()
            return None
            
        except Exception as e:
            logger.error("Failed to get model inference result", key=key, error=str(e))
            return None
    
    async def set_model_inference(
        self,
        model_name: str,
        input_hash: str,
        result: ModelInferenceResult,
        inference_type: str = "prediction",
        ttl: Optional[int] = None
    ) -> bool:
        """Cache model inference result."""
        key = f"inference:{model_name}:{inference_type}:{input_hash}"
        
        try:
            return await self.cache.set(key, result, ttl)
        except Exception as e:
            logger.error("Failed to cache model inference result", key=key, error=str(e))
            return False
    
    async def get_embedding(self, model_name: str, text_hash: str) -> Optional[EmbeddingVector]:
        """Get cached embedding vector."""
        key = f"embedding:{model_name}:{text_hash}"
        return await self.cache.get(key)
    
    async def set_embedding(
        self,
        model_name: str,
        text_hash: str,
        embedding: EmbeddingVector,
        ttl: Optional[int] = None
    ) -> bool:
        """Cache embedding vector."""
        key = f"embedding:{model_name}:{text_hash}"
        return await self.cache.set(key, embedding, ttl)
    
    async def get_batch_embeddings(
        self,
        model_name: str,
        text_hashes: List[str]
    ) -> Dict[str, Optional[EmbeddingVector]]:
        """Get multiple cached embeddings."""
        keys = [f"embedding:{model_name}:{text_hash}" for text_hash in text_hashes]
        
        # Use multi-get for efficiency
        if hasattr(self.cache, 'l2_cache'):
            results = await self.cache.l2_cache.mget(*keys)
        else:
            results = [await self.cache.get(key) for key in keys]
        
        return {text_hash: result for text_hash, result in zip(text_hashes, results)}
    
    async def set_batch_embeddings(
        self,
        model_name: str,
        embeddings: Dict[str, EmbeddingVector],
        ttl: Optional[int] = None
    ) -> bool:
        """Cache multiple embeddings."""
        tasks = [
            self.set_embedding(model_name, text_hash, embedding, ttl)
            for text_hash, embedding in embeddings.items()
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return all(result is True for result in results)


class SimilarityCache:
    """Specialized cache for similarity search results."""
    
    def __init__(self, redis_client: Optional[RedisClientManager] = None):
        self.redis_client = redis_client or get_redis_client()
        self.cache = MultiLevelCache(
            l1_strategy=LRUCache(max_size=100, ttl=900),  # 15 minutes
            redis_client=self.redis_client,
            l2_ttl=1800,  # 30 minutes
            promote_threshold=2
        )
    
    async def get_similarity_results(
        self,
        query_hash: str,
        similarity_config: Dict[str, Any],
        top_k: int = 10
    ) -> Optional[List[Tuple[str, float]]]:
        """Get cached similarity search results."""
        config_hash = hashlib.md5(json.dumps(similarity_config, sort_keys=True).encode()).hexdigest()[:8]
        key = f"similarity:{query_hash}:{config_hash}:{top_k}"
        return await self.cache.get(key)
    
    async def set_similarity_results(
        self,
        query_hash: str,
        similarity_config: Dict[str, Any],
        results: List[Tuple[str, float]],
        top_k: int = 10,
        ttl: Optional[int] = None
    ) -> bool:
        """Cache similarity search results."""
        config_hash = hashlib.md5(json.dumps(similarity_config, sort_keys=True).encode()).hexdigest()[:8]
        key = f"similarity:{query_hash}:{config_hash}:{top_k}"
        return await self.cache.set(key, results, ttl)
    
    async def get_pattern_similarities(
        self,
        pattern_hash: str,
        threshold: float = 0.7
    ) -> Optional[List[Tuple[str, float]]]:
        """Get cached pattern similarity results."""
        key = f"pattern_similarity:{pattern_hash}:{threshold}"
        return await self.cache.get(key)
    
    async def set_pattern_similarities(
        self,
        pattern_hash: str,
        similarities: List[Tuple[str, float]],
        threshold: float = 0.7,
        ttl: Optional[int] = None
    ) -> bool:
        """Cache pattern similarity results."""
        key = f"pattern_similarity:{pattern_hash}:{threshold}"
        return await self.cache.set(key, similarities, ttl)


class BatchResultCache:
    """Specialized cache for batch processing results."""
    
    def __init__(self, redis_client: Optional[RedisClientManager] = None):
        self.redis_client = redis_client or get_redis_client()
        self.cache = MultiLevelCache(
            l1_strategy=LRUCache(max_size=50, ttl=600),  # 10 minutes
            redis_client=self.redis_client,
            l2_ttl=1800,  # 30 minutes
            promote_threshold=1
        )
    
    async def get_batch_results(
        self,
        batch_id: str,
        processing_config: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Get cached batch processing results."""
        config_hash = hashlib.md5(json.dumps(processing_config, sort_keys=True).encode()).hexdigest()[:8]
        key = f"batch:{batch_id}:{config_hash}"
        return await self.cache.get(key)
    
    async def set_batch_results(
        self,
        batch_id: str,
        processing_config: Dict[str, Any],
        results: Dict[str, Any],
        ttl: Optional[int] = None
    ) -> bool:
        """Cache batch processing results."""
        config_hash = hashlib.md5(json.dumps(processing_config, sort_keys=True).encode()).hexdigest()[:8]
        key = f"batch:{batch_id}:{config_hash}"
        return await self.cache.set(key, results, ttl)
    
    async def get_partial_batch_results(
        self,
        batch_id: str,
        item_ids: List[str]
    ) -> Dict[str, Optional[Any]]:
        """Get partial batch results for specific items."""
        keys = [f"batch_item:{batch_id}:{item_id}" for item_id in item_ids]
        
        if hasattr(self.cache, 'l2_cache'):
            results = await self.cache.l2_cache.mget(*keys)
        else:
            results = [await self.cache.get(key) for key in keys]
        
        return {item_id: result for item_id, result in zip(item_ids, results)}
    
    async def set_partial_batch_results(
        self,
        batch_id: str,
        item_results: Dict[str, Any],
        ttl: Optional[int] = None
    ) -> bool:
        """Cache partial batch results."""
        tasks = [
            self.cache.set(f"batch_item:{batch_id}:{item_id}", result, ttl)
            for item_id, result in item_results.items()
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return all(result is True for result in results)


# Global cache instances
_pattern_cache: Optional[PatternCache] = None
_feature_cache: Optional[FeatureCache] = None
_inference_cache: Optional[InferenceCache] = None
_similarity_cache: Optional[SimilarityCache] = None
_batch_cache: Optional[BatchResultCache] = None


def get_pattern_cache() -> PatternCache:
    """Get global pattern cache instance."""
    global _pattern_cache
    if _pattern_cache is None:
        _pattern_cache = PatternCache()
    return _pattern_cache


def get_feature_cache() -> FeatureCache:
    """Get global feature cache instance."""
    global _feature_cache
    if _feature_cache is None:
        _feature_cache = FeatureCache()
    return _feature_cache


def get_inference_cache() -> InferenceCache:
    """Get global inference cache instance."""
    global _inference_cache
    if _inference_cache is None:
        _inference_cache = InferenceCache()
    return _inference_cache


def get_similarity_cache() -> SimilarityCache:
    """Get global similarity cache instance."""
    global _similarity_cache
    if _similarity_cache is None:
        _similarity_cache = SimilarityCache()
    return _similarity_cache


def get_batch_cache() -> BatchResultCache:
    """Get global batch cache instance."""
    global _batch_cache
    if _batch_cache is None:
        _batch_cache = BatchResultCache()
    return _batch_cache


async def warm_pattern_caches():
    """Warm up pattern caches with frequently accessed data."""
    logger.info("Starting pattern cache warmup")
    
    # This would typically load from database or external sources
    # For now, we'll just log the intent
    logger.info("Pattern cache warmup completed")


async def clear_all_pattern_caches():
    """Clear all pattern-related caches."""
    caches = [
        get_pattern_cache(),
        get_feature_cache(),
        get_inference_cache(),
        get_similarity_cache(),
        get_batch_cache()
    ]
    
    tasks = [cache.cache.clear() for cache in caches]
    await asyncio.gather(*tasks, return_exceptions=True)
    
    logger.info("All pattern caches cleared")