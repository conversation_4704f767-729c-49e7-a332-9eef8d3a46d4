"""
Caching Strategies

Advanced caching strategies including LRU, TTL, multi-level caching,
cache warming, and intelligent invalidation.
"""

import asyncio
import time
import threading
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple, Union, Callable, Set
from dataclasses import dataclass, field
from enum import Enum
from collections import OrderedDict
from contextlib import asynccontextmanager
import hashlib
import json
import heapq
import weakref

import structlog
from prometheus_client import Counter, Histogram, Gauge

from .redis_client import RedisClientManager, get_redis_client

logger = structlog.get_logger()


class CacheLevel(Enum):
    """Cache level enumeration."""
    L1_MEMORY = "l1_memory"
    L2_REDIS = "l2_redis"
    L3_DISTRIBUTED = "l3_distributed"


class EvictionPolicy(Enum):
    """Cache eviction policies."""
    LRU = "lru"
    LFU = "lfu"
    FIFO = "fifo"
    RANDOM = "random"
    TTL = "ttl"


class InvalidationStrategy(Enum):
    """Cache invalidation strategies."""
    TTL = "ttl"
    WRITE_THROUGH = "write_through"
    WRITE_BEHIND = "write_behind"
    MANUAL = "manual"
    TAG_BASED = "tag_based"
    DEPENDENCY_BASED = "dependency_based"


@dataclass
class CacheEntry:
    """Cache entry with metadata."""
    key: str
    value: Any
    created_at: float
    accessed_at: float
    access_count: int = 0
    ttl: Optional[float] = None
    size: int = 0
    tags: Set[str] = field(default_factory=set)
    dependencies: Set[str] = field(default_factory=set)
    
    @property
    def is_expired(self) -> bool:
        """Check if entry is expired."""
        if self.ttl is None:
            return False
        return time.time() > self.created_at + self.ttl
    
    @property
    def age(self) -> float:
        """Get entry age in seconds."""
        return time.time() - self.created_at
    
    def touch(self) -> None:
        """Update access information."""
        self.accessed_at = time.time()
        self.access_count += 1


class CacheStrategy(ABC):
    """Abstract base class for cache strategies."""
    
    def __init__(self, max_size: int = 1000, ttl: Optional[float] = None):
        self.max_size = max_size
        self.default_ttl = ttl
        self.entries: Dict[str, CacheEntry] = {}
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'size': 0
        }
        
        # Prometheus metrics
        self.cache_hits = Counter(
            'cache_hits_total',
            'Total number of cache hits',
            ['strategy']
        )
        
        self.cache_misses = Counter(
            'cache_misses_total',
            'Total number of cache misses',
            ['strategy']
        )
        
        self.cache_evictions = Counter(
            'cache_evictions_total',
            'Total number of cache evictions',
            ['strategy']
        )
        
        self.cache_size = Gauge(
            'cache_size_current',
            'Current cache size',
            ['strategy']
        )
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        pass
    
    @abstractmethod
    async def set(self, key: str, value: Any, ttl: Optional[float] = None, tags: Optional[Set[str]] = None) -> bool:
        """Set value in cache."""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete value from cache."""
        pass
    
    @abstractmethod
    async def clear(self) -> None:
        """Clear all cache entries."""
        pass
    
    @abstractmethod
    async def evict(self) -> bool:
        """Evict entries according to strategy."""
        pass
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        return key in self.entries and not self.entries[key].is_expired
    
    async def size(self) -> int:
        """Get current cache size."""
        return len(self.entries)
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = self.stats['hits'] / total_requests if total_requests > 0 else 0
        
        return {
            'hits': self.stats['hits'],
            'misses': self.stats['misses'],
            'evictions': self.stats['evictions'],
            'size': len(self.entries),
            'hit_rate': hit_rate,
            'max_size': self.max_size
        }
    
    def _calculate_size(self, value: Any) -> int:
        """Calculate approximate size of value."""
        try:
            return len(json.dumps(value, default=str))
        except (TypeError, ValueError):
            return len(str(value))
    
    def _cleanup_expired(self) -> None:
        """Remove expired entries."""
        expired_keys = [
            key for key, entry in self.entries.items()
            if entry.is_expired
        ]
        for key in expired_keys:
            del self.entries[key]


class LRUCache(CacheStrategy):
    """Least Recently Used cache implementation."""
    
    def __init__(self, max_size: int = 1000, ttl: Optional[float] = None):
        super().__init__(max_size, ttl)
        self.access_order: OrderedDict[str, None] = OrderedDict()
        self.strategy_name = "lru"
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from LRU cache."""
        self._cleanup_expired()
        
        if key not in self.entries:
            self.stats['misses'] += 1
            self.cache_misses.labels(strategy=self.strategy_name).inc()
            return None
        
        entry = self.entries[key]
        if entry.is_expired:
            del self.entries[key]
            self.access_order.pop(key, None)
            self.stats['misses'] += 1
            self.cache_misses.labels(strategy=self.strategy_name).inc()
            return None
        
        # Update access order
        self.access_order.move_to_end(key)
        entry.touch()
        
        self.stats['hits'] += 1
        self.cache_hits.labels(strategy=self.strategy_name).inc()
        return entry.value
    
    async def set(self, key: str, value: Any, ttl: Optional[float] = None, tags: Optional[Set[str]] = None) -> bool:
        """Set value in LRU cache."""
        try:
            # Remove existing entry if present
            if key in self.entries:
                del self.entries[key]
                self.access_order.pop(key, None)
            
            # Evict if necessary
            while len(self.entries) >= self.max_size:
                await self.evict()
            
            # Create new entry
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=time.time(),
                accessed_at=time.time(),
                ttl=ttl or self.default_ttl,
                size=self._calculate_size(value),
                tags=tags or set()
            )
            
            self.entries[key] = entry
            self.access_order[key] = None
            
            self.cache_size.labels(strategy=self.strategy_name).set(len(self.entries))
            return True
            
        except Exception as e:
            logger.error("Failed to set cache entry", key=key, error=str(e))
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete value from LRU cache."""
        if key in self.entries:
            del self.entries[key]
            self.access_order.pop(key, None)
            self.cache_size.labels(strategy=self.strategy_name).set(len(self.entries))
            return True
        return False
    
    async def clear(self) -> None:
        """Clear all cache entries."""
        self.entries.clear()
        self.access_order.clear()
        self.cache_size.labels(strategy=self.strategy_name).set(0)
    
    async def evict(self) -> bool:
        """Evict least recently used entry."""
        if not self.access_order:
            return False
        
        # Get least recently used key
        lru_key = next(iter(self.access_order))
        
        # Remove entry
        del self.entries[lru_key]
        del self.access_order[lru_key]
        
        self.stats['evictions'] += 1
        self.cache_evictions.labels(strategy=self.strategy_name).inc()
        self.cache_size.labels(strategy=self.strategy_name).set(len(self.entries))
        
        return True


class TTLCache(CacheStrategy):
    """Time To Live cache implementation."""
    
    def __init__(self, max_size: int = 1000, ttl: float = 3600):
        super().__init__(max_size, ttl)
        self.strategy_name = "ttl"
        self.expiration_heap: List[Tuple[float, str]] = []
        self._cleanup_task: Optional[asyncio.Task] = None
        self._start_cleanup_task()
    
    def _start_cleanup_task(self) -> None:
        """Start background cleanup task."""
        if self._cleanup_task is None:
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
    
    async def _cleanup_loop(self) -> None:
        """Background cleanup loop for expired entries."""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                await self._cleanup_expired_entries()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Cleanup task failed", error=str(e))
    
    async def _cleanup_expired_entries(self) -> None:
        """Clean up expired entries."""
        current_time = time.time()
        
        # Remove expired entries from heap
        while self.expiration_heap and self.expiration_heap[0][0] <= current_time:
            _, key = heapq.heappop(self.expiration_heap)
            if key in self.entries and self.entries[key].is_expired:
                del self.entries[key]
                self.stats['evictions'] += 1
                self.cache_evictions.labels(strategy=self.strategy_name).inc()
        
        self.cache_size.labels(strategy=self.strategy_name).set(len(self.entries))
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from TTL cache."""
        if key not in self.entries:
            self.stats['misses'] += 1
            self.cache_misses.labels(strategy=self.strategy_name).inc()
            return None
        
        entry = self.entries[key]
        if entry.is_expired:
            del self.entries[key]
            self.stats['misses'] += 1
            self.cache_misses.labels(strategy=self.strategy_name).inc()
            return None
        
        entry.touch()
        self.stats['hits'] += 1
        self.cache_hits.labels(strategy=self.strategy_name).inc()
        return entry.value
    
    async def set(self, key: str, value: Any, ttl: Optional[float] = None, tags: Optional[Set[str]] = None) -> bool:
        """Set value in TTL cache."""
        try:
            # Remove existing entry if present
            if key in self.entries:
                del self.entries[key]
            
            # Evict if necessary
            while len(self.entries) >= self.max_size:
                await self.evict()
            
            # Create new entry
            entry_ttl = ttl or self.default_ttl
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=time.time(),
                accessed_at=time.time(),
                ttl=entry_ttl,
                size=self._calculate_size(value),
                tags=tags or set()
            )
            
            self.entries[key] = entry
            
            # Add to expiration heap
            if entry_ttl:
                expiration_time = entry.created_at + entry_ttl
                heapq.heappush(self.expiration_heap, (expiration_time, key))
            
            self.cache_size.labels(strategy=self.strategy_name).set(len(self.entries))
            return True
            
        except Exception as e:
            logger.error("Failed to set cache entry", key=key, error=str(e))
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete value from TTL cache."""
        if key in self.entries:
            del self.entries[key]
            self.cache_size.labels(strategy=self.strategy_name).set(len(self.entries))
            return True
        return False
    
    async def clear(self) -> None:
        """Clear all cache entries."""
        self.entries.clear()
        self.expiration_heap.clear()
        self.cache_size.labels(strategy=self.strategy_name).set(0)
    
    async def evict(self) -> bool:
        """Evict expired entries."""
        await self._cleanup_expired_entries()
        
        # If still over capacity, evict oldest entries
        if len(self.entries) >= self.max_size:
            # Sort by creation time and remove oldest
            oldest_key = min(self.entries.keys(), key=lambda k: self.entries[k].created_at)
            del self.entries[oldest_key]
            self.stats['evictions'] += 1
            self.cache_evictions.labels(strategy=self.strategy_name).inc()
            self.cache_size.labels(strategy=self.strategy_name).set(len(self.entries))
            return True
        
        return False
    
    async def close(self) -> None:
        """Close TTL cache and cleanup tasks."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass


class MultiLevelCache:
    """Multi-level cache with L1 (memory) and L2 (Redis) levels."""
    
    def __init__(
        self,
        l1_strategy: CacheStrategy,
        redis_client: Optional[RedisClientManager] = None,
        l2_ttl: int = 3600,
        promote_threshold: int = 2
    ):
        self.l1_cache = l1_strategy
        self.l2_cache = redis_client or get_redis_client()
        self.l2_ttl = l2_ttl
        self.promote_threshold = promote_threshold
        self.access_counts: Dict[str, int] = {}
        
        # Prometheus metrics
        self.level_hits = Counter(
            'multilevel_cache_hits_total',
            'Cache hits by level',
            ['level']
        )
        
        self.level_misses = Counter(
            'multilevel_cache_misses_total',
            'Cache misses by level',
            ['level']
        )
        
        self.promotions = Counter(
            'multilevel_cache_promotions_total',
            'Cache promotions from L2 to L1'
        )
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from multi-level cache."""
        # Try L1 cache first
        value = await self.l1_cache.get(key)
        if value is not None:
            self.level_hits.labels(level='l1').inc()
            return value
        
        # Try L2 cache
        value = await self.l2_cache.get(key)
        if value is not None:
            self.level_hits.labels(level='l2').inc()
            
            # Track access for promotion
            self.access_counts[key] = self.access_counts.get(key, 0) + 1
            
            # Promote to L1 if frequently accessed
            if self.access_counts[key] >= self.promote_threshold:
                await self.l1_cache.set(key, value)
                self.promotions.inc()
                del self.access_counts[key]
            
            return value
        
        # Cache miss
        self.level_misses.labels(level='l2').inc()
        return None
    
    async def set(self, key: str, value: Any, ttl: Optional[float] = None, tags: Optional[Set[str]] = None) -> bool:
        """Set value in multi-level cache."""
        # Set in both L1 and L2
        l1_success = await self.l1_cache.set(key, value, ttl, tags)
        l2_success = await self.l2_cache.set(key, value, ex=ttl or self.l2_ttl)
        
        # Reset access count
        self.access_counts.pop(key, None)
        
        return l1_success or l2_success
    
    async def delete(self, key: str) -> bool:
        """Delete value from multi-level cache."""
        l1_success = await self.l1_cache.delete(key)
        l2_success = bool(await self.l2_cache.delete(key))
        
        # Clean up access count
        self.access_counts.pop(key, None)
        
        return l1_success or l2_success
    
    async def clear(self) -> None:
        """Clear all cache levels."""
        await self.l1_cache.clear()
        await self.l2_cache.flush_all()
        self.access_counts.clear()
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get combined cache statistics."""
        l1_stats = await self.l1_cache.get_stats()
        l2_metrics = await self.l2_cache.get_metrics()
        
        return {
            'l1': l1_stats,
            'l2': l2_metrics.to_dict(),
            'promotion_candidates': len(self.access_counts)
        }


class CacheInvalidationStrategy:
    """Advanced cache invalidation strategies."""
    
    def __init__(self, cache: Union[CacheStrategy, MultiLevelCache]):
        self.cache = cache
        self.tag_registry: Dict[str, Set[str]] = {}  # tag -> set of keys
        self.dependency_graph: Dict[str, Set[str]] = {}  # key -> dependencies
        self.dependents_graph: Dict[str, Set[str]] = {}  # key -> dependents
        
        # Prometheus metrics
        self.invalidations = Counter(
            'cache_invalidations_total',
            'Total cache invalidations',
            ['strategy', 'reason']
        )
    
    async def invalidate_by_tag(self, tag: str) -> int:
        """Invalidate all entries with specific tag."""
        if tag not in self.tag_registry:
            return 0
        
        keys_to_invalidate = self.tag_registry[tag].copy()
        count = 0
        
        for key in keys_to_invalidate:
            if await self.cache.delete(key):
                count += 1
                self.invalidations.labels(strategy='tag', reason=tag).inc()
        
        # Clean up tag registry
        del self.tag_registry[tag]
        
        return count
    
    async def invalidate_by_dependency(self, dependency_key: str) -> int:
        """Invalidate all entries that depend on a specific key."""
        if dependency_key not in self.dependents_graph:
            return 0
        
        dependents = self.dependents_graph[dependency_key].copy()
        count = 0
        
        for dependent in dependents:
            if await self.cache.delete(dependent):
                count += 1
                self.invalidations.labels(strategy='dependency', reason=dependency_key).inc()
        
        # Clean up dependency graph
        del self.dependents_graph[dependency_key]
        
        return count
    
    async def invalidate_by_pattern(self, pattern: str) -> int:
        """Invalidate entries matching a pattern."""
        if hasattr(self.cache, 'l2_cache'):
            # Multi-level cache
            redis_client = self.cache.l2_cache
        elif hasattr(self.cache, 'entries'):
            # Single-level cache - scan entries
            matching_keys = [
                key for key in self.cache.entries.keys()
                if self._matches_pattern(key, pattern)
            ]
            count = 0
            for key in matching_keys:
                if await self.cache.delete(key):
                    count += 1
                    self.invalidations.labels(strategy='pattern', reason=pattern).inc()
            return count
        else:
            # Redis cache
            redis_client = self.cache
        
        # Use Redis SCAN for pattern matching
        cursor = 0
        count = 0
        
        while True:
            cursor, keys = await redis_client.execute_command('scan', cursor, match=pattern)
            
            for key in keys:
                if await self.cache.delete(key):
                    count += 1
                    self.invalidations.labels(strategy='pattern', reason=pattern).inc()
            
            if cursor == 0:
                break
        
        return count
    
    def _matches_pattern(self, key: str, pattern: str) -> bool:
        """Check if key matches pattern (simple glob-style matching)."""
        import fnmatch
        return fnmatch.fnmatch(key, pattern)
    
    async def register_tag(self, key: str, tag: str) -> None:
        """Register a key with a tag."""
        if tag not in self.tag_registry:
            self.tag_registry[tag] = set()
        self.tag_registry[tag].add(key)
    
    async def register_dependency(self, key: str, dependency: str) -> None:
        """Register a dependency relationship."""
        if key not in self.dependency_graph:
            self.dependency_graph[key] = set()
        self.dependency_graph[key].add(dependency)
        
        if dependency not in self.dependents_graph:
            self.dependents_graph[dependency] = set()
        self.dependents_graph[dependency].add(key)
    
    async def get_invalidation_stats(self) -> Dict[str, Any]:
        """Get invalidation statistics."""
        return {
            'registered_tags': len(self.tag_registry),
            'dependency_relationships': len(self.dependency_graph),
            'total_tagged_keys': sum(len(keys) for keys in self.tag_registry.values()),
            'total_dependent_keys': sum(len(deps) for deps in self.dependents_graph.values())
        }


class CacheWarmupStrategy:
    """Cache warming and preloading strategies."""
    
    def __init__(self, cache: Union[CacheStrategy, MultiLevelCache]):
        self.cache = cache
        self.warmup_tasks: Dict[str, asyncio.Task] = {}
        
        # Prometheus metrics
        self.warmup_operations = Counter(
            'cache_warmup_operations_total',
            'Total cache warmup operations',
            ['status']
        )
        
        self.warmup_duration = Histogram(
            'cache_warmup_duration_seconds',
            'Cache warmup operation duration'
        )
    
    async def warmup_batch(
        self,
        data_loader: Callable[[], List[Tuple[str, Any]]],
        batch_size: int = 100,
        ttl: Optional[float] = None
    ) -> int:
        """Warmup cache with batch data loading."""
        start_time = time.time()
        
        try:
            # Load data in batches
            data = data_loader()
            warmed_count = 0
            
            for i in range(0, len(data), batch_size):
                batch = data[i:i + batch_size]
                
                # Set batch in cache
                tasks = [
                    self.cache.set(key, value, ttl)
                    for key, value in batch
                ]
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                warmed_count += sum(1 for result in results if result is True)
            
            duration = time.time() - start_time
            self.warmup_operations.labels(status='success').inc()
            self.warmup_duration.observe(duration)
            
            logger.info(
                "Cache warmup completed",
                warmed_count=warmed_count,
                total_items=len(data),
                duration=duration
            )
            
            return warmed_count
            
        except Exception as e:
            self.warmup_operations.labels(status='error').inc()
            logger.error("Cache warmup failed", error=str(e))
            raise
    
    async def warmup_async(
        self,
        key_generator: Callable[[], List[str]],
        value_loader: Callable[[str], Any],
        concurrency: int = 10,
        ttl: Optional[float] = None
    ) -> int:
        """Warmup cache with async data loading."""
        start_time = time.time()
        
        try:
            keys = key_generator()
            semaphore = asyncio.Semaphore(concurrency)
            
            async def load_and_cache(key: str) -> bool:
                async with semaphore:
                    try:
                        value = await asyncio.get_event_loop().run_in_executor(
                            None, value_loader, key
                        )
                        return await self.cache.set(key, value, ttl)
                    except Exception as e:
                        logger.error("Failed to warmup key", key=key, error=str(e))
                        return False
            
            tasks = [load_and_cache(key) for key in keys]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            warmed_count = sum(1 for result in results if result is True)
            duration = time.time() - start_time
            
            self.warmup_operations.labels(status='success').inc()
            self.warmup_duration.observe(duration)
            
            logger.info(
                "Async cache warmup completed",
                warmed_count=warmed_count,
                total_keys=len(keys),
                duration=duration
            )
            
            return warmed_count
            
        except Exception as e:
            self.warmup_operations.labels(status='error').inc()
            logger.error("Async cache warmup failed", error=str(e))
            raise
    
    async def schedule_warmup(
        self,
        name: str,
        warmup_func: Callable[[], Any],
        interval: float = 3600  # 1 hour
    ) -> None:
        """Schedule periodic cache warmup."""
        async def warmup_loop():
            while True:
                try:
                    await asyncio.sleep(interval)
                    await warmup_func()
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    logger.error("Scheduled warmup failed", name=name, error=str(e))
        
        if name in self.warmup_tasks:
            self.warmup_tasks[name].cancel()
        
        self.warmup_tasks[name] = asyncio.create_task(warmup_loop())
    
    async def stop_warmup(self, name: str) -> None:
        """Stop scheduled warmup."""
        if name in self.warmup_tasks:
            self.warmup_tasks[name].cancel()
            del self.warmup_tasks[name]
    
    async def stop_all_warmups(self) -> None:
        """Stop all scheduled warmups."""
        for task in self.warmup_tasks.values():
            task.cancel()
        self.warmup_tasks.clear()


# Factory functions for common cache configurations
def create_lru_cache(max_size: int = 1000, ttl: Optional[float] = None) -> LRUCache:
    """Create LRU cache with specified configuration."""
    return LRUCache(max_size=max_size, ttl=ttl)


def create_ttl_cache(max_size: int = 1000, ttl: float = 3600) -> TTLCache:
    """Create TTL cache with specified configuration."""
    return TTLCache(max_size=max_size, ttl=ttl)


def create_multi_level_cache(
    l1_max_size: int = 1000,
    l1_ttl: Optional[float] = None,
    l2_ttl: int = 3600,
    promote_threshold: int = 2
) -> MultiLevelCache:
    """Create multi-level cache with specified configuration."""
    l1_cache = create_lru_cache(l1_max_size, l1_ttl)
    return MultiLevelCache(
        l1_strategy=l1_cache,
        l2_ttl=l2_ttl,
        promote_threshold=promote_threshold
    )