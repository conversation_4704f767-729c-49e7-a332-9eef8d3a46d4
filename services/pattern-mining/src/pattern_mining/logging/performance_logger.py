"""
Performance Logging System

Specialized logging for performance monitoring including:
- Request/response timing
- Database query performance
- ML inference performance
- Resource utilization logging
- Performance alert generation
"""

import time
import asyncio
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field
from contextlib import asynccontextmanager
from datetime import datetime, timed<PERSON>ta
from collections import deque, defaultdict
import logging
from functools import wraps
import threading
from enum import Enum

from .structured_logger import StructuredLogger, LogLevel, get_logger

logger = get_logger(__name__)


class PerformanceLevel(Enum):
    """Performance level classifications."""
    EXCELLENT = "excellent"
    GOOD = "good"
    ACCEPTABLE = "acceptable"
    POOR = "poor"
    CRITICAL = "critical"


@dataclass
class PerformanceThreshold:
    """Performance threshold configuration."""
    metric_name: str
    excellent_threshold: float
    good_threshold: float
    acceptable_threshold: float
    poor_threshold: float
    unit: str = "ms"
    
    def classify(self, value: float) -> PerformanceLevel:
        """Classify performance level based on value."""
        if value <= self.excellent_threshold:
            return PerformanceLevel.EXCELLENT
        elif value <= self.good_threshold:
            return PerformanceLevel.GOOD
        elif value <= self.acceptable_threshold:
            return PerformanceLevel.ACCEPTABLE
        elif value <= self.poor_threshold:
            return PerformanceLevel.POOR
        else:
            return PerformanceLevel.CRITICAL


@dataclass
class PerformanceMetric:
    """Performance metric data."""
    timestamp: datetime
    metric_name: str
    value: float
    unit: str
    level: PerformanceLevel
    context: Dict[str, Any] = field(default_factory=dict)
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class PerformanceEvent:
    """Performance event for tracking operations."""
    operation_id: str
    operation_name: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    status: str = "running"
    metadata: Dict[str, Any] = field(default_factory=dict)
    metrics: List[PerformanceMetric] = field(default_factory=list)


class PerformanceLogger:
    """
    Performance logging system for tracking and analyzing operation performance.
    
    Provides comprehensive performance logging with:
    - Automatic timing and classification
    - Performance thresholds and alerting
    - Detailed performance metrics
    - Operation correlation
    - Performance trend analysis
    """
    
    def __init__(self, service_name: str = "pattern-mining"):
        """Initialize performance logger."""
        self.service_name = service_name
        self.logger = get_logger(f"{service_name}.performance")
        
        # Performance tracking
        self._active_operations: Dict[str, PerformanceEvent] = {}
        self._performance_history: deque = deque(maxlen=10000)
        self._performance_metrics: deque = deque(maxlen=50000)
        
        # Thresholds
        self._thresholds: Dict[str, PerformanceThreshold] = {}
        self._init_default_thresholds()
        
        # Alerts
        self._alert_callbacks: List[Callable] = []
        
        # Thread safety
        self._lock = threading.Lock()
        
        logger.info("Performance logger initialized", service_name=service_name)
    
    def _init_default_thresholds(self):
        """Initialize default performance thresholds."""
        self._thresholds.update({
            'http_request': PerformanceThreshold(
                metric_name='http_request',
                excellent_threshold=100,  # 100ms
                good_threshold=500,       # 500ms
                acceptable_threshold=1000, # 1s
                poor_threshold=3000,      # 3s
                unit='ms'
            ),
            'database_query': PerformanceThreshold(
                metric_name='database_query',
                excellent_threshold=10,   # 10ms
                good_threshold=50,        # 50ms
                acceptable_threshold=100, # 100ms
                poor_threshold=500,       # 500ms
                unit='ms'
            ),
            'ml_inference': PerformanceThreshold(
                metric_name='ml_inference',
                excellent_threshold=100,  # 100ms
                good_threshold=500,       # 500ms
                acceptable_threshold=1000, # 1s
                poor_threshold=5000,      # 5s
                unit='ms'
            ),
            'pattern_detection': PerformanceThreshold(
                metric_name='pattern_detection',
                excellent_threshold=50,   # 50ms
                good_threshold=200,       # 200ms
                acceptable_threshold=500, # 500ms
                poor_threshold=2000,      # 2s
                unit='ms'
            ),
            'file_processing': PerformanceThreshold(
                metric_name='file_processing',
                excellent_threshold=500,  # 500ms
                good_threshold=2000,      # 2s
                acceptable_threshold=5000, # 5s
                poor_threshold=15000,     # 15s
                unit='ms'
            )
        })
    
    def set_threshold(self, threshold: PerformanceThreshold):
        """Set performance threshold."""
        with self._lock:
            self._thresholds[threshold.metric_name] = threshold
        
        logger.info(
            "Performance threshold updated",
            metric_name=threshold.metric_name,
            thresholds={
                'excellent': threshold.excellent_threshold,
                'good': threshold.good_threshold,
                'acceptable': threshold.acceptable_threshold,
                'poor': threshold.poor_threshold,
                'unit': threshold.unit
            }
        )
    
    def add_alert_callback(self, callback: Callable[[PerformanceMetric], None]):
        """Add alert callback for performance issues."""
        self._alert_callbacks.append(callback)
    
    def start_operation(self, operation_name: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """Start tracking a performance operation."""
        operation_id = f"{operation_name}_{int(time.time() * 1000)}_{id(self)}"
        
        event = PerformanceEvent(
            operation_id=operation_id,
            operation_name=operation_name,
            start_time=time.time(),
            metadata=metadata or {}
        )
        
        with self._lock:
            self._active_operations[operation_id] = event
        
        logger.debug(
            "Performance operation started",
            operation_id=operation_id,
            operation_name=operation_name,
            metadata=metadata
        )
        
        return operation_id
    
    def end_operation(self, operation_id: str, status: str = "completed", 
                     metadata: Optional[Dict[str, Any]] = None) -> Optional[PerformanceEvent]:
        """End tracking a performance operation."""
        with self._lock:
            event = self._active_operations.get(operation_id)
            if not event:
                logger.warning(
                    "Operation not found for completion",
                    operation_id=operation_id
                )
                return None
        
        # Update event
        event.end_time = time.time()
        event.duration = event.end_time - event.start_time
        event.status = status
        
        if metadata:
            event.metadata.update(metadata)
        
        # Get performance level
        threshold = self._thresholds.get(event.operation_name)
        if threshold:
            duration_ms = event.duration * 1000
            performance_level = threshold.classify(duration_ms)
        else:
            performance_level = PerformanceLevel.ACCEPTABLE
        
        # Create performance metric
        metric = PerformanceMetric(
            timestamp=datetime.now(),
            metric_name=event.operation_name,
            value=event.duration * 1000,  # Convert to ms
            unit='ms',
            level=performance_level,
            context=event.metadata,
            tags={'operation_id': operation_id, 'status': status}
        )
        
        event.metrics.append(metric)
        
        # Log performance
        self._log_performance_event(event, metric)
        
        # Check for alerts
        self._check_performance_alerts(metric)
        
        # Move to history
        with self._lock:
            self._performance_history.append(event)
            self._performance_metrics.append(metric)
            if operation_id in self._active_operations:
                del self._active_operations[operation_id]
        
        return event
    
    def _log_performance_event(self, event: PerformanceEvent, metric: PerformanceMetric):
        """Log performance event."""
        duration_ms = event.duration * 1000
        
        # Choose log level based on performance
        if metric.level == PerformanceLevel.CRITICAL:
            log_level = LogLevel.ERROR
        elif metric.level == PerformanceLevel.POOR:
            log_level = LogLevel.WARNING
        elif metric.level == PerformanceLevel.ACCEPTABLE:
            log_level = LogLevel.INFO
        else:
            log_level = LogLevel.DEBUG
        
        # Log the event
        log_data = {
            'operation_id': event.operation_id,
            'operation_name': event.operation_name,
            'duration_ms': duration_ms,
            'performance_level': metric.level.value,
            'status': event.status,
            'metadata': event.metadata,
            'event_type': 'performance_operation'
        }
        
        self.logger._log(
            log_level,
            f"Performance operation completed: {event.operation_name}",
            log_data
        )
    
    def _check_performance_alerts(self, metric: PerformanceMetric):
        """Check if performance metric triggers alerts."""
        if metric.level in [PerformanceLevel.POOR, PerformanceLevel.CRITICAL]:
            for callback in self._alert_callbacks:
                try:
                    callback(metric)
                except Exception as e:
                    logger.error(
                        "Error in performance alert callback",
                        exception=e,
                        metric_name=metric.metric_name,
                        metric_value=metric.value
                    )
    
    @asynccontextmanager
    async def track_operation(self, operation_name: str, metadata: Optional[Dict[str, Any]] = None):
        """Context manager for tracking operation performance."""
        operation_id = self.start_operation(operation_name, metadata)
        try:
            yield operation_id
        except Exception as e:
            self.end_operation(operation_id, "failed", {"error": str(e)})
            raise
        else:
            self.end_operation(operation_id, "completed")
    
    def track_function(self, operation_name: Optional[str] = None, 
                      metadata: Optional[Dict[str, Any]] = None):
        """Decorator for tracking function performance."""
        def decorator(func):
            nonlocal operation_name
            if operation_name is None:
                operation_name = f"{func.__module__}.{func.__name__}"
            
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                async with self.track_operation(operation_name, metadata):
                    return await func(*args, **kwargs)
            
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                operation_id = self.start_operation(operation_name, metadata)
                try:
                    result = func(*args, **kwargs)
                    self.end_operation(operation_id, "completed")
                    return result
                except Exception as e:
                    self.end_operation(operation_id, "failed", {"error": str(e)})
                    raise
            
            return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
        return decorator
    
    def log_http_request(self, method: str, url: str, status_code: int, 
                        duration: float, request_size: int = None, 
                        response_size: int = None, user_agent: str = None):
        """Log HTTP request performance."""
        duration_ms = duration * 1000
        
        # Get performance level
        threshold = self._thresholds.get('http_request')
        performance_level = threshold.classify(duration_ms) if threshold else PerformanceLevel.ACCEPTABLE
        
        # Create metric
        metric = PerformanceMetric(
            timestamp=datetime.now(),
            metric_name='http_request',
            value=duration_ms,
            unit='ms',
            level=performance_level,
            context={
                'method': method,
                'url': url,
                'status_code': status_code,
                'request_size': request_size,
                'response_size': response_size,
                'user_agent': user_agent
            }
        )
        
        # Log
        log_level = LogLevel.WARNING if performance_level == PerformanceLevel.POOR else LogLevel.INFO
        self.logger._log(
            log_level,
            f"HTTP request performance: {method} {url}",
            {
                'method': method,
                'url': url,
                'status_code': status_code,
                'duration_ms': duration_ms,
                'performance_level': performance_level.value,
                'request_size_bytes': request_size,
                'response_size_bytes': response_size,
                'user_agent': user_agent,
                'event_type': 'http_request_performance'
            }
        )
        
        # Store metric
        with self._lock:
            self._performance_metrics.append(metric)
        
        # Check alerts
        self._check_performance_alerts(metric)
    
    def log_database_query(self, query_type: str, table: str, duration: float,
                          rows_affected: int = None, query_hash: str = None):
        """Log database query performance."""
        duration_ms = duration * 1000
        
        # Get performance level
        threshold = self._thresholds.get('database_query')
        performance_level = threshold.classify(duration_ms) if threshold else PerformanceLevel.ACCEPTABLE
        
        # Create metric
        metric = PerformanceMetric(
            timestamp=datetime.now(),
            metric_name='database_query',
            value=duration_ms,
            unit='ms',
            level=performance_level,
            context={
                'query_type': query_type,
                'table': table,
                'rows_affected': rows_affected,
                'query_hash': query_hash
            }
        )
        
        # Log
        log_level = LogLevel.WARNING if performance_level == PerformanceLevel.POOR else LogLevel.INFO
        self.logger._log(
            log_level,
            f"Database query performance: {query_type} on {table}",
            {
                'query_type': query_type,
                'table': table,
                'duration_ms': duration_ms,
                'performance_level': performance_level.value,
                'rows_affected': rows_affected,
                'query_hash': query_hash,
                'event_type': 'database_query_performance'
            }
        )
        
        # Store metric
        with self._lock:
            self._performance_metrics.append(metric)
        
        # Check alerts
        self._check_performance_alerts(metric)
    
    def log_ml_inference(self, model_name: str, model_version: str, 
                        batch_size: int, duration: float, accuracy: float = None):
        """Log ML inference performance."""
        duration_ms = duration * 1000
        
        # Get performance level
        threshold = self._thresholds.get('ml_inference')
        performance_level = threshold.classify(duration_ms) if threshold else PerformanceLevel.ACCEPTABLE
        
        # Create metric
        metric = PerformanceMetric(
            timestamp=datetime.now(),
            metric_name='ml_inference',
            value=duration_ms,
            unit='ms',
            level=performance_level,
            context={
                'model_name': model_name,
                'model_version': model_version,
                'batch_size': batch_size,
                'accuracy': accuracy
            }
        )
        
        # Log
        log_level = LogLevel.WARNING if performance_level == PerformanceLevel.POOR else LogLevel.INFO
        self.logger._log(
            log_level,
            f"ML inference performance: {model_name}",
            {
                'model_name': model_name,
                'model_version': model_version,
                'batch_size': batch_size,
                'duration_ms': duration_ms,
                'performance_level': performance_level.value,
                'accuracy': accuracy,
                'event_type': 'ml_inference_performance'
            }
        )
        
        # Store metric
        with self._lock:
            self._performance_metrics.append(metric)
        
        # Check alerts
        self._check_performance_alerts(metric)
    
    def log_pattern_detection(self, pattern_type: str, confidence: float,
                            detection_time: float, file_path: str = None):
        """Log pattern detection performance."""
        duration_ms = detection_time * 1000
        
        # Get performance level
        threshold = self._thresholds.get('pattern_detection')
        performance_level = threshold.classify(duration_ms) if threshold else PerformanceLevel.ACCEPTABLE
        
        # Create metric
        metric = PerformanceMetric(
            timestamp=datetime.now(),
            metric_name='pattern_detection',
            value=duration_ms,
            unit='ms',
            level=performance_level,
            context={
                'pattern_type': pattern_type,
                'confidence': confidence,
                'file_path': file_path
            }
        )
        
        # Log
        log_level = LogLevel.WARNING if performance_level == PerformanceLevel.POOR else LogLevel.INFO
        self.logger._log(
            log_level,
            f"Pattern detection performance: {pattern_type}",
            {
                'pattern_type': pattern_type,
                'confidence': confidence,
                'duration_ms': duration_ms,
                'performance_level': performance_level.value,
                'file_path': file_path,
                'event_type': 'pattern_detection_performance'
            }
        )
        
        # Store metric
        with self._lock:
            self._performance_metrics.append(metric)
        
        # Check alerts
        self._check_performance_alerts(metric)
    
    def log_custom_metric(self, metric_name: str, value: float, unit: str = "ms",
                         context: Optional[Dict[str, Any]] = None):
        """Log custom performance metric."""
        # Get performance level
        threshold = self._thresholds.get(metric_name)
        performance_level = threshold.classify(value) if threshold else PerformanceLevel.ACCEPTABLE
        
        # Create metric
        metric = PerformanceMetric(
            timestamp=datetime.now(),
            metric_name=metric_name,
            value=value,
            unit=unit,
            level=performance_level,
            context=context or {}
        )
        
        # Log
        log_level = LogLevel.WARNING if performance_level == PerformanceLevel.POOR else LogLevel.INFO
        self.logger._log(
            log_level,
            f"Custom performance metric: {metric_name}",
            {
                'metric_name': metric_name,
                'value': value,
                'unit': unit,
                'performance_level': performance_level.value,
                'context': context,
                'event_type': 'custom_performance_metric'
            }
        )
        
        # Store metric
        with self._lock:
            self._performance_metrics.append(metric)
        
        # Check alerts
        self._check_performance_alerts(metric)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary."""
        with self._lock:
            active_ops = len(self._active_operations)
            total_metrics = len(self._performance_metrics)
            
            # Calculate performance distribution
            level_counts = defaultdict(int)
            for metric in self._performance_metrics:
                level_counts[metric.level.value] += 1
            
            # Calculate average performance by operation
            operation_stats = defaultdict(list)
            for metric in self._performance_metrics:
                operation_stats[metric.metric_name].append(metric.value)
            
            averages = {}
            for op_name, values in operation_stats.items():
                averages[op_name] = {
                    'average': sum(values) / len(values),
                    'min': min(values),
                    'max': max(values),
                    'count': len(values)
                }
            
            return {
                'active_operations': active_ops,
                'total_metrics': total_metrics,
                'performance_distribution': dict(level_counts),
                'operation_averages': averages,
                'thresholds': {name: {
                    'excellent': t.excellent_threshold,
                    'good': t.good_threshold,
                    'acceptable': t.acceptable_threshold,
                    'poor': t.poor_threshold,
                    'unit': t.unit
                } for name, t in self._thresholds.items()}
            }
    
    def get_performance_trends(self, hours: int = 24) -> Dict[str, List[Dict]]:
        """Get performance trends over time."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        with self._lock:
            recent_metrics = [
                m for m in self._performance_metrics
                if m.timestamp > cutoff_time
            ]
        
        # Group by operation and hour
        trends = defaultdict(list)
        
        for metric in recent_metrics:
            hour_key = metric.timestamp.strftime('%Y-%m-%d %H:00')
            trends[metric.metric_name].append({
                'timestamp': hour_key,
                'value': metric.value,
                'level': metric.level.value
            })
        
        return dict(trends)
    
    def cleanup_old_data(self, max_age_hours: int = 24):
        """Clean up old performance data."""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        with self._lock:
            # Clean metrics
            old_metrics_count = len(self._performance_metrics)
            self._performance_metrics = deque(
                [m for m in self._performance_metrics if m.timestamp > cutoff_time],
                maxlen=50000
            )
            
            # Clean history
            old_history_count = len(self._performance_history)
            self._performance_history = deque(
                [e for e in self._performance_history if datetime.fromtimestamp(e.start_time) > cutoff_time],
                maxlen=10000
            )
            
            cleaned_metrics = old_metrics_count - len(self._performance_metrics)
            cleaned_history = old_history_count - len(self._performance_history)
            
            if cleaned_metrics > 0 or cleaned_history > 0:
                logger.info(
                    "Cleaned up old performance data",
                    cleaned_metrics=cleaned_metrics,
                    cleaned_history=cleaned_history,
                    max_age_hours=max_age_hours
                )


# Global performance logger instance
_performance_logger_instance: Optional[PerformanceLogger] = None


def get_performance_logger() -> PerformanceLogger:
    """Get the global performance logger instance."""
    global _performance_logger_instance
    if _performance_logger_instance is None:
        _performance_logger_instance = PerformanceLogger()
    return _performance_logger_instance


def init_performance_logger(service_name: str = "pattern-mining") -> PerformanceLogger:
    """Initialize the global performance logger instance."""
    global _performance_logger_instance
    _performance_logger_instance = PerformanceLogger(service_name)
    return _performance_logger_instance