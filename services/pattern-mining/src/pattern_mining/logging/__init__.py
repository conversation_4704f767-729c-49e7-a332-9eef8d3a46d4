"""
Logging System for Pattern Mining Service

This module provides comprehensive logging capabilities including:
- Structured logging with JSON output
- Log aggregation and correlation
- Performance logging
- Error tracking and alerting
- Audit logging for security

Components:
- StructuredLogger: Core structured logging
- PerformanceLogger: Performance-specific logging
- AuditLogger: Security and compliance logging
- ErrorTracker: Error tracking and alerting
- LogAggregator: Log aggregation and correlation
"""

from .structured_logger import StructuredLogger, LogLevel, LogContext
from .performance_logger import PerformanceLogger
from .audit_logger import AuditLogger
from .error_tracker import ErrorTracker
from .log_aggregator import LogAggregator
from .correlation import CorrelationManager

__all__ = [
    "StructuredLogger",
    "PerformanceLogger",
    "AuditLogger",
    "ErrorTracker",
    "LogAggregator",
    "CorrelationManager",
    "LogLevel",
    "LogContext"
]