"""
Structured Logging System

Production-ready structured logging with JSON output, correlation IDs,
and comprehensive context tracking for the pattern mining service.
"""

import json
import logging
import sys
import time
import traceback
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum
from contextlib import contextmanager
from contextvars import ContextVar
import threading
import uuid
import structlog
from structlog.stdlib import LoggerFactory
from structlog.processors import <PERSON><PERSON><PERSON><PERSON><PERSON>, TimeStamper, add_log_level, add_logger_name

# Context variables for correlation
correlation_id_var: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)
request_id_var: ContextVar[Optional[str]] = ContextVar('request_id', default=None)
user_id_var: ContextVar[Optional[str]] = ContextVar('user_id', default=None)
session_id_var: ContextVar[Optional[str]] = ContextVar('session_id', default=None)


class LogLevel(Enum):
    """Log levels with severity mapping."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


@dataclass
class LogContext:
    """Context information for structured logging."""
    correlation_id: Optional[str] = None
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    component: Optional[str] = None
    operation: Optional[str] = None
    trace_id: Optional[str] = None
    span_id: Optional[str] = None
    environment: Optional[str] = None
    service_name: Optional[str] = None
    version: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary, filtering out None values."""
        return {k: v for k, v in asdict(self).items() if v is not None}


@dataclass
class LogEntry:
    """Structured log entry."""
    timestamp: str
    level: str
    logger_name: str
    message: str
    context: Dict[str, Any]
    extra: Dict[str, Any]
    exception: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        entry = asdict(self)
        if not entry['exception']:
            del entry['exception']
        return entry
    
    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(self.to_dict(), default=str)


class StructuredLogger:
    """
    Production-ready structured logger with JSON output and correlation tracking.
    
    Provides comprehensive logging capabilities with:
    - JSON structured output
    - Correlation ID tracking
    - Performance metrics
    - Error tracking
    - Audit trails
    """
    
    def __init__(self, name: str, level: LogLevel = LogLevel.INFO, 
                 service_name: str = "pattern-mining", version: str = "1.0.0",
                 environment: str = "production"):
        """Initialize structured logger."""
        self.name = name
        self.level = level
        self.service_name = service_name
        self.version = version
        self.environment = environment
        
        # Configure structlog
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                TimeStamper(fmt="iso"),
                self._add_context,
                self._add_service_info,
                JSONRenderer()
            ],
            context_class=dict,
            logger_factory=LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
        
        # Get structlog logger
        self.logger = structlog.get_logger(name)
        
        # Set up Python logging
        logging.basicConfig(
            level=getattr(logging, level.value),
            format='%(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        # Internal state
        self._lock = threading.Lock()
        self._log_buffer: List[LogEntry] = []
        self._buffer_size = 1000
        
    def _add_context(self, logger, name, event_dict):
        """Add correlation context to log entry."""
        correlation_id = correlation_id_var.get()
        request_id = request_id_var.get()
        user_id = user_id_var.get()
        session_id = session_id_var.get()
        
        if correlation_id:
            event_dict['correlation_id'] = correlation_id
        if request_id:
            event_dict['request_id'] = request_id
        if user_id:
            event_dict['user_id'] = user_id
        if session_id:
            event_dict['session_id'] = session_id
        
        return event_dict
    
    def _add_service_info(self, logger, name, event_dict):
        """Add service information to log entry."""
        event_dict.update({
            'service': self.service_name,
            'version': self.version,
            'environment': self.environment,
            'hostname': self._get_hostname(),
            'pid': self._get_pid(),
            'thread_id': self._get_thread_id()
        })
        return event_dict
    
    def _get_hostname(self) -> str:
        """Get hostname."""
        import socket
        return socket.gethostname()
    
    def _get_pid(self) -> int:
        """Get process ID."""
        import os
        return os.getpid()
    
    def _get_thread_id(self) -> int:
        """Get thread ID."""
        return threading.get_ident()
    
    def _format_exception(self, exc: Exception) -> Dict[str, Any]:
        """Format exception for logging."""
        return {
            'type': type(exc).__name__,
            'message': str(exc),
            'traceback': traceback.format_exception(type(exc), exc, exc.__traceback__)
        }
    
    def _should_log(self, level: LogLevel) -> bool:
        """Check if message should be logged based on level."""
        level_values = {
            LogLevel.DEBUG: 10,
            LogLevel.INFO: 20,
            LogLevel.WARNING: 30,
            LogLevel.ERROR: 40,
            LogLevel.CRITICAL: 50
        }
        return level_values[level] >= level_values[self.level]
    
    def _log(self, level: LogLevel, message: str, extra: Optional[Dict[str, Any]] = None,
             exception: Optional[Exception] = None, **kwargs):
        """Internal logging method."""
        if not self._should_log(level):
            return
        
        # Prepare log data
        log_data = {
            'message': message,
            **(extra or {}),
            **kwargs
        }
        
        # Add exception if present
        if exception:
            log_data['exception'] = self._format_exception(exception)
        
        # Log using structlog
        log_method = getattr(self.logger, level.value.lower())
        log_method(**log_data)
    
    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Log debug message."""
        self._log(LogLevel.DEBUG, message, extra, **kwargs)
    
    def info(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Log info message."""
        self._log(LogLevel.INFO, message, extra, **kwargs)
    
    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Log warning message."""
        self._log(LogLevel.WARNING, message, extra, **kwargs)
    
    def error(self, message: str, extra: Optional[Dict[str, Any]] = None, 
              exception: Optional[Exception] = None, **kwargs):
        """Log error message."""
        self._log(LogLevel.ERROR, message, extra, exception, **kwargs)
    
    def critical(self, message: str, extra: Optional[Dict[str, Any]] = None,
                exception: Optional[Exception] = None, **kwargs):
        """Log critical message."""
        self._log(LogLevel.CRITICAL, message, extra, exception, **kwargs)
    
    def exception(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Log exception with traceback."""
        exc_info = sys.exc_info()
        if exc_info[1]:
            self._log(LogLevel.ERROR, message, extra, exc_info[1], **kwargs)
        else:
            self._log(LogLevel.ERROR, message, extra, **kwargs)
    
    @contextmanager
    def context(self, **context_vars):
        """Context manager for adding context to logs."""
        tokens = []
        
        try:
            # Set context variables
            for var_name, value in context_vars.items():
                if var_name == 'correlation_id':
                    tokens.append(correlation_id_var.set(value))
                elif var_name == 'request_id':
                    tokens.append(request_id_var.set(value))
                elif var_name == 'user_id':
                    tokens.append(user_id_var.set(value))
                elif var_name == 'session_id':
                    tokens.append(session_id_var.set(value))
            
            yield
            
        finally:
            # Reset context variables
            for token in tokens:
                if hasattr(token, 'var'):
                    token.var.reset(token)
    
    def bind(self, **kwargs) -> 'StructuredLogger':
        """Bind additional context to logger."""
        bound_logger = StructuredLogger(
            self.name,
            self.level,
            self.service_name,
            self.version,
            self.environment
        )
        bound_logger.logger = self.logger.bind(**kwargs)
        return bound_logger
    
    def with_correlation_id(self, correlation_id: str) -> 'StructuredLogger':
        """Create logger with correlation ID."""
        return self.bind(correlation_id=correlation_id)
    
    def with_request_id(self, request_id: str) -> 'StructuredLogger':
        """Create logger with request ID."""
        return self.bind(request_id=request_id)
    
    def with_user_id(self, user_id: str) -> 'StructuredLogger':
        """Create logger with user ID."""
        return self.bind(user_id=user_id)
    
    def with_session_id(self, session_id: str) -> 'StructuredLogger':
        """Create logger with session ID."""
        return self.bind(session_id=session_id)
    
    def with_context(self, context: LogContext) -> 'StructuredLogger':
        """Create logger with full context."""
        return self.bind(**context.to_dict())
    
    def log_request(self, method: str, url: str, status_code: int, 
                   duration: float, user_agent: str = None, 
                   request_size: int = None, response_size: int = None):
        """Log HTTP request."""
        self.info(
            "HTTP request processed",
            method=method,
            url=url,
            status_code=status_code,
            duration_ms=duration * 1000,
            user_agent=user_agent,
            request_size_bytes=request_size,
            response_size_bytes=response_size,
            event_type="http_request"
        )
    
    def log_database_query(self, query_type: str, table: str, duration: float,
                          rows_affected: int = None, query_hash: str = None):
        """Log database query."""
        self.info(
            "Database query executed",
            query_type=query_type,
            table=table,
            duration_ms=duration * 1000,
            rows_affected=rows_affected,
            query_hash=query_hash,
            event_type="database_query"
        )
    
    def log_ml_inference(self, model_name: str, model_version: str, 
                        batch_size: int, duration: float, accuracy: float = None):
        """Log ML inference."""
        self.info(
            "ML inference completed",
            model_name=model_name,
            model_version=model_version,
            batch_size=batch_size,
            duration_ms=duration * 1000,
            accuracy=accuracy,
            event_type="ml_inference"
        )
    
    def log_pattern_detection(self, pattern_type: str, confidence: float,
                            file_path: str, line_number: int, detection_time: float):
        """Log pattern detection."""
        self.info(
            "Pattern detected",
            pattern_type=pattern_type,
            confidence=confidence,
            file_path=file_path,
            line_number=line_number,
            detection_time_ms=detection_time * 1000,
            event_type="pattern_detection"
        )
    
    def log_security_event(self, event_type: str, severity: str, 
                          source_ip: str = None, user_id: str = None,
                          details: Dict[str, Any] = None):
        """Log security event."""
        self.warning(
            "Security event detected",
            security_event_type=event_type,
            severity=severity,
            source_ip=source_ip,
            user_id=user_id,
            details=details,
            event_type="security_event"
        )
    
    def log_performance_metric(self, metric_name: str, value: float, 
                             unit: str, tags: Dict[str, str] = None):
        """Log performance metric."""
        self.info(
            "Performance metric recorded",
            metric_name=metric_name,
            value=value,
            unit=unit,
            tags=tags,
            event_type="performance_metric"
        )
    
    def log_business_event(self, event_name: str, user_id: str = None,
                          value: float = None, metadata: Dict[str, Any] = None):
        """Log business event."""
        self.info(
            "Business event recorded",
            event_name=event_name,
            user_id=user_id,
            value=value,
            metadata=metadata,
            event_type="business_event"
        )
    
    def log_audit_event(self, action: str, resource: str, user_id: str,
                       success: bool, details: Dict[str, Any] = None):
        """Log audit event."""
        level = LogLevel.INFO if success else LogLevel.WARNING
        self._log(
            level,
            "Audit event recorded",
            {
                'action': action,
                'resource': resource,
                'user_id': user_id,
                'success': success,
                'details': details,
                'event_type': 'audit_event'
            }
        )
    
    def set_level(self, level: LogLevel):
        """Set logging level."""
        self.level = level
        logging.getLogger().setLevel(getattr(logging, level.value))
    
    def get_context(self) -> Dict[str, Any]:
        """Get current logging context."""
        return {
            'correlation_id': correlation_id_var.get(),
            'request_id': request_id_var.get(),
            'user_id': user_id_var.get(),
            'session_id': session_id_var.get()
        }
    
    def flush(self):
        """Flush any buffered logs."""
        # In this implementation, logs are written immediately
        # This method is kept for compatibility
        pass


# Global logger instances
_loggers: Dict[str, StructuredLogger] = {}
_default_logger: Optional[StructuredLogger] = None


def get_logger(name: str = None, level: LogLevel = LogLevel.INFO) -> StructuredLogger:
    """Get or create a structured logger."""
    global _loggers, _default_logger
    
    if name is None:
        if _default_logger is None:
            _default_logger = StructuredLogger("pattern-mining", level)
        return _default_logger
    
    if name not in _loggers:
        _loggers[name] = StructuredLogger(name, level)
    
    return _loggers[name]


def set_correlation_id(correlation_id: str):
    """Set correlation ID for current context."""
    correlation_id_var.set(correlation_id)


def set_request_id(request_id: str):
    """Set request ID for current context."""
    request_id_var.set(request_id)


def set_user_id(user_id: str):
    """Set user ID for current context."""
    user_id_var.set(user_id)


def set_session_id(session_id: str):
    """Set session ID for current context."""
    session_id_var.set(session_id)


def generate_correlation_id() -> str:
    """Generate a new correlation ID."""
    return str(uuid.uuid4())


def generate_request_id() -> str:
    """Generate a new request ID."""
    return str(uuid.uuid4())


@contextmanager
def logging_context(**context_vars):
    """Context manager for setting logging context."""
    tokens = []
    
    try:
        # Set context variables
        for var_name, value in context_vars.items():
            if var_name == 'correlation_id':
                tokens.append(correlation_id_var.set(value))
            elif var_name == 'request_id':
                tokens.append(request_id_var.set(value))
            elif var_name == 'user_id':
                tokens.append(user_id_var.set(value))
            elif var_name == 'session_id':
                tokens.append(session_id_var.set(value))
        
        yield
        
    finally:
        # Reset context variables
        for token in tokens:
            if hasattr(token, 'var'):
                token.var.reset(token)