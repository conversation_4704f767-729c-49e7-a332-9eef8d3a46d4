"""
Performance Metrics Collection

Advanced performance monitoring including:
- Request/response performance
- ML model inference performance
- Database query performance
- Resource utilization
- System health indicators
"""

import time
import psutil
import asyncio
import threading
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, field
from collections import deque, defaultdict
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
import logging
import json
from enum import Enum
import tracemalloc
import gc
from prometheus_client import Counter, Histogram, Gauge, Summary

logger = logging.getLogger(__name__)


class PerformanceLevel(Enum):
    """Performance level classifications."""
    EXCELLENT = "excellent"
    GOOD = "good"
    ACCEPTABLE = "acceptable"
    POOR = "poor"
    CRITICAL = "critical"


@dataclass
class PerformanceWindow:
    """Performance measurement window."""
    start_time: float
    end_time: Optional[float] = None
    operation: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    memory_start: Optional[int] = None
    memory_end: Optional[int] = None


@dataclass
class PerformanceAlert:
    """Performance alert configuration."""
    metric_name: str
    threshold_value: float
    operator: str  # "gt", "lt", "eq", "ge", "le"
    window_seconds: int
    alert_level: str
    message: str
    callback: Optional[Callable] = None


class PerformanceMetrics:
    """
    Advanced performance metrics collector.
    
    Provides comprehensive performance monitoring with alerting,
    trend analysis, and detailed performance profiling.
    """
    
    def __init__(self, enable_memory_profiling: bool = True):
        """Initialize performance metrics collector."""
        self.enable_memory_profiling = enable_memory_profiling
        self._performance_windows: Dict[str, PerformanceWindow] = {}
        self._performance_history: deque = deque(maxlen=10000)
        self._alerts: List[PerformanceAlert] = []
        self._alert_state: Dict[str, bool] = {}
        self._lock = threading.Lock()
        
        # Start memory profiling if enabled
        if self.enable_memory_profiling:
            tracemalloc.start()
        
        # Initialize performance metrics
        self._init_metrics()
        
        # Background monitoring
        self._monitoring_task = None
        self._stop_monitoring = False
        
        logger.info("Performance metrics initialized")
    
    def _init_metrics(self):
        """Initialize Prometheus metrics."""
        # Request performance metrics
        self.request_duration = Histogram(
            'request_duration_seconds',
            'Request duration in seconds',
            ['method', 'endpoint', 'status_code'],
            buckets=[0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1.0, 2.5, 5.0, 7.5, 10.0]
        )
        
        self.request_throughput = Counter(
            'requests_per_second',
            'Request throughput per second',
            ['endpoint', 'method']
        )
        
        self.concurrent_requests = Gauge(
            'concurrent_requests_current',
            'Current number of concurrent requests',
            ['endpoint']
        )
        
        # Database performance metrics
        self.db_query_duration = Histogram(
            'db_query_duration_seconds',
            'Database query duration in seconds',
            ['operation', 'table', 'query_type'],
            buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
        )
        
        self.db_connection_pool_size = Gauge(
            'db_connection_pool_size',
            'Database connection pool size',
            ['database', 'state']
        )
        
        self.db_query_cache_hit_ratio = Gauge(
            'db_query_cache_hit_ratio',
            'Database query cache hit ratio',
            ['cache_type']
        )
        
        # ML model performance metrics
        self.ml_inference_duration = Histogram(
            'ml_inference_duration_seconds',
            'ML model inference duration in seconds',
            ['model_name', 'batch_size_range', 'input_size_range'],
            buckets=[0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
        )
        
        self.ml_model_accuracy = Gauge(
            'ml_model_accuracy_score',
            'ML model accuracy score',
            ['model_name', 'evaluation_type']
        )
        
        self.ml_throughput = Gauge(
            'ml_throughput_predictions_per_second',
            'ML model throughput in predictions per second',
            ['model_name']
        )
        
        # Memory performance metrics
        self.memory_usage = Gauge(
            'memory_usage_bytes',
            'Memory usage in bytes',
            ['memory_type', 'component']
        )
        
        self.memory_allocation_rate = Gauge(
            'memory_allocation_rate_bytes_per_second',
            'Memory allocation rate in bytes per second',
            ['component']
        )
        
        self.garbage_collection_duration = Histogram(
            'garbage_collection_duration_seconds',
            'Garbage collection duration in seconds',
            ['generation'],
            buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0]
        )
        
        # CPU performance metrics
        self.cpu_usage = Gauge(
            'cpu_usage_percent',
            'CPU usage percentage',
            ['cpu_type', 'component']
        )
        
        self.cpu_context_switches = Counter(
            'cpu_context_switches_total',
            'Total CPU context switches',
            ['switch_type']
        )
        
        # I/O performance metrics
        self.io_operations = Counter(
            'io_operations_total',
            'Total I/O operations',
            ['operation_type', 'device']
        )
        
        self.io_duration = Histogram(
            'io_duration_seconds',
            'I/O operation duration in seconds',
            ['operation_type', 'device'],
            buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0]
        )
        
        # Network performance metrics
        self.network_bandwidth = Gauge(
            'network_bandwidth_bytes_per_second',
            'Network bandwidth in bytes per second',
            ['interface', 'direction']
        )
        
        self.network_latency = Histogram(
            'network_latency_seconds',
            'Network latency in seconds',
            ['destination', 'protocol'],
            buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0]
        )
        
        # Cache performance metrics
        self.cache_hit_ratio = Gauge(
            'cache_hit_ratio',
            'Cache hit ratio',
            ['cache_name', 'cache_type']
        )
        
        self.cache_size = Gauge(
            'cache_size_bytes',
            'Cache size in bytes',
            ['cache_name']
        )
        
        self.cache_eviction_rate = Gauge(
            'cache_eviction_rate_per_second',
            'Cache eviction rate per second',
            ['cache_name', 'eviction_reason']
        )
        
        # Application performance metrics
        self.response_time_percentiles = Summary(
            'response_time_percentiles',
            'Response time percentiles',
            ['endpoint', 'method']
        )
        
        self.error_rate = Gauge(
            'error_rate_percent',
            'Error rate percentage',
            ['component', 'error_type']
        )
        
        self.saturation_level = Gauge(
            'saturation_level_percent',
            'Resource saturation level percentage',
            ['resource_type']
        )
    
    @asynccontextmanager
    async def measure_performance(self, operation: str, metadata: Optional[Dict] = None):
        """Context manager for measuring operation performance."""
        window_id = f"{operation}_{time.time()}_{id(self)}"
        
        # Start performance measurement
        start_time = time.time()
        memory_start = None
        
        if self.enable_memory_profiling:
            memory_start = tracemalloc.get_traced_memory()[0]
        
        window = PerformanceWindow(
            start_time=start_time,
            operation=operation,
            metadata=metadata or {},
            memory_start=memory_start
        )
        
        with self._lock:
            self._performance_windows[window_id] = window
        
        try:
            yield window
        finally:
            # End performance measurement
            end_time = time.time()
            duration = end_time - start_time
            
            memory_end = None
            if self.enable_memory_profiling:
                memory_end = tracemalloc.get_traced_memory()[0]
            
            window.end_time = end_time
            window.memory_end = memory_end
            
            # Record metrics
            self._record_performance_metrics(window, duration)
            
            # Move to history
            with self._lock:
                self._performance_history.append(window)
                if window_id in self._performance_windows:
                    del self._performance_windows[window_id]
    
    def _record_performance_metrics(self, window: PerformanceWindow, duration: float):
        """Record performance metrics from a measurement window."""
        operation = window.operation
        metadata = window.metadata
        
        # Record duration based on operation type
        if operation.startswith('request_'):
            method = metadata.get('method', 'unknown')
            endpoint = metadata.get('endpoint', 'unknown')
            status_code = metadata.get('status_code', 'unknown')
            
            self.request_duration.labels(
                method=method,
                endpoint=endpoint,
                status_code=str(status_code)
            ).observe(duration)
            
            self.response_time_percentiles.labels(
                endpoint=endpoint,
                method=method
            ).observe(duration)
        
        elif operation.startswith('db_'):
            db_operation = metadata.get('operation', 'unknown')
            table = metadata.get('table', 'unknown')
            query_type = metadata.get('query_type', 'unknown')
            
            self.db_query_duration.labels(
                operation=db_operation,
                table=table,
                query_type=query_type
            ).observe(duration)
        
        elif operation.startswith('ml_'):
            model_name = metadata.get('model_name', 'unknown')
            batch_size = metadata.get('batch_size', 1)
            input_size = metadata.get('input_size', 0)
            
            batch_size_range = self._get_batch_size_range(batch_size)
            input_size_range = self._get_input_size_range(input_size)
            
            self.ml_inference_duration.labels(
                model_name=model_name,
                batch_size_range=batch_size_range,
                input_size_range=input_size_range
            ).observe(duration)
        
        # Record memory usage if available
        if window.memory_start is not None and window.memory_end is not None:
            memory_delta = window.memory_end - window.memory_start
            
            self.memory_usage.labels(
                memory_type='allocated',
                component=operation
            ).set(memory_delta)
    
    def record_request_metrics(self, method: str, endpoint: str, status_code: int,
                             duration: float, request_size: int = 0, response_size: int = 0):
        """Record HTTP request metrics."""
        labels = {
            'method': method,
            'endpoint': endpoint,
            'status_code': str(status_code)
        }
        
        self.request_duration.labels(**labels).observe(duration)
        self.request_throughput.labels(endpoint=endpoint, method=method).inc()
        
        # Calculate throughput
        throughput = 1.0 / duration if duration > 0 else 0
        
        # Record error rate
        if status_code >= 400:
            self.error_rate.labels(
                component='api',
                error_type='http_error'
            ).inc()
    
    def record_database_metrics(self, operation: str, table: str, duration: float,
                              rows_affected: int = 0, cache_hit: bool = False):
        """Record database operation metrics."""
        self.db_query_duration.labels(
            operation=operation,
            table=table,
            query_type='select' if operation.lower().startswith('select') else 'write'
        ).observe(duration)
        
        # Update cache hit ratio
        if cache_hit:
            self.db_query_cache_hit_ratio.labels(cache_type='query').inc()
    
    def record_ml_metrics(self, model_name: str, inference_time: float,
                         batch_size: int, accuracy: Optional[float] = None,
                         throughput: Optional[float] = None):
        """Record ML model metrics."""
        batch_size_range = self._get_batch_size_range(batch_size)
        
        self.ml_inference_duration.labels(
            model_name=model_name,
            batch_size_range=batch_size_range,
            input_size_range='unknown'
        ).observe(inference_time)
        
        if accuracy is not None:
            self.ml_model_accuracy.labels(
                model_name=model_name,
                evaluation_type='realtime'
            ).set(accuracy)
        
        if throughput is not None:
            self.ml_throughput.labels(model_name=model_name).set(throughput)
    
    def update_system_metrics(self):
        """Update system performance metrics."""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=0.1)
            self.cpu_usage.labels(cpu_type='overall', component='system').set(cpu_percent)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            self.memory_usage.labels(memory_type='used', component='system').set(memory.used)
            self.memory_usage.labels(memory_type='available', component='system').set(memory.available)
            
            # I/O metrics
            disk_io = psutil.disk_io_counters()
            if disk_io:
                self.io_operations.labels(operation_type='read', device='disk').inc(disk_io.read_count)
                self.io_operations.labels(operation_type='write', device='disk').inc(disk_io.write_count)
            
            # Network metrics
            net_io = psutil.net_io_counters()
            if net_io:
                self.network_bandwidth.labels(interface='total', direction='sent').set(net_io.bytes_sent)
                self.network_bandwidth.labels(interface='total', direction='received').set(net_io.bytes_recv)
            
            # Calculate saturation levels
            cpu_saturation = min(cpu_percent, 100.0)
            memory_saturation = (memory.used / memory.total) * 100
            
            self.saturation_level.labels(resource_type='cpu').set(cpu_saturation)
            self.saturation_level.labels(resource_type='memory').set(memory_saturation)
            
        except Exception as e:
            logger.error(f"Error updating system metrics: {e}")
    
    def add_performance_alert(self, alert: PerformanceAlert):
        """Add a performance alert."""
        self._alerts.append(alert)
        self._alert_state[alert.metric_name] = False
        logger.info(f"Added performance alert: {alert.metric_name}")
    
    def check_alerts(self):
        """Check all performance alerts."""
        for alert in self._alerts:
            try:
                current_value = self._get_metric_value(alert.metric_name)
                if current_value is None:
                    continue
                
                triggered = self._evaluate_alert_condition(
                    current_value, alert.threshold_value, alert.operator
                )
                
                previous_state = self._alert_state.get(alert.metric_name, False)
                
                if triggered and not previous_state:
                    # Alert triggered
                    self._trigger_alert(alert, current_value)
                    self._alert_state[alert.metric_name] = True
                elif not triggered and previous_state:
                    # Alert resolved
                    self._resolve_alert(alert, current_value)
                    self._alert_state[alert.metric_name] = False
                    
            except Exception as e:
                logger.error(f"Error checking alert {alert.metric_name}: {e}")
    
    def _get_metric_value(self, metric_name: str) -> Optional[float]:
        """Get current value of a metric."""
        # This would need to be implemented based on your metric storage
        # For now, return None
        return None
    
    def _evaluate_alert_condition(self, current_value: float, threshold: float, operator: str) -> bool:
        """Evaluate alert condition."""
        if operator == 'gt':
            return current_value > threshold
        elif operator == 'lt':
            return current_value < threshold
        elif operator == 'ge':
            return current_value >= threshold
        elif operator == 'le':
            return current_value <= threshold
        elif operator == 'eq':
            return current_value == threshold
        else:
            return False
    
    def _trigger_alert(self, alert: PerformanceAlert, value: float):
        """Trigger a performance alert."""
        message = f"Performance alert triggered: {alert.message} (value: {value})"
        logger.warning(message)
        
        if alert.callback:
            try:
                alert.callback(alert, value)
            except Exception as e:
                logger.error(f"Error executing alert callback: {e}")
    
    def _resolve_alert(self, alert: PerformanceAlert, value: float):
        """Resolve a performance alert."""
        message = f"Performance alert resolved: {alert.message} (value: {value})"
        logger.info(message)
    
    def _get_batch_size_range(self, batch_size: int) -> str:
        """Get batch size range category."""
        if batch_size <= 1:
            return "single"
        elif batch_size <= 10:
            return "small"
        elif batch_size <= 50:
            return "medium"
        elif batch_size <= 100:
            return "large"
        else:
            return "very_large"
    
    def _get_input_size_range(self, input_size: int) -> str:
        """Get input size range category."""
        if input_size <= 1000:
            return "small"
        elif input_size <= 10000:
            return "medium"
        elif input_size <= 100000:
            return "large"
        else:
            return "very_large"
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary."""
        with self._lock:
            active_operations = len(self._performance_windows)
            completed_operations = len(self._performance_history)
            
            # Calculate average durations
            if self._performance_history:
                durations = [
                    (w.end_time - w.start_time) for w in self._performance_history
                    if w.end_time is not None
                ]
                avg_duration = sum(durations) / len(durations) if durations else 0
            else:
                avg_duration = 0
            
            return {
                'active_operations': active_operations,
                'completed_operations': completed_operations,
                'average_duration': avg_duration,
                'alerts_active': sum(1 for state in self._alert_state.values() if state),
                'memory_profiling_enabled': self.enable_memory_profiling
            }
    
    def get_performance_trends(self, hours: int = 24) -> Dict[str, List[Dict]]:
        """Get performance trends over time."""
        cutoff_time = time.time() - (hours * 3600)
        
        with self._lock:
            recent_windows = [
                w for w in self._performance_history
                if w.start_time > cutoff_time and w.end_time is not None
            ]
        
        # Group by operation and hour
        trends = defaultdict(list)
        
        for window in recent_windows:
            operation = window.operation
            duration = window.end_time - window.start_time
            hour = int(window.start_time // 3600) * 3600
            
            trends[operation].append({
                'timestamp': hour,
                'duration': duration,
                'memory_delta': (window.memory_end or 0) - (window.memory_start or 0)
            })
        
        return dict(trends)
    
    async def start_monitoring(self, interval: int = 30):
        """Start background performance monitoring."""
        self._stop_monitoring = False
        
        async def monitor():
            while not self._stop_monitoring:
                try:
                    self.update_system_metrics()
                    self.check_alerts()
                    await asyncio.sleep(interval)
                except Exception as e:
                    logger.error(f"Error in performance monitoring: {e}")
                    await asyncio.sleep(5)
        
        self._monitoring_task = asyncio.create_task(monitor())
        logger.info("Performance monitoring started")
    
    async def stop_monitoring(self):
        """Stop background performance monitoring."""
        self._stop_monitoring = True
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("Performance monitoring stopped")
    
    def cleanup_old_data(self, max_age_hours: int = 24):
        """Clean up old performance data."""
        cutoff_time = time.time() - (max_age_hours * 3600)
        
        with self._lock:
            self._performance_history = deque(
                [w for w in self._performance_history if w.start_time > cutoff_time],
                maxlen=10000
            )
        
        logger.info(f"Cleaned up performance data older than {max_age_hours} hours")


# Global performance metrics instance
_performance_metrics_instance: Optional[PerformanceMetrics] = None


def get_performance_metrics() -> PerformanceMetrics:
    """Get the global performance metrics instance."""
    global _performance_metrics_instance
    if _performance_metrics_instance is None:
        _performance_metrics_instance = PerformanceMetrics()
    return _performance_metrics_instance


def init_performance_metrics(enable_memory_profiling: bool = True) -> PerformanceMetrics:
    """Initialize the global performance metrics instance."""
    global _performance_metrics_instance
    _performance_metrics_instance = PerformanceMetrics(enable_memory_profiling)
    return _performance_metrics_instance