"""
Prometheus Metrics Collection

Core Prometheus metrics for the pattern mining service including:
- HTTP request metrics
- Database operation metrics
- ML model performance metrics
- System resource metrics
- Custom business metrics
"""

import time
import psutil
import asyncio
from typing import Dict, Any, Optional, List
from prometheus_client import Counter, Histogram, Gauge, Info, Enum, Summary
from prometheus_client import CollectorRegistry, generate_latest, CONTENT_TYPE_LATEST
from contextlib import asynccontextmanager
from functools import wraps
import logging
import threading
from dataclasses import dataclass
from enum import Enum as PyEnum

logger = logging.getLogger(__name__)


class MetricType(PyEnum):
    """Metric types supported by the monitoring system."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"
    INFO = "info"
    ENUM = "enum"


@dataclass
class MetricConfig:
    """Configuration for a metric."""
    name: str
    description: str
    labels: List[str]
    metric_type: MetricType
    buckets: Optional[List[float]] = None
    states: Optional[List[str]] = None


class PrometheusMetrics:
    """
    Prometheus metrics collector for the pattern mining service.
    
    Provides a centralized way to collect and expose metrics to Prometheus.
    """
    
    def __init__(self, registry: Optional[CollectorRegistry] = None):
        """Initialize Prometheus metrics collector."""
        self.registry = registry or CollectorRegistry()
        self._metrics: Dict[str, Any] = {}
        self._lock = threading.Lock()
        
        # Initialize core metrics
        self._init_http_metrics()
        self._init_database_metrics()
        self._init_ml_metrics()
        self._init_system_metrics()
        self._init_business_metrics()
        
        # Start background metric collection
        self._collection_task = None
        self._stop_collection = False
        
        logger.info("Prometheus metrics initialized")
    
    def _init_http_metrics(self):
        """Initialize HTTP request metrics."""
        self._metrics.update({
            'http_requests_total': Counter(
                'http_requests_total',
                'Total HTTP requests',
                ['method', 'endpoint', 'status_code'],
                registry=self.registry
            ),
            'http_request_duration_seconds': Histogram(
                'http_request_duration_seconds',
                'HTTP request duration in seconds',
                ['method', 'endpoint'],
                buckets=[0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1.0, 2.5, 5.0, 7.5, 10.0],
                registry=self.registry
            ),
            'http_request_size_bytes': Histogram(
                'http_request_size_bytes',
                'HTTP request size in bytes',
                ['method', 'endpoint'],
                buckets=[100, 1000, 10000, 100000, 1000000, 10000000],
                registry=self.registry
            ),
            'http_response_size_bytes': Histogram(
                'http_response_size_bytes',
                'HTTP response size in bytes',
                ['method', 'endpoint'],
                registry=self.registry
            ),
            'http_concurrent_requests': Gauge(
                'http_concurrent_requests',
                'Number of concurrent HTTP requests',
                registry=self.registry
            ),
            'websocket_connections': Gauge(
                'websocket_connections',
                'Number of active WebSocket connections',
                registry=self.registry
            )
        })
    
    def _init_database_metrics(self):
        """Initialize database operation metrics."""
        self._metrics.update({
            'database_connections': Gauge(
                'database_connections',
                'Number of database connections',
                ['database', 'state'],
                registry=self.registry
            ),
            'database_query_duration_seconds': Histogram(
                'database_query_duration_seconds',
                'Database query duration in seconds',
                ['database', 'operation', 'table'],
                buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0],
                registry=self.registry
            ),
            'database_queries_total': Counter(
                'database_queries_total',
                'Total database queries',
                ['database', 'operation', 'table', 'status'],
                registry=self.registry
            ),
            'database_rows_affected': Counter(
                'database_rows_affected',
                'Number of rows affected by database operations',
                ['database', 'operation', 'table'],
                registry=self.registry
            ),
            'bigquery_jobs_total': Counter(
                'bigquery_jobs_total',
                'Total BigQuery jobs',
                ['job_type', 'status'],
                registry=self.registry
            ),
            'bigquery_bytes_processed': Counter(
                'bigquery_bytes_processed',
                'Total bytes processed by BigQuery',
                ['job_type'],
                registry=self.registry
            ),
            'bigquery_slot_seconds': Counter(
                'bigquery_slot_seconds',
                'Total slot seconds used by BigQuery',
                ['job_type'],
                registry=self.registry
            )
        })
    
    def _init_ml_metrics(self):
        """Initialize ML model performance metrics."""
        self._metrics.update({
            'ml_model_inference_duration_seconds': Histogram(
                'ml_model_inference_duration_seconds',
                'ML model inference duration in seconds',
                ['model_name', 'model_version'],
                buckets=[0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0],
                registry=self.registry
            ),
            'ml_model_predictions_total': Counter(
                'ml_model_predictions_total',
                'Total ML model predictions',
                ['model_name', 'model_version', 'status'],
                registry=self.registry
            ),
            'ml_model_accuracy': Gauge(
                'ml_model_accuracy',
                'ML model accuracy score',
                ['model_name', 'model_version'],
                registry=self.registry
            ),
            'ml_model_confidence': Histogram(
                'ml_model_confidence',
                'ML model prediction confidence',
                ['model_name', 'model_version'],
                buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
                registry=self.registry
            ),
            'ml_batch_size': Histogram(
                'ml_batch_size',
                'ML batch processing size',
                ['model_name'],
                buckets=[1, 5, 10, 25, 50, 100, 250, 500, 1000],
                registry=self.registry
            ),
            'ml_gpu_utilization': Gauge(
                'ml_gpu_utilization',
                'GPU utilization percentage',
                ['gpu_id'],
                registry=self.registry
            ),
            'ml_gpu_memory_used': Gauge(
                'ml_gpu_memory_used',
                'GPU memory used in bytes',
                ['gpu_id'],
                registry=self.registry
            )
        })
    
    def _init_system_metrics(self):
        """Initialize system resource metrics."""
        self._metrics.update({
            'system_cpu_usage_percent': Gauge(
                'system_cpu_usage_percent',
                'CPU usage percentage',
                ['cpu'],
                registry=self.registry
            ),
            'system_memory_usage_bytes': Gauge(
                'system_memory_usage_bytes',
                'Memory usage in bytes',
                ['type'],
                registry=self.registry
            ),
            'system_disk_usage_bytes': Gauge(
                'system_disk_usage_bytes',
                'Disk usage in bytes',
                ['device', 'mountpoint', 'type'],
                registry=self.registry
            ),
            'system_network_bytes_total': Counter(
                'system_network_bytes_total',
                'Total network bytes',
                ['interface', 'direction'],
                registry=self.registry
            ),
            'system_load_average': Gauge(
                'system_load_average',
                'System load average',
                ['period'],
                registry=self.registry
            ),
            'python_gc_objects_collected_total': Counter(
                'python_gc_objects_collected_total',
                'Objects collected during gc',
                ['generation'],
                registry=self.registry
            ),
            'python_gc_time_seconds': Counter(
                'python_gc_time_seconds',
                'Time spent in garbage collection',
                ['generation'],
                registry=self.registry
            )
        })
    
    def _init_business_metrics(self):
        """Initialize business-specific metrics."""
        self._metrics.update({
            'patterns_detected_total': Counter(
                'patterns_detected_total',
                'Total patterns detected',
                ['pattern_type', 'confidence_level'],
                registry=self.registry
            ),
            'pattern_detection_accuracy': Gauge(
                'pattern_detection_accuracy',
                'Pattern detection accuracy',
                ['pattern_type'],
                registry=self.registry
            ),
            'code_analysis_requests_total': Counter(
                'code_analysis_requests_total',
                'Total code analysis requests',
                ['language', 'status'],
                registry=self.registry
            ),
            'repository_analysis_duration_seconds': Histogram(
                'repository_analysis_duration_seconds',
                'Repository analysis duration in seconds',
                ['repository_size'],
                buckets=[1, 5, 10, 30, 60, 120, 300, 600, 1200, 3600],
                registry=self.registry
            ),
            'cache_hit_ratio': Gauge(
                'cache_hit_ratio',
                'Cache hit ratio',
                ['cache_type'],
                registry=self.registry
            ),
            'active_users': Gauge(
                'active_users',
                'Number of active users',
                ['time_window'],
                registry=self.registry
            )
        })
    
    def increment_counter(self, name: str, labels: Optional[Dict[str, str]] = None, value: float = 1.0):
        """Increment a counter metric."""
        with self._lock:
            if name in self._metrics:
                metric = self._metrics[name]
                if labels:
                    metric.labels(**labels).inc(value)
                else:
                    metric.inc(value)
    
    def set_gauge(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Set a gauge metric value."""
        with self._lock:
            if name in self._metrics:
                metric = self._metrics[name]
                if labels:
                    metric.labels(**labels).set(value)
                else:
                    metric.set(value)
    
    def observe_histogram(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Observe a histogram metric."""
        with self._lock:
            if name in self._metrics:
                metric = self._metrics[name]
                if labels:
                    metric.labels(**labels).observe(value)
                else:
                    metric.observe(value)
    
    def observe_summary(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Observe a summary metric."""
        with self._lock:
            if name in self._metrics:
                metric = self._metrics[name]
                if labels:
                    metric.labels(**labels).observe(value)
                else:
                    metric.observe(value)
    
    @asynccontextmanager
    async def time_histogram(self, name: str, labels: Optional[Dict[str, str]] = None):
        """Context manager to time an operation and record it in a histogram."""
        start_time = time.time()
        try:
            yield
        finally:
            duration = time.time() - start_time
            self.observe_histogram(name, duration, labels)
    
    def time_function(self, metric_name: str, labels: Optional[Dict[str, str]] = None):
        """Decorator to time a function execution."""
        def decorator(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                async with self.time_histogram(metric_name, labels):
                    return await func(*args, **kwargs)
            
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    duration = time.time() - start_time
                    self.observe_histogram(metric_name, duration, labels)
            
            return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
        return decorator
    
    def record_http_request(self, method: str, endpoint: str, status_code: int, 
                          duration: float, request_size: Optional[int] = None, 
                          response_size: Optional[int] = None):
        """Record HTTP request metrics."""
        labels = {
            'method': method,
            'endpoint': endpoint,
            'status_code': str(status_code)
        }
        
        self.increment_counter('http_requests_total', labels)
        self.observe_histogram('http_request_duration_seconds', duration, 
                             {'method': method, 'endpoint': endpoint})
        
        if request_size:
            self.observe_histogram('http_request_size_bytes', request_size,
                                 {'method': method, 'endpoint': endpoint})
        
        if response_size:
            self.observe_histogram('http_response_size_bytes', response_size,
                                 {'method': method, 'endpoint': endpoint})
    
    def record_database_query(self, database: str, operation: str, table: str, 
                            duration: float, rows_affected: int = 0, status: str = "success"):
        """Record database query metrics."""
        labels = {
            'database': database,
            'operation': operation,
            'table': table
        }
        
        self.observe_histogram('database_query_duration_seconds', duration, labels)
        self.increment_counter('database_queries_total', 
                             {**labels, 'status': status})
        
        if rows_affected > 0:
            self.increment_counter('database_rows_affected', labels, rows_affected)
    
    def record_ml_inference(self, model_name: str, model_version: str, 
                          duration: float, confidence: float, status: str = "success"):
        """Record ML model inference metrics."""
        labels = {
            'model_name': model_name,
            'model_version': model_version
        }
        
        self.observe_histogram('ml_model_inference_duration_seconds', duration, labels)
        self.increment_counter('ml_model_predictions_total', 
                             {**labels, 'status': status})
        self.observe_histogram('ml_model_confidence', confidence, labels)
    
    def record_pattern_detection(self, pattern_type: str, confidence: float, 
                               accuracy: Optional[float] = None):
        """Record pattern detection metrics."""
        confidence_level = "high" if confidence > 0.8 else "medium" if confidence > 0.5 else "low"
        
        self.increment_counter('patterns_detected_total', {
            'pattern_type': pattern_type,
            'confidence_level': confidence_level
        })
        
        if accuracy is not None:
            self.set_gauge('pattern_detection_accuracy', accuracy, 
                         {'pattern_type': pattern_type})
    
    def update_system_metrics(self):
        """Update system resource metrics."""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1, percpu=True)
            for i, cpu in enumerate(cpu_percent):
                self.set_gauge('system_cpu_usage_percent', cpu, {'cpu': str(i)})
            
            # Memory usage
            memory = psutil.virtual_memory()
            self.set_gauge('system_memory_usage_bytes', memory.used, {'type': 'used'})
            self.set_gauge('system_memory_usage_bytes', memory.available, {'type': 'available'})
            self.set_gauge('system_memory_usage_bytes', memory.total, {'type': 'total'})
            
            # Disk usage
            for disk in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(disk.mountpoint)
                    labels = {
                        'device': disk.device,
                        'mountpoint': disk.mountpoint,
                        'type': 'used'
                    }
                    self.set_gauge('system_disk_usage_bytes', usage.used, labels)
                    
                    labels['type'] = 'free'
                    self.set_gauge('system_disk_usage_bytes', usage.free, labels)
                    
                    labels['type'] = 'total'
                    self.set_gauge('system_disk_usage_bytes', usage.total, labels)
                except PermissionError:
                    # Skip disks we can't access
                    continue
            
            # Network I/O
            net_io = psutil.net_io_counters(pernic=True)
            for interface, stats in net_io.items():
                self.set_gauge('system_network_bytes_total', stats.bytes_sent,
                             {'interface': interface, 'direction': 'sent'})
                self.set_gauge('system_network_bytes_total', stats.bytes_recv,
                             {'interface': interface, 'direction': 'received'})
            
            # Load average
            load_avg = psutil.getloadavg()
            self.set_gauge('system_load_average', load_avg[0], {'period': '1m'})
            self.set_gauge('system_load_average', load_avg[1], {'period': '5m'})
            self.set_gauge('system_load_average', load_avg[2], {'period': '15m'})
            
        except Exception as e:
            logger.error(f"Error updating system metrics: {e}")
    
    async def start_background_collection(self, interval: int = 30):
        """Start background metric collection."""
        self._stop_collection = False
        
        async def collect_metrics():
            while not self._stop_collection:
                try:
                    self.update_system_metrics()
                    await asyncio.sleep(interval)
                except Exception as e:
                    logger.error(f"Error in background metric collection: {e}")
                    await asyncio.sleep(5)  # Brief pause before retry
        
        self._collection_task = asyncio.create_task(collect_metrics())
        logger.info("Background metric collection started")
    
    async def stop_background_collection(self):
        """Stop background metric collection."""
        self._stop_collection = True
        if self._collection_task:
            self._collection_task.cancel()
            try:
                await self._collection_task
            except asyncio.CancelledError:
                pass
        logger.info("Background metric collection stopped")
    
    def get_metrics(self) -> bytes:
        """Get all metrics in Prometheus format."""
        return generate_latest(self.registry)
    
    def get_metric_names(self) -> List[str]:
        """Get list of all metric names."""
        return list(self._metrics.keys())
    
    def get_metric(self, name: str) -> Optional[Any]:
        """Get a specific metric by name."""
        return self._metrics.get(name)
    
    def reset_metrics(self):
        """Reset all metrics (useful for testing)."""
        with self._lock:
            for metric in self._metrics.values():
                if hasattr(metric, 'clear'):
                    metric.clear()
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        asyncio.create_task(self.stop_background_collection())


# Global metrics instance
_metrics_instance: Optional[PrometheusMetrics] = None


def get_metrics() -> PrometheusMetrics:
    """Get the global metrics instance."""
    global _metrics_instance
    if _metrics_instance is None:
        _metrics_instance = PrometheusMetrics()
    return _metrics_instance


def init_metrics(registry: Optional[CollectorRegistry] = None) -> PrometheusMetrics:
    """Initialize the global metrics instance."""
    global _metrics_instance
    _metrics_instance = PrometheusMetrics(registry)
    return _metrics_instance