"""
Pattern-Specific Metrics Collection

Advanced metrics for pattern detection and analysis including:
- Pattern detection performance
- Pattern quality metrics
- Pattern clustering metrics
- Pattern lifecycle tracking
- Pattern recommendation metrics
"""

import time
import asyncio
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
from prometheus_client import Counter, Histogram, Gauge, Summary
import logging
from enum import Enum
import json
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class PatternType(Enum):
    """Types of patterns that can be detected."""
    SECURITY_VULNERABILITY = "security_vulnerability"
    PERFORMANCE_ISSUE = "performance_issue"
    CODE_SMELL = "code_smell"
    DESIGN_PATTERN = "design_pattern"
    ANTI_PATTERN = "anti_pattern"
    BEST_PRACTICE = "best_practice"
    COMPLEXITY_ISSUE = "complexity_issue"
    MAINTAINABILITY_ISSUE = "maintainability_issue"
    TESTING_PATTERN = "testing_pattern"
    ARCHITECTURE_PATTERN = "architecture_pattern"


class PatternSeverity(Enum):
    """Severity levels for detected patterns."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


@dataclass
class PatternDetectionResult:
    """Result of pattern detection."""
    pattern_id: str
    pattern_type: PatternType
    severity: PatternSeverity
    confidence: float
    detection_time: float
    file_path: str
    line_number: int
    description: str
    metadata: Dict = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class PatternAnalysisSession:
    """Session for pattern analysis tracking."""
    session_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    patterns_detected: List[PatternDetectionResult] = field(default_factory=list)
    files_analyzed: Set[str] = field(default_factory=set)
    total_lines_analyzed: int = 0
    analysis_time: float = 0.0
    status: str = "active"


class PatternMetrics:
    """
    Pattern-specific metrics collector.
    
    Tracks detailed metrics about pattern detection, analysis performance,
    and pattern quality metrics.
    """
    
    def __init__(self, prometheus_metrics=None):
        """Initialize pattern metrics collector."""
        self.prometheus_metrics = prometheus_metrics
        self._sessions: Dict[str, PatternAnalysisSession] = {}
        self._pattern_history: deque = deque(maxlen=10000)  # Keep last 10k patterns
        self._session_history: deque = deque(maxlen=1000)   # Keep last 1k sessions
        
        # Pattern detection metrics
        self.pattern_detection_counter = Counter(
            'pattern_detection_total',
            'Total patterns detected',
            ['pattern_type', 'severity', 'language', 'confidence_level']
        )
        
        self.pattern_detection_time = Histogram(
            'pattern_detection_duration_seconds',
            'Time taken to detect patterns',
            ['pattern_type', 'analysis_type'],
            buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0]
        )
        
        self.pattern_confidence_score = Histogram(
            'pattern_confidence_score',
            'Confidence score of detected patterns',
            ['pattern_type', 'severity'],
            buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
        )
        
        # Analysis session metrics
        self.analysis_sessions_total = Counter(
            'analysis_sessions_total',
            'Total analysis sessions',
            ['status', 'analysis_type']
        )
        
        self.analysis_session_duration = Histogram(
            'analysis_session_duration_seconds',
            'Duration of analysis sessions',
            ['analysis_type', 'files_count_range'],
            buckets=[1, 5, 10, 30, 60, 120, 300, 600, 1200, 3600]
        )
        
        self.files_analyzed_per_session = Histogram(
            'files_analyzed_per_session',
            'Number of files analyzed per session',
            ['analysis_type'],
            buckets=[1, 5, 10, 25, 50, 100, 250, 500, 1000, 2500, 5000]
        )
        
        self.lines_analyzed_per_session = Histogram(
            'lines_analyzed_per_session',
            'Number of lines analyzed per session',
            ['analysis_type'],
            buckets=[100, 500, 1000, 5000, 10000, 25000, 50000, 100000, 250000, 500000]
        )
        
        # Pattern quality metrics
        self.pattern_false_positive_rate = Gauge(
            'pattern_false_positive_rate',
            'False positive rate for pattern detection',
            ['pattern_type', 'model_version']
        )
        
        self.pattern_accuracy = Gauge(
            'pattern_accuracy',
            'Pattern detection accuracy',
            ['pattern_type', 'model_version']
        )
        
        self.pattern_recall = Gauge(
            'pattern_recall',
            'Pattern detection recall',
            ['pattern_type', 'model_version']
        )
        
        self.pattern_precision = Gauge(
            'pattern_precision',
            'Pattern detection precision',
            ['pattern_type', 'model_version']
        )
        
        # Pattern clustering metrics
        self.pattern_clusters_detected = Counter(
            'pattern_clusters_detected_total',
            'Total pattern clusters detected',
            ['cluster_type', 'similarity_threshold']
        )
        
        self.pattern_cluster_size = Histogram(
            'pattern_cluster_size',
            'Size of pattern clusters',
            ['cluster_type'],
            buckets=[1, 2, 5, 10, 25, 50, 100, 250, 500, 1000]
        )
        
        self.pattern_similarity_score = Histogram(
            'pattern_similarity_score',
            'Similarity score between patterns',
            ['comparison_type'],
            buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
        )
        
        # Pattern remediation metrics
        self.pattern_remediation_attempts = Counter(
            'pattern_remediation_attempts_total',
            'Total pattern remediation attempts',
            ['pattern_type', 'remediation_type', 'status']
        )
        
        self.pattern_remediation_success_rate = Gauge(
            'pattern_remediation_success_rate',
            'Pattern remediation success rate',
            ['pattern_type', 'remediation_type']
        )
        
        # Performance metrics
        self.analysis_throughput = Gauge(
            'analysis_throughput_lines_per_second',
            'Analysis throughput in lines per second',
            ['analysis_type']
        )
        
        self.memory_usage_per_analysis = Histogram(
            'memory_usage_per_analysis_mb',
            'Memory usage per analysis in MB',
            ['analysis_type'],
            buckets=[10, 25, 50, 100, 250, 500, 1000, 2500, 5000, 10000]
        )
        
        # Business metrics
        self.patterns_by_repository = Counter(
            'patterns_by_repository_total',
            'Total patterns by repository',
            ['repository', 'pattern_type', 'severity']
        )
        
        self.code_quality_score = Gauge(
            'code_quality_score',
            'Overall code quality score',
            ['repository', 'metric_type']
        )
        
        self.technical_debt_score = Gauge(
            'technical_debt_score',
            'Technical debt score',
            ['repository', 'debt_type']
        )
        
        logger.info("Pattern metrics initialized")
    
    def start_analysis_session(self, session_id: str, analysis_type: str = "full") -> PatternAnalysisSession:
        """Start a new analysis session."""
        session = PatternAnalysisSession(
            session_id=session_id,
            start_time=datetime.now(),
            status="active"
        )
        
        self._sessions[session_id] = session
        self.analysis_sessions_total.labels(status="started", analysis_type=analysis_type).inc()
        
        logger.info(f"Started analysis session: {session_id}")
        return session
    
    def end_analysis_session(self, session_id: str, status: str = "completed") -> Optional[PatternAnalysisSession]:
        """End an analysis session and record metrics."""
        session = self._sessions.get(session_id)
        if not session:
            logger.warning(f"Session not found: {session_id}")
            return None
        
        session.end_time = datetime.now()
        session.status = status
        session.analysis_time = (session.end_time - session.start_time).total_seconds()
        
        # Record session metrics
        analysis_type = "full"  # Default, could be parameterized
        files_count = len(session.files_analyzed)
        files_range = self._get_files_count_range(files_count)
        
        self.analysis_sessions_total.labels(status=status, analysis_type=analysis_type).inc()
        self.analysis_session_duration.labels(
            analysis_type=analysis_type,
            files_count_range=files_range
        ).observe(session.analysis_time)
        
        self.files_analyzed_per_session.labels(analysis_type=analysis_type).observe(files_count)
        self.lines_analyzed_per_session.labels(analysis_type=analysis_type).observe(session.total_lines_analyzed)
        
        if session.analysis_time > 0:
            throughput = session.total_lines_analyzed / session.analysis_time
            self.analysis_throughput.labels(analysis_type=analysis_type).set(throughput)
        
        # Move to history
        self._session_history.append(session)
        del self._sessions[session_id]
        
        logger.info(f"Ended analysis session: {session_id} ({status})")
        return session
    
    def record_pattern_detection(self, session_id: str, pattern_result: PatternDetectionResult):
        """Record a pattern detection result."""
        session = self._sessions.get(session_id)
        if session:
            session.patterns_detected.append(pattern_result)
            session.files_analyzed.add(pattern_result.file_path)
        
        # Record pattern detection metrics
        confidence_level = self._get_confidence_level(pattern_result.confidence)
        language = self._extract_language_from_path(pattern_result.file_path)
        
        self.pattern_detection_counter.labels(
            pattern_type=pattern_result.pattern_type.value,
            severity=pattern_result.severity.value,
            language=language,
            confidence_level=confidence_level
        ).inc()
        
        self.pattern_detection_time.labels(
            pattern_type=pattern_result.pattern_type.value,
            analysis_type="detection"
        ).observe(pattern_result.detection_time)
        
        self.pattern_confidence_score.labels(
            pattern_type=pattern_result.pattern_type.value,
            severity=pattern_result.severity.value
        ).observe(pattern_result.confidence)
        
        # Add to history
        self._pattern_history.append(pattern_result)
        
        logger.debug(f"Recorded pattern detection: {pattern_result.pattern_id}")
    
    def record_pattern_cluster(self, cluster_type: str, cluster_size: int, 
                             similarity_threshold: float, patterns: List[str]):
        """Record pattern cluster formation."""
        self.pattern_clusters_detected.labels(
            cluster_type=cluster_type,
            similarity_threshold=str(similarity_threshold)
        ).inc()
        
        self.pattern_cluster_size.labels(cluster_type=cluster_type).observe(cluster_size)
        
        logger.info(f"Recorded pattern cluster: {cluster_type} with {cluster_size} patterns")
    
    def record_pattern_similarity(self, pattern1_id: str, pattern2_id: str, 
                                similarity_score: float, comparison_type: str = "structural"):
        """Record pattern similarity measurement."""
        self.pattern_similarity_score.labels(
            comparison_type=comparison_type
        ).observe(similarity_score)
        
        logger.debug(f"Recorded pattern similarity: {similarity_score:.3f} ({comparison_type})")
    
    def record_pattern_remediation(self, pattern_id: str, pattern_type: PatternType, 
                                 remediation_type: str, status: str):
        """Record pattern remediation attempt."""
        self.pattern_remediation_attempts.labels(
            pattern_type=pattern_type.value,
            remediation_type=remediation_type,
            status=status
        ).inc()
        
        logger.info(f"Recorded pattern remediation: {pattern_id} ({status})")
    
    def update_pattern_quality_metrics(self, pattern_type: PatternType, model_version: str,
                                     accuracy: float, precision: float, recall: float,
                                     false_positive_rate: float):
        """Update pattern quality metrics."""
        labels = {
            'pattern_type': pattern_type.value,
            'model_version': model_version
        }
        
        self.pattern_accuracy.labels(**labels).set(accuracy)
        self.pattern_precision.labels(**labels).set(precision)
        self.pattern_recall.labels(**labels).set(recall)
        self.pattern_false_positive_rate.labels(**labels).set(false_positive_rate)
        
        logger.info(f"Updated quality metrics for {pattern_type.value}: "
                   f"accuracy={accuracy:.3f}, precision={precision:.3f}, "
                   f"recall={recall:.3f}, fpr={false_positive_rate:.3f}")
    
    def update_code_quality_score(self, repository: str, metric_type: str, score: float):
        """Update code quality score for a repository."""
        self.code_quality_score.labels(
            repository=repository,
            metric_type=metric_type
        ).set(score)
        
        logger.info(f"Updated code quality score for {repository}: {metric_type}={score:.3f}")
    
    def update_technical_debt_score(self, repository: str, debt_type: str, score: float):
        """Update technical debt score for a repository."""
        self.technical_debt_score.labels(
            repository=repository,
            debt_type=debt_type
        ).set(score)
        
        logger.info(f"Updated technical debt score for {repository}: {debt_type}={score:.3f}")
    
    def record_repository_patterns(self, repository: str, patterns: List[PatternDetectionResult]):
        """Record patterns found in a repository."""
        for pattern in patterns:
            self.patterns_by_repository.labels(
                repository=repository,
                pattern_type=pattern.pattern_type.value,
                severity=pattern.severity.value
            ).inc()
    
    def record_memory_usage(self, analysis_type: str, memory_mb: float):
        """Record memory usage for analysis."""
        self.memory_usage_per_analysis.labels(analysis_type=analysis_type).observe(memory_mb)
    
    def get_session_stats(self, session_id: str) -> Optional[Dict]:
        """Get statistics for a specific session."""
        session = self._sessions.get(session_id)
        if not session:
            return None
        
        current_time = datetime.now()
        elapsed_time = (current_time - session.start_time).total_seconds()
        
        return {
            'session_id': session_id,
            'start_time': session.start_time.isoformat(),
            'elapsed_time': elapsed_time,
            'patterns_detected': len(session.patterns_detected),
            'files_analyzed': len(session.files_analyzed),
            'total_lines_analyzed': session.total_lines_analyzed,
            'status': session.status,
            'patterns_by_type': self._count_patterns_by_type(session.patterns_detected),
            'patterns_by_severity': self._count_patterns_by_severity(session.patterns_detected)
        }
    
    def get_global_stats(self) -> Dict:
        """Get global pattern detection statistics."""
        total_patterns = len(self._pattern_history)
        total_sessions = len(self._session_history)
        
        return {
            'total_patterns_detected': total_patterns,
            'total_sessions_completed': total_sessions,
            'active_sessions': len(self._sessions),
            'patterns_by_type': self._count_patterns_by_type(list(self._pattern_history)),
            'patterns_by_severity': self._count_patterns_by_severity(list(self._pattern_history)),
            'average_confidence': self._calculate_average_confidence(list(self._pattern_history)),
            'detection_trends': self._calculate_detection_trends()
        }
    
    def _get_confidence_level(self, confidence: float) -> str:
        """Convert confidence score to level."""
        if confidence >= 0.8:
            return "high"
        elif confidence >= 0.6:
            return "medium"
        elif confidence >= 0.4:
            return "low"
        else:
            return "very_low"
    
    def _extract_language_from_path(self, file_path: str) -> str:
        """Extract programming language from file path."""
        extension_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.rs': 'rust',
            '.go': 'go',
            '.rb': 'ruby',
            '.php': 'php',
            '.cs': 'csharp',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.r': 'r',
            '.sql': 'sql',
            '.sh': 'bash',
            '.ps1': 'powershell'
        }
        
        for ext, lang in extension_map.items():
            if file_path.endswith(ext):
                return lang
        
        return 'unknown'
    
    def _get_files_count_range(self, count: int) -> str:
        """Get files count range category."""
        if count <= 1:
            return "single"
        elif count <= 10:
            return "small"
        elif count <= 100:
            return "medium"
        elif count <= 1000:
            return "large"
        else:
            return "very_large"
    
    def _count_patterns_by_type(self, patterns: List[PatternDetectionResult]) -> Dict[str, int]:
        """Count patterns by type."""
        counts = defaultdict(int)
        for pattern in patterns:
            counts[pattern.pattern_type.value] += 1
        return dict(counts)
    
    def _count_patterns_by_severity(self, patterns: List[PatternDetectionResult]) -> Dict[str, int]:
        """Count patterns by severity."""
        counts = defaultdict(int)
        for pattern in patterns:
            counts[pattern.severity.value] += 1
        return dict(counts)
    
    def _calculate_average_confidence(self, patterns: List[PatternDetectionResult]) -> float:
        """Calculate average confidence score."""
        if not patterns:
            return 0.0
        
        total_confidence = sum(pattern.confidence for pattern in patterns)
        return total_confidence / len(patterns)
    
    def _calculate_detection_trends(self) -> Dict[str, List[Dict]]:
        """Calculate detection trends over time."""
        # Group patterns by hour for the last 24 hours
        now = datetime.now()
        hourly_data = defaultdict(int)
        
        for pattern in self._pattern_history:
            if (now - pattern.timestamp).total_seconds() <= 86400:  # 24 hours
                hour_key = pattern.timestamp.strftime('%Y-%m-%d %H:00')
                hourly_data[hour_key] += 1
        
        # Convert to list of dictionaries
        trends = []
        for hour, count in sorted(hourly_data.items()):
            trends.append({
                'timestamp': hour,
                'count': count
            })
        
        return {'hourly': trends}
    
    def cleanup_old_data(self, max_age_hours: int = 24):
        """Clean up old pattern data."""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        # Clean pattern history
        self._pattern_history = deque(
            [p for p in self._pattern_history if p.timestamp > cutoff_time],
            maxlen=10000
        )
        
        # Clean session history
        self._session_history = deque(
            [s for s in self._session_history if s.start_time > cutoff_time],
            maxlen=1000
        )
        
        logger.info(f"Cleaned up pattern data older than {max_age_hours} hours")


# Global pattern metrics instance
_pattern_metrics_instance: Optional[PatternMetrics] = None


def get_pattern_metrics() -> PatternMetrics:
    """Get the global pattern metrics instance."""
    global _pattern_metrics_instance
    if _pattern_metrics_instance is None:
        _pattern_metrics_instance = PatternMetrics()
    return _pattern_metrics_instance


def init_pattern_metrics(prometheus_metrics=None) -> PatternMetrics:
    """Initialize the global pattern metrics instance."""
    global _pattern_metrics_instance
    _pattern_metrics_instance = PatternMetrics(prometheus_metrics)
    return _pattern_metrics_instance