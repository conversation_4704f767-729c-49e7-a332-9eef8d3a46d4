"""
Alert Manager for Monitoring System

Manages alerts and notifications for the pattern mining service including:
- Alert rule definitions
- Notification channels
- Escalation policies
- Alert correlation and deduplication
- Performance degradation alerts
"""

import time
import asyncio
from typing import Dict, List, Optional, Callable, Any, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from collections import defaultdict, deque
import logging
import json
import threading
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)


class AlertSeverity(Enum):
    """Alert severity levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class AlertState(Enum):
    """Alert states."""
    ACTIVE = "active"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"
    ACKNOWLEDGED = "acknowledged"


class NotificationChannel(Enum):
    """Notification channels."""
    EMAIL = "email"
    SLACK = "slack"
    WEBHOOK = "webhook"
    SMS = "sms"
    PAGERDUTY = "pagerduty"
    CONSOLE = "console"


@dataclass
class AlertRule:
    """Alert rule definition."""
    name: str
    description: str
    metric_name: str
    operator: str  # "gt", "lt", "eq", "ge", "le"
    threshold: float
    severity: AlertSeverity
    duration: int  # seconds
    labels: Dict[str, str] = field(default_factory=dict)
    annotations: Dict[str, str] = field(default_factory=dict)
    enabled: bool = True
    
    def evaluate(self, value: float) -> bool:
        """Evaluate if alert should fire."""
        if self.operator == "gt":
            return value > self.threshold
        elif self.operator == "lt":
            return value < self.threshold
        elif self.operator == "ge":
            return value >= self.threshold
        elif self.operator == "le":
            return value <= self.threshold
        elif self.operator == "eq":
            return value == self.threshold
        return False


@dataclass
class Alert:
    """Alert instance."""
    id: str
    rule: AlertRule
    value: float
    state: AlertState
    created_at: datetime
    updated_at: datetime
    resolved_at: Optional[datetime] = None
    acknowledged_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None
    fingerprint: str = ""
    labels: Dict[str, str] = field(default_factory=dict)
    annotations: Dict[str, str] = field(default_factory=dict)
    
    def __post_init__(self):
        """Generate fingerprint for deduplication."""
        if not self.fingerprint:
            self.fingerprint = self._generate_fingerprint()
    
    def _generate_fingerprint(self) -> str:
        """Generate fingerprint for alert deduplication."""
        key_parts = [
            self.rule.name,
            self.rule.metric_name,
            str(sorted(self.labels.items()))
        ]
        return hash(tuple(key_parts))


@dataclass
class NotificationRule:
    """Notification rule configuration."""
    name: str
    channels: List[NotificationChannel]
    severity_filter: List[AlertSeverity]
    label_filters: Dict[str, str] = field(default_factory=dict)
    enabled: bool = True
    repeat_interval: int = 3600  # seconds
    
    def matches(self, alert: Alert) -> bool:
        """Check if notification rule matches alert."""
        if not self.enabled:
            return False
        
        # Check severity filter
        if self.severity_filter and alert.rule.severity not in self.severity_filter:
            return False
        
        # Check label filters
        for key, value in self.label_filters.items():
            if key not in alert.labels or alert.labels[key] != value:
                return False
        
        return True


@dataclass
class EscalationPolicy:
    """Escalation policy configuration."""
    name: str
    steps: List[Dict[str, Any]]
    enabled: bool = True
    
    def get_escalation_step(self, alert: Alert, elapsed_time: int) -> Optional[Dict[str, Any]]:
        """Get escalation step based on elapsed time."""
        for step in self.steps:
            if elapsed_time >= step.get('delay', 0):
                return step
        return None


class AlertManager:
    """
    Alert manager for monitoring system.
    
    Manages alert rules, notifications, and escalation policies
    with support for alert correlation and deduplication.
    """
    
    def __init__(self):
        """Initialize alert manager."""
        self._alert_rules: Dict[str, AlertRule] = {}
        self._notification_rules: Dict[str, NotificationRule] = {}
        self._escalation_policies: Dict[str, EscalationPolicy] = {}
        self._active_alerts: Dict[str, Alert] = {}
        self._alert_history: deque = deque(maxlen=10000)
        self._metric_values: Dict[str, float] = {}
        self._notification_channels: Dict[NotificationChannel, Callable] = {}
        
        # Alert state tracking
        self._alert_timers: Dict[str, float] = {}
        self._notification_history: deque = deque(maxlen=5000)
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Background processing
        self._processing_task = None
        self._stop_processing = False
        
        # Initialize default rules
        self._init_default_rules()
        
        logger.info("Alert manager initialized")
    
    def _init_default_rules(self):
        """Initialize default alert rules."""
        default_rules = [
            AlertRule(
                name="high_cpu_usage",
                description="High CPU usage detected",
                metric_name="cpu_usage_percent",
                operator="gt",
                threshold=80.0,
                severity=AlertSeverity.HIGH,
                duration=300,  # 5 minutes
                labels={"component": "system"},
                annotations={"summary": "CPU usage is above 80%"}
            ),
            AlertRule(
                name="high_memory_usage",
                description="High memory usage detected",
                metric_name="memory_usage_percent",
                operator="gt",
                threshold=85.0,
                severity=AlertSeverity.HIGH,
                duration=300,
                labels={"component": "system"},
                annotations={"summary": "Memory usage is above 85%"}
            ),
            AlertRule(
                name="high_error_rate",
                description="High error rate detected",
                metric_name="error_rate_percent",
                operator="gt",
                threshold=5.0,
                severity=AlertSeverity.CRITICAL,
                duration=60,
                labels={"component": "api"},
                annotations={"summary": "Error rate is above 5%"}
            ),
            AlertRule(
                name="slow_response_time",
                description="Slow response time detected",
                metric_name="response_time_p95",
                operator="gt",
                threshold=1000.0,  # 1 second
                severity=AlertSeverity.MEDIUM,
                duration=600,  # 10 minutes
                labels={"component": "api"},
                annotations={"summary": "95th percentile response time is above 1 second"}
            ),
            AlertRule(
                name="ml_model_accuracy_drop",
                description="ML model accuracy drop detected",
                metric_name="ml_model_accuracy",
                operator="lt",
                threshold=0.8,
                severity=AlertSeverity.HIGH,
                duration=300,
                labels={"component": "ml"},
                annotations={"summary": "ML model accuracy dropped below 80%"}
            ),
            AlertRule(
                name="database_connection_pool_exhausted",
                description="Database connection pool exhausted",
                metric_name="db_connection_pool_utilization",
                operator="gt",
                threshold=90.0,
                severity=AlertSeverity.CRITICAL,
                duration=60,
                labels={"component": "database"},
                annotations={"summary": "Database connection pool utilization is above 90%"}
            ),
            AlertRule(
                name="disk_space_low",
                description="Disk space is running low",
                metric_name="disk_usage_percent",
                operator="gt",
                threshold=85.0,
                severity=AlertSeverity.HIGH,
                duration=300,
                labels={"component": "system"},
                annotations={"summary": "Disk usage is above 85%"}
            ),
            AlertRule(
                name="pattern_detection_failure_rate",
                description="High pattern detection failure rate",
                metric_name="pattern_detection_failure_rate",
                operator="gt",
                threshold=10.0,
                severity=AlertSeverity.MEDIUM,
                duration=300,
                labels={"component": "pattern_mining"},
                annotations={"summary": "Pattern detection failure rate is above 10%"}
            )
        ]
        
        for rule in default_rules:
            self.add_alert_rule(rule)
        
        # Initialize default notification rules
        self.add_notification_rule(NotificationRule(
            name="critical_alerts",
            channels=[NotificationChannel.CONSOLE, NotificationChannel.EMAIL],
            severity_filter=[AlertSeverity.CRITICAL],
            repeat_interval=1800  # 30 minutes
        ))
        
        self.add_notification_rule(NotificationRule(
            name="high_severity_alerts",
            channels=[NotificationChannel.CONSOLE],
            severity_filter=[AlertSeverity.HIGH],
            repeat_interval=3600  # 1 hour
        ))
    
    def add_alert_rule(self, rule: AlertRule):
        """Add an alert rule."""
        with self._lock:
            self._alert_rules[rule.name] = rule
        
        logger.info(
            "Alert rule added",
            rule_name=rule.name,
            metric_name=rule.metric_name,
            threshold=rule.threshold,
            severity=rule.severity.value
        )
    
    def remove_alert_rule(self, rule_name: str):
        """Remove an alert rule."""
        with self._lock:
            if rule_name in self._alert_rules:
                del self._alert_rules[rule_name]
                logger.info("Alert rule removed", rule_name=rule_name)
    
    def add_notification_rule(self, rule: NotificationRule):
        """Add a notification rule."""
        with self._lock:
            self._notification_rules[rule.name] = rule
        
        logger.info(
            "Notification rule added",
            rule_name=rule.name,
            channels=[c.value for c in rule.channels],
            severity_filter=[s.value for s in rule.severity_filter]
        )
    
    def add_escalation_policy(self, policy: EscalationPolicy):
        """Add an escalation policy."""
        with self._lock:
            self._escalation_policies[policy.name] = policy
        
        logger.info("Escalation policy added", policy_name=policy.name)
    
    def register_notification_channel(self, channel: NotificationChannel, handler: Callable):
        """Register a notification channel handler."""
        self._notification_channels[channel] = handler
        logger.info("Notification channel registered", channel=channel.value)
    
    def update_metric(self, metric_name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Update metric value and check for alerts."""
        with self._lock:
            self._metric_values[metric_name] = value
        
        # Check all rules for this metric
        for rule_name, rule in self._alert_rules.items():
            if rule.metric_name == metric_name and rule.enabled:
                self._evaluate_rule(rule, value, labels or {})
    
    def _evaluate_rule(self, rule: AlertRule, value: float, labels: Dict[str, str]):
        """Evaluate an alert rule."""
        should_fire = rule.evaluate(value)
        alert_key = f"{rule.name}:{hash(str(sorted(labels.items())))}"
        
        if should_fire:
            # Check if alert is already active
            if alert_key in self._active_alerts:
                # Update existing alert
                alert = self._active_alerts[alert_key]
                alert.value = value
                alert.updated_at = datetime.now()
            else:
                # Check duration threshold
                if alert_key not in self._alert_timers:
                    self._alert_timers[alert_key] = time.time()
                
                elapsed = time.time() - self._alert_timers[alert_key]
                if elapsed >= rule.duration:
                    # Fire new alert
                    self._fire_alert(rule, value, labels)
                    # Remove from timers
                    if alert_key in self._alert_timers:
                        del self._alert_timers[alert_key]
        else:
            # Remove from timers if condition is no longer met
            if alert_key in self._alert_timers:
                del self._alert_timers[alert_key]
            
            # Resolve alert if active
            if alert_key in self._active_alerts:
                self._resolve_alert(alert_key)
    
    def _fire_alert(self, rule: AlertRule, value: float, labels: Dict[str, str]):
        """Fire a new alert."""
        alert_id = f"{rule.name}_{int(time.time())}_{hash(str(sorted(labels.items())))}"
        
        alert = Alert(
            id=alert_id,
            rule=rule,
            value=value,
            state=AlertState.ACTIVE,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            labels={**rule.labels, **labels},
            annotations=rule.annotations.copy()
        )
        
        alert_key = f"{rule.name}:{hash(str(sorted(labels.items())))}"
        
        with self._lock:
            self._active_alerts[alert_key] = alert
            self._alert_history.append(alert)
        
        logger.warning(
            "Alert fired",
            alert_id=alert_id,
            rule_name=rule.name,
            metric_name=rule.metric_name,
            value=value,
            threshold=rule.threshold,
            severity=rule.severity.value,
            labels=labels
        )
        
        # Send notifications
        self._send_notifications(alert)
    
    def _resolve_alert(self, alert_key: str):
        """Resolve an active alert."""
        with self._lock:
            alert = self._active_alerts.get(alert_key)
            if alert:
                alert.state = AlertState.RESOLVED
                alert.resolved_at = datetime.now()
                alert.updated_at = datetime.now()
                
                # Move to history
                self._alert_history.append(alert)
                del self._active_alerts[alert_key]
                
                logger.info(
                    "Alert resolved",
                    alert_id=alert.id,
                    rule_name=alert.rule.name,
                    duration_seconds=(alert.resolved_at - alert.created_at).total_seconds()
                )
                
                # Send resolution notifications
                self._send_resolution_notifications(alert)
    
    def _send_notifications(self, alert: Alert):
        """Send notifications for an alert."""
        for rule_name, rule in self._notification_rules.items():
            if rule.matches(alert):
                for channel in rule.channels:
                    if channel in self._notification_channels:
                        try:
                            self._notification_channels[channel](alert, "fired")
                        except Exception as e:
                            logger.error(
                                "Failed to send notification",
                                exception=e,
                                channel=channel.value,
                                alert_id=alert.id
                            )
    
    def _send_resolution_notifications(self, alert: Alert):
        """Send resolution notifications for an alert."""
        for rule_name, rule in self._notification_rules.items():
            if rule.matches(alert):
                for channel in rule.channels:
                    if channel in self._notification_channels:
                        try:
                            self._notification_channels[channel](alert, "resolved")
                        except Exception as e:
                            logger.error(
                                "Failed to send resolution notification",
                                exception=e,
                                channel=channel.value,
                                alert_id=alert.id
                            )
    
    def acknowledge_alert(self, alert_id: str, acknowledged_by: str):
        """Acknowledge an alert."""
        with self._lock:
            for alert in self._active_alerts.values():
                if alert.id == alert_id:
                    alert.state = AlertState.ACKNOWLEDGED
                    alert.acknowledged_at = datetime.now()
                    alert.acknowledged_by = acknowledged_by
                    alert.updated_at = datetime.now()
                    
                    logger.info(
                        "Alert acknowledged",
                        alert_id=alert_id,
                        acknowledged_by=acknowledged_by
                    )
                    return True
        return False
    
    def suppress_alert(self, alert_id: str):
        """Suppress an alert."""
        with self._lock:
            for alert in self._active_alerts.values():
                if alert.id == alert_id:
                    alert.state = AlertState.SUPPRESSED
                    alert.updated_at = datetime.now()
                    
                    logger.info("Alert suppressed", alert_id=alert_id)
                    return True
        return False
    
    def get_active_alerts(self) -> List[Alert]:
        """Get all active alerts."""
        with self._lock:
            return list(self._active_alerts.values())
    
    def get_alert_by_id(self, alert_id: str) -> Optional[Alert]:
        """Get alert by ID."""
        with self._lock:
            for alert in self._active_alerts.values():
                if alert.id == alert_id:
                    return alert
            
            # Search in history
            for alert in self._alert_history:
                if alert.id == alert_id:
                    return alert
        
        return None
    
    def get_alert_statistics(self) -> Dict[str, Any]:
        """Get alert statistics."""
        with self._lock:
            active_count = len(self._active_alerts)
            
            # Count by severity
            severity_counts = defaultdict(int)
            for alert in self._active_alerts.values():
                severity_counts[alert.rule.severity.value] += 1
            
            # Count by state
            state_counts = defaultdict(int)
            for alert in self._active_alerts.values():
                state_counts[alert.state.value] += 1
            
            # Recent history stats
            recent_cutoff = datetime.now() - timedelta(hours=24)
            recent_alerts = [a for a in self._alert_history if a.created_at > recent_cutoff]
            
            return {
                'active_alerts': active_count,
                'severity_distribution': dict(severity_counts),
                'state_distribution': dict(state_counts),
                'alerts_last_24h': len(recent_alerts),
                'alert_rules': len(self._alert_rules),
                'notification_rules': len(self._notification_rules),
                'escalation_policies': len(self._escalation_policies)
            }
    
    def get_alert_trends(self, hours: int = 24) -> Dict[str, List[Dict]]:
        """Get alert trends over time."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        with self._lock:
            recent_alerts = [
                a for a in self._alert_history
                if a.created_at > cutoff_time
            ]
        
        # Group by hour
        hourly_data = defaultdict(int)
        for alert in recent_alerts:
            hour_key = alert.created_at.strftime('%Y-%m-%d %H:00')
            hourly_data[hour_key] += 1
        
        # Convert to list
        trends = []
        for hour, count in sorted(hourly_data.items()):
            trends.append({
                'timestamp': hour,
                'count': count
            })
        
        return {'hourly': trends}
    
    async def start_processing(self, interval: int = 60):
        """Start background alert processing."""
        self._stop_processing = False
        
        async def process():
            while not self._stop_processing:
                try:
                    # Check for escalations
                    self._check_escalations()
                    
                    # Clean up old data
                    self._cleanup_old_data()
                    
                    await asyncio.sleep(interval)
                except Exception as e:
                    logger.error(f"Error in alert processing: {e}")
                    await asyncio.sleep(5)
        
        self._processing_task = asyncio.create_task(process())
        logger.info("Alert processing started")
    
    async def stop_processing(self):
        """Stop background alert processing."""
        self._stop_processing = True
        if self._processing_task:
            self._processing_task.cancel()
            try:
                await self._processing_task
            except asyncio.CancelledError:
                pass
        logger.info("Alert processing stopped")
    
    def _check_escalations(self):
        """Check for alert escalations."""
        # Implementation would check escalation policies
        # and send escalated notifications
        pass
    
    def _cleanup_old_data(self):
        """Clean up old alert data."""
        cutoff_time = datetime.now() - timedelta(days=7)
        
        with self._lock:
            old_count = len(self._alert_history)
            self._alert_history = deque(
                [a for a in self._alert_history if a.created_at > cutoff_time],
                maxlen=10000
            )
            
            cleaned_count = old_count - len(self._alert_history)
            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} old alerts")
    
    def export_configuration(self) -> Dict[str, Any]:
        """Export alert manager configuration."""
        return {
            'alert_rules': [
                {
                    'name': rule.name,
                    'description': rule.description,
                    'metric_name': rule.metric_name,
                    'operator': rule.operator,
                    'threshold': rule.threshold,
                    'severity': rule.severity.value,
                    'duration': rule.duration,
                    'labels': rule.labels,
                    'annotations': rule.annotations,
                    'enabled': rule.enabled
                } for rule in self._alert_rules.values()
            ],
            'notification_rules': [
                {
                    'name': rule.name,
                    'channels': [c.value for c in rule.channels],
                    'severity_filter': [s.value for s in rule.severity_filter],
                    'label_filters': rule.label_filters,
                    'enabled': rule.enabled,
                    'repeat_interval': rule.repeat_interval
                } for rule in self._notification_rules.values()
            ]
        }


# Global alert manager instance
_alert_manager_instance: Optional[AlertManager] = None


def get_alert_manager() -> AlertManager:
    """Get the global alert manager instance."""
    global _alert_manager_instance
    if _alert_manager_instance is None:
        _alert_manager_instance = AlertManager()
    return _alert_manager_instance


def init_alert_manager() -> AlertManager:
    """Initialize the global alert manager instance."""
    global _alert_manager_instance
    _alert_manager_instance = AlertManager()
    return _alert_manager_instance