"""
Business Metrics Collection

Business and operational metrics including:
- User engagement metrics
- Pattern analysis usage
- Revenue/cost metrics
- SLA compliance
- Business KPIs
"""

import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from collections import defaultdict, deque
from datetime import datetime, timedelta
from enum import Enum
import logging
from prometheus_client import Counter, Histogram, Gauge, Info

logger = logging.getLogger(__name__)


class UserTier(Enum):
    """User subscription tiers."""
    FREE = "free"
    BASIC = "basic"
    PREMIUM = "premium"
    ENTERPRISE = "enterprise"


class AnalysisType(Enum):
    """Types of analysis performed."""
    PATTERN_DETECTION = "pattern_detection"
    CODE_REVIEW = "code_review"
    REPOSITORY_SCAN = "repository_scan"
    SECURITY_AUDIT = "security_audit"
    PERFORMANCE_ANALYSIS = "performance_analysis"
    QUALITY_ASSESSMENT = "quality_assessment"


@dataclass
class BusinessEvent:
    """Business event tracking."""
    event_type: str
    user_id: str
    timestamp: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)
    value: float = 0.0


@dataclass
class UserSession:
    """User session tracking."""
    session_id: str
    user_id: str
    user_tier: UserTier
    start_time: datetime
    end_time: Optional[datetime] = None
    actions: List[str] = field(default_factory=list)
    analyses_performed: List[AnalysisType] = field(default_factory=list)
    patterns_found: int = 0
    bytes_processed: int = 0


class BusinessMetrics:
    """
    Business metrics collector for tracking operational KPIs,
    user engagement, and business performance indicators.
    """
    
    def __init__(self):
        """Initialize business metrics collector."""
        self._user_sessions: Dict[str, UserSession] = {}
        self._business_events: deque = deque(maxlen=100000)
        self._daily_stats: Dict[str, Dict[str, Any]] = defaultdict(dict)
        
        # Initialize metrics
        self._init_metrics()
        
        logger.info("Business metrics initialized")
    
    def _init_metrics(self):
        """Initialize Prometheus metrics."""
        # User engagement metrics
        self.active_users = Gauge(
            'active_users_total',
            'Total number of active users',
            ['time_window', 'user_tier']
        )
        
        self.user_sessions_total = Counter(
            'user_sessions_total',
            'Total number of user sessions',
            ['user_tier', 'session_outcome']
        )
        
        self.user_session_duration = Histogram(
            'user_session_duration_seconds',
            'User session duration in seconds',
            ['user_tier'],
            buckets=[60, 300, 900, 1800, 3600, 7200, 14400, 28800, 86400]
        )
        
        self.user_actions_per_session = Histogram(
            'user_actions_per_session',
            'Number of actions per user session',
            ['user_tier', 'action_type'],
            buckets=[1, 5, 10, 25, 50, 100, 250, 500, 1000]
        )
        
        # Analysis usage metrics
        self.analyses_performed = Counter(
            'analyses_performed_total',
            'Total number of analyses performed',
            ['analysis_type', 'user_tier', 'status']
        )
        
        self.analysis_duration = Histogram(
            'analysis_duration_seconds',
            'Analysis duration in seconds',
            ['analysis_type', 'complexity_level'],
            buckets=[1, 5, 10, 30, 60, 120, 300, 600, 1200, 3600]
        )
        
        self.patterns_detected_per_analysis = Histogram(
            'patterns_detected_per_analysis',
            'Number of patterns detected per analysis',
            ['analysis_type', 'user_tier'],
            buckets=[0, 1, 5, 10, 25, 50, 100, 250, 500, 1000]
        )
        
        # Data processing metrics
        self.bytes_processed = Counter(
            'bytes_processed_total',
            'Total bytes processed',
            ['analysis_type', 'user_tier']
        )
        
        self.repositories_analyzed = Counter(
            'repositories_analyzed_total',
            'Total repositories analyzed',
            ['repository_size', 'user_tier']
        )
        
        self.files_processed = Counter(
            'files_processed_total',
            'Total files processed',
            ['file_type', 'analysis_type']
        )
        
        # Revenue and cost metrics
        self.revenue_per_user = Gauge(
            'revenue_per_user_dollars',
            'Revenue per user in dollars',
            ['user_tier', 'time_period']
        )
        
        self.cost_per_analysis = Gauge(
            'cost_per_analysis_dollars',
            'Cost per analysis in dollars',
            ['analysis_type', 'resource_type']
        )
        
        self.profit_margin = Gauge(
            'profit_margin_percent',
            'Profit margin percentage',
            ['user_tier', 'service_type']
        )
        
        # SLA compliance metrics
        self.sla_compliance_rate = Gauge(
            'sla_compliance_rate_percent',
            'SLA compliance rate percentage',
            ['sla_type', 'user_tier']
        )
        
        self.response_time_sla = Gauge(
            'response_time_sla_seconds',
            'Response time SLA in seconds',
            ['endpoint', 'user_tier']
        )
        
        self.uptime_percentage = Gauge(
            'uptime_percentage',
            'Service uptime percentage',
            ['service_component', 'time_window']
        )
        
        # Quality metrics
        self.customer_satisfaction = Gauge(
            'customer_satisfaction_score',
            'Customer satisfaction score',
            ['user_tier', 'feedback_type']
        )
        
        self.false_positive_rate = Gauge(
            'false_positive_rate_percent',
            'False positive rate percentage',
            ['pattern_type', 'model_version']
        )
        
        self.accuracy_score = Gauge(
            'accuracy_score_percent',
            'Accuracy score percentage',
            ['analysis_type', 'evaluation_type']
        )
        
        # Usage limits and quotas
        self.usage_quota_utilization = Gauge(
            'usage_quota_utilization_percent',
            'Usage quota utilization percentage',
            ['user_tier', 'resource_type']
        )
        
        self.rate_limit_violations = Counter(
            'rate_limit_violations_total',
            'Total rate limit violations',
            ['user_tier', 'endpoint']
        )
        
        # Business health indicators
        self.churn_rate = Gauge(
            'churn_rate_percent',
            'Customer churn rate percentage',
            ['user_tier', 'time_period']
        )
        
        self.conversion_rate = Gauge(
            'conversion_rate_percent',
            'Conversion rate percentage',
            ['from_tier', 'to_tier']
        )
        
        self.feature_adoption_rate = Gauge(
            'feature_adoption_rate_percent',
            'Feature adoption rate percentage',
            ['feature_name', 'user_tier']
        )
    
    def start_user_session(self, session_id: str, user_id: str, user_tier: UserTier) -> UserSession:
        """Start tracking a user session."""
        session = UserSession(
            session_id=session_id,
            user_id=user_id,
            user_tier=user_tier,
            start_time=datetime.now()
        )
        
        self._user_sessions[session_id] = session
        
        # Update active users
        self._update_active_users()
        
        logger.info(f"Started user session: {session_id} (user: {user_id}, tier: {user_tier.value})")
        return session
    
    def end_user_session(self, session_id: str, outcome: str = "completed") -> Optional[UserSession]:
        """End a user session and record metrics."""
        session = self._user_sessions.get(session_id)
        if not session:
            logger.warning(f"Session not found: {session_id}")
            return None
        
        session.end_time = datetime.now()
        session_duration = (session.end_time - session.start_time).total_seconds()
        
        # Record session metrics
        self.user_sessions_total.labels(
            user_tier=session.user_tier.value,
            session_outcome=outcome
        ).inc()
        
        self.user_session_duration.labels(
            user_tier=session.user_tier.value
        ).observe(session_duration)
        
        # Record actions per session
        for action in session.actions:
            self.user_actions_per_session.labels(
                user_tier=session.user_tier.value,
                action_type=action
            ).observe(1)
        
        # Record bytes processed
        if session.bytes_processed > 0:
            self.bytes_processed.labels(
                analysis_type="session_total",
                user_tier=session.user_tier.value
            ).inc(session.bytes_processed)
        
        # Remove from active sessions
        del self._user_sessions[session_id]
        
        # Update active users
        self._update_active_users()
        
        logger.info(f"Ended user session: {session_id} ({outcome})")
        return session
    
    def record_user_action(self, session_id: str, action: str, metadata: Optional[Dict] = None):
        """Record a user action."""
        session = self._user_sessions.get(session_id)
        if session:
            session.actions.append(action)
        
        # Record business event
        event = BusinessEvent(
            event_type=f"user_action_{action}",
            user_id=session.user_id if session else "unknown",
            timestamp=datetime.now(),
            metadata=metadata or {}
        )
        self._business_events.append(event)
    
    def record_analysis_performed(self, session_id: str, analysis_type: AnalysisType,
                                duration: float, patterns_found: int, bytes_processed: int,
                                status: str = "completed"):
        """Record an analysis performed."""
        session = self._user_sessions.get(session_id)
        user_tier = session.user_tier if session else UserTier.FREE
        
        if session:
            session.analyses_performed.append(analysis_type)
            session.patterns_found += patterns_found
            session.bytes_processed += bytes_processed
        
        # Record metrics
        self.analyses_performed.labels(
            analysis_type=analysis_type.value,
            user_tier=user_tier.value,
            status=status
        ).inc()
        
        complexity_level = self._get_complexity_level(bytes_processed)
        self.analysis_duration.labels(
            analysis_type=analysis_type.value,
            complexity_level=complexity_level
        ).observe(duration)
        
        self.patterns_detected_per_analysis.labels(
            analysis_type=analysis_type.value,
            user_tier=user_tier.value
        ).observe(patterns_found)
        
        self.bytes_processed.labels(
            analysis_type=analysis_type.value,
            user_tier=user_tier.value
        ).inc(bytes_processed)
    
    def record_repository_analysis(self, repository_size: str, user_tier: UserTier,
                                 files_count: int, file_types: List[str]):
        """Record repository analysis."""
        self.repositories_analyzed.labels(
            repository_size=repository_size,
            user_tier=user_tier.value
        ).inc()
        
        for file_type in file_types:
            self.files_processed.labels(
                file_type=file_type,
                analysis_type="repository_scan"
            ).inc()
    
    def update_revenue_metrics(self, user_tier: UserTier, revenue: float, time_period: str):
        """Update revenue metrics."""
        self.revenue_per_user.labels(
            user_tier=user_tier.value,
            time_period=time_period
        ).set(revenue)
    
    def update_cost_metrics(self, analysis_type: AnalysisType, resource_type: str, cost: float):
        """Update cost metrics."""
        self.cost_per_analysis.labels(
            analysis_type=analysis_type.value,
            resource_type=resource_type
        ).set(cost)
    
    def update_sla_metrics(self, sla_type: str, user_tier: UserTier, compliance_rate: float):
        """Update SLA compliance metrics."""
        self.sla_compliance_rate.labels(
            sla_type=sla_type,
            user_tier=user_tier.value
        ).set(compliance_rate)
    
    def record_rate_limit_violation(self, user_tier: UserTier, endpoint: str):
        """Record a rate limit violation."""
        self.rate_limit_violations.labels(
            user_tier=user_tier.value,
            endpoint=endpoint
        ).inc()
    
    def update_quality_metrics(self, pattern_type: str, model_version: str,
                             false_positive_rate: float, accuracy: float):
        """Update quality metrics."""
        self.false_positive_rate.labels(
            pattern_type=pattern_type,
            model_version=model_version
        ).set(false_positive_rate)
        
        self.accuracy_score.labels(
            analysis_type=pattern_type,
            evaluation_type="production"
        ).set(accuracy)
    
    def update_usage_quota(self, user_tier: UserTier, resource_type: str, utilization: float):
        """Update usage quota utilization."""
        self.usage_quota_utilization.labels(
            user_tier=user_tier.value,
            resource_type=resource_type
        ).set(utilization)
    
    def update_business_health(self, churn_rate: float, conversion_rates: Dict[str, float],
                             feature_adoption: Dict[str, float]):
        """Update business health indicators."""
        for user_tier in UserTier:
            self.churn_rate.labels(
                user_tier=user_tier.value,
                time_period="monthly"
            ).set(churn_rate)
        
        for conversion_key, rate in conversion_rates.items():
            from_tier, to_tier = conversion_key.split('_to_')
            self.conversion_rate.labels(
                from_tier=from_tier,
                to_tier=to_tier
            ).set(rate)
        
        for feature, adoption_rate in feature_adoption.items():
            for user_tier in UserTier:
                self.feature_adoption_rate.labels(
                    feature_name=feature,
                    user_tier=user_tier.value
                ).set(adoption_rate)
    
    def _update_active_users(self):
        """Update active user counts."""
        # Count active users by tier
        tier_counts = defaultdict(int)
        for session in self._user_sessions.values():
            tier_counts[session.user_tier] += 1
        
        # Update metrics
        for tier, count in tier_counts.items():
            self.active_users.labels(
                time_window="current",
                user_tier=tier.value
            ).set(count)
    
    def _get_complexity_level(self, bytes_processed: int) -> str:
        """Get complexity level based on bytes processed."""
        if bytes_processed < 1000:
            return "very_low"
        elif bytes_processed < 10000:
            return "low"
        elif bytes_processed < 100000:
            return "medium"
        elif bytes_processed < 1000000:
            return "high"
        else:
            return "very_high"
    
    def get_daily_stats(self, date: str) -> Dict[str, Any]:
        """Get daily statistics."""
        return self._daily_stats.get(date, {})
    
    def get_user_engagement_stats(self) -> Dict[str, Any]:
        """Get user engagement statistics."""
        active_sessions = len(self._user_sessions)
        
        # Calculate average session duration
        total_duration = 0
        session_count = 0
        
        for session in self._user_sessions.values():
            current_duration = (datetime.now() - session.start_time).total_seconds()
            total_duration += current_duration
            session_count += 1
        
        avg_session_duration = total_duration / session_count if session_count > 0 else 0
        
        # Count by tier
        tier_distribution = defaultdict(int)
        for session in self._user_sessions.values():
            tier_distribution[session.user_tier.value] += 1
        
        return {
            'active_sessions': active_sessions,
            'average_session_duration': avg_session_duration,
            'tier_distribution': dict(tier_distribution),
            'total_events': len(self._business_events)
        }
    
    def get_analysis_stats(self) -> Dict[str, Any]:
        """Get analysis statistics."""
        # Count analyses by type from recent events
        analysis_counts = defaultdict(int)
        
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        for event in self._business_events:
            if event.timestamp > cutoff_time and event.event_type.startswith('analysis_'):
                analysis_type = event.event_type.replace('analysis_', '')
                analysis_counts[analysis_type] += 1
        
        return dict(analysis_counts)
    
    def cleanup_old_events(self, max_age_hours: int = 24):
        """Clean up old business events."""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        old_length = len(self._business_events)
        self._business_events = deque(
            [event for event in self._business_events if event.timestamp > cutoff_time],
            maxlen=100000
        )
        
        cleaned_count = old_length - len(self._business_events)
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} old business events")


# Global business metrics instance
_business_metrics_instance: Optional[BusinessMetrics] = None


def get_business_metrics() -> BusinessMetrics:
    """Get the global business metrics instance."""
    global _business_metrics_instance
    if _business_metrics_instance is None:
        _business_metrics_instance = BusinessMetrics()
    return _business_metrics_instance


def init_business_metrics() -> BusinessMetrics:
    """Initialize the global business metrics instance."""
    global _business_metrics_instance
    _business_metrics_instance = BusinessMetrics()
    return _business_metrics_instance