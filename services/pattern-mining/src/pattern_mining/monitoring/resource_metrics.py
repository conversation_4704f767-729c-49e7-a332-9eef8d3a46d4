"""
Resource Metrics Collection

System resource monitoring including:
- CPU, memory, disk, network utilization
- GPU metrics for ML workloads
- Database connection pools
- Cache performance
- External service health
"""

import psutil
import time
import asyncio
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
from prometheus_client import Counter, Histogram, Gauge
import threading
from collections import deque, defaultdict

try:
    import pynvml
    NVIDIA_GPU_AVAILABLE = True
except ImportError:
    NVIDIA_GPU_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class ResourceSnapshot:
    """Snapshot of system resources at a point in time."""
    timestamp: datetime
    cpu_percent: float
    memory_usage: Dict[str, int]
    disk_usage: Dict[str, Dict[str, int]]
    network_io: Dict[str, int]
    gpu_usage: Optional[Dict[str, Any]] = None
    process_info: Optional[Dict[str, Any]] = None


class ResourceMetrics:
    """
    System resource metrics collector.
    
    Monitors CPU, memory, disk, network, GPU, and other system resources
    with detailed breakdowns and alerting capabilities.
    """
    
    def __init__(self, monitor_gpu: bool = True, monitor_processes: bool = True):
        """Initialize resource metrics collector."""
        self.monitor_gpu = monitor_gpu and NVIDIA_GPU_AVAILABLE
        self.monitor_processes = monitor_processes
        
        # Initialize NVIDIA GPU monitoring if available
        if self.monitor_gpu:
            try:
                pynvml.nvmlInit()
                self.gpu_count = pynvml.nvmlDeviceGetCount()
                logger.info(f"Initialized GPU monitoring for {self.gpu_count} GPUs")
            except Exception as e:
                logger.warning(f"Failed to initialize GPU monitoring: {e}")
                self.monitor_gpu = False
        
        # Resource snapshots history
        self._resource_history: deque = deque(maxlen=1000)
        self._collection_lock = threading.Lock()
        
        # Initialize metrics
        self._init_metrics()
        
        # Background collection
        self._collection_task = None
        self._stop_collection = False
        
        logger.info("Resource metrics initialized")
    
    def _init_metrics(self):
        """Initialize Prometheus metrics."""
        # CPU metrics
        self.cpu_usage_percent = Gauge(
            'cpu_usage_percent',
            'CPU usage percentage',
            ['cpu_id', 'mode']
        )
        
        self.cpu_load_average = Gauge(
            'cpu_load_average',
            'CPU load average',
            ['period']
        )
        
        self.cpu_context_switches = Counter(
            'cpu_context_switches_total',
            'Total CPU context switches'
        )
        
        self.cpu_interrupts = Counter(
            'cpu_interrupts_total',
            'Total CPU interrupts'
        )
        
        self.cpu_frequency = Gauge(
            'cpu_frequency_mhz',
            'CPU frequency in MHz',
            ['cpu_id', 'type']
        )
        
        # Memory metrics
        self.memory_usage_bytes = Gauge(
            'memory_usage_bytes',
            'Memory usage in bytes',
            ['memory_type']
        )
        
        self.memory_usage_percent = Gauge(
            'memory_usage_percent',
            'Memory usage percentage',
            ['memory_type']
        )
        
        self.swap_usage_bytes = Gauge(
            'swap_usage_bytes',
            'Swap usage in bytes',
            ['swap_type']
        )
        
        # Disk metrics
        self.disk_usage_bytes = Gauge(
            'disk_usage_bytes',
            'Disk usage in bytes',
            ['device', 'mountpoint', 'usage_type']
        )
        
        self.disk_usage_percent = Gauge(
            'disk_usage_percent',
            'Disk usage percentage',
            ['device', 'mountpoint']
        )
        
        self.disk_io_operations = Counter(
            'disk_io_operations_total',
            'Total disk I/O operations',
            ['device', 'operation']
        )
        
        self.disk_io_bytes = Counter(
            'disk_io_bytes_total',
            'Total disk I/O bytes',
            ['device', 'operation']
        )
        
        self.disk_io_time = Counter(
            'disk_io_time_seconds_total',
            'Total disk I/O time in seconds',
            ['device', 'operation']
        )
        
        # Network metrics
        self.network_bytes = Counter(
            'network_bytes_total',
            'Total network bytes',
            ['interface', 'direction']
        )
        
        self.network_packets = Counter(
            'network_packets_total',
            'Total network packets',
            ['interface', 'direction']
        )
        
        self.network_errors = Counter(
            'network_errors_total',
            'Total network errors',
            ['interface', 'error_type']
        )
        
        self.network_dropped = Counter(
            'network_dropped_total',
            'Total network dropped packets',
            ['interface', 'direction']
        )
        
        # GPU metrics (if available)
        if self.monitor_gpu:
            self.gpu_utilization_percent = Gauge(
                'gpu_utilization_percent',
                'GPU utilization percentage',
                ['gpu_id', 'utilization_type']
            )
            
            self.gpu_memory_usage_bytes = Gauge(
                'gpu_memory_usage_bytes',
                'GPU memory usage in bytes',
                ['gpu_id', 'memory_type']
            )
            
            self.gpu_temperature_celsius = Gauge(
                'gpu_temperature_celsius',
                'GPU temperature in Celsius',
                ['gpu_id']
            )
            
            self.gpu_power_usage_watts = Gauge(
                'gpu_power_usage_watts',
                'GPU power usage in watts',
                ['gpu_id']
            )
            
            self.gpu_fan_speed_percent = Gauge(
                'gpu_fan_speed_percent',
                'GPU fan speed percentage',
                ['gpu_id']
            )
        
        # Process metrics
        if self.monitor_processes:
            self.process_cpu_percent = Gauge(
                'process_cpu_percent',
                'Process CPU usage percentage',
                ['pid', 'name']
            )
            
            self.process_memory_bytes = Gauge(
                'process_memory_bytes',
                'Process memory usage in bytes',
                ['pid', 'name', 'memory_type']
            )
            
            self.process_open_files = Gauge(
                'process_open_files',
                'Number of open files per process',
                ['pid', 'name']
            )
            
            self.process_threads = Gauge(
                'process_threads',
                'Number of threads per process',
                ['pid', 'name']
            )
        
        # System metrics
        self.system_uptime_seconds = Gauge(
            'system_uptime_seconds',
            'System uptime in seconds'
        )
        
        self.system_boot_time = Gauge(
            'system_boot_time',
            'System boot time as Unix timestamp'
        )
        
        self.system_processes_total = Gauge(
            'system_processes_total',
            'Total number of processes'
        )
        
        self.system_threads_total = Gauge(
            'system_threads_total',
            'Total number of threads'
        )
        
        # Database connection pool metrics
        self.db_connections = Gauge(
            'db_connections',
            'Database connections',
            ['database', 'state']
        )
        
        self.db_pool_size = Gauge(
            'db_pool_size',
            'Database connection pool size',
            ['database', 'pool_type']
        )
        
        # Cache metrics
        self.cache_size_bytes = Gauge(
            'cache_size_bytes',
            'Cache size in bytes',
            ['cache_name']
        )
        
        self.cache_hit_rate_percent = Gauge(
            'cache_hit_rate_percent',
            'Cache hit rate percentage',
            ['cache_name']
        )
        
        self.cache_evictions = Counter(
            'cache_evictions_total',
            'Total cache evictions',
            ['cache_name', 'reason']
        )
    
    def collect_resource_snapshot(self) -> ResourceSnapshot:
        """Collect a snapshot of current system resources."""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            memory_usage = {
                'total': memory.total,
                'available': memory.available,
                'used': memory.used,
                'free': memory.free,
                'buffers': getattr(memory, 'buffers', 0),
                'cached': getattr(memory, 'cached', 0)
            }
            
            # Disk metrics
            disk_usage = {}
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_usage[partition.device] = {
                        'mountpoint': partition.mountpoint,
                        'total': usage.total,
                        'used': usage.used,
                        'free': usage.free
                    }
                except PermissionError:
                    # Skip inaccessible partitions
                    continue
            
            # Network metrics
            network_io = psutil.net_io_counters()
            network_usage = {
                'bytes_sent': network_io.bytes_sent,
                'bytes_recv': network_io.bytes_recv,
                'packets_sent': network_io.packets_sent,
                'packets_recv': network_io.packets_recv,
                'errin': network_io.errin,
                'errout': network_io.errout,
                'dropin': network_io.dropin,
                'dropout': network_io.dropout
            }
            
            # GPU metrics
            gpu_usage = None
            if self.monitor_gpu:
                gpu_usage = self._collect_gpu_metrics()
            
            # Process metrics
            process_info = None
            if self.monitor_processes:
                process_info = self._collect_process_metrics()
            
            snapshot = ResourceSnapshot(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_usage=memory_usage,
                disk_usage=disk_usage,
                network_io=network_usage,
                gpu_usage=gpu_usage,
                process_info=process_info
            )
            
            with self._collection_lock:
                self._resource_history.append(snapshot)
            
            return snapshot
            
        except Exception as e:
            logger.error(f"Error collecting resource snapshot: {e}")
            raise
    
    def _collect_gpu_metrics(self) -> Dict[str, Any]:
        """Collect GPU metrics."""
        if not self.monitor_gpu:
            return {}
        
        gpu_metrics = {}
        
        try:
            for i in range(self.gpu_count):
                handle = pynvml.nvmlDeviceGetHandleByIndex(i)
                
                # GPU utilization
                util = pynvml.nvmlDeviceGetUtilizationRates(handle)
                
                # Memory info
                mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                
                # Temperature
                temp = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
                
                # Power usage
                try:
                    power = pynvml.nvmlDeviceGetPowerUsage(handle)
                except pynvml.NVMLError:
                    power = 0
                
                # Fan speed
                try:
                    fan_speed = pynvml.nvmlDeviceGetFanSpeed(handle)
                except pynvml.NVMLError:
                    fan_speed = 0
                
                gpu_metrics[f'gpu_{i}'] = {
                    'utilization': util.gpu,
                    'memory_utilization': util.memory,
                    'memory_total': mem_info.total,
                    'memory_used': mem_info.used,
                    'memory_free': mem_info.free,
                    'temperature': temp,
                    'power_usage': power,
                    'fan_speed': fan_speed
                }
                
        except Exception as e:
            logger.error(f"Error collecting GPU metrics: {e}")
        
        return gpu_metrics
    
    def _collect_process_metrics(self) -> Dict[str, Any]:
        """Collect process metrics."""
        if not self.monitor_processes:
            return {}
        
        process_metrics = {}
        
        try:
            # Get top processes by CPU and memory
            processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_info', 'num_threads']):
                try:
                    proc_info = proc.info
                    if proc_info['cpu_percent'] is not None:
                        processes.append(proc_info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # Sort by CPU usage and take top 10
            top_cpu_processes = sorted(processes, key=lambda x: x['cpu_percent'], reverse=True)[:10]
            
            # Sort by memory usage and take top 10
            top_memory_processes = sorted(processes, key=lambda x: x['memory_info'].rss, reverse=True)[:10]
            
            process_metrics = {
                'top_cpu_processes': top_cpu_processes,
                'top_memory_processes': top_memory_processes,
                'total_processes': len(processes)
            }
            
        except Exception as e:
            logger.error(f"Error collecting process metrics: {e}")
        
        return process_metrics
    
    def update_prometheus_metrics(self, snapshot: ResourceSnapshot):
        """Update Prometheus metrics with resource snapshot."""
        try:
            # CPU metrics
            self.cpu_usage_percent.labels(cpu_id='overall', mode='combined').set(snapshot.cpu_percent)
            
            # Load average
            load_avg = psutil.getloadavg()
            self.cpu_load_average.labels(period='1m').set(load_avg[0])
            self.cpu_load_average.labels(period='5m').set(load_avg[1])
            self.cpu_load_average.labels(period='15m').set(load_avg[2])
            
            # CPU stats
            cpu_stats = psutil.cpu_stats()
            self.cpu_context_switches.inc(cpu_stats.ctx_switches)
            self.cpu_interrupts.inc(cpu_stats.interrupts)
            
            # Memory metrics
            memory = snapshot.memory_usage
            self.memory_usage_bytes.labels(memory_type='total').set(memory['total'])
            self.memory_usage_bytes.labels(memory_type='used').set(memory['used'])
            self.memory_usage_bytes.labels(memory_type='available').set(memory['available'])
            self.memory_usage_bytes.labels(memory_type='free').set(memory['free'])
            
            memory_percent = (memory['used'] / memory['total']) * 100
            self.memory_usage_percent.labels(memory_type='used').set(memory_percent)
            
            # Swap metrics
            swap = psutil.swap_memory()
            self.swap_usage_bytes.labels(swap_type='total').set(swap.total)
            self.swap_usage_bytes.labels(swap_type='used').set(swap.used)
            self.swap_usage_bytes.labels(swap_type='free').set(swap.free)
            
            # Disk metrics
            for device, usage in snapshot.disk_usage.items():
                mountpoint = usage['mountpoint']
                self.disk_usage_bytes.labels(device=device, mountpoint=mountpoint, usage_type='total').set(usage['total'])
                self.disk_usage_bytes.labels(device=device, mountpoint=mountpoint, usage_type='used').set(usage['used'])
                self.disk_usage_bytes.labels(device=device, mountpoint=mountpoint, usage_type='free').set(usage['free'])
                
                disk_percent = (usage['used'] / usage['total']) * 100
                self.disk_usage_percent.labels(device=device, mountpoint=mountpoint).set(disk_percent)
            
            # Disk I/O metrics
            disk_io = psutil.disk_io_counters(perdisk=True)
            for device, io_stats in disk_io.items():
                self.disk_io_operations.labels(device=device, operation='read').inc(io_stats.read_count)
                self.disk_io_operations.labels(device=device, operation='write').inc(io_stats.write_count)
                self.disk_io_bytes.labels(device=device, operation='read').inc(io_stats.read_bytes)
                self.disk_io_bytes.labels(device=device, operation='write').inc(io_stats.write_bytes)
                self.disk_io_time.labels(device=device, operation='read').inc(io_stats.read_time / 1000.0)
                self.disk_io_time.labels(device=device, operation='write').inc(io_stats.write_time / 1000.0)
            
            # Network metrics
            network_io = psutil.net_io_counters(pernic=True)
            for interface, io_stats in network_io.items():
                self.network_bytes.labels(interface=interface, direction='sent').inc(io_stats.bytes_sent)
                self.network_bytes.labels(interface=interface, direction='recv').inc(io_stats.bytes_recv)
                self.network_packets.labels(interface=interface, direction='sent').inc(io_stats.packets_sent)
                self.network_packets.labels(interface=interface, direction='recv').inc(io_stats.packets_recv)
                self.network_errors.labels(interface=interface, error_type='in').inc(io_stats.errin)
                self.network_errors.labels(interface=interface, error_type='out').inc(io_stats.errout)
                self.network_dropped.labels(interface=interface, direction='in').inc(io_stats.dropin)
                self.network_dropped.labels(interface=interface, direction='out').inc(io_stats.dropout)
            
            # GPU metrics
            if snapshot.gpu_usage:
                for gpu_id, gpu_data in snapshot.gpu_usage.items():
                    self.gpu_utilization_percent.labels(gpu_id=gpu_id, utilization_type='gpu').set(gpu_data['utilization'])
                    self.gpu_utilization_percent.labels(gpu_id=gpu_id, utilization_type='memory').set(gpu_data['memory_utilization'])
                    self.gpu_memory_usage_bytes.labels(gpu_id=gpu_id, memory_type='total').set(gpu_data['memory_total'])
                    self.gpu_memory_usage_bytes.labels(gpu_id=gpu_id, memory_type='used').set(gpu_data['memory_used'])
                    self.gpu_memory_usage_bytes.labels(gpu_id=gpu_id, memory_type='free').set(gpu_data['memory_free'])
                    self.gpu_temperature_celsius.labels(gpu_id=gpu_id).set(gpu_data['temperature'])
                    self.gpu_power_usage_watts.labels(gpu_id=gpu_id).set(gpu_data['power_usage'])
                    self.gpu_fan_speed_percent.labels(gpu_id=gpu_id).set(gpu_data['fan_speed'])
            
            # Process metrics
            if snapshot.process_info:
                for proc in snapshot.process_info['top_cpu_processes']:
                    pid = str(proc['pid'])
                    name = proc['name']
                    self.process_cpu_percent.labels(pid=pid, name=name).set(proc['cpu_percent'])
                    self.process_memory_bytes.labels(pid=pid, name=name, memory_type='rss').set(proc['memory_info'].rss)
                    self.process_threads.labels(pid=pid, name=name).set(proc['num_threads'])
            
            # System metrics
            self.system_uptime_seconds.set(time.time() - psutil.boot_time())
            self.system_boot_time.set(psutil.boot_time())
            
            process_count = len(psutil.pids())
            self.system_processes_total.set(process_count)
            
        except Exception as e:
            logger.error(f"Error updating Prometheus metrics: {e}")
    
    def update_database_metrics(self, database: str, total_connections: int, 
                              active_connections: int, idle_connections: int,
                              pool_size: int, max_pool_size: int):
        """Update database connection metrics."""
        self.db_connections.labels(database=database, state='total').set(total_connections)
        self.db_connections.labels(database=database, state='active').set(active_connections)
        self.db_connections.labels(database=database, state='idle').set(idle_connections)
        self.db_pool_size.labels(database=database, pool_type='current').set(pool_size)
        self.db_pool_size.labels(database=database, pool_type='max').set(max_pool_size)
    
    def update_cache_metrics(self, cache_name: str, size_bytes: int, hit_rate: float, evictions: int):
        """Update cache metrics."""
        self.cache_size_bytes.labels(cache_name=cache_name).set(size_bytes)
        self.cache_hit_rate_percent.labels(cache_name=cache_name).set(hit_rate * 100)
        self.cache_evictions.labels(cache_name=cache_name, reason='lru').inc(evictions)
    
    def get_resource_summary(self) -> Dict[str, Any]:
        """Get resource usage summary."""
        with self._collection_lock:
            if not self._resource_history:
                return {}
            
            latest = self._resource_history[-1]
            
            summary = {
                'timestamp': latest.timestamp.isoformat(),
                'cpu_percent': latest.cpu_percent,
                'memory_usage_percent': (latest.memory_usage['used'] / latest.memory_usage['total']) * 100,
                'disk_usage': {},
                'network_io': latest.network_io,
                'gpu_usage': latest.gpu_usage,
                'process_count': latest.process_info['total_processes'] if latest.process_info else 0
            }
            
            # Disk usage percentages
            for device, usage in latest.disk_usage.items():
                summary['disk_usage'][device] = {
                    'mountpoint': usage['mountpoint'],
                    'usage_percent': (usage['used'] / usage['total']) * 100
                }
            
            return summary
    
    def get_resource_trends(self, hours: int = 24) -> Dict[str, List[Dict]]:
        """Get resource usage trends over time."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        with self._collection_lock:
            recent_snapshots = [
                s for s in self._resource_history
                if s.timestamp > cutoff_time
            ]
        
        trends = {
            'cpu': [],
            'memory': [],
            'disk': [],
            'network': [],
            'gpu': []
        }
        
        for snapshot in recent_snapshots:
            timestamp = snapshot.timestamp.isoformat()
            
            # CPU trend
            trends['cpu'].append({
                'timestamp': timestamp,
                'value': snapshot.cpu_percent
            })
            
            # Memory trend
            memory_percent = (snapshot.memory_usage['used'] / snapshot.memory_usage['total']) * 100
            trends['memory'].append({
                'timestamp': timestamp,
                'value': memory_percent
            })
            
            # Network trend
            trends['network'].append({
                'timestamp': timestamp,
                'bytes_sent': snapshot.network_io['bytes_sent'],
                'bytes_recv': snapshot.network_io['bytes_recv']
            })
            
            # GPU trend
            if snapshot.gpu_usage:
                for gpu_id, gpu_data in snapshot.gpu_usage.items():
                    if gpu_id not in trends['gpu']:
                        trends['gpu'].append({
                            'gpu_id': gpu_id,
                            'data': []
                        })
                    
                    trends['gpu'][-1]['data'].append({
                        'timestamp': timestamp,
                        'utilization': gpu_data['utilization'],
                        'memory_utilization': gpu_data['memory_utilization']
                    })
        
        return trends
    
    async def start_collection(self, interval: int = 30):
        """Start background resource collection."""
        self._stop_collection = False
        
        async def collect():
            while not self._stop_collection:
                try:
                    snapshot = self.collect_resource_snapshot()
                    self.update_prometheus_metrics(snapshot)
                    await asyncio.sleep(interval)
                except Exception as e:
                    logger.error(f"Error in resource collection: {e}")
                    await asyncio.sleep(5)
        
        self._collection_task = asyncio.create_task(collect())
        logger.info("Resource collection started")
    
    async def stop_collection(self):
        """Stop background resource collection."""
        self._stop_collection = True
        if self._collection_task:
            self._collection_task.cancel()
            try:
                await self._collection_task
            except asyncio.CancelledError:
                pass
        logger.info("Resource collection stopped")
    
    def cleanup_old_snapshots(self, max_age_hours: int = 24):
        """Clean up old resource snapshots."""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        with self._collection_lock:
            old_length = len(self._resource_history)
            self._resource_history = deque(
                [s for s in self._resource_history if s.timestamp > cutoff_time],
                maxlen=1000
            )
            
            cleaned_count = old_length - len(self._resource_history)
            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} old resource snapshots")


# Global resource metrics instance
_resource_metrics_instance: Optional[ResourceMetrics] = None


def get_resource_metrics() -> ResourceMetrics:
    """Get the global resource metrics instance."""
    global _resource_metrics_instance
    if _resource_metrics_instance is None:
        _resource_metrics_instance = ResourceMetrics()
    return _resource_metrics_instance


def init_resource_metrics(monitor_gpu: bool = True, monitor_processes: bool = True) -> ResourceMetrics:
    """Initialize the global resource metrics instance."""
    global _resource_metrics_instance
    _resource_metrics_instance = ResourceMetrics(monitor_gpu, monitor_processes)
    return _resource_metrics_instance