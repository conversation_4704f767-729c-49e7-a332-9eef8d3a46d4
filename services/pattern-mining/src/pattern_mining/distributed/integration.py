"""
Integration module for distributed pattern mining services.

This module provides high-level integration of all distributed computing
components for seamless operation in production environments.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
import ray

from .cluster import Ray<PERSON>lusterManager, ClusterConfig, create_default_cluster_config
from .ml_pipeline import DistributedMLPipeline, DistributedTrainingConfig, ServingConfig
from .gpu_acceleration import GPUAccelerationManager, GPUConfig, MultiGPUConfig
from .task_manager import DistributedTaskManager, TaskType, TaskPriority, WorkerConfig
from .scaling import AutoScalingManager, ScalingConfig

logger = logging.getLogger(__name__)


@dataclass
class DistributedSystemConfig:
    """Configuration for the complete distributed system."""
    cluster_name: str = "pattern-mining-cluster"
    cluster_config: ClusterConfig = field(default_factory=lambda: create_default_cluster_config("pattern-mining-cluster"))
    gpu_config: GPUConfig = field(default_factory=GPUConfig)
    scaling_config: ScalingConfig = field(default_factory=ScalingConfig)
    enable_gpu_acceleration: bool = True
    enable_auto_scaling: bool = True
    enable_ml_pipeline: bool = True
    enable_task_distribution: bool = True
    num_workers: int = 4
    worker_configs: List[WorkerConfig] = field(default_factory=list)


class DistributedPatternMiningSystem:
    """
    Complete distributed pattern mining system.
    
    Integrates all distributed computing components for comprehensive
    pattern mining operations with scaling, GPU acceleration, and ML capabilities.
    """
    
    def __init__(self, config: DistributedSystemConfig):
        self.config = config
        self.cluster_manager = None
        self.gpu_manager = None
        self.ml_pipeline = None
        self.task_manager = None
        self.scaling_manager = None
        self.is_initialized = False
        self.is_running = False
        
        logger.info(f"Initialized distributed pattern mining system: {config.cluster_name}")
    
    async def initialize(self) -> bool:
        """
        Initialize the complete distributed system.
        
        Returns:
            bool: True if initialization successful
        """
        try:
            logger.info("Initializing distributed pattern mining system")
            
            # Initialize cluster manager
            self.cluster_manager = RayClusterManager(self.config.cluster_config)
            if not await self.cluster_manager.initialize_cluster():
                raise RuntimeError("Failed to initialize cluster")
            
            # Initialize GPU manager if enabled
            if self.config.enable_gpu_acceleration:
                self.gpu_manager = GPUAccelerationManager(self.config.gpu_config)
                if not await self.gpu_manager.initialize_gpu_resources():
                    logger.warning("GPU initialization failed, continuing without GPU support")
                    self.gpu_manager = None
            
            # Initialize ML pipeline if enabled
            if self.config.enable_ml_pipeline:
                self.ml_pipeline = DistributedMLPipeline(self.cluster_manager)
            
            # Initialize task manager if enabled
            if self.config.enable_task_distribution:
                self.task_manager = DistributedTaskManager(self.cluster_manager)
                
                # Create default worker configs if none provided
                if not self.config.worker_configs:
                    self.config.worker_configs = self._create_default_worker_configs()
                
                # Start workers
                if not await self.task_manager.start_workers(self.config.worker_configs):
                    raise RuntimeError("Failed to start task workers")
            
            # Initialize auto-scaling if enabled
            if self.config.enable_auto_scaling:
                self.scaling_manager = AutoScalingManager(
                    self.config.scaling_config,
                    self.cluster_manager,
                    self.task_manager,
                    self.gpu_manager
                )
                if not await self.scaling_manager.start_autoscaling():
                    logger.warning("Auto-scaling failed to start")
            
            self.is_initialized = True
            self.is_running = True
            
            logger.info("Distributed system initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize distributed system: {e}")
            await self.shutdown()
            return False
    
    async def shutdown(self) -> bool:
        """
        Shutdown the complete distributed system.
        
        Returns:
            bool: True if shutdown successful
        """
        try:
            logger.info("Shutting down distributed pattern mining system")
            
            self.is_running = False
            
            # Stop auto-scaling
            if self.scaling_manager:
                await self.scaling_manager.stop_autoscaling()
            
            # Stop task manager
            if self.task_manager:
                await self.task_manager.stop_workers()
            
            # Clean up GPU resources
            if self.gpu_manager:
                await self.gpu_manager.cleanup_gpu_resources()
            
            # Shutdown cluster
            if self.cluster_manager:
                await self.cluster_manager.shutdown_cluster()
            
            self.is_initialized = False
            
            logger.info("Distributed system shutdown completed")
            return True
            
        except Exception as e:
            logger.error(f"Failed to shutdown distributed system: {e}")
            return False
    
    async def train_pattern_model(
        self,
        model_class: type,
        training_config: DistributedTrainingConfig,
        dataset_path: str = None
    ) -> Any:
        """
        Train a pattern recognition model using distributed ML pipeline.
        
        Args:
            model_class: Model class to train
            training_config: Training configuration
            dataset_path: Path to training dataset
            
        Returns:
            Training result
        """
        if not self.ml_pipeline:
            raise RuntimeError("ML pipeline not initialized")
        
        if dataset_path:
            training_config.dataset_path = dataset_path
        
        return await self.ml_pipeline.train_model_distributed(
            training_config,
            model_class
        )
    
    async def serve_pattern_model(
        self,
        model_name: str,
        model_path: str,
        serving_config: ServingConfig = None
    ) -> Any:
        """
        Deploy a trained model for serving.
        
        Args:
            model_name: Name of the model
            model_path: Path to trained model
            serving_config: Serving configuration
            
        Returns:
            Deployment handle
        """
        if not self.ml_pipeline:
            raise RuntimeError("ML pipeline not initialized")
        
        if serving_config is None:
            serving_config = ServingConfig(
                model_name=model_name,
                model_path=model_path
            )
        
        return await self.ml_pipeline.serve_model(serving_config)
    
    async def submit_pattern_detection_task(
        self,
        code: str,
        config: Dict[str, Any] = None,
        priority: TaskPriority = TaskPriority.MEDIUM
    ) -> str:
        """
        Submit a pattern detection task for distributed processing.
        
        Args:
            code: Source code to analyze
            config: Detection configuration
            priority: Task priority
            
        Returns:
            Task ID
        """
        if not self.task_manager:
            raise RuntimeError("Task manager not initialized")
        
        task_data = {
            "code": code,
            "config": config or {}
        }
        
        return await self.task_manager.submit_task(
            TaskType.PATTERN_DETECTION,
            task_data,
            priority
        )
    
    async def submit_feature_extraction_task(
        self,
        code: str,
        config: Dict[str, Any] = None,
        priority: TaskPriority = TaskPriority.MEDIUM
    ) -> str:
        """
        Submit a feature extraction task for distributed processing.
        
        Args:
            code: Source code to analyze
            config: Extraction configuration
            priority: Task priority
            
        Returns:
            Task ID
        """
        if not self.task_manager:
            raise RuntimeError("Task manager not initialized")
        
        task_data = {
            "code": code,
            "config": config or {}
        }
        
        return await self.task_manager.submit_task(
            TaskType.FEATURE_EXTRACTION,
            task_data,
            priority
        )
    
    async def submit_repository_analysis_task(
        self,
        repo_path: str,
        config: Dict[str, Any] = None,
        priority: TaskPriority = TaskPriority.MEDIUM
    ) -> str:
        """
        Submit a repository analysis task for distributed processing.
        
        Args:
            repo_path: Path to repository
            config: Analysis configuration
            priority: Task priority
            
        Returns:
            Task ID
        """
        if not self.task_manager:
            raise RuntimeError("Task manager not initialized")
        
        task_data = {
            "repo_path": repo_path,
            "config": config or {}
        }
        
        return await self.task_manager.submit_task(
            TaskType.REPOSITORY_ANALYSIS,
            task_data,
            priority
        )
    
    async def get_task_result(self, task_id: str, timeout: int = None) -> Any:
        """
        Get result of a submitted task.
        
        Args:
            task_id: Task identifier
            timeout: Timeout in seconds
            
        Returns:
            Task result
        """
        if not self.task_manager:
            raise RuntimeError("Task manager not initialized")
        
        return await self.task_manager.get_task_result(task_id, timeout)
    
    async def get_system_status(self) -> Dict[str, Any]:
        """
        Get comprehensive system status.
        
        Returns:
            System status information
        """
        try:
            status = {
                "system": {
                    "initialized": self.is_initialized,
                    "running": self.is_running,
                    "cluster_name": self.config.cluster_name
                }
            }
            
            # Cluster status
            if self.cluster_manager:
                status["cluster"] = await self.cluster_manager.get_cluster_status()
            
            # GPU status
            if self.gpu_manager:
                status["gpu"] = await self.gpu_manager.get_gpu_metrics()
            
            # ML pipeline status
            if self.ml_pipeline:
                status["ml_pipeline"] = await self.ml_pipeline.get_pipeline_status()
            
            # Task manager status
            if self.task_manager:
                status["task_manager"] = await self.task_manager.get_manager_status()
            
            # Scaling status
            if self.scaling_manager:
                status["scaling"] = await self.scaling_manager.get_scaling_status()
            
            return status
            
        except Exception as e:
            logger.error(f"Failed to get system status: {e}")
            return {"error": str(e)}
    
    async def scale_system(self, target_workers: int) -> bool:
        """
        Manually scale the system to target number of workers.
        
        Args:
            target_workers: Target number of workers
            
        Returns:
            bool: True if scaling successful
        """
        if not self.scaling_manager:
            raise RuntimeError("Auto-scaling not initialized")
        
        return await self.scaling_manager.manual_scale(target_workers)
    
    async def get_scaling_recommendations(self) -> Dict[str, Any]:
        """
        Get scaling recommendations based on current system state.
        
        Returns:
            Scaling recommendations
        """
        if not self.scaling_manager:
            raise RuntimeError("Auto-scaling not initialized")
        
        return await self.scaling_manager.get_scaling_recommendations()
    
    async def health_check(self) -> Dict[str, bool]:
        """
        Perform comprehensive system health check.
        
        Returns:
            Health check results
        """
        try:
            health = {
                "system": self.is_running,
                "cluster": True,
                "gpu": True,
                "ml_pipeline": True,
                "task_manager": True,
                "scaling": True
            }
            
            # Check cluster health
            if self.cluster_manager:
                health["cluster"] = await self.cluster_manager.health_check()
            
            # Check GPU health (if enabled)
            if self.gpu_manager:
                gpu_metrics = await self.gpu_manager.get_gpu_metrics()
                health["gpu"] = gpu_metrics.get("gpu_count", 0) > 0
            
            # Check ML pipeline health
            if self.ml_pipeline:
                pipeline_status = await self.ml_pipeline.get_pipeline_status()
                health["ml_pipeline"] = pipeline_status.get("status") != "failed"
            
            # Check task manager health
            if self.task_manager:
                manager_status = await self.task_manager.get_manager_status()
                health["task_manager"] = manager_status.get("workers", {}).get("active", 0) > 0
            
            # Check scaling health
            if self.scaling_manager:
                scaling_status = await self.scaling_manager.get_scaling_status()
                health["scaling"] = scaling_status.get("scaling_active", False)
            
            return health
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {"error": str(e)}
    
    def _create_default_worker_configs(self) -> List[WorkerConfig]:
        """Create default worker configurations."""
        return [
            WorkerConfig(
                worker_id="pattern_detector_1",
                worker_type="pattern_detector",
                num_cpus=2,
                num_gpus=0,
                memory=**********,
                specialization=[TaskType.PATTERN_DETECTION]
            ),
            WorkerConfig(
                worker_id="pattern_detector_2",
                worker_type="pattern_detector",
                num_cpus=2,
                num_gpus=0,
                memory=**********,
                specialization=[TaskType.PATTERN_DETECTION]
            ),
            WorkerConfig(
                worker_id="feature_extractor_1",
                worker_type="feature_extractor",
                num_cpus=4,
                num_gpus=0,
                memory=**********,
                specialization=[TaskType.FEATURE_EXTRACTION]
            ),
            WorkerConfig(
                worker_id="ml_trainer_1",
                worker_type="ml_trainer",
                num_cpus=8,
                num_gpus=1 if self.config.enable_gpu_acceleration else 0,
                memory=16000000000,
                specialization=[TaskType.MODEL_TRAINING, TaskType.BATCH_INFERENCE]
            )
        ]


# Example usage and factory functions
async def create_distributed_system(
    cluster_name: str = "pattern-mining-cluster",
    num_workers: int = 4,
    enable_gpu: bool = True,
    enable_scaling: bool = True
) -> DistributedPatternMiningSystem:
    """
    Create and initialize a distributed pattern mining system.
    
    Args:
        cluster_name: Name of the cluster
        num_workers: Number of workers
        enable_gpu: Enable GPU acceleration
        enable_scaling: Enable auto-scaling
        
    Returns:
        Initialized distributed system
    """
    config = DistributedSystemConfig(
        cluster_name=cluster_name,
        num_workers=num_workers,
        enable_gpu_acceleration=enable_gpu,
        enable_auto_scaling=enable_scaling
    )
    
    system = DistributedPatternMiningSystem(config)
    
    if await system.initialize():
        return system
    else:
        raise RuntimeError("Failed to initialize distributed system")


async def create_development_system() -> DistributedPatternMiningSystem:
    """
    Create a development-oriented distributed system.
    
    Returns:
        Development distributed system
    """
    config = DistributedSystemConfig(
        cluster_name="development-cluster",
        num_workers=2,
        enable_gpu_acceleration=False,
        enable_auto_scaling=False,
        scaling_config=ScalingConfig(
            min_workers=1,
            max_workers=4,
            enable_cost_optimization=False
        )
    )
    
    system = DistributedPatternMiningSystem(config)
    
    if await system.initialize():
        return system
    else:
        raise RuntimeError("Failed to initialize development system")


async def create_production_system() -> DistributedPatternMiningSystem:
    """
    Create a production-ready distributed system.
    
    Returns:
        Production distributed system
    """
    config = DistributedSystemConfig(
        cluster_name="production-cluster",
        num_workers=10,
        enable_gpu_acceleration=True,
        enable_auto_scaling=True,
        scaling_config=ScalingConfig(
            min_workers=5,
            max_workers=50,
            enable_cost_optimization=True,
            enable_gpu_scaling=True,
            enable_preemptible_instances=True
        )
    )
    
    system = DistributedPatternMiningSystem(config)
    
    if await system.initialize():
        return system
    else:
        raise RuntimeError("Failed to initialize production system")


# Context manager for automatic cleanup
class DistributedSystemContext:
    """Context manager for distributed system lifecycle."""
    
    def __init__(self, system: DistributedPatternMiningSystem):
        self.system = system
    
    async def __aenter__(self):
        if not self.system.is_initialized:
            await self.system.initialize()
        return self.system
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.system.shutdown()


# Usage example
async def example_usage():
    """Example of using the distributed pattern mining system."""
    # Create and initialize system
    system = await create_distributed_system(
        cluster_name="example-cluster",
        num_workers=4,
        enable_gpu=True,
        enable_scaling=True
    )
    
    try:
        # Submit pattern detection task
        task_id = await system.submit_pattern_detection_task(
            code="def example_function():\n    return 'hello world'",
            priority=TaskPriority.HIGH
        )
        
        # Get task result
        result = await system.get_task_result(task_id, timeout=60)
        print(f"Pattern detection result: {result}")
        
        # Train a model (example)
        from torch import nn
        
        class SimpleModel(nn.Module):
            def __init__(self):
                super().__init__()
                self.linear = nn.Linear(10, 2)
            
            def forward(self, x):
                return self.linear(x)
        
        training_config = DistributedTrainingConfig(
            model_name="example_model",
            dataset_path="/path/to/dataset.parquet",
            output_path="/path/to/output",
            num_epochs=5,
            num_workers=2
        )
        
        # Note: This would require actual dataset
        # training_result = await system.train_pattern_model(
        #     SimpleModel,
        #     training_config
        # )
        
        # Get system status
        status = await system.get_system_status()
        print(f"System status: {status}")
        
        # Perform health check
        health = await system.health_check()
        print(f"System health: {health}")
        
    finally:
        # Shutdown system
        await system.shutdown()


if __name__ == "__main__":
    asyncio.run(example_usage())