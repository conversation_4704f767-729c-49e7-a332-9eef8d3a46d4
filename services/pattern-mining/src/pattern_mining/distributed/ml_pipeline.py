"""
Distributed ML pipeline using <PERSON> for scalable pattern mining.

This module provides distributed machine learning capabilities including:
- Distributed model training with <PERSON> Train
- Distributed inference processing
- Model serving and deployment with Ray Serve
- Hyperparameter tuning with <PERSON> Tune
- Model parallel and data parallel training
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
import pandas as pd
import ray
from ray import tune
from ray.train import Trainer, TrainingResult
from ray.train.torch import TorchTrainer
from ray.train.sklearn import SklearnTrainer
from ray.serve import Deployment, deployment
from ray.serve.handle import DeploymentHandle
from ray.tune.search.hyperopt import HyperOptSearch
from ray.tune.schedulers import ASHAScheduler
from ray.data import Dataset
from ray.air import session, RunConfig, ScalingConfig
from ray.air.integrations.wandb import WandbLoggerCallback
import torch
import torch.nn as nn
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel
from transformers import AutoModel, AutoTokenizer
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

from ..ml.components.base_model import BaseModel
from ..ml.components.transformer_models import TransformerPatternModel
from ..models.ml import TrainingConfig, InferenceConfig, ModelMetrics

logger = logging.getLogger(__name__)


class PipelineStatus(Enum):
    """ML pipeline status enumeration."""
    IDLE = "idle"
    TRAINING = "training"
    TUNING = "tuning"
    SERVING = "serving"
    EVALUATING = "evaluating"
    FAILED = "failed"


@dataclass
class DistributedTrainingConfig:
    """Configuration for distributed training."""
    model_name: str
    dataset_path: str
    output_path: str
    batch_size: int = 32
    learning_rate: float = 1e-4
    num_epochs: int = 10
    num_workers: int = 2
    use_gpu: bool = True
    backend: str = "nccl"  # nccl for GPU, gloo for CPU
    mixed_precision: bool = True
    gradient_clipping: float = 1.0
    save_checkpoints: bool = True
    checkpoint_freq: int = 5
    validation_split: float = 0.2
    early_stopping_patience: int = 3
    
    # Distributed specific settings
    data_parallel: bool = True
    model_parallel: bool = False
    pipeline_parallel: bool = False
    num_gpus_per_worker: int = 1
    num_cpus_per_worker: int = 2
    memory_per_worker: int = 8000000000  # 8GB


@dataclass
class HyperparameterConfig:
    """Configuration for hyperparameter tuning."""
    search_space: Dict[str, Any]
    num_samples: int = 20
    max_concurrent_trials: int = 4
    scheduler: str = "asha"  # asha, hyperband, pbt
    search_algorithm: str = "hyperopt"  # hyperopt, optuna, bayesopt
    metric: str = "accuracy"
    mode: str = "max"
    grace_period: int = 2
    reduction_factor: int = 2
    max_t: int = 100


@dataclass
class ServingConfig:
    """Configuration for model serving."""
    model_name: str
    model_path: str
    num_replicas: int = 2
    max_concurrent_queries: int = 100
    batch_size: int = 32
    max_batch_wait_time: float = 0.1
    health_check_period: int = 30
    deployment_config: Dict[str, Any] = field(default_factory=dict)


class DistributedMLPipeline:
    """
    Distributed ML pipeline for pattern mining operations.
    
    Provides comprehensive distributed ML capabilities including training,
    inference, serving, and hyperparameter tuning using Ray.
    """
    
    def __init__(self, cluster_manager=None):
        self.cluster_manager = cluster_manager
        self.status = PipelineStatus.IDLE
        self.current_training_job = None
        self.current_tuning_job = None
        self.deployed_models: Dict[str, DeploymentHandle] = {}
        self.metrics_history: List[Dict[str, Any]] = []
        self.model_registry: Dict[str, str] = {}
        
        logger.info("Initialized distributed ML pipeline")
    
    async def train_model_distributed(
        self,
        config: DistributedTrainingConfig,
        model_class: type,
        dataset: Optional[Dataset] = None
    ) -> TrainingResult:
        """
        Train model using distributed training.
        
        Args:
            config: Training configuration
            model_class: Model class to train
            dataset: Optional Ray dataset
            
        Returns:
            TrainingResult with metrics and model
        """
        try:
            logger.info(f"Starting distributed training for {config.model_name}")
            self.status = PipelineStatus.TRAINING
            
            # Prepare dataset
            if dataset is None:
                dataset = await self._load_dataset(config.dataset_path)
            
            # Split dataset
            train_dataset, val_dataset = await self._split_dataset(
                dataset, config.validation_split
            )
            
            # Configure trainer
            trainer = await self._create_trainer(config, model_class)
            
            # Configure scaling
            scaling_config = ScalingConfig(
                num_workers=config.num_workers,
                use_gpu=config.use_gpu,
                resources_per_worker={
                    "CPU": config.num_cpus_per_worker,
                    "GPU": config.num_gpus_per_worker if config.use_gpu else 0
                }
            )
            
            # Configure run
            run_config = RunConfig(
                name=f"train_{config.model_name}_{int(time.time())}",
                storage_path=config.output_path,
                checkpoint_config=ray.train.CheckpointConfig(
                    num_to_keep=3,
                    checkpoint_score_attribute="accuracy"
                ),
                callbacks=[
                    WandbLoggerCallback(
                        project="pattern-mining",
                        name=config.model_name
                    )
                ] if self._should_use_wandb() else []
            )
            
            # Start training
            trainer_instance = trainer(
                train_loop_per_worker=self._get_train_loop(config),
                train_loop_config={
                    "model_class": model_class,
                    "config": config,
                    "train_dataset": train_dataset,
                    "val_dataset": val_dataset
                },
                scaling_config=scaling_config,
                run_config=run_config
            )
            
            # Store training job reference
            self.current_training_job = trainer_instance
            
            # Execute training
            result = trainer_instance.fit()
            
            # Store model in registry
            self.model_registry[config.model_name] = result.checkpoint.path
            
            # Update metrics
            await self._update_training_metrics(result)
            
            self.status = PipelineStatus.IDLE
            logger.info(f"Distributed training completed for {config.model_name}")
            
            return result
            
        except Exception as e:
            logger.error(f"Distributed training failed: {e}")
            self.status = PipelineStatus.FAILED
            raise
    
    async def tune_hyperparameters(
        self,
        config: HyperparameterConfig,
        training_config: DistributedTrainingConfig,
        model_class: type
    ) -> tune.ResultGrid:
        """
        Perform distributed hyperparameter tuning.
        
        Args:
            config: Hyperparameter tuning configuration
            training_config: Base training configuration
            model_class: Model class to tune
            
        Returns:
            Tuning results
        """
        try:
            logger.info(f"Starting hyperparameter tuning for {training_config.model_name}")
            self.status = PipelineStatus.TUNING
            
            # Configure search algorithm
            search_alg = self._get_search_algorithm(config)
            
            # Configure scheduler
            scheduler = self._get_scheduler(config)
            
            # Configure tuner
            tuner = tune.Tuner(
                trainable=self._get_trainable_function(training_config, model_class),
                param_space=config.search_space,
                tune_config=tune.TuneConfig(
                    search_alg=search_alg,
                    scheduler=scheduler,
                    num_samples=config.num_samples,
                    max_concurrent_trials=config.max_concurrent_trials,
                    metric=config.metric,
                    mode=config.mode
                ),
                run_config=RunConfig(
                    name=f"tune_{training_config.model_name}_{int(time.time())}",
                    storage_path=training_config.output_path,
                    checkpoint_config=ray.train.CheckpointConfig(
                        num_to_keep=1,
                        checkpoint_score_attribute=config.metric
                    )
                )
            )
            
            # Store tuning job reference
            self.current_tuning_job = tuner
            
            # Execute tuning
            result_grid = tuner.fit()
            
            # Get best configuration
            best_result = result_grid.get_best_result(
                metric=config.metric,
                mode=config.mode
            )
            
            # Update metrics
            await self._update_tuning_metrics(result_grid, best_result)
            
            self.status = PipelineStatus.IDLE
            logger.info(f"Hyperparameter tuning completed for {training_config.model_name}")
            
            return result_grid
            
        except Exception as e:
            logger.error(f"Hyperparameter tuning failed: {e}")
            self.status = PipelineStatus.FAILED
            raise
    
    async def serve_model(
        self,
        config: ServingConfig
    ) -> DeploymentHandle:
        """
        Deploy model for serving with Ray Serve.
        
        Args:
            config: Serving configuration
            
        Returns:
            Deployment handle
        """
        try:
            logger.info(f"Deploying model for serving: {config.model_name}")
            self.status = PipelineStatus.SERVING
            
            # Create deployment
            deployment_func = self._create_deployment_function(config)
            
            # Configure deployment
            deployment_config = {
                "num_replicas": config.num_replicas,
                "max_concurrent_queries": config.max_concurrent_queries,
                "health_check_period_s": config.health_check_period,
                **config.deployment_config
            }
            
            # Deploy model
            deployment_instance = deployment(**deployment_config)(deployment_func)
            handle = deployment_instance.deploy()
            
            # Store deployment handle
            self.deployed_models[config.model_name] = handle
            
            # Wait for deployment to be ready
            await self._wait_for_deployment_ready(handle)
            
            self.status = PipelineStatus.IDLE
            logger.info(f"Model serving deployed: {config.model_name}")
            
            return handle
            
        except Exception as e:
            logger.error(f"Model serving deployment failed: {e}")
            self.status = PipelineStatus.FAILED
            raise
    
    async def batch_inference(
        self,
        model_name: str,
        dataset: Dataset,
        batch_size: int = 32
    ) -> Dataset:
        """
        Perform distributed batch inference.
        
        Args:
            model_name: Name of deployed model
            dataset: Input dataset
            batch_size: Batch size for inference
            
        Returns:
            Dataset with predictions
        """
        try:
            logger.info(f"Starting batch inference with {model_name}")
            
            # Get model handle
            if model_name not in self.deployed_models:
                raise ValueError(f"Model {model_name} not deployed")
            
            model_handle = self.deployed_models[model_name]
            
            # Create inference function
            def inference_batch(batch):
                """Inference function for batch processing."""
                predictions = []
                
                for item in batch:
                    # Call model asynchronously
                    result = ray.get(model_handle.predict.remote(item))
                    predictions.append(result)
                
                return {"predictions": predictions}
            
            # Apply inference to dataset
            result_dataset = dataset.map_batches(
                inference_batch,
                batch_size=batch_size,
                compute=ray.data.ActorPoolStrategy(size=4)
            )
            
            logger.info(f"Batch inference completed for {model_name}")
            return result_dataset
            
        except Exception as e:
            logger.error(f"Batch inference failed: {e}")
            raise
    
    async def evaluate_model(
        self,
        model_name: str,
        test_dataset: Dataset,
        metrics: List[str] = None
    ) -> Dict[str, float]:
        """
        Evaluate model performance on test dataset.
        
        Args:
            model_name: Name of model to evaluate
            test_dataset: Test dataset
            metrics: List of metrics to compute
            
        Returns:
            Dictionary of computed metrics
        """
        try:
            logger.info(f"Evaluating model: {model_name}")
            self.status = PipelineStatus.EVALUATING
            
            if metrics is None:
                metrics = ["accuracy", "precision", "recall", "f1"]
            
            # Perform inference on test dataset
            predictions_dataset = await self.batch_inference(
                model_name, test_dataset
            )
            
            # Collect results
            results = predictions_dataset.take_all()
            
            # Extract predictions and ground truth
            y_true = []
            y_pred = []
            
            for result in results:
                y_true.append(result["label"])
                y_pred.append(result["prediction"])
            
            # Compute metrics
            computed_metrics = {}
            
            if "accuracy" in metrics:
                computed_metrics["accuracy"] = accuracy_score(y_true, y_pred)
            
            if "precision" in metrics:
                computed_metrics["precision"] = precision_score(
                    y_true, y_pred, average="weighted"
                )
            
            if "recall" in metrics:
                computed_metrics["recall"] = recall_score(
                    y_true, y_pred, average="weighted"
                )
            
            if "f1" in metrics:
                computed_metrics["f1"] = f1_score(
                    y_true, y_pred, average="weighted"
                )
            
            # Store evaluation metrics
            await self._store_evaluation_metrics(model_name, computed_metrics)
            
            self.status = PipelineStatus.IDLE
            logger.info(f"Model evaluation completed: {model_name}")
            
            return computed_metrics
            
        except Exception as e:
            logger.error(f"Model evaluation failed: {e}")
            self.status = PipelineStatus.FAILED
            raise
    
    async def get_pipeline_status(self) -> Dict[str, Any]:
        """
        Get comprehensive pipeline status.
        
        Returns:
            Pipeline status information
        """
        return {
            "status": self.status.value,
            "training_job": {
                "active": self.current_training_job is not None,
                "job_id": getattr(self.current_training_job, "job_id", None)
            },
            "tuning_job": {
                "active": self.current_tuning_job is not None,
                "job_id": getattr(self.current_tuning_job, "job_id", None)
            },
            "deployed_models": list(self.deployed_models.keys()),
            "model_registry": self.model_registry,
            "metrics_history": self.metrics_history[-10:]  # Last 10 entries
        }
    
    async def stop_training(self) -> bool:
        """
        Stop current training job.
        
        Returns:
            True if training stopped successfully
        """
        try:
            if self.current_training_job:
                # Stop training job
                # Note: Implementation depends on Ray Train API
                logger.info("Stopping training job")
                self.current_training_job = None
                self.status = PipelineStatus.IDLE
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to stop training: {e}")
            return False
    
    async def undeploy_model(self, model_name: str) -> bool:
        """
        Undeploy serving model.
        
        Args:
            model_name: Name of model to undeploy
            
        Returns:
            True if undeployment successful
        """
        try:
            if model_name in self.deployed_models:
                handle = self.deployed_models[model_name]
                # Undeploy model
                # Note: Implementation depends on Ray Serve API
                del self.deployed_models[model_name]
                logger.info(f"Undeployed model: {model_name}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to undeploy model: {e}")
            return False
    
    async def _load_dataset(self, dataset_path: str) -> Dataset:
        """Load dataset using Ray Data."""
        try:
            if dataset_path.endswith('.parquet'):
                return ray.data.read_parquet(dataset_path)
            elif dataset_path.endswith('.csv'):
                return ray.data.read_csv(dataset_path)
            elif dataset_path.endswith('.json'):
                return ray.data.read_json(dataset_path)
            else:
                raise ValueError(f"Unsupported dataset format: {dataset_path}")
                
        except Exception as e:
            logger.error(f"Failed to load dataset: {e}")
            raise
    
    async def _split_dataset(
        self, 
        dataset: Dataset, 
        validation_split: float
    ) -> Tuple[Dataset, Dataset]:
        """Split dataset into train and validation sets."""
        try:
            # Shuffle dataset
            dataset = dataset.random_shuffle()
            
            # Split dataset
            train_dataset, val_dataset = dataset.train_test_split(
                test_size=validation_split
            )
            
            return train_dataset, val_dataset
            
        except Exception as e:
            logger.error(f"Failed to split dataset: {e}")
            raise
    
    async def _create_trainer(
        self, 
        config: DistributedTrainingConfig, 
        model_class: type
    ) -> type:
        """Create appropriate trainer based on model type."""
        try:
            if issubclass(model_class, nn.Module):
                return TorchTrainer
            elif hasattr(model_class, 'fit'):  # sklearn-like
                return SklearnTrainer
            else:
                raise ValueError(f"Unsupported model type: {model_class}")
                
        except Exception as e:
            logger.error(f"Failed to create trainer: {e}")
            raise
    
    def _get_train_loop(self, config: DistributedTrainingConfig) -> Callable:
        """Get training loop function."""
        def train_loop_per_worker(loop_config):
            """Training loop executed on each worker."""
            # Get configuration
            model_class = loop_config["model_class"]
            training_config = loop_config["config"]
            train_dataset = loop_config["train_dataset"]
            val_dataset = loop_config["val_dataset"]
            
            # Initialize distributed training
            if training_config.use_gpu:
                device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
                if torch.cuda.is_available() and dist.is_initialized():
                    torch.cuda.set_device(session.get_local_rank())
            else:
                device = torch.device("cpu")
            
            # Create model
            model = model_class()
            model.to(device)
            
            # Wrap model for distributed training
            if training_config.data_parallel and dist.is_initialized():
                model = DistributedDataParallel(model)
            
            # Setup optimizer
            optimizer = torch.optim.AdamW(
                model.parameters(),
                lr=training_config.learning_rate
            )
            
            # Setup loss function
            criterion = nn.CrossEntropyLoss()
            
            # Training loop
            for epoch in range(training_config.num_epochs):
                # Training phase
                model.train()
                train_loss = 0.0
                train_correct = 0
                train_total = 0
                
                for batch in train_dataset.iter_batches(
                    batch_size=training_config.batch_size
                ):
                    optimizer.zero_grad()
                    
                    # Forward pass
                    outputs = model(batch["input"])
                    loss = criterion(outputs, batch["label"])
                    
                    # Backward pass
                    loss.backward()
                    
                    # Gradient clipping
                    if training_config.gradient_clipping > 0:
                        torch.nn.utils.clip_grad_norm_(
                            model.parameters(), 
                            training_config.gradient_clipping
                        )
                    
                    optimizer.step()
                    
                    # Statistics
                    train_loss += loss.item()
                    _, predicted = torch.max(outputs.data, 1)
                    train_total += batch["label"].size(0)
                    train_correct += (predicted == batch["label"]).sum().item()
                
                # Validation phase
                model.eval()
                val_loss = 0.0
                val_correct = 0
                val_total = 0
                
                with torch.no_grad():
                    for batch in val_dataset.iter_batches(
                        batch_size=training_config.batch_size
                    ):
                        outputs = model(batch["input"])
                        loss = criterion(outputs, batch["label"])
                        
                        val_loss += loss.item()
                        _, predicted = torch.max(outputs.data, 1)
                        val_total += batch["label"].size(0)
                        val_correct += (predicted == batch["label"]).sum().item()
                
                # Calculate metrics
                train_acc = train_correct / train_total
                val_acc = val_correct / val_total
                
                # Report metrics
                session.report({
                    "epoch": epoch,
                    "train_loss": train_loss,
                    "train_accuracy": train_acc,
                    "val_loss": val_loss,
                    "val_accuracy": val_acc,
                    "accuracy": val_acc  # Primary metric
                })
                
                # Save checkpoint
                if training_config.save_checkpoints and epoch % training_config.checkpoint_freq == 0:
                    session.report({}, checkpoint=ray.train.Checkpoint.from_dict({
                        "model_state_dict": model.state_dict(),
                        "optimizer_state_dict": optimizer.state_dict(),
                        "epoch": epoch
                    }))
        
        return train_loop_per_worker
    
    def _get_search_algorithm(self, config: HyperparameterConfig):
        """Get hyperparameter search algorithm."""
        if config.search_algorithm == "hyperopt":
            return HyperOptSearch()
        else:
            return None  # Use default
    
    def _get_scheduler(self, config: HyperparameterConfig):
        """Get hyperparameter scheduler."""
        if config.scheduler == "asha":
            return ASHAScheduler(
                max_t=config.max_t,
                grace_period=config.grace_period,
                reduction_factor=config.reduction_factor
            )
        else:
            return None  # Use default
    
    def _get_trainable_function(
        self, 
        training_config: DistributedTrainingConfig, 
        model_class: type
    ) -> Callable:
        """Get trainable function for hyperparameter tuning."""
        def trainable(tune_config):
            """Trainable function for hyperparameter tuning."""
            # Update training config with tuned parameters
            updated_config = training_config
            for key, value in tune_config.items():
                if hasattr(updated_config, key):
                    setattr(updated_config, key, value)
            
            # Create and run trainer
            trainer = self._create_trainer(updated_config, model_class)
            
            # Training loop (simplified for tuning)
            # Implementation would be similar to train_loop_per_worker
            # but with tune.report() instead of session.report()
            
            pass
        
        return trainable
    
    def _create_deployment_function(self, config: ServingConfig) -> Callable:
        """Create deployment function for model serving."""
        
        @deployment(
            name=config.model_name,
            num_replicas=config.num_replicas,
            ray_actor_options={"num_cpus": 2, "num_gpus": 0.5}
        )
        class ModelDeployment:
            def __init__(self):
                # Load model from registry
                model_path = config.model_path
                self.model = self._load_model(model_path)
                self.tokenizer = None
                
                # Initialize tokenizer if needed
                if hasattr(self.model, 'tokenizer_name'):
                    self.tokenizer = AutoTokenizer.from_pretrained(
                        self.model.tokenizer_name
                    )
            
            def _load_model(self, model_path: str):
                """Load model from checkpoint."""
                # Implementation depends on model type
                # For now, return placeholder
                return None
            
            async def predict(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
                """Perform inference on input data."""
                try:
                    # Preprocess input
                    if self.tokenizer:
                        inputs = self.tokenizer(
                            input_data["text"],
                            return_tensors="pt",
                            padding=True,
                            truncation=True
                        )
                    else:
                        inputs = input_data
                    
                    # Run inference
                    with torch.no_grad():
                        outputs = self.model(inputs)
                    
                    # Postprocess output
                    prediction = torch.argmax(outputs.logits, dim=-1)
                    
                    return {
                        "prediction": prediction.item(),
                        "confidence": torch.max(torch.softmax(outputs.logits, dim=-1)).item()
                    }
                    
                except Exception as e:
                    logger.error(f"Inference failed: {e}")
                    return {"error": str(e)}
            
            async def health_check(self) -> Dict[str, Any]:
                """Health check endpoint."""
                return {"status": "healthy", "model": config.model_name}
        
        return ModelDeployment
    
    async def _wait_for_deployment_ready(self, handle: DeploymentHandle, timeout: int = 300):
        """Wait for deployment to be ready."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # Test health check
                result = await handle.health_check.remote()
                if result.get("status") == "healthy":
                    return True
                
                await asyncio.sleep(5)
                
            except Exception as e:
                logger.debug(f"Waiting for deployment ready: {e}")
                await asyncio.sleep(5)
        
        return False
    
    def _should_use_wandb(self) -> bool:
        """Check if Weights & Biases should be used."""
        return os.getenv("WANDB_API_KEY") is not None
    
    async def _update_training_metrics(self, result: TrainingResult):
        """Update training metrics history."""
        try:
            metrics = {
                "type": "training",
                "timestamp": time.time(),
                "metrics": result.metrics,
                "checkpoint": result.checkpoint.path if result.checkpoint else None
            }
            
            self.metrics_history.append(metrics)
            
            # Keep only last 100 entries
            if len(self.metrics_history) > 100:
                self.metrics_history = self.metrics_history[-100:]
                
        except Exception as e:
            logger.error(f"Failed to update training metrics: {e}")
    
    async def _update_tuning_metrics(self, result_grid: tune.ResultGrid, best_result):
        """Update tuning metrics history."""
        try:
            metrics = {
                "type": "tuning",
                "timestamp": time.time(),
                "num_trials": len(result_grid),
                "best_result": best_result.metrics if best_result else None,
                "best_config": best_result.config if best_result else None
            }
            
            self.metrics_history.append(metrics)
            
        except Exception as e:
            logger.error(f"Failed to update tuning metrics: {e}")
    
    async def _store_evaluation_metrics(self, model_name: str, metrics: Dict[str, float]):
        """Store evaluation metrics."""
        try:
            evaluation_metrics = {
                "type": "evaluation",
                "model_name": model_name,
                "timestamp": time.time(),
                "metrics": metrics
            }
            
            self.metrics_history.append(evaluation_metrics)
            
        except Exception as e:
            logger.error(f"Failed to store evaluation metrics: {e}")


# Example usage functions
async def create_training_config() -> DistributedTrainingConfig:
    """Create example training configuration."""
    return DistributedTrainingConfig(
        model_name="pattern_classifier",
        dataset_path="/data/patterns.parquet",
        output_path="/models/pattern_classifier",
        batch_size=32,
        learning_rate=1e-4,
        num_epochs=10,
        num_workers=4,
        use_gpu=True,
        mixed_precision=True
    )


async def create_hyperparameter_config() -> HyperparameterConfig:
    """Create example hyperparameter configuration."""
    return HyperparameterConfig(
        search_space={
            "learning_rate": tune.loguniform(1e-6, 1e-3),
            "batch_size": tune.choice([16, 32, 64, 128]),
            "num_layers": tune.choice([2, 4, 6, 8]),
            "hidden_size": tune.choice([128, 256, 512, 1024])
        },
        num_samples=20,
        max_concurrent_trials=4,
        metric="accuracy",
        mode="max"
    )


async def create_serving_config() -> ServingConfig:
    """Create example serving configuration."""
    return ServingConfig(
        model_name="pattern_classifier",
        model_path="/models/pattern_classifier/best_model.pt",
        num_replicas=2,
        max_concurrent_queries=100,
        batch_size=32
    )