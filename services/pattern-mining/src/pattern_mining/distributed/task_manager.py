"""
Distributed task management and scheduling for pattern mining.

This module provides comprehensive task distribution capabilities including:
- Distributed task scheduling and execution
- Pattern detection task distribution
- Repository analysis parallelization
- Feature extraction scaling
- Result aggregation and management
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, List, Optional, Any, Tuple, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import pickle
import json
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
import ray
from ray.util.queue import Queue
from ray.util.placement_group import PlacementGroup
import numpy as np
import pandas as pd

from ..models.patterns import Pattern, PatternMatch
from ..models.api import AnalysisRequest, AnalysisResult
from ..features.extractor import FeatureExtractor
from ..detectors.manager import DetectorManager

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """Task status enumeration."""
    PENDING = "pending"
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRY = "retry"


class TaskType(Enum):
    """Task type enumeration."""
    PATTERN_DETECTION = "pattern_detection"
    FEATURE_EXTRACTION = "feature_extraction"
    REPOSITORY_ANALYSIS = "repository_analysis"
    MODEL_TRAINING = "model_training"
    BATCH_INFERENCE = "batch_inference"
    DATA_PROCESSING = "data_processing"


class TaskPriority(Enum):
    """Task priority enumeration."""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class TaskConfig:
    """Configuration for distributed tasks."""
    task_id: str
    task_type: TaskType
    priority: TaskPriority = TaskPriority.MEDIUM
    max_retries: int = 3
    timeout: int = 3600  # 1 hour
    resources: Dict[str, float] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    callback_url: Optional[str] = None
    placement_group: Optional[PlacementGroup] = None


@dataclass
class TaskResult:
    """Task execution result."""
    task_id: str
    status: TaskStatus
    result: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    resource_usage: Dict[str, float] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    worker_id: Optional[str] = None
    timestamp: float = field(default_factory=time.time)


@dataclass
class WorkerConfig:
    """Configuration for task workers."""
    worker_id: str
    worker_type: str
    num_cpus: int = 2
    num_gpus: int = 0
    memory: int = 4000000000  # 4GB
    max_concurrent_tasks: int = 4
    specialization: List[TaskType] = field(default_factory=list)
    placement_group: Optional[PlacementGroup] = None


class DistributedTaskManager:
    """
    Distributed task manager for pattern mining operations.
    
    Provides comprehensive task distribution including scheduling,
    execution, monitoring, and result aggregation.
    """
    
    def __init__(self, cluster_manager=None):
        self.cluster_manager = cluster_manager
        self.task_queue = Queue(maxsize=1000)
        self.result_queue = Queue(maxsize=1000)
        self.workers: Dict[str, ray.ObjectRef] = {}
        self.tasks: Dict[str, TaskConfig] = {}
        self.results: Dict[str, TaskResult] = {}
        self.task_dependencies: Dict[str, List[str]] = {}
        self.worker_configs: Dict[str, WorkerConfig] = {}
        self.executor = ThreadPoolExecutor(max_workers=10)
        self.monitoring_active = False
        self.metrics: Dict[str, Any] = {}
        
        # Initialize task scheduling
        self.scheduler = TaskScheduler(self)
        
        logger.info("Initialized distributed task manager")
    
    async def start_workers(self, worker_configs: List[WorkerConfig]) -> bool:
        """
        Start distributed workers for task execution.
        
        Args:
            worker_configs: List of worker configurations
            
        Returns:
            bool: True if workers started successfully
        """
        try:
            logger.info(f"Starting {len(worker_configs)} workers")
            
            for config in worker_configs:
                # Create worker actor
                worker_ref = await self._create_worker(config)
                
                # Store worker reference
                self.workers[config.worker_id] = worker_ref
                self.worker_configs[config.worker_id] = config
            
            # Start monitoring
            await self._start_monitoring()
            
            logger.info(f"Started {len(self.workers)} workers")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start workers: {e}")
            return False
    
    async def submit_task(
        self,
        task_type: TaskType,
        task_data: Any,
        priority: TaskPriority = TaskPriority.MEDIUM,
        dependencies: List[str] = None,
        resources: Dict[str, float] = None,
        metadata: Dict[str, Any] = None
    ) -> str:
        """
        Submit task for distributed execution.
        
        Args:
            task_type: Type of task
            task_data: Task input data
            priority: Task priority
            dependencies: Task dependencies
            resources: Required resources
            metadata: Task metadata
            
        Returns:
            Task ID
        """
        try:
            # Generate task ID
            task_id = str(uuid.uuid4())
            
            # Create task configuration
            task_config = TaskConfig(
                task_id=task_id,
                task_type=task_type,
                priority=priority,
                dependencies=dependencies or [],
                resources=resources or {},
                metadata=metadata or {}
            )
            
            # Store task configuration
            self.tasks[task_id] = task_config
            
            # Add to dependency tracking
            if dependencies:
                self.task_dependencies[task_id] = dependencies
            
            # Submit task to scheduler
            await self.scheduler.schedule_task(task_id, task_data)
            
            logger.info(f"Submitted task {task_id} of type {task_type.value}")
            return task_id
            
        except Exception as e:
            logger.error(f"Failed to submit task: {e}")
            raise
    
    async def submit_batch_tasks(
        self,
        task_type: TaskType,
        task_data_list: List[Any],
        priority: TaskPriority = TaskPriority.MEDIUM,
        batch_size: int = 10
    ) -> List[str]:
        """
        Submit batch of tasks for distributed execution.
        
        Args:
            task_type: Type of tasks
            task_data_list: List of task input data
            priority: Task priority
            batch_size: Batch size for processing
            
        Returns:
            List of task IDs
        """
        try:
            task_ids = []
            
            # Submit tasks in batches
            for i in range(0, len(task_data_list), batch_size):
                batch = task_data_list[i:i + batch_size]
                
                for task_data in batch:
                    task_id = await self.submit_task(
                        task_type=task_type,
                        task_data=task_data,
                        priority=priority,
                        metadata={"batch_index": i // batch_size}
                    )
                    task_ids.append(task_id)
                
                # Small delay between batches
                await asyncio.sleep(0.1)
            
            logger.info(f"Submitted {len(task_ids)} batch tasks")
            return task_ids
            
        except Exception as e:
            logger.error(f"Failed to submit batch tasks: {e}")
            raise
    
    async def get_task_result(
        self,
        task_id: str,
        timeout: int = None
    ) -> TaskResult:
        """
        Get task execution result.
        
        Args:
            task_id: Task identifier
            timeout: Timeout in seconds
            
        Returns:
            Task result
        """
        try:
            # Check if result is already available
            if task_id in self.results:
                return self.results[task_id]
            
            # Wait for result
            start_time = time.time()
            
            while True:
                if task_id in self.results:
                    return self.results[task_id]
                
                if timeout and (time.time() - start_time) > timeout:
                    raise TimeoutError(f"Task {task_id} timed out")
                
                await asyncio.sleep(0.1)
                
        except Exception as e:
            logger.error(f"Failed to get task result: {e}")
            raise
    
    async def get_batch_results(
        self,
        task_ids: List[str],
        timeout: int = None
    ) -> List[TaskResult]:
        """
        Get batch task execution results.
        
        Args:
            task_ids: List of task identifiers
            timeout: Timeout in seconds
            
        Returns:
            List of task results
        """
        try:
            results = []
            
            # Get results concurrently
            tasks = [
                self.get_task_result(task_id, timeout)
                for task_id in task_ids
            ]
            
            completed_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in completed_results:
                if isinstance(result, Exception):
                    logger.error(f"Task failed: {result}")
                    results.append(TaskResult(
                        task_id="unknown",
                        status=TaskStatus.FAILED,
                        error=str(result)
                    ))
                else:
                    results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to get batch results: {e}")
            raise
    
    async def cancel_task(self, task_id: str) -> bool:
        """
        Cancel task execution.
        
        Args:
            task_id: Task identifier
            
        Returns:
            bool: True if cancellation successful
        """
        try:
            if task_id in self.tasks:
                # Update task status
                result = TaskResult(
                    task_id=task_id,
                    status=TaskStatus.CANCELLED
                )
                self.results[task_id] = result
                
                # Remove from tasks
                del self.tasks[task_id]
                
                logger.info(f"Cancelled task {task_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to cancel task: {e}")
            return False
    
    async def get_task_status(self, task_id: str) -> TaskStatus:
        """
        Get task execution status.
        
        Args:
            task_id: Task identifier
            
        Returns:
            Task status
        """
        try:
            if task_id in self.results:
                return self.results[task_id].status
            elif task_id in self.tasks:
                return TaskStatus.PENDING
            else:
                return TaskStatus.FAILED
                
        except Exception as e:
            logger.error(f"Failed to get task status: {e}")
            return TaskStatus.FAILED
    
    async def get_manager_status(self) -> Dict[str, Any]:
        """
        Get comprehensive task manager status.
        
        Returns:
            Manager status information
        """
        try:
            return {
                "workers": {
                    "total": len(self.workers),
                    "active": len([w for w in self.workers.values() if w is not None]),
                    "configs": list(self.worker_configs.keys())
                },
                "tasks": {
                    "total": len(self.tasks),
                    "pending": len([t for t in self.tasks.values() if t.task_id not in self.results]),
                    "completed": len([r for r in self.results.values() if r.status == TaskStatus.COMPLETED]),
                    "failed": len([r for r in self.results.values() if r.status == TaskStatus.FAILED])
                },
                "queues": {
                    "task_queue_size": self.task_queue.qsize() if hasattr(self.task_queue, 'qsize') else 0,
                    "result_queue_size": self.result_queue.qsize() if hasattr(self.result_queue, 'qsize') else 0
                },
                "metrics": self.metrics
            }
            
        except Exception as e:
            logger.error(f"Failed to get manager status: {e}")
            return {"error": str(e)}
    
    async def stop_workers(self) -> bool:
        """
        Stop all workers and cleanup resources.
        
        Returns:
            bool: True if cleanup successful
        """
        try:
            # Stop monitoring
            self.monitoring_active = False
            
            # Stop workers
            for worker_id, worker_ref in self.workers.items():
                if worker_ref:
                    ray.kill(worker_ref)
            
            # Clear workers
            self.workers.clear()
            self.worker_configs.clear()
            
            # Shutdown executor
            self.executor.shutdown(wait=True)
            
            logger.info("Stopped all workers")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop workers: {e}")
            return False
    
    async def _create_worker(self, config: WorkerConfig) -> ray.ObjectRef:
        """Create worker actor."""
        try:
            # Create worker actor
            worker_class = self._get_worker_class(config.worker_type)
            
            # Configure resources
            resources = {
                "num_cpus": config.num_cpus,
                "num_gpus": config.num_gpus,
                "memory": config.memory
            }
            
            # Create actor
            worker_ref = worker_class.options(**resources).remote(config)
            
            return worker_ref
            
        except Exception as e:
            logger.error(f"Failed to create worker: {e}")
            raise
    
    def _get_worker_class(self, worker_type: str):
        """Get worker class based on type."""
        if worker_type == "pattern_detector":
            return PatternDetectorWorker
        elif worker_type == "feature_extractor":
            return FeatureExtractorWorker
        elif worker_type == "repository_analyzer":
            return RepositoryAnalyzerWorker
        elif worker_type == "ml_trainer":
            return MLTrainerWorker
        else:
            return GenericWorker
    
    async def _start_monitoring(self):
        """Start monitoring tasks."""
        try:
            self.monitoring_active = True
            
            # Start monitoring tasks
            asyncio.create_task(self._monitor_tasks())
            asyncio.create_task(self._monitor_workers())
            asyncio.create_task(self._monitor_queues())
            
            logger.info("Started task monitoring")
            
        except Exception as e:
            logger.error(f"Failed to start monitoring: {e}")
    
    async def _monitor_tasks(self):
        """Monitor task execution."""
        while self.monitoring_active:
            try:
                # Update task metrics
                total_tasks = len(self.tasks)
                completed_tasks = len([r for r in self.results.values() if r.status == TaskStatus.COMPLETED])
                failed_tasks = len([r for r in self.results.values() if r.status == TaskStatus.FAILED])
                
                self.metrics.update({
                    "total_tasks": total_tasks,
                    "completed_tasks": completed_tasks,
                    "failed_tasks": failed_tasks,
                    "success_rate": completed_tasks / total_tasks if total_tasks > 0 else 0
                })
                
                await asyncio.sleep(30)  # Update every 30 seconds
                
            except Exception as e:
                logger.error(f"Task monitoring error: {e}")
                await asyncio.sleep(60)
    
    async def _monitor_workers(self):
        """Monitor worker status."""
        while self.monitoring_active:
            try:
                # Check worker health
                healthy_workers = 0
                
                for worker_id, worker_ref in self.workers.items():
                    try:
                        # Check if worker is alive
                        if worker_ref:
                            healthy_workers += 1
                    except Exception as e:
                        logger.warning(f"Worker {worker_id} health check failed: {e}")
                
                self.metrics.update({
                    "healthy_workers": healthy_workers,
                    "total_workers": len(self.workers)
                })
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Worker monitoring error: {e}")
                await asyncio.sleep(120)
    
    async def _monitor_queues(self):
        """Monitor queue sizes."""
        while self.monitoring_active:
            try:
                # Update queue metrics
                self.metrics.update({
                    "task_queue_size": self.task_queue.qsize() if hasattr(self.task_queue, 'qsize') else 0,
                    "result_queue_size": self.result_queue.qsize() if hasattr(self.result_queue, 'qsize') else 0
                })
                
                await asyncio.sleep(30)  # Update every 30 seconds
                
            except Exception as e:
                logger.error(f"Queue monitoring error: {e}")
                await asyncio.sleep(60)


class TaskScheduler:
    """
    Task scheduler for distributed execution.
    
    Handles task prioritization, dependency resolution,
    and worker assignment.
    """
    
    def __init__(self, task_manager: DistributedTaskManager):
        self.task_manager = task_manager
        self.pending_tasks: Dict[str, Any] = {}
        self.running_tasks: Dict[str, str] = {}  # task_id -> worker_id
        
    async def schedule_task(self, task_id: str, task_data: Any):
        """Schedule task for execution."""
        try:
            # Check dependencies
            if await self._check_dependencies(task_id):
                # Find available worker
                worker_id = await self._find_available_worker(task_id)
                
                if worker_id:
                    # Assign task to worker
                    await self._assign_task_to_worker(task_id, worker_id, task_data)
                else:
                    # Queue task
                    self.pending_tasks[task_id] = task_data
            else:
                # Queue task until dependencies are resolved
                self.pending_tasks[task_id] = task_data
                
        except Exception as e:
            logger.error(f"Failed to schedule task: {e}")
            raise
    
    async def _check_dependencies(self, task_id: str) -> bool:
        """Check if task dependencies are satisfied."""
        try:
            if task_id not in self.task_manager.task_dependencies:
                return True
            
            dependencies = self.task_manager.task_dependencies[task_id]
            
            for dep_id in dependencies:
                if dep_id not in self.task_manager.results:
                    return False
                
                if self.task_manager.results[dep_id].status != TaskStatus.COMPLETED:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to check dependencies: {e}")
            return False
    
    async def _find_available_worker(self, task_id: str) -> Optional[str]:
        """Find available worker for task."""
        try:
            task_config = self.task_manager.tasks[task_id]
            
            # Find workers that can handle this task type
            suitable_workers = []
            
            for worker_id, worker_config in self.task_manager.worker_configs.items():
                if not worker_config.specialization or task_config.task_type in worker_config.specialization:
                    suitable_workers.append(worker_id)
            
            # Find available worker
            for worker_id in suitable_workers:
                if worker_id not in self.running_tasks.values():
                    return worker_id
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to find available worker: {e}")
            return None
    
    async def _assign_task_to_worker(self, task_id: str, worker_id: str, task_data: Any):
        """Assign task to specific worker."""
        try:
            # Get worker reference
            worker_ref = self.task_manager.workers[worker_id]
            
            # Submit task to worker
            result_ref = worker_ref.execute_task.remote(task_id, task_data)
            
            # Track running task
            self.running_tasks[task_id] = worker_id
            
            # Handle result asynchronously
            asyncio.create_task(self._handle_task_result(task_id, result_ref))
            
        except Exception as e:
            logger.error(f"Failed to assign task to worker: {e}")
            raise
    
    async def _handle_task_result(self, task_id: str, result_ref: ray.ObjectRef):
        """Handle task result from worker."""
        try:
            # Get result
            result = await result_ref
            
            # Store result
            self.task_manager.results[task_id] = result
            
            # Remove from running tasks
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
            
            # Check for pending tasks that can now be scheduled
            await self._check_pending_tasks()
            
        except Exception as e:
            logger.error(f"Failed to handle task result: {e}")
            
            # Create error result
            error_result = TaskResult(
                task_id=task_id,
                status=TaskStatus.FAILED,
                error=str(e)
            )
            self.task_manager.results[task_id] = error_result
    
    async def _check_pending_tasks(self):
        """Check if any pending tasks can be scheduled."""
        try:
            tasks_to_schedule = []
            
            for task_id, task_data in self.pending_tasks.items():
                if await self._check_dependencies(task_id):
                    worker_id = await self._find_available_worker(task_id)
                    if worker_id:
                        tasks_to_schedule.append((task_id, task_data))
            
            # Schedule tasks
            for task_id, task_data in tasks_to_schedule:
                await self.schedule_task(task_id, task_data)
                del self.pending_tasks[task_id]
                
        except Exception as e:
            logger.error(f"Failed to check pending tasks: {e}")


# Worker implementations
@ray.remote
class GenericWorker:
    """Generic worker for task execution."""
    
    def __init__(self, config: WorkerConfig):
        self.config = config
        self.current_tasks: Dict[str, Any] = {}
        
    async def execute_task(self, task_id: str, task_data: Any) -> TaskResult:
        """Execute generic task."""
        try:
            start_time = time.time()
            
            # Process task data
            result = await self._process_task(task_data)
            
            execution_time = time.time() - start_time
            
            return TaskResult(
                task_id=task_id,
                status=TaskStatus.COMPLETED,
                result=result,
                execution_time=execution_time,
                worker_id=self.config.worker_id
            )
            
        except Exception as e:
            return TaskResult(
                task_id=task_id,
                status=TaskStatus.FAILED,
                error=str(e),
                worker_id=self.config.worker_id
            )
    
    async def _process_task(self, task_data: Any) -> Any:
        """Process task data."""
        # Generic processing
        return task_data


@ray.remote
class PatternDetectorWorker(GenericWorker):
    """Specialized worker for pattern detection tasks."""
    
    def __init__(self, config: WorkerConfig):
        super().__init__(config)
        self.detector_manager = DetectorManager()
    
    async def _process_task(self, task_data: Any) -> Any:
        """Process pattern detection task."""
        try:
            # Extract code and configuration
            code = task_data.get("code", "")
            detection_config = task_data.get("config", {})
            
            # Run pattern detection
            patterns = await self.detector_manager.detect_patterns(code, detection_config)
            
            return {
                "patterns": patterns,
                "metadata": {
                    "code_length": len(code),
                    "pattern_count": len(patterns)
                }
            }
            
        except Exception as e:
            logger.error(f"Pattern detection failed: {e}")
            raise


@ray.remote
class FeatureExtractorWorker(GenericWorker):
    """Specialized worker for feature extraction tasks."""
    
    def __init__(self, config: WorkerConfig):
        super().__init__(config)
        self.feature_extractor = FeatureExtractor()
    
    async def _process_task(self, task_data: Any) -> Any:
        """Process feature extraction task."""
        try:
            # Extract code and configuration
            code = task_data.get("code", "")
            extraction_config = task_data.get("config", {})
            
            # Extract features
            features = await self.feature_extractor.extract_all_features(code, extraction_config)
            
            return {
                "features": features,
                "metadata": {
                    "code_length": len(code),
                    "feature_count": len(features)
                }
            }
            
        except Exception as e:
            logger.error(f"Feature extraction failed: {e}")
            raise


@ray.remote
class RepositoryAnalyzerWorker(GenericWorker):
    """Specialized worker for repository analysis tasks."""
    
    def __init__(self, config: WorkerConfig):
        super().__init__(config)
    
    async def _process_task(self, task_data: Any) -> Any:
        """Process repository analysis task."""
        try:
            # Extract repository data
            repo_path = task_data.get("repo_path", "")
            analysis_config = task_data.get("config", {})
            
            # Analyze repository
            # Implementation would depend on specific analysis requirements
            
            return {
                "analysis": {},
                "metadata": {
                    "repo_path": repo_path
                }
            }
            
        except Exception as e:
            logger.error(f"Repository analysis failed: {e}")
            raise


@ray.remote
class MLTrainerWorker(GenericWorker):
    """Specialized worker for ML training tasks."""
    
    def __init__(self, config: WorkerConfig):
        super().__init__(config)
    
    async def _process_task(self, task_data: Any) -> Any:
        """Process ML training task."""
        try:
            # Extract training data and configuration
            model_config = task_data.get("model_config", {})
            training_data = task_data.get("training_data", [])
            
            # Train model
            # Implementation would depend on specific training requirements
            
            return {
                "model_path": "",
                "metrics": {},
                "metadata": {
                    "training_samples": len(training_data)
                }
            }
            
        except Exception as e:
            logger.error(f"ML training failed: {e}")
            raise


# Example usage functions
def create_worker_configs() -> List[WorkerConfig]:
    """Create example worker configurations."""
    return [
        WorkerConfig(
            worker_id="pattern_detector_1",
            worker_type="pattern_detector",
            num_cpus=2,
            num_gpus=0,
            memory=4000000000,
            specialization=[TaskType.PATTERN_DETECTION]
        ),
        WorkerConfig(
            worker_id="feature_extractor_1",
            worker_type="feature_extractor",
            num_cpus=4,
            num_gpus=0,
            memory=8000000000,
            specialization=[TaskType.FEATURE_EXTRACTION]
        ),
        WorkerConfig(
            worker_id="ml_trainer_1",
            worker_type="ml_trainer",
            num_cpus=8,
            num_gpus=1,
            memory=16000000000,
            specialization=[TaskType.MODEL_TRAINING, TaskType.BATCH_INFERENCE]
        )
    ]