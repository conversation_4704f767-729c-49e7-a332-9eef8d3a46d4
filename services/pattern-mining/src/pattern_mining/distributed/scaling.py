"""
Auto-scaling and resource management for distributed pattern mining.

This module provides comprehensive auto-scaling capabilities including:
- Auto-scaling policies and configuration
- Load balancing strategies
- Resource monitoring and allocation
- Performance optimization
- Cost management and optimization
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import json
import os
import psutil
import numpy as np
from collections import deque
import ray
from ray.autoscaler import sdk

from .cluster import RayClusterManager, ClusterStatus
from .task_manager import DistributedTaskManager, TaskStatus
from .gpu_acceleration import GPUAccelerationManager

logger = logging.getLogger(__name__)


class ScalingDirection(Enum):
    """Scaling direction enumeration."""
    UP = "up"
    DOWN = "down"
    STABLE = "stable"


class ScalingPolicy(Enum):
    """Scaling policy enumeration."""
    REACTIVE = "reactive"
    PREDICTIVE = "predictive"
    HYBRID = "hybrid"
    MANUAL = "manual"


class LoadBalancingStrategy(Enum):
    """Load balancing strategy enumeration."""
    ROUND_ROBIN = "round_robin"
    LEAST_LOADED = "least_loaded"
    RESOURCE_AWARE = "resource_aware"
    LOCALITY_AWARE = "locality_aware"


@dataclass
class ScalingMetrics:
    """Scaling metrics for decision making."""
    cpu_utilization: float = 0.0
    memory_utilization: float = 0.0
    gpu_utilization: float = 0.0
    queue_length: int = 0
    task_completion_rate: float = 0.0
    error_rate: float = 0.0
    response_time: float = 0.0
    throughput: float = 0.0
    cost_per_hour: float = 0.0
    timestamp: float = field(default_factory=time.time)


@dataclass
class ScalingConfig:
    """Configuration for auto-scaling."""
    min_workers: int = 1
    max_workers: int = 100
    target_cpu_utilization: float = 70.0
    target_memory_utilization: float = 80.0
    target_gpu_utilization: float = 75.0
    scale_up_threshold: float = 80.0
    scale_down_threshold: float = 30.0
    scale_up_cooldown: int = 300  # 5 minutes
    scale_down_cooldown: int = 600  # 10 minutes
    min_queue_length: int = 10
    max_queue_length: int = 1000
    metrics_window: int = 600  # 10 minutes
    prediction_horizon: int = 1800  # 30 minutes
    cost_threshold: float = 1000.0  # per hour
    enable_cost_optimization: bool = True
    enable_gpu_scaling: bool = True
    enable_preemptible_instances: bool = True


@dataclass
class ResourceRequest:
    """Resource request for scaling."""
    worker_type: str
    num_workers: int
    cpu_per_worker: int
    memory_per_worker: int
    gpu_per_worker: int = 0
    preemptible: bool = False
    max_price: float = 0.0
    placement_constraints: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ScalingDecision:
    """Scaling decision result."""
    direction: ScalingDirection
    resource_request: Optional[ResourceRequest] = None
    reason: str = ""
    confidence: float = 0.0
    estimated_cost: float = 0.0
    estimated_completion_time: float = 0.0
    metrics: ScalingMetrics = None


class AutoScalingManager:
    """
    Auto-scaling manager for distributed pattern mining.
    
    Provides comprehensive auto-scaling including resource monitoring,
    scaling decisions, load balancing, and cost optimization.
    """
    
    def __init__(
        self,
        config: ScalingConfig,
        cluster_manager: RayClusterManager,
        task_manager: DistributedTaskManager,
        gpu_manager: GPUAccelerationManager = None
    ):
        self.config = config
        self.cluster_manager = cluster_manager
        self.task_manager = task_manager
        self.gpu_manager = gpu_manager
        
        # Scaling state
        self.current_workers = 0
        self.target_workers = config.min_workers
        self.last_scale_up_time = 0
        self.last_scale_down_time = 0
        self.scaling_active = False
        
        # Metrics and monitoring
        self.metrics_history: deque = deque(maxlen=100)
        self.scaling_history: List[ScalingDecision] = []
        self.cost_history: List[float] = []
        
        # Load balancing
        self.load_balancer = LoadBalancer(config)
        
        # Predictive scaling
        self.predictor = WorkloadPredictor(config)
        
        # Cost optimizer
        self.cost_optimizer = CostOptimizer(config)
        
        logger.info("Initialized auto-scaling manager")
    
    async def start_autoscaling(self) -> bool:
        """
        Start auto-scaling monitoring and decisions.
        
        Returns:
            bool: True if auto-scaling started successfully
        """
        try:
            logger.info("Starting auto-scaling")
            
            # Initialize with minimum workers
            await self._scale_to_target(self.config.min_workers)
            
            # Start monitoring and scaling tasks
            self.scaling_active = True
            asyncio.create_task(self._monitor_metrics())
            asyncio.create_task(self._make_scaling_decisions())
            asyncio.create_task(self._optimize_costs())
            
            logger.info("Auto-scaling started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start auto-scaling: {e}")
            return False
    
    async def stop_autoscaling(self) -> bool:
        """
        Stop auto-scaling and cleanup resources.
        
        Returns:
            bool: True if auto-scaling stopped successfully
        """
        try:
            logger.info("Stopping auto-scaling")
            
            # Stop scaling
            self.scaling_active = False
            
            # Scale down to minimum
            await self._scale_to_target(self.config.min_workers)
            
            logger.info("Auto-scaling stopped successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop auto-scaling: {e}")
            return False
    
    async def manual_scale(self, target_workers: int) -> bool:
        """
        Manually scale cluster to target number of workers.
        
        Args:
            target_workers: Target number of workers
            
        Returns:
            bool: True if scaling successful
        """
        try:
            logger.info(f"Manual scaling to {target_workers} workers")
            
            # Validate target
            if target_workers < self.config.min_workers:
                target_workers = self.config.min_workers
            elif target_workers > self.config.max_workers:
                target_workers = self.config.max_workers
            
            # Scale to target
            success = await self._scale_to_target(target_workers)
            
            if success:
                self.target_workers = target_workers
                logger.info(f"Manual scaling completed: {target_workers} workers")
            
            return success
            
        except Exception as e:
            logger.error(f"Manual scaling failed: {e}")
            return False
    
    async def get_scaling_status(self) -> Dict[str, Any]:
        """
        Get comprehensive scaling status.
        
        Returns:
            Scaling status information
        """
        try:
            current_metrics = await self._collect_metrics()
            
            return {
                "scaling_active": self.scaling_active,
                "current_workers": self.current_workers,
                "target_workers": self.target_workers,
                "min_workers": self.config.min_workers,
                "max_workers": self.config.max_workers,
                "current_metrics": {
                    "cpu_utilization": current_metrics.cpu_utilization,
                    "memory_utilization": current_metrics.memory_utilization,
                    "gpu_utilization": current_metrics.gpu_utilization,
                    "queue_length": current_metrics.queue_length,
                    "task_completion_rate": current_metrics.task_completion_rate,
                    "error_rate": current_metrics.error_rate,
                    "response_time": current_metrics.response_time,
                    "throughput": current_metrics.throughput,
                    "cost_per_hour": current_metrics.cost_per_hour
                },
                "scaling_history": [
                    {
                        "direction": decision.direction.value,
                        "reason": decision.reason,
                        "confidence": decision.confidence,
                        "estimated_cost": decision.estimated_cost,
                        "timestamp": decision.metrics.timestamp if decision.metrics else time.time()
                    }
                    for decision in self.scaling_history[-10:]  # Last 10 decisions
                ],
                "cost_optimization": await self.cost_optimizer.get_optimization_status(),
                "load_balancing": await self.load_balancer.get_balancing_status()
            }
            
        except Exception as e:
            logger.error(f"Failed to get scaling status: {e}")
            return {"error": str(e)}
    
    async def get_scaling_recommendations(self) -> Dict[str, Any]:
        """
        Get scaling recommendations based on current metrics.
        
        Returns:
            Scaling recommendations
        """
        try:
            current_metrics = await self._collect_metrics()
            
            # Make scaling decision
            decision = await self._make_scaling_decision(current_metrics)
            
            # Get cost optimization recommendations
            cost_recommendations = await self.cost_optimizer.get_recommendations(current_metrics)
            
            # Get load balancing recommendations
            load_recommendations = await self.load_balancer.get_recommendations(current_metrics)
            
            return {
                "scaling_decision": {
                    "direction": decision.direction.value,
                    "reason": decision.reason,
                    "confidence": decision.confidence,
                    "estimated_cost": decision.estimated_cost,
                    "estimated_completion_time": decision.estimated_completion_time
                },
                "cost_optimization": cost_recommendations,
                "load_balancing": load_recommendations,
                "metrics": {
                    "cpu_utilization": current_metrics.cpu_utilization,
                    "memory_utilization": current_metrics.memory_utilization,
                    "gpu_utilization": current_metrics.gpu_utilization,
                    "queue_length": current_metrics.queue_length,
                    "task_completion_rate": current_metrics.task_completion_rate
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get scaling recommendations: {e}")
            return {"error": str(e)}
    
    async def _monitor_metrics(self):
        """Background task to monitor scaling metrics."""
        while self.scaling_active:
            try:
                # Collect current metrics
                metrics = await self._collect_metrics()
                
                # Store metrics
                self.metrics_history.append(metrics)
                
                # Update cost history
                self.cost_history.append(metrics.cost_per_hour)
                
                # Keep only recent cost history
                if len(self.cost_history) > 100:
                    self.cost_history = self.cost_history[-100:]
                
                await asyncio.sleep(60)  # Collect metrics every minute
                
            except Exception as e:
                logger.error(f"Metrics monitoring error: {e}")
                await asyncio.sleep(120)
    
    async def _make_scaling_decisions(self):
        """Background task to make scaling decisions."""
        while self.scaling_active:
            try:
                if len(self.metrics_history) > 0:
                    current_metrics = self.metrics_history[-1]
                    
                    # Make scaling decision
                    decision = await self._make_scaling_decision(current_metrics)
                    
                    # Execute scaling decision
                    if decision.direction != ScalingDirection.STABLE:
                        await self._execute_scaling_decision(decision)
                    
                    # Store decision
                    self.scaling_history.append(decision)
                    
                    # Keep only recent history
                    if len(self.scaling_history) > 100:
                        self.scaling_history = self.scaling_history[-100:]
                
                await asyncio.sleep(120)  # Make decisions every 2 minutes
                
            except Exception as e:
                logger.error(f"Scaling decision error: {e}")
                await asyncio.sleep(300)
    
    async def _optimize_costs(self):
        """Background task to optimize costs."""
        while self.scaling_active:
            try:
                if len(self.metrics_history) > 0:
                    current_metrics = self.metrics_history[-1]
                    
                    # Optimize costs
                    await self.cost_optimizer.optimize_costs(current_metrics)
                
                await asyncio.sleep(600)  # Optimize costs every 10 minutes
                
            except Exception as e:
                logger.error(f"Cost optimization error: {e}")
                await asyncio.sleep(1200)
    
    async def _collect_metrics(self) -> ScalingMetrics:
        """Collect current scaling metrics."""
        try:
            # Get cluster status
            cluster_status = await self.cluster_manager.get_cluster_status()
            
            # Get task manager status
            task_status = await self.task_manager.get_manager_status()
            
            # Get GPU metrics if available
            gpu_metrics = {}
            if self.gpu_manager:
                gpu_metrics = await self.gpu_manager.get_gpu_metrics()
            
            # Calculate metrics
            cpu_utilization = self._calculate_cpu_utilization(cluster_status)
            memory_utilization = self._calculate_memory_utilization(cluster_status)
            gpu_utilization = gpu_metrics.get("average_utilization", 0.0)
            queue_length = task_status.get("queues", {}).get("task_queue_size", 0)
            
            # Calculate performance metrics
            task_completion_rate = self._calculate_task_completion_rate(task_status)
            error_rate = self._calculate_error_rate(task_status)
            response_time = self._calculate_response_time()
            throughput = self._calculate_throughput()
            cost_per_hour = self._calculate_cost_per_hour()
            
            return ScalingMetrics(
                cpu_utilization=cpu_utilization,
                memory_utilization=memory_utilization,
                gpu_utilization=gpu_utilization,
                queue_length=queue_length,
                task_completion_rate=task_completion_rate,
                error_rate=error_rate,
                response_time=response_time,
                throughput=throughput,
                cost_per_hour=cost_per_hour
            )
            
        except Exception as e:
            logger.error(f"Failed to collect metrics: {e}")
            return ScalingMetrics()
    
    async def _make_scaling_decision(self, metrics: ScalingMetrics) -> ScalingDecision:
        """Make scaling decision based on metrics."""
        try:
            # Check cooldown periods
            current_time = time.time()
            
            if current_time - self.last_scale_up_time < self.config.scale_up_cooldown:
                if current_time - self.last_scale_down_time < self.config.scale_down_cooldown:
                    return ScalingDecision(
                        direction=ScalingDirection.STABLE,
                        reason="Cooldown period active",
                        confidence=1.0,
                        metrics=metrics
                    )
            
            # Determine scaling direction
            should_scale_up = (
                metrics.cpu_utilization > self.config.scale_up_threshold or
                metrics.memory_utilization > self.config.scale_up_threshold or
                metrics.gpu_utilization > self.config.scale_up_threshold or
                metrics.queue_length > self.config.max_queue_length
            )
            
            should_scale_down = (
                metrics.cpu_utilization < self.config.scale_down_threshold and
                metrics.memory_utilization < self.config.scale_down_threshold and
                metrics.gpu_utilization < self.config.scale_down_threshold and
                metrics.queue_length < self.config.min_queue_length
            )
            
            if should_scale_up and self.current_workers < self.config.max_workers:
                # Scale up decision
                num_workers_to_add = self._calculate_workers_to_add(metrics)
                
                return ScalingDecision(
                    direction=ScalingDirection.UP,
                    resource_request=ResourceRequest(
                        worker_type="standard",
                        num_workers=num_workers_to_add,
                        cpu_per_worker=4,
                        memory_per_worker=8000000000,
                        gpu_per_worker=1 if self.config.enable_gpu_scaling else 0,
                        preemptible=self.config.enable_preemptible_instances
                    ),
                    reason=f"High resource utilization: CPU={metrics.cpu_utilization:.1f}%, Memory={metrics.memory_utilization:.1f}%, Queue={metrics.queue_length}",
                    confidence=0.8,
                    estimated_cost=self._estimate_scaling_cost(num_workers_to_add, True),
                    estimated_completion_time=self._estimate_completion_time(metrics),
                    metrics=metrics
                )
            
            elif should_scale_down and self.current_workers > self.config.min_workers:
                # Scale down decision
                num_workers_to_remove = self._calculate_workers_to_remove(metrics)
                
                return ScalingDecision(
                    direction=ScalingDirection.DOWN,
                    resource_request=ResourceRequest(
                        worker_type="standard",
                        num_workers=num_workers_to_remove,
                        cpu_per_worker=4,
                        memory_per_worker=8000000000
                    ),
                    reason=f"Low resource utilization: CPU={metrics.cpu_utilization:.1f}%, Memory={metrics.memory_utilization:.1f}%, Queue={metrics.queue_length}",
                    confidence=0.7,
                    estimated_cost=self._estimate_scaling_cost(num_workers_to_remove, False),
                    estimated_completion_time=self._estimate_completion_time(metrics),
                    metrics=metrics
                )
            
            else:
                # No scaling needed
                return ScalingDecision(
                    direction=ScalingDirection.STABLE,
                    reason="Resource utilization within target range",
                    confidence=0.9,
                    metrics=metrics
                )
                
        except Exception as e:
            logger.error(f"Failed to make scaling decision: {e}")
            return ScalingDecision(
                direction=ScalingDirection.STABLE,
                reason=f"Decision error: {e}",
                confidence=0.0,
                metrics=metrics
            )
    
    async def _execute_scaling_decision(self, decision: ScalingDecision):
        """Execute scaling decision."""
        try:
            if decision.direction == ScalingDirection.UP:
                # Scale up
                new_target = self.current_workers + decision.resource_request.num_workers
                await self._scale_to_target(new_target)
                self.last_scale_up_time = time.time()
                
            elif decision.direction == ScalingDirection.DOWN:
                # Scale down
                new_target = self.current_workers - decision.resource_request.num_workers
                await self._scale_to_target(new_target)
                self.last_scale_down_time = time.time()
            
            logger.info(f"Executed scaling decision: {decision.direction.value} to {self.current_workers} workers")
            
        except Exception as e:
            logger.error(f"Failed to execute scaling decision: {e}")
    
    async def _scale_to_target(self, target_workers: int) -> bool:
        """Scale cluster to target number of workers."""
        try:
            # Validate target
            target_workers = max(self.config.min_workers, min(target_workers, self.config.max_workers))
            
            # Scale cluster
            success = await self.cluster_manager.scale_cluster(target_workers)
            
            if success:
                self.current_workers = target_workers
                self.target_workers = target_workers
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to scale to target: {e}")
            return False
    
    def _calculate_cpu_utilization(self, cluster_status: Dict[str, Any]) -> float:
        """Calculate CPU utilization."""
        try:
            # Get CPU metrics from cluster status
            # This is a simplified calculation
            return cluster_status.get("metrics", {}).get("cpu_utilization", 0.0)
        except:
            return 0.0
    
    def _calculate_memory_utilization(self, cluster_status: Dict[str, Any]) -> float:
        """Calculate memory utilization."""
        try:
            # Get memory metrics from cluster status
            # This is a simplified calculation
            return cluster_status.get("metrics", {}).get("memory_utilization", 0.0)
        except:
            return 0.0
    
    def _calculate_task_completion_rate(self, task_status: Dict[str, Any]) -> float:
        """Calculate task completion rate."""
        try:
            completed = task_status.get("tasks", {}).get("completed", 0)
            total = task_status.get("tasks", {}).get("total", 0)
            
            if total > 0:
                return completed / total
            return 0.0
        except:
            return 0.0
    
    def _calculate_error_rate(self, task_status: Dict[str, Any]) -> float:
        """Calculate error rate."""
        try:
            failed = task_status.get("tasks", {}).get("failed", 0)
            total = task_status.get("tasks", {}).get("total", 0)
            
            if total > 0:
                return failed / total
            return 0.0
        except:
            return 0.0
    
    def _calculate_response_time(self) -> float:
        """Calculate average response time."""
        try:
            # Calculate from metrics history
            if len(self.metrics_history) > 0:
                return np.mean([m.response_time for m in self.metrics_history[-10:]])
            return 0.0
        except:
            return 0.0
    
    def _calculate_throughput(self) -> float:
        """Calculate throughput."""
        try:
            # Calculate from metrics history
            if len(self.metrics_history) > 0:
                return np.mean([m.throughput for m in self.metrics_history[-10:]])
            return 0.0
        except:
            return 0.0
    
    def _calculate_cost_per_hour(self) -> float:
        """Calculate cost per hour."""
        try:
            # Simplified cost calculation
            # In production, this would integrate with cloud provider pricing APIs
            base_cost_per_worker = 0.5  # $0.50 per worker per hour
            return self.current_workers * base_cost_per_worker
        except:
            return 0.0
    
    def _calculate_workers_to_add(self, metrics: ScalingMetrics) -> int:
        """Calculate number of workers to add."""
        try:
            # Simple heuristic based on resource utilization
            max_utilization = max(
                metrics.cpu_utilization,
                metrics.memory_utilization,
                metrics.gpu_utilization
            )
            
            if max_utilization > 90:
                return min(4, self.config.max_workers - self.current_workers)
            elif max_utilization > 80:
                return min(2, self.config.max_workers - self.current_workers)
            else:
                return min(1, self.config.max_workers - self.current_workers)
                
        except:
            return 1
    
    def _calculate_workers_to_remove(self, metrics: ScalingMetrics) -> int:
        """Calculate number of workers to remove."""
        try:
            # Simple heuristic based on resource utilization
            max_utilization = max(
                metrics.cpu_utilization,
                metrics.memory_utilization,
                metrics.gpu_utilization
            )
            
            if max_utilization < 20:
                return min(2, self.current_workers - self.config.min_workers)
            else:
                return min(1, self.current_workers - self.config.min_workers)
                
        except:
            return 1
    
    def _estimate_scaling_cost(self, num_workers: int, is_scale_up: bool) -> float:
        """Estimate scaling cost."""
        try:
            base_cost_per_worker = 0.5  # $0.50 per worker per hour
            
            if is_scale_up:
                return num_workers * base_cost_per_worker
            else:
                return -num_workers * base_cost_per_worker
                
        except:
            return 0.0
    
    def _estimate_completion_time(self, metrics: ScalingMetrics) -> float:
        """Estimate task completion time."""
        try:
            if metrics.task_completion_rate > 0:
                return metrics.queue_length / metrics.task_completion_rate
            return 0.0
        except:
            return 0.0


class LoadBalancer:
    """Load balancer for distributing tasks across workers."""
    
    def __init__(self, config: ScalingConfig):
        self.config = config
        self.strategy = LoadBalancingStrategy.RESOURCE_AWARE
        self.worker_loads: Dict[str, float] = {}
    
    async def get_balancing_status(self) -> Dict[str, Any]:
        """Get load balancing status."""
        return {
            "strategy": self.strategy.value,
            "worker_loads": self.worker_loads
        }
    
    async def get_recommendations(self, metrics: ScalingMetrics) -> Dict[str, Any]:
        """Get load balancing recommendations."""
        return {
            "strategy": self.strategy.value,
            "recommendations": []
        }


class WorkloadPredictor:
    """Workload predictor for predictive scaling."""
    
    def __init__(self, config: ScalingConfig):
        self.config = config
        self.history_window = config.metrics_window
        self.prediction_horizon = config.prediction_horizon
    
    async def predict_workload(self, metrics_history: List[ScalingMetrics]) -> Dict[str, Any]:
        """Predict future workload."""
        try:
            # Simple prediction based on trends
            if len(metrics_history) < 2:
                return {"prediction": "stable", "confidence": 0.5}
            
            # Calculate trend
            recent_metrics = metrics_history[-10:]
            cpu_trend = np.polyfit(range(len(recent_metrics)), [m.cpu_utilization for m in recent_metrics], 1)[0]
            
            if cpu_trend > 5:
                return {"prediction": "increasing", "confidence": 0.7}
            elif cpu_trend < -5:
                return {"prediction": "decreasing", "confidence": 0.7}
            else:
                return {"prediction": "stable", "confidence": 0.8}
                
        except:
            return {"prediction": "stable", "confidence": 0.5}


class CostOptimizer:
    """Cost optimizer for resource management."""
    
    def __init__(self, config: ScalingConfig):
        self.config = config
        self.cost_threshold = config.cost_threshold
    
    async def optimize_costs(self, metrics: ScalingMetrics):
        """Optimize costs based on current metrics."""
        try:
            if metrics.cost_per_hour > self.cost_threshold:
                # Consider using preemptible instances
                # Consider scaling down
                logger.info(f"Cost optimization: current cost ${metrics.cost_per_hour:.2f}/hour exceeds threshold ${self.cost_threshold:.2f}/hour")
        except Exception as e:
            logger.error(f"Cost optimization failed: {e}")
    
    async def get_optimization_status(self) -> Dict[str, Any]:
        """Get cost optimization status."""
        return {
            "cost_threshold": self.cost_threshold,
            "optimization_enabled": self.config.enable_cost_optimization
        }
    
    async def get_recommendations(self, metrics: ScalingMetrics) -> Dict[str, Any]:
        """Get cost optimization recommendations."""
        recommendations = []
        
        if metrics.cost_per_hour > self.cost_threshold:
            recommendations.append({
                "type": "cost_reduction",
                "message": f"Consider scaling down or using preemptible instances",
                "priority": "high",
                "estimated_savings": self.cost_threshold - metrics.cost_per_hour
            })
        
        return {"recommendations": recommendations}


# Example usage functions
def create_scaling_config() -> ScalingConfig:
    """Create example scaling configuration."""
    return ScalingConfig(
        min_workers=2,
        max_workers=20,
        target_cpu_utilization=70.0,
        target_memory_utilization=80.0,
        scale_up_threshold=80.0,
        scale_down_threshold=30.0,
        scale_up_cooldown=300,
        scale_down_cooldown=600,
        enable_cost_optimization=True,
        enable_gpu_scaling=True
    )