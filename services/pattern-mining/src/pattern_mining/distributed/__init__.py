"""
Distributed computing infrastructure for pattern mining service.

This module provides Ray-based distributed computing capabilities for:
- Distributed model training and inference
- GPU acceleration and multi-GPU support
- Auto-scaling and resource management
- Task distribution and scheduling
- Fault tolerance and recovery

Key Components:
- cluster: Ray cluster management and configuration
- ml_pipeline: Distributed ML pipeline for training and inference
- gpu_acceleration: GPU resource management and optimization
- task_manager: Distributed task scheduling and execution
- scaling: Auto-scaling policies and resource optimization
"""

from .cluster import RayClusterManager
from .ml_pipeline import DistributedMLPipeline
from .gpu_acceleration import GPUAccelerationManager
from .task_manager import DistributedTaskManager
from .scaling import AutoScalingManager

__all__ = [
    "RayClusterManager",
    "DistributedMLPipeline", 
    "GPUAccelerationManager",
    "DistributedTaskManager",
    "AutoScalingManager"
]