"""
Application Settings

Main application settings and configuration management.
"""

from pydantic import Field, validator, root_validator
from pydantic_settings import BaseSettings
from typing import List, Optional, Dict, Any
from functools import lru_cache
import os
from enum import Enum
import structlog

logger = structlog.get_logger()


class Settings(BaseSettings):
    """Application settings."""
    
    # Application settings
    app_name: str = Field(default="pattern-mining", description="Application name")
    app_version: str = Field(default="1.0.0", description="Application version")
    environment: str = Field(default="development", description="Environment")
    debug: bool = Field(default=False, description="Debug mode")
    
    # Server settings
    host: str = Field(default="0.0.0.0", description="Server host")
    port: int = Field(default=8000, description="Server port")
    workers: int = Field(default=1, description="Number of workers")
    
    # Security settings
    secret_key: str = Field(..., description="Secret key for session management")
    cors_origins: List[str] = Field(default=["*"], description="CORS origins")
    
    # Rate limiting
    rate_limit_per_minute: int = Field(default=100, description="Rate limit per minute")
    rate_limit_burst: int = Field(default=20, description="Rate limit burst")
    
    # Batch processing
    max_batch_size: int = Field(default=100, description="Maximum batch size")
    batch_timeout_seconds: int = Field(default=3600, description="Batch timeout in seconds")
    
    # WebSocket settings
    websocket_max_connections: int = Field(default=1000, description="Maximum WebSocket connections")
    websocket_heartbeat_interval: int = Field(default=30, description="WebSocket heartbeat interval")
    
    # Analytics settings
    analytics_retention_days: int = Field(default=90, description="Analytics data retention days")
    enable_real_time_analytics: bool = Field(default=True, description="Enable real-time analytics")
    
    # Performance settings
    enable_caching: bool = Field(default=True, description="Enable caching")
    cache_ttl_seconds: int = Field(default=3600, description="Cache TTL in seconds")
    
    # Monitoring settings
    enable_metrics: bool = Field(default=True, description="Enable metrics collection")
    enable_tracing: bool = Field(default=True, description="Enable distributed tracing")
    metrics_port: int = Field(default=9090, description="Metrics server port")
    
    # Database settings
    database_url: str = Field(..., description="Database URL")
    database_pool_size: int = Field(default=10, description="Database pool size")
    database_max_overflow: int = Field(default=20, description="Database max overflow")
    database_echo: bool = Field(default=False, description="Database echo SQL queries")
    
    # Redis settings
    redis_url: str = Field(default="redis://localhost:6379", description="Redis URL")
    redis_max_connections: int = Field(default=50, description="Redis max connections")
    redis_max_connections_per_node: int = Field(default=10, description="Redis max connections per node")
    redis_connection_timeout: float = Field(default=5.0, description="Redis connection timeout in seconds")
    redis_socket_timeout: float = Field(default=5.0, description="Redis socket timeout in seconds")
    redis_socket_keepalive: bool = Field(default=True, description="Redis socket keepalive")
    redis_health_check_interval: float = Field(default=30.0, description="Redis health check interval in seconds")
    redis_retry_on_timeout: bool = Field(default=True, description="Redis retry on timeout")
    redis_max_retries: int = Field(default=3, description="Redis max retries")
    redis_retry_backoff_factor: float = Field(default=1.0, description="Redis retry backoff factor")
    redis_decode_responses: bool = Field(default=True, description="Redis decode responses")
    redis_password: Optional[str] = Field(default=None, description="Redis password")
    redis_db: int = Field(default=0, description="Redis database number")
    redis_ssl: bool = Field(default=False, description="Redis SSL enabled")
    redis_ssl_ca_certs: Optional[str] = Field(default=None, description="Redis SSL CA certificates path")
    redis_ssl_cert_reqs: str = Field(default="required", description="Redis SSL certificate requirements")
    redis_cluster_mode: bool = Field(default=False, description="Redis cluster mode enabled")
    redis_cluster_nodes: List[str] = Field(default_factory=list, description="Redis cluster nodes")
    redis_cluster_skip_full_coverage_check: bool = Field(default=False, description="Redis cluster skip full coverage check")
    redis_cluster_readonly_mode: bool = Field(default=False, description="Redis cluster readonly mode")
    redis_sentinel_mode: bool = Field(default=False, description="Redis sentinel mode enabled")
    redis_sentinel_service_name: str = Field(default="mymaster", description="Redis sentinel service name")
    redis_sentinel_password: Optional[str] = Field(default=None, description="Redis sentinel password")
    redis_compression: bool = Field(default=False, description="Redis compression enabled")
    
    # Advanced Redis caching settings
    redis_cache_key_prefix: str = Field(default="pattern_mining:", description="Redis cache key prefix")
    redis_cache_compression_threshold: int = Field(default=1024, description="Redis cache compression threshold in bytes")
    redis_cache_default_ttl: int = Field(default=3600, description="Redis cache default TTL in seconds")
    redis_cache_max_key_length: int = Field(default=250, description="Redis cache max key length")
    redis_cache_serialization_format: str = Field(default="json", description="Redis cache serialization format")
    
    # Cache monitoring settings
    cache_monitoring_enabled: bool = Field(default=True, description="Cache monitoring enabled")
    cache_monitoring_interval: float = Field(default=30.0, description="Cache monitoring interval in seconds")
    cache_monitoring_retention_days: int = Field(default=7, description="Cache monitoring data retention in days")
    cache_alerting_enabled: bool = Field(default=True, description="Cache alerting enabled")
    cache_alert_cooldown: int = Field(default=300, description="Cache alert cooldown in seconds")
    
    # Cache performance settings
    cache_l1_max_size: int = Field(default=1000, description="L1 cache max size")
    cache_l1_ttl: int = Field(default=3600, description="L1 cache TTL in seconds")
    cache_l2_ttl: int = Field(default=7200, description="L2 cache TTL in seconds")
    cache_promotion_threshold: int = Field(default=2, description="Cache promotion threshold")
    cache_warmup_enabled: bool = Field(default=True, description="Cache warmup enabled")
    cache_warmup_concurrency: int = Field(default=10, description="Cache warmup concurrency")
    
    # Distributed cache settings
    distributed_cache_enabled: bool = Field(default=False, description="Distributed cache enabled")
    distributed_cache_replication_factor: int = Field(default=2, description="Distributed cache replication factor")
    distributed_cache_consistency_level: str = Field(default="eventual", description="Distributed cache consistency level")
    distributed_cache_read_timeout: float = Field(default=3.0, description="Distributed cache read timeout in seconds")
    distributed_cache_write_timeout: float = Field(default=5.0, description="Distributed cache write timeout in seconds")
    distributed_cache_read_repair: bool = Field(default=True, description="Distributed cache read repair enabled")
    
    # Google Cloud settings
    gcp_project_id: str = Field(..., description="GCP project ID")
    gcp_location: str = Field(default="us-central1", description="GCP location")
    
    # BigQuery settings
    bigquery_dataset_id: str = Field(..., description="BigQuery dataset ID")
    bigquery_location: str = Field(default="US", description="BigQuery location")
    bigquery_table_prefix: str = Field(default="pattern_mining", description="BigQuery table prefix")
    bigquery_max_connections: int = Field(default=10, description="BigQuery max connections")
    bigquery_connection_timeout: int = Field(default=30, description="BigQuery connection timeout")
    bigquery_read_timeout: int = Field(default=300, description="BigQuery read timeout")
    bigquery_max_retry_attempts: int = Field(default=3, description="BigQuery max retry attempts")
    bigquery_dry_run: bool = Field(default=False, description="BigQuery dry run mode")
    bigquery_use_cache: bool = Field(default=True, description="BigQuery use cache")
    bigquery_max_bytes_billed: Optional[int] = Field(None, description="BigQuery max bytes billed")
    google_application_credentials: Optional[str] = Field(None, description="Google application credentials path")
    
    # ML settings
    ml_model_storage_path: str = Field(default="/tmp/models", description="ML model storage path")
    ml_batch_size: int = Field(default=32, description="ML batch size")
    ml_max_sequence_length: int = Field(default=512, description="ML max sequence length")
    
    # Vector embeddings settings
    embedding_model: str = Field(default="sentence-transformers/all-MiniLM-L6-v2", description="Embedding model")
    embedding_dimension: int = Field(default=384, description="Embedding dimension")
    
    # API settings
    api_rate_limit: int = Field(default=100, description="API rate limit per minute")
    api_timeout: int = Field(default=30, description="API timeout in seconds")
    
    # Security settings
    secret_key: str = Field(..., description="Secret key for JWT")
    cors_origins: List[str] = Field(default=["*"], description="CORS origins")
    
    # Logging settings
    log_level: str = Field(default="INFO", description="Log level")
    log_format: str = Field(default="json", description="Log format")
    
    # Monitoring settings
    enable_metrics: bool = Field(default=True, description="Enable metrics")
    metrics_port: int = Field(default=9090, description="Metrics port")
    enable_tracing: bool = Field(default=True, description="Enable OpenTelemetry tracing")
    jaeger_endpoint: str = Field(default="http://localhost:14268/api/traces", description="Jaeger endpoint")
    
    # Performance settings
    max_request_size: int = Field(default=10 * 1024 * 1024, description="Max request size in bytes")
    response_timeout: int = Field(default=25, description="Response timeout in seconds")
    worker_timeout: int = Field(default=120, description="Worker timeout in seconds")
    
    # Pattern detection settings
    max_patterns_per_request: int = Field(default=100, description="Max patterns per request")
    pattern_confidence_threshold: float = Field(default=0.7, description="Pattern confidence threshold")
    enable_streaming: bool = Field(default=True, description="Enable streaming responses")
    
    # BigQuery settings
    bigquery_max_results: int = Field(default=10000, description="BigQuery max results")
    bigquery_job_timeout: int = Field(default=300, description="BigQuery job timeout in seconds")
    
    # Cache settings
    cache_ttl: int = Field(default=3600, description="Cache TTL in seconds")
    cache_max_size: int = Field(default=1000, description="Cache max size")
    
    # Feature flags
    enable_gpu: bool = Field(default=False, description="Enable GPU acceleration")
    enable_distributed: bool = Field(default=False, description="Enable distributed processing")
    enable_caching: bool = Field(default=True, description="Enable caching")
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        validate_assignment = True  # Enable validation on assignment
        
    @validator('environment')
    def validate_environment(cls, v):
        """Validate environment setting."""
        valid_environments = ["development", "staging", "production"]
        if v not in valid_environments:
            raise ValueError(f"Environment must be one of {valid_environments}")
        return v
    
    @validator('log_level')
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of {valid_levels}")
        return v.upper()
    
    @validator('log_format')
    def validate_log_format(cls, v):
        """Validate log format."""
        valid_formats = ["json", "text"]
        if v not in valid_formats:
            raise ValueError(f"Log format must be one of {valid_formats}")
        return v
    
    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment == "development"
    
    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment == "production"
    
    @property
    def database_config(self) -> Dict[str, Any]:
        """Get database configuration."""
        return {
            "url": self.database_url,
            "pool_size": self.database_pool_size,
            "max_overflow": self.database_max_overflow,
            "echo": self.database_echo
        }
    
    @property
    def redis_config(self) -> Dict[str, Any]:
        """Get Redis configuration."""
        return {
            "url": self.redis_url,
            "max_connections": self.redis_max_connections,
            "max_connections_per_node": self.redis_max_connections_per_node,
            "connection_timeout": self.redis_connection_timeout,
            "socket_timeout": self.redis_socket_timeout,
            "socket_keepalive": self.redis_socket_keepalive,
            "health_check_interval": self.redis_health_check_interval,
            "retry_on_timeout": self.redis_retry_on_timeout,
            "max_retries": self.redis_max_retries,
            "retry_backoff_factor": self.redis_retry_backoff_factor,
            "decode_responses": self.redis_decode_responses,
            "password": self.redis_password,
            "db": self.redis_db,
            "ssl": self.redis_ssl,
            "ssl_ca_certs": self.redis_ssl_ca_certs,
            "ssl_cert_reqs": self.redis_ssl_cert_reqs,
            "cluster_mode": self.redis_cluster_mode,
            "cluster_nodes": self.redis_cluster_nodes,
            "cluster_skip_full_coverage_check": self.redis_cluster_skip_full_coverage_check,
            "cluster_readonly_mode": self.redis_cluster_readonly_mode,
            "sentinel_mode": self.redis_sentinel_mode,
            "sentinel_service_name": self.redis_sentinel_service_name,
            "sentinel_password": self.redis_sentinel_password,
            "compression": self.redis_compression,
        }
    
    @property
    def cache_config(self) -> Dict[str, Any]:
        """Get cache configuration."""
        return {
            "key_prefix": self.redis_cache_key_prefix,
            "compression_threshold": self.redis_cache_compression_threshold,
            "default_ttl": self.redis_cache_default_ttl,
            "max_key_length": self.redis_cache_max_key_length,
            "serialization_format": self.redis_cache_serialization_format,
            "l1_max_size": self.cache_l1_max_size,
            "l1_ttl": self.cache_l1_ttl,
            "l2_ttl": self.cache_l2_ttl,
            "promotion_threshold": self.cache_promotion_threshold,
            "warmup_enabled": self.cache_warmup_enabled,
            "warmup_concurrency": self.cache_warmup_concurrency,
        }
    
    @property
    def distributed_cache_config(self) -> Dict[str, Any]:
        """Get distributed cache configuration."""
        return {
            "enabled": self.distributed_cache_enabled,
            "replication_factor": self.distributed_cache_replication_factor,
            "consistency_level": self.distributed_cache_consistency_level,
            "read_timeout": self.distributed_cache_read_timeout,
            "write_timeout": self.distributed_cache_write_timeout,
            "read_repair": self.distributed_cache_read_repair,
        }
    
    @property
    def cache_monitoring_config(self) -> Dict[str, Any]:
        """Get cache monitoring configuration."""
        return {
            "enabled": self.cache_monitoring_enabled,
            "interval": self.cache_monitoring_interval,
            "retention_days": self.cache_monitoring_retention_days,
            "alerting_enabled": self.cache_alerting_enabled,
            "alert_cooldown": self.cache_alert_cooldown,
        }
    
    @property
    def ml_config(self) -> Dict[str, Any]:
        """Get ML configuration."""
        return {
            "model_storage_path": self.ml_model_storage_path,
            "batch_size": self.ml_batch_size,
            "max_sequence_length": self.ml_max_sequence_length,
            "embedding_model": self.embedding_model,
            "embedding_dimension": self.embedding_dimension,
            "enable_gpu": self.enable_gpu,
            "enable_distributed": self.enable_distributed
        }
    
    @property
    def monitoring_config(self) -> Dict[str, Any]:
        """Get monitoring configuration."""
        return {
            "enable_metrics": self.enable_metrics,
            "metrics_port": self.metrics_port,
            "enable_tracing": self.enable_tracing,
            "jaeger_endpoint": self.jaeger_endpoint
        }
    
    @property
    def performance_config(self) -> Dict[str, Any]:
        """Get performance configuration."""
        return {
            "max_request_size": self.max_request_size,
            "response_timeout": self.response_timeout,
            "worker_timeout": self.worker_timeout,
            "max_patterns_per_request": self.max_patterns_per_request,
            "pattern_confidence_threshold": self.pattern_confidence_threshold,
            "enable_streaming": self.enable_streaming
        }
    
    @property
    def bigquery_config(self) -> Dict[str, Any]:
        """Get BigQuery configuration."""
        return {
            "project_id": self.gcp_project_id,
            "dataset_id": self.bigquery_dataset_id,
            "location": self.bigquery_location,
            "table_prefix": self.bigquery_table_prefix,
            "max_connections": self.bigquery_max_connections,
            "connection_timeout": self.bigquery_connection_timeout,
            "read_timeout": self.bigquery_read_timeout,
            "max_retry_attempts": self.bigquery_max_retry_attempts,
            "dry_run": self.bigquery_dry_run,
            "use_cache": self.bigquery_use_cache,
            "max_bytes_billed": self.bigquery_max_bytes_billed,
            "credentials_path": self.google_application_credentials,
            "max_results": self.bigquery_max_results,
            "job_timeout": self.bigquery_job_timeout
        }
    
    @root_validator
    def validate_security_configuration(cls, values):
        """Comprehensive security validation for all configuration parameters."""
        try:
            from .validation import get_config_auditor
            
            # Perform security audit
            auditor = get_config_auditor()
            audit_result = auditor.audit_configuration(values)
            
            # Log security audit results
            logger.info(
                "Configuration security audit completed",
                security_score=audit_result["security_score"],
                violations=audit_result["violation_summary"]["total"],
                critical_violations=audit_result["violation_summary"]["critical"]
            )
            
            # Fail validation if critical security issues found
            if audit_result["violation_summary"]["critical"] > 0:
                critical_violations = [
                    v for v in audit_result["violations"] 
                    if v["severity"] == "critical"
                ]
                violation_messages = [v["message"] for v in critical_violations[:3]]  # Show first 3
                
                raise ValueError(
                    f"Critical security violations found in configuration: "
                    f"{'; '.join(violation_messages)}"
                )
            
            # Warn about other violations
            if audit_result["violation_summary"]["error"] > 0:
                logger.warning(
                    "Configuration security errors found",
                    error_count=audit_result["violation_summary"]["error"],
                    recommendations=audit_result["recommendations"][:3]
                )
            
            # Store audit results for monitoring
            values["_security_audit"] = audit_result
            
        except ImportError:
            # Validation module not available, skip security validation
            logger.warning("Security validation module not available")
        except Exception as e:
            logger.error("Configuration security validation failed", error=str(e))
            # Don't fail startup for validation errors, but log them
        
        return values
    
    def get_security_audit(self) -> Optional[Dict[str, Any]]:
        """Get the security audit results for this configuration."""
        return getattr(self, "_security_audit", None)


@lru_cache()
def get_settings() -> Settings:
    """Get application settings (cached)."""
    return Settings()