"""
Comprehensive Configuration Validation for Pattern Mining Service

This module provides security-focused validation for all 165+ configuration parameters
with input sanitization, injection prevention, and security constraint enforcement.
"""

import re
import ipaddress
import urllib.parse
from typing import Any, Dict, List, Optional, Union, Set, Tuple
from enum import Enum
from pathlib import Path
import structlog
from pydantic import validator, ValidationError
from datetime import datetime

logger = structlog.get_logger()


class SecurityLevel(str, Enum):
    """Security classification levels for configuration parameters."""
    PUBLIC = "public"          # Safe to log and expose
    INTERNAL = "internal"      # Internal use, can log but not expose
    SENSITIVE = "sensitive"    # Should not be logged or exposed
    SECRET = "secret"          # Must be encrypted, never logged


class ValidationSeverity(str, Enum):
    """Validation error severity levels."""
    INFO = "info"
    WARNING = "warning"  
    ERROR = "error"
    CRITICAL = "critical"


class ConfigurationViolation:
    """Represents a configuration security violation."""
    
    def __init__(
        self,
        parameter: str,
        violation_type: str,
        severity: ValidationSeverity,
        message: str,
        suggested_fix: Optional[str] = None,
        security_impact: Optional[str] = None
    ):
        self.parameter = parameter
        self.violation_type = violation_type
        self.severity = severity
        self.message = message
        self.suggested_fix = suggested_fix
        self.security_impact = security_impact
        self.timestamp = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert violation to dictionary."""
        return {
            "parameter": self.parameter,
            "violation_type": self.violation_type,
            "severity": self.severity.value,
            "message": self.message,
            "suggested_fix": self.suggested_fix,
            "security_impact": self.security_impact,
            "timestamp": self.timestamp.isoformat()
        }


class ParameterClassification:
    """Security classification for configuration parameters."""
    
    # Security level mapping for all 165+ parameters
    PARAMETER_SECURITY_LEVELS = {
        # Application Settings (PUBLIC)
        "app_name": SecurityLevel.PUBLIC,
        "app_version": SecurityLevel.PUBLIC,
        "environment": SecurityLevel.PUBLIC,
        "debug": SecurityLevel.INTERNAL,
        "host": SecurityLevel.INTERNAL,
        "port": SecurityLevel.INTERNAL,
        "workers": SecurityLevel.INTERNAL,
        
        # Security Settings (SECRET/SENSITIVE)
        "secret_key": SecurityLevel.SECRET,
        "jwt_secret": SecurityLevel.SECRET,
        "cors_origins": SecurityLevel.SENSITIVE,
        "rate_limit_per_minute": SecurityLevel.INTERNAL,
        "rate_limit_burst": SecurityLevel.INTERNAL,
        
        # Database Settings (SECRET/SENSITIVE)
        "database_url": SecurityLevel.SECRET,
        "database_pool_size": SecurityLevel.INTERNAL,
        "database_max_overflow": SecurityLevel.INTERNAL,
        "database_echo": SecurityLevel.SENSITIVE,
        
        # Redis Settings (SECRET/SENSITIVE)
        "redis_url": SecurityLevel.SECRET,
        "redis_password": SecurityLevel.SECRET,
        "redis_max_connections": SecurityLevel.INTERNAL,
        "redis_connection_timeout": SecurityLevel.INTERNAL,
        "redis_socket_timeout": SecurityLevel.INTERNAL,
        "redis_ssl": SecurityLevel.SENSITIVE,
        "redis_ssl_ca_certs": SecurityLevel.SENSITIVE,
        "redis_cluster_mode": SecurityLevel.INTERNAL,
        "redis_sentinel_mode": SecurityLevel.INTERNAL,
        "redis_sentinel_password": SecurityLevel.SECRET,
        
        # GCP Settings (SECRET/SENSITIVE)
        "gcp_project_id": SecurityLevel.SENSITIVE,
        "gcp_location": SecurityLevel.INTERNAL,
        "google_application_credentials": SecurityLevel.SECRET,
        "bigquery_dataset_id": SecurityLevel.SENSITIVE,
        "bigquery_max_bytes_billed": SecurityLevel.SENSITIVE,
        
        # Gemini API Settings (SECRET)
        "gemini_api_key": SecurityLevel.SECRET,
        "gemini_model": SecurityLevel.INTERNAL,
        "gemini_requests_per_minute": SecurityLevel.INTERNAL,
        "gemini_tokens_per_minute": SecurityLevel.INTERNAL,
        
        # ML Settings (INTERNAL)
        "ml_model_storage_path": SecurityLevel.SENSITIVE,
        "ml_batch_size": SecurityLevel.INTERNAL,
        "embedding_model": SecurityLevel.INTERNAL,
        "gpu_enabled": SecurityLevel.INTERNAL,
        
        # Cache Settings (INTERNAL)
        "cache_ttl": SecurityLevel.INTERNAL,
        "cache_max_size": SecurityLevel.INTERNAL,
        "cache_l1_max_size": SecurityLevel.INTERNAL,
        "cache_compression": SecurityLevel.INTERNAL,
        
        # Monitoring Settings (SENSITIVE)
        "enable_metrics": SecurityLevel.INTERNAL,
        "metrics_port": SecurityLevel.SENSITIVE,
        "jaeger_endpoint": SecurityLevel.SENSITIVE,
        "log_level": SecurityLevel.INTERNAL,
    }
    
    @classmethod
    def get_security_level(cls, parameter: str) -> SecurityLevel:
        """Get security level for a parameter."""
        return cls.PARAMETER_SECURITY_LEVELS.get(parameter.lower(), SecurityLevel.INTERNAL)
    
    @classmethod
    def is_secret(cls, parameter: str) -> bool:
        """Check if parameter contains secrets."""
        return cls.get_security_level(parameter) == SecurityLevel.SECRET
    
    @classmethod
    def is_sensitive(cls, parameter: str) -> bool:
        """Check if parameter is sensitive."""
        level = cls.get_security_level(parameter)
        return level in [SecurityLevel.SECRET, SecurityLevel.SENSITIVE]


class SecurityValidator:
    """Comprehensive security validator for configuration parameters."""
    
    def __init__(self):
        # Dangerous patterns that should never appear in configuration
        self.dangerous_patterns = [
            # SQL injection patterns
            r"(?i)(?:union\s+select|or\s+1\s*=\s*1|drop\s+table|delete\s+from)",
            
            # Command injection patterns  
            r"(?i)(?:[;&|`]\s*(?:rm|cat|ls|ps|kill|wget|curl|nc|netcat))",
            r"\$\([^)]+\)|`[^`]+`",
            
            # XSS patterns
            r"(?i)<script[^>]*>|javascript:|on(?:load|error|click)\s*=",
            
            # Path traversal
            r"\.\.[\\/]|\.\.\\\\",
            
            # Code injection
            r"(?i)(?:eval|exec|compile|import|__import__)\s*\(",
            
            # Suspicious protocols
            r"(?i)(?:file|ftp|ldap|gopher|dict|telnet)://",
        ]
        
        self.compiled_patterns = [
            re.compile(pattern, re.IGNORECASE | re.MULTILINE)
            for pattern in self.dangerous_patterns
        ]
        
        # Valid environment names
        self.valid_environments = {"development", "staging", "production", "test"}
        
        # Valid log levels
        self.valid_log_levels = {"DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"}
        
        # Port range limits
        self.min_port = 1024
        self.max_port = 65535
        
        # Security constraints
        self.min_secret_length = 32
        self.max_config_value_length = 10000  # Prevent DoS
        
    def validate_all_parameters(self, config_dict: Dict[str, Any]) -> Tuple[bool, List[ConfigurationViolation]]:
        """
        Validate all configuration parameters for security issues.
        
        Returns:
            Tuple of (is_valid, violations_list)
        """
        violations = []
        
        for param_name, param_value in config_dict.items():
            param_violations = self.validate_parameter(param_name, param_value)
            violations.extend(param_violations)
        
        # Check for critical security violations
        critical_violations = [v for v in violations if v.severity == ValidationSeverity.CRITICAL]
        is_valid = len(critical_violations) == 0
        
        return is_valid, violations
    
    def validate_parameter(self, param_name: str, param_value: Any) -> List[ConfigurationViolation]:
        """Validate a single configuration parameter."""
        violations = []
        
        if param_value is None:
            return violations
            
        param_str = str(param_value)
        security_level = ParameterClassification.get_security_level(param_name)
        
        # 1. Check for dangerous patterns
        violations.extend(self._check_dangerous_patterns(param_name, param_str))
        
        # 2. Check parameter-specific validation
        violations.extend(self._validate_by_type(param_name, param_value, security_level))
        
        # 3. Check for security-specific constraints
        violations.extend(self._check_security_constraints(param_name, param_str, security_level))
        
        # 4. Check length constraints
        violations.extend(self._check_length_constraints(param_name, param_str))
        
        return violations
    
    def _check_dangerous_patterns(self, param_name: str, param_value: str) -> List[ConfigurationViolation]:
        """Check for dangerous injection patterns."""
        violations = []
        
        for pattern in self.compiled_patterns:
            if pattern.search(param_value):
                violations.append(ConfigurationViolation(
                    parameter=param_name,
                    violation_type="dangerous_pattern",
                    severity=ValidationSeverity.CRITICAL,
                    message=f"Dangerous pattern detected in parameter value",
                    suggested_fix="Remove suspicious content and validate input",
                    security_impact="Potential injection attack vector"
                ))
        
        return violations
    
    def _validate_by_type(self, param_name: str, param_value: Any, security_level: SecurityLevel) -> List[ConfigurationViolation]:
        """Validate parameter based on its type and name."""
        violations = []
        param_lower = param_name.lower()
        
        # URL validation
        if "url" in param_lower:
            violations.extend(self._validate_url(param_name, str(param_value)))
        
        # Port validation
        elif "port" in param_lower:
            violations.extend(self._validate_port(param_name, param_value))
        
        # Environment validation
        elif param_lower == "environment":
            violations.extend(self._validate_environment(param_name, str(param_value)))
        
        # Log level validation
        elif "log_level" in param_lower:
            violations.extend(self._validate_log_level(param_name, str(param_value)))
        
        # Path validation
        elif "path" in param_lower or "file" in param_lower:
            violations.extend(self._validate_path(param_name, str(param_value)))
        
        # API key validation
        elif "api_key" in param_lower or "secret" in param_lower:
            violations.extend(self._validate_secret(param_name, str(param_value)))
        
        # Boolean validation
        elif param_lower.startswith(("enable_", "is_", "has_", "use_")):
            violations.extend(self._validate_boolean(param_name, param_value))
        
        # Numeric validation for size/count parameters
        elif any(word in param_lower for word in ["size", "count", "max", "min", "limit"]):
            violations.extend(self._validate_numeric_limit(param_name, param_value))
        
        return violations
    
    def _validate_url(self, param_name: str, url: str) -> List[ConfigurationViolation]:
        """Validate URL parameters."""
        violations = []
        
        try:
            parsed = urllib.parse.urlparse(url)
            
            # Check for dangerous schemes
            dangerous_schemes = {"file", "ftp", "ldap", "gopher", "dict", "telnet"}
            if parsed.scheme.lower() in dangerous_schemes:
                violations.append(ConfigurationViolation(
                    parameter=param_name,
                    violation_type="dangerous_scheme",
                    severity=ValidationSeverity.ERROR,
                    message=f"Dangerous URL scheme: {parsed.scheme}",
                    suggested_fix="Use http/https for external URLs",
                    security_impact="Potential SSRF or file disclosure"
                ))
            
            # Check for private IP addresses in external URLs
            if parsed.hostname:
                try:
                    ip = ipaddress.ip_address(parsed.hostname)
                    if ip.is_private and "localhost" not in url and "127.0.0.1" not in url:
                        violations.append(ConfigurationViolation(
                            parameter=param_name,
                            violation_type="private_ip",
                            severity=ValidationSeverity.WARNING,
                            message=f"Private IP address in URL: {parsed.hostname}",
                            suggested_fix="Use public endpoints or validate network access",
                            security_impact="Potential SSRF to internal services"
                        ))
                except ValueError:
                    pass  # Not an IP address
            
        except Exception:
            violations.append(ConfigurationViolation(
                parameter=param_name,
                violation_type="invalid_url",
                severity=ValidationSeverity.ERROR,
                message="Invalid URL format",
                suggested_fix="Provide a valid URL",
                security_impact="Configuration parsing errors"
            ))
        
        return violations
    
    def _validate_port(self, param_name: str, port: Any) -> List[ConfigurationViolation]:
        """Validate port numbers."""
        violations = []
        
        try:
            port_num = int(port)
            
            if port_num < self.min_port or port_num > self.max_port:
                violations.append(ConfigurationViolation(
                    parameter=param_name,
                    violation_type="invalid_port_range",
                    severity=ValidationSeverity.ERROR,
                    message=f"Port {port_num} outside safe range {self.min_port}-{self.max_port}",
                    suggested_fix=f"Use port between {self.min_port} and {self.max_port}",
                    security_impact="Potential service binding to privileged ports"
                ))
            
            # Check for commonly attacked ports
            dangerous_ports = {21, 22, 23, 25, 53, 135, 139, 445, 1433, 3389}
            if port_num in dangerous_ports:
                violations.append(ConfigurationViolation(
                    parameter=param_name,
                    violation_type="dangerous_port",
                    severity=ValidationSeverity.WARNING,
                    message=f"Port {port_num} is commonly targeted by attackers",
                    suggested_fix="Consider using a different port if possible",
                    security_impact="Increased attack surface"
                ))
                
        except (ValueError, TypeError):
            violations.append(ConfigurationViolation(
                parameter=param_name,
                violation_type="invalid_port_format",
                severity=ValidationSeverity.ERROR,
                message="Port must be a valid integer",
                suggested_fix="Provide a numeric port value",
                security_impact="Configuration parsing errors"
            ))
        
        return violations
    
    def _validate_environment(self, param_name: str, environment: str) -> List[ConfigurationViolation]:
        """Validate environment parameter."""
        violations = []
        
        if environment.lower() not in self.valid_environments:
            violations.append(ConfigurationViolation(
                parameter=param_name,
                violation_type="invalid_environment",
                severity=ValidationSeverity.ERROR,
                message=f"Invalid environment: {environment}",
                suggested_fix=f"Use one of: {', '.join(self.valid_environments)}",
                security_impact="Incorrect security configurations may be applied"
            ))
        
        return violations
    
    def _validate_log_level(self, param_name: str, log_level: str) -> List[ConfigurationViolation]:
        """Validate log level parameter."""
        violations = []
        
        if log_level.upper() not in self.valid_log_levels:
            violations.append(ConfigurationViolation(
                parameter=param_name,
                violation_type="invalid_log_level",
                severity=ValidationSeverity.ERROR,
                message=f"Invalid log level: {log_level}",
                suggested_fix=f"Use one of: {', '.join(self.valid_log_levels)}",
                security_impact="Potential information disclosure or logging bypass"
            ))
        
        return violations
    
    def _validate_path(self, param_name: str, path: str) -> List[ConfigurationViolation]:
        """Validate file path parameters."""
        violations = []
        
        # Check for path traversal
        if ".." in path or "\\" in path:
            violations.append(ConfigurationViolation(
                parameter=param_name,
                violation_type="path_traversal",
                severity=ValidationSeverity.CRITICAL,
                message="Path traversal detected in file path",
                suggested_fix="Use absolute paths without .. sequences",
                security_impact="Potential file system access outside allowed directories"
            ))
        
        # Check for dangerous paths
        dangerous_paths = {"/etc/passwd", "/etc/shadow", "/etc/hosts", "/proc", "/sys"}
        if any(dangerous in path.lower() for dangerous in dangerous_paths):
            violations.append(ConfigurationViolation(
                parameter=param_name,
                violation_type="dangerous_path",
                severity=ValidationSeverity.ERROR,
                message="Path points to sensitive system location",
                suggested_fix="Use application-specific directories",
                security_impact="Potential access to sensitive system files"
            ))
        
        return violations
    
    def _validate_secret(self, param_name: str, secret: str) -> List[ConfigurationViolation]:
        """Validate secret parameters."""
        violations = []
        
        # Check minimum length
        if len(secret) < self.min_secret_length:
            violations.append(ConfigurationViolation(
                parameter=param_name,
                violation_type="weak_secret",
                severity=ValidationSeverity.CRITICAL,
                message=f"Secret too short (minimum {self.min_secret_length} characters)",
                suggested_fix=f"Use at least {self.min_secret_length} random characters",
                security_impact="Weak secrets are vulnerable to brute force attacks"
            ))
        
        # Check for common weak patterns
        weak_patterns = [
            r"^(password|secret|key|token)\d*$",
            r"^(admin|user|test|demo)\d*$",
            r"^(123|abc|qwe)",
            r"(.)\1{4,}",  # Repeated characters
        ]
        
        for pattern in weak_patterns:
            if re.search(pattern, secret, re.IGNORECASE):
                violations.append(ConfigurationViolation(
                    parameter=param_name,
                    violation_type="predictable_secret",
                    severity=ValidationSeverity.ERROR,
                    message="Secret uses predictable pattern",
                    suggested_fix="Use cryptographically random secrets",
                    security_impact="Predictable secrets can be easily guessed"
                ))
                break
        
        return violations
    
    def _validate_boolean(self, param_name: str, value: Any) -> List[ConfigurationViolation]:
        """Validate boolean parameters."""
        violations = []
        
        valid_bool_strings = {"true", "false", "1", "0", "yes", "no", "on", "off"}
        value_str = str(value).lower()
        
        if not isinstance(value, bool) and value_str not in valid_bool_strings:
            violations.append(ConfigurationViolation(
                parameter=param_name,
                violation_type="invalid_boolean",
                severity=ValidationSeverity.ERROR,
                message=f"Invalid boolean value: {value}",
                suggested_fix="Use true/false or 1/0",
                security_impact="Configuration parsing errors"
            ))
        
        return violations
    
    def _validate_numeric_limit(self, param_name: str, value: Any) -> List[ConfigurationViolation]:
        """Validate numeric limit parameters."""
        violations = []
        
        try:
            num_value = float(value)
            
            # Check for reasonable limits
            if "size" in param_name.lower() and num_value < 0:
                violations.append(ConfigurationViolation(
                    parameter=param_name,
                    violation_type="negative_size",
                    severity=ValidationSeverity.ERROR,
                    message="Size parameters cannot be negative",
                    suggested_fix="Use positive values for size parameters",
                    security_impact="Potential resource allocation errors"
                ))
            
            # Check for extremely large values that could cause DoS
            if num_value > 2**31:  # 2GB limit for most size parameters
                violations.append(ConfigurationViolation(
                    parameter=param_name,
                    violation_type="excessive_limit",
                    severity=ValidationSeverity.WARNING,
                    message="Extremely large value may cause resource exhaustion",
                    suggested_fix="Use reasonable limits based on system capacity",
                    security_impact="Potential denial of service"
                ))
                
        except (ValueError, TypeError):
            violations.append(ConfigurationViolation(
                parameter=param_name,
                violation_type="invalid_numeric",
                severity=ValidationSeverity.ERROR,
                message="Parameter must be a valid number",
                suggested_fix="Provide a numeric value",
                security_impact="Configuration parsing errors"
            ))
        
        return violations
    
    def _check_security_constraints(self, param_name: str, param_value: str, security_level: SecurityLevel) -> List[ConfigurationViolation]:
        """Check security-specific constraints."""
        violations = []
        
        # Check for secrets in public/internal parameters
        if security_level in [SecurityLevel.PUBLIC, SecurityLevel.INTERNAL]:
            if self._contains_potential_secret(param_value):
                violations.append(ConfigurationViolation(
                    parameter=param_name,
                    violation_type="secret_in_public_param",
                    severity=ValidationSeverity.CRITICAL,
                    message="Potential secret found in non-secret parameter",
                    suggested_fix="Move sensitive data to secret management",
                    security_impact="Secret exposure in logs or configuration dumps"
                ))
        
        return violations
    
    def _check_length_constraints(self, param_name: str, param_value: str) -> List[ConfigurationViolation]:
        """Check parameter length constraints."""
        violations = []
        
        if len(param_value) > self.max_config_value_length:
            violations.append(ConfigurationViolation(
                parameter=param_name,
                violation_type="excessive_length",
                severity=ValidationSeverity.ERROR,
                message=f"Parameter value too long (>{self.max_config_value_length} chars)",
                suggested_fix="Reduce parameter value length",
                security_impact="Potential denial of service or buffer overflow"
            ))
        
        return violations
    
    def _contains_potential_secret(self, value: str) -> bool:
        """Check if value contains potential secret material."""
        # Check for patterns that look like API keys, tokens, etc.
        secret_patterns = [
            r"(?i)(?:api[_-]?key|secret|token|password|passwd|pwd).*[a-zA-Z0-9]{16,}",
            r"[A-Za-z0-9+/]{32,}={0,2}",  # Base64-like strings
            r"[A-Fa-f0-9]{32,}",  # Hex strings
            r"(?i)AIza[A-Za-z0-9_-]{35}",  # Google API key pattern
            r"(?i)sk-[A-Za-z0-9]{48}",  # OpenAI API key pattern
        ]
        
        for pattern in secret_patterns:
            if re.search(pattern, value):
                return True
        
        return False


class ConfigurationAuditor:
    """Audits configuration changes and maintains security compliance."""
    
    def __init__(self):
        self.validator = SecurityValidator()
        self.audit_log: List[Dict[str, Any]] = []
    
    def audit_configuration(self, config_dict: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform complete security audit of configuration.
        
        Returns:
            Audit report with findings and recommendations
        """
        is_valid, violations = self.validator.validate_all_parameters(config_dict)
        
        # Categorize violations by severity
        critical_violations = [v for v in violations if v.severity == ValidationSeverity.CRITICAL]
        error_violations = [v for v in violations if v.severity == ValidationSeverity.ERROR]
        warning_violations = [v for v in violations if v.severity == ValidationSeverity.WARNING]
        
        # Calculate security score (0-100)
        total_params = len(config_dict)
        violation_count = len(violations)
        security_score = max(0, 100 - (violation_count * 10) - (len(critical_violations) * 20))
        
        # Generate recommendations
        recommendations = self._generate_recommendations(violations)
        
        audit_report = {
            "audit_timestamp": datetime.utcnow().isoformat(),
            "is_secure": is_valid and len(critical_violations) == 0,
            "security_score": security_score,
            "total_parameters": total_params,
            "violation_summary": {
                "critical": len(critical_violations),
                "error": len(error_violations),
                "warning": len(warning_violations),
                "total": len(violations)
            },
            "violations": [v.to_dict() for v in violations],
            "recommendations": recommendations,
            "compliance_status": self._check_compliance(violations)
        }
        
        # Log audit
        audit_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "parameters_audited": total_params,
            "violations_found": len(violations),
            "security_score": security_score
        }
        self.audit_log.append(audit_entry)
        
        logger.info(
            "Configuration audit completed",
            total_parameters=total_params,
            violations=len(violations),
            security_score=security_score
        )
        
        return audit_report
    
    def _generate_recommendations(self, violations: List[ConfigurationViolation]) -> List[str]:
        """Generate security recommendations based on violations."""
        recommendations = []
        
        violation_types = {v.violation_type for v in violations}
        
        if "weak_secret" in violation_types:
            recommendations.append("Generate cryptographically strong secrets using secure random functions")
        
        if "dangerous_pattern" in violation_types:
            recommendations.append("Review configuration for injection attack patterns and sanitize inputs")
        
        if "secret_in_public_param" in violation_types:
            recommendations.append("Move sensitive data to proper secret management systems")
        
        if "invalid_url" in violation_types:
            recommendations.append("Validate all URL configurations and use secure protocols")
        
        if "dangerous_port" in violation_types:
            recommendations.append("Review port assignments and avoid commonly targeted ports")
        
        # Add general recommendations
        if violations:
            recommendations.extend([
                "Implement configuration parameter access controls",
                "Enable configuration change audit logging",
                "Use environment-specific configuration validation",
                "Regularly rotate secrets and API keys",
                "Monitor configuration for suspicious changes"
            ])
        
        return recommendations
    
    def _check_compliance(self, violations: List[ConfigurationViolation]) -> Dict[str, bool]:
        """Check compliance with security standards."""
        critical_violations = [v for v in violations if v.severity == ValidationSeverity.CRITICAL]
        
        return {
            "secure_secrets": not any(v.violation_type in ["weak_secret", "predictable_secret"] for v in violations),
            "no_injection_vectors": not any(v.violation_type == "dangerous_pattern" for v in violations),
            "proper_access_controls": len(critical_violations) == 0,
            "audit_ready": True,  # Audit logging is implemented
            "encryption_compliant": not any(v.violation_type == "secret_in_public_param" for v in violations)
        }


# Global validator instance
_config_validator: Optional[SecurityValidator] = None
_config_auditor: Optional[ConfigurationAuditor] = None


def get_config_validator() -> SecurityValidator:
    """Get global configuration validator instance."""
    global _config_validator
    if _config_validator is None:
        _config_validator = SecurityValidator()
    return _config_validator


def get_config_auditor() -> ConfigurationAuditor:
    """Get global configuration auditor instance."""
    global _config_auditor
    if _config_auditor is None:
        _config_auditor = ConfigurationAuditor()
    return _config_auditor


def validate_config_dict(config_dict: Dict[str, Any]) -> Tuple[bool, List[ConfigurationViolation]]:
    """Convenience function to validate a configuration dictionary."""
    validator = get_config_validator()
    return validator.validate_all_parameters(config_dict)


def audit_config_dict(config_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Convenience function to audit a configuration dictionary."""
    auditor = get_config_auditor()
    return auditor.audit_configuration(config_dict)