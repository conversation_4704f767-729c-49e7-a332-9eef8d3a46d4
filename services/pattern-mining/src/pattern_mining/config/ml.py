"""
ML Configuration

Machine learning configuration and model settings.
"""

from pydantic import Field, validator
from pydantic_settings import BaseSettings
from typing import Dict, Any, List, Optional
from enum import Enum
import os


class MLFramework(str, Enum):
    """Supported ML frameworks."""
    
    PYTORCH = "pytorch"
    TENSORFLOW = "tensorflow"
    SCIKIT_LEARN = "scikit_learn"
    TRANSFORMERS = "transformers"


class DeviceType(str, Enum):
    """Device types for ML computation."""
    
    CPU = "cpu"
    GPU = "gpu"
    TPU = "tpu"
    AUTO = "auto"


class MLConfig(BaseSettings):
    """ML configuration."""
    
    # Model settings
    model_storage_path: str = Field(default="/tmp/models", description="Model storage path")
    model_cache_size: int = Field(default=5, description="Number of models to cache")
    model_timeout: int = Field(default=300, description="Model loading timeout in seconds")
    
    # Training settings
    default_batch_size: int = Field(default=32, description="Default batch size")
    default_learning_rate: float = Field(default=0.001, description="Default learning rate")
    default_epochs: int = Field(default=100, description="Default number of epochs")
    early_stopping_patience: int = Field(default=10, description="Early stopping patience")
    
    # Inference settings
    inference_batch_size: int = Field(default=64, description="Inference batch size")
    max_sequence_length: int = Field(default=512, description="Maximum sequence length")
    inference_timeout: int = Field(default=30, description="Inference timeout in seconds")
    
    # Hardware settings
    device_type: DeviceType = Field(default=DeviceType.AUTO, description="Device type")
    num_workers: int = Field(default=4, description="Number of data loading workers")
    mixed_precision: bool = Field(default=True, description="Use mixed precision training")
    
    # Distributed settings
    enable_distributed: bool = Field(default=False, description="Enable distributed training")
    world_size: int = Field(default=1, description="World size for distributed training")
    
    # Monitoring settings
    enable_wandb: bool = Field(default=False, description="Enable Weights & Biases")
    enable_tensorboard: bool = Field(default=True, description="Enable TensorBoard")
    log_interval: int = Field(default=10, description="Logging interval")
    
    # Model-specific settings
    transformer_models: Dict[str, str] = Field(
        default={
            "code_embeddings": "microsoft/codebert-base",
            "text_embeddings": "sentence-transformers/all-MiniLM-L6-v2",
            "pattern_classifier": "distilbert-base-uncased"
        },
        description="Transformer model configurations"
    )
    
    # Feature extraction settings
    max_ast_depth: int = Field(default=20, description="Maximum AST depth")
    max_tokens: int = Field(default=10000, description="Maximum number of tokens")
    enable_semantic_features: bool = Field(default=True, description="Enable semantic features")
    
    # Caching settings
    enable_feature_cache: bool = Field(default=True, description="Enable feature caching")
    cache_ttl: int = Field(default=3600, description="Cache TTL in seconds")
    
    # Security settings
    model_signature_verification: bool = Field(default=True, description="Verify model signatures")
    allowed_model_sources: List[str] = Field(
        default=["huggingface", "local", "gcs"],
        description="Allowed model sources"
    )
    
    class Config:
        """Pydantic configuration."""
        env_prefix = "ML_"
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    @validator('device_type')
    def validate_device_type(cls, v):
        """Validate device type."""
        return v
    
    @validator('model_storage_path')
    def validate_model_storage_path(cls, v):
        """Validate model storage path."""
        if not os.path.isabs(v):
            raise ValueError("Model storage path must be absolute")
        return v
    
    @validator('default_learning_rate')
    def validate_learning_rate(cls, v):
        """Validate learning rate."""
        if v <= 0 or v >= 1:
            raise ValueError("Learning rate must be between 0 and 1")
        return v
    
    @validator('allowed_model_sources')
    def validate_model_sources(cls, v):
        """Validate model sources."""
        valid_sources = ["huggingface", "local", "gcs", "s3"]
        for source in v:
            if source not in valid_sources:
                raise ValueError(f"Invalid model source: {source}")
        return v
    
    @property
    def pytorch_device(self) -> str:
        """Get PyTorch device string."""
        if self.device_type == DeviceType.AUTO:
            import torch
            return "cuda" if torch.cuda.is_available() else "cpu"
        elif self.device_type == DeviceType.GPU:
            return "cuda"
        elif self.device_type == DeviceType.TPU:
            return "xla"
        else:
            return "cpu"
    
    @property
    def tensorflow_device(self) -> str:
        """Get TensorFlow device string."""
        if self.device_type == DeviceType.AUTO:
            import tensorflow as tf
            return "/GPU:0" if tf.config.list_physical_devices('GPU') else "/CPU:0"
        elif self.device_type == DeviceType.GPU:
            return "/GPU:0"
        elif self.device_type == DeviceType.TPU:
            return "/TPU:0"
        else:
            return "/CPU:0"
    
    @property
    def training_config(self) -> Dict[str, Any]:
        """Get training configuration."""
        return {
            "batch_size": self.default_batch_size,
            "learning_rate": self.default_learning_rate,
            "epochs": self.default_epochs,
            "early_stopping_patience": self.early_stopping_patience,
            "mixed_precision": self.mixed_precision,
            "num_workers": self.num_workers,
            "device": self.pytorch_device
        }
    
    @property
    def inference_config(self) -> Dict[str, Any]:
        """Get inference configuration."""
        return {
            "batch_size": self.inference_batch_size,
            "max_sequence_length": self.max_sequence_length,
            "timeout": self.inference_timeout,
            "device": self.pytorch_device
        }
    
    def get_model_config(self, model_name: str) -> Dict[str, Any]:
        """Get configuration for a specific model."""
        base_config = {
            "model_name": model_name,
            "device": self.pytorch_device,
            "cache_dir": self.model_storage_path,
            "max_sequence_length": self.max_sequence_length
        }
        
        # Add model-specific configuration
        if model_name in self.transformer_models:
            base_config["model_path"] = self.transformer_models[model_name]
        
        return base_config


# Global ML configuration instance
_ml_config: Optional[MLConfig] = None


def get_ml_config() -> MLConfig:
    """Get ML configuration instance for Google models."""
    global _ml_config
    if _ml_config is None:
        _ml_config = MLConfig()
    return _ml_config


def reset_ml_config() -> None:
    """Reset ML configuration (useful for testing)."""
    global _ml_config
    _ml_config = None


def get_cost_estimate(task_type: str, input_tokens: int, output_tokens: int) -> float:
    """Estimate cost for Google model API calls."""
    # Gemini 2.5 Flash pricing (as of 2025)
    pricing = {
        "gemini-2.5-flash": {
            "input_tokens_per_million": 0.075,  # $0.075 per 1M input tokens
            "output_tokens_per_million": 0.30   # $0.30 per 1M output tokens
        },
        "gemini-2.0-flash-exp": {
            "input_tokens_per_million": 0.05,
            "output_tokens_per_million": 0.20
        },
        "text-embedding-004": {
            "input_tokens_per_million": 0.025,
            "output_tokens_per_million": 0.0  # No output tokens for embeddings
        }
    }
    
    config = get_ml_config()
    model_name = config.google_models.get(task_type, "gemini-2.5-flash")
    
    if model_name not in pricing:
        model_name = "gemini-2.5-flash"  # Default pricing
    
    model_pricing = pricing[model_name]
    
    input_cost = (input_tokens / 1_000_000) * model_pricing["input_tokens_per_million"]
    output_cost = (output_tokens / 1_000_000) * model_pricing["output_tokens_per_million"]
    
    return input_cost + output_cost