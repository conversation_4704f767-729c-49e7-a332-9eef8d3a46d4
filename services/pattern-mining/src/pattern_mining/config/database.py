"""
Database Configuration

Database connection and configuration management.
"""

from pydantic import Field, validator
from pydantic_settings import BaseSettings
from typing import Dict, Any, Optional
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import AsyncSession
import asyncio


class DatabaseConfig(BaseSettings):
    """Database configuration."""
    
    # Connection settings
    host: str = Field(..., description="Database host")
    port: int = Field(default=5432, description="Database port")
    database: str = Field(..., description="Database name")
    username: str = Field(..., description="Database username")
    password: str = Field(..., description="Database password")
    
    # Pool settings
    pool_size: int = Field(default=10, description="Connection pool size")
    max_overflow: int = Field(default=20, description="Max overflow connections")
    pool_timeout: int = Field(default=30, description="Pool timeout in seconds")
    pool_recycle: int = Field(default=3600, description="Pool recycle time in seconds")
    
    # SSL settings
    ssl_mode: str = Field(default="prefer", description="SSL mode")
    ssl_cert: Optional[str] = Field(None, description="SSL certificate path")
    ssl_key: Optional[str] = Field(None, description="SSL key path")
    ssl_ca: Optional[str] = Field(None, description="SSL CA path")
    
    # Query settings
    echo: bool = Field(default=False, description="Echo SQL queries")
    echo_pool: bool = Field(default=False, description="Echo pool events")
    
    class Config:
        """Pydantic configuration."""
        env_prefix = "DB_"
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    @validator('ssl_mode')
    def validate_ssl_mode(cls, v):
        """Validate SSL mode."""
        valid_modes = ["disable", "allow", "prefer", "require", "verify-ca", "verify-full"]
        if v not in valid_modes:
            raise ValueError(f"SSL mode must be one of {valid_modes}")
        return v
    
    @property
    def sync_url(self) -> str:
        """Get synchronous database URL."""
        return (
            f"postgresql://{self.username}:{self.password}@"
            f"{self.host}:{self.port}/{self.database}"
        )
    
    @property
    def async_url(self) -> str:
        """Get asynchronous database URL."""
        return (
            f"postgresql+asyncpg://{self.username}:{self.password}@"
            f"{self.host}:{self.port}/{self.database}"
        )
    
    @property
    def engine_kwargs(self) -> Dict[str, Any]:
        """Get SQLAlchemy engine kwargs."""
        kwargs = {
            "pool_size": self.pool_size,
            "max_overflow": self.max_overflow,
            "pool_timeout": self.pool_timeout,
            "pool_recycle": self.pool_recycle,
            "echo": self.echo,
            "echo_pool": self.echo_pool,
        }
        
        # Add SSL options if configured
        if self.ssl_cert:
            kwargs["connect_args"] = {
                "sslmode": self.ssl_mode,
                "sslcert": self.ssl_cert,
                "sslkey": self.ssl_key,
                "sslrootcert": self.ssl_ca,
            }
        
        return kwargs
    
    def create_sync_engine(self):
        """Create synchronous SQLAlchemy engine."""
        return create_engine(self.sync_url, **self.engine_kwargs)
    
    def create_async_engine(self):
        """Create asynchronous SQLAlchemy engine."""
        return create_async_engine(self.async_url, **self.engine_kwargs)
    
    def create_session_factory(self):
        """Create session factory for synchronous operations."""
        engine = self.create_sync_engine()
        return sessionmaker(bind=engine)
    
    def create_async_session_factory(self):
        """Create session factory for asynchronous operations."""
        engine = self.create_async_engine()
        return sessionmaker(
            bind=engine,
            class_=AsyncSession,
            expire_on_commit=False
        )


# Global database configuration instance
_db_config: Optional[DatabaseConfig] = None


def get_database_config() -> DatabaseConfig:
    """Get database configuration instance."""
    global _db_config
    if _db_config is None:
        _db_config = DatabaseConfig()
    return _db_config


async def get_database_status() -> bool:
    """Check database connectivity status."""
    try:
        config = get_database_config()
        engine = config.create_async_engine()
        
        async with engine.begin() as conn:
            await conn.execute("SELECT 1")
        
        await engine.dispose()
        return True
    except Exception:
        return False