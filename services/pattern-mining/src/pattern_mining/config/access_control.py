"""
Configuration Access Control and Audit Logging System

This module provides role-based access control for configuration parameters
and comprehensive audit logging for all configuration changes.
"""

import asyncio
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass, field
from enum import Enum
import structlog
import redis.asyncio as redis

from .validation import SecurityLevel, ParameterClassification

logger = structlog.get_logger()


class ConfigAction(str, Enum):
    """Configuration action types."""
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    EXPORT = "export"
    IMPORT = "import"
    AUDIT = "audit"


class ConfigRole(str, Enum):
    """Configuration access roles."""
    ADMIN = "admin"                    # Full access to all parameters
    SECURITY_ADMIN = "security_admin"  # Access to security-related parameters
    OPERATOR = "operator"              # Access to operational parameters
    DEVELOPER = "developer"            # Access to development parameters
    MONITOR = "monitor"                # Read-only access for monitoring
    SERVICE = "service"                # Service account access
    READONLY = "readonly"              # Read-only access to public parameters


@dataclass
class AccessPolicy:
    """Configuration access policy."""
    role: ConfigRole
    allowed_actions: Set[ConfigAction]
    parameter_patterns: List[str]  # Regex patterns for allowed parameters
    security_levels: Set[SecurityLevel]  # Allowed security levels
    time_restrictions: Optional[Dict[str, Any]] = None  # Time-based access
    ip_restrictions: Optional[List[str]] = None  # IP-based access
    conditions: Optional[Dict[str, Any]] = None  # Additional conditions


@dataclass
class ConfigAuditEntry:
    """Configuration audit log entry."""
    timestamp: datetime
    user_id: str
    user_role: ConfigRole
    action: ConfigAction
    parameter_name: str
    old_value: Optional[str]
    new_value: Optional[str]
    source_ip: Optional[str]
    user_agent: Optional[str]
    success: bool
    failure_reason: Optional[str] = None
    security_level: Optional[SecurityLevel] = None
    session_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert audit entry to dictionary."""
        return {
            "timestamp": self.timestamp.isoformat(),
            "user_id": self.user_id,
            "user_role": self.user_role.value,
            "action": self.action.value,
            "parameter_name": self.parameter_name,
            "old_value": self.old_value if self.security_level != SecurityLevel.SECRET else "[REDACTED]",
            "new_value": self.new_value if self.security_level != SecurityLevel.SECRET else "[REDACTED]",
            "source_ip": self.source_ip,
            "user_agent": self.user_agent,
            "success": self.success,
            "failure_reason": self.failure_reason,
            "security_level": self.security_level.value if self.security_level else None,
            "session_id": self.session_id
        }


class ConfigAccessController:
    """
    Role-based access control for configuration parameters.
    
    Provides:
    - Parameter-level access control
    - Role-based permissions
    - Time and IP-based restrictions
    - Comprehensive audit logging
    - Security policy enforcement
    """
    
    def __init__(self, redis_client: redis.Redis):
        self.redis_client = redis_client
        self.audit_log: List[ConfigAuditEntry] = []
        
        # Default access policies
        self.access_policies = {
            ConfigRole.ADMIN: AccessPolicy(
                role=ConfigRole.ADMIN,
                allowed_actions={ConfigAction.READ, ConfigAction.WRITE, ConfigAction.DELETE, 
                               ConfigAction.EXPORT, ConfigAction.IMPORT, ConfigAction.AUDIT},
                parameter_patterns=[".*"],  # All parameters
                security_levels={SecurityLevel.PUBLIC, SecurityLevel.INTERNAL, 
                               SecurityLevel.SENSITIVE, SecurityLevel.SECRET}
            ),
            
            ConfigRole.SECURITY_ADMIN: AccessPolicy(
                role=ConfigRole.SECURITY_ADMIN,
                allowed_actions={ConfigAction.READ, ConfigAction.WRITE, ConfigAction.AUDIT},
                parameter_patterns=[
                    r".*secret.*", r".*key.*", r".*password.*", r".*token.*",
                    r".*auth.*", r".*security.*", r".*ssl.*", r".*tls.*"
                ],
                security_levels={SecurityLevel.PUBLIC, SecurityLevel.INTERNAL, 
                               SecurityLevel.SENSITIVE, SecurityLevel.SECRET}
            ),
            
            ConfigRole.OPERATOR: AccessPolicy(
                role=ConfigRole.OPERATOR,
                allowed_actions={ConfigAction.READ, ConfigAction.WRITE},
                parameter_patterns=[
                    r".*pool.*", r".*timeout.*", r".*connection.*", r".*worker.*",
                    r".*cache.*", r".*batch.*", r".*limit.*", r".*size.*"
                ],
                security_levels={SecurityLevel.PUBLIC, SecurityLevel.INTERNAL}
            ),
            
            ConfigRole.DEVELOPER: AccessPolicy(
                role=ConfigRole.DEVELOPER,
                allowed_actions={ConfigAction.READ, ConfigAction.WRITE},
                parameter_patterns=[
                    r".*debug.*", r".*log.*", r".*dev.*", r".*test.*",
                    r".*hot_reload.*", r".*mock.*"
                ],
                security_levels={SecurityLevel.PUBLIC, SecurityLevel.INTERNAL}
            ),
            
            ConfigRole.MONITOR: AccessPolicy(
                role=ConfigRole.MONITOR,
                allowed_actions={ConfigAction.READ, ConfigAction.AUDIT},
                parameter_patterns=[".*"],
                security_levels={SecurityLevel.PUBLIC, SecurityLevel.INTERNAL}
            ),
            
            ConfigRole.SERVICE: AccessPolicy(
                role=ConfigRole.SERVICE,
                allowed_actions={ConfigAction.READ},
                parameter_patterns=[".*"],
                security_levels={SecurityLevel.PUBLIC, SecurityLevel.INTERNAL, SecurityLevel.SENSITIVE}
            ),
            
            ConfigRole.READONLY: AccessPolicy(
                role=ConfigRole.READONLY,
                allowed_actions={ConfigAction.READ},
                parameter_patterns=[".*"],
                security_levels={SecurityLevel.PUBLIC}
            )
        }
    
    async def check_access(
        self,
        user_id: str,
        user_role: ConfigRole,
        action: ConfigAction,
        parameter_name: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, Optional[str]]:
        """
        Check if user has access to perform action on parameter.
        
        Args:
            user_id: User identifier
            user_role: User's role
            action: Action to perform
            parameter_name: Configuration parameter name
            context: Additional context (IP, user agent, etc.)
            
        Returns:
            Tuple of (has_access, denial_reason)
        """
        try:
            # Get access policy for role
            policy = self.access_policies.get(user_role)
            if not policy:
                return False, f"No access policy defined for role: {user_role}"
            
            # Check if action is allowed
            if action not in policy.allowed_actions:
                return False, f"Action {action} not allowed for role {user_role}"
            
            # Check parameter pattern access
            import re
            pattern_match = False
            for pattern in policy.parameter_patterns:
                if re.match(pattern, parameter_name, re.IGNORECASE):
                    pattern_match = True
                    break
            
            if not pattern_match:
                return False, f"Parameter {parameter_name} not accessible for role {user_role}"
            
            # Check security level access
            param_security_level = ParameterClassification.get_security_level(parameter_name)
            if param_security_level not in policy.security_levels:
                return False, f"Security level {param_security_level} not allowed for role {user_role}"
            
            # Check time restrictions
            if policy.time_restrictions:
                access_allowed = await self._check_time_restrictions(policy.time_restrictions)
                if not access_allowed:
                    return False, "Access denied due to time restrictions"
            
            # Check IP restrictions
            if policy.ip_restrictions and context:
                source_ip = context.get("source_ip")
                if source_ip and source_ip not in policy.ip_restrictions:
                    return False, f"Access denied from IP: {source_ip}"
            
            # Check additional conditions
            if policy.conditions:
                conditions_met = await self._check_additional_conditions(
                    policy.conditions, user_id, context
                )
                if not conditions_met:
                    return False, "Access denied due to policy conditions"
            
            return True, None
            
        except Exception as e:
            logger.error("Access check failed", error=str(e))
            return False, f"Access check error: {str(e)}"
    
    async def log_access(
        self,
        user_id: str,
        user_role: ConfigRole,
        action: ConfigAction,
        parameter_name: str,
        success: bool,
        old_value: Optional[str] = None,
        new_value: Optional[str] = None,
        failure_reason: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log configuration access attempt."""
        try:
            security_level = ParameterClassification.get_security_level(parameter_name)
            
            audit_entry = ConfigAuditEntry(
                timestamp=datetime.utcnow(),
                user_id=user_id,
                user_role=user_role,
                action=action,
                parameter_name=parameter_name,
                old_value=old_value,
                new_value=new_value,
                source_ip=context.get("source_ip") if context else None,
                user_agent=context.get("user_agent") if context else None,
                success=success,
                failure_reason=failure_reason,
                security_level=security_level,
                session_id=context.get("session_id") if context else None
            )
            
            # Store in memory
            self.audit_log.append(audit_entry)
            
            # Store in Redis
            await self._store_audit_entry(audit_entry)
            
            # Log to structured logger
            logger.info(
                "Configuration access logged",
                user_id=user_id,
                user_role=user_role.value,
                action=action.value,
                parameter=parameter_name,
                success=success,
                security_level=security_level.value
            )
            
        except Exception as e:
            logger.error("Failed to log access", error=str(e))
    
    async def get_audit_log(
        self,
        user_id: Optional[str] = None,
        parameter_name: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Retrieve audit log entries with filtering."""
        try:
            # Build Redis query
            query_key = "config_audit:*"
            
            # Get all audit keys
            keys = await self.redis_client.keys(query_key)
            entries = []
            
            for key in keys[-limit:]:  # Get most recent entries
                entry_data = await self.redis_client.hgetall(key)
                if entry_data:
                    # Apply filters
                    if user_id and entry_data.get("user_id") != user_id:
                        continue
                    if parameter_name and entry_data.get("parameter_name") != parameter_name:
                        continue
                    
                    entry_time = datetime.fromisoformat(entry_data.get("timestamp", ""))
                    if start_time and entry_time < start_time:
                        continue
                    if end_time and entry_time > end_time:
                        continue
                    
                    entries.append(entry_data)
            
            # Sort by timestamp (most recent first)
            entries.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
            
            return entries
            
        except Exception as e:
            logger.error("Failed to retrieve audit log", error=str(e))
            return []
    
    async def get_access_summary(self, user_id: str, time_window_hours: int = 24) -> Dict[str, Any]:
        """Get access summary for a user."""
        try:
            start_time = datetime.utcnow() - timedelta(hours=time_window_hours)
            
            audit_entries = await self.get_audit_log(
                user_id=user_id,
                start_time=start_time,
                limit=1000
            )
            
            # Calculate summary statistics
            total_requests = len(audit_entries)
            successful_requests = sum(1 for entry in audit_entries if entry.get("success") == "True")
            failed_requests = total_requests - successful_requests
            
            # Count by action
            actions_count = {}
            for entry in audit_entries:
                action = entry.get("action", "unknown")
                actions_count[action] = actions_count.get(action, 0) + 1
            
            # Count by parameter
            parameters_count = {}
            for entry in audit_entries:
                param = entry.get("parameter_name", "unknown")
                parameters_count[param] = parameters_count.get(param, 0) + 1
            
            # Get top accessed parameters
            top_parameters = sorted(parameters_count.items(), key=lambda x: x[1], reverse=True)[:10]
            
            return {
                "user_id": user_id,
                "time_window_hours": time_window_hours,
                "summary": {
                    "total_requests": total_requests,
                    "successful_requests": successful_requests,
                    "failed_requests": failed_requests,
                    "success_rate": successful_requests / total_requests if total_requests > 0 else 0
                },
                "actions": actions_count,
                "top_parameters": dict(top_parameters)
            }
            
        except Exception as e:
            logger.error("Failed to get access summary", error=str(e))
            return {"error": str(e)}
    
    async def detect_suspicious_activity(self) -> List[Dict[str, Any]]:
        """Detect suspicious configuration access patterns."""
        try:
            suspicious_activities = []
            
            # Check for rapid failed access attempts
            recent_time = datetime.utcnow() - timedelta(minutes=15)
            recent_entries = await self.get_audit_log(start_time=recent_time, limit=500)
            
            # Group by user_id
            user_failures = {}
            for entry in recent_entries:
                if entry.get("success") == "False":
                    user_id = entry.get("user_id")
                    user_failures[user_id] = user_failures.get(user_id, 0) + 1
            
            # Check for users with many failed attempts
            for user_id, failure_count in user_failures.items():
                if failure_count >= 10:  # 10 failures in 15 minutes
                    suspicious_activities.append({
                        "type": "excessive_failures",
                        "user_id": user_id,
                        "failure_count": failure_count,
                        "time_window": "15 minutes",
                        "severity": "high"
                    })
            
            # Check for unusual parameter access patterns
            param_access = {}
            for entry in recent_entries:
                param = entry.get("parameter_name")
                security_level = entry.get("security_level")
                if security_level in ["secret", "sensitive"]:
                    param_access[param] = param_access.get(param, 0) + 1
            
            # Check for excessive secret access
            for param, access_count in param_access.items():
                if access_count >= 5:  # 5 accesses to secrets in 15 minutes
                    suspicious_activities.append({
                        "type": "excessive_secret_access",
                        "parameter": param,
                        "access_count": access_count,
                        "time_window": "15 minutes",
                        "severity": "medium"
                    })
            
            return suspicious_activities
            
        except Exception as e:
            logger.error("Failed to detect suspicious activity", error=str(e))
            return []
    
    async def _check_time_restrictions(self, time_restrictions: Dict[str, Any]) -> bool:
        """Check time-based access restrictions."""
        current_time = datetime.utcnow()
        
        # Check allowed hours
        if "allowed_hours" in time_restrictions:
            allowed_hours = time_restrictions["allowed_hours"]
            if current_time.hour not in allowed_hours:
                return False
        
        # Check allowed days
        if "allowed_days" in time_restrictions:
            allowed_days = time_restrictions["allowed_days"]
            if current_time.weekday() not in allowed_days:
                return False
        
        return True
    
    async def _check_additional_conditions(
        self,
        conditions: Dict[str, Any],
        user_id: str,
        context: Optional[Dict[str, Any]]
    ) -> bool:
        """Check additional access conditions."""
        # Check session requirements
        if conditions.get("require_session") and not context.get("session_id"):
            return False
        
        # Check recent activity requirements
        if "max_requests_per_hour" in conditions:
            max_requests = conditions["max_requests_per_hour"]
            recent_time = datetime.utcnow() - timedelta(hours=1)
            recent_entries = await self.get_audit_log(
                user_id=user_id,
                start_time=recent_time,
                limit=max_requests + 1
            )
            if len(recent_entries) >= max_requests:
                return False
        
        return True
    
    async def _store_audit_entry(self, entry: ConfigAuditEntry) -> None:
        """Store audit entry in Redis."""
        try:
            # Generate unique key
            key_data = f"{entry.timestamp.isoformat()}:{entry.user_id}:{entry.parameter_name}"
            key_hash = hashlib.md5(key_data.encode()).hexdigest()
            redis_key = f"config_audit:{key_hash}"
            
            # Store entry
            await self.redis_client.hset(redis_key, mapping=entry.to_dict())
            
            # Set expiration (keep audit logs for 90 days)
            await self.redis_client.expire(redis_key, 90 * 24 * 3600)
            
        except Exception as e:
            logger.error("Failed to store audit entry", error=str(e))


class ConfigPermissionManager:
    """Manages configuration permissions and policy enforcement."""
    
    def __init__(self, access_controller: ConfigAccessController):
        self.access_controller = access_controller
        self.permission_cache: Dict[str, Tuple[bool, float]] = {}
        self.cache_ttl = 300  # 5 minutes
    
    async def has_permission(
        self,
        user_id: str,
        user_role: ConfigRole,
        action: ConfigAction,
        parameter_name: str,
        context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Check if user has permission for action."""
        # Check cache first
        cache_key = f"{user_id}:{user_role}:{action}:{parameter_name}"
        if cache_key in self.permission_cache:
            has_permission, timestamp = self.permission_cache[cache_key]
            if datetime.utcnow().timestamp() - timestamp < self.cache_ttl:
                return has_permission
        
        # Check access
        has_access, denial_reason = await self.access_controller.check_access(
            user_id, user_role, action, parameter_name, context
        )
        
        # Cache result
        self.permission_cache[cache_key] = (has_access, datetime.utcnow().timestamp())
        
        # Log access attempt
        await self.access_controller.log_access(
            user_id=user_id,
            user_role=user_role,
            action=action,
            parameter_name=parameter_name,
            success=has_access,
            failure_reason=denial_reason,
            context=context
        )
        
        return has_access
    
    def clear_permission_cache(self, user_id: Optional[str] = None) -> None:
        """Clear permission cache."""
        if user_id:
            # Clear cache for specific user
            keys_to_remove = [
                key for key in self.permission_cache.keys()
                if key.startswith(f"{user_id}:")
            ]
            for key in keys_to_remove:
                del self.permission_cache[key]
        else:
            # Clear all cache
            self.permission_cache.clear()


# Global instances
_access_controller: Optional[ConfigAccessController] = None
_permission_manager: Optional[ConfigPermissionManager] = None


def get_access_controller(redis_client: redis.Redis) -> ConfigAccessController:
    """Get global configuration access controller."""
    global _access_controller
    if _access_controller is None:
        _access_controller = ConfigAccessController(redis_client)
    return _access_controller


def get_permission_manager(redis_client: redis.Redis) -> ConfigPermissionManager:
    """Get global configuration permission manager."""
    global _permission_manager
    if _permission_manager is None:
        access_controller = get_access_controller(redis_client)
        _permission_manager = ConfigPermissionManager(access_controller)
    return _permission_manager