"""
Gemini API Configuration

Configuration for Google Gemini API integration including model parameters,
rate limiting, safety settings, and API key management.
"""

from pydantic import Field, validator, SecretStr
from pydantic_settings import BaseSettings
from typing import Dict, Any, List, Optional, Union
from enum import Enum
import os


class GeminiModel(str, Enum):
    """Supported Gemini models."""
    
    GEMINI_PRO = "gemini-pro"
    GEMINI_PRO_VISION = "gemini-pro-vision"
    GEMINI_2_5_PRO = "gemini-2.5-pro"
    GEMINI_2_5_PRO_LATEST = "gemini-2.5-pro-latest"
    GEMINI_2_5_FLASH = "gemini-2.5-flash"
    GEMINI_2_5_FLASH_LATEST = "gemini-2.5-flash-latest"


class SafetyLevel(str, Enum):
    """Safety filter levels."""
    
    BLOCK_NONE = "BLOCK_NONE"
    BLOCK_FEW = "BLOCK_FEW"
    BLOCK_SOME = "BLOCK_SOME"
    BLOCK_MOST = "BLOCK_MOST"


class SafetyCategory(str, Enum):
    """Safety categories."""
    
    HARASSMENT = "HARM_CATEGORY_HARASSMENT"
    HATE_SPEECH = "HARM_CATEGORY_HATE_SPEECH"
    SEXUALLY_EXPLICIT = "HARM_CATEGORY_SEXUALLY_EXPLICIT"
    DANGEROUS_CONTENT = "HARM_CATEGORY_DANGEROUS_CONTENT"


class GeminiConfig(BaseSettings):
    """Gemini API configuration."""
    
    # Authentication
    api_key: Optional[SecretStr] = Field(
        default=None,
        description="Gemini API key (use environment variable)"
    )
    
    # Model configuration
    default_model: GeminiModel = Field(
        default=GeminiModel.GEMINI_2_5_PRO,
        description="Default Gemini model"
    )
    
    # Generation parameters
    temperature: float = Field(
        default=0.1,
        ge=0.0,
        le=1.0,
        description="Temperature for generation (0.0 = deterministic)"
    )
    
    top_p: float = Field(
        default=0.95,
        ge=0.0,
        le=1.0,
        description="Top-p nucleus sampling"
    )
    
    top_k: int = Field(
        default=40,
        ge=1,
        le=100,
        description="Top-k sampling"
    )
    
    max_output_tokens: int = Field(
        default=8192,
        ge=1,
        le=1048576,
        description="Maximum output tokens (up to 1M context)"
    )
    
    # Context window
    context_window: int = Field(
        default=1000000,
        ge=1,
        le=2000000,
        description="Context window size (up to 2M tokens)"
    )
    
    # Rate limiting
    requests_per_minute: int = Field(
        default=60,
        ge=1,
        le=1000,
        description="Maximum requests per minute"
    )
    
    tokens_per_minute: int = Field(
        default=1000000,
        ge=1000,
        le=4000000,
        description="Maximum tokens per minute"
    )
    
    concurrent_requests: int = Field(
        default=10,
        ge=1,
        le=100,
        description="Maximum concurrent requests"
    )
    
    # Retry configuration
    max_retries: int = Field(
        default=3,
        ge=0,
        le=10,
        description="Maximum retry attempts"
    )
    
    retry_delay: float = Field(
        default=1.0,
        ge=0.1,
        le=60.0,
        description="Base retry delay in seconds"
    )
    
    retry_exponential_base: float = Field(
        default=2.0,
        ge=1.0,
        le=10.0,
        description="Exponential backoff base"
    )
    
    # Timeout configuration
    request_timeout: float = Field(
        default=60.0,
        ge=1.0,
        le=600.0,
        description="Request timeout in seconds"
    )
    
    connection_timeout: float = Field(
        default=10.0,
        ge=1.0,
        le=60.0,
        description="Connection timeout in seconds"
    )
    
    # Safety settings
    safety_settings: Dict[str, str] = Field(
        default={
            SafetyCategory.HARASSMENT: SafetyLevel.BLOCK_NONE,
            SafetyCategory.HATE_SPEECH: SafetyLevel.BLOCK_NONE,
            SafetyCategory.SEXUALLY_EXPLICIT: SafetyLevel.BLOCK_NONE,
            SafetyCategory.DANGEROUS_CONTENT: SafetyLevel.BLOCK_NONE,
        },
        description="Safety filter settings"
    )
    
    # Feature flags
    enable_caching: bool = Field(
        default=True,
        description="Enable response caching"
    )
    
    enable_streaming: bool = Field(
        default=False,
        description="Enable streaming responses"
    )
    
    enable_function_calling: bool = Field(
        default=True,
        description="Enable function calling"
    )
    
    enable_json_mode: bool = Field(
        default=True,
        description="Enable JSON response mode"
    )
    
    # Caching configuration
    cache_ttl: int = Field(
        default=3600,
        ge=60,
        le=86400,
        description="Cache TTL in seconds"
    )
    
    cache_max_size: int = Field(
        default=1000,
        ge=10,
        le=10000,
        description="Maximum cache entries"
    )
    
    # Embedding configuration
    embedding_model: str = Field(
        default="models/embedding-001",
        description="Embedding model name"
    )
    
    embedding_dimension: int = Field(
        default=768,
        ge=256,
        le=4096,
        description="Embedding dimension"
    )
    
    embedding_batch_size: int = Field(
        default=100,
        ge=1,
        le=1000,
        description="Embedding batch size"
    )
    
    # Pattern analysis configuration
    pattern_analysis_model: GeminiModel = Field(
        default=GeminiModel.GEMINI_2_5_PRO,
        description="Model for pattern analysis"
    )
    
    code_explanation_model: GeminiModel = Field(
        default=GeminiModel.GEMINI_2_5_PRO,
        description="Model for code explanation"
    )
    
    anti_pattern_detection_model: GeminiModel = Field(
        default=GeminiModel.GEMINI_2_5_PRO,
        description="Model for anti-pattern detection"
    )
    
    # Performance optimization
    enable_batch_processing: bool = Field(
        default=True,
        description="Enable batch processing"
    )
    
    batch_size: int = Field(
        default=10,
        ge=1,
        le=100,
        description="Batch size for processing"
    )
    
    enable_parallel_processing: bool = Field(
        default=True,
        description="Enable parallel processing"
    )
    
    max_parallel_requests: int = Field(
        default=5,
        ge=1,
        le=20,
        description="Maximum parallel requests"
    )
    
    # Monitoring and logging
    enable_request_logging: bool = Field(
        default=True,
        description="Enable request logging"
    )
    
    enable_performance_metrics: bool = Field(
        default=True,
        description="Enable performance metrics"
    )
    
    enable_error_tracking: bool = Field(
        default=True,
        description="Enable error tracking"
    )
    
    # Development settings
    debug_mode: bool = Field(
        default=False,
        description="Enable debug mode"
    )
    
    log_requests: bool = Field(
        default=False,
        description="Log all requests and responses"
    )
    
    mock_responses: bool = Field(
        default=False,
        description="Use mock responses for testing"
    )
    
    class Config:
        """Pydantic configuration."""
        env_prefix = "GEMINI_"
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    @validator('api_key')
    def validate_api_key(cls, v):
        """Validate API key with security enhancement."""
        if v is None:
            # Check environment variable (deprecated - use secret manager)
            api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
            if not api_key:
                # Try to get from secret manager
                try:
                    from ..security.secret_rotation import SecretRotationManager
                    # This will be injected by the application at runtime
                    pass
                except ImportError:
                    pass
                raise ValueError("Gemini API key is required - use secret manager or environment variable")
            
            # Validate API key format for security
            if not (api_key.startswith('AIza') or api_key.startswith('gai-')):
                raise ValueError("Invalid Gemini API key format")
            
            return SecretStr(api_key)
        return v
    
    @validator('temperature')
    def validate_temperature(cls, v):
        """Validate temperature."""
        if not 0.0 <= v <= 1.0:
            raise ValueError("Temperature must be between 0.0 and 1.0")
        return v
    
    @validator('top_p')
    def validate_top_p(cls, v):
        """Validate top_p."""
        if not 0.0 <= v <= 1.0:
            raise ValueError("Top-p must be between 0.0 and 1.0")
        return v
    
    @validator('max_output_tokens')
    def validate_max_output_tokens(cls, v):
        """Validate max output tokens."""
        if v > 1048576:  # 1M tokens
            raise ValueError("Max output tokens cannot exceed 1M")
        return v
    
    @validator('context_window')
    def validate_context_window(cls, v):
        """Validate context window."""
        if v > 2000000:  # 2M tokens
            raise ValueError("Context window cannot exceed 2M tokens")
        return v
    
    @validator('safety_settings')
    def validate_safety_settings(cls, v):
        """Validate safety settings."""
        valid_categories = {cat.value for cat in SafetyCategory}
        valid_levels = {level.value for level in SafetyLevel}
        
        for category, level in v.items():
            if category not in valid_categories:
                raise ValueError(f"Invalid safety category: {category}")
            if level not in valid_levels:
                raise ValueError(f"Invalid safety level: {level}")
        
        return v
    
    @property
    def generation_config(self) -> Dict[str, Any]:
        """Get generation configuration."""
        return {
            "temperature": self.temperature,
            "top_p": self.top_p,
            "top_k": self.top_k,
            "max_output_tokens": self.max_output_tokens,
        }
    
    @property
    def safety_config(self) -> List[Dict[str, str]]:
        """Get safety configuration."""
        return [
            {"category": category, "threshold": level}
            for category, level in self.safety_settings.items()
        ]
    
    @property
    def rate_limit_config(self) -> Dict[str, Any]:
        """Get rate limiting configuration."""
        return {
            "requests_per_minute": self.requests_per_minute,
            "tokens_per_minute": self.tokens_per_minute,
            "concurrent_requests": self.concurrent_requests,
        }
    
    @property
    def retry_config(self) -> Dict[str, Any]:
        """Get retry configuration."""
        return {
            "max_retries": self.max_retries,
            "retry_delay": self.retry_delay,
            "exponential_base": self.retry_exponential_base,
        }
    
    @property
    def timeout_config(self) -> Dict[str, Any]:
        """Get timeout configuration."""
        return {
            "request_timeout": self.request_timeout,
            "connection_timeout": self.connection_timeout,
        }
    
    def get_model_config(self, model: Optional[GeminiModel] = None) -> Dict[str, Any]:
        """Get model configuration."""
        selected_model = model or self.default_model
        
        return {
            "model": selected_model.value,
            "generation_config": self.generation_config,
            "safety_settings": self.safety_config,
        }
    
    def get_embedding_config(self) -> Dict[str, Any]:
        """Get embedding configuration."""
        return {
            "model": self.embedding_model,
            "dimension": self.embedding_dimension,
            "batch_size": self.embedding_batch_size,
        }
    
    def get_performance_config(self) -> Dict[str, Any]:
        """Get performance configuration."""
        return {
            "enable_caching": self.enable_caching,
            "enable_batch_processing": self.enable_batch_processing,
            "batch_size": self.batch_size,
            "enable_parallel_processing": self.enable_parallel_processing,
            "max_parallel_requests": self.max_parallel_requests,
        }
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """Get monitoring configuration."""
        return {
            "enable_request_logging": self.enable_request_logging,
            "enable_performance_metrics": self.enable_performance_metrics,
            "enable_error_tracking": self.enable_error_tracking,
            "debug_mode": self.debug_mode,
            "log_requests": self.log_requests,
        }


# Global Gemini configuration instance
_gemini_config: Optional[GeminiConfig] = None


def get_gemini_config() -> GeminiConfig:
    """Get Gemini configuration instance."""
    global _gemini_config
    if _gemini_config is None:
        _gemini_config = GeminiConfig()
    return _gemini_config


def reset_gemini_config() -> None:
    """Reset Gemini configuration (for testing)."""
    global _gemini_config
    _gemini_config = None


# Pre-configured settings for different use cases
PATTERN_ANALYSIS_CONFIG = {
    "temperature": 0.1,
    "top_p": 0.95,
    "top_k": 40,
    "max_output_tokens": 8192,
}

CODE_EXPLANATION_CONFIG = {
    "temperature": 0.2,
    "top_p": 0.9,
    "top_k": 50,
    "max_output_tokens": 4096,
}

ANTI_PATTERN_DETECTION_CONFIG = {
    "temperature": 0.0,
    "top_p": 0.95,
    "top_k": 40,
    "max_output_tokens": 2048,
}

EMBEDDING_CONFIG = {
    "model": "models/embedding-001",
    "dimension": 768,
    "batch_size": 100,
}