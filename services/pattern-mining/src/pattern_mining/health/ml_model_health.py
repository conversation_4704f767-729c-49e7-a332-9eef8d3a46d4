"""
ML Model Health Checker

Comprehensive ML model health monitoring including:
- Model availability and loading status
- Inference performance monitoring
- Model accuracy tracking
- GPU/CPU resource utilization
- Model version compatibility
"""

import time
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging
import numpy as np

from .health_checker import HealthCheck, HealthResult, HealthStatus, HealthCheckConfig, CheckType

logger = logging.getLogger(__name__)


class MLModelHealthChecker(HealthCheck):
    """
    ML model health checker for monitoring model availability,
    performance, and accuracy.
    """
    
    def __init__(self, 
                 model_manager=None,
                 config: Optional[HealthCheckConfig] = None):
        """Initialize ML model health checker."""
        if config is None:
            config = HealthCheckConfig(
                name="ml_models",
                check_type=CheckType.READINESS,
                interval=60,
                timeout=15,
                retries=2,
                critical=True
            )
        
        super().__init__(config)
        self.model_manager = model_manager
        
        # Health thresholds
        self.inference_time_threshold = 2.0  # 2 seconds
        self.accuracy_threshold = 0.75       # 75% accuracy
        self.memory_usage_threshold = 0.8    # 80% memory usage
        self.gpu_utilization_threshold = 0.9 # 90% GPU utilization
        
        # Test data for model validation
        self.test_inputs = self._generate_test_inputs()
        
        logger.info("ML model health checker initialized")
    
    def _generate_test_inputs(self) -> Dict[str, Any]:
        """Generate test inputs for model validation."""
        return {
            'pattern_detection': {
                'code_sample': """
def calculate_total(items):
    total = 0
    for item in items:
        total += item.price
    return total
""",
                'language': 'python',
                'file_path': 'test/example.py'
            },
            'embedding': {
                'text': "This is a test function for pattern detection",
                'context': 'code_analysis'
            },
            'classification': {
                'features': [0.1, 0.2, 0.3, 0.4, 0.5],
                'metadata': {'test': True}
            }
        }
    
    async def check(self) -> HealthResult:
        """Perform ML model health check."""
        start_time = time.time()
        details = {}
        status = HealthStatus.HEALTHY
        messages = []
        
        try:
            # Check model availability
            if self.model_manager:
                availability_result = await self._check_model_availability()
                details['model_availability'] = availability_result
                
                if availability_result['status'] != 'healthy':
                    status = HealthStatus.UNHEALTHY
                    messages.append(f"Model availability: {availability_result['message']}")
            
            # Check inference performance
            inference_result = await self._check_inference_performance()
            details['inference_performance'] = inference_result
            
            if inference_result['status'] != 'healthy':
                if inference_result['status'] == 'unhealthy':
                    status = HealthStatus.UNHEALTHY
                elif status == HealthStatus.HEALTHY:
                    status = HealthStatus.DEGRADED
                messages.append(f"Inference performance: {inference_result['message']}")
            
            # Check model accuracy
            accuracy_result = await self._check_model_accuracy()
            details['model_accuracy'] = accuracy_result
            
            if accuracy_result['status'] != 'healthy':
                if accuracy_result['status'] == 'unhealthy':
                    status = HealthStatus.UNHEALTHY
                elif status == HealthStatus.HEALTHY:
                    status = HealthStatus.DEGRADED
                messages.append(f"Model accuracy: {accuracy_result['message']}")
            
            # Check resource utilization
            resource_result = await self._check_resource_utilization()
            details['resource_utilization'] = resource_result
            
            if resource_result['status'] != 'healthy':
                if resource_result['status'] == 'unhealthy':
                    status = HealthStatus.UNHEALTHY
                elif status == HealthStatus.HEALTHY:
                    status = HealthStatus.DEGRADED
                messages.append(f"Resource utilization: {resource_result['message']}")
            
            # Check GPU health (if available)
            gpu_result = await self._check_gpu_health()
            details['gpu_health'] = gpu_result
            
            if gpu_result['status'] not in ['healthy', 'unknown']:
                if gpu_result['status'] == 'unhealthy':
                    status = HealthStatus.UNHEALTHY
                elif status == HealthStatus.HEALTHY:
                    status = HealthStatus.DEGRADED
                messages.append(f"GPU health: {gpu_result['message']}")
            
            # Overall message
            if status == HealthStatus.HEALTHY:
                message = "ML models are healthy"
            else:
                message = "; ".join(messages)
            
            duration = time.time() - start_time
            
            return HealthResult(
                check_name=self.name,
                status=status,
                message=message,
                timestamp=datetime.now(),
                duration=duration,
                details=details,
                check_type=self.config.check_type
            )
            
        except Exception as e:
            duration = time.time() - start_time
            error_msg = f"ML model health check failed: {str(e)}"
            
            return HealthResult(
                check_name=self.name,
                status=HealthStatus.UNHEALTHY,
                message=error_msg,
                timestamp=datetime.now(),
                duration=duration,
                details=details,
                error=str(e),
                check_type=self.config.check_type
            )
    
    async def _check_model_availability(self) -> Dict[str, Any]:
        """Check if ML models are available and loaded."""
        try:
            if not self.model_manager:
                return {
                    'status': 'unknown',
                    'message': 'Model manager not available'
                }
            
            # Check if model manager has required models
            available_models = []
            unavailable_models = []
            
            expected_models = [
                'pattern_detector',
                'embedding_model',
                'classification_model',
                'gemini_client'
            ]
            
            for model_name in expected_models:
                try:
                    if hasattr(self.model_manager, model_name):
                        model = getattr(self.model_manager, model_name)
                        if model is not None:
                            available_models.append(model_name)
                        else:
                            unavailable_models.append(model_name)
                    else:
                        unavailable_models.append(model_name)
                except Exception as e:
                    unavailable_models.append(f"{model_name} (error: {str(e)})")
            
            availability_ratio = len(available_models) / len(expected_models)
            
            if availability_ratio == 1.0:
                status = 'healthy'
                message = 'All models are available'
            elif availability_ratio >= 0.5:
                status = 'degraded'
                message = f'{len(unavailable_models)} models unavailable: {", ".join(unavailable_models)}'
            else:
                status = 'unhealthy'
                message = f'Most models unavailable: {", ".join(unavailable_models)}'
            
            return {
                'status': status,
                'message': message,
                'available_models': available_models,
                'unavailable_models': unavailable_models,
                'availability_ratio': availability_ratio
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'Model availability check failed: {str(e)}',
                'error': str(e)
            }
    
    async def _check_inference_performance(self) -> Dict[str, Any]:
        """Check ML model inference performance."""
        try:
            if not self.model_manager:
                return {
                    'status': 'unknown',
                    'message': 'Model manager not available'
                }
            
            inference_results = {}
            overall_status = 'healthy'
            
            # Test pattern detection inference
            if hasattr(self.model_manager, 'pattern_detector'):
                try:
                    start_time = time.time()
                    test_input = self.test_inputs['pattern_detection']
                    
                    # Simulate pattern detection
                    if hasattr(self.model_manager.pattern_detector, 'detect_patterns'):
                        result = await self.model_manager.pattern_detector.detect_patterns(
                            test_input['code_sample'],
                            test_input['language']
                        )
                    else:
                        result = {'patterns': []}
                    
                    inference_time = time.time() - start_time
                    
                    if inference_time > self.inference_time_threshold:
                        overall_status = 'degraded'
                        status = 'degraded'
                        message = f'Slow inference: {inference_time:.2f}s'
                    else:
                        status = 'healthy'
                        message = f'Inference time: {inference_time:.2f}s'
                    
                    inference_results['pattern_detection'] = {
                        'status': status,
                        'message': message,
                        'inference_time_seconds': inference_time,
                        'patterns_detected': len(result.get('patterns', []))
                    }
                    
                except Exception as e:
                    overall_status = 'unhealthy'
                    inference_results['pattern_detection'] = {
                        'status': 'unhealthy',
                        'message': f'Pattern detection inference failed: {str(e)}',
                        'error': str(e)
                    }
            
            # Test embedding model inference
            if hasattr(self.model_manager, 'embedding_model'):
                try:
                    start_time = time.time()
                    test_input = self.test_inputs['embedding']
                    
                    # Simulate embedding generation
                    if hasattr(self.model_manager.embedding_model, 'encode'):
                        result = await self.model_manager.embedding_model.encode(
                            test_input['text']
                        )
                    else:
                        result = np.random.rand(768)  # Mock embedding
                    
                    inference_time = time.time() - start_time
                    
                    if inference_time > self.inference_time_threshold:
                        if overall_status == 'healthy':
                            overall_status = 'degraded'
                        status = 'degraded'
                        message = f'Slow embedding: {inference_time:.2f}s'
                    else:
                        status = 'healthy'
                        message = f'Embedding time: {inference_time:.2f}s'
                    
                    inference_results['embedding'] = {
                        'status': status,
                        'message': message,
                        'inference_time_seconds': inference_time,
                        'embedding_dimension': len(result) if hasattr(result, '__len__') else 0
                    }
                    
                except Exception as e:
                    overall_status = 'unhealthy'
                    inference_results['embedding'] = {
                        'status': 'unhealthy',
                        'message': f'Embedding inference failed: {str(e)}',
                        'error': str(e)
                    }
            
            return {
                'status': overall_status,
                'message': f'Inference performance: {overall_status}',
                'inference_results': inference_results
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'Inference performance check failed: {str(e)}',
                'error': str(e)
            }
    
    async def _check_model_accuracy(self) -> Dict[str, Any]:
        """Check ML model accuracy against known test cases."""
        try:
            if not self.model_manager:
                return {
                    'status': 'unknown',
                    'message': 'Model manager not available'
                }
            
            accuracy_results = {}
            overall_status = 'healthy'
            
            # Test pattern detection accuracy
            if hasattr(self.model_manager, 'pattern_detector'):
                try:
                    # Use a known code sample with expected patterns
                    test_code = """
def vulnerable_function(user_input):
    query = "SELECT * FROM users WHERE id = " + user_input
    return execute_query(query)
"""
                    
                    if hasattr(self.model_manager.pattern_detector, 'detect_patterns'):
                        result = await self.model_manager.pattern_detector.detect_patterns(
                            test_code, 'python'
                        )
                        
                        # Check if SQL injection pattern is detected
                        sql_injection_detected = any(
                            'sql' in pattern.get('type', '').lower() or 
                            'injection' in pattern.get('type', '').lower()
                            for pattern in result.get('patterns', [])
                        )
                        
                        if sql_injection_detected:
                            accuracy = 1.0
                            status = 'healthy'
                            message = 'Pattern detection accuracy good'
                        else:
                            accuracy = 0.0
                            status = 'unhealthy'
                            message = 'Pattern detection failed to detect known pattern'
                            overall_status = 'unhealthy'
                    else:
                        accuracy = 0.0
                        status = 'unknown'
                        message = 'Pattern detection method not available'
                    
                    accuracy_results['pattern_detection'] = {
                        'status': status,
                        'message': message,
                        'accuracy': accuracy,
                        'test_pattern_detected': sql_injection_detected if 'sql_injection_detected' in locals() else False
                    }
                    
                except Exception as e:
                    overall_status = 'unhealthy'
                    accuracy_results['pattern_detection'] = {
                        'status': 'unhealthy',
                        'message': f'Pattern detection accuracy test failed: {str(e)}',
                        'error': str(e)
                    }
            
            # Test embedding model accuracy (similarity test)
            if hasattr(self.model_manager, 'embedding_model'):
                try:
                    similar_texts = [
                        "This function calculates the total",
                        "This method computes the sum"
                    ]
                    different_texts = [
                        "This function calculates the total",
                        "The weather is nice today"
                    ]
                    
                    if hasattr(self.model_manager.embedding_model, 'encode'):
                        # Test similarity of similar texts
                        emb1 = await self.model_manager.embedding_model.encode(similar_texts[0])
                        emb2 = await self.model_manager.embedding_model.encode(similar_texts[1])
                        
                        # Calculate cosine similarity
                        similarity_similar = np.dot(emb1, emb2) / (np.linalg.norm(emb1) * np.linalg.norm(emb2))
                        
                        # Test similarity of different texts
                        emb3 = await self.model_manager.embedding_model.encode(different_texts[1])
                        similarity_different = np.dot(emb1, emb3) / (np.linalg.norm(emb1) * np.linalg.norm(emb3))
                        
                        # Check if similar texts are more similar than different texts
                        if similarity_similar > similarity_different:
                            accuracy = 1.0
                            status = 'healthy'
                            message = 'Embedding model accuracy good'
                        else:
                            accuracy = 0.0
                            status = 'degraded'
                            message = 'Embedding model accuracy questionable'
                            if overall_status == 'healthy':
                                overall_status = 'degraded'
                    else:
                        accuracy = 0.0
                        status = 'unknown'
                        message = 'Embedding encode method not available'
                    
                    accuracy_results['embedding'] = {
                        'status': status,
                        'message': message,
                        'accuracy': accuracy,
                        'similarity_similar': similarity_similar if 'similarity_similar' in locals() else 0,
                        'similarity_different': similarity_different if 'similarity_different' in locals() else 0
                    }
                    
                except Exception as e:
                    overall_status = 'unhealthy'
                    accuracy_results['embedding'] = {
                        'status': 'unhealthy',
                        'message': f'Embedding accuracy test failed: {str(e)}',
                        'error': str(e)
                    }
            
            return {
                'status': overall_status,
                'message': f'Model accuracy: {overall_status}',
                'accuracy_results': accuracy_results
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'Model accuracy check failed: {str(e)}',
                'error': str(e)
            }
    
    async def _check_resource_utilization(self) -> Dict[str, Any]:
        """Check ML model resource utilization."""
        try:
            import psutil
            
            # Check CPU usage
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            # Check memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Check if memory usage is too high
            if memory_percent > self.memory_usage_threshold * 100:
                status = 'degraded'
                message = f'High memory usage: {memory_percent:.1f}%'
            elif cpu_percent > 80:
                status = 'degraded'
                message = f'High CPU usage: {cpu_percent:.1f}%'
            else:
                status = 'healthy'
                message = f'Resource usage normal (CPU: {cpu_percent:.1f}%, Memory: {memory_percent:.1f}%)'
            
            return {
                'status': status,
                'message': message,
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'memory_available_gb': memory.available / (1024**3)
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'Resource utilization check failed: {str(e)}',
                'error': str(e)
            }
    
    async def _check_gpu_health(self) -> Dict[str, Any]:
        """Check GPU health and utilization."""
        try:
            try:
                import pynvml
                pynvml.nvmlInit()
                
                device_count = pynvml.nvmlDeviceGetCount()
                
                if device_count == 0:
                    return {
                        'status': 'unknown',
                        'message': 'No GPU devices found'
                    }
                
                gpu_info = []
                overall_status = 'healthy'
                
                for i in range(device_count):
                    handle = pynvml.nvmlDeviceGetHandleByIndex(i)
                    
                    # Get GPU utilization
                    util = pynvml.nvmlDeviceGetUtilizationRates(handle)
                    
                    # Get memory info
                    mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                    
                    # Get temperature
                    temp = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
                    
                    gpu_utilization = util.gpu / 100.0
                    memory_utilization = mem_info.used / mem_info.total
                    
                    gpu_status = 'healthy'
                    if gpu_utilization > self.gpu_utilization_threshold:
                        gpu_status = 'degraded'
                        overall_status = 'degraded'
                    
                    if temp > 80:  # Temperature over 80°C
                        gpu_status = 'degraded'
                        overall_status = 'degraded'
                    
                    gpu_info.append({
                        'gpu_id': i,
                        'utilization_percent': util.gpu,
                        'memory_utilization_percent': memory_utilization * 100,
                        'memory_used_mb': mem_info.used / (1024**2),
                        'memory_total_mb': mem_info.total / (1024**2),
                        'temperature_celsius': temp,
                        'status': gpu_status
                    })
                
                return {
                    'status': overall_status,
                    'message': f'GPU health: {overall_status}',
                    'gpu_count': device_count,
                    'gpu_info': gpu_info
                }
                
            except ImportError:
                return {
                    'status': 'unknown',
                    'message': 'pynvml not available, cannot check GPU health'
                }
                
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'GPU health check failed: {str(e)}',
                'error': str(e)
            }


def create_ml_model_health_checker(model_manager=None,
                                  check_type: CheckType = CheckType.READINESS,
                                  interval: int = 60) -> MLModelHealthChecker:
    """Create an ML model health checker with default configuration."""
    config = HealthCheckConfig(
        name="ml_models",
        check_type=check_type,
        interval=interval,
        timeout=15,
        retries=2,
        critical=True
    )
    
    return MLModelHealthChecker(
        model_manager=model_manager,
        config=config
    )