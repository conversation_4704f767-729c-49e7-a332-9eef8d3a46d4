"""
Core Health Checker System

Comprehensive health checking system with:
- Configurable health checks
- Health status aggregation
- Readiness and liveness probes
- Health trend monitoring
- Automated recovery actions
"""

import time
import asyncio
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from collections import deque, defaultdict
import logging
from contextlib import asynccontextmanager
import threading
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class HealthStatus(Enum):
    """Health check status levels."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


class CheckType(Enum):
    """Health check types."""
    READINESS = "readiness"
    LIVENESS = "liveness"
    STARTUP = "startup"
    DEPENDENCY = "dependency"


@dataclass
class HealthResult:
    """Result of a health check."""
    check_name: str
    status: HealthStatus
    message: str
    timestamp: datetime
    duration: float
    details: Dict[str, Any] = field(default_factory=dict)
    error: Optional[str] = None
    check_type: CheckType = CheckType.READINESS
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'check_name': self.check_name,
            'status': self.status.value,
            'message': self.message,
            'timestamp': self.timestamp.isoformat(),
            'duration_ms': self.duration * 1000,
            'details': self.details,
            'error': self.error,
            'check_type': self.check_type.value
        }


@dataclass
class HealthCheckConfig:
    """Configuration for a health check."""
    name: str
    check_type: CheckType
    interval: int  # seconds
    timeout: int   # seconds
    retries: int
    enabled: bool = True
    critical: bool = True  # If false, doesn't affect overall health
    
    def __post_init__(self):
        """Validate configuration."""
        if self.interval <= 0:
            raise ValueError("Interval must be positive")
        if self.timeout <= 0:
            raise ValueError("Timeout must be positive")
        if self.retries < 0:
            raise ValueError("Retries must be non-negative")


class HealthCheck(ABC):
    """Abstract base class for health checks."""
    
    def __init__(self, config: HealthCheckConfig):
        """Initialize health check."""
        self.config = config
        self.name = config.name
        self._last_result: Optional[HealthResult] = None
        self._consecutive_failures = 0
    
    @abstractmethod
    async def check(self) -> HealthResult:
        """Perform the health check."""
        pass
    
    async def run_check(self) -> HealthResult:
        """Run the health check with timeout and retries."""
        start_time = time.time()
        
        for attempt in range(self.config.retries + 1):
            try:
                result = await asyncio.wait_for(
                    self.check(),
                    timeout=self.config.timeout
                )
                
                # Reset consecutive failures on success
                if result.status == HealthStatus.HEALTHY:
                    self._consecutive_failures = 0
                else:
                    self._consecutive_failures += 1
                
                self._last_result = result
                return result
                
            except asyncio.TimeoutError:
                error_msg = f"Health check timed out after {self.config.timeout}s"
                if attempt < self.config.retries:
                    logger.warning(f"Health check timeout, retrying: {self.name}")
                    continue
                
                result = HealthResult(
                    check_name=self.name,
                    status=HealthStatus.UNHEALTHY,
                    message=error_msg,
                    timestamp=datetime.now(),
                    duration=time.time() - start_time,
                    error=error_msg,
                    check_type=self.config.check_type
                )
                
            except Exception as e:
                error_msg = f"Health check failed: {str(e)}"
                if attempt < self.config.retries:
                    logger.warning(f"Health check failed, retrying: {self.name} - {error_msg}")
                    continue
                
                result = HealthResult(
                    check_name=self.name,
                    status=HealthStatus.UNHEALTHY,
                    message=error_msg,
                    timestamp=datetime.now(),
                    duration=time.time() - start_time,
                    error=error_msg,
                    check_type=self.config.check_type
                )
        
        self._consecutive_failures += 1
        self._last_result = result
        return result
    
    @property
    def last_result(self) -> Optional[HealthResult]:
        """Get the last health check result."""
        return self._last_result
    
    @property
    def consecutive_failures(self) -> int:
        """Get the number of consecutive failures."""
        return self._consecutive_failures


class HealthChecker:
    """
    Comprehensive health checker system.
    
    Manages multiple health checks with different types and provides
    aggregated health status for the service.
    """
    
    def __init__(self, service_name: str = "pattern-mining"):
        """Initialize health checker."""
        self.service_name = service_name
        self._health_checks: Dict[str, HealthCheck] = {}
        self._check_results: Dict[str, HealthResult] = {}
        self._health_history: deque = deque(maxlen=1000)
        self._recovery_actions: Dict[str, Callable] = {}
        
        # Background checking
        self._check_tasks: Dict[str, asyncio.Task] = {}
        self._stop_checking = False
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Service startup time
        self._startup_time = datetime.now()
        
        logger.info("Health checker initialized", service_name=service_name)
    
    def add_health_check(self, health_check: HealthCheck):
        """Add a health check."""
        with self._lock:
            self._health_checks[health_check.name] = health_check
        
        logger.info(
            "Health check added",
            check_name=health_check.name,
            check_type=health_check.config.check_type.value,
            interval=health_check.config.interval,
            critical=health_check.config.critical
        )
    
    def remove_health_check(self, check_name: str):
        """Remove a health check."""
        with self._lock:
            if check_name in self._health_checks:
                del self._health_checks[check_name]
            
            if check_name in self._check_results:
                del self._check_results[check_name]
            
            if check_name in self._check_tasks:
                self._check_tasks[check_name].cancel()
                del self._check_tasks[check_name]
        
        logger.info("Health check removed", check_name=check_name)
    
    def add_recovery_action(self, check_name: str, action: Callable):
        """Add a recovery action for a health check."""
        self._recovery_actions[check_name] = action
        logger.info("Recovery action added", check_name=check_name)
    
    async def check_health(self, check_name: str) -> HealthResult:
        """Perform a single health check."""
        with self._lock:
            health_check = self._health_checks.get(check_name)
        
        if not health_check:
            return HealthResult(
                check_name=check_name,
                status=HealthStatus.UNKNOWN,
                message="Health check not found",
                timestamp=datetime.now(),
                duration=0.0,
                error="Health check not found"
            )
        
        if not health_check.config.enabled:
            return HealthResult(
                check_name=check_name,
                status=HealthStatus.HEALTHY,
                message="Health check disabled",
                timestamp=datetime.now(),
                duration=0.0
            )
        
        result = await health_check.run_check()
        
        with self._lock:
            self._check_results[check_name] = result
            self._health_history.append(result)
        
        # Execute recovery action if unhealthy
        if result.status == HealthStatus.UNHEALTHY and check_name in self._recovery_actions:
            try:
                await self._recovery_actions[check_name]()
                logger.info("Recovery action executed", check_name=check_name)
            except Exception as e:
                logger.error(
                    "Recovery action failed",
                    check_name=check_name,
                    error=str(e)
                )
        
        return result
    
    async def check_all_health(self) -> Dict[str, HealthResult]:
        """Perform all health checks."""
        results = {}
        
        # Run checks concurrently
        tasks = []
        check_names = []
        
        with self._lock:
            for check_name, health_check in self._health_checks.items():
                if health_check.config.enabled:
                    tasks.append(self.check_health(check_name))
                    check_names.append(check_name)
        
        if tasks:
            check_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for check_name, result in zip(check_names, check_results):
                if isinstance(result, Exception):
                    results[check_name] = HealthResult(
                        check_name=check_name,
                        status=HealthStatus.UNHEALTHY,
                        message=f"Health check failed: {str(result)}",
                        timestamp=datetime.now(),
                        duration=0.0,
                        error=str(result)
                    )
                else:
                    results[check_name] = result
        
        return results
    
    def get_overall_health(self) -> HealthResult:
        """Get overall health status."""
        with self._lock:
            results = list(self._check_results.values())
        
        if not results:
            return HealthResult(
                check_name="overall",
                status=HealthStatus.HEALTHY,
                message="No health checks configured",
                timestamp=datetime.now(),
                duration=0.0
            )
        
        # Determine overall status
        critical_results = [r for r in results if self._health_checks[r.check_name].config.critical]
        
        if not critical_results:
            critical_results = results
        
        unhealthy_count = len([r for r in critical_results if r.status == HealthStatus.UNHEALTHY])
        degraded_count = len([r for r in critical_results if r.status == HealthStatus.DEGRADED])
        
        if unhealthy_count > 0:
            status = HealthStatus.UNHEALTHY
            message = f"{unhealthy_count} critical health checks are unhealthy"
        elif degraded_count > 0:
            status = HealthStatus.DEGRADED
            message = f"{degraded_count} critical health checks are degraded"
        else:
            status = HealthStatus.HEALTHY
            message = "All health checks are healthy"
        
        return HealthResult(
            check_name="overall",
            status=status,
            message=message,
            timestamp=datetime.now(),
            duration=0.0,
            details={
                'total_checks': len(results),
                'healthy_checks': len([r for r in results if r.status == HealthStatus.HEALTHY]),
                'degraded_checks': degraded_count,
                'unhealthy_checks': unhealthy_count,
                'critical_checks': len(critical_results)
            }
        )
    
    def get_readiness_status(self) -> HealthResult:
        """Get readiness status (for readiness probe)."""
        with self._lock:
            readiness_results = [
                r for r in self._check_results.values()
                if self._health_checks[r.check_name].config.check_type == CheckType.READINESS
            ]
        
        if not readiness_results:
            return HealthResult(
                check_name="readiness",
                status=HealthStatus.HEALTHY,
                message="No readiness checks configured",
                timestamp=datetime.now(),
                duration=0.0
            )
        
        unhealthy_count = len([r for r in readiness_results if r.status == HealthStatus.UNHEALTHY])
        
        if unhealthy_count > 0:
            status = HealthStatus.UNHEALTHY
            message = f"{unhealthy_count} readiness checks are failing"
        else:
            status = HealthStatus.HEALTHY
            message = "All readiness checks are passing"
        
        return HealthResult(
            check_name="readiness",
            status=status,
            message=message,
            timestamp=datetime.now(),
            duration=0.0,
            check_type=CheckType.READINESS
        )
    
    def get_liveness_status(self) -> HealthResult:
        """Get liveness status (for liveness probe)."""
        with self._lock:
            liveness_results = [
                r for r in self._check_results.values()
                if self._health_checks[r.check_name].config.check_type == CheckType.LIVENESS
            ]
        
        if not liveness_results:
            return HealthResult(
                check_name="liveness",
                status=HealthStatus.HEALTHY,
                message="No liveness checks configured",
                timestamp=datetime.now(),
                duration=0.0
            )
        
        unhealthy_count = len([r for r in liveness_results if r.status == HealthStatus.UNHEALTHY])
        
        if unhealthy_count > 0:
            status = HealthStatus.UNHEALTHY
            message = f"{unhealthy_count} liveness checks are failing"
        else:
            status = HealthStatus.HEALTHY
            message = "All liveness checks are passing"
        
        return HealthResult(
            check_name="liveness",
            status=status,
            message=message,
            timestamp=datetime.now(),
            duration=0.0,
            check_type=CheckType.LIVENESS
        )
    
    def get_startup_status(self) -> HealthResult:
        """Get startup status (for startup probe)."""
        # Check if service has been running for minimum time
        uptime = (datetime.now() - self._startup_time).total_seconds()
        min_uptime = 30  # 30 seconds minimum uptime
        
        if uptime < min_uptime:
            return HealthResult(
                check_name="startup",
                status=HealthStatus.UNHEALTHY,
                message=f"Service starting up (uptime: {uptime:.1f}s)",
                timestamp=datetime.now(),
                duration=0.0,
                check_type=CheckType.STARTUP
            )
        
        # Check startup-specific health checks
        with self._lock:
            startup_results = [
                r for r in self._check_results.values()
                if self._health_checks[r.check_name].config.check_type == CheckType.STARTUP
            ]
        
        if not startup_results:
            return HealthResult(
                check_name="startup",
                status=HealthStatus.HEALTHY,
                message="Service startup completed",
                timestamp=datetime.now(),
                duration=0.0,
                check_type=CheckType.STARTUP
            )
        
        unhealthy_count = len([r for r in startup_results if r.status == HealthStatus.UNHEALTHY])
        
        if unhealthy_count > 0:
            status = HealthStatus.UNHEALTHY
            message = f"{unhealthy_count} startup checks are failing"
        else:
            status = HealthStatus.HEALTHY
            message = "All startup checks are passing"
        
        return HealthResult(
            check_name="startup",
            status=status,
            message=message,
            timestamp=datetime.now(),
            duration=0.0,
            check_type=CheckType.STARTUP
        )
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get comprehensive health summary."""
        with self._lock:
            results = list(self._check_results.values())
        
        overall_health = self.get_overall_health()
        readiness_status = self.get_readiness_status()
        liveness_status = self.get_liveness_status()
        startup_status = self.get_startup_status()
        
        # Calculate uptime
        uptime = (datetime.now() - self._startup_time).total_seconds()
        
        # Health statistics
        status_counts = defaultdict(int)
        for result in results:
            status_counts[result.status.value] += 1
        
        # Check type distribution
        type_counts = defaultdict(int)
        for check_name, health_check in self._health_checks.items():
            type_counts[health_check.config.check_type.value] += 1
        
        return {
            'service_name': self.service_name,
            'timestamp': datetime.now().isoformat(),
            'uptime_seconds': uptime,
            'overall_health': overall_health.to_dict(),
            'readiness': readiness_status.to_dict(),
            'liveness': liveness_status.to_dict(),
            'startup': startup_status.to_dict(),
            'individual_checks': {
                result.check_name: result.to_dict()
                for result in results
            },
            'statistics': {
                'total_checks': len(results),
                'status_distribution': dict(status_counts),
                'type_distribution': dict(type_counts),
                'enabled_checks': len([c for c in self._health_checks.values() if c.config.enabled]),
                'critical_checks': len([c for c in self._health_checks.values() if c.config.critical])
            }
        }
    
    def get_health_trends(self, hours: int = 24) -> Dict[str, List[Dict]]:
        """Get health trends over time."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        with self._lock:
            recent_results = [
                r for r in self._health_history
                if r.timestamp > cutoff_time
            ]
        
        # Group by check name and hour
        trends = defaultdict(list)
        
        for result in recent_results:
            hour_key = result.timestamp.strftime('%Y-%m-%d %H:00')
            trends[result.check_name].append({
                'timestamp': hour_key,
                'status': result.status.value,
                'duration_ms': result.duration * 1000
            })
        
        return dict(trends)
    
    async def start_background_checks(self):
        """Start background health checking."""
        self._stop_checking = False
        
        for check_name, health_check in self._health_checks.items():
            if health_check.config.enabled:
                task = asyncio.create_task(
                    self._background_check_loop(check_name, health_check)
                )
                self._check_tasks[check_name] = task
        
        logger.info("Background health checks started")
    
    async def stop_background_checks(self):
        """Stop background health checking."""
        self._stop_checking = True
        
        for task in self._check_tasks.values():
            task.cancel()
        
        if self._check_tasks:
            await asyncio.gather(*self._check_tasks.values(), return_exceptions=True)
        
        self._check_tasks.clear()
        logger.info("Background health checks stopped")
    
    async def _background_check_loop(self, check_name: str, health_check: HealthCheck):
        """Background loop for a single health check."""
        while not self._stop_checking:
            try:
                await self.check_health(check_name)
                await asyncio.sleep(health_check.config.interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(
                    "Error in background health check",
                    check_name=check_name,
                    error=str(e)
                )
                await asyncio.sleep(5)  # Brief pause before retry
    
    def cleanup_old_results(self, max_age_hours: int = 24):
        """Clean up old health check results."""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        with self._lock:
            old_count = len(self._health_history)
            self._health_history = deque(
                [r for r in self._health_history if r.timestamp > cutoff_time],
                maxlen=1000
            )
            
            cleaned_count = old_count - len(self._health_history)
            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} old health check results")


# Global health checker instance
_health_checker_instance: Optional[HealthChecker] = None


def get_health_checker() -> HealthChecker:
    """Get the global health checker instance."""
    global _health_checker_instance
    if _health_checker_instance is None:
        _health_checker_instance = HealthChecker()
    return _health_checker_instance


def init_health_checker(service_name: str = "pattern-mining") -> HealthChecker:
    """Initialize the global health checker instance."""
    global _health_checker_instance
    _health_checker_instance = HealthChecker(service_name)
    return _health_checker_instance