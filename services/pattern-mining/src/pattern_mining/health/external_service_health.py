"""
External Service Health Checker

Monitors external service dependencies including:
- API endpoints availability
- Service response times
- Authentication status
- Rate limiting status
- Circuit breaker state
"""

import time
import asyncio
import aiohttp
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging

from .health_checker import HealthCheck, HealthResult, HealthStatus, HealthCheckConfig, CheckType

logger = logging.getLogger(__name__)


class ExternalServiceHealthChecker(HealthCheck):
    """
    External service health checker for monitoring API endpoints
    and external service dependencies.
    """
    
    def __init__(self, 
                 services: List[Dict[str, Any]] = None,
                 config: Optional[HealthCheckConfig] = None):
        """Initialize external service health checker."""
        if config is None:
            config = HealthCheckConfig(
                name="external_services",
                check_type=CheckType.DEPENDENCY,
                interval=30,
                timeout=10,
                retries=2,
                critical=False  # External services are dependencies, not critical to core health
            )
        
        super().__init__(config)
        self.services = services or self._default_services()
        
        # Health thresholds
        self.response_time_threshold = 5.0  # 5 seconds
        self.critical_response_time_threshold = 10.0  # 10 seconds
        
        logger.info("External service health checker initialized")
    
    def _default_services(self) -> List[Dict[str, Any]]:
        """Define default external services to monitor."""
        return [
            {
                'name': 'google_gemini_api',
                'url': 'https://generativelanguage.googleapis.com/v1/models',
                'method': 'GET',
                'headers': {},
                'timeout': 10,
                'critical': True,
                'expected_status': [200, 401]  # 401 is OK if no auth provided
            },
            {
                'name': 'google_cloud_storage',
                'url': 'https://storage.googleapis.com/storage/v1/b',
                'method': 'GET',
                'headers': {},
                'timeout': 10,
                'critical': True,
                'expected_status': [200, 401]
            },
            {
                'name': 'google_bigquery',
                'url': 'https://bigquery.googleapis.com/bigquery/v2/projects',
                'method': 'GET',
                'headers': {},
                'timeout': 15,
                'critical': True,
                'expected_status': [200, 401]
            },
            {
                'name': 'redis_health',
                'url': 'redis://localhost:6379',
                'method': 'PING',
                'timeout': 5,
                'critical': False,
                'service_type': 'redis'
            }
        ]
    
    async def check(self) -> HealthResult:
        """Perform external service health check."""
        start_time = time.time()
        details = {}
        status = HealthStatus.HEALTHY
        messages = []
        
        try:
            service_results = await self._check_all_services()
            details['service_results'] = service_results
            
            # Determine overall status
            critical_failures = 0
            total_failures = 0
            slow_responses = 0
            
            for service_name, result in service_results.items():
                if result['status'] == 'unhealthy':
                    total_failures += 1
                    service_config = next(
                        (s for s in self.services if s['name'] == service_name), 
                        {}
                    )
                    if service_config.get('critical', False):
                        critical_failures += 1
                        messages.append(f"Critical service {service_name} is down")
                    else:
                        messages.append(f"Service {service_name} is down")
                
                elif result['status'] == 'degraded':
                    slow_responses += 1
                    messages.append(f"Service {service_name} is slow")
            
            # Determine overall health
            if critical_failures > 0:
                status = HealthStatus.UNHEALTHY
            elif total_failures > 0 or slow_responses > 0:
                status = HealthStatus.DEGRADED
            else:
                status = HealthStatus.HEALTHY
            
            # Create summary message
            if status == HealthStatus.HEALTHY:
                message = "All external services are healthy"
            else:
                message = "; ".join(messages)
            
            duration = time.time() - start_time
            
            return HealthResult(
                check_name=self.name,
                status=status,
                message=message,
                timestamp=datetime.now(),
                duration=duration,
                details=details,
                check_type=self.config.check_type
            )
            
        except Exception as e:
            duration = time.time() - start_time
            error_msg = f"External service health check failed: {str(e)}"
            
            return HealthResult(
                check_name=self.name,
                status=HealthStatus.UNHEALTHY,
                message=error_msg,
                timestamp=datetime.now(),
                duration=duration,
                details=details,
                error=str(e),
                check_type=self.config.check_type
            )
    
    async def _check_all_services(self) -> Dict[str, Dict[str, Any]]:
        """Check all configured external services."""
        results = {}
        
        # Create tasks for all service checks
        tasks = []
        service_names = []
        
        for service in self.services:
            task = self._check_single_service(service)
            tasks.append(task)
            service_names.append(service['name'])
        
        # Run all checks concurrently
        if tasks:
            check_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for service_name, result in zip(service_names, check_results):
                if isinstance(result, Exception):
                    results[service_name] = {
                        'status': 'unhealthy',
                        'message': f'Service check failed: {str(result)}',
                        'error': str(result),
                        'response_time_seconds': 0
                    }
                else:
                    results[service_name] = result
        
        return results
    
    async def _check_single_service(self, service: Dict[str, Any]) -> Dict[str, Any]:
        """Check a single external service."""
        service_name = service['name']
        service_type = service.get('service_type', 'http')
        
        try:
            if service_type == 'redis':
                return await self._check_redis_service(service)
            else:
                return await self._check_http_service(service)
                
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'Service check failed: {str(e)}',
                'error': str(e),
                'response_time_seconds': 0
            }
    
    async def _check_http_service(self, service: Dict[str, Any]) -> Dict[str, Any]:
        """Check HTTP-based external service."""
        service_name = service['name']
        url = service['url']
        method = service.get('method', 'GET')
        headers = service.get('headers', {})
        timeout = service.get('timeout', 10)
        expected_status = service.get('expected_status', [200])
        
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
                async with session.request(method, url, headers=headers) as response:
                    response_time = time.time() - start_time
                    status_code = response.status
                    
                    # Check if status code is expected
                    if status_code in expected_status:
                        # Check response time
                        if response_time > self.critical_response_time_threshold:
                            status = 'unhealthy'
                            message = f'Service critically slow: {response_time:.2f}s'
                        elif response_time > self.response_time_threshold:
                            status = 'degraded'
                            message = f'Service slow: {response_time:.2f}s'
                        else:
                            status = 'healthy'
                            message = f'Service responsive: {response_time:.2f}s'
                    else:
                        status = 'unhealthy'
                        message = f'Unexpected status code: {status_code}'
                    
                    return {
                        'status': status,
                        'message': message,
                        'response_time_seconds': response_time,
                        'status_code': status_code,
                        'url': url
                    }
                    
        except asyncio.TimeoutError:
            response_time = time.time() - start_time
            return {
                'status': 'unhealthy',
                'message': f'Service timeout after {timeout}s',
                'response_time_seconds': response_time,
                'error': 'timeout',
                'url': url
            }
            
        except aiohttp.ClientError as e:
            response_time = time.time() - start_time
            return {
                'status': 'unhealthy',
                'message': f'Service connection error: {str(e)}',
                'response_time_seconds': response_time,
                'error': str(e),
                'url': url
            }
    
    async def _check_redis_service(self, service: Dict[str, Any]) -> Dict[str, Any]:
        """Check Redis service health."""
        service_name = service['name']
        url = service['url']
        timeout = service.get('timeout', 5)
        
        start_time = time.time()
        
        try:
            import aioredis
            
            redis = aioredis.from_url(url, socket_connect_timeout=timeout)
            
            # Perform ping
            await redis.ping()
            
            response_time = time.time() - start_time
            
            # Check response time
            if response_time > self.response_time_threshold:
                status = 'degraded'
                message = f'Redis slow: {response_time:.2f}s'
            else:
                status = 'healthy'
                message = f'Redis responsive: {response_time:.2f}s'
            
            await redis.close()
            
            return {
                'status': status,
                'message': message,
                'response_time_seconds': response_time,
                'url': url
            }
            
        except ImportError:
            return {
                'status': 'unknown',
                'message': 'aioredis not available',
                'response_time_seconds': 0,
                'url': url
            }
            
        except Exception as e:
            response_time = time.time() - start_time
            return {
                'status': 'unhealthy',
                'message': f'Redis connection failed: {str(e)}',
                'response_time_seconds': response_time,
                'error': str(e),
                'url': url
            }


class PerformanceHealthChecker(HealthCheck):
    """
    Performance health checker for monitoring system performance
    against defined thresholds.
    """
    
    def __init__(self, 
                 performance_metrics=None,
                 config: Optional[HealthCheckConfig] = None):
        """Initialize performance health checker."""
        if config is None:
            config = HealthCheckConfig(
                name="performance",
                check_type=CheckType.READINESS,
                interval=30,
                timeout=5,
                retries=1,
                critical=True
            )
        
        super().__init__(config)
        self.performance_metrics = performance_metrics
        
        # Performance thresholds
        self.thresholds = {
            'cpu_usage_percent': 80.0,
            'memory_usage_percent': 85.0,
            'disk_usage_percent': 90.0,
            'response_time_p95': 1000.0,  # 1 second
            'error_rate_percent': 5.0,
            'request_rate_per_second': 1000.0
        }
        
        logger.info("Performance health checker initialized")
    
    async def check(self) -> HealthResult:
        """Perform performance health check."""
        start_time = time.time()
        details = {}
        status = HealthStatus.HEALTHY
        messages = []
        
        try:
            # Check system resources
            resource_result = await self._check_system_resources()
            details['system_resources'] = resource_result
            
            if resource_result['status'] != 'healthy':
                if resource_result['status'] == 'unhealthy':
                    status = HealthStatus.UNHEALTHY
                else:
                    status = HealthStatus.DEGRADED
                messages.append(f"System resources: {resource_result['message']}")
            
            # Check application performance
            app_result = await self._check_application_performance()
            details['application_performance'] = app_result
            
            if app_result['status'] != 'healthy':
                if app_result['status'] == 'unhealthy':
                    status = HealthStatus.UNHEALTHY
                elif status == HealthStatus.HEALTHY:
                    status = HealthStatus.DEGRADED
                messages.append(f"Application performance: {app_result['message']}")
            
            # Overall message
            if status == HealthStatus.HEALTHY:
                message = "Performance is within acceptable limits"
            else:
                message = "; ".join(messages)
            
            duration = time.time() - start_time
            
            return HealthResult(
                check_name=self.name,
                status=status,
                message=message,
                timestamp=datetime.now(),
                duration=duration,
                details=details,
                check_type=self.config.check_type
            )
            
        except Exception as e:
            duration = time.time() - start_time
            error_msg = f"Performance health check failed: {str(e)}"
            
            return HealthResult(
                check_name=self.name,
                status=HealthStatus.UNHEALTHY,
                message=error_msg,
                timestamp=datetime.now(),
                duration=duration,
                details=details,
                error=str(e),
                check_type=self.config.check_type
            )
    
    async def _check_system_resources(self) -> Dict[str, Any]:
        """Check system resource usage."""
        try:
            import psutil
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Disk usage (root partition)
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # Check thresholds
            issues = []
            
            if cpu_percent > self.thresholds['cpu_usage_percent']:
                issues.append(f'CPU usage high: {cpu_percent:.1f}%')
            
            if memory_percent > self.thresholds['memory_usage_percent']:
                issues.append(f'Memory usage high: {memory_percent:.1f}%')
            
            if disk_percent > self.thresholds['disk_usage_percent']:
                issues.append(f'Disk usage high: {disk_percent:.1f}%')
            
            if issues:
                if len(issues) > 1:
                    status = 'unhealthy'
                else:
                    status = 'degraded'
                message = '; '.join(issues)
            else:
                status = 'healthy'
                message = 'System resources within limits'
            
            return {
                'status': status,
                'message': message,
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'disk_percent': disk_percent
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'System resource check failed: {str(e)}',
                'error': str(e)
            }
    
    async def _check_application_performance(self) -> Dict[str, Any]:
        """Check application performance metrics."""
        try:
            if not self.performance_metrics:
                return {
                    'status': 'unknown',
                    'message': 'Performance metrics not available'
                }
            
            # Get current metrics (this would need to be implemented based on your metrics system)
            # For now, we'll simulate some metrics
            
            # Simulated metrics - in real implementation, these would come from your metrics system
            current_metrics = {
                'response_time_p95': 500.0,  # milliseconds
                'error_rate_percent': 2.0,
                'request_rate_per_second': 100.0
            }
            
            issues = []
            
            if current_metrics['response_time_p95'] > self.thresholds['response_time_p95']:
                issues.append(f'Response time high: {current_metrics["response_time_p95"]:.0f}ms')
            
            if current_metrics['error_rate_percent'] > self.thresholds['error_rate_percent']:
                issues.append(f'Error rate high: {current_metrics["error_rate_percent"]:.1f}%')
            
            if issues:
                status = 'degraded'
                message = '; '.join(issues)
            else:
                status = 'healthy'
                message = 'Application performance within limits'
            
            return {
                'status': status,
                'message': message,
                'metrics': current_metrics
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'Application performance check failed: {str(e)}',
                'error': str(e)
            }


def create_external_service_health_checker(services: List[Dict[str, Any]] = None,
                                          check_type: CheckType = CheckType.DEPENDENCY,
                                          interval: int = 30) -> ExternalServiceHealthChecker:
    """Create an external service health checker with default configuration."""
    config = HealthCheckConfig(
        name="external_services",
        check_type=check_type,
        interval=interval,
        timeout=10,
        retries=2,
        critical=False
    )
    
    return ExternalServiceHealthChecker(
        services=services,
        config=config
    )


def create_performance_health_checker(performance_metrics=None,
                                     check_type: CheckType = CheckType.READINESS,
                                     interval: int = 30) -> PerformanceHealthChecker:
    """Create a performance health checker with default configuration."""
    config = HealthCheckConfig(
        name="performance",
        check_type=check_type,
        interval=interval,
        timeout=5,
        retries=1,
        critical=True
    )
    
    return PerformanceHealthChecker(
        performance_metrics=performance_metrics,
        config=config
    )