"""
Database Health Checker

Comprehensive database health monitoring including:
- Connection pool health
- Query performance monitoring
- Transaction health
- Replication status
- Resource utilization
"""

import time
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging

from .health_checker import HealthCheck, HealthResult, HealthStatus, HealthCheckConfig, CheckType

logger = logging.getLogger(__name__)


class DatabaseHealthChecker(HealthCheck):
    """
    Database health checker for monitoring database connectivity,
    performance, and resource utilization.
    """
    
    def __init__(self, 
                 db_connection_pool=None,
                 bigquery_client=None,
                 config: Optional[HealthCheckConfig] = None):
        """Initialize database health checker."""
        if config is None:
            config = HealthCheckConfig(
                name="database",
                check_type=CheckType.READINESS,
                interval=30,
                timeout=10,
                retries=2,
                critical=True
            )
        
        super().__init__(config)
        self.db_connection_pool = db_connection_pool
        self.bigquery_client = bigquery_client
        
        # Health thresholds
        self.connection_pool_threshold = 0.8  # 80% utilization
        self.query_timeout_threshold = 5.0    # 5 seconds
        self.connection_timeout_threshold = 2.0  # 2 seconds
        
        logger.info("Database health checker initialized")
    
    async def check(self) -> HealthResult:
        """Perform database health check."""
        start_time = time.time()
        details = {}
        status = HealthStatus.HEALTHY
        messages = []
        
        try:
            # Check database connection pool
            if self.db_connection_pool:
                pool_health = await self._check_connection_pool()
                details['connection_pool'] = pool_health
                
                if pool_health['status'] != 'healthy':
                    status = HealthStatus.DEGRADED
                    messages.append(f"Connection pool: {pool_health['message']}")
            
            # Check BigQuery connection
            if self.bigquery_client:
                bigquery_health = await self._check_bigquery()
                details['bigquery'] = bigquery_health
                
                if bigquery_health['status'] != 'healthy':
                    if bigquery_health['status'] == 'unhealthy':
                        status = HealthStatus.UNHEALTHY
                    elif status == HealthStatus.HEALTHY:
                        status = HealthStatus.DEGRADED
                    messages.append(f"BigQuery: {bigquery_health['message']}")
            
            # Perform basic connectivity test
            connectivity_result = await self._check_connectivity()
            details['connectivity'] = connectivity_result
            
            if connectivity_result['status'] != 'healthy':
                status = HealthStatus.UNHEALTHY
                messages.append(f"Connectivity: {connectivity_result['message']}")
            
            # Check query performance
            performance_result = await self._check_query_performance()
            details['query_performance'] = performance_result
            
            if performance_result['status'] != 'healthy':
                if performance_result['status'] == 'unhealthy':
                    status = HealthStatus.UNHEALTHY
                elif status == HealthStatus.HEALTHY:
                    status = HealthStatus.DEGRADED
                messages.append(f"Query performance: {performance_result['message']}")
            
            # Overall message
            if status == HealthStatus.HEALTHY:
                message = "Database is healthy"
            else:
                message = "; ".join(messages)
            
            duration = time.time() - start_time
            
            return HealthResult(
                check_name=self.name,
                status=status,
                message=message,
                timestamp=datetime.now(),
                duration=duration,
                details=details,
                check_type=self.config.check_type
            )
            
        except Exception as e:
            duration = time.time() - start_time
            error_msg = f"Database health check failed: {str(e)}"
            
            return HealthResult(
                check_name=self.name,
                status=HealthStatus.UNHEALTHY,
                message=error_msg,
                timestamp=datetime.now(),
                duration=duration,
                details=details,
                error=str(e),
                check_type=self.config.check_type
            )
    
    async def _check_connection_pool(self) -> Dict[str, Any]:
        """Check database connection pool health."""
        try:
            if not hasattr(self.db_connection_pool, 'size'):
                return {
                    'status': 'unknown',
                    'message': 'Connection pool info not available'
                }
            
            pool_size = self.db_connection_pool.size
            checked_out = self.db_connection_pool.checkedout
            utilization = checked_out / pool_size if pool_size > 0 else 0
            
            if utilization > self.connection_pool_threshold:
                status = 'degraded'
                message = f'High connection pool utilization: {utilization:.1%}'
            else:
                status = 'healthy'
                message = f'Connection pool utilization: {utilization:.1%}'
            
            return {
                'status': status,
                'message': message,
                'pool_size': pool_size,
                'checked_out': checked_out,
                'utilization_percent': utilization * 100
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'Connection pool check failed: {str(e)}',
                'error': str(e)
            }
    
    async def _check_bigquery(self) -> Dict[str, Any]:
        """Check BigQuery connection and basic functionality."""
        try:
            if not self.bigquery_client:
                return {
                    'status': 'unknown',
                    'message': 'BigQuery client not available'
                }
            
            start_time = time.time()
            
            # Test basic BigQuery connectivity with a simple query
            query = "SELECT 1 as test_value"
            
            try:
                query_job = self.bigquery_client.query(query)
                results = query_job.result(timeout=self.query_timeout_threshold)
                
                # Check if we got expected result
                rows = list(results)
                if len(rows) == 1 and rows[0].test_value == 1:
                    query_duration = time.time() - start_time
                    
                    if query_duration > self.query_timeout_threshold:
                        status = 'degraded'
                        message = f'BigQuery query slow: {query_duration:.2f}s'
                    else:
                        status = 'healthy'
                        message = f'BigQuery responsive: {query_duration:.2f}s'
                    
                    return {
                        'status': status,
                        'message': message,
                        'query_duration_seconds': query_duration,
                        'job_id': query_job.job_id
                    }
                else:
                    return {
                        'status': 'unhealthy',
                        'message': 'BigQuery returned unexpected result'
                    }
                    
            except Exception as query_error:
                return {
                    'status': 'unhealthy',
                    'message': f'BigQuery query failed: {str(query_error)}',
                    'error': str(query_error)
                }
                
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'BigQuery health check failed: {str(e)}',
                'error': str(e)
            }
    
    async def _check_connectivity(self) -> Dict[str, Any]:
        """Check basic database connectivity."""
        try:
            start_time = time.time()
            
            # Test database connection
            if self.db_connection_pool:
                async with self.db_connection_pool.acquire() as conn:
                    # Simple ping query
                    result = await conn.execute("SELECT 1")
                    await result.fetchone()
                
                connection_duration = time.time() - start_time
                
                if connection_duration > self.connection_timeout_threshold:
                    status = 'degraded'
                    message = f'Database connection slow: {connection_duration:.2f}s'
                else:
                    status = 'healthy'
                    message = f'Database connection responsive: {connection_duration:.2f}s'
                
                return {
                    'status': status,
                    'message': message,
                    'connection_duration_seconds': connection_duration
                }
            else:
                return {
                    'status': 'unknown',
                    'message': 'No database connection pool available'
                }
                
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'Database connectivity check failed: {str(e)}',
                'error': str(e)
            }
    
    async def _check_query_performance(self) -> Dict[str, Any]:
        """Check database query performance."""
        try:
            if not self.db_connection_pool:
                return {
                    'status': 'unknown',
                    'message': 'No database connection pool available'
                }
            
            start_time = time.time()
            
            # Test query performance with a slightly more complex query
            async with self.db_connection_pool.acquire() as conn:
                # Check if we can create a temporary table and query it
                await conn.execute("""
                    CREATE TEMPORARY TABLE health_check_temp AS 
                    SELECT generate_series(1, 100) as id, 
                           'test_' || generate_series(1, 100) as name
                """)
                
                result = await conn.execute("""
                    SELECT COUNT(*) as count, AVG(id) as avg_id 
                    FROM health_check_temp
                """)
                
                row = await result.fetchone()
                
                # Clean up
                await conn.execute("DROP TABLE health_check_temp")
            
            query_duration = time.time() - start_time
            
            if query_duration > self.query_timeout_threshold:
                status = 'degraded'
                message = f'Database query performance slow: {query_duration:.2f}s'
            else:
                status = 'healthy'
                message = f'Database query performance good: {query_duration:.2f}s'
            
            return {
                'status': status,
                'message': message,
                'query_duration_seconds': query_duration,
                'test_row_count': row[0] if row else 0
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'Database query performance check failed: {str(e)}',
                'error': str(e)
            }


class PostgresHealthChecker(DatabaseHealthChecker):
    """PostgreSQL-specific health checker."""
    
    async def _check_postgres_specific(self) -> Dict[str, Any]:
        """Check PostgreSQL-specific health metrics."""
        try:
            if not self.db_connection_pool:
                return {
                    'status': 'unknown',
                    'message': 'No PostgreSQL connection pool available'
                }
            
            async with self.db_connection_pool.acquire() as conn:
                # Check active connections
                result = await conn.execute("""
                    SELECT count(*) as active_connections
                    FROM pg_stat_activity 
                    WHERE state = 'active'
                """)
                active_connections = (await result.fetchone())[0]
                
                # Check database size
                result = await conn.execute("""
                    SELECT pg_size_pretty(pg_database_size(current_database())) as db_size
                """)
                db_size = (await result.fetchone())[0]
                
                # Check for long-running queries
                result = await conn.execute("""
                    SELECT count(*) as long_running_queries
                    FROM pg_stat_activity 
                    WHERE state = 'active' 
                    AND query_start < now() - interval '5 minutes'
                """)
                long_running = (await result.fetchone())[0]
                
                # Check replication lag (if applicable)
                try:
                    result = await conn.execute("""
                        SELECT EXTRACT(EPOCH FROM (now() - pg_last_xact_replay_timestamp())) as lag_seconds
                    """)
                    lag_row = await result.fetchone()
                    replication_lag = lag_row[0] if lag_row and lag_row[0] else 0
                except:
                    replication_lag = None
                
                status = 'healthy'
                messages = []
                
                if long_running > 0:
                    status = 'degraded'
                    messages.append(f'{long_running} long-running queries')
                
                if replication_lag and replication_lag > 60:  # 1 minute lag
                    status = 'degraded'
                    messages.append(f'Replication lag: {replication_lag:.1f}s')
                
                return {
                    'status': status,
                    'message': '; '.join(messages) if messages else 'PostgreSQL healthy',
                    'active_connections': active_connections,
                    'database_size': db_size,
                    'long_running_queries': long_running,
                    'replication_lag_seconds': replication_lag
                }
                
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'PostgreSQL health check failed: {str(e)}',
                'error': str(e)
            }


class BigQueryHealthChecker(DatabaseHealthChecker):
    """BigQuery-specific health checker."""
    
    async def _check_bigquery_specific(self) -> Dict[str, Any]:
        """Check BigQuery-specific health metrics."""
        try:
            if not self.bigquery_client:
                return {
                    'status': 'unknown',
                    'message': 'BigQuery client not available'
                }
            
            # Check job history
            jobs = list(self.bigquery_client.list_jobs(max_results=10))
            
            # Check for recent failures
            failed_jobs = [job for job in jobs if job.state == 'DONE' and job.error_result]
            
            # Check slot utilization (if available)
            project_id = self.bigquery_client.project
            
            # Get dataset info
            datasets = list(self.bigquery_client.list_datasets(max_results=5))
            
            status = 'healthy'
            messages = []
            
            if len(failed_jobs) > 2:  # More than 2 failures in recent jobs
                status = 'degraded'
                messages.append(f'{len(failed_jobs)} failed jobs in recent history')
            
            return {
                'status': status,
                'message': '; '.join(messages) if messages else 'BigQuery healthy',
                'recent_jobs': len(jobs),
                'failed_jobs': len(failed_jobs),
                'project_id': project_id,
                'dataset_count': len(datasets)
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': f'BigQuery health check failed: {str(e)}',
                'error': str(e)
            }


def create_database_health_checker(db_connection_pool=None, bigquery_client=None,
                                 check_type: CheckType = CheckType.READINESS,
                                 interval: int = 30) -> DatabaseHealthChecker:
    """Create a database health checker with default configuration."""
    config = HealthCheckConfig(
        name="database",
        check_type=check_type,
        interval=interval,
        timeout=10,
        retries=2,
        critical=True
    )
    
    return DatabaseHealthChecker(
        db_connection_pool=db_connection_pool,
        bigquery_client=bigquery_client,
        config=config
    )


def create_postgres_health_checker(db_connection_pool=None,
                                 check_type: CheckType = CheckType.READINESS,
                                 interval: int = 30) -> PostgresHealthChecker:
    """Create a PostgreSQL health checker with default configuration."""
    config = HealthCheckConfig(
        name="postgres",
        check_type=check_type,
        interval=interval,
        timeout=10,
        retries=2,
        critical=True
    )
    
    return PostgresHealthChecker(
        db_connection_pool=db_connection_pool,
        config=config
    )


def create_bigquery_health_checker(bigquery_client=None,
                                  check_type: CheckType = CheckType.READINESS,
                                  interval: int = 60) -> BigQueryHealthChecker:
    """Create a BigQuery health checker with default configuration."""
    config = HealthCheckConfig(
        name="bigquery",
        check_type=check_type,
        interval=interval,
        timeout=15,
        retries=2,
        critical=True
    )
    
    return BigQueryHealthChecker(
        bigquery_client=bigquery_client,
        config=config
    )