"""
Base Model Classes

Abstract base classes for all ML models in the pattern detection system.
Provides common interface and functionality for model lifecycle management.
"""

import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

import numpy as np
import torch
from pydantic import BaseModel

logger = logging.getLogger(__name__)


@dataclass
class ModelConfig:
    """Configuration for ML models."""
    model_name: str
    model_type: str
    version: str
    framework: str
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
    batch_size: int = 32
    max_sequence_length: int = 512
    num_classes: int = 10
    learning_rate: float = 1e-4
    dropout_rate: float = 0.1
    hidden_size: int = 768
    num_layers: int = 12
    num_attention_heads: int = 12
    intermediate_size: int = 3072
    activation: str = "gelu"
    layer_norm_eps: float = 1e-12
    initializer_range: float = 0.02
    max_position_embeddings: int = 1024
    vocab_size: int = 50000
    pad_token_id: int = 0
    bos_token_id: int = 1
    eos_token_id: int = 2
    temperature: float = 1.0
    top_k: int = 50
    top_p: float = 0.95
    repetition_penalty: float = 1.1
    length_penalty: float = 1.0
    early_stopping: bool = True
    num_beams: int = 1
    use_cache: bool = True
    output_attentions: bool = False
    output_hidden_states: bool = False
    return_dict: bool = True
    gradient_checkpointing: bool = False
    mixed_precision: bool = True
    compile_model: bool = True
    optimize_for_inference: bool = True
    quantization: Optional[str] = None  # "int8", "fp16", etc.
    custom_params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.custom_params is None:
            self.custom_params = {}


class ModelMetrics(BaseModel):
    """Model performance metrics."""
    accuracy: float = 0.0
    precision: float = 0.0
    recall: float = 0.0
    f1_score: float = 0.0
    auc_roc: float = 0.0
    auc_pr: float = 0.0
    loss: float = 0.0
    perplexity: float = 0.0
    bleu_score: float = 0.0
    rouge_score: float = 0.0
    inference_time: float = 0.0
    throughput: float = 0.0
    memory_usage: int = 0
    gpu_memory_usage: int = 0
    energy_consumption: float = 0.0
    carbon_footprint: float = 0.0
    custom_metrics: Dict[str, float] = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return self.dict()
    
    def update(self, metrics: Dict[str, float]):
        """Update metrics with new values."""
        for key, value in metrics.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                self.custom_metrics[key] = value
    
    def get_summary(self) -> Dict[str, float]:
        """Get summary of key metrics."""
        return {
            "accuracy": self.accuracy,
            "f1_score": self.f1_score,
            "inference_time": self.inference_time,
            "throughput": self.throughput,
            "memory_usage": self.memory_usage
        }


class BaseModel(ABC):
    """Abstract base class for all ML models."""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        self.model_id = f"{config.model_name}:{config.version}"
        self.model = None
        self.tokenizer = None
        self.device = torch.device(config.device)
        self.metrics = ModelMetrics()
        self.is_loaded = False
        self.is_trained = False
        self.creation_time = datetime.now()
        self.last_updated = datetime.now()
        
        # Model state
        self._model_state = "initialized"
        self._training_state = "not_trained"
        
        # Performance tracking
        self._prediction_count = 0
        self._total_inference_time = 0.0
        self._error_count = 0
        
        logger.info(f"Initialized {self.__class__.__name__} with config: {config.model_name}")
    
    @abstractmethod
    async def load_model(self, model_path: Optional[str] = None) -> bool:
        """Load model from storage."""
        pass
    
    @abstractmethod
    async def save_model(self, model_path: str) -> bool:
        """Save model to storage."""
        pass
    
    @abstractmethod
    async def train(self, train_data: Any, validation_data: Optional[Any] = None) -> Dict[str, Any]:
        """Train the model."""
        pass
    
    @abstractmethod
    async def predict(self, input_data: Any) -> Any:
        """Make prediction."""
        pass
    
    @abstractmethod
    async def evaluate(self, test_data: Any) -> ModelMetrics:
        """Evaluate model performance."""
        pass
    
    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information."""
        pass
    
    def preprocess_input(self, input_data: Any) -> Any:
        """Preprocess input data."""
        return input_data
    
    def postprocess_output(self, output: Any) -> Any:
        """Postprocess model output."""
        return output
    
    async def batch_predict(self, batch_input: List[Any]) -> List[Any]:
        """Make batch predictions."""
        predictions = []
        batch_size = self.config.batch_size
        
        for i in range(0, len(batch_input), batch_size):
            batch = batch_input[i:i + batch_size]
            batch_predictions = []
            
            for input_data in batch:
                prediction = await self.predict(input_data)
                batch_predictions.append(prediction)
            
            predictions.extend(batch_predictions)
        
        return predictions
    
    def get_memory_usage(self) -> Dict[str, int]:
        """Get memory usage statistics."""
        if not self.model:
            return {"cpu_memory": 0, "gpu_memory": 0}
        
        cpu_memory = 0
        gpu_memory = 0
        
        try:
            # Calculate CPU memory
            if hasattr(self.model, 'parameters'):
                cpu_memory = sum(p.numel() * p.element_size() for p in self.model.parameters())
            
            # Calculate GPU memory
            if torch.cuda.is_available() and hasattr(self.model, 'parameters'):
                gpu_memory = sum(
                    p.numel() * p.element_size() 
                    for p in self.model.parameters() 
                    if p.is_cuda
                )
        except Exception as e:
            logger.warning(f"Error calculating memory usage: {e}")
        
        return {"cpu_memory": cpu_memory, "gpu_memory": gpu_memory}
    
    def get_model_size(self) -> int:
        """Get model size in bytes."""
        if not self.model:
            return 0
        
        try:
            if hasattr(self.model, 'parameters'):
                return sum(p.numel() * p.element_size() for p in self.model.parameters())
            return 0
        except Exception as e:
            logger.warning(f"Error calculating model size: {e}")
            return 0
    
    def get_parameter_count(self) -> Dict[str, int]:
        """Get parameter count statistics."""
        if not self.model:
            return {"total": 0, "trainable": 0, "non_trainable": 0}
        
        try:
            if hasattr(self.model, 'parameters'):
                total_params = sum(p.numel() for p in self.model.parameters())
                trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
                non_trainable_params = total_params - trainable_params
                
                return {
                    "total": total_params,
                    "trainable": trainable_params,
                    "non_trainable": non_trainable_params
                }
            return {"total": 0, "trainable": 0, "non_trainable": 0}
        except Exception as e:
            logger.warning(f"Error calculating parameter count: {e}")
            return {"total": 0, "trainable": 0, "non_trainable": 0}
    
    def optimize_for_inference(self):
        """Optimize model for inference."""
        if not self.model:
            return
        
        try:
            # Set to evaluation mode
            if hasattr(self.model, 'eval'):
                self.model.eval()
            
            # Disable gradient computation
            if hasattr(self.model, 'requires_grad_'):
                for param in self.model.parameters():
                    param.requires_grad_(False)
            
            # Move to device
            if hasattr(self.model, 'to'):
                self.model = self.model.to(self.device)
            
            # Enable inference optimizations
            if hasattr(self.model, 'half') and self.config.mixed_precision:
                if self.device.type == 'cuda':
                    self.model = self.model.half()
            
            # Compile model for faster inference (PyTorch 2.0+)
            if self.config.compile_model:
                try:
                    if hasattr(torch, 'compile'):
                        self.model = torch.compile(self.model)
                        logger.info("Model compiled for faster inference")
                except Exception as e:
                    logger.warning(f"Could not compile model: {e}")
            
            logger.info("Model optimized for inference")
        
        except Exception as e:
            logger.error(f"Error optimizing model for inference: {e}")
    
    def enable_gradient_checkpointing(self):
        """Enable gradient checkpointing to save memory."""
        if not self.model:
            return
        
        try:
            if hasattr(self.model, 'gradient_checkpointing_enable'):
                self.model.gradient_checkpointing_enable()
                logger.info("Gradient checkpointing enabled")
            elif hasattr(self.model, 'enable_gradient_checkpointing'):
                self.model.enable_gradient_checkpointing()
                logger.info("Gradient checkpointing enabled")
        except Exception as e:
            logger.warning(f"Could not enable gradient checkpointing: {e}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        avg_inference_time = (
            self._total_inference_time / self._prediction_count
            if self._prediction_count > 0 else 0.0
        )
        
        throughput = 1.0 / avg_inference_time if avg_inference_time > 0 else 0.0
        error_rate = self._error_count / self._prediction_count if self._prediction_count > 0 else 0.0
        
        return {
            "prediction_count": self._prediction_count,
            "total_inference_time": self._total_inference_time,
            "avg_inference_time": avg_inference_time,
            "throughput": throughput,
            "error_count": self._error_count,
            "error_rate": error_rate,
            "model_state": self._model_state,
            "training_state": self._training_state,
            "is_loaded": self.is_loaded,
            "is_trained": self.is_trained,
            "creation_time": self.creation_time.isoformat(),
            "last_updated": self.last_updated.isoformat()
        }
    
    def update_performance_stats(self, inference_time: float, success: bool = True):
        """Update performance statistics."""
        self._prediction_count += 1
        self._total_inference_time += inference_time
        
        if not success:
            self._error_count += 1
        
        self.last_updated = datetime.now()
    
    def reset_performance_stats(self):
        """Reset performance statistics."""
        self._prediction_count = 0
        self._total_inference_time = 0.0
        self._error_count = 0
        self.last_updated = datetime.now()
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get model health status."""
        memory_usage = self.get_memory_usage()
        param_count = self.get_parameter_count()
        performance_stats = self.get_performance_stats()
        
        # Determine health status
        status = "healthy"
        issues = []
        
        if not self.is_loaded:
            status = "not_loaded"
            issues.append("Model not loaded")
        elif performance_stats["error_rate"] > 0.1:
            status = "degraded"
            issues.append(f"High error rate: {performance_stats['error_rate']:.2%}")
        elif performance_stats["avg_inference_time"] > 10.0:
            status = "slow"
            issues.append(f"Slow inference: {performance_stats['avg_inference_time']:.2f}s")
        
        return {
            "status": status,
            "issues": issues,
            "model_id": self.model_id,
            "model_state": self._model_state,
            "training_state": self._training_state,
            "memory_usage": memory_usage,
            "parameter_count": param_count,
            "performance": performance_stats,
            "last_updated": self.last_updated.isoformat()
        }
    
    def __str__(self) -> str:
        """String representation."""
        return f"{self.__class__.__name__}(model_id={self.model_id}, state={self._model_state})"
    
    def __repr__(self) -> str:
        """Detailed representation."""
        return (
            f"{self.__class__.__name__}("
            f"model_id={self.model_id}, "
            f"state={self._model_state}, "
            f"loaded={self.is_loaded}, "
            f"trained={self.is_trained}, "
            f"device={self.device})"
        )


class ModelFactory:
    """Factory for creating model instances."""
    
    _registry: Dict[str, type] = {}
    
    @classmethod
    def register(cls, model_type: str, model_class: type):
        """Register a model class."""
        cls._registry[model_type] = model_class
        logger.info(f"Registered model type: {model_type}")
    
    @classmethod
    def create(cls, model_type: str, config: ModelConfig) -> BaseModel:
        """Create a model instance."""
        if model_type not in cls._registry:
            raise ValueError(f"Unknown model type: {model_type}")
        
        model_class = cls._registry[model_type]
        return model_class(config)
    
    @classmethod
    def get_available_types(cls) -> List[str]:
        """Get available model types."""
        return list(cls._registry.keys())
    
    @classmethod
    def get_model_class(cls, model_type: str) -> type:
        """Get model class for a type."""
        return cls._registry.get(model_type)


# Model type decorator
def register_model(model_type: str):
    """Decorator to register a model type."""
    def decorator(cls):
        ModelFactory.register(model_type, cls)
        return cls
    return decorator