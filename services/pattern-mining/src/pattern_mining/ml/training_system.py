"""
Advanced ML Training System

Comprehensive training system with model training, continuous learning, and evaluation.
Supports distributed training, hyperparameter optimization, and model lifecycle management.
"""

import asyncio
import logging
import os
import json
import pickle
import time
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Tuple, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset, DistributedSampler
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.cuda.amp import GradScaler, autocast
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, roc_auc_score

from ..config.ml import get_ml_config
from ..database.connection import get_database_session
from ..database.repository import ModelRepository, TrainingJobRepository
from ..models.ml import ModelTrainingRequest, TrainingStatus, ModelInfo, EvaluationMetrics
from .gpu_utils import get_gpu_manager, get_gpu_optimizer
from .components.base_model import BaseModel, ModelConfig, ModelFactory

logger = logging.getLogger(__name__)


class TrainingPhase(Enum):
    """Training phase enumeration."""
    INITIALIZATION = "initialization"
    DATA_LOADING = "data_loading"
    MODEL_SETUP = "model_setup"
    TRAINING = "training"
    VALIDATION = "validation"
    EVALUATION = "evaluation"
    CHECKPOINT = "checkpoint"
    HYPERPARAMETER_TUNING = "hyperparameter_tuning"
    COMPLETION = "completion"
    FAILED = "failed"


@dataclass
class TrainingConfig:
    """Training configuration."""
    job_id: str
    model_id: str
    model_type: str
    dataset_path: str
    output_path: str
    
    # Training parameters
    batch_size: int = 32
    learning_rate: float = 1e-4
    num_epochs: int = 10
    validation_split: float = 0.2
    test_split: float = 0.1
    
    # Optimization
    optimizer: str = "adam"
    weight_decay: float = 1e-5
    warmup_steps: int = 1000
    lr_scheduler: str = "cosine"
    gradient_clipping: float = 1.0
    
    # Regularization
    dropout_rate: float = 0.1
    label_smoothing: float = 0.0
    mixup_alpha: float = 0.0
    
    # Advanced features
    mixed_precision: bool = True
    gradient_checkpointing: bool = False
    distributed_training: bool = False
    num_workers: int = 4
    pin_memory: bool = True
    
    # Early stopping
    early_stopping: bool = True
    patience: int = 5
    min_delta: float = 1e-4
    
    # Checkpointing
    save_checkpoints: bool = True
    checkpoint_frequency: int = 1000
    keep_checkpoints: int = 3
    
    # Logging
    log_frequency: int = 100
    eval_frequency: int = 500
    
    # Hyperparameter tuning
    enable_tuning: bool = False
    tuning_trials: int = 20
    tuning_algorithm: str = "optuna"
    
    # Custom parameters
    custom_params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.custom_params is None:
            self.custom_params = {}


@dataclass
class TrainingMetrics:
    """Training metrics and statistics."""
    epoch: int
    step: int
    train_loss: float
    train_accuracy: float
    val_loss: Optional[float] = None
    val_accuracy: Optional[float] = None
    learning_rate: float = 0.0
    throughput: float = 0.0
    memory_usage: int = 0
    gpu_utilization: float = 0.0
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class TrainingJob:
    """Training job with state and progress tracking."""
    job_id: str
    model_id: str
    config: TrainingConfig
    status: TrainingStatus = TrainingStatus.PENDING
    phase: TrainingPhase = TrainingPhase.INITIALIZATION
    progress: float = 0.0
    message: str = ""
    
    # Metrics
    current_epoch: int = 0
    current_step: int = 0
    total_steps: int = 0
    best_val_loss: float = float('inf')
    best_val_accuracy: float = 0.0
    
    # Timing
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None
    
    # Metrics history
    metrics_history: List[TrainingMetrics] = None
    
    # State
    model_state: Optional[Dict[str, Any]] = None
    optimizer_state: Optional[Dict[str, Any]] = None
    scheduler_state: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.metrics_history is None:
            self.metrics_history = []
    
    def add_metrics(self, metrics: TrainingMetrics):
        """Add training metrics."""
        self.metrics_history.append(metrics)
        
        # Update current state
        self.current_epoch = metrics.epoch
        self.current_step = metrics.step
        
        # Update best metrics
        if metrics.val_loss and metrics.val_loss < self.best_val_loss:
            self.best_val_loss = metrics.val_loss
        
        if metrics.val_accuracy and metrics.val_accuracy > self.best_val_accuracy:
            self.best_val_accuracy = metrics.val_accuracy
    
    def update_progress(self):
        """Update progress based on current step."""
        if self.total_steps > 0:
            self.progress = min(100.0, (self.current_step / self.total_steps) * 100.0)
    
    def estimate_completion(self):
        """Estimate completion time."""
        if self.start_time and self.total_steps > 0 and self.current_step > 0:
            elapsed = datetime.now() - self.start_time
            steps_per_second = self.current_step / elapsed.total_seconds()
            remaining_steps = self.total_steps - self.current_step
            remaining_seconds = remaining_steps / steps_per_second
            self.estimated_completion = datetime.now() + timedelta(seconds=remaining_seconds)


class EarlyStopping:
    """Early stopping implementation."""
    
    def __init__(self, patience: int = 5, min_delta: float = 1e-4, restore_best: bool = True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best = restore_best
        
        self.best_score = None
        self.counter = 0
        self.best_weights = None
        self.early_stop = False
    
    def __call__(self, val_loss: float, model: nn.Module) -> bool:
        """Check if training should stop early."""
        score = -val_loss  # Higher is better
        
        if self.best_score is None:
            self.best_score = score
            self.save_checkpoint(model)
        elif score < self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
                if self.restore_best and self.best_weights is not None:
                    model.load_state_dict(self.best_weights)
        else:
            self.best_score = score
            self.save_checkpoint(model)
            self.counter = 0
        
        return self.early_stop
    
    def save_checkpoint(self, model: nn.Module):
        """Save model checkpoint."""
        if self.restore_best:
            self.best_weights = model.state_dict().copy()


class HyperparameterTuner:
    """Hyperparameter tuning implementation."""
    
    def __init__(self, algorithm: str = "optuna", n_trials: int = 20):
        self.algorithm = algorithm
        self.n_trials = n_trials
        self.study = None
        
        if algorithm == "optuna":
            try:
                import optuna
                self.optuna = optuna
            except ImportError:
                logger.warning("Optuna not available, using random search")
                self.algorithm = "random"
    
    def suggest_hyperparameters(self, trial_id: int) -> Dict[str, Any]:
        """Suggest hyperparameters for a trial."""
        if self.algorithm == "optuna" and self.study:
            trial = self.study.ask()
            return {
                "learning_rate": trial.suggest_float("learning_rate", 1e-5, 1e-2, log=True),
                "batch_size": trial.suggest_categorical("batch_size", [16, 32, 64, 128]),
                "weight_decay": trial.suggest_float("weight_decay", 1e-6, 1e-3, log=True),
                "dropout_rate": trial.suggest_float("dropout_rate", 0.0, 0.5),
                "warmup_steps": trial.suggest_int("warmup_steps", 100, 2000),
            }
        else:
            # Random search fallback
            import random
            return {
                "learning_rate": random.uniform(1e-5, 1e-2),
                "batch_size": random.choice([16, 32, 64, 128]),
                "weight_decay": random.uniform(1e-6, 1e-3),
                "dropout_rate": random.uniform(0.0, 0.5),
                "warmup_steps": random.randint(100, 2000),
            }
    
    def report_trial_result(self, trial_id: int, score: float):
        """Report trial result."""
        if self.algorithm == "optuna" and self.study:
            # This would be handled by Optuna's callback system
            pass
    
    def get_best_params(self) -> Dict[str, Any]:
        """Get best hyperparameters."""
        if self.algorithm == "optuna" and self.study:
            return self.study.best_params
        return {}


class AdvancedTrainingSystem:
    """Advanced training system with comprehensive features."""
    
    def __init__(self):
        self.config = get_ml_config()
        self.gpu_manager = get_gpu_manager()
        self.gpu_optimizer = get_gpu_optimizer()
        
        # Training state
        self.active_jobs: Dict[str, TrainingJob] = {}
        self.job_threads: Dict[str, threading.Thread] = {}
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Device management
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.world_size = 1
        self.rank = 0
        
        # Performance tracking
        self.total_jobs = 0
        self.successful_jobs = 0
        self.failed_jobs = 0
        
        logger.info("AdvancedTrainingSystem initialized")
    
    async def create_training_job(
        self,
        model_id: str,
        model_type: str,
        training_config: TrainingConfig
    ) -> str:
        """Create a new training job."""
        try:
            job_id = training_config.job_id or str(uuid.uuid4())
            
            # Create training job
            job = TrainingJob(
                job_id=job_id,
                model_id=model_id,
                config=training_config
            )
            
            # Store in database
            async with get_database_session() as session:
                job_repo = TrainingJobRepository(session)
                await job_repo.create_training_job(
                    job_id=job_id,
                    model_id=model_id,
                    config=training_config.__dict__,
                    status=TrainingStatus.PENDING
                )
                await session.commit()
            
            # Store in memory
            self.active_jobs[job_id] = job
            
            logger.info(f"Created training job {job_id} for model {model_id}")
            return job_id
        
        except Exception as e:
            logger.error(f"Error creating training job: {e}")
            raise
    
    async def start_training_job(self, job_id: str) -> bool:
        """Start a training job."""
        try:
            if job_id not in self.active_jobs:
                logger.error(f"Training job {job_id} not found")
                return False
            
            job = self.active_jobs[job_id]
            
            # Update status
            job.status = TrainingStatus.RUNNING
            job.start_time = datetime.now()
            await self._update_job_status(job_id, job)
            
            # Start training in thread
            thread = threading.Thread(
                target=self._run_training_job,
                args=(job_id,),
                daemon=True
            )
            thread.start()
            self.job_threads[job_id] = thread
            
            self.total_jobs += 1
            
            logger.info(f"Started training job {job_id}")
            return True
        
        except Exception as e:
            logger.error(f"Error starting training job {job_id}: {e}")
            return False
    
    def _run_training_job(self, job_id: str):
        """Run training job in thread."""
        try:
            asyncio.run(self._execute_training_job(job_id))
        except Exception as e:
            logger.error(f"Error in training job {job_id}: {e}")
            # Update job status to failed
            asyncio.run(self._mark_job_failed(job_id, str(e)))
    
    async def _execute_training_job(self, job_id: str):
        """Execute training job with all phases."""
        job = self.active_jobs[job_id]
        
        try:
            # Phase 1: Data Loading
            job.phase = TrainingPhase.DATA_LOADING
            await self._update_job_status(job_id, job)
            
            train_loader, val_loader, test_loader = await self._load_training_data(job)
            job.total_steps = len(train_loader) * job.config.num_epochs
            
            # Phase 2: Model Setup
            job.phase = TrainingPhase.MODEL_SETUP
            await self._update_job_status(job_id, job)
            
            model, optimizer, scheduler, scaler = await self._setup_training_components(job)
            
            # Phase 3: Training Loop
            job.phase = TrainingPhase.TRAINING
            await self._update_job_status(job_id, job)
            
            if job.config.enable_tuning:
                best_model = await self._train_with_hyperparameter_tuning(
                    job, model, optimizer, scheduler, scaler, train_loader, val_loader
                )
            else:
                best_model = await self._train_model(
                    job, model, optimizer, scheduler, scaler, train_loader, val_loader
                )
            
            # Phase 4: Final Evaluation
            job.phase = TrainingPhase.EVALUATION
            await self._update_job_status(job_id, job)
            
            final_metrics = await self._evaluate_model(job, best_model, test_loader)
            
            # Phase 5: Save Model
            job.phase = TrainingPhase.COMPLETION
            await self._save_trained_model(job, best_model, final_metrics)
            
            # Mark as completed
            job.status = TrainingStatus.COMPLETED
            job.end_time = datetime.now()
            job.progress = 100.0
            job.message = "Training completed successfully"
            
            await self._update_job_status(job_id, job)
            self.successful_jobs += 1
            
            logger.info(f"Training job {job_id} completed successfully")
        
        except Exception as e:
            await self._mark_job_failed(job_id, str(e))
            self.failed_jobs += 1
            logger.error(f"Training job {job_id} failed: {e}")
    
    async def _load_training_data(self, job: TrainingJob) -> Tuple[DataLoader, DataLoader, DataLoader]:
        """Load and prepare training data."""
        try:
            # This is a placeholder - in real implementation, would load actual data
            # based on job.config.dataset_path
            
            class DummyDataset(Dataset):
                def __init__(self, size: int):
                    self.size = size
                
                def __len__(self):
                    return self.size
                
                def __getitem__(self, idx):
                    # Return dummy data
                    return {
                        "input": torch.randn(768),
                        "label": torch.randint(0, 10, (1,)).squeeze()
                    }
            
            # Create datasets
            total_size = 10000  # Dummy size
            train_size = int(total_size * (1 - job.config.validation_split - job.config.test_split))
            val_size = int(total_size * job.config.validation_split)
            test_size = total_size - train_size - val_size
            
            train_dataset = DummyDataset(train_size)
            val_dataset = DummyDataset(val_size)
            test_dataset = DummyDataset(test_size)
            
            # Create data loaders
            train_loader = DataLoader(
                train_dataset,
                batch_size=job.config.batch_size,
                shuffle=True,
                num_workers=job.config.num_workers,
                pin_memory=job.config.pin_memory
            )
            
            val_loader = DataLoader(
                val_dataset,
                batch_size=job.config.batch_size,
                shuffle=False,
                num_workers=job.config.num_workers,
                pin_memory=job.config.pin_memory
            )
            
            test_loader = DataLoader(
                test_dataset,
                batch_size=job.config.batch_size,
                shuffle=False,
                num_workers=job.config.num_workers,
                pin_memory=job.config.pin_memory
            )
            
            logger.info(f"Loaded training data: {len(train_dataset)} train, {len(val_dataset)} val, {len(test_dataset)} test")
            return train_loader, val_loader, test_loader
        
        except Exception as e:
            logger.error(f"Error loading training data: {e}")
            raise
    
    async def _setup_training_components(self, job: TrainingJob) -> Tuple[nn.Module, optim.Optimizer, Any, GradScaler]:
        """Setup model, optimizer, scheduler, and scaler."""
        try:
            # Create model
            model_config = ModelConfig(
                model_name=job.model_id,
                model_type=job.config.model_type,
                version="1.0",
                framework="pytorch",
                device=str(self.device),
                dropout_rate=job.config.dropout_rate,
                learning_rate=job.config.learning_rate
            )
            
            # Create model instance
            if job.config.model_type in ["codet5plus", "graphcodebert", "nova"]:
                base_model = ModelFactory.create(job.config.model_type, model_config)
                await base_model.load_model()
                model = base_model.model
            else:
                # Create simple model for demonstration
                model = nn.Sequential(
                    nn.Linear(768, 512),
                    nn.ReLU(),
                    nn.Dropout(job.config.dropout_rate),
                    nn.Linear(512, 256),
                    nn.ReLU(),
                    nn.Dropout(job.config.dropout_rate),
                    nn.Linear(256, 10)  # Assuming 10 classes
                )
            
            # Move to device
            model = model.to(self.device)
            
            # Setup for distributed training if needed
            if job.config.distributed_training and torch.cuda.device_count() > 1:
                model = DDP(model)
            
            # Create optimizer
            if job.config.optimizer == "adam":
                optimizer = optim.Adam(
                    model.parameters(),
                    lr=job.config.learning_rate,
                    weight_decay=job.config.weight_decay
                )
            elif job.config.optimizer == "adamw":
                optimizer = optim.AdamW(
                    model.parameters(),
                    lr=job.config.learning_rate,
                    weight_decay=job.config.weight_decay
                )
            elif job.config.optimizer == "sgd":
                optimizer = optim.SGD(
                    model.parameters(),
                    lr=job.config.learning_rate,
                    momentum=0.9,
                    weight_decay=job.config.weight_decay
                )
            else:
                optimizer = optim.Adam(model.parameters(), lr=job.config.learning_rate)
            
            # Create scheduler
            if job.config.lr_scheduler == "cosine":
                scheduler = optim.lr_scheduler.CosineAnnealingLR(
                    optimizer, T_max=job.config.num_epochs
                )
            elif job.config.lr_scheduler == "step":
                scheduler = optim.lr_scheduler.StepLR(
                    optimizer, step_size=job.config.num_epochs // 3, gamma=0.1
                )
            elif job.config.lr_scheduler == "warmup":
                scheduler = optim.lr_scheduler.LinearLR(
                    optimizer, start_factor=0.1, total_iters=job.config.warmup_steps
                )
            else:
                scheduler = None
            
            # Create scaler for mixed precision
            scaler = GradScaler() if job.config.mixed_precision and torch.cuda.is_available() else None
            
            logger.info("Training components setup completed")
            return model, optimizer, scheduler, scaler
        
        except Exception as e:
            logger.error(f"Error setting up training components: {e}")
            raise
    
    async def _train_model(
        self,
        job: TrainingJob,
        model: nn.Module,
        optimizer: optim.Optimizer,
        scheduler: Any,
        scaler: GradScaler,
        train_loader: DataLoader,
        val_loader: DataLoader
    ) -> nn.Module:
        """Train model with standard training loop."""
        try:
            # Setup early stopping
            early_stopping = EarlyStopping(
                patience=job.config.patience,
                min_delta=job.config.min_delta
            ) if job.config.early_stopping else None
            
            # Training loop
            for epoch in range(job.config.num_epochs):
                # Training phase
                model.train()
                train_loss = 0.0
                train_correct = 0
                train_total = 0
                
                for batch_idx, batch in enumerate(train_loader):
                    inputs = batch["input"].to(self.device)
                    labels = batch["label"].to(self.device)
                    
                    optimizer.zero_grad()
                    
                    # Forward pass with mixed precision
                    if scaler:
                        with autocast():
                            outputs = model(inputs)
                            loss = nn.CrossEntropyLoss()(outputs, labels)
                        
                        scaler.scale(loss).backward()
                        scaler.unscale_(optimizer)
                        torch.nn.utils.clip_grad_norm_(model.parameters(), job.config.gradient_clipping)
                        scaler.step(optimizer)
                        scaler.update()
                    else:
                        outputs = model(inputs)
                        loss = nn.CrossEntropyLoss()(outputs, labels)
                        loss.backward()
                        torch.nn.utils.clip_grad_norm_(model.parameters(), job.config.gradient_clipping)
                        optimizer.step()
                    
                    # Update metrics
                    train_loss += loss.item()
                    _, predicted = torch.max(outputs.data, 1)
                    train_total += labels.size(0)
                    train_correct += (predicted == labels).sum().item()
                    
                    # Update job progress
                    job.current_step = epoch * len(train_loader) + batch_idx + 1
                    job.update_progress()
                    
                    # Log progress
                    if batch_idx % job.config.log_frequency == 0:
                        current_lr = optimizer.param_groups[0]['lr']
                        logger.info(
                            f"Job {job.job_id} - Epoch {epoch+1}/{job.config.num_epochs}, "
                            f"Batch {batch_idx}/{len(train_loader)}, "
                            f"Loss: {loss.item():.4f}, LR: {current_lr:.6f}"
                        )
                    
                    # Validation
                    if batch_idx % job.config.eval_frequency == 0 and val_loader:
                        val_loss, val_accuracy = await self._validate_model(model, val_loader)
                        
                        # Add metrics
                        metrics = TrainingMetrics(
                            epoch=epoch,
                            step=job.current_step,
                            train_loss=train_loss / (batch_idx + 1),
                            train_accuracy=train_correct / train_total,
                            val_loss=val_loss,
                            val_accuracy=val_accuracy,
                            learning_rate=optimizer.param_groups[0]['lr'],
                            throughput=job.config.batch_size / (time.time() - time.time()),  # Simplified
                            memory_usage=torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
                        )
                        job.add_metrics(metrics)
                        
                        # Check early stopping
                        if early_stopping and early_stopping(val_loss, model):
                            logger.info(f"Early stopping triggered for job {job.job_id}")
                            break
                    
                    # Save checkpoint
                    if job.config.save_checkpoints and batch_idx % job.config.checkpoint_frequency == 0:
                        await self._save_checkpoint(job, model, optimizer, scheduler, epoch, batch_idx)
                    
                    # Update job in database
                    if batch_idx % (job.config.log_frequency * 5) == 0:
                        await self._update_job_status(job.job_id, job)
                
                # Step scheduler
                if scheduler:
                    scheduler.step()
                
                # End of epoch validation
                if val_loader:
                    val_loss, val_accuracy = await self._validate_model(model, val_loader)
                    
                    logger.info(
                        f"Job {job.job_id} - Epoch {epoch+1} completed: "
                        f"Train Loss: {train_loss/len(train_loader):.4f}, "
                        f"Train Acc: {train_correct/train_total:.4f}, "
                        f"Val Loss: {val_loss:.4f}, "
                        f"Val Acc: {val_accuracy:.4f}"
                    )
                    
                    # Check early stopping
                    if early_stopping and early_stopping(val_loss, model):
                        logger.info(f"Early stopping triggered for job {job.job_id}")
                        break
            
            logger.info(f"Training completed for job {job.job_id}")
            return model
        
        except Exception as e:
            logger.error(f"Error training model: {e}")
            raise
    
    async def _train_with_hyperparameter_tuning(
        self,
        job: TrainingJob,
        model: nn.Module,
        optimizer: optim.Optimizer,
        scheduler: Any,
        scaler: GradScaler,
        train_loader: DataLoader,
        val_loader: DataLoader
    ) -> nn.Module:
        """Train model with hyperparameter tuning."""
        try:
            tuner = HyperparameterTuner(
                algorithm=job.config.tuning_algorithm,
                n_trials=job.config.tuning_trials
            )
            
            best_model = None
            best_score = float('-inf')
            
            for trial in range(job.config.tuning_trials):
                logger.info(f"Starting hyperparameter tuning trial {trial+1}/{job.config.tuning_trials}")
                
                # Get hyperparameters for this trial
                trial_params = tuner.suggest_hyperparameters(trial)
                
                # Update job config with trial parameters
                trial_config = job.config
                for key, value in trial_params.items():
                    setattr(trial_config, key, value)
                
                # Create new model and optimizer for this trial
                trial_model, trial_optimizer, trial_scheduler, trial_scaler = await self._setup_training_components(job)
                
                # Train for fewer epochs in tuning
                original_epochs = job.config.num_epochs
                job.config.num_epochs = min(5, original_epochs)  # Quick trials
                
                try:
                    # Train model
                    trained_model = await self._train_model(
                        job, trial_model, trial_optimizer, trial_scheduler, 
                        trial_scaler, train_loader, val_loader
                    )
                    
                    # Evaluate model
                    val_loss, val_accuracy = await self._validate_model(trained_model, val_loader)
                    
                    # Check if this is the best trial
                    if val_accuracy > best_score:
                        best_score = val_accuracy
                        best_model = trained_model
                        logger.info(f"New best trial {trial+1} with accuracy: {val_accuracy:.4f}")
                    
                    # Report result to tuner
                    tuner.report_trial_result(trial, val_accuracy)
                
                except Exception as e:
                    logger.warning(f"Trial {trial+1} failed: {e}")
                    tuner.report_trial_result(trial, 0.0)
                
                # Restore original epochs
                job.config.num_epochs = original_epochs
            
            if best_model is None:
                logger.warning("No successful trials, using last model")
                best_model = model
            
            # Final training with best hyperparameters
            logger.info("Training final model with best hyperparameters")
            best_params = tuner.get_best_params()
            if best_params:
                for key, value in best_params.items():
                    setattr(job.config, key, value)
                
                # Retrain with full epochs
                final_model, final_optimizer, final_scheduler, final_scaler = await self._setup_training_components(job)
                best_model = await self._train_model(
                    job, final_model, final_optimizer, final_scheduler,
                    final_scaler, train_loader, val_loader
                )
            
            return best_model
        
        except Exception as e:
            logger.error(f"Error in hyperparameter tuning: {e}")
            return model  # Fallback to original model
    
    async def _validate_model(self, model: nn.Module, val_loader: DataLoader) -> Tuple[float, float]:
        """Validate model and return loss and accuracy."""
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for batch in val_loader:
                inputs = batch["input"].to(self.device)
                labels = batch["label"].to(self.device)
                
                outputs = model(inputs)
                loss = nn.CrossEntropyLoss()(outputs, labels)
                
                val_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()
        
        model.train()  # Return to training mode
        
        avg_loss = val_loss / len(val_loader)
        accuracy = val_correct / val_total
        
        return avg_loss, accuracy
    
    async def _evaluate_model(self, job: TrainingJob, model: nn.Module, test_loader: DataLoader) -> EvaluationMetrics:
        """Evaluate trained model on test set."""
        model.eval()
        
        all_predictions = []
        all_labels = []
        all_probabilities = []
        
        with torch.no_grad():
            for batch in test_loader:
                inputs = batch["input"].to(self.device)
                labels = batch["label"].to(self.device)
                
                outputs = model(inputs)
                probabilities = torch.softmax(outputs, dim=1)
                _, predicted = torch.max(outputs, 1)
                
                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_probabilities.extend(probabilities.cpu().numpy())
        
        # Calculate metrics
        accuracy = accuracy_score(all_labels, all_predictions)
        precision, recall, f1, _ = precision_recall_fscore_support(all_labels, all_predictions, average='weighted')
        
        # Calculate AUC if binary classification
        try:
            auc_roc = roc_auc_score(all_labels, np.array(all_probabilities)[:, 1])
        except:
            auc_roc = None
        
        metrics = EvaluationMetrics(
            accuracy=accuracy,
            precision=precision,
            recall=recall,
            f1_score=f1,
            roc_auc=auc_roc
        )
        
        logger.info(f"Final evaluation - Accuracy: {accuracy:.4f}, F1: {f1:.4f}")
        
        model.train()  # Return to training mode
        return metrics
    
    async def _save_checkpoint(
        self,
        job: TrainingJob,
        model: nn.Module,
        optimizer: optim.Optimizer,
        scheduler: Any,
        epoch: int,
        step: int
    ):
        """Save training checkpoint."""
        try:
            checkpoint_dir = Path(job.config.output_path) / "checkpoints"
            checkpoint_dir.mkdir(parents=True, exist_ok=True)
            
            checkpoint_path = checkpoint_dir / f"checkpoint_epoch_{epoch}_step_{step}.pt"
            
            checkpoint = {
                "epoch": epoch,
                "step": step,
                "model_state_dict": model.state_dict(),
                "optimizer_state_dict": optimizer.state_dict(),
                "scheduler_state_dict": scheduler.state_dict() if scheduler else None,
                "job_config": asdict(job.config),
                "metrics_history": [asdict(m) for m in job.metrics_history]
            }
            
            torch.save(checkpoint, checkpoint_path)
            
            # Keep only recent checkpoints
            checkpoints = sorted(checkpoint_dir.glob("checkpoint_*.pt"))
            if len(checkpoints) > job.config.keep_checkpoints:
                for old_checkpoint in checkpoints[:-job.config.keep_checkpoints]:
                    old_checkpoint.unlink()
            
            logger.debug(f"Saved checkpoint: {checkpoint_path}")
        
        except Exception as e:
            logger.warning(f"Error saving checkpoint: {e}")
    
    async def _save_trained_model(
        self,
        job: TrainingJob,
        model: nn.Module,
        metrics: EvaluationMetrics
    ):
        """Save final trained model."""
        try:
            output_dir = Path(job.config.output_path)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Save model
            model_path = output_dir / "model.pt"
            torch.save(model.state_dict(), model_path)
            
            # Save model info
            model_info = {
                "model_id": job.model_id,
                "model_type": job.config.model_type,
                "training_config": asdict(job.config),
                "final_metrics": asdict(metrics),
                "metrics_history": [asdict(m) for m in job.metrics_history],
                "creation_time": datetime.now().isoformat()
            }
            
            info_path = output_dir / "model_info.json"
            with open(info_path, 'w') as f:
                json.dump(model_info, f, indent=2, default=str)
            
            # Update model in database
            async with get_database_session() as session:
                model_repo = ModelRepository(session)
                await model_repo.update_model_metrics(job.model_id, asdict(metrics))
                await model_repo.update_model_status(job.model_id, "trained")
                await session.commit()
            
            logger.info(f"Saved trained model to {output_dir}")
        
        except Exception as e:
            logger.error(f"Error saving trained model: {e}")
            raise
    
    async def _update_job_status(self, job_id: str, job: TrainingJob):
        """Update job status in database."""
        try:
            async with get_database_session() as session:
                job_repo = TrainingJobRepository(session)
                await job_repo.update_training_job_status(
                    job_id=job_id,
                    status=job.status,
                    progress=int(job.progress),
                    message=job.message
                )
                
                # Update metrics if available
                if job.metrics_history:
                    latest_metrics = job.metrics_history[-1]
                    await job_repo.set_training_job_metrics(job_id, asdict(latest_metrics))
                
                await session.commit()
        
        except Exception as e:
            logger.warning(f"Error updating job status: {e}")
    
    async def _mark_job_failed(self, job_id: str, error_message: str):
        """Mark job as failed."""
        if job_id in self.active_jobs:
            job = self.active_jobs[job_id]
            job.status = TrainingStatus.FAILED
            job.phase = TrainingPhase.FAILED
            job.message = f"Training failed: {error_message}"
            job.end_time = datetime.now()
            
            await self._update_job_status(job_id, job)
    
    async def cancel_training_job(self, job_id: str) -> bool:
        """Cancel a training job."""
        try:
            if job_id not in self.active_jobs:
                return False
            
            job = self.active_jobs[job_id]
            job.status = TrainingStatus.CANCELLED
            job.message = "Training cancelled by user"
            job.end_time = datetime.now()
            
            # Stop thread if running
            if job_id in self.job_threads:
                # Note: Python threads can't be forcefully killed
                # This is a limitation - in production, use proper cancellation mechanisms
                pass
            
            await self._update_job_status(job_id, job)
            
            logger.info(f"Cancelled training job {job_id}")
            return True
        
        except Exception as e:
            logger.error(f"Error cancelling training job {job_id}: {e}")
            return False
    
    async def get_training_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get training job status."""
        try:
            if job_id in self.active_jobs:
                job = self.active_jobs[job_id]
                
                # Update estimated completion
                job.estimate_completion()
                
                return {
                    "job_id": job.job_id,
                    "model_id": job.model_id,
                    "status": job.status.value,
                    "phase": job.phase.value,
                    "progress": job.progress,
                    "message": job.message,
                    "current_epoch": job.current_epoch,
                    "current_step": job.current_step,
                    "total_steps": job.total_steps,
                    "best_val_loss": job.best_val_loss if job.best_val_loss != float('inf') else None,
                    "best_val_accuracy": job.best_val_accuracy,
                    "start_time": job.start_time.isoformat() if job.start_time else None,
                    "estimated_completion": job.estimated_completion.isoformat() if job.estimated_completion else None,
                    "metrics_count": len(job.metrics_history)
                }
            
            # Check database
            async with get_database_session() as session:
                job_repo = TrainingJobRepository(session)
                job_data = await job_repo.get_training_job_by_id(job_id)
                
                if job_data:
                    return {
                        "job_id": job_data.job_id,
                        "model_id": job_data.model_id,
                        "status": job_data.status,
                        "progress": job_data.progress,
                        "message": job_data.message,
                        "metrics": job_data.metrics,
                        "created_at": job_data.created_at.isoformat(),
                        "updated_at": job_data.updated_at.isoformat()
                    }
            
            return None
        
        except Exception as e:
            logger.error(f"Error getting training status: {e}")
            return None
    
    def list_active_jobs(self) -> List[str]:
        """List active training job IDs."""
        return list(self.active_jobs.keys())
    
    def get_training_statistics(self) -> Dict[str, Any]:
        """Get training system statistics."""
        return {
            "total_jobs": self.total_jobs,
            "successful_jobs": self.successful_jobs,
            "failed_jobs": self.failed_jobs,
            "active_jobs": len(self.active_jobs),
            "success_rate": self.successful_jobs / self.total_jobs if self.total_jobs > 0 else 0.0,
            "device": str(self.device),
            "gpu_available": torch.cuda.is_available(),
            "gpu_count": torch.cuda.device_count() if torch.cuda.is_available() else 0
        }
    
    async def cleanup(self):
        """Cleanup training system resources."""
        try:
            # Cancel all active jobs
            for job_id in list(self.active_jobs.keys()):
                await self.cancel_training_job(job_id)
            
            # Clear state
            self.active_jobs.clear()
            self.job_threads.clear()
            
            # Shutdown executor
            self.executor.shutdown(wait=True)
            
            # Clear GPU cache
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            logger.info("Training system cleaned up")
        
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


# Global training system instance
_training_system: Optional[AdvancedTrainingSystem] = None


def get_training_system() -> AdvancedTrainingSystem:
    """Get global training system instance."""
    global _training_system
    if _training_system is None:
        _training_system = AdvancedTrainingSystem()
    return _training_system


async def cleanup_training_system():
    """Cleanup global training system."""
    global _training_system
    if _training_system:
        await _training_system.cleanup()
        _training_system = None