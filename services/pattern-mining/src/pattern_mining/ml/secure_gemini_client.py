"""
Secure Gemini Client with Secret Rotation and Prompt Injection Protection

This module provides a security-enhanced wrapper around the Gemini client
with automatic secret rotation, prompt injection detection, and response validation.
"""

import asyncio
import json
import re
import hashlib
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta
import structlog
from dataclasses import dataclass

from .gemini_client import GeminiClient, GeminiModel
from ..security.secret_rotation import SecretRotationManager, SecretType
from ..config.gemini import get_gemini_config

logger = structlog.get_logger()


@dataclass
class SecurityScanResult:
    """Result of security scanning for prompts/responses."""
    is_safe: bool
    risk_score: float  # 0.0 = safe, 1.0 = high risk
    detected_threats: List[str]
    sanitized_content: Optional[str] = None


class PromptInjectionDetector:
    """
    Advanced prompt injection detection system.
    
    Detects various types of prompt injection attacks including:
    - Direct instruction injection
    - Role-playing attacks  
    - System prompt leakage attempts
    - Code execution attempts
    - Data exfiltration attempts
    """
    
    def __init__(self):
        # Suspicious patterns for prompt injection
        self.injection_patterns = [
            # Direct instruction injection
            r"ignore\s+(?:previous|above|all)\s+(?:instructions|prompts?|commands?)",
            r"forget\s+(?:everything|all)\s+(?:above|before)",
            r"disregard\s+(?:previous|above|all)\s+(?:instructions|prompts?)",
            
            # Role-playing attacks
            r"(?:now\s+)?(?:you\s+are|act\s+as|pretend\s+to\s+be|roleplay\s+as)\s+(?:a\s+)?\w+",
            r"from\s+now\s+on\s+you\s+(?:are|will\s+be|should\s+be)",
            
            # System prompt leakage
            r"(?:show|display|reveal|tell\s+me)\s+(?:your\s+)?(?:system\s+)?(?:prompt|instructions)",
            r"what\s+(?:are\s+)?your\s+(?:initial\s+)?(?:instructions|prompts?|guidelines)",
            
            # Code execution attempts
            r"(?:execute|run|eval)\s*\([^)]*\)",
            r"import\s+\w+|from\s+\w+\s+import",
            r"__\w+__",  # Python dunder methods
            
            # Data exfiltration
            r"(?:read|access|open|get)\s+(?:file|data|database|memory)",
            r"(?:dump|export|extract|retrieve)\s+(?:all\s+)?(?:data|information|content)",
            
            # Jailbreak attempts
            r"(?:jailbreak|bypass|circumvent|override)\s+(?:safety|security|restrictions)",
            r"(?:enable|activate)\s+(?:developer|debug|admin)\s+mode",
            
            # SQL injection style
            r"';?\s*(?:drop|delete|update|insert|select)\s+",
            r"union\s+select|or\s+1\s*=\s*1",
            
            # XSS style
            r"<script[^>]*>|javascript:",
            r"on(?:load|error|click)\s*=",
            
            # Command injection
            r"[;&|`]\s*(?:rm|cat|ls|ps|kill|wget|curl|nc|netcat)",
            r"\$\([^)]+\)|`[^`]+`",
        ]
        
        # Compile patterns for performance
        self.compiled_patterns = [
            re.compile(pattern, re.IGNORECASE | re.MULTILINE)
            for pattern in self.injection_patterns
        ]
        
        # Suspicious keywords that increase risk score
        self.risk_keywords = {
            "high": ["execute", "eval", "import", "system", "admin", "root", "password", "secret"],
            "medium": ["bypass", "override", "ignore", "forget", "disregard", "jailbreak"],
            "low": ["roleplay", "pretend", "act as", "you are"]
        }
    
    def scan_prompt(self, prompt: str) -> SecurityScanResult:
        """Scan a prompt for injection attempts."""
        detected_threats = []
        risk_score = 0.0
        
        # Check for injection patterns
        for i, pattern in enumerate(self.compiled_patterns):
            if pattern.search(prompt):
                threat_name = self._get_threat_name(i)
                detected_threats.append(threat_name)
                risk_score += 0.3  # Each pattern adds 0.3 to risk score
        
        # Check for risk keywords
        prompt_lower = prompt.lower()
        for risk_level, keywords in self.risk_keywords.items():
            for keyword in keywords:
                if keyword in prompt_lower:
                    detected_threats.append(f"Suspicious keyword: {keyword}")
                    if risk_level == "high":
                        risk_score += 0.2
                    elif risk_level == "medium":
                        risk_score += 0.1
                    else:
                        risk_score += 0.05
        
        # Cap risk score at 1.0
        risk_score = min(risk_score, 1.0)
        
        # Generate sanitized content if needed
        sanitized_content = None
        if risk_score > 0.3:  # If moderate to high risk
            sanitized_content = self._sanitize_prompt(prompt)
        
        return SecurityScanResult(
            is_safe=risk_score < 0.5,  # Safe if risk score below 0.5
            risk_score=risk_score,
            detected_threats=detected_threats,
            sanitized_content=sanitized_content
        )
    
    def _get_threat_name(self, pattern_index: int) -> str:
        """Get threat name for pattern index."""
        threat_names = [
            "Direct instruction injection",
            "Role-playing attack",
            "System prompt leakage",
            "Code execution attempt",
            "Data exfiltration attempt",
            "Jailbreak attempt",
            "SQL injection style",
            "XSS injection",
            "Command injection"
        ]
        
        # Map pattern index to threat category
        if pattern_index < 3:
            return threat_names[0]
        elif pattern_index < 6:
            return threat_names[1]
        elif pattern_index < 9:
            return threat_names[2]
        elif pattern_index < 12:
            return threat_names[3]
        elif pattern_index < 15:
            return threat_names[4]
        elif pattern_index < 18:
            return threat_names[5]
        elif pattern_index < 21:
            return threat_names[6]
        elif pattern_index < 24:
            return threat_names[7]
        else:
            return threat_names[8]
    
    def _sanitize_prompt(self, prompt: str) -> str:
        """Sanitize a potentially malicious prompt."""
        sanitized = prompt
        
        # Remove potential injection patterns
        for pattern in self.compiled_patterns:
            sanitized = pattern.sub("[REMOVED_SUSPICIOUS_CONTENT]", sanitized)
        
        # Remove or escape dangerous characters
        dangerous_chars = ["<", ">", "`", "$", ";", "&", "|"]
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, f"\\{char}")
        
        return sanitized.strip()


class ResponseValidator:
    """Validates Gemini API responses for security issues."""
    
    def __init__(self):
        # Patterns that shouldn't appear in responses
        self.forbidden_patterns = [
            # Code execution
            r"(?:import|from)\s+\w+",
            r"(?:exec|eval|compile)\s*\(",
            
            # File system access
            r"(?:open|read|write)\s*\([^)]*['\"][^'\"]*\.[^'\"]*['\"]",
            
            # Network requests
            r"(?:requests?|urllib|http)\.(?:get|post|put|delete)",
            
            # System commands
            r"(?:os|subprocess|system)\.[^(\s]*\(",
            
            # Sensitive data patterns
            r"[A-Z0-9]{32,}",  # Potential API keys
            r"password\s*[:=]\s*['\"][^'\"]+['\"]",
        ]
        
        self.compiled_patterns = [
            re.compile(pattern, re.IGNORECASE)
            for pattern in self.forbidden_patterns
        ]
    
    def validate_response(self, response: str) -> SecurityScanResult:
        """Validate a response from Gemini API."""
        detected_threats = []
        risk_score = 0.0
        
        # Check for forbidden patterns
        for pattern in self.compiled_patterns:
            if pattern.search(response):
                detected_threats.append("Potentially unsafe content detected")
                risk_score += 0.4
        
        # Check response length (extremely long responses might be suspicious)
        if len(response) > 100000:  # 100KB
            detected_threats.append("Unusually long response")
            risk_score += 0.2
        
        return SecurityScanResult(
            is_safe=risk_score < 0.5,
            risk_score=min(risk_score, 1.0),
            detected_threats=detected_threats
        )


class SecureGeminiClient:
    """
    Security-enhanced Gemini client with:
    - Automatic secret rotation for API keys
    - Prompt injection detection and prevention
    - Response validation and sanitization
    - Rate limiting and abuse prevention
    - Comprehensive security monitoring
    """
    
    def __init__(
        self,
        secret_rotation_manager: SecretRotationManager,
        base_config=None
    ):
        self.secret_rotation_manager = secret_rotation_manager
        self.config = base_config or get_gemini_config()
        self.base_client: Optional[GeminiClient] = None
        
        # Security components
        self.injection_detector = PromptInjectionDetector()
        self.response_validator = ResponseValidator()
        
        # Security metrics
        self.security_metrics = {
            "blocked_prompts": 0,
            "suspicious_responses": 0,
            "api_key_rotations": 0,
            "last_security_check": None
        }
        
        # Cache for recent security scans
        self.scan_cache: Dict[str, SecurityScanResult] = {}
        self.cache_ttl = 300  # 5 minutes
        
        # Blocked content cache
        self.blocked_content_hashes: Set[str] = set()
    
    async def initialize(self) -> None:
        """Initialize the secure client."""
        try:
            # Get current API key from secret manager
            api_key = await self.secret_rotation_manager.get_active_secret("gemini_api_key")
            
            if not api_key:
                # Register a new API key for rotation
                raise ValueError("No Gemini API key found in secret manager. Please register one.")
            
            # Update config with current API key
            from pydantic import SecretStr
            self.config.api_key = SecretStr(api_key)
            
            # Initialize base client
            self.base_client = GeminiClient(self.config)
            await self.base_client.start()
            
            # Start background tasks
            asyncio.create_task(self._monitor_api_key_rotation())
            asyncio.create_task(self._cleanup_cache())
            
            logger.info("Secure Gemini client initialized")
            
        except Exception as e:
            logger.error("Failed to initialize secure Gemini client", error=str(e))
            raise
    
    async def generate_content_secure(
        self,
        prompt: str,
        model: Optional[GeminiModel] = None,
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate content with comprehensive security checks.
        
        Args:
            prompt: User prompt to process
            model: Gemini model to use
            system_prompt: Optional system prompt
            **kwargs: Additional arguments for generation
            
        Returns:
            Response with security metadata
        """
        request_id = hashlib.md5(f"{prompt}{datetime.utcnow()}".encode()).hexdigest()
        
        try:
            # 1. Scan prompt for injection attempts
            prompt_scan = await self._scan_prompt_cached(prompt)
            
            if not prompt_scan.is_safe:
                self.security_metrics["blocked_prompts"] += 1
                logger.warning(
                    "Blocked potentially malicious prompt",
                    request_id=request_id,
                    risk_score=prompt_scan.risk_score,
                    threats=prompt_scan.detected_threats
                )
                
                # Use sanitized content if available, otherwise reject
                if prompt_scan.sanitized_content:
                    prompt = prompt_scan.sanitized_content
                else:
                    raise ValueError("Prompt blocked due to security concerns")
            
            # 2. Check system prompt if provided
            if system_prompt:
                system_scan = await self._scan_prompt_cached(system_prompt)
                if not system_scan.is_safe:
                    logger.warning("Unsafe system prompt detected", request_id=request_id)
                    system_prompt = system_scan.sanitized_content or None
            
            # 3. Ensure we have a valid API key
            await self._ensure_valid_api_key()
            
            # 4. Generate content using base client
            response = await self.base_client.generate_content(
                prompt=prompt,
                model=model,
                system_prompt=system_prompt,
                **kwargs
            )
            
            # 5. Validate response
            response_text = response.get("text", "")
            response_scan = self.response_validator.validate_response(response_text)
            
            if not response_scan.is_safe:
                self.security_metrics["suspicious_responses"] += 1
                logger.warning(
                    "Suspicious response detected",
                    request_id=request_id,
                    threats=response_scan.detected_threats
                )
                
                # Sanitize or block response based on risk
                if response_scan.risk_score > 0.8:
                    response["text"] = "[RESPONSE_BLOCKED_DUE_TO_SECURITY_CONCERNS]"
                    response["security_warning"] = "High-risk content detected and blocked"
            
            # 6. Add security metadata
            response["security_metadata"] = {
                "request_id": request_id,
                "prompt_risk_score": prompt_scan.risk_score,
                "response_risk_score": response_scan.risk_score,
                "prompt_threats": prompt_scan.detected_threats,
                "response_threats": response_scan.detected_threats,
                "api_key_age_hours": await self._get_api_key_age_hours()
            }
            
            return response
            
        except Exception as e:
            logger.error(
                "Secure content generation failed",
                request_id=request_id,
                error=str(e)
            )
            raise
    
    async def analyze_code_secure(
        self,
        code: str,
        language: str,
        analysis_type: str = "pattern_detection"
    ) -> Dict[str, Any]:
        """Secure code analysis with additional validation."""
        # Validate code input
        if len(code) > 1000000:  # 1MB limit
            raise ValueError("Code too large for analysis")
        
        # Check for potentially malicious code patterns
        malicious_patterns = [
            r"eval\s*\([^)]*\)",
            r"exec\s*\([^)]*\)",
            r"__import__\s*\(",
            r"compile\s*\([^)]*\)",
            r"getattr\s*\([^)]*\)",
            r"setattr\s*\([^)]*\)",
        ]
        
        for pattern in malicious_patterns:
            if re.search(pattern, code, re.IGNORECASE):
                logger.warning("Potentially malicious code detected for analysis")
                break
        
        # Use the base client's code analysis
        return await self.base_client.analyze_code_with_thinking(
            code=code,
            language=language,
            analysis_type=analysis_type
        )
    
    async def get_security_metrics(self) -> Dict[str, Any]:
        """Get comprehensive security metrics."""
        return {
            **self.security_metrics,
            "api_key_status": await self._get_api_key_status(),
            "cache_size": len(self.scan_cache),
            "blocked_hashes": len(self.blocked_content_hashes),
            "last_check": datetime.utcnow().isoformat()
        }
    
    # Private helper methods
    
    async def _scan_prompt_cached(self, prompt: str) -> SecurityScanResult:
        """Scan prompt with caching."""
        prompt_hash = hashlib.sha256(prompt.encode()).hexdigest()
        
        # Check cache first
        if prompt_hash in self.scan_cache:
            cached_result = self.scan_cache[prompt_hash]
            # Check if cache entry is still valid
            if hasattr(cached_result, 'timestamp'):
                if datetime.utcnow().timestamp() - cached_result.timestamp < self.cache_ttl:
                    return cached_result
        
        # Perform scan
        result = self.injection_detector.scan_prompt(prompt)
        result.timestamp = datetime.utcnow().timestamp()
        
        # Cache result
        self.scan_cache[prompt_hash] = result
        
        return result
    
    async def _ensure_valid_api_key(self) -> None:
        """Ensure we have a valid, current API key."""
        if not self.base_client:
            raise ValueError("Base client not initialized")
        
        # Check if API key needs rotation (older than 12 hours)
        key_age = await self._get_api_key_age_hours()
        if key_age > 12:
            await self._rotate_api_key()
    
    async def _rotate_api_key(self) -> None:
        """Rotate the Gemini API key."""
        try:
            logger.info("Rotating Gemini API key")
            
            # Force rotation
            rotation_record = await self.secret_rotation_manager.rotate_secret(
                "gemini_api_key",
                force=True
            )
            
            if rotation_record.status.value == "active":
                # Get new API key
                new_api_key = await self.secret_rotation_manager.get_active_secret("gemini_api_key")
                
                # Update client configuration
                from pydantic import SecretStr
                self.config.api_key = SecretStr(new_api_key)
                
                # Reinitialize base client
                await self.base_client.close()
                self.base_client = GeminiClient(self.config)
                await self.base_client.start()
                
                self.security_metrics["api_key_rotations"] += 1
                logger.info("API key rotation completed successfully")
            else:
                logger.error("API key rotation failed")
                
        except Exception as e:
            logger.error("Failed to rotate API key", error=str(e))
            # Continue with current key
    
    async def _get_api_key_age_hours(self) -> float:
        """Get age of current API key in hours."""
        try:
            versions = await self.secret_rotation_manager.get_secret_versions("gemini_api_key")
            if versions:
                latest_version = versions[0]
                created_at = datetime.fromisoformat(latest_version["created_at"])
                age = datetime.utcnow() - created_at
                return age.total_seconds() / 3600
        except Exception:
            pass
        
        return 0.0
    
    async def _get_api_key_status(self) -> Dict[str, Any]:
        """Get API key status information."""
        try:
            versions = await self.secret_rotation_manager.get_secret_versions("gemini_api_key")
            age_hours = await self._get_api_key_age_hours()
            
            return {
                "versions_count": len(versions),
                "age_hours": age_hours,
                "rotation_due": age_hours > 24,
                "last_rotation": versions[0]["created_at"] if versions else None
            }
        except Exception:
            return {"status": "unknown"}
    
    async def _monitor_api_key_rotation(self) -> None:
        """Background task to monitor API key rotation."""
        while True:
            try:
                # Check if rotation is needed every hour
                await asyncio.sleep(3600)
                
                key_age = await self._get_api_key_age_hours()
                if key_age > 24:  # Rotate after 24 hours
                    await self._rotate_api_key()
                    
            except Exception as e:
                logger.error("API key monitoring error", error=str(e))
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def _cleanup_cache(self) -> None:
        """Background task to clean up security scan cache."""
        while True:
            try:
                await asyncio.sleep(600)  # Clean up every 10 minutes
                
                current_time = datetime.utcnow().timestamp()
                expired_keys = [
                    key for key, result in self.scan_cache.items()
                    if hasattr(result, 'timestamp') and 
                    current_time - result.timestamp > self.cache_ttl
                ]
                
                for key in expired_keys:
                    del self.scan_cache[key]
                    
            except Exception as e:
                logger.error("Cache cleanup error", error=str(e))
                await asyncio.sleep(300)


# Factory function for creating secure client
async def create_secure_gemini_client(
    secret_rotation_manager: SecretRotationManager,
    config=None
) -> SecureGeminiClient:
    """Create and initialize a secure Gemini client."""
    client = SecureGeminiClient(secret_rotation_manager, config)
    await client.initialize()
    return client