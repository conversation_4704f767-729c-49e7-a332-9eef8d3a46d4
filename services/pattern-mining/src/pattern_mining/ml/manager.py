"""
ML Model Manager - Google Models Integration

Comprehensive ML model manager using Google's pre-trained models via Vertex AI.
Implements production-ready model lifecycle management using Gemini 2.5 Flash and
other Google models for pattern detection.
"""

from typing import Dict, Any, List, Optional, Union, Tuple
import asyncio
from datetime import datetime, timedelta
import logging
import json
import gc
from pathlib import Path
import hashlib
from contextlib import asynccontextmanager
from dataclasses import dataclass
from enum import Enum
import threading
import weakref
from collections import defaultdict

from google.cloud import aiplatform
from google.cloud.aiplatform import gapic as aip
import google.generativeai as genai
from vertexai.preview.generative_models import GenerativeModel, Part

from ..config.ml import get_ml_config
from ..config.gemini import get_gemini_config, GeminiModel
from ..database.connection import get_database_session
from ..database.repository import ModelRepository, TrainingJobRepository
from ..models.ml import ModelInfo, EvaluationMetrics, TrainingStatus
from .gemini_client import GeminiClient

logger = logging.getLogger(__name__)


class ModelStatus(Enum):
    """Model connection and execution status."""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    FAILED = "failed"
    CACHED = "cached"
    RATE_LIMITED = "rate_limited"


@dataclass
class ModelCacheEntry:
    """Model cache entry with metadata for Google models."""
    model_name: str
    model_client: Any  # Gemini client or Vertex AI client
    model_info: ModelInfo
    connection_time: datetime
    access_time: datetime
    access_count: int
    token_usage: int
    cost_estimate: float
    status: ModelStatus
    lock: threading.Lock


@dataclass
class ModelMetrics:
    """Model performance metrics for Google models."""
    inference_time: float
    token_usage: int
    cost_per_request: float
    accuracy: float
    throughput: float
    error_rate: float
    cache_hit_rate: float
    rate_limit_hits: int


class ModelRegistry:
    """Registry for model versions and metadata."""
    
    def __init__(self):
        self._models: Dict[str, Dict[str, ModelInfo]] = defaultdict(dict)
        self._default_versions: Dict[str, str] = {}
        self._lock = threading.RLock()
    
    def register_model(self, model_name: str, version: str, model_info: ModelInfo):
        """Register a model version."""
        with self._lock:
            self._models[model_name][version] = model_info
            
            # Set as default if first version or explicitly marked
            if not self._default_versions.get(model_name) or version == "latest":
                self._default_versions[model_name] = version
    
    def get_model_info(self, model_name: str, version: Optional[str] = None) -> Optional[ModelInfo]:
        """Get model information by name and version."""
        with self._lock:
            if model_name not in self._models:
                return None
            
            if version is None:
                version = self._default_versions.get(model_name)
            
            if version is None:
                return None
            
            return self._models[model_name].get(version)
    
    def list_models(self) -> Dict[str, List[str]]:
        """List all registered models and their versions."""
        with self._lock:
            return {name: list(versions.keys()) for name, versions in self._models.items()}
    
    def get_default_version(self, model_name: str) -> Optional[str]:
        """Get default version for a model."""
        with self._lock:
            return self._default_versions.get(model_name)


class ModelCache:
    """LRU cache for loaded models with memory management."""
    
    def __init__(self, max_size: int = 10, max_memory_mb: int = 8192):
        self.max_size = max_size
        self.max_memory_mb = max_memory_mb
        self._cache: Dict[str, ModelCacheEntry] = {}
        self._access_order: List[str] = []
        self._lock = threading.RLock()
        self._total_memory = 0
        self._total_gpu_memory = 0
    
    def get(self, model_id: str) -> Optional[ModelCacheEntry]:
        """Get model from cache."""
        with self._lock:
            if model_id in self._cache:
                entry = self._cache[model_id]
                entry.access_time = datetime.now()
                entry.access_count += 1
                
                # Move to end (most recently used)
                self._access_order.remove(model_id)
                self._access_order.append(model_id)
                
                return entry
            return None
    
    def put(self, model_id: str, entry: ModelCacheEntry):
        """Add model to cache."""
        with self._lock:
            # Remove if already exists
            if model_id in self._cache:
                self._remove_entry(model_id)
            
            # Check memory constraints
            while (len(self._cache) >= self.max_size or 
                   self._total_memory + entry.memory_usage > self.max_memory_mb * 1024 * 1024):
                if not self._access_order:
                    break
                self._evict_lru()
            
            # Add new entry
            self._cache[model_id] = entry
            self._access_order.append(model_id)
            self._total_memory += entry.memory_usage
            self._total_gpu_memory += entry.gpu_memory_usage
    
    def remove(self, model_id: str) -> bool:
        """Remove model from cache."""
        with self._lock:
            if model_id in self._cache:
                self._remove_entry(model_id)
                return True
            return False
    
    def _remove_entry(self, model_id: str):
        """Remove entry and update memory counters."""
        entry = self._cache.pop(model_id)
        self._access_order.remove(model_id)
        self._total_memory -= entry.memory_usage
        self._total_gpu_memory -= entry.gpu_memory_usage
    
    def _evict_lru(self):
        """Evict least recently used model."""
        if self._access_order:
            lru_id = self._access_order[0]
            logger.info(f"Evicting model {lru_id} from cache")
            self._remove_entry(lru_id)
    
    def clear(self):
        """Clear entire cache."""
        with self._lock:
            self._cache.clear()
            self._access_order.clear()
            self._total_memory = 0
            self._total_gpu_memory = 0
    
    def stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            return {
                "size": len(self._cache),
                "max_size": self.max_size,
                "memory_usage_mb": self._total_memory / (1024 * 1024),
                "gpu_memory_usage_mb": self._total_gpu_memory / (1024 * 1024),
                "max_memory_mb": self.max_memory_mb,
                "models": list(self._cache.keys())
            }


class MLManager:
    """Comprehensive ML model manager using Google's pre-trained models."""
    
    def __init__(self):
        self.config = get_ml_config()
        self.gemini_config = get_gemini_config()
        
        # Core components
        self.registry = ModelRegistry()
        self.cache = ModelCache(
            max_size=self.config.get("cache_max_size", 10),
            max_memory_mb=self.config.get("cache_max_memory_mb", 2048)  # Reduced for API-based models
        )
        
        # Google model clients
        self.gemini_client: Optional[GeminiClient] = None
        self.vertex_ai_client: Optional[aiplatform.gapic.PredictionServiceAsyncClient] = None
        
        # State tracking
        self._connecting_models: Dict[str, asyncio.Event] = {}
        self._model_locks: Dict[str, threading.Lock] = defaultdict(threading.Lock)
        self._metrics: Dict[str, ModelMetrics] = {}
        
        # Background tasks
        self._cleanup_task: Optional[asyncio.Task] = None
        self._monitoring_task: Optional[asyncio.Task] = None
        
        # Initialize Google model mappings
        self._init_google_models()
    
    def _init_google_models(self):
        """Initialize Google model configurations and mappings."""
        try:
            # Initialize Google Cloud AI Platform
            aiplatform.init(
                project=self.gemini_config.project_id,
                location=self.gemini_config.location
            )
            
            # Define Google model mappings for different pattern types
            self.google_models = {
                "pattern_detection": GeminiModel.GEMINI_2_5_FLASH,
                "code_analysis": GeminiModel.GEMINI_2_5_FLASH,
                "security_analysis": GeminiModel.GEMINI_2_5_FLASH,
                "embeddings": "text-embedding-004",  # Latest Google embeddings model
                "classification": GeminiModel.GEMINI_2_0_FLASH_EXP,
            }
            
            logger.info("Google models configuration initialized successfully")
        except Exception as e:
            logger.warning(f"Google models initialization failed: {e}")
            # Set defaults if initialization fails
            self.google_models = {
                "pattern_detection": GeminiModel.GEMINI_2_5_FLASH,
                "code_analysis": GeminiModel.GEMINI_2_5_FLASH,
                "security_analysis": GeminiModel.GEMINI_2_5_FLASH,
                "embeddings": "text-embedding-004",
                "classification": GeminiModel.GEMINI_2_0_FLASH_EXP,
            }
    
    async def start(self):
        """Start background tasks and initialize Google model clients."""
        # Initialize Gemini client
        self.gemini_client = GeminiClient(self.gemini_config)
        await self.gemini_client.start()
        
        # Initialize Vertex AI client
        self.vertex_ai_client = aiplatform.gapic.PredictionServiceAsyncClient()
        
        # Start background tasks
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("ML Manager with Google models started successfully")
    
    async def stop(self):
        """Stop background tasks and cleanup."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
        if self._monitoring_task:
            self._monitoring_task.cancel()
        
        # Close Google model clients
        if self.gemini_client:
            await self.gemini_client.close()
        if self.vertex_ai_client:
            await self.vertex_ai_client.close()
        
        await self.cleanup()
        logger.info("ML Manager stopped")
    
    async def _cleanup_loop(self):
        """Background cleanup of expired models."""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                await self._cleanup_expired_models()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
    
    async def _monitoring_loop(self):
        """Background monitoring of model performance."""
        while True:
            try:
                await asyncio.sleep(60)  # Run every minute
                await self._update_metrics()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
    
    async def _cleanup_expired_models(self):
        """Cleanup model connections that haven't been accessed recently."""
        cutoff_time = datetime.now() - timedelta(hours=1)
        
        for model_id, entry in list(self.cache._cache.items()):
            if entry.access_time < cutoff_time and entry.access_count < 10:
                logger.info(f"Cleaning up expired model connection: {model_id}")
                await self.disconnect_model(model_id)
    
    async def _update_metrics(self):
        """Update model performance metrics."""
        try:
            # Update token usage and cost metrics
            for model_id, entry in self.cache._cache.items():
                if entry.status == ModelStatus.CONNECTED:
                    # Update cost estimates based on token usage
                    if model_id in self._metrics:
                        metrics = self._metrics[model_id]
                        # Estimate cost based on Gemini pricing
                        if "gemini" in entry.model_name.lower():
                            # Gemini 2.5 Flash pricing: ~$0.075 per 1M input tokens
                            estimated_cost = (metrics.token_usage / 1_000_000) * 0.075
                            metrics.cost_per_request = estimated_cost / max(entry.access_count, 1)
        except Exception as e:
            logger.error(f"Error updating metrics: {e}")
    
    def register_model(self, model_name: str, version: str, model_info: ModelInfo):
        """Register a new model version."""
        self.registry.register_model(model_name, version, model_info)
        logger.info(f"Registered model {model_name} version {version}")
    
    def get_model_registry(self) -> Dict[str, List[str]]:
        """Get all registered models and versions."""
        return self.registry.list_models()
    
    async def list_models(self) -> List[ModelInfo]:
        """List all available models."""
        async with get_database_session() as session:
            repo = ModelRepository(session)
            models = await repo.get_models_by_status("deployed")
            
            model_infos = []
            for model in models:
                model_info = ModelInfo(
                    model_id=model.model_id,
                    name=model.name,
                    description=model.description,
                    model_type=model.model_type,
                    version=model.version,
                    status=model.status,
                    framework=model.framework,
                    input_schema=model.input_schema,
                    output_schema=model.output_schema,
                    metrics=model.metrics,
                    hyperparameters=model.hyperparameters,
                    model_size=model.model_size,
                    inference_time=model.inference_time,
                    created_at=model.created_at,
                    updated_at=model.updated_at
                )
                model_infos.append(model_info)
            
            return model_infos
    
    async def get_model_info(self, model_id: str) -> Optional[ModelInfo]:
        """Get detailed information about a model."""
        async with get_database_session() as session:
            repo = ModelRepository(session)
            model = await repo.get_model_by_id(model_id)
            
            if not model:
                return None
            
            return ModelInfo(
                model_id=model.model_id,
                name=model.name,
                description=model.description,
                model_type=model.model_type,
                version=model.version,
                status=model.status,
                framework=model.framework,
                input_schema=model.input_schema,
                output_schema=model.output_schema,
                metrics=model.metrics,
                hyperparameters=model.hyperparameters,
                model_size=model.model_size,
                inference_time=model.inference_time,
                created_at=model.created_at,
                updated_at=model.updated_at
            )
    
    async def connect_model(self, model_id: str, force_reconnect: bool = False) -> bool:
        """Connect to a Google model endpoint with caching."""
        try:
            # Check if model is already connected and cached
            if not force_reconnect:
                cached_entry = self.cache.get(model_id)
                if cached_entry and cached_entry.status == ModelStatus.CONNECTED:
                    logger.debug(f"Model {model_id} already connected from cache")
                    return True
            
            # Prevent concurrent connection of the same model
            if model_id in self._connecting_models:
                logger.debug(f"Model {model_id} is already being connected, waiting...")
                await self._connecting_models[model_id].wait()
                return self.cache.get(model_id) is not None
            
            # Set connecting event
            self._connecting_models[model_id] = asyncio.Event()
            
            try:
                # Get model info from database or registry
                model_info = await self.get_model_info(model_id)
                if not model_info:
                    # Create default model info for Google models
                    model_info = ModelInfo(
                        model_id=model_id,
                        name=model_id,
                        description=f"Google model: {model_id}",
                        model_type="google_pretrained",
                        version="latest",
                        status="deployed",
                        framework="google_ai",
                        input_schema={"type": "text"},
                        output_schema={"type": "text"},
                        metrics={},
                        hyperparameters={},
                        model_size=0,  # API-based models don't have local size
                        inference_time=0.0,
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                
                # Connect to Google model
                connection_start = datetime.now()
                model_client = await self._connect_google_model(model_id, model_info)
                connection_time = datetime.now() - connection_start
                
                if model_client:
                    # Create cache entry
                    cache_entry = ModelCacheEntry(
                        model_name=model_id,
                        model_client=model_client,
                        model_info=model_info,
                        connection_time=connection_start,
                        access_time=datetime.now(),
                        access_count=1,
                        token_usage=0,
                        cost_estimate=0.0,
                        status=ModelStatus.CONNECTED,
                        lock=threading.Lock()
                    )
                    
                    # Add to cache
                    self.cache.put(model_id, cache_entry)
                    
                    # Initialize metrics
                    self._metrics[model_id] = ModelMetrics(
                        inference_time=0.0,
                        token_usage=0,
                        cost_per_request=0.0,
                        accuracy=0.0,
                        throughput=0.0,
                        error_rate=0.0,
                        cache_hit_rate=0.0,
                        rate_limit_hits=0
                    )
                    
                    logger.info(f"Model {model_id} connected successfully in {connection_time.total_seconds():.2f}s")
                    return True
                else:
                    logger.error(f"Failed to connect to model {model_id}")
                    return False
            
            finally:
                # Signal connection complete
                self._connecting_models[model_id].set()
                del self._connecting_models[model_id]
        
        except Exception as e:
            logger.error(f"Error connecting to model {model_id}: {str(e)}")
            return False
    
    async def _connect_google_model(self, model_id: str, model_info: ModelInfo) -> Any:
        """Connect to a specific Google model."""
        try:
            # Determine model type and create appropriate client
            if model_id in self.google_models.values() or "gemini" in model_id.lower():
                # Use Gemini client for Gemini models
                return self.gemini_client
            elif "embedding" in model_id.lower():
                # Use Vertex AI for embedding models
                return self.vertex_ai_client
            elif model_id in self.google_models:
                # Use mapped model
                mapped_model = self.google_models[model_id]
                if isinstance(mapped_model, GeminiModel):
                    return self.gemini_client
                else:
                    return self.vertex_ai_client
            else:
                # Default to Gemini client
                return self.gemini_client
                
        except Exception as e:
            logger.error(f"Failed to connect to Google model {model_id}: {e}")
            return None
    
    async def disconnect_model(self, model_id: str) -> bool:
        """Disconnect from a Google model endpoint with proper cleanup."""
        try:
            # Remove from cache
            cached_entry = self.cache.get(model_id)
            if cached_entry:
                with cached_entry.lock:
                    cached_entry.status = ModelStatus.DISCONNECTED
                    
                    # No GPU cleanup needed for API-based models
                    # Just remove from cache
                    self.cache.remove(model_id)
                    
                    # Clean up metrics
                    if model_id in self._metrics:
                        del self._metrics[model_id]
                    
                    # Force garbage collection
                    gc.collect()
                    
                    logger.info(f"Model {model_id} disconnected successfully")
                    return True
            
            return False
        
        except Exception as e:
            logger.error(f"Error disconnecting model {model_id}: {str(e)}")
            return False
    
    async def predict(self, model_id: str, input_data: Dict[str, Any], 
                     model_name: Optional[str] = None, version: Optional[str] = None) -> Any:
        """Make prediction using a Google model with caching and performance tracking."""
        prediction_start = datetime.now()
        
        try:
            # Resolve model ID if model name is provided
            if model_name:
                model_info = self.registry.get_model_info(model_name, version)
                if model_info:
                    model_id = model_info.model_id
                else:
                    # Use model name as ID for Google models
                    model_id = model_name
            
            # Ensure model is connected
            cached_entry = self.cache.get(model_id)
            if not cached_entry or cached_entry.status != ModelStatus.CONNECTED:
                success = await self.connect_model(model_id)
                if not success:
                    raise ValueError(f"Model {model_id} could not be connected")
                cached_entry = self.cache.get(model_id)
            
            # Make prediction with performance tracking
            with cached_entry.lock:
                model_client = cached_entry.model_client
                
                # Use appropriate client based on model type
                if model_client == self.gemini_client:
                    prediction = await self._predict_with_gemini(model_id, input_data)
                else:
                    prediction = await self._predict_with_vertex_ai(model_id, input_data)
                
                # Update metrics
                prediction_time = (datetime.now() - prediction_start).total_seconds()
                if model_id in self._metrics:
                    metrics = self._metrics[model_id]
                    metrics.inference_time = (metrics.inference_time + prediction_time) / 2
                    metrics.throughput = 1.0 / prediction_time
                    
                    # Update token usage from prediction result
                    if isinstance(prediction, dict) and "usage" in prediction:
                        tokens_used = prediction["usage"].get("total_tokens", 0)
                        metrics.token_usage += tokens_used
                        cached_entry.token_usage += tokens_used
                
                return prediction
        
        except Exception as e:
            # Update error metrics
            if model_id in self._metrics:
                self._metrics[model_id].error_rate += 0.01
            
            logger.error(f"Error making prediction with model {model_id}: {str(e)}")
            raise
    
    async def _predict_with_gemini(self, model_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Make prediction using Gemini model."""
        try:
            # Determine the appropriate Gemini model
            if model_id in self.google_models:
                gemini_model = self.google_models[model_id]
            elif "gemini" in model_id.lower():
                gemini_model = GeminiModel(model_id)
            else:
                gemini_model = GeminiModel.GEMINI_2_5_FLASH  # Default
            
            # Extract prompt from input data
            prompt = input_data.get("prompt", "")
            system_prompt = input_data.get("system_prompt")
            
            # Make prediction using Gemini client
            response = await self.gemini_client.generate_content(
                prompt=prompt,
                model=gemini_model,
                system_prompt=system_prompt
            )
            
            return response
        
        except Exception as e:
            logger.error(f"Error making Gemini prediction: {e}")
            raise
    
    async def _predict_with_vertex_ai(self, model_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Make prediction using Vertex AI model."""
        try:
            # For embeddings and other Vertex AI models
            if "embedding" in model_id.lower():
                # Handle embedding requests
                texts = input_data.get("texts", [])
                if isinstance(texts, str):
                    texts = [texts]
                
                embeddings = await self.gemini_client.generate_embeddings(
                    texts=texts,
                    model=model_id
                )
                
                return {
                    "embeddings": embeddings,
                    "model": model_id,
                    "usage": {
                        "total_tokens": sum(len(text.split()) for text in texts)
                    }
                }
            else:
                # Fallback to Gemini for other models
                return await self._predict_with_gemini(model_id, input_data)
        
        except Exception as e:
            logger.error(f"Error making Vertex AI prediction: {e}")
            raise
    
    async def delete_model(self, model_id: str) -> bool:
        """Disconnect from a Google model (cannot delete Google's models)."""
        try:
            # Disconnect model if connected
            await self.disconnect_model(model_id)
            
            # Remove from database if it's a custom entry
            async with get_database_session() as session:
                repo = ModelRepository(session)
                deleted = await repo.delete_model(model_id)
                await session.commit()
                
                if deleted:
                    logger.info(f"Model reference {model_id} deleted successfully")
                    return True
                else:
                    logger.info(f"Model reference {model_id} not found in database (Google model)")
                    return True  # Return True for Google models as they don't need DB deletion
        
        except Exception as e:
            logger.error(f"Error deleting model reference {model_id}: {str(e)}")
            return False
    
    async def get_training_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get training job status."""
        async with get_database_session() as session:
            repo = TrainingJobRepository(session)
            job = await repo.get_training_job_by_id(job_id)
            
            if not job:
                return None
            
            return {
                "job_id": job.job_id,
                "model_id": job.model_id,
                "status": job.status,
                "progress": job.progress,
                "message": job.message,
                "metrics": job.metrics,
                "created_at": job.created_at,
                "updated_at": job.updated_at
            }
    
    async def cleanup(self):
        """Comprehensive cleanup of all resources."""
        try:
            # Disconnect from all models
            for model_id in list(self.cache._cache.keys()):
                await self.disconnect_model(model_id)
            
            # Clear cache
            self.cache.clear()
            
            # Clear metrics
            self._metrics.clear()
            
            # Force garbage collection
            gc.collect()
            
            logger.info("ML Manager cleanup completed")
        
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get detailed cache statistics."""
        stats = self.cache.stats()
        stats["gpu_info"] = self.gpu_info
        stats["loaded_models"] = len(self.cache._cache)
        stats["loading_models"] = len(self._loading_models)
        return stats
    
    def get_model_metrics(self, model_id: str) -> Optional[ModelMetrics]:
        """Get performance metrics for a model."""
        return self._metrics.get(model_id)
    
    def get_all_metrics(self) -> Dict[str, ModelMetrics]:
        """Get performance metrics for all models."""
        return self._metrics.copy()
    
    async def warmup_model(self, model_id: str, sample_input: Dict[str, Any]) -> bool:
        """Warm up a Google model connection with sample input."""
        try:
            # Connect to model
            success = await self.connect_model(model_id)
            if not success:
                return False
            
            # Run warmup predictions
            logger.info(f"Warming up model connection {model_id}")
            for i in range(2):  # Reduced warmup calls for API-based models
                await self.predict(model_id, sample_input)
            
            logger.info(f"Model {model_id} warmed up successfully")
            return True
        
        except Exception as e:
            logger.error(f"Error warming up model {model_id}: {e}")
            return False
    
    async def batch_predict(self, model_id: str, batch_input: List[Dict[str, Any]],
                           batch_size: int = 10) -> List[Any]:  # Reduced batch size for API rate limits
        """Make batch predictions with optimized processing for Google models."""
        try:
            # Ensure model is connected
            cached_entry = self.cache.get(model_id)
            if not cached_entry or cached_entry.status != ModelStatus.CONNECTED:
                success = await self.connect_model(model_id)
                if not success:
                    raise ValueError(f"Model {model_id} could not be connected")
                cached_entry = self.cache.get(model_id)
            
            # Process in smaller batches to respect API rate limits
            results = []
            with cached_entry.lock:
                model_client = cached_entry.model_client
                
                for i in range(0, len(batch_input), batch_size):
                    batch = batch_input[i:i + batch_size]
                    
                    # Process each item in the batch
                    batch_results = []
                    for item in batch:
                        try:
                            result = await self.predict(model_id, item)
                            batch_results.append(result)
                            
                            # Small delay to avoid rate limiting
                            await asyncio.sleep(0.1)
                        except Exception as e:
                            logger.warning(f"Batch item failed: {e}")
                            batch_results.append({"error": str(e)})
                    
                    results.extend(batch_results)
            
            return results
        
        except Exception as e:
            logger.error(f"Error in batch prediction with model {model_id}: {e}")
            raise
    
    @property
    def connected_models(self) -> List[str]:
        """Get list of connected model IDs."""
        return [model_id for model_id, entry in self.cache._cache.items() 
                if entry.status == ModelStatus.CONNECTED]
    
    @property
    def loaded_models(self) -> List[str]:
        """Get list of connected model IDs (for backward compatibility)."""
        return self.connected_models
    
    @property
    def usage_stats(self) -> Dict[str, Any]:
        """Get comprehensive usage statistics for Google models."""
        stats = self.cache.stats()
        
        # Calculate total token usage and costs
        total_tokens = sum(entry.token_usage for entry in self.cache._cache.values())
        total_cost = sum(entry.cost_estimate for entry in self.cache._cache.values())
        
        return {
            "cache_stats": stats,
            "token_usage": {
                "total_tokens": total_tokens,
                "estimated_cost": total_cost
            },
            "model_count": len(self.cache._cache),
            "connecting_count": len(self._connecting_models),
            "metrics_count": len(self._metrics)
        }
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status."""
        return {
            "status": "healthy",
            "connected_models": len(self.connected_models),
            "cache_stats": self.cache.stats(),
            "google_models_available": {
                "gemini_client": self.gemini_client is not None,
                "vertex_ai_client": self.vertex_ai_client is not None
            },
            "usage_stats": self.usage_stats,
            "active_tasks": {
                "cleanup": self._cleanup_task is not None and not self._cleanup_task.done(),
                "monitoring": self._monitoring_task is not None and not self._monitoring_task.done()
            }
        }


# Global ML manager instance
_ml_manager: Optional[MLManager] = None


async def init_ml_manager(settings) -> None:
    """Initialize global ML manager."""
    global _ml_manager
    
    try:
        _ml_manager = MLManager()
        await _ml_manager.start()
        logger.info("ML manager initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize ML manager: {str(e)}")
        raise


async def close_ml_manager() -> None:
    """Close global ML manager."""
    global _ml_manager
    
    if _ml_manager:
        try:
            await _ml_manager.stop()
            logger.info("ML manager closed successfully")
        except Exception as e:
            logger.error(f"Error closing ML manager: {str(e)}")
    
    _ml_manager = None


def get_ml_manager() -> MLManager:
    """Get ML manager instance."""
    global _ml_manager
    if _ml_manager is None:
        _ml_manager = MLManager()
    return _ml_manager


@asynccontextmanager
async def managed_model(model_id: str, manager: Optional[MLManager] = None):
    """Context manager for automatic model connection and cleanup."""
    if manager is None:
        manager = get_ml_manager()
    
    try:
        success = await manager.connect_model(model_id)
        if not success:
            raise ValueError(f"Failed to connect to model {model_id}")
        
        yield manager.cache.get(model_id)
    
    finally:
        # Model connection will be automatically cleaned up by the manager's cleanup loop
        pass