"""
Gemini Pattern Analysis Service

Advanced pattern analysis using Gemini 2.5 Pro for complex reasoning,
code explanation, documentation generation, and anti-pattern detection.
"""

import json
import asyncio
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import logging
from datetime import datetime
import hashlib
import structlog

from .gemini_client import Gemini<PERSON><PERSON>, create_gemini_client
from ..config.gemini import get_gemini_config, GeminiModel
from ..models.patterns import PatternType, Pattern, AntiPattern
from ..utils.validation import validate_input


logger = structlog.get_logger(__name__)


class AnalysisType(Enum):
    """Analysis type enumeration."""
    PATTERN_DETECTION = "pattern_detection"
    CODE_EXPLANATION = "code_explanation"
    DOCUMENTATION_GENERATION = "documentation_generation"
    ANTI_PATTERN_DETECTION = "anti_pattern_detection"
    REFACTORING_SUGGESTIONS = "refactoring_suggestions"
    SECURITY_ANALYSIS = "security_analysis"
    PERFORMANCE_ANALYSIS = "performance_analysis"
    DESIGN_PATTERN_RECOGNITION = "design_pattern_recognition"


@dataclass
class AnalysisResult:
    """Analysis result container."""
    analysis_type: AnalysisType
    confidence: float
    findings: List[Dict[str, Any]]
    explanation: str
    recommendations: List[str]
    metadata: Dict[str, Any]
    processing_time: float
    model_used: str
    tokens_used: int


@dataclass
class CodeContext:
    """Code context for analysis."""
    code: str
    language: str
    file_path: Optional[str] = None
    project_context: Optional[str] = None
    dependencies: List[str] = None
    complexity_score: Optional[float] = None
    line_count: Optional[int] = None


class GeminiAnalyzer:
    """Advanced pattern analyzer using Gemini 2.5 Pro."""
    
    def __init__(self, client: Optional[GeminiClient] = None):
        """Initialize the analyzer."""
        self.client = client
        self.config = get_gemini_config()
        self._analysis_prompts = self._load_analysis_prompts()
        self._pattern_definitions = self._load_pattern_definitions()
    
    async def __aenter__(self):
        """Async context manager entry."""
        if self.client is None:
            self.client = await create_gemini_client(self.config)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.client:
            await self.client.close()
    
    def _load_analysis_prompts(self) -> Dict[str, str]:
        """Load analysis prompts for different analysis types."""
        return {
            AnalysisType.PATTERN_DETECTION: """
            You are an expert software architect and pattern recognition specialist.
            
            Analyze the provided code for design patterns, architectural patterns, and code patterns.
            
            Focus on:
            1. Design patterns (GoF, architectural, etc.)
            2. Code organization patterns
            3. Error handling patterns
            4. Concurrency patterns
            5. Data access patterns
            6. Performance patterns
            
            For each pattern found, provide:
            - Pattern name and type
            - Confidence score (0-100)
            - Code location/lines
            - Implementation quality (1-10)
            - Adherence to best practices
            - Potential improvements
            
            Response format: JSON with detailed analysis.
            """,
            
            AnalysisType.CODE_EXPLANATION: """
            You are an expert software engineer and technical writer.
            
            Analyze the provided code and generate comprehensive explanations.
            
            Provide:
            1. High-level overview of functionality
            2. Detailed explanation of key components
            3. Data flow and algorithm explanation
            4. Dependencies and interactions
            5. Performance characteristics
            6. Potential edge cases
            
            Make explanations clear and suitable for different audiences:
            - Junior developers
            - Senior developers
            - Technical leads
            - Documentation purposes
            
            Response format: JSON with structured explanations.
            """,
            
            AnalysisType.DOCUMENTATION_GENERATION: """
            You are an expert technical writer and documentation specialist.
            
            Generate comprehensive documentation for the provided code.
            
            Include:
            1. API documentation (if applicable)
            2. Usage examples
            3. Parameter descriptions
            4. Return value explanations
            5. Error handling documentation
            6. Performance notes
            7. Best practices
            8. Common pitfalls
            
            Format the documentation in Markdown with proper structure.
            
            Response format: JSON with documentation content.
            """,
            
            AnalysisType.ANTI_PATTERN_DETECTION: """
            You are an expert code reviewer and software quality specialist.
            
            Analyze the provided code for anti-patterns and code smells.
            
            Detect:
            1. Code smells (long methods, large classes, etc.)
            2. Anti-patterns (God Object, Spaghetti Code, etc.)
            3. Performance issues
            4. Security vulnerabilities
            5. Maintainability problems
            6. Design flaws
            
            For each issue found, provide:
            - Issue type and severity
            - Confidence score (0-100)
            - Code location
            - Impact assessment
            - Refactoring recommendations
            - Prevention strategies
            
            Response format: JSON with detailed analysis.
            """,
            
            AnalysisType.REFACTORING_SUGGESTIONS: """
            You are an expert software refactoring specialist.
            
            Analyze the provided code and suggest refactoring improvements.
            
            Focus on:
            1. Code structure improvements
            2. Performance optimizations
            3. Readability enhancements
            4. Maintainability improvements
            5. Design pattern applications
            6. Error handling improvements
            
            For each suggestion, provide:
            - Refactoring type
            - Priority level (1-5)
            - Effort estimation
            - Expected benefits
            - Implementation steps
            - Code examples
            
            Response format: JSON with refactoring plan.
            """,
            
            AnalysisType.SECURITY_ANALYSIS: """
            You are an expert security analyst and secure coding specialist.
            
            Analyze the provided code for security vulnerabilities and issues.
            
            Check for:
            1. OWASP Top 10 vulnerabilities
            2. Input validation issues
            3. Authentication/authorization flaws
            4. Data exposure risks
            5. Injection vulnerabilities
            6. Cryptographic issues
            
            For each finding, provide:
            - Vulnerability type and severity
            - CVSS score if applicable
            - Exploitation potential
            - Remediation steps
            - Prevention measures
            - Security best practices
            
            Response format: JSON with security analysis.
            """,
            
            AnalysisType.PERFORMANCE_ANALYSIS: """
            You are an expert performance engineer and optimization specialist.
            
            Analyze the provided code for performance issues and optimization opportunities.
            
            Evaluate:
            1. Time complexity
            2. Space complexity
            3. Memory usage patterns
            4. I/O operations
            5. Concurrency issues
            6. Resource utilization
            
            For each finding, provide:
            - Performance issue type
            - Impact assessment
            - Optimization suggestions
            - Expected improvements
            - Implementation complexity
            - Trade-offs
            
            Response format: JSON with performance analysis.
            """,
            
            AnalysisType.DESIGN_PATTERN_RECOGNITION: """
            You are an expert software architect and design pattern specialist.
            
            Analyze the provided code for design pattern usage and opportunities.
            
            Identify:
            1. Existing design patterns
            2. Pattern implementation quality
            3. Missed pattern opportunities
            4. Pattern misuse
            5. Architectural patterns
            6. Domain-specific patterns
            
            For each pattern, provide:
            - Pattern name and category
            - Implementation quality (1-10)
            - Adherence to principles
            - Improvement suggestions
            - Alternative patterns
            - Usage recommendations
            
            Response format: JSON with pattern analysis.
            """
        }
    
    def _load_pattern_definitions(self) -> Dict[str, Dict[str, Any]]:
        """Load pattern definitions and characteristics."""
        return {
            "design_patterns": {
                "creational": [
                    "Singleton", "Factory Method", "Abstract Factory", "Builder", "Prototype"
                ],
                "structural": [
                    "Adapter", "Bridge", "Composite", "Decorator", "Facade", "Flyweight", "Proxy"
                ],
                "behavioral": [
                    "Chain of Responsibility", "Command", "Interpreter", "Iterator", 
                    "Mediator", "Memento", "Observer", "State", "Strategy", "Template Method", "Visitor"
                ]
            },
            "architectural_patterns": [
                "MVC", "MVP", "MVVM", "Layered", "Hexagonal", "Clean Architecture", 
                "Microservices", "Event-Driven", "CQRS", "Event Sourcing"
            ],
            "anti_patterns": [
                "God Object", "Spaghetti Code", "Copy-Paste Programming", "Hard Coding",
                "Magic Numbers", "Dead Code", "Duplicate Code", "Long Method", "Large Class"
            ]
        }
    
    async def analyze_patterns(
        self,
        context: CodeContext,
        analysis_types: List[AnalysisType] = None
    ) -> Dict[AnalysisType, AnalysisResult]:
        """Analyze code patterns using multiple analysis types."""
        if analysis_types is None:
            analysis_types = [AnalysisType.PATTERN_DETECTION]
        
        results = {}
        
        for analysis_type in analysis_types:
            try:
                result = await self._perform_analysis(context, analysis_type)
                results[analysis_type] = result
            except Exception as e:
                logger.error(f"Analysis failed for {analysis_type}: {e}")
                results[analysis_type] = self._create_error_result(analysis_type, str(e))
        
        return results
    
    async def _perform_analysis(
        self,
        context: CodeContext,
        analysis_type: AnalysisType
    ) -> AnalysisResult:
        """Perform specific analysis type."""
        start_time = asyncio.get_event_loop().time()
        
        # Prepare the prompt
        system_prompt = self._analysis_prompts[analysis_type]
        user_prompt = self._create_analysis_prompt(context, analysis_type)
        
        # Select appropriate model
        model = self._select_model(analysis_type)
        
        # Generate analysis
        response = await self.client.generate_content(
            prompt=user_prompt,
            system_prompt=system_prompt,
            model=model
        )
        
        # Process response
        analysis_result = self._process_analysis_response(
            response, analysis_type, context
        )
        
        processing_time = asyncio.get_event_loop().time() - start_time
        
        return AnalysisResult(
            analysis_type=analysis_type,
            confidence=analysis_result.get("confidence", 0),
            findings=analysis_result.get("findings", []),
            explanation=analysis_result.get("explanation", ""),
            recommendations=analysis_result.get("recommendations", []),
            metadata=analysis_result.get("metadata", {}),
            processing_time=processing_time,
            model_used=model.value,
            tokens_used=response.get("usage", {}).get("total_tokens", 0)
        )
    
    def _create_analysis_prompt(
        self,
        context: CodeContext,
        analysis_type: AnalysisType
    ) -> str:
        """Create analysis prompt for specific type."""
        base_prompt = f"""
        Language: {context.language}
        Analysis Type: {analysis_type.value}
        """
        
        if context.file_path:
            base_prompt += f"\nFile Path: {context.file_path}"
        
        if context.project_context:
            base_prompt += f"\nProject Context: {context.project_context}"
        
        if context.dependencies:
            base_prompt += f"\nDependencies: {', '.join(context.dependencies)}"
        
        if context.complexity_score:
            base_prompt += f"\nComplexity Score: {context.complexity_score}"
        
        base_prompt += f"""
        
        Code to analyze:
        ```{context.language}
        {context.code}
        ```
        
        Additional context:
        - Lines of code: {context.line_count or len(context.code.splitlines())}
        - Pattern definitions: {json.dumps(self._pattern_definitions, indent=2)}
        
        Please provide a comprehensive analysis following the specified format.
        """
        
        return base_prompt
    
    def _select_model(self, analysis_type: AnalysisType) -> GeminiModel:
        """Select appropriate model for analysis type."""
        if analysis_type == AnalysisType.PATTERN_DETECTION:
            return self.config.pattern_analysis_model
        elif analysis_type == AnalysisType.CODE_EXPLANATION:
            return self.config.code_explanation_model
        elif analysis_type == AnalysisType.ANTI_PATTERN_DETECTION:
            return self.config.anti_pattern_detection_model
        else:
            return self.config.default_model
    
    def _process_analysis_response(
        self,
        response: Dict[str, Any],
        analysis_type: AnalysisType,
        context: CodeContext
    ) -> Dict[str, Any]:
        """Process analysis response from Gemini."""
        try:
            # Try to parse as JSON
            result = json.loads(response["text"])
            
            # Validate and enhance the result
            return self._validate_and_enhance_result(result, analysis_type, context)
            
        except json.JSONDecodeError:
            # Fallback to text processing
            return self._process_text_response(response["text"], analysis_type, context)
    
    def _validate_and_enhance_result(
        self,
        result: Dict[str, Any],
        analysis_type: AnalysisType,
        context: CodeContext
    ) -> Dict[str, Any]:
        """Validate and enhance analysis result."""
        # Ensure required fields
        if "confidence" not in result:
            result["confidence"] = 50
        
        if "findings" not in result:
            result["findings"] = []
        
        if "explanation" not in result:
            result["explanation"] = "Analysis completed"
        
        if "recommendations" not in result:
            result["recommendations"] = []
        
        # Add metadata
        result["metadata"] = {
            "language": context.language,
            "analysis_type": analysis_type.value,
            "file_path": context.file_path,
            "line_count": context.line_count or len(context.code.splitlines()),
            "timestamp": datetime.now().isoformat()
        }
        
        # Validate confidence score
        if not isinstance(result["confidence"], (int, float)):
            result["confidence"] = 50
        result["confidence"] = max(0, min(100, result["confidence"]))
        
        return result
    
    def _process_text_response(
        self,
        text: str,
        analysis_type: AnalysisType,
        context: CodeContext
    ) -> Dict[str, Any]:
        """Process text response when JSON parsing fails."""
        return {
            "confidence": 50,
            "findings": [{"type": "text_analysis", "content": text}],
            "explanation": text,
            "recommendations": [],
            "metadata": {
                "language": context.language,
                "analysis_type": analysis_type.value,
                "format": "text",
                "timestamp": datetime.now().isoformat()
            }
        }
    
    def _create_error_result(
        self,
        analysis_type: AnalysisType,
        error_message: str
    ) -> AnalysisResult:
        """Create error result for failed analysis."""
        return AnalysisResult(
            analysis_type=analysis_type,
            confidence=0,
            findings=[],
            explanation=f"Analysis failed: {error_message}",
            recommendations=[],
            metadata={"error": error_message},
            processing_time=0,
            model_used="none",
            tokens_used=0
        )
    
    async def explain_code(
        self,
        context: CodeContext,
        audience: str = "developer",
        detail_level: str = "medium"
    ) -> Dict[str, Any]:
        """Generate code explanation."""
        enhanced_prompt = f"""
        Generate a code explanation for audience: {audience}
        Detail level: {detail_level}
        
        Tailor the explanation appropriately:
        - junior: Simple terms, basic concepts
        - senior: Technical depth, patterns, trade-offs
        - architect: Design decisions, scalability, maintainability
        - documentation: Formal, structured, comprehensive
        
        Detail levels:
        - basic: High-level overview
        - medium: Detailed explanation with examples
        - comprehensive: Full analysis with alternatives
        """
        
        result = await self._perform_analysis(context, AnalysisType.CODE_EXPLANATION)
        
        return {
            "explanation": result.explanation,
            "audience": audience,
            "detail_level": detail_level,
            "confidence": result.confidence,
            "metadata": result.metadata
        }
    
    async def generate_documentation(
        self,
        context: CodeContext,
        doc_type: str = "api",
        format: str = "markdown"
    ) -> Dict[str, Any]:
        """Generate documentation."""
        enhanced_prompt = f"""
        Generate {doc_type} documentation in {format} format.
        
        Documentation types:
        - api: API reference with parameters, returns, examples
        - user: User guide with usage examples
        - developer: Technical documentation for maintainers
        - readme: Project README documentation
        """
        
        result = await self._perform_analysis(context, AnalysisType.DOCUMENTATION_GENERATION)
        
        return {
            "documentation": result.explanation,
            "doc_type": doc_type,
            "format": format,
            "confidence": result.confidence,
            "metadata": result.metadata
        }
    
    async def detect_anti_patterns(
        self,
        context: CodeContext,
        severity_threshold: int = 3
    ) -> List[Dict[str, Any]]:
        """Detect anti-patterns and code smells."""
        result = await self._perform_analysis(context, AnalysisType.ANTI_PATTERN_DETECTION)
        
        # Filter by severity
        filtered_findings = [
            finding for finding in result.findings
            if finding.get("severity", 0) >= severity_threshold
        ]
        
        return {
            "anti_patterns": filtered_findings,
            "total_found": len(filtered_findings),
            "confidence": result.confidence,
            "recommendations": result.recommendations,
            "metadata": result.metadata
        }
    
    async def suggest_refactoring(
        self,
        context: CodeContext,
        priority_threshold: int = 3
    ) -> Dict[str, Any]:
        """Suggest refactoring improvements."""
        result = await self._perform_analysis(context, AnalysisType.REFACTORING_SUGGESTIONS)
        
        # Filter by priority
        high_priority_suggestions = [
            suggestion for suggestion in result.findings
            if suggestion.get("priority", 0) >= priority_threshold
        ]
        
        return {
            "refactoring_suggestions": high_priority_suggestions,
            "total_suggestions": len(result.findings),
            "high_priority_count": len(high_priority_suggestions),
            "confidence": result.confidence,
            "recommendations": result.recommendations,
            "metadata": result.metadata
        }
    
    async def analyze_security(
        self,
        context: CodeContext,
        include_owasp: bool = True
    ) -> Dict[str, Any]:
        """Analyze security vulnerabilities."""
        result = await self._perform_analysis(context, AnalysisType.SECURITY_ANALYSIS)
        
        # Categorize findings
        vulnerabilities = []
        for finding in result.findings:
            if include_owasp or finding.get("category") != "owasp":
                vulnerabilities.append(finding)
        
        return {
            "vulnerabilities": vulnerabilities,
            "total_found": len(vulnerabilities),
            "high_severity": len([v for v in vulnerabilities if v.get("severity") == "high"]),
            "confidence": result.confidence,
            "recommendations": result.recommendations,
            "metadata": result.metadata
        }
    
    async def analyze_performance(
        self,
        context: CodeContext,
        focus_areas: List[str] = None
    ) -> Dict[str, Any]:
        """Analyze performance issues."""
        if focus_areas is None:
            focus_areas = ["time_complexity", "space_complexity", "memory_usage"]
        
        result = await self._perform_analysis(context, AnalysisType.PERFORMANCE_ANALYSIS)
        
        # Filter by focus areas
        filtered_findings = []
        for finding in result.findings:
            if any(area in finding.get("category", "") for area in focus_areas):
                filtered_findings.append(finding)
        
        return {
            "performance_issues": filtered_findings,
            "focus_areas": focus_areas,
            "total_found": len(filtered_findings),
            "confidence": result.confidence,
            "recommendations": result.recommendations,
            "metadata": result.metadata
        }
    
    async def batch_analyze(
        self,
        contexts: List[CodeContext],
        analysis_types: List[AnalysisType] = None
    ) -> Dict[str, Dict[AnalysisType, AnalysisResult]]:
        """Perform batch analysis on multiple code contexts."""
        results = {}
        
        # Process in parallel with semaphore limit
        semaphore = asyncio.Semaphore(self.config.max_parallel_requests)
        
        async def analyze_single(context: CodeContext) -> Tuple[str, Dict[AnalysisType, AnalysisResult]]:
            async with semaphore:
                key = context.file_path or f"code_{hash(context.code)}"
                result = await self.analyze_patterns(context, analysis_types)
                return key, result
        
        # Execute analyses
        tasks = [analyze_single(context) for context in contexts]
        completed_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for result in completed_results:
            if isinstance(result, Exception):
                logger.error(f"Batch analysis failed: {result}")
                continue
            
            key, analysis_result = result
            results[key] = analysis_result
        
        return results
    
    def get_analysis_summary(
        self,
        results: Dict[AnalysisType, AnalysisResult]
    ) -> Dict[str, Any]:
        """Get summary of analysis results."""
        summary = {
            "total_analyses": len(results),
            "average_confidence": 0,
            "total_findings": 0,
            "total_recommendations": 0,
            "total_processing_time": 0,
            "total_tokens_used": 0,
            "analysis_breakdown": {}
        }
        
        if not results:
            return summary
        
        # Calculate metrics
        confidences = []
        for analysis_type, result in results.items():
            summary["total_findings"] += len(result.findings)
            summary["total_recommendations"] += len(result.recommendations)
            summary["total_processing_time"] += result.processing_time
            summary["total_tokens_used"] += result.tokens_used
            confidences.append(result.confidence)
            
            summary["analysis_breakdown"][analysis_type.value] = {
                "confidence": result.confidence,
                "findings_count": len(result.findings),
                "recommendations_count": len(result.recommendations),
                "processing_time": result.processing_time,
                "tokens_used": result.tokens_used,
                "model_used": result.model_used
            }
        
        summary["average_confidence"] = sum(confidences) / len(confidences)
        
        return summary


# Factory function
async def create_gemini_analyzer(client: Optional[GeminiClient] = None) -> GeminiAnalyzer:
    """Create a Gemini analyzer instance."""
    analyzer = GeminiAnalyzer(client)
    if client is None:
        await analyzer.__aenter__()
    return analyzer