"""
Machine Learning Module

ML model management, training, inference, and Gemini API integration.
"""

from .manager import <PERSON><PERSON><PERSON><PERSON>, get_ml_manager
from .trainer import <PERSON><PERSON>rainer, get_model_trainer
from .inference import InferenceEngine, get_inference_engine
from .gemini_client import GeminiClient, create_gemini_client
from .gemini_analyzer import GeminiAnalyzer, create_gemini_analyzer, AnalysisType, CodeContext
from .gemini_embeddings import GeminiEmbeddingService, create_embedding_service, EmbeddingType
from .gemini_integration import GeminiIntegration, create_gemini_integration, IntegrationMode

__all__ = [
    "MLManager",
    "get_ml_manager",
    "ModelTrainer",
    "get_model_trainer",
    "InferenceEngine",
    "get_inference_engine",
    "GeminiClient",
    "create_gemini_client",
    "GeminiAnalyzer",
    "create_gemini_analyzer",
    "AnalysisType",
    "CodeContext",
    "GeminiEmbeddingService",
    "create_embedding_service",
    "EmbeddingType",
    "GeminiIntegration",
    "create_gemini_integration",
    "IntegrationMode"
]