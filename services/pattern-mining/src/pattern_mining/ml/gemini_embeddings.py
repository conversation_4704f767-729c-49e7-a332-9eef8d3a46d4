"""
Gemini Embeddings Service

Code embedding generation, similarity search optimization, vector storage
and retrieval using Gemini's embedding models.
"""

import asyncio
import numpy as np
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import logging
from datetime import datetime
import hashlib
import json
import pickle
from pathlib import Path
import structlog
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import KMeans
import faiss

from .gemini_client import GeminiClient, create_gemini_client
from ..config.gemini import get_gemini_config
from ..utils.validation import validate_input


logger = structlog.get_logger(__name__)


class EmbeddingType(Enum):
    """Embedding type enumeration."""
    CODE = "code"
    DOCUMENTATION = "documentation"
    COMMENT = "comment"
    FUNCTION = "function"
    CLASS = "class"
    MODULE = "module"
    PATTERN = "pattern"
    QUERY = "query"


@dataclass
class EmbeddingMetadata:
    """Embedding metadata."""
    embedding_id: str
    embedding_type: EmbeddingType
    source_code: str
    language: str
    file_path: Optional[str] = None
    function_name: Optional[str] = None
    class_name: Optional[str] = None
    line_numbers: Optional[Tuple[int, int]] = None
    complexity_score: Optional[float] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


@dataclass
class SimilarityResult:
    """Similarity search result."""
    embedding_id: str
    similarity_score: float
    metadata: EmbeddingMetadata
    distance: float
    rank: int


@dataclass
class ClusterResult:
    """Clustering result."""
    cluster_id: int
    cluster_center: np.ndarray
    embedding_ids: List[str]
    cluster_size: int
    inertia: float
    representative_embeddings: List[str]


class EmbeddingCache:
    """In-memory embedding cache with persistence."""
    
    def __init__(self, max_size: int = 10000, cache_file: Optional[str] = None):
        self.max_size = max_size
        self.cache_file = cache_file
        self.cache: Dict[str, np.ndarray] = {}
        self.metadata_cache: Dict[str, EmbeddingMetadata] = {}
        self.access_order: List[str] = []
        
        if cache_file and Path(cache_file).exists():
            self._load_cache()
    
    def get(self, key: str) -> Optional[Tuple[np.ndarray, EmbeddingMetadata]]:
        """Get embedding from cache."""
        if key in self.cache:
            # Update access order
            self.access_order.remove(key)
            self.access_order.append(key)
            return self.cache[key], self.metadata_cache[key]
        return None
    
    def put(self, key: str, embedding: np.ndarray, metadata: EmbeddingMetadata):
        """Put embedding in cache."""
        if len(self.cache) >= self.max_size:
            # Remove least recently used
            lru_key = self.access_order.pop(0)
            del self.cache[lru_key]
            del self.metadata_cache[lru_key]
        
        self.cache[key] = embedding
        self.metadata_cache[key] = metadata
        self.access_order.append(key)
    
    def _load_cache(self):
        """Load cache from file."""
        try:
            with open(self.cache_file, 'rb') as f:
                data = pickle.load(f)
                self.cache = data.get('cache', {})
                self.metadata_cache = data.get('metadata_cache', {})
                self.access_order = data.get('access_order', [])
        except Exception as e:
            logger.warning(f"Failed to load cache: {e}")
    
    def save_cache(self):
        """Save cache to file."""
        if not self.cache_file:
            return
        
        try:
            with open(self.cache_file, 'wb') as f:
                pickle.dump({
                    'cache': self.cache,
                    'metadata_cache': self.metadata_cache,
                    'access_order': self.access_order
                }, f)
        except Exception as e:
            logger.warning(f"Failed to save cache: {e}")
    
    def clear(self):
        """Clear cache."""
        self.cache.clear()
        self.metadata_cache.clear()
        self.access_order.clear()


class VectorIndex:
    """FAISS-based vector index for similarity search."""
    
    def __init__(self, dimension: int = 768, index_type: str = "flat"):
        self.dimension = dimension
        self.index_type = index_type
        self.index = None
        self.embedding_ids: List[str] = []
        self.metadata_store: Dict[str, EmbeddingMetadata] = {}
        
        self._create_index()
    
    def _create_index(self):
        """Create FAISS index."""
        if self.index_type == "flat":
            self.index = faiss.IndexFlatIP(self.dimension)  # Inner product (cosine similarity)
        elif self.index_type == "ivf":
            quantizer = faiss.IndexFlatIP(self.dimension)
            self.index = faiss.IndexIVFFlat(quantizer, self.dimension, 100)
        elif self.index_type == "hnsw":
            self.index = faiss.IndexHNSWFlat(self.dimension, 32)
        else:
            raise ValueError(f"Unsupported index type: {self.index_type}")
    
    def add_embeddings(
        self,
        embeddings: np.ndarray,
        embedding_ids: List[str],
        metadata: List[EmbeddingMetadata]
    ):
        """Add embeddings to index."""
        if embeddings.shape[1] != self.dimension:
            raise ValueError(f"Embedding dimension {embeddings.shape[1]} != {self.dimension}")
        
        # Normalize embeddings for cosine similarity
        normalized_embeddings = embeddings / np.linalg.norm(embeddings, axis=1, keepdims=True)
        
        # Add to index
        if self.index_type == "ivf" and not self.index.is_trained:
            self.index.train(normalized_embeddings)
        
        self.index.add(normalized_embeddings.astype(np.float32))
        
        # Store metadata
        for i, (embedding_id, meta) in enumerate(zip(embedding_ids, metadata)):
            self.embedding_ids.append(embedding_id)
            self.metadata_store[embedding_id] = meta
    
    def search(
        self,
        query_embedding: np.ndarray,
        k: int = 10,
        threshold: float = 0.0
    ) -> List[SimilarityResult]:
        """Search for similar embeddings."""
        if query_embedding.shape[0] != self.dimension:
            raise ValueError(f"Query embedding dimension {query_embedding.shape[0]} != {self.dimension}")
        
        # Normalize query
        normalized_query = query_embedding / np.linalg.norm(query_embedding)
        normalized_query = normalized_query.reshape(1, -1).astype(np.float32)
        
        # Search
        scores, indices = self.index.search(normalized_query, k)
        
        # Convert to results
        results = []
        for rank, (score, idx) in enumerate(zip(scores[0], indices[0])):
            if score >= threshold and idx < len(self.embedding_ids):
                embedding_id = self.embedding_ids[idx]
                results.append(SimilarityResult(
                    embedding_id=embedding_id,
                    similarity_score=float(score),
                    metadata=self.metadata_store[embedding_id],
                    distance=1.0 - float(score),
                    rank=rank
                ))
        
        return results
    
    def get_size(self) -> int:
        """Get index size."""
        return self.index.ntotal
    
    def save_index(self, filepath: str):
        """Save index to file."""
        faiss.write_index(self.index, filepath)
        
        # Save metadata
        metadata_file = filepath + ".metadata"
        with open(metadata_file, 'wb') as f:
            pickle.dump({
                'embedding_ids': self.embedding_ids,
                'metadata_store': self.metadata_store,
                'dimension': self.dimension,
                'index_type': self.index_type
            }, f)
    
    def load_index(self, filepath: str):
        """Load index from file."""
        self.index = faiss.read_index(filepath)
        
        # Load metadata
        metadata_file = filepath + ".metadata"
        with open(metadata_file, 'rb') as f:
            data = pickle.load(f)
            self.embedding_ids = data['embedding_ids']
            self.metadata_store = data['metadata_store']
            self.dimension = data['dimension']
            self.index_type = data['index_type']


class GeminiEmbeddingService:
    """Advanced embedding service using Gemini."""
    
    def __init__(self, client: Optional[GeminiClient] = None):
        """Initialize embedding service."""
        self.client = client
        self.config = get_gemini_config()
        self.cache = EmbeddingCache(
            max_size=self.config.cache_max_size,
            cache_file="embeddings_cache.pkl"
        )
        self.vector_index = VectorIndex(
            dimension=self.config.embedding_dimension,
            index_type="hnsw"
        )
        
        # Performance metrics
        self.metrics = {
            "embeddings_generated": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "searches_performed": 0,
            "total_processing_time": 0
        }
    
    async def __aenter__(self):
        """Async context manager entry."""
        if self.client is None:
            self.client = await create_gemini_client(self.config)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        self.cache.save_cache()
        if self.client:
            await self.client.close()
    
    def _generate_embedding_id(self, content: str, embedding_type: EmbeddingType) -> str:
        """Generate unique embedding ID."""
        content_hash = hashlib.sha256(content.encode()).hexdigest()
        return f"{embedding_type.value}:{content_hash[:16]}"
    
    def _create_cache_key(self, content: str, embedding_type: EmbeddingType) -> str:
        """Create cache key for embedding."""
        return f"{embedding_type.value}:{hashlib.md5(content.encode()).hexdigest()}"
    
    def _preprocess_code(self, code: str, language: str) -> str:
        """Preprocess code for embedding."""
        # Remove comments and extra whitespace
        lines = code.split('\n')
        processed_lines = []
        
        for line in lines:
            stripped = line.strip()
            if stripped and not stripped.startswith('#'):  # Basic comment removal
                processed_lines.append(stripped)
        
        return '\n'.join(processed_lines)
    
    async def generate_embedding(
        self,
        content: str,
        embedding_type: EmbeddingType,
        language: str = "python",
        metadata: Optional[Dict[str, Any]] = None
    ) -> Tuple[np.ndarray, EmbeddingMetadata]:
        """Generate embedding for content."""
        start_time = asyncio.get_event_loop().time()
        
        # Check cache first
        cache_key = self._create_cache_key(content, embedding_type)
        cached_result = self.cache.get(cache_key)
        
        if cached_result:
            self.metrics["cache_hits"] += 1
            return cached_result
        
        self.metrics["cache_misses"] += 1
        
        try:
            # Preprocess content
            if embedding_type == EmbeddingType.CODE:
                processed_content = self._preprocess_code(content, language)
            else:
                processed_content = content
            
            # Generate embedding using Gemini
            embeddings = await self.client.generate_embeddings([processed_content])
            embedding_vector = np.array(embeddings[0])
            
            # Create metadata
            embedding_metadata = EmbeddingMetadata(
                embedding_id=self._generate_embedding_id(content, embedding_type),
                embedding_type=embedding_type,
                source_code=content,
                language=language,
                **(metadata or {})
            )
            
            # Cache result
            self.cache.put(cache_key, embedding_vector, embedding_metadata)
            
            # Update metrics
            self.metrics["embeddings_generated"] += 1
            processing_time = asyncio.get_event_loop().time() - start_time
            self.metrics["total_processing_time"] += processing_time
            
            logger.info(
                f"Generated embedding for {embedding_type.value}",
                extra={
                    "embedding_id": embedding_metadata.embedding_id,
                    "processing_time": processing_time,
                    "language": language
                }
            )
            
            return embedding_vector, embedding_metadata
            
        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            raise
    
    async def batch_generate_embeddings(
        self,
        contents: List[str],
        embedding_types: List[EmbeddingType],
        languages: List[str],
        metadata_list: Optional[List[Dict[str, Any]]] = None
    ) -> List[Tuple[np.ndarray, EmbeddingMetadata]]:
        """Generate embeddings in batch."""
        if not all(len(contents) == len(x) for x in [embedding_types, languages]):
            raise ValueError("All input lists must have the same length")
        
        if metadata_list is None:
            metadata_list = [{}] * len(contents)
        
        results = []
        
        # Check cache for all items
        cache_keys = []
        cache_results = []
        uncached_indices = []
        
        for i, (content, embedding_type) in enumerate(zip(contents, embedding_types)):
            cache_key = self._create_cache_key(content, embedding_type)
            cache_keys.append(cache_key)
            
            cached_result = self.cache.get(cache_key)
            if cached_result:
                cache_results.append((i, cached_result))
                self.metrics["cache_hits"] += 1
            else:
                uncached_indices.append(i)
                self.metrics["cache_misses"] += 1
        
        # Process uncached items
        if uncached_indices:
            uncached_contents = [contents[i] for i in uncached_indices]
            uncached_types = [embedding_types[i] for i in uncached_indices]
            uncached_languages = [languages[i] for i in uncached_indices]
            
            # Preprocess contents
            processed_contents = []
            for content, embedding_type, language in zip(uncached_contents, uncached_types, uncached_languages):
                if embedding_type == EmbeddingType.CODE:
                    processed_content = self._preprocess_code(content, language)
                else:
                    processed_content = content
                processed_contents.append(processed_content)
            
            # Generate embeddings
            embeddings = await self.client.generate_embeddings(processed_contents)
            
            # Create results and cache
            for i, (idx, embedding) in enumerate(zip(uncached_indices, embeddings)):
                embedding_vector = np.array(embedding)
                embedding_metadata = EmbeddingMetadata(
                    embedding_id=self._generate_embedding_id(contents[idx], embedding_types[idx]),
                    embedding_type=embedding_types[idx],
                    source_code=contents[idx],
                    language=languages[idx],
                    **(metadata_list[idx] or {})
                )
                
                # Cache result
                self.cache.put(cache_keys[idx], embedding_vector, embedding_metadata)
                
                cache_results.append((idx, (embedding_vector, embedding_metadata)))
        
        # Sort results by original index
        cache_results.sort(key=lambda x: x[0])
        results = [result for _, result in cache_results]
        
        self.metrics["embeddings_generated"] += len(uncached_indices)
        
        return results
    
    async def add_to_index(
        self,
        embeddings: List[np.ndarray],
        metadata: List[EmbeddingMetadata]
    ):
        """Add embeddings to vector index."""
        if len(embeddings) != len(metadata):
            raise ValueError("Embeddings and metadata must have same length")
        
        embedding_ids = [meta.embedding_id for meta in metadata]
        embeddings_array = np.array(embeddings)
        
        self.vector_index.add_embeddings(embeddings_array, embedding_ids, metadata)
        
        logger.info(f"Added {len(embeddings)} embeddings to index")
    
    async def search_similar(
        self,
        query_content: str,
        query_type: EmbeddingType,
        language: str = "python",
        k: int = 10,
        threshold: float = 0.7,
        filter_by_type: Optional[EmbeddingType] = None,
        filter_by_language: Optional[str] = None
    ) -> List[SimilarityResult]:
        """Search for similar embeddings."""
        start_time = asyncio.get_event_loop().time()
        
        # Generate query embedding
        query_embedding, _ = await self.generate_embedding(
            query_content, query_type, language
        )
        
        # Search in index
        results = self.vector_index.search(query_embedding, k, threshold)
        
        # Apply filters
        if filter_by_type or filter_by_language:
            filtered_results = []
            for result in results:
                if filter_by_type and result.metadata.embedding_type != filter_by_type:
                    continue
                if filter_by_language and result.metadata.language != filter_by_language:
                    continue
                filtered_results.append(result)
            results = filtered_results
        
        # Update metrics
        self.metrics["searches_performed"] += 1
        processing_time = asyncio.get_event_loop().time() - start_time
        self.metrics["total_processing_time"] += processing_time
        
        logger.info(
            f"Similarity search completed",
            extra={
                "query_type": query_type.value,
                "results_count": len(results),
                "processing_time": processing_time
            }
        )
        
        return results
    
    async def cluster_embeddings(
        self,
        embedding_ids: List[str],
        n_clusters: int = 5,
        algorithm: str = "kmeans"
    ) -> List[ClusterResult]:
        """Cluster embeddings by similarity."""
        if n_clusters <= 0:
            raise ValueError("Number of clusters must be positive")
        
        # Get embeddings from index
        embeddings = []
        metadata = []
        
        for embedding_id in embedding_ids:
            if embedding_id in self.vector_index.metadata_store:
                # Get embedding from index (this is a simplified approach)
                # In a real implementation, you'd need to store embeddings separately
                meta = self.vector_index.metadata_store[embedding_id]
                # For now, we'll regenerate the embedding
                embedding, _ = await self.generate_embedding(
                    meta.source_code, meta.embedding_type, meta.language
                )
                embeddings.append(embedding)
                metadata.append(meta)
        
        if not embeddings:
            return []
        
        embeddings_array = np.array(embeddings)
        
        # Perform clustering
        if algorithm == "kmeans":
            kmeans = KMeans(n_clusters=min(n_clusters, len(embeddings)), random_state=42)
            cluster_labels = kmeans.fit_predict(embeddings_array)
            cluster_centers = kmeans.cluster_centers_
            inertia = kmeans.inertia_
        else:
            raise ValueError(f"Unsupported clustering algorithm: {algorithm}")
        
        # Create cluster results
        cluster_results = []
        for cluster_id in range(n_clusters):
            cluster_indices = np.where(cluster_labels == cluster_id)[0]
            cluster_embedding_ids = [embedding_ids[i] for i in cluster_indices]
            
            # Find representative embeddings (closest to center)
            if len(cluster_indices) > 0:
                distances = np.linalg.norm(
                    embeddings_array[cluster_indices] - cluster_centers[cluster_id], axis=1
                )
                representative_indices = cluster_indices[np.argsort(distances)[:3]]
                representative_embeddings = [embedding_ids[i] for i in representative_indices]
            else:
                representative_embeddings = []
            
            cluster_results.append(ClusterResult(
                cluster_id=cluster_id,
                cluster_center=cluster_centers[cluster_id],
                embedding_ids=cluster_embedding_ids,
                cluster_size=len(cluster_embedding_ids),
                inertia=inertia,
                representative_embeddings=representative_embeddings
            ))
        
        return cluster_results
    
    async def compute_similarity_matrix(
        self,
        embedding_ids: List[str]
    ) -> np.ndarray:
        """Compute similarity matrix for embeddings."""
        embeddings = []
        
        for embedding_id in embedding_ids:
            if embedding_id in self.vector_index.metadata_store:
                meta = self.vector_index.metadata_store[embedding_id]
                embedding, _ = await self.generate_embedding(
                    meta.source_code, meta.embedding_type, meta.language
                )
                embeddings.append(embedding)
        
        if not embeddings:
            return np.array([])
        
        embeddings_array = np.array(embeddings)
        return cosine_similarity(embeddings_array)
    
    async def find_duplicate_code(
        self,
        threshold: float = 0.95,
        min_length: int = 50
    ) -> List[List[str]]:
        """Find duplicate or near-duplicate code."""
        duplicates = []
        
        # Get all code embeddings
        code_embeddings = []
        embedding_ids = []
        
        for embedding_id, metadata in self.vector_index.metadata_store.items():
            if (metadata.embedding_type == EmbeddingType.CODE and 
                len(metadata.source_code) >= min_length):
                
                embedding, _ = await self.generate_embedding(
                    metadata.source_code, metadata.embedding_type, metadata.language
                )
                code_embeddings.append(embedding)
                embedding_ids.append(embedding_id)
        
        if len(code_embeddings) < 2:
            return duplicates
        
        # Compute similarity matrix
        similarity_matrix = cosine_similarity(code_embeddings)
        
        # Find duplicates
        processed = set()
        for i in range(len(code_embeddings)):
            if embedding_ids[i] in processed:
                continue
            
            duplicate_group = [embedding_ids[i]]
            for j in range(i + 1, len(code_embeddings)):
                if similarity_matrix[i][j] >= threshold:
                    duplicate_group.append(embedding_ids[j])
                    processed.add(embedding_ids[j])
            
            if len(duplicate_group) > 1:
                duplicates.append(duplicate_group)
            
            processed.add(embedding_ids[i])
        
        return duplicates
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get service metrics."""
        return {
            **self.metrics,
            "cache_hit_rate": (
                self.metrics["cache_hits"] / 
                (self.metrics["cache_hits"] + self.metrics["cache_misses"])
                if (self.metrics["cache_hits"] + self.metrics["cache_misses"]) > 0 else 0
            ),
            "index_size": self.vector_index.get_size(),
            "cache_size": len(self.cache.cache),
            "average_processing_time": (
                self.metrics["total_processing_time"] / 
                max(self.metrics["embeddings_generated"], 1)
            )
        }
    
    def clear_cache(self):
        """Clear embedding cache."""
        self.cache.clear()
        logger.info("Embedding cache cleared")
    
    def save_index(self, filepath: str):
        """Save vector index to file."""
        self.vector_index.save_index(filepath)
        logger.info(f"Index saved to {filepath}")
    
    def load_index(self, filepath: str):
        """Load vector index from file."""
        self.vector_index.load_index(filepath)
        logger.info(f"Index loaded from {filepath}")


# Factory function
async def create_embedding_service(client: Optional[GeminiClient] = None) -> GeminiEmbeddingService:
    """Create embedding service instance."""
    service = GeminiEmbeddingService(client)
    if client is None:
        await service.__aenter__()
    return service