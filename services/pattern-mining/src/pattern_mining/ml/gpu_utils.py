"""
GPU Utilities for ML Model Management

Provides GPU detection, memory optimization, and parallelization utilities.
Optimized for NVIDIA GPUs with CUDA support and RAPIDS integration.
"""

import logging
import os
import platform
import subprocess
import threading
import time
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


@dataclass
class GPUInfo:
    """GPU hardware information."""
    device_id: int
    name: str
    memory_total: int
    memory_free: int
    memory_used: int
    compute_capability: str
    cuda_version: str
    driver_version: str
    temperature: Optional[float] = None
    utilization: Optional[float] = None
    power_draw: Optional[float] = None
    power_limit: Optional[float] = None


@dataclass
class GPUMemoryInfo:
    """GPU memory usage information."""
    total_memory: int
    used_memory: int
    free_memory: int
    cached_memory: int
    reserved_memory: int
    active_memory: int
    timestamp: datetime


class GPUManager:
    """GPU detection and monitoring manager."""
    
    def __init__(self):
        self.cuda_available = self._check_cuda_availability()
        self.gpu_count = self._get_gpu_count()
        self.gpu_info: List[GPUInfo] = []
        self.memory_info: Dict[int, GPUMemoryInfo] = {}
        self._monitoring_thread: Optional[threading.Thread] = None
        self._stop_monitoring = threading.Event()
        
        if self.cuda_available:
            self._initialize_gpu_info()
            self._start_monitoring()
    
    def _check_cuda_availability(self) -> bool:
        """Check if CUDA is available."""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            logger.warning("PyTorch not available, checking CUDA directly")
            try:
                result = subprocess.run(
                    ["nvidia-smi", "--query-gpu=name", "--format=csv,noheader"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                return result.returncode == 0
            except (subprocess.TimeoutExpired, FileNotFoundError):
                return False
    
    def _get_gpu_count(self) -> int:
        """Get number of available GPUs."""
        if not self.cuda_available:
            return 0
        
        try:
            import torch
            return torch.cuda.device_count()
        except ImportError:
            try:
                result = subprocess.run(
                    ["nvidia-smi", "--query-gpu=count", "--format=csv,noheader"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                if result.returncode == 0:
                    return int(result.stdout.strip())
                return 0
            except (subprocess.TimeoutExpired, FileNotFoundError, ValueError):
                return 0
    
    def _initialize_gpu_info(self):
        """Initialize GPU information."""
        try:
            import torch
            
            for device_id in range(self.gpu_count):
                device = torch.device(f"cuda:{device_id}")
                torch.cuda.set_device(device)
                
                # Get GPU properties
                props = torch.cuda.get_device_properties(device_id)
                
                # Get memory info
                memory_info = torch.cuda.mem_get_info(device_id)
                memory_free, memory_total = memory_info
                memory_used = memory_total - memory_free
                
                # Get CUDA version
                cuda_version = torch.version.cuda or "Unknown"
                
                # Get driver version from nvidia-ml-py if available
                driver_version = "Unknown"
                try:
                    import pynvml
                    pynvml.nvmlInit()
                    handle = pynvml.nvmlDeviceGetHandleByIndex(device_id)
                    driver_version = pynvml.nvmlSystemGetDriverVersion().decode('utf-8')
                except ImportError:
                    pass
                
                gpu_info = GPUInfo(
                    device_id=device_id,
                    name=props.name,
                    memory_total=memory_total,
                    memory_free=memory_free,
                    memory_used=memory_used,
                    compute_capability=f"{props.major}.{props.minor}",
                    cuda_version=cuda_version,
                    driver_version=driver_version
                )
                
                self.gpu_info.append(gpu_info)
                logger.info(f"GPU {device_id}: {gpu_info.name} ({gpu_info.memory_total // (1024**3)} GB)")
        
        except ImportError:
            logger.warning("PyTorch not available, GPU info limited")
        except Exception as e:
            logger.error(f"Error initializing GPU info: {e}")
    
    def _start_monitoring(self):
        """Start GPU monitoring thread."""
        if self._monitoring_thread is None or not self._monitoring_thread.is_alive():
            self._monitoring_thread = threading.Thread(target=self._monitor_gpu_usage)
            self._monitoring_thread.daemon = True
            self._monitoring_thread.start()
            logger.info("GPU monitoring started")
    
    def _monitor_gpu_usage(self):
        """Monitor GPU usage in background thread."""
        while not self._stop_monitoring.is_set():
            try:
                self._update_gpu_metrics()
                time.sleep(5)  # Update every 5 seconds
            except Exception as e:
                logger.error(f"Error monitoring GPU: {e}")
                time.sleep(10)  # Wait longer on error
    
    def _update_gpu_metrics(self):
        """Update GPU metrics."""
        try:
            import torch
            
            for device_id in range(self.gpu_count):
                # Update memory info
                memory_info = torch.cuda.mem_get_info(device_id)
                memory_free, memory_total = memory_info
                memory_used = memory_total - memory_free
                
                # Get cached memory
                cached_memory = torch.cuda.memory_cached(device_id)
                reserved_memory = torch.cuda.memory_reserved(device_id)
                
                self.memory_info[device_id] = GPUMemoryInfo(
                    total_memory=memory_total,
                    used_memory=memory_used,
                    free_memory=memory_free,
                    cached_memory=cached_memory,
                    reserved_memory=reserved_memory,
                    active_memory=memory_used - cached_memory,
                    timestamp=datetime.now()
                )
                
                # Update GPU info
                if device_id < len(self.gpu_info):
                    self.gpu_info[device_id].memory_total = memory_total
                    self.gpu_info[device_id].memory_free = memory_free
                    self.gpu_info[device_id].memory_used = memory_used
                
                # Get additional metrics from nvidia-ml-py if available
                try:
                    import pynvml
                    pynvml.nvmlInit()
                    handle = pynvml.nvmlDeviceGetHandleByIndex(device_id)
                    
                    # Temperature
                    temp = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)
                    self.gpu_info[device_id].temperature = temp
                    
                    # Utilization
                    util = pynvml.nvmlDeviceGetUtilizationRates(handle)
                    self.gpu_info[device_id].utilization = util.gpu
                    
                    # Power
                    power = pynvml.nvmlDeviceGetPowerUsage(handle) / 1000.0  # Convert to watts
                    self.gpu_info[device_id].power_draw = power
                    
                    power_limit = pynvml.nvmlDeviceGetPowerManagementDefaultLimitConstraints(handle)[1] / 1000.0
                    self.gpu_info[device_id].power_limit = power_limit
                    
                except ImportError:
                    pass  # nvidia-ml-py not available
                except Exception as e:
                    logger.debug(f"Error getting extended GPU metrics: {e}")
        
        except ImportError:
            pass  # PyTorch not available
        except Exception as e:
            logger.error(f"Error updating GPU metrics: {e}")
    
    def get_gpu_info(self) -> List[GPUInfo]:
        """Get current GPU information."""
        return self.gpu_info.copy()
    
    def get_memory_usage(self, device_id: Optional[int] = None) -> Dict[str, Any]:
        """Get GPU memory usage."""
        if device_id is None:
            # Return all devices
            return {
                str(dev_id): {
                    "total_memory": info.total_memory,
                    "used_memory": info.used_memory,
                    "free_memory": info.free_memory,
                    "cached_memory": info.cached_memory,
                    "reserved_memory": info.reserved_memory,
                    "active_memory": info.active_memory,
                    "utilization": info.used_memory / info.total_memory * 100,
                    "timestamp": info.timestamp.isoformat()
                }
                for dev_id, info in self.memory_info.items()
            }
        else:
            # Return specific device
            if device_id in self.memory_info:
                info = self.memory_info[device_id]
                return {
                    "total_memory": info.total_memory,
                    "used_memory": info.used_memory,
                    "free_memory": info.free_memory,
                    "cached_memory": info.cached_memory,
                    "reserved_memory": info.reserved_memory,
                    "active_memory": info.active_memory,
                    "utilization": info.used_memory / info.total_memory * 100,
                    "timestamp": info.timestamp.isoformat()
                }
            return {}
    
    def get_available_memory(self, device_id: int = 0) -> int:
        """Get available memory on specific GPU."""
        if device_id in self.memory_info:
            return self.memory_info[device_id].free_memory
        return 0
    
    def get_model_memory_usage(self, model_id: str) -> int:
        """Get memory usage of a specific model (placeholder)."""
        # This would track model-specific memory usage
        # For now, return 0 as placeholder
        return 0
    
    def clear_cache(self, device_id: Optional[int] = None):
        """Clear GPU cache."""
        try:
            import torch
            
            if device_id is None:
                # Clear all devices
                for dev_id in range(self.gpu_count):
                    torch.cuda.empty_cache()
                    torch.cuda.ipc_collect()
            else:
                # Clear specific device
                torch.cuda.empty_cache()
                torch.cuda.ipc_collect()
            
            logger.info(f"GPU cache cleared for device {device_id or 'all'}")
        
        except ImportError:
            logger.warning("PyTorch not available, cannot clear GPU cache")
        except Exception as e:
            logger.error(f"Error clearing GPU cache: {e}")
    
    def set_memory_fraction(self, fraction: float, device_id: int = 0):
        """Set memory fraction for a GPU."""
        try:
            import torch
            
            if 0 < fraction <= 1.0:
                torch.cuda.set_per_process_memory_fraction(fraction, device_id)
                logger.info(f"GPU {device_id} memory fraction set to {fraction}")
            else:
                logger.warning(f"Invalid memory fraction: {fraction}")
        
        except ImportError:
            logger.warning("PyTorch not available, cannot set memory fraction")
        except Exception as e:
            logger.error(f"Error setting memory fraction: {e}")
    
    def get_recommended_batch_size(self, model_size: int, device_id: int = 0) -> int:
        """Get recommended batch size based on available memory."""
        if device_id in self.memory_info:
            available_memory = self.memory_info[device_id].free_memory
            
            # Reserve 20% of available memory for overhead
            usable_memory = available_memory * 0.8
            
            # Estimate batch size (rough approximation)
            estimated_batch_size = int(usable_memory / (model_size * 2))  # 2x for gradients
            
            # Clamp to reasonable range
            return max(1, min(estimated_batch_size, 256))
        
        return 1
    
    def get_optimal_device(self) -> int:
        """Get optimal GPU device based on availability."""
        if not self.cuda_available or not self.gpu_info:
            return -1  # No GPU available
        
        # Find device with most free memory
        best_device = 0
        max_free_memory = 0
        
        for device_id, info in self.memory_info.items():
            if info.free_memory > max_free_memory:
                max_free_memory = info.free_memory
                best_device = device_id
        
        return best_device
    
    def stop_monitoring(self):
        """Stop GPU monitoring."""
        self._stop_monitoring.set()
        if self._monitoring_thread and self._monitoring_thread.is_alive():
            self._monitoring_thread.join(timeout=5)
            logger.info("GPU monitoring stopped")


class GPUOptimizer:
    """GPU memory and performance optimizer."""
    
    def __init__(self, gpu_manager: Optional[GPUManager] = None):
        self.gpu_manager = gpu_manager or GPUManager()
        self.optimization_history: List[Dict[str, Any]] = []
    
    def optimize_memory_allocation(self):
        """Optimize GPU memory allocation."""
        try:
            import torch
            
            if not self.gpu_manager.cuda_available:
                logger.info("No GPU available for optimization")
                return
            
            # Clear GPU cache
            self.gpu_manager.clear_cache()
            
            # Set memory growth (if supported)
            try:
                # Enable memory growth to prevent pre-allocation
                for device_id in range(self.gpu_manager.gpu_count):
                    torch.cuda.set_per_process_memory_fraction(0.95, device_id)
            except Exception as e:
                logger.debug(f"Could not set memory growth: {e}")
            
            # Record optimization
            self.optimization_history.append({
                "timestamp": datetime.now(),
                "action": "memory_optimization",
                "devices": list(range(self.gpu_manager.gpu_count)),
                "memory_before": self.gpu_manager.get_memory_usage()
            })
            
            logger.info("GPU memory optimization completed")
        
        except ImportError:
            logger.warning("PyTorch not available for GPU optimization")
        except Exception as e:
            logger.error(f"Error optimizing GPU memory: {e}")
    
    def optimize_for_inference(self, model: Any, device_id: int = 0) -> Any:
        """Optimize model for inference."""
        try:
            import torch
            
            if not self.gpu_manager.cuda_available:
                return model
            
            # Move model to GPU
            if hasattr(model, 'to'):
                model = model.to(f'cuda:{device_id}')
            
            # Enable inference mode optimizations
            if hasattr(model, 'eval'):
                model.eval()
            
            # Enable TensorRT optimization if available
            try:
                # This would use torch-tensorrt if available
                pass
            except ImportError:
                pass
            
            # Compile model for faster inference (PyTorch 2.0+)
            try:
                if hasattr(torch, 'compile'):
                    model = torch.compile(model)
            except Exception as e:
                logger.debug(f"Could not compile model: {e}")
            
            logger.info(f"Model optimized for inference on GPU {device_id}")
            return model
        
        except ImportError:
            logger.warning("PyTorch not available for inference optimization")
            return model
        except Exception as e:
            logger.error(f"Error optimizing model for inference: {e}")
            return model
    
    def setup_distributed_training(self, world_size: int, rank: int):
        """Setup distributed training across multiple GPUs."""
        try:
            import torch
            import torch.distributed as dist
            
            if not self.gpu_manager.cuda_available:
                logger.error("No GPU available for distributed training")
                return False
            
            # Initialize distributed training
            dist.init_process_group(
                backend='nccl',
                world_size=world_size,
                rank=rank
            )
            
            # Set device for current process
            torch.cuda.set_device(rank)
            
            logger.info(f"Distributed training setup: rank {rank}/{world_size}")
            return True
        
        except ImportError:
            logger.error("PyTorch not available for distributed training")
            return False
        except Exception as e:
            logger.error(f"Error setting up distributed training: {e}")
            return False
    
    def optimize_batch_size(self, model: Any, sample_input: Any, device_id: int = 0) -> int:
        """Find optimal batch size for model."""
        try:
            import torch
            
            if not self.gpu_manager.cuda_available:
                return 1
            
            # Start with a reasonable batch size
            batch_size = 1
            max_batch_size = 1
            
            # Test increasing batch sizes
            for test_batch_size in [1, 2, 4, 8, 16, 32, 64, 128, 256]:
                try:
                    # Create test batch
                    if isinstance(sample_input, torch.Tensor):
                        test_input = sample_input.repeat(test_batch_size, *([1] * (sample_input.dim() - 1)))
                    else:
                        test_input = [sample_input] * test_batch_size
                    
                    # Test forward pass
                    with torch.no_grad():
                        if hasattr(model, 'forward'):
                            _ = model(test_input)
                        else:
                            _ = model.predict(test_input)
                    
                    max_batch_size = test_batch_size
                    
                    # Check memory usage
                    memory_info = self.gpu_manager.get_memory_usage(device_id)
                    if memory_info and memory_info.get('utilization', 0) > 90:
                        break
                
                except torch.cuda.OutOfMemoryError:
                    break
                except Exception as e:
                    logger.debug(f"Error testing batch size {test_batch_size}: {e}")
                    break
            
            # Use 75% of max batch size for safety
            optimal_batch_size = max(1, int(max_batch_size * 0.75))
            
            logger.info(f"Optimal batch size: {optimal_batch_size}")
            return optimal_batch_size
        
        except ImportError:
            logger.warning("PyTorch not available for batch size optimization")
            return 1
        except Exception as e:
            logger.error(f"Error optimizing batch size: {e}")
            return 1
    
    def enable_mixed_precision(self, model: Any) -> Tuple[Any, Any]:
        """Enable mixed precision training."""
        try:
            import torch
            
            if not self.gpu_manager.cuda_available:
                return model, None
            
            # Check if mixed precision is supported
            if not torch.cuda.is_available() or not torch.cuda.is_bf16_supported():
                logger.warning("Mixed precision not supported on this GPU")
                return model, None
            
            # Create scaler for mixed precision
            scaler = torch.cuda.amp.GradScaler()
            
            # Convert model to half precision where possible
            if hasattr(model, 'half'):
                model = model.half()
            
            logger.info("Mixed precision enabled")
            return model, scaler
        
        except ImportError:
            logger.warning("PyTorch not available for mixed precision")
            return model, None
        except Exception as e:
            logger.error(f"Error enabling mixed precision: {e}")
            return model, None
    
    def get_optimization_recommendations(self) -> Dict[str, Any]:
        """Get GPU optimization recommendations."""
        recommendations = {
            "memory_optimization": [],
            "performance_optimization": [],
            "hardware_recommendations": []
        }
        
        if not self.gpu_manager.cuda_available:
            recommendations["hardware_recommendations"].append(
                "Consider using GPU-enabled hardware for better performance"
            )
            return recommendations
        
        # Memory optimization recommendations
        for device_id, info in self.gpu_manager.memory_info.items():
            utilization = info.used_memory / info.total_memory * 100
            
            if utilization > 90:
                recommendations["memory_optimization"].append(
                    f"GPU {device_id}: Memory usage is high ({utilization:.1f}%). "
                    "Consider reducing batch size or model size."
                )
            elif utilization < 50:
                recommendations["memory_optimization"].append(
                    f"GPU {device_id}: Memory usage is low ({utilization:.1f}%). "
                    "Consider increasing batch size for better throughput."
                )
        
        # Performance optimization recommendations
        for gpu_info in self.gpu_manager.gpu_info:
            if gpu_info.temperature and gpu_info.temperature > 80:
                recommendations["performance_optimization"].append(
                    f"GPU {gpu_info.device_id}: Temperature is high ({gpu_info.temperature}°C). "
                    "Consider improving cooling or reducing workload."
                )
            
            if gpu_info.utilization and gpu_info.utilization < 70:
                recommendations["performance_optimization"].append(
                    f"GPU {gpu_info.device_id}: Utilization is low ({gpu_info.utilization:.1f}%). "
                    "Consider increasing batch size or parallelization."
                )
        
        return recommendations
    
    def get_optimization_history(self) -> List[Dict[str, Any]]:
        """Get optimization history."""
        return self.optimization_history.copy()


# Global instances
_gpu_manager: Optional[GPUManager] = None
_gpu_optimizer: Optional[GPUOptimizer] = None


def get_gpu_manager() -> GPUManager:
    """Get global GPU manager instance."""
    global _gpu_manager
    if _gpu_manager is None:
        _gpu_manager = GPUManager()
    return _gpu_manager


def get_gpu_optimizer() -> GPUOptimizer:
    """Get global GPU optimizer instance."""
    global _gpu_optimizer
    if _gpu_optimizer is None:
        _gpu_optimizer = GPUOptimizer()
    return _gpu_optimizer


def cleanup_gpu_resources():
    """Cleanup GPU resources."""
    global _gpu_manager, _gpu_optimizer
    
    if _gpu_manager:
        _gpu_manager.stop_monitoring()
        _gpu_manager.clear_cache()
    
    if _gpu_optimizer:
        _gpu_optimizer.optimization_history.clear()
    
    _gpu_manager = None
    _gpu_optimizer = None