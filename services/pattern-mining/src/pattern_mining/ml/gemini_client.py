"""
Gemini API Client - Enhanced for 2.5 Flash

Production-ready async client for Google Gemini API with comprehensive
error handling, rate limiting, retry logic, and monitoring.
Optimized for Gemini 2.5 Flash with thinking capabilities and code analysis.
"""

import asyncio
import json
import time
from typing import Dict, Any, List, Optional, Union, AsyncGenerator, Callable
from dataclasses import dataclass, field
from enum import Enum
import logging
from datetime import datetime, timedelta
import hashlib
import aiohttp
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold, GenerationConfig
from google.ai.generativelanguage_v1beta import GenerativeServiceAsyncClient
from google.auth.credentials import Credentials
from google.auth import default
import structlog
from vertexai.preview.generative_models import GenerativeModel, Part

from ..config.gemini import get_gemini_config, GeminiModel, SafetyLevel
from ..utils.validation import validate_input


logger = structlog.get_logger(__name__)


class RequestStatus(Enum):
    """Request status enumeration."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    RATE_LIMITED = "rate_limited"
    TIMEOUT = "timeout"


@dataclass
class RequestMetrics:
    """Request metrics tracking."""
    request_id: str
    start_time: float
    end_time: Optional[float] = None
    tokens_used: int = 0
    response_size: int = 0
    model_used: str = ""
    status: RequestStatus = RequestStatus.PENDING
    error: Optional[str] = None
    retry_count: int = 0


@dataclass
class RateLimitBucket:
    """Rate limiting bucket."""
    capacity: int
    tokens: int
    last_refill: float
    refill_rate: float = field(default=1.0)


class GeminiRateLimiter:
    """Advanced rate limiter for Gemini API."""
    
    def __init__(self, requests_per_minute: int, tokens_per_minute: int):
        self.requests_bucket = RateLimitBucket(
            capacity=requests_per_minute,
            tokens=requests_per_minute,
            last_refill=time.time(),
            refill_rate=requests_per_minute / 60.0
        )
        
        self.tokens_bucket = RateLimitBucket(
            capacity=tokens_per_minute,
            tokens=tokens_per_minute,
            last_refill=time.time(),
            refill_rate=tokens_per_minute / 60.0
        )
        
        self.lock = asyncio.Lock()
    
    async def acquire(self, estimated_tokens: int = 1000) -> bool:
        """Acquire rate limit tokens."""
        async with self.lock:
            current_time = time.time()
            
            # Refill buckets
            self._refill_bucket(self.requests_bucket, current_time)
            self._refill_bucket(self.tokens_bucket, current_time)
            
            # Check if we can make the request
            if (self.requests_bucket.tokens >= 1 and 
                self.tokens_bucket.tokens >= estimated_tokens):
                self.requests_bucket.tokens -= 1
                self.tokens_bucket.tokens -= estimated_tokens
                return True
            
            return False
    
    def _refill_bucket(self, bucket: RateLimitBucket, current_time: float):
        """Refill rate limit bucket."""
        time_passed = current_time - bucket.last_refill
        tokens_to_add = int(time_passed * bucket.refill_rate)
        
        if tokens_to_add > 0:
            bucket.tokens = min(bucket.capacity, bucket.tokens + tokens_to_add)
            bucket.last_refill = current_time
    
    def get_wait_time(self, estimated_tokens: int = 1000) -> float:
        """Get estimated wait time for rate limit."""
        current_time = time.time()
        
        # Calculate wait time for requests
        if self.requests_bucket.tokens < 1:
            tokens_needed = 1 - self.requests_bucket.tokens
            request_wait = tokens_needed / self.requests_bucket.refill_rate
        else:
            request_wait = 0
        
        # Calculate wait time for tokens
        if self.tokens_bucket.tokens < estimated_tokens:
            tokens_needed = estimated_tokens - self.tokens_bucket.tokens
            token_wait = tokens_needed / self.tokens_bucket.refill_rate
        else:
            token_wait = 0
        
        return max(request_wait, token_wait)


class GeminiClient:
    """Production-ready Gemini API client optimized for 2.5 Flash."""
    
    def __init__(self, config=None):
        """Initialize Gemini client."""
        self.config = config or get_gemini_config()
        self.client: Optional[GenerativeServiceAsyncClient] = None
        self.session: Optional[aiohttp.ClientSession] = None
        self.rate_limiter = GeminiRateLimiter(
            self.config.requests_per_minute,
            self.config.tokens_per_minute
        )
        self.request_metrics: Dict[str, RequestMetrics] = {}
        self.cache: Dict[str, Any] = {}
        self.cache_ttl: Dict[str, float] = {}
        self.semaphore = asyncio.Semaphore(self.config.concurrent_requests)
        
        # Enhanced configuration for Gemini 2.5 Flash
        self._setup_enhanced_config()
        
        # Initialize the client
        self._initialize_client()
    
    def _setup_enhanced_config(self):
        """Setup enhanced configuration for Gemini 2.5 Flash."""
        # Enhanced generation config for thinking and code analysis
        self.enhanced_generation_config = GenerationConfig(
            temperature=0.1,  # Lower temperature for more consistent code analysis
            top_p=0.9,
            top_k=40,
            max_output_tokens=8192,  # Increased for detailed analysis
            candidate_count=1,
            stop_sequences=["<END_ANALYSIS>", "<STOP>"]
        )
        
        # Thinking-specific configuration
        self.thinking_config = GenerationConfig(
            temperature=0.2,  # Slightly higher for creative thinking
            top_p=0.95,
            top_k=50,
            max_output_tokens=4096,
            candidate_count=1
        )
        
        # Code analysis prompts
        self.code_analysis_prompts = {
            "pattern_detection": """
You are an expert code analyst specializing in pattern detection.
Analyze the provided code for patterns, anti-patterns, and code smells.

Thinking process:
1. First, understand the code structure and purpose
2. Identify any design patterns or anti-patterns
3. Look for code smells and quality issues
4. Consider security vulnerabilities
5. Evaluate performance implications

Provide your analysis in structured JSON format with confidence scores.
""",
            "security_analysis": """
You are a security expert analyzing code for vulnerabilities.
Use your thinking capabilities to systematically identify security issues.

Thinking process:
1. Scan for common vulnerability patterns (OWASP Top 10)
2. Check for input validation issues
3. Look for authentication/authorization flaws
4. Identify potential injection vulnerabilities
5. Check for insecure data handling

Provide detailed security analysis with severity ratings.
""",
            "performance_analysis": """
You are a performance optimization expert.
Analyze the code for performance bottlenecks and optimization opportunities.

Thinking process:
1. Identify algorithmic complexity issues
2. Look for inefficient data structures
3. Check for unnecessary computations
4. Identify memory usage patterns
5. Consider scalability implications

Provide optimization recommendations with impact estimates.
"""
        }
    
    def _initialize_client(self):
        """Initialize the Gemini client."""
        try:
            # Configure the API
            if self.config.api_key:
                genai.configure(api_key=self.config.api_key.get_secret_value())
            else:
                # Use default credentials
                credentials, _ = default()
                genai.configure(credentials=credentials)
            
            logger.info("Gemini 2.5 Flash client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Gemini client: {e}")
            raise
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def start(self):
        """Start the client."""
        if self.session is None:
            timeout = aiohttp.ClientTimeout(
                total=self.config.request_timeout,
                connect=self.config.connection_timeout
            )
            self.session = aiohttp.ClientSession(timeout=timeout)
        
        logger.info("Gemini client started")
    
    async def close(self):
        """Close the client."""
        if self.session:
            await self.session.close()
            self.session = None
        
        logger.info("Gemini client closed")
    
    def _generate_request_id(self, prompt: str, model: str) -> str:
        """Generate unique request ID."""
        content = f"{prompt}:{model}:{time.time()}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _get_cache_key(self, prompt: str, model: str, use_thinking: bool = False, analysis_type: Optional[str] = None, **kwargs) -> str:
        """Generate cache key including thinking and analysis parameters."""
        key_data = {
            "prompt": prompt,
            "model": model,
            "use_thinking": use_thinking,
            "analysis_type": analysis_type,
            **kwargs
        }
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.sha256(key_string.encode()).hexdigest()
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cache entry is valid."""
        if not self.config.enable_caching:
            return False
        
        if cache_key not in self.cache:
            return False
        
        if cache_key not in self.cache_ttl:
            return False
        
        return time.time() < self.cache_ttl[cache_key]
    
    def _cache_response(self, cache_key: str, response: Any):
        """Cache response."""
        if not self.config.enable_caching:
            return
        
        self.cache[cache_key] = response
        self.cache_ttl[cache_key] = time.time() + self.config.cache_ttl
        
        # Clean up old cache entries
        if len(self.cache) > self.config.cache_max_size:
            self._cleanup_cache()
    
    def _cleanup_cache(self):
        """Clean up old cache entries."""
        current_time = time.time()
        expired_keys = [
            key for key, ttl in self.cache_ttl.items()
            if current_time > ttl
        ]
        
        for key in expired_keys:
            self.cache.pop(key, None)
            self.cache_ttl.pop(key, None)
    
    async def _wait_for_rate_limit(self, estimated_tokens: int = 1000) -> bool:
        """Wait for rate limit availability."""
        max_wait = 60  # Maximum wait time in seconds
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            if await self.rate_limiter.acquire(estimated_tokens):
                return True
            
            wait_time = self.rate_limiter.get_wait_time(estimated_tokens)
            await asyncio.sleep(min(wait_time, 1.0))
        
        return False
    
    async def _retry_with_backoff(
        self,
        func: Callable,
        *args,
        **kwargs
    ) -> Any:
        """Retry function with exponential backoff."""
        last_exception = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                
                if attempt == self.config.max_retries:
                    break
                
                # Calculate delay
                delay = (self.config.retry_delay * 
                        (self.config.retry_exponential_base ** attempt))
                
                logger.warning(
                    f"Request failed (attempt {attempt + 1}): {e}. "
                    f"Retrying in {delay:.2f} seconds..."
                )
                
                await asyncio.sleep(delay)
        
        raise last_exception
    
    async def generate_content(
        self,
        prompt: str,
        model: Optional[GeminiModel] = None,
        system_prompt: Optional[str] = None,
        use_thinking: bool = False,
        analysis_type: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate content using Gemini API with enhanced capabilities."""
        model = model or GeminiModel.GEMINI_2_5_FLASH  # Default to 2.5 Flash
        request_id = self._generate_request_id(prompt, model.value)
        
        # Create metrics
        metrics = RequestMetrics(
            request_id=request_id,
            start_time=time.time(),
            model_used=model.value
        )
        self.request_metrics[request_id] = metrics
        
        try:
            # Check cache
            cache_key = self._get_cache_key(prompt, model.value, use_thinking, analysis_type, **kwargs)
            if self._is_cache_valid(cache_key):
                response = self.cache[cache_key]
                metrics.status = RequestStatus.COMPLETED
                metrics.end_time = time.time()
                logger.info(f"Cache hit for request {request_id}")
                return response
            
            # Rate limiting
            estimated_tokens = len(prompt.split()) * 2  # Rough estimation
            if not await self._wait_for_rate_limit(estimated_tokens):
                metrics.status = RequestStatus.RATE_LIMITED
                raise Exception("Rate limit exceeded")
            
            # Acquire semaphore
            async with self.semaphore:
                metrics.status = RequestStatus.IN_PROGRESS
                
                # Generate content with enhanced capabilities
                response = await self._retry_with_backoff(
                    self._generate_content_impl,
                    prompt,
                    model,
                    system_prompt,
                    use_thinking,
                    analysis_type,
                    **kwargs
                )
                
                # Cache response
                self._cache_response(cache_key, response)
                
                # Update metrics
                metrics.status = RequestStatus.COMPLETED
                metrics.end_time = time.time()
                metrics.tokens_used = response.get("usage", {}).get("total_tokens", 0)
                metrics.response_size = len(str(response))
                
                logger.info(
                    f"Request {request_id} completed successfully",
                    extra={
                        "request_id": request_id,
                        "model": model.value,
                        "tokens_used": metrics.tokens_used,
                        "duration": metrics.end_time - metrics.start_time,
                        "analysis_type": analysis_type,
                        "used_thinking": use_thinking
                    }
                )
                
                return response
                
        except Exception as e:
            metrics.status = RequestStatus.FAILED
            metrics.error = str(e)
            metrics.end_time = time.time()
            
            logger.error(
                f"Request {request_id} failed: {e}",
                extra={
                    "request_id": request_id,
                    "model": model.value,
                    "error": str(e)
                }
            )
            raise
    
    async def _generate_content_impl(
        self,
        prompt: str,
        model: GeminiModel,
        system_prompt: Optional[str] = None,
        use_thinking: bool = False,
        analysis_type: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Implementation of content generation with enhanced capabilities."""
        try:
            # Choose generation config based on use case
            if use_thinking:
                generation_config = self.thinking_config
            elif analysis_type:
                generation_config = self.enhanced_generation_config
            else:
                generation_config = self.config.generation_config
            
            # Get the model
            genai_model = genai.GenerativeModel(
                model_name=model.value,
                generation_config=generation_config,
                safety_settings=self._get_safety_settings()
            )
            
            # Prepare the enhanced prompt
            full_prompt = self._prepare_enhanced_prompt(
                prompt, system_prompt, use_thinking, analysis_type
            )
            
            # Generate content
            if self.config.enable_streaming:
                response = await genai_model.generate_content_async(
                    full_prompt,
                    stream=True
                )
                
                # Collect streaming response
                full_response = ""
                thinking_section = ""
                analysis_section = ""
                
                async for chunk in response:
                    if chunk.text:
                        full_response += chunk.text
                
                # Parse thinking and analysis sections if present
                if use_thinking and "<thinking>" in full_response:
                    thinking_section = self._extract_section(full_response, "thinking")
                    analysis_section = self._extract_section(full_response, "analysis")
                
                return {
                    "text": full_response,
                    "thinking": thinking_section,
                    "analysis": analysis_section,
                    "model": model.value,
                    "analysis_type": analysis_type,
                    "usage": {
                        "prompt_tokens": len(full_prompt.split()),
                        "completion_tokens": len(full_response.split()),
                        "total_tokens": len(full_prompt.split()) + len(full_response.split())
                    }
                }
            else:
                response = await genai_model.generate_content_async(full_prompt)
                
                # Parse response sections
                thinking_section = ""
                analysis_section = ""
                
                if use_thinking and "<thinking>" in response.text:
                    thinking_section = self._extract_section(response.text, "thinking")
                    analysis_section = self._extract_section(response.text, "analysis")
                
                return {
                    "text": response.text,
                    "thinking": thinking_section,
                    "analysis": analysis_section,
                    "model": model.value,
                    "analysis_type": analysis_type,
                    "usage": {
                        "prompt_tokens": len(full_prompt.split()),
                        "completion_tokens": len(response.text.split()),
                        "total_tokens": len(full_prompt.split()) + len(response.text.split())
                    },
                    "finish_reason": "stop",
                    "safety_ratings": response.candidates[0].safety_ratings if response.candidates else []
                }
                
        except Exception as e:
            logger.error(f"Failed to generate content: {e}")
            raise
    
    def _get_safety_settings(self) -> List[Dict[str, Any]]:
        """Get safety settings for the model."""
        safety_settings = []
        
        for category, threshold in self.config.safety_settings.items():
            # Convert to genai format
            if category == "HARM_CATEGORY_HARASSMENT":
                harm_category = HarmCategory.HARM_CATEGORY_HARASSMENT
            elif category == "HARM_CATEGORY_HATE_SPEECH":
                harm_category = HarmCategory.HARM_CATEGORY_HATE_SPEECH
            elif category == "HARM_CATEGORY_SEXUALLY_EXPLICIT":
                harm_category = HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT
            elif category == "HARM_CATEGORY_DANGEROUS_CONTENT":
                harm_category = HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT
            else:
                continue
            
            # Convert threshold
            if threshold == "BLOCK_NONE":
                harm_threshold = HarmBlockThreshold.BLOCK_NONE
            elif threshold == "BLOCK_FEW":
                harm_threshold = HarmBlockThreshold.BLOCK_ONLY_HIGH
            elif threshold == "BLOCK_SOME":
                harm_threshold = HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
            elif threshold == "BLOCK_MOST":
                harm_threshold = HarmBlockThreshold.BLOCK_LOW_AND_ABOVE
            else:
                harm_threshold = HarmBlockThreshold.BLOCK_NONE
            
            safety_settings.append({
                "category": harm_category,
                "threshold": harm_threshold
            })
        
        return safety_settings
    
    async def generate_embeddings(
        self,
        texts: List[str],
        model: Optional[str] = None
    ) -> List[List[float]]:
        """Generate embeddings for texts."""
        model = model or self.config.embedding_model
        
        try:
            embeddings = []
            
            # Process in batches
            batch_size = self.config.embedding_batch_size
            for i in range(0, len(texts), batch_size):
                batch = texts[i:i + batch_size]
                
                # Rate limiting
                if not await self._wait_for_rate_limit(len(batch) * 100):
                    raise Exception("Rate limit exceeded for embeddings")
                
                # Generate embeddings for batch
                batch_embeddings = await self._generate_embeddings_batch(batch, model)
                embeddings.extend(batch_embeddings)
            
            return embeddings
            
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {e}")
            raise
    
    async def _generate_embeddings_batch(
        self,
        texts: List[str],
        model: str
    ) -> List[List[float]]:
        """Generate embeddings for a batch of texts."""
        try:
            # Use the embeddings model
            result = genai.embed_content(
                model=model,
                content=texts,
                task_type="retrieval_document"
            )
            
            return result['embedding']
            
        except Exception as e:
            logger.error(f"Failed to generate embeddings batch: {e}")
            raise
    
    def _prepare_enhanced_prompt(
        self,
        prompt: str,
        system_prompt: Optional[str],
        use_thinking: bool,
        analysis_type: Optional[str]
    ) -> str:
        """Prepare enhanced prompt with thinking capabilities."""
        # Start with system prompt if provided
        full_prompt = ""
        
        if system_prompt:
            full_prompt += f"{system_prompt}\n\n"
        elif analysis_type and analysis_type in self.code_analysis_prompts:
            full_prompt += f"{self.code_analysis_prompts[analysis_type]}\n\n"
        
        # Add thinking instruction if requested
        if use_thinking:
            full_prompt += """
<thinking>
Let me think through this step by step:
1. I need to carefully analyze the provided code
2. I should consider multiple aspects: structure, patterns, quality, security
3. I'll provide a detailed reasoning process
4. I'll give concrete examples and explanations
</thinking>

<analysis>
"""
        
        full_prompt += prompt
        
        if use_thinking:
            full_prompt += "\n</analysis>"
        
        return full_prompt
    
    def _extract_section(self, text: str, section_name: str) -> str:
        """Extract a specific section from the response."""
        try:
            start_tag = f"<{section_name}>"
            end_tag = f"</{section_name}>"
            
            start_idx = text.find(start_tag)
            if start_idx == -1:
                return ""
            
            start_idx += len(start_tag)
            end_idx = text.find(end_tag, start_idx)
            
            if end_idx == -1:
                return text[start_idx:].strip()
            
            return text[start_idx:end_idx].strip()
        except Exception as e:
            logger.warning(f"Failed to extract section {section_name}: {e}")
            return ""
    
    async def analyze_code_with_thinking(
        self,
        code: str,
        language: str,
        analysis_type: str = "pattern_detection"
    ) -> Dict[str, Any]:
        """Analyze code using Gemini 2.5 Flash with thinking capabilities."""
        prompt = f"""
Analyze the following {language} code for {analysis_type.replace('_', ' ')}:

```{language}
{code}
```

Provide a comprehensive analysis including:
1. Identified patterns, anti-patterns, or issues
2. Confidence scores (0-100) for each finding
3. Detailed explanations and reasoning
4. Specific recommendations for improvement
5. Code examples for suggested fixes

Format your response as valid JSON with the following structure:
{{
  "findings": [
    {{
      "type": "pattern|anti_pattern|code_smell|security|performance",
      "name": "Pattern/Issue Name",
      "description": "Detailed description",
      "confidence": 85,
      "severity": "low|medium|high|critical",
      "location": {{
        "line_start": 1,
        "line_end": 10
      }},
      "recommendations": ["suggestion1", "suggestion2"]
    }}
  ],
  "summary": "Overall assessment",
  "quality_score": 75
}}
"""
        
        response = await self.generate_content(
            prompt=prompt,
            model=GeminiModel.GEMINI_2_5_FLASH,
            use_thinking=True,
            analysis_type=analysis_type
        )
        
        try:
            # Try to parse the analysis section as JSON
            analysis_text = response.get("analysis", response.get("text", ""))
            
            # Clean up the text to extract JSON
            json_start = analysis_text.find('{')
            json_end = analysis_text.rfind('}') + 1
            
            if json_start != -1 and json_end > json_start:
                json_text = analysis_text[json_start:json_end]
                result = json.loads(json_text)
                
                # Add metadata
                result["thinking_process"] = response.get("thinking", "")
                result["model_used"] = response.get("model", "")
                result["analysis_type"] = analysis_type
                
                return result
            else:
                raise json.JSONDecodeError("No valid JSON found", analysis_text, 0)
                
        except json.JSONDecodeError:
            # Fallback to structured text response
            return {
                "findings": [{
                    "type": "analysis",
                    "name": "Code Analysis",
                    "description": response.get("analysis", response.get("text", "")),
                    "confidence": 70,
                    "severity": "medium",
                    "location": {"line_start": 1, "line_end": len(code.split('\n'))},
                    "recommendations": ["Review the analysis provided"]
                }],
                "thinking_process": response.get("thinking", ""),
                "summary": "Analysis completed with text response",
                "quality_score": 50,
                "format": "text_fallback"
            }
    
    async def analyze_code(
        self,
        code: str,
        language: str,
        analysis_type: str = "pattern"
    ) -> Dict[str, Any]:
        """Analyze code using Gemini (backward compatibility)."""
        return await self.analyze_code_with_thinking(code, language, f"{analysis_type}_detection")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get client metrics."""
        current_time = time.time()
        
        # Calculate metrics
        total_requests = len(self.request_metrics)
        completed_requests = sum(
            1 for m in self.request_metrics.values()
            if m.status == RequestStatus.COMPLETED
        )
        failed_requests = sum(
            1 for m in self.request_metrics.values()
            if m.status == RequestStatus.FAILED
        )
        
        # Calculate average response time
        completed_metrics = [
            m for m in self.request_metrics.values()
            if m.status == RequestStatus.COMPLETED and m.end_time
        ]
        
        if completed_metrics:
            avg_response_time = sum(
                m.end_time - m.start_time for m in completed_metrics
            ) / len(completed_metrics)
        else:
            avg_response_time = 0
        
        # Calculate total tokens
        total_tokens = sum(m.tokens_used for m in self.request_metrics.values())
        
        return {
            "total_requests": total_requests,
            "completed_requests": completed_requests,
            "failed_requests": failed_requests,
            "success_rate": completed_requests / total_requests if total_requests > 0 else 0,
            "avg_response_time": avg_response_time,
            "total_tokens_used": total_tokens,
            "cache_hit_rate": self._calculate_cache_hit_rate(),
            "current_cache_size": len(self.cache),
            "rate_limit_tokens_available": self.rate_limiter.requests_bucket.tokens,
            "rate_limit_requests_available": self.rate_limiter.tokens_bucket.tokens
        }
    
    def _calculate_cache_hit_rate(self) -> float:
        """Calculate cache hit rate."""
        # This is a simplified calculation
        # In a real implementation, you'd track cache hits vs misses
        return min(len(self.cache) / max(len(self.request_metrics), 1), 1.0)
    
    def clear_cache(self):
        """Clear the response cache."""
        self.cache.clear()
        self.cache_ttl.clear()
        logger.info("Cache cleared")
    
    def clear_metrics(self):
        """Clear request metrics."""
        self.request_metrics.clear()
        logger.info("Metrics cleared")


    async def generate_embeddings_enhanced(
        self,
        texts: List[str],
        model: str = "text-embedding-004",
        task_type: str = "retrieval_document"
    ) -> Dict[str, Any]:
        """Generate embeddings with enhanced metadata."""
        try:
            embeddings = await self.generate_embeddings(texts, model)
            
            return {
                "embeddings": embeddings,
                "model": model,
                "task_type": task_type,
                "dimensions": len(embeddings[0]) if embeddings else 0,
                "count": len(embeddings),
                "usage": {
                    "total_tokens": sum(len(text.split()) for text in texts)
                }
            }
        except Exception as e:
            logger.error(f"Failed to generate enhanced embeddings: {e}")
            raise
    
    async def batch_analyze_code(
        self,
        code_samples: List[Dict[str, str]],
        analysis_type: str = "pattern_detection"
    ) -> List[Dict[str, Any]]:
        """Batch analyze multiple code samples with rate limiting."""
        results = []
        
        for i, sample in enumerate(code_samples):
            try:
                code = sample.get("code", "")
                language = sample.get("language", "python")
                
                result = await self.analyze_code_with_thinking(
                    code, language, analysis_type
                )
                
                result["sample_id"] = sample.get("id", f"sample_{i}")
                results.append(result)
                
                # Rate limiting delay
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"Failed to analyze sample {i}: {e}")
                results.append({
                    "sample_id": sample.get("id", f"sample_{i}"),
                    "error": str(e),
                    "findings": []
                })
        
        return results


# Factory function for creating client instances
async def create_gemini_client(config=None) -> GeminiClient:
    """Create and start an enhanced Gemini 2.5 Flash client."""
    client = GeminiClient(config)
    await client.start()
    return client