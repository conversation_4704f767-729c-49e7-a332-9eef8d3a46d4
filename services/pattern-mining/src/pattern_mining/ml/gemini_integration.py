"""
Gemini Integration Layer

Integration layer connecting Gemini services with the existing ML pipeline,
providing hybrid reasoning, confidence scoring, and result aggregation.
"""

import asyncio
import json
import numpy as np
from typing import Dict, Any, List, Optional, Tuple, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import logging
from datetime import datetime, timedelta
import structlog

from .gemini_client import GeminiClient, create_gemini_client
from .gemini_analyzer import GeminiAnalyzer, AnalysisType, AnalysisResult, CodeContext
from .gemini_embeddings import GeminiEmbeddingService, EmbeddingType
from ..ml.manager import MLManager
from ..ml.inference import InferenceEngine
from ..config.gemini import get_gemini_config
from ..models.patterns import Pattern, PatternType
from ..utils.validation import validate_input


logger = structlog.get_logger(__name__)


class IntegrationMode(Enum):
    """Integration mode enumeration."""
    GEMINI_ONLY = "gemini_only"
    LOCAL_ONLY = "local_only"
    HYBRID_PARALLEL = "hybrid_parallel"
    HYBRID_SEQUENTIAL = "hybrid_sequential"
    CONFIDENCE_WEIGHTED = "confidence_weighted"
    ENSEMBLE = "ensemble"


@dataclass
class HybridResult:
    """Hybrid analysis result."""
    gemini_result: Optional[AnalysisResult] = None
    local_result: Optional[Any] = None
    combined_confidence: float = 0.0
    final_result: Optional[Any] = None
    integration_mode: IntegrationMode = IntegrationMode.HYBRID_PARALLEL
    processing_time: float = 0.0
    tokens_used: int = 0
    local_processing_time: float = 0.0
    gemini_processing_time: float = 0.0
    confidence_breakdown: Dict[str, float] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class EnsembleWeights:
    """Ensemble model weights."""
    gemini_weight: float = 0.6
    local_weight: float = 0.4
    confidence_threshold: float = 0.7
    agreement_bonus: float = 0.1
    disagreement_penalty: float = 0.05


class ConfidenceAggregator:
    """Advanced confidence scoring and aggregation."""
    
    def __init__(self):
        self.history: List[Dict[str, Any]] = []
        self.performance_metrics: Dict[str, float] = {
            "gemini_accuracy": 0.85,
            "local_accuracy": 0.78,
            "hybrid_accuracy": 0.92
        }
    
    def calculate_combined_confidence(
        self,
        gemini_confidence: float,
        local_confidence: float,
        agreement_score: float,
        weights: EnsembleWeights
    ) -> float:
        """Calculate combined confidence score."""
        # Base weighted confidence
        base_confidence = (
            gemini_confidence * weights.gemini_weight +
            local_confidence * weights.local_weight
        )
        
        # Agreement bonus/penalty
        if agreement_score > 0.8:
            bonus = weights.agreement_bonus * agreement_score
        else:
            bonus = -weights.disagreement_penalty * (1 - agreement_score)
        
        # Historical performance adjustment
        performance_factor = (
            self.performance_metrics["hybrid_accuracy"] /
            max(self.performance_metrics["gemini_accuracy"], 
                self.performance_metrics["local_accuracy"])
        )
        
        final_confidence = min(1.0, max(0.0, base_confidence + bonus * performance_factor))
        
        return final_confidence
    
    def calculate_agreement_score(
        self,
        gemini_result: Any,
        local_result: Any,
        result_type: str = "pattern"
    ) -> float:
        """Calculate agreement score between results."""
        if result_type == "pattern":
            return self._calculate_pattern_agreement(gemini_result, local_result)
        elif result_type == "embedding":
            return self._calculate_embedding_agreement(gemini_result, local_result)
        else:
            return 0.5  # Default neutral agreement
    
    def _calculate_pattern_agreement(
        self,
        gemini_result: AnalysisResult,
        local_result: Any
    ) -> float:
        """Calculate pattern detection agreement."""
        if not gemini_result or not local_result:
            return 0.0
        
        # Extract pattern types from both results
        gemini_patterns = set()
        if hasattr(gemini_result, 'findings'):
            for finding in gemini_result.findings:
                if 'pattern_type' in finding:
                    gemini_patterns.add(finding['pattern_type'])
        
        local_patterns = set()
        if hasattr(local_result, 'detected_patterns'):
            for pattern in local_result.detected_patterns:
                local_patterns.add(pattern.pattern_type)
        
        # Calculate Jaccard similarity
        if not gemini_patterns and not local_patterns:
            return 1.0
        
        intersection = len(gemini_patterns & local_patterns)
        union = len(gemini_patterns | local_patterns)
        
        return intersection / union if union > 0 else 0.0
    
    def _calculate_embedding_agreement(
        self,
        gemini_embedding: np.ndarray,
        local_embedding: np.ndarray
    ) -> float:
        """Calculate embedding similarity agreement."""
        if gemini_embedding is None or local_embedding is None:
            return 0.0
        
        # Calculate cosine similarity
        dot_product = np.dot(gemini_embedding, local_embedding)
        norm_product = np.linalg.norm(gemini_embedding) * np.linalg.norm(local_embedding)
        
        if norm_product == 0:
            return 0.0
        
        return float(dot_product / norm_product)
    
    def update_performance_metrics(
        self,
        result_type: str,
        gemini_accuracy: float,
        local_accuracy: float,
        hybrid_accuracy: float
    ):
        """Update performance metrics based on feedback."""
        # Use exponential moving average
        alpha = 0.1
        self.performance_metrics[f"{result_type}_gemini_accuracy"] = (
            alpha * gemini_accuracy + 
            (1 - alpha) * self.performance_metrics.get(f"{result_type}_gemini_accuracy", 0.85)
        )
        
        self.performance_metrics[f"{result_type}_local_accuracy"] = (
            alpha * local_accuracy + 
            (1 - alpha) * self.performance_metrics.get(f"{result_type}_local_accuracy", 0.78)
        )
        
        self.performance_metrics[f"{result_type}_hybrid_accuracy"] = (
            alpha * hybrid_accuracy + 
            (1 - alpha) * self.performance_metrics.get(f"{result_type}_hybrid_accuracy", 0.92)
        )


class GeminiIntegration:
    """Main integration layer for Gemini services."""
    
    def __init__(
        self,
        gemini_client: Optional[GeminiClient] = None,
        ml_manager: Optional[MLManager] = None
    ):
        """Initialize integration layer."""
        self.config = get_gemini_config()
        self.gemini_client = gemini_client
        self.ml_manager = ml_manager
        self.gemini_analyzer: Optional[GeminiAnalyzer] = None
        self.gemini_embeddings: Optional[GeminiEmbeddingService] = None
        self.inference_engine: Optional[InferenceEngine] = None
        
        self.confidence_aggregator = ConfidenceAggregator()
        self.ensemble_weights = EnsembleWeights()
        
        # Performance monitoring
        self.metrics = {
            "hybrid_analyses": 0,
            "gemini_only_analyses": 0,
            "local_only_analyses": 0,
            "total_processing_time": 0.0,
            "average_confidence": 0.0,
            "agreement_scores": [],
            "errors": 0
        }
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def initialize(self):
        """Initialize all components."""
        try:
            # Initialize Gemini client if not provided
            if self.gemini_client is None:
                self.gemini_client = await create_gemini_client(self.config)
            
            # Initialize Gemini services
            self.gemini_analyzer = GeminiAnalyzer(self.gemini_client)
            self.gemini_embeddings = GeminiEmbeddingService(self.gemini_client)
            
            # Initialize local ML components if not provided
            if self.ml_manager is None:
                self.ml_manager = MLManager()
            
            self.inference_engine = InferenceEngine()
            
            logger.info("Gemini integration initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Gemini integration: {e}")
            raise
    
    async def close(self):
        """Close all components."""
        if self.gemini_analyzer:
            await self.gemini_analyzer.__aexit__(None, None, None)
        
        if self.gemini_embeddings:
            await self.gemini_embeddings.__aexit__(None, None, None)
        
        if self.gemini_client:
            await self.gemini_client.close()
        
        logger.info("Gemini integration closed")
    
    async def analyze_code_hybrid(
        self,
        code: str,
        language: str,
        file_path: Optional[str] = None,
        analysis_types: List[AnalysisType] = None,
        integration_mode: IntegrationMode = IntegrationMode.HYBRID_PARALLEL,
        confidence_threshold: float = 0.7
    ) -> HybridResult:
        """Perform hybrid code analysis using both Gemini and local models."""
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Create code context
            context = CodeContext(
                code=code,
                language=language,
                file_path=file_path,
                line_count=len(code.splitlines())
            )
            
            if analysis_types is None:
                analysis_types = [AnalysisType.PATTERN_DETECTION]
            
            # Execute analysis based on integration mode
            if integration_mode == IntegrationMode.GEMINI_ONLY:
                result = await self._analyze_gemini_only(context, analysis_types)
            elif integration_mode == IntegrationMode.LOCAL_ONLY:
                result = await self._analyze_local_only(context, analysis_types)
            elif integration_mode == IntegrationMode.HYBRID_PARALLEL:
                result = await self._analyze_hybrid_parallel(context, analysis_types)
            elif integration_mode == IntegrationMode.HYBRID_SEQUENTIAL:
                result = await self._analyze_hybrid_sequential(context, analysis_types)
            elif integration_mode == IntegrationMode.CONFIDENCE_WEIGHTED:
                result = await self._analyze_confidence_weighted(context, analysis_types, confidence_threshold)
            else:
                result = await self._analyze_ensemble(context, analysis_types)
            
            # Update metrics
            processing_time = asyncio.get_event_loop().time() - start_time
            result.processing_time = processing_time
            result.integration_mode = integration_mode
            
            self.metrics["total_processing_time"] += processing_time
            self.metrics["hybrid_analyses"] += 1
            
            if result.combined_confidence > 0:
                current_avg = self.metrics["average_confidence"]
                count = self.metrics["hybrid_analyses"]
                self.metrics["average_confidence"] = (
                    (current_avg * (count - 1) + result.combined_confidence) / count
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Hybrid analysis failed: {e}")
            self.metrics["errors"] += 1
            raise
    
    async def _analyze_gemini_only(
        self,
        context: CodeContext,
        analysis_types: List[AnalysisType]
    ) -> HybridResult:
        """Analyze using Gemini only."""
        gemini_start = asyncio.get_event_loop().time()
        
        results = await self.gemini_analyzer.analyze_patterns(context, analysis_types)
        
        gemini_time = asyncio.get_event_loop().time() - gemini_start
        
        # Combine results
        combined_result = self._combine_gemini_results(results)
        
        return HybridResult(
            gemini_result=combined_result,
            local_result=None,
            combined_confidence=combined_result.confidence,
            final_result=combined_result,
            gemini_processing_time=gemini_time,
            local_processing_time=0.0,
            tokens_used=combined_result.tokens_used,
            confidence_breakdown={"gemini": combined_result.confidence}
        )
    
    async def _analyze_local_only(
        self,
        context: CodeContext,
        analysis_types: List[AnalysisType]
    ) -> HybridResult:
        """Analyze using local models only."""
        local_start = asyncio.get_event_loop().time()
        
        # Use local inference engine
        local_result = await self.inference_engine.analyze_patterns(
            context.code, context.language
        )
        
        local_time = asyncio.get_event_loop().time() - local_start
        
        # Extract confidence from local result
        local_confidence = getattr(local_result, 'confidence', 0.5)
        
        return HybridResult(
            gemini_result=None,
            local_result=local_result,
            combined_confidence=local_confidence,
            final_result=local_result,
            gemini_processing_time=0.0,
            local_processing_time=local_time,
            tokens_used=0,
            confidence_breakdown={"local": local_confidence}
        )
    
    async def _analyze_hybrid_parallel(
        self,
        context: CodeContext,
        analysis_types: List[AnalysisType]
    ) -> HybridResult:
        """Analyze using both Gemini and local models in parallel."""
        # Run both analyses in parallel
        gemini_task = asyncio.create_task(
            self.gemini_analyzer.analyze_patterns(context, analysis_types)
        )
        local_task = asyncio.create_task(
            self.inference_engine.analyze_patterns(context.code, context.language)
        )
        
        # Wait for both to complete
        gemini_results, local_result = await asyncio.gather(
            gemini_task, local_task, return_exceptions=True
        )
        
        # Handle exceptions
        if isinstance(gemini_results, Exception):
            logger.error(f"Gemini analysis failed: {gemini_results}")
            gemini_results = None
        
        if isinstance(local_result, Exception):
            logger.error(f"Local analysis failed: {local_result}")
            local_result = None
        
        # Combine results
        return self._combine_hybrid_results(gemini_results, local_result)
    
    async def _analyze_hybrid_sequential(
        self,
        context: CodeContext,
        analysis_types: List[AnalysisType]
    ) -> HybridResult:
        """Analyze using sequential hybrid approach."""
        # Start with local analysis (faster)
        local_start = asyncio.get_event_loop().time()
        local_result = await self.inference_engine.analyze_patterns(
            context.code, context.language
        )
        local_time = asyncio.get_event_loop().time() - local_start
        
        # Check if local confidence is sufficient
        local_confidence = getattr(local_result, 'confidence', 0.5)
        
        if local_confidence >= self.ensemble_weights.confidence_threshold:
            # Local result is confident enough
            return HybridResult(
                gemini_result=None,
                local_result=local_result,
                combined_confidence=local_confidence,
                final_result=local_result,
                local_processing_time=local_time,
                confidence_breakdown={"local": local_confidence}
            )
        
        # Local confidence is low, use Gemini for enhancement
        gemini_start = asyncio.get_event_loop().time()
        gemini_results = await self.gemini_analyzer.analyze_patterns(context, analysis_types)
        gemini_time = asyncio.get_event_loop().time() - gemini_start
        
        # Combine results
        hybrid_result = self._combine_hybrid_results(gemini_results, local_result)
        hybrid_result.local_processing_time = local_time
        hybrid_result.gemini_processing_time = gemini_time
        
        return hybrid_result
    
    async def _analyze_confidence_weighted(
        self,
        context: CodeContext,
        analysis_types: List[AnalysisType],
        confidence_threshold: float
    ) -> HybridResult:
        """Analyze using confidence-weighted approach."""
        # Run both analyses
        result = await self._analyze_hybrid_parallel(context, analysis_types)
        
        # Apply confidence weighting
        if result.gemini_result and result.local_result:
            gemini_confidence = result.gemini_result.confidence
            local_confidence = getattr(result.local_result, 'confidence', 0.5)
            
            # Weight final result based on confidence
            if gemini_confidence > local_confidence + 0.1:
                result.final_result = result.gemini_result
                result.combined_confidence = gemini_confidence
            elif local_confidence > gemini_confidence + 0.1:
                result.final_result = result.local_result
                result.combined_confidence = local_confidence
            else:
                # Combine results
                result.combined_confidence = (
                    gemini_confidence * self.ensemble_weights.gemini_weight +
                    local_confidence * self.ensemble_weights.local_weight
                )
        
        return result
    
    async def _analyze_ensemble(
        self,
        context: CodeContext,
        analysis_types: List[AnalysisType]
    ) -> HybridResult:
        """Analyze using ensemble approach."""
        # Run parallel analysis
        result = await self._analyze_hybrid_parallel(context, analysis_types)
        
        # Calculate ensemble result
        if result.gemini_result and result.local_result:
            gemini_confidence = result.gemini_result.confidence
            local_confidence = getattr(result.local_result, 'confidence', 0.5)
            
            # Calculate agreement score
            agreement_score = self.confidence_aggregator.calculate_agreement_score(
                result.gemini_result, result.local_result, "pattern"
            )
            
            # Calculate combined confidence
            combined_confidence = self.confidence_aggregator.calculate_combined_confidence(
                gemini_confidence, local_confidence, agreement_score, self.ensemble_weights
            )
            
            result.combined_confidence = combined_confidence
            result.confidence_breakdown = {
                "gemini": gemini_confidence,
                "local": local_confidence,
                "agreement": agreement_score,
                "combined": combined_confidence
            }
            
            # Store agreement score for metrics
            self.metrics["agreement_scores"].append(agreement_score)
        
        return result
    
    def _combine_gemini_results(
        self,
        results: Dict[AnalysisType, AnalysisResult]
    ) -> AnalysisResult:
        """Combine multiple Gemini analysis results."""
        if not results:
            return AnalysisResult(
                analysis_type=AnalysisType.PATTERN_DETECTION,
                confidence=0.0,
                findings=[],
                explanation="No results",
                recommendations=[],
                metadata={},
                processing_time=0.0,
                model_used="none",
                tokens_used=0
            )
        
        # Take the first result as base
        first_result = list(results.values())[0]
        
        # Combine all findings
        all_findings = []
        all_recommendations = []
        total_tokens = 0
        total_time = 0.0
        
        for result in results.values():
            all_findings.extend(result.findings)
            all_recommendations.extend(result.recommendations)
            total_tokens += result.tokens_used
            total_time += result.processing_time
        
        # Calculate average confidence
        avg_confidence = sum(r.confidence for r in results.values()) / len(results)
        
        return AnalysisResult(
            analysis_type=AnalysisType.PATTERN_DETECTION,
            confidence=avg_confidence,
            findings=all_findings,
            explanation=first_result.explanation,
            recommendations=all_recommendations,
            metadata={"combined_analysis": True, "analysis_count": len(results)},
            processing_time=total_time,
            model_used=first_result.model_used,
            tokens_used=total_tokens
        )
    
    def _combine_hybrid_results(
        self,
        gemini_results: Optional[Dict[AnalysisType, AnalysisResult]],
        local_result: Optional[Any]
    ) -> HybridResult:
        """Combine Gemini and local analysis results."""
        # Process Gemini results
        if gemini_results:
            combined_gemini = self._combine_gemini_results(gemini_results)
        else:
            combined_gemini = None
        
        # Calculate confidence and final result
        if combined_gemini and local_result:
            gemini_confidence = combined_gemini.confidence
            local_confidence = getattr(local_result, 'confidence', 0.5)
            
            # Simple weighted average for now
            combined_confidence = (
                gemini_confidence * self.ensemble_weights.gemini_weight +
                local_confidence * self.ensemble_weights.local_weight
            )
            
            # Choose final result based on confidence
            if gemini_confidence > local_confidence:
                final_result = combined_gemini
            else:
                final_result = local_result
        
        elif combined_gemini:
            combined_confidence = combined_gemini.confidence
            final_result = combined_gemini
        elif local_result:
            combined_confidence = getattr(local_result, 'confidence', 0.5)
            final_result = local_result
        else:
            combined_confidence = 0.0
            final_result = None
        
        return HybridResult(
            gemini_result=combined_gemini,
            local_result=local_result,
            combined_confidence=combined_confidence,
            final_result=final_result,
            tokens_used=combined_gemini.tokens_used if combined_gemini else 0,
            confidence_breakdown={
                "gemini": combined_gemini.confidence if combined_gemini else 0,
                "local": getattr(local_result, 'confidence', 0) if local_result else 0
            }
        )
    
    async def generate_hybrid_embeddings(
        self,
        code: str,
        language: str,
        embedding_type: EmbeddingType = EmbeddingType.CODE
    ) -> Dict[str, Any]:
        """Generate embeddings using both Gemini and local models."""
        # Generate Gemini embeddings
        gemini_embedding, gemini_metadata = await self.gemini_embeddings.generate_embedding(
            code, embedding_type, language
        )
        
        # Generate local embeddings (if available)
        local_embedding = None
        if self.ml_manager and hasattr(self.ml_manager, 'generate_embeddings'):
            local_embedding = await self.ml_manager.generate_embeddings(code, language)
        
        # Combine embeddings
        if local_embedding is not None:
            # Average the embeddings
            combined_embedding = (gemini_embedding + local_embedding) / 2
            confidence = 0.9
        else:
            combined_embedding = gemini_embedding
            confidence = 0.8
        
        return {
            "embedding": combined_embedding,
            "gemini_embedding": gemini_embedding,
            "local_embedding": local_embedding,
            "metadata": gemini_metadata,
            "confidence": confidence,
            "hybrid": local_embedding is not None
        }
    
    async def search_similar_hybrid(
        self,
        query_code: str,
        language: str,
        k: int = 10,
        threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """Search for similar code using hybrid approach."""
        # Generate hybrid query embedding
        query_embedding_result = await self.generate_hybrid_embeddings(
            query_code, language, EmbeddingType.QUERY
        )
        
        # Search using Gemini embeddings
        gemini_results = await self.gemini_embeddings.search_similar(
            query_code, EmbeddingType.QUERY, language, k, threshold
        )
        
        # Combine with local search if available
        local_results = []
        if self.ml_manager and hasattr(self.ml_manager, 'search_similar'):
            local_results = await self.ml_manager.search_similar(
                query_embedding_result["embedding"], k, threshold
            )
        
        # Merge and re-rank results
        combined_results = self._merge_search_results(gemini_results, local_results)
        
        return combined_results[:k]
    
    def _merge_search_results(
        self,
        gemini_results: List[Any],
        local_results: List[Any]
    ) -> List[Dict[str, Any]]:
        """Merge and re-rank search results."""
        # Simple merge based on similarity scores
        all_results = []
        
        # Process Gemini results
        for result in gemini_results:
            all_results.append({
                "source": "gemini",
                "similarity_score": result.similarity_score,
                "embedding_id": result.embedding_id,
                "metadata": result.metadata,
                "rank": result.rank
            })
        
        # Process local results
        for i, result in enumerate(local_results):
            all_results.append({
                "source": "local",
                "similarity_score": getattr(result, 'similarity_score', 0.5),
                "embedding_id": getattr(result, 'id', f"local_{i}"),
                "metadata": getattr(result, 'metadata', {}),
                "rank": i
            })
        
        # Sort by similarity score
        all_results.sort(key=lambda x: x['similarity_score'], reverse=True)
        
        return all_results
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get integration metrics."""
        base_metrics = self.metrics.copy()
        
        # Add derived metrics
        if self.metrics["agreement_scores"]:
            base_metrics["average_agreement"] = sum(self.metrics["agreement_scores"]) / len(self.metrics["agreement_scores"])
        else:
            base_metrics["average_agreement"] = 0.0
        
        # Add component metrics
        if self.gemini_client:
            base_metrics["gemini_client_metrics"] = self.gemini_client.get_metrics()
        
        if self.gemini_embeddings:
            base_metrics["gemini_embeddings_metrics"] = self.gemini_embeddings.get_metrics()
        
        return base_metrics
    
    def update_ensemble_weights(
        self,
        gemini_weight: float,
        local_weight: float,
        confidence_threshold: float = None
    ):
        """Update ensemble weights based on performance."""
        self.ensemble_weights.gemini_weight = gemini_weight
        self.ensemble_weights.local_weight = local_weight
        
        if confidence_threshold is not None:
            self.ensemble_weights.confidence_threshold = confidence_threshold
        
        logger.info(f"Updated ensemble weights: Gemini={gemini_weight}, Local={local_weight}")
    
    def clear_metrics(self):
        """Clear performance metrics."""
        self.metrics = {
            "hybrid_analyses": 0,
            "gemini_only_analyses": 0,
            "local_only_analyses": 0,
            "total_processing_time": 0.0,
            "average_confidence": 0.0,
            "agreement_scores": [],
            "errors": 0
        }
        
        logger.info("Integration metrics cleared")


# Factory function
async def create_gemini_integration(
    gemini_client: Optional[GeminiClient] = None,
    ml_manager: Optional[MLManager] = None
) -> GeminiIntegration:
    """Create and initialize Gemini integration."""
    integration = GeminiIntegration(gemini_client, ml_manager)
    await integration.initialize()
    return integration