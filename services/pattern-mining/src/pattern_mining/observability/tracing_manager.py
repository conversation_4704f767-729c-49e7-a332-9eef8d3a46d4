"""
Distributed Tracing Manager with OpenTelemetry

Provides comprehensive distributed tracing including:
- Automatic span creation and management
- Context propagation across services
- Custom span attributes and events
- Performance metrics collection
- Error tracking and correlation
"""

import time
import asyncio
from typing import Dict, Any, Optional, List, Callable, Union
from dataclasses import dataclass, field
from contextlib import contextmanager, asynccontextmanager
from datetime import datetime
import logging
import uuid
from functools import wraps
from enum import Enum

from opentelemetry import trace, context
from opentelemetry.exporter.gcp.trace import CloudTraceSpanExporter
from opentelemetry.exporter.jaeger.thrift import JaegerExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.sdk.resources import Resource
from opentelemetry.trace import Status, StatusCode
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.aiohttp_client import AioHttpClientInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor

logger = logging.getLogger(__name__)


class SpanKind(Enum):
    """Span kinds for different operation types."""
    SERVER = "server"
    CLIENT = "client"
    PRODUCER = "producer"
    CONSUMER = "consumer"
    INTERNAL = "internal"


@dataclass
class SpanContext:
    """Span context information."""
    trace_id: str
    span_id: str
    parent_span_id: Optional[str] = None
    baggage: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'trace_id': self.trace_id,
            'span_id': self.span_id,
            'parent_span_id': self.parent_span_id,
            'baggage': self.baggage
        }


@dataclass
class TraceEvent:
    """Trace event data."""
    name: str
    timestamp: datetime
    attributes: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'name': self.name,
            'timestamp': self.timestamp.isoformat(),
            'attributes': self.attributes
        }


class TracingManager:
    """
    Distributed tracing manager using OpenTelemetry.
    
    Provides comprehensive tracing capabilities with automatic
    instrumentation and manual span management.
    """
    
    def __init__(self, 
                 service_name: str = "pattern-mining",
                 service_version: str = "1.0.0",
                 environment: str = "production",
                 enable_auto_instrumentation: bool = True):
        """Initialize tracing manager."""
        self.service_name = service_name
        self.service_version = service_version
        self.environment = environment
        self.enable_auto_instrumentation = enable_auto_instrumentation
        
        # Initialize OpenTelemetry
        self._setup_tracing()
        
        # Get tracer
        self.tracer = trace.get_tracer(
            service_name,
            service_version
        )
        
        # Custom span processors
        self._span_processors: List[Callable] = []
        
        # Active spans tracking
        self._active_spans: Dict[str, trace.Span] = {}
        
        logger.info(
            "Tracing manager initialized",
            service_name=service_name,
            service_version=service_version,
            environment=environment
        )
    
    def _setup_tracing(self):
        """Setup OpenTelemetry tracing."""
        # Create resource
        resource = Resource.create({
            "service.name": self.service_name,
            "service.version": self.service_version,
            "deployment.environment": self.environment
        })
        
        # Create tracer provider
        provider = TracerProvider(resource=resource)
        
        # Add span processors
        self._add_span_processors(provider)
        
        # Set global tracer provider
        trace.set_tracer_provider(provider)
        
        # Auto-instrumentation
        if self.enable_auto_instrumentation:
            self._setup_auto_instrumentation()
    
    def _add_span_processors(self, provider: TracerProvider):
        """Add span processors to the tracer provider."""
        # Google Cloud Trace exporter
        try:
            cloud_trace_exporter = CloudTraceSpanExporter()
            provider.add_span_processor(
                BatchSpanProcessor(cloud_trace_exporter)
            )
            logger.info("Google Cloud Trace exporter added")
        except Exception as e:
            logger.warning(f"Failed to add Google Cloud Trace exporter: {e}")
        
        # Jaeger exporter (for local development)
        try:
            jaeger_exporter = JaegerExporter(
                agent_host_name="localhost",
                agent_port=6831,
            )
            provider.add_span_processor(
                BatchSpanProcessor(jaeger_exporter)
            )
            logger.info("Jaeger exporter added")
        except Exception as e:
            logger.warning(f"Failed to add Jaeger exporter: {e}")
    
    def _setup_auto_instrumentation(self):
        """Setup automatic instrumentation."""
        try:
            # FastAPI instrumentation
            FastAPIInstrumentor.instrument()
            logger.info("FastAPI instrumentation enabled")
        except Exception as e:
            logger.warning(f"Failed to instrument FastAPI: {e}")
        
        try:
            # HTTP client instrumentation
            AioHttpClientInstrumentor().instrument()
            logger.info("HTTP client instrumentation enabled")
        except Exception as e:
            logger.warning(f"Failed to instrument HTTP client: {e}")
        
        try:
            # SQLAlchemy instrumentation
            SQLAlchemyInstrumentor().instrument()
            logger.info("SQLAlchemy instrumentation enabled")
        except Exception as e:
            logger.warning(f"Failed to instrument SQLAlchemy: {e}")
        
        try:
            # Redis instrumentation
            RedisInstrumentor().instrument()
            logger.info("Redis instrumentation enabled")
        except Exception as e:
            logger.warning(f"Failed to instrument Redis: {e}")
    
    def add_span_processor(self, processor: Callable):
        """Add a custom span processor."""
        self._span_processors.append(processor)
    
    @contextmanager
    def trace_operation(self, operation_name: str, 
                       span_kind: SpanKind = SpanKind.INTERNAL,
                       attributes: Optional[Dict[str, Any]] = None):
        """Context manager for tracing operations."""
        span = self.tracer.start_span(
            operation_name,
            kind=getattr(trace.SpanKind, span_kind.value.upper())
        )
        
        # Add attributes
        if attributes:
            for key, value in attributes.items():
                span.set_attribute(key, value)
        
        # Store span for tracking
        span_id = str(uuid.uuid4())
        self._active_spans[span_id] = span
        
        try:
            with trace.use_span(span, end_on_exit=True):
                yield span
        except Exception as e:
            # Record exception
            span.record_exception(e)
            span.set_status(Status(StatusCode.ERROR, str(e)))
            raise
        finally:
            # Remove from active spans
            if span_id in self._active_spans:
                del self._active_spans[span_id]
    
    @asynccontextmanager
    async def trace_async_operation(self, operation_name: str,
                                  span_kind: SpanKind = SpanKind.INTERNAL,
                                  attributes: Optional[Dict[str, Any]] = None):
        """Async context manager for tracing operations."""
        span = self.tracer.start_span(
            operation_name,
            kind=getattr(trace.SpanKind, span_kind.value.upper())
        )
        
        # Add attributes
        if attributes:
            for key, value in attributes.items():
                span.set_attribute(key, value)
        
        # Store span for tracking
        span_id = str(uuid.uuid4())
        self._active_spans[span_id] = span
        
        try:
            with trace.use_span(span, end_on_exit=True):
                yield span
        except Exception as e:
            # Record exception
            span.record_exception(e)
            span.set_status(Status(StatusCode.ERROR, str(e)))
            raise
        finally:
            # Remove from active spans
            if span_id in self._active_spans:
                del self._active_spans[span_id]
    
    def trace_function(self, operation_name: Optional[str] = None,
                      span_kind: SpanKind = SpanKind.INTERNAL,
                      attributes: Optional[Dict[str, Any]] = None):
        """Decorator for tracing functions."""
        def decorator(func):
            nonlocal operation_name
            if operation_name is None:
                operation_name = f"{func.__module__}.{func.__name__}"
            
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                async with self.trace_async_operation(
                    operation_name, span_kind, attributes
                ) as span:
                    # Add function-specific attributes
                    span.set_attribute("function.name", func.__name__)
                    span.set_attribute("function.module", func.__module__)
                    span.set_attribute("function.args_count", len(args))
                    span.set_attribute("function.kwargs_count", len(kwargs))
                    
                    # Call custom span processors
                    for processor in self._span_processors:
                        try:
                            processor(span, func, args, kwargs)
                        except Exception as e:
                            logger.warning(f"Span processor failed: {e}")
                    
                    result = await func(*args, **kwargs)
                    
                    # Add result attributes if possible
                    if hasattr(result, '__len__'):
                        span.set_attribute("result.length", len(result))
                    
                    return result
            
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                with self.trace_operation(
                    operation_name, span_kind, attributes
                ) as span:
                    # Add function-specific attributes
                    span.set_attribute("function.name", func.__name__)
                    span.set_attribute("function.module", func.__module__)
                    span.set_attribute("function.args_count", len(args))
                    span.set_attribute("function.kwargs_count", len(kwargs))
                    
                    # Call custom span processors
                    for processor in self._span_processors:
                        try:
                            processor(span, func, args, kwargs)
                        except Exception as e:
                            logger.warning(f"Span processor failed: {e}")
                    
                    result = func(*args, **kwargs)
                    
                    # Add result attributes if possible
                    if hasattr(result, '__len__'):
                        span.set_attribute("result.length", len(result))
                    
                    return result
            
            return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
        return decorator
    
    def get_current_span(self) -> Optional[trace.Span]:
        """Get the current active span."""
        return trace.get_current_span()
    
    def get_current_trace_id(self) -> Optional[str]:
        """Get the current trace ID."""
        span = self.get_current_span()
        if span and span.get_span_context().is_valid:
            return format(span.get_span_context().trace_id, '032x')
        return None
    
    def get_current_span_id(self) -> Optional[str]:
        """Get the current span ID."""
        span = self.get_current_span()
        if span and span.get_span_context().is_valid:
            return format(span.get_span_context().span_id, '016x')
        return None
    
    def get_span_context(self) -> Optional[SpanContext]:
        """Get the current span context."""
        span = self.get_current_span()
        if span and span.get_span_context().is_valid:
            span_context = span.get_span_context()
            return SpanContext(
                trace_id=format(span_context.trace_id, '032x'),
                span_id=format(span_context.span_id, '016x'),
                parent_span_id=None,  # Would need parent tracking
                baggage={}  # Would need baggage extraction
            )
        return None
    
    def add_event(self, name: str, attributes: Optional[Dict[str, Any]] = None):
        """Add an event to the current span."""
        span = self.get_current_span()
        if span:
            span.add_event(name, attributes or {})
    
    def set_attribute(self, key: str, value: Any):
        """Set an attribute on the current span."""
        span = self.get_current_span()
        if span:
            span.set_attribute(key, value)
    
    def set_attributes(self, attributes: Dict[str, Any]):
        """Set multiple attributes on the current span."""
        span = self.get_current_span()
        if span:
            for key, value in attributes.items():
                span.set_attribute(key, value)
    
    def record_exception(self, exception: Exception):
        """Record an exception in the current span."""
        span = self.get_current_span()
        if span:
            span.record_exception(exception)
            span.set_status(Status(StatusCode.ERROR, str(exception)))
    
    def set_status(self, status_code: StatusCode, description: str = ""):
        """Set the status of the current span."""
        span = self.get_current_span()
        if span:
            span.set_status(Status(status_code, description))
    
    def trace_http_request(self, method: str, url: str, status_code: int,
                          duration: float, request_size: int = None,
                          response_size: int = None):
        """Trace HTTP request with standard attributes."""
        attributes = {
            "http.method": method,
            "http.url": url,
            "http.status_code": status_code,
            "http.response_time_ms": duration * 1000
        }
        
        if request_size:
            attributes["http.request_content_length"] = request_size
        if response_size:
            attributes["http.response_content_length"] = response_size
        
        self.set_attributes(attributes)
    
    def trace_database_query(self, query_type: str, table: str, duration: float,
                           rows_affected: int = None):
        """Trace database query with standard attributes."""
        attributes = {
            "db.operation": query_type,
            "db.sql.table": table,
            "db.query_duration_ms": duration * 1000
        }
        
        if rows_affected is not None:
            attributes["db.rows_affected"] = rows_affected
        
        self.set_attributes(attributes)
    
    def trace_ml_inference(self, model_name: str, model_version: str,
                          batch_size: int, duration: float, accuracy: float = None):
        """Trace ML inference with standard attributes."""
        attributes = {
            "ml.model_name": model_name,
            "ml.model_version": model_version,
            "ml.batch_size": batch_size,
            "ml.inference_duration_ms": duration * 1000
        }
        
        if accuracy is not None:
            attributes["ml.accuracy"] = accuracy
        
        self.set_attributes(attributes)
    
    def trace_pattern_detection(self, pattern_type: str, confidence: float,
                              file_path: str, line_number: int, duration: float):
        """Trace pattern detection with standard attributes."""
        attributes = {
            "pattern.type": pattern_type,
            "pattern.confidence": confidence,
            "pattern.file_path": file_path,
            "pattern.line_number": line_number,
            "pattern.detection_duration_ms": duration * 1000
        }
        
        self.set_attributes(attributes)
    
    def trace_business_event(self, event_type: str, user_id: str = None,
                           session_id: str = None, metadata: Dict[str, Any] = None):
        """Trace business event with standard attributes."""
        attributes = {
            "business.event_type": event_type,
            "business.timestamp": datetime.now().isoformat()
        }
        
        if user_id:
            attributes["business.user_id"] = user_id
        if session_id:
            attributes["business.session_id"] = session_id
        if metadata:
            for key, value in metadata.items():
                attributes[f"business.{key}"] = value
        
        self.set_attributes(attributes)
    
    def get_active_spans_count(self) -> int:
        """Get the number of active spans."""
        return len(self._active_spans)
    
    def get_tracing_stats(self) -> Dict[str, Any]:
        """Get tracing statistics."""
        return {
            'service_name': self.service_name,
            'service_version': self.service_version,
            'environment': self.environment,
            'active_spans': len(self._active_spans),
            'auto_instrumentation_enabled': self.enable_auto_instrumentation,
            'span_processors': len(self._span_processors)
        }
    
    def flush_spans(self):
        """Flush all pending spans."""
        try:
            # Force flush all span processors
            provider = trace.get_tracer_provider()
            if hasattr(provider, 'force_flush'):
                provider.force_flush()
        except Exception as e:
            logger.error(f"Failed to flush spans: {e}")
    
    def shutdown(self):
        """Shutdown the tracing manager."""
        try:
            self.flush_spans()
            
            # Shutdown tracer provider
            provider = trace.get_tracer_provider()
            if hasattr(provider, 'shutdown'):
                provider.shutdown()
                
            logger.info("Tracing manager shutdown completed")
        except Exception as e:
            logger.error(f"Error during tracing manager shutdown: {e}")


# Global tracing manager instance
_tracing_manager_instance: Optional[TracingManager] = None


def get_tracing_manager() -> TracingManager:
    """Get the global tracing manager instance."""
    global _tracing_manager_instance
    if _tracing_manager_instance is None:
        _tracing_manager_instance = TracingManager()
    return _tracing_manager_instance


def init_tracing_manager(service_name: str = "pattern-mining",
                        service_version: str = "1.0.0",
                        environment: str = "production",
                        enable_auto_instrumentation: bool = True) -> TracingManager:
    """Initialize the global tracing manager instance."""
    global _tracing_manager_instance
    _tracing_manager_instance = TracingManager(
        service_name=service_name,
        service_version=service_version,
        environment=environment,
        enable_auto_instrumentation=enable_auto_instrumentation
    )
    return _tracing_manager_instance