"""
Observability System for Pattern Mining Service

This module provides comprehensive observability capabilities including:
- Distributed tracing with OpenTelemetry
- Request correlation and tracking
- Performance profiling
- Error tracking and analysis
- User behavior analytics

Components:
- TracingManager: Distributed tracing management
- CorrelationManager: Request correlation and context propagation
- PerformanceProfiler: Performance profiling and analysis
- ErrorTracker: Error tracking and analysis
- UserAnalytics: User behavior analytics
"""

from .tracing_manager import TracingManager, SpanContext
from .correlation_manager import CorrelationManager
from .performance_profiler import PerformanceProfiler
from .error_tracker import ErrorTracker
from .user_analytics import UserAnalytics

__all__ = [
    "TracingManager",
    "SpanContext",
    "CorrelationManager",
    "PerformanceProfiler",
    "ErrorTracker",
    "UserAnalytics"
]