"""
Repository Analysis Routes

API endpoints for repository-level pattern analysis and mining operations.
Handles full repository analysis, pattern summaries, and repository comparisons.
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query, Path, Request
from fastapi.responses import StreamingResponse
from typing import List, Dict, Any, Optional, AsyncGenerator
import asyncio
import json
from uuid import uuid4
from datetime import datetime
import structlog

from ...config.settings import get_settings, Settings
from ...models.patterns import (
    DetectedPattern,
    PatternStatistics,
    PatternType,
    SeverityLevel,
    PatternAnalysisJob
)
from ...models.api import (
    RepositoryAnalysisRequest,
    RepositoryAnalysisResponse,
    RepositoryPatternsResponse,
    RepositorySummary,
    RepositoryComparison,
    RepositoryComparisonRequest
)
from ...detectors.manager import get_pattern_detector_manager
from ...features.extractor import get_feature_extractor
from ...utils.validation import validate_repository_input
from ...database.repositories.pattern_repository import get_pattern_repository
from ...database.repositories.analysis_repository import get_analysis_repository
from ..middleware.error_handling import handle_api_errors
from ..middleware.monitoring import track_response_time
from ..utils.cache import get_cache_manager

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.post("/analyze", response_model=RepositoryAnalysisResponse)
@handle_api_errors
@track_response_time
async def analyze_repository(
    request: RepositoryAnalysisRequest,
    background_tasks: BackgroundTasks,
    req: Request,
    settings: Settings = Depends(get_settings)
) -> RepositoryAnalysisResponse:
    """
    Analyze an entire repository for patterns.
    
    Performs comprehensive analysis of a repository including file-level
    pattern detection, cross-file relationships, and repository-wide metrics.
    
    Args:
        request: Repository analysis request containing repository info and parameters
        background_tasks: FastAPI background tasks for async processing
        req: FastAPI request object for context
        settings: Application settings
        
    Returns:
        RepositoryAnalysisResponse: Comprehensive repository analysis results
        
    Raises:
        HTTPException: 400 for invalid input, 429 for rate limiting, 500 for server errors
    """
    start_time = datetime.utcnow()
    job_id = str(uuid4())
    
    logger.info(
        "Repository analysis request started",
        job_id=job_id,
        repository_url=request.repository_url,
        include_files=len(request.include_files) if request.include_files else "all",
        enable_cross_file_analysis=request.enable_cross_file_analysis
    )
    
    # Validate repository input
    if not validate_repository_input(request):
        logger.warning("Invalid repository input", job_id=job_id)
        raise HTTPException(
            status_code=400,
            detail="Invalid repository input: Repository URL or files must be provided"
        )
    
    # Check cache for similar repository analysis
    cache_manager = get_cache_manager()
    cache_key = f"repo_analysis:{hash(request.repository_url)}:{request.branch}:{','.join(request.detection_types)}"
    cached_result = await cache_manager.get(cache_key)
    
    if cached_result and not request.force_refresh:
        logger.info("Returning cached repository analysis", job_id=job_id)
        return RepositoryAnalysisResponse.parse_obj(cached_result)
    
    try:
        # Get detector manager
        detector_manager = get_pattern_detector_manager()
        
        # Create analysis job
        analysis_repo = get_analysis_repository()
        await analysis_repo.create_repository_analysis_job(
            job_id=job_id,
            request_data=request.dict(),
            status="processing"
        )
        
        # Start background processing for large repositories
        if request.async_processing:
            background_tasks.add_task(
                process_repository_analysis,
                job_id,
                request,
                detector_manager
            )
            
            return RepositoryAnalysisResponse(
                job_id=job_id,
                repository_url=request.repository_url,
                status="processing",
                patterns=[],
                summary=RepositorySummary(
                    total_files=0,
                    total_patterns=0,
                    critical_patterns=0,
                    high_confidence_patterns=0,
                    languages_analyzed=[],
                    analysis_duration=0.0
                ),
                metadata={
                    "processing_started": datetime.utcnow().isoformat(),
                    "estimated_completion": (datetime.utcnow().timestamp() + 300) * 1000,  # 5 minutes
                    "job_url": f"/api/v1/patterns/jobs/{job_id}"
                }
            )
        
        # Process repository synchronously
        analysis_results = await detector_manager.analyze_repository(
            repository_url=request.repository_url,
            branch=request.branch,
            include_files=request.include_files,
            exclude_files=request.exclude_files,
            detection_types=request.detection_types,
            confidence_threshold=request.confidence_threshold,
            enable_cross_file_analysis=request.enable_cross_file_analysis,
            max_file_size=request.max_file_size
        )
        
        # Calculate processing time
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        # Create comprehensive summary
        summary = RepositorySummary(
            total_files=analysis_results.get("total_files", 0),
            total_patterns=len(analysis_results.get("patterns", [])),
            critical_patterns=sum(1 for p in analysis_results.get("patterns", []) if p.is_critical),
            high_confidence_patterns=sum(1 for p in analysis_results.get("patterns", []) if p.is_high_confidence),
            languages_analyzed=analysis_results.get("languages", []),
            analysis_duration=processing_time,
            file_coverage=analysis_results.get("file_coverage", 0.0),
            pattern_density=analysis_results.get("pattern_density", 0.0),
            quality_score=analysis_results.get("quality_score", 0.0)
        )
        
        # Create response
        response = RepositoryAnalysisResponse(
            job_id=job_id,
            repository_url=request.repository_url,
            status="completed",
            patterns=analysis_results.get("patterns", []),
            summary=summary,
            metadata={
                "processing_time": processing_time,
                "model_versions": detector_manager.get_model_versions(),
                "detection_types_used": request.detection_types,
                "branch": request.branch,
                "files_analyzed": analysis_results.get("files_analyzed", []),
                "skipped_files": analysis_results.get("skipped_files", [])
            }
        )
        
        # Cache the results
        await cache_manager.set(cache_key, response.dict(), ttl=7200)  # Cache for 2 hours
        
        # Update job status
        await analysis_repo.update_repository_analysis_job(
            job_id=job_id,
            status="completed",
            results=response.dict()
        )
        
        logger.info(
            "Repository analysis completed successfully",
            job_id=job_id,
            repository_url=request.repository_url,
            patterns_found=len(response.patterns),
            processing_time=processing_time
        )
        
        return response
        
    except Exception as e:
        logger.error(
            "Repository analysis failed",
            job_id=job_id,
            repository_url=request.repository_url,
            error=str(e),
            exc_info=True
        )
        
        # Update job status
        await analysis_repo.update_repository_analysis_job(
            job_id=job_id,
            status="failed",
            error=str(e)
        )
        
        raise HTTPException(
            status_code=500,
            detail=f"Repository analysis failed: {str(e)}"
        )


@router.get("/{repo_id}/patterns", response_model=RepositoryPatternsResponse)
@handle_api_errors
async def get_repository_patterns(
    repo_id: str = Path(..., description="Repository identifier"),
    pattern_type: Optional[PatternType] = Query(None, description="Filter by pattern type"),
    severity: Optional[SeverityLevel] = Query(None, description="Filter by severity level"),
    language: Optional[str] = Query(None, description="Filter by programming language"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of patterns to return"),
    offset: int = Query(0, ge=0, description="Number of patterns to skip")
) -> RepositoryPatternsResponse:
    """
    Get patterns detected in a specific repository.
    
    Retrieves patterns from a previously analyzed repository with optional
    filtering by type, severity, language, and pagination support.
    
    Args:
        repo_id: Repository identifier from previous analysis
        pattern_type: Optional pattern type filter
        severity: Optional severity level filter
        language: Optional programming language filter
        limit: Maximum number of patterns to return (1-1000)
        offset: Number of patterns to skip for pagination
        
    Returns:
        RepositoryPatternsResponse: Filtered patterns with pagination info
        
    Raises:
        HTTPException: 404 if repository not found, 500 for server errors
    """
    logger.info(
        "Getting repository patterns",
        repo_id=repo_id,
        pattern_type=pattern_type,
        severity=severity,
        language=language,
        limit=limit,
        offset=offset
    )
    
    try:
        pattern_repo = get_pattern_repository()
        
        # Get filtered patterns
        patterns, total_count = await pattern_repo.get_repository_patterns(
            repo_id=repo_id,
            pattern_type=pattern_type,
            severity=severity,
            language=language,
            limit=limit,
            offset=offset
        )
        
        if not patterns and offset == 0:
            raise HTTPException(
                status_code=404,
                detail=f"No patterns found for repository {repo_id}"
            )
        
        # Calculate pagination info
        has_next = (offset + limit) < total_count
        has_previous = offset > 0
        
        response = RepositoryPatternsResponse(
            repo_id=repo_id,
            patterns=patterns,
            pagination={
                "total_count": total_count,
                "limit": limit,
                "offset": offset,
                "has_next": has_next,
                "has_previous": has_previous,
                "next_offset": offset + limit if has_next else None,
                "previous_offset": max(0, offset - limit) if has_previous else None
            },
            filters={
                "pattern_type": pattern_type,
                "severity": severity,
                "language": language
            }
        )
        
        logger.info(
            "Repository patterns retrieved successfully",
            repo_id=repo_id,
            patterns_count=len(patterns),
            total_count=total_count
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to get repository patterns",
            repo_id=repo_id,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get repository patterns: {str(e)}"
        )


@router.get("/{repo_id}/summary", response_model=RepositorySummary)
@handle_api_errors
async def get_repository_summary(
    repo_id: str = Path(..., description="Repository identifier"),
    include_trends: bool = Query(False, description="Include trend analysis")
) -> RepositorySummary:
    """
    Get analysis summary for a specific repository.
    
    Provides comprehensive summary information including pattern counts,
    quality metrics, and optional trend analysis.
    
    Args:
        repo_id: Repository identifier from previous analysis
        include_trends: Whether to include trend analysis
        
    Returns:
        RepositorySummary: Comprehensive repository analysis summary
        
    Raises:
        HTTPException: 404 if repository not found, 500 for server errors
    """
    logger.info(
        "Getting repository summary",
        repo_id=repo_id,
        include_trends=include_trends
    )
    
    try:
        analysis_repo = get_analysis_repository()
        summary = await analysis_repo.get_repository_summary(
            repo_id=repo_id,
            include_trends=include_trends
        )
        
        if not summary:
            raise HTTPException(
                status_code=404,
                detail=f"Repository {repo_id} not found"
            )
        
        logger.info(
            "Repository summary retrieved successfully",
            repo_id=repo_id,
            total_patterns=summary.total_patterns
        )
        
        return summary
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to get repository summary",
            repo_id=repo_id,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get repository summary: {str(e)}"
        )


@router.post("/compare", response_model=RepositoryComparison)
@handle_api_errors
async def compare_repositories(
    request: RepositoryComparisonRequest,
    req: Request,
    settings: Settings = Depends(get_settings)
) -> RepositoryComparison:
    """
    Compare multiple repositories for pattern similarities and differences.
    
    Performs comprehensive comparison of repositories including pattern
    overlap, unique patterns, quality metrics, and architectural similarities.
    
    Args:
        request: Repository comparison request with repository identifiers
        req: FastAPI request object for context
        settings: Application settings
        
    Returns:
        RepositoryComparison: Comprehensive repository comparison results
        
    Raises:
        HTTPException: 400 for invalid input, 404 if repositories not found, 500 for server errors
    """
    if len(request.repository_ids) < 2:
        raise HTTPException(
            status_code=400,
            detail="At least 2 repositories are required for comparison"
        )
    
    if len(request.repository_ids) > 10:
        raise HTTPException(
            status_code=400,
            detail="Maximum 10 repositories can be compared at once"
        )
    
    comparison_id = str(uuid4())
    logger.info(
        "Starting repository comparison",
        comparison_id=comparison_id,
        repository_ids=request.repository_ids,
        comparison_type=request.comparison_type
    )
    
    try:
        analysis_repo = get_analysis_repository()
        pattern_repo = get_pattern_repository()
        
        # Get repository summaries
        repositories_data = []
        for repo_id in request.repository_ids:
            repo_summary = await analysis_repo.get_repository_summary(repo_id)
            if not repo_summary:
                raise HTTPException(
                    status_code=404,
                    detail=f"Repository {repo_id} not found"
                )
            repositories_data.append(repo_summary)
        
        # Perform comparison analysis
        comparison_results = await pattern_repo.compare_repositories(
            repository_ids=request.repository_ids,
            comparison_type=request.comparison_type,
            include_patterns=request.include_patterns,
            similarity_threshold=request.similarity_threshold
        )
        
        # Create comprehensive comparison response
        comparison = RepositoryComparison(
            comparison_id=comparison_id,
            repository_ids=request.repository_ids,
            comparison_type=request.comparison_type,
            repositories_data=repositories_data,
            pattern_overlap=comparison_results.get("pattern_overlap", {}),
            unique_patterns=comparison_results.get("unique_patterns", {}),
            quality_comparison=comparison_results.get("quality_comparison", {}),
            similarity_matrix=comparison_results.get("similarity_matrix", {}),
            recommendations=comparison_results.get("recommendations", []),
            metadata={
                "comparison_time": datetime.utcnow().isoformat(),
                "total_patterns_compared": comparison_results.get("total_patterns", 0),
                "comparison_algorithm": comparison_results.get("algorithm", "cosine_similarity")
            }
        )
        
        logger.info(
            "Repository comparison completed successfully",
            comparison_id=comparison_id,
            repositories_count=len(request.repository_ids),
            total_patterns=comparison_results.get("total_patterns", 0)
        )
        
        return comparison
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Repository comparison failed",
            comparison_id=comparison_id,
            repository_ids=request.repository_ids,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Repository comparison failed: {str(e)}"
        )


@router.get("/{repo_id}/analyze/stream")
@handle_api_errors
async def stream_repository_analysis(
    repo_id: str = Path(..., description="Repository identifier"),
    branch: str = Query("main", description="Git branch to analyze"),
    detection_types: List[str] = Query(default=["ml_inference"], description="Detection types to use")
) -> StreamingResponse:
    """
    Stream repository analysis results in real-time.
    
    Provides real-time streaming of repository analysis progress and results
    as files are processed. Useful for large repositories or interactive applications.
    
    Args:
        repo_id: Repository identifier
        branch: Git branch to analyze
        detection_types: List of detection types to use
        
    Returns:
        StreamingResponse: Server-sent events with analysis progress and results
    """
    
    async def generate_analysis_stream() -> AsyncGenerator[str, None]:
        """Generate streaming repository analysis results."""
        job_id = str(uuid4())
        
        try:
            # Send initial status
            yield f"data: {json.dumps({'event': 'started', 'job_id': job_id, 'repo_id': repo_id})}\n\n"
            
            # Get detector manager
            detector_manager = get_pattern_detector_manager()
            
            # Stream repository analysis
            async for progress in detector_manager.stream_repository_analysis(
                repository_url=repo_id,  # Assuming repo_id is URL or can be resolved
                branch=branch,
                detection_types=detection_types
            ):
                yield f"data: {json.dumps({'event': 'progress', 'data': progress})}\n\n"
            
            # Send completion
            yield f"data: {json.dumps({'event': 'completed', 'job_id': job_id})}\n\n"
            
        except Exception as e:
            logger.error("Streaming repository analysis failed", job_id=job_id, repo_id=repo_id, error=str(e))
            yield f"data: {json.dumps({'event': 'error', 'error': str(e)})}\n\n"
    
    return StreamingResponse(
        generate_analysis_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no"
        }
    )


@router.get("/{repo_id}/statistics", response_model=PatternStatistics)
@handle_api_errors
async def get_repository_statistics(
    repo_id: str = Path(..., description="Repository identifier"),
    breakdown_by: str = Query("type", regex="^(type|severity|language|file)$", description="Statistics breakdown")
) -> PatternStatistics:
    """
    Get comprehensive statistics for a repository.
    
    Returns detailed statistics about patterns found in the repository
    with various breakdown options.
    
    Args:
        repo_id: Repository identifier
        breakdown_by: Statistics breakdown type (type, severity, language, file)
        
    Returns:
        PatternStatistics: Comprehensive repository statistics
        
    Raises:
        HTTPException: 404 if repository not found, 500 for server errors
    """
    logger.info(
        "Getting repository statistics",
        repo_id=repo_id,
        breakdown_by=breakdown_by
    )
    
    try:
        pattern_repo = get_pattern_repository()
        statistics = await pattern_repo.get_repository_statistics(
            repo_id=repo_id,
            breakdown_by=breakdown_by
        )
        
        if not statistics:
            raise HTTPException(
                status_code=404,
                detail=f"No statistics found for repository {repo_id}"
            )
        
        return statistics
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to get repository statistics",
            repo_id=repo_id,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get repository statistics: {str(e)}"
        )


# Background task implementations

async def process_repository_analysis(
    job_id: str,
    request: RepositoryAnalysisRequest,
    detector_manager
) -> None:
    """
    Background task for repository analysis processing.
    
    Processes repository analysis requests asynchronously with progress
    updates and comprehensive error handling.
    
    Args:
        job_id: Analysis job identifier
        request: Repository analysis request
        detector_manager: Pattern detector manager instance
    """
    logger.info(
        "Processing repository analysis",
        job_id=job_id,
        repository_url=request.repository_url
    )
    
    try:
        # Update job status
        analysis_repo = get_analysis_repository()
        await analysis_repo.update_repository_analysis_job(
            job_id=job_id,
            status="processing"
        )
        
        # Process repository analysis
        analysis_results = await detector_manager.analyze_repository(
            repository_url=request.repository_url,
            branch=request.branch,
            include_files=request.include_files,
            exclude_files=request.exclude_files,
            detection_types=request.detection_types,
            confidence_threshold=request.confidence_threshold,
            enable_cross_file_analysis=request.enable_cross_file_analysis,
            max_file_size=request.max_file_size
        )
        
        # Update job with results
        await analysis_repo.update_repository_analysis_job(
            job_id=job_id,
            status="completed",
            results=analysis_results
        )
        
        logger.info(
            "Repository analysis completed",
            job_id=job_id,
            repository_url=request.repository_url,
            patterns_found=len(analysis_results.get("patterns", []))
        )
        
    except Exception as e:
        logger.error(
            "Repository analysis failed",
            job_id=job_id,
            repository_url=request.repository_url,
            error=str(e),
            exc_info=True
        )
        await analysis_repo.update_repository_analysis_job(
            job_id=job_id,
            status="failed",
            error=str(e)
        )