"""
Pattern Detection Routes

Comprehensive API endpoints for pattern detection, batch processing, and streaming operations.
Production-ready with proper error handling, authentication, and monitoring.
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query, Path, Request
from fastapi.responses import StreamingResponse, JSONResponse
from typing import List, Dict, Any, Optional, AsyncGenerator
import asyncio
import json
from uuid import uuid4
from datetime import datetime
import structlog
from contextlib import asynccontextmanager

from ...config.settings import get_settings, Settings
from ...models.patterns import (
    PatternDetectionRequest,
    PatternDetectionResponse,
    PatternAnalysisJob,
    PatternResult,
    DetectedPattern,
    PatternFeedback,
    PatternStatistics,
    PatternType,
    SeverityLevel,
    DetectionType
)
from ...detectors.manager import get_pattern_detector_manager
from ...features.extractor import get_feature_extractor
from ...utils.validation import validate_code_input
from ...database.repositories.pattern_repository import get_pattern_repository
from ...database.repositories.feedback_repository import get_feedback_repository
from ...database.repositories.analysis_repository import get_analysis_repository
from ..middleware.rate_limiting import RateLimitMiddleware
from ..middleware.monitoring import track_request_metrics, track_response_time
from ..utils.cache import get_cache_manager
from ..middleware.error_handling import handle_api_errors

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.post("/detect", response_model=PatternDetectionResponse)
@handle_api_errors
@track_response_time
async def detect_patterns(
    request: PatternDetectionRequest,
    background_tasks: BackgroundTasks,
    req: Request,
    settings: Settings = Depends(get_settings)
) -> PatternDetectionResponse:
    """
    Detect patterns in code using ML models.
    
    This endpoint analyzes code and returns detected patterns with confidence scores,
    recommendations, and comprehensive metadata. Supports various detection types
    including ML inference, heuristic analysis, and hybrid approaches.
    
    Args:
        request: Pattern detection request containing code and parameters
        background_tasks: FastAPI background tasks for async processing
        req: FastAPI request object for context
        settings: Application settings
        
    Returns:
        PatternDetectionResponse: Comprehensive pattern detection results
        
    Raises:
        HTTPException: 400 for invalid input, 429 for rate limiting, 500 for server errors
    """
    start_time = datetime.utcnow()
    job_id = str(uuid4())
    
    # Log request start
    logger.info(
        "Pattern detection request started",
        job_id=job_id,
        language=request.language,
        code_length=len(request.code),
        detection_types=request.detection_types,
        enable_deep_analysis=request.enable_deep_analysis
    )
    
    # Validate input
    if not validate_code_input(request.code):
        logger.warning("Invalid code input", job_id=job_id)
        raise HTTPException(
            status_code=400,
            detail="Invalid code input: Code must be non-empty and contain valid syntax"
        )
    
    # Check cache for similar requests
    cache_manager = get_cache_manager()
    cache_key = f"pattern_detect:{hash(request.code)}:{request.language}:{','.join(request.detection_types)}"
    cached_result = await cache_manager.get(cache_key)
    
    if cached_result and not request.enable_deep_analysis:
        logger.info("Returning cached result", job_id=job_id)
        return PatternDetectionResponse.parse_obj(cached_result)
    
    try:
        # Get detector manager and feature extractor
        detector_manager = get_pattern_detector_manager()
        feature_extractor = get_feature_extractor()
        
        # Extract features with timing
        feature_start = datetime.utcnow()
        features = await feature_extractor.extract_features(
            request.code,
            request.language
        )
        feature_time = (datetime.utcnow() - feature_start).total_seconds()
        
        # Detect patterns with timing
        detection_start = datetime.utcnow()
        patterns = await detector_manager.detect_patterns(
            code=request.code,
            language=request.language,
            features=features,
            detection_types=request.detection_types,
            confidence_threshold=request.confidence_threshold,
            pattern_types=request.pattern_types
        )
        detection_time = (datetime.utcnow() - detection_start).total_seconds()
        
        # Calculate processing time
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        # Create response with comprehensive metadata
        response = PatternDetectionResponse(
            job_id=job_id,
            patterns=patterns,
            processing_time=processing_time,
            metadata={
                "feature_count": len(features),
                "feature_extraction_time": feature_time,
                "detection_time": detection_time,
                "model_versions": detector_manager.get_model_versions(),
                "detection_types_used": request.detection_types,
                "confidence_threshold": request.confidence_threshold,
                "code_metrics": {
                    "lines_of_code": len(request.code.splitlines()),
                    "character_count": len(request.code),
                    "language": request.language
                }
            },
            quality_metrics={
                "average_confidence": sum(p.confidence_score for p in patterns) / len(patterns) if patterns else 0.0,
                "high_confidence_count": sum(1 for p in patterns if p.is_high_confidence),
                "critical_patterns_count": sum(1 for p in patterns if p.is_critical)
            }
        )
        
        # Cache the result for future requests
        await cache_manager.set(cache_key, response.dict(), ttl=3600)  # Cache for 1 hour
        
        # Schedule background analysis if requested
        if request.enable_deep_analysis:
            background_tasks.add_task(
                run_deep_analysis,
                job_id,
                request.code,
                request.language,
                patterns
            )
        
        # Store analysis job for tracking
        analysis_repo = get_analysis_repository()
        await analysis_repo.create_analysis_job(
            job_id=job_id,
            request_data=request.dict(),
            patterns=patterns,
            processing_time=processing_time,
            metadata=response.metadata
        )
        
        # Track metrics
        track_request_metrics("pattern_detection", processing_time, len(patterns))
        
        logger.info(
            "Pattern detection completed successfully",
            job_id=job_id,
            patterns_found=len(patterns),
            processing_time=processing_time,
            critical_patterns=sum(1 for p in patterns if p.is_critical)
        )
        
        return response
        
    except Exception as e:
        logger.error(
            "Pattern detection failed",
            job_id=job_id,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Pattern detection failed: {str(e)}"
        )


@router.post("/batch", response_model=Dict[str, Any])
@handle_api_errors
async def batch_pattern_detection(
    requests: List[PatternDetectionRequest],
    background_tasks: BackgroundTasks,
    req: Request,
    priority: str = Query("normal", regex="^(low|normal|high|urgent)$", description="Batch processing priority"),
    settings: Settings = Depends(get_settings)
) -> Dict[str, Any]:
    """
    Process multiple pattern detection requests in batch.
    
    Efficiently processes multiple pattern detection requests with proper
    queueing, progress tracking, and resource management. Supports different
    priority levels and provides comprehensive batch status information.
    
    Args:
        requests: List of pattern detection requests to process
        background_tasks: FastAPI background tasks for async processing
        req: FastAPI request object for context
        priority: Processing priority (low, normal, high, urgent)
        settings: Application settings
        
    Returns:
        Dict containing batch_id, job_ids, and processing status
        
    Raises:
        HTTPException: 400 for invalid input, 429 for rate limiting, 500 for server errors
    """
    if not requests:
        raise HTTPException(
            status_code=400,
            detail="No requests provided for batch processing"
        )
    
    if len(requests) > settings.max_batch_size:
        raise HTTPException(
            status_code=400,
            detail=f"Batch size {len(requests)} exceeds maximum allowed {settings.max_batch_size}"
        )
    
    batch_id = str(uuid4())
    job_ids = []
    
    logger.info(
        "Starting batch pattern detection",
        batch_id=batch_id,
        request_count=len(requests),
        priority=priority
    )
    
    try:
        # Validate all requests first
        for i, request in enumerate(requests):
            if not validate_code_input(request.code):
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid code input in request {i}"
                )
        
        # Create analysis repository entry for batch
        analysis_repo = get_analysis_repository()
        await analysis_repo.create_batch_job(
            batch_id=batch_id,
            requests=requests,
            priority=priority,
            total_requests=len(requests)
        )
        
        # Schedule individual jobs
        for i, request in enumerate(requests):
            job_id = str(uuid4())
            job_ids.append(job_id)
            
            # Schedule background processing with priority
            background_tasks.add_task(
                process_batch_pattern_detection,
                batch_id,
                job_id,
                request,
                i,
                priority
            )
        
        # Estimate completion time based on batch size and priority
        estimated_completion_minutes = {
            "urgent": len(requests) * 0.5,
            "high": len(requests) * 1.0,
            "normal": len(requests) * 2.0,
            "low": len(requests) * 5.0
        }.get(priority, len(requests) * 2.0)
        
        response = {
            "batch_id": batch_id,
            "job_ids": job_ids,
            "status": "queued",
            "total_requests": len(requests),
            "priority": priority,
            "estimated_completion_minutes": estimated_completion_minutes,
            "created_at": datetime.utcnow().isoformat(),
            "progress_url": f"/api/v1/patterns/batch/{batch_id}/progress"
        }
        
        logger.info(
            "Batch pattern detection queued",
            batch_id=batch_id,
            job_count=len(job_ids),
            priority=priority
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to queue batch pattern detection",
            batch_id=batch_id,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to queue batch processing: {str(e)}"
        )


@router.get("/detect/stream")
@handle_api_errors
async def stream_pattern_detection(
    code: str = Query(..., description="Code to analyze"),
    language: str = Query(..., description="Programming language"),
    detection_types: List[DetectionType] = Query(
        default=[DetectionType.ML_INFERENCE], 
        description="Detection types to use"
    )
) -> StreamingResponse:
    """
    Stream pattern detection results in real-time.
    
    Provides real-time streaming of pattern detection results as they are
    discovered. Useful for long-running analyses or interactive applications.
    
    Args:
        code: Source code to analyze
        language: Programming language of the code
        detection_types: List of detection types to use
        
    Returns:
        StreamingResponse: Server-sent events with pattern detection results
    """
    if not validate_code_input(code):
        raise HTTPException(
            status_code=400,
            detail="Invalid code input"
        )
    
    async def generate_pattern_stream() -> AsyncGenerator[str, None]:
        """Generate streaming pattern detection results."""
        job_id = str(uuid4())
        
        try:
            # Send initial status
            yield f"data: {json.dumps({'event': 'started', 'job_id': job_id})}\n\n"
            
            # Get detector manager
            detector_manager = get_pattern_detector_manager()
            feature_extractor = get_feature_extractor()
            
            # Send feature extraction update
            yield f"data: {json.dumps({'event': 'feature_extraction', 'status': 'started'})}\n\n"
            
            features = await feature_extractor.extract_features(code, language)
            
            yield f"data: {json.dumps({'event': 'feature_extraction', 'status': 'completed', 'feature_count': len(features)})}\n\n"
            
            # Stream pattern detection results
            async for pattern in detector_manager.stream_detect_patterns(
                code=code,
                language=language,
                features=features,
                detection_types=detection_types
            ):
                yield f"data: {json.dumps({'event': 'pattern_detected', 'pattern': pattern.dict()})}\n\n"
            
            # Send completion
            yield f"data: {json.dumps({'event': 'completed', 'job_id': job_id})}\n\n"
            
        except Exception as e:
            logger.error("Streaming pattern detection failed", job_id=job_id, error=str(e))
            yield f"data: {json.dumps({'event': 'error', 'error': str(e)})}\n\n"
    
    return StreamingResponse(
        generate_pattern_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no"
        }
    )


@router.get("/jobs/{job_id}", response_model=PatternAnalysisJob)
@handle_api_errors
async def get_analysis_job(
    job_id: str = Path(..., description="Analysis job identifier"),
    include_results: bool = Query(True, description="Include pattern results in response")
) -> PatternAnalysisJob:
    """
    Get the status and results of a pattern analysis job.
    
    Retrieves comprehensive information about a pattern analysis job including
    status, progress, results, and performance metrics.
    
    Args:
        job_id: Unique identifier for the analysis job
        include_results: Whether to include full pattern results (default: True)
        
    Returns:
        PatternAnalysisJob: Complete job information with status and results
        
    Raises:
        HTTPException: 404 if job not found, 500 for server errors
    """
    logger.info("Retrieving analysis job", job_id=job_id)
    
    try:
        analysis_repo = get_analysis_repository()
        job = await analysis_repo.get_job(job_id)
        
        if not job:
            logger.warning("Analysis job not found", job_id=job_id)
            raise HTTPException(
                status_code=404,
                detail=f"Analysis job {job_id} not found"
            )
        
        # Optionally exclude results for lighter response
        if not include_results:
            job.results = []
        
        logger.info(
            "Analysis job retrieved successfully",
            job_id=job_id,
            status=job.status,
            results_count=len(job.results)
        )
        
        return job
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to retrieve analysis job",
            job_id=job_id,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve job: {str(e)}"
        )


@router.get("/batch/{batch_id}/progress")
@handle_api_errors
async def get_batch_progress(
    batch_id: str = Path(..., description="Batch identifier")
) -> Dict[str, Any]:
    """
    Get progress information for a batch processing job.
    
    Returns comprehensive progress information including completed jobs,
    estimated completion time, and error details.
    
    Args:
        batch_id: Unique identifier for the batch job
        
    Returns:
        Dict containing batch progress information
        
    Raises:
        HTTPException: 404 if batch not found, 500 for server errors
    """
    logger.info("Getting batch progress", batch_id=batch_id)
    
    try:
        analysis_repo = get_analysis_repository()
        batch_info = await analysis_repo.get_batch_progress(batch_id)
        
        if not batch_info:
            raise HTTPException(
                status_code=404,
                detail=f"Batch {batch_id} not found"
            )
        
        return batch_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to get batch progress",
            batch_id=batch_id,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get batch progress: {str(e)}"
        )


@router.get("/{pattern_id}", response_model=DetectedPattern)
@handle_api_errors
async def get_pattern_details(
    pattern_id: str = Path(..., description="Pattern identifier")
) -> DetectedPattern:
    """
    Get detailed information about a specific pattern.
    
    Retrieves comprehensive information about a detected pattern including
    location, confidence metrics, explanations, and recommendations.
    
    Args:
        pattern_id: Unique identifier for the pattern
        
    Returns:
        DetectedPattern: Complete pattern information
        
    Raises:
        HTTPException: 404 if pattern not found, 500 for server errors
    """
    logger.info("Getting pattern details", pattern_id=pattern_id)
    
    try:
        pattern_repo = get_pattern_repository()
        pattern = await pattern_repo.get_pattern(pattern_id)
        
        if not pattern:
            raise HTTPException(
                status_code=404,
                detail=f"Pattern {pattern_id} not found"
            )
        
        logger.info("Pattern details retrieved", pattern_id=pattern_id)
        return pattern
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to get pattern details",
            pattern_id=pattern_id,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get pattern details: {str(e)}"
        )


@router.get("/similar/{pattern_id}", response_model=List[DetectedPattern])
@handle_api_errors
async def find_similar_patterns(
    pattern_id: str = Path(..., description="Pattern identifier to find similar patterns for"),
    limit: int = Query(10, ge=1, le=100, description="Maximum number of similar patterns to return"),
    similarity_threshold: float = Query(0.8, ge=0.0, le=1.0, description="Minimum similarity threshold")
) -> List[DetectedPattern]:
    """
    Find similar patterns using vector similarity search.
    
    Uses advanced vector embeddings and similarity search to find patterns
    that are similar to the specified pattern. Useful for discovering
    related code patterns and anti-patterns.
    
    Args:
        pattern_id: Unique identifier of the pattern to find similarities for
        limit: Maximum number of similar patterns to return (1-100)
        similarity_threshold: Minimum similarity score (0.0-1.0)
        
    Returns:
        List[DetectedPattern]: List of similar patterns with similarity scores
        
    Raises:
        HTTPException: 404 if pattern not found, 500 for server errors
    """
    logger.info(
        "Finding similar patterns",
        pattern_id=pattern_id,
        limit=limit,
        similarity_threshold=similarity_threshold
    )
    
    try:
        pattern_repo = get_pattern_repository()
        
        # Get the base pattern
        base_pattern = await pattern_repo.get_pattern(pattern_id)
        if not base_pattern:
            logger.warning("Pattern not found", pattern_id=pattern_id)
            raise HTTPException(
                status_code=404,
                detail=f"Pattern {pattern_id} not found"
            )
        
        # Find similar patterns using vector similarity
        similar_patterns = await pattern_repo.find_similar_patterns(
            pattern_id=pattern_id,
            limit=limit,
            similarity_threshold=similarity_threshold
        )
        
        logger.info(
            "Similar patterns found",
            pattern_id=pattern_id,
            similar_count=len(similar_patterns)
        )
        
        return similar_patterns
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to find similar patterns",
            pattern_id=pattern_id,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to find similar patterns: {str(e)}"
        )


@router.post("/feedback", response_model=Dict[str, str])
@handle_api_errors
async def submit_pattern_feedback(
    feedback: PatternFeedback,
    req: Request
) -> Dict[str, str]:
    """
    Submit feedback for a detected pattern.
    
    Allows users to provide feedback on pattern detection accuracy,
    which is used to improve the ML models and detection algorithms.
    
    Args:
        feedback: Pattern feedback containing validation and comments
        req: FastAPI request object for context
        
    Returns:
        Dict containing success message and feedback ID
        
    Raises:
        HTTPException: 400 for invalid input, 500 for server errors
    """
    feedback_id = str(uuid4())
    
    logger.info(
        "Submitting pattern feedback",
        feedback_id=feedback_id,
        pattern_id=feedback.pattern_id,
        user_id=feedback.user_id,
        is_correct=feedback.is_correct
    )
    
    try:
        feedback_repo = get_feedback_repository()
        await feedback_repo.create_feedback(
            feedback_id=feedback_id,
            feedback=feedback
        )
        
        logger.info("Pattern feedback submitted", feedback_id=feedback_id)
        
        return {
            "message": "Feedback submitted successfully",
            "feedback_id": feedback_id
        }
        
    except Exception as e:
        logger.error(
            "Failed to submit pattern feedback",
            feedback_id=feedback_id,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to submit feedback: {str(e)}"
        )


@router.get("/statistics", response_model=PatternStatistics)
@handle_api_errors
async def get_pattern_statistics(
    time_range: str = Query("24h", regex="^(1h|24h|7d|30d)$", description="Time range for statistics"),
    language: Optional[str] = Query(None, description="Filter by programming language"),
    pattern_type: Optional[PatternType] = Query(None, description="Filter by pattern type")
) -> PatternStatistics:
    """
    Get comprehensive pattern detection statistics.
    
    Returns aggregated statistics about pattern detection including
    counts by type, severity, confidence levels, and performance metrics.
    
    Args:
        time_range: Time range for statistics (1h, 24h, 7d, 30d)
        language: Optional language filter
        pattern_type: Optional pattern type filter
        
    Returns:
        PatternStatistics: Comprehensive statistics about pattern detection
    """
    logger.info(
        "Getting pattern statistics",
        time_range=time_range,
        language=language,
        pattern_type=pattern_type
    )
    
    try:
        pattern_repo = get_pattern_repository()
        statistics = await pattern_repo.get_statistics(
            time_range=time_range,
            language=language,
            pattern_type=pattern_type
        )
        
        return statistics
        
    except Exception as e:
        logger.error(
            "Failed to get pattern statistics",
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get statistics: {str(e)}"
        )


# Background task implementations

async def run_deep_analysis(
    job_id: str, 
    code: str, 
    language: str,
    initial_patterns: List[DetectedPattern]
) -> None:
    """
    Background task for deep pattern analysis.
    
    Performs comprehensive analysis including cross-references,
    pattern relationships, and advanced ML inference.
    
    Args:
        job_id: Analysis job identifier
        code: Source code to analyze
        language: Programming language
        initial_patterns: Patterns found in initial analysis
    """
    logger.info("Starting deep analysis", job_id=job_id)
    
    try:
        # Update job status
        analysis_repo = get_analysis_repository()
        await analysis_repo.update_job_status(job_id, "deep_analysis_running")
        
        # Perform deep analysis
        detector_manager = get_pattern_detector_manager()
        deep_patterns = await detector_manager.run_deep_analysis(
            code=code,
            language=language,
            initial_patterns=initial_patterns
        )
        
        # Update job with deep analysis results
        await analysis_repo.update_job_results(job_id, deep_patterns)
        await analysis_repo.update_job_status(job_id, "deep_analysis_completed")
        
        logger.info(
            "Deep analysis completed",
            job_id=job_id,
            deep_patterns_count=len(deep_patterns)
        )
        
    except Exception as e:
        logger.error(
            "Deep analysis failed",
            job_id=job_id,
            error=str(e),
            exc_info=True
        )
        await analysis_repo.update_job_status(job_id, "deep_analysis_failed")


async def process_batch_pattern_detection(
    batch_id: str,
    job_id: str,
    request: PatternDetectionRequest,
    index: int,
    priority: str
) -> None:
    """
    Background task for batch pattern detection processing.
    
    Processes individual pattern detection requests within a batch,
    providing progress updates and handling errors gracefully.
    
    Args:
        batch_id: Batch identifier
        job_id: Individual job identifier
        request: Pattern detection request
        index: Request index within batch
        priority: Processing priority
    """
    logger.info(
        "Processing batch pattern detection",
        batch_id=batch_id,
        job_id=job_id,
        index=index,
        priority=priority
    )
    
    try:
        # Update job status
        analysis_repo = get_analysis_repository()
        await analysis_repo.update_batch_job_status(batch_id, job_id, "processing")
        
        # Process the pattern detection request
        detector_manager = get_pattern_detector_manager()
        feature_extractor = get_feature_extractor()
        
        # Extract features
        features = await feature_extractor.extract_features(
            request.code,
            request.language
        )
        
        # Detect patterns
        patterns = await detector_manager.detect_patterns(
            code=request.code,
            language=request.language,
            features=features,
            detection_types=request.detection_types,
            confidence_threshold=request.confidence_threshold
        )
        
        # Update batch job with results
        await analysis_repo.update_batch_job_results(batch_id, job_id, patterns)
        await analysis_repo.update_batch_job_status(batch_id, job_id, "completed")
        
        logger.info(
            "Batch pattern detection completed",
            batch_id=batch_id,
            job_id=job_id,
            patterns_found=len(patterns)
        )
        
    except Exception as e:
        logger.error(
            "Batch pattern detection failed",
            batch_id=batch_id,
            job_id=job_id,
            error=str(e),
            exc_info=True
        )
        await analysis_repo.update_batch_job_status(batch_id, job_id, "failed")
        await analysis_repo.update_batch_job_error(batch_id, job_id, str(e))