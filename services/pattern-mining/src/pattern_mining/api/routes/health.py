"""
Health Check Routes

Comprehensive health and readiness endpoints for the pattern mining service.
"""

from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any
import asyncio
from datetime import datetime
import psutil
import time

from ...config.settings import get_settings, Settings
from ...database.connection import get_database_status
from ...models.health import HealthResponse, HealthStatus
from ..utils.cache import get_redis_client
from ...ml.manager import get_ml_manager

router = APIRouter()


@router.get("/", response_model=HealthResponse)
async def health_check() -> HealthResponse:
    """Basic health check endpoint."""
    return HealthResponse(
        status=HealthStatus.HEALTHY,
        timestamp=datetime.utcnow(),
        service="pattern-mining",
        version="1.0.0"
    )


@router.get("/live")
async def liveness_check() -> Dict[str, Any]:
    """Liveness check endpoint - basic health status."""
    return {
        "status": "alive",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "pattern-mining",
        "version": "1.0.0"
    }


@router.get("/ready", response_model=Dict[str, Any])
async def readiness_check(settings: Settings = Depends(get_settings)) -> Dict[str, Any]:
    """Comprehensive readiness check with dependency validation."""
    checks = {}
    overall_status = HealthStatus.HEALTHY
    check_start_time = time.time()
    
    # Check database connection
    try:
        db_start = time.time()
        db_status = await get_database_status()
        db_duration = time.time() - db_start
        
        checks["database"] = {
            "status": "healthy" if db_status else "unhealthy",
            "details": "Database connection successful" if db_status else "Database connection failed",
            "response_time": f"{db_duration:.3f}s",
            "last_checked": datetime.utcnow().isoformat()
        }
        if not db_status:
            overall_status = HealthStatus.UNHEALTHY
    except Exception as e:
        checks["database"] = {
            "status": "unhealthy",
            "details": f"Database check failed: {str(e)}",
            "last_checked": datetime.utcnow().isoformat()
        }
        overall_status = HealthStatus.UNHEALTHY
    
    # Check Redis connection
    try:
        redis_start = time.time()
        redis_client = get_redis_client()
        await redis_client.ping()
        redis_duration = time.time() - redis_start
        
        checks["redis"] = {
            "status": "healthy",
            "details": "Redis connection successful",
            "response_time": f"{redis_duration:.3f}s",
            "last_checked": datetime.utcnow().isoformat()
        }
    except Exception as e:
        checks["redis"] = {
            "status": "unhealthy",
            "details": f"Redis check failed: {str(e)}",
            "last_checked": datetime.utcnow().isoformat()
        }
        # Redis is not critical for basic functionality
        if overall_status == HealthStatus.HEALTHY:
            overall_status = HealthStatus.DEGRADED
    
    # Check ML models availability
    try:
        ml_manager = get_ml_manager()
        if ml_manager:
            models = await ml_manager.get_available_models()
            
            checks["ml_models"] = {
                "status": "healthy" if models else "unhealthy",
                "details": f"Found {len(models)} available models" if models else "No models available",
                "models": list(models.keys()) if models else [],
                "loaded_models": ml_manager.loaded_models if hasattr(ml_manager, 'loaded_models') else [],
                "memory_usage": ml_manager.memory_usage if hasattr(ml_manager, 'memory_usage') else {},
                "last_checked": datetime.utcnow().isoformat()
            }
            
            if not models:
                overall_status = HealthStatus.UNHEALTHY
        else:
            checks["ml_models"] = {
                "status": "unhealthy",
                "details": "ML manager not initialized",
                "last_checked": datetime.utcnow().isoformat()
            }
            overall_status = HealthStatus.UNHEALTHY
    except Exception as e:
        checks["ml_models"] = {
            "status": "unhealthy",
            "details": f"ML models check failed: {str(e)}",
            "last_checked": datetime.utcnow().isoformat()
        }
        overall_status = HealthStatus.UNHEALTHY
    
    # Check system resources
    try:
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        cpu_percent = psutil.cpu_percent(interval=0.1)
        
        # Define thresholds
        memory_threshold = 85.0  # 85% memory usage
        disk_threshold = 90.0    # 90% disk usage
        cpu_threshold = 80.0     # 80% CPU usage
        
        memory_status = "healthy" if memory.percent < memory_threshold else "unhealthy"
        disk_status = "healthy" if (disk.used / disk.total * 100) < disk_threshold else "unhealthy"
        cpu_status = "healthy" if cpu_percent < cpu_threshold else "unhealthy"
        
        checks["system_resources"] = {
            "status": "healthy" if all(s == "healthy" for s in [memory_status, disk_status, cpu_status]) else "unhealthy",
            "memory": {
                "status": memory_status,
                "usage_percent": memory.percent,
                "available_gb": memory.available / (1024**3),
                "total_gb": memory.total / (1024**3)
            },
            "disk": {
                "status": disk_status,
                "usage_percent": disk.used / disk.total * 100,
                "free_gb": disk.free / (1024**3),
                "total_gb": disk.total / (1024**3)
            },
            "cpu": {
                "status": cpu_status,
                "usage_percent": cpu_percent,
                "cores": psutil.cpu_count()
            },
            "last_checked": datetime.utcnow().isoformat()
        }
        
        if checks["system_resources"]["status"] == "unhealthy":
            if overall_status == HealthStatus.HEALTHY:
                overall_status = HealthStatus.DEGRADED
    except Exception as e:
        checks["system_resources"] = {
            "status": "unhealthy",
            "details": f"System resources check failed: {str(e)}",
            "last_checked": datetime.utcnow().isoformat()
        }
    
    # Check BigQuery connectivity (if configured)
    if settings.bigquery_dataset:
        try:
            from google.cloud import bigquery
            client = bigquery.Client(project=settings.gcp_project_id)
            
            # Simple query to test connectivity
            query = "SELECT 1 as test_value"
            query_job = client.query(query)
            results = list(query_job.result())
            
            checks["bigquery"] = {
                "status": "healthy" if results else "unhealthy",
                "details": "BigQuery connection successful" if results else "BigQuery query failed",
                "project_id": settings.gcp_project_id,
                "dataset": settings.bigquery_dataset,
                "last_checked": datetime.utcnow().isoformat()
            }
        except Exception as e:
            checks["bigquery"] = {
                "status": "unhealthy",
                "details": f"BigQuery check failed: {str(e)}",
                "last_checked": datetime.utcnow().isoformat()
            }
            # BigQuery is not critical for basic functionality
            if overall_status == HealthStatus.HEALTHY:
                overall_status = HealthStatus.DEGRADED
    
    total_check_time = time.time() - check_start_time
    
    if overall_status == HealthStatus.UNHEALTHY:
        raise HTTPException(status_code=503, detail="Service not ready")
    
    return {
        "status": overall_status.value,
        "timestamp": datetime.utcnow().isoformat(),
        "checks": checks,
        "total_check_time": f"{total_check_time:.3f}s",
        "version": settings.app_version,
        "environment": settings.environment,
        "service": "pattern-mining",
        "uptime": time.time() - (getattr(request.app.state, 'startup_time', datetime.utcnow()).timestamp() if hasattr(request.app.state, 'startup_time') else time.time()),
        "summary": {
            "total_checks": len(checks),
            "healthy_checks": len([c for c in checks.values() if c.get("status") == "healthy"]),
            "unhealthy_checks": len([c for c in checks.values() if c.get("status") == "unhealthy"]),
            "degraded_checks": len([c for c in checks.values() if c.get("status") == "degraded"]),
        }
    }


@router.get("/startup")
async def startup_check() -> Dict[str, Any]:
    """Startup check endpoint - indicates if service is ready to accept traffic."""
    # This would check if all initialization is complete
    return {
        "status": "ready",
        "timestamp": datetime.utcnow().isoformat(),
        "message": "Service startup complete"
    }


@router.get("/stats")
async def service_stats() -> Dict[str, Any]:
    """Detailed service statistics."""
    uptime_seconds = time.time() - psutil.boot_time()
    
    # Get ML manager stats
    ml_stats = {}
    try:
        ml_manager = get_ml_manager()
        if ml_manager:
            ml_stats = {
                "loaded_models": ml_manager.loaded_models if hasattr(ml_manager, 'loaded_models') else [],
                "memory_usage": ml_manager.memory_usage if hasattr(ml_manager, 'memory_usage') else {},
            }
    except Exception as e:
        ml_stats = {"error": str(e)}
    
    return {
        "uptime_seconds": uptime_seconds,
        "memory_usage": dict(psutil.virtual_memory()._asdict()),
        "cpu_usage": psutil.cpu_percent(interval=0.1),
        "disk_usage": dict(psutil.disk_usage('/')._asdict()),
        "active_connections": len(psutil.net_connections()),
        "process_info": {
            "pid": psutil.Process().pid,
            "cpu_percent": psutil.Process().cpu_percent(),
            "memory_percent": psutil.Process().memory_percent(),
            "num_threads": psutil.Process().num_threads(),
            "create_time": psutil.Process().create_time(),
        },
        "ml_stats": ml_stats,
        "timestamp": datetime.utcnow().isoformat()
    }