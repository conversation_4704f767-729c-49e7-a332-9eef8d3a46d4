"""
Analytics Routes

API endpoints for pattern analytics, trends, performance metrics, and quality analysis.
Provides comprehensive insights into pattern detection performance and code quality trends.
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Path, Request
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import structlog

from ...config.settings import get_settings, Settings
from ...models.patterns import (
    PatternType,
    SeverityLevel,
    PatternStatistics,
    DetectionType
)
from ...models.api import (
    AnalyticsReport,
    TrendAnalysis,
    PerformanceMetrics,
    QualityMetrics,
    PatternTrends,
    AnalyticsQuery
)
from ...database.repositories.pattern_repository import get_pattern_repository
from ...database.repositories.analysis_repository import get_analysis_repository
from ..middleware.error_handling import handle_api_errors
from ..middleware.monitoring import track_response_time
from ..utils.cache import get_cache_manager

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.get("/patterns", response_model=AnalyticsReport)
@handle_api_errors
@track_response_time
async def get_pattern_analytics(
    time_range: str = Query("7d", regex="^(1h|24h|7d|30d|90d)$", description="Time range for analytics"),
    pattern_type: Optional[PatternType] = Query(None, description="Filter by pattern type"),
    language: Optional[str] = Query(None, description="Filter by programming language"),
    severity: Optional[SeverityLevel] = Query(None, description="Filter by severity level"),
    granularity: str = Query("day", regex="^(hour|day|week|month)$", description="Time granularity"),
    include_predictions: bool = Query(False, description="Include trend predictions"),
    settings: Settings = Depends(get_settings)
) -> AnalyticsReport:
    """
    Get comprehensive pattern analytics.
    
    Provides detailed analytics about pattern detection including counts,
    trends, distribution, and performance metrics over time.
    
    Args:
        time_range: Time range for analytics (1h, 24h, 7d, 30d, 90d)
        pattern_type: Optional pattern type filter
        language: Optional programming language filter
        severity: Optional severity level filter
        granularity: Time granularity (hour, day, week, month)
        include_predictions: Whether to include trend predictions
        settings: Application settings
        
    Returns:
        AnalyticsReport: Comprehensive pattern analytics report
        
    Raises:
        HTTPException: 400 for invalid parameters, 500 for server errors
    """
    logger.info(
        "Getting pattern analytics",
        time_range=time_range,
        pattern_type=pattern_type,
        language=language,
        severity=severity,
        granularity=granularity,
        include_predictions=include_predictions
    )
    
    try:
        # Check cache first
        cache_manager = get_cache_manager()
        cache_key = f"analytics:patterns:{time_range}:{pattern_type}:{language}:{severity}:{granularity}:{include_predictions}"
        cached_result = await cache_manager.get(cache_key)
        
        if cached_result:
            logger.info("Returning cached analytics result")
            return AnalyticsReport.parse_obj(cached_result)
        
        # Get analytics repository
        pattern_repo = get_pattern_repository()
        analysis_repo = get_analysis_repository()
        
        # Calculate time boundaries
        end_time = datetime.utcnow()
        time_deltas = {
            "1h": timedelta(hours=1),
            "24h": timedelta(hours=24),
            "7d": timedelta(days=7),
            "30d": timedelta(days=30),
            "90d": timedelta(days=90)
        }
        start_time = end_time - time_deltas[time_range]
        
        # Get pattern statistics
        pattern_stats = await pattern_repo.get_analytics_statistics(
            start_time=start_time,
            end_time=end_time,
            pattern_type=pattern_type,
            language=language,
            severity=severity,
            granularity=granularity
        )
        
        # Get trend analysis
        trend_analysis = await pattern_repo.get_trend_analysis(
            start_time=start_time,
            end_time=end_time,
            pattern_type=pattern_type,
            language=language,
            severity=severity,
            granularity=granularity,
            include_predictions=include_predictions
        )
        
        # Get performance metrics
        performance_metrics = await analysis_repo.get_performance_metrics(
            start_time=start_time,
            end_time=end_time,
            granularity=granularity
        )
        
        # Get quality metrics
        quality_metrics = await pattern_repo.get_quality_metrics(
            start_time=start_time,
            end_time=end_time,
            pattern_type=pattern_type,
            language=language
        )
        
        # Create comprehensive analytics report
        analytics_report = AnalyticsReport(
            time_range=time_range,
            start_time=start_time,
            end_time=end_time,
            granularity=granularity,
            filters={
                "pattern_type": pattern_type,
                "language": language,
                "severity": severity
            },
            pattern_statistics=pattern_stats,
            trend_analysis=trend_analysis,
            performance_metrics=performance_metrics,
            quality_metrics=quality_metrics,
            insights=await _generate_insights(
                pattern_stats, trend_analysis, performance_metrics, quality_metrics
            ),
            metadata={
                "generated_at": datetime.utcnow().isoformat(),
                "cache_ttl": 300,  # 5 minutes
                "data_freshness": "realtime"
            }
        )
        
        # Cache the result
        await cache_manager.set(cache_key, analytics_report.dict(), ttl=300)
        
        logger.info(
            "Pattern analytics generated successfully",
            time_range=time_range,
            total_patterns=pattern_stats.total_patterns if pattern_stats else 0
        )
        
        return analytics_report
        
    except Exception as e:
        logger.error(
            "Failed to get pattern analytics",
            time_range=time_range,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get pattern analytics: {str(e)}"
        )


@router.get("/trends", response_model=TrendAnalysis)
@handle_api_errors
async def get_pattern_trends(
    metric: str = Query("pattern_count", regex="^(pattern_count|severity_distribution|confidence_trend|detection_rate)$"),
    time_range: str = Query("30d", regex="^(7d|30d|90d|1y)$", description="Time range for trend analysis"),
    pattern_type: Optional[PatternType] = Query(None, description="Filter by pattern type"),
    language: Optional[str] = Query(None, description="Filter by programming language"),
    smoothing: str = Query("none", regex="^(none|moving_average|exponential)$", description="Trend smoothing method"),
    forecast_days: int = Query(0, ge=0, le=30, description="Number of days to forecast")
) -> TrendAnalysis:
    """
    Get pattern trend analysis.
    
    Analyzes trends in pattern detection over time including growth rates,
    seasonal patterns, and optional forecasting.
    
    Args:
        metric: Metric to analyze trends for
        time_range: Time range for trend analysis
        pattern_type: Optional pattern type filter
        language: Optional programming language filter
        smoothing: Trend smoothing method
        forecast_days: Number of days to forecast (0-30)
        
    Returns:
        TrendAnalysis: Comprehensive trend analysis results
        
    Raises:
        HTTPException: 400 for invalid parameters, 500 for server errors
    """
    logger.info(
        "Getting pattern trends",
        metric=metric,
        time_range=time_range,
        pattern_type=pattern_type,
        language=language,
        smoothing=smoothing,
        forecast_days=forecast_days
    )
    
    try:
        pattern_repo = get_pattern_repository()
        
        # Calculate time boundaries
        end_time = datetime.utcnow()
        time_deltas = {
            "7d": timedelta(days=7),
            "30d": timedelta(days=30),
            "90d": timedelta(days=90),
            "1y": timedelta(days=365)
        }
        start_time = end_time - time_deltas[time_range]
        
        # Get trend analysis
        trend_analysis = await pattern_repo.get_detailed_trend_analysis(
            metric=metric,
            start_time=start_time,
            end_time=end_time,
            pattern_type=pattern_type,
            language=language,
            smoothing=smoothing,
            forecast_days=forecast_days
        )
        
        logger.info(
            "Pattern trends retrieved successfully",
            metric=metric,
            time_range=time_range,
            data_points=len(trend_analysis.data_points) if trend_analysis.data_points else 0
        )
        
        return trend_analysis
        
    except Exception as e:
        logger.error(
            "Failed to get pattern trends",
            metric=metric,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get pattern trends: {str(e)}"
        )


@router.get("/performance", response_model=PerformanceMetrics)
@handle_api_errors
async def get_performance_metrics(
    time_range: str = Query("24h", regex="^(1h|24h|7d|30d)$", description="Time range for performance metrics"),
    metric_type: str = Query("all", regex="^(all|latency|throughput|error_rate|resource_usage)$"),
    granularity: str = Query("hour", regex="^(minute|hour|day)$", description="Time granularity"),
    detection_type: Optional[DetectionType] = Query(None, description="Filter by detection type"),
    percentiles: List[float] = Query(default=[50, 95, 99], description="Percentiles to calculate")
) -> PerformanceMetrics:
    """
    Get performance metrics for pattern detection.
    
    Provides comprehensive performance analytics including latency,
    throughput, error rates, and resource usage metrics.
    
    Args:
        time_range: Time range for metrics
        metric_type: Type of metrics to return
        granularity: Time granularity for metrics
        detection_type: Optional detection type filter
        percentiles: Percentiles to calculate for latency metrics
        
    Returns:
        PerformanceMetrics: Comprehensive performance metrics
        
    Raises:
        HTTPException: 400 for invalid parameters, 500 for server errors
    """
    logger.info(
        "Getting performance metrics",
        time_range=time_range,
        metric_type=metric_type,
        granularity=granularity,
        detection_type=detection_type,
        percentiles=percentiles
    )
    
    try:
        analysis_repo = get_analysis_repository()
        
        # Calculate time boundaries
        end_time = datetime.utcnow()
        time_deltas = {
            "1h": timedelta(hours=1),
            "24h": timedelta(hours=24),
            "7d": timedelta(days=7),
            "30d": timedelta(days=30)
        }
        start_time = end_time - time_deltas[time_range]
        
        # Get performance metrics
        metrics = await analysis_repo.get_detailed_performance_metrics(
            start_time=start_time,
            end_time=end_time,
            metric_type=metric_type,
            granularity=granularity,
            detection_type=detection_type,
            percentiles=percentiles
        )
        
        logger.info(
            "Performance metrics retrieved successfully",
            time_range=time_range,
            metric_type=metric_type,
            data_points=len(metrics.latency_metrics) if metrics.latency_metrics else 0
        )
        
        return metrics
        
    except Exception as e:
        logger.error(
            "Failed to get performance metrics",
            time_range=time_range,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get performance metrics: {str(e)}"
        )


@router.get("/quality", response_model=QualityMetrics)
@handle_api_errors
async def get_quality_metrics(
    time_range: str = Query("7d", regex="^(24h|7d|30d)$", description="Time range for quality metrics"),
    repository_id: Optional[str] = Query(None, description="Filter by repository ID"),
    language: Optional[str] = Query(None, description="Filter by programming language"),
    include_benchmarks: bool = Query(True, description="Include industry benchmarks"),
    include_recommendations: bool = Query(True, description="Include quality recommendations")
) -> QualityMetrics:
    """
    Get code quality metrics based on pattern analysis.
    
    Provides comprehensive quality metrics including maintainability scores,
    technical debt indicators, and quality trends over time.
    
    Args:
        time_range: Time range for quality metrics
        repository_id: Optional repository filter
        language: Optional programming language filter
        include_benchmarks: Whether to include industry benchmarks
        include_recommendations: Whether to include quality recommendations
        
    Returns:
        QualityMetrics: Comprehensive quality metrics and analysis
        
    Raises:
        HTTPException: 400 for invalid parameters, 500 for server errors
    """
    logger.info(
        "Getting quality metrics",
        time_range=time_range,
        repository_id=repository_id,
        language=language,
        include_benchmarks=include_benchmarks,
        include_recommendations=include_recommendations
    )
    
    try:
        pattern_repo = get_pattern_repository()
        
        # Calculate time boundaries
        end_time = datetime.utcnow()
        time_deltas = {
            "24h": timedelta(hours=24),
            "7d": timedelta(days=7),
            "30d": timedelta(days=30)
        }
        start_time = end_time - time_deltas[time_range]
        
        # Get quality metrics
        quality_metrics = await pattern_repo.get_comprehensive_quality_metrics(
            start_time=start_time,
            end_time=end_time,
            repository_id=repository_id,
            language=language,
            include_benchmarks=include_benchmarks,
            include_recommendations=include_recommendations
        )
        
        logger.info(
            "Quality metrics retrieved successfully",
            time_range=time_range,
            repository_id=repository_id,
            overall_score=quality_metrics.overall_quality_score if quality_metrics else 0.0
        )
        
        return quality_metrics
        
    except Exception as e:
        logger.error(
            "Failed to get quality metrics",
            time_range=time_range,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get quality metrics: {str(e)}"
        )


@router.get("/dashboard", response_model=Dict[str, Any])
@handle_api_errors
async def get_analytics_dashboard(
    time_range: str = Query("7d", regex="^(24h|7d|30d)$", description="Time range for dashboard"),
    user_id: Optional[str] = Query(None, description="User ID for personalized dashboard"),
    include_alerts: bool = Query(True, description="Include alert notifications"),
    settings: Settings = Depends(get_settings)
) -> Dict[str, Any]:
    """
    Get comprehensive analytics dashboard data.
    
    Provides all necessary data for rendering an analytics dashboard including
    key metrics, trends, alerts, and personalized insights.
    
    Args:
        time_range: Time range for dashboard data
        user_id: Optional user ID for personalization
        include_alerts: Whether to include alert notifications
        settings: Application settings
        
    Returns:
        Dict containing comprehensive dashboard data
        
    Raises:
        HTTPException: 500 for server errors
    """
    logger.info(
        "Getting analytics dashboard",
        time_range=time_range,
        user_id=user_id,
        include_alerts=include_alerts
    )
    
    try:
        # Get multiple analytics components in parallel
        pattern_repo = get_pattern_repository()
        analysis_repo = get_analysis_repository()
        
        # Calculate time boundaries
        end_time = datetime.utcnow()
        time_deltas = {
            "24h": timedelta(hours=24),
            "7d": timedelta(days=7),
            "30d": timedelta(days=30)
        }
        start_time = end_time - time_deltas[time_range]
        
        # Get dashboard components
        dashboard_data = await _get_dashboard_components(
            pattern_repo=pattern_repo,
            analysis_repo=analysis_repo,
            start_time=start_time,
            end_time=end_time,
            user_id=user_id,
            include_alerts=include_alerts
        )
        
        logger.info(
            "Analytics dashboard retrieved successfully",
            time_range=time_range,
            components=list(dashboard_data.keys())
        )
        
        return dashboard_data
        
    except Exception as e:
        logger.error(
            "Failed to get analytics dashboard",
            time_range=time_range,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get analytics dashboard: {str(e)}"
        )


@router.post("/query", response_model=Dict[str, Any])
@handle_api_errors
async def execute_analytics_query(
    query: AnalyticsQuery,
    req: Request,
    settings: Settings = Depends(get_settings)
) -> Dict[str, Any]:
    """
    Execute custom analytics query.
    
    Allows for flexible analytics queries with custom filters,
    aggregations, and time ranges.
    
    Args:
        query: Analytics query specification
        req: FastAPI request object for context
        settings: Application settings
        
    Returns:
        Dict containing query results
        
    Raises:
        HTTPException: 400 for invalid query, 500 for server errors
    """
    logger.info(
        "Executing analytics query",
        query_type=query.query_type,
        filters=query.filters,
        time_range=query.time_range
    )
    
    try:
        pattern_repo = get_pattern_repository()
        
        # Validate query
        if not query.metrics:
            raise HTTPException(
                status_code=400,
                detail="At least one metric must be specified"
            )
        
        # Execute query
        query_results = await pattern_repo.execute_analytics_query(
            query_type=query.query_type,
            metrics=query.metrics,
            filters=query.filters,
            time_range=query.time_range,
            group_by=query.group_by,
            aggregations=query.aggregations,
            limit=query.limit,
            offset=query.offset
        )
        
        logger.info(
            "Analytics query executed successfully",
            query_type=query.query_type,
            results_count=len(query_results.get("data", []))
        )
        
        return query_results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to execute analytics query",
            query_type=query.query_type,
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to execute analytics query: {str(e)}"
        )


@router.get("/alerts", response_model=List[Dict[str, Any]])
@handle_api_errors
async def get_analytics_alerts(
    severity: Optional[str] = Query(None, regex="^(info|warning|error|critical)$", description="Filter by alert severity"),
    status: Optional[str] = Query(None, regex="^(active|resolved|dismissed)$", description="Filter by alert status"),
    limit: int = Query(50, ge=1, le=200, description="Maximum number of alerts to return"),
    offset: int = Query(0, ge=0, description="Number of alerts to skip")
) -> List[Dict[str, Any]]:
    """
    Get analytics alerts and notifications.
    
    Retrieves active alerts about pattern detection anomalies,
    performance issues, and quality degradations.
    
    Args:
        severity: Optional alert severity filter
        status: Optional alert status filter
        limit: Maximum number of alerts to return
        offset: Number of alerts to skip
        
    Returns:
        List of alert objects with details and recommendations
        
    Raises:
        HTTPException: 500 for server errors
    """
    logger.info(
        "Getting analytics alerts",
        severity=severity,
        status=status,
        limit=limit,
        offset=offset
    )
    
    try:
        analysis_repo = get_analysis_repository()
        
        # Get alerts
        alerts = await analysis_repo.get_analytics_alerts(
            severity=severity,
            status=status,
            limit=limit,
            offset=offset
        )
        
        logger.info(
            "Analytics alerts retrieved successfully",
            alerts_count=len(alerts)
        )
        
        return alerts
        
    except Exception as e:
        logger.error(
            "Failed to get analytics alerts",
            error=str(e),
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get analytics alerts: {str(e)}"
        )


# Helper functions

async def _generate_insights(
    pattern_stats: PatternStatistics,
    trend_analysis: TrendAnalysis,
    performance_metrics: PerformanceMetrics,
    quality_metrics: QualityMetrics
) -> List[Dict[str, Any]]:
    """Generate analytical insights from metrics."""
    insights = []
    
    # Pattern insights
    if pattern_stats and pattern_stats.total_patterns > 0:
        critical_ratio = pattern_stats.critical_patterns_ratio
        if critical_ratio > 0.1:
            insights.append({
                "type": "warning",
                "title": "High Critical Pattern Ratio",
                "description": f"Critical patterns account for {critical_ratio:.1%} of all patterns",
                "recommendation": "Review critical patterns and prioritize fixes",
                "impact": "high"
            })
    
    # Trend insights
    if trend_analysis and trend_analysis.growth_rate:
        if trend_analysis.growth_rate > 0.2:
            insights.append({
                "type": "alert",
                "title": "Rapid Pattern Growth",
                "description": f"Pattern detection rate increased by {trend_analysis.growth_rate:.1%}",
                "recommendation": "Investigate causes of pattern increase",
                "impact": "medium"
            })
    
    # Performance insights
    if performance_metrics and performance_metrics.average_response_time:
        if performance_metrics.average_response_time > 5000:  # 5 seconds
            insights.append({
                "type": "performance",
                "title": "High Response Time",
                "description": f"Average response time is {performance_metrics.average_response_time/1000:.1f}s",
                "recommendation": "Optimize detection algorithms or increase resources",
                "impact": "medium"
            })
    
    # Quality insights
    if quality_metrics and quality_metrics.overall_quality_score:
        if quality_metrics.overall_quality_score < 0.6:
            insights.append({
                "type": "quality",
                "title": "Low Code Quality Score",
                "description": f"Overall quality score is {quality_metrics.overall_quality_score:.1%}",
                "recommendation": "Focus on addressing high-severity patterns",
                "impact": "high"
            })
    
    return insights


async def _get_dashboard_components(
    pattern_repo,
    analysis_repo,
    start_time: datetime,
    end_time: datetime,
    user_id: Optional[str],
    include_alerts: bool
) -> Dict[str, Any]:
    """Get all dashboard components."""
    dashboard_data = {}
    
    # Get summary metrics
    summary_metrics = await pattern_repo.get_summary_metrics(
        start_time=start_time,
        end_time=end_time,
        user_id=user_id
    )
    dashboard_data["summary"] = summary_metrics
    
    # Get recent patterns
    recent_patterns = await pattern_repo.get_recent_patterns(
        limit=10,
        user_id=user_id
    )
    dashboard_data["recent_patterns"] = recent_patterns
    
    # Get performance overview
    performance_overview = await analysis_repo.get_performance_overview(
        start_time=start_time,
        end_time=end_time
    )
    dashboard_data["performance"] = performance_overview
    
    # Get quality overview
    quality_overview = await pattern_repo.get_quality_overview(
        start_time=start_time,
        end_time=end_time
    )
    dashboard_data["quality"] = quality_overview
    
    # Get alerts if requested
    if include_alerts:
        alerts = await analysis_repo.get_analytics_alerts(
            status="active",
            limit=5
        )
        dashboard_data["alerts"] = alerts
    
    return dashboard_data