"""
Configuration Management API with Access Control

Provides secure endpoints for configuration parameter management with:
- Role-based access control
- Comprehensive audit logging
- Parameter validation
- Security monitoring
"""

from fastapi import APIRouter, HTTPException, Depends, Request, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import structlog
import redis.asyncio as redis

from ..config.access_control import (
    ConfigAccessController, ConfigPermissionManager, ConfigRole, ConfigAction,
    get_access_controller, get_permission_manager
)
from ..config.validation import get_config_validator, get_config_auditor
from ..config.settings import get_settings
from ..security.auth import get_current_user_role, verify_session

logger = structlog.get_logger()
security = HTTPBearer()
router = APIRouter(prefix="/config", tags=["Configuration Management"])


# Dependency injection
async def get_redis_client() -> redis.Redis:
    """Get Redis client for configuration management."""
    settings = get_settings()
    return redis.from_url(settings.redis_url)


async def get_user_context(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """Extract user context from request."""
    # In production, integrate with your authentication system
    user_id = await get_current_user_id(credentials.credentials)
    user_role = await get_current_user_role(credentials.credentials)
    session_id = await verify_session(credentials.credentials)

    return {
        "user_id": user_id,
        "user_role": user_role,
        "source_ip": request.client.host,
        "user_agent": request.headers.get("user-agent"),
        "session_id": session_id
    }


async def get_current_user_id(token: str) -> str:
    """Get current user ID from authentication token."""
    # TODO: Implement actual authentication
    return "dev-user-001"  # Placeholder for development


@router.get("/parameters", summary="List accessible configuration parameters")
async def list_configuration_parameters(
    user_context: Dict[str, Any] = Depends(get_user_context),
    redis_client: redis.Redis = Depends(get_redis_client)
) -> Dict[str, Any]:
    """
    List configuration parameters accessible to the current user.

    Returns parameters filtered by user's role permissions.
    """
    try:
        permission_manager = get_permission_manager(redis_client)
        user_role = ConfigRole(user_context["user_role"])

        # Get all settings
        settings = get_settings()
        all_params = settings.dict()

        # Filter parameters based on user permissions
        accessible_params = {}
        inaccessible_params = []

        for param_name, param_value in all_params.items():
            has_permission = await permission_manager.has_permission(
                user_id=user_context["user_id"],
                user_role=user_role,
                action=ConfigAction.READ,
                parameter_name=param_name,
                context=user_context
            )

            if has_permission:
                # Redact sensitive values
                from ..config.validation import ParameterClassification, SecurityLevel
                security_level = ParameterClassification.get_security_level(param_name)

                if security_level == SecurityLevel.SECRET:
                    accessible_params[param_name] = "[REDACTED]"
                elif security_level == SecurityLevel.SENSITIVE:
                    accessible_params[param_name] = f"[SENSITIVE: {type(param_value).__name__}]"
                else:
                    accessible_params[param_name] = param_value
            else:
                inaccessible_params.append(param_name)

        return {
            "accessible_parameters": accessible_params,
            "inaccessible_count": len(inaccessible_parameters),
            "total_parameters": len(all_params),
            "user_role": user_role.value,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error("Failed to list configuration parameters", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve configuration parameters"
        )


@router.get("/parameters/{parameter_name}", summary="Get specific configuration parameter")
async def get_configuration_parameter(
    parameter_name: str,
    user_context: Dict[str, Any] = Depends(get_user_context),
    redis_client: redis.Redis = Depends(get_redis_client)
) -> Dict[str, Any]:
    """Get a specific configuration parameter value."""
    try:
        permission_manager = get_permission_manager(redis_client)
        user_role = ConfigRole(user_context["user_role"])

        # Check read permission
        has_permission = await permission_manager.has_permission(
            user_id=user_context["user_id"],
            user_role=user_role,
            action=ConfigAction.READ,
            parameter_name=parameter_name,
            context=user_context
        )

        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied to parameter: {parameter_name}"
            )

        # Get parameter value
        settings = get_settings()
        all_params = settings.dict()

        if parameter_name not in all_params:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Parameter not found: {parameter_name}"
            )

        param_value = all_params[parameter_name]

        # Apply security level redaction
        from ..config.validation import ParameterClassification, SecurityLevel
        security_level = ParameterClassification.get_security_level(parameter_name)

        if security_level == SecurityLevel.SECRET:
            display_value = "[REDACTED]"
        elif security_level == SecurityLevel.SENSITIVE:
            display_value = f"[SENSITIVE: {type(param_value).__name__}]"
        else:
            display_value = param_value

        return {
            "parameter_name": parameter_name,
            "value": display_value,
            "security_level": security_level.value,
            "type": type(param_value).__name__,
            "user_role": user_role.value,
            "timestamp": datetime.utcnow().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get configuration parameter", parameter=parameter_name, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve configuration parameter"
        )


@router.put("/parameters/{parameter_name}", summary="Update configuration parameter")
async def update_configuration_parameter(
    parameter_name: str,
    new_value: Any,
    user_context: Dict[str, Any] = Depends(get_user_context),
    redis_client: redis.Redis = Depends(get_redis_client)
) -> Dict[str, Any]:
    """Update a configuration parameter value (requires appropriate permissions)."""
    try:
        permission_manager = get_permission_manager(redis_client)
        user_role = ConfigRole(user_context["user_role"])

        # Check write permission
        has_permission = await permission_manager.has_permission(
            user_id=user_context["user_id"],
            user_role=user_role,
            action=ConfigAction.WRITE,
            parameter_name=parameter_name,
            context=user_context
        )

        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Write access denied to parameter: {parameter_name}"
            )

        # Validate new value
        validator = get_config_validator()
        violations = validator.validate_parameter(parameter_name, new_value)

        critical_violations = [v for v in violations if v.severity.value == "critical"]
        if critical_violations:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Critical security violations found",
                    "violations": [v.to_dict() for v in critical_violations]
                }
            )

        # Get current value for audit logging
        settings = get_settings()
        all_params = settings.dict()
        old_value = all_params.get(parameter_name)

        # Update parameter (this would need to be implemented based on your configuration system)
        # For now, we'll simulate the update and log it

        # Log the configuration change
        access_controller = get_access_controller(redis_client)
        await access_controller.log_access(
            user_id=user_context["user_id"],
            user_role=user_role,
            action=ConfigAction.WRITE,
            parameter_name=parameter_name,
            success=True,
            old_value=str(old_value) if old_value is not None else None,
            new_value=str(new_value),
            context=user_context
        )

        logger.info(
            "Configuration parameter updated",
            parameter=parameter_name,
            user_id=user_context["user_id"],
            user_role=user_role.value
        )

        return {
            "parameter_name": parameter_name,
            "old_value": "[REDACTED]" if old_value and ParameterClassification.is_secret(parameter_name) else old_value,
            "new_value": "[REDACTED]" if ParameterClassification.is_secret(parameter_name) else new_value,
            "updated_by": user_context["user_id"],
            "updated_at": datetime.utcnow().isoformat(),
            "violations": [v.to_dict() for v in violations if v.severity.value in ["warning", "error"]]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update configuration parameter", parameter=parameter_name, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update configuration parameter"
        )


@router.get("/audit", summary="Get configuration audit log")
async def get_configuration_audit_log(
    user_context: Dict[str, Any] = Depends(get_user_context),
    redis_client: redis.Redis = Depends(get_redis_client),
    parameter_name: Optional[str] = None,
    user_id: Optional[str] = None,
    hours: int = 24,
    limit: int = 100
) -> Dict[str, Any]:
    """
    Get configuration audit log entries.

    Requires AUDIT permission.
    """
    try:
        permission_manager = get_permission_manager(redis_client)
        user_role = ConfigRole(user_context["user_role"])

        # Check audit permission
        has_permission = await permission_manager.has_permission(
            user_id=user_context["user_id"],
            user_role=user_role,
            action=ConfigAction.AUDIT,
            parameter_name="system",  # System-level permission
            context=user_context
        )

        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Audit access denied"
            )

        access_controller = get_access_controller(redis_client)

        # Calculate time range
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=hours)

        # Get audit entries
        audit_entries = await access_controller.get_audit_log(
            user_id=user_id,
            parameter_name=parameter_name,
            start_time=start_time,
            end_time=end_time,
            limit=limit
        )

        return {
            "audit_entries": audit_entries,
            "total_entries": len(audit_entries),
            "time_range": {
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "hours": hours
            },
            "filters": {
                "parameter_name": parameter_name,
                "user_id": user_id
            },
            "retrieved_at": datetime.utcnow().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get audit log", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve audit log"
        )


@router.get("/security/status", summary="Get configuration security status")
async def get_security_status(
    user_context: Dict[str, Any] = Depends(get_user_context),
    redis_client: redis.Redis = Depends(get_redis_client)
) -> Dict[str, Any]:
    """Get comprehensive security status of configuration system."""
    try:
        permission_manager = get_permission_manager(redis_client)
        user_role = ConfigRole(user_context["user_role"])

        # Check if user can view security status
        if user_role not in [ConfigRole.ADMIN, ConfigRole.SECURITY_ADMIN, ConfigRole.MONITOR]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Security status access denied"
            )

        # Get configuration audit
        settings = get_settings()
        config_dict = settings.dict()

        auditor = get_config_auditor()
        audit_report = auditor.audit_configuration(config_dict)

        # Get access summary for current user
        access_controller = get_access_controller(redis_client)
        access_summary = await access_controller.get_access_summary(
            user_context["user_id"],
            time_window_hours=24
        )

        # Check for suspicious activity
        suspicious_activities = await access_controller.detect_suspicious_activity()

        return {
            "security_audit": audit_report,
            "user_access_summary": access_summary,
            "suspicious_activities": suspicious_activities,
            "system_status": {
                "total_parameters": len(config_dict),
                "secret_parameters": sum(1 for p in config_dict.keys() if ParameterClassification.is_secret(p)),
                "sensitive_parameters": sum(1 for p in config_dict.keys() if ParameterClassification.is_sensitive(p)),
                "access_control_enabled": True,
                "audit_logging_enabled": True
            },
            "retrieved_at": datetime.utcnow().isoformat(),
            "retrieved_by": user_context["user_id"]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get security status", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve security status"
        )


@router.post("/security/validate", summary="Validate configuration changes")
async def validate_configuration_changes(
    changes: Dict[str, Any],
    user_context: Dict[str, Any] = Depends(get_user_context),
    redis_client: redis.Redis = Depends(get_redis_client)
) -> Dict[str, Any]:
    """
    Validate proposed configuration changes without applying them.

    Useful for pre-validation before committing changes.
    """
    try:
        permission_manager = get_permission_manager(redis_client)
        user_role = ConfigRole(user_context["user_role"])

        validation_results = {}
        overall_valid = True

        for parameter_name, new_value in changes.items():
            # Check write permission
            has_permission = await permission_manager.has_permission(
                user_id=user_context["user_id"],
                user_role=user_role,
                action=ConfigAction.WRITE,
                parameter_name=parameter_name,
                context=user_context
            )

            if not has_permission:
                validation_results[parameter_name] = {
                    "valid": False,
                    "reason": "Access denied",
                    "violations": []
                }
                overall_valid = False
                continue

            # Validate parameter
            validator = get_config_validator()
            violations = validator.validate_parameter(parameter_name, new_value)

            critical_violations = [v for v in violations if v.severity.value == "critical"]
            has_critical = len(critical_violations) > 0

            validation_results[parameter_name] = {
                "valid": not has_critical,
                "violations": [v.to_dict() for v in violations],
                "critical_violations": len(critical_violations),
                "warning_violations": len([v for v in violations if v.severity.value == "warning"])
            }

            if has_critical:
                overall_valid = False

        return {
            "overall_valid": overall_valid,
            "parameter_results": validation_results,
            "total_parameters": len(changes),
            "validated_at": datetime.utcnow().isoformat(),
            "validated_by": user_context["user_id"]
        }

    except Exception as e:
        logger.error("Failed to validate configuration changes", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate configuration changes"
        )


# Import these in __init__.py to register the routes
from ..config.validation import ParameterClassification
