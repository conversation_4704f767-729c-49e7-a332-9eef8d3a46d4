"""
Monitoring Middleware

Prometheus metrics collection and performance monitoring.
"""

import time
from typing import Dict, Any, Optional
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry, generate_latest
import structlog
import psutil
import asyncio

logger = structlog.get_logger()

# Prometheus metrics
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status_code', 'service']
)

REQUEST_DURATION = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration',
    ['method', 'endpoint', 'service'],
    buckets=[0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1.0, 2.5, 5.0, 7.5, 10.0]
)

REQUEST_SIZE = Histogram(
    'http_request_size_bytes',
    'HTTP request size',
    ['method', 'endpoint', 'service']
)

RESPONSE_SIZE = Histogram(
    'http_response_size_bytes',
    'HTTP response size',
    ['method', 'endpoint', 'service']
)

ACTIVE_REQUESTS = Gauge(
    'http_active_requests',
    'Number of active HTTP requests',
    ['service']
)

PATTERN_DETECTION_COUNT = Counter(
    'pattern_detection_total',
    'Total pattern detection requests',
    ['pattern_type', 'language', 'status']
)

PATTERN_DETECTION_DURATION = Histogram(
    'pattern_detection_duration_seconds',
    'Pattern detection duration',
    ['pattern_type', 'language'],
    buckets=[0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0, 25.0, 50.0, 100.0]
)

ML_MODEL_INFERENCE_COUNT = Counter(
    'ml_model_inference_total',
    'Total ML model inference requests',
    ['model_id', 'status']
)

ML_MODEL_INFERENCE_DURATION = Histogram(
    'ml_model_inference_duration_seconds',
    'ML model inference duration',
    ['model_id'],
    buckets=[0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0]
)

# System metrics
MEMORY_USAGE = Gauge(
    'memory_usage_bytes',
    'Memory usage in bytes',
    ['type']
)

CPU_USAGE = Gauge(
    'cpu_usage_percent',
    'CPU usage percentage'
)

DISK_USAGE = Gauge(
    'disk_usage_bytes',
    'Disk usage in bytes',
    ['type']
)


class PrometheusMiddleware(BaseHTTPMiddleware):
    """Middleware for collecting Prometheus metrics."""
    
    def __init__(self, app, service_name: str = "pattern-mining"):
        super().__init__(app)
        self.service_name = service_name
        
        # Start system metrics collection
        asyncio.create_task(self._collect_system_metrics())
    
    async def dispatch(self, request: Request, call_next) -> Response:
        # Skip metrics collection for metrics endpoint
        if request.url.path == "/metrics":
            return await call_next(request)
        
        # Get endpoint pattern (remove dynamic parts)
        endpoint = self._get_endpoint_pattern(request.url.path)
        method = request.method
        
        # Track active requests
        ACTIVE_REQUESTS.labels(service=self.service_name).inc()
        
        # Track request size
        request_size = int(request.headers.get("content-length", 0))
        REQUEST_SIZE.labels(
            method=method,
            endpoint=endpoint,
            service=self.service_name
        ).observe(request_size)
        
        # Measure request duration
        start_time = time.time()
        
        try:
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Track metrics
            REQUEST_COUNT.labels(
                method=method,
                endpoint=endpoint,
                status_code=response.status_code,
                service=self.service_name
            ).inc()
            
            REQUEST_DURATION.labels(
                method=method,
                endpoint=endpoint,
                service=self.service_name
            ).observe(duration)
            
            # Track response size
            response_size = len(response.body) if hasattr(response, 'body') else 0
            RESPONSE_SIZE.labels(
                method=method,
                endpoint=endpoint,
                service=self.service_name
            ).observe(response_size)
            
            return response
            
        except Exception as e:
            # Track error metrics
            REQUEST_COUNT.labels(
                method=method,
                endpoint=endpoint,
                status_code=500,
                service=self.service_name
            ).inc()
            
            duration = time.time() - start_time
            REQUEST_DURATION.labels(
                method=method,
                endpoint=endpoint,
                service=self.service_name
            ).observe(duration)
            
            raise
        finally:
            # Decrease active requests
            ACTIVE_REQUESTS.labels(service=self.service_name).dec()
    
    def _get_endpoint_pattern(self, path: str) -> str:
        """Convert path to endpoint pattern for metrics."""
        # Remove query parameters
        path = path.split('?')[0]
        
        # Common endpoint patterns
        patterns = [
            (r'/api/v1/patterns/detect', '/api/v1/patterns/detect'),
            (r'/api/v1/patterns/jobs/[^/]+', '/api/v1/patterns/jobs/{job_id}'),
            (r'/api/v1/patterns/similar/[^/]+', '/api/v1/patterns/similar/{pattern_id}'),
            (r'/api/v1/ml/models/[^/]+/train', '/api/v1/ml/models/{model_id}/train'),
            (r'/api/v1/ml/models/[^/]+/predict', '/api/v1/ml/models/{model_id}/predict'),
            (r'/api/v1/ml/models/[^/]+', '/api/v1/ml/models/{model_id}'),
            (r'/api/v1/ml/training/[^/]+', '/api/v1/ml/training/{job_id}'),
            (r'/health/.*', '/health'),
        ]\n        \n        import re\n        for pattern, replacement in patterns:\n            if re.match(pattern, path):\n                return replacement\n        \n        return path\n    \n    async def _collect_system_metrics(self) -> None:\n        \"\"\"Collect system metrics periodically.\"\"\"\n        while True:\n            try:\n                # Memory metrics\n                memory = psutil.virtual_memory()\n                MEMORY_USAGE.labels(type=\"total\").set(memory.total)\n                MEMORY_USAGE.labels(type=\"available\").set(memory.available)\n                MEMORY_USAGE.labels(type=\"used\").set(memory.used)\n                \n                # CPU metrics\n                cpu_percent = psutil.cpu_percent(interval=1)\n                CPU_USAGE.set(cpu_percent)\n                \n                # Disk metrics\n                disk = psutil.disk_usage('/')\n                DISK_USAGE.labels(type=\"total\").set(disk.total)\n                DISK_USAGE.labels(type=\"used\").set(disk.used)\n                DISK_USAGE.labels(type=\"free\").set(disk.free)\n                \n            except Exception as e:\n                logger.error(\"Failed to collect system metrics\", error=str(e))\n            \n            # Wait 30 seconds before next collection\n            await asyncio.sleep(30)\n\n\nclass PerformanceMonitoringMiddleware(BaseHTTPMiddleware):\n    \"\"\"Middleware for performance monitoring and alerting.\"\"\"\n    \n    def __init__(self, app, slow_request_threshold: float = 5.0):\n        super().__init__(app)\n        self.slow_request_threshold = slow_request_threshold\n        self.request_stats = {}\n    \n    async def dispatch(self, request: Request, call_next) -> Response:\n        start_time = time.time()\n        \n        try:\n            response = await call_next(request)\n            \n            # Calculate duration\n            duration = time.time() - start_time\n            \n            # Log slow requests\n            if duration > self.slow_request_threshold:\n                request_id = getattr(request.state, \"request_id\", \"unknown\")\n                logger.warning(\n                    \"Slow request detected\",\n                    request_id=request_id,\n                    method=request.method,\n                    path=request.url.path,\n                    duration=duration,\n                    threshold=self.slow_request_threshold,\n                )\n            \n            # Update request statistics\n            endpoint = request.url.path\n            if endpoint not in self.request_stats:\n                self.request_stats[endpoint] = {\n                    \"count\": 0,\n                    \"total_time\": 0,\n                    \"min_time\": float('inf'),\n                    \"max_time\": 0,\n                }\n            \n            stats = self.request_stats[endpoint]\n            stats[\"count\"] += 1\n            stats[\"total_time\"] += duration\n            stats[\"min_time\"] = min(stats[\"min_time\"], duration)\n            stats[\"max_time\"] = max(stats[\"max_time\"], duration)\n            \n            return response\n            \n        except Exception as e:\n            duration = time.time() - start_time\n            request_id = getattr(request.state, \"request_id\", \"unknown\")\n            \n            logger.error(\n                \"Request failed\",\n                request_id=request_id,\n                method=request.method,\n                path=request.url.path,\n                duration=duration,\n                error=str(e),\n            )\n            \n            raise\n    \n    def get_stats(self) -> Dict[str, Any]:\n        \"\"\"Get performance statistics.\"\"\"\n        stats = {}\n        for endpoint, data in self.request_stats.items():\n            if data[\"count\"] > 0:\n                stats[endpoint] = {\n                    \"count\": data[\"count\"],\n                    \"avg_time\": data[\"total_time\"] / data[\"count\"],\n                    \"min_time\": data[\"min_time\"],\n                    \"max_time\": data[\"max_time\"],\n                }\n        return stats\n\n\nclass PatternDetectionMetrics:\n    \"\"\"Helper class for pattern detection metrics.\"\"\"\n    \n    @staticmethod\n    def record_pattern_detection(\n        pattern_type: str,\n        language: str,\n        duration: float,\n        status: str = \"success\"\n    ) -> None:\n        \"\"\"Record pattern detection metrics.\"\"\"\n        PATTERN_DETECTION_COUNT.labels(\n            pattern_type=pattern_type,\n            language=language,\n            status=status\n        ).inc()\n        \n        PATTERN_DETECTION_DURATION.labels(\n            pattern_type=pattern_type,\n            language=language\n        ).observe(duration)\n\n\nclass MLModelMetrics:\n    \"\"\"Helper class for ML model metrics.\"\"\"\n    \n    @staticmethod\n    def record_model_inference(\n        model_id: str,\n        duration: float,\n        status: str = \"success\"\n    ) -> None:\n        \"\"\"Record ML model inference metrics.\"\"\"\n        ML_MODEL_INFERENCE_COUNT.labels(\n            model_id=model_id,\n            status=status\n        ).inc()\n        \n        ML_MODEL_INFERENCE_DURATION.labels(\n            model_id=model_id\n        ).observe(duration)\n\n\ndef setup_monitoring(app, settings) -> None:\n    \"\"\"Setup monitoring middleware.\"\"\"\n    if not settings.enable_metrics:\n        return\n    \n    # Add performance monitoring\n    performance_middleware = PerformanceMonitoringMiddleware(\n        app,\n        slow_request_threshold=5.0\n    )\n    app.add_middleware(PerformanceMonitoringMiddleware, slow_request_threshold=5.0)\n    \n    # Add Prometheus metrics\n    app.add_middleware(PrometheusMiddleware, service_name=\"pattern-mining\")\n    \n    # Add metrics endpoint\n    from fastapi import FastAPI\n    from fastapi.responses import PlainTextResponse\n    \n    @app.get(\"/metrics\")\n    async def metrics():\n        \"\"\"Prometheus metrics endpoint.\"\"\"\n        return PlainTextResponse(\n            generate_latest(),\n            media_type=\"text/plain\"\n        )\n    \n    # Store performance middleware for stats access\n    app.state.performance_middleware = performance_middleware"