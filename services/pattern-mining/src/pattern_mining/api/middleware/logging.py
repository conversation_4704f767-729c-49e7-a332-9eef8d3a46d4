"""
Structured Logging Middleware

Production-ready logging middleware with structured logging, tracing, and monitoring.
"""

import time
import uuid
import json
from typing import Callable, Dict, Any
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import structlog
from opentelemetry import trace
from opentelemetry.instrumentation.fastapi import <PERSON><PERSON><PERSON>nstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.exporter.jaeger.thrift import J<PERSON><PERSON>Exporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.sdk.resources import SERVICE_NAME, Resource

logger = structlog.get_logger()


class StructuredLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for structured logging with OpenTelemetry tracing."""
    
    def __init__(self, app, service_name: str = "pattern-mining"):
        super().__init__(app)
        self.service_name = service_name
        self.tracer = trace.get_tracer(__name__)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Generate request ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # Extract client information
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent", "")
        
        # Start timing
        start_time = time.time()
        
        # Create span for tracing
        with self.tracer.start_as_current_span(
            f"{request.method} {request.url.path}",
            attributes={
                "http.method": request.method,
                "http.url": str(request.url),
                "http.scheme": request.url.scheme,
                "http.host": request.url.hostname,
                "http.target": request.url.path,
                "http.user_agent": user_agent,
                "http.client_ip": client_ip,
                "request.id": request_id,
            }
        ) as span:
            # Log request start
            logger.info(
                "Request started",
                request_id=request_id,
                method=request.method,
                url=str(request.url),
                path=request.url.path,
                query_params=dict(request.query_params),
                client_ip=client_ip,
                user_agent=user_agent,
                content_length=request.headers.get("content-length"),
                content_type=request.headers.get("content-type"),
                service=self.service_name,
            )
            
            # Process request
            try:
                response = await call_next(request)
                
                # Calculate timing
                process_time = time.time() - start_time
                
                # Update span attributes
                span.set_attribute("http.status_code", response.status_code)
                span.set_attribute("http.response_time", process_time)
                
                # Log response
                log_level = "warning" if response.status_code >= 400 else "info"
                getattr(logger, log_level)(
                    "Request completed",
                    request_id=request_id,
                    status_code=response.status_code,
                    process_time=process_time,
                    content_length=response.headers.get("content-length"),
                    content_type=response.headers.get("content-type"),
                    service=self.service_name,
                )
                
                # Add response headers
                response.headers["X-Request-ID"] = request_id
                response.headers["X-Process-Time"] = f"{process_time:.4f}"
                response.headers["X-Service"] = self.service_name
                
                return response
                
            except Exception as e:
                # Calculate timing for error cases
                process_time = time.time() - start_time
                
                # Update span with error
                span.set_attribute("error", True)
                span.set_attribute("error.type", type(e).__name__)
                span.set_attribute("error.message", str(e))
                
                # Log error
                logger.error(
                    "Request failed",
                    request_id=request_id,
                    error=str(e),
                    error_type=type(e).__name__,
                    process_time=process_time,
                    service=self.service_name,
                    exc_info=True,
                )
                
                raise
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP from request headers."""
        # Check for forwarded headers (common in load balancers)
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fall back to direct client IP
        return request.client.host if request.client else "unknown"


class RequestContextMiddleware(BaseHTTPMiddleware):
    """Middleware for adding request context to all log messages."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Add request context to structlog
        request_id = getattr(request.state, "request_id", "unknown")
        
        # Bind context to logger
        structlog.contextvars.clear_contextvars()
        structlog.contextvars.bind_contextvars(
            request_id=request_id,
            method=request.method,
            path=request.url.path,
        )
        
        try:
            response = await call_next(request)
            return response
        finally:
            # Clear context after request
            structlog.contextvars.clear_contextvars()


def setup_tracing(settings) -> None:
    """Configure OpenTelemetry tracing."""
    if not settings.enable_tracing:
        return
    
    # Create tracer provider
    resource = Resource.create({SERVICE_NAME: "pattern-mining"})
    trace.set_tracer_provider(TracerProvider(resource=resource))
    
    # Configure Jaeger exporter
    jaeger_exporter = JaegerExporter(
        agent_host_name="localhost",
        agent_port=6831,
        collector_endpoint=settings.jaeger_endpoint,
    )
    
    # Add span processor
    span_processor = BatchSpanProcessor(jaeger_exporter)
    trace.get_tracer_provider().add_span_processor(span_processor)
    
    # Auto-instrument FastAPI and requests
    FastAPIInstrumentor.instrument()
    RequestsInstrumentor.instrument()


def setup_structured_logging(settings) -> None:
    """Configure structured logging."""
    processors = [
        structlog.contextvars.merge_contextvars,
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
    ]
    
    # Use JSON renderer for production
    if settings.log_format == "json":
        processors.append(structlog.processors.JSONRenderer())
    else:
        processors.append(structlog.dev.ConsoleRenderer())
    
    structlog.configure(
        processors=processors,
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )