"""
Middleware Package

Production-ready middleware components for FastAPI.
"""

from .logging import (
    StructuredLoggingMiddleware,
    RequestContextMiddleware,
    setup_tracing,
    setup_structured_logging,
)
from .rate_limiting import (
    RateLimitMiddleware,
    BurstProtectionMiddleware,
    setup_rate_limiting,
)
from .error_handling import (
    ErrorHandlingMiddleware,
    TimeoutMiddleware,
    RequestSizeMiddleware,
    setup_error_handling,
)
from .monitoring import (
    PrometheusMiddleware,
    PerformanceMonitoringMiddleware,
    PatternDetectionMetrics,
    MLModelMetrics,
    setup_monitoring,
)

__all__ = [
    # Logging
    "StructuredLoggingMiddleware",
    "RequestContextMiddleware",
    "setup_tracing",
    "setup_structured_logging",
    
    # Rate limiting
    "RateLimitMiddleware",
    "BurstProtectionMiddleware",
    "setup_rate_limiting",
    
    # Error handling
    "ErrorHandlingMiddleware",
    "TimeoutMiddleware",
    "RequestSizeMiddleware",
    "setup_error_handling",
    
    # Monitoring
    "PrometheusMiddleware",
    "PerformanceMonitoringMiddleware",
    "PatternDetectionMetrics",
    "MLModelMetrics",
    "setup_monitoring",
]