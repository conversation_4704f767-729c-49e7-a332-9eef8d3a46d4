"""
Error Handling Middleware

Global exception handling with structured error responses and monitoring.
"""

import traceback
from typing import Dict, Any, Optional
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
import structlog
from pydantic import ValidationError
from sqlalchemy.exc import SQLAlchemyError
import redis.exceptions

logger = structlog.get_logger()


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Global error handling middleware."""
    
    def __init__(self, app, include_traceback: bool = False):
        super().__init__(app)
        self.include_traceback = include_traceback
    
    async def dispatch(self, request: Request, call_next):
        try:
            response = await call_next(request)
            return response
        except Exception as e:
            return await self._handle_exception(request, e)
    
    async def _handle_exception(self, request: Request, exc: Exception) -> JSONResponse:
        """Handle different types of exceptions."""
        request_id = getattr(request.state, "request_id", "unknown")
        
        # HTTP exceptions (already handled by FastAPI)
        if isinstance(exc, HTTPException):
            return await self._handle_http_exception(request, exc)
        
        # Validation errors
        if isinstance(exc, ValidationError):
            return await self._handle_validation_error(request, exc)
        
        # Database errors
        if isinstance(exc, SQLAlchemyError):
            return await self._handle_database_error(request, exc)
        
        # Redis errors
        if isinstance(exc, redis.exceptions.RedisError):
            return await self._handle_redis_error(request, exc)
        
        # ML/AI specific errors
        if self._is_ml_error(exc):
            return await self._handle_ml_error(request, exc)
        
        # Generic server errors
        return await self._handle_server_error(request, exc)
    
    async def _handle_http_exception(self, request: Request, exc: HTTPException) -> JSONResponse:
        """Handle HTTP exceptions."""
        request_id = getattr(request.state, "request_id", "unknown")
        
        logger.warning(
            "HTTP exception",
            request_id=request_id,
            status_code=exc.status_code,
            detail=exc.detail,
            path=request.url.path,
        )
        
        error_response = {
            "error": {
                "type": "http_error",
                "code": exc.status_code,
                "message": exc.detail,
                "request_id": request_id,
                "path": request.url.path,
            }
        }
        
        return JSONResponse(
            status_code=exc.status_code,
            content=error_response,
            headers=exc.headers
        )
    
    async def _handle_validation_error(self, request: Request, exc: ValidationError) -> JSONResponse:
        """Handle Pydantic validation errors."""
        request_id = getattr(request.state, "request_id", "unknown")
        
        logger.warning(
            "Validation error",
            request_id=request_id,
            error_count=exc.error_count(),
            errors=exc.errors(),
            path=request.url.path,
        )
        
        error_response = {
            "error": {
                "type": "validation_error",
                "code": 422,
                "message": "Request validation failed",
                "request_id": request_id,
                "path": request.url.path,
                "details": exc.errors(),
            }
        }
        
        return JSONResponse(
            status_code=422,
            content=error_response
        )
    
    async def _handle_database_error(self, request: Request, exc: SQLAlchemyError) -> JSONResponse:
        """Handle database errors."""
        request_id = getattr(request.state, "request_id", "unknown")
        
        logger.error(
            "Database error",
            request_id=request_id,
            error=str(exc),
            error_type=type(exc).__name__,
            path=request.url.path,
        )
        
        error_response = {
            "error": {
                "type": "database_error",
                "code": 503,
                "message": "Database operation failed",
                "request_id": request_id,
                "path": request.url.path,
            }\n        }\n        \n        # Don't expose database details in production\n        if self.include_traceback:\n            error_response[\"error\"][\"details\"] = str(exc)\n        \n        return JSONResponse(\n            status_code=503,\n            content=error_response\n        )\n    \n    async def _handle_redis_error(self, request: Request, exc: redis.exceptions.RedisError) -> JSONResponse:\n        \"\"\"Handle Redis errors.\"\"\"\n        request_id = getattr(request.state, \"request_id\", \"unknown\")\n        \n        logger.error(\n            \"Redis error\",\n            request_id=request_id,\n            error=str(exc),\n            error_type=type(exc).__name__,\n            path=request.url.path,\n        )\n        \n        error_response = {\n            \"error\": {\n                \"type\": \"cache_error\",\n                \"code\": 503,\n                \"message\": \"Cache service unavailable\",\n                \"request_id\": request_id,\n                \"path\": request.url.path,\n            }\n        }\n        \n        return JSONResponse(\n            status_code=503,\n            content=error_response\n        )\n    \n    def _is_ml_error(self, exc: Exception) -> bool:\n        \"\"\"Check if exception is ML/AI related.\"\"\"\n        ml_error_types = [\n            \"ModelLoadError\",\n            \"InferenceError\",\n            \"FeatureExtractionError\",\n            \"EmbeddingError\",\n            \"TokenizerError\",\n        ]\n        \n        return type(exc).__name__ in ml_error_types or \"ml\" in str(exc).lower()\n    \n    async def _handle_ml_error(self, request: Request, exc: Exception) -> JSONResponse:\n        \"\"\"Handle ML/AI specific errors.\"\"\"\n        request_id = getattr(request.state, \"request_id\", \"unknown\")\n        \n        logger.error(\n            \"ML error\",\n            request_id=request_id,\n            error=str(exc),\n            error_type=type(exc).__name__,\n            path=request.url.path,\n        )\n        \n        error_response = {\n            \"error\": {\n                \"type\": \"ml_error\",\n                \"code\": 503,\n                \"message\": \"ML processing failed\",\n                \"request_id\": request_id,\n                \"path\": request.url.path,\n            }\n        }\n        \n        if self.include_traceback:\n            error_response[\"error\"][\"details\"] = str(exc)\n        \n        return JSONResponse(\n            status_code=503,\n            content=error_response\n        )\n    \n    async def _handle_server_error(self, request: Request, exc: Exception) -> JSONResponse:\n        \"\"\"Handle generic server errors.\"\"\"\n        request_id = getattr(request.state, \"request_id\", \"unknown\")\n        \n        logger.error(\n            \"Server error\",\n            request_id=request_id,\n            error=str(exc),\n            error_type=type(exc).__name__,\n            path=request.url.path,\n            exc_info=True,\n        )\n        \n        error_response = {\n            \"error\": {\n                \"type\": \"server_error\",\n                \"code\": 500,\n                \"message\": \"Internal server error\",\n                \"request_id\": request_id,\n                \"path\": request.url.path,\n            }\n        }\n        \n        if self.include_traceback:\n            error_response[\"error\"][\"traceback\"] = traceback.format_exc()\n        \n        return JSONResponse(\n            status_code=500,\n            content=error_response\n        )\n\n\nclass TimeoutMiddleware(BaseHTTPMiddleware):\n    \"\"\"Middleware to handle request timeouts.\"\"\"\n    \n    def __init__(self, app, timeout: int = 30):\n        super().__init__(app)\n        self.timeout = timeout\n    \n    async def dispatch(self, request: Request, call_next):\n        import asyncio\n        \n        try:\n            response = await asyncio.wait_for(\n                call_next(request),\n                timeout=self.timeout\n            )\n            return response\n        except asyncio.TimeoutError:\n            request_id = getattr(request.state, \"request_id\", \"unknown\")\n            \n            logger.error(\n                \"Request timeout\",\n                request_id=request_id,\n                timeout=self.timeout,\n                path=request.url.path,\n            )\n            \n            error_response = {\n                \"error\": {\n                    \"type\": \"timeout_error\",\n                    \"code\": 504,\n                    \"message\": f\"Request timed out after {self.timeout} seconds\",\n                    \"request_id\": request_id,\n                    \"path\": request.url.path,\n                }\n            }\n            \n            return JSONResponse(\n                status_code=504,\n                content=error_response\n            )\n\n\nclass RequestSizeMiddleware(BaseHTTPMiddleware):\n    \"\"\"Middleware to enforce request size limits.\"\"\"\n    \n    def __init__(self, app, max_size: int = 10 * 1024 * 1024):\n        super().__init__(app)\n        self.max_size = max_size\n    \n    async def dispatch(self, request: Request, call_next):\n        content_length = request.headers.get(\"content-length\")\n        \n        if content_length and int(content_length) > self.max_size:\n            request_id = getattr(request.state, \"request_id\", \"unknown\")\n            \n            logger.warning(\n                \"Request too large\",\n                request_id=request_id,\n                content_length=content_length,\n                max_size=self.max_size,\n                path=request.url.path,\n            )\n            \n            error_response = {\n                \"error\": {\n                    \"type\": \"request_too_large\",\n                    \"code\": 413,\n                    \"message\": f\"Request too large. Maximum size: {self.max_size} bytes\",\n                    \"request_id\": request_id,\n                    \"path\": request.url.path,\n                }\n            }\n            \n            return JSONResponse(\n                status_code=413,\n                content=error_response\n            )\n        \n        return await call_next(request)\n\n\ndef setup_error_handling(app, settings) -> None:\n    \"\"\"Setup error handling middleware.\"\"\"\n    # Request size limiting\n    app.add_middleware(\n        RequestSizeMiddleware,\n        max_size=settings.max_request_size\n    )\n    \n    # Request timeout\n    app.add_middleware(\n        TimeoutMiddleware,\n        timeout=settings.response_timeout\n    )\n    \n    # Global error handling\n    app.add_middleware(\n        ErrorHandlingMiddleware,\n        include_traceback=settings.is_development\n    )"