"""
Rate Limiting Middleware

Production-ready rate limiting with Redis backend and sliding window algorithm.
"""

import time
import json
from typing import Callable, Dict, Any, Optional
from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
import redis.asyncio as redis
import structlog
from ..utils.cache import get_redis_client

logger = structlog.get_logger()


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Redis-based rate limiting middleware with sliding window."""
    
    def __init__(
        self,
        app,
        default_rate_limit: int = 100,
        window_size: int = 60,
        burst_limit: int = 10,
        redis_client: Optional[redis.Redis] = None
    ):
        super().__init__(app)
        self.default_rate_limit = default_rate_limit
        self.window_size = window_size
        self.burst_limit = burst_limit
        self.redis_client = redis_client
        
        # Rate limits by endpoint
        self.endpoint_limits = {
            "/api/v1/patterns/detect": 50,
            "/api/v1/patterns/batch": 10,
            "/api/v1/ml/models": 200,
            "/api/v2/patterns/stream": 30,
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Skip rate limiting for health checks
        if request.url.path.startswith("/health"):
            return await call_next(request)
        
        # Get client identifier
        client_id = self._get_client_id(request)
        
        # Check rate limit
        is_allowed, remaining, reset_time = await self._check_rate_limit(
            client_id, request.url.path
        )
        
        if not is_allowed:
            logger.warning(
                "Rate limit exceeded",
                client_id=client_id,
                path=request.url.path,
                remaining=remaining,
                reset_time=reset_time,
            )
            
            # Return rate limit error
            raise HTTPException(
                status_code=429,
                detail={
                    "error": "Rate limit exceeded",
                    "message": f"Too many requests. Try again in {reset_time} seconds.",
                    "remaining": remaining,
                    "reset_time": reset_time,
                },
                headers={
                    "X-RateLimit-Limit": str(self._get_rate_limit(request.url.path)),
                    "X-RateLimit-Remaining": str(remaining),
                    "X-RateLimit-Reset": str(reset_time),
                    "Retry-After": str(reset_time),
                }
            )
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers
        response.headers["X-RateLimit-Limit"] = str(self._get_rate_limit(request.url.path))
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(reset_time)
        
        return response
    
    def _get_client_id(self, request: Request) -> str:
        """Get client identifier for rate limiting."""
        # Check for API key first
        api_key = request.headers.get("x-api-key")
        if api_key:
            return f"api_key:{api_key}"
        
        # Check for JWT token
        auth_header = request.headers.get("authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header.split(" ", 1)[1]
            return f"token:{token[:20]}"  # Use first 20 chars
        
        # Fall back to IP address
        client_ip = self._get_client_ip(request)
        return f"ip:{client_ip}"
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP from request headers."""
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    def _get_rate_limit(self, path: str) -> int:
        """Get rate limit for specific endpoint."""
        # Check exact path match
        if path in self.endpoint_limits:
            return self.endpoint_limits[path]
        
        # Check prefix matches
        for endpoint_path, limit in self.endpoint_limits.items():
            if path.startswith(endpoint_path):
                return limit
        
        return self.default_rate_limit
    
    async def _check_rate_limit(
        self, client_id: str, path: str
    ) -> tuple[bool, int, int]:
        """Check if request is within rate limit using sliding window."""
        if not self.redis_client:
            # Fall back to in-memory rate limiting (not recommended for production)
            return True, 999, 0
        
        current_time = int(time.time())
        window_start = current_time - self.window_size
        
        # Rate limit key
        rate_limit_key = f"rate_limit:{client_id}:{path}"
        
        try:
            # Use Redis pipeline for atomic operations
            async with self.redis_client.pipeline(transaction=True) as pipe:
                # Remove old entries
                pipe.zremrangebyscore(rate_limit_key, 0, window_start)
                
                # Count current requests
                pipe.zcard(rate_limit_key)
                
                # Execute pipeline
                results = await pipe.execute()
                current_requests = results[1]
                
                # Check if under limit
                rate_limit = self._get_rate_limit(path)
                if current_requests >= rate_limit:
                    remaining = 0
                    reset_time = self.window_size
                    return False, remaining, reset_time
                
                # Add current request
                request_id = f"{current_time}:{client_id}"
                await self.redis_client.zadd(rate_limit_key, {request_id: current_time})
                
                # Set expiration
                await self.redis_client.expire(rate_limit_key, self.window_size)
                
                # Calculate remaining requests and reset time
                remaining = rate_limit - current_requests - 1
                reset_time = self.window_size
                
                return True, remaining, reset_time
                
        except Exception as e:
            logger.error(
                "Rate limit check failed",
                client_id=client_id,
                path=path,
                error=str(e),
            )
            # Allow request on Redis error
            return True, 999, 0


class BurstProtectionMiddleware(BaseHTTPMiddleware):
    """Middleware to protect against burst attacks."""
    
    def __init__(
        self,
        app,
        burst_limit: int = 10,
        burst_window: int = 1,
        redis_client: Optional[redis.Redis] = None
    ):
        super().__init__(app)
        self.burst_limit = burst_limit
        self.burst_window = burst_window
        self.redis_client = redis_client
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Skip for health checks
        if request.url.path.startswith("/health"):
            return await call_next(request)
        
        client_id = self._get_client_id(request)
        
        # Check burst limit
        is_allowed = await self._check_burst_limit(client_id)
        
        if not is_allowed:
            logger.warning(
                "Burst limit exceeded",
                client_id=client_id,
                path=request.url.path,
                burst_limit=self.burst_limit,
                burst_window=self.burst_window,
            )
            
            raise HTTPException(
                status_code=429,
                detail={
                    "error": "Burst limit exceeded",
                    "message": f"Too many requests in short time. Try again in {self.burst_window} seconds.",
                },
                headers={
                    "X-Burst-Limit": str(self.burst_limit),
                    "X-Burst-Window": str(self.burst_window),
                    "Retry-After": str(self.burst_window),
                }
            )
        
        return await call_next(request)
    
    def _get_client_id(self, request: Request) -> str:
        """Get client identifier for burst protection."""
        # Use same logic as rate limiting
        client_ip = request.client.host if request.client else "unknown"
        return f"burst:{client_ip}"
    
    async def _check_burst_limit(self, client_id: str) -> bool:
        """Check if request is within burst limit."""
        if not self.redis_client:
            return True
        
        current_time = int(time.time())
        window_start = current_time - self.burst_window
        
        burst_key = f"burst:{client_id}"
        
        try:
            # Remove old entries and count current requests
            async with self.redis_client.pipeline(transaction=True) as pipe:
                pipe.zremrangebyscore(burst_key, 0, window_start)
                pipe.zcard(burst_key)
                results = await pipe.execute()
                
                current_requests = results[1]
                
                if current_requests >= self.burst_limit:
                    return False
                
                # Add current request
                request_id = f"{current_time}:{client_id}"
                await self.redis_client.zadd(burst_key, {request_id: current_time})
                await self.redis_client.expire(burst_key, self.burst_window)
                
                return True
                
        except Exception as e:
            logger.error(
                "Burst limit check failed",
                client_id=client_id,
                error=str(e),
            )
            return True


def setup_rate_limiting(app, settings) -> None:
    """Setup rate limiting middleware."""
    if not settings.enable_caching:
        return
    
    # Get Redis client
    redis_client = get_redis_client(settings.redis_config)
    
    # Add burst protection
    app.add_middleware(
        BurstProtectionMiddleware,
        burst_limit=10,
        burst_window=1,
        redis_client=redis_client
    )
    
    # Add rate limiting
    app.add_middleware(
        RateLimitMiddleware,
        default_rate_limit=settings.api_rate_limit,
        window_size=60,
        burst_limit=10,
        redis_client=redis_client
    )