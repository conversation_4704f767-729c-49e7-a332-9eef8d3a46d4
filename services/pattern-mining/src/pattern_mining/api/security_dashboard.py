"""
Security Dashboard API

Provides comprehensive security monitoring and alerting for the configuration system.
"""

from fastapi import APIRouter, HTTPException, Depends, Request, status
from fastapi.security import HTTPBearer
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import json
import structlog
import redis.asyncio as redis

from ..config.access_control import (
    ConfigRole, get_access_controller, get_permission_manager
)
from ..config.validation import get_config_auditor, SecurityLevel, ValidationSeverity
from ..config.settings import get_settings
from .config_management import get_redis_client, get_user_context

logger = structlog.get_logger()
security = HTTPBearer()
router = APIRouter(prefix="/security", tags=["Security Dashboard"])


@router.get("/dashboard", summary="Get comprehensive security dashboard")
async def get_security_dashboard(
    user_context: Dict[str, Any] = Depends(get_user_context),
    redis_client: redis.Redis = Depends(get_redis_client),
    time_window_hours: int = 24
) -> Dict[str, Any]:
    """
    Get comprehensive security dashboard with real-time metrics and alerts.
    
    Requires ADMIN or SECURITY_ADMIN role.
    """
    try:
        user_role = ConfigRole(user_context["user_role"])
        
        # Check authorization
        if user_role not in [ConfigRole.ADMIN, ConfigRole.SECURITY_ADMIN]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Security dashboard access denied"
            )
        
        access_controller = get_access_controller(redis_client)
        settings = get_settings()
        
        # Calculate time range
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=time_window_hours)
        
        # Get audit statistics
        audit_entries = await access_controller.get_audit_log(
            start_time=start_time,
            end_time=end_time,
            limit=1000
        )
        
        # Calculate security metrics
        total_requests = len(audit_entries)
        failed_requests = sum(1 for entry in audit_entries if entry.get("success") == "False")
        success_rate = (total_requests - failed_requests) / total_requests if total_requests > 0 else 1.0
        
        # Group by action type
        action_counts = {}
        for entry in audit_entries:
            action = entry.get("action", "unknown")
            action_counts[action] = action_counts.get(action, 0) + 1
        
        # Group by user
        user_activity = {}
        for entry in audit_entries:
            user_id = entry.get("user_id", "unknown")
            if user_id not in user_activity:
                user_activity[user_id] = {"total": 0, "failed": 0, "role": entry.get("user_role", "unknown")}
            user_activity[user_id]["total"] += 1
            if entry.get("success") == "False":
                user_activity[user_id]["failed"] += 1
        
        # Get suspicious activities
        suspicious_activities = await access_controller.detect_suspicious_activity()
        
        # Configuration security audit
        config_dict = settings.dict()
        auditor = get_config_auditor()
        config_audit = auditor.audit_configuration(config_dict)
        
        # Parameter security breakdown
        secret_params = []
        sensitive_params = []
        public_params = []
        
        for param_name in config_dict.keys():
            from ..config.validation import ParameterClassification
            security_level = ParameterClassification.get_security_level(param_name)
            
            if security_level == SecurityLevel.SECRET:
                secret_params.append(param_name)
            elif security_level == SecurityLevel.SENSITIVE:
                sensitive_params.append(param_name)
            else:
                public_params.append(param_name)
        
        # Recent security events (high priority)
        recent_critical_events = [
            entry for entry in audit_entries[-50:]  # Last 50 events
            if (entry.get("success") == "False" and 
                any(word in entry.get("failure_reason", "").lower() 
                    for word in ["critical", "denied", "violation", "suspicious"]))
        ]
        
        # Top accessed parameters
        param_access_counts = {}
        for entry in audit_entries:
            param = entry.get("parameter_name", "unknown")
            param_access_counts[param] = param_access_counts.get(param, 0) + 1
        
        top_parameters = sorted(param_access_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        
        # Security trends (comparing with previous period)
        previous_start = start_time - timedelta(hours=time_window_hours)
        previous_entries = await access_controller.get_audit_log(
            start_time=previous_start,
            end_time=start_time,
            limit=1000
        )
        
        previous_failed = sum(1 for entry in previous_entries if entry.get("success") == "False")
        previous_total = len(previous_entries)
        
        failed_trend = "stable"
        if previous_total > 0:
            previous_failure_rate = previous_failed / previous_total
            current_failure_rate = failed_requests / total_requests if total_requests > 0 else 0
            
            if current_failure_rate > previous_failure_rate * 1.2:
                failed_trend = "increasing"
            elif current_failure_rate < previous_failure_rate * 0.8:
                failed_trend = "decreasing"
        
        return {
            "dashboard": {
                "generated_at": datetime.utcnow().isoformat(),
                "time_window_hours": time_window_hours,
                "generated_by": user_context["user_id"]
            },
            "overview": {
                "total_configuration_parameters": len(config_dict),
                "secret_parameters": len(secret_params),
                "sensitive_parameters": len(sensitive_params),
                "public_parameters": len(public_params),
                "security_score": config_audit["security_score"],
                "compliance_status": config_audit["compliance_status"]
            },
            "access_metrics": {
                "total_requests": total_requests,
                "failed_requests": failed_requests,
                "success_rate": round(success_rate * 100, 2),
                "actions": action_counts,
                "trends": {
                    "failure_rate_trend": failed_trend,
                    "previous_period_failed": previous_failed,
                    "previous_period_total": previous_total
                }
            },
            "user_activity": {
                "active_users": len(user_activity),
                "top_users": sorted(
                    [(uid, data) for uid, data in user_activity.items()],
                    key=lambda x: x[1]["total"],
                    reverse=True
                )[:10],
                "users_with_failures": len([u for u in user_activity.values() if u["failed"] > 0])
            },
            "security_violations": {
                "critical_violations": config_audit["violation_summary"]["critical"],
                "error_violations": config_audit["violation_summary"]["error"],
                "warning_violations": config_audit["violation_summary"]["warning"],
                "total_violations": config_audit["violation_summary"]["total"],
                "recent_violations": config_audit["violations"][-10:] if config_audit["violations"] else []
            },
            "suspicious_activities": {
                "total_alerts": len(suspicious_activities),
                "high_severity": len([a for a in suspicious_activities if a.get("severity") == "high"]),
                "medium_severity": len([a for a in suspicious_activities if a.get("severity") == "medium"]),
                "alerts": suspicious_activities
            },
            "parameter_access": {
                "top_accessed_parameters": dict(top_parameters),
                "secret_parameter_accesses": sum(
                    count for param, count in param_access_counts.items()
                    if param in secret_params
                ),
                "sensitive_parameter_accesses": sum(
                    count for param, count in param_access_counts.items()
                    if param in sensitive_params
                )
            },
            "recent_events": {
                "critical_events": recent_critical_events,
                "latest_access_attempts": audit_entries[-10:] if audit_entries else []
            },
            "recommendations": config_audit.get("recommendations", [])
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to generate security dashboard", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate security dashboard"
        )


@router.get("/alerts", summary="Get active security alerts")
async def get_security_alerts(
    user_context: Dict[str, Any] = Depends(get_user_context),
    redis_client: redis.Redis = Depends(get_redis_client),
    severity: Optional[str] = None
) -> Dict[str, Any]:
    """Get active security alerts with optional severity filtering."""
    try:
        user_role = ConfigRole(user_context["user_role"])
        
        # Check authorization
        if user_role not in [ConfigRole.ADMIN, ConfigRole.SECURITY_ADMIN, ConfigRole.MONITOR]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Security alerts access denied"
            )
        
        access_controller = get_access_controller(redis_client)
        
        # Get suspicious activities (our alerts)
        suspicious_activities = await access_controller.detect_suspicious_activity()
        
        # Filter by severity if specified
        if severity:
            suspicious_activities = [
                activity for activity in suspicious_activities
                if activity.get("severity", "").lower() == severity.lower()
            ]
        
        # Get recent failed access attempts for additional context
        recent_time = datetime.utcnow() - timedelta(hours=1)
        recent_entries = await access_controller.get_audit_log(
            start_time=recent_time,
            limit=100
        )
        
        failed_entries = [
            entry for entry in recent_entries
            if entry.get("success") == "False"
        ]
        
        # Group failed attempts by type
        failure_types = {}
        for entry in failed_entries:
            reason = entry.get("failure_reason", "Unknown")
            if reason not in failure_types:
                failure_types[reason] = []
            failure_types[reason].append(entry)
        
        return {
            "alerts": {
                "total_alerts": len(suspicious_activities),
                "high_severity": len([a for a in suspicious_activities if a.get("severity") == "high"]),
                "medium_severity": len([a for a in suspicious_activities if a.get("severity") == "medium"]),
                "low_severity": len([a for a in suspicious_activities if a.get("severity") == "low"]),
                "active_alerts": suspicious_activities
            },
            "recent_failures": {
                "total_failures_last_hour": len(failed_entries),
                "failure_types": {
                    failure_type: len(entries)
                    for failure_type, entries in failure_types.items()
                },
                "recent_failure_details": failed_entries[-5:]  # Last 5 failures
            },
            "generated_at": datetime.utcnow().isoformat(),
            "generated_by": user_context["user_id"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get security alerts", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve security alerts"
        )


@router.get("/metrics", summary="Get security metrics for monitoring")
async def get_security_metrics(
    user_context: Dict[str, Any] = Depends(get_user_context),
    redis_client: redis.Redis = Depends(get_redis_client)
) -> Dict[str, Any]:
    """
    Get security metrics in a format suitable for Prometheus or other monitoring systems.
    
    Returns numerical metrics that can be easily graphed and alerted on.
    """
    try:
        user_role = ConfigRole(user_context["user_role"])
        
        # Check authorization
        if user_role not in [ConfigRole.ADMIN, ConfigRole.SECURITY_ADMIN, ConfigRole.MONITOR]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Security metrics access denied"
            )
        
        access_controller = get_access_controller(redis_client)
        settings = get_settings()
        
        # Get recent activity metrics
        recent_time = datetime.utcnow() - timedelta(hours=1)
        recent_entries = await access_controller.get_audit_log(
            start_time=recent_time,
            limit=1000
        )
        
        # Calculate metrics
        total_requests = len(recent_entries)
        failed_requests = sum(1 for entry in recent_entries if entry.get("success") == "False")
        success_rate = (total_requests - failed_requests) / total_requests if total_requests > 0 else 1.0
        
        # Get suspicious activities
        suspicious_activities = await access_controller.detect_suspicious_activity()
        
        # Configuration audit metrics
        config_dict = settings.dict()
        auditor = get_config_auditor()
        config_audit = auditor.audit_configuration(config_dict)
        
        # Parameter security counts
        secret_count = 0
        sensitive_count = 0
        
        for param_name in config_dict.keys():
            from ..config.validation import ParameterClassification
            security_level = ParameterClassification.get_security_level(param_name)
            
            if security_level == SecurityLevel.SECRET:
                secret_count += 1
            elif security_level == SecurityLevel.SENSITIVE:
                sensitive_count += 1
        
        return {
            "config_security_score": config_audit["security_score"],
            "config_total_parameters": len(config_dict),
            "config_secret_parameters": secret_count,
            "config_sensitive_parameters": sensitive_count,
            "config_violations_critical": config_audit["violation_summary"]["critical"],
            "config_violations_error": config_audit["violation_summary"]["error"],
            "config_violations_warning": config_audit["violation_summary"]["warning"],
            "config_violations_total": config_audit["violation_summary"]["total"],
            "access_requests_total_last_hour": total_requests,
            "access_requests_failed_last_hour": failed_requests,
            "access_success_rate_percent": round(success_rate * 100, 2),
            "security_alerts_total": len(suspicious_activities),
            "security_alerts_high": len([a for a in suspicious_activities if a.get("severity") == "high"]),
            "security_alerts_medium": len([a for a in suspicious_activities if a.get("severity") == "medium"]),
            "compliance_secure_secrets": 1 if config_audit["compliance_status"]["secure_secrets"] else 0,
            "compliance_no_injection_vectors": 1 if config_audit["compliance_status"]["no_injection_vectors"] else 0,
            "compliance_proper_access_controls": 1 if config_audit["compliance_status"]["proper_access_controls"] else 0,
            "compliance_encryption_compliant": 1 if config_audit["compliance_status"]["encryption_compliant"] else 0,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get security metrics", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve security metrics"
        )


@router.post("/alerts/acknowledge", summary="Acknowledge security alert")
async def acknowledge_security_alert(
    alert_id: str,
    user_context: Dict[str, Any] = Depends(get_user_context),
    redis_client: redis.Redis = Depends(get_redis_client)
) -> Dict[str, Any]:
    """Acknowledge a security alert (marks it as reviewed)."""
    try:
        user_role = ConfigRole(user_context["user_role"])
        
        # Check authorization
        if user_role not in [ConfigRole.ADMIN, ConfigRole.SECURITY_ADMIN]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Alert acknowledgment access denied"
            )
        
        # Store acknowledgment in Redis
        ack_key = f"alert_ack:{alert_id}"
        ack_data = {
            "acknowledged_by": user_context["user_id"],
            "acknowledged_at": datetime.utcnow().isoformat(),
            "user_role": user_role.value
        }
        
        await redis_client.hset(ack_key, mapping=ack_data)
        await redis_client.expire(ack_key, 7 * 24 * 3600)  # Keep for 7 days
        
        logger.info(
            "Security alert acknowledged",
            alert_id=alert_id,
            acknowledged_by=user_context["user_id"]
        )
        
        return {
            "alert_id": alert_id,
            "acknowledged": True,
            "acknowledged_by": user_context["user_id"],
            "acknowledged_at": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to acknowledge alert", alert_id=alert_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to acknowledge security alert"
        )