"""
WebSocket Support for Pattern Mining

Real-time WebSocket endpoints for pattern detection updates, progress tracking,
and live analytics. Supports multiple connection types and authentication.
"""

from fastapi import WebSocket, WebSocketDisconnect, Depends, HTTPException, Query
from typing import Dict, List, Optional, Any
import asyncio
import json
from datetime import datetime
from uuid import uuid4
import structlog

from ..config.settings import get_settings, Settings
from ..models.patterns import (
    PatternDetectionRequest,
    DetectedPattern,
    PatternAnalysisJob
)
from ..detectors.manager import get_pattern_detector_manager
from ..features.extractor import get_feature_extractor
from ..database.repositories.pattern_repository import get_pattern_repository
from ..database.repositories.analysis_repository import get_analysis_repository
from ..utils.validation import validate_code_input
from .middleware.monitoring import track_websocket_connection

logger = structlog.get_logger(__name__)


class ConnectionManager:
    """Manages WebSocket connections for pattern detection."""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        self.room_connections: Dict[str, List[str]] = {}
    
    async def connect(self, websocket: WebSocket, connection_id: str, user_id: Optional[str] = None):
        """Accept a WebSocket connection."""
        await websocket.accept()
        self.active_connections[connection_id] = websocket
        self.connection_metadata[connection_id] = {
            "user_id": user_id,
            "connected_at": datetime.utcnow(),
            "last_activity": datetime.utcnow(),
            "rooms": []
        }
        
        logger.info(
            "WebSocket connection established",
            connection_id=connection_id,
            user_id=user_id,
            total_connections=len(self.active_connections)
        )
    
    def disconnect(self, connection_id: str):
        """Remove a WebSocket connection."""
        if connection_id in self.active_connections:
            # Remove from all rooms
            metadata = self.connection_metadata.get(connection_id, {})
            for room in metadata.get("rooms", []):
                self.leave_room(connection_id, room)
            
            # Remove connection
            del self.active_connections[connection_id]
            del self.connection_metadata[connection_id]
            
            logger.info(
                "WebSocket connection closed",
                connection_id=connection_id,
                total_connections=len(self.active_connections)
            )
    
    def join_room(self, connection_id: str, room: str):
        """Add connection to a room."""
        if room not in self.room_connections:
            self.room_connections[room] = []
        
        if connection_id not in self.room_connections[room]:
            self.room_connections[room].append(connection_id)
            
            # Update metadata
            if connection_id in self.connection_metadata:
                self.connection_metadata[connection_id]["rooms"].append(room)
            
            logger.info(
                "Connection joined room",
                connection_id=connection_id,
                room=room,
                room_size=len(self.room_connections[room])
            )
    
    def leave_room(self, connection_id: str, room: str):
        """Remove connection from a room."""
        if room in self.room_connections and connection_id in self.room_connections[room]:
            self.room_connections[room].remove(connection_id)
            
            # Update metadata
            if connection_id in self.connection_metadata:
                rooms = self.connection_metadata[connection_id]["rooms"]
                if room in rooms:
                    rooms.remove(room)
            
            # Clean up empty rooms
            if not self.room_connections[room]:
                del self.room_connections[room]
            
            logger.info(
                "Connection left room",
                connection_id=connection_id,
                room=room,
                room_size=len(self.room_connections.get(room, []))
            )
    
    async def send_personal_message(self, connection_id: str, message: Dict[str, Any]):
        """Send message to a specific connection."""
        if connection_id in self.active_connections:
            try:
                await self.active_connections[connection_id].send_text(json.dumps(message))
                # Update last activity
                if connection_id in self.connection_metadata:
                    self.connection_metadata[connection_id]["last_activity"] = datetime.utcnow()
            except Exception as e:
                logger.error(
                    "Failed to send personal message",
                    connection_id=connection_id,
                    error=str(e)
                )
                self.disconnect(connection_id)
    
    async def broadcast_to_room(self, room: str, message: Dict[str, Any]):
        """Broadcast message to all connections in a room."""
        if room in self.room_connections:
            disconnected_connections = []
            
            for connection_id in self.room_connections[room]:
                if connection_id in self.active_connections:
                    try:
                        await self.active_connections[connection_id].send_text(json.dumps(message))
                        # Update last activity
                        if connection_id in self.connection_metadata:
                            self.connection_metadata[connection_id]["last_activity"] = datetime.utcnow()
                    except Exception as e:
                        logger.error(
                            "Failed to send room message",
                            connection_id=connection_id,
                            room=room,
                            error=str(e)
                        )
                        disconnected_connections.append(connection_id)
            
            # Clean up disconnected connections
            for connection_id in disconnected_connections:
                self.disconnect(connection_id)
    
    async def broadcast_to_all(self, message: Dict[str, Any]):
        """Broadcast message to all connections."""
        disconnected_connections = []
        
        for connection_id in list(self.active_connections.keys()):
            try:
                await self.active_connections[connection_id].send_text(json.dumps(message))
                # Update last activity
                if connection_id in self.connection_metadata:
                    self.connection_metadata[connection_id]["last_activity"] = datetime.utcnow()
            except Exception as e:
                logger.error(
                    "Failed to send broadcast message",
                    connection_id=connection_id,
                    error=str(e)
                )
                disconnected_connections.append(connection_id)
        
        # Clean up disconnected connections
        for connection_id in disconnected_connections:
            self.disconnect(connection_id)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        return {
            "total_connections": len(self.active_connections),
            "total_rooms": len(self.room_connections),
            "connections_by_room": {
                room: len(connections) 
                for room, connections in self.room_connections.items()
            },
            "active_users": len(set(
                metadata.get("user_id") 
                for metadata in self.connection_metadata.values() 
                if metadata.get("user_id")
            ))
        }


# Global connection manager
connection_manager = ConnectionManager()


async def websocket_endpoint(
    websocket: WebSocket,
    connection_type: str = Query(..., regex="^(pattern_detection|analytics|progress|general)$"),
    user_id: Optional[str] = Query(None),
    room: Optional[str] = Query(None),
    settings: Settings = Depends(get_settings)
):
    """
    Main WebSocket endpoint for pattern mining connections.
    
    Supports different connection types:
    - pattern_detection: Real-time pattern detection updates
    - analytics: Live analytics and metrics updates
    - progress: Job progress tracking
    - general: General purpose connections
    
    Args:
        websocket: WebSocket connection
        connection_type: Type of connection
        user_id: Optional user identifier
        room: Optional room to join
        settings: Application settings
    """
    connection_id = str(uuid4())
    
    try:
        # Establish connection
        await connection_manager.connect(websocket, connection_id, user_id)
        
        # Track connection
        track_websocket_connection(connection_type, "connected")
        
        # Join room if specified
        if room:
            connection_manager.join_room(connection_id, room)
        
        # Send welcome message
        await connection_manager.send_personal_message(connection_id, {
            "type": "welcome",
            "connection_id": connection_id,
            "connection_type": connection_type,
            "room": room,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        # Handle messages based on connection type
        if connection_type == "pattern_detection":
            await handle_pattern_detection_connection(websocket, connection_id)
        elif connection_type == "analytics":
            await handle_analytics_connection(websocket, connection_id)
        elif connection_type == "progress":
            await handle_progress_connection(websocket, connection_id)
        else:
            await handle_general_connection(websocket, connection_id)
    
    except WebSocketDisconnect:
        logger.info("WebSocket disconnected", connection_id=connection_id)
    except Exception as e:
        logger.error(
            "WebSocket error",
            connection_id=connection_id,
            error=str(e),
            exc_info=True
        )
        await connection_manager.send_personal_message(connection_id, {
            "type": "error",
            "message": "Connection error occurred",
            "timestamp": datetime.utcnow().isoformat()
        })
    finally:
        # Clean up connection
        connection_manager.disconnect(connection_id)
        track_websocket_connection(connection_type, "disconnected")


async def handle_pattern_detection_connection(websocket: WebSocket, connection_id: str):
    """Handle pattern detection WebSocket connection."""
    logger.info("Starting pattern detection WebSocket handler", connection_id=connection_id)
    
    try:
        while True:
            # Receive message
            data = await websocket.receive_text()
            message = json.loads(data)
            
            message_type = message.get("type")
            
            if message_type == "detect_patterns":
                await handle_pattern_detection_request(connection_id, message)
            elif message_type == "stream_analysis":
                await handle_stream_analysis_request(connection_id, message)
            elif message_type == "join_room":
                room = message.get("room")
                if room:
                    connection_manager.join_room(connection_id, room)
                    await connection_manager.send_personal_message(connection_id, {
                        "type": "room_joined",
                        "room": room,
                        "timestamp": datetime.utcnow().isoformat()
                    })
            elif message_type == "leave_room":
                room = message.get("room")
                if room:
                    connection_manager.leave_room(connection_id, room)
                    await connection_manager.send_personal_message(connection_id, {
                        "type": "room_left",
                        "room": room,
                        "timestamp": datetime.utcnow().isoformat()
                    })
            else:
                await connection_manager.send_personal_message(connection_id, {
                    "type": "error",
                    "message": f"Unknown message type: {message_type}",
                    "timestamp": datetime.utcnow().isoformat()
                })
    
    except WebSocketDisconnect:
        logger.info("Pattern detection WebSocket disconnected", connection_id=connection_id)
    except Exception as e:
        logger.error(
            "Error in pattern detection WebSocket handler",
            connection_id=connection_id,
            error=str(e),
            exc_info=True
        )


async def handle_analytics_connection(websocket: WebSocket, connection_id: str):
    """Handle analytics WebSocket connection."""
    logger.info("Starting analytics WebSocket handler", connection_id=connection_id)
    
    try:
        # Start analytics streaming
        analytics_task = asyncio.create_task(
            stream_analytics_updates(connection_id)
        )
        
        while True:
            # Receive message
            data = await websocket.receive_text()
            message = json.loads(data)
            
            message_type = message.get("type")
            
            if message_type == "subscribe_metrics":
                metrics = message.get("metrics", [])
                await handle_metrics_subscription(connection_id, metrics)
            elif message_type == "unsubscribe_metrics":
                metrics = message.get("metrics", [])
                await handle_metrics_unsubscription(connection_id, metrics)
            elif message_type == "get_live_stats":
                await handle_live_stats_request(connection_id)
            else:
                await connection_manager.send_personal_message(connection_id, {
                    "type": "error",
                    "message": f"Unknown message type: {message_type}",
                    "timestamp": datetime.utcnow().isoformat()
                })
    
    except WebSocketDisconnect:
        logger.info("Analytics WebSocket disconnected", connection_id=connection_id)
    except Exception as e:
        logger.error(
            "Error in analytics WebSocket handler",
            connection_id=connection_id,
            error=str(e),
            exc_info=True
        )
    finally:
        # Clean up analytics task
        if 'analytics_task' in locals():
            analytics_task.cancel()


async def handle_progress_connection(websocket: WebSocket, connection_id: str):
    """Handle progress tracking WebSocket connection."""
    logger.info("Starting progress WebSocket handler", connection_id=connection_id)
    
    try:
        while True:
            # Receive message
            data = await websocket.receive_text()
            message = json.loads(data)
            
            message_type = message.get("type")
            
            if message_type == "track_job":
                job_id = message.get("job_id")
                if job_id:
                    await handle_job_tracking_request(connection_id, job_id)
            elif message_type == "track_batch":
                batch_id = message.get("batch_id")
                if batch_id:
                    await handle_batch_tracking_request(connection_id, batch_id)
            else:
                await connection_manager.send_personal_message(connection_id, {
                    "type": "error",
                    "message": f"Unknown message type: {message_type}",
                    "timestamp": datetime.utcnow().isoformat()
                })
    
    except WebSocketDisconnect:
        logger.info("Progress WebSocket disconnected", connection_id=connection_id)
    except Exception as e:
        logger.error(
            "Error in progress WebSocket handler",
            connection_id=connection_id,
            error=str(e),
            exc_info=True
        )


async def handle_general_connection(websocket: WebSocket, connection_id: str):
    """Handle general purpose WebSocket connection."""
    logger.info("Starting general WebSocket handler", connection_id=connection_id)
    
    try:
        while True:
            # Receive message
            data = await websocket.receive_text()
            message = json.loads(data)
            
            message_type = message.get("type")
            
            if message_type == "ping":
                await connection_manager.send_personal_message(connection_id, {
                    "type": "pong",
                    "timestamp": datetime.utcnow().isoformat()
                })
            elif message_type == "get_stats":
                stats = connection_manager.get_connection_stats()
                await connection_manager.send_personal_message(connection_id, {
                    "type": "stats",
                    "data": stats,
                    "timestamp": datetime.utcnow().isoformat()
                })
            else:
                await connection_manager.send_personal_message(connection_id, {
                    "type": "error",
                    "message": f"Unknown message type: {message_type}",
                    "timestamp": datetime.utcnow().isoformat()
                })
    
    except WebSocketDisconnect:
        logger.info("General WebSocket disconnected", connection_id=connection_id)
    except Exception as e:
        logger.error(
            "Error in general WebSocket handler",
            connection_id=connection_id,
            error=str(e),
            exc_info=True
        )


# Message handlers

async def handle_pattern_detection_request(connection_id: str, message: Dict[str, Any]):
    """Handle pattern detection request via WebSocket."""
    try:
        # Parse request
        request_data = message.get("data", {})
        request = PatternDetectionRequest(**request_data)
        
        # Validate input
        if not validate_code_input(request.code):
            await connection_manager.send_personal_message(connection_id, {
                "type": "error",
                "message": "Invalid code input",
                "timestamp": datetime.utcnow().isoformat()
            })
            return
        
        # Send processing start message
        job_id = str(uuid4())
        await connection_manager.send_personal_message(connection_id, {
            "type": "detection_started",
            "job_id": job_id,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        # Get detector manager
        detector_manager = get_pattern_detector_manager()
        feature_extractor = get_feature_extractor()
        
        # Extract features
        await connection_manager.send_personal_message(connection_id, {
            "type": "feature_extraction_started",
            "job_id": job_id,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        features = await feature_extractor.extract_features(
            request.code,
            request.language
        )
        
        await connection_manager.send_personal_message(connection_id, {
            "type": "feature_extraction_completed",
            "job_id": job_id,
            "feature_count": len(features),
            "timestamp": datetime.utcnow().isoformat()
        })
        
        # Detect patterns with streaming
        await connection_manager.send_personal_message(connection_id, {
            "type": "pattern_detection_started",
            "job_id": job_id,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        patterns = []\n        async for pattern in detector_manager.stream_detect_patterns(\n            code=request.code,\n            language=request.language,\n            features=features,\n            detection_types=request.detection_types\n        ):\n            patterns.append(pattern)\n            await connection_manager.send_personal_message(connection_id, {\n                \"type\": \"pattern_detected\",\n                \"job_id\": job_id,\n                \"pattern\": pattern.dict(),\n                \"timestamp\": datetime.utcnow().isoformat()\n            })\n        \n        # Send completion message\n        await connection_manager.send_personal_message(connection_id, {\n            \"type\": \"detection_completed\",\n            \"job_id\": job_id,\n            \"total_patterns\": len(patterns),\n            \"timestamp\": datetime.utcnow().isoformat()\n        })\n        \n    except Exception as e:\n        logger.error(\n            \"Error handling pattern detection request\",\n            connection_id=connection_id,\n            error=str(e),\n            exc_info=True\n        )\n        await connection_manager.send_personal_message(connection_id, {\n            \"type\": \"error\",\n            \"message\": f\"Pattern detection failed: {str(e)}\",\n            \"timestamp\": datetime.utcnow().isoformat()\n        })\n\n\nasync def handle_stream_analysis_request(connection_id: str, message: Dict[str, Any]):\n    \"\"\"Handle stream analysis request via WebSocket.\"\"\"\n    try:\n        # Parse request\n        repository_url = message.get(\"repository_url\")\n        branch = message.get(\"branch\", \"main\")\n        detection_types = message.get(\"detection_types\", [\"ml_inference\"])\n        \n        if not repository_url:\n            await connection_manager.send_personal_message(connection_id, {\n                \"type\": \"error\",\n                \"message\": \"Repository URL is required\",\n                \"timestamp\": datetime.utcnow().isoformat()\n            })\n            return\n        \n        # Start streaming analysis\n        job_id = str(uuid4())\n        await connection_manager.send_personal_message(connection_id, {\n            \"type\": \"stream_analysis_started\",\n            \"job_id\": job_id,\n            \"repository_url\": repository_url,\n            \"timestamp\": datetime.utcnow().isoformat()\n        })\n        \n        # Get detector manager\n        detector_manager = get_pattern_detector_manager()\n        \n        # Stream repository analysis\n        async for progress in detector_manager.stream_repository_analysis(\n            repository_url=repository_url,\n            branch=branch,\n            detection_types=detection_types\n        ):\n            await connection_manager.send_personal_message(connection_id, {\n                \"type\": \"analysis_progress\",\n                \"job_id\": job_id,\n                \"progress\": progress,\n                \"timestamp\": datetime.utcnow().isoformat()\n            })\n        \n        # Send completion message\n        await connection_manager.send_personal_message(connection_id, {\n            \"type\": \"stream_analysis_completed\",\n            \"job_id\": job_id,\n            \"timestamp\": datetime.utcnow().isoformat()\n        })\n        \n    except Exception as e:\n        logger.error(\n            \"Error handling stream analysis request\",\n            connection_id=connection_id,\n            error=str(e),\n            exc_info=True\n        )\n        await connection_manager.send_personal_message(connection_id, {\n            \"type\": \"error\",\n            \"message\": f\"Stream analysis failed: {str(e)}\",\n            \"timestamp\": datetime.utcnow().isoformat()\n        })\n\n\nasync def handle_metrics_subscription(connection_id: str, metrics: List[str]):\n    \"\"\"Handle metrics subscription request.\"\"\"\n    try:\n        # Store subscription preferences (implement according to your needs)\n        # For now, just acknowledge\n        await connection_manager.send_personal_message(connection_id, {\n            \"type\": \"metrics_subscribed\",\n            \"metrics\": metrics,\n            \"timestamp\": datetime.utcnow().isoformat()\n        })\n        \n    except Exception as e:\n        logger.error(\n            \"Error handling metrics subscription\",\n            connection_id=connection_id,\n            error=str(e),\n            exc_info=True\n        )\n\n\nasync def handle_metrics_unsubscription(connection_id: str, metrics: List[str]):\n    \"\"\"Handle metrics unsubscription request.\"\"\"\n    try:\n        # Remove subscription preferences (implement according to your needs)\n        # For now, just acknowledge\n        await connection_manager.send_personal_message(connection_id, {\n            \"type\": \"metrics_unsubscribed\",\n            \"metrics\": metrics,\n            \"timestamp\": datetime.utcnow().isoformat()\n        })\n        \n    except Exception as e:\n        logger.error(\n            \"Error handling metrics unsubscription\",\n            connection_id=connection_id,\n            error=str(e),\n            exc_info=True\n        )\n\n\nasync def handle_live_stats_request(connection_id: str):\n    \"\"\"Handle live stats request.\"\"\"\n    try:\n        # Get live statistics (implement according to your needs)\n        pattern_repo = get_pattern_repository()\n        live_stats = await pattern_repo.get_live_statistics()\n        \n        await connection_manager.send_personal_message(connection_id, {\n            \"type\": \"live_stats\",\n            \"data\": live_stats,\n            \"timestamp\": datetime.utcnow().isoformat()\n        })\n        \n    except Exception as e:\n        logger.error(\n            \"Error handling live stats request\",\n            connection_id=connection_id,\n            error=str(e),\n            exc_info=True\n        )\n\n\nasync def handle_job_tracking_request(connection_id: str, job_id: str):\n    \"\"\"Handle job tracking request.\"\"\"\n    try:\n        # Join job-specific room\n        room = f\"job_{job_id}\"\n        connection_manager.join_room(connection_id, room)\n        \n        # Get current job status\n        analysis_repo = get_analysis_repository()\n        job = await analysis_repo.get_job(job_id)\n        \n        if job:\n            await connection_manager.send_personal_message(connection_id, {\n                \"type\": \"job_status\",\n                \"job_id\": job_id,\n                \"status\": job.status,\n                \"progress\": job.progress,\n                \"timestamp\": datetime.utcnow().isoformat()\n            })\n        else:\n            await connection_manager.send_personal_message(connection_id, {\n                \"type\": \"error\",\n                \"message\": f\"Job {job_id} not found\",\n                \"timestamp\": datetime.utcnow().isoformat()\n            })\n        \n    except Exception as e:\n        logger.error(\n            \"Error handling job tracking request\",\n            connection_id=connection_id,\n            job_id=job_id,\n            error=str(e),\n            exc_info=True\n        )\n\n\nasync def handle_batch_tracking_request(connection_id: str, batch_id: str):\n    \"\"\"Handle batch tracking request.\"\"\"\n    try:\n        # Join batch-specific room\n        room = f\"batch_{batch_id}\"\n        connection_manager.join_room(connection_id, room)\n        \n        # Get current batch status\n        analysis_repo = get_analysis_repository()\n        batch_info = await analysis_repo.get_batch_progress(batch_id)\n        \n        if batch_info:\n            await connection_manager.send_personal_message(connection_id, {\n                \"type\": \"batch_status\",\n                \"batch_id\": batch_id,\n                \"status\": batch_info.get(\"status\"),\n                \"progress\": batch_info.get(\"progress\"),\n                \"completed_jobs\": batch_info.get(\"completed_jobs\"),\n                \"total_jobs\": batch_info.get(\"total_jobs\"),\n                \"timestamp\": datetime.utcnow().isoformat()\n            })\n        else:\n            await connection_manager.send_personal_message(connection_id, {\n                \"type\": \"error\",\n                \"message\": f\"Batch {batch_id} not found\",\n                \"timestamp\": datetime.utcnow().isoformat()\n            })\n        \n    except Exception as e:\n        logger.error(\n            \"Error handling batch tracking request\",\n            connection_id=connection_id,\n            batch_id=batch_id,\n            error=str(e),\n            exc_info=True\n        )\n\n\nasync def stream_analytics_updates(connection_id: str):\n    \"\"\"Stream analytics updates to a connection.\"\"\"\n    try:\n        while connection_id in connection_manager.active_connections:\n            # Get latest analytics data\n            pattern_repo = get_pattern_repository()\n            analytics_data = await pattern_repo.get_real_time_analytics()\n            \n            # Send analytics update\n            await connection_manager.send_personal_message(connection_id, {\n                \"type\": \"analytics_update\",\n                \"data\": analytics_data,\n                \"timestamp\": datetime.utcnow().isoformat()\n            })\n            \n            # Wait before next update\n            await asyncio.sleep(30)  # Update every 30 seconds\n            \n    except Exception as e:\n        logger.error(\n            \"Error streaming analytics updates\",\n            connection_id=connection_id,\n            error=str(e),\n            exc_info=True\n        )\n\n\n# Utility functions for broadcasting updates\n\nasync def broadcast_pattern_detected(pattern: DetectedPattern, job_id: str):\n    \"\"\"Broadcast pattern detection to relevant connections.\"\"\"\n    message = {\n        \"type\": \"pattern_detected\",\n        \"job_id\": job_id,\n        \"pattern\": pattern.dict(),\n        \"timestamp\": datetime.utcnow().isoformat()\n    }\n    \n    # Broadcast to job-specific room\n    room = f\"job_{job_id}\"\n    await connection_manager.broadcast_to_room(room, message)\n\n\nasync def broadcast_job_status_update(job_id: str, status: str, progress: int):\n    \"\"\"Broadcast job status update to relevant connections.\"\"\"\n    message = {\n        \"type\": \"job_status_update\",\n        \"job_id\": job_id,\n        \"status\": status,\n        \"progress\": progress,\n        \"timestamp\": datetime.utcnow().isoformat()\n    }\n    \n    # Broadcast to job-specific room\n    room = f\"job_{job_id}\"\n    await connection_manager.broadcast_to_room(room, message)\n\n\nasync def broadcast_batch_status_update(batch_id: str, status: str, progress: Dict[str, Any]):\n    \"\"\"Broadcast batch status update to relevant connections.\"\"\"\n    message = {\n        \"type\": \"batch_status_update\",\n        \"batch_id\": batch_id,\n        \"status\": status,\n        \"progress\": progress,\n        \"timestamp\": datetime.utcnow().isoformat()\n    }\n    \n    # Broadcast to batch-specific room\n    room = f\"batch_{batch_id}\"\n    await connection_manager.broadcast_to_room(room, message)\n\n\nasync def broadcast_analytics_update(analytics_data: Dict[str, Any]):\n    \"\"\"Broadcast analytics update to all analytics connections.\"\"\"\n    message = {\n        \"type\": \"analytics_broadcast\",\n        \"data\": analytics_data,\n        \"timestamp\": datetime.utcnow().isoformat()\n    }\n    \n    # Broadcast to analytics room\n    room = \"analytics\"\n    await connection_manager.broadcast_to_room(room, message)\n\n\n# Export connection manager and utility functions\n__all__ = [\n    \"websocket_endpoint\",\n    \"connection_manager\",\n    \"broadcast_pattern_detected\",\n    \"broadcast_job_status_update\",\n    \"broadcast_batch_status_update\",\n    \"broadcast_analytics_update\"\n]"