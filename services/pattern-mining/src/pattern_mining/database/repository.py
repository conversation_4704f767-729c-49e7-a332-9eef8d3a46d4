"""
Database Repository

Repository pattern implementation for database operations.
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_, or_
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
from uuid import UUID
import asyncio

from .models import <PERSON><PERSON>, <PERSON><PERSON>ob, Model, TrainingJob, FeatureVector
from ..models.patterns import PatternType, SeverityLevel, DetectionType
from ..models.ml import ModelType, ModelStatus, TrainingStatus


class BaseRepository:
    """Base repository with common operations."""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def commit(self):
        """Commit transaction."""
        await self.session.commit()
    
    async def rollback(self):
        """Rollback transaction."""
        await self.session.rollback()


class PatternRepository(BaseRepository):
    """Repository for pattern operations."""
    
    async def create_pattern(self, pattern_data: Dict[str, Any]) -> Pattern:
        """Create a new pattern."""
        pattern = Pattern(**pattern_data)
        self.session.add(pattern)
        await self.session.flush()
        return pattern
    
    async def get_pattern_by_id(self, pattern_id: str) -> Optional[Pattern]:
        """Get pattern by ID."""
        stmt = select(Pattern).where(Pattern.pattern_id == pattern_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_patterns_by_type(self, pattern_type: PatternType) -> List[Pattern]:
        """Get patterns by type."""
        stmt = select(Pattern).where(Pattern.pattern_type == pattern_type.value)
        result = await self.session.execute(stmt)
        return result.scalars().all()
    
    async def get_patterns_by_severity(self, severity: SeverityLevel) -> List[Pattern]:
        """Get patterns by severity."""
        stmt = select(Pattern).where(Pattern.severity == severity.value)
        result = await self.session.execute(stmt)
        return result.scalars().all()
    
    async def get_patterns_by_language(self, language: str) -> List[Pattern]:
        """Get patterns by programming language."""
        stmt = select(Pattern).where(Pattern.language == language)
        result = await self.session.execute(stmt)
        return result.scalars().all()
    
    async def search_patterns(
        self,
        language: Optional[str] = None,
        pattern_type: Optional[PatternType] = None,
        severity: Optional[SeverityLevel] = None,
        min_confidence: Optional[float] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Pattern]:
        """Search patterns with filters."""
        stmt = select(Pattern)
        
        # Apply filters
        conditions = []
        if language:
            conditions.append(Pattern.language == language)
        if pattern_type:
            conditions.append(Pattern.pattern_type == pattern_type.value)
        if severity:
            conditions.append(Pattern.severity == severity.value)
        if min_confidence:
            conditions.append(Pattern.confidence >= min_confidence)
        
        if conditions:
            stmt = stmt.where(and_(*conditions))
        
        # Apply pagination
        stmt = stmt.offset(offset).limit(limit)
        
        result = await self.session.execute(stmt)
        return result.scalars().all()
    
    async def update_pattern(self, pattern_id: str, update_data: Dict[str, Any]) -> Optional[Pattern]:
        """Update pattern."""
        stmt = (
            update(Pattern)
            .where(Pattern.pattern_id == pattern_id)
            .values(**update_data)
            .returning(Pattern)
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def delete_pattern(self, pattern_id: str) -> bool:
        """Delete pattern."""
        stmt = delete(Pattern).where(Pattern.pattern_id == pattern_id)
        result = await self.session.execute(stmt)
        return result.rowcount > 0
    
    async def get_similar_patterns(
        self,
        pattern_id: str,
        similarity_threshold: float = 0.8,
        limit: int = 10
    ) -> List[Pattern]:
        """Get similar patterns using vector similarity."""
        # This would typically use vector similarity search
        # For now, return patterns of the same type
        pattern = await self.get_pattern_by_id(pattern_id)
        if not pattern:
            return []
        
        stmt = (
            select(Pattern)
            .where(
                and_(
                    Pattern.pattern_id != pattern_id,
                    Pattern.pattern_type == pattern.pattern_type,
                    Pattern.confidence >= similarity_threshold
                )
            )
            .limit(limit)
        )
        result = await self.session.execute(stmt)
        return result.scalars().all()


class AnalysisJobRepository(BaseRepository):
    """Repository for analysis job operations."""
    
    async def create_job(self, job_data: Dict[str, Any]) -> AnalysisJob:
        """Create a new analysis job."""
        job = AnalysisJob(**job_data)
        self.session.add(job)
        await self.session.flush()
        return job
    
    async def get_job_by_id(self, job_id: str) -> Optional[AnalysisJob]:
        """Get job by ID."""
        stmt = select(AnalysisJob).where(AnalysisJob.job_id == job_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def update_job_status(
        self,
        job_id: str,
        status: str,
        progress: Optional[int] = None,
        message: Optional[str] = None
    ) -> Optional[AnalysisJob]:
        """Update job status."""
        update_data = {"status": status}
        if progress is not None:
            update_data["progress"] = progress
        if message is not None:
            update_data["message"] = message
        
        stmt = (
            update(AnalysisJob)
            .where(AnalysisJob.job_id == job_id)
            .values(**update_data)
            .returning(AnalysisJob)
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def set_job_result(
        self,
        job_id: str,
        result: Dict[str, Any],
        processing_time: Optional[float] = None
    ) -> Optional[AnalysisJob]:
        """Set job result."""
        update_data = {"result": result, "status": "completed"}
        if processing_time is not None:
            update_data["processing_time"] = processing_time
        
        stmt = (
            update(AnalysisJob)
            .where(AnalysisJob.job_id == job_id)
            .values(**update_data)
            .returning(AnalysisJob)
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def set_job_error(self, job_id: str, error: str) -> Optional[AnalysisJob]:
        """Set job error."""
        stmt = (
            update(AnalysisJob)
            .where(AnalysisJob.job_id == job_id)
            .values(status="failed", error=error)
            .returning(AnalysisJob)
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_jobs_by_status(self, status: str) -> List[AnalysisJob]:
        """Get jobs by status."""
        stmt = select(AnalysisJob).where(AnalysisJob.status == status)
        result = await self.session.execute(stmt)
        return result.scalars().all()


class ModelRepository(BaseRepository):
    """Repository for ML model operations."""
    
    async def create_model(self, model_data: Dict[str, Any]) -> Model:
        """Create a new model."""
        model = Model(**model_data)
        self.session.add(model)
        await self.session.flush()
        return model
    
    async def get_model_by_id(self, model_id: str) -> Optional[Model]:
        """Get model by ID."""
        stmt = select(Model).where(Model.model_id == model_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_models_by_type(self, model_type: ModelType) -> List[Model]:
        """Get models by type."""
        stmt = select(Model).where(Model.model_type == model_type.value)
        result = await self.session.execute(stmt)
        return result.scalars().all()
    
    async def get_models_by_status(self, status: ModelStatus) -> List[Model]:
        """Get models by status."""
        stmt = select(Model).where(Model.status == status.value)
        result = await self.session.execute(stmt)
        return result.scalars().all()
    
    async def update_model_status(self, model_id: str, status: ModelStatus) -> Optional[Model]:
        """Update model status."""
        stmt = (
            update(Model)
            .where(Model.model_id == model_id)
            .values(status=status.value)
            .returning(Model)
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def update_model_metrics(self, model_id: str, metrics: Dict[str, Any]) -> Optional[Model]:
        """Update model metrics."""
        stmt = (
            update(Model)
            .where(Model.model_id == model_id)
            .values(metrics=metrics)
            .returning(Model)
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def delete_model(self, model_id: str) -> bool:
        """Delete model."""
        stmt = delete(Model).where(Model.model_id == model_id)
        result = await self.session.execute(stmt)
        return result.rowcount > 0


class TrainingJobRepository(BaseRepository):
    """Repository for training job operations."""
    
    async def create_training_job(self, job_data: Dict[str, Any]) -> TrainingJob:
        """Create a new training job."""
        job = TrainingJob(**job_data)
        self.session.add(job)
        await self.session.flush()
        return job
    
    async def get_training_job_by_id(self, job_id: str) -> Optional[TrainingJob]:
        """Get training job by ID."""
        stmt = (
            select(TrainingJob)
            .options(selectinload(TrainingJob.model))
            .where(TrainingJob.job_id == job_id)
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def update_training_job_status(
        self,
        job_id: str,
        status: TrainingStatus,
        progress: Optional[int] = None,
        message: Optional[str] = None
    ) -> Optional[TrainingJob]:
        """Update training job status."""
        update_data = {"status": status.value}
        if progress is not None:
            update_data["progress"] = progress
        if message is not None:
            update_data["message"] = message
        
        stmt = (
            update(TrainingJob)
            .where(TrainingJob.job_id == job_id)
            .values(**update_data)
            .returning(TrainingJob)
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def set_training_job_metrics(
        self,
        job_id: str,
        metrics: Dict[str, Any]
    ) -> Optional[TrainingJob]:
        """Set training job metrics."""
        stmt = (
            update(TrainingJob)
            .where(TrainingJob.job_id == job_id)
            .values(metrics=metrics)
            .returning(TrainingJob)
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_training_jobs_by_model(self, model_id: UUID) -> List[TrainingJob]:
        """Get training jobs for a model."""
        stmt = (
            select(TrainingJob)
            .where(TrainingJob.model_id == model_id)
            .order_by(TrainingJob.created_at.desc())
        )
        result = await self.session.execute(stmt)
        return result.scalars().all()