"""
Database Connection

Database connection management and utilities.
"""

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional
import asyncio
import structlog

from ..config.database import get_database_config
from ..config.settings import Settings

logger = structlog.get_logger(__name__)


# Global database engine
_engine = None
_session_factory = None


def get_engine():
    """Get database engine."""
    global _engine
    if _engine is None:
        config = get_database_config()
        _engine = config.create_async_engine()
    return _engine


def get_session_factory():
    """Get session factory."""
    global _session_factory
    if _session_factory is None:
        engine = get_engine()
        _session_factory = sessionmaker(
            bind=engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
    return _session_factory


@asynccontextmanager
async def get_database_session() -> AsyncGenerator[AsyncSession, None]:
    """Get database session context manager."""
    session_factory = get_session_factory()
    async with session_factory() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def get_database_connection() -> AsyncSession:
    """Get database connection (for dependency injection)."""
    session_factory = get_session_factory()
    return session_factory()


async def get_database_status() -> bool:
    """Check database connectivity status."""
    try:
        if not _engine:
            return False
        async with _engine.begin() as conn:
            await conn.execute(text("SELECT 1"))
        return True
    except Exception as e:
        logger.error("Database status check failed", error=str(e))
        return False


async def init_database(settings: Settings) -> None:
    """Initialize database connection."""
    global _engine, _session_factory
    
    try:
        # Create async engine
        _engine = create_async_engine(
            settings.database_url,
            pool_size=settings.database_pool_size,
            max_overflow=settings.database_max_overflow,
            echo=settings.database_echo,
            pool_pre_ping=True,
            pool_recycle=3600,  # 1 hour
            future=True,
        )
        
        # Create session factory
        _session_factory = sessionmaker(
            bind=_engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=False,
        )
        
        # Test connection
        async with _engine.begin() as conn:
            await conn.execute(text("SELECT 1"))
        
        logger.info("Database initialized successfully")
        
    except Exception as e:
        logger.error("Failed to initialize database", error=str(e))
        raise


async def close_database() -> None:
    """Close database connection."""
    global _engine, _session_factory
    
    if _engine:
        try:
            await _engine.dispose()
            logger.info("Database connection closed successfully")
        except Exception as e:
            logger.error("Error closing database connection", error=str(e))
    
    _engine = None
    _session_factory = None


async def close_database_connections():
    """Close database connections (legacy function)."""
    await close_database()