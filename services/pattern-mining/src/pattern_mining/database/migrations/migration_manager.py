"""
Migration Manager

Table creation and schema management utilities for BigQuery.
Handles table creation, schema updates, and migration tracking.
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, date
from enum import Enum
import json
import hashlib

from google.cloud.bigquery import <PERSON><PERSON><PERSON><PERSON><PERSON>, Table
from google.cloud.bigquery.schema import SchemaFieldType
from google.cloud.exceptions import NotFound, Conflict
import structlog

from ..bigquery import BigQueryClient, BigQueryError, get_bigquery_client
from ..models import (
    Pat<PERSON>Result,
    PatternEmbedding,
    ModelRegistry,
    RepositoryAnalysis,
    FeatureVector,
    BIGQUERY_TABLE_MAPPING
)
from ...config.settings import get_settings

logger = structlog.get_logger(__name__)


class MigrationStatus(str, Enum):
    """Migration status enum."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    ROLLED_BACK = "rolled_back"


class MigrationError(Exception):
    """Migration-specific error."""
    
    def __init__(self, message: str, original_error: Optional[Exception] = None):
        super().__init__(message)
        self.original_error = original_error


class MigrationRecord:
    """Migration record for tracking."""
    
    def __init__(
        self,
        migration_id: str,
        table_name: str,
        operation: str,
        status: MigrationStatus = MigrationStatus.PENDING,
        started_at: Optional[datetime] = None,
        completed_at: Optional[datetime] = None,
        error_message: Optional[str] = None,
        schema_hash: Optional[str] = None
    ):
        self.migration_id = migration_id
        self.table_name = table_name
        self.operation = operation
        self.status = status
        self.started_at = started_at
        self.completed_at = completed_at
        self.error_message = error_message
        self.schema_hash = schema_hash
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage."""
        return {
            "migration_id": self.migration_id,
            "table_name": self.table_name,
            "operation": self.operation,
            "status": self.status.value,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "error_message": self.error_message,
            "schema_hash": self.schema_hash
        }


class SchemaManager:
    """Schema management utilities."""
    
    def __init__(self, bigquery_client: BigQueryClient):
        self.client = bigquery_client
        
    def calculate_schema_hash(self, schema: List[SchemaField]) -> str:
        """Calculate hash for schema fields."""
        schema_str = json.dumps([
            {
                "name": field.name,
                "type": field.field_type,
                "mode": field.mode,
                "description": field.description
            }
            for field in schema
        ], sort_keys=True)
        
        return hashlib.sha256(schema_str.encode()).hexdigest()
    
    def compare_schemas(
        self, 
        current_schema: List[SchemaField], 
        new_schema: List[SchemaField]
    ) -> Dict[str, Any]:
        """Compare two schemas and return differences."""
        current_fields = {field.name: field for field in current_schema}
        new_fields = {field.name: field for field in new_schema}
        
        added_fields = []
        removed_fields = []
        modified_fields = []
        
        # Check for added fields
        for field_name, field in new_fields.items():
            if field_name not in current_fields:
                added_fields.append(field)
        
        # Check for removed fields
        for field_name, field in current_fields.items():
            if field_name not in new_fields:
                removed_fields.append(field)
        
        # Check for modified fields
        for field_name, new_field in new_fields.items():
            if field_name in current_fields:
                current_field = current_fields[field_name]
                if (current_field.field_type != new_field.field_type or
                    current_field.mode != new_field.mode or
                    current_field.description != new_field.description):
                    modified_fields.append({
                        "field_name": field_name,
                        "current": current_field,
                        "new": new_field
                    })
        
        return {
            "added_fields": added_fields,
            "removed_fields": removed_fields,
            "modified_fields": modified_fields,
            "has_changes": bool(added_fields or removed_fields or modified_fields)
        }
    
    def validate_schema_compatibility(
        self, 
        current_schema: List[SchemaField], 
        new_schema: List[SchemaField]
    ) -> Tuple[bool, List[str]]:
        """Validate if schema changes are compatible."""
        differences = self.compare_schemas(current_schema, new_schema)
        errors = []
        
        # Check for breaking changes
        for field in differences["removed_fields"]:
            if field.mode == "REQUIRED":
                errors.append(f"Cannot remove required field: {field.name}")
        
        for modification in differences["modified_fields"]:
            current_field = modification["current"]
            new_field = modification["new"]
            
            # Check if changing from optional to required
            if current_field.mode == "NULLABLE" and new_field.mode == "REQUIRED":
                errors.append(f"Cannot change field {current_field.name} from NULLABLE to REQUIRED")
            
            # Check for incompatible type changes
            if current_field.field_type != new_field.field_type:
                # Only certain type changes are allowed
                compatible_changes = {
                    ("INTEGER", "FLOAT"),
                    ("INTEGER", "STRING"),
                    ("FLOAT", "STRING"),
                    ("BOOLEAN", "STRING"),
                    ("DATE", "STRING"),
                    ("DATETIME", "STRING"),
                    ("TIMESTAMP", "STRING")
                }
                
                if (current_field.field_type, new_field.field_type) not in compatible_changes:
                    errors.append(f"Incompatible type change for field {current_field.name}: "
                                f"{current_field.field_type} -> {new_field.field_type}")
        
        return len(errors) == 0, errors


class MigrationManager:
    """Migration manager for BigQuery table operations."""
    
    def __init__(self, bigquery_client: Optional[BigQueryClient] = None):
        self.client = bigquery_client
        self.schema_manager = SchemaManager(self.client) if self.client else None
        self._migration_records: List[MigrationRecord] = []
        
    async def initialize(self):
        """Initialize the migration manager."""
        if self.client is None:
            self.client = await get_bigquery_client()
            self.schema_manager = SchemaManager(self.client)
        
        # Create migration tracking table if it doesn't exist
        await self._ensure_migration_table_exists()
        
        logger.info("Migration manager initialized successfully")
    
    async def _ensure_migration_table_exists(self):
        """Ensure migration tracking table exists."""
        table_name = "migration_history"
        
        try:
            exists = await self.client.table_exists(table_name)
            if not exists:
                schema = [
                    SchemaField("migration_id", "STRING", mode="REQUIRED"),
                    SchemaField("table_name", "STRING", mode="REQUIRED"),
                    SchemaField("operation", "STRING", mode="REQUIRED"),
                    SchemaField("status", "STRING", mode="REQUIRED"),
                    SchemaField("started_at", "TIMESTAMP", mode="REQUIRED"),
                    SchemaField("completed_at", "TIMESTAMP", mode="NULLABLE"),
                    SchemaField("error_message", "STRING", mode="NULLABLE"),
                    SchemaField("schema_hash", "STRING", mode="NULLABLE"),
                ]
                
                await self.client.create_table(
                    table_name=table_name,
                    schema=schema,
                    description="Migration tracking table",
                    partition_field="started_at"
                )
                
                logger.info("Created migration tracking table")
        
        except Exception as e:
            logger.error("Failed to create migration tracking table", error=str(e))
            raise MigrationError(f"Failed to create migration tracking table: {str(e)}", e)
    
    async def create_table_from_model(self, model_class, force: bool = False) -> bool:
        """Create table from SQLAlchemy model."""
        table_name = model_class.__tablename__
        
        logger.info("Creating table from model", table_name=table_name, model=model_class.__name__)
        
        try:
            # Check if table already exists
            if not force and await self.client.table_exists(table_name):
                logger.info("Table already exists", table_name=table_name)
                return False
            
            # Get schema and configuration from model
            schema = model_class.get_bigquery_schema()
            table_config = model_class.get_bigquery_table_config()
            
            # Create migration record
            migration_record = MigrationRecord(
                migration_id=f"create_{table_name}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                table_name=table_name,
                operation="CREATE_TABLE",
                started_at=datetime.utcnow(),
                schema_hash=self.schema_manager.calculate_schema_hash(schema)
            )
            
            migration_record.status = MigrationStatus.RUNNING
            await self._record_migration(migration_record)
            
            # Create table
            await self.client.create_table(
                table_name=table_name,
                schema=schema,
                partition_field=table_config.get("partition_field"),
                clustering_fields=table_config.get("clustering_fields"),
                description=table_config.get("description"),
                expiration_days=table_config.get("expiration_days")
            )
            
            # Update migration record
            migration_record.status = MigrationStatus.COMPLETED
            migration_record.completed_at = datetime.utcnow()
            await self._record_migration(migration_record)
            
            logger.info("Table created successfully", table_name=table_name)
            return True
            
        except Exception as e:
            # Update migration record with error
            migration_record.status = MigrationStatus.FAILED
            migration_record.error_message = str(e)
            migration_record.completed_at = datetime.utcnow()
            await self._record_migration(migration_record)
            
            logger.error("Failed to create table", table_name=table_name, error=str(e))
            raise MigrationError(f"Failed to create table {table_name}: {str(e)}", e)
    
    async def update_table_schema(self, model_class, dry_run: bool = False) -> Dict[str, Any]:
        """Update table schema from model."""
        table_name = model_class.__tablename__
        
        logger.info("Updating table schema", table_name=table_name, dry_run=dry_run)
        
        try:
            # Get current table info
            table_info = await self.client.get_table_info(table_name)
            current_schema = [
                SchemaField(field["name"], field["type"], mode=field["mode"], description=field.get("description"))
                for field in table_info["schema"]
            ]
            
            # Get new schema from model
            new_schema = model_class.get_bigquery_schema()
            
            # Compare schemas
            differences = self.schema_manager.compare_schemas(current_schema, new_schema)
            
            if not differences["has_changes"]:
                logger.info("No schema changes detected", table_name=table_name)
                return {"changes": False, "differences": differences}
            
            # Validate compatibility
            is_compatible, errors = self.schema_manager.validate_schema_compatibility(current_schema, new_schema)
            
            if not is_compatible:
                logger.error("Schema changes are not compatible", table_name=table_name, errors=errors)
                raise MigrationError(f"Schema changes are not compatible: {errors}")
            
            if dry_run:
                logger.info("Dry run: Schema changes would be applied", table_name=table_name, differences=differences)
                return {"changes": True, "differences": differences, "dry_run": True}
            
            # Create migration record
            migration_record = MigrationRecord(
                migration_id=f"update_{table_name}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                table_name=table_name,
                operation="UPDATE_SCHEMA",
                started_at=datetime.utcnow(),
                schema_hash=self.schema_manager.calculate_schema_hash(new_schema)
            )
            
            migration_record.status = MigrationStatus.RUNNING
            await self._record_migration(migration_record)
            
            # Apply schema changes
            # Note: BigQuery supports adding fields, but not removing or modifying existing ones
            # For more complex changes, we would need to create a new table and migrate data
            
            # For now, we'll focus on adding new fields
            if differences["added_fields"]:
                await self._add_fields_to_table(table_name, differences["added_fields"])
            
            if differences["removed_fields"] or differences["modified_fields"]:
                logger.warning("Complex schema changes detected - manual intervention may be required",
                             table_name=table_name)
            
            # Update migration record
            migration_record.status = MigrationStatus.COMPLETED
            migration_record.completed_at = datetime.utcnow()
            await self._record_migration(migration_record)
            
            logger.info("Schema updated successfully", table_name=table_name)
            return {"changes": True, "differences": differences}
            
        except Exception as e:
            # Update migration record with error
            migration_record.status = MigrationStatus.FAILED
            migration_record.error_message = str(e)
            migration_record.completed_at = datetime.utcnow()
            await self._record_migration(migration_record)
            
            logger.error("Failed to update table schema", table_name=table_name, error=str(e))
            raise MigrationError(f"Failed to update table schema {table_name}: {str(e)}", e)
    
    async def _add_fields_to_table(self, table_name: str, fields: List[SchemaField]):
        """Add fields to an existing table."""
        logger.info("Adding fields to table", table_name=table_name, field_count=len(fields))
        
        try:
            # Get current table
            async with self.client.pool.get_client() as client:
                table_ref = client.dataset(self.client.config.dataset_id).table(table_name)
                table = client.get_table(table_ref)
                
                # Add new fields to schema
                new_schema = list(table.schema)
                new_schema.extend(fields)
                
                # Update table schema
                table.schema = new_schema
                client.update_table(table, ["schema"])
                
                logger.info("Fields added successfully", table_name=table_name)
                
        except Exception as e:
            logger.error("Failed to add fields to table", table_name=table_name, error=str(e))
            raise MigrationError(f"Failed to add fields to table {table_name}: {str(e)}", e)
    
    async def migrate_all_tables(self, force: bool = False) -> Dict[str, Any]:
        """Migrate all tables from models."""
        logger.info("Starting migration of all tables", force=force)
        
        results = {}
        errors = []
        
        for table_name, model_class in BIGQUERY_TABLE_MAPPING.items():
            try:
                logger.info("Migrating table", table_name=table_name)
                
                # Check if table exists
                exists = await self.client.table_exists(table_name)
                
                if not exists:
                    # Create new table
                    created = await self.create_table_from_model(model_class, force=force)
                    results[table_name] = {
                        "action": "created" if created else "exists",
                        "success": True
                    }
                else:
                    # Update existing table schema
                    update_result = await self.update_table_schema(model_class, dry_run=False)
                    results[table_name] = {
                        "action": "updated" if update_result["changes"] else "no_changes",
                        "success": True,
                        "differences": update_result.get("differences", {})
                    }
                
            except Exception as e:
                logger.error("Failed to migrate table", table_name=table_name, error=str(e))
                errors.append({"table": table_name, "error": str(e)})
                results[table_name] = {
                    "action": "failed",
                    "success": False,
                    "error": str(e)
                }
        
        success_count = sum(1 for r in results.values() if r["success"])
        total_count = len(results)
        
        logger.info(
            "Migration completed",
            success_count=success_count,
            total_count=total_count,
            error_count=len(errors)
        )
        
        return {
            "success": len(errors) == 0,
            "total_tables": total_count,
            "successful_tables": success_count,
            "failed_tables": len(errors),
            "results": results,
            "errors": errors
        }
    
    async def get_migration_history(self, table_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get migration history."""
        try:
            query = "SELECT * FROM `migration_history`"
            parameters = {}
            
            if table_name:
                query += " WHERE table_name = @table_name"
                parameters["table_name"] = table_name
            
            query += " ORDER BY started_at DESC LIMIT 100"
            
            results = await self.client.execute_query(query, parameters)
            return results
            
        except Exception as e:
            logger.error("Failed to get migration history", error=str(e))
            raise MigrationError(f"Failed to get migration history: {str(e)}", e)
    
    async def rollback_migration(self, migration_id: str) -> bool:
        """Rollback a migration (limited support)."""
        logger.warning("Rollback requested", migration_id=migration_id)
        
        # Note: BigQuery doesn't support true rollbacks
        # This would need to be implemented based on the specific migration type
        # For now, we'll just mark it as rolled back
        
        try:
            # Update migration record
            migration_record = MigrationRecord(
                migration_id=migration_id,
                table_name="unknown",
                operation="ROLLBACK",
                status=MigrationStatus.ROLLED_BACK,
                started_at=datetime.utcnow(),
                completed_at=datetime.utcnow()
            )
            
            await self._record_migration(migration_record)
            
            logger.info("Migration marked as rolled back", migration_id=migration_id)
            return True
            
        except Exception as e:
            logger.error("Failed to rollback migration", migration_id=migration_id, error=str(e))
            raise MigrationError(f"Failed to rollback migration {migration_id}: {str(e)}", e)
    
    async def _record_migration(self, migration_record: MigrationRecord):
        """Record migration in tracking table."""
        try:
            await self.client.streaming_insert(
                table_name="migration_history",
                rows=[migration_record.to_dict()]
            )
            
        except Exception as e:
            logger.error("Failed to record migration", migration_id=migration_record.migration_id, error=str(e))
            # Don't raise exception here as it would prevent the actual migration
    
    async def get_table_status(self) -> Dict[str, Any]:
        """Get status of all tables."""
        status = {}
        
        for table_name, model_class in BIGQUERY_TABLE_MAPPING.items():
            try:
                exists = await self.client.table_exists(table_name)
                
                if exists:
                    table_info = await self.client.get_table_info(table_name)
                    status[table_name] = {
                        "exists": True,
                        "rows": table_info.get("num_rows", 0),
                        "size_bytes": table_info.get("num_bytes", 0),
                        "created": table_info.get("created"),
                        "modified": table_info.get("modified"),
                        "partitioned": table_info.get("partitioning", {}).get("field") is not None,
                        "clustered": bool(table_info.get("clustering")),
                        "field_count": len(table_info.get("schema", []))
                    }
                else:
                    status[table_name] = {
                        "exists": False,
                        "model": model_class.__name__
                    }
                    
            except Exception as e:
                logger.error("Failed to get table status", table_name=table_name, error=str(e))
                status[table_name] = {
                    "exists": False,
                    "error": str(e)
                }
        
        return status
    
    async def validate_all_schemas(self) -> Dict[str, Any]:
        """Validate all table schemas against models."""
        validation_results = {}
        
        for table_name, model_class in BIGQUERY_TABLE_MAPPING.items():
            try:
                exists = await self.client.table_exists(table_name)
                
                if not exists:
                    validation_results[table_name] = {
                        "valid": False,
                        "error": "Table does not exist"
                    }
                    continue
                
                # Get current schema
                table_info = await self.client.get_table_info(table_name)
                current_schema = [
                    SchemaField(field["name"], field["type"], mode=field["mode"], description=field.get("description"))
                    for field in table_info["schema"]
                ]
                
                # Get expected schema
                expected_schema = model_class.get_bigquery_schema()
                
                # Compare schemas
                differences = self.schema_manager.compare_schemas(current_schema, expected_schema)
                is_compatible, errors = self.schema_manager.validate_schema_compatibility(current_schema, expected_schema)
                
                validation_results[table_name] = {
                    "valid": not differences["has_changes"],
                    "compatible": is_compatible,
                    "differences": differences,
                    "errors": errors
                }
                
            except Exception as e:
                logger.error("Failed to validate schema", table_name=table_name, error=str(e))
                validation_results[table_name] = {
                    "valid": False,
                    "error": str(e)
                }
        
        return validation_results


# Global migration manager instance
_migration_manager: Optional[MigrationManager] = None


async def get_migration_manager() -> MigrationManager:
    """Get the global migration manager instance."""
    global _migration_manager
    
    if _migration_manager is None:
        _migration_manager = MigrationManager()
        await _migration_manager.initialize()
    
    return _migration_manager


async def create_all_tables(force: bool = False) -> Dict[str, Any]:
    """Create all tables from models."""
    migration_manager = await get_migration_manager()
    return await migration_manager.migrate_all_tables(force=force)


async def get_table_status() -> Dict[str, Any]:
    """Get status of all tables."""
    migration_manager = await get_migration_manager()
    return await migration_manager.get_table_status()


async def validate_all_schemas() -> Dict[str, Any]:
    """Validate all table schemas."""
    migration_manager = await get_migration_manager()
    return await migration_manager.validate_all_schemas()