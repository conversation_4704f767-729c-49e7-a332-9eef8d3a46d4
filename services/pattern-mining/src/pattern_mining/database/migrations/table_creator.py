"""
Table Creator

Creates BigQuery tables with proper configuration and optimization.
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
import structlog

from google.cloud.bigquery import SchemaField, Table, TimePartitioning, TimePartitioningType
from google.cloud.exceptions import Conflict

from ..bigquery import BigQueryClient, get_bigquery_session

logger = structlog.get_logger(__name__)


class TableCreator:
    """Creates BigQuery tables with proper configuration."""
    
    def __init__(self, project_id: str, dataset_id: str):
        self.project_id = project_id
        self.dataset_id = dataset_id
        self._bigquery_client: Optional[BigQueryClient] = None
    
    async def get_bigquery_client(self) -> BigQueryClient:
        """Get BigQuery client."""
        if self._bigquery_client is None:
            async with get_bigquery_session() as client:
                self._bigquery_client = client
        return self._bigquery_client
    
    async def create_table(
        self,
        table_name: str,
        schema: List[<PERSON>hem<PERSON><PERSON><PERSON>],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create a BigQuery table with configuration."""
        logger.info(
            "Creating BigQuery table",
            table_name=table_name,
            schema_fields=len(schema)
        )
        
        try:
            client = await self.get_bigquery_client()
            
            # Create table with configuration
            table = await client.create_table(
                table_name=table_name,
                schema=schema,
                partition_field=config.get("partition_field"),
                clustering_fields=config.get("clustering_fields"),
                description=config.get("description"),
                expiration_days=config.get("expiration_days")
            )
            
            # Apply additional optimizations
            await self._apply_table_optimizations(table_name, config)
            
            logger.info(
                "Table created successfully",
                table_name=table_name,
                partition_field=config.get("partition_field"),
                clustering_fields=config.get("clustering_fields")
            )
            
            return {
                "action": "created",
                "table_name": table_name,
                "schema_fields": len(schema),
                "partition_field": config.get("partition_field"),
                "clustering_fields": config.get("clustering_fields"),
                "description": config.get("description")
            }
            
        except Conflict:
            logger.info("Table already exists", table_name=table_name)
            return {
                "action": "exists",
                "table_name": table_name,
                "message": "Table already exists"
            }
        except Exception as e:
            logger.error(
                "Failed to create table",
                table_name=table_name,
                error=str(e)
            )
            return {
                "action": "failed",
                "table_name": table_name,
                "error": str(e)
            }
    
    async def _apply_table_optimizations(
        self,
        table_name: str,
        config: Dict[str, Any]
    ) -> None:
        """Apply table optimizations."""
        try:
            client = await self.get_bigquery_client()
            
            # Create vector index for embedding tables
            if table_name == "pattern_embeddings":
                await self._create_vector_index(table_name)
            
            # Create search index for text search
            if table_name == "pattern_results":
                await self._create_search_index(table_name)
            
            # Create materialized views for performance
            if table_name in ["pattern_results", "repository_analysis"]:
                await self._create_materialized_views(table_name)
            
        except Exception as e:
            logger.warning(
                "Failed to apply table optimizations",
                table_name=table_name,
                error=str(e)
            )
    
    async def _create_vector_index(self, table_name: str) -> None:
        """Create vector index for embeddings."""
        try:
            client = await self.get_bigquery_client()
            
            # Create vector index query
            query = f"""
            CREATE VECTOR INDEX IF NOT EXISTS embedding_idx
            ON `{self.project_id}.{self.dataset_id}.{table_name}`(embedding)
            OPTIONS(
                distance_type='COSINE',
                tree_ah_index=true,
                index_type='IVF_FLAT',
                num_lists=1000
            )
            """
            
            await client.execute_query(query)
            
            logger.info(
                "Created vector index",
                table_name=table_name,
                index_name="embedding_idx"
            )
            
        except Exception as e:
            logger.warning(
                "Failed to create vector index",
                table_name=table_name,
                error=str(e)
            )
    
    async def _create_search_index(self, table_name: str) -> None:
        """Create search index for text search."""
        try:
            client = await self.get_bigquery_client()
            
            # Create search index query
            query = f"""
            CREATE SEARCH INDEX IF NOT EXISTS pattern_search_idx
            ON `{self.project_id}.{self.dataset_id}.{table_name}`(pattern_type, pattern_name, file_path)
            OPTIONS(
                analyzer='LOG_ANALYZER',
                data_types=['STRING']
            )
            """
            
            await client.execute_query(query)
            
            logger.info(
                "Created search index",
                table_name=table_name,
                index_name="pattern_search_idx"
            )
            
        except Exception as e:
            logger.warning(
                "Failed to create search index",
                table_name=table_name,
                error=str(e)
            )
    
    async def _create_materialized_views(self, table_name: str) -> None:
        """Create materialized views for performance."""
        try:
            client = await self.get_bigquery_client()
            
            if table_name == "pattern_results":
                # Daily pattern trends materialized view
                query = f"""
                CREATE MATERIALIZED VIEW IF NOT EXISTS `{self.project_id}.{self.dataset_id}.daily_pattern_trends`
                PARTITION BY trend_date
                CLUSTER BY pattern_type, repository_id
                OPTIONS(
                    enable_refresh=true,
                    refresh_interval_minutes=60,
                    max_staleness=INTERVAL 2 HOUR
                )
                AS
                SELECT 
                    DATE(detected_at) as trend_date,
                    pattern_type,
                    pattern_category,
                    COUNT(DISTINCT repository_id) as unique_repos,
                    COUNT(*) as total_detections,
                    AVG(confidence_score) as avg_confidence,
                    STDDEV(confidence_score) as stddev_confidence,
                    APPROX_QUANTILES(confidence_score, 100)[OFFSET(50)] as median_confidence,
                    APPROX_QUANTILES(confidence_score, 100)[OFFSET(95)] as p95_confidence,
                    AVG(processing_time_ms) as avg_processing_time,
                    SUM(file_size_bytes) / 1024 / 1024 as total_mb_processed
                FROM `{self.project_id}.{self.dataset_id}.{table_name}`
                WHERE detected_at >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
                GROUP BY trend_date, pattern_type, pattern_category
                """
                
                await client.execute_query(query)
                
                logger.info(
                    "Created materialized view",
                    table_name=table_name,
                    view_name="daily_pattern_trends"
                )
            
            elif table_name == "repository_analysis":
                # Repository summary materialized view
                query = f"""
                CREATE MATERIALIZED VIEW IF NOT EXISTS `{self.project_id}.{self.dataset_id}.repository_summary`
                CLUSTER BY repository_id
                OPTIONS(
                    enable_refresh=true,
                    refresh_interval_minutes=30,
                    max_staleness=INTERVAL 1 HOUR
                )
                AS
                SELECT 
                    repository_id,
                    COUNT(*) as total_analyses,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_analyses,
                    AVG(CASE WHEN status = 'completed' THEN patterns_detected ELSE NULL END) as avg_patterns,
                    AVG(CASE WHEN status = 'completed' THEN code_quality_score ELSE NULL END) as avg_quality,
                    AVG(CASE WHEN status = 'completed' THEN security_score ELSE NULL END) as avg_security,
                    MAX(created_at) as last_analysis,
                    ARRAY_AGG(
                        CASE WHEN status = 'completed' THEN primary_language ELSE NULL END
                        IGNORE NULLS
                        ORDER BY created_at DESC
                        LIMIT 1
                    )[OFFSET(0)] as primary_language
                FROM `{self.project_id}.{self.dataset_id}.{table_name}`
                WHERE created_at >= DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY)
                GROUP BY repository_id
                """
                
                await client.execute_query(query)
                
                logger.info(
                    "Created materialized view",
                    table_name=table_name,
                    view_name="repository_summary"
                )
            
        except Exception as e:
            logger.warning(
                "Failed to create materialized views",
                table_name=table_name,
                error=str(e)
            )
    
    async def create_dashboard_views(self) -> Dict[str, Any]:
        """Create dashboard views for BI Engine."""
        logger.info("Creating dashboard views")
        
        results = {}
        
        try:
            client = await self.get_bigquery_client()
            
            # Pattern dashboard view
            pattern_dashboard_query = f"""
            CREATE OR REPLACE VIEW `{self.project_id}.{self.dataset_id}.pattern_dashboard`
            OPTIONS(
                description="Optimized for BI Engine acceleration"
            )
            AS
            SELECT 
                repository_id,
                pattern_category,
                COUNT(*) as pattern_count,
                AVG(confidence_score) as avg_confidence,
                SUM(CASE WHEN severity = 'critical' THEN 1 ELSE 0 END) as critical_count,
                SUM(CASE WHEN severity = 'high' THEN 1 ELSE 0 END) as high_count,
                SUM(CASE WHEN severity = 'medium' THEN 1 ELSE 0 END) as medium_count,
                SUM(CASE WHEN severity = 'low' THEN 1 ELSE 0 END) as low_count,
                MAX(detected_at) as last_detection
            FROM `{self.project_id}.{self.dataset_id}.pattern_results`
            WHERE detected_at >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)
            GROUP BY repository_id, pattern_category
            """
            
            await client.execute_query(pattern_dashboard_query)
            results["pattern_dashboard"] = "created"
            
            # Analysis dashboard view
            analysis_dashboard_query = f"""
            CREATE OR REPLACE VIEW `{self.project_id}.{self.dataset_id}.analysis_dashboard`
            OPTIONS(
                description="Analysis metrics for dashboard"
            )
            AS
            SELECT 
                DATE(created_at) as analysis_date,
                analysis_type,
                COUNT(*) as total_analyses,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_analyses,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_analyses,
                AVG(CASE WHEN status = 'completed' THEN analysis_duration_ms ELSE NULL END) as avg_duration,
                AVG(CASE WHEN status = 'completed' THEN patterns_detected ELSE NULL END) as avg_patterns,
                AVG(CASE WHEN status = 'completed' THEN code_quality_score ELSE NULL END) as avg_quality
            FROM `{self.project_id}.{self.dataset_id}.repository_analysis`
            WHERE created_at >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
            GROUP BY analysis_date, analysis_type
            ORDER BY analysis_date DESC
            """
            
            await client.execute_query(analysis_dashboard_query)
            results["analysis_dashboard"] = "created"
            
            # Model performance dashboard view
            model_dashboard_query = f"""
            CREATE OR REPLACE VIEW `{self.project_id}.{self.dataset_id}.model_dashboard`
            OPTIONS(
                description="Model performance metrics"
            )
            AS
            SELECT 
                mr.model_name,
                mr.model_type,
                mr.version,
                mr.deployment_status,
                mr.precision,
                mr.recall,
                mr.f1_score,
                mr.inference_time_ms,
                mr.deployment_timestamp,
                COALESCE(
                    (SELECT COUNT(*) FROM `{self.project_id}.{self.dataset_id}.model_training_logs` mtl 
                     WHERE mtl.model_id = mr.model_id), 0
                ) as training_steps
            FROM `{self.project_id}.{self.dataset_id}.model_registry` mr
            ORDER BY mr.deployment_timestamp DESC
            """
            
            await client.execute_query(model_dashboard_query)
            results["model_dashboard"] = "created"
            
            logger.info(
                "Created dashboard views",
                views_created=len(results)
            )
            
            return {
                "success": True,
                "views_created": results
            }
            
        except Exception as e:
            logger.error(
                "Failed to create dashboard views",
                error=str(e)
            )
            return {
                "success": False,
                "error": str(e),
                "views_created": results
            }
    
    async def create_analytics_functions(self) -> Dict[str, Any]:
        """Create analytics UDFs for pattern mining."""
        logger.info("Creating analytics functions")
        
        results = {}
        
        try:
            client = await self.get_bigquery_client()
            
            # Pattern similarity function
            similarity_function = f"""
            CREATE OR REPLACE FUNCTION `{self.project_id}.{self.dataset_id}.calculate_pattern_similarity`(
                embedding1 ARRAY<FLOAT64>,
                embedding2 ARRAY<FLOAT64>
            )
            RETURNS FLOAT64
            LANGUAGE js AS '''
                if (!embedding1 || !embedding2 || embedding1.length !== embedding2.length) {{
                    return 0.0;
                }}
                
                let dot_product = 0.0;
                let norm1 = 0.0;
                let norm2 = 0.0;
                
                for (let i = 0; i < embedding1.length; i++) {{
                    dot_product += embedding1[i] * embedding2[i];
                    norm1 += embedding1[i] * embedding1[i];
                    norm2 += embedding2[i] * embedding2[i];
                }}
                
                if (norm1 === 0.0 || norm2 === 0.0) {{
                    return 0.0;
                }}
                
                return dot_product / (Math.sqrt(norm1) * Math.sqrt(norm2));
            ''';
            """
            
            await client.execute_query(similarity_function)
            results["calculate_pattern_similarity"] = "created"
            
            # Pattern confidence adjustment function
            confidence_function = f"""
            CREATE OR REPLACE FUNCTION `{self.project_id}.{self.dataset_id}.adjust_pattern_confidence`(
                base_confidence FLOAT64,
                user_feedback_score FLOAT64,
                feedback_count INT64
            )
            RETURNS FLOAT64
            LANGUAGE js AS '''
                if (base_confidence < 0 || base_confidence > 1) {{
                    return 0.0;
                }}
                
                if (feedback_count === 0) {{
                    return base_confidence;
                }}
                
                // Weight feedback based on number of responses
                let feedback_weight = Math.min(feedback_count / 10.0, 0.3);
                let adjusted_confidence = base_confidence * (1 - feedback_weight) + 
                                        user_feedback_score * feedback_weight;
                
                return Math.max(0.0, Math.min(1.0, adjusted_confidence));
            ''';
            """
            
            await client.execute_query(confidence_function)
            results["adjust_pattern_confidence"] = "created"
            
            # Pattern trend analysis function
            trend_function = f"""
            CREATE OR REPLACE FUNCTION `{self.project_id}.{self.dataset_id}.calculate_pattern_trend`(
                daily_counts ARRAY<INT64>
            )
            RETURNS STRUCT<
                trend_direction STRING,
                trend_strength FLOAT64,
                is_significant BOOL
            >
            LANGUAGE js AS '''
                if (!daily_counts || daily_counts.length < 3) {{
                    return {{
                        trend_direction: "unknown",
                        trend_strength: 0.0,
                        is_significant: false
                    }};
                }}
                
                // Calculate linear regression
                let n = daily_counts.length;
                let x_sum = 0, y_sum = 0, xy_sum = 0, x2_sum = 0;
                
                for (let i = 0; i < n; i++) {{
                    x_sum += i;
                    y_sum += daily_counts[i];
                    xy_sum += i * daily_counts[i];
                    x2_sum += i * i;
                }}
                
                let slope = (n * xy_sum - x_sum * y_sum) / (n * x2_sum - x_sum * x_sum);
                let y_mean = y_sum / n;
                
                // Determine trend direction and strength
                let trend_direction = slope > 0 ? "increasing" : 
                                    slope < 0 ? "decreasing" : "stable";
                let trend_strength = Math.abs(slope) / (y_mean + 1);
                let is_significant = trend_strength > 0.1 && n >= 7;
                
                return {{
                    trend_direction: trend_direction,
                    trend_strength: trend_strength,
                    is_significant: is_significant
                }};
            ''';
            """
            
            await client.execute_query(trend_function)
            results["calculate_pattern_trend"] = "created"
            
            logger.info(
                "Created analytics functions",
                functions_created=len(results)
            )
            
            return {
                "success": True,
                "functions_created": results
            }
            
        except Exception as e:
            logger.error(
                "Failed to create analytics functions",
                error=str(e)
            )
            return {
                "success": False,
                "error": str(e),
                "functions_created": results
            }
    
    async def create_stored_procedures(self) -> Dict[str, Any]:
        """Create stored procedures for common operations."""
        logger.info("Creating stored procedures")
        
        results = {}
        
        try:
            client = await self.get_bigquery_client()
            
            # Pattern cleanup procedure
            cleanup_procedure = f"""
            CREATE OR REPLACE PROCEDURE `{self.project_id}.{self.dataset_id}.cleanup_old_patterns`(
                retention_days INT64
            )
            BEGIN
                DECLARE rows_deleted INT64;
                
                -- Delete old patterns with low confidence
                DELETE FROM `{self.project_id}.{self.dataset_id}.pattern_results`
                WHERE detected_at < DATE_SUB(CURRENT_DATE(), INTERVAL retention_days DAY)
                    AND confidence_score < 0.3;
                
                SET rows_deleted = @@row_count;
                
                -- Log cleanup action
                INSERT INTO `{self.project_id}.{self.dataset_id}._migrations` (
                    migration_id,
                    table_name,
                    operation,
                    status,
                    started_at,
                    completed_at,
                    details
                )
                VALUES (
                    GENERATE_UUID(),
                    'pattern_results',
                    'cleanup',
                    'completed',
                    CURRENT_TIMESTAMP(),
                    CURRENT_TIMESTAMP(),
                    JSON_OBJECT('rows_deleted', rows_deleted, 'retention_days', retention_days)
                );
                
                SELECT CONCAT('Deleted ', CAST(rows_deleted AS STRING), ' old patterns') as result;
            END;
            """
            
            await client.execute_query(cleanup_procedure)
            results["cleanup_old_patterns"] = "created"
            
            # Pattern statistics procedure
            stats_procedure = f"""
            CREATE OR REPLACE PROCEDURE `{self.project_id}.{self.dataset_id}.calculate_pattern_statistics`(
                repository_id STRING,
                days INT64
            )
            BEGIN
                DECLARE start_date DATE;
                SET start_date = DATE_SUB(CURRENT_DATE(), INTERVAL days DAY);
                
                -- Create or update statistics summary
                CREATE OR REPLACE TABLE `{self.project_id}.{self.dataset_id}.pattern_statistics_temp` AS
                SELECT 
                    repository_id as repo_id,
                    pattern_type,
                    pattern_category,
                    COUNT(*) as pattern_count,
                    AVG(confidence_score) as avg_confidence,
                    STDDEV(confidence_score) as stddev_confidence,
                    MIN(detected_at) as first_detection,
                    MAX(detected_at) as last_detection,
                    ARRAY_AGG(
                        STRUCT(
                            DATE(detected_at) as date,
                            COUNT(*) as daily_count
                        )
                        ORDER BY DATE(detected_at)
                    ) as daily_counts
                FROM `{self.project_id}.{self.dataset_id}.pattern_results`
                WHERE (repository_id = repository_id OR repository_id IS NULL)
                    AND detected_at >= start_date
                GROUP BY repo_id, pattern_type, pattern_category;
                
                SELECT 'Pattern statistics calculated successfully' as result;
            END;
            """
            
            await client.execute_query(stats_procedure)
            results["calculate_pattern_statistics"] = "created"
            
            logger.info(
                "Created stored procedures",
                procedures_created=len(results)
            )
            
            return {
                "success": True,
                "procedures_created": results
            }
            
        except Exception as e:
            logger.error(
                "Failed to create stored procedures",
                error=str(e)
            )
            return {
                "success": False,
                "error": str(e),
                "procedures_created": results
            }
    
    async def setup_bi_engine_optimization(self) -> Dict[str, Any]:
        """Set up BI Engine optimization."""
        logger.info("Setting up BI Engine optimization")
        
        try:
            client = await self.get_bigquery_client()
            
            # Create BI Engine reservation
            reservation_query = f"""
            -- This would typically be done through the Cloud Console or gcloud CLI
            -- as BigQuery SQL doesn't support creating reservations directly
            SELECT 'BI Engine optimization setup requires manual configuration' as message
            """
            
            await client.execute_query(reservation_query)
            
            # Create optimized tables for BI Engine
            optimized_tables = [
                "pattern_dashboard",
                "analysis_dashboard", 
                "model_dashboard",
                "daily_pattern_trends",
                "repository_summary"
            ]
            
            return {
                "success": True,
                "message": "BI Engine optimization configured",
                "optimized_tables": optimized_tables,
                "note": "Manual BI Engine reservation configuration may be required"
            }
            
        except Exception as e:
            logger.error(
                "Failed to setup BI Engine optimization",
                error=str(e)
            )
            return {
                "success": False,
                "error": str(e)
            }