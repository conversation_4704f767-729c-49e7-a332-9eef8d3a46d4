"""
SQLAlchemy Database Models

SQLAlchemy models for BigQuery integration with proper schema mapping and serialization.
These models provide a bridge between SQLAlchemy ORM and BigQuery tables.
"""

import uuid
from datetime import datetime, date
from typing import Dict, Any, List, Optional, Union
from enum import Enum

from sqlalchemy import (
    Column,
    String,
    Integer,
    Float,
    Boolean,
    Text,
    DateTime,
    Date,
    JSON,
    ForeignKey,
    Index,
    UniqueConstraint,
    CheckConstraint,
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.ext.hybrid import hybrid_property
from google.cloud.bigquery import <PERSON>hemaField

from ..models.patterns import PatternType, SeverityLevel, DetectionType, PatternCategory, ConfidenceLevel

Base = declarative_base()


class PatternResult(Base):
    """Pattern detection results table."""
    
    __tablename__ = "pattern_results"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Detection metadata
    detection_id = Column(String(255), unique=True, nullable=False, index=True)
    repository_id = Column(String(255), nullable=False, index=True)
    file_path = Column(Text, nullable=False)
    
    # Pattern information
    pattern_id = Column(String(255), nullable=False, index=True)
    pattern_name = Column(String(255), nullable=False)
    pattern_type = Column(String(50), nullable=False, index=True)
    pattern_category = Column(String(50), nullable=False, index=True)
    
    # Detection results
    severity = Column(String(20), nullable=False, index=True)
    confidence = Column(Float, nullable=False, index=True)
    confidence_level = Column(String(20), nullable=False)
    
    # Location information
    line_start = Column(Integer, nullable=False)
    line_end = Column(Integer, nullable=False)
    column_start = Column(Integer, nullable=True)
    column_end = Column(Integer, nullable=True)
    
    # Context
    function_name = Column(String(255), nullable=True)
    class_name = Column(String(255), nullable=True)
    module_name = Column(String(255), nullable=True)
    
    # Detection metadata
    detection_method = Column(String(50), nullable=False)
    model_version = Column(String(50), nullable=True)
    language = Column(String(50), nullable=False, index=True)
    
    # Code metrics
    lines_of_code = Column(Integer, nullable=True)
    cyclomatic_complexity = Column(Float, nullable=True)
    
    # Processing metadata
    processing_time_ms = Column(Integer, nullable=True)
    detected_at = Column(DateTime, nullable=False, default=datetime.utcnow, index=True)
    
    # Partitioning field for BigQuery
    detection_date = Column(Date, nullable=False, default=date.today, index=True)
    
    # Validation and feedback
    validation_status = Column(String(50), nullable=True)
    feedback_score = Column(Float, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Constraints
    __table_args__ = (
        CheckConstraint('confidence >= 0.0 AND confidence <= 1.0', name='confidence_range'),
        CheckConstraint('feedback_score IS NULL OR (feedback_score >= 0.0 AND feedback_score <= 1.0)', name='feedback_score_range'),
        Index('idx_pattern_detection_main', 'repository_id', 'pattern_type', 'detection_date'),
        Index('idx_pattern_confidence', 'confidence', 'severity'),
        Index('idx_pattern_language_date', 'language', 'detection_date'),
    )
    
    def to_bigquery_dict(self) -> Dict[str, Any]:
        """Convert to BigQuery-compatible dictionary."""
        return {
            "detection_id": self.detection_id,
            "repository_id": self.repository_id,
            "file_path": self.file_path,
            "pattern_id": self.pattern_id,
            "pattern_name": self.pattern_name,
            "pattern_type": self.pattern_type,
            "pattern_category": self.pattern_category,
            "severity": self.severity,
            "confidence": self.confidence,
            "confidence_level": self.confidence_level,
            "line_start": self.line_start,
            "line_end": self.line_end,
            "column_start": self.column_start,
            "column_end": self.column_end,
            "function_name": self.function_name,
            "class_name": self.class_name,
            "module_name": self.module_name,
            "detection_method": self.detection_method,
            "model_version": self.model_version,
            "language": self.language,
            "lines_of_code": self.lines_of_code,
            "cyclomatic_complexity": self.cyclomatic_complexity,
            "processing_time_ms": self.processing_time_ms,
            "detected_at": self.detected_at.isoformat() if self.detected_at else None,
            "detection_date": self.detection_date.isoformat() if self.detection_date else None,
            "validation_status": self.validation_status,
            "feedback_score": self.feedback_score,
        }
    
    @classmethod
    def get_bigquery_schema(cls) -> List[SchemaField]:
        """Get BigQuery schema fields."""
        return [
            SchemaField("detection_id", "STRING", mode="REQUIRED"),
            SchemaField("repository_id", "STRING", mode="REQUIRED"),
            SchemaField("file_path", "STRING", mode="REQUIRED"),
            SchemaField("pattern_id", "STRING", mode="REQUIRED"),
            SchemaField("pattern_name", "STRING", mode="REQUIRED"),
            SchemaField("pattern_type", "STRING", mode="REQUIRED"),
            SchemaField("pattern_category", "STRING", mode="REQUIRED"),
            SchemaField("severity", "STRING", mode="REQUIRED"),
            SchemaField("confidence", "FLOAT", mode="REQUIRED"),
            SchemaField("confidence_level", "STRING", mode="REQUIRED"),
            SchemaField("line_start", "INTEGER", mode="REQUIRED"),
            SchemaField("line_end", "INTEGER", mode="REQUIRED"),
            SchemaField("column_start", "INTEGER", mode="NULLABLE"),
            SchemaField("column_end", "INTEGER", mode="NULLABLE"),
            SchemaField("function_name", "STRING", mode="NULLABLE"),
            SchemaField("class_name", "STRING", mode="NULLABLE"),
            SchemaField("module_name", "STRING", mode="NULLABLE"),
            SchemaField("detection_method", "STRING", mode="REQUIRED"),
            SchemaField("model_version", "STRING", mode="NULLABLE"),
            SchemaField("language", "STRING", mode="REQUIRED"),
            SchemaField("lines_of_code", "INTEGER", mode="NULLABLE"),
            SchemaField("cyclomatic_complexity", "FLOAT", mode="NULLABLE"),
            SchemaField("processing_time_ms", "INTEGER", mode="NULLABLE"),
            SchemaField("detected_at", "TIMESTAMP", mode="REQUIRED"),
            SchemaField("detection_date", "DATE", mode="REQUIRED"),
            SchemaField("validation_status", "STRING", mode="NULLABLE"),
            SchemaField("feedback_score", "FLOAT", mode="NULLABLE"),
        ]
    
    @classmethod
    def get_bigquery_table_config(cls) -> Dict[str, Any]:
        """Get BigQuery table configuration."""
        return {
            "partition_field": "detection_date",
            "clustering_fields": ["pattern_type", "severity", "language"],
            "description": "Pattern detection results with ML predictions",
            "expiration_days": 90,
        }


class PatternEmbedding(Base):
    """Pattern embeddings for similarity search."""
    
    __tablename__ = "pattern_embeddings"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Pattern information
    pattern_id = Column(String(255), nullable=False, index=True)
    pattern_type = Column(String(50), nullable=False, index=True)
    
    # Embedding data
    embedding = Column(JSON, nullable=False)  # Store as JSON array
    embedding_model = Column(String(100), nullable=False)
    embedding_version = Column(String(50), nullable=False)
    
    # Metadata for filtering
    language = Column(String(50), nullable=False, index=True)
    framework = Column(String(100), nullable=True)
    pattern_category = Column(String(50), nullable=False, index=True)
    
    # Vector search optimization
    embedding_norm = Column(Float, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Constraints
    __table_args__ = (
        Index('idx_embedding_pattern_type', 'pattern_type', 'embedding_model'),
        Index('idx_embedding_language', 'language', 'pattern_category'),
        UniqueConstraint('pattern_id', 'embedding_model', name='unique_pattern_embedding'),
    )
    
    def to_bigquery_dict(self) -> Dict[str, Any]:
        """Convert to BigQuery-compatible dictionary."""
        return {
            "pattern_id": self.pattern_id,
            "pattern_type": self.pattern_type,
            "embedding": self.embedding,
            "embedding_model": self.embedding_model,
            "embedding_version": self.embedding_version,
            "language": self.language,
            "framework": self.framework,
            "pattern_category": self.pattern_category,
            "embedding_norm": self.embedding_norm,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }
    
    @classmethod
    def get_bigquery_schema(cls) -> List[SchemaField]:
        """Get BigQuery schema fields."""
        return [
            SchemaField("pattern_id", "STRING", mode="REQUIRED"),
            SchemaField("pattern_type", "STRING", mode="REQUIRED"),
            SchemaField("embedding", "FLOAT", mode="REPEATED"),
            SchemaField("embedding_model", "STRING", mode="REQUIRED"),
            SchemaField("embedding_version", "STRING", mode="REQUIRED"),
            SchemaField("language", "STRING", mode="REQUIRED"),
            SchemaField("framework", "STRING", mode="NULLABLE"),
            SchemaField("pattern_category", "STRING", mode="REQUIRED"),
            SchemaField("embedding_norm", "FLOAT", mode="NULLABLE"),
            SchemaField("created_at", "TIMESTAMP", mode="REQUIRED"),
        ]
    
    @classmethod
    def get_bigquery_table_config(cls) -> Dict[str, Any]:
        """Get BigQuery table configuration."""
        return {
            "clustering_fields": ["pattern_type", "embedding_model"],
            "description": "Pattern embeddings for vector similarity search",
            "expiration_days": None,  # No expiration for embeddings
        }


class ModelRegistry(Base):
    """ML model registry and versions."""
    
    __tablename__ = "model_registry"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Model information
    model_id = Column(String(255), nullable=False, unique=True, index=True)
    model_name = Column(String(255), nullable=False)
    model_type = Column(String(100), nullable=False)
    version = Column(String(50), nullable=False)
    
    # Model Configuration
    architecture_json = Column(JSON, nullable=True)
    hyperparameters = Column(JSON, nullable=True)
    training_dataset_id = Column(String(255), nullable=True)
    
    # Performance Metrics
    precision = Column(Float, nullable=True)
    recall = Column(Float, nullable=True)
    f1_score = Column(Float, nullable=True)
    inference_time_ms = Column(Float, nullable=True)
    
    # Deployment Info
    deployment_status = Column(String(50), nullable=False, default="staging")
    deployment_timestamp = Column(DateTime, nullable=True)
    gpu_required = Column(Boolean, nullable=False, default=False)
    min_gpu_memory_gb = Column(Integer, nullable=True)
    
    # A/B Testing
    traffic_percentage = Column(Float, nullable=True)
    ab_test_group = Column(String(100), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Constraints
    __table_args__ = (
        CheckConstraint('precision IS NULL OR (precision >= 0.0 AND precision <= 1.0)', name='precision_range'),
        CheckConstraint('recall IS NULL OR (recall >= 0.0 AND recall <= 1.0)', name='recall_range'),
        CheckConstraint('f1_score IS NULL OR (f1_score >= 0.0 AND f1_score <= 1.0)', name='f1_score_range'),
        CheckConstraint('traffic_percentage IS NULL OR (traffic_percentage >= 0.0 AND traffic_percentage <= 1.0)', name='traffic_percentage_range'),
        Index('idx_model_status', 'deployment_status', 'model_type'),
        Index('idx_model_performance', 'f1_score', 'inference_time_ms'),
        UniqueConstraint('model_name', 'version', name='unique_model_version'),
    )
    
    def to_bigquery_dict(self) -> Dict[str, Any]:
        """Convert to BigQuery-compatible dictionary."""
        return {
            "model_id": self.model_id,
            "model_name": self.model_name,
            "model_type": self.model_type,
            "version": self.version,
            "architecture_json": self.architecture_json,
            "hyperparameters": self.hyperparameters,
            "training_dataset_id": self.training_dataset_id,
            "precision": self.precision,
            "recall": self.recall,
            "f1_score": self.f1_score,
            "inference_time_ms": self.inference_time_ms,
            "deployment_status": self.deployment_status,
            "deployment_timestamp": self.deployment_timestamp.isoformat() if self.deployment_timestamp else None,
            "gpu_required": self.gpu_required,
            "min_gpu_memory_gb": self.min_gpu_memory_gb,
            "traffic_percentage": self.traffic_percentage,
            "ab_test_group": self.ab_test_group,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
    
    @classmethod
    def get_bigquery_schema(cls) -> List[SchemaField]:
        """Get BigQuery schema fields."""
        return [
            SchemaField("model_id", "STRING", mode="REQUIRED"),
            SchemaField("model_name", "STRING", mode="REQUIRED"),
            SchemaField("model_type", "STRING", mode="REQUIRED"),
            SchemaField("version", "STRING", mode="REQUIRED"),
            SchemaField("architecture_json", "JSON", mode="NULLABLE"),
            SchemaField("hyperparameters", "JSON", mode="NULLABLE"),
            SchemaField("training_dataset_id", "STRING", mode="NULLABLE"),
            SchemaField("precision", "FLOAT", mode="NULLABLE"),
            SchemaField("recall", "FLOAT", mode="NULLABLE"),
            SchemaField("f1_score", "FLOAT", mode="NULLABLE"),
            SchemaField("inference_time_ms", "FLOAT", mode="NULLABLE"),
            SchemaField("deployment_status", "STRING", mode="REQUIRED"),
            SchemaField("deployment_timestamp", "TIMESTAMP", mode="NULLABLE"),
            SchemaField("gpu_required", "BOOLEAN", mode="REQUIRED"),
            SchemaField("min_gpu_memory_gb", "INTEGER", mode="NULLABLE"),
            SchemaField("traffic_percentage", "FLOAT", mode="NULLABLE"),
            SchemaField("ab_test_group", "STRING", mode="NULLABLE"),
            SchemaField("created_at", "TIMESTAMP", mode="REQUIRED"),
            SchemaField("updated_at", "TIMESTAMP", mode="REQUIRED"),
        ]
    
    @classmethod
    def get_bigquery_table_config(cls) -> Dict[str, Any]:
        """Get BigQuery table configuration."""
        return {
            "clustering_fields": ["model_type", "deployment_status"],
            "description": "ML model versions and performance metrics",
            "expiration_days": None,  # No expiration for model registry
        }


class RepositoryAnalysis(Base):
    """Repository analysis results and metrics."""
    
    __tablename__ = "repository_analysis"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Repository information
    repository_id = Column(String(255), nullable=False, index=True)
    repository_url = Column(String(500), nullable=False)
    repository_name = Column(String(255), nullable=False)
    owner = Column(String(255), nullable=False)
    
    # Analysis metadata
    analysis_id = Column(String(255), nullable=False, unique=True, index=True)
    analysis_type = Column(String(100), nullable=False)
    analysis_status = Column(String(50), nullable=False, default="pending")
    
    # Repository metrics
    total_files = Column(Integer, nullable=True)
    total_lines = Column(Integer, nullable=True)
    languages = Column(JSON, nullable=True)  # Store as JSON array
    primary_language = Column(String(50), nullable=True)
    
    # Analysis results
    patterns_detected = Column(Integer, nullable=True)
    critical_issues = Column(Integer, nullable=True)
    high_issues = Column(Integer, nullable=True)
    medium_issues = Column(Integer, nullable=True)
    low_issues = Column(Integer, nullable=True)
    
    # Quality metrics
    quality_score = Column(Float, nullable=True)
    maintainability_score = Column(Float, nullable=True)
    security_score = Column(Float, nullable=True)
    
    # Processing metadata
    processing_time_ms = Column(Integer, nullable=True)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # Partitioning field for BigQuery
    analysis_date = Column(Date, nullable=False, default=date.today, index=True)
    
    # Timestamps
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Constraints
    __table_args__ = (
        CheckConstraint('quality_score IS NULL OR (quality_score >= 0.0 AND quality_score <= 100.0)', name='quality_score_range'),
        CheckConstraint('maintainability_score IS NULL OR (maintainability_score >= 0.0 AND maintainability_score <= 100.0)', name='maintainability_score_range'),
        CheckConstraint('security_score IS NULL OR (security_score >= 0.0 AND security_score <= 100.0)', name='security_score_range'),
        Index('idx_repository_analysis_main', 'repository_id', 'analysis_status', 'analysis_date'),
        Index('idx_repository_quality', 'quality_score', 'analysis_date'),
        Index('idx_repository_language', 'primary_language', 'analysis_date'),
    )
    
    def to_bigquery_dict(self) -> Dict[str, Any]:
        """Convert to BigQuery-compatible dictionary."""
        return {
            "repository_id": self.repository_id,
            "repository_url": self.repository_url,
            "repository_name": self.repository_name,
            "owner": self.owner,
            "analysis_id": self.analysis_id,
            "analysis_type": self.analysis_type,
            "analysis_status": self.analysis_status,
            "total_files": self.total_files,
            "total_lines": self.total_lines,
            "languages": self.languages,
            "primary_language": self.primary_language,
            "patterns_detected": self.patterns_detected,
            "critical_issues": self.critical_issues,
            "high_issues": self.high_issues,
            "medium_issues": self.medium_issues,
            "low_issues": self.low_issues,
            "quality_score": self.quality_score,
            "maintainability_score": self.maintainability_score,
            "security_score": self.security_score,
            "processing_time_ms": self.processing_time_ms,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "analysis_date": self.analysis_date.isoformat() if self.analysis_date else None,
        }
    
    @classmethod
    def get_bigquery_schema(cls) -> List[SchemaField]:
        """Get BigQuery schema fields."""
        return [
            SchemaField("repository_id", "STRING", mode="REQUIRED"),
            SchemaField("repository_url", "STRING", mode="REQUIRED"),
            SchemaField("repository_name", "STRING", mode="REQUIRED"),
            SchemaField("owner", "STRING", mode="REQUIRED"),
            SchemaField("analysis_id", "STRING", mode="REQUIRED"),
            SchemaField("analysis_type", "STRING", mode="REQUIRED"),
            SchemaField("analysis_status", "STRING", mode="REQUIRED"),
            SchemaField("total_files", "INTEGER", mode="NULLABLE"),
            SchemaField("total_lines", "INTEGER", mode="NULLABLE"),
            SchemaField("languages", "STRING", mode="REPEATED"),
            SchemaField("primary_language", "STRING", mode="NULLABLE"),
            SchemaField("patterns_detected", "INTEGER", mode="NULLABLE"),
            SchemaField("critical_issues", "INTEGER", mode="NULLABLE"),
            SchemaField("high_issues", "INTEGER", mode="NULLABLE"),
            SchemaField("medium_issues", "INTEGER", mode="NULLABLE"),
            SchemaField("low_issues", "INTEGER", mode="NULLABLE"),
            SchemaField("quality_score", "FLOAT", mode="NULLABLE"),
            SchemaField("maintainability_score", "FLOAT", mode="NULLABLE"),
            SchemaField("security_score", "FLOAT", mode="NULLABLE"),
            SchemaField("processing_time_ms", "INTEGER", mode="NULLABLE"),
            SchemaField("started_at", "TIMESTAMP", mode="NULLABLE"),
            SchemaField("completed_at", "TIMESTAMP", mode="NULLABLE"),
            SchemaField("analysis_date", "DATE", mode="REQUIRED"),
        ]
    
    @classmethod
    def get_bigquery_table_config(cls) -> Dict[str, Any]:
        """Get BigQuery table configuration."""
        return {
            "partition_field": "analysis_date",
            "clustering_fields": ["analysis_status", "primary_language"],
            "description": "Repository analysis results and metrics",
            "expiration_days": 365,  # Keep for 1 year
        }


class FeatureVector(Base):
    """Feature vectors for ML model training and inference."""
    
    __tablename__ = "feature_vectors"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Feature metadata
    feature_id = Column(String(255), nullable=False, unique=True, index=True)
    repository_id = Column(String(255), nullable=False, index=True)
    file_path = Column(Text, nullable=False)
    language = Column(String(50), nullable=False, index=True)
    
    # Feature vectors (stored as JSON arrays)
    structural_features = Column(JSON, nullable=False)
    lexical_features = Column(JSON, nullable=False)
    semantic_features = Column(JSON, nullable=False)
    statistical_features = Column(JSON, nullable=False)
    
    # Feature metadata
    feature_count = Column(Integer, nullable=False)
    feature_version = Column(String(50), nullable=False)
    
    # Quality metrics
    feature_quality_score = Column(Float, nullable=False)
    extraction_success = Column(Boolean, nullable=False)
    
    # Code metrics
    lines_of_code = Column(Integer, nullable=False)
    ast_depth = Column(Integer, nullable=False)
    node_count = Column(Integer, nullable=False)
    
    # Processing metadata
    extraction_time_ms = Column(Integer, nullable=False)
    extracted_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    
    # Partitioning field for BigQuery
    extraction_date = Column(Date, nullable=False, default=date.today, index=True)
    
    # Timestamps
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Constraints
    __table_args__ = (
        CheckConstraint('feature_quality_score >= 0.0 AND feature_quality_score <= 1.0', name='feature_quality_score_range'),
        Index('idx_feature_language_date', 'language', 'extraction_date'),
        Index('idx_feature_repository', 'repository_id', 'feature_version'),
        Index('idx_feature_quality', 'feature_quality_score', 'extraction_success'),
    )
    
    def to_bigquery_dict(self) -> Dict[str, Any]:
        """Convert to BigQuery-compatible dictionary."""
        return {
            "feature_id": self.feature_id,
            "repository_id": self.repository_id,
            "file_path": self.file_path,
            "language": self.language,
            "structural_features": self.structural_features,
            "lexical_features": self.lexical_features,
            "semantic_features": self.semantic_features,
            "statistical_features": self.statistical_features,
            "feature_count": self.feature_count,
            "feature_version": self.feature_version,
            "feature_quality_score": self.feature_quality_score,
            "extraction_success": self.extraction_success,
            "lines_of_code": self.lines_of_code,
            "ast_depth": self.ast_depth,
            "node_count": self.node_count,
            "extraction_time_ms": self.extraction_time_ms,
            "extracted_at": self.extracted_at.isoformat() if self.extracted_at else None,
            "extraction_date": self.extraction_date.isoformat() if self.extraction_date else None,
        }
    
    @classmethod
    def get_bigquery_schema(cls) -> List[SchemaField]:
        """Get BigQuery schema fields."""
        return [
            SchemaField("feature_id", "STRING", mode="REQUIRED"),
            SchemaField("repository_id", "STRING", mode="REQUIRED"),
            SchemaField("file_path", "STRING", mode="REQUIRED"),
            SchemaField("language", "STRING", mode="REQUIRED"),
            SchemaField("structural_features", "JSON", mode="REQUIRED"),
            SchemaField("lexical_features", "JSON", mode="REQUIRED"),
            SchemaField("semantic_features", "JSON", mode="REQUIRED"),
            SchemaField("statistical_features", "JSON", mode="REQUIRED"),
            SchemaField("feature_count", "INTEGER", mode="REQUIRED"),
            SchemaField("feature_version", "STRING", mode="REQUIRED"),
            SchemaField("feature_quality_score", "FLOAT", mode="REQUIRED"),
            SchemaField("extraction_success", "BOOLEAN", mode="REQUIRED"),
            SchemaField("lines_of_code", "INTEGER", mode="REQUIRED"),
            SchemaField("ast_depth", "INTEGER", mode="REQUIRED"),
            SchemaField("node_count", "INTEGER", mode="REQUIRED"),
            SchemaField("extraction_time_ms", "INTEGER", mode="REQUIRED"),
            SchemaField("extracted_at", "TIMESTAMP", mode="REQUIRED"),
            SchemaField("extraction_date", "DATE", mode="REQUIRED"),
        ]
    
    @classmethod
    def get_bigquery_table_config(cls) -> Dict[str, Any]:
        """Get BigQuery table configuration."""
        return {
            "partition_field": "extraction_date",
            "clustering_fields": ["language", "feature_version"],
            "description": "Feature vectors for ML model training and inference",
            "expiration_days": 180,  # Keep for 6 months
        }


# Table mapping for BigQuery operations
BIGQUERY_TABLE_MAPPING = {
    "pattern_results": PatternResult,
    "pattern_embeddings": PatternEmbedding,
    "model_registry": ModelRegistry,
    "repository_analysis": RepositoryAnalysis,
    "feature_vectors": FeatureVector,
}