"""
Feedback Repository

Repository for user feedback on pattern detection results.
"""

from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
import uuid
from sqlalchemy import select, and_, or_, func, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from .base import BaseRepository, DatabaseError
from ..models import UserFeedback
from ..connection import get_database_session

logger = structlog.get_logger(__name__)


class FeedbackRepository(BaseRepository[UserFeedback]):
    """Repository for user feedback."""
    
    def __init__(self):
        super().__init__(UserFeedback)
    
    async def get_by_feedback_id(self, feedback_id: str) -> Optional[UserFeedback]:
        """Get feedback by feedback_id."""
        async with get_database_session() as session:
            try:
                result = await session.execute(
                    select(self.model_class).where(
                        self.model_class.feedback_id == feedback_id
                    )
                )
                return result.scalar_one_or_none()
            except Exception as e:
                logger.error(
                    "Error getting feedback by feedback_id",
                    feedback_id=feedback_id,
                    error=str(e)
                )
                raise DatabaseError(f"Get by feedback_id error: {str(e)}")
    
    async def get_by_pattern_id(
        self,
        pattern_id: str,
        limit: int = 100,
        offset: int = 0
    ) -> List[UserFeedback]:
        """Get feedback by pattern ID."""
        async with get_database_session() as session:
            try:
                query = select(self.model_class).where(
                    self.model_class.pattern_id == pattern_id
                ).order_by(desc(self.model_class.created_at)).limit(limit).offset(offset)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(
                    "Error getting feedback by pattern ID",
                    pattern_id=pattern_id,
                    error=str(e)
                )
                raise DatabaseError(f"Get by pattern ID error: {str(e)}")
    
    async def get_by_user_id(
        self,
        user_id: str,
        limit: int = 100,
        offset: int = 0,
        feedback_type: Optional[str] = None
    ) -> List[UserFeedback]:
        """Get feedback by user ID."""
        async with get_database_session() as session:
            try:
                query = select(self.model_class).where(
                    self.model_class.user_id == user_id
                )
                
                if feedback_type:
                    query = query.where(self.model_class.feedback_type == feedback_type)
                
                query = query.order_by(desc(self.model_class.created_at)).limit(limit).offset(offset)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(
                    "Error getting feedback by user ID",
                    user_id=user_id,
                    error=str(e)
                )
                raise DatabaseError(f"Get by user ID error: {str(e)}")
    
    async def get_by_feedback_type(
        self,
        feedback_type: str,
        limit: int = 100,
        offset: int = 0,
        repository_id: Optional[str] = None
    ) -> List[UserFeedback]:
        """Get feedback by type."""
        async with get_database_session() as session:
            try:
                query = select(self.model_class).where(
                    self.model_class.feedback_type == feedback_type
                )
                
                if repository_id:
                    query = query.where(self.model_class.repository_id == repository_id)
                
                query = query.order_by(desc(self.model_class.created_at)).limit(limit).offset(offset)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(
                    "Error getting feedback by type",
                    feedback_type=feedback_type,
                    error=str(e)
                )
                raise DatabaseError(f"Get by feedback type error: {str(e)}")
    
    async def get_by_repository(
        self,
        repository_id: str,
        limit: int = 100,
        offset: int = 0,
        feedback_type: Optional[str] = None
    ) -> List[UserFeedback]:
        """Get feedback by repository."""
        async with get_database_session() as session:
            try:
                query = select(self.model_class).where(
                    self.model_class.repository_id == repository_id
                )
                
                if feedback_type:
                    query = query.where(self.model_class.feedback_type == feedback_type)
                
                query = query.order_by(desc(self.model_class.created_at)).limit(limit).offset(offset)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(
                    "Error getting feedback by repository",
                    repository_id=repository_id,
                    error=str(e)
                )
                raise DatabaseError(f"Get by repository error: {str(e)}")
    
    async def submit_feedback(
        self,
        pattern_id: str,
        user_id: str,
        feedback_type: str,
        rating: Optional[int] = None,
        is_correct: Optional[bool] = None,
        is_useful: Optional[bool] = None,
        comment: Optional[str] = None,
        suggested_fix: Optional[str] = None,
        tags: Optional[List[str]] = None,
        session_id: Optional[str] = None,
        repository_id: Optional[str] = None
    ) -> UserFeedback:
        """Submit user feedback."""
        try:
            feedback_id = str(uuid.uuid4())
            
            feedback = UserFeedback(
                feedback_id=feedback_id,
                pattern_id=pattern_id,
                user_id=user_id,
                feedback_type=feedback_type,
                rating=rating,
                is_correct=is_correct,
                is_useful=is_useful,
                comment=comment,
                suggested_fix=suggested_fix,
                tags=tags,
                session_id=session_id,
                repository_id=repository_id
            )
            
            created_feedback = await self.create(feedback)
            
            logger.info(
                "Submitted feedback",
                feedback_id=feedback_id,
                pattern_id=pattern_id,
                user_id=user_id,
                feedback_type=feedback_type,
                rating=rating
            )
            
            return created_feedback
            
        except Exception as e:
            logger.error(
                "Error submitting feedback",
                pattern_id=pattern_id,
                user_id=user_id,
                error=str(e)
            )
            raise DatabaseError(f"Submit feedback error: {str(e)}")
    
    async def get_feedback_statistics(
        self,
        days: int = 30,
        repository_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get feedback statistics."""
        async with get_database_session() as session:
            try:
                cutoff_date = datetime.utcnow() - timedelta(days=days)
                
                base_query = select(self.model_class).where(
                    self.model_class.created_at >= cutoff_date
                )
                
                if repository_id:
                    base_query = base_query.where(
                        self.model_class.repository_id == repository_id
                    )
                
                # Total feedback
                total_count = await session.execute(
                    select(func.count()).select_from(base_query.subquery())
                )
                total_feedback = total_count.scalar() or 0
                
                # Feedback by type
                type_stats = await session.execute(
                    select(
                        self.model_class.feedback_type,
                        func.count().label('count')
                    ).select_from(base_query.subquery())
                    .group_by(self.model_class.feedback_type)
                )
                
                type_counts = {row.feedback_type: row.count for row in type_stats}
                
                # Average rating
                avg_rating = await session.execute(
                    select(func.avg(self.model_class.rating))
                    .select_from(base_query.subquery())
                    .where(self.model_class.rating.is_not(None))
                )
                average_rating = avg_rating.scalar() or 0.0
                
                # Correctness statistics
                correctness_stats = await session.execute(
                    select(
                        self.model_class.is_correct,
                        func.count().label('count')
                    ).select_from(base_query.subquery())
                    .where(self.model_class.is_correct.is_not(None))
                    .group_by(self.model_class.is_correct)
                )
                
                correctness_counts = {
                    str(row.is_correct): row.count 
                    for row in correctness_stats
                }
                
                # Usefulness statistics
                usefulness_stats = await session.execute(
                    select(
                        self.model_class.is_useful,
                        func.count().label('count')
                    ).select_from(base_query.subquery())
                    .where(self.model_class.is_useful.is_not(None))
                    .group_by(self.model_class.is_useful)
                )
                
                usefulness_counts = {
                    str(row.is_useful): row.count 
                    for row in usefulness_stats
                }
                
                # User engagement
                unique_users = await session.execute(
                    select(func.count(func.distinct(self.model_class.user_id)))
                    .select_from(base_query.subquery())
                )
                unique_user_count = unique_users.scalar() or 0
                
                # Patterns with feedback
                patterns_with_feedback = await session.execute(
                    select(func.count(func.distinct(self.model_class.pattern_id)))
                    .select_from(base_query.subquery())
                )
                patterns_count = patterns_with_feedback.scalar() or 0
                
                return {
                    "total_feedback": total_feedback,
                    "type_distribution": type_counts,
                    "average_rating": float(average_rating),
                    "correctness_distribution": correctness_counts,
                    "usefulness_distribution": usefulness_counts,
                    "unique_users": unique_user_count,
                    "patterns_with_feedback": patterns_count,
                    "time_period_days": days,
                    "repository_id": repository_id
                }
                
            except Exception as e:
                logger.error(
                    "Error getting feedback statistics",
                    repository_id=repository_id,
                    error=str(e)
                )
                raise DatabaseError(f"Get feedback statistics error: {str(e)}")
    
    async def get_pattern_feedback_summary(
        self,
        pattern_id: str
    ) -> Dict[str, Any]:
        """Get feedback summary for a pattern."""
        async with get_database_session() as session:
            try:
                # Get all feedback for pattern
                feedback_list = await self.get_by_pattern_id(pattern_id)
                
                if not feedback_list:
                    return {
                        "pattern_id": pattern_id,
                        "has_feedback": False,
                        "total_feedback": 0
                    }
                
                # Calculate statistics
                total_feedback = len(feedback_list)
                
                # Type distribution
                type_counts = {}
                for feedback in feedback_list:
                    feedback_type = feedback.feedback_type
                    type_counts[feedback_type] = type_counts.get(feedback_type, 0) + 1
                
                # Rating statistics
                ratings = [f.rating for f in feedback_list if f.rating is not None]
                avg_rating = sum(ratings) / len(ratings) if ratings else 0.0
                
                # Correctness
                correct_count = sum(1 for f in feedback_list if f.is_correct is True)
                incorrect_count = sum(1 for f in feedback_list if f.is_correct is False)
                
                # Usefulness
                useful_count = sum(1 for f in feedback_list if f.is_useful is True)
                not_useful_count = sum(1 for f in feedback_list if f.is_useful is False)
                
                # Comments and suggestions
                comments = [f.comment for f in feedback_list if f.comment]
                suggestions = [f.suggested_fix for f in feedback_list if f.suggested_fix]
                
                # Recent feedback
                recent_feedback = sorted(feedback_list, key=lambda x: x.created_at, reverse=True)[:5]
                
                return {
                    "pattern_id": pattern_id,
                    "has_feedback": True,
                    "total_feedback": total_feedback,
                    "type_distribution": type_counts,
                    "average_rating": avg_rating,
                    "correctness": {
                        "correct": correct_count,
                        "incorrect": incorrect_count,
                        "correctness_rate": correct_count / (correct_count + incorrect_count) if (correct_count + incorrect_count) > 0 else 0.0
                    },
                    "usefulness": {
                        "useful": useful_count,
                        "not_useful": not_useful_count,
                        "usefulness_rate": useful_count / (useful_count + not_useful_count) if (useful_count + not_useful_count) > 0 else 0.0
                    },
                    "engagement": {
                        "unique_users": len(set(f.user_id for f in feedback_list)),
                        "comments_count": len(comments),
                        "suggestions_count": len(suggestions)
                    },
                    "recent_feedback": [
                        {
                            "feedback_id": f.feedback_id,
                            "user_id": f.user_id,
                            "feedback_type": f.feedback_type,
                            "rating": f.rating,
                            "is_correct": f.is_correct,
                            "is_useful": f.is_useful,
                            "comment": f.comment,
                            "created_at": f.created_at
                        }
                        for f in recent_feedback
                    ]
                }
                
            except Exception as e:
                logger.error(
                    "Error getting pattern feedback summary",
                    pattern_id=pattern_id,
                    error=str(e)
                )
                raise DatabaseError(f"Get pattern feedback summary error: {str(e)}")
    
    async def get_user_feedback_history(
        self,
        user_id: str,
        limit: int = 100,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get user feedback history."""
        async with get_database_session() as session:
            try:
                cutoff_date = datetime.utcnow() - timedelta(days=days)
                
                # Get recent feedback
                recent_feedback = await session.execute(
                    select(self.model_class).where(
                        and_(
                            self.model_class.user_id == user_id,
                            self.model_class.created_at >= cutoff_date
                        )
                    ).order_by(desc(self.model_class.created_at)).limit(limit)
                )
                
                feedback_list = recent_feedback.scalars().all()
                
                if not feedback_list:
                    return {
                        "user_id": user_id,
                        "has_feedback": False,
                        "total_feedback": 0
                    }
                
                # Calculate statistics
                total_feedback = len(feedback_list)
                
                # Type distribution
                type_counts = {}
                for feedback in feedback_list:
                    feedback_type = feedback.feedback_type
                    type_counts[feedback_type] = type_counts.get(feedback_type, 0) + 1
                
                # Rating statistics
                ratings = [f.rating for f in feedback_list if f.rating is not None]
                avg_rating = sum(ratings) / len(ratings) if ratings else 0.0
                
                # Engagement metrics
                unique_patterns = len(set(f.pattern_id for f in feedback_list))
                unique_repositories = len(set(f.repository_id for f in feedback_list if f.repository_id))
                
                # Activity timeline
                activity_by_date = {}
                for feedback in feedback_list:
                    date_str = feedback.created_at.date().isoformat()
                    activity_by_date[date_str] = activity_by_date.get(date_str, 0) + 1
                
                return {
                    "user_id": user_id,
                    "has_feedback": True,
                    "total_feedback": total_feedback,
                    "type_distribution": type_counts,
                    "average_rating": avg_rating,
                    "engagement": {
                        "unique_patterns": unique_patterns,
                        "unique_repositories": unique_repositories,
                        "feedback_frequency": total_feedback / days
                    },
                    "activity_timeline": activity_by_date,
                    "time_period_days": days
                }
                
            except Exception as e:
                logger.error(
                    "Error getting user feedback history",
                    user_id=user_id,
                    error=str(e)
                )
                raise DatabaseError(f"Get user feedback history error: {str(e)}")
    
    async def get_feedback_trends(
        self,
        days: int = 30,
        repository_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get feedback trends over time."""
        try:
            # Use BigQuery for complex analytics
            conditions = []
            parameters = {"days": days}
            
            if repository_id:
                conditions.append("repository_id = @repository_id")
                parameters["repository_id"] = repository_id
            
            where_clause = ""
            if conditions:
                where_clause = f"AND {' AND '.join(conditions)}"
            
            query = f"""
            WITH daily_feedback AS (
                SELECT 
                    DATE(created_at) as feedback_date,
                    feedback_type,
                    COUNT(*) as feedback_count,
                    AVG(rating) as avg_rating,
                    SUM(CASE WHEN is_correct = true THEN 1 ELSE 0 END) as correct_count,
                    SUM(CASE WHEN is_correct = false THEN 1 ELSE 0 END) as incorrect_count,
                    SUM(CASE WHEN is_useful = true THEN 1 ELSE 0 END) as useful_count,
                    SUM(CASE WHEN is_useful = false THEN 1 ELSE 0 END) as not_useful_count
                FROM `{self.bigquery_table_name}`
                WHERE created_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL @days DAY)
                    {where_clause}
                GROUP BY DATE(created_at), feedback_type
            ),
            trend_analysis AS (
                SELECT 
                    feedback_date,
                    feedback_type,
                    feedback_count,
                    avg_rating,
                    correct_count,
                    incorrect_count,
                    useful_count,
                    not_useful_count,
                    -- Calculate 7-day moving average
                    AVG(feedback_count) OVER (
                        PARTITION BY feedback_type 
                        ORDER BY feedback_date 
                        ROWS BETWEEN 6 PRECEDING AND CURRENT ROW
                    ) as moving_avg_count,
                    -- Calculate trend
                    feedback_count - LAG(feedback_count) OVER (
                        PARTITION BY feedback_type 
                        ORDER BY feedback_date
                    ) as daily_change
                FROM daily_feedback
            )
            SELECT 
                feedback_date,
                feedback_type,
                feedback_count,
                avg_rating,
                correct_count,
                incorrect_count,
                useful_count,
                not_useful_count,
                moving_avg_count,
                daily_change
            FROM trend_analysis
            ORDER BY feedback_date, feedback_type
            """
            
            results = await self.query_bigquery(query, parameters)
            
            # Process results
            trends_by_type = {}
            for row in results:
                feedback_type = row["feedback_type"]
                if feedback_type not in trends_by_type:
                    trends_by_type[feedback_type] = []
                
                trends_by_type[feedback_type].append({
                    "date": row["feedback_date"],
                    "count": row["feedback_count"],
                    "avg_rating": row["avg_rating"],
                    "correct_count": row["correct_count"],
                    "incorrect_count": row["incorrect_count"],
                    "useful_count": row["useful_count"],
                    "not_useful_count": row["not_useful_count"],
                    "moving_avg_count": row["moving_avg_count"],
                    "daily_change": row["daily_change"]
                })
            
            return {
                "time_period_days": days,
                "repository_id": repository_id,
                "trends_by_type": trends_by_type,
                "generated_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(
                "Error getting feedback trends",
                repository_id=repository_id,
                error=str(e)
            )
            # Fallback to simpler PostgreSQL query
            return await self.get_feedback_statistics(days=days, repository_id=repository_id)
    
    async def get_top_contributors(
        self,
        days: int = 30,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get top feedback contributors."""
        async with get_database_session() as session:
            try:
                cutoff_date = datetime.utcnow() - timedelta(days=days)
                
                contributors = await session.execute(
                    select(
                        self.model_class.user_id,
                        func.count().label('feedback_count'),
                        func.avg(self.model_class.rating).label('avg_rating'),
                        func.count(func.distinct(self.model_class.pattern_id)).label('unique_patterns'),
                        func.count(self.model_class.comment).label('comments_count'),
                        func.count(self.model_class.suggested_fix).label('suggestions_count')
                    ).where(
                        self.model_class.created_at >= cutoff_date
                    ).group_by(self.model_class.user_id).order_by(desc('feedback_count')).limit(limit)
                )
                
                return [
                    {
                        "user_id": row.user_id,
                        "feedback_count": row.feedback_count,
                        "average_rating": float(row.avg_rating) if row.avg_rating else 0.0,
                        "unique_patterns": row.unique_patterns,
                        "comments_count": row.comments_count,
                        "suggestions_count": row.suggestions_count
                    }
                    for row in contributors
                ]
                
            except Exception as e:
                logger.error(
                    "Error getting top contributors",
                    error=str(e)
                )
                raise DatabaseError(f"Get top contributors error: {str(e)}")
    
    async def cleanup_old_feedback(
        self,
        days: int = 365
    ) -> int:
        """Clean up old feedback."""
        async with get_database_session() as session:
            try:
                cutoff_date = datetime.utcnow() - timedelta(days=days)
                
                # Get old feedback
                old_feedback = await session.execute(
                    select(self.model_class).where(
                        self.model_class.created_at < cutoff_date
                    )
                )
                
                feedback_to_delete = old_feedback.scalars().all()
                
                if feedback_to_delete:
                    for feedback in feedback_to_delete:
                        await session.delete(feedback)
                    
                    await session.commit()
                    
                    logger.info(
                        "Cleaned up old feedback",
                        count=len(feedback_to_delete),
                        days=days
                    )
                    
                    return len(feedback_to_delete)
                
                return 0
                
            except Exception as e:
                await session.rollback()
                logger.error(
                    "Error cleaning up old feedback",
                    error=str(e)
                )
                raise DatabaseError(f"Cleanup error: {str(e)}")