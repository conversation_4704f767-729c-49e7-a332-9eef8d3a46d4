"""
Pattern Repository

Repository for pattern detection operations with BigQuery analytics.
"""

from typing import List, Dict, Any, Optional, Union
from datetime import datetime, date, timedelta
import uuid

from sqlalchemy import select, func, and_, or_, desc, asc
from sqlalchemy.orm import selectinload
import structlog

from .base import BaseRepository, DatabaseError, RepositoryError
from ..models import PatternResult
from ...models.patterns import PatternType, SeverityLevel, DetectionType, PatternCategory, ConfidenceLevel
from ...models.api import PatternDetectionResponse, PatternAnalyticsResponse

logger = structlog.get_logger(__name__)


class PatternRepository(BaseRepository[PatternResult]):
    """Repository for pattern detection operations."""
    
    def __init__(self):
        super().__init__(PatternResult)
    
    async def create_pattern_detection(
        self,
        repository_id: str,
        file_path: str,
        pattern_data: Dict[str, Any]
    ) -> PatternResult:
        """Create a new pattern detection record."""
        try:
            # Generate unique IDs
            detection_id = str(uuid.uuid4())
            pattern_id = pattern_data.get('pattern_id', str(uuid.uuid4()))
            
            # Create pattern result
            pattern_result = PatternResult(
                detection_id=detection_id,
                repository_id=repository_id,
                file_path=file_path,
                pattern_id=pattern_id,
                pattern_name=pattern_data['pattern_name'],
                pattern_type=pattern_data['pattern_type'],
                pattern_category=pattern_data['pattern_category'],
                severity=pattern_data['severity'],
                confidence=pattern_data['confidence'],
                confidence_level=pattern_data['confidence_level'],
                line_start=pattern_data['line_start'],
                line_end=pattern_data['line_end'],
                column_start=pattern_data.get('column_start'),
                column_end=pattern_data.get('column_end'),
                function_name=pattern_data.get('function_name'),
                class_name=pattern_data.get('class_name'),
                module_name=pattern_data.get('module_name'),
                detection_method=pattern_data['detection_method'],
                model_version=pattern_data.get('model_version'),
                language=pattern_data['language'],
                lines_of_code=pattern_data.get('lines_of_code'),
                cyclomatic_complexity=pattern_data.get('cyclomatic_complexity'),
                processing_time_ms=pattern_data.get('processing_time_ms'),
                detected_at=datetime.utcnow(),
                detection_date=date.today(),
                validation_status=pattern_data.get('validation_status'),
                feedback_score=pattern_data.get('feedback_score'),
            )
            
            # Save to PostgreSQL
            created_pattern = await self.create(pattern_result)
            
            # Insert to BigQuery for analytics
            await self.insert_to_bigquery([created_pattern])
            
            logger.info(
                "Created pattern detection",
                detection_id=detection_id,
                pattern_type=pattern_data['pattern_type'],
                repository_id=repository_id
            )
            
            return created_pattern
            
        except Exception as e:
            logger.error(
                "Error creating pattern detection",
                error=str(e),
                repository_id=repository_id,
                pattern_type=pattern_data.get('pattern_type')
            )
            raise RepositoryError(f"Failed to create pattern detection: {str(e)}")
    
    async def get_patterns_by_repository(
        self,
        repository_id: str,
        limit: int = 100,
        offset: int = 0,
        pattern_type: Optional[str] = None,
        severity: Optional[str] = None,
        confidence_threshold: Optional[float] = None,
        date_from: Optional[date] = None,
        date_to: Optional[date] = None
    ) -> List[PatternResult]:
        """Get patterns for a specific repository with filters."""
        try:
            # Build filter conditions
            filters = {"repository_id": repository_id}
            
            if pattern_type:
                filters["pattern_type"] = pattern_type
            if severity:
                filters["severity"] = severity
            if confidence_threshold:
                filters["confidence__gte"] = confidence_threshold
            if date_from:
                filters["detection_date__gte"] = date_from
            if date_to:
                filters["detection_date__lte"] = date_to
            
            # Use the base filter method
            return await self.filter(**filters)
            
        except Exception as e:
            logger.error(
                "Error getting patterns by repository",
                error=str(e),
                repository_id=repository_id
            )
            raise RepositoryError(f"Failed to get patterns by repository: {str(e)}")
    
    async def get_pattern_analytics(
        self,
        repository_id: Optional[str] = None,
        date_from: Optional[date] = None,
        date_to: Optional[date] = None
    ) -> Dict[str, Any]:
        """Get pattern analytics from BigQuery."""
        try:
            # Build query conditions
            conditions = []
            parameters = {}
            
            if repository_id:
                conditions.append("repository_id = @repository_id")
                parameters["repository_id"] = repository_id
            
            if date_from:
                conditions.append("detection_date >= @date_from")
                parameters["date_from"] = date_from
            
            if date_to:
                conditions.append("detection_date <= @date_to")
                parameters["date_to"] = date_to
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            # Main analytics query
            query = f"""
            SELECT
                COUNT(*) as total_patterns,
                COUNT(DISTINCT pattern_type) as unique_pattern_types,
                AVG(confidence) as avg_confidence,
                
                -- Severity distribution
                COUNTIF(severity = 'critical') as critical_count,
                COUNTIF(severity = 'high') as high_count,
                COUNTIF(severity = 'medium') as medium_count,
                COUNTIF(severity = 'low') as low_count,
                
                -- Pattern type distribution
                COUNTIF(pattern_category = 'design_pattern') as design_patterns,
                COUNTIF(pattern_category = 'anti_pattern') as anti_patterns,
                COUNTIF(pattern_category = 'code_smell') as code_smells,
                COUNTIF(pattern_category = 'security_issue') as security_issues,
                COUNTIF(pattern_category = 'performance_issue') as performance_issues,
                
                -- Language distribution
                ARRAY_AGG(DISTINCT language) as languages,
                
                -- Quality metrics
                AVG(processing_time_ms) as avg_processing_time,
                COUNT(DISTINCT repository_id) as unique_repositories,
                COUNT(DISTINCT file_path) as unique_files
                
            FROM `{self.bigquery_table_name}`
            WHERE {where_clause}
            """
            
            # Execute query
            results = await self.query_bigquery(query, parameters)
            
            if not results:
                return {
                    "total_patterns": 0,
                    "unique_pattern_types": 0,
                    "avg_confidence": 0.0,
                    "critical_count": 0,
                    "high_count": 0,
                    "medium_count": 0,
                    "low_count": 0,
                    "design_patterns": 0,
                    "anti_patterns": 0,
                    "code_smells": 0,
                    "security_issues": 0,
                    "performance_issues": 0,
                    "languages": [],
                    "avg_processing_time": 0.0,
                    "unique_repositories": 0,
                    "unique_files": 0
                }
            
            result = results[0]
            
            return {
                "total_patterns": result.get('total_patterns', 0),
                "unique_pattern_types": result.get('unique_pattern_types', 0),
                "avg_confidence": result.get('avg_confidence', 0.0),
                "critical_count": result.get('critical_count', 0),
                "high_count": result.get('high_count', 0),
                "medium_count": result.get('medium_count', 0),
                "low_count": result.get('low_count', 0),
                "design_patterns": result.get('design_patterns', 0),
                "anti_patterns": result.get('anti_patterns', 0),
                "code_smells": result.get('code_smells', 0),
                "security_issues": result.get('security_issues', 0),
                "performance_issues": result.get('performance_issues', 0),
                "languages": result.get('languages', []),
                "avg_processing_time": result.get('avg_processing_time', 0.0),
                "unique_repositories": result.get('unique_repositories', 0),
                "unique_files": result.get('unique_files', 0)
            }
            
        except Exception as e:
            logger.error(
                "Error getting pattern analytics",
                error=str(e),
                repository_id=repository_id
            )
            raise RepositoryError(f"Failed to get pattern analytics: {str(e)}")
    
    async def get_pattern_trends(
        self,
        repository_id: Optional[str] = None,
        days: int = 30,
        group_by: str = 'day'
    ) -> List[Dict[str, Any]]:
        """Get pattern detection trends over time."""
        try:
            # Build query conditions
            conditions = []
            parameters = {}
            
            if repository_id:
                conditions.append("repository_id = @repository_id")
                parameters["repository_id"] = repository_id
            
            # Date range
            conditions.append("detection_date >= @date_from")
            parameters["date_from"] = date.today() - timedelta(days=days)
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            # Group by clause
            if group_by == 'day':
                group_clause = "DATE(detected_at)"
                date_format = "DATE(detected_at)"
            elif group_by == 'week':
                group_clause = "DATE_TRUNC(DATE(detected_at), WEEK)"
                date_format = "DATE_TRUNC(DATE(detected_at), WEEK)"
            elif group_by == 'month':
                group_clause = "DATE_TRUNC(DATE(detected_at), MONTH)"
                date_format = "DATE_TRUNC(DATE(detected_at), MONTH)"
            else:
                raise ValueError(f"Invalid group_by value: {group_by}")
            
            query = f"""
            SELECT
                {date_format} as date,
                COUNT(*) as total_patterns,
                AVG(confidence) as avg_confidence,
                COUNT(DISTINCT pattern_type) as unique_pattern_types,
                
                -- Severity counts
                COUNTIF(severity = 'critical') as critical_count,
                COUNTIF(severity = 'high') as high_count,
                COUNTIF(severity = 'medium') as medium_count,
                COUNTIF(severity = 'low') as low_count,
                
                -- Top pattern types
                ARRAY_AGG(
                    STRUCT(pattern_type, COUNT(*) as count)
                    ORDER BY COUNT(*) DESC
                    LIMIT 5
                ) as top_pattern_types
                
            FROM `{self.bigquery_table_name}`
            WHERE {where_clause}
            GROUP BY {group_clause}
            ORDER BY date DESC
            """
            
            results = await self.query_bigquery(query, parameters)
            
            logger.info(
                "Retrieved pattern trends",
                repository_id=repository_id,
                days=days,
                group_by=group_by,
                result_count=len(results)
            )
            
            return results
            
        except Exception as e:
            logger.error(
                "Error getting pattern trends",
                error=str(e),
                repository_id=repository_id,
                days=days
            )
            raise RepositoryError(f"Failed to get pattern trends: {str(e)}")
    
    async def get_similar_patterns(
        self,
        pattern_id: str,
        limit: int = 10,
        similarity_threshold: float = 0.8
    ) -> List[Dict[str, Any]]:
        """Get similar patterns using vector similarity."""
        try:
            # Get the reference pattern
            reference_pattern = await self.get_by_id(pattern_id)
            if not reference_pattern:
                raise RepositoryError(f"Pattern {pattern_id} not found")
            
            # Find similar patterns using pattern metadata
            query = f"""
            SELECT
                detection_id,
                pattern_name,
                pattern_type,
                pattern_category,
                severity,
                confidence,
                language,
                file_path,
                repository_id,
                detected_at,
                
                -- Simple similarity score based on metadata
                (
                    CASE WHEN pattern_type = @pattern_type THEN 0.4 ELSE 0.0 END +
                    CASE WHEN pattern_category = @pattern_category THEN 0.3 ELSE 0.0 END +
                    CASE WHEN language = @language THEN 0.2 ELSE 0.0 END +
                    CASE WHEN severity = @severity THEN 0.1 ELSE 0.0 END
                ) as similarity_score
                
            FROM `{self.bigquery_table_name}`
            WHERE 
                detection_id != @pattern_id
                AND (
                    pattern_type = @pattern_type
                    OR pattern_category = @pattern_category
                    OR language = @language
                )
            HAVING similarity_score >= @similarity_threshold
            ORDER BY similarity_score DESC, confidence DESC
            LIMIT @limit
            """
            
            parameters = {
                "pattern_id": pattern_id,
                "pattern_type": reference_pattern.pattern_type,
                "pattern_category": reference_pattern.pattern_category,
                "language": reference_pattern.language,
                "severity": reference_pattern.severity,
                "similarity_threshold": similarity_threshold,
                "limit": limit
            }
            
            results = await self.query_bigquery(query, parameters)
            
            logger.info(
                "Retrieved similar patterns",
                pattern_id=pattern_id,
                result_count=len(results),
                similarity_threshold=similarity_threshold
            )
            
            return results
            
        except Exception as e:
            logger.error(
                "Error getting similar patterns",
                error=str(e),
                pattern_id=pattern_id
            )
            raise RepositoryError(f"Failed to get similar patterns: {str(e)}")
    
    async def update_pattern_validation(
        self,
        detection_id: str,
        validation_status: str,
        feedback_score: Optional[float] = None
    ) -> Optional[PatternResult]:
        """Update pattern validation status and feedback."""
        try:
            # Update in PostgreSQL
            update_data = {"validation_status": validation_status}
            if feedback_score is not None:
                update_data["feedback_score"] = feedback_score
            
            updated_pattern = await self.update(detection_id, **update_data)
            
            if updated_pattern:
                # Update in BigQuery as well
                await self.insert_to_bigquery([updated_pattern])
                
                logger.info(
                    "Updated pattern validation",
                    detection_id=detection_id,
                    validation_status=validation_status,
                    feedback_score=feedback_score
                )
            
            return updated_pattern
            
        except Exception as e:
            logger.error(
                "Error updating pattern validation",
                error=str(e),
                detection_id=detection_id
            )
            raise RepositoryError(f"Failed to update pattern validation: {str(e)}")
    
    async def batch_create_patterns(
        self,
        repository_id: str,
        patterns_data: List[Dict[str, Any]]
    ) -> List[PatternResult]:
        """Create multiple pattern detections in batch."""
        try:
            pattern_results = []
            
            for pattern_data in patterns_data:
                # Generate unique IDs
                detection_id = str(uuid.uuid4())
                pattern_id = pattern_data.get('pattern_id', str(uuid.uuid4()))
                
                pattern_result = PatternResult(
                    detection_id=detection_id,
                    repository_id=repository_id,
                    file_path=pattern_data['file_path'],
                    pattern_id=pattern_id,
                    pattern_name=pattern_data['pattern_name'],
                    pattern_type=pattern_data['pattern_type'],
                    pattern_category=pattern_data['pattern_category'],
                    severity=pattern_data['severity'],
                    confidence=pattern_data['confidence'],
                    confidence_level=pattern_data['confidence_level'],
                    line_start=pattern_data['line_start'],
                    line_end=pattern_data['line_end'],
                    column_start=pattern_data.get('column_start'),
                    column_end=pattern_data.get('column_end'),
                    function_name=pattern_data.get('function_name'),
                    class_name=pattern_data.get('class_name'),
                    module_name=pattern_data.get('module_name'),
                    detection_method=pattern_data['detection_method'],
                    model_version=pattern_data.get('model_version'),
                    language=pattern_data['language'],
                    lines_of_code=pattern_data.get('lines_of_code'),
                    cyclomatic_complexity=pattern_data.get('cyclomatic_complexity'),
                    processing_time_ms=pattern_data.get('processing_time_ms'),
                    detected_at=datetime.utcnow(),
                    detection_date=date.today(),
                    validation_status=pattern_data.get('validation_status'),
                    feedback_score=pattern_data.get('feedback_score'),
                )
                
                pattern_results.append(pattern_result)
            
            # Batch create in PostgreSQL
            created_patterns = await self.batch_create(pattern_results)
            
            # Insert to BigQuery for analytics
            await self.insert_to_bigquery(created_patterns)
            
            logger.info(
                "Batch created pattern detections",
                repository_id=repository_id,
                pattern_count=len(created_patterns)
            )
            
            return created_patterns
            
        except Exception as e:
            logger.error(
                "Error batch creating patterns",
                error=str(e),
                repository_id=repository_id,
                pattern_count=len(patterns_data)
            )
            raise RepositoryError(f"Failed to batch create patterns: {str(e)}")
    
    async def get_pattern_statistics(
        self,
        repository_id: Optional[str] = None,
        date_from: Optional[date] = None,
        date_to: Optional[date] = None
    ) -> Dict[str, Any]:
        """Get comprehensive pattern statistics."""
        try:
            # Build query conditions
            conditions = []
            parameters = {}
            
            if repository_id:
                conditions.append("repository_id = @repository_id")
                parameters["repository_id"] = repository_id
            
            if date_from:
                conditions.append("detection_date >= @date_from")
                parameters["date_from"] = date_from
            
            if date_to:
                conditions.append("detection_date <= @date_to")
                parameters["date_to"] = date_to
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            # Comprehensive statistics query
            query = f"""
            WITH base_stats AS (
                SELECT
                    COUNT(*) as total_patterns,
                    COUNT(DISTINCT pattern_type) as unique_pattern_types,
                    COUNT(DISTINCT repository_id) as unique_repositories,
                    COUNT(DISTINCT file_path) as unique_files,
                    COUNT(DISTINCT language) as unique_languages,
                    
                    AVG(confidence) as avg_confidence,
                    STDDEV(confidence) as stddev_confidence,
                    MIN(confidence) as min_confidence,
                    MAX(confidence) as max_confidence,
                    
                    AVG(processing_time_ms) as avg_processing_time,
                    MAX(processing_time_ms) as max_processing_time,
                    MIN(processing_time_ms) as min_processing_time,
                    
                    AVG(lines_of_code) as avg_lines_of_code,
                    AVG(cyclomatic_complexity) as avg_complexity
                    
                FROM `{self.bigquery_table_name}`
                WHERE {where_clause}
            ),
            severity_stats AS (
                SELECT
                    severity,
                    COUNT(*) as count,
                    AVG(confidence) as avg_confidence
                FROM `{self.bigquery_table_name}`
                WHERE {where_clause}
                GROUP BY severity
            ),
            pattern_type_stats AS (
                SELECT
                    pattern_type,
                    COUNT(*) as count,
                    AVG(confidence) as avg_confidence
                FROM `{self.bigquery_table_name}`
                WHERE {where_clause}
                GROUP BY pattern_type
                ORDER BY count DESC
            ),
            language_stats AS (
                SELECT
                    language,
                    COUNT(*) as count,
                    AVG(confidence) as avg_confidence
                FROM `{self.bigquery_table_name}`
                WHERE {where_clause}
                GROUP BY language
                ORDER BY count DESC
            )
            
            SELECT 
                b.*,
                ARRAY_AGG(
                    STRUCT(
                        s.severity,
                        s.count,
                        s.avg_confidence
                    )
                ) as severity_breakdown,
                ARRAY_AGG(
                    STRUCT(
                        p.pattern_type,
                        p.count,
                        p.avg_confidence
                    )
                ) as pattern_type_breakdown,
                ARRAY_AGG(
                    STRUCT(
                        l.language,
                        l.count,
                        l.avg_confidence
                    )
                ) as language_breakdown
            FROM base_stats b
            CROSS JOIN severity_stats s
            CROSS JOIN pattern_type_stats p
            CROSS JOIN language_stats l
            GROUP BY b.total_patterns, b.unique_pattern_types, b.unique_repositories, 
                     b.unique_files, b.unique_languages, b.avg_confidence, b.stddev_confidence,
                     b.min_confidence, b.max_confidence, b.avg_processing_time, 
                     b.max_processing_time, b.min_processing_time, b.avg_lines_of_code, 
                     b.avg_complexity
            """
            
            results = await self.query_bigquery(query, parameters)
            
            if not results:
                return {
                    "total_patterns": 0,
                    "unique_pattern_types": 0,
                    "unique_repositories": 0,
                    "unique_files": 0,
                    "unique_languages": 0,
                    "confidence_stats": {},
                    "processing_stats": {},
                    "code_metrics": {},
                    "severity_breakdown": [],
                    "pattern_type_breakdown": [],
                    "language_breakdown": []
                }
            
            result = results[0]
            
            return {
                "total_patterns": result.get("total_patterns", 0),
                "unique_pattern_types": result.get("unique_pattern_types", 0),
                "unique_repositories": result.get("unique_repositories", 0),
                "unique_files": result.get("unique_files", 0),
                "unique_languages": result.get("unique_languages", 0),
                "confidence_stats": {
                    "avg": result.get("avg_confidence", 0.0),
                    "stddev": result.get("stddev_confidence", 0.0),
                    "min": result.get("min_confidence", 0.0),
                    "max": result.get("max_confidence", 0.0)
                },
                "processing_stats": {
                    "avg_time_ms": result.get("avg_processing_time", 0.0),
                    "max_time_ms": result.get("max_processing_time", 0.0),
                    "min_time_ms": result.get("min_processing_time", 0.0)
                },
                "code_metrics": {
                    "avg_lines_of_code": result.get("avg_lines_of_code", 0.0),
                    "avg_complexity": result.get("avg_complexity", 0.0)
                },
                "severity_breakdown": result.get("severity_breakdown", []),
                "pattern_type_breakdown": result.get("pattern_type_breakdown", []),
                "language_breakdown": result.get("language_breakdown", [])
            }
            
        except Exception as e:
            logger.error(
                "Error getting pattern statistics",
                error=str(e),
                repository_id=repository_id
            )
            raise RepositoryError(f"Failed to get pattern statistics: {str(e)}")
    
    async def delete_patterns_by_repository(
        self,
        repository_id: str,
        date_before: Optional[date] = None
    ) -> int:
        """Delete patterns for a repository (cleanup operation)."""
        try:
            # Count patterns to be deleted
            filters = {"repository_id": repository_id}
            if date_before:
                filters["detection_date__lt"] = date_before
                
            count = await self.count(**filters)
            
            if count > 0:
                # Delete from PostgreSQL
                # Note: BigQuery data will expire based on table partitioning settings
                # For now, we'll just mark them as deleted (soft delete)
                
                logger.info(
                    "Would delete patterns for repository",
                    repository_id=repository_id,
                    count=count,
                    date_before=date_before
                )
                
                # TODO: Implement actual deletion logic
                return count
            
            return 0
            
        except Exception as e:
            logger.error(
                "Error deleting patterns by repository",
                error=str(e),
                repository_id=repository_id
            )
            raise RepositoryError(f"Failed to delete patterns by repository: {str(e)}")