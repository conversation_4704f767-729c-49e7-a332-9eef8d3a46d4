"""
Model Repository

Repository for ML model registry and training logs.
"""

from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
import uuid
from sqlalchemy import select, and_, or_, func, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from .base import BaseRepository, DatabaseError
from ..models import Model, TrainingJob, ModelRegistry, ModelTrainingLog
from ..connection import get_database_session

logger = structlog.get_logger(__name__)


class ModelRepository(BaseRepository[ModelRegistry]):
    """Repository for ML model registry."""
    
    def __init__(self):
        super().__init__(ModelRegistry)
    
    async def get_by_model_id(self, model_id: str) -> Optional[ModelRegistry]:
        """Get model by model_id."""
        async with get_database_session() as session:
            try:
                result = await session.execute(
                    select(self.model_class).where(
                        self.model_class.model_id == model_id
                    )
                )
                return result.scalar_one_or_none()
            except Exception as e:
                logger.error(
                    "Error getting model by model_id",
                    model_id=model_id,
                    error=str(e)
                )
                raise DatabaseError(f"Get by model_id error: {str(e)}")
    
    async def get_by_name_and_version(
        self,
        model_name: str,
        version: str
    ) -> Optional[ModelRegistry]:
        """Get model by name and version."""
        async with get_database_session() as session:
            try:
                result = await session.execute(
                    select(self.model_class).where(
                        and_(
                            self.model_class.model_name == model_name,
                            self.model_class.version == version
                        )
                    )
                )
                return result.scalar_one_or_none()
            except Exception as e:
                logger.error(
                    "Error getting model by name and version",
                    model_name=model_name,
                    version=version,
                    error=str(e)
                )
                raise DatabaseError(f"Get by name and version error: {str(e)}")
    
    async def get_by_status(
        self,
        deployment_status: str,
        limit: int = 100,
        offset: int = 0
    ) -> List[ModelRegistry]:
        """Get models by deployment status."""
        async with get_database_session() as session:
            try:
                query = select(self.model_class).where(
                    self.model_class.deployment_status == deployment_status
                ).order_by(desc(self.model_class.created_at)).limit(limit).offset(offset)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(
                    "Error getting models by status",
                    deployment_status=deployment_status,
                    error=str(e)
                )
                raise DatabaseError(f"Get by status error: {str(e)}")
    
    async def get_production_models(self, limit: int = 100) -> List[ModelRegistry]:
        """Get production models."""
        return await self.get_by_status("production", limit=limit)
    
    async def get_staging_models(self, limit: int = 100) -> List[ModelRegistry]:
        """Get staging models."""
        return await self.get_by_status("staging", limit=limit)
    
    async def get_by_model_type(
        self,
        model_type: str,
        limit: int = 100
    ) -> List[ModelRegistry]:
        """Get models by type."""
        async with get_database_session() as session:
            try:
                query = select(self.model_class).where(
                    self.model_class.model_type == model_type
                ).order_by(desc(self.model_class.created_at)).limit(limit)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(
                    "Error getting models by type",
                    model_type=model_type,
                    error=str(e)
                )
                raise DatabaseError(f"Get by type error: {str(e)}")
    
    async def get_latest_version(self, model_name: str) -> Optional[ModelRegistry]:
        """Get latest version of a model."""
        async with get_database_session() as session:
            try:
                query = select(self.model_class).where(
                    self.model_class.model_name == model_name
                ).order_by(desc(self.model_class.created_at)).limit(1)
                
                result = await session.execute(query)
                return result.scalar_one_or_none()
                
            except Exception as e:
                logger.error(
                    "Error getting latest version",
                    model_name=model_name,
                    error=str(e)
                )
                raise DatabaseError(f"Get latest version error: {str(e)}")
    
    async def register_model(
        self,
        model_name: str,
        model_type: str,
        version: str,
        architecture_json: Optional[Dict[str, Any]] = None,
        hyperparameters: Optional[Dict[str, Any]] = None,
        training_dataset_id: Optional[str] = None,
        deployment_status: str = "staging"
    ) -> ModelRegistry:
        """Register a new model."""
        try:
            model_id = str(uuid.uuid4())
            
            model = ModelRegistry(
                model_id=model_id,
                model_name=model_name,
                model_type=model_type,
                version=version,
                architecture_json=architecture_json,
                hyperparameters=hyperparameters,
                training_dataset_id=training_dataset_id,
                deployment_status=deployment_status
            )
            
            created_model = await self.create(model)
            
            logger.info(
                "Registered new model",
                model_id=model_id,
                model_name=model_name,
                version=version,
                model_type=model_type
            )
            
            return created_model
            
        except Exception as e:
            logger.error(
                "Error registering model",
                model_name=model_name,
                version=version,
                error=str(e)
            )
            raise DatabaseError(f"Register model error: {str(e)}")
    
    async def update_model_metrics(
        self,
        model_id: str,
        precision: Optional[float] = None,
        recall: Optional[float] = None,
        f1_score: Optional[float] = None,
        inference_time_ms: Optional[float] = None
    ) -> bool:
        """Update model performance metrics."""
        try:
            update_data = {"updated_at": datetime.utcnow()}
            
            if precision is not None:
                update_data["precision"] = precision
            if recall is not None:
                update_data["recall"] = recall
            if f1_score is not None:
                update_data["f1_score"] = f1_score
            if inference_time_ms is not None:
                update_data["inference_time_ms"] = inference_time_ms
            
            result = await self.update(model_id, **update_data)
            
            if result:
                logger.info(
                    "Updated model metrics",
                    model_id=model_id,
                    precision=precision,
                    recall=recall,
                    f1_score=f1_score
                )
                return True
            
            return False
            
        except Exception as e:
            logger.error(
                "Error updating model metrics",
                model_id=model_id,
                error=str(e)
            )
            raise DatabaseError(f"Update model metrics error: {str(e)}")
    
    async def deploy_model(
        self,
        model_id: str,
        deployment_status: str = "production",
        traffic_percentage: Optional[float] = None,
        ab_test_group: Optional[str] = None
    ) -> bool:
        """Deploy a model to production."""
        try:
            update_data = {
                "deployment_status": deployment_status,
                "deployment_timestamp": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            
            if traffic_percentage is not None:
                update_data["traffic_percentage"] = traffic_percentage
            if ab_test_group:
                update_data["ab_test_group"] = ab_test_group
            
            result = await self.update(model_id, **update_data)
            
            if result:
                logger.info(
                    "Deployed model",
                    model_id=model_id,
                    deployment_status=deployment_status,
                    traffic_percentage=traffic_percentage
                )
                return True
            
            return False
            
        except Exception as e:
            logger.error(
                "Error deploying model",
                model_id=model_id,
                error=str(e)
            )
            raise DatabaseError(f"Deploy model error: {str(e)}")
    
    async def retire_model(self, model_id: str) -> bool:
        """Retire a model."""
        return await self.deploy_model(model_id, deployment_status="retired")
    
    async def get_model_statistics(
        self,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get model registry statistics."""
        async with get_database_session() as session:
            try:
                cutoff_date = datetime.utcnow() - timedelta(days=days)
                
                # Total models
                total_count = await session.execute(
                    select(func.count(self.model_class.id)).where(
                        self.model_class.created_at >= cutoff_date
                    )
                )
                total_models = total_count.scalar() or 0
                
                # Models by status
                status_stats = await session.execute(
                    select(
                        self.model_class.deployment_status,
                        func.count().label('count')
                    ).where(
                        self.model_class.created_at >= cutoff_date
                    ).group_by(self.model_class.deployment_status)
                )
                
                status_counts = {row.deployment_status: row.count for row in status_stats}
                
                # Models by type
                type_stats = await session.execute(
                    select(
                        self.model_class.model_type,
                        func.count().label('count')
                    ).where(
                        self.model_class.created_at >= cutoff_date
                    ).group_by(self.model_class.model_type)
                )
                
                type_counts = {row.model_type: row.count for row in type_stats}
                
                # Average performance metrics
                avg_metrics = await session.execute(
                    select(
                        func.avg(self.model_class.precision).label('avg_precision'),
                        func.avg(self.model_class.recall).label('avg_recall'),
                        func.avg(self.model_class.f1_score).label('avg_f1'),
                        func.avg(self.model_class.inference_time_ms).label('avg_inference_time')
                    ).where(
                        and_(
                            self.model_class.created_at >= cutoff_date,
                            self.model_class.deployment_status == "production"
                        )
                    )
                )
                
                metrics_row = avg_metrics.first()
                
                return {
                    "total_models": total_models,
                    "status_distribution": status_counts,
                    "type_distribution": type_counts,
                    "average_metrics": {
                        "precision": float(metrics_row.avg_precision) if metrics_row.avg_precision else 0.0,
                        "recall": float(metrics_row.avg_recall) if metrics_row.avg_recall else 0.0,
                        "f1_score": float(metrics_row.avg_f1) if metrics_row.avg_f1 else 0.0,
                        "inference_time_ms": float(metrics_row.avg_inference_time) if metrics_row.avg_inference_time else 0.0
                    },
                    "time_period_days": days
                }
                
            except Exception as e:
                logger.error(
                    "Error getting model statistics",
                    error=str(e)
                )
                raise DatabaseError(f"Get model statistics error: {str(e)}")
    
    async def get_best_performing_models(
        self,
        metric: str = "f1_score",
        limit: int = 10
    ) -> List[ModelRegistry]:
        """Get best performing models by metric."""
        async with get_database_session() as session:
            try:
                if not hasattr(self.model_class, metric):
                    raise ValueError(f"Invalid metric: {metric}")
                
                metric_column = getattr(self.model_class, metric)
                
                query = select(self.model_class).where(
                    and_(
                        self.model_class.deployment_status == "production",
                        metric_column.is_not(None)
                    )
                ).order_by(desc(metric_column)).limit(limit)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(
                    "Error getting best performing models",
                    metric=metric,
                    error=str(e)
                )
                raise DatabaseError(f"Get best performing models error: {str(e)}")
    
    async def get_models_for_ab_test(
        self,
        ab_test_group: str,
        limit: int = 10
    ) -> List[ModelRegistry]:
        """Get models in A/B test group."""
        async with get_database_session() as session:
            try:
                query = select(self.model_class).where(
                    and_(
                        self.model_class.ab_test_group == ab_test_group,
                        self.model_class.deployment_status == "production"
                    )
                ).order_by(desc(self.model_class.deployment_timestamp)).limit(limit)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(
                    "Error getting models for A/B test",
                    ab_test_group=ab_test_group,
                    error=str(e)
                )
                raise DatabaseError(f"Get models for A/B test error: {str(e)}")


class TrainingLogRepository(BaseRepository[ModelTrainingLog]):
    """Repository for model training logs."""
    
    def __init__(self):
        super().__init__(ModelTrainingLog)
    
    async def get_by_training_job_id(
        self,
        training_job_id: str,
        limit: int = 1000
    ) -> List[ModelTrainingLog]:
        """Get training logs by job ID."""
        async with get_database_session() as session:
            try:
                query = select(self.model_class).where(
                    self.model_class.training_job_id == training_job_id
                ).order_by(asc(self.model_class.step)).limit(limit)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(
                    "Error getting training logs by job ID",
                    training_job_id=training_job_id,
                    error=str(e)
                )
                raise DatabaseError(f"Get by training job ID error: {str(e)}")
    
    async def get_by_model_id(
        self,
        model_id: str,
        limit: int = 1000
    ) -> List[ModelTrainingLog]:
        """Get training logs by model ID."""
        async with get_database_session() as session:
            try:
                query = select(self.model_class).where(
                    self.model_class.model_id == model_id
                ).order_by(asc(self.model_class.step)).limit(limit)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(
                    "Error getting training logs by model ID",
                    model_id=model_id,
                    error=str(e)
                )
                raise DatabaseError(f"Get by model ID error: {str(e)}")
    
    async def log_training_step(
        self,
        training_job_id: str,
        model_id: str,
        epoch: int,
        batch: int,
        step: int,
        loss: Optional[float] = None,
        accuracy: Optional[float] = None,
        precision: Optional[float] = None,
        recall: Optional[float] = None,
        f1_score: Optional[float] = None,
        learning_rate: Optional[float] = None,
        gpu_memory_mb: Optional[int] = None,
        cpu_usage_percent: Optional[float] = None
    ) -> ModelTrainingLog:
        """Log a training step."""
        try:
            training_log = ModelTrainingLog(
                training_job_id=training_job_id,
                model_id=model_id,
                epoch=epoch,
                batch=batch,
                step=step,
                loss=loss,
                accuracy=accuracy,
                precision=precision,
                recall=recall,
                f1_score=f1_score,
                learning_rate=learning_rate,
                gpu_memory_mb=gpu_memory_mb,
                cpu_usage_percent=cpu_usage_percent,
                timestamp=datetime.utcnow()
            )
            
            created_log = await self.create(training_log)
            
            logger.debug(
                "Logged training step",
                training_job_id=training_job_id,
                epoch=epoch,
                batch=batch,
                step=step,
                loss=loss
            )
            
            return created_log
            
        except Exception as e:
            logger.error(
                "Error logging training step",
                training_job_id=training_job_id,
                step=step,
                error=str(e)
            )
            raise DatabaseError(f"Log training step error: {str(e)}")
    
    async def batch_log_training_steps(
        self,
        logs_data: List[Dict[str, Any]]
    ) -> List[ModelTrainingLog]:
        """Batch log training steps."""
        try:
            training_logs = []
            
            for data in logs_data:
                training_log = ModelTrainingLog(
                    training_job_id=data["training_job_id"],
                    model_id=data["model_id"],
                    epoch=data["epoch"],
                    batch=data["batch"],
                    step=data["step"],
                    loss=data.get("loss"),
                    accuracy=data.get("accuracy"),
                    precision=data.get("precision"),
                    recall=data.get("recall"),
                    f1_score=data.get("f1_score"),
                    learning_rate=data.get("learning_rate"),
                    gpu_memory_mb=data.get("gpu_memory_mb"),
                    cpu_usage_percent=data.get("cpu_usage_percent"),
                    timestamp=data.get("timestamp", datetime.utcnow())
                )
                
                training_logs.append(training_log)
            
            created_logs = await self.batch_create(training_logs)
            
            logger.info(
                "Batch logged training steps",
                count=len(created_logs)
            )
            
            return created_logs
            
        except Exception as e:
            logger.error(
                "Error batch logging training steps",
                count=len(logs_data),
                error=str(e)
            )
            raise DatabaseError(f"Batch log training steps error: {str(e)}")
    
    async def get_training_progress(
        self,
        training_job_id: str
    ) -> Dict[str, Any]:
        """Get training progress summary."""
        async with get_database_session() as session:
            try:
                # Get latest metrics
                latest_log = await session.execute(
                    select(self.model_class).where(
                        self.model_class.training_job_id == training_job_id
                    ).order_by(desc(self.model_class.step)).limit(1)
                )
                
                latest = latest_log.scalar_one_or_none()
                
                if not latest:
                    return {
                        "training_job_id": training_job_id,
                        "has_logs": False
                    }
                
                # Get epoch statistics
                epoch_stats = await session.execute(
                    select(
                        self.model_class.epoch,
                        func.avg(self.model_class.loss).label('avg_loss'),
                        func.avg(self.model_class.accuracy).label('avg_accuracy'),
                        func.count().label('step_count')
                    ).where(
                        self.model_class.training_job_id == training_job_id
                    ).group_by(self.model_class.epoch).order_by(self.model_class.epoch)
                )
                
                epoch_summaries = []
                for row in epoch_stats:
                    epoch_summaries.append({
                        "epoch": row.epoch,
                        "avg_loss": float(row.avg_loss) if row.avg_loss else None,
                        "avg_accuracy": float(row.avg_accuracy) if row.avg_accuracy else None,
                        "step_count": row.step_count
                    })
                
                # Get overall statistics
                overall_stats = await session.execute(
                    select(
                        func.count().label('total_steps'),
                        func.min(self.model_class.loss).label('best_loss'),
                        func.max(self.model_class.accuracy).label('best_accuracy'),
                        func.avg(self.model_class.gpu_memory_mb).label('avg_gpu_memory'),
                        func.avg(self.model_class.cpu_usage_percent).label('avg_cpu_usage')
                    ).where(
                        self.model_class.training_job_id == training_job_id
                    )
                )
                
                stats_row = overall_stats.first()
                
                return {
                    "training_job_id": training_job_id,
                    "has_logs": True,
                    "latest_metrics": {
                        "epoch": latest.epoch,
                        "step": latest.step,
                        "loss": latest.loss,
                        "accuracy": latest.accuracy,
                        "f1_score": latest.f1_score,
                        "learning_rate": latest.learning_rate,
                        "timestamp": latest.timestamp
                    },
                    "epoch_summaries": epoch_summaries,
                    "overall_statistics": {
                        "total_steps": stats_row.total_steps,
                        "best_loss": float(stats_row.best_loss) if stats_row.best_loss else None,
                        "best_accuracy": float(stats_row.best_accuracy) if stats_row.best_accuracy else None,
                        "avg_gpu_memory_mb": float(stats_row.avg_gpu_memory) if stats_row.avg_gpu_memory else None,
                        "avg_cpu_usage_percent": float(stats_row.avg_cpu_usage) if stats_row.avg_cpu_usage else None
                    }
                }
                
            except Exception as e:
                logger.error(
                    "Error getting training progress",
                    training_job_id=training_job_id,
                    error=str(e)
                )
                raise DatabaseError(f"Get training progress error: {str(e)}")
    
    async def get_training_metrics_analysis(
        self,
        training_job_id: str
    ) -> Dict[str, Any]:
        """Get detailed training metrics analysis."""
        try:
            # Use BigQuery for complex analytics
            query = f"""
            WITH training_metrics AS (
                SELECT 
                    epoch,
                    step,
                    loss,
                    accuracy,
                    f1_score,
                    learning_rate,
                    gpu_memory_mb,
                    cpu_usage_percent,
                    timestamp,
                    -- Calculate moving averages
                    AVG(loss) OVER (
                        ORDER BY step 
                        ROWS BETWEEN 99 PRECEDING AND CURRENT ROW
                    ) as loss_moving_avg,
                    AVG(accuracy) OVER (
                        ORDER BY step 
                        ROWS BETWEEN 99 PRECEDING AND CURRENT ROW
                    ) as accuracy_moving_avg
                FROM `{self.bigquery_table_name}`
                WHERE training_job_id = @training_job_id
                ORDER BY step
            ),
            convergence_analysis AS (
                SELECT 
                    epoch,
                    AVG(loss) as avg_loss,
                    STDDEV(loss) as stddev_loss,
                    AVG(accuracy) as avg_accuracy,
                    STDDEV(accuracy) as stddev_accuracy,
                    -- Calculate loss improvement
                    AVG(loss) - LAG(AVG(loss)) OVER (ORDER BY epoch) as loss_improvement,
                    -- Calculate accuracy improvement
                    AVG(accuracy) - LAG(AVG(accuracy)) OVER (ORDER BY epoch) as accuracy_improvement
                FROM training_metrics
                WHERE loss IS NOT NULL
                GROUP BY epoch
                ORDER BY epoch
            )
            SELECT 
                'convergence' as analysis_type,
                ARRAY_AGG(
                    STRUCT(
                        epoch,
                        avg_loss,
                        stddev_loss,
                        avg_accuracy,
                        stddev_accuracy,
                        loss_improvement,
                        accuracy_improvement
                    )
                    ORDER BY epoch
                ) as convergence_data
            FROM convergence_analysis
            
            UNION ALL
            
            SELECT 
                'resource_usage' as analysis_type,
                ARRAY_AGG(
                    STRUCT(
                        epoch,
                        step,
                        gpu_memory_mb,
                        cpu_usage_percent,
                        timestamp
                    )
                    ORDER BY step
                ) as resource_data
            FROM training_metrics
            WHERE gpu_memory_mb IS NOT NULL OR cpu_usage_percent IS NOT NULL
            """
            
            results = await self.query_bigquery(query, {"training_job_id": training_job_id})
            
            # Process results
            analysis = {}
            for row in results:
                analysis[row["analysis_type"]] = row.get("convergence_data", row.get("resource_data", []))
            
            return {
                "training_job_id": training_job_id,
                "analysis": analysis,
                "generated_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(
                "Error getting training metrics analysis",
                training_job_id=training_job_id,
                error=str(e)
            )
            raise DatabaseError(f"Get training metrics analysis error: {str(e)}")
    
    async def cleanup_old_training_logs(
        self,
        days: int = 90
    ) -> int:
        """Clean up old training logs."""
        async with get_database_session() as session:
            try:
                cutoff_date = datetime.utcnow() - timedelta(days=days)
                
                # Get old training logs
                old_logs = await session.execute(
                    select(self.model_class).where(
                        self.model_class.timestamp < cutoff_date
                    )
                )
                
                logs_to_delete = old_logs.scalars().all()
                
                if logs_to_delete:
                    for log in logs_to_delete:
                        await session.delete(log)
                    
                    await session.commit()
                    
                    logger.info(
                        "Cleaned up old training logs",
                        count=len(logs_to_delete),
                        days=days
                    )
                    
                    return len(logs_to_delete)
                
                return 0
                
            except Exception as e:
                await session.rollback()
                logger.error(
                    "Error cleaning up old training logs",
                    error=str(e)
                )
                raise DatabaseError(f"Cleanup error: {str(e)}")