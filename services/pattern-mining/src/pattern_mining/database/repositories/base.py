"""
Base Repository

Base repository with common database operations for both PostgreSQL and BigQuery.
"""

from abc import ABC, abstractmethod
from typing import (
    Any,
    Dict,
    List,
    Optional,
    Union,
    Type,
    TypeVar,
    Generic,
    Tuple,
    Callable,
)
from datetime import datetime, timedelta
import uuid
import asyncio
from contextlib import asynccontextmanager

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy import select, update, delete, func, and_, or_, desc, asc
from sqlalchemy.exc import IntegrityError, NoResultFound
from google.cloud.bigquery import SchemaField
import structlog

from ..bigquery import BigQueryClient, get_bigquery_session
from ..connection import get_database_session
from ..models import Base
from ...models.database import BaseModel

logger = structlog.get_logger(__name__)

T = TypeVar("T", bound=Base)


class DatabaseError(Exception):
    """Database operation error."""
    pass


class RepositoryError(Exception):
    """Repository operation error."""
    pass


class BaseRepository(Generic[T], ABC):
    """Base repository with common database operations."""
    
    def __init__(self, model_class: Type[T]):
        self.model_class = model_class
        self.table_name = model_class.__tablename__
        self._bigquery_client: Optional[BigQueryClient] = None
        
    @property
    def bigquery_table_name(self) -> str:
        """Get BigQuery table name."""
        # Map SQLAlchemy table names to BigQuery table names
        table_mapping = {
            "patterns": "pattern_results",
            "analysis_jobs": "repository_analysis",
            "models": "model_registry",
            "training_jobs": "model_training_logs",
            "feature_vectors": "pattern_embeddings",
            "pattern_similarities": "pattern_similarities",
        }
        return table_mapping.get(self.table_name, self.table_name)
    
    async def get_bigquery_client(self) -> BigQueryClient:
        """Get BigQuery client instance."""
        if self._bigquery_client is None:
            async with get_bigquery_session() as client:
                self._bigquery_client = client
        return self._bigquery_client
    
    # PostgreSQL operations
    async def create(self, obj: T) -> T:
        """Create a new record in PostgreSQL."""
        async with get_database_session() as session:
            try:
                session.add(obj)
                await session.commit()
                await session.refresh(obj)
                
                logger.info(
                    "Created record",
                    table=self.table_name,
                    id=getattr(obj, 'id', None)
                )
                
                return obj
                
            except IntegrityError as e:
                await session.rollback()
                logger.error(
                    "Integrity error creating record",
                    table=self.table_name,
                    error=str(e)
                )
                raise DatabaseError(f"Integrity error: {str(e)}")
            except Exception as e:
                await session.rollback()
                logger.error(
                    "Error creating record",
                    table=self.table_name,
                    error=str(e)
                )
                raise DatabaseError(f"Create error: {str(e)}")
    
    async def get_by_id(self, id: Union[str, uuid.UUID]) -> Optional[T]:
        """Get a record by ID from PostgreSQL."""
        async with get_database_session() as session:
            try:
                result = await session.execute(
                    select(self.model_class).where(self.model_class.id == id)
                )
                return result.scalar_one_or_none()
            except Exception as e:
                logger.error(
                    "Error getting record by ID",
                    table=self.table_name,
                    id=id,
                    error=str(e)
                )
                raise DatabaseError(f"Get by ID error: {str(e)}")
    
    async def get_all(
        self,
        limit: int = 100,
        offset: int = 0,
        order_by: Optional[str] = None,
        order_desc: bool = False
    ) -> List[T]:
        """Get all records from PostgreSQL."""
        async with get_database_session() as session:
            try:
                query = select(self.model_class)
                
                # Apply ordering
                if order_by:
                    order_column = getattr(self.model_class, order_by)
                    if order_desc:
                        query = query.order_by(desc(order_column))
                    else:
                        query = query.order_by(asc(order_column))
                
                # Apply pagination
                query = query.limit(limit).offset(offset)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(
                    "Error getting all records",
                    table=self.table_name,
                    error=str(e)
                )
                raise DatabaseError(f"Get all error: {str(e)}")
    
    async def update(self, id: Union[str, uuid.UUID], **kwargs) -> Optional[T]:
        """Update a record in PostgreSQL."""
        async with get_database_session() as session:
            try:
                # Update the record
                stmt = (
                    update(self.model_class)
                    .where(self.model_class.id == id)
                    .values(**kwargs)
                    .returning(self.model_class)
                )
                
                result = await session.execute(stmt)
                updated_obj = result.scalar_one_or_none()
                
                if updated_obj:
                    await session.commit()
                    logger.info(
                        "Updated record",
                        table=self.table_name,
                        id=id
                    )
                    return updated_obj
                else:
                    await session.rollback()
                    return None
                    
            except Exception as e:
                await session.rollback()
                logger.error(
                    "Error updating record",
                    table=self.table_name,
                    id=id,
                    error=str(e)
                )
                raise DatabaseError(f"Update error: {str(e)}")
    
    async def delete(self, id: Union[str, uuid.UUID]) -> bool:
        """Delete a record from PostgreSQL."""
        async with get_database_session() as session:
            try:
                stmt = delete(self.model_class).where(self.model_class.id == id)
                result = await session.execute(stmt)
                
                if result.rowcount > 0:
                    await session.commit()
                    logger.info(
                        "Deleted record",
                        table=self.table_name,
                        id=id
                    )
                    return True
                else:
                    await session.rollback()
                    return False
                    
            except Exception as e:
                await session.rollback()
                logger.error(
                    "Error deleting record",
                    table=self.table_name,
                    id=id,
                    error=str(e)
                )
                raise DatabaseError(f"Delete error: {str(e)}")
    
    async def count(self, **filters) -> int:
        """Count records in PostgreSQL."""
        async with get_database_session() as session:
            try:
                query = select(func.count(self.model_class.id))
                
                # Apply filters
                if filters:
                    conditions = []
                    for key, value in filters.items():
                        if hasattr(self.model_class, key):
                            conditions.append(getattr(self.model_class, key) == value)
                    if conditions:
                        query = query.where(and_(*conditions))
                
                result = await session.execute(query)
                return result.scalar() or 0
                
            except Exception as e:
                logger.error(
                    "Error counting records",
                    table=self.table_name,
                    error=str(e)
                )
                raise DatabaseError(f"Count error: {str(e)}")
    
    async def filter(self, **filters) -> List[T]:
        """Filter records in PostgreSQL."""
        async with get_database_session() as session:
            try:
                query = select(self.model_class)
                
                # Apply filters
                if filters:
                    conditions = []
                    for key, value in filters.items():
                        if hasattr(self.model_class, key):
                            conditions.append(getattr(self.model_class, key) == value)
                    if conditions:
                        query = query.where(and_(*conditions))
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(
                    "Error filtering records",
                    table=self.table_name,
                    error=str(e)
                )
                raise DatabaseError(f"Filter error: {str(e)}")
    
    # BigQuery operations
    async def insert_to_bigquery(self, data: List[Dict[str, Any]]) -> None:
        """Insert data to BigQuery using streaming insert."""
        if not data:
            return
        
        try:
            client = await self.get_bigquery_client()
            
            # Convert data to BigQuery format if needed
            bigquery_data = []
            for item in data:
                if hasattr(item, 'to_bigquery_dict'):
                    bigquery_data.append(item.to_bigquery_dict())
                elif isinstance(item, dict):
                    bigquery_data.append(item)
                else:
                    # Convert model instance to dict
                    bigquery_data.append(self._model_to_dict(item))
            
            await client.streaming_insert(
                table_name=self.bigquery_table_name,
                rows=bigquery_data
            )
            
            logger.info(
                "Inserted data to BigQuery",
                table=self.bigquery_table_name,
                row_count=len(bigquery_data)
            )
            
        except Exception as e:
            logger.error(
                "Error inserting to BigQuery",
                table=self.bigquery_table_name,
                error=str(e)
            )
            raise DatabaseError(f"BigQuery insert error: {str(e)}")
    
    async def query_bigquery(
        self,
        query: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Execute a query on BigQuery."""
        try:
            client = await self.get_bigquery_client()
            return await client.execute_query(query, parameters)
            
        except Exception as e:
            logger.error(
                "Error querying BigQuery",
                table=self.bigquery_table_name,
                error=str(e)
            )
            raise DatabaseError(f"BigQuery query error: {str(e)}")
    
    async def create_bigquery_table(self) -> None:
        """Create BigQuery table if it doesn't exist."""
        try:
            client = await self.get_bigquery_client()
            
            # Check if table exists
            if await client.table_exists(self.bigquery_table_name):
                logger.info(
                    "BigQuery table already exists",
                    table=self.bigquery_table_name
                )
                return
            
            # Get schema and configuration
            if hasattr(self.model_class, 'get_bigquery_schema'):
                schema = self.model_class.get_bigquery_schema()
                config = self.model_class.get_bigquery_table_config()
                
                await client.create_table(
                    table_name=self.bigquery_table_name,
                    schema=schema,
                    partition_field=config.get('partition_field'),
                    clustering_fields=config.get('clustering_fields'),
                    description=config.get('description'),
                    expiration_days=config.get('expiration_days')
                )
                
                logger.info(
                    "Created BigQuery table",
                    table=self.bigquery_table_name
                )
            else:
                logger.warning(
                    "Model class does not have BigQuery schema method",
                    model_class=self.model_class.__name__
                )
                
        except Exception as e:
            logger.error(
                "Error creating BigQuery table",
                table=self.bigquery_table_name,
                error=str(e)
            )
            raise DatabaseError(f"BigQuery table creation error: {str(e)}")
    
    # Utility methods
    def _model_to_dict(self, obj: T) -> Dict[str, Any]:
        """Convert model instance to dictionary."""
        result = {}
        for column in self.model_class.__table__.columns:
            value = getattr(obj, column.name)
            if isinstance(value, datetime):
                result[column.name] = value.isoformat()
            elif isinstance(value, uuid.UUID):
                result[column.name] = str(value)
            else:
                result[column.name] = value
        return result
    
    async def batch_create(self, objects: List[T]) -> List[T]:
        """Create multiple records in batch."""
        if not objects:
            return []
        
        async with get_database_session() as session:
            try:
                session.add_all(objects)
                await session.commit()
                
                # Refresh all objects
                for obj in objects:
                    await session.refresh(obj)
                
                logger.info(
                    "Batch created records",
                    table=self.table_name,
                    count=len(objects)
                )
                
                return objects
                
            except Exception as e:
                await session.rollback()
                logger.error(
                    "Error batch creating records",
                    table=self.table_name,
                    error=str(e)
                )
                raise DatabaseError(f"Batch create error: {str(e)}")
    
    async def batch_update(self, updates: List[Dict[str, Any]]) -> int:
        """Update multiple records in batch."""
        if not updates:
            return 0
        
        async with get_database_session() as session:
            try:
                updated_count = 0
                
                for update_data in updates:
                    id_value = update_data.pop('id', None)
                    if id_value:
                        stmt = (
                            update(self.model_class)
                            .where(self.model_class.id == id_value)
                            .values(**update_data)
                        )
                        result = await session.execute(stmt)
                        updated_count += result.rowcount
                
                await session.commit()
                
                logger.info(
                    "Batch updated records",
                    table=self.table_name,
                    count=updated_count
                )
                
                return updated_count
                
            except Exception as e:
                await session.rollback()
                logger.error(
                    "Error batch updating records",
                    table=self.table_name,
                    error=str(e)
                )
                raise DatabaseError(f"Batch update error: {str(e)}")
    
    async def exists(self, **filters) -> bool:
        """Check if record exists."""
        count = await self.count(**filters)
        return count > 0
    
    async def get_or_create(self, defaults: Optional[Dict[str, Any]] = None, **kwargs) -> Tuple[T, bool]:
        """Get or create a record."""
        # Try to get existing record
        existing = await self.filter(**kwargs)
        if existing:
            return existing[0], False
        
        # Create new record
        create_data = {**kwargs}
        if defaults:
            create_data.update(defaults)
        
        new_obj = self.model_class(**create_data)
        created_obj = await self.create(new_obj)
        return created_obj, True
    
    async def get_recent(
        self,
        limit: int = 10,
        hours: int = 24,
        timestamp_field: str = 'created_at'
    ) -> List[T]:
        """Get recent records."""
        async with get_database_session() as session:
            try:
                cutoff_time = datetime.utcnow() - timedelta(hours=hours)
                timestamp_column = getattr(self.model_class, timestamp_field)
                
                query = (
                    select(self.model_class)
                    .where(timestamp_column >= cutoff_time)
                    .order_by(desc(timestamp_column))
                    .limit(limit)
                )
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(
                    "Error getting recent records",
                    table=self.table_name,
                    error=str(e)
                )
                raise DatabaseError(f"Get recent error: {str(e)}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Check repository health."""
        try:
            # Check PostgreSQL connection
            pg_count = await self.count()
            
            # Check BigQuery connection if available
            bigquery_status = "not_configured"
            try:
                client = await self.get_bigquery_client()
                bigquery_status = "connected"
            except Exception:
                bigquery_status = "error"
            
            return {
                "table_name": self.table_name,
                "bigquery_table_name": self.bigquery_table_name,
                "postgresql_record_count": pg_count,
                "bigquery_status": bigquery_status,
                "status": "healthy"
            }
            
        except Exception as e:
            logger.error(
                "Repository health check failed",
                table=self.table_name,
                error=str(e)
            )
            return {
                "table_name": self.table_name,
                "status": "unhealthy",
                "error": str(e)
            }