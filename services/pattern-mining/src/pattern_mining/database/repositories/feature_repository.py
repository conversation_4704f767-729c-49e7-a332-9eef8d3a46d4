"""
Feature Repository

Repository for feature extraction and embedding operations.
"""

from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
import uuid
import hashlib
from sqlalchemy import select, and_, or_, func, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from .base import BaseRepository, DatabaseError
from ..models import FeatureVector, PatternEmbedding
from ..connection import get_database_session

logger = structlog.get_logger(__name__)


class FeatureRepository(BaseRepository[FeatureVector]):
    """Repository for feature vectors and embeddings."""
    
    def __init__(self):
        super().__init__(FeatureVector)
    
    async def get_by_code_hash(self, code_hash: str) -> Optional[FeatureVector]:
        """Get feature vector by code hash."""
        async with get_database_session() as session:
            try:
                result = await session.execute(
                    select(self.model_class).where(
                        self.model_class.code_hash == code_hash
                    )
                )
                return result.scalar_one_or_none()
            except Exception as e:
                logger.error(
                    "Error getting feature vector by code hash",
                    code_hash=code_hash,
                    error=str(e)
                )
                raise DatabaseError(f"Get by code hash error: {str(e)}")
    
    async def get_by_language(
        self,
        language: str,
        limit: int = 100,
        offset: int = 0
    ) -> List[FeatureVector]:
        """Get feature vectors by language."""
        async with get_database_session() as session:
            try:
                query = select(self.model_class).where(
                    self.model_class.language == language
                ).order_by(desc(self.model_class.created_at)).limit(limit).offset(offset)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(
                    "Error getting feature vectors by language",
                    language=language,
                    error=str(e)
                )
                raise DatabaseError(f"Get by language error: {str(e)}")
    
    async def store_feature_vector(
        self,
        code: str,
        language: str,
        features: Dict[str, Any],
        embedding: Optional[List[float]] = None,
        extraction_method: str = "default",
        feature_version: str = "1.0"
    ) -> FeatureVector:
        """Store a feature vector for code."""
        try:
            # Generate code hash
            code_hash = hashlib.sha256(code.encode()).hexdigest()
            
            # Check if feature vector already exists
            existing = await self.get_by_code_hash(code_hash)
            if existing:
                logger.info(
                    "Feature vector already exists",
                    code_hash=code_hash
                )
                return existing
            
            # Create new feature vector
            feature_vector = FeatureVector(
                code_hash=code_hash,
                language=language,
                features=features,
                embedding=embedding,
                extraction_method=extraction_method,
                feature_version=feature_version
            )
            
            created_vector = await self.create(feature_vector)
            
            logger.info(
                "Stored feature vector",
                code_hash=code_hash,
                language=language,
                extraction_method=extraction_method
            )
            
            return created_vector
            
        except Exception as e:
            logger.error(
                "Error storing feature vector",
                language=language,
                error=str(e)
            )
            raise DatabaseError(f"Store feature vector error: {str(e)}")
    
    async def get_features_for_training(
        self,
        language: Optional[str] = None,
        extraction_method: Optional[str] = None,
        feature_version: Optional[str] = None,
        limit: int = 1000
    ) -> List[FeatureVector]:
        """Get feature vectors for model training."""
        async with get_database_session() as session:
            try:
                query = select(self.model_class)
                
                conditions = []
                
                if language:
                    conditions.append(self.model_class.language == language)
                
                if extraction_method:
                    conditions.append(self.model_class.extraction_method == extraction_method)
                
                if feature_version:
                    conditions.append(self.model_class.feature_version == feature_version)
                
                if conditions:
                    query = query.where(and_(*conditions))
                
                query = query.order_by(desc(self.model_class.created_at)).limit(limit)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(
                    "Error getting features for training",
                    language=language,
                    error=str(e)
                )
                raise DatabaseError(f"Get features for training error: {str(e)}")
    
    async def batch_store_features(
        self,
        feature_data: List[Dict[str, Any]]
    ) -> List[FeatureVector]:
        """Batch store feature vectors."""
        try:
            feature_vectors = []
            
            for data in feature_data:
                # Generate code hash
                code_hash = hashlib.sha256(data["code"].encode()).hexdigest()
                
                # Check if already exists
                existing = await self.get_by_code_hash(code_hash)
                if existing:
                    feature_vectors.append(existing)
                    continue
                
                # Create new feature vector
                feature_vector = FeatureVector(
                    code_hash=code_hash,
                    language=data["language"],
                    features=data["features"],
                    embedding=data.get("embedding"),
                    extraction_method=data.get("extraction_method", "default"),
                    feature_version=data.get("feature_version", "1.0")
                )
                
                feature_vectors.append(feature_vector)
            
            # Filter out existing ones and batch create new ones
            new_vectors = [v for v in feature_vectors if not hasattr(v, 'id')]
            if new_vectors:
                created_vectors = await self.batch_create(new_vectors)
                
                logger.info(
                    "Batch stored feature vectors",
                    total_requested=len(feature_data),
                    new_created=len(created_vectors),
                    existing_found=len(feature_vectors) - len(created_vectors)
                )
            
            return feature_vectors
            
        except Exception as e:
            logger.error(
                "Error batch storing features",
                count=len(feature_data),
                error=str(e)
            )
            raise DatabaseError(f"Batch store features error: {str(e)}")
    
    async def get_feature_statistics(
        self,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get feature extraction statistics."""
        async with get_database_session() as session:
            try:
                cutoff_date = datetime.utcnow() - timedelta(days=days)
                
                # Total feature vectors
                total_count = await session.execute(
                    select(func.count(self.model_class.id)).where(
                        self.model_class.created_at >= cutoff_date
                    )
                )
                total_features = total_count.scalar() or 0
                
                # Features by language
                language_stats = await session.execute(
                    select(
                        self.model_class.language,
                        func.count().label('count')
                    ).where(
                        self.model_class.created_at >= cutoff_date
                    ).group_by(self.model_class.language)
                )
                
                language_counts = {row.language: row.count for row in language_stats}
                
                # Features by extraction method
                method_stats = await session.execute(
                    select(
                        self.model_class.extraction_method,
                        func.count().label('count')
                    ).where(
                        self.model_class.created_at >= cutoff_date
                    ).group_by(self.model_class.extraction_method)
                )
                
                method_counts = {row.extraction_method: row.count for row in method_stats}
                
                # Features by version
                version_stats = await session.execute(
                    select(
                        self.model_class.feature_version,
                        func.count().label('count')
                    ).where(
                        self.model_class.created_at >= cutoff_date
                    ).group_by(self.model_class.feature_version)
                )
                
                version_counts = {row.feature_version: row.count for row in version_stats}
                
                return {
                    "total_features": total_features,
                    "language_distribution": language_counts,
                    "method_distribution": method_counts,
                    "version_distribution": version_counts,
                    "time_period_days": days
                }
                
            except Exception as e:
                logger.error(
                    "Error getting feature statistics",
                    error=str(e)
                )
                raise DatabaseError(f"Get feature statistics error: {str(e)}")
    
    async def update_embeddings(
        self,
        code_hash: str,
        embedding: List[float]
    ) -> bool:
        """Update embeddings for a feature vector."""
        try:
            result = await self.update(
                code_hash,
                embedding=embedding,
                updated_at=datetime.utcnow()
            )
            
            if result:
                logger.info(
                    "Updated embeddings",
                    code_hash=code_hash,
                    embedding_size=len(embedding)
                )
                return True
            
            return False
            
        except Exception as e:
            logger.error(
                "Error updating embeddings",
                code_hash=code_hash,
                error=str(e)
            )
            raise DatabaseError(f"Update embeddings error: {str(e)}")
    
    async def cleanup_old_features(
        self,
        days: int = 180
    ) -> int:
        """Clean up old feature vectors."""
        async with get_database_session() as session:
            try:
                cutoff_date = datetime.utcnow() - timedelta(days=days)
                
                # Get old feature vectors
                old_features = await session.execute(
                    select(self.model_class).where(
                        self.model_class.created_at < cutoff_date
                    )
                )
                
                features_to_delete = old_features.scalars().all()
                
                if features_to_delete:
                    for feature in features_to_delete:
                        await session.delete(feature)
                    
                    await session.commit()
                    
                    logger.info(
                        "Cleaned up old feature vectors",
                        count=len(features_to_delete),
                        days=days
                    )
                    
                    return len(features_to_delete)
                
                return 0
                
            except Exception as e:
                await session.rollback()
                logger.error(
                    "Error cleaning up old features",
                    error=str(e)
                )
                raise DatabaseError(f"Cleanup error: {str(e)}")


class EmbeddingRepository(BaseRepository[PatternEmbedding]):
    """Repository for pattern embeddings."""
    
    def __init__(self):
        super().__init__(PatternEmbedding)
    
    async def get_by_pattern_id(self, pattern_id: str) -> Optional[PatternEmbedding]:
        """Get embedding by pattern ID."""
        async with get_database_session() as session:
            try:
                result = await session.execute(
                    select(self.model_class).where(
                        self.model_class.pattern_id == pattern_id
                    )
                )
                return result.scalar_one_or_none()
            except Exception as e:
                logger.error(
                    "Error getting embedding by pattern ID",
                    pattern_id=pattern_id,
                    error=str(e)
                )
                raise DatabaseError(f"Get by pattern ID error: {str(e)}")
    
    async def store_embedding(
        self,
        pattern_id: str,
        pattern_type: str,
        embedding: List[float],
        embedding_model: str,
        embedding_version: str,
        language: str,
        pattern_category: str,
        framework: Optional[str] = None
    ) -> PatternEmbedding:
        """Store a pattern embedding."""
        try:
            # Check if embedding already exists
            existing = await self.get_by_pattern_id(pattern_id)
            if existing:
                # Update existing embedding
                await self.update(
                    existing.id,
                    embedding=embedding,
                    embedding_model=embedding_model,
                    embedding_version=embedding_version,
                    embedding_norm=self._calculate_norm(embedding),
                    updated_at=datetime.utcnow()
                )
                logger.info(
                    "Updated existing embedding",
                    pattern_id=pattern_id
                )
                return existing
            
            # Create new embedding
            pattern_embedding = PatternEmbedding(
                pattern_id=pattern_id,
                pattern_type=pattern_type,
                embedding=embedding,
                embedding_model=embedding_model,
                embedding_version=embedding_version,
                language=language,
                framework=framework,
                pattern_category=pattern_category,
                embedding_norm=self._calculate_norm(embedding)
            )
            
            created_embedding = await self.create(pattern_embedding)
            
            logger.info(
                "Stored pattern embedding",
                pattern_id=pattern_id,
                embedding_model=embedding_model,
                embedding_size=len(embedding)
            )
            
            return created_embedding
            
        except Exception as e:
            logger.error(
                "Error storing embedding",
                pattern_id=pattern_id,
                error=str(e)
            )
            raise DatabaseError(f"Store embedding error: {str(e)}")
    
    async def find_similar_patterns(
        self,
        query_embedding: List[float],
        pattern_type: Optional[str] = None,
        language: Optional[str] = None,
        limit: int = 10,
        similarity_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """Find similar patterns using embedding similarity."""
        try:
            # Use BigQuery for vector similarity search
            conditions = []
            parameters = {
                "query_embedding": query_embedding,
                "similarity_threshold": similarity_threshold,
                "limit": limit
            }
            
            if pattern_type:
                conditions.append("pattern_type = @pattern_type")
                parameters["pattern_type"] = pattern_type
            
            if language:
                conditions.append("language = @language")
                parameters["language"] = language
            
            where_clause = ""
            if conditions:
                where_clause = f"WHERE {' AND '.join(conditions)}"
            
            query = f"""
            SELECT 
                pattern_id,
                pattern_type,
                language,
                pattern_category,
                embedding_model,
                -- Calculate cosine similarity
                (
                    SELECT SUM(a * b) / (SQRT(SUM(a * a)) * SQRT(SUM(b * b)))
                    FROM UNNEST(@query_embedding) AS a WITH OFFSET pos_a
                    JOIN UNNEST(embedding) AS b WITH OFFSET pos_b
                    ON pos_a = pos_b
                ) AS similarity_score
            FROM `{self.bigquery_table_name}`
            {where_clause}
            HAVING similarity_score >= @similarity_threshold
            ORDER BY similarity_score DESC
            LIMIT @limit
            """
            
            results = await self.query_bigquery(query, parameters)
            
            logger.info(
                "Found similar patterns",
                results_count=len(results),
                similarity_threshold=similarity_threshold
            )
            
            return results
            
        except Exception as e:
            logger.error(
                "Error finding similar patterns",
                error=str(e)
            )
            raise DatabaseError(f"Find similar patterns error: {str(e)}")
    
    async def batch_store_embeddings(
        self,
        embeddings_data: List[Dict[str, Any]]
    ) -> List[PatternEmbedding]:
        """Batch store pattern embeddings."""
        try:
            embeddings = []
            
            for data in embeddings_data:
                # Check if already exists
                existing = await self.get_by_pattern_id(data["pattern_id"])
                if existing:
                    embeddings.append(existing)
                    continue
                
                # Create new embedding
                embedding = PatternEmbedding(
                    pattern_id=data["pattern_id"],
                    pattern_type=data["pattern_type"],
                    embedding=data["embedding"],
                    embedding_model=data["embedding_model"],
                    embedding_version=data["embedding_version"],
                    language=data["language"],
                    framework=data.get("framework"),
                    pattern_category=data["pattern_category"],
                    embedding_norm=self._calculate_norm(data["embedding"])
                )
                
                embeddings.append(embedding)
            
            # Filter out existing ones and batch create new ones
            new_embeddings = [e for e in embeddings if not hasattr(e, 'id')]
            if new_embeddings:
                created_embeddings = await self.batch_create(new_embeddings)
                
                logger.info(
                    "Batch stored embeddings",
                    total_requested=len(embeddings_data),
                    new_created=len(created_embeddings),
                    existing_found=len(embeddings) - len(created_embeddings)
                )
            
            return embeddings
            
        except Exception as e:
            logger.error(
                "Error batch storing embeddings",
                count=len(embeddings_data),
                error=str(e)
            )
            raise DatabaseError(f"Batch store embeddings error: {str(e)}")
    
    async def get_embedding_statistics(
        self,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get embedding statistics."""
        async with get_database_session() as session:
            try:
                cutoff_date = datetime.utcnow() - timedelta(days=days)
                
                # Total embeddings
                total_count = await session.execute(
                    select(func.count(self.model_class.id)).where(
                        self.model_class.created_at >= cutoff_date
                    )
                )
                total_embeddings = total_count.scalar() or 0
                
                # Embeddings by model
                model_stats = await session.execute(
                    select(
                        self.model_class.embedding_model,
                        func.count().label('count')
                    ).where(
                        self.model_class.created_at >= cutoff_date
                    ).group_by(self.model_class.embedding_model)
                )
                
                model_counts = {row.embedding_model: row.count for row in model_stats}
                
                # Embeddings by language
                language_stats = await session.execute(
                    select(
                        self.model_class.language,
                        func.count().label('count')
                    ).where(
                        self.model_class.created_at >= cutoff_date
                    ).group_by(self.model_class.language)
                )
                
                language_counts = {row.language: row.count for row in language_stats}
                
                # Embeddings by pattern type
                type_stats = await session.execute(
                    select(
                        self.model_class.pattern_type,
                        func.count().label('count')
                    ).where(
                        self.model_class.created_at >= cutoff_date
                    ).group_by(self.model_class.pattern_type)
                )
                
                type_counts = {row.pattern_type: row.count for row in type_stats}
                
                return {
                    "total_embeddings": total_embeddings,
                    "model_distribution": model_counts,
                    "language_distribution": language_counts,
                    "type_distribution": type_counts,
                    "time_period_days": days
                }
                
            except Exception as e:
                logger.error(
                    "Error getting embedding statistics",
                    error=str(e)
                )
                raise DatabaseError(f"Get embedding statistics error: {str(e)}")
    
    def _calculate_norm(self, embedding: List[float]) -> float:
        """Calculate L2 norm of embedding vector."""
        try:
            return sum(x * x for x in embedding) ** 0.5
        except Exception:
            return 0.0
    
    async def update_embedding_model(
        self,
        pattern_id: str,
        new_embedding: List[float],
        embedding_model: str,
        embedding_version: str
    ) -> bool:
        """Update embedding with new model."""
        try:
            result = await self.update(
                pattern_id,
                embedding=new_embedding,
                embedding_model=embedding_model,
                embedding_version=embedding_version,
                embedding_norm=self._calculate_norm(new_embedding),
                updated_at=datetime.utcnow()
            )
            
            if result:
                logger.info(
                    "Updated embedding model",
                    pattern_id=pattern_id,
                    embedding_model=embedding_model
                )
                return True
            
            return False
            
        except Exception as e:
            logger.error(
                "Error updating embedding model",
                pattern_id=pattern_id,
                error=str(e)
            )
            raise DatabaseError(f"Update embedding model error: {str(e)}")
    
    async def cleanup_old_embeddings(
        self,
        days: int = 365
    ) -> int:
        """Clean up old embeddings."""
        async with get_database_session() as session:
            try:
                cutoff_date = datetime.utcnow() - timedelta(days=days)
                
                # Get old embeddings
                old_embeddings = await session.execute(
                    select(self.model_class).where(
                        self.model_class.created_at < cutoff_date
                    )
                )
                
                embeddings_to_delete = old_embeddings.scalars().all()
                
                if embeddings_to_delete:
                    for embedding in embeddings_to_delete:
                        await session.delete(embedding)
                    
                    await session.commit()
                    
                    logger.info(
                        "Cleaned up old embeddings",
                        count=len(embeddings_to_delete),
                        days=days
                    )
                    
                    return len(embeddings_to_delete)
                
                return 0
                
            except Exception as e:
                await session.rollback()
                logger.error(
                    "Error cleaning up old embeddings",
                    error=str(e)
                )
                raise DatabaseError(f"Cleanup error: {str(e)}")