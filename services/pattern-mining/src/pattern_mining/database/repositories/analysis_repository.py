"""
Analysis Repository

Repository for repository analysis results and job tracking.
"""

from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
import uuid
from sqlalchemy import select, and_, or_, func, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from .base import BaseRepository, DatabaseError
from ..models import AnalysisJob, RepositoryAnalysis
from ..connection import get_database_session
from ...models.api import AnalysisJobStatus

logger = structlog.get_logger(__name__)


class AnalysisRepository(BaseRepository[RepositoryAnalysis]):
    """Repository for repository analysis results."""
    
    def __init__(self):
        super().__init__(RepositoryAnalysis)
    
    async def get_by_analysis_id(self, analysis_id: str) -> Optional[RepositoryAnalysis]:
        """Get analysis by analysis_id."""
        async with get_database_session() as session:
            try:
                result = await session.execute(
                    select(self.model_class).where(
                        self.model_class.analysis_id == analysis_id
                    )
                )
                return result.scalar_one_or_none()
            except Exception as e:
                logger.error(
                    "Error getting analysis by analysis_id",
                    analysis_id=analysis_id,
                    error=str(e)
                )
                raise DatabaseError(f"Get by analysis_id error: {str(e)}")
    
    async def get_by_repository(
        self,
        repository_id: str,
        limit: int = 100,
        offset: int = 0,
        status: Optional[str] = None,
        analysis_type: Optional[str] = None
    ) -> List[RepositoryAnalysis]:
        """Get analyses by repository with filters."""
        async with get_database_session() as session:
            try:
                query = select(self.model_class).where(
                    self.model_class.repository_id == repository_id
                )
                
                if status:
                    query = query.where(self.model_class.status == status)
                
                if analysis_type:
                    query = query.where(self.model_class.analysis_type == analysis_type)
                
                query = query.order_by(desc(self.model_class.created_at))
                query = query.limit(limit).offset(offset)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(
                    "Error getting analyses by repository",
                    repository_id=repository_id,
                    error=str(e)
                )
                raise DatabaseError(f"Get by repository error: {str(e)}")
    
    async def get_by_status(
        self,
        status: str,
        limit: int = 100,
        offset: int = 0
    ) -> List[RepositoryAnalysis]:
        """Get analyses by status."""
        async with get_database_session() as session:
            try:
                query = select(self.model_class).where(
                    self.model_class.status == status
                ).order_by(desc(self.model_class.created_at)).limit(limit).offset(offset)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(
                    "Error getting analyses by status",
                    status=status,
                    error=str(e)
                )
                raise DatabaseError(f"Get by status error: {str(e)}")
    
    async def get_running_analyses(self, limit: int = 100) -> List[RepositoryAnalysis]:
        """Get currently running analyses."""
        return await self.get_by_status("running", limit=limit)
    
    async def get_completed_analyses(
        self,
        limit: int = 100,
        days: int = 30
    ) -> List[RepositoryAnalysis]:
        """Get recently completed analyses."""
        async with get_database_session() as session:
            try:
                cutoff_date = datetime.utcnow() - timedelta(days=days)
                
                query = select(self.model_class).where(
                    and_(
                        self.model_class.status == "completed",
                        self.model_class.updated_at >= cutoff_date
                    )
                ).order_by(desc(self.model_class.updated_at)).limit(limit)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(
                    "Error getting completed analyses",
                    error=str(e)
                )
                raise DatabaseError(f"Get completed analyses error: {str(e)}")
    
    async def get_failed_analyses(
        self,
        limit: int = 100,
        days: int = 7
    ) -> List[RepositoryAnalysis]:
        """Get recently failed analyses."""
        async with get_database_session() as session:
            try:
                cutoff_date = datetime.utcnow() - timedelta(days=days)
                
                query = select(self.model_class).where(
                    and_(
                        self.model_class.status == "failed",
                        self.model_class.updated_at >= cutoff_date
                    )
                ).order_by(desc(self.model_class.updated_at)).limit(limit)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(
                    "Error getting failed analyses",
                    error=str(e)
                )
                raise DatabaseError(f"Get failed analyses error: {str(e)}")
    
    async def get_latest_analysis(
        self,
        repository_id: str,
        analysis_type: Optional[str] = None
    ) -> Optional[RepositoryAnalysis]:
        """Get the latest analysis for a repository."""
        async with get_database_session() as session:
            try:
                query = select(self.model_class).where(
                    self.model_class.repository_id == repository_id
                )
                
                if analysis_type:
                    query = query.where(self.model_class.analysis_type == analysis_type)
                
                query = query.order_by(desc(self.model_class.created_at)).limit(1)
                
                result = await session.execute(query)
                return result.scalar_one_or_none()
                
            except Exception as e:
                logger.error(
                    "Error getting latest analysis",
                    repository_id=repository_id,
                    error=str(e)
                )
                raise DatabaseError(f"Get latest analysis error: {str(e)}")
    
    async def update_analysis_status(
        self,
        analysis_id: str,
        status: str,
        progress: Optional[int] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """Update analysis status and progress."""
        try:
            update_data = {
                "status": status,
                "updated_at": datetime.utcnow()
            }
            
            if progress is not None:
                update_data["progress"] = progress
            
            if error_message:
                update_data["error_message"] = error_message
            
            result = await self.update(analysis_id, **update_data)
            
            if result:
                logger.info(
                    "Updated analysis status",
                    analysis_id=analysis_id,
                    status=status,
                    progress=progress
                )
                return True
            
            return False
            
        except Exception as e:
            logger.error(
                "Error updating analysis status",
                analysis_id=analysis_id,
                error=str(e)
            )
            raise DatabaseError(f"Update status error: {str(e)}")
    
    async def update_analysis_results(
        self,
        analysis_id: str,
        results: Dict[str, Any],
        performance_metrics: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Update analysis results and metrics."""
        try:
            update_data = {
                "updated_at": datetime.utcnow()
            }
            
            # Update basic metrics from results
            if "total_files" in results:
                update_data["total_files"] = results["total_files"]
            if "analyzed_files" in results:
                update_data["analyzed_files"] = results["analyzed_files"]
            if "total_lines" in results:
                update_data["total_lines"] = results["total_lines"]
            if "primary_language" in results:
                update_data["primary_language"] = results["primary_language"]
            if "languages_detected" in results:
                update_data["languages_detected"] = results["languages_detected"]
            
            # Update pattern counts
            if "patterns_detected" in results:
                update_data["patterns_detected"] = results["patterns_detected"]
            if "critical_patterns" in results:
                update_data["critical_patterns"] = results["critical_patterns"]
            if "high_severity_patterns" in results:
                update_data["high_severity_patterns"] = results["high_severity_patterns"]
            if "medium_severity_patterns" in results:
                update_data["medium_severity_patterns"] = results["medium_severity_patterns"]
            if "low_severity_patterns" in results:
                update_data["low_severity_patterns"] = results["low_severity_patterns"]
            
            # Update quality scores
            if "code_quality_score" in results:
                update_data["code_quality_score"] = results["code_quality_score"]
            if "maintainability_score" in results:
                update_data["maintainability_score"] = results["maintainability_score"]
            if "security_score" in results:
                update_data["security_score"] = results["security_score"]
            
            # Update performance metrics
            if performance_metrics:
                if "analysis_duration_ms" in performance_metrics:
                    update_data["analysis_duration_ms"] = performance_metrics["analysis_duration_ms"]
                if "cpu_time_ms" in performance_metrics:
                    update_data["cpu_time_ms"] = performance_metrics["cpu_time_ms"]
                if "memory_peak_mb" in performance_metrics:
                    update_data["memory_peak_mb"] = performance_metrics["memory_peak_mb"]
            
            result = await self.update(analysis_id, **update_data)
            
            if result:
                logger.info(
                    "Updated analysis results",
                    analysis_id=analysis_id,
                    patterns_detected=results.get("patterns_detected", 0)
                )
                return True
            
            return False
            
        except Exception as e:
            logger.error(
                "Error updating analysis results",
                analysis_id=analysis_id,
                error=str(e)
            )
            raise DatabaseError(f"Update results error: {str(e)}")
    
    async def get_analysis_statistics(
        self,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get analysis statistics."""
        async with get_database_session() as session:
            try:
                cutoff_date = datetime.utcnow() - timedelta(days=days)
                
                # Total analyses
                total_count = await session.execute(
                    select(func.count(self.model_class.id)).where(
                        self.model_class.created_at >= cutoff_date
                    )
                )
                total_analyses = total_count.scalar() or 0
                
                # Analyses by status
                status_stats = await session.execute(
                    select(
                        self.model_class.status,
                        func.count().label('count')
                    ).where(
                        self.model_class.created_at >= cutoff_date
                    ).group_by(self.model_class.status)
                )
                
                status_counts = {row.status: row.count for row in status_stats}
                
                # Analyses by type
                type_stats = await session.execute(
                    select(
                        self.model_class.analysis_type,
                        func.count().label('count')
                    ).where(
                        self.model_class.created_at >= cutoff_date
                    ).group_by(self.model_class.analysis_type)
                )
                
                type_counts = {row.analysis_type: row.count for row in type_stats}
                
                # Average duration for completed analyses
                avg_duration = await session.execute(
                    select(func.avg(self.model_class.analysis_duration_ms)).where(
                        and_(
                            self.model_class.created_at >= cutoff_date,
                            self.model_class.status == "completed",
                            self.model_class.analysis_duration_ms.is_not(None)
                        )
                    )
                )
                average_duration = avg_duration.scalar() or 0.0
                
                # Success rate
                success_rate = 0.0
                if total_analyses > 0:
                    completed_count = status_counts.get("completed", 0)
                    success_rate = completed_count / total_analyses
                
                return {
                    "total_analyses": total_analyses,
                    "status_distribution": status_counts,
                    "type_distribution": type_counts,
                    "average_duration_ms": float(average_duration),
                    "success_rate": success_rate,
                    "time_period_days": days
                }
                
            except Exception as e:
                logger.error(
                    "Error getting analysis statistics",
                    error=str(e)
                )
                raise DatabaseError(f"Get analysis statistics error: {str(e)}")
    
    async def get_repository_analysis_summary(
        self,
        repository_id: str
    ) -> Dict[str, Any]:
        """Get analysis summary for a repository."""
        async with get_database_session() as session:
            try:
                # Get latest analysis
                latest_analysis = await self.get_latest_analysis(repository_id)
                
                if not latest_analysis:
                    return {
                        "repository_id": repository_id,
                        "has_analysis": False,
                        "latest_analysis": None
                    }
                
                # Get analysis history
                history = await self.get_by_repository(
                    repository_id,
                    limit=10,
                    offset=0
                )
                
                # Calculate trends
                completed_analyses = [
                    a for a in history 
                    if a.status == "completed" and a.patterns_detected is not None
                ]
                
                pattern_trend = None
                if len(completed_analyses) >= 2:
                    recent_patterns = completed_analyses[0].patterns_detected or 0
                    previous_patterns = completed_analyses[1].patterns_detected or 0
                    if previous_patterns > 0:
                        pattern_trend = (recent_patterns - previous_patterns) / previous_patterns
                
                return {
                    "repository_id": repository_id,
                    "has_analysis": True,
                    "latest_analysis": {
                        "analysis_id": latest_analysis.analysis_id,
                        "status": latest_analysis.status,
                        "analysis_type": latest_analysis.analysis_type,
                        "created_at": latest_analysis.created_at,
                        "progress": latest_analysis.progress,
                        "patterns_detected": latest_analysis.patterns_detected,
                        "primary_language": latest_analysis.primary_language,
                        "code_quality_score": latest_analysis.code_quality_score,
                        "security_score": latest_analysis.security_score
                    },
                    "analysis_history_count": len(history),
                    "pattern_trend": pattern_trend,
                    "total_analyses": len(history),
                    "completed_analyses": len(completed_analyses)
                }
                
            except Exception as e:
                logger.error(
                    "Error getting repository analysis summary",
                    repository_id=repository_id,
                    error=str(e)
                )
                raise DatabaseError(f"Get repository summary error: {str(e)}")
    
    async def get_slow_analyses(
        self,
        threshold_ms: int = 300000,  # 5 minutes
        limit: int = 50
    ) -> List[RepositoryAnalysis]:
        """Get analyses that took longer than threshold."""
        async with get_database_session() as session:
            try:
                query = select(self.model_class).where(
                    and_(
                        self.model_class.analysis_duration_ms > threshold_ms,
                        self.model_class.status == "completed"
                    )
                ).order_by(desc(self.model_class.analysis_duration_ms)).limit(limit)
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                logger.error(
                    "Error getting slow analyses",
                    error=str(e)
                )
                raise DatabaseError(f"Get slow analyses error: {str(e)}")
    
    async def cleanup_old_analyses(
        self,
        days: int = 90,
        keep_latest: int = 5
    ) -> int:
        """Clean up old analysis records, keeping the latest N per repository."""
        async with get_database_session() as session:
            try:
                cutoff_date = datetime.utcnow() - timedelta(days=days)
                
                # Get repositories with analyses
                repositories = await session.execute(
                    select(self.model_class.repository_id).distinct()
                )
                
                deleted_count = 0
                
                for (repo_id,) in repositories:
                    # Get all analyses for this repository
                    analyses = await session.execute(
                        select(self.model_class).where(
                            and_(
                                self.model_class.repository_id == repo_id,
                                self.model_class.created_at < cutoff_date
                            )
                        ).order_by(desc(self.model_class.created_at))
                    )
                    
                    analyses_list = analyses.scalars().all()
                    
                    # Keep the latest N analyses, delete the rest
                    if len(analyses_list) > keep_latest:
                        to_delete = analyses_list[keep_latest:]
                        
                        for analysis in to_delete:
                            await session.delete(analysis)
                            deleted_count += 1
                
                await session.commit()
                
                logger.info(
                    "Cleaned up old analyses",
                    deleted_count=deleted_count,
                    days=days,
                    keep_latest=keep_latest
                )
                
                return deleted_count
                
            except Exception as e:
                await session.rollback()
                logger.error(
                    "Error cleaning up old analyses",
                    error=str(e)
                )
                raise DatabaseError(f"Cleanup error: {str(e)}")
    
    async def get_performance_metrics(
        self,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get performance metrics for analyses."""
        try:
            # Use BigQuery for complex analytics
            query = f"""
            SELECT 
                analysis_type,
                COUNT(*) as total_analyses,
                AVG(analysis_duration_ms) as avg_duration_ms,
                STDDEV(analysis_duration_ms) as stddev_duration_ms,
                MIN(analysis_duration_ms) as min_duration_ms,
                MAX(analysis_duration_ms) as max_duration_ms,
                AVG(cpu_time_ms) as avg_cpu_time_ms,
                AVG(memory_peak_mb) as avg_memory_mb,
                AVG(total_files) as avg_files_processed,
                AVG(patterns_detected) as avg_patterns_detected,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) / COUNT(*) as success_rate
            FROM `{self.bigquery_table_name}`
            WHERE created_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL {days} DAY)
                AND analysis_duration_ms IS NOT NULL
            GROUP BY analysis_type
            ORDER BY total_analyses DESC
            """
            
            results = await self.query_bigquery(query)
            
            return {
                "time_period_days": days,
                "by_analysis_type": results,
                "generated_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(
                "Error getting performance metrics",
                error=str(e)
            )
            # Fallback to simpler PostgreSQL query
            return await self.get_analysis_statistics(days=days)
    
    async def start_analysis(
        self,
        repository_id: str,
        analysis_type: str = "full",
        commit_sha: str = "HEAD",
        repository_name: str = "",
        repository_url: str = "",
        branch: str = "main"
    ) -> RepositoryAnalysis:
        """Start a new analysis job."""
        try:
            analysis_id = str(uuid.uuid4())
            
            analysis = RepositoryAnalysis(
                repository_id=repository_id,
                analysis_id=analysis_id,
                commit_sha=commit_sha,
                analysis_type=analysis_type,
                status="running",
                progress=0,
                repository_name=repository_name,
                repository_url=repository_url,
                branch=branch
            )
            
            created_analysis = await self.create(analysis)
            
            logger.info(
                "Started new analysis",
                analysis_id=analysis_id,
                repository_id=repository_id,
                analysis_type=analysis_type
            )
            
            return created_analysis
            
        except Exception as e:
            logger.error(
                "Error starting analysis",
                repository_id=repository_id,
                error=str(e)
            )
            raise DatabaseError(f"Start analysis error: {str(e)}")
    
    async def complete_analysis(
        self,
        analysis_id: str,
        results: Dict[str, Any],
        duration_ms: int
    ) -> bool:
        """Complete an analysis job."""
        try:
            update_data = {
                "status": "completed",
                "progress": 100,
                "analysis_duration_ms": duration_ms,
                "updated_at": datetime.utcnow()
            }
            
            # Update all results
            await self.update_analysis_results(analysis_id, results)
            
            # Update status
            result = await self.update(analysis_id, **update_data)
            
            if result:
                logger.info(
                    "Completed analysis",
                    analysis_id=analysis_id,
                    duration_ms=duration_ms
                )
                return True
            
            return False
            
        except Exception as e:
            logger.error(
                "Error completing analysis",
                analysis_id=analysis_id,
                error=str(e)
            )
            raise DatabaseError(f"Complete analysis error: {str(e)}")
    
    async def fail_analysis(
        self,
        analysis_id: str,
        error_message: str,
        duration_ms: Optional[int] = None
    ) -> bool:
        """Mark an analysis as failed."""
        try:
            update_data = {
                "status": "failed",
                "error_message": error_message,
                "updated_at": datetime.utcnow()
            }
            
            if duration_ms is not None:
                update_data["analysis_duration_ms"] = duration_ms
            
            result = await self.update(analysis_id, **update_data)
            
            if result:
                logger.error(
                    "Failed analysis",
                    analysis_id=analysis_id,
                    error_message=error_message
                )
                return True
            
            return False
            
        except Exception as e:
            logger.error(
                "Error failing analysis",
                analysis_id=analysis_id,
                error=str(e)
            )
            raise DatabaseError(f"Fail analysis error: {str(e)}")