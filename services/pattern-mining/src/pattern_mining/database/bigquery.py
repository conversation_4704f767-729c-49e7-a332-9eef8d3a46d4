"""
BigQuery Connection Utilities

Async BigQuery client with connection pooling, authentication, and error handling.
Optimized for production use with streaming inserts and query optimization.
"""

import asyncio
import json
import logging
from contextlib import asynccontextmanager
from typing import (
    Any,
    AsyncGenerator,
    Dict,
    List,
    Optional,
    Union,
    Tuple,
    Callable,
    TypeVar,
)
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import uuid
from functools import wraps
import time

from google.cloud import bigquery
from google.cloud.bigquery import Client, Query<PERSON>ob, LoadJob
from google.cloud.bigquery.retry import DEFAULT_RETRY
from google.cloud.bigquery.table import Table
from google.cloud.bigquery.dataset import Dataset
from google.cloud.bigquery.job import WriteDisposition, CreateDisposition
from google.cloud.bigquery.schema import Schema<PERSON>ield
from google.cloud.bigquery.streaming_insert import StreamingInsertJobConfig
from google.cloud.exceptions import (
    NotFound,
    Conflict,
    BadRequest,
    GoogleCloudError,
    RetryError,
    ServiceUnavailable,
)
from google.api_core.retry import Retry
from google.auth.exceptions import DefaultCredentialsError
import structlog

from ..config.settings import get_settings

logger = structlog.get_logger(__name__)

T = TypeVar("T")


@dataclass
class BigQueryConnectionConfig:
    """BigQuery connection configuration."""
    
    project_id: str
    dataset_id: str
    location: str = "US"
    
    # Authentication
    credentials_path: Optional[str] = None
    service_account_json: Optional[str] = None
    
    # Connection pooling
    max_connections: int = 10
    connection_timeout: int = 30
    read_timeout: int = 300
    
    # Query configuration
    dry_run: bool = False
    use_query_cache: bool = True
    use_legacy_sql: bool = False
    maximum_bytes_billed: Optional[int] = None
    
    # Streaming configuration
    max_streaming_batch_size: int = 1000
    streaming_buffer_timeout: int = 5
    ignore_unknown_values: bool = False
    skip_invalid_rows: bool = False
    
    # Retry configuration
    max_retry_attempts: int = 3
    retry_delay: float = 1.0
    retry_multiplier: float = 2.0
    retry_max_delay: float = 60.0
    
    # Performance optimization
    enable_bi_engine: bool = True
    enable_clustering: bool = True
    enable_partitioning: bool = True
    
    # Cost optimization
    enable_slot_autoscaling: bool = True
    max_slots: int = 2000
    target_job_concurrency: int = 100


@dataclass
class QueryMetrics:
    """Query execution metrics."""
    
    query_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_ms: Optional[int] = None
    bytes_processed: Optional[int] = None
    bytes_billed: Optional[int] = None
    slot_ms: Optional[int] = None
    cache_hit: Optional[bool] = None
    error: Optional[str] = None
    
    def __post_init__(self):
        if self.end_time and self.start_time:
            self.duration_ms = int((self.end_time - self.start_time).total_seconds() * 1000)


class BigQueryError(Exception):
    """BigQuery-specific error."""
    
    def __init__(self, message: str, original_error: Optional[Exception] = None):
        super().__init__(message)
        self.original_error = original_error


class BigQueryConnectionPool:
    """Connection pool for BigQuery clients."""
    
    def __init__(self, config: BigQueryConnectionConfig):
        self.config = config
        self._clients: List[Client] = []
        self._available_clients: asyncio.Queue = asyncio.Queue()
        self._lock = asyncio.Lock()
        self._initialized = False
        self._metrics: Dict[str, QueryMetrics] = {}
        
    async def initialize(self):
        """Initialize the connection pool."""
        if self._initialized:
            return
            
        async with self._lock:
            if self._initialized:
                return
                
            logger.info("Initializing BigQuery connection pool", 
                       pool_size=self.config.max_connections)
            
            try:
                # Create client pool
                for i in range(self.config.max_connections):
                    client = await self._create_client()
                    self._clients.append(client)
                    await self._available_clients.put(client)
                
                # Test connection
                test_client = await self._available_clients.get()
                await self._test_connection(test_client)
                await self._available_clients.put(test_client)
                
                self._initialized = True
                logger.info("BigQuery connection pool initialized successfully")
                
            except Exception as e:
                logger.error("Failed to initialize BigQuery connection pool", error=str(e))
                await self.close()
                raise BigQueryError(f"Failed to initialize connection pool: {str(e)}", e)
    
    async def _create_client(self) -> Client:
        """Create a BigQuery client."""
        try:
            if self.config.credentials_path:
                client = Client.from_service_account_json(
                    self.config.credentials_path,
                    project=self.config.project_id,
                    location=self.config.location
                )
            elif self.config.service_account_json:
                credentials_info = json.loads(self.config.service_account_json)
                client = Client.from_service_account_info(
                    credentials_info,
                    project=self.config.project_id,
                    location=self.config.location
                )
            else:
                client = Client(
                    project=self.config.project_id,
                    location=self.config.location
                )
            
            return client
            
        except DefaultCredentialsError as e:
            raise BigQueryError(f"Authentication failed: {str(e)}", e)
        except Exception as e:
            raise BigQueryError(f"Failed to create client: {str(e)}", e)
    
    async def _test_connection(self, client: Client):
        """Test BigQuery connection."""
        try:
            # Simple query to test connection
            query = "SELECT 1 as test"
            job = client.query(query, dry_run=True)
            logger.debug("BigQuery connection test successful")
        except Exception as e:
            raise BigQueryError(f"Connection test failed: {str(e)}", e)
    
    @asynccontextmanager
    async def get_client(self) -> AsyncGenerator[Client, None]:
        """Get a client from the pool."""
        if not self._initialized:
            await self.initialize()
            
        # Get client from pool
        try:
            client = await asyncio.wait_for(
                self._available_clients.get(),
                timeout=self.config.connection_timeout
            )
        except asyncio.TimeoutError:
            raise BigQueryError("Connection pool timeout")
        
        try:
            yield client
        finally:
            # Return client to pool
            await self._available_clients.put(client)
    
    async def close(self):
        """Close all connections in the pool."""
        async with self._lock:
            if not self._initialized:
                return
                
            logger.info("Closing BigQuery connection pool")
            
            # Close all clients
            for client in self._clients:
                try:
                    client.close()
                except Exception as e:
                    logger.warning("Error closing client", error=str(e))
            
            self._clients.clear()
            
            # Clear queue
            while not self._available_clients.empty():
                try:
                    self._available_clients.get_nowait()
                except asyncio.QueueEmpty:
                    break
            
            self._initialized = False
            logger.info("BigQuery connection pool closed")
    
    def get_metrics(self) -> Dict[str, QueryMetrics]:
        """Get query execution metrics."""
        return self._metrics.copy()
    
    def _record_metrics(self, query_id: str, metrics: QueryMetrics):
        """Record query metrics."""
        self._metrics[query_id] = metrics
        
        # Keep only last 1000 metrics
        if len(self._metrics) > 1000:
            oldest_key = min(self._metrics.keys(), key=lambda k: self._metrics[k].start_time)
            del self._metrics[oldest_key]


def with_retry(
    max_attempts: int = 3,
    delay: float = 1.0,
    multiplier: float = 2.0,
    max_delay: float = 60.0,
    exceptions: Tuple[Exception, ...] = (ServiceUnavailable, RetryError, GoogleCloudError)
):
    """Decorator for retry logic."""
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt == max_attempts - 1:
                        break
                    
                    logger.warning(
                        "Retrying after error",
                        attempt=attempt + 1,
                        max_attempts=max_attempts,
                        error=str(e),
                        delay=current_delay
                    )
                    
                    await asyncio.sleep(current_delay)
                    current_delay = min(current_delay * multiplier, max_delay)
                except Exception as e:
                    # Don't retry non-retryable exceptions
                    raise
            
            raise last_exception
        return wrapper
    return decorator


class BigQueryClient:
    """Async BigQuery client with connection pooling and optimizations."""
    
    def __init__(self, config: BigQueryConnectionConfig):
        self.config = config
        self.pool = BigQueryConnectionPool(config)
        self._dataset_ref = None
        
    async def initialize(self):
        """Initialize the BigQuery client."""
        await self.pool.initialize()
        self._dataset_ref = f"{self.config.project_id}.{self.config.dataset_id}"
        
        # Ensure dataset exists
        await self._ensure_dataset_exists()
        
        logger.info("BigQuery client initialized successfully")
    
    async def close(self):
        """Close the BigQuery client."""
        await self.pool.close()
    
    async def _ensure_dataset_exists(self):
        """Ensure the dataset exists."""
        async with self.pool.get_client() as client:
            try:
                dataset = client.get_dataset(self.config.dataset_id)
                logger.debug("Dataset exists", dataset_id=self.config.dataset_id)
            except NotFound:
                logger.info("Creating dataset", dataset_id=self.config.dataset_id)
                
                dataset = Dataset(self._dataset_ref)
                dataset.location = self.config.location
                dataset.description = "Pattern mining service dataset"
                
                # Set default table expiration
                dataset.default_table_expiration_ms = 90 * 24 * 60 * 60 * 1000  # 90 days
                
                client.create_dataset(dataset)
                logger.info("Dataset created successfully")
    
    @with_retry()
    async def execute_query(
        self,
        query: str,
        parameters: Optional[Dict[str, Any]] = None,
        dry_run: bool = False,
        use_cache: bool = True,
        maximum_bytes_billed: Optional[int] = None,
        timeout: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Execute a SQL query."""
        query_id = str(uuid.uuid4())
        start_time = datetime.utcnow()
        
        logger.debug(
            "Executing query",
            query_id=query_id,
            query_preview=query[:200] + "..." if len(query) > 200 else query,
            parameters=parameters,
            dry_run=dry_run
        )
        
        metrics = QueryMetrics(query_id=query_id, start_time=start_time)
        
        try:
            async with self.pool.get_client() as client:
                # Configure query job
                job_config = bigquery.QueryJobConfig(
                    dry_run=dry_run or self.config.dry_run,
                    use_query_cache=use_cache and self.config.use_query_cache,
                    use_legacy_sql=self.config.use_legacy_sql,
                    maximum_bytes_billed=maximum_bytes_billed or self.config.maximum_bytes_billed,
                    query_parameters=self._format_parameters(parameters) if parameters else None,
                )
                
                # Execute query
                query_job = client.query(query, job_config=job_config)
                
                # Wait for completion
                query_timeout = timeout or self.config.read_timeout
                results = query_job.result(timeout=query_timeout)
                
                # Convert to list of dictionaries
                rows = []
                if not dry_run:
                    for row in results:
                        rows.append(dict(row))
                
                # Record metrics
                metrics.end_time = datetime.utcnow()
                metrics.bytes_processed = query_job.total_bytes_processed
                metrics.bytes_billed = query_job.total_bytes_billed
                metrics.slot_ms = query_job.slot_millis
                metrics.cache_hit = query_job.cache_hit
                
                self.pool._record_metrics(query_id, metrics)
                
                logger.debug(
                    "Query completed successfully",
                    query_id=query_id,
                    duration_ms=metrics.duration_ms,
                    rows_returned=len(rows),
                    bytes_processed=metrics.bytes_processed,
                    cache_hit=metrics.cache_hit
                )
                
                return rows
                
        except Exception as e:
            metrics.end_time = datetime.utcnow()
            metrics.error = str(e)
            self.pool._record_metrics(query_id, metrics)
            
            logger.error(
                "Query execution failed",
                query_id=query_id,
                error=str(e),
                duration_ms=metrics.duration_ms
            )
            
            raise BigQueryError(f"Query execution failed: {str(e)}", e)
    
    def _format_parameters(self, parameters: Dict[str, Any]) -> List[bigquery.ScalarQueryParameter]:
        """Format query parameters for BigQuery."""
        formatted_params = []
        
        for name, value in parameters.items():
            if isinstance(value, str):
                param_type = "STRING"
            elif isinstance(value, int):
                param_type = "INT64"
            elif isinstance(value, float):
                param_type = "FLOAT64"
            elif isinstance(value, bool):
                param_type = "BOOL"
            elif isinstance(value, datetime):
                param_type = "TIMESTAMP"
            elif isinstance(value, list):
                # Handle arrays
                if value and isinstance(value[0], str):
                    param_type = "ARRAY<STRING>"
                elif value and isinstance(value[0], int):
                    param_type = "ARRAY<INT64>"
                elif value and isinstance(value[0], float):
                    param_type = "ARRAY<FLOAT64>"
                else:
                    param_type = "ARRAY<STRING>"
            else:
                # Convert to string for unknown types
                param_type = "STRING"
                value = str(value)
            
            formatted_params.append(
                bigquery.ScalarQueryParameter(name, param_type, value)
            )
        
        return formatted_params
    
    @with_retry()
    async def streaming_insert(
        self,
        table_name: str,
        rows: List[Dict[str, Any]],
        ignore_unknown_values: Optional[bool] = None,
        skip_invalid_rows: Optional[bool] = None,
        template_suffix: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Insert rows using streaming insert."""
        if not rows:
            return []
        
        logger.debug(
            "Streaming insert",
            table_name=table_name,
            row_count=len(rows),
            template_suffix=template_suffix
        )
        
        try:
            async with self.pool.get_client() as client:
                table_ref = client.dataset(self.config.dataset_id).table(table_name)
                table = client.get_table(table_ref)
                
                # Configure streaming insert
                ignore_unknown = (
                    ignore_unknown_values 
                    if ignore_unknown_values is not None 
                    else self.config.ignore_unknown_values
                )
                skip_invalid = (
                    skip_invalid_rows 
                    if skip_invalid_rows is not None 
                    else self.config.skip_invalid_rows
                )
                
                # Batch insert if necessary
                all_errors = []
                batch_size = self.config.max_streaming_batch_size
                
                for i in range(0, len(rows), batch_size):
                    batch = rows[i:i + batch_size]
                    
                    # Add insert IDs to prevent duplicates
                    rows_with_ids = []
                    for row in batch:
                        rows_with_ids.append({
                            "insertId": str(uuid.uuid4()),
                            "json": row
                        })
                    
                    # Insert batch
                    errors = client.insert_rows_json(
                        table,
                        [row["json"] for row in rows_with_ids],
                        row_ids=[row["insertId"] for row in rows_with_ids],
                        ignore_unknown_values=ignore_unknown,
                        skip_invalid_rows=skip_invalid,
                        template_suffix=template_suffix
                    )
                    
                    all_errors.extend(errors)
                
                if all_errors:
                    logger.error(
                        "Streaming insert errors",
                        table_name=table_name,
                        errors=all_errors
                    )
                    raise BigQueryError(f"Streaming insert errors: {all_errors}")
                
                logger.debug(
                    "Streaming insert completed successfully",
                    table_name=table_name,
                    rows_inserted=len(rows)
                )
                
                return all_errors
                
        except Exception as e:
            logger.error(
                "Streaming insert failed",
                table_name=table_name,
                error=str(e)
            )
            raise BigQueryError(f"Streaming insert failed: {str(e)}", e)
    
    @with_retry()
    async def create_table(
        self,
        table_name: str,
        schema: List[SchemaField],
        partition_field: Optional[str] = None,
        clustering_fields: Optional[List[str]] = None,
        description: Optional[str] = None,
        expiration_days: Optional[int] = None
    ) -> Table:
        """Create a BigQuery table."""
        logger.info(
            "Creating table",
            table_name=table_name,
            partition_field=partition_field,
            clustering_fields=clustering_fields
        )
        
        try:
            async with self.pool.get_client() as client:
                table_ref = client.dataset(self.config.dataset_id).table(table_name)
                table = Table(table_ref, schema=schema)
                
                # Set description
                if description:
                    table.description = description
                
                # Set expiration
                if expiration_days:
                    table.expires = datetime.utcnow() + timedelta(days=expiration_days)
                
                # Configure partitioning
                if partition_field and self.config.enable_partitioning:
                    table.time_partitioning = bigquery.TimePartitioning(
                        type_=bigquery.TimePartitioningType.DAY,
                        field=partition_field,
                        expiration_ms=expiration_days * 24 * 60 * 60 * 1000 if expiration_days else None
                    )
                
                # Configure clustering
                if clustering_fields and self.config.enable_clustering:
                    table.clustering_fields = clustering_fields
                
                # Create table
                table = client.create_table(table, exists_ok=False)
                
                logger.info(
                    "Table created successfully",
                    table_name=table_name,
                    table_id=table.table_id
                )
                
                return table
                
        except Conflict:
            logger.warning("Table already exists", table_name=table_name)
            raise BigQueryError(f"Table {table_name} already exists")
        except Exception as e:
            logger.error(
                "Table creation failed",
                table_name=table_name,
                error=str(e)
            )
            raise BigQueryError(f"Table creation failed: {str(e)}", e)
    
    @with_retry()
    async def table_exists(self, table_name: str) -> bool:
        """Check if a table exists."""
        try:
            async with self.pool.get_client() as client:
                table_ref = client.dataset(self.config.dataset_id).table(table_name)
                client.get_table(table_ref)
                return True
        except NotFound:
            return False
        except Exception as e:
            logger.error(
                "Error checking table existence",
                table_name=table_name,
                error=str(e)
            )
            raise BigQueryError(f"Error checking table existence: {str(e)}", e)
    
    @with_retry()
    async def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """Get table information."""
        try:
            async with self.pool.get_client() as client:
                table_ref = client.dataset(self.config.dataset_id).table(table_name)
                table = client.get_table(table_ref)
                
                return {
                    "table_id": table.table_id,
                    "dataset_id": table.dataset_id,
                    "project": table.project,
                    "created": table.created,
                    "modified": table.modified,
                    "num_rows": table.num_rows,
                    "num_bytes": table.num_bytes,
                    "schema": [
                        {
                            "name": field.name,
                            "type": field.field_type,
                            "mode": field.mode,
                            "description": field.description
                        }
                        for field in table.schema
                    ],
                    "description": table.description,
                    "partitioning": {
                        "type": table.time_partitioning.type_ if table.time_partitioning else None,
                        "field": table.time_partitioning.field if table.time_partitioning else None,
                    },
                    "clustering": table.clustering_fields,
                }
                
        except NotFound:
            raise BigQueryError(f"Table {table_name} not found")
        except Exception as e:
            logger.error(
                "Error getting table info",
                table_name=table_name,
                error=str(e)
            )
            raise BigQueryError(f"Error getting table info: {str(e)}", e)
    
    async def get_connection_status(self) -> Dict[str, Any]:
        """Get connection pool status."""
        return {
            "initialized": self.pool._initialized,
            "pool_size": len(self.pool._clients),
            "available_connections": self.pool._available_clients.qsize(),
            "dataset_id": self.config.dataset_id,
            "project_id": self.config.project_id,
            "location": self.config.location,
            "metrics_count": len(self.pool._metrics)
        }
    
    async def get_query_metrics(self) -> Dict[str, Any]:
        """Get query execution metrics."""
        metrics = self.pool.get_metrics()
        
        if not metrics:
            return {"total_queries": 0}
        
        total_queries = len(metrics)
        successful_queries = sum(1 for m in metrics.values() if m.error is None)
        failed_queries = total_queries - successful_queries
        
        durations = [m.duration_ms for m in metrics.values() if m.duration_ms is not None]
        bytes_processed = [m.bytes_processed for m in metrics.values() if m.bytes_processed is not None]
        
        return {
            "total_queries": total_queries,
            "successful_queries": successful_queries,
            "failed_queries": failed_queries,
            "success_rate": successful_queries / total_queries if total_queries > 0 else 0,
            "avg_duration_ms": sum(durations) / len(durations) if durations else 0,
            "total_bytes_processed": sum(bytes_processed) if bytes_processed else 0,
            "cache_hit_rate": sum(1 for m in metrics.values() if m.cache_hit) / total_queries if total_queries > 0 else 0,
        }


# Global BigQuery client instance
_bigquery_client: Optional[BigQueryClient] = None


async def get_bigquery_client() -> BigQueryClient:
    """Get the global BigQuery client instance."""
    global _bigquery_client
    
    if _bigquery_client is None:
        from ..config.settings import get_settings
        settings = get_settings()
        
        config = BigQueryConnectionConfig(
            project_id=settings.gcp_project_id,
            dataset_id=settings.bigquery_dataset_id,
            location=settings.bigquery_location,
            credentials_path=settings.google_application_credentials,
            max_connections=settings.bigquery_max_connections,
            connection_timeout=settings.bigquery_connection_timeout,
            read_timeout=settings.bigquery_read_timeout,
            max_retry_attempts=settings.bigquery_max_retry_attempts,
            dry_run=settings.bigquery_dry_run,
            use_query_cache=settings.bigquery_use_cache,
            maximum_bytes_billed=settings.bigquery_max_bytes_billed,
        )
        
        _bigquery_client = BigQueryClient(config)
        await _bigquery_client.initialize()
    
    return _bigquery_client


async def close_bigquery_client():
    """Close the global BigQuery client."""
    global _bigquery_client
    
    if _bigquery_client is not None:
        await _bigquery_client.close()
        _bigquery_client = None


# Context manager for BigQuery client
@asynccontextmanager
async def get_bigquery_session() -> AsyncGenerator[BigQueryClient, None]:
    """Get BigQuery client session."""
    client = await get_bigquery_client()
    try:
        yield client
    finally:
        # Client is reused, no need to close here
        pass