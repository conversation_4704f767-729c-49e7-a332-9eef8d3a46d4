"""
M<PERSON> Pattern Detector - Gemini 2.5 Flash Integration

Advanced pattern detection using Google's Gemini 2.5 Flash model with thinking capabilities.
"""

from typing import Dict, Any, List, Optional
import asyncio
import logging
import uuid
import json

from ..models.patterns import PatternResult, PatternType, SeverityLevel
from ..ml.manager import get_ml_manager
from ..ml.gemini_client import Gemini<PERSON>lient
from ..config.gemini import get_gemini_config, GeminiModel
from .base import BasePatternDetector

logger = logging.getLogger(__name__)


class MLPatternDetector(BasePatternDetector):
    """Advanced pattern detector using Gemini 2.5 Flash."""
    
    def __init__(self):
        super().__init__()
        self.name = "GeminiMLPatternDetector"
        self.version = "2.0.0"
        self.ml_manager = get_ml_manager()
        self.gemini_config = get_gemini_config()
        self.gemini_client: Optional[GeminiClient] = None
        
        # Enhanced analysis type mapping for Gemini 2.5 Flash
        self.analysis_mapping = {
            PatternType.CODE_SMELL: "code_quality_analysis",
            PatternType.ANTI_PATTERN: "anti_pattern_detection",
            PatternType.DESIGN_PATTERN: "design_pattern_recognition",
            PatternType.SECURITY_ISSUE: "security_vulnerability_analysis",
            PatternType.PERFORMANCE_ISSUE: "performance_optimization_analysis"
        }
        
        # Confidence thresholds for different pattern types
        self.confidence_thresholds = {
            PatternType.SECURITY_ISSUE: 70,
            PatternType.PERFORMANCE_ISSUE: 65,
            PatternType.CODE_SMELL: 60,
            PatternType.ANTI_PATTERN: 70,
            PatternType.DESIGN_PATTERN: 55
        }
    
    async def detect_patterns(
        self,
        code: str,
        language: str,
        features: Dict[str, Any]
    ) -> List[PatternResult]:
        """Detect patterns using Gemini 2.5 Flash with thinking capabilities."""
        try:
            if not await self.validate_input(code, language, features):
                return []
            
            # Initialize Gemini client if not already done
            if not self.gemini_client:
                await self._initialize_gemini_client()
            
            patterns = []
            
            # Preprocess code
            preprocessed_code = await self.preprocess_code(code, language)
            
            # Use Gemini 2.5 Flash for comprehensive pattern analysis
            gemini_patterns = await self._detect_patterns_with_gemini(
                preprocessed_code, language, features
            )
            patterns.extend(gemini_patterns)
            
            # Run specific analysis types for detailed detection
            analysis_tasks = []
            
            # Security-focused analysis
            analysis_tasks.append(
                self._analyze_with_gemini(
                    preprocessed_code, language, "security_vulnerability_analysis"
                )
            )
            
            # Performance-focused analysis
            analysis_tasks.append(
                self._analyze_with_gemini(
                    preprocessed_code, language, "performance_optimization_analysis"
                )
            )
            
            # Code quality analysis
            analysis_tasks.append(
                self._analyze_with_gemini(
                    preprocessed_code, language, "code_quality_analysis"
                )
            )
            
            # Wait for all analyses to complete
            results = await asyncio.gather(*analysis_tasks, return_exceptions=True)
            
            # Process results from specific analyses
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"Gemini analysis failed: {str(result)}")
                    continue
                
                if isinstance(result, list):
                    patterns.extend(result)
            
            # Deduplicate and filter patterns
            patterns = await self._deduplicate_patterns(patterns)
            patterns = await self._filter_by_confidence(patterns)
            
            # Postprocess patterns
            patterns = await self.postprocess_patterns(patterns)
            
            return patterns
        
        except Exception as e:
            logger.error(f"Error in Gemini pattern detection: {str(e)}")
            return []
    
    async def _initialize_gemini_client(self):
        """Initialize Gemini client for pattern detection."""
        try:
            self.gemini_client = GeminiClient(self.gemini_config)
            await self.gemini_client.start()
            logger.info("Gemini client initialized for pattern detection")
        except Exception as e:
            logger.error(f"Failed to initialize Gemini client: {e}")
            raise
    
    async def _detect_patterns_with_gemini(
        self,
        code: str,
        language: str,
        features: Dict[str, Any]
    ) -> List[PatternResult]:
        """Detect patterns using Gemini 2.5 Flash comprehensive analysis."""
        try:
            # Run comprehensive pattern analysis
            analysis_result = await self.gemini_client.analyze_code_with_thinking(
                code=code,
                language=language,
                analysis_type="comprehensive_pattern_analysis"
            )
            
            patterns = []
            findings = analysis_result.get("findings", [])
            
            for finding in findings:
                try:
                    pattern = await self._convert_gemini_finding_to_pattern(
                        finding, code, language
                    )
                    if pattern:
                        patterns.append(pattern)
                except Exception as e:
                    logger.warning(f"Failed to convert finding to pattern: {e}")
                    continue
            
            return patterns
        
        except Exception as e:
            logger.error(f"Error in Gemini pattern detection: {e}")
            return []
    
    async def _analyze_with_gemini(
        self,
        code: str,
        language: str,
        analysis_type: str
    ) -> List[PatternResult]:
        """Run specific analysis with Gemini 2.5 Flash."""
        try:
            analysis_result = await self.gemini_client.analyze_code_with_thinking(
                code=code,
                language=language,
                analysis_type=analysis_type
            )
            
            patterns = []
            findings = analysis_result.get("findings", [])
            
            for finding in findings:
                try:
                    pattern = await self._convert_gemini_finding_to_pattern(
                        finding, code, language, analysis_type
                    )
                    if pattern:
                        patterns.append(pattern)
                except Exception as e:
                    logger.warning(f"Failed to convert {analysis_type} finding: {e}")
                    continue
            
            return patterns
        
        except Exception as e:
            logger.error(f"Error in Gemini {analysis_type}: {e}")
            return []
    
    async def _convert_gemini_finding_to_pattern(
        self,
        finding: Dict[str, Any],
        code: str,
        language: str,
        analysis_type: Optional[str] = None
    ) -> Optional[PatternResult]:
        """Convert Gemini finding to PatternResult."""
        try:
            # Map finding type to PatternType
            finding_type = finding.get("type", "unknown")
            pattern_type = self._map_finding_type_to_pattern_type(finding_type)
            
            # Check confidence threshold
            confidence = finding.get("confidence", 0) / 100.0  # Convert to 0-1 range
            threshold = self.confidence_thresholds.get(pattern_type, 50) / 100.0
            
            if confidence < threshold:
                return None
            
            # Extract location information
            location = finding.get("location", {})
            if not location:
                location = {"line_start": 1, "line_end": len(code.split('\n'))}
            
            # Extract code snippet
            lines = code.split('\n')
            start_line = max(0, location.get("line_start", 1) - 1)
            end_line = min(len(lines), location.get("line_end", len(lines)))
            code_snippet = '\n'.join(lines[start_line:end_line])
            
            # Create pattern result
            pattern = self._create_pattern_result(
                pattern_id=str(uuid.uuid4()),
                pattern_type=pattern_type,
                name=finding.get("name", "Unknown Pattern"),
                description=finding.get("description", ""),
                severity=finding.get("severity", "medium"),
                confidence=confidence,
                location=location,
                code_snippet=code_snippet,
                suggestions=finding.get("recommendations", [])
            )
            
            # Add metadata from Gemini analysis
            if hasattr(pattern, 'metadata'):
                pattern.metadata.update({
                    "gemini_analysis_type": analysis_type,
                    "gemini_finding_type": finding_type,
                    "model_used": "gemini-2.5-flash"
                })
            
            return pattern
        
        except Exception as e:
            logger.error(f"Error converting Gemini finding: {e}")
            return None
    
    def _map_finding_type_to_pattern_type(self, finding_type: str) -> PatternType:
        """Map Gemini finding type to internal PatternType."""
        type_mapping = {
            "code_smell": PatternType.CODE_SMELL,
            "anti_pattern": PatternType.ANTI_PATTERN,
            "pattern": PatternType.DESIGN_PATTERN,
            "design_pattern": PatternType.DESIGN_PATTERN,
            "security": PatternType.SECURITY_ISSUE,
            "security_issue": PatternType.SECURITY_ISSUE,
            "vulnerability": PatternType.SECURITY_ISSUE,
            "performance": PatternType.PERFORMANCE_ISSUE,
            "performance_issue": PatternType.PERFORMANCE_ISSUE,
            "optimization": PatternType.PERFORMANCE_ISSUE
        }
        
        return type_mapping.get(finding_type.lower(), PatternType.CODE_SMELL)
    
    
    async def _deduplicate_patterns(self, patterns: List[PatternResult]) -> List[PatternResult]:
        """Remove duplicate patterns based on similarity."""
        if not patterns:
            return patterns
        
        deduplicated = []
        seen_patterns = set()
        
        for pattern in patterns:
            # Create a signature for the pattern
            signature = f"{pattern.pattern_type}:{pattern.name}:{pattern.location}"
            
            if signature not in seen_patterns:
                seen_patterns.add(signature)
                deduplicated.append(pattern)
            else:
                # If similar pattern exists, keep the one with higher confidence
                for i, existing in enumerate(deduplicated):
                    existing_sig = f"{existing.pattern_type}:{existing.name}:{existing.location}"
                    if existing_sig == signature and pattern.confidence > existing.confidence:
                        deduplicated[i] = pattern
                        break
        
        return deduplicated
    
    async def _filter_by_confidence(self, patterns: List[PatternResult]) -> List[PatternResult]:
        """Filter patterns by confidence thresholds."""
        filtered = []
        
        for pattern in patterns:
            threshold = self.confidence_thresholds.get(pattern.pattern_type, 50) / 100.0
            if pattern.confidence >= threshold:
                filtered.append(pattern)
        
        return filtered
    
    async def batch_detect_patterns(
        self,
        code_samples: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Batch detect patterns across multiple code samples."""
        try:
            if not self.gemini_client:
                await self._initialize_gemini_client()
            
            # Prepare batch analysis
            batch_data = []
            for sample in code_samples:
                batch_data.append({
                    "code": sample.get("code", ""),
                    "language": sample.get("language", "python"),
                    "id": sample.get("id", str(uuid.uuid4()))
                })
            
            # Run batch analysis with Gemini
            results = await self.gemini_client.batch_analyze_code(
                batch_data, "comprehensive_pattern_analysis"
            )
            
            # Convert results to pattern format
            pattern_results = []
            for i, result in enumerate(results):
                sample = code_samples[i] if i < len(code_samples) else {}
                
                if "error" in result:
                    pattern_results.append({
                        "sample_id": result.get("sample_id", f"sample_{i}"),
                        "patterns": [],
                        "error": result["error"]
                    })
                    continue
                
                # Convert findings to patterns
                patterns = []
                for finding in result.get("findings", []):
                    pattern = await self._convert_gemini_finding_to_pattern(
                        finding, sample.get("code", ""), sample.get("language", "python")
                    )
                    if pattern:
                        patterns.append(pattern)
                
                pattern_results.append({
                    "sample_id": result.get("sample_id", f"sample_{i}"),
                    "patterns": patterns,
                    "thinking_process": result.get("thinking_process", ""),
                    "quality_score": result.get("quality_score", 0)
                })
            
            return pattern_results
        
        except Exception as e:
            logger.error(f"Error in batch pattern detection: {e}")
            return []
    
    async def get_pattern_statistics(self) -> Dict[str, Any]:
        """Get statistics about pattern detection performance."""
        try:
            if not self.gemini_client:
                return {"status": "not_initialized"}
            
            metrics = self.gemini_client.get_metrics()
            
            return {
                "status": "active",
                "model": "gemini-2.5-flash",
                "total_requests": metrics.get("total_requests", 0),
                "success_rate": metrics.get("success_rate", 0),
                "avg_response_time": metrics.get("avg_response_time", 0),
                "cache_hit_rate": metrics.get("cache_hit_rate", 0),
                "supported_pattern_types": await self.get_supported_pattern_types(),
                "confidence_thresholds": self.confidence_thresholds
            }
        except Exception as e:
            logger.error(f"Error getting pattern statistics: {e}")
            return {"status": "error", "error": str(e)}
    
    async def get_supported_pattern_types(self) -> List[PatternType]:
        """Get supported pattern types."""
        return [
            PatternType.CODE_SMELL,
            PatternType.ANTI_PATTERN,
            PatternType.DESIGN_PATTERN,
            PatternType.SECURITY_ISSUE,
            PatternType.PERFORMANCE_ISSUE
        ]
    
    async def get_analysis_capabilities(self) -> Dict[str, Any]:
        """Get detailed analysis capabilities."""
        return {
            "model": "gemini-2.5-flash",
            "thinking_enabled": True,
            "supported_languages": [
                "python", "javascript", "typescript", "java", "go", 
                "rust", "cpp", "c", "csharp", "php", "ruby", "kotlin", "swift"
            ],
            "analysis_types": list(self.analysis_mapping.values()),
            "confidence_thresholds": self.confidence_thresholds,
            "features": [
                "comprehensive_pattern_analysis",
                "security_vulnerability_detection",
                "performance_optimization_analysis",
                "code_quality_assessment",
                "design_pattern_recognition",
                "anti_pattern_detection"
            ]
        }
    
    async def cleanup(self):
        """Cleanup Gemini detector resources."""
        try:
            if self.gemini_client:
                await self.gemini_client.close()
                self.gemini_client = None
            logger.info("Gemini ML detector cleaned up successfully")
        except Exception as e:
            logger.error(f"Error cleaning up Gemini detector: {e}")