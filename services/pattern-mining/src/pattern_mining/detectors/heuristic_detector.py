"""
Heuristic Pattern Detector

Rule-based pattern detection using heuristics.
"""

from typing import Dict, Any, List, Optional
import asyncio
import logging
import uuid
import re

from ..models.patterns import PatternResult, PatternType, SeverityLevel
from .base import BasePatternDetector

logger = logging.getLogger(__name__)


class HeuristicPatternDetector(BasePatternDetector):
    """Heuristic-based pattern detector."""
    
    def __init__(self):
        super().__init__()
        self.name = "HeuristicPatternDetector"
        self.version = "1.0.0"
        self.rules = self._load_detection_rules()
    
    def _load_detection_rules(self) -> Dict[str, Any]:
        """Load detection rules."""
        return {
            "code_smells": {
                "long_method": {"threshold": 50, "severity": "medium"},
                "long_parameter_list": {"threshold": 5, "severity": "medium"},
                "duplicate_code": {"threshold": 3, "severity": "medium"},
                "large_class": {"threshold": 20, "severity": "high"},
                "feature_envy": {"threshold": 0.7, "severity": "medium"},
                "data_clumps": {"threshold": 3, "severity": "medium"}
            },
            "anti_patterns": {
                "god_object": {"threshold": 30, "severity": "high"},
                "spaghetti_code": {"threshold": 15, "severity": "high"},
                "copy_paste": {"threshold": 0.8, "severity": "medium"},
                "magic_numbers": {"threshold": 3, "severity": "medium"},
                "global_variables": {"threshold": 1, "severity": "medium"}
            },
            "security_issues": {
                "sql_injection": {"severity": "critical"},
                "xss_vulnerability": {"severity": "critical"},
                "hardcoded_credentials": {"severity": "critical"},
                "insecure_random": {"severity": "high"},
                "path_traversal": {"severity": "high"}
            },
            "performance_issues": {
                "inefficient_loop": {"threshold": 3, "severity": "medium"},
                "memory_leak": {"severity": "high"},
                "unnecessary_computation": {"severity": "medium"},
                "blocking_operations": {"severity": "medium"}
            }
        }
    
    async def detect_patterns(
        self,
        code: str,
        language: str,
        features: Dict[str, Any]
    ) -> List[PatternResult]:
        """Detect patterns using heuristic rules."""
        try:
            if not await self.validate_input(code, language, features):
                return []
            
            patterns = []
            
            # Preprocess code
            preprocessed_code = await self.preprocess_code(code, language)
            
            # Run different detection methods
            detection_methods = [
                self._detect_code_smells_heuristic,
                self._detect_anti_patterns_heuristic,
                self._detect_security_issues_heuristic,
                self._detect_performance_issues_heuristic
            ]
            
            for method in detection_methods:
                try:
                    method_patterns = await method(preprocessed_code, language, features)
                    patterns.extend(method_patterns)
                except Exception as e:
                    logger.error(f"Error in detection method {method.__name__}: {str(e)}")
            
            # Postprocess patterns
            patterns = await self.postprocess_patterns(patterns)
            
            return patterns
        
        except Exception as e:
            logger.error(f"Error in heuristic pattern detection: {str(e)}")
            return []
    
    async def _detect_code_smells_heuristic(
        self,
        code: str,
        language: str,
        features: Dict[str, Any]
    ) -> List[PatternResult]:
        """Detect code smells using heuristic rules."""
        try:
            patterns = []
            lines = code.split('\n')
            
            # Long method detection
            patterns.extend(await self._detect_long_methods(code, lines, features))
            
            # Long parameter list detection
            patterns.extend(await self._detect_long_parameter_lists(code, lines, features))
            
            # Large class detection
            patterns.extend(await self._detect_large_classes(code, lines, features))
            
            # Duplicate code detection
            patterns.extend(await self._detect_duplicate_code_heuristic(code, lines, features))
            
            # Magic numbers detection
            patterns.extend(await self._detect_magic_numbers(code, lines, features))
            
            # Deep nesting detection
            patterns.extend(await self._detect_deep_nesting(code, lines, features))
            
            return patterns
        
        except Exception as e:
            logger.error(f"Error detecting code smells: {str(e)}")
            return []
    
    async def _detect_long_methods(
        self,
        code: str,
        lines: List[str],
        features: Dict[str, Any]
    ) -> List[PatternResult]:
        """Detect long methods."""
        patterns = []
        threshold = self.rules["code_smells"]["long_method"]["threshold"]
        
        current_method = None
        method_start = 0
        method_line_count = 0
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            
            # Detect method start
            if stripped.startswith('def ') and ':' in stripped:
                # Save previous method if it was long
                if current_method and method_line_count > threshold:
                    pattern = self._create_pattern_result(
                        pattern_id=str(uuid.uuid4()),
                        pattern_type=PatternType.CODE_SMELL,
                        name="Long Method",
                        description=f"Method '{current_method}' is too long ({method_line_count} lines)",
                        severity="medium",
                        confidence=0.8,
                        location={"line_start": method_start + 1, "line_end": i},
                        code_snippet=self._extract_code_snippet(code, method_start + 1, i),
                        suggestions=self._generate_suggestions(PatternType.CODE_SMELL, "Long Method")
                    )
                    patterns.append(pattern)
                
                # Start new method
                current_method = stripped.split('(')[0].replace('def ', '')
                method_start = i
                method_line_count = 0
            
            # Count lines in current method
            if current_method and (stripped or i == method_start):
                method_line_count += 1
        
        # Check last method
        if current_method and method_line_count > threshold:
            pattern = self._create_pattern_result(
                pattern_id=str(uuid.uuid4()),
                pattern_type=PatternType.CODE_SMELL,
                name="Long Method",
                description=f"Method '{current_method}' is too long ({method_line_count} lines)",
                severity="medium",
                confidence=0.8,
                location={"line_start": method_start + 1, "line_end": len(lines)},
                code_snippet=self._extract_code_snippet(code, method_start + 1, len(lines)),
                suggestions=self._generate_suggestions(PatternType.CODE_SMELL, "Long Method")
            )
            patterns.append(pattern)
        
        return patterns
    
    async def _detect_long_parameter_lists(
        self,
        code: str,
        lines: List[str],
        features: Dict[str, Any]
    ) -> List[PatternResult]:
        """Detect long parameter lists."""
        patterns = []
        threshold = self.rules["code_smells"]["long_parameter_list"]["threshold"]
        
        # Find function definitions
        func_pattern = r'def\s+(\w+)\s*\((.*?)\):'
        
        for i, line in enumerate(lines):
            match = re.search(func_pattern, line)
            if match:
                func_name = match.group(1)
                params_str = match.group(2)
                
                # Count parameters
                params = [p.strip() for p in params_str.split(',') if p.strip()]
                
                if len(params) > threshold:
                    pattern = self._create_pattern_result(
                        pattern_id=str(uuid.uuid4()),
                        pattern_type=PatternType.CODE_SMELL,
                        name="Long Parameter List",
                        description=f"Function '{func_name}' has too many parameters ({len(params)})",
                        severity="medium",
                        confidence=0.9,
                        location={"line_start": i + 1, "line_end": i + 1},
                        code_snippet=line,
                        suggestions=[
                            "Consider using parameter objects",
                            "Break down the function into smaller functions",
                            "Use keyword arguments or configuration objects"
                        ]
                    )
                    patterns.append(pattern)
        
        return patterns
    
    async def _detect_large_classes(
        self,
        code: str,
        lines: List[str],
        features: Dict[str, Any]
    ) -> List[PatternResult]:
        """Detect large classes."""
        patterns = []
        threshold = self.rules["code_smells"]["large_class"]["threshold"]
        
        current_class = None
        class_start = 0
        method_count = 0
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            
            # Detect class start
            if stripped.startswith('class ') and ':' in stripped:
                # Save previous class if it was large
                if current_class and method_count > threshold:
                    pattern = self._create_pattern_result(
                        pattern_id=str(uuid.uuid4()),
                        pattern_type=PatternType.CODE_SMELL,
                        name="Large Class",
                        description=f"Class '{current_class}' has too many methods ({method_count})",
                        severity="high",
                        confidence=0.85,
                        location={"line_start": class_start + 1, "line_end": i},
                        code_snippet=self._extract_code_snippet(code, class_start + 1, min(i, class_start + 10)),
                        suggestions=self._generate_suggestions(PatternType.CODE_SMELL, "Large Class")
                    )
                    patterns.append(pattern)
                
                # Start new class
                current_class = stripped.split('(')[0].replace('class ', '').replace(':', '')
                class_start = i
                method_count = 0
            
            # Count methods in current class
            elif current_class and stripped.startswith('def '):
                method_count += 1
        
        # Check last class
        if current_class and method_count > threshold:
            pattern = self._create_pattern_result(
                pattern_id=str(uuid.uuid4()),
                pattern_type=PatternType.CODE_SMELL,
                name="Large Class",
                description=f"Class '{current_class}' has too many methods ({method_count})",
                severity="high",
                confidence=0.85,
                location={"line_start": class_start + 1, "line_end": len(lines)},
                code_snippet=self._extract_code_snippet(code, class_start + 1, min(len(lines), class_start + 10)),
                suggestions=self._generate_suggestions(PatternType.CODE_SMELL, "Large Class")
            )
            patterns.append(pattern)
        
        return patterns
    
    async def _detect_duplicate_code_heuristic(
        self,
        code: str,
        lines: List[str],
        features: Dict[str, Any]
    ) -> List[PatternResult]:
        """Detect duplicate code blocks."""
        patterns = []
        threshold = self.rules["code_smells"]["duplicate_code"]["threshold"]
        
        # Find duplicate lines
        line_occurrences = {}
        for i, line in enumerate(lines):
            stripped = line.strip()
            if stripped and not stripped.startswith('#') and len(stripped) > 10:
                if stripped not in line_occurrences:
                    line_occurrences[stripped] = []
                line_occurrences[stripped].append(i)
        
        # Find lines that appear multiple times
        for line_content, occurrences in line_occurrences.items():
            if len(occurrences) >= threshold:
                pattern = self._create_pattern_result(
                    pattern_id=str(uuid.uuid4()),
                    pattern_type=PatternType.CODE_SMELL,
                    name="Duplicate Code",
                    description=f"Code line appears {len(occurrences)} times",
                    severity="medium",
                    confidence=0.7,
                    location={"line_start": occurrences[0] + 1, "line_end": occurrences[-1] + 1},
                    code_snippet=line_content,
                    suggestions=self._generate_suggestions(PatternType.CODE_SMELL, "Duplicate Code")
                )
                patterns.append(pattern)
        
        return patterns
    
    async def _detect_magic_numbers(
        self,
        code: str,
        lines: List[str],
        features: Dict[str, Any]
    ) -> List[PatternResult]:
        """Detect magic numbers."""
        patterns = []
        
        # Find numeric literals (excluding common values like 0, 1, 2)
        magic_number_pattern = r'\b(?<![\w.])[3-9]\d*\b|\b\d{2,}\b'
        
        for i, line in enumerate(lines):
            if not line.strip().startswith('#'):
                matches = re.finditer(magic_number_pattern, line)
                for match in matches:
                    number = match.group()
                    if number not in ['10', '100', '1000']:  # Common acceptable numbers
                        pattern = self._create_pattern_result(
                            pattern_id=str(uuid.uuid4()),
                            pattern_type=PatternType.CODE_SMELL,
                            name="Magic Number",
                            description=f"Magic number '{number}' should be replaced with a named constant",
                            severity="low",
                            confidence=0.6,
                            location={"line_start": i + 1, "line_end": i + 1},
                            code_snippet=line.strip(),
                            suggestions=[
                                "Replace magic numbers with named constants",
                                "Use configuration files for configurable values",
                                "Add comments explaining the significance of the number"
                            ]
                        )
                        patterns.append(pattern)
        
        return patterns
    
    async def _detect_deep_nesting(
        self,
        code: str,
        lines: List[str],
        features: Dict[str, Any]
    ) -> List[PatternResult]:
        """Detect deeply nested code."""
        patterns = []
        max_depth = 0
        current_depth = 0
        deep_line = 0
        
        for i, line in enumerate(lines):
            # Calculate indentation depth
            indent = len(line) - len(line.lstrip())
            if line.strip():
                depth = indent // 4  # Assuming 4-space indentation
                current_depth = depth
                
                if current_depth > max_depth:
                    max_depth = current_depth
                    deep_line = i
        
        if max_depth > 4:  # Threshold for deep nesting
            pattern = self._create_pattern_result(
                pattern_id=str(uuid.uuid4()),
                pattern_type=PatternType.CODE_SMELL,
                name="Deep Nesting",
                description=f"Code has deep nesting level ({max_depth})",
                severity="medium",
                confidence=0.8,
                location={"line_start": deep_line + 1, "line_end": deep_line + 1},
                code_snippet=lines[deep_line],
                suggestions=[
                    "Reduce nesting by using early returns",
                    "Extract nested logic into separate functions",
                    "Use guard clauses to reduce complexity"
                ]
            )
            patterns.append(pattern)
        
        return patterns
    
    async def _detect_anti_patterns_heuristic(
        self,
        code: str,
        language: str,
        features: Dict[str, Any]
    ) -> List[PatternResult]:
        """Detect anti-patterns using heuristic rules."""
        try:
            patterns = []
            
            # Global variables detection
            patterns.extend(await self._detect_global_variables(code, features))
            
            # God object detection
            patterns.extend(await self._detect_god_object(code, features))
            
            # Copy-paste programming detection
            patterns.extend(await self._detect_copy_paste_programming(code, features))
            
            return patterns
        
        except Exception as e:
            logger.error(f"Error detecting anti-patterns: {str(e)}")
            return []
    
    async def _detect_global_variables(
        self,
        code: str,
        features: Dict[str, Any]
    ) -> List[PatternResult]:
        """Detect global variables."""
        patterns = []
        lines = code.split('\n')
        
        # Look for global variable declarations
        for i, line in enumerate(lines):
            stripped = line.strip()
            if stripped.startswith('global ') or (
                '=' in stripped and 
                not stripped.startswith(('def ', 'class ', ' ', '\t', '#'))
            ):
                pattern = self._create_pattern_result(
                    pattern_id=str(uuid.uuid4()),
                    pattern_type=PatternType.ANTI_PATTERN,
                    name="Global Variable",
                    description="Global variable usage detected",
                    severity="medium",
                    confidence=0.7,
                    location={"line_start": i + 1, "line_end": i + 1},
                    code_snippet=line,
                    suggestions=[
                        "Avoid global variables when possible",
                        "Use dependency injection instead",
                        "Consider using class attributes or function parameters"
                    ]
                )
                patterns.append(pattern)
        
        return patterns
    
    async def _detect_god_object(
        self,
        code: str,
        features: Dict[str, Any]
    ) -> List[PatternResult]:
        """Detect god object pattern."""
        patterns = []
        
        # Use AST features to detect god objects
        ast_features = features.get("ast", {})
        class_metrics = ast_features.get("class_metrics", {})
        
        avg_methods = class_metrics.get("avg_methods", 0)
        threshold = self.rules["anti_patterns"]["god_object"]["threshold"]
        
        if avg_methods > threshold:
            pattern = self._create_pattern_result(
                pattern_id=str(uuid.uuid4()),
                pattern_type=PatternType.ANTI_PATTERN,
                name="God Object",
                description=f"Class has too many responsibilities ({avg_methods} methods)",
                severity="high",
                confidence=0.85,
                location={"line_start": 1, "line_end": len(code.split('\n'))},
                suggestions=self._generate_suggestions(PatternType.ANTI_PATTERN, "God Object")
            )
            patterns.append(pattern)
        
        return patterns
    
    async def _detect_copy_paste_programming(
        self,
        code: str,
        features: Dict[str, Any]
    ) -> List[PatternResult]:
        """Detect copy-paste programming."""
        patterns = []
        
        # This is a simplified version - would use more sophisticated analysis
        text_features = features.get("text", {})
        ngrams = text_features.get("ngrams", {})
        
        # Check for repeated code patterns
        if ngrams:
            trigrams = ngrams.get("trigrams", {})
            for trigram, count in trigrams.items():
                if count > 3:  # Threshold for copy-paste detection
                    pattern = self._create_pattern_result(
                        pattern_id=str(uuid.uuid4()),
                        pattern_type=PatternType.ANTI_PATTERN,
                        name="Copy-Paste Programming",
                        description=f"Code pattern '{trigram}' appears {count} times",
                        severity="medium",
                        confidence=0.6,
                        location={"line_start": 1, "line_end": len(code.split('\n'))},
                        suggestions=[
                            "Extract common code into reusable functions",
                            "Use inheritance or composition to avoid duplication",
                            "Consider creating utility classes"
                        ]
                    )
                    patterns.append(pattern)
        
        return patterns
    
    async def _detect_security_issues_heuristic(
        self,
        code: str,
        language: str,
        features: Dict[str, Any]
    ) -> List[PatternResult]:
        """Detect security issues using heuristic rules."""
        try:
            patterns = []
            
            # SQL injection detection
            patterns.extend(await self._detect_sql_injection_heuristic(code))
            
            # XSS vulnerability detection
            patterns.extend(await self._detect_xss_vulnerability(code))
            
            # Hardcoded credentials detection
            patterns.extend(await self._detect_hardcoded_credentials_heuristic(code))
            
            # Insecure random detection
            patterns.extend(await self._detect_insecure_random(code))
            
            return patterns
        
        except Exception as e:
            logger.error(f"Error detecting security issues: {str(e)}")
            return []
    
    async def _detect_sql_injection_heuristic(self, code: str) -> List[PatternResult]:
        """Detect SQL injection vulnerabilities."""
        patterns = []
        lines = code.split('\n')
        
        sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'CREATE']
        
        for i, line in enumerate(lines):
            upper_line = line.upper()
            
            # Check for string concatenation with SQL
            if any(keyword in upper_line for keyword in sql_keywords):
                if '+' in line or '%' in line or '.format(' in line or 'f"' in line:
                    pattern = self._create_pattern_result(
                        pattern_id=str(uuid.uuid4()),
                        pattern_type=PatternType.SECURITY_ISSUE,
                        name="SQL Injection Risk",
                        description="Potential SQL injection vulnerability detected",
                        severity="critical",
                        confidence=0.8,
                        location={"line_start": i + 1, "line_end": i + 1},
                        code_snippet=line,
                        suggestions=self._generate_suggestions(PatternType.SECURITY_ISSUE, "SQL Injection Risk")
                    )
                    patterns.append(pattern)
        
        return patterns
    
    async def _detect_xss_vulnerability(self, code: str) -> List[PatternResult]:
        """Detect XSS vulnerabilities."""
        patterns = []
        lines = code.split('\n')
        
        xss_indicators = ['innerHTML', 'outerHTML', 'document.write', 'eval(']
        
        for i, line in enumerate(lines):
            if any(indicator in line for indicator in xss_indicators):
                pattern = self._create_pattern_result(
                    pattern_id=str(uuid.uuid4()),
                    pattern_type=PatternType.SECURITY_ISSUE,
                    name="XSS Vulnerability",
                    description="Potential XSS vulnerability detected",
                    severity="high",
                    confidence=0.7,
                    location={"line_start": i + 1, "line_end": i + 1},
                    code_snippet=line,
                    suggestions=[
                        "Sanitize user input before rendering",
                        "Use safe DOM manipulation methods",
                        "Implement Content Security Policy"
                    ]
                )
                patterns.append(pattern)
        
        return patterns
    
    async def _detect_hardcoded_credentials_heuristic(self, code: str) -> List[PatternResult]:
        """Detect hardcoded credentials."""
        patterns = []
        lines = code.split('\n')
        
        credential_patterns = [
            r'password\s*=\s*["\'][^"\']+["\']',
            r'secret\s*=\s*["\'][^"\']+["\']',
            r'key\s*=\s*["\'][^"\']+["\']',
            r'token\s*=\s*["\'][^"\']+["\']'
        ]
        
        for i, line in enumerate(lines):
            for pattern in credential_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    pattern_result = self._create_pattern_result(
                        pattern_id=str(uuid.uuid4()),
                        pattern_type=PatternType.SECURITY_ISSUE,
                        name="Hardcoded Credentials",
                        description="Hardcoded credentials detected",
                        severity="critical",
                        confidence=0.9,
                        location={"line_start": i + 1, "line_end": i + 1},
                        code_snippet=line,
                        suggestions=self._generate_suggestions(PatternType.SECURITY_ISSUE, "Hardcoded Credentials")
                    )
                    patterns.append(pattern_result)
        
        return patterns
    
    async def _detect_insecure_random(self, code: str) -> List[PatternResult]:
        """Detect insecure random number generation."""
        patterns = []
        lines = code.split('\n')
        
        insecure_random_patterns = ['random.random()', 'Math.random()', 'rand()']
        
        for i, line in enumerate(lines):
            if any(pattern in line for pattern in insecure_random_patterns):
                pattern = self._create_pattern_result(
                    pattern_id=str(uuid.uuid4()),
                    pattern_type=PatternType.SECURITY_ISSUE,
                    name="Insecure Random",
                    description="Insecure random number generation detected",
                    severity="medium",
                    confidence=0.7,
                    location={"line_start": i + 1, "line_end": i + 1},
                    code_snippet=line,
                    suggestions=[
                        "Use cryptographically secure random number generators",
                        "Use secrets module in Python",
                        "Use crypto.randomBytes() in Node.js"
                    ]
                )
                patterns.append(pattern)
        
        return patterns
    
    async def _detect_performance_issues_heuristic(
        self,
        code: str,
        language: str,
        features: Dict[str, Any]
    ) -> List[PatternResult]:
        """Detect performance issues using heuristic rules."""
        try:
            patterns = []
            
            # Inefficient loop detection
            patterns.extend(await self._detect_inefficient_loops_heuristic(code))
            
            # Memory leak detection
            patterns.extend(await self._detect_memory_leaks_heuristic(code))
            
            # Unnecessary computation detection
            patterns.extend(await self._detect_unnecessary_computation(code))
            
            return patterns
        
        except Exception as e:
            logger.error(f"Error detecting performance issues: {str(e)}")
            return []
    
    async def _detect_inefficient_loops_heuristic(self, code: str) -> List[PatternResult]:
        """Detect inefficient loops."""
        patterns = []
        lines = code.split('\n')
        
        # Track nested loops
        loop_depth = 0
        loop_stack = []
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            
            if stripped.startswith(('for ', 'while ')):
                loop_depth += 1
                loop_stack.append((i, stripped))
                
                if loop_depth > 2:  # Threshold for inefficient nesting
                    pattern = self._create_pattern_result(
                        pattern_id=str(uuid.uuid4()),
                        pattern_type=PatternType.PERFORMANCE_ISSUE,
                        name="Inefficient Loop Nesting",
                        description=f"Deeply nested loops detected (depth: {loop_depth})",
                        severity="medium",
                        confidence=0.7,
                        location={"line_start": i + 1, "line_end": i + 1},
                        code_snippet=line,
                        suggestions=self._generate_suggestions(PatternType.PERFORMANCE_ISSUE, "Inefficient Loop Nesting")
                    )
                    patterns.append(pattern)
            
            elif not stripped.startswith((' ', '\t')) and loop_depth > 0:
                # Reset loop depth for top-level statements
                loop_depth = 0
                loop_stack = []
        
        return patterns
    
    async def _detect_memory_leaks_heuristic(self, code: str) -> List[PatternResult]:
        """Detect potential memory leaks."""
        patterns = []
        lines = code.split('\n')
        
        # Track resource allocation without proper cleanup
        open_resources = ['open(', 'socket(', 'urllib.urlopen(']
        close_resources = ['close()', 'with ', 'try:', 'finally:']
        
        for i, line in enumerate(lines):
            if any(resource in line for resource in open_resources):
                # Check if there's proper cleanup in nearby lines
                has_cleanup = False
                for j in range(i, min(i + 10, len(lines))):
                    if any(cleanup in lines[j] for cleanup in close_resources):
                        has_cleanup = True
                        break
                
                if not has_cleanup:
                    pattern = self._create_pattern_result(
                        pattern_id=str(uuid.uuid4()),
                        pattern_type=PatternType.PERFORMANCE_ISSUE,
                        name="Memory Leak Risk",
                        description="Resource opened without proper cleanup",
                        severity="high",
                        confidence=0.6,
                        location={"line_start": i + 1, "line_end": i + 1},
                        code_snippet=line,
                        suggestions=self._generate_suggestions(PatternType.PERFORMANCE_ISSUE, "Memory Leak Risk")
                    )
                    patterns.append(pattern)
        
        return patterns
    
    async def _detect_unnecessary_computation(self, code: str) -> List[PatternResult]:
        """Detect unnecessary computations."""
        patterns = []
        lines = code.split('\n')
        
        # Look for computations in loops that could be moved outside
        in_loop = False
        loop_start = 0
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            
            if stripped.startswith(('for ', 'while ')):
                in_loop = True
                loop_start = i
            elif in_loop and not stripped.startswith((' ', '\t')):
                in_loop = False
            elif in_loop and ('len(' in line or 'range(' in line):
                # These could potentially be moved outside the loop
                pattern = self._create_pattern_result(
                    pattern_id=str(uuid.uuid4()),
                    pattern_type=PatternType.PERFORMANCE_ISSUE,
                    name="Unnecessary Computation",
                    description="Computation inside loop that could be optimized",
                    severity="low",
                    confidence=0.5,
                    location={"line_start": i + 1, "line_end": i + 1},
                    code_snippet=line,
                    suggestions=[
                        "Move invariant computations outside loops",
                        "Cache expensive function calls",
                        "Use list comprehensions when appropriate"
                    ]
                )
                patterns.append(pattern)
        
        return patterns
    
    async def get_supported_pattern_types(self) -> List[PatternType]:
        """Get supported pattern types."""
        return [
            PatternType.CODE_SMELL,
            PatternType.ANTI_PATTERN,
            PatternType.SECURITY_ISSUE,
            PatternType.PERFORMANCE_ISSUE
        ]
    
    async def update_config(self, config: Dict[str, Any]) -> bool:
        """Update detector configuration."""
        try:
            if "rules" in config:
                self.rules.update(config["rules"])
            return await super().update_config(config)
        except Exception as e:
            logger.error(f"Error updating heuristic detector config: {str(e)}")
            return False