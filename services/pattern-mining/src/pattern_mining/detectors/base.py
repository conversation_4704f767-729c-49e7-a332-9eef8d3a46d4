"""
Base Pattern Detector

Abstract base class for pattern detectors.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
import asyncio
import logging

from ..models.patterns import PatternResult, PatternType, DetectionType

logger = logging.getLogger(__name__)


class BasePatternDetector(ABC):
    """Base class for pattern detectors."""
    
    def __init__(self):
        self.name = self.__class__.__name__
        self.version = "1.0.0"
        self.config = {}
    
    @abstractmethod
    async def detect_patterns(
        self,
        code: str,
        language: str,
        features: Dict[str, Any]
    ) -> List[PatternResult]:
        """
        Detect patterns in the given code.
        
        Args:
            code: Source code to analyze
            language: Programming language
            features: Extracted features from the code
            
        Returns:
            List of detected patterns
        """
        pass
    
    @abstractmethod
    async def get_supported_pattern_types(self) -> List[PatternType]:
        """
        Get list of pattern types supported by this detector.
        
        Returns:
            List of supported pattern types
        """
        pass
    
    async def get_detector_info(self) -> Dict[str, Any]:
        """
        Get information about this detector.
        
        Returns:
            Dictionary containing detector information
        """
        return {
            "name": self.name,
            "version": self.version,
            "config": self.config,
            "supported_pattern_types": await self.get_supported_pattern_types()
        }
    
    async def update_config(self, config: Dict[str, Any]) -> bool:
        """
        Update detector configuration.
        
        Args:
            config: New configuration parameters
            
        Returns:
            True if configuration was updated successfully
        """
        try:
            self.config.update(config)
            return True
        except Exception as e:
            logger.error(f"Error updating config for {self.name}: {str(e)}")
            return False
    
    def get_version(self) -> str:
        """Get detector version."""
        return self.version
    
    async def validate_input(self, code: str, language: str, features: Dict[str, Any]) -> bool:
        """
        Validate input parameters.
        
        Args:
            code: Source code to analyze
            language: Programming language
            features: Extracted features from the code
            
        Returns:
            True if input is valid
        """
        if not code or not code.strip():
            return False
        
        if not language:
            return False
        
        if not isinstance(features, dict):
            return False
        
        return True
    
    async def preprocess_code(self, code: str, language: str) -> str:
        """
        Preprocess code before pattern detection.
        
        Args:
            code: Source code to preprocess
            language: Programming language
            
        Returns:
            Preprocessed code
        """
        # Default implementation - return code as-is
        return code
    
    async def postprocess_patterns(self, patterns: List[PatternResult]) -> List[PatternResult]:
        """
        Postprocess detected patterns.
        
        Args:
            patterns: List of detected patterns
            
        Returns:
            Postprocessed patterns
        """
        # Default implementation - return patterns as-is
        return patterns
    
    async def cleanup(self):
        """Cleanup detector resources."""
        # Default implementation - no cleanup needed
        pass
    
    def _create_pattern_result(
        self,
        pattern_id: str,
        pattern_type: PatternType,
        name: str,
        description: str,
        severity: str,
        confidence: float,
        location: Dict[str, Any],
        code_snippet: Optional[str] = None,
        suggestions: Optional[List[str]] = None,
        references: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> PatternResult:
        """
        Create a PatternResult object.
        
        Args:
            pattern_id: Unique pattern identifier
            pattern_type: Type of pattern
            name: Pattern name
            description: Pattern description
            severity: Severity level
            confidence: Confidence score
            location: Pattern location information
            code_snippet: Code snippet (optional)
            suggestions: Improvement suggestions (optional)
            references: Reference links (optional)
            metadata: Additional metadata (optional)
            
        Returns:
            PatternResult object
        """
        from ..models.patterns import PatternLocation, SeverityLevel
        
        # Create location object
        pattern_location = PatternLocation(
            file_path=location.get("file_path"),
            line_start=location.get("line_start", 1),
            line_end=location.get("line_end", 1),
            column_start=location.get("column_start"),
            column_end=location.get("column_end")
        )
        
        # Convert severity string to enum
        severity_enum = SeverityLevel(severity.lower())
        
        # Create pattern result
        pattern_result = PatternResult(
            pattern_id=pattern_id,
            pattern_type=pattern_type,
            name=name,
            description=description,
            severity=severity_enum,
            confidence=confidence,
            location=pattern_location,
            detection_method=DetectionType.HEURISTIC,  # Will be overridden by manager
            code_snippet=code_snippet,
            suggestions=suggestions or [],
            references=references or [],
            metadata=metadata or {}
        )
        
        return pattern_result
    
    def _extract_code_snippet(self, code: str, line_start: int, line_end: int) -> str:
        """
        Extract code snippet from source code.
        
        Args:
            code: Source code
            line_start: Starting line number
            line_end: Ending line number
            
        Returns:
            Code snippet
        """
        lines = code.split('\n')
        
        # Adjust for 0-based indexing
        start_idx = max(0, line_start - 1)
        end_idx = min(len(lines), line_end)
        
        snippet_lines = lines[start_idx:end_idx]
        return '\n'.join(snippet_lines)
    
    def _calculate_confidence(self, indicators: List[float]) -> float:
        """
        Calculate confidence score from multiple indicators.
        
        Args:
            indicators: List of confidence indicators (0.0 to 1.0)
            
        Returns:
            Overall confidence score
        """
        if not indicators:
            return 0.0
        
        # Use weighted average (can be customized per detector)
        weights = [1.0] * len(indicators)
        weighted_sum = sum(i * w for i, w in zip(indicators, weights))
        total_weight = sum(weights)
        
        return weighted_sum / total_weight if total_weight > 0 else 0.0
    
    def _generate_suggestions(self, pattern_type: PatternType, pattern_name: str) -> List[str]:
        """
        Generate improvement suggestions for a pattern.
        
        Args:
            pattern_type: Type of pattern
            pattern_name: Name of pattern
            
        Returns:
            List of suggestions
        """
        # Default suggestions based on pattern type
        suggestions = []
        
        if pattern_type == PatternType.CODE_SMELL:
            suggestions.append("Consider refactoring this code to improve readability")
            suggestions.append("Break down complex logic into smaller functions")
        elif pattern_type == PatternType.ANTI_PATTERN:
            suggestions.append("Replace this anti-pattern with a better design")
            suggestions.append("Review design patterns that could solve this problem")
        elif pattern_type == PatternType.SECURITY_ISSUE:
            suggestions.append("Review security implications of this code")
            suggestions.append("Follow secure coding practices")
        elif pattern_type == PatternType.PERFORMANCE_ISSUE:
            suggestions.append("Optimize this code for better performance")
            suggestions.append("Consider using more efficient algorithms or data structures")
        
        return suggestions
    
    def _get_pattern_references(self, pattern_type: PatternType, pattern_name: str) -> List[str]:
        """
        Get reference links for a pattern.
        
        Args:
            pattern_type: Type of pattern
            pattern_name: Name of pattern
            
        Returns:
            List of reference URLs
        """
        # Default references
        references = []
        
        if pattern_type == PatternType.DESIGN_PATTERN:
            references.append("https://refactoring.guru/design-patterns")
        elif pattern_type == PatternType.CODE_SMELL:
            references.append("https://refactoring.guru/refactoring/smells")
        elif pattern_type == PatternType.SECURITY_ISSUE:
            references.append("https://owasp.org/www-project-top-ten/")
        
        return references