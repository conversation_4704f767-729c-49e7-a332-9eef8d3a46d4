"""
Pattern Detector Manager

Manages different pattern detection algorithms.
"""

from typing import Dict, Any, List, Optional
import asyncio
import logging
from datetime import datetime

from ..models.patterns import PatternResult, PatternType, DetectionType
from .base import BasePatternDetector
from .ml_detector import M<PERSON>atternDetector
from .heuristic_detector import HeuristicPatternDetector

logger = logging.getLogger(__name__)


class PatternDetectorManager:
    """Pattern detector manager."""
    
    def __init__(self):
        self.detectors: Dict[DetectionType, BasePatternDetector] = {
            DetectionType.ML_INFERENCE: MLPatternDetector(),
            DetectionType.HEURISTIC: HeuristicPatternDetector(),
            DetectionType.HYBRID: MLPatternDetector()  # Use ML for hybrid for now
        }
    
    async def detect_patterns(
        self,
        code: str,
        language: str,
        features: Dict[str, Any],
        detection_types: List[DetectionType]
    ) -> List[PatternResult]:
        """Detect patterns using specified detection types."""
        try:
            all_patterns = []
            
            # Run detectors concurrently
            tasks = []
            for detection_type in detection_types:
                if detection_type in self.detectors:
                    detector = self.detectors[detection_type]
                    task = detector.detect_patterns(code, language, features)
                    tasks.append((detection_type, task))
            
            # Wait for all detectors to complete
            results = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)
            
            # Process results
            for i, result in enumerate(results):
                detection_type, _ = tasks[i]
                
                if isinstance(result, Exception):
                    logger.error(f"Detection failed for {detection_type}: {str(result)}")
                    continue
                
                # Add detection method to patterns
                for pattern in result:
                    pattern.detection_method = detection_type
                    all_patterns.append(pattern)
            
            # Remove duplicates and merge similar patterns
            unique_patterns = await self._deduplicate_patterns(all_patterns)
            
            return unique_patterns
        
        except Exception as e:
            logger.error(f"Error in pattern detection: {str(e)}")
            return []
    
    async def _deduplicate_patterns(self, patterns: List[PatternResult]) -> List[PatternResult]:
        """Remove duplicate patterns and merge similar ones."""
        try:
            if not patterns:
                return []
            
            # Simple deduplication based on pattern type and location
            unique_patterns = []
            seen_patterns = set()
            
            for pattern in patterns:
                # Create a signature for the pattern
                signature = (
                    pattern.pattern_type,
                    pattern.name,
                    pattern.location.line_start,
                    pattern.location.line_end
                )
                
                if signature not in seen_patterns:
                    seen_patterns.add(signature)
                    unique_patterns.append(pattern)
                else:
                    # Find existing pattern and merge if confidence is higher
                    for existing_pattern in unique_patterns:
                        existing_signature = (
                            existing_pattern.pattern_type,
                            existing_pattern.name,
                            existing_pattern.location.line_start,
                            existing_pattern.location.line_end
                        )
                        
                        if existing_signature == signature:
                            if pattern.confidence > existing_pattern.confidence:
                                # Replace with higher confidence pattern
                                unique_patterns.remove(existing_pattern)
                                unique_patterns.append(pattern)
                            break
            
            return unique_patterns
        
        except Exception as e:
            logger.error(f"Error deduplicating patterns: {str(e)}")
            return patterns
    
    async def get_detector_info(self, detection_type: DetectionType) -> Optional[Dict[str, Any]]:
        """Get information about a detector."""
        if detection_type not in self.detectors:
            return None
        
        detector = self.detectors[detection_type]
        return await detector.get_detector_info()
    
    async def get_supported_pattern_types(self, detection_type: DetectionType) -> List[PatternType]:
        """Get supported pattern types for a detector."""
        if detection_type not in self.detectors:
            return []
        
        detector = self.detectors[detection_type]
        return await detector.get_supported_pattern_types()
    
    async def update_detector_config(
        self,
        detection_type: DetectionType,
        config: Dict[str, Any]
    ) -> bool:
        """Update detector configuration."""
        if detection_type not in self.detectors:
            return False
        
        detector = self.detectors[detection_type]
        return await detector.update_config(config)
    
    def get_model_versions(self) -> Dict[str, str]:
        """Get model versions for all detectors."""
        versions = {}
        for detection_type, detector in self.detectors.items():
            versions[detection_type.value] = detector.get_version()
        return versions
    
    async def benchmark_detectors(
        self,
        test_code: str,
        test_language: str,
        test_features: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Benchmark all detectors on test code."""
        benchmark_results = {}
        
        for detection_type, detector in self.detectors.items():
            try:
                start_time = datetime.utcnow()
                patterns = await detector.detect_patterns(test_code, test_language, test_features)
                end_time = datetime.utcnow()
                
                processing_time = (end_time - start_time).total_seconds()
                
                benchmark_results[detection_type.value] = {
                    "pattern_count": len(patterns),
                    "processing_time": processing_time,
                    "patterns_per_second": len(patterns) / processing_time if processing_time > 0 else 0,
                    "avg_confidence": sum(p.confidence for p in patterns) / len(patterns) if patterns else 0
                }
            
            except Exception as e:
                logger.error(f"Benchmark failed for {detection_type}: {str(e)}")
                benchmark_results[detection_type.value] = {
                    "error": str(e),
                    "pattern_count": 0,
                    "processing_time": 0
                }
        
        return benchmark_results
    
    async def validate_detector(self, detection_type: DetectionType) -> bool:
        """Validate that a detector is working correctly."""
        if detection_type not in self.detectors:
            return False
        
        try:
            detector = self.detectors[detection_type]
            
            # Test with simple code
            test_code = "def test_function():\n    pass"
            test_features = {"ast": {}, "semantic": {}, "text": {}}
            
            patterns = await detector.detect_patterns(test_code, "python", test_features)
            
            # Basic validation - detector should return a list
            return isinstance(patterns, list)
        
        except Exception as e:
            logger.error(f"Detector validation failed for {detection_type}: {str(e)}")
            return False
    
    async def cleanup(self):
        """Cleanup detector resources."""
        for detector in self.detectors.values():
            await detector.cleanup()
    
    @property
    def available_detectors(self) -> List[DetectionType]:
        """Get list of available detection types."""
        return list(self.detectors.keys())
    
    @property
    def detector_count(self) -> int:
        """Get number of available detectors."""
        return len(self.detectors)


# Global detector manager instance
_detector_manager: Optional[PatternDetectorManager] = None


def get_pattern_detector_manager() -> PatternDetectorManager:
    """Get pattern detector manager instance."""
    global _detector_manager
    if _detector_manager is None:
        _detector_manager = PatternDetectorManager()
    return _detector_manager