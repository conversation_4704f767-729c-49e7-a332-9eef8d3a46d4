# Pattern Mining Service - Development Dependencies
# These are only needed for development and testing
# Install with: pip install -r requirements-dev.txt

# Testing Framework
pytest
pytest-asyncio
pytest-cov
pytest-benchmark
pytest-mock
pytest-timeout
pytest-xdist  # Parallel test execution
hypothesis  # Property-based testing
faker  # Test data generation

# Code Quality & Linting
black
ruff  # Ultra-fast Python linter
mypy
pyright
isort
pre-commit
flake8  # Additional linting
pylint  # Comprehensive linting

# Documentation
mkdocs
mkdocs-material
mkdocstrings[python]
mkdocs-mermaid2-plugin
mkdocs-git-revision-date-localized-plugin

# Type Stubs
types-redis
types-requests
types-PyYAML
types-aiofiles
types-python-dateutil

# Development Tools
ipython
jupyter
notebook
jupyterlab
ipdb  # Interactive debugger
watchdog  # File system monitoring
python-dotenv[cli]  # .env management

# Profiling & Performance
py-spy  # Sampling profiler
memory-profiler
line-profiler
scalene  # High-performance profiler

# Security Testing
bandit[toml]
safety==3.3.0
pip-audit
semgrep  # Static analysis

# API Testing
httpx[cli]  # HTTP client with CLI
locust  # Load testing
responses  # Mock HTTP responses

# Database Tools
alembic[tz]  # Database migrations
sqlalchemy-utils
faker-sqlalchemy

# Debugging & Monitoring
rich  # Beautiful terminal output
icecream  # Better debugging
loguru  # Enhanced logging
python-json-logger

# Build & Packaging
build
wheel
setuptools
twine  # Package publishing

# Git Hooks
pre-commit
commitizen  # Conventional commits
gitlint

# Additional Development Utilities
python-decouple  # Settings management
environs  # Environment variable parsing
dynaconf  # Configuration management
pydantic-extra-types  # Extra Pydantic types
