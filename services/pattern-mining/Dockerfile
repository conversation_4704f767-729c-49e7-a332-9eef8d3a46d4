# Pattern Mining Service Multi-Stage Dockerfile
# Supports development, production, and GPU environments
# Build targets: development, production, production-gpu

ARG PYTHON_VERSION=3.13
ARG CUDA_VERSION=12.3

# =============================================================================
# Base stage with common dependencies
# =============================================================================
FROM python:${PYTHON_VERSION}-slim-bookworm AS base

# Set environment variables
ENV PYTHONPATH=/app/src \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    g++ \
    git \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN groupadd --gid 1000 app \
    && useradd --uid 1000 --gid app --shell /bin/bash --create-home app

WORKDIR /app

# =============================================================================
# Development stage
# =============================================================================
FROM base AS development

# Install development dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    vim \
    htop \
    iputils-ping \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements files
COPY --chown=app:app requirements*.txt ./

# Install all dependencies including dev
RUN pip install --upgrade pip setuptools wheel \
    && pip install -r requirements.txt \
    && if [ -f requirements-dev.txt ]; then pip install -r requirements-dev.txt; fi

# Copy application code
COPY --chown=app:app . .

# Install application in editable mode
RUN pip install -e .

USER app
EXPOSE 8000

# Development command with auto-reload
CMD ["uvicorn", "pattern_mining.api.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# =============================================================================
# Builder stage for production
# =============================================================================
FROM base AS builder

# Copy requirements
COPY requirements.txt .

# Install dependencies with specific versions
RUN pip install --upgrade pip setuptools wheel \
    && pip wheel --no-cache-dir --no-deps --wheel-dir /app/wheels -r requirements.txt

# =============================================================================
# Production stage
# =============================================================================
FROM python:${PYTHON_VERSION}-slim-bookworm AS production

# Install only runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN groupadd --gid 1000 app \
    && useradd --uid 1000 --gid app --shell /bin/bash --create-home app

WORKDIR /app

# Copy wheels from builder
COPY --from=builder /app/wheels /wheels

# Install pre-built wheels
RUN pip install --no-cache /wheels/*

# Copy application code
COPY --chown=app:app src/ src/
COPY --chown=app:app pyproject.toml .
COPY --chown=app:app README.md .

# Install application
RUN pip install .

# Set up non-root user
USER app

# Environment variables
ENV PYTHONPATH=/app/src \
    PYTHONUNBUFFERED=1

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Production command
CMD ["uvicorn", "pattern_mining.api.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1"]

# =============================================================================
# GPU Production stage
# =============================================================================
FROM nvidia/cuda:${CUDA_VERSION}-runtime-ubuntu22.04 AS production-gpu

ARG PYTHON_VERSION

# Install Python
RUN apt-get update && apt-get install -y --no-install-recommends \
    python${PYTHON_VERSION} \
    python${PYTHON_VERSION}-distutils \
    python3-pip \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && ln -s /usr/bin/python${PYTHON_VERSION} /usr/bin/python

# Create app user
RUN groupadd --gid 1000 app \
    && useradd --uid 1000 --gid app --shell /bin/bash --create-home app

WORKDIR /app

# Copy from builder
COPY --from=builder /app/wheels /wheels

# Install dependencies
RUN pip install --no-cache /wheels/*

# Copy application
COPY --chown=app:app src/ src/
COPY --chown=app:app pyproject.toml .
COPY --chown=app:app README.md .

# Install application
RUN pip install .

# Install GPU-specific requirements
COPY --chown=app:app requirements-gpu.txt .
RUN pip install -r requirements-gpu.txt

USER app

ENV PYTHONPATH=/app/src \
    PYTHONUNBUFFERED=1

EXPOSE 8000

HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

CMD ["uvicorn", "pattern_mining.api.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1"]