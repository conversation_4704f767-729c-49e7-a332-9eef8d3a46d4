# Kubernetes Ingress for Pattern Mining Service
# Production-ready with SSL termination and security headers
# Author: Episteme CCL Team
# Version: 2.0.0

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: pattern-mining-ingress
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: ingress
    version: v2.0.0
  annotations:
    # Nginx Ingress Controller
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    
    # Security Headers
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "X-Frame-Options: DENY";
      more_set_headers "X-Content-Type-Options: nosniff";
      more_set_headers "X-XSS-Protection: 1; mode=block";
      more_set_headers "Strict-Transport-Security: max-age=31536000; includeSubDomains";
      more_set_headers "Content-Security-Policy: default-src 'self'";
      more_set_headers "Referrer-Policy: strict-origin-when-cross-origin";
      more_set_headers "Permissions-Policy: geolocation=(), microphone=(), camera=()";
    
    # Rate Limiting
    nginx.ingress.kubernetes.io/rate-limit-connections: "10"
    nginx.ingress.kubernetes.io/rate-limit-requests-per-second: "20"
    nginx.ingress.kubernetes.io/rate-limit-window-size: "1m"
    
    # CORS Configuration
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://app.episteme.com,https://dashboard.episteme.com"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"
    nginx.ingress.kubernetes.io/cors-max-age: "86400"
    
    # Request/Response sizes
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    
    # SSL Configuration
    nginx.ingress.kubernetes.io/ssl-ciphers: "ECDHE-RSA-AES128-GCM-SHA256,ECDHE-RSA-AES256-GCM-SHA384,ECDHE-RSA-AES128-SHA256,ECDHE-RSA-AES256-SHA384"
    nginx.ingress.kubernetes.io/ssl-protocols: "TLSv1.2 TLSv1.3"
    
    # Load Balancing
    nginx.ingress.kubernetes.io/upstream-hash-by: "$request_uri"
    nginx.ingress.kubernetes.io/affinity: "cookie"
    nginx.ingress.kubernetes.io/affinity-mode: "persistent"
    
    # Monitoring
    nginx.ingress.kubernetes.io/enable-access-log: "true"
    nginx.ingress.kubernetes.io/enable-rewrite-log: "true"
    
    # Certificate Management
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    cert-manager.io/acme-challenge-type: "http01"
    
    # Google Cloud Load Balancer (if using GKE)
    cloud.google.com/load-balancer-type: "External"
    cloud.google.com/backend-config: '{"default": "pattern-mining-backend-config"}'
    cloud.google.com/neg: '{"ingress": true}'
    
    # AWS Load Balancer (if using EKS)
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/ssl-policy: ELBSecurityPolicy-TLS-1-2-2017-01
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:us-east-1:123456789012:certificate/12345678-1234-1234-1234-123456789012"
    
spec:
  tls:
  - hosts:
    - api.episteme.com
    - pattern-mining.episteme.com
    secretName: pattern-mining-tls
  rules:
  - host: api.episteme.com
    http:
      paths:
      - path: /v1/patterns
        pathType: Prefix
        backend:
          service:
            name: pattern-mining-service
            port:
              number: 80
      - path: /v1/mining
        pathType: Prefix
        backend:
          service:
            name: pattern-mining-service
            port:
              number: 80
      - path: /v1/analysis
        pathType: Prefix
        backend:
          service:
            name: pattern-mining-service
            port:
              number: 80
  - host: pattern-mining.episteme.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: pattern-mining-service
            port:
              number: 80
      - path: /health
        pathType: Exact
        backend:
          service:
            name: pattern-mining-service
            port:
              number: 80
      - path: /metrics
        pathType: Exact
        backend:
          service:
            name: pattern-mining-service
            port:
              number: 8001
      - path: /docs
        pathType: Prefix
        backend:
          service:
            name: pattern-mining-service
            port:
              number: 80

---
# Internal Ingress for GPU Service
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: pattern-mining-gpu-ingress
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining-gpu
    component: ingress
    version: v2.0.0
    gpu: "true"
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    
    # GPU-specific configurations
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-body-size: "500m"
    
    # Rate limiting for GPU resources
    nginx.ingress.kubernetes.io/rate-limit-connections: "5"
    nginx.ingress.kubernetes.io/rate-limit-requests-per-second: "10"
    
    # Internal access only
    nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8,**********/12,***********/16"
    
    # Certificate Management
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - gpu.pattern-mining.episteme.com
    secretName: pattern-mining-gpu-tls
  rules:
  - host: gpu.pattern-mining.episteme.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: pattern-mining-gpu-service
            port:
              number: 8000
      - path: /health
        pathType: Exact
        backend:
          service:
            name: pattern-mining-gpu-service
            port:
              number: 8000
      - path: /metrics
        pathType: Exact
        backend:
          service:
            name: pattern-mining-gpu-service
            port:
              number: 8001

---
# Backend Configuration for Google Cloud Load Balancer
apiVersion: cloud.google.com/v1
kind: BackendConfig
metadata:
  name: pattern-mining-backend-config
  namespace: episteme-pattern-mining
spec:
  healthCheck:
    checkIntervalSec: 30
    timeoutSec: 10
    healthyThreshold: 2
    unhealthyThreshold: 3
    type: HTTP
    requestPath: /health
    port: 8000
  
  timeoutSec: 120
  
  connectionDraining:
    drainingTimeoutSec: 60
  
  logging:
    enable: true
    sampleRate: 1.0
  
  iap:
    enabled: false
  
  securityPolicy:
    name: "pattern-mining-security-policy"
  
  cdn:
    enabled: false
    cachePolicy:
      includeHost: true
      includeProtocol: true
      includeQueryString: false
  
  sessionAffinity:
    affinityType: "CLIENT_IP"
    affinityCookieTtlSec: 3600