# ConfigMap for Pattern Mining Service
apiVersion: v1
kind: ConfigMap
metadata:
  name: pattern-mining-config
  namespace: pattern-mining
  labels:
    app: pattern-mining
    component: config
    version: v1.0.0
data:
  # Application Configuration
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  DEBUG: "false"
  
  # Service Configuration
  BIND: "0.0.0.0:8000"
  WORKERS: "4"
  MAX_WORKERS: "8"
  WORKER_CONNECTIONS: "1000"
  KEEPALIVE_TIMEOUT: "65"
  GRACEFUL_TIMEOUT: "30"
  TIMEOUT: "120"
  
  # Database Configuration
  DATABASE_HOST: "postgres-service"
  DATABASE_PORT: "5432"
  DATABASE_NAME: "pattern_mining"
  DATABASE_POOL_SIZE: "20"
  DATABASE_MAX_OVERFLOW: "30"
  DATABASE_TIMEOUT: "30"
  
  # Redis Configuration
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  REDIS_DB: "0"
  REDIS_MAX_CONNECTIONS: "50"
  REDIS_TIMEOUT: "5"
  
  # Google Cloud Configuration
  GOOGLE_CLOUD_PROJECT: "episteme-production"
  BIGQUERY_DATASET: "pattern_mining"
  BIGQUERY_LOCATION: "us-central1"
  
  # ML Configuration
  PRELOAD_MODELS: "true"
  MODEL_CACHE_SIZE: "10"
  MODEL_TIMEOUT: "300"
  BATCH_SIZE: "32"
  MAX_SEQUENCE_LENGTH: "512"
  
  # GPU Configuration
  GPU_ENABLED: "true"
  GPU_MEMORY_FRACTION: "0.8"
  CUDA_VISIBLE_DEVICES: "0"
  
  # Security Configuration
  CORS_ORIGINS: "https://app.episteme.com,https://api.episteme.com"
  ALLOWED_HOSTS: "pattern-mining-service,localhost"
  SECURE_COOKIES: "true"
  
  # Monitoring Configuration
  PROMETHEUS_ENABLED: "true"
  PROMETHEUS_PORT: "9090"
  TRACING_ENABLED: "true"
  JAEGER_AGENT_HOST: "jaeger-agent"
  JAEGER_AGENT_PORT: "6831"
  
  # Cache Configuration
  CACHE_TTL: "3600"
  CACHE_SIZE: "10000"
  CACHE_STRATEGY: "lru"
  
  # Performance Configuration
  CPU_THREADS: "4"
  MEMORY_LIMIT: "8Gi"
  PYTHONOPTIMIZE: "1"
  PYTHONHASHSEED: "0"
  
  # Logging Configuration
  LOG_FORMAT: "json"
  LOG_ROTATION: "daily"
  LOG_RETENTION: "30d"
  
  # Health Check Configuration
  HEALTH_CHECK_INTERVAL: "30s"
  HEALTH_CHECK_TIMEOUT: "10s"
  HEALTH_CHECK_RETRIES: "3"
  
  # Rate Limiting Configuration
  RATE_LIMIT_REQUESTS: "1000"
  RATE_LIMIT_WINDOW: "60"
  RATE_LIMIT_BURST: "50"

---
# ConfigMap for Application Configuration Files
apiVersion: v1
kind: ConfigMap
metadata:
  name: pattern-mining-app-config
  namespace: pattern-mining
  labels:
    app: pattern-mining
    component: app-config
    version: v1.0.0
data:
  # Logging Configuration
  logging.yaml: |
    version: 1
    disable_existing_loggers: false
    formatters:
      standard:
        format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
      json:
        format: '{"timestamp": "%(asctime)s", "logger": "%(name)s", "level": "%(levelname)s", "message": "%(message)s", "module": "%(module)s", "function": "%(funcName)s", "line": %(lineno)d}'
    handlers:
      console:
        class: logging.StreamHandler
        level: INFO
        formatter: json
        stream: ext://sys.stdout
      file:
        class: logging.handlers.RotatingFileHandler
        level: INFO
        formatter: json
        filename: /app/logs/application.log
        maxBytes: 10485760  # 10MB
        backupCount: 5
    loggers:
      pattern_mining:
        level: INFO
        handlers: [console, file]
        propagate: false
      uvicorn:
        level: INFO
        handlers: [console]
        propagate: false
    root:
      level: INFO
      handlers: [console, file]

  # ML Model Configuration
  models.yaml: |
    models:
      pattern_detector:
        type: transformer
        model_name: microsoft/codebert-base
        max_length: 512
        batch_size: 32
        cache_size: 100
        gpu_enabled: true
        
      semantic_analyzer:
        type: sentence_transformer
        model_name: sentence-transformers/all-MiniLM-L6-v2
        max_length: 384
        batch_size: 64
        cache_size: 1000
        gpu_enabled: true
        
      code_embeddings:
        type: custom
        model_path: /app/models/code_embeddings
        dimensions: 768
        similarity_threshold: 0.85
        cache_size: 5000
        gpu_enabled: true
    
    training:
      batch_size: 16
      learning_rate: 0.0001
      epochs: 10
      validation_split: 0.2
      early_stopping: true
      patience: 3
      
    inference:
      batch_size: 32
      max_concurrent_requests: 100
      timeout: 30
      retry_attempts: 3

  # Cache Configuration
  cache.yaml: |
    redis:
      host: redis-service
      port: 6379
      db: 0
      password: ""
      max_connections: 50
      timeout: 5
      retry_on_timeout: true
      health_check_interval: 30
      
    local:
      max_size: 1000
      ttl: 3600
      cleanup_interval: 300
      
    patterns:
      ttl: 7200  # 2 hours
      max_size: 10000
      compression: true
      
    models:
      ttl: 86400  # 24 hours
      max_size: 100
      persistent: true

  # Security Configuration
  security.yaml: |
    authentication:
      jwt:
        algorithm: HS256
        expires_in: 3600
        refresh_expires_in: 86400
        
    authorization:
      rbac:
        enabled: true
        cache_ttl: 300
        
    rate_limiting:
      global:
        requests_per_minute: 1000
        burst: 50
      endpoint_specific:
        /api/v1/analyze:
          requests_per_minute: 100
          burst: 10
        /api/v1/patterns:
          requests_per_minute: 500
          burst: 25
          
    cors:
      allow_origins:
        - https://app.episteme.com
        - https://api.episteme.com
      allow_methods:
        - GET
        - POST
        - PUT
        - DELETE
        - OPTIONS
      allow_headers:
        - Authorization
        - Content-Type
        - X-Requested-With
      max_age: 86400