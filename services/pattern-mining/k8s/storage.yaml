# Persistent Storage for Pattern Mining Service
# Production-ready with multiple storage classes and backup policies
# Author: Episteme CCL Team
# Version: 2.0.0

# Storage Class for high-performance SSD storage
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: pattern-mining-ssd
  labels:
    app: pattern-mining
    component: storage
    version: v2.0.0
  annotations:
    storageclass.kubernetes.io/is-default-class: "false"
    description: "High-performance SSD storage for Pattern Mining Service"
provisioner: kubernetes.io/gce-pd  # Change based on your cloud provider
parameters:
  type: pd-ssd
  replication-type: regional-pd
  zones: us-central1-a,us-central1-b,us-central1-c
reclaimPolicy: Retain
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true
mountOptions:
  - debug
  - noatime

---
# Storage Class for standard storage
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: pattern-mining-standard
  labels:
    app: pattern-mining
    component: storage
    version: v2.0.0
  annotations:
    storageclass.kubernetes.io/is-default-class: "false"
    description: "Standard storage for Pattern Mining Service"
provisioner: kubernetes.io/gce-pd
parameters:
  type: pd-standard
  replication-type: regional-pd
  zones: us-central1-a,us-central1-b,us-central1-c
reclaimPolicy: Retain
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true
mountOptions:
  - debug
  - noatime

---
# PersistentVolumeClaim for application logs
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: pattern-mining-logs-pvc
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: logs
    version: v2.0.0
  annotations:
    volume.beta.kubernetes.io/storage-class: "pattern-mining-standard"
    description: "Storage for application logs"
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: pattern-mining-standard

---
# PersistentVolumeClaim for cache data
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: pattern-mining-cache-pvc
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: cache
    version: v2.0.0
  annotations:
    volume.beta.kubernetes.io/storage-class: "pattern-mining-ssd"
    description: "High-performance storage for cache data"
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi
  storageClassName: pattern-mining-ssd

---
# PersistentVolumeClaim for application data
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: pattern-mining-data-pvc
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: data
    version: v2.0.0
  annotations:
    volume.beta.kubernetes.io/storage-class: "pattern-mining-standard"
    description: "Storage for application data"
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi
  storageClassName: pattern-mining-standard

---
# PersistentVolumeClaim for ML models
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: pattern-mining-models-pvc
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: models
    version: v2.0.0
  annotations:
    volume.beta.kubernetes.io/storage-class: "pattern-mining-ssd"
    description: "High-performance storage for ML models"
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 200Gi
  storageClassName: pattern-mining-ssd

---
# PersistentVolumeClaim for GPU service logs
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: pattern-mining-gpu-logs-pvc
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining-gpu
    component: logs
    version: v2.0.0
    gpu: "true"
  annotations:
    volume.beta.kubernetes.io/storage-class: "pattern-mining-standard"
    description: "Storage for GPU service logs"
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: pattern-mining-standard

---
# PersistentVolumeClaim for GPU service cache
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: pattern-mining-gpu-cache-pvc
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining-gpu
    component: cache
    version: v2.0.0
    gpu: "true"
  annotations:
    volume.beta.kubernetes.io/storage-class: "pattern-mining-ssd"
    description: "High-performance storage for GPU service cache"
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi
  storageClassName: pattern-mining-ssd

---
# PersistentVolumeClaim for GPU service data
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: pattern-mining-gpu-data-pvc
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining-gpu
    component: data
    version: v2.0.0
    gpu: "true"
  annotations:
    volume.beta.kubernetes.io/storage-class: "pattern-mining-standard"
    description: "Storage for GPU service data"
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 200Gi
  storageClassName: pattern-mining-standard

---
# PersistentVolumeClaim for GPU service models
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: pattern-mining-gpu-models-pvc
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining-gpu
    component: models
    version: v2.0.0
    gpu: "true"
  annotations:
    volume.beta.kubernetes.io/storage-class: "pattern-mining-ssd"
    description: "High-performance storage for GPU service models"
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 500Gi
  storageClassName: pattern-mining-ssd

---
# VolumeSnapshot for backup
apiVersion: snapshot.storage.k8s.io/v1
kind: VolumeSnapshot
metadata:
  name: pattern-mining-data-snapshot
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: backup
    version: v2.0.0
spec:
  volumeSnapshotClassName: csi-hostpath-snapclass
  source:
    persistentVolumeClaimName: pattern-mining-data-pvc

---
# VolumeSnapshotClass for backup policy
apiVersion: snapshot.storage.k8s.io/v1
kind: VolumeSnapshotClass
metadata:
  name: pattern-mining-snapshot-class
  labels:
    app: pattern-mining
    component: backup
    version: v2.0.0
  annotations:
    description: "Snapshot class for Pattern Mining Service backups"
driver: pd.csi.storage.gke.io
deletionPolicy: Retain
parameters:
  storage-locations: us-central1
  snapshot-type: regional

---
# Custom Resource for backup scheduling (requires external operator)
apiVersion: v1
kind: ConfigMap
metadata:
  name: pattern-mining-backup-config
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: backup
    version: v2.0.0
data:
  backup-schedule.yaml: |
    apiVersion: backup.io/v1
    kind: BackupSchedule
    metadata:
      name: pattern-mining-backup-schedule
      namespace: episteme-pattern-mining
    spec:
      schedule: "0 2 * * *"  # Daily at 2 AM
      retention:
        daily: 7
        weekly: 4
        monthly: 12
      resources:
        - persistentVolumeClaims:
          - pattern-mining-data-pvc
          - pattern-mining-models-pvc
          - pattern-mining-gpu-data-pvc
          - pattern-mining-gpu-models-pvc
      destination:
        gcpCloudStorage:
          bucket: episteme-pattern-mining-backups
          prefix: "{{ .Namespace }}/{{ .Name }}/{{ .Timestamp }}"
        encryption:
          enabled: true
          key: projects/episteme-prod/locations/global/keyRings/pattern-mining/cryptoKeys/backup-key

---
# StatefulSet for data persistence (alternative to Deployment)
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: pattern-mining-stateful
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining-stateful
    component: service
    version: v2.0.0
spec:
  serviceName: pattern-mining-headless
  replicas: 1
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app: pattern-mining-stateful
      component: service
  template:
    metadata:
      labels:
        app: pattern-mining-stateful
        component: service
        version: v2.0.0
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      serviceAccountName: pattern-mining-service
      containers:
      - name: pattern-mining
        image: gcr.io/episteme-prod/pattern-mining:2.0.0
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: STATEFUL_MODE
          value: "true"
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        volumeMounts:
        - name: data
          mountPath: /app/data
        - name: models
          mountPath: /app/models
        - name: cache
          mountPath: /app/cache
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "8Gi"
            cpu: "4000m"
  volumeClaimTemplates:
  - metadata:
      name: data
      labels:
        app: pattern-mining-stateful
        component: data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: pattern-mining-standard
      resources:
        requests:
          storage: 100Gi
  - metadata:
      name: models
      labels:
        app: pattern-mining-stateful
        component: models
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: pattern-mining-ssd
      resources:
        requests:
          storage: 200Gi
  - metadata:
      name: cache
      labels:
        app: pattern-mining-stateful
        component: cache
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: pattern-mining-ssd
      resources:
        requests:
          storage: 50Gi