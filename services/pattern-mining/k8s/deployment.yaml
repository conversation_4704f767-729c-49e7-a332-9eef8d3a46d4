# Kubernetes Deployment for Pattern Mining Service
# Production-ready with security policies, resource limits, and GPU support
# Author: Episteme CCL Team
# Version: 2.0.0

apiVersion: apps/v1
kind: Deployment
metadata:
  name: pattern-mining-service
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: service
    version: v2.0.0
    tier: backend
    environment: production
  annotations:
    deployment.kubernetes.io/revision: "1"
    kubernetes.io/change-cause: "Initial deployment of pattern-mining v2.0.0"
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: pattern-mining
      component: service
  template:
    metadata:
      labels:
        app: pattern-mining
        component: service
        version: v2.0.0
        tier: backend
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
        cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
    spec:
      # Security Context
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        seccompProfile:
          type: RuntimeDefault
      
      # Service Account
      serviceAccountName: pattern-mining-service
      
      # Node Selection
      nodeSelector:
        kubernetes.io/arch: amd64
        node-type: compute
      
      # Pod Disruption Budget
      terminationGracePeriodSeconds: 60
      
      # Init Containers
      initContainers:
      - name: db-migration
        image: gcr.io/episteme-prod/pattern-mining:2.0.0
        command: ["/bin/bash", "-c"]
        args:
          - |
            echo "Running database migrations..."
            python -m alembic upgrade head
            echo "Database migrations completed"
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: pattern-mining-secrets
              key: database-url
        - name: ENVIRONMENT
          value: "production"
        - name: SKIP_MIGRATIONS
          value: "false"
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1000
          runAsGroup: 1000
          capabilities:
            drop:
            - ALL
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        volumeMounts:
        - name: tmp-volume
          mountPath: /tmp
        - name: app-cache
          mountPath: /app/cache
      
      # Main Container
      containers:
      - name: pattern-mining
        image: gcr.io/episteme-prod/pattern-mining:2.0.0
        imagePullPolicy: Always
        
        # Security Context
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1000
          runAsGroup: 1000
          capabilities:
            drop:
            - ALL
        
        # Ports
        ports:
        - name: http
          containerPort: 8000
          protocol: TCP
        - name: metrics
          containerPort: 8001
          protocol: TCP
        
        # Environment Variables
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "INFO"
        - name: DEBUG
          value: "false"
        - name: WORKERS
          value: "4"
        - name: MAX_WORKERS
          value: "8"
        - name: WORKER_CONNECTIONS
          value: "1000"
        - name: TIMEOUT
          value: "120"
        - name: KEEPALIVE
          value: "5"
        - name: GRACEFUL_TIMEOUT
          value: "30"
        - name: BIND
          value: "0.0.0.0:8000"
        - name: PRELOAD_MODELS
          value: "true"
        - name: PROMETHEUS_ENABLED
          value: "true"
        - name: TRACING_ENABLED
          value: "true"
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: "http://jaeger-collector:14268/api/traces"
        - name: OTEL_SERVICE_NAME
          value: "pattern-mining-service"
        - name: OTEL_RESOURCE_ATTRIBUTES
          value: "service.name=pattern-mining,service.version=2.0.0"
        
        # Secrets
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: pattern-mining-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: pattern-mining-secrets
              key: redis-url
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: pattern-mining-secrets
              key: secret-key
        - name: GOOGLE_CLOUD_PROJECT
          valueFrom:
            secretKeyRef:
              name: pattern-mining-secrets
              key: gcp-project-id
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: "/var/secrets/google/key.json"
        
        # ConfigMap
        envFrom:
        - configMapRef:
            name: pattern-mining-config
        
        # Resource Limits
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
            ephemeral-storage: "2Gi"
          limits:
            memory: "4Gi"
            cpu: "2000m"
            ephemeral-storage: "5Gi"
        
        # Volume Mounts
        volumeMounts:
        - name: tmp-volume
          mountPath: /tmp
        - name: app-logs
          mountPath: /app/logs
        - name: app-cache
          mountPath: /app/cache
        - name: app-data
          mountPath: /app/data
        - name: app-models
          mountPath: /app/models
        - name: google-cloud-key
          mountPath: /var/secrets/google
          readOnly: true
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        
        # Health Checks
        livenessProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        
        readinessProbe:
          httpGet:
            path: /ready
            port: http
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        
        startupProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 20
          successThreshold: 1
        
        # Lifecycle hooks
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/bash
              - -c
              - |
                echo "Graceful shutdown initiated..."
                sleep 15
                echo "Graceful shutdown completed"
      
      # Volumes
      volumes:
      - name: tmp-volume
        emptyDir:
          sizeLimit: 1Gi
      - name: app-logs
        persistentVolumeClaim:
          claimName: pattern-mining-logs-pvc
      - name: app-cache
        persistentVolumeClaim:
          claimName: pattern-mining-cache-pvc
      - name: app-data
        persistentVolumeClaim:
          claimName: pattern-mining-data-pvc
      - name: app-models
        persistentVolumeClaim:
          claimName: pattern-mining-models-pvc
      - name: google-cloud-key
        secret:
          secretName: google-cloud-key
          defaultMode: 0400
      - name: config-volume
        configMap:
          name: pattern-mining-config
          defaultMode: 0644
      
      # Tolerations
      tolerations:
      - key: "node-type"
        operator: "Equal"
        value: "compute"
        effect: "NoSchedule"
      
      # Affinity
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - pattern-mining
              topologyKey: kubernetes.io/hostname
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: node-type
                operator: In
                values:
                - compute
              - key: kubernetes.io/arch
                operator: In
                values:
                - amd64

---
# GPU-enabled Deployment for Pattern Mining Service
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pattern-mining-gpu-service
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining-gpu
    component: service
    version: v2.0.0
    tier: backend
    gpu: "true"
    environment: production
  annotations:
    deployment.kubernetes.io/revision: "1"
    kubernetes.io/change-cause: "Initial GPU deployment of pattern-mining v2.0.0"
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: pattern-mining-gpu
      component: service
  template:
    metadata:
      labels:
        app: pattern-mining-gpu
        component: service
        version: v2.0.0
        tier: backend
        gpu: "true"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
        cluster-autoscaler.kubernetes.io/safe-to-evict: "false"
    spec:
      # Security Context
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        seccompProfile:
          type: RuntimeDefault
      
      # Service Account
      serviceAccountName: pattern-mining-service
      
      # Node Selection for GPU nodes
      nodeSelector:
        kubernetes.io/arch: amd64
        node-type: gpu-compute
        accelerator: nvidia-tesla-v100
      
      # Pod Disruption Budget
      terminationGracePeriodSeconds: 120
      
      # Main Container with GPU support
      containers:
      - name: pattern-mining-gpu
        image: gcr.io/episteme-prod/pattern-mining:2.0.0-gpu
        imagePullPolicy: Always
        
        # Security Context
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1000
          runAsGroup: 1000
          capabilities:
            drop:
            - ALL
        
        # Ports
        ports:
        - name: http
          containerPort: 8000
          protocol: TCP
        - name: metrics
          containerPort: 8001
          protocol: TCP
        
        # Environment Variables
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "INFO"
        - name: DEBUG
          value: "false"
        - name: WORKERS
          value: "2"
        - name: MAX_WORKERS
          value: "4"
        - name: WORKER_CONNECTIONS
          value: "1000"
        - name: TIMEOUT
          value: "300"
        - name: KEEPALIVE
          value: "5"
        - name: GRACEFUL_TIMEOUT
          value: "60"
        - name: BIND
          value: "0.0.0.0:8000"
        - name: PRELOAD_MODELS
          value: "true"
        - name: GPU_ENABLED
          value: "true"
        - name: CUDA_VISIBLE_DEVICES
          value: "0"
        - name: NVIDIA_VISIBLE_DEVICES
          value: "0"
        - name: NVIDIA_DRIVER_CAPABILITIES
          value: "compute,utility"
        - name: PROMETHEUS_ENABLED
          value: "true"
        - name: TRACING_ENABLED
          value: "true"
        
        # Secrets
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: pattern-mining-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: pattern-mining-secrets
              key: redis-url
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: pattern-mining-secrets
              key: secret-key
        - name: GOOGLE_CLOUD_PROJECT
          valueFrom:
            secretKeyRef:
              name: pattern-mining-secrets
              key: gcp-project-id
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: "/var/secrets/google/key.json"
        
        # ConfigMap
        envFrom:
        - configMapRef:
            name: pattern-mining-config
        
        # Resource Limits with GPU
        resources:
          requests:
            memory: "4Gi"
            cpu: "1000m"
            ephemeral-storage: "5Gi"
            nvidia.com/gpu: 1
          limits:
            memory: "16Gi"
            cpu: "4000m"
            ephemeral-storage: "20Gi"
            nvidia.com/gpu: 1
        
        # Volume Mounts
        volumeMounts:
        - name: tmp-volume
          mountPath: /tmp
        - name: app-logs
          mountPath: /app/logs
        - name: app-cache
          mountPath: /app/cache
        - name: app-data
          mountPath: /app/data
        - name: app-models
          mountPath: /app/models
        - name: google-cloud-key
          mountPath: /var/secrets/google
          readOnly: true
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        
        # Health Checks
        livenessProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 120
          periodSeconds: 60
          timeoutSeconds: 30
          failureThreshold: 3
          successThreshold: 1
        
        readinessProbe:
          httpGet:
            path: /ready
            port: http
            scheme: HTTP
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        
        startupProbe:
          httpGet:
            path: /health
            port: http
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
          successThreshold: 1
        
        # Lifecycle hooks
        lifecycle:
          preStop:
            exec:
              command:
              - /bin/bash
              - -c
              - |
                echo "Graceful shutdown initiated for GPU service..."
                sleep 30
                echo "Graceful shutdown completed for GPU service"
      
      # Volumes
      volumes:
      - name: tmp-volume
        emptyDir:
          sizeLimit: 5Gi
      - name: app-logs
        persistentVolumeClaim:
          claimName: pattern-mining-gpu-logs-pvc
      - name: app-cache
        persistentVolumeClaim:
          claimName: pattern-mining-gpu-cache-pvc
      - name: app-data
        persistentVolumeClaim:
          claimName: pattern-mining-gpu-data-pvc
      - name: app-models
        persistentVolumeClaim:
          claimName: pattern-mining-gpu-models-pvc
      - name: google-cloud-key
        secret:
          secretName: google-cloud-key
          defaultMode: 0400
      - name: config-volume
        configMap:
          name: pattern-mining-config
          defaultMode: 0644
      
      # Tolerations for GPU nodes
      tolerations:
      - key: "node-type"
        operator: "Equal"
        value: "gpu-compute"
        effect: "NoSchedule"
      - key: "nvidia.com/gpu"
        operator: "Exists"
        effect: "NoSchedule"
      
      # Affinity for GPU nodes
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: node-type
                operator: In
                values:
                - gpu-compute
              - key: accelerator
                operator: In
                values:
                - nvidia-tesla-v100
                - nvidia-tesla-t4
                - nvidia-tesla-a100
              - key: kubernetes.io/arch
                operator: In
                values:
                - amd64