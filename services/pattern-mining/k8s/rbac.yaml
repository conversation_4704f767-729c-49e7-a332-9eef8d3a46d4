# Role-Based Access Control (RBAC) for Pattern Mining Service
# Production-ready with least privilege principle
# Author: Episteme CCL Team
# Version: 2.0.0

apiVersion: v1
kind: ServiceAccount
metadata:
  name: pattern-mining-service
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: service-account
    version: v2.0.0
  annotations:
    # Google Cloud Service Account binding
    iam.gke.io/gcp-service-account: <EMAIL>
    
    # AWS IAM Role binding
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/pattern-mining-service-role
    
    # Description
    description: "Service account for Pattern Mining Service with minimal required permissions"
automountServiceAccountToken: true

---
# Role for Pattern Mining Service
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: pattern-mining-service-role
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: rbac
    version: v2.0.0
rules:
# ConfigMap permissions for configuration management
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
  resourceNames: ["pattern-mining-config"]

# Secret permissions for accessing credentials
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list", "watch"]
  resourceNames: 
    - "pattern-mining-secrets"
    - "google-cloud-key"
    - "pattern-mining-tls"

# Pod permissions for health checks and metrics
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
  resourceNames: []

# Service permissions for service discovery
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get", "list", "watch"]
  resourceNames: 
    - "pattern-mining-service"
    - "pattern-mining-internal"
    - "pattern-mining-gpu-service"

# Endpoint permissions for load balancing
- apiGroups: [""]
  resources: ["endpoints"]
  verbs: ["get", "list", "watch"]

# PersistentVolumeClaim permissions for storage
- apiGroups: [""]
  resources: ["persistentvolumeclaims"]
  verbs: ["get", "list", "watch"]
  resourceNames:
    - "pattern-mining-logs-pvc"
    - "pattern-mining-cache-pvc"
    - "pattern-mining-data-pvc"
    - "pattern-mining-models-pvc"

# Event permissions for debugging
- apiGroups: [""]
  resources: ["events"]
  verbs: ["get", "list", "watch"]

---
# RoleBinding for Pattern Mining Service
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: pattern-mining-service-binding
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: rbac
    version: v2.0.0
subjects:
- kind: ServiceAccount
  name: pattern-mining-service
  namespace: episteme-pattern-mining
roleRef:
  kind: Role
  name: pattern-mining-service-role
  apiGroup: rbac.authorization.k8s.io

---
# ClusterRole for cross-namespace operations (if needed)
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: pattern-mining-cluster-role
  labels:
    app: pattern-mining
    component: rbac
    version: v2.0.0
rules:
# Node metrics for resource monitoring
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["get", "list", "watch"]

# Namespace information for multi-tenant operations
- apiGroups: [""]
  resources: ["namespaces"]
  verbs: ["get", "list", "watch"]

# CustomResourceDefinitions for extending Kubernetes API
- apiGroups: ["apiextensions.k8s.io"]
  resources: ["customresourcedefinitions"]
  verbs: ["get", "list", "watch"]

# Metrics server access for autoscaling
- apiGroups: ["metrics.k8s.io"]
  resources: ["nodes", "pods"]
  verbs: ["get", "list", "watch"]

---
# ClusterRoleBinding for Pattern Mining Service
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: pattern-mining-cluster-binding
  labels:
    app: pattern-mining
    component: rbac
    version: v2.0.0
subjects:
- kind: ServiceAccount
  name: pattern-mining-service
  namespace: episteme-pattern-mining
roleRef:
  kind: ClusterRole
  name: pattern-mining-cluster-role
  apiGroup: rbac.authorization.k8s.io

---
# ServiceAccount for monitoring and observability
apiVersion: v1
kind: ServiceAccount
metadata:
  name: pattern-mining-monitoring
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: monitoring
    version: v2.0.0
  annotations:
    description: "Service account for Pattern Mining Service monitoring components"
automountServiceAccountToken: true

---
# Role for monitoring components
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: pattern-mining-monitoring-role
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: rbac-monitoring
    version: v2.0.0
rules:
# Pod metrics access
- apiGroups: [""]
  resources: ["pods", "pods/log"]
  verbs: ["get", "list", "watch"]

# Service metrics access
- apiGroups: [""]
  resources: ["services", "endpoints"]
  verbs: ["get", "list", "watch"]

# ConfigMap for monitoring configuration
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
  resourceNames: ["pattern-mining-monitoring-config"]

---
# RoleBinding for monitoring components
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: pattern-mining-monitoring-binding
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: rbac-monitoring
    version: v2.0.0
subjects:
- kind: ServiceAccount
  name: pattern-mining-monitoring
  namespace: episteme-pattern-mining
roleRef:
  kind: Role
  name: pattern-mining-monitoring-role
  apiGroup: rbac.authorization.k8s.io

---
# ServiceAccount for CI/CD deployments
apiVersion: v1
kind: ServiceAccount
metadata:
  name: pattern-mining-deployer
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: deployer
    version: v2.0.0
  annotations:
    description: "Service account for Pattern Mining Service CI/CD deployments"
automountServiceAccountToken: false

---
# Role for CI/CD deployments
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: pattern-mining-deployer-role
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: rbac-deployer
    version: v2.0.0
rules:
# Deployment management
- apiGroups: ["apps"]
  resources: ["deployments", "deployments/status"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# Pod management for deployments
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch", "delete"]

# Service management
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# ConfigMap and Secret management
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# Ingress management
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# HPA management
- apiGroups: ["autoscaling"]
  resources: ["horizontalpodautoscalers"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

# PDB management
- apiGroups: ["policy"]
  resources: ["poddisruptionbudgets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

---
# RoleBinding for CI/CD deployments
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: pattern-mining-deployer-binding
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: rbac-deployer
    version: v2.0.0
subjects:
- kind: ServiceAccount
  name: pattern-mining-deployer
  namespace: episteme-pattern-mining
roleRef:
  kind: Role
  name: pattern-mining-deployer-role
  apiGroup: rbac.authorization.k8s.io

---
# NetworkPolicy for Pattern Mining Service
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: pattern-mining-network-policy
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: network-policy
    version: v2.0.0
spec:
  podSelector:
    matchLabels:
      app: pattern-mining
  policyTypes:
  - Ingress
  - Egress
  
  ingress:
  # Allow traffic from ingress controller
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8000
  
  # Allow traffic from monitoring namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8001
  
  # Allow traffic from other pattern-mining pods
  - from:
    - podSelector:
        matchLabels:
          app: pattern-mining
    ports:
    - protocol: TCP
      port: 8000
    - protocol: TCP
      port: 8001
  
  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # Allow HTTPS traffic for external APIs
  - to: []
    ports:
    - protocol: TCP
      port: 443
  
  # Allow database access
  - to:
    - namespaceSelector:
        matchLabels:
          name: database
    ports:
    - protocol: TCP
      port: 5432
  
  # Allow Redis access
  - to:
    - namespaceSelector:
        matchLabels:
          name: cache
    ports:
    - protocol: TCP
      port: 6379
  
  # Allow Google Cloud API access
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80