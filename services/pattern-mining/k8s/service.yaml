# Kubernetes Services for Pattern Mining Service
# Author: Episteme CCL Team
# Version: 2.0.0

apiVersion: v1
kind: Service
metadata:
  name: pattern-mining-service
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: service
    version: v2.0.0
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: nlb
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: http
    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: "arn:aws:acm:us-east-1:123456789012:certificate/12345678-1234-1234-1234-123456789012"
    service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "https"
    cloud.google.com/backend-config: '{"default": "pattern-mining-backend-config"}'
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
    prometheus.io/path: "/metrics"
spec:
  type: LoadBalancer
  selector:
    app: pattern-mining
    component: service
  ports:
  - name: http
    port: 80
    targetPort: 8000
    protocol: TCP
  - name: https
    port: 443
    targetPort: 8000
    protocol: TCP
  - name: metrics
    port: 8001
    targetPort: 8001
    protocol: TCP
  sessionAffinity: None
  loadBalancerSourceRanges:
  - 0.0.0.0/0  # Configure this based on your security requirements

---
# Internal Service for Pattern Mining Service
apiVersion: v1
kind: Service
metadata:
  name: pattern-mining-internal
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: service
    version: v2.0.0
    scope: internal
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  selector:
    app: pattern-mining
    component: service
  ports:
  - name: http
    port: 8000
    targetPort: 8000
    protocol: TCP
  - name: metrics
    port: 8001
    targetPort: 8001
    protocol: TCP
  sessionAffinity: None

---
# GPU Service for Pattern Mining
apiVersion: v1
kind: Service
metadata:
  name: pattern-mining-gpu-service
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining-gpu
    component: service
    version: v2.0.0
    gpu: "true"
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  selector:
    app: pattern-mining-gpu
    component: service
  ports:
  - name: http
    port: 8000
    targetPort: 8000
    protocol: TCP
  - name: metrics
    port: 8001
    targetPort: 8001
    protocol: TCP
  sessionAffinity: None

---
# Headless Service for StatefulSet components
apiVersion: v1
kind: Service
metadata:
  name: pattern-mining-headless
  namespace: episteme-pattern-mining
  labels:
    app: pattern-mining
    component: service
    version: v2.0.0
    service-type: headless
spec:
  clusterIP: None
  selector:
    app: pattern-mining
    component: service
  ports:
  - name: http
    port: 8000
    targetPort: 8000
    protocol: TCP
  - name: metrics
    port: 8001
    targetPort: 8001
    protocol: TCP