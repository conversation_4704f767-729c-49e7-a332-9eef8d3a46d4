[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "pattern-mining"
description = "Advanced pattern detection and mining service for code analysis"
readme = "README.md"
requires-python = ">=3.11"
license = {text = "MIT"}
authors = [
    {name = "Episteme CCL Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Quality Assurance",
]
keywords = ["machine learning", "pattern detection", "code analysis", "mining", "ai"]
dependencies = [
    "fastapi>=0.115.0",
    "uvicorn[standard]>=0.32.0",
    "httpx>=0.28.0",
    "websockets>=13.0",
    "pydantic>=2.9.0",
    "pydantic-settings>=2.5.0",
    "sqlalchemy>=2.0.35",
    "asyncpg>=0.30.0",
    "alembic>=1.14.0",
    "google-cloud-bigquery>=3.26.0",
    "google-cloud-aiplatform>=1.70.0",
    "google-cloud-storage>=2.19.0",
    "numpy>=2.1.0",
    "pandas>=2.2.3",
    "scikit-learn>=1.6.0",
    "torch>=2.4.0",
    "transformers>=4.53.0",
    "sentence-transformers>=5.0.0",
    "libcst>=1.8.2",
    "tree-sitter>=0.24.0",
    "networkx>=3.4",
    "hdbscan>=0.8.40",
    "umap-learn>=0.5.7",
    "redis>=5.2.0",
    "structlog>=24.4.0",
    "prometheus-client>=0.21.0",
    "orjson>=3.10.0",
    "python-dotenv>=1.0.1",
    "rich>=13.9.0",
    "typer>=0.13.0",
]
dynamic = ["version"]

[project.optional-dependencies]
gpu = [
    "cupy-cuda12x>=13.3.0",
    "cuml-cu12>=25.6.0",
    "cudf-cu12>=25.6.0",
    "flash-attn>=2.6.0",
]
dev = [
    "pytest>=8.3.0",
    "pytest-asyncio>=0.24.0",
    "pytest-cov>=6.0.0",
    "pytest-mock>=3.14.0",
    "black>=24.10.0",
    "ruff>=0.7.2",
    "mypy>=1.12.0",
    "pre-commit>=4.0.0",
    "mkdocs>=1.6.0",
    "mkdocs-material>=9.5.40",
]
test = [
    "pytest>=8.3.0",
    "pytest-asyncio>=0.24.0",
    "pytest-cov>=6.0.0",
    "pytest-mock>=3.14.0",
    "hypothesis>=6.115.0",
]

[project.urls]
Homepage = "https://github.com/episteme/pattern-mining"
Documentation = "https://docs.episteme.com/pattern-mining"
Repository = "https://github.com/episteme/pattern-mining"
Issues = "https://github.com/episteme/pattern-mining/issues"

[project.scripts]
pattern-mining = "pattern_mining.main:main"

[tool.hatch.version]
path = "src/pattern_mining/__init__.py"

[tool.hatch.build.targets.wheel]
packages = ["src/pattern_mining"]

[tool.hatch.envs.default]
dependencies = [
    "pytest",
    "pytest-cov",
    "pytest-asyncio",
]

[tool.hatch.envs.default.scripts]
test = "pytest {args:tests}"
test-cov = "pytest --cov=pattern_mining --cov-report=term-missing {args:tests}"
cov-report = "coverage report"
cov-html = "coverage html"

[tool.hatch.envs.lint]
detached = true
dependencies = [
    "black",
    "ruff",
    "mypy",
]

[tool.hatch.envs.lint.scripts]
typing = "mypy --install-types --non-interactive {args:src/pattern_mining tests}"
style = [
    "ruff {args:.}",
    "black --check --diff {args:.}",
]
fmt = [
    "black {args:.}",
    "ruff --fix {args:.}",
    "style",
]
all = [
    "style",
    "typing",
]

[tool.black]
target-version = ["py311"]
line-length = 88
skip-string-normalization = true
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "A",
    "ARG",
    "B",
    "C",
    "DTZ",
    "E",
    "EM",
    "F",
    "FBT",
    "I",
    "ICN",
    "ISC",
    "N",
    "PLC",
    "PLE",
    "PLR",
    "PLW",
    "Q",
    "RUF",
    "S",
    "T",
    "TID",
    "UP",
    "W",
    "YTT",
]
ignore = [
    # Allow non-abstract empty methods in abstract base classes
    "B027",
    # Allow boolean positional values in function calls, like `dict.get(... True)`
    "FBT003",
    # Ignore checks for possible passwords
    "S105", "S106", "S107",
    # Ignore complexity
    "C901", "PLR0911", "PLR0912", "PLR0913", "PLR0915",
]
unfixable = [
    # Don't touch unused imports
    "F401",
]

[tool.ruff.isort]
known-first-party = ["pattern_mining"]

[tool.ruff.flake8-tidy-imports]
ban-relative-imports = "all"

[tool.ruff.per-file-ignores]
# Tests can use magic values, assertions, and relative imports
"tests/**/*" = ["PLR2004", "S101", "TID252"]

[tool.coverage.run]
source_pkgs = ["pattern_mining", "tests"]
branch = true
parallel = true
omit = [
    "src/pattern_mining/__about__.py",
]

[tool.coverage.paths]
pattern_mining = ["src/pattern_mining", "*/pattern-mining/src/pattern_mining"]
tests = ["tests", "*/pattern-mining/tests"]

[tool.coverage.report]
exclude_lines = [
    "no cov",
    "if __name__ == .__main__.:",
    "if TYPE_CHECKING:",
]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "tree_sitter.*",
    "libcst.*",
    "sentence_transformers.*",
    "hdbscan.*",
    "umap.*",
    "networkx.*",
    "sklearn.*",
    "cupy.*",
    "cuml.*",
    "cudf.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = [
    "tests",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "gpu: marks tests that require GPU",
]
asyncio_mode = "auto"
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]