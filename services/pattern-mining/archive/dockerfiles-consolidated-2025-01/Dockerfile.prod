# Production Multi-stage Dockerfile for Pattern Mining Service
# Supports both CPU and GPU environments with comprehensive security hardening
# Author: Episteme CCL Team
# Version: 2.0.0

# Build arguments
ARG PYTHON_VERSION=3.13
ARG RUNTIME_BASE=cpu-runtime
ARG TARGETPLATFORM=linux/amd64
ARG BUILDPLATFORM=linux/amd64
ARG CUDA_VERSION=12.3

# =============================================================================
# Stage 1: Base Python Image Selection
# =============================================================================

# CPU Runtime Base
FROM python:${PYTHON_VERSION}-slim-bookworm AS cpu-runtime
LABEL stage=cpu-runtime

# GPU Runtime Base (NVIDIA CUDA)
FROM nvidia/cuda:${CUDA_VERSION}-runtime-ubuntu22.04 AS cuda-runtime
LABEL stage=cuda-runtime

# Install Python on GPU runtime
RUN apt-get update && apt-get install -y --no-install-recommends \
    python${PYTHON_VERSION} \
    python${PYTHON_VERSION}-dev \
    python${PYTHON_VERSION}-distutils \
    python3-pip \
    && rm -rf /var/lib/apt/lists/* \
    && ln -s /usr/bin/python${PYTHON_VERSION} /usr/bin/python \
    && ln -s /usr/bin/python${PYTHON_VERSION} /usr/bin/python3

# =============================================================================
# Stage 2: System Dependencies Builder
# =============================================================================

FROM ${RUNTIME_BASE} AS system-deps
LABEL stage=system-deps

# Security: Create app user early
RUN groupadd --gid 1000 app \
    && useradd --uid 1000 --gid app --shell /bin/bash --create-home app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    # Build essentials
    gcc \
    g++ \
    make \
    cmake \
    pkg-config \
    # Development libraries
    libffi-dev \
    libssl-dev \
    libpq-dev \
    libcurl4-openssl-dev \
    libxml2-dev \
    libxslt1-dev \
    libjpeg-dev \
    libpng-dev \
    libfreetype6-dev \
    # ML/Scientific libraries
    libblas-dev \
    liblapack-dev \
    libopenblas-dev \
    libhdf5-dev \
    # Tree-sitter and parsing dependencies
    libtree-sitter-dev \
    # Version control
    git \
    # Network utilities
    curl \
    wget \
    # Process management
    procps \
    dumb-init \
    # Security tools
    gnupg \
    ca-certificates \
    # Cleanup
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# =============================================================================
# Stage 3: Python Dependencies Builder
# =============================================================================

FROM system-deps AS python-deps
LABEL stage=python-deps

# Install UV package manager for faster installs
RUN pip install --no-cache-dir uv==0.5.0

# Set working directory
WORKDIR /app

# Copy dependency files
COPY --chown=app:app requirements.txt requirements-gpu.txt ./

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Install Python dependencies based on runtime
RUN if [ "$RUNTIME_BASE" = "cuda-runtime" ]; then \
        echo "Installing GPU dependencies..." && \
        uv pip install --no-cache-dir -r requirements-gpu.txt; \
    else \
        echo "Installing CPU dependencies..." && \
        uv pip install --no-cache-dir -r requirements.txt; \
    fi

# Install gunicorn for production
RUN pip install --no-cache-dir gunicorn==21.2.0

# =============================================================================
# Stage 4: Application Builder
# =============================================================================

FROM python-deps AS app-builder
LABEL stage=app-builder

# Copy application code
COPY --chown=app:app src/ src/
COPY --chown=app:app pyproject.toml README.md ./

# Install application in development mode
RUN pip install --no-cache-dir -e .

# =============================================================================
# Stage 5: Security Scanner
# =============================================================================

FROM app-builder AS security-scanner
LABEL stage=security-scanner

# Install security scanning tools
RUN pip install --no-cache-dir \
    safety==3.3.0 \
    bandit==1.8.0 \
    semgrep==1.90.0

# Run security scans
RUN safety check --json --output /tmp/safety-report.json || true
RUN bandit -r src/ -f json -o /tmp/bandit-report.json || true
RUN semgrep --config=auto src/ --json --output=/tmp/semgrep-report.json || true

# =============================================================================
# Stage 6: Development Environment
# =============================================================================

FROM app-builder AS development
LABEL stage=development
LABEL maintainer="Episteme CCL Team <<EMAIL>>"
LABEL version="2.0.0"
LABEL description="Pattern Mining Service - Development Environment"

# Install development dependencies
RUN pip install --no-cache-dir \
    pytest==8.3.0 \
    pytest-asyncio==0.24.0 \
    pytest-cov==6.0.0 \
    pytest-mock==3.14.0 \
    pytest-benchmark==5.0.0 \
    black==24.10.0 \
    ruff==0.7.2 \
    mypy==1.12.0 \
    pre-commit==4.0.0 \
    ipython==8.29.0 \
    jupyter==1.1.1

# Set environment variables
ENV PYTHONPATH=/app/src \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONIOENCODING=UTF-8 \
    PYTHONUTF8=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    ENVIRONMENT=development \
    LOG_LEVEL=DEBUG \
    DEBUG=true

# Create application directories
RUN mkdir -p /app/logs /app/data /app/cache /app/models /app/coverage \
    && chown -R app:app /app

# Security: Switch to non-root user
USER app

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose ports
EXPOSE 8000 8001 8002

# Default command
CMD ["uvicorn", "pattern_mining.api.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--reload-dir", "src"]

# =============================================================================
# Stage 7: Production Environment
# =============================================================================

FROM system-deps AS production
LABEL stage=production
LABEL maintainer="Episteme CCL Team <<EMAIL>>"
LABEL version="2.0.0"
LABEL description="Pattern Mining Service - Production Environment"
LABEL org.opencontainers.image.title="pattern-mining-service"
LABEL org.opencontainers.image.description="Advanced pattern detection and mining service for code analysis"
LABEL org.opencontainers.image.vendor="Episteme CCL"
LABEL org.opencontainers.image.url="https://github.com/episteme/pattern-mining"
LABEL org.opencontainers.image.source="https://github.com/episteme/pattern-mining"
LABEL org.opencontainers.image.licenses="MIT"

# Copy virtual environment from builder
COPY --from=python-deps --chown=app:app /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy application code
COPY --from=app-builder --chown=app:app /app/src /app/src
COPY --from=app-builder --chown=app:app /app/pyproject.toml /app/README.md /app/

# Install production application
WORKDIR /app
RUN pip install --no-cache-dir -e .

# Create production directories
RUN mkdir -p /app/logs /app/data /app/cache /app/models /app/tmp \
    && chown -R app:app /app

# Production environment variables
ENV PYTHONPATH=/app/src \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONIOENCODING=UTF-8 \
    PYTHONUTF8=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    ENVIRONMENT=production \
    LOG_LEVEL=INFO \
    DEBUG=false \
    WORKERS=4 \
    MAX_WORKERS=8 \
    WORKER_CONNECTIONS=1000 \
    WORKER_CLASS=uvicorn.workers.UvicornWorker \
    TIMEOUT=120 \
    KEEPALIVE=5 \
    MAX_REQUESTS=1000 \
    MAX_REQUESTS_JITTER=50 \
    PRELOAD_APP=true

# Security hardening
RUN rm -rf /tmp/* /var/tmp/* /var/cache/apt/* /var/lib/apt/lists/* \
    && find /app -type f -name "*.pyc" -delete \
    && find /app -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true

# Copy entrypoint script
COPY --chown=app:app scripts/entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

# Switch to non-root user
USER app

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Use dumb-init for proper signal handling
ENTRYPOINT ["/usr/bin/dumb-init", "--"]

# Production command with entrypoint
CMD ["/app/entrypoint.sh"]

# =============================================================================
# Stage 8: Distroless Production (Ultra-secure)
# =============================================================================

FROM gcr.io/distroless/python3-debian12:latest AS distroless
LABEL stage=distroless
LABEL maintainer="Episteme CCL Team <<EMAIL>>"
LABEL version="2.0.0"
LABEL description="Pattern Mining Service - Distroless Production Environment"

# Copy virtual environment and application
COPY --from=python-deps --chown=1000:1000 /opt/venv /opt/venv
COPY --from=app-builder --chown=1000:1000 /app/src /app/src
COPY --from=app-builder --chown=1000:1000 /app/pyproject.toml /app/README.md /app/

# Set environment variables
ENV PYTHONPATH=/app/src:/opt/venv/lib/python3.13/site-packages \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/opt/venv/bin:$PATH" \
    ENVIRONMENT=production \
    LOG_LEVEL=INFO \
    DEBUG=false

# Switch to non-root user
USER 1000:1000

# Expose port
EXPOSE 8000

# Ultra-minimal command
ENTRYPOINT ["/opt/venv/bin/python", "-m", "uvicorn", "pattern_mining.api.main:app", "--host", "0.0.0.0", "--port", "8000"]

# =============================================================================
# Stage 9: Testing Environment
# =============================================================================

FROM development AS testing
LABEL stage=testing
LABEL maintainer="Episteme CCL Team <<EMAIL>>"
LABEL version="2.0.0"
LABEL description="Pattern Mining Service - Testing Environment"

# Install additional testing dependencies
RUN pip install --no-cache-dir \
    pytest-xdist==3.6.0 \
    pytest-html==4.1.1 \
    pytest-json-report==1.5.0 \
    pytest-timeout==2.3.1 \
    pytest-randomly==3.15.0 \
    pytest-profiling==1.7.0 \
    locust==2.31.0 \
    faker==30.8.0

# Copy test files
COPY --chown=app:app tests/ tests/

# Environment for testing
ENV ENVIRONMENT=test \
    LOG_LEVEL=DEBUG \
    DEBUG=true \
    PYTEST_DISABLE_PLUGIN_AUTOLOAD=1

# Default test command
CMD ["python", "-m", "pytest", "tests/", "-v", "--tb=short", "--cov=pattern_mining", "--cov-report=html:/app/coverage/html", "--cov-report=term", "--cov-report=json:/app/coverage/coverage.json", "--html=/app/coverage/report.html", "--json-report", "--json-report-file=/app/coverage/report.json"]

# =============================================================================
# Final Stage Selection
# =============================================================================

# Default to production stage
FROM production AS final

# =============================================================================
# Build Documentation
# =============================================================================
# To build for different targets:
# 
# CPU-only production:
# docker build -t pattern-mining:cpu-prod --target production .
#
# GPU-enabled production:
# docker build -t pattern-mining:gpu-prod --target production --build-arg RUNTIME_BASE=cuda-runtime .
#
# Development:
# docker build -t pattern-mining:dev --target development .
#
# Testing:
# docker build -t pattern-mining:test --target testing .
#
# Distroless (ultra-secure):
# docker build -t pattern-mining:distroless --target distroless .
#
# Multi-architecture:
# docker buildx build --platform linux/amd64,linux/arm64 -t pattern-mining:multi-arch .