# Pattern Detection Service - Google Models Only (Minimal Setup)
# Optimized for Google API-based pattern detection without heavy ML dependencies

# Web framework
fastapi==0.115.0
uvicorn[standard]==0.32.0
httpx==0.28.0
websockets==13.0
aiohttp==3.10.0

# Data validation
pydantic==2.9.0
pydantic-settings==2.5.0

# Database
sqlalchemy==2.0.35
asyncpg==0.30.0
alembic==1.14.0

# Google Cloud Services - Core APIs Only
google-cloud-aiplatform==1.70.0  # Vertex AI with Gemini 2.5 Flash
google-generativeai==0.8.3  # Google Gemini API (2.5 Flash with thinking)
google-cloud-storage==2.19.0
google-cloud-monitoring==2.22.0
google-cloud-secret-manager==2.21.0
google-auth==2.35.0
google-auth-oauthlib==1.2.1
google-auth-httplib2==0.2.0

# Essential Data Processing
numpy==2.1.0
pandas==2.2.3
scipy==1.14.0

# Code Analysis - Essential Only
libcst==1.8.2  # Python CST parsing
tree-sitter==0.24.0  # Fast incremental parsing
tree-sitter-languages==1.11.0  # Language grammars

# Security & Authentication
pyjwt==2.9.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.9
cryptography==43.0.0

# Async utilities
aiocache==0.12.3
aiofiles==24.1.0
aioredis==2.1.0

# Monitoring & Observability
opentelemetry-api==1.28.0
opentelemetry-sdk==1.28.0
opentelemetry-instrumentation-fastapi==0.49.0
prometheus-fastapi-instrumentator==7.0.0
structlog==24.4.0

# Caching & Storage
redis==5.2.0
hiredis==2.4.0

# Development & Testing
pytest==8.3.0
pytest-asyncio==0.24.0
pytest-cov==6.0.0
black==24.10.0
ruff==0.7.2
mypy==1.12.0

# Additional utilities
pyyaml==6.0.2
python-dotenv==1.0.1
rich==13.9.0
orjson==3.10.0  # Fast JSON
msgpack==1.1.0  # Binary serialization