#!/bin/bash

# Production entrypoint script for Pattern Mining Service
# Handles initialization, migrations, and graceful startup

set -euo pipefail

# Color output for logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_debug() {
    if [[ "${DEBUG:-false}" == "true" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
    fi
}

# Signal handlers for graceful shutdown
cleanup() {
    log_info "Received shutdown signal, starting graceful shutdown..."
    
    # Kill background processes
    if [[ -n "${UVICORN_PID:-}" ]]; then
        log_info "Stopping uvicorn process (PID: $UVICORN_PID)..."
        kill -TERM "$UVICORN_PID" 2>/dev/null || true
        
        # Wait for graceful shutdown
        local timeout=30
        while kill -0 "$UVICORN_PID" 2>/dev/null && [[ $timeout -gt 0 ]]; do
            sleep 1
            ((timeout--))
        done
        
        if kill -0 "$UVICORN_PID" 2>/dev/null; then
            log_warn "Forcing shutdown of uvicorn process..."
            kill -KILL "$UVICORN_PID" 2>/dev/null || true
        fi
    fi
    
    log_info "Graceful shutdown completed"
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT SIGQUIT

# Environment validation
validate_environment() {
    log_info "Validating environment configuration..."
    
    # Required environment variables
    local required_vars=(
        "ENVIRONMENT"
        "LOG_LEVEL"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            log_error "Required environment variable $var is not set"
            exit 1
        fi
    done
    
    # Validate log level
    local valid_levels=("DEBUG" "INFO" "WARNING" "ERROR" "CRITICAL")
    if [[ ! " ${valid_levels[*]} " =~ " ${LOG_LEVEL} " ]]; then
        log_error "Invalid LOG_LEVEL: $LOG_LEVEL. Must be one of: ${valid_levels[*]}"
        exit 1
    fi
    
    log_info "Environment validation passed"
}

# Health check function
health_check() {
    log_info "Performing initial health check..."
    
    # Check if required directories exist
    local dirs=("/app/logs" "/app/data" "/app/cache" "/app/tmp")
    for dir in "${dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            log_info "Creating directory: $dir"
            mkdir -p "$dir"
        fi
    done
    
    # Check Python environment
    python -c "import pattern_mining; print('Pattern mining module loaded successfully')" || {
        log_error "Failed to import pattern_mining module"
        exit 1
    }
    
    # Check critical dependencies
    python -c "
import sys
try:
    import fastapi
    import uvicorn
    import redis
    import google.cloud.bigquery
    print('Critical dependencies loaded successfully')
except ImportError as e:
    print(f'Failed to import critical dependency: {e}')
    sys.exit(1)
" || {
        log_error "Critical dependencies check failed"
        exit 1
    }
    
    log_info "Health check passed"
}

# Database migration function
run_migrations() {
    log_info "Running database migrations..."
    
    # Check if migrations are needed
    if [[ "${SKIP_MIGRATIONS:-false}" == "true" ]]; then
        log_info "Skipping migrations (SKIP_MIGRATIONS=true)"
        return
    fi
    
    # Run Alembic migrations
    if command -v alembic &> /dev/null; then
        log_info "Running Alembic migrations..."
        alembic upgrade head || {
            log_error "Database migrations failed"
            exit 1
        }
        log_info "Database migrations completed successfully"
    else
        log_warn "Alembic not found, skipping database migrations"
    fi
}

# Cache initialization
init_cache() {
    log_info "Initializing cache systems..."
    
    # Test Redis connection
    python -c "
import asyncio
import redis.asyncio as redis
import os

async def test_redis():
    redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379')
    try:
        r = redis.from_url(redis_url)
        await r.ping()
        print('Redis connection successful')
        await r.close()
    except Exception as e:
        print(f'Redis connection failed: {e}')
        # Don't exit here, Redis might be optional
        
asyncio.run(test_redis())
" || log_warn "Redis connection test failed (continuing without Redis)"
    
    log_info "Cache initialization completed"
}

# ML model initialization
init_models() {
    log_info "Initializing ML models..."
    
    # Pre-load models if configured
    if [[ "${PRELOAD_MODELS:-false}" == "true" ]]; then
        log_info "Pre-loading ML models..."
        python -c "
from pattern_mining.ml.manager import MLManager
try:
    manager = MLManager()
    print('ML models initialized successfully')
except Exception as e:
    print(f'ML model initialization failed: {e}')
    # Don't exit here, models might be loaded on demand
" || log_warn "ML model pre-loading failed (models will be loaded on demand)"
    fi
    
    log_info "ML model initialization completed"
}

# Configure logging
configure_logging() {
    log_info "Configuring logging..."
    
    # Set up log rotation
    local log_dir="/app/logs"
    mkdir -p "$log_dir"
    
    # Configure structured logging
    export PYTHONPATH="${PYTHONPATH}:/app/src"
    
    log_info "Logging configured (level: $LOG_LEVEL)"
}

# GPU detection and configuration
configure_gpu() {
    if command -v nvidia-smi &> /dev/null; then
        log_info "GPU detected, configuring CUDA environment..."
        
        # Check GPU availability
        nvidia-smi --query-gpu=name,memory.total,memory.used --format=csv,noheader,nounits | while read -r line; do
            log_info "GPU: $line"
        done
        
        # Set GPU environment variables
        export CUDA_VISIBLE_DEVICES="${CUDA_VISIBLE_DEVICES:-0}"
        export NVIDIA_VISIBLE_DEVICES="${NVIDIA_VISIBLE_DEVICES:-0}"
        
        log_info "GPU configuration completed"
    else
        log_info "No GPU detected, running in CPU-only mode"
        export CUDA_VISIBLE_DEVICES=""
    fi
}

# Security hardening
apply_security_hardening() {
    log_info "Applying security hardening..."
    
    # Set secure file permissions
    find /app -type f -name "*.py" -exec chmod 644 {} \;
    find /app -type d -exec chmod 755 {} \;
    
    # Remove potential security risks
    unset HISTFILE
    export HISTSIZE=0
    
    log_info "Security hardening applied"
}

# Performance optimization
optimize_performance() {
    log_info "Applying performance optimizations..."
    
    # Python optimizations
    export PYTHONOPTIMIZE=1
    export PYTHONHASHSEED=0
    
    # Memory optimization
    export MALLOC_ARENA_MAX=2
    
    # CPU optimization
    if [[ -n "${CPU_THREADS:-}" ]]; then
        export OMP_NUM_THREADS="$CPU_THREADS"
        export MKL_NUM_THREADS="$CPU_THREADS"
        export OPENBLAS_NUM_THREADS="$CPU_THREADS"
    fi
    
    log_info "Performance optimizations applied"
}

# Main execution function
main() {
    log_info "Starting Pattern Mining Service..."
    log_info "Environment: $ENVIRONMENT"
    log_info "Python version: $(python --version)"
    log_info "Platform: $(uname -a)"
    
    # Initialize services
    validate_environment
    configure_logging
    configure_gpu
    apply_security_hardening
    optimize_performance
    health_check
    run_migrations
    init_cache
    init_models
    
    log_info "Service initialization completed successfully"
    
    # Determine startup command
    local cmd
    if [[ $# -gt 0 ]]; then
        cmd=("$@")
    else
        # Default production command
        cmd=(
            "uvicorn"
            "pattern_mining.api.main:app"
            "--host" "${BIND%:*}"
            "--port" "${BIND#*:}"
            "--workers" "${WORKERS:-4}"
            "--worker-class" "uvicorn.workers.UvicornWorker"
            "--log-level" "${LOG_LEVEL,,}"
            "--access-log"
            "--no-use-colors"
            "--timeout-keep-alive" "${KEEPALIVE_TIMEOUT:-65}"
            "--timeout-graceful-shutdown" "${GRACEFUL_TIMEOUT:-30}"
        )
        
        # Add production-specific flags
        if [[ "$ENVIRONMENT" == "production" ]]; then
            cmd+=("--no-access-log" "--no-use-colors")
        fi
    fi
    
    log_info "Starting application with command: ${cmd[*]}"
    
    # Start the application
    exec "${cmd[@]}" &
    UVICORN_PID=$!
    
    # Wait for the application to start
    sleep 5
    
    # Perform startup health check
    local retries=10
    local count=0
    while [[ $count -lt $retries ]]; do
        if curl -f -s "http://localhost:${BIND#*:}/health" > /dev/null 2>&1; then
            log_info "Application started successfully and health check passed"
            break
        fi
        
        ((count++))
        log_info "Waiting for application to start... (attempt $count/$retries)"
        sleep 2
    done
    
    if [[ $count -eq $retries ]]; then
        log_error "Application failed to start within expected time"
        exit 1
    fi
    
    # Wait for the application process
    wait $UVICORN_PID
}

# Execute main function
main "$@"