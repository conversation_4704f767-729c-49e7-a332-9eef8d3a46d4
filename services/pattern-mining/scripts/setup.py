#!/usr/bin/env python3
"""
Setup Script

Setup script for the pattern mining service.
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Main setup function."""
    print("Setting up Pattern Mining Service...")
    
    # Get project root
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    # Create virtual environment
    print("Creating virtual environment...")
    subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
    
    # Activate virtual environment
    venv_python = "venv/bin/python" if os.name != "nt" else "venv\\Scripts\\python.exe"
    venv_pip = "venv/bin/pip" if os.name != "nt" else "venv\\Scripts\\pip.exe"
    
    # Upgrade pip
    print("Upgrading pip...")
    subprocess.run([venv_pip, "install", "--upgrade", "pip"], check=True)
    
    # Install dependencies
    print("Installing dependencies...")
    subprocess.run([venv_pip, "install", "-r", "requirements.txt"], check=True)
    
    # Install development dependencies
    print("Installing development dependencies...")
    subprocess.run([venv_pip, "install", "-e", ".[dev]"], check=True)
    
    # Copy environment file
    env_file = project_root / ".env"
    env_example = project_root / ".env.example"
    
    if not env_file.exists() and env_example.exists():
        print("Creating .env file...")
        import shutil
        shutil.copy(env_example, env_file)
    
    # Run database migrations (if applicable)
    print("Running database setup...")
    # subprocess.run([venv_python, "-m", "alembic", "upgrade", "head"], check=True)
    
    print("Setup complete!")
    print(f"To activate the virtual environment, run:")
    if os.name == "nt":
        print("  venv\\Scripts\\activate")
    else:
        print("  source venv/bin/activate")
    
    print("To start the service, run:")
    print("  python -m pattern_mining.main")

if __name__ == "__main__":
    main()