# Pattern Detection Service - Google Models Integration (July 2025)
# Python 3.11+ required for optimal compatibility with Google Cloud APIs
# Focused on API-based models, removed heavy ML training dependencies

# Web framework
fastapi==0.115.0
uvicorn[standard]==0.32.0
httpx==0.28.0
websockets==13.0
aiohttp==3.10.0

# Data validation
pydantic==2.9.0
pydantic-settings==2.5.0

# Database
sqlalchemy==2.0.35
asyncpg==0.30.0
alembic==1.14.0

# Google Cloud Services - API Integration Focus
google-cloud-bigquery==3.26.0
google-cloud-bigquery-storage==2.26.0
google-cloud-aiplatform==1.70.0  # Vertex AI with Gemini 2.5 Flash
google-generativeai==0.8.3  # Google Gemini API (2.5 Flash with thinking)
google-cloud-storage==2.19.0
google-cloud-pubsub==2.23.0
google-cloud-spanner==3.49.0
google-cloud-monitoring==2.22.0
google-cloud-secret-manager==2.21.0  # Secret Manager for security
google-auth==2.35.0  # Enhanced Google authentication
google-auth-oauthlib==1.2.1  # Google OAuth2
google-auth-httplib2==0.2.0  # Google auth HTTP adapter

# Data Science Core - Lightweight for API Processing
numpy==2.1.0  # For basic array operations
pandas==2.2.3  # For data processing

joblib==1.4.2  # For caching and parallel processing


# AST Analysis & Code Parsing - Lightweight
libcst==1.8.2  # Python CST parsing
tree-sitter==0.24.0  # Fast incremental parsing
py-tree-sitter-languages  # Language grammars
ast-grep-py==0.26.0  # High-performance AST search
astor==0.8.1  # AST manipulation

# Graph Analysis - Lightweight
networkx==3.4  # Graph algorithms (without deep learning)

# Pattern Discovery - Essential Only
hdbscan==0.8.40  # Density-based clustering
umap-learn==0.5.7  # Dimensionality reduction
scikit-learn==1.6.0  # Basic ML algorithms




# Security Analysis Tools
semgrep==1.90.0  # With AI features
bandit==1.8.0
safety==3.3.0
codeql-python==0.2.0  # CodeQL integration

# Security & Authentication - Essential
pyjwt==2.9.0  # JWT tokens
passlib[bcrypt]==1.7.4  # Password hashing
python-multipart==0.0.9  # Form data parsing
python-jose[cryptography]==3.3.0  # JWT with cryptography
cryptography==43.0.0  # Modern cryptography
bleach==6.1.0  # HTML sanitization
html5lib==1.1  # HTML parsing

# Async utilities
aiocache==0.12.3
aiofiles==24.1.0
asyncio-throttle==1.0.2
aioredis==2.1.0

# Monitoring & Observability
opentelemetry-api==1.28.0
opentelemetry-sdk==1.28.0
opentelemetry-instrumentation-fastapi==0.49.0
opentelemetry-exporter-gcp-trace==1.10.0
prometheus-fastapi-instrumentator==7.0.0
prometheus-client==0.21.0
structlog==24.4.0

# Caching & Storage
redis==5.2.0  # With vector search support
hiredis==2.4.0
motor==3.6.0  # Async MongoDB
minio==7.2.0  # Object storage


# Additional utilities
pyyaml==6.0.2
python-dotenv==1.0.1
rich==13.9.0
typer==0.13.0
click==8.1.7
tqdm==4.67.0
orjson==3.10.0  # Fast JSON
msgpack==1.1.0  # Binary serialization
