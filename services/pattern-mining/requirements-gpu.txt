# GPU-Specific Requirements for Pattern Detection Service (July 2025)
# Optimized for NVIDIA H100/H200 and A100 GPUs with CUDA 12.1+

# NVIDIA RAPIDS Suite - Latest Stable (25.6)
# GPU-accelerated data science libraries
cupy-cuda12x==13.3.0  # GPU-accelerated NumPy
cuml-cu12==25.6.0  # GPU-accelerated scikit-learn
cudf-cu12==25.6.0  # GPU-accelerated pandas
cugraph-cu12==25.6.0  # GPU-accelerated NetworkX
cuspatial-cu12==25.6.0  # Spatial analytics
cuxfilter-cu12==25.6.0  # GPU visualization
cusignal-cu12==25.6.0  # Signal processing
dask-cuda==25.6.0  # Distributed GPU computing
rapids-dask-dependency==25.6.0

# PyTorch with CUDA 12.1 Support
# Use specific index for CUDA builds
--index-url https://download.pytorch.org/whl/cu121
torch==2.4.0+cu121
torchvision==0.19.0+cu121
torchaudio==2.4.0+cu121

# Advanced Attention Mechanisms
flash-attn==2.6.0  # FlashAttention-3 for H100/H200
xformers==0.0.28  # Memory-efficient transformers
triton==3.0.0  # Custom GPU kernels

# Inference Optimization
tensorrt==10.3.0  # NVIDIA TensorRT for inference
torch-tensorrt==2.4.0  # PyTorch-TensorRT integration
onnxruntime-gpu==1.19.0  # ONNX Runtime with CUDA

# Distributed Training
horovod==0.28.1  # Distributed deep learning
deepspeed==0.15.0  # Extreme-scale model training
fairscale==0.4.13  # PyTorch extensions
apex @ git+https://github.com/NVIDIA/apex  # Mixed precision training

# Memory Optimization
bitsandbytes==0.44.0  # 8-bit optimizers and quantization
pytorch-quantization==2.1.3  # INT8 quantization
nvtx==0.2.10  # NVIDIA Tools Extension for profiling

# GPU Monitoring and Management
nvidia-ml-py==12.560.30  # NVIDIA Management Library
pynvml==11.5.0  # Python bindings for NVML
gpustat==1.1.1  # GPU status monitoring
py3nvml==0.2.7  # Python 3 NVML wrapper
nvidia-ml-py3==7.352.0  # Alternative NVML binding

# JAX Ecosystem (Alternative to PyTorch)
jax[cuda12]==0.4.31
jaxlib==0.4.31+cuda12.cudnn89
flax==0.9.0  # Neural networks for JAX
optax==0.2.3  # Optimization library

# Vector Search and Similarity
faiss-gpu==1.8.0  # Facebook AI Similarity Search

# Additional GPU Libraries
pycuda==2024.1  # Python wrapper for CUDA
cuda-python==12.6.0  # Official CUDA Python bindings
numba==0.60.0  # JIT compiler with CUDA support
cupy-profiler==13.3.0  # Profiling tools

# Image Processing (GPU)
cucim==25.6.0  # GPU-accelerated image I/O
nvjpeg2k-cu12==0.8.0  # GPU JPEG decoding

# Development and Debugging
torch-tb-profiler==0.4.3  # TensorBoard profiler
nvitop==1.3.2  # Interactive GPU process viewer
jupyterlab-nvdashboard==0.10.0  # GPU monitoring in Jupyter

# Cloud-Specific GPU Support
# For Google Cloud TPUs (optional)
# torch-xla==2.4.0
# tensorflow-tpu==2.17.0

# Installation Instructions:
# 1. Ensure CUDA 12.1+ is installed:
#    nvcc --version
#    nvidia-smi
#
# 2. Set environment variables:
#    export CUDA_HOME=/usr/local/cuda-12.1
#    export LD_LIBRARY_PATH=$CUDA_HOME/lib64:$LD_LIBRARY_PATH
#    export PATH=$CUDA_HOME/bin:$PATH
#
# 3. Install requirements:
#    pip install -r requirements-gpu.txt
#
# 4. Verify GPU access:
#    python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
#    python -c "import cupy; print(f'CuPy version: {cupy.__version__}')"
#
# 5. For H100/H200 GPUs, enable optimizations:
#    export TORCH_CUDA_ARCH_LIST="9.0"  # For H100
#    export CUDA_LAUNCH_BLOCKING=0
#    export TORCH_USE_CUDA_DSA=1

# Recommended GPU Configurations:
# - Development: 1x NVIDIA T4 (16GB)
# - Production: 1x NVIDIA A100 (40GB) or H100 (80GB)
# - Training: 4x NVIDIA A100 (40GB) or 2x H100 (80GB)
# - Inference: 1x NVIDIA L4 (24GB) or H100 (80GB)