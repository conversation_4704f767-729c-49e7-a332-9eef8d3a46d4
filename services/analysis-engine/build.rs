// services/analysis-engine/build.rs

use std::collections::HashSet;
use std::env;
use std::fs;
use std::path::{Path, PathBuf};
use std::process::Command;
use walkdir::WalkDir;

#[derive(Debug)]
pub enum BuildError {
    EnvVar(String),
    CargoMetadata(std::io::Error),
    JsonParsing(serde_json::Error),
    FileWrite(std::io::Error),
    InvalidPath(String),
}

impl std::fmt::Display for BuildError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            BuildError::EnvVar(var) => write!(f, "Environment variable not found: {var}"),
            BuildError::CargoMetadata(e) => write!(f, "Cargo metadata failed: {e}"),
            BuildError::JsonParsing(e) => write!(f, "JSON parsing failed: {e}"),
            BuildError::FileWrite(e) => write!(f, "File write failed: {e}"),
            BuildError::InvalidPath(path) => write!(f, "Invalid path: {path}"),
        }
    }
}

impl std::error::Error for BuildError {}

impl From<std::io::Error> for BuildError {
    fn from(error: std::io::Error) -> Self {
        BuildError::FileWrite(error)
    }
}

impl From<serde_json::Error> for BuildError {
    fn from(error: serde_json::Error) -> Self {
        BuildError::JsonParsing(error)
    }
}

/// Main entry point for the build script.
///
/// This script is responsible for the following:
/// 1.  **Finding `tree-sitter-*` grammar crates**: It parses `Cargo.toml` metadata
///     to identify all direct and transitive dependencies that follow the `tree-sitter-*` naming convention.
/// 2.  **Locating Grammar Source Files**: For each grammar crate, it finds the `src` directory
///     containing the C/C++ source files (`parser.c`, `scanner.c`, `scanner.cc`, etc.).
/// 3.  **Compiling Grammars**: It uses the `cc` crate to compile the C/C++ source files
///     into a single static library (`tree-sitter-grammars.a`).
/// 4.  **Handling C++ Scanners**: It correctly detects and compiles C++ scanners (`scanner.cc` or `scanner.cpp`)
///     by enabling the C++ compiler in the `cc` crate.
/// 5.  **Linking the Static Library**: It instructs `rustc` to link the compiled static library
///     into the final `analysis-engine` binary.
/// 6.  **Generating Language Bindings**: It generates a Rust source file (`language_bindings.rs`)
///     that contains the necessary `extern "C"` blocks to link the compiled grammar functions.
fn main() -> Result<(), BuildError> {
    let out_dir = PathBuf::from(
        env::var("OUT_DIR").map_err(|_| BuildError::EnvVar("OUT_DIR".to_string()))?
    );
    let grammar_paths = find_tree_sitter_grammars()?;

    let mut config = cc::Build::new();
    config.opt_level(3);
    config.warnings(false);

    let mut cpp = false;
    let mut files = Vec::new();

    for (_, grammar_path) in &grammar_paths {
        for entry in WalkDir::new(grammar_path)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            let path = entry.path();
            if let Some(extension) = path.extension() {
                if extension == "c" {
                    files.push(path.to_path_buf());
                    if let Some(parent) = path.parent() {
                        config.include(parent);
                    }
                } else if extension == "cc" || extension == "cpp" {
                    files.push(path.to_path_buf());
                    cpp = true;
                    if let Some(parent) = path.parent() {
                        config.include(parent);
                    }
                }
            }
        }
    }

    config.files(&files);

    if cpp {
        config.cpp(true);
    }

    config.compile("tree-sitter-grammars");

    generate_language_bindings(&out_dir, &grammar_paths)?;
    Ok(())
}

/// Finds all `tree-sitter-*` grammar crates in the dependency graph.
///
/// It uses the `cargo metadata` command to get a list of all dependencies
/// and filters them to find the ones that are tree-sitter grammars.
///
/// # Returns
///
/// A vector of `PathBuf`s, where each path points to the root directory of a grammar crate.
fn find_tree_sitter_grammars() -> Result<Vec<(String, PathBuf)>, BuildError> {
    let metadata_output = Command::new("cargo")
        .arg("metadata")
        .arg("--format-version=1")
        .output()
        .map_err(BuildError::CargoMetadata)?;

    if !metadata_output.status.success() {
        return Err(BuildError::CargoMetadata(std::io::Error::other(
            "Cargo metadata command failed"
        )));
    }

    let metadata: serde_json::Value = serde_json::from_slice(&metadata_output.stdout)?;

    let packages = metadata["packages"].as_array()
        .ok_or_else(|| BuildError::JsonParsing(
            serde_json::Error::io(std::io::Error::new(
                std::io::ErrorKind::InvalidData,
                "packages field not found or not array"
            ))
        ))?;

    let mut grammar_packages = HashSet::new();

    for package in packages {
        let name = package["name"].as_str()
            .ok_or_else(|| BuildError::JsonParsing(
                serde_json::Error::io(std::io::Error::new(
                    std::io::ErrorKind::InvalidData,
                    "package name not found or not string"
                ))
            ))?;

        if name.starts_with("tree-sitter-") && name != "tree-sitter-language" {
            let manifest_path_str = package["manifest_path"].as_str()
                .ok_or_else(|| BuildError::JsonParsing(
                    serde_json::Error::io(std::io::Error::new(
                        std::io::ErrorKind::InvalidData,
                        "manifest_path not found or not string"
                    ))
                ))?;

            let manifest_path = PathBuf::from(manifest_path_str);
            let parent_path = manifest_path.parent()
                .ok_or_else(|| BuildError::InvalidPath(format!("Invalid manifest path: {manifest_path_str}")))?;

            grammar_packages.insert((
                name.to_string(),
                parent_path.to_path_buf(),
            ));
        }
    }

    Ok(grammar_packages.into_iter().collect())
}

/// Generates a Rust source file with `extern "C"` blocks for each language.
///
/// This allows the Rust code to call the C functions from the compiled static library.
/// The generated file is placed in the `OUT_DIR` and included in the main crate.
///
/// # Arguments
///
/// * `out_dir` - The directory where the generated file should be saved.
/// * `grammar_paths` - A slice of paths to the grammar crates.
fn generate_language_bindings(out_dir: &Path, grammars: &[(String, PathBuf)]) -> Result<(), BuildError> {
    let mut bindings = String::new();
    bindings.push_str("use tree_sitter::Language;\n\n");

    bindings.push_str("pub fn get_language(name: &str) -> Option<Language> {\n");
    bindings.push_str("    match name {\n");
    for (name, _) in grammars {
        let grammar_name = name.replace("tree-sitter-", "");
        bindings.push_str(&format!(
            "        \"{grammar_name}\" => crate::parser::unsafe_bindings::load_language_unsafe(\"{grammar_name}\").ok(),\n"
        ));
    }
    bindings.push_str("        _ => None,\n");
    bindings.push_str("    }\n");
    bindings.push_str("}\n\n");

    bindings.push_str("pub const SUPPORTED_LANGUAGES: &[&str] = &[\n");
    for (name, _) in grammars {
        let grammar_name = name.replace("tree-sitter-", "");
        bindings.push_str(&format!("    \"{grammar_name}\",\n"));
    }
    bindings.push_str("];\n");

    let dest_path = out_dir.join("language_bindings.rs");
    fs::write(&dest_path, bindings)?;
    Ok(())
}
