version: '3.8'

services:
  analysis-engine:
    build: 
      context: .
      dockerfile: Dockerfile.cloudrun
    ports:
      - "8001:8001"
    environment:
      # Google Cloud Configuration
      - GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/sa-key.json
      - GCP_PROJECT_ID=vibe-match-463114
      - GCP_REGION=us-central1
      
      # Service Configuration
      - ENVIRONMENT=development
      - PORT=8001
      - HOST=0.0.0.0
      
      # GCP Services
      - SPANNER_INSTANCE=ccl-production
      - SPANNER_DATABASE=ccl-main
      - STORAGE_BUCKET=ccl-analysis-artifacts
      - STORAGE_BUCKET_NAME=ccl-analysis-vibe-match-463114
      - PUBSUB_TOPIC=analysis-events
      
      # Development Settings
      - ENABLE_AUTH=false
      - RUST_LOG=analysis_engine=debug,tower_http=debug
      - RUST_BACKTRACE=1
      
      # Analysis Configuration
      - MAX_CONCURRENT_ANALYSES=50
      - MAX_REPOSITORY_SIZE_GB=10
      - ANALYSIS_TIMEOUT_SECONDS=300
      - MAX_FILE_SIZE_MB=50
      - TEMP_DIR=/tmp/ccl-analysis
      
      # CORS
      - CORS_ORIGINS=*
    volumes:
      # Mount the service account key file
      - ../../vibe-match-463114-dbda8d8a6cb9.json:/app/credentials/sa-key.json:ro
      # Mount temp directory for analysis
      - ./tmp:/tmp/ccl-analysis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 3s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # Optional: Add Redis for local development
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

volumes:
  redis-data: