#!/bin/bash

# Test script for Analysis Engine deployment
SERVICE_URL="https://analysis-engine-l3nxty7oka-uc.a.run.app"

echo "=== Testing Analysis Engine Deployment ==="
echo "Service URL: $SERVICE_URL"
echo

echo "1. Testing health endpoint..."
curl -s "$SERVICE_URL/health" | jq .
echo

echo "2. Testing languages endpoint (public)..."
LANGUAGES_COUNT=$(curl -s "$SERVICE_URL/api/v1/languages" | jq '.languages | length')
echo "Languages supported: $LANGUAGES_COUNT"
echo

echo "3. Testing authenticated endpoint with test API key..."
curl -s "$SERVICE_URL/api/v1/analysis" \
  -H "x-api-key: ak_test_key_12345678" \
  -H "Content-Type: application/json" \
  -X POST \
  -d '{"repository_url": "https://github.com/test/repo"}' | jq .
echo

echo "4. Testing version endpoint..."
curl -s "$SERVICE_URL/api/v1/version" | jq .
echo

echo "=== Test Complete ==="