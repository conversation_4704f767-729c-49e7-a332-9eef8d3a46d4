# Analysis Engine Scripts

This directory contains operational scripts for the Analysis Engine service.

## Directory Structure

```
scripts/
├── standard/        # Production-ready scripts for daily operations
├── archive/         # One-time setup scripts (historical reference)
├── deployment/      # Legacy deployment scripts
└── README.md        # This file
```

## Standard Scripts

These are the primary scripts for operating the Analysis Engine in production:

### 🚀 deploy.sh
Deploy the Analysis Engine to Google Cloud Run.

```bash
# Basic deployment
./scripts/standard/deploy.sh

# Deploy with specific tag
./scripts/standard/deploy.sh --tag v1.2.3

# Deploy existing image without rebuild
./scripts/standard/deploy.sh --no-build --tag v1.2.3

# Dry run to see what would be deployed
./scripts/standard/deploy.sh --dry-run
```

**Features:**
- Builds and pushes Docker images
- Configures all environment variables
- Handles JWT secret from Secret Manager
- Supports dry-run mode

### 🏥 health-check.sh
Check the health and status of the deployed service.

```bash
# Basic health check
./scripts/standard/health-check.sh

# Detailed health check with logs
./scripts/standard/health-check.sh --detailed
```

**Checks:**
- All health endpoints
- Service readiness (Spanner, Storage, Pub/Sub)
- Recent errors/warnings
- Service configuration

### 🔧 maintenance.sh
Perform maintenance operations on the service.

```bash
# Show service status
./scripts/standard/maintenance.sh status

# View logs
./scripts/standard/maintenance.sh logs

# Restart service
./scripts/standard/maintenance.sh restart

# Scale instances
./scripts/standard/maintenance.sh scale

# Backup configuration
./scripts/standard/maintenance.sh backup

# Clean up old resources
./scripts/standard/maintenance.sh cleanup
```

**Features:**
- Interactive log viewer
- Safe restart with confirmation
- Scaling configuration
- Configuration backup
- Resource cleanup (old revisions, images)

### ✅ verify.sh
Verify all infrastructure components are properly configured.

```bash
./scripts/standard/verify.sh
```

**Verifies:**
- Cloud Run deployment
- Spanner database
- Redis cache
- Storage bucket
- Pub/Sub topics
- Service account permissions
- Environment variables

### 🗄️ verify-spanner.sh
Specifically verify Spanner configuration.

```bash
./scripts/standard/verify-spanner.sh
```

## Environment Configuration

The scripts expect these environment variables or use defaults:

```bash
PROJECT_ID="vibe-match-463114"
REGION="us-central1"
SERVICE_NAME="analysis-engine"

# Optional: Set JWT_SECRET for authentication
export JWT_SECRET="your-secret-here"
```

## Production Deployment Workflow

1. **Initial Deployment:**
   ```bash
   ./scripts/standard/deploy.sh
   ```

2. **Verify Deployment:**
   ```bash
   ./scripts/standard/health-check.sh --detailed
   ./scripts/standard/verify.sh
   ```

3. **Monitor Status:**
   ```bash
   ./scripts/standard/maintenance.sh status
   ```

4. **View Logs if Issues:**
   ```bash
   ./scripts/standard/maintenance.sh logs
   ```

5. **Scale if Needed:**
   ```bash
   ./scripts/standard/maintenance.sh scale
   ```

## Archived Scripts

The `archive/` directory contains one-time setup scripts used during initial deployment. These are kept for historical reference but should not be used for regular operations.

## Deployment Configuration

### Current Infrastructure:
- **Cloud Run**: Deployed in us-central1
- **Spanner**: Instance `ccl-instance`, Database `ccl_main`
- **Redis**: 4GB instance at `***********:6379`
- **Storage**: Bucket `ccl-analysis-artifacts`
- **Pub/Sub Topics**:
  - `analysis-events`
  - `analysis-progress`
  - `pattern-detected`

### Service Configuration:
- Memory: 4Gi
- CPU: 4
- Min Instances: 1
- Max Instances: 100
- Timeout: 600s
- Concurrency: 50

## Troubleshooting

### Service Won't Start
```bash
# Check logs
./scripts/standard/maintenance.sh logs

# Verify infrastructure
./scripts/standard/verify.sh
```

### Health Checks Failing
```bash
# Detailed health check
./scripts/standard/health-check.sh --detailed

# Check specific component
./scripts/standard/verify-spanner.sh
```

### Performance Issues
```bash
# Check current status
./scripts/standard/maintenance.sh status

# Scale up if needed
./scripts/standard/maintenance.sh scale
```

## Security Notes

- JWT_SECRET is stored in Google Secret Manager
- Service account has minimal required permissions
- All scripts use secure defaults
- Backups are stored locally (consider GCS for production)

## Contributing

When adding new scripts:
1. Place operational scripts in `standard/`
2. Make scripts executable: `chmod +x script.sh`
3. Include usage instructions
4. Use consistent error handling
5. Support dry-run mode where applicable