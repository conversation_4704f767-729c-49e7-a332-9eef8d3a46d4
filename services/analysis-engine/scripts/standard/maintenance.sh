#!/bin/bash
# Standard maintenance script for Analysis Engine
# Usage: ./maintenance.sh [command]
# Commands:
#   status      - Show service status and metrics
#   logs        - View recent logs
#   restart     - Restart the service
#   scale       - Scale instances (interactive)
#   backup      - Backup configuration
#   cleanup     - Clean up old resources
set -euo pipefail

# Configuration
PROJECT_ID="vibe-match-463114"
REGION="us-central1"
SERVICE_NAME="analysis-engine"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Function to show usage
show_usage() {
    echo "Usage: $0 [command]"
    echo "Commands:"
    echo "  status   - Show service status and metrics"
    echo "  logs     - View recent logs"
    echo "  restart  - Restart the service"
    echo "  scale    - Scale instances"
    echo "  backup   - Backup configuration"
    echo "  cleanup  - Clean up old resources"
    exit 1
}

# Status command
show_status() {
    echo "📊 Service Status"
    echo "================"
    
    # Get service details
    echo -e "\n${GREEN}Service Information:${NC}"
    gcloud run services describe ${SERVICE_NAME} \
        --region ${REGION} \
        --project ${PROJECT_ID} \
        --format="table(
            status.url:label=URL,
            status.latestReadyRevisionName:label=REVISION,
            status.conditions[0].status:label=STATUS
        )"
    
    # Get metrics
    echo -e "\n${GREEN}Current Metrics:${NC}"
    gcloud run services describe ${SERVICE_NAME} \
        --region ${REGION} \
        --project ${PROJECT_ID} \
        --format="table(
            spec.template.spec.containers[0].resources.limits.memory:label=MEMORY,
            spec.template.spec.containers[0].resources.limits.cpu:label=CPU,
            spec.template.metadata.annotations.'autoscaling.knative.dev/minScale':label=MIN_INSTANCES,
            spec.template.metadata.annotations.'autoscaling.knative.dev/maxScale':label=MAX_INSTANCES
        )"
    
    # Check recent errors
    echo -e "\n${GREEN}Recent Errors (last hour):${NC}"
    ERROR_COUNT=$(gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=${SERVICE_NAME} AND severity>=ERROR" \
        --limit=1000 \
        --project=${PROJECT_ID} \
        --freshness=1h \
        --format="value(timestamp)" 2>/dev/null | wc -l | tr -d ' ')
    
    if [ "$ERROR_COUNT" -eq 0 ]; then
        echo -e "${GREEN}✅ No errors in the last hour${NC}"
    else
        echo -e "${RED}❌ ${ERROR_COUNT} errors in the last hour${NC}"
    fi
}

# Logs command
show_logs() {
    echo "📜 Recent Logs"
    echo "============="
    echo ""
    echo "1) All logs"
    echo "2) Errors only"
    echo "3) Warnings and above"
    echo "4) Search logs"
    read -p "Select option (1-4): " option
    
    case $option in
        1)
            gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=${SERVICE_NAME}" \
                --limit=50 \
                --project=${PROJECT_ID} \
                --format="table(timestamp,severity,textPayload)"
            ;;
        2)
            gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=${SERVICE_NAME} AND severity>=ERROR" \
                --limit=50 \
                --project=${PROJECT_ID} \
                --format="table(timestamp,severity,textPayload)"
            ;;
        3)
            gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=${SERVICE_NAME} AND severity>=WARNING" \
                --limit=50 \
                --project=${PROJECT_ID} \
                --format="table(timestamp,severity,textPayload)"
            ;;
        4)
            read -p "Enter search term: " search_term
            gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=${SERVICE_NAME} AND textPayload:\"${search_term}\"" \
                --limit=50 \
                --project=${PROJECT_ID} \
                --format="table(timestamp,severity,textPayload)"
            ;;
        *)
            echo "Invalid option"
            ;;
    esac
}

# Restart command
restart_service() {
    echo "🔄 Restarting Service"
    echo "===================="
    echo -e "${YELLOW}⚠️  This will cause a brief downtime${NC}"
    read -p "Are you sure you want to restart? (y/N): " confirm
    
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        # Get current revision
        CURRENT_REVISION=$(gcloud run services describe ${SERVICE_NAME} \
            --region ${REGION} \
            --project ${PROJECT_ID} \
            --format="value(status.latestReadyRevisionName)")
        
        echo "Current revision: $CURRENT_REVISION"
        echo "Creating new revision..."
        
        # Force new revision by updating a label
        gcloud run services update ${SERVICE_NAME} \
            --region ${REGION} \
            --project ${PROJECT_ID} \
            --update-labels "restart-time=$(date +%s)"
        
        echo -e "${GREEN}✅ Service restarted${NC}"
    else
        echo "Restart cancelled"
    fi
}

# Scale command
scale_service() {
    echo "⚖️  Scale Service"
    echo "==============="
    
    # Get current scaling
    CURRENT_MIN=$(gcloud run services describe ${SERVICE_NAME} \
        --region ${REGION} \
        --project ${PROJECT_ID} \
        --format="value(spec.template.metadata.annotations.'autoscaling.knative.dev/minScale')")
    CURRENT_MAX=$(gcloud run services describe ${SERVICE_NAME} \
        --region ${REGION} \
        --project ${PROJECT_ID} \
        --format="value(spec.template.metadata.annotations.'autoscaling.knative.dev/maxScale')")
    
    echo "Current scaling: Min=${CURRENT_MIN:-0}, Max=${CURRENT_MAX:-1000}"
    echo ""
    
    read -p "Enter new minimum instances (current: ${CURRENT_MIN:-0}): " new_min
    read -p "Enter new maximum instances (current: ${CURRENT_MAX:-1000}): " new_max
    
    if [[ -n "$new_min" ]] || [[ -n "$new_max" ]]; then
        SCALE_CMD="gcloud run services update ${SERVICE_NAME} --region ${REGION} --project ${PROJECT_ID}"
        
        if [[ -n "$new_min" ]]; then
            SCALE_CMD="$SCALE_CMD --min-instances=$new_min"
        fi
        if [[ -n "$new_max" ]]; then
            SCALE_CMD="$SCALE_CMD --max-instances=$new_max"
        fi
        
        echo "Updating scaling configuration..."
        eval $SCALE_CMD
        echo -e "${GREEN}✅ Scaling updated${NC}"
    else
        echo "No changes made"
    fi
}

# Backup command
backup_config() {
    echo "💾 Backup Configuration"
    echo "====================="
    
    BACKUP_DIR="backups/$(date +%Y%m%d-%H%M%S)"
    mkdir -p $BACKUP_DIR
    
    # Export service configuration
    echo "Backing up service configuration..."
    gcloud run services export ${SERVICE_NAME} \
        --region ${REGION} \
        --project ${PROJECT_ID} \
        --format export > "${BACKUP_DIR}/service-config.yaml"
    
    # Save environment variables
    echo "Backing up environment variables..."
    gcloud run services describe ${SERVICE_NAME} \
        --region ${REGION} \
        --project ${PROJECT_ID} \
        --format json | jq '.spec.template.spec.containers[0].env' > "${BACKUP_DIR}/env-vars.json"
    
    # Save current deployment info
    echo "Backing up deployment info..."
    cat > "${BACKUP_DIR}/deployment-info.txt" <<EOF
Backup Date: $(date)
Project: ${PROJECT_ID}
Service: ${SERVICE_NAME}
Region: ${REGION}
Service URL: $(gcloud run services describe ${SERVICE_NAME} --region ${REGION} --project ${PROJECT_ID} --format="value(status.url)")
Current Revision: $(gcloud run services describe ${SERVICE_NAME} --region ${REGION} --project ${PROJECT_ID} --format="value(status.latestReadyRevisionName)")
EOF
    
    echo -e "${GREEN}✅ Backup saved to ${BACKUP_DIR}${NC}"
}

# Cleanup command
cleanup_resources() {
    echo "🧹 Cleanup Old Resources"
    echo "======================="
    
    # List old revisions
    echo "Finding old revisions..."
    OLD_REVISIONS=$(gcloud run revisions list \
        --service ${SERVICE_NAME} \
        --region ${REGION} \
        --project ${PROJECT_ID} \
        --format="value(name)" \
        --filter="status.conditions[0].status:True AND metadata.name!=$(gcloud run services describe ${SERVICE_NAME} --region ${REGION} --project ${PROJECT_ID} --format='value(status.latestReadyRevisionName)')")
    
    if [ -z "$OLD_REVISIONS" ]; then
        echo "No old revisions to clean up"
    else
        echo "Found old revisions:"
        echo "$OLD_REVISIONS"
        echo ""
        read -p "Delete these revisions? (y/N): " confirm
        
        if [[ "$confirm" =~ ^[Yy]$ ]]; then
            for revision in $OLD_REVISIONS; do
                echo "Deleting $revision..."
                gcloud run revisions delete $revision \
                    --region ${REGION} \
                    --project ${PROJECT_ID} \
                    --quiet
            done
            echo -e "${GREEN}✅ Old revisions deleted${NC}"
        fi
    fi
    
    # Clean old container images
    echo ""
    echo "Cleaning old container images..."
    IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"
    
    # List images older than 30 days
    OLD_IMAGES=$(gcloud container images list-tags ${IMAGE_NAME} \
        --filter="timestamp.datetime < $(date -d '30 days ago' --iso-8601)" \
        --format="get(digest)" \
        --limit=20)
    
    if [ -z "$OLD_IMAGES" ]; then
        echo "No old images to clean up"
    else
        IMAGE_COUNT=$(echo "$OLD_IMAGES" | wc -l)
        echo "Found $IMAGE_COUNT images older than 30 days"
        read -p "Delete these images? (y/N): " confirm
        
        if [[ "$confirm" =~ ^[Yy]$ ]]; then
            for digest in $OLD_IMAGES; do
                echo "Deleting ${IMAGE_NAME}@${digest}..."
                gcloud container images delete "${IMAGE_NAME}@${digest}" --quiet
            done
            echo -e "${GREEN}✅ Old images deleted${NC}"
        fi
    fi
}

# Main script
case "${1:-}" in
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    restart)
        restart_service
        ;;
    scale)
        scale_service
        ;;
    backup)
        backup_config
        ;;
    cleanup)
        cleanup_resources
        ;;
    *)
        show_usage
        ;;
esac