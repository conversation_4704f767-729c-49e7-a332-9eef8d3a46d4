#!/bin/bash
# Standard deployment script for Analysis Engine
# Usage: ./deploy.sh [options]
#   Options:
#     --tag TAG       Docker image tag (default: latest)
#     --no-build      Skip Docker build, use existing image
#     --dry-run       Show what would be deployed without doing it
set -euo pipefail

# Default values
IMAGE_TAG="latest"
SKIP_BUILD=false
DRY_RUN=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --no-build)
            SKIP_BUILD=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            echo "Usage: $0 [--tag TAG] [--no-build] [--dry-run]"
            exit 1
            ;;
    esac
done

# Configuration
PROJECT_ID="vibe-match-463114"
REGION="us-central1"
SERVICE_NAME="analysis-engine"
REGISTRY="gcr.io"
IMAGE_NAME="${REGISTRY}/${PROJECT_ID}/${SERVICE_NAME}"

# Load secrets from Secret Manager if available
JWT_SECRET="${JWT_SECRET:-$(gcloud secrets versions access latest --secret=analysis-engine-jwt-secret 2>/dev/null || echo '')}"
if [ -z "$JWT_SECRET" ]; then
    echo "⚠️  Warning: JWT_SECRET not found in environment or Secret Manager"
    echo "   Authentication will be disabled. Set JWT_SECRET to enable."
fi

echo "🚀 Deploying Analysis Engine"
echo "=========================="
echo "Project: ${PROJECT_ID}"
echo "Service: ${SERVICE_NAME}"
echo "Region: ${REGION}"
echo "Image Tag: ${IMAGE_TAG}"
echo ""

# Verify IAM permissions
echo "🔐 Verifying IAM permissions..."
SERVICE_ACCOUNT="analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com"

# Check if service account exists
if ! gcloud iam service-accounts describe ${SERVICE_ACCOUNT} --project=${PROJECT_ID} &>/dev/null; then
    echo "❌ Service account ${SERVICE_ACCOUNT} not found!"
    exit 1
fi

# Check required project-level roles
REQUIRED_ROLES=(
    "roles/spanner.databaseUser"
    "roles/storage.objectAdmin"
    "roles/pubsub.publisher"
    "roles/pubsub.viewer"
    "roles/aiplatform.user"
    "roles/monitoring.metricWriter"
    "roles/cloudtrace.agent"
)

echo "Checking project-level IAM roles..."
CURRENT_ROLES=$(gcloud projects get-iam-policy ${PROJECT_ID} \
    --flatten="bindings[].members" \
    --filter="bindings.members:serviceAccount:${SERVICE_ACCOUNT}" \
    --format="value(bindings.role)" 2>/dev/null | sort)

MISSING_ROLES=()
for role in "${REQUIRED_ROLES[@]}"; do
    if ! echo "$CURRENT_ROLES" | grep -q "^${role}$"; then
        MISSING_ROLES+=("$role")
    fi
done

if [ ${#MISSING_ROLES[@]} -ne 0 ]; then
    echo "⚠️  Warning: Missing IAM roles for service account:"
    printf '   - %s\n' "${MISSING_ROLES[@]}"
    echo "   To grant missing roles, run:"
    echo "   for role in ${MISSING_ROLES[@]}; do"
    echo "     gcloud projects add-iam-policy-binding ${PROJECT_ID} \\"
    echo "       --member=\"serviceAccount:${SERVICE_ACCOUNT}\" \\"
    echo "       --role=\"\$role\""
    echo "   done"
fi

# Check bucket permissions
BUCKET_NAME="ccl-analysis-artifacts"
echo "Checking bucket permissions for ${BUCKET_NAME}..."
if ! gsutil iam get gs://${BUCKET_NAME} 2>/dev/null | grep -q "${SERVICE_ACCOUNT}"; then
    echo "⚠️  Warning: Service account may not have bucket-level permissions"
    echo "   To grant bucket reader permission, run:"
    echo "   gsutil iam ch serviceAccount:${SERVICE_ACCOUNT}:legacyBucketReader gs://${BUCKET_NAME}"
fi

echo "✅ Permission check complete"
echo ""

# Build Docker image if not skipping
if [ "$SKIP_BUILD" = false ]; then
    echo "📦 Building Docker image..."
    docker build \
        --platform linux/amd64 \
        -t ${IMAGE_NAME}:${IMAGE_TAG} \
        -t ${IMAGE_NAME}:latest \
        -f Dockerfile.simple \
        .
    
    if [ "$DRY_RUN" = false ]; then
        echo "📤 Pushing image to Container Registry..."
        docker push ${IMAGE_NAME}:${IMAGE_TAG}
        docker push ${IMAGE_NAME}:latest
    fi
else
    echo "⏭️  Skipping build (using existing image)"
fi

# Prepare deployment command
DEPLOY_CMD="gcloud run deploy ${SERVICE_NAME} \
    --image ${IMAGE_NAME}:${IMAGE_TAG} \
    --platform managed \
    --region ${REGION} \
    --project ${PROJECT_ID} \
    --memory 4Gi \
    --cpu 4 \
    --timeout 600 \
    --max-instances 100 \
    --min-instances 1 \
    --concurrency 50 \
    --service-account analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com \
    --vpc-connector analysis-engine-connector \
    --set-env-vars GCP_PROJECT_ID=${PROJECT_ID} \
    --set-env-vars GOOGLE_CLOUD_PROJECT=${PROJECT_ID} \
    --set-env-vars SPANNER_PROJECT_ID=${PROJECT_ID} \
    --set-env-vars SPANNER_INSTANCE_ID=ccl-instance \
    --set-env-vars SPANNER_DATABASE_ID=ccl_main \
    --set-env-vars REDIS_URL=redis://***********:6379 \
    --set-env-vars STORAGE_BUCKET=ccl-analysis-artifacts \
    --set-env-vars STORAGE_BUCKET_NAME=ccl-analysis-artifacts \
    --set-env-vars PUBSUB_TOPIC=analysis-events \
    --set-env-vars PUBSUB_TOPIC_EVENTS=analysis-events \
    --set-env-vars PUBSUB_TOPIC_PROGRESS=analysis-progress \
    --set-env-vars PUBSUB_TOPIC_PATTERNS=pattern-detected \
    --set-env-vars ENVIRONMENT=production \
    --set-env-vars RUST_LOG=info \
    --set-env-vars RUST_BACKTRACE=1 \
    --set-env-vars MAX_CONCURRENT_ANALYSES=50 \
    --set-env-vars MAX_FILE_SIZE_BYTES=******** \
    --set-env-vars PARSE_TIMEOUT_SECONDS=30 \
    --set-env-vars MAX_ANALYSIS_MEMORY_MB=2048 \
    --set-env-vars MAX_DEPENDENCY_COUNT=10000"

# Add JWT_SECRET if available
if [ -n "$JWT_SECRET" ]; then
    DEPLOY_CMD="${DEPLOY_CMD} \
    --set-env-vars JWT_SECRET=${JWT_SECRET} \
    --set-env-vars ENABLE_AUTH=true \
    --set-env-vars CORS_ORIGINS=* \
    --set-env-vars API_KEY_HEADER=x-api-key \
    --set-env-vars RATE_LIMIT_PER_HOUR=1000 \
    --set-env-vars JWT_ROTATION_DAYS=7 \
    --set-env-vars ENABLE_AUDIT_LOGGING=true"
fi

# Add unauthenticated access (can be changed later)
DEPLOY_CMD="${DEPLOY_CMD} --allow-unauthenticated"

if [ "$DRY_RUN" = true ]; then
    echo "🔍 Dry run mode - would execute:"
    echo "$DEPLOY_CMD"
    exit 0
fi

# Deploy the service
echo "🚀 Deploying to Cloud Run..."
eval $DEPLOY_CMD

# Get service URL
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} \
    --platform managed \
    --region ${REGION} \
    --project ${PROJECT_ID} \
    --format 'value(status.url)')

echo ""
echo "✅ Deployment complete!"
echo "Service URL: ${SERVICE_URL}"
echo ""
echo "Run './scripts/standard/health-check.sh' to verify deployment"