#!/bin/bash
# Setup Google Cloud Monitoring dashboard for Analysis Engine
set -euo pipefail

echo "📊 Setting up Analysis Engine Monitoring Dashboard..."

# Configuration
PROJECT_ID="vibe-match-463114"
SERVICE_NAME="analysis-engine"
DASHBOARD_NAME="Analysis Engine Production Monitoring"

# Create temporary dashboard JSON
cat > /tmp/analysis-engine-dashboard.json << 'EOF'
{
  "displayName": "Analysis Engine Production Monitoring",
  "mosaicLayout": {
    "columns": 12,
    "tiles": [
      {
        "width": 6,
        "height": 4,
        "widget": {
          "title": "Request Rate",
          "xyChart": {
            "dataSets": [{
              "timeSeriesQuery": {
                "timeSeriesFilter": {
                  "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"analysis-engine\" AND metric.type=\"run.googleapis.com/request_count\"",
                  "aggregation": {
                    "alignmentPeriod": "60s",
                    "perSeriesAligner": "ALIGN_RATE"
                  }
                }
              },
              "plotType": "LINE"
            }]
          }
        }
      },
      {
        "xPos": 6,
        "width": 6,
        "height": 4,
        "widget": {
          "title": "Request Latency (p95)",
          "xyChart": {
            "dataSets": [{
              "timeSeriesQuery": {
                "timeSeriesFilter": {
                  "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"analysis-engine\" AND metric.type=\"run.googleapis.com/request_latencies\"",
                  "aggregation": {
                    "alignmentPeriod": "60s",
                    "perSeriesAligner": "ALIGN_PERCENTILE_95",
                    "crossSeriesReducer": "REDUCE_MEAN"
                  }
                }
              },
              "plotType": "LINE"
            }]
          }
        }
      },
      {
        "yPos": 4,
        "width": 6,
        "height": 4,
        "widget": {
          "title": "Error Rate",
          "xyChart": {
            "dataSets": [{
              "timeSeriesQuery": {
                "timeSeriesFilter": {
                  "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"analysis-engine\" AND metric.type=\"run.googleapis.com/request_count\" AND metric.labels.response_code_class!=\"2xx\"",
                  "aggregation": {
                    "alignmentPeriod": "60s",
                    "perSeriesAligner": "ALIGN_RATE"
                  }
                }
              },
              "plotType": "LINE"
            }]
          }
        }
      },
      {
        "xPos": 6,
        "yPos": 4,
        "width": 6,
        "height": 4,
        "widget": {
          "title": "Memory Utilization",
          "xyChart": {
            "dataSets": [{
              "timeSeriesQuery": {
                "timeSeriesFilter": {
                  "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"analysis-engine\" AND metric.type=\"run.googleapis.com/container/memory/utilizations\"",
                  "aggregation": {
                    "alignmentPeriod": "60s",
                    "perSeriesAligner": "ALIGN_MEAN"
                  }
                }
              },
              "plotType": "LINE"
            }]
          }
        }
      },
      {
        "yPos": 8,
        "width": 6,
        "height": 4,
        "widget": {
          "title": "CPU Utilization",
          "xyChart": {
            "dataSets": [{
              "timeSeriesQuery": {
                "timeSeriesFilter": {
                  "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"analysis-engine\" AND metric.type=\"run.googleapis.com/container/cpu/utilizations\"",
                  "aggregation": {
                    "alignmentPeriod": "60s",
                    "perSeriesAligner": "ALIGN_MEAN"
                  }
                }
              },
              "plotType": "LINE"
            }]
          }
        }
      },
      {
        "xPos": 6,
        "yPos": 8,
        "width": 6,
        "height": 4,
        "widget": {
          "title": "Instance Count",
          "xyChart": {
            "dataSets": [{
              "timeSeriesQuery": {
                "timeSeriesFilter": {
                  "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"analysis-engine\" AND metric.type=\"run.googleapis.com/container/instance_count\"",
                  "aggregation": {
                    "alignmentPeriod": "60s",
                    "perSeriesAligner": "ALIGN_MEAN"
                  }
                }
              },
              "plotType": "LINE"
            }]
          }
        }
      },
      {
        "yPos": 12,
        "width": 12,
        "height": 4,
        "widget": {
          "title": "Concurrent Analyses",
          "xyChart": {
            "dataSets": [{
              "timeSeriesQuery": {
                "timeSeriesFilter": {
                  "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"analysis-engine\" AND metric.type=\"run.googleapis.com/container/request_count\"",
                  "aggregation": {
                    "alignmentPeriod": "60s",
                    "perSeriesAligner": "ALIGN_SUM"
                  }
                }
              },
              "plotType": "LINE"
            }]
          }
        }
      },
      {
        "yPos": 16,
        "width": 6,
        "height": 4,
        "widget": {
          "title": "Redis Cache Hit Rate",
          "scorecard": {
            "timeSeriesQuery": {
              "timeSeriesFilter": {
                "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"analysis-engine\" AND metric.type=\"logging.googleapis.com/user/redis_cache_hit_rate\"",
                "aggregation": {
                  "alignmentPeriod": "300s",
                  "perSeriesAligner": "ALIGN_MEAN"
                }
              }
            },
            "gaugeView": {
              "lowerBound": 0.0,
              "upperBound": 1.0
            }
          }
        }
      },
      {
        "xPos": 6,
        "yPos": 16,
        "width": 6,
        "height": 4,
        "widget": {
          "title": "Analysis Success Rate",
          "scorecard": {
            "timeSeriesQuery": {
              "timeSeriesFilter": {
                "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"analysis-engine\" AND metric.type=\"logging.googleapis.com/user/analysis_success_rate\"",
                "aggregation": {
                  "alignmentPeriod": "300s",
                  "perSeriesAligner": "ALIGN_MEAN"
                }
              }
            },
            "gaugeView": {
              "lowerBound": 0.0,
              "upperBound": 1.0
            }
          }
        }
      }
    ]
  }
}
EOF

# Create the dashboard using gcloud
echo "📊 Creating dashboard in Google Cloud Monitoring..."
gcloud monitoring dashboards create --config-from-file=/tmp/analysis-engine-dashboard.json --project=${PROJECT_ID}

# Create alert policies
echo "🚨 Creating alert policies..."

# High error rate alert
cat > /tmp/high-error-rate-alert.json << EOF
{
  "displayName": "Analysis Engine - High Error Rate",
  "conditions": [
    {
      "displayName": "Error rate > 5%",
      "conditionThreshold": {
        "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"analysis-engine\" AND metric.type=\"run.googleapis.com/request_count\" AND metric.labels.response_code_class!=\"2xx\"",
        "aggregations": [
          {
            "alignmentPeriod": "300s",
            "perSeriesAligner": "ALIGN_RATE"
          }
        ],
        "comparison": "COMPARISON_GT",
        "thresholdValue": 0.05
      }
    }
  ],
  "alertStrategy": {
    "autoClose": "1800s"
  }
}
EOF

# High memory usage alert
cat > /tmp/high-memory-alert.json << EOF
{
  "displayName": "Analysis Engine - High Memory Usage",
  "conditions": [
    {
      "displayName": "Memory utilization > 80%",
      "conditionThreshold": {
        "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"analysis-engine\" AND metric.type=\"run.googleapis.com/container/memory/utilizations\"",
        "aggregations": [
          {
            "alignmentPeriod": "300s",
            "perSeriesAligner": "ALIGN_MEAN"
          }
        ],
        "comparison": "COMPARISON_GT",
        "thresholdValue": 0.8
      }
    }
  ],
  "alertStrategy": {
    "autoClose": "1800s"
  }
}
EOF

# Service down alert
cat > /tmp/service-down-alert.json << EOF
{
  "displayName": "Analysis Engine - Service Down",
  "conditions": [
    {
      "displayName": "No requests for 5 minutes",
      "conditionThreshold": {
        "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"analysis-engine\" AND metric.type=\"run.googleapis.com/request_count\"",
        "aggregations": [
          {
            "alignmentPeriod": "300s",
            "perSeriesAligner": "ALIGN_SUM"
          }
        ],
        "comparison": "COMPARISON_LT",
        "thresholdValue": 1
      }
    }
  ],
  "alertStrategy": {
    "autoClose": "1800s"
  }
}
EOF

# Create alerts (skip if alpha not installed)
if gcloud components list --filter="id=alpha" --format="value(state)" | grep -q "Installed"; then
    echo "Creating alert policies..."
    gcloud alpha monitoring policies create --policy-from-file=/tmp/high-error-rate-alert.json --project=${PROJECT_ID}
    gcloud alpha monitoring policies create --policy-from-file=/tmp/high-memory-alert.json --project=${PROJECT_ID}
    gcloud alpha monitoring policies create --policy-from-file=/tmp/service-down-alert.json --project=${PROJECT_ID}
else
    echo "⚠️  Skipping alert creation (gcloud alpha not installed)"
    echo "   To install: gcloud components install alpha"
fi

# Create custom log metrics for Redis and Analysis metrics
echo "📈 Creating custom log-based metrics..."

# Redis cache hit rate metric
gcloud logging metrics create redis_cache_hit_rate \
    --log-filter='resource.type="cloud_run_revision"
resource.labels.service_name="analysis-engine"
textPayload=~"Redis cache (hit|miss)"' \
    --value-extractor='EXTRACT(textPayload) =~ "hit" ? 1 : 0' \
    --metric-kind=GAUGE \
    --project=${PROJECT_ID}

# Analysis success rate metric
gcloud logging metrics create analysis_success_rate \
    --log-filter='resource.type="cloud_run_revision"
resource.labels.service_name="analysis-engine"
textPayload=~"Analysis completed"' \
    --value-extractor='EXTRACT(jsonPayload.success) ? 1 : 0' \
    --metric-kind=GAUGE \
    --project=${PROJECT_ID}

# Clean up temp files
rm -f /tmp/analysis-engine-dashboard.json
rm -f /tmp/*-alert.json

echo "✅ Monitoring setup complete!"
echo ""
echo "📊 Dashboard available at:"
echo "   https://console.cloud.google.com/monitoring/dashboards"
echo ""
echo "🚨 Alert policies created:"
echo "   - High Error Rate (>5%)"
echo "   - High Memory Usage (>80%)"
echo "   - Service Down (no requests for 5 min)"
echo ""
echo "📈 Custom metrics created:"
echo "   - redis_cache_hit_rate"
echo "   - analysis_success_rate"
echo ""
echo "Next steps:"
echo "1. Configure notification channels for alerts"
echo "2. Customize dashboard layout as needed"
echo "3. Add additional metrics based on usage patterns"