#!/bin/bash
# Verify all infrastructure components for Analysis Engine
set -euo pipefail

echo "🔍 Verifying Analysis Engine Infrastructure..."
echo "============================================"

# Configuration
PROJECT_ID="vibe-match-463114"
REGION="us-central1"
BUCKET_NAME="ccl-analysis-artifacts"
PUBSUB_TOPIC="analysis-results"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check status
check_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
        return 0
    else
        echo -e "${RED}❌ $2${NC}"
        return 1
    fi
}

# 1. Cloud Run Service
echo -e "\n1️⃣  Cloud Run Service:"
SERVICE_URL=$(gcloud run services describe analysis-engine \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --format='value(status.url)' 2>/dev/null || echo "")

if [ -n "$SERVICE_URL" ]; then
    echo -e "${GREEN}✅ Service deployed at: $SERVICE_URL${NC}"
    
    # Test health endpoint
    if curl -sf "${SERVICE_URL}/health" > /dev/null; then
        echo -e "${GREEN}✅ Health check passed${NC}"
    else
        echo -e "${RED}❌ Health check failed${NC}"
    fi
else
    echo -e "${RED}❌ Service not found${NC}"
fi

# 2. Spanner Database
echo -e "\n2️⃣  Spanner Database:"
if gcloud spanner databases describe ccl_main \
    --instance=ccl-instance \
    --project=${PROJECT_ID} >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Database 'ccl_main' exists${NC}"
    
    # Check for tables
    TABLES=$(gcloud spanner databases execute-sql ccl_main \
        --instance=ccl-instance \
        --project=${PROJECT_ID} \
        --sql="SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = ''" \
        --format="value(table_count)" 2>/dev/null || echo "0")
    
    if [ "$TABLES" -gt 0 ]; then
        echo -e "${GREEN}✅ Database has $TABLES tables${NC}"
    else
        echo -e "${YELLOW}⚠️  No tables yet (will be created on first use)${NC}"
    fi
else
    echo -e "${RED}❌ Database not found${NC}"
fi

# 3. Redis Cache
echo -e "\n3️⃣  Redis Cache:"
REDIS_STATE=$(gcloud redis instances describe analysis-engine-cache \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --format='value(state)' 2>/dev/null || echo "")

if [ "$REDIS_STATE" = "READY" ]; then
    echo -e "${GREEN}✅ Redis instance is READY${NC}"
    
    # Check Redis connection in logs
    REDIS_LOGS=$(gcloud logging read \
        "resource.type=cloud_run_revision AND textPayload:\"Redis connection successful\"" \
        --limit=1 \
        --project=${PROJECT_ID} \
        --format="value(textPayload)" \
        --freshness=1h 2>/dev/null || echo "")
    
    if [ -n "$REDIS_LOGS" ]; then
        echo -e "${GREEN}✅ Redis connected from Cloud Run${NC}"
    else
        echo -e "${YELLOW}⚠️  No recent Redis connection logs${NC}"
    fi
else
    echo -e "${RED}❌ Redis not ready (state: $REDIS_STATE)${NC}"
fi

# 4. VPC Connector
echo -e "\n4️⃣  VPC Connector:"
VPC_STATE=$(gcloud compute networks vpc-access connectors describe analysis-engine-connector \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --format='value(state)' 2>/dev/null || echo "")

if [ "$VPC_STATE" = "READY" ]; then
    echo -e "${GREEN}✅ VPC Connector is READY${NC}"
else
    echo -e "${RED}❌ VPC Connector not ready (state: $VPC_STATE)${NC}"
fi

# 5. Storage Bucket
echo -e "\n5️⃣  Storage Bucket:"
if gsutil ls -b gs://${BUCKET_NAME} >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Storage bucket exists: gs://${BUCKET_NAME}${NC}"
else
    echo -e "${YELLOW}⚠️  Storage bucket does not exist${NC}"
    echo "   Run: gsutil mb -p ${PROJECT_ID} -c STANDARD -l ${REGION} gs://${BUCKET_NAME}"
fi

# 6. Pub/Sub Topic
echo -e "\n6️⃣  Pub/Sub Topic:"
if gcloud pubsub topics describe ${PUBSUB_TOPIC} --project=${PROJECT_ID} >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Pub/Sub topic exists: ${PUBSUB_TOPIC}${NC}"
else
    echo -e "${YELLOW}⚠️  Pub/Sub topic does not exist${NC}"
    echo "   Run: gcloud pubsub topics create ${PUBSUB_TOPIC} --project=${PROJECT_ID}"
fi

# 7. Service Account and IAM Permissions
echo -e "\n7️⃣  Service Account and IAM Permissions:"
SA_EMAIL="analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com"
if gcloud iam service-accounts describe ${SA_EMAIL} --project=${PROJECT_ID} >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Service account exists: ${SA_EMAIL}${NC}"
    
    # Check required IAM roles
    echo "   Checking IAM roles..."
    REQUIRED_ROLES=(
        "roles/spanner.databaseUser"
        "roles/storage.objectAdmin"
        "roles/pubsub.publisher"
        "roles/pubsub.viewer"
        "roles/aiplatform.user"
        "roles/monitoring.metricWriter"
        "roles/cloudtrace.agent"
    )
    
    CURRENT_ROLES=$(gcloud projects get-iam-policy ${PROJECT_ID} \
        --flatten="bindings[].members" \
        --filter="bindings.members:serviceAccount:${SA_EMAIL}" \
        --format="value(bindings.role)" 2>/dev/null | sort)
    
    for role in "${REQUIRED_ROLES[@]}"; do
        if echo "$CURRENT_ROLES" | grep -q "^${role}$"; then
            echo -e "   ${GREEN}✅ ${role}${NC}"
        else
            echo -e "   ${RED}❌ ${role} (missing)${NC}"
        fi
    done
    
    # Check bucket permissions
    echo "   Checking bucket permissions..."
    if gsutil iam get gs://${BUCKET_NAME} 2>/dev/null | grep -q "${SA_EMAIL}"; then
        echo -e "   ${GREEN}✅ Has bucket permissions${NC}"
    else
        echo -e "   ${YELLOW}⚠️  Missing bucket reader permission${NC}"
        echo "      Run: gsutil iam ch serviceAccount:${SA_EMAIL}:legacyBucketReader gs://${BUCKET_NAME}"
    fi
else
    echo -e "${RED}❌ Service account not found${NC}"
fi

# 8. Environment Variables
echo -e "\n8️⃣  Environment Variables:"
ENV_VARS=$(gcloud run services describe analysis-engine \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --format='value(spec.template.spec.containers[0].env[].name)' 2>/dev/null || echo "")

REQUIRED_VARS=("GCP_PROJECT_ID" "SPANNER_INSTANCE_ID" "SPANNER_DATABASE_ID" "REDIS_URL")
for var in "${REQUIRED_VARS[@]}"; do
    if echo "$ENV_VARS" | grep -q "^${var}$"; then
        echo -e "${GREEN}✅ ${var} is set${NC}"
    else
        echo -e "${RED}❌ ${var} is missing${NC}"
    fi
done

# Check for JWT_SECRET
if echo "$ENV_VARS" | grep -q "^JWT_SECRET$"; then
    echo -e "${GREEN}✅ JWT_SECRET is set (authentication enabled)${NC}"
else
    echo -e "${YELLOW}⚠️  JWT_SECRET not set (authentication disabled)${NC}"
fi

# Summary
echo -e "\n📊 Infrastructure Summary:"
echo "=========================="
echo -e "🌐 Service URL: ${SERVICE_URL}"
echo -e "🗄️  Spanner: ccl-instance/ccl_main"
echo -e "💾 Redis: analysis-engine-cache (10.76.85.67:6379)"
echo -e "🔌 VPC: analysis-engine-connector"
echo -e "📦 Storage: gs://${BUCKET_NAME}"
echo -e "📨 Pub/Sub: ${PUBSUB_TOPIC}"

# Next steps
echo -e "\n📋 Next Steps:"
echo "============="
echo "1. Create missing resources (Storage bucket, Pub/Sub topic if needed)"
echo "2. Deploy with JWT_SECRET for authentication: ./deploy-production.sh"
echo "3. Run a test analysis to verify end-to-end functionality"
echo "4. Set up monitoring and alerts"
echo "5. Perform load testing"