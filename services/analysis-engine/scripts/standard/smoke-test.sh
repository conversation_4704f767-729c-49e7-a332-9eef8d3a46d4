#!/bin/bash
# Smoke test script for analysis-engine
# 
# Usage:
#   ./scripts/smoke_test.sh https://analysis-engine-url

set -euo pipefail

SERVICE_URL="${1:-http://localhost:8001}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test 1: Health check
log_info "Testing health endpoint..."
HEALTH_RESPONSE=$(curl -s "$SERVICE_URL/health")
if echo "$HEALTH_RESPONSE" | grep -q "healthy"; then
    log_info "✅ Health check passed"
else
    log_error "❌ Health check failed"
    exit 1
fi

# Test 2: Readiness check
log_info "Testing readiness endpoint..."
READY_RESPONSE=$(curl -s "$SERVICE_URL/health/ready")
READY_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL/health/ready")
if [[ "$READY_STATUS" == "200" || "$READY_STATUS" == "503" ]]; then
    log_info "✅ Readiness endpoint accessible (status: $READY_STATUS)"
    echo "  Response: $READY_RESPONSE"
else
    log_error "❌ Readiness check failed with status: $READY_STATUS"
    exit 1
fi

# Test 3: API version endpoint
log_info "Testing API version..."
VERSION_RESPONSE=$(curl -s "$SERVICE_URL/api/v1/version")
if echo "$VERSION_RESPONSE" | grep -q "version"; then
    log_info "✅ API version endpoint working"
    echo "  Version: $VERSION_RESPONSE"
else
    log_error "❌ API version endpoint failed"
    exit 1
fi

# Test 4: Create analysis (requires API key)
if [[ -n "${API_KEY:-}" ]]; then
    log_info "Testing analysis creation..."
    
    ANALYSIS_REQUEST='{
        "repository_url": "https://github.com/rust-lang/mdBook.git",
        "branch": "master",
        "languages": ["rust"],
        "enable_pattern_detection": false,
        "enable_embeddings": false
    }'
    
    ANALYSIS_RESPONSE=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $API_KEY" \
        -d "$ANALYSIS_REQUEST" \
        "$SERVICE_URL/api/v1/analysis")
    
    if echo "$ANALYSIS_RESPONSE" | grep -q "analysis_id"; then
        log_info "✅ Analysis creation successful"
        ANALYSIS_ID=$(echo "$ANALYSIS_RESPONSE" | jq -r '.analysis_id')
        echo "  Analysis ID: $ANALYSIS_ID"
    else
        log_error "❌ Analysis creation failed"
        echo "  Response: $ANALYSIS_RESPONSE"
        exit 1
    fi
else
    log_info "⚠️  Skipping analysis test (API_KEY not set)"
fi

# Test 5: Metrics endpoint
log_info "Testing metrics endpoint..."
METRICS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL/metrics")
if [[ "$METRICS_STATUS" == "200" ]]; then
    log_info "✅ Metrics endpoint accessible"
else
    log_error "❌ Metrics endpoint failed with status: $METRICS_STATUS"
    exit 1
fi

log_info "🎉 All smoke tests passed!"