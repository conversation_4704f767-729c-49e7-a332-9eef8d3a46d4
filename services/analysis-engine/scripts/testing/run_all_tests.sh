#!/bin/bash

# Test Runner for Analysis Engine
# Executes all test suites in sequence

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test results
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Header
echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
echo -e "${CYAN}           Analysis Engine - Complete Test Suite                   ${NC}"
echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
echo -e "Timestamp: $(date)"
echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}\n"

# Function to run a test script
run_test() {
    local test_name="$1"
    local test_script="$2"
    
    ((TOTAL_TESTS++))
    echo -e "${BLUE}▶ Running $test_name...${NC}"
    
    if [[ -x "$test_script" ]]; then
        if $test_script; then
            echo -e "${GREEN}✓ $test_name PASSED${NC}\n"
            ((PASSED_TESTS++))
        else
            echo -e "${RED}✗ $test_name FAILED${NC}\n"
            ((FAILED_TESTS++))
        fi
    else
        echo -e "${YELLOW}⚠ $test_name SKIPPED (script not found or not executable)${NC}\n"
        ((FAILED_TESTS++))
    fi
}

# Run all test suites
echo -e "${CYAN}Running Unit Tests...${NC}"
echo "────────────────────────────────────────────"
if cargo test --quiet; then
    echo -e "${GREEN}✓ Unit tests PASSED${NC}\n"
    ((PASSED_TESTS++))
    ((TOTAL_TESTS++))
else
    echo -e "${RED}✗ Unit tests FAILED${NC}\n"
    ((FAILED_TESTS++))
    ((TOTAL_TESTS++))
fi

echo -e "${CYAN}Running Integration Tests...${NC}"
echo "────────────────────────────────────────────"
run_test "Integration Tests" "$SCRIPT_DIR/integration_test.sh"

echo -e "${CYAN}Running Database Tests...${NC}"
echo "────────────────────────────────────────────"
run_test "Database Operations" "$SCRIPT_DIR/test_database_operations.sh"
run_test "Migration Tests" "$SCRIPT_DIR/test_migrations.sh"

echo -e "${CYAN}Running API Tests...${NC}"
echo "────────────────────────────────────────────"
run_test "API Endpoints" "$SCRIPT_DIR/test_endpoints.sh"

echo -e "${CYAN}Running AI Integration Tests...${NC}"
echo "────────────────────────────────────────────"
run_test "AI Integration" "$SCRIPT_DIR/test_ai_integration.sh"

echo -e "${CYAN}Running Performance Tests...${NC}"
echo "────────────────────────────────────────────"
run_test "Load Tests" "$SCRIPT_DIR/run_load_tests.sh"

# Summary
echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
echo -e "${CYAN}                          TEST SUMMARY                             ${NC}"
echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
echo -e "Total Tests: $TOTAL_TESTS"
echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
echo -e "${RED}Failed: $FAILED_TESTS${NC}"

if [[ $FAILED_TESTS -eq 0 ]]; then
    echo -e "\n${GREEN}✓ ALL TESTS PASSED!${NC}"
    exit 0
else
    echo -e "\n${RED}✗ SOME TESTS FAILED!${NC}"
    exit 1
fi