#!/bin/bash

# Test script to verify database operations with new schema
# This script tests the basic database operations after schema migration

echo "Testing database operations after schema migration..."

# Check if we can connect to the database
echo "1. Testing database connection..."
gcloud spanner databases execute-sql ccl-main \
  --instance=ccl-production \
  --sql="SELECT COUNT(*) as migration_count FROM schema_migrations"

if [ $? -eq 0 ]; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed"
    exit 1
fi

# Check if all migrations are applied
echo "2. Checking applied migrations..."
gcloud spanner databases execute-sql ccl-main \
  --instance=ccl-production \
  --sql="SELECT version, description FROM schema_migrations ORDER BY version"

# Test basic operations on analyses table
echo "3. Testing analyses table operations..."

# Insert a test analysis
gcloud spanner databases execute-sql ccl-main \
  --instance=ccl-production \
  --sql="INSERT INTO analyses (analysis_id, repository_url, branch, commit_hash, repository_size_bytes, clone_time_ms, status, started_at, user_id, file_count, success_rate, warnings, created_at)
         VALUES ('test-analysis-001', 'https://github.com/test/repo', 'main', 'abc123', 1024, 2500, 'completed', CURRENT_TIMESTAMP(), 'test-user', 10, 0.95, JSON '{\"count\": 0}', CURRENT_TIMESTAMP())"

if [ $? -eq 0 ]; then
    echo "✅ Insert into analyses table successful"
else
    echo "❌ Insert into analyses table failed"
    exit 1
fi

# Query the test analysis
echo "4. Testing analysis retrieval..."
gcloud spanner databases execute-sql ccl-main \
  --instance=ccl-production \
  --sql="SELECT analysis_id, repository_url, commit_hash, repository_size_bytes, clone_time_ms, warnings FROM analyses WHERE analysis_id = 'test-analysis-001'"

if [ $? -eq 0 ]; then
    echo "✅ Query analyses table successful"
else
    echo "❌ Query analyses table failed"
    exit 1
fi

# Test file_analyses table
echo "5. Testing file_analyses table operations..."
gcloud spanner databases execute-sql ccl-main \
  --instance=ccl-production \
  --sql="INSERT INTO file_analyses (analysis_id, file_id, file_path, language, content_hash, size_bytes, lines_of_code, complexity_score, created_at)
         VALUES ('test-analysis-001', 'file-001', 'src/main.rs', 'rust', 'hash123', 512, 100, 2.5, CURRENT_TIMESTAMP())"

if [ $? -eq 0 ]; then
    echo "✅ Insert into file_analyses table successful"
else
    echo "❌ Insert into file_analyses table failed"
    exit 1
fi

# Query file analysis
echo "6. Testing file analysis retrieval..."
gcloud spanner databases execute-sql ccl-main \
  --instance=ccl-production \
  --sql="SELECT analysis_id, file_path, language, size_bytes, lines_of_code FROM file_analyses WHERE analysis_id = 'test-analysis-001'"

if [ $? -eq 0 ]; then
    echo "✅ Query file_analyses table successful"
else
    echo "❌ Query file_analyses table failed"
    exit 1
fi

# Clean up test data
echo "7. Cleaning up test data..."
gcloud spanner databases execute-sql ccl-main \
  --instance=ccl-production \
  --sql="DELETE FROM analyses WHERE analysis_id = 'test-analysis-001'"

echo "✅ All database operations tests passed!"
echo "Schema migration is complete and functional."