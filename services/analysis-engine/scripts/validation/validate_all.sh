#!/bin/bash

# Consolidated Validation Script for Analysis Engine
# Combines database, performance, security, and deployment validations
# This script performs comprehensive post-deployment validation

set -euo pipefail

# Configuration
PROJECT_ID="${PROJECT_ID:-vibe-match-463114}"
INSTANCE_ID="${INSTANCE_ID:-ccl-production}"
DATABASE_ID="${DATABASE_ID:-ccl-main}"
SERVICE_NAME="${SERVICE_NAME:-analysis-engine}"
SERVICE_URL="${SERVICE_URL:-https://analysis-engine-xxxx.run.app}"
LOG_FILE="${LOG_FILE:-validation_$(date +%Y%m%d_%H%M%S).log}"

# Test configuration
LOAD_TEST_DURATION="${LOAD_TEST_DURATION:-60}"
CONCURRENT_USERS="${CONCURRENT_USERS:-10}"
REQUEST_RATE="${REQUEST_RATE:-100}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNINGS=0

# Logging functions
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "${BLUE}$*${NC}"
}

log_success() {
    log "SUCCESS" "${GREEN}✓ $*${NC}"
    ((PASSED_CHECKS++))
    ((TOTAL_CHECKS++))
}

log_warn() {
    log "WARN" "${YELLOW}⚠ $*${NC}"
    ((WARNINGS++))
    ((TOTAL_CHECKS++))
}

log_error() {
    log "ERROR" "${RED}✗ $*${NC}"
    ((FAILED_CHECKS++))
    ((TOTAL_CHECKS++))
}

header() {
    echo -e "\n${CYAN}════════════════════════════════════════════════════════════════${NC}"
    echo -e "${CYAN}▶ $1${NC}"
    echo -e "${CYAN}════════════════════════════════════════════════════════════════${NC}"
}

# 1. DEPLOYMENT VALIDATION
validate_deployment() {
    header "DEPLOYMENT VALIDATION"
    
    log_info "Checking Cloud Run service status..."
    if gcloud run services describe $SERVICE_NAME --region=us-central1 --format="get(status.url)" &>/dev/null; then
        log_success "Cloud Run service is deployed"
    else
        log_error "Cloud Run service not found"
        return 1
    fi
    
    log_info "Checking service health endpoint..."
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL/health" || echo "000")
    if [[ "$HTTP_CODE" == "200" ]]; then
        log_success "Health endpoint responding (HTTP $HTTP_CODE)"
    else
        log_error "Health endpoint failed (HTTP $HTTP_CODE)"
    fi
    
    log_info "Checking API readiness..."
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL/ready" || echo "000")
    if [[ "$HTTP_CODE" == "200" ]]; then
        log_success "Ready endpoint responding"
    else
        log_warn "Ready endpoint returned HTTP $HTTP_CODE"
    fi
}

# 2. DATABASE VALIDATION
validate_database() {
    header "DATABASE VALIDATION"
    
    log_info "Checking Spanner instance connectivity..."
    if gcloud spanner instances describe $INSTANCE_ID --project=$PROJECT_ID &>/dev/null; then
        log_success "Spanner instance accessible"
    else
        log_error "Cannot access Spanner instance"
        return 1
    fi
    
    log_info "Validating database schema..."
    TABLES=$(gcloud spanner databases execute-sql $DATABASE_ID \
        --instance=$INSTANCE_ID \
        --project=$PROJECT_ID \
        --sql="SELECT table_name FROM information_schema.tables WHERE table_schema = ''" \
        --format="value(table_name)" 2>/dev/null || echo "")
    
    EXPECTED_TABLES=("analyses" "file_analyses" "patterns" "repositories" "security_findings")
    for table in "${EXPECTED_TABLES[@]}"; do
        if echo "$TABLES" | grep -q "^$table$"; then
            log_success "Table '$table' exists"
        else
            log_error "Table '$table' missing"
        fi
    done
    
    log_info "Validating indexes..."
    INDEX_COUNT=$(gcloud spanner databases execute-sql $DATABASE_ID \
        --instance=$INSTANCE_ID \
        --project=$PROJECT_ID \
        --sql="SELECT COUNT(*) FROM information_schema.indexes WHERE table_schema = '' AND index_name != 'PRIMARY_KEY'" \
        --format="value()" 2>/dev/null || echo "0")
    
    if [[ "$INDEX_COUNT" -ge "5" ]]; then
        log_success "Found $INDEX_COUNT indexes"
    else
        log_warn "Only $INDEX_COUNT indexes found (expected 5+)"
    fi
}

# 3. PERFORMANCE VALIDATION
validate_performance() {
    header "PERFORMANCE VALIDATION"
    
    log_info "Running basic performance tests..."
    
    # Test 1: Response time
    log_info "Testing API response time..."
    START_TIME=$(date +%s%N)
    curl -s "$SERVICE_URL/health" > /dev/null
    END_TIME=$(date +%s%N)
    RESPONSE_TIME=$((($END_TIME - $START_TIME) / 1000000))
    
    if [[ "$RESPONSE_TIME" -lt "200" ]]; then
        log_success "Health check response time: ${RESPONSE_TIME}ms"
    else
        log_warn "Health check response time: ${RESPONSE_TIME}ms (>200ms)"
    fi
    
    # Test 2: Concurrent connections
    log_info "Testing concurrent connections..."
    CONCURRENT_SUCCESS=0
    for i in {1..10}; do
        curl -s "$SERVICE_URL/health" > /dev/null &
    done
    wait
    log_success "Handled 10 concurrent connections"
    
    # Test 3: Memory check (if metrics endpoint available)
    log_info "Checking memory usage..."
    METRICS=$(curl -s "$SERVICE_URL/metrics" 2>/dev/null || echo "")
    if [[ -n "$METRICS" ]]; then
        MEMORY_MB=$(echo "$METRICS" | grep "memory_usage_bytes" | awk '{print int($2/1048576)}' || echo "0")
        if [[ "$MEMORY_MB" -gt "0" && "$MEMORY_MB" -lt "4096" ]]; then
            log_success "Memory usage: ${MEMORY_MB}MB (within 4GB limit)"
        else
            log_warn "Memory usage: ${MEMORY_MB}MB"
        fi
    else
        log_warn "Metrics endpoint not available"
    fi
}

# 4. SECURITY VALIDATION
validate_security() {
    header "SECURITY VALIDATION"
    
    log_info "Checking security headers..."
    HEADERS=$(curl -sI "$SERVICE_URL/health" 2>/dev/null || echo "")
    
    # Check for security headers
    if echo "$HEADERS" | grep -qi "x-content-type-options: nosniff"; then
        log_success "X-Content-Type-Options header present"
    else
        log_warn "X-Content-Type-Options header missing"
    fi
    
    if echo "$HEADERS" | grep -qi "x-frame-options"; then
        log_success "X-Frame-Options header present"
    else
        log_warn "X-Frame-Options header missing"
    fi
    
    log_info "Checking HTTPS enforcement..."
    if [[ "$SERVICE_URL" == https://* ]]; then
        log_success "HTTPS enforced"
    else
        log_error "Service not using HTTPS"
    fi
    
    log_info "Validating authentication..."
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL/api/v1/analysis" || echo "000")
    if [[ "$HTTP_CODE" == "401" || "$HTTP_CODE" == "403" ]]; then
        log_success "Authentication required for API endpoints"
    else
        log_warn "API endpoint returned HTTP $HTTP_CODE (expected 401/403)"
    fi
}

# 5. FEATURE VALIDATION
validate_features() {
    header "FEATURE VALIDATION"
    
    log_info "Checking language support endpoint..."
    LANGUAGES=$(curl -s "$SERVICE_URL/api/v1/languages" 2>/dev/null || echo "")
    if [[ -n "$LANGUAGES" ]] && echo "$LANGUAGES" | grep -q "rust"; then
        LANG_COUNT=$(echo "$LANGUAGES" | jq -r '.languages | length' 2>/dev/null || echo "0")
        if [[ "$LANG_COUNT" -ge "18" ]]; then
            log_success "Language support: $LANG_COUNT languages"
        else
            log_warn "Only $LANG_COUNT languages supported (expected 18+)"
        fi
    else
        log_error "Language support endpoint failed"
    fi
    
    log_info "Checking AI integration..."
    if gcloud services list --enabled --filter="name:aiplatform.googleapis.com" --format="value(name)" | grep -q "aiplatform"; then
        log_success "Vertex AI API enabled"
    else
        log_error "Vertex AI API not enabled"
    fi
}

# 6. FINAL SUMMARY
print_summary() {
    header "VALIDATION SUMMARY"
    
    echo -e "\n${CYAN}Total Checks:${NC} $TOTAL_CHECKS"
    echo -e "${GREEN}Passed:${NC} $PASSED_CHECKS"
    echo -e "${RED}Failed:${NC} $FAILED_CHECKS"
    echo -e "${YELLOW}Warnings:${NC} $WARNINGS"
    
    SCORE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    echo -e "\n${CYAN}Validation Score:${NC} ${SCORE}%"
    
    if [[ "$FAILED_CHECKS" -eq 0 ]]; then
        echo -e "\n${GREEN}✓ ALL VALIDATIONS PASSED!${NC}"
        echo -e "${GREEN}The Analysis Engine is ready for production use.${NC}"
        return 0
    elif [[ "$SCORE" -ge 80 ]]; then
        echo -e "\n${YELLOW}⚠ VALIDATION PASSED WITH WARNINGS${NC}"
        echo -e "${YELLOW}The service is operational but review warnings above.${NC}"
        return 0
    else
        echo -e "\n${RED}✗ VALIDATION FAILED${NC}"
        echo -e "${RED}Critical issues found. Review errors above.${NC}"
        return 1
    fi
}

# Main execution
main() {
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
    echo -e "${CYAN}           Analysis Engine - Comprehensive Validation              ${NC}"
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
    echo -e "Timestamp: $(date)"
    echo -e "Project: $PROJECT_ID"
    echo -e "Service: $SERVICE_NAME"
    echo -e "Log File: $LOG_FILE"
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════════${NC}"
    
    # Run all validations
    validate_deployment
    validate_database
    validate_performance
    validate_security
    validate_features
    
    # Print summary
    print_summary
}

# Run main function
main "$@"