#!/bin/bash

# Deployment Validation Script for Analysis Engine
# Validates deployment readiness and documentation accuracy

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Validation results
VALIDATION_RESULTS=()
CRITICAL_ISSUES=0
MAJOR_ISSUES=0
MINOR_ISSUES=0

echo -e "${BLUE}🚀 Deployment Validation - Analysis Engine${NC}"
echo "============================================="
echo "Timestamp: $(date)"
echo ""

# Helper function to add validation result
add_result() {
    local severity="$1"
    local component="$2"
    local check="$3"
    local status="$4"
    local message="$5"
    
    case "$severity" in
        "CRITICAL") CRITICAL_ISSUES=$((CRITICAL_ISSUES + 1)) ;;
        "MAJOR") MAJOR_ISSUES=$((MAJOR_ISSUES + 1)) ;;
        "MINOR") MINOR_ISSUES=$((MINOR_ISSUES + 1)) ;;
    esac
    
    VALIDATION_RESULTS+=("$severity|$component|$check|$status|$message")
}

# Validate Docker configuration
validate_docker() {
    echo -e "${BLUE}🐳 Validating Docker Configuration...${NC}"
    
    # Check Dockerfile exists and is valid
    if [ -f "Dockerfile" ]; then
        echo -e "${GREEN}✅ Dockerfile present${NC}"
        add_result "INFO" "Docker" "Dockerfile" "PASS" "Dockerfile exists"
        
        # Check for security best practices
        if grep -q "USER.*[^root]" Dockerfile; then
            echo -e "${GREEN}✅ Non-root user configured${NC}"
            add_result "INFO" "Docker" "Security" "PASS" "Non-root user"
        else
            echo -e "${YELLOW}⚠️  Running as root user${NC}"
            add_result "MINOR" "Docker" "Security" "WARN" "Consider non-root user"
        fi
        
        # Check for multi-stage build
        if grep -q "FROM.*AS" Dockerfile; then
            echo -e "${GREEN}✅ Multi-stage build${NC}"
            add_result "INFO" "Docker" "Optimization" "PASS" "Multi-stage build"
        else
            echo -e "${YELLOW}⚠️  Single-stage build${NC}"
            add_result "MINOR" "Docker" "Optimization" "WARN" "Consider multi-stage"
        fi
        
        # Check for health check
        if grep -q "HEALTHCHECK" Dockerfile; then
            echo -e "${GREEN}✅ Health check configured${NC}"
            add_result "INFO" "Docker" "Health Check" "PASS" "Health check present"
        else
            echo -e "${YELLOW}⚠️  No health check${NC}"
            add_result "MINOR" "Docker" "Health Check" "WARN" "Consider adding health check"
        fi
    else
        echo -e "${RED}❌ Dockerfile missing${NC}"
        add_result "CRITICAL" "Docker" "Dockerfile" "FAIL" "Dockerfile not found"
    fi
    
    # Check docker-compose for local development
    if [ -f "docker-compose.yml" ]; then
        echo -e "${GREEN}✅ Docker Compose configuration present${NC}"
        add_result "INFO" "Docker" "Compose" "PASS" "Docker Compose available"
    else
        echo -e "${YELLOW}⚠️  No Docker Compose configuration${NC}"
        add_result "MINOR" "Docker" "Compose" "WARN" "Consider adding docker-compose.yml"
    fi
    
    echo ""
}

# Validate Cloud Build configuration
validate_cicd() {
    echo -e "${BLUE}⚙️  Validating CI/CD Configuration...${NC}"
    
    # Check Cloud Build configuration
    if [ -f "cloudbuild.yaml" ]; then
        echo -e "${GREEN}✅ Cloud Build configuration present${NC}"
        add_result "INFO" "CI/CD" "Cloud Build" "PASS" "cloudbuild.yaml exists"
        
        # Check for test step
        if grep -q "test\|cargo test" cloudbuild.yaml; then
            echo -e "${GREEN}✅ Test step configured${NC}"
            add_result "INFO" "CI/CD" "Testing" "PASS" "Tests in CI pipeline"
        else
            echo -e "${YELLOW}⚠️  No test step in CI${NC}"
            add_result "MINOR" "CI/CD" "Testing" "WARN" "Add tests to CI pipeline"
        fi
        
        # Check for security scanning
        if grep -q "security\|scan\|vulnerability" cloudbuild.yaml; then
            echo -e "${GREEN}✅ Security scanning configured${NC}"
            add_result "INFO" "CI/CD" "Security" "PASS" "Security scanning present"
        else
            echo -e "${YELLOW}⚠️  No security scanning${NC}"
            add_result "MINOR" "CI/CD" "Security" "WARN" "Consider security scanning"
        fi
    else
        echo -e "${YELLOW}⚠️  No Cloud Build configuration${NC}"
        add_result "MINOR" "CI/CD" "Cloud Build" "WARN" "Consider adding cloudbuild.yaml"
    fi
    
    # Check deployment script
    if [ -f "deploy.sh" ]; then
        echo -e "${GREEN}✅ Deployment script present${NC}"
        add_result "INFO" "CI/CD" "Deployment" "PASS" "deploy.sh exists"
        
        if [ -x "deploy.sh" ]; then
            echo -e "${GREEN}✅ Deployment script executable${NC}"
            add_result "INFO" "CI/CD" "Permissions" "PASS" "deploy.sh executable"
        else
            echo -e "${YELLOW}⚠️  Deployment script not executable${NC}"
            add_result "MINOR" "CI/CD" "Permissions" "WARN" "Make deploy.sh executable"
        fi
    else
        echo -e "${YELLOW}⚠️  No deployment script${NC}"
        add_result "MINOR" "CI/CD" "Deployment" "WARN" "Consider adding deploy.sh"
    fi
    
    echo ""
}

# Validate documentation
validate_documentation() {
    echo -e "${BLUE}📚 Validating Documentation...${NC}"
    
    # Check README
    if [ -f "README.md" ]; then
        echo -e "${GREEN}✅ README.md present${NC}"
        add_result "INFO" "Documentation" "README" "PASS" "README.md exists"
        
        # Check README content
        local readme_sections=0
        if grep -q "# Analysis Engine\|## Overview\|## Features" README.md; then
            ((readme_sections++))
        fi
        if grep -q "## Installation\|## Setup\|## Getting Started" README.md; then
            ((readme_sections++))
        fi
        if grep -q "## API\|## Usage\|## Examples" README.md; then
            ((readme_sections++))
        fi
        if grep -q "## Development\|## Contributing\|## Testing" README.md; then
            ((readme_sections++))
        fi
        
        if [ "$readme_sections" -ge 3 ]; then
            echo -e "${GREEN}✅ Comprehensive README content${NC}"
            add_result "INFO" "Documentation" "README Content" "PASS" "Well-structured README"
        else
            echo -e "${YELLOW}⚠️  README could be more comprehensive${NC}"
            add_result "MINOR" "Documentation" "README Content" "WARN" "Improve README structure"
        fi
    else
        echo -e "${RED}❌ README.md missing${NC}"
        add_result "MAJOR" "Documentation" "README" "FAIL" "README.md not found"
    fi
    
    # Check API documentation
    if [ -f "docs/api.md" ] || grep -q "API" README.md; then
        echo -e "${GREEN}✅ API documentation available${NC}"
        add_result "INFO" "Documentation" "API Docs" "PASS" "API documentation present"
    else
        echo -e "${YELLOW}⚠️  No API documentation${NC}"
        add_result "MINOR" "Documentation" "API Docs" "WARN" "Consider adding API docs"
    fi
    
    # Check deployment documentation
    if [ -f "DEPLOYMENT.md" ] || [ -f "docs/deployment.md" ]; then
        echo -e "${GREEN}✅ Deployment documentation present${NC}"
        add_result "INFO" "Documentation" "Deployment" "PASS" "Deployment docs available"
    else
        echo -e "${YELLOW}⚠️  No deployment documentation${NC}"
        add_result "MINOR" "Documentation" "Deployment" "WARN" "Consider adding deployment docs"
    fi
    
    # Check changelog
    if [ -f "CHANGELOG.md" ] || [ -f "HISTORY.md" ]; then
        echo -e "${GREEN}✅ Changelog present${NC}"
        add_result "INFO" "Documentation" "Changelog" "PASS" "Changelog available"
    else
        echo -e "${YELLOW}⚠️  No changelog${NC}"
        add_result "MINOR" "Documentation" "Changelog" "WARN" "Consider adding CHANGELOG.md"
    fi
    
    echo ""
}

# Validate environment configuration
validate_environment() {
    echo -e "${BLUE}🌍 Validating Environment Configuration...${NC}"
    
    # Check environment variable documentation
    if grep -q "env\|ENV\|environment" README.md 2>/dev/null || [ -f ".env.example" ]; then
        echo -e "${GREEN}✅ Environment variables documented${NC}"
        add_result "INFO" "Environment" "Documentation" "PASS" "Environment vars documented"
    else
        echo -e "${YELLOW}⚠️  Environment variables not documented${NC}"
        add_result "MINOR" "Environment" "Documentation" "WARN" "Document environment variables"
    fi
    
    # Check for .env.example
    if [ -f ".env.example" ]; then
        echo -e "${GREEN}✅ Environment example file present${NC}"
        add_result "INFO" "Environment" "Example" "PASS" ".env.example exists"
    else
        echo -e "${YELLOW}⚠️  No .env.example file${NC}"
        add_result "MINOR" "Environment" "Example" "WARN" "Consider adding .env.example"
    fi
    
    # Check configuration handling in code
    if grep -q "env::var\|std::env" src/config.rs 2>/dev/null; then
        echo -e "${GREEN}✅ Environment configuration handling${NC}"
        add_result "INFO" "Environment" "Handling" "PASS" "Environment config implemented"
    else
        echo -e "${YELLOW}⚠️  Limited environment configuration${NC}"
        add_result "MINOR" "Environment" "Handling" "WARN" "Review environment handling"
    fi
    
    echo ""
}

# Validate service dependencies
validate_dependencies() {
    echo -e "${BLUE}📦 Validating Dependencies...${NC}"
    
    # Check Cargo.toml
    if [ -f "Cargo.toml" ]; then
        echo -e "${GREEN}✅ Cargo.toml present${NC}"
        add_result "INFO" "Dependencies" "Cargo" "PASS" "Cargo.toml exists"
        
        # Check for security audit
        if command -v cargo-audit &> /dev/null; then
            if cargo audit > /dev/null 2>&1; then
                echo -e "${GREEN}✅ No security vulnerabilities in dependencies${NC}"
                add_result "INFO" "Dependencies" "Security" "PASS" "Dependencies secure"
            else
                echo -e "${RED}❌ Security vulnerabilities found in dependencies${NC}"
                add_result "CRITICAL" "Dependencies" "Security" "FAIL" "Vulnerable dependencies"
            fi
        else
            echo -e "${YELLOW}⚠️  cargo-audit not available${NC}"
            add_result "MINOR" "Dependencies" "Security" "WARN" "Install cargo-audit"
        fi
        
        # Check for outdated dependencies
        if command -v cargo-outdated &> /dev/null; then
            local outdated=$(cargo outdated --quiet 2>/dev/null | wc -l)
            if [ "$outdated" -eq 0 ]; then
                echo -e "${GREEN}✅ Dependencies up to date${NC}"
                add_result "INFO" "Dependencies" "Updates" "PASS" "Dependencies current"
            else
                echo -e "${YELLOW}⚠️  $outdated outdated dependencies${NC}"
                add_result "MINOR" "Dependencies" "Updates" "WARN" "Consider updating dependencies"
            fi
        else
            echo -e "${YELLOW}⚠️  cargo-outdated not available${NC}"
            add_result "MINOR" "Dependencies" "Updates" "WARN" "Install cargo-outdated"
        fi
    else
        echo -e "${RED}❌ Cargo.toml missing${NC}"
        add_result "CRITICAL" "Dependencies" "Cargo" "FAIL" "Cargo.toml not found"
    fi
    
    echo ""
}

# Test deployment build
test_deployment_build() {
    echo -e "${BLUE}🔨 Testing Deployment Build...${NC}"
    
    # Test release build
    echo "Building release version..."
    if cargo build --release > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Release build successful${NC}"
        add_result "INFO" "Build" "Release" "PASS" "Release build works"
        
        # Check binary size
        if [ -f "target/release/analysis-engine" ]; then
            local size=$(du -h target/release/analysis-engine | cut -f1)
            echo "Binary size: $size"
            add_result "INFO" "Build" "Binary Size" "PASS" "Binary size: $size"
        fi
    else
        echo -e "${RED}❌ Release build failed${NC}"
        add_result "CRITICAL" "Build" "Release" "FAIL" "Release build broken"
    fi
    
    # Test Docker build if Dockerfile exists
    if [ -f "Dockerfile" ] && command -v docker &> /dev/null; then
        echo "Testing Docker build..."
        if docker build -t analysis-engine-test . > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Docker build successful${NC}"
            add_result "INFO" "Build" "Docker" "PASS" "Docker build works"
            
            # Clean up test image
            docker rmi analysis-engine-test > /dev/null 2>&1 || true
        else
            echo -e "${RED}❌ Docker build failed${NC}"
            add_result "MAJOR" "Build" "Docker" "FAIL" "Docker build broken"
        fi
    else
        echo -e "${YELLOW}⚠️  Docker not available for testing${NC}"
        add_result "MINOR" "Build" "Docker" "WARN" "Docker not available"
    fi
    
    echo ""
}

# Generate deployment validation report
generate_validation_report() {
    echo -e "${BLUE}📋 Generating Validation Report...${NC}"
    
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local report_file="deployment_validation_report_${timestamp}.md"
    
    cat > "$report_file" << EOF
# Deployment Validation Report

**Date**: $(date)  
**Service**: Analysis Engine  
**Version**: $(grep version Cargo.toml | head -1 | cut -d'"' -f2)

## Executive Summary

- **Critical Issues**: $CRITICAL_ISSUES
- **Major Issues**: $MAJOR_ISSUES  
- **Minor Issues**: $MINOR_ISSUES
- **Total Checks**: ${#VALIDATION_RESULTS[@]}

## Validation Results

| Severity | Component | Check | Status | Details |
|----------|-----------|-------|--------|---------|
EOF
    
    for result in "${VALIDATION_RESULTS[@]}"; do
        IFS='|' read -r severity component check status message <<< "$result"
        echo "| $severity | $component | $check | $status | $message |" >> "$report_file"
    done
    
    cat >> "$report_file" << EOF

## Deployment Readiness Score

**Overall Score**: $(echo "scale=1; (${#VALIDATION_RESULTS[@]} - $CRITICAL_ISSUES * 3 - $MAJOR_ISSUES * 2 - $MINOR_ISSUES) * 100 / ${#VALIDATION_RESULTS[@]}" | bc -l)%

## Recommendations

### Critical Issues (Must Fix Before Deployment)
$(for result in "${VALIDATION_RESULTS[@]}"; do
    IFS='|' read -r severity component check status message <<< "$result"
    if [ "$severity" = "CRITICAL" ]; then
        echo "- **$component - $check**: $message"
    fi
done)

### Major Issues (Should Fix Before Deployment)
$(for result in "${VALIDATION_RESULTS[@]}"; do
    IFS='|' read -r severity component check status message <<< "$result"
    if [ "$severity" = "MAJOR" ]; then
        echo "- **$component - $check**: $message"
    fi
done)

### Minor Issues (Consider Fixing)
$(for result in "${VALIDATION_RESULTS[@]}"; do
    IFS='|' read -r severity component check status message <<< "$result"
    if [ "$severity" = "MINOR" ]; then
        echo "- **$component - $check**: $message"
    fi
done)

## Next Steps

1. Address all critical issues immediately
2. Plan resolution for major issues  
3. Consider minor improvements
4. Re-run validation after fixes
5. Proceed with deployment when ready

## Deployment Checklist

- [ ] All critical issues resolved
- [ ] Major issues addressed or accepted
- [ ] Documentation updated
- [ ] Environment variables configured
- [ ] Monitoring and alerting set up
- [ ] Backup and recovery procedures in place
- [ ] Rollback plan prepared
- [ ] Team notified of deployment
EOF
    
    echo -e "${GREEN}✅ Validation report generated: $report_file${NC}"
}

# Main execution
main() {
    validate_docker
    validate_cicd
    validate_documentation
    validate_environment
    validate_dependencies
    test_deployment_build
    
    echo -e "${BLUE}📊 Validation Summary${NC}"
    echo "===================="
    echo -e "Critical Issues: ${RED}$CRITICAL_ISSUES${NC}"
    echo -e "Major Issues: ${YELLOW}$MAJOR_ISSUES${NC}"
    echo -e "Minor Issues: ${YELLOW}$MINOR_ISSUES${NC}"
    echo -e "Total Checks: ${BLUE}${#VALIDATION_RESULTS[@]}${NC}"
    echo ""
    
    generate_validation_report
    
    # Determine deployment readiness
    if [ "$CRITICAL_ISSUES" -eq 0 ] && [ "$MAJOR_ISSUES" -eq 0 ]; then
        echo -e "${GREEN}🚀 Ready for Deployment!${NC}"
        echo "The Analysis Engine is ready for production deployment."
        return 0
    elif [ "$CRITICAL_ISSUES" -eq 0 ]; then
        echo -e "${YELLOW}⚠️  Nearly Ready for Deployment${NC}"
        echo "Address major issues before production deployment."
        return 1
    else
        echo -e "${RED}❌ Not Ready for Deployment${NC}"
        echo "Critical issues must be resolved before deployment."
        return 2
    fi
}

# Check if bc is available for calculations
if ! command -v bc &> /dev/null; then
    echo -e "${RED}❌ 'bc' calculator not found. Please install it first.${NC}"
    exit 1
fi

# Run main function
main "$@"
