#!/bin/bash
# Script to grant necessary IAM permissions for Analysis Engine AI integration

set -e

echo "=== Analysis Engine IAM Permission Grant Script ==="
echo ""
echo "This script requires project owner or IAM admin permissions."
echo "Service Account: <EMAIL>"
echo ""

PROJECT_ID="vibe-match-463114"
SERVICE_ACCOUNT="analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com"

# Function to check if user has permission
check_permission() {
    echo "Checking your permissions..."
    if gcloud projects get-iam-policy $PROJECT_ID --format="value(bindings.members)" | grep -q "$(gcloud config get-value account)"; then
        echo "✓ You have access to the project"
    else
        echo "✗ You may not have sufficient permissions"
        echo "Please ensure you have 'Project IAM Admin' or 'Owner' role"
    fi
}

# Function to grant permissions
grant_permissions() {
    echo ""
    echo "Granting required permissions..."
    
    # Grant Vertex AI User role
    echo "1. Granting Vertex AI User role..."
    gcloud projects add-iam-policy-binding $PROJECT_ID \
        --member="serviceAccount:${SERVICE_ACCOUNT}" \
        --role="roles/aiplatform.user" \
        && echo "✓ Vertex AI User role granted" \
        || echo "✗ Failed to grant Vertex AI User role"
    
    # Grant Service Usage Consumer role
    echo ""
    echo "2. Granting Service Usage Consumer role..."
    gcloud projects add-iam-policy-binding $PROJECT_ID \
        --member="serviceAccount:${SERVICE_ACCOUNT}" \
        --role="roles/serviceusage.serviceUsageConsumer" \
        && echo "✓ Service Usage Consumer role granted" \
        || echo "✗ Failed to grant Service Usage Consumer role"
    
    # Grant additional required roles
    echo ""
    echo "3. Granting Spanner Database User role..."
    gcloud projects add-iam-policy-binding $PROJECT_ID \
        --member="serviceAccount:${SERVICE_ACCOUNT}" \
        --role="roles/spanner.databaseUser" \
        && echo "✓ Spanner Database User role granted" \
        || echo "✗ Failed to grant Spanner Database User role"
    
    echo ""
    echo "4. Granting Storage Object Admin role..."
    gcloud projects add-iam-policy-binding $PROJECT_ID \
        --member="serviceAccount:${SERVICE_ACCOUNT}" \
        --role="roles/storage.objectAdmin" \
        && echo "✓ Storage Object Admin role granted" \
        || echo "✗ Failed to grant Storage Object Admin role"
    
    echo ""
    echo "5. Granting Cloud Run Invoker role (for service-to-service)..."
    gcloud projects add-iam-policy-binding $PROJECT_ID \
        --member="serviceAccount:${SERVICE_ACCOUNT}" \
        --role="roles/run.invoker" \
        && echo "✓ Cloud Run Invoker role granted" \
        || echo "✗ Failed to grant Cloud Run Invoker role"
}

# Function to verify permissions
verify_permissions() {
    echo ""
    echo "=== Verifying Permissions ==="
    echo "Current roles for ${SERVICE_ACCOUNT}:"
    
    gcloud projects get-iam-policy $PROJECT_ID \
        --flatten="bindings[].members" \
        --filter="bindings.members:serviceAccount:${SERVICE_ACCOUNT}" \
        --format="table(bindings.role)" 2>/dev/null || echo "Failed to get IAM policy"
}

# Main execution
echo "Current user: $(gcloud config get-value account)"
check_permission

echo ""
read -p "Do you want to proceed with granting permissions? (y/N) " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    grant_permissions
    verify_permissions
    
    echo ""
    echo "=== Next Steps ==="
    echo "1. Re-run ./test_ai_integration.sh to verify AI access"
    echo "2. Run 'cargo run --bin test_ai_services' to test AI features"
    echo "3. Proceed with deployment using ./deploy-optimized.sh"
else
    echo "Permission grant cancelled."
    exit 0
fi

echo ""
echo "=== Permission Grant Complete ==="