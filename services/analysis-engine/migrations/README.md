# Database Migrations

This directory contains SQL migration scripts for the Analysis Engine Spanner database schema.

## Migration Naming Convention

Migrations should be named with the following pattern:
```
{version}_{description}.sql
```

Examples:
- `001_add_analysis_metadata.sql`
- `002_create_file_analyses_table.sql`
- `003_add_performance_indexes.sql`

## Migration Structure

Each migration file should contain:

1. **Header comment** with description and date
2. **Forward migration** (DDL statements to apply changes)
3. **Rollback instructions** (commented out, for reference)

## Example Migration File

```sql
-- Migration: Add analysis metadata columns
-- Date: 2025-01-08
-- Description: Add commit_hash, repository_size_bytes, clone_time_ms, and warnings columns to analyses table

-- Forward migration
ALTER TABLE analyses ADD COLUMN commit_hash STRING(40);
ALTER TABLE analyses ADD COLUMN repository_size_bytes INT64;
ALTER TABLE analyses ADD COLUMN clone_time_ms INT64;
ALTER TABLE analyses ADD COLUMN warnings JSON;

-- Create index for commit_hash lookups
CREATE INDEX idx_analyses_commit_hash ON analyses(commit_hash);

-- Rollback (for reference only - do not uncomment)
-- DROP INDEX idx_analyses_commit_hash;
-- ALTER TABLE analyses DROP COLUMN warnings;
-- ALTER TABLE analyses DROP COLUMN clone_time_ms;
-- ALTER TABLE analyses DROP COLUMN repository_size_bytes;
-- ALTER TABLE analyses DROP COLUMN commit_hash;
```

## Running Migrations

### Using gcloud CLI:
```bash
# Apply a single migration
gcloud spanner databases ddl update ccl_main \
  --instance=ccl-instance \
  --ddl-file=migrations/001_add_analysis_metadata.sql

# Check current schema
gcloud spanner databases ddl describe ccl_main \
  --instance=ccl-instance
```

### Using the Migration Manager (Rust):
```rust
use crate::migrations::MigrationManager;

let manager = MigrationManager::new(spanner_client);
manager.apply_pending_migrations().await?;
```

## Migration Safety Guidelines

1. **Always test migrations** on a development database first
2. **Use nullable columns** for new fields to avoid blocking existing data
3. **Create indexes concurrently** when possible to avoid blocking operations
4. **Keep migrations small** and focused on a single change
5. **Document rollback procedures** even if not automated
6. **Backup before major changes** in production

## Migration Status Tracking

The system tracks applied migrations in the `schema_migrations` table:

```sql
CREATE TABLE schema_migrations (
    version STRING(50) NOT NULL,
    applied_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    description STRING(255),
) PRIMARY KEY (version);
```

## Current Migration Status

- `001_add_analysis_metadata.sql` - ✅ Ready to apply
- `002_create_file_analyses_table.sql` - 🔄 In development
- `003_add_performance_indexes.sql` - 📋 Planned
