-- Migration: Add analysis metadata columns
-- Date: 2025-01-08
-- Description: Add commit_hash, repository_size_bytes, clone_time_ms, and warnings columns to analyses table
-- This enables intelligent caching, performance tracking, and warning collection

-- Forward migration: Add new columns to analyses table

-- Add commit hash for intelligent cache validation
ALTER TABLE analyses ADD COLUMN commit_hash STRING(40);

-- Add repository size tracking for performance analysis
ALTER TABLE analyses ADD COLUMN repository_size_bytes INT64;

-- Add clone time tracking for performance optimization
ALTER TABLE analyses ADD COLUMN clone_time_ms INT64;

-- Add warnings collection for production monitoring
ALTER TABLE analyses ADD COLUMN warnings JSON;

-- Create index for commit_hash lookups to enable fast cache validation
CREATE INDEX idx_analyses_commit_hash ON analyses(commit_hash);

-- Create index for repository_url + commit_hash for cache key lookups
CREATE INDEX idx_analyses_repo_commit ON analyses(repository_url, commit_hash);

-- Create index for performance analysis queries
CREATE INDEX idx_analyses_performance ON analyses(repository_size_bytes, clone_time_ms) WHERE repository_size_bytes IS NOT NULL;

-- Rollback instructions (for reference only - do not uncomment in production)
-- These commands would reverse the migration if needed:
--
-- DROP INDEX idx_analyses_performance;
-- DROP INDEX idx_analyses_repo_commit;
-- DROP INDEX idx_analyses_commit_hash;
-- ALTER TABLE analyses DROP COLUMN warnings;
-- ALTER TABLE analyses DROP COLUMN clone_time_ms;
-- ALTER TABLE analyses DROP COLUMN repository_size_bytes;
-- ALTER TABLE analyses DROP COLUMN commit_hash;
