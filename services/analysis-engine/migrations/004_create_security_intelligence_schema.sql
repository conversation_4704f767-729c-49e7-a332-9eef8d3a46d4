-- Migration: Create Security Intelligence Schema for Phase 3
-- Date: 2025-07-09
-- Description: Create tables for advanced security intelligence features

-- Security vulnerabilities table
CREATE TABLE security_vulnerabilities (
    vulnerability_id STRING(36) NOT NULL,
    analysis_id STRING(36) NOT NULL,
    cve_id STRING(50),
    cwe_id STRING(50),
    vulnerability_type STRING(100) NOT NULL,
    severity STRING(20) NOT NULL, -- critical, high, medium, low, info
    confidence_score FLOAT64 NOT NULL, -- 0.0 to 1.0
    file_path STRING(2048) NOT NULL,
    line_start INT64,
    line_end INT64,
    code_snippet STRING(MAX),
    description STRING(MAX) NOT NULL,
    remediation_advice STRING(MAX),
    owasp_category STRING(100),
    attack_vector STRING(100),
    exploitability_score FLOAT64,
    impact_score FLOAT64,
    false_positive_probability FLOAT64,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP OPTIONS (allow_commit_timestamp=true),
) PRIMARY KEY (vulnerability_id);

-- Dependency vulnerabilities table
CREATE TABLE dependency_vulnerabilities (
    dependency_vuln_id STRING(36) NOT NULL,
    analysis_id STRING(36) NOT NULL,
    dependency_name STRING(255) NOT NULL,
    dependency_version STRING(100) NOT NULL,
    package_manager STRING(50) NOT NULL, -- npm, pip, cargo, composer, etc.
    cve_id STRING(50),
    vulnerability_source STRING(100) NOT NULL, -- nvd, github, snyk, etc.
    severity STRING(20) NOT NULL,
    cvss_score FLOAT64,
    cvss_vector STRING(255),
    description STRING(MAX),
    published_date TIMESTAMP,
    last_modified_date TIMESTAMP,
    affected_versions JSON,
    patched_versions JSON,
    workaround STRING(MAX),
    exploit_available BOOL DEFAULT FALSE,
    proof_of_concept_available BOOL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP OPTIONS (allow_commit_timestamp=true),
) PRIMARY KEY (dependency_vuln_id);

-- Secrets detection table
CREATE TABLE detected_secrets (
    secret_id STRING(36) NOT NULL,
    analysis_id STRING(36) NOT NULL,
    secret_type STRING(100) NOT NULL, -- api_key, password, token, certificate, etc.
    file_path STRING(2048) NOT NULL,
    line_number INT64,
    secret_hash STRING(255), -- SHA-256 hash of the secret for deduplication
    entropy_score FLOAT64, -- Measure of randomness
    pattern_name STRING(100), -- Name of the regex/ML pattern that detected it
    confidence_score FLOAT64 NOT NULL, -- 0.0 to 1.0
    is_false_positive BOOL DEFAULT FALSE,
    is_test_data BOOL DEFAULT FALSE,
    severity STRING(20) NOT NULL,
    context STRING(1000), -- Surrounding code context
    masked_value STRING(255), -- Partially masked secret for review
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP OPTIONS (allow_commit_timestamp=true),
) PRIMARY KEY (secret_id);

-- Compliance violations table
CREATE TABLE compliance_violations (
    violation_id STRING(36) NOT NULL,
    analysis_id STRING(36) NOT NULL,
    compliance_framework STRING(50) NOT NULL, -- OWASP, CWE, NIST, SOC2, etc.
    rule_id STRING(100) NOT NULL,
    rule_name STRING(255) NOT NULL,
    violation_type STRING(100) NOT NULL,
    severity STRING(20) NOT NULL,
    file_path STRING(2048),
    line_number INT64,
    description STRING(MAX) NOT NULL,
    remediation_guidance STRING(MAX),
    compliance_category STRING(100),
    risk_rating STRING(20), -- low, medium, high, critical
    business_impact STRING(MAX),
    technical_debt_hours FLOAT64, -- Estimated hours to fix
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP OPTIONS (allow_commit_timestamp=true),
) PRIMARY KEY (violation_id);

-- Security scores and risk assessments table
CREATE TABLE security_assessments (
    assessment_id STRING(36) NOT NULL,
    analysis_id STRING(36) NOT NULL,
    overall_security_score FLOAT64 NOT NULL, -- 0.0 to 100.0
    vulnerability_score FLOAT64 NOT NULL,
    dependency_score FLOAT64 NOT NULL,
    secrets_score FLOAT64 NOT NULL,
    compliance_score FLOAT64 NOT NULL,
    risk_level STRING(20) NOT NULL, -- low, medium, high, critical
    total_vulnerabilities INT64,
    critical_vulnerabilities INT64,
    high_vulnerabilities INT64,
    medium_vulnerabilities INT64,
    low_vulnerabilities INT64,
    total_secrets_found INT64,
    high_entropy_secrets INT64,
    compliance_violations_count INT64,
    security_debt_score FLOAT64, -- Technical debt related to security
    improvement_recommendations JSON,
    trending_direction STRING(20), -- improving, declining, stable
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP OPTIONS (allow_commit_timestamp=true),
) PRIMARY KEY (assessment_id);

-- Threat models table
CREATE TABLE threat_models (
    threat_model_id STRING(36) NOT NULL,
    analysis_id STRING(36) NOT NULL,
    threat_category STRING(100) NOT NULL, -- STRIDE, PASTA, etc.
    threat_name STRING(255) NOT NULL,
    threat_description STRING(MAX) NOT NULL,
    threat_actor STRING(100), -- insider, external, nation-state, etc.
    attack_vector STRING(200),
    asset_affected STRING(255),
    likelihood STRING(20), -- very_low, low, medium, high, very_high
    impact STRING(20), -- very_low, low, medium, high, very_high
    risk_score FLOAT64, -- Calculated from likelihood * impact
    mitigation_status STRING(50), -- not_mitigated, partially_mitigated, fully_mitigated
    mitigation_measures JSON,
    residual_risk_score FLOAT64,
    associated_vulnerabilities JSON, -- Array of vulnerability IDs
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP OPTIONS (allow_commit_timestamp=true),
) PRIMARY KEY (threat_model_id);

-- Security intelligence metadata table
CREATE TABLE security_intelligence_metadata (
    metadata_id STRING(36) NOT NULL,
    analysis_id STRING(36) NOT NULL,
    threat_intel_sources JSON, -- Sources used for threat intelligence
    last_threat_intel_update TIMESTAMP,
    vulnerability_databases_used JSON,
    ml_models_used JSON,
    detection_rules_version STRING(50),
    false_positive_rate FLOAT64,
    detection_accuracy FLOAT64,
    scan_duration_ms INT64,
    total_files_scanned INT64,
    total_dependencies_scanned INT64,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP OPTIONS (allow_commit_timestamp=true),
) PRIMARY KEY (metadata_id);

-- Create indexes for security intelligence tables

-- Security vulnerabilities indexes
CREATE INDEX idx_security_vulnerabilities_analysis_id ON security_vulnerabilities(analysis_id);
CREATE INDEX idx_security_vulnerabilities_severity ON security_vulnerabilities(severity);
CREATE INDEX idx_security_vulnerabilities_cve_id ON security_vulnerabilities(cve_id) WHERE cve_id IS NOT NULL;
CREATE INDEX idx_security_vulnerabilities_type ON security_vulnerabilities(vulnerability_type);
CREATE INDEX idx_security_vulnerabilities_confidence ON security_vulnerabilities(confidence_score DESC);

-- Dependency vulnerabilities indexes
CREATE INDEX idx_dependency_vulnerabilities_analysis_id ON dependency_vulnerabilities(analysis_id);
CREATE INDEX idx_dependency_vulnerabilities_dependency ON dependency_vulnerabilities(dependency_name, dependency_version);
CREATE INDEX idx_dependency_vulnerabilities_severity ON dependency_vulnerabilities(severity);
CREATE INDEX idx_dependency_vulnerabilities_cve ON dependency_vulnerabilities(cve_id) WHERE cve_id IS NOT NULL;
CREATE INDEX idx_dependency_vulnerabilities_cvss ON dependency_vulnerabilities(cvss_score DESC) WHERE cvss_score IS NOT NULL;

-- Secrets detection indexes
CREATE INDEX idx_detected_secrets_analysis_id ON detected_secrets(analysis_id);
CREATE INDEX idx_detected_secrets_type ON detected_secrets(secret_type);
CREATE INDEX idx_detected_secrets_severity ON detected_secrets(severity);
CREATE INDEX idx_detected_secrets_confidence ON detected_secrets(confidence_score DESC);
CREATE INDEX idx_detected_secrets_hash ON detected_secrets(secret_hash) WHERE secret_hash IS NOT NULL;

-- Compliance violations indexes
CREATE INDEX idx_compliance_violations_analysis_id ON compliance_violations(analysis_id);
CREATE INDEX idx_compliance_violations_framework ON compliance_violations(compliance_framework);
CREATE INDEX idx_compliance_violations_severity ON compliance_violations(severity);
CREATE INDEX idx_compliance_violations_category ON compliance_violations(compliance_category);

-- Security assessments indexes
CREATE INDEX idx_security_assessments_analysis_id ON security_assessments(analysis_id);
CREATE INDEX idx_security_assessments_score ON security_assessments(overall_security_score DESC);
CREATE INDEX idx_security_assessments_risk_level ON security_assessments(risk_level);
CREATE INDEX idx_security_assessments_created_at ON security_assessments(created_at DESC);

-- Threat models indexes
CREATE INDEX idx_threat_models_analysis_id ON threat_models(analysis_id);
CREATE INDEX idx_threat_models_category ON threat_models(threat_category);
CREATE INDEX idx_threat_models_risk_score ON threat_models(risk_score DESC);
CREATE INDEX idx_threat_models_likelihood ON threat_models(likelihood);

-- Security intelligence metadata indexes
CREATE INDEX idx_security_intelligence_metadata_analysis_id ON security_intelligence_metadata(analysis_id);
CREATE INDEX idx_security_intelligence_metadata_created_at ON security_intelligence_metadata(created_at DESC);

-- Foreign key constraints (logical - Spanner doesn't enforce foreign keys)
-- These are documented for application-level constraint validation

-- security_vulnerabilities.analysis_id -> analyses.analysis_id
-- dependency_vulnerabilities.analysis_id -> analyses.analysis_id
-- detected_secrets.analysis_id -> analyses.analysis_id
-- compliance_violations.analysis_id -> analyses.analysis_id
-- security_assessments.analysis_id -> analyses.analysis_id
-- threat_models.analysis_id -> analyses.analysis_id
-- security_intelligence_metadata.analysis_id -> analyses.analysis_id

-- Audit logs enhancement for security events
CREATE TABLE security_audit_logs (
    log_id STRING(36) NOT NULL,
    analysis_id STRING(36),
    event_type STRING(100) NOT NULL, -- vulnerability_detected, secret_found, threat_identified, etc.
    event_severity STRING(20) NOT NULL,
    event_details JSON,
    user_id STRING(255),
    ip_address STRING(45),
    user_agent STRING(500),
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
) PRIMARY KEY (log_id);

CREATE INDEX idx_security_audit_logs_analysis_id ON security_audit_logs(analysis_id) WHERE analysis_id IS NOT NULL;
CREATE INDEX idx_security_audit_logs_event_type ON security_audit_logs(event_type);
CREATE INDEX idx_security_audit_logs_severity ON security_audit_logs(event_severity);
CREATE INDEX idx_security_audit_logs_created_at ON security_audit_logs(created_at DESC);

-- Note: This migration creates the foundation for Phase 3 security intelligence
-- Additional migrations may be needed as features are implemented and requirements evolve