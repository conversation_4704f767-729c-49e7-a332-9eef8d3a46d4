-- Migration: Create base schema for Analysis Engine
-- Date: 2025-01-08
-- Description: Create the initial analyses table and schema_migrations table

-- Create schema_migrations table for tracking applied migrations
CREATE TABLE schema_migrations (
    version STRING(50) NOT NULL,
    applied_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    description STRING(255),
) PRIMARY KEY (version);

-- Create analyses table - the main table for storing analysis results
CREATE TABLE analyses (
    analysis_id STRING(36) NOT NULL,
    repository_url STRING(2048) NOT NULL,
    branch STRING(255) NOT NULL,
    status STRING(50) NOT NULL,
    started_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    completed_at TIMESTAMP OPTIONS (allow_commit_timestamp=true),
    duration_seconds INT64,
    metrics JSON,
    patterns JSON,
    languages JSON,
    embeddings JSON,
    error_message STRING(MAX),
    user_id STRING(255),
    file_count INT64,
    success_rate FLOAT64,
    successful_analyses JSON,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP OPTIONS (allow_commit_timestamp=true),
) PRIMARY KEY (analysis_id);

-- Create indexes for common queries
CREATE INDEX idx_analyses_repository_url ON analyses(repository_url);
CREATE INDEX idx_analyses_status ON analyses(status);
CREATE INDEX idx_analyses_user_id ON analyses(user_id) WHERE user_id IS NOT NULL;
CREATE INDEX idx_analyses_started_at ON analyses(started_at DESC);
CREATE INDEX idx_analyses_completed_at ON analyses(completed_at DESC) WHERE completed_at IS NOT NULL;

-- Note: Migration record will be inserted via DML after DDL is applied

-- Rollback instructions (for reference only - do not uncomment in production)
-- DROP INDEX idx_analyses_completed_at;
-- DROP INDEX idx_analyses_started_at;
-- DROP INDEX idx_analyses_user_id;
-- DROP INDEX idx_analyses_status;
-- DROP INDEX idx_analyses_repository_url;
-- DROP TABLE analyses;
-- DROP TABLE schema_migrations;