   Compiling analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
warning: unused import: `Symbol`
 --> src/services/intelligent_documentation.rs:2:35
  |
2 | use crate::models::{FileAnalysis, Symbol, SymbolType};
  |                                   ^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused import: `Context`
 --> src/services/semantic_search.rs:1:14
  |
1 | use anyhow::{Context, Result};
  |              ^^^^^^^

warning: unused imports: `Deserialize` and `Serialize`
 --> src/services/security_analyzer.rs:4:13
  |
4 | use serde::{Deserialize, Serialize};
  |             ^^^^^^^^^^^  ^^^^^^^^^

warning: unused import: `HashSet`
 --> src/services/security_analyzer.rs:5:33
  |
5 | use std::collections::{HashMap, HashSet};
  |                                 ^^^^^^^

warning: unused imports: `error` and `warn`
 --> src/services/security_analyzer.rs:9:28
  |
9 | use tracing::{debug, info, warn, error};
  |                            ^^^^  ^^^^^

warning: unused import: `http::StatusCode`
 --> src/api/handlers/security.rs:3:5
  |
3 |     http::StatusCode,
  |     ^^^^^^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
 --> src/parser/adapters.rs:3:5
  |
3 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::path::Path`
  --> src/parser/language_validation_test.rs:3:5
   |
3  | use std::path::Path;
   |     ^^^^^^^^^^^^^^^
   |
help: if this is a test module, consider adding a `#[cfg(test)]` to the containing module
  --> src/parser/mod.rs:16:1
   |
16 | mod language_validation_test;
   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `tree_sitter_rust`
  --> src/parser/mod.rs:25:5
   |
25 | use tree_sitter_rust;
   |     ^^^^^^^^^^^^^^^^

warning: unused import: `tree_sitter_python`
  --> src/parser/mod.rs:26:5
   |
26 | use tree_sitter_python;
   |     ^^^^^^^^^^^^^^^^^^

warning: unused import: `tree_sitter_javascript`
  --> src/parser/mod.rs:27:5
   |
27 | use tree_sitter_javascript;
   |     ^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `tree_sitter_typescript`
  --> src/parser/mod.rs:28:5
   |
28 | use tree_sitter_typescript;
   |     ^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `tree_sitter_go`
  --> src/parser/mod.rs:29:5
   |
29 | use tree_sitter_go;
   |     ^^^^^^^^^^^^^^

warning: unused import: `tree_sitter_java`
  --> src/parser/mod.rs:30:5
   |
30 | use tree_sitter_java;
   |     ^^^^^^^^^^^^^^^^

warning: unused import: `tree_sitter_c`
  --> src/parser/mod.rs:31:5
   |
31 | use tree_sitter_c;
   |     ^^^^^^^^^^^^^

warning: unused import: `tree_sitter_cpp`
  --> src/parser/mod.rs:32:5
   |
32 | use tree_sitter_cpp;
   |     ^^^^^^^^^^^^^^^

warning: unused import: `tree_sitter_ruby`
  --> src/parser/mod.rs:39:5
   |
39 | use tree_sitter_ruby;
   |     ^^^^^^^^^^^^^^^^

warning: unused import: `tree_sitter_bash`
  --> src/parser/mod.rs:40:5
   |
40 | use tree_sitter_bash;
   |     ^^^^^^^^^^^^^^^^

warning: unused import: `tree_sitter_md`
  --> src/parser/mod.rs:41:5
   |
41 | use tree_sitter_md;
   |     ^^^^^^^^^^^^^^

warning: unused import: `tree_sitter_xml`
  --> src/parser/mod.rs:42:5
   |
42 | use tree_sitter_xml;
   |     ^^^^^^^^^^^^^^^

warning: unused import: `tree_sitter_swift`
  --> src/parser/mod.rs:45:5
   |
45 | use tree_sitter_swift;
   |     ^^^^^^^^^^^^^^^^^

warning: unused import: `tree_sitter_objc`
  --> src/parser/mod.rs:47:5
   |
47 | use tree_sitter_objc;
   |     ^^^^^^^^^^^^^^^^

warning: unused import: `tree_sitter_r`
  --> src/parser/mod.rs:50:5
   |
50 | use tree_sitter_r;
   |     ^^^^^^^^^^^^^

warning: unused import: `tree_sitter_julia`
  --> src/parser/mod.rs:51:5
   |
51 | use tree_sitter_julia;
   |     ^^^^^^^^^^^^^^^^^

warning: unused import: `tree_sitter_haskell`
  --> src/parser/mod.rs:54:5
   |
54 | use tree_sitter_haskell;
   |     ^^^^^^^^^^^^^^^^^^^

warning: unused import: `tree_sitter_scala`
  --> src/parser/mod.rs:55:5
   |
55 | use tree_sitter_scala;
   |     ^^^^^^^^^^^^^^^^^

warning: unused import: `tree_sitter_elixir`
  --> src/parser/mod.rs:57:5
   |
57 | use tree_sitter_elixir;
   |     ^^^^^^^^^^^^^^^^^^

warning: unused import: `tree_sitter_zig`
  --> src/parser/mod.rs:60:5
   |
60 | use tree_sitter_zig;
   |     ^^^^^^^^^^^^^^^

warning: unused import: `tree_sitter_nix`
  --> src/parser/mod.rs:66:5
   |
66 | use tree_sitter_nix;
   |     ^^^^^^^^^^^^^^^

warning: unused import: `Instant`
 --> src/metrics/mod.rs:4:27
  |
4 | use std::time::{Duration, Instant};
  |                           ^^^^^^^

warning: unused variable: `repo_path`
   --> src/services/analyzer.rs:574:9
    |
574 |         repo_path: &Path,
    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_repo_path`
    |
    = note: `#[warn(unused_variables)]` on by default

warning: unused variable: `streaming_processor`
   --> src/services/analyzer.rs:596:13
    |
596 |         let streaming_processor = StreamingFileProcessor::new(
    |             ^^^^^^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_streaming_processor`

warning: unused variable: `batch_idx`
   --> src/services/analyzer.rs:615:14
    |
615 |         for (batch_idx, batch) in files.chunks(batch_size).enumerate() {
    |              ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_batch_idx`

warning: unused variable: `config`
   --> src/services/analyzer.rs:787:9
    |
787 |         config: &StreamingAnalysisConfig,
    |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_config`

warning: variable does not need to be mutable
    --> src/parser/mod.rs:1019:13
     |
1019 |         let mut file = File::open(file_path).await.map_err(|e| ParseError {
     |             ----^^^^
     |             |
     |             help: remove this `mut`
     |
     = note: `#[warn(unused_mut)]` on by default

warning: unused variable: `embedding`
   --> src/services/semantic_search.rs:382:9
    |
382 |         embedding: &CodeEmbedding,
    |         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_embedding`

warning: unused variable: `threat_intel_enabled`
    --> src/services/security_analyzer.rs:1812:9
     |
1812 |         threat_intel_enabled: bool,
     |         ^^^^^^^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_threat_intel_enabled`

warning: unused variable: `dependency_vulnerabilities`
    --> src/services/security_analyzer.rs:3106:9
     |
3106 |         dependency_vulnerabilities: &[DependencyVulnerability],
     |         ^^^^^^^^^^^^^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_dependency_vulnerabilities`

warning: unused variable: `security_assessment`
    --> src/services/security_analyzer.rs:3107:9
     |
3107 |         security_assessment: &SecurityAssessment,
     |         ^^^^^^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_security_assessment`

warning: variable does not need to be mutable
    --> src/storage/spanner.rs:1318:13
     |
1318 |         let mut vulnerabilities = Vec::new();
     |             ----^^^^^^^^^^^^^^^
     |             |
     |             help: remove this `mut`

warning: variable does not need to be mutable
    --> src/storage/spanner.rs:1338:13
     |
1338 |         let mut secrets = Vec::new();
     |             ----^^^^^^^
     |             |
     |             help: remove this `mut`

warning: variable does not need to be mutable
    --> src/storage/spanner.rs:1358:13
     |
1358 |         let mut violations = Vec::new();
     |             ----^^^^^^^^^^
     |             |
     |             help: remove this `mut`

warning: variable does not need to be mutable
    --> src/storage/spanner.rs:1378:13
     |
1378 |         let mut threat_models = Vec::new();
     |             ----^^^^^^^^^^^^^
     |             |
     |             help: remove this `mut`

warning: unused variable: `parser`
   --> src/parser/mod.rs:632:9
    |
632 |         parser: &TreeSitterParser,
    |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_parser`

warning: unreachable expression
   --> src/metrics/mod.rs:521:9
    |
517 |               return Ok(MemoryInfo { total, used, available });
    |               ------------------------------------------------ any code following this expression is unreachable
...
521 | /         Ok(MemoryInfo {
522 | |             total: 4 * 1024 * 1024 * 1024, // 4GB default
523 | |             used: 2 * 1024 * 1024 * 1024,  // 2GB used
524 | |             available: 2 * 1024 * 1024 * 1024, // 2GB available
525 | |         })
    | |__________^ unreachable expression
    |
    = note: `#[warn(unreachable_code)]` on by default

warning: unused import: `rayon::prelude`
  --> src/services/analyzer.rs:13:5
   |
13 | use rayon::prelude::*;
   |     ^^^^^^^^^^^^^^

warning: unused import: `Hasher`
 --> src/services/semantic_search.rs:9:23
  |
9 | use std::hash::{Hash, Hasher};
  |                       ^^^^^^

warning: unused import: `Hash`
 --> src/services/semantic_search.rs:9:17
  |
9 | use std::hash::{Hash, Hasher};
  |                 ^^^^

warning: unused variable: `analysis`
   --> src/services/ai_pattern_detector.rs:404:37
    |
404 |             let range = if let Some(analysis) = file_analysis {
    |                                     ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_analysis`

warning: unused variable: `patterns`
   --> src/services/repository_insights.rs:750:9
    |
750 |         patterns: &[DetectedPattern],
    |         ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_patterns`

warning: variable `function_count` is assigned to, but never used
   --> src/services/intelligent_documentation.rs:713:17
    |
713 |         let mut function_count = 0;
    |                 ^^^^^^^^^^^^^^
    |
    = note: consider using `_function_count` instead

warning: variable `class_count` is assigned to, but never used
   --> src/services/intelligent_documentation.rs:714:17
    |
714 |         let mut class_count = 0;
    |                 ^^^^^^^^^^^
    |
    = note: consider using `_class_count` instead

warning: unused variable: `m`
    --> src/services/security_analyzer.rs:1330:33
     |
1330 |                     if let Some(m) = regex.find(line) {
     |                                 ^ help: if this is intentional, prefix it with an underscore: `_m`

warning: unused variable: `language`
    --> src/services/security_analyzer.rs:1566:55
     |
1566 |     fn detect_weak_cryptography(&self, content: &str, language: &str) -> Result<Vec<AIVulnerability>> {
     |                                                       ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_language`

warning: unused variable: `language`
    --> src/services/security_analyzer.rs:1604:59
     |
1604 |     fn detect_broken_authentication(&self, content: &str, language: &str) -> Result<Vec<AIVulnerability>> {
     |                                                           ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_language`

warning: unused variable: `cap`
    --> src/services/security_analyzer.rs:3422:25
     |
3422 |             if let Some(cap) = self.regex.find(line) {
     |                         ^^^ help: if this is intentional, prefix it with an underscore: `_cap`

warning: value assigned to `current_line` is never read
   --> src/parser/adapters.rs:309:17
    |
309 |         let mut current_line = 0;
    |                 ^^^^^^^^^^^^
    |
    = help: maybe it is overwritten before being read?
    = note: `#[warn(unused_assignments)]` on by default

warning: variable `blank_lines` is assigned to, but never used
   --> src/parser/language_metrics.rs:889:17
    |
889 |         let mut blank_lines = 0;
    |                 ^^^^^^^^^^^
    |
    = note: consider using `_blank_lines` instead

warning: unreachable pattern
   --> src/parser/mod.rs:120:9
    |
91  |         "xml" => None, // tree_sitter_xml API unclear, use custom XML parser instead
    |         ----- matches all the relevant values
...
120 |         "xml" => None,  // Use custom XML parser via quick-xml crate
    |         ^^^^^ no value can reach this
    |
    = note: `#[warn(unreachable_patterns)]` on by default

warning: unused variable: `reporter_clone`
   --> src/parser/mod.rs:590:13
    |
590 |         let reporter_clone = progress_reporter.clone();
    |             ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_reporter_clone`

warning: type `EmbeddingMetrics` is more private than the item `EnhancedEmbeddingsService::get_metrics`
   --> src/services/embeddings_enhancement.rs:595:5
    |
595 |     pub async fn get_metrics(&self) -> EmbeddingMetrics {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ method `EnhancedEmbeddingsService::get_metrics` is reachable at visibility `pub`
    |
note: but type `EmbeddingMetrics` is only usable at visibility `pub(self)`
   --> src/services/embeddings_enhancement.rs:107:1
    |
107 | struct EmbeddingMetrics {
    | ^^^^^^^^^^^^^^^^^^^^^^^
    = note: `#[warn(private_interfaces)]` on by default

warning: type `AIPatternMetrics` is more private than the item `AIPatternDetector::get_metrics`
   --> src/services/ai_pattern_detector.rs:607:5
    |
607 |     pub async fn get_metrics(&self) -> AIPatternMetrics {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ method `AIPatternDetector::get_metrics` is reachable at visibility `pub`
    |
note: but type `AIPatternMetrics` is only usable at visibility `pub(self)`
   --> src/services/ai_pattern_detector.rs:106:1
    |
106 | struct AIPatternMetrics {
    | ^^^^^^^^^^^^^^^^^^^^^^^

warning: type `QualityAssessmentMetrics` is more private than the item `CodeQualityAssessor::get_metrics`
   --> src/services/code_quality_assessor.rs:770:5
    |
770 |     pub async fn get_metrics(&self) -> QualityAssessmentMetrics {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ method `CodeQualityAssessor::get_metrics` is reachable at visibility `pub`
    |
note: but type `QualityAssessmentMetrics` is only usable at visibility `pub(self)`
   --> src/services/code_quality_assessor.rs:191:1
    |
191 | struct QualityAssessmentMetrics {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: type `InsightsMetricsData` is more private than the item `RepositoryInsightsService::get_metrics`
    --> src/services/repository_insights.rs:1044:5
     |
1044 |     pub async fn get_metrics(&self) -> InsightsMetricsData {
     |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ method `RepositoryInsightsService::get_metrics` is reachable at visibility `pub`
     |
note: but type `InsightsMetricsData` is only usable at visibility `pub(self)`
    --> src/services/repository_insights.rs:380:1
     |
380  | struct InsightsMetricsData {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: type `intelligent_documentation::DocumentationMetrics` is more private than the item `IntelligentDocumentationService::get_metrics`
    --> src/services/intelligent_documentation.rs:1174:5
     |
1174 |     pub async fn get_metrics(&self) -> DocumentationMetrics {
     |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ method `IntelligentDocumentationService::get_metrics` is reachable at visibility `pub`
     |
note: but type `intelligent_documentation::DocumentationMetrics` is only usable at visibility `pub(self)`
    --> src/services/intelligent_documentation.rs:497:1
     |
497  | struct DocumentationMetrics {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: type `SearchMetrics` is more private than the item `SemanticSearchService::get_metrics`
   --> src/services/semantic_search.rs:649:5
    |
649 |     pub async fn get_metrics(&self) -> SearchMetrics {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ method `SemanticSearchService::get_metrics` is reachable at visibility `pub`
    |
note: but type `SearchMetrics` is only usable at visibility `pub(self)`
   --> src/services/semantic_search.rs:111:1
    |
111 | struct SearchMetrics {
    | ^^^^^^^^^^^^^^^^^^^^

warning: field `streaming_processor` is never read
  --> src/services/analyzer.rs:31:5
   |
20 | pub struct AnalysisService {
   |            --------------- field in this struct
...
31 |     streaming_processor: Option<Arc<StreamingFileProcessor>>,
   |     ^^^^^^^^^^^^^^^^^^^
   |
   = note: `#[warn(dead_code)]` on by default

warning: field `truncated` is never read
  --> src/services/embeddings_enhancement.rs:57:5
   |
55 | struct EmbeddingStats {
   |        -------------- field in this struct
56 |     token_count: Option<u32>,
57 |     truncated: Option<bool>,
   |     ^^^^^^^^^
   |
   = note: `EmbeddingStats` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `confidence` and `reasoning` are never read
  --> src/services/ai_pattern_detector.rs:73:5
   |
71 | struct AIPatternAnalysis {
   |        ----------------- fields in this struct
72 |     patterns: Vec<AIDetectedPattern>,
73 |     confidence: f32,
   |     ^^^^^^^^^^
74 |     reasoning: String,
   |     ^^^^^^^^^
   |
   = note: `AIPatternAnalysis` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: fields `pattern_name` and `severity` are never read
  --> src/services/ai_pattern_detector.rs:80:5
   |
78 | struct AIDetectedPattern {
   |        ----------------- fields in this struct
79 |     pattern_type: String,
80 |     pattern_name: String,
   |     ^^^^^^^^^^^^
...
85 |     severity: String,
   |     ^^^^^^^^
   |
   = note: `AIDetectedPattern` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis

warning: field `embeddings_service` is never read
   --> src/services/ai_pattern_detector.rs:128:5
    |
117 | pub struct AIPatternDetector {
    |            ----------------- field in this struct
...
128 |     embeddings_service: Arc<EnhancedEmbeddingsService>,
    |     ^^^^^^^^^^^^^^^^^^

warning: field `embeddings_service` is never read
   --> src/services/code_quality_assessor.rs:212:5
    |
201 | pub struct CodeQualityAssessor {
    |            ------------------- field in this struct
...
212 |     embeddings_service: Arc<EnhancedEmbeddingsService>,
    |     ^^^^^^^^^^^^^^^^^^

warning: field `embeddings_service` is never read
   --> src/services/repository_insights.rs:400:5
    |
389 | pub struct RepositoryInsightsService {
    |            ------------------------- field in this struct
...
400 |     embeddings_service: Arc<EnhancedEmbeddingsService>,
    |     ^^^^^^^^^^^^^^^^^^

warning: field `embeddings_service` is never read
   --> src/services/intelligent_documentation.rs:517:5
    |
506 | pub struct IntelligentDocumentationService {
    |            ------------------------------- field in this struct
...
517 |     embeddings_service: Arc<EnhancedEmbeddingsService>,
    |     ^^^^^^^^^^^^^^^^^^

warning: field `model_version` is never read
    --> src/services/security_analyzer.rs:1140:5
     |
1139 | pub struct MlVulnerabilityClassifier {
     |            ------------------------- field in this struct
1140 |     model_version: String,
     |     ^^^^^^^^^^^^^

warning: methods `detect_sql_injection_patterns`, `detect_xss_patterns`, and `detect_command_injection_patterns` are never used
    --> src/services/security_analyzer.rs:1245:8
     |
1143 | impl MlVulnerabilityClassifier {
     | ------------------------------ methods in this implementation
...
1245 |     fn detect_sql_injection_patterns(&self, content: &str) -> bool {
     |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
1263 |     fn detect_xss_patterns(&self, content: &str) -> bool {
     |        ^^^^^^^^^^^^^^^^^^^
...
1281 |     fn detect_command_injection_patterns(&self, content: &str) -> bool {
     |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: fields `connection_pool` and `batch_processor` are never read
  --> src/storage/spanner.rs:50:5
   |
39 | pub struct SpannerOperations {
   |            ----------------- fields in this struct
...
50 |     connection_pool: Arc<ConnectionPool>,
   |     ^^^^^^^^^^^^^^^
51 |     /// Batch processor for efficient bulk operations
52 |     batch_processor: Arc<BatchProcessor>,
   |     ^^^^^^^^^^^^^^^

warning: fields `client` and `connection_id` are never read
  --> src/storage/spanner.rs:89:5
   |
88 | pub struct PooledConnection {
   |            ---------------- fields in this struct
89 |     client: Client,
   |     ^^^^^^
...
92 |     connection_id: String,
   |     ^^^^^^^^^^^^^

warning: methods `threat_category_to_string`, `threat_actor_to_string`, `mitigation_status_to_string`, `trending_direction_to_string`, `likelihood_to_string`, and `impact_to_string` are never used
    --> src/storage/spanner.rs:1419:8
     |
974  | impl SpannerOperations {
     | ---------------------- methods in this implementation
...
1419 |     fn threat_category_to_string(&self, category: &ThreatCategory) -> String {
     |        ^^^^^^^^^^^^^^^^^^^^^^^^^
...
1431 |     fn threat_actor_to_string(&self, actor: &ThreatActor) -> String {
     |        ^^^^^^^^^^^^^^^^^^^^^^
...
1443 |     fn mitigation_status_to_string(&self, status: &MitigationStatus) -> String {
     |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
1451 |     fn trending_direction_to_string(&self, direction: &TrendingDirection) -> String {
     |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
1459 |     fn likelihood_to_string(&self, likelihood: &Likelihood) -> String {
     |        ^^^^^^^^^^^^^^^^^^^^
...
1469 |     fn impact_to_string(&self, impact: &Impact) -> String {
     |        ^^^^^^^^^^^^^^^^

warning: constant `METADATA_CACHE_TTL` is never used
  --> src/storage/cache.rs:23:7
   |
23 | const METADATA_CACHE_TTL: u64 = 1800; // 30 minutes
   |       ^^^^^^^^^^^^^^^^^^

warning: constant `POPULAR_CACHE_TTL` is never used
  --> src/storage/cache.rs:24:7
   |
24 | const POPULAR_CACHE_TTL: u64 = 43200; // 12 hours
   |       ^^^^^^^^^^^^^^^^^

warning: method `shrink_if_underutilized` is never used
   --> src/parser/mod.rs:264:12
    |
145 | impl ParserPool {
    | --------------- method in this implementation
...
264 |     pub fn shrink_if_underutilized(&self, min_utilization: f64) -> Result<()> {
    |            ^^^^^^^^^^^^^^^^^^^^^^^

warning: associated function `process_large_file_streaming` is never used
   --> src/parser/mod.rs:733:14
    |
582 | impl StreamingFileProcessor {
    | --------------------------- associated function in this implementation
...
733 |     async fn process_large_file_streaming(
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: struct `ChunkMetrics` is never constructed
   --> src/parser/mod.rs:821:8
    |
821 | struct ChunkMetrics {
    |        ^^^^^^^^^^^^

warning: methods `parse_file_streaming_hybrid`, `create_content_stream`, `analyze_chunk`, `extract_symbols_from_chunk`, and `create_chunks_from_content` are never used
    --> src/parser/mod.rs:1152:14
     |
829  | impl TreeSitterParser {
     | --------------------- methods in this implementation
...
1152 |     async fn parse_file_streaming_hybrid(&self, file_path: &Path, file_size: u64, config: &StreamingConfig) -> Result<FileAnalysis, Parse...
     |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
1291 |     async fn create_content_stream(&self, file_path: &Path, config: &StreamingConfig) -> Result<Vec<ContentChunk>, ParseError> {
     |              ^^^^^^^^^^^^^^^^^^^^^
...
1362 |     fn analyze_chunk(&self, content: &str, language: &str, _start_line: usize) -> ChunkMetrics {
     |        ^^^^^^^^^^^^^
...
1411 |     fn extract_symbols_from_chunk(&self, content: &str, language: &str, start_line: usize, start_offset: usize) -> Vec<Symbol> {
     |        ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
1485 |     fn create_chunks_from_content(&self, content: &str, language: &str, start_line: usize, start_offset: usize) -> Vec<CodeChunk> {
     |        ^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: function `extract_symbol_name` is never used
    --> src/parser/mod.rs:2058:4
     |
2058 | fn extract_symbol_name(line: &str, prefix: &str) -> Option<String> {
     |    ^^^^^^^^^^^^^^^^^^^

warning: fields `import_patterns` and `cyclomatic_complexity_keywords` are never read
  --> src/parser/language_metrics.rs:16:9
   |
12 | pub struct LanguagePatterns {
   |            ---------------- fields in this struct
...
16 |     pub import_patterns: Vec<String>,
   |         ^^^^^^^^^^^^^^^
17 |     pub complexity_patterns: Vec<String>,
18 |     pub cyclomatic_complexity_keywords: Vec<String>,
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = note: `LanguagePatterns` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: field `max_class_length` is never read
  --> src/parser/language_metrics.rs:27:9
   |
24 | pub struct MaintainabilityFactors {
   |            ---------------------- field in this struct
...
27 |     pub max_class_length: usize,
   |         ^^^^^^^^^^^^^^^^
   |
   = note: `MaintainabilityFactors` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis

warning: field `config` is never read
   --> src/metrics/mod.rs:178:5
    |
176 | pub struct BottleneckDetector {
    |            ------------------ field in this struct
177 |     /// Bottleneck analysis configuration
178 |     config: BottleneckDetectorConfig,
    |     ^^^^^^

warning: fields `config` and `alert_history` are never read
   --> src/metrics/mod.rs:254:5
    |
252 | pub struct AlertingSystem {
    |            -------------- fields in this struct
253 |     /// Alert configuration
254 |     config: AlertingConfig,
    |     ^^^^^^
...
258 |     alert_history: Arc<RwLock<Vec<PerformanceAlert>>>,
    |     ^^^^^^^^^^^^^

warning: `analysis-engine` (lib) generated 90 warnings (run `cargo fix --lib -p analysis-engine` to apply 35 suggestions)
