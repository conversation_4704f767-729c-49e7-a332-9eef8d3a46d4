{"debug_mode": true, "logging": {"level": "debug", "target_modules": ["analysis_engine::services::security", "analysis_engine::services::parser", "analysis_engine::api", "analysis_engine::storage"], "enable_tracing": true}, "analysis_config": {"max_file_size_mb": 5, "parse_timeout_seconds": 15, "max_concurrent_analyses": 5, "enable_ast_debug_output": true, "enable_intermediate_results_logging": false}, "security_config": {"enable_vulnerability_detector": true, "enable_dependency_scanner": true, "enable_secrets_detector": true, "enable_compliance_checker": false, "enable_threat_modeler": false, "enable_risk_assessor": false, "disable_redox_mitigation": false}, "parser_config": {"supported_languages": ["rust", "python", "javascript", "go", "java", "yaml", "kotlin", "erlang", "d", "lua", "dart", "html", "css", "json"], "parser_limits": {"max_nodes": 500000, "max_depth": 500}, "force_reparse": false}, "test_cases": [{"name": "Rust Vulnerability Scan", "file_path": "/path/to/test_data/rust_vulnerable_code.rs", "language": "rust", "analysis_types": ["security"], "expected_findings_count": 3}, {"name": "JavaScript Dependency Scan", "file_path": "/path/to/test_data/package.json", "language": "json", "analysis_types": ["dependency_scanning"], "simulate_failure": false}, {"name": "Secret Detection Test", "file_content_snippet": "const AWS_SECRET_KEY = 'AKIAIOSFODNN7EXAMPLE';\nconst JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';", "language": "plaintext", "analysis_types": ["secrets_detection"]}], "api_debug": {"mock_external_services": false, "simulate_api_latency_ms": 0, "force_circuit_breaker_open": false}}