steps:
  # Step 1: Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-image'
    args:
      - 'build'
      - '-t'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_SERVICE_NAME}:${COMMIT_SHA}'
      - '-t'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_SERVICE_NAME}:latest'
      - '-f'
      - 'Dockerfile'
      - '--build-arg'
      - 'RUNTIME_TARGET=distroless'
      - '.'

  # Step 2: Push the container image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-image'
    args:
      - 'push'
      - '--all-tags'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_SERVICE_NAME}'
    waitFor: ['build-image']

  # Step 3: Deploy to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'deploy-service'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - '${_SERVICE_NAME}'
      - '--image'
      - '${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_SERVICE_NAME}:${COMMIT_SHA}'
      - '--region'
      - '${_REGION}'
      - '--platform'
      - 'managed'
      - '--port'
      - '8001'
      - '--memory'
      - '${_MEMORY}'
      - '--cpu'
      - '${_CPU}'
      - '--timeout'
      - '300s'
      - '--concurrency'
      - '${_CONCURRENCY}'
      - '--min-instances'
      - '${_MIN_INSTANCES}'
      - '--max-instances'
      - '${_MAX_INSTANCES}'
      - '--set-env-vars'
      - 'ENVIRONMENT=${_ENVIRONMENT},RUST_LOG=${_RUST_LOG},GCP_PROJECT_ID=${PROJECT_ID},GCP_REGION=${_REGION}'
      - '--service-account'
      - '${_SERVICE_ACCOUNT}'
      - '--allow-unauthenticated'
    waitFor: ['push-image']

# Substitutions with defaults
substitutions:
  _REGION: us-central1
  _REPOSITORY: ccl-services
  _SERVICE_NAME: analysis-engine
  _ENVIRONMENT: production
  _RUST_LOG: info
  _MEMORY: 2Gi
  _CPU: '2'
  _CONCURRENCY: '1000'
  _MIN_INSTANCES: '0'
  _MAX_INSTANCES: '100'
  _SERVICE_ACCOUNT: analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com

options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'
  substitutionOption: 'ALLOW_LOOSE'

timeout: 1200s