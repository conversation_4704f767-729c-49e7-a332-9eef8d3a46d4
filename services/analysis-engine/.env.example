# Analysis Engine Environment Configuration
# Copy this file to .env and update with your actual values

# Google Cloud Configuration
GOOGLE_APPLICATION_CREDENTIALS=../../vibe-match-463114-dbda8d8a6cb9.json
GCP_PROJECT_ID=vibe-match-463114
GCP_REGION=us-central1

# Service Configuration
ENVIRONMENT=development
PORT=8001
HOST=0.0.0.0

# GCP Service Configuration
SPANNER_INSTANCE=ccl-production
SPANNER_DATABASE=ccl-main
STORAGE_BUCKET=ccl-analysis-artifacts
STORAGE_BUCKET_NAME=ccl-analysis-vibe-match-463114
PUBSUB_TOPIC=analysis-events

# Development Emulators (optional)
# SPANNER_EMULATOR_HOST=http://localhost:9010
# STORAGE_EMULATOR_HOST=http://localhost:4443
# PUBSUB_EMULATOR_HOST=http://localhost:8085

# Authentication
ENABLE_AUTH=false
JWT_SECRET=development-secret-change-in-production

# Logging and Monitoring
RUST_LOG=analysis_engine=debug,tower_http=debug
RUST_BACKTRACE=1
ENABLE_TRACING=true
ENABLE_METRICS=true
LOG_LEVEL=info

# Analysis Configuration
MAX_CONCURRENT_ANALYSES=50
MAX_REPOSITORY_SIZE_GB=10
ANALYSIS_TIMEOUT_SECONDS=300
MAX_FILE_SIZE_MB=50
TEMP_DIR=/tmp/ccl-analysis

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Redis Configuration (optional)
# REDIS_URL=redis://localhost:6379