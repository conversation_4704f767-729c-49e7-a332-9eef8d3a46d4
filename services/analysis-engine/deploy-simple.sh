#!/bin/bash
# Simple deployment script for analysis-engine on Cloud Run
set -euo pipefail

echo "🚀 Deploying Analysis Engine to Cloud Run..."

# Configuration
PROJECT_ID="vibe-match-463114"
SERVICE_NAME="analysis-engine"
REGION="us-central1"
IMAGE_TAG="simple-$(date +%Y%m%d-%H%M%S)"

echo "📦 Building Docker image..."
docker build \
    --platform linux/amd64 \
    -t gcr.io/${PROJECT_ID}/${SERVICE_NAME}:${IMAGE_TAG} \
    -f Dockerfile.simple \
    .

echo "🔧 Configuring Docker for GCR..."
gcloud auth configure-docker --quiet

echo "📤 Pushing image to Container Registry..."
docker push gcr.io/${PROJECT_ID}/${SERVICE_NAME}:${IMAGE_TAG}

echo "🚀 Deploying to Cloud Run..."
gcloud run deploy ${SERVICE_NAME} \
    --image gcr.io/${PROJECT_ID}/${SERVICE_NAME}:${IMAGE_TAG} \
    --platform managed \
    --region ${REGION} \
    --memory 4Gi \
    --cpu 4 \
    --timeout 600 \
    --max-instances 100 \
    --concurrency 50 \
    --set-env-vars "RUST_LOG=info" \
    --set-env-vars "GCP_PROJECT_ID=${PROJECT_ID}" \
    --set-env-vars "SPANNER_INSTANCE=ccl-instance" \
    --set-env-vars "SPANNER_DATABASE=ccl_main" \
    --set-env-vars "STORAGE_BUCKET=ccl-analysis-artifacts" \
    --set-env-vars "REDIS_URL=redis://localhost:6379" \
    --service-account analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com \
    --allow-unauthenticated \
    --project ${PROJECT_ID}

echo "🔍 Getting service URL..."
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} \
    --platform managed \
    --region ${REGION} \
    --project ${PROJECT_ID} \
    --format 'value(status.url)')

echo "Service URL: $SERVICE_URL"

echo "🏥 Testing health endpoint..."
sleep 10  # Give Cloud Run time to start

if curl -f "${SERVICE_URL}/health"; then
    echo -e "\n✅ Deployment successful!"
    echo "🎉 Analysis Engine is running at: ${SERVICE_URL}"
else
    echo -e "\n❌ Health check failed. Checking logs..."
    gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=${SERVICE_NAME}" \
        --limit 20 \
        --project ${PROJECT_ID}
fi