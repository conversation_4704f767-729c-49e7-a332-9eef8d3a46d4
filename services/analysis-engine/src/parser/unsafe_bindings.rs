//! SAFETY MODULE: All unsafe tree-sitter FFI calls are isolated here
//!
//! This module is the ONLY place in the codebase allowed to contain unsafe
//! calls to tree-sitter language functions. All unsafe operations must be
//! thoroughly documented with safety invariants.
//!
//! ## Safety Invariants
//!
//! 1. **Function Validity**: All tree-sitter language functions are guaranteed
//!    to be valid by the build system that compiles and links them.
//! 2. **Memory Safety**: Tree-sitter language functions return valid Language
//!    structs or panic (following C FFI contract). No manual memory management required.
//! 3. **Thread Safety**: Language structs are immutable once created and safe
//!    to share across threads.
//! 4. **Initialization**: All functions are properly initialized during static
//!    library linking phase.

use once_cell::sync::Lazy;
use std::collections::HashMap;
use tree_sitter::Language;

/// Error type for language loading failures
#[derive(Debug, thiserror::Error)]
pub enum LanguageLoadError {
    #[error("Language '{0}' is not supported")]
    Unsupported(String),
    #[error("FFI call failed for language '{0}': {1}")]
    FfiFailure(String, String),
}

/// Static registry of all supported languages for fast lookup
static SUPPORTED_LANGUAGE_SET: Lazy<HashMap<&'static str, &'static str>> = Lazy::new(|| {
    let mut map = HashMap::new();
    map.insert("erlang", "tree_sitter_erlang");
    map.insert("scala", "tree_sitter_scala");
    map.insert("rust", "tree_sitter_rust");
    map.insert("ocaml", "tree_sitter_ocaml");
    map.insert("html", "tree_sitter_html");
    map.insert("go", "tree_sitter_go");
    map.insert("bash", "tree_sitter_bash");
    map.insert("c", "tree_sitter_c");
    map.insert("xml", "tree_sitter_xml");
    map.insert("php", "tree_sitter_php");
    map.insert("typescript", "tree_sitter_typescript");
    map.insert("elixir", "tree_sitter_elixir");
    map.insert("d", "tree_sitter_d");
    map.insert("swift", "tree_sitter_swift");
    map.insert("ruby", "tree_sitter_ruby");
    map.insert("nix", "tree_sitter_nix");
    map.insert("md", "tree_sitter_markdown");
    map.insert("java", "tree_sitter_java");
    map.insert("julia", "tree_sitter_julia");
    map.insert("css", "tree_sitter_css");
    map.insert("javascript", "tree_sitter_javascript");
    map.insert("json", "tree_sitter_json");
    map.insert("haskell", "tree_sitter_haskell");
    map.insert("kotlin", "tree_sitter_kotlin");
    map.insert("objc", "tree_sitter_objc");
    map.insert("r", "tree_sitter_r");
    map.insert("cpp", "tree_sitter_cpp");
    map.insert("lua", "tree_sitter_lua");
    map.insert("yaml", "tree_sitter_yaml");
    map.insert("zig", "tree_sitter_zig");
    map.insert("python", "tree_sitter_python");
    map
});

// External C function declarations for tree-sitter language parsers
//
// SAFETY: These functions are provided by the tree-sitter static library
// compiled during the build process. Each function returns a valid Language
// struct that represents the grammar for the respective programming language.
extern "C" {
    fn tree_sitter_erlang() -> Language;
    fn tree_sitter_scala() -> Language;
    fn tree_sitter_rust() -> Language;
    fn tree_sitter_ocaml() -> Language;
    fn tree_sitter_html() -> Language;
    fn tree_sitter_go() -> Language;
    fn tree_sitter_bash() -> Language;
    fn tree_sitter_c() -> Language;
    fn tree_sitter_xml() -> Language;
    fn tree_sitter_php() -> Language;
    fn tree_sitter_typescript() -> Language;
    fn tree_sitter_elixir() -> Language;
    fn tree_sitter_d() -> Language;
    fn tree_sitter_swift() -> Language;
    fn tree_sitter_ruby() -> Language;
    fn tree_sitter_nix() -> Language;
    fn tree_sitter_markdown() -> Language;
    fn tree_sitter_java() -> Language;
    fn tree_sitter_julia() -> Language;
    fn tree_sitter_css() -> Language;
    fn tree_sitter_javascript() -> Language;
    fn tree_sitter_json() -> Language;
    fn tree_sitter_haskell() -> Language;
    fn tree_sitter_kotlin() -> Language;
    fn tree_sitter_objc() -> Language;
    fn tree_sitter_r() -> Language;
    fn tree_sitter_cpp() -> Language;
    fn tree_sitter_lua() -> Language;
    fn tree_sitter_yaml() -> Language;
    fn tree_sitter_zig() -> Language;
    fn tree_sitter_python() -> Language;
}

/// SAFETY: This function contains unsafe FFI calls to tree-sitter language functions.
///
/// ## Safety Invariants:
/// - All tree-sitter language functions are guaranteed to be valid by the build system
/// - Language functions return valid Language structs or panic (C FFI contract)
/// - No memory management required - tree-sitter handles internal allocations
/// - Functions are statically linked and initialized during program startup
///
/// ## Error Handling:
/// - Returns `LanguageLoadError::Unsupported` for unknown language names
/// - Panics are allowed to propagate as they indicate serious system issues
///
/// ## Performance:
/// - Uses static HashMap for O(1) language name lookup
/// - FFI calls are minimal overhead once validated
pub fn load_language_unsafe(name: &str) -> Result<Language, LanguageLoadError> {
    // Fast path: check if language is supported before attempting FFI call
    if !SUPPORTED_LANGUAGE_SET.contains_key(name) {
        return Err(LanguageLoadError::Unsupported(name.to_string()));
    }

    // SAFETY: All match arms call functions that are:
    // 1. Declared in extern "C" block above
    // 2. Linked by build.rs during compilation
    // 3. Guaranteed to return valid Language structs
    // 4. Part of the tree-sitter C library contract
    let language = unsafe {
        match name {
            "erlang" => tree_sitter_erlang(),
            "scala" => tree_sitter_scala(),
            "rust" => tree_sitter_rust(),
            "ocaml" => tree_sitter_ocaml(),
            "html" => tree_sitter_html(),
            "go" => tree_sitter_go(),
            "bash" => tree_sitter_bash(),
            "c" => tree_sitter_c(),
            "xml" => tree_sitter_xml(),
            "php" => tree_sitter_php(),
            "typescript" => tree_sitter_typescript(),
            "elixir" => tree_sitter_elixir(),
            "d" => tree_sitter_d(),
            "swift" => tree_sitter_swift(),
            "ruby" => tree_sitter_ruby(),
            "nix" => tree_sitter_nix(),
            "md" => tree_sitter_markdown(),
            "java" => tree_sitter_java(),
            "julia" => tree_sitter_julia(),
            "css" => tree_sitter_css(),
            "javascript" => tree_sitter_javascript(),
            "json" => tree_sitter_json(),
            "haskell" => tree_sitter_haskell(),
            "kotlin" => tree_sitter_kotlin(),
            "objc" => tree_sitter_objc(),
            "r" => tree_sitter_r(),
            "cpp" => tree_sitter_cpp(),
            "lua" => tree_sitter_lua(),
            "yaml" => tree_sitter_yaml(),
            "zig" => tree_sitter_zig(),
            "python" => tree_sitter_python(),
            _ => {
                // This should never happen due to the check above, but provides defense in depth
                return Err(LanguageLoadError::Unsupported(name.to_string()));
            }
        }
    };

    Ok(language)
}

/// Check if a language is supported without loading it
pub fn is_language_supported(name: &str) -> bool {
    SUPPORTED_LANGUAGE_SET.contains_key(name)
}

/// Get all supported language names
pub fn supported_languages() -> Vec<&'static str> {
    SUPPORTED_LANGUAGE_SET.keys().copied().collect()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_supported_language_check() {
        assert!(is_language_supported("rust"));
        assert!(is_language_supported("python"));
        assert!(is_language_supported("javascript"));
        assert!(!is_language_supported("invalid_language"));
    }

    #[test]
    fn test_load_supported_language() {
        let result = load_language_unsafe("rust");
        assert!(result.is_ok());

        let result = load_language_unsafe("python");
        assert!(result.is_ok());
    }

    #[test]
    fn test_load_unsupported_language() {
        let result = load_language_unsafe("invalid_language");
        assert!(result.is_err());

        match result.unwrap_err() {
            LanguageLoadError::Unsupported(lang) => {
                assert_eq!(lang, "invalid_language");
            }
            _ => panic!("Expected Unsupported error"),
        }
    }

    #[test]
    fn test_supported_languages_list() {
        let languages = supported_languages();
        assert!(languages.contains(&"rust"));
        assert!(languages.contains(&"python"));
        assert!(languages.contains(&"javascript"));
        assert_eq!(languages.len(), 31); // Should match the number of supported languages
    }
}
