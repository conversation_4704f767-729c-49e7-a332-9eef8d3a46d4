use std::time::Duration;

/// Configuration for streaming file processing
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct StreamingConfig {
    /// Threshold in bytes above which to use streaming
    pub streaming_threshold: u64,
    /// Size of chunks when reading files
    pub chunk_size: usize,
    /// Maximum memory usage allowed during parsing
    pub max_memory_mb: usize,
    /// Whether to enable memory monitoring
    pub enable_memory_monitoring: bool,
    /// Interval for memory checks
    pub memory_check_interval: Duration,
}

impl Default for StreamingConfig {
    fn default() -> Self {
        Self {
            streaming_threshold: 10 * 1024 * 1024, // 10MB
            chunk_size: 64 * 1024,                 // 64KB chunks
            max_memory_mb: 500,                    // 500MB max memory
            enable_memory_monitoring: true,
            memory_check_interval: Duration::from_millis(100),
        }
    }
}

/// Configuration for streaming analysis
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct StreamingAnalysisConfig {
    /// Maximum file size to process (0 = unlimited)
    pub max_file_size: u64,
    /// Timeout for processing a single file
    pub file_timeout: Duration,
    /// Whether to collect detailed metrics
    pub collect_metrics: bool,
    /// Whether to extract symbols
    pub extract_symbols: bool,
    /// Whether to extract code chunks
    pub extract_chunks: bool,
    /// Maximum depth for AST traversal
    pub max_ast_depth: usize,
}

impl Default for StreamingAnalysisConfig {
    fn default() -> Self {
        Self {
            max_file_size: 100 * 1024 * 1024,     // 100MB
            file_timeout: Duration::from_secs(30), // 30 seconds
            collect_metrics: true,
            extract_symbols: true,
            extract_chunks: true,
            max_ast_depth: 100,
        }
    }
}

/// Configuration for language detection
#[derive(Debug, Clone)]
pub struct LanguageDetectionConfig {
    /// Whether to use file extension for detection
    pub use_extension: bool,
    /// Whether to use file content for detection
    pub use_content: bool,
    /// Maximum bytes to read for content detection
    pub content_sample_size: usize,
    /// Confidence threshold for content-based detection
    pub confidence_threshold: f32,
}

impl Default for LanguageDetectionConfig {
    fn default() -> Self {
        Self {
            use_extension: true,
            use_content: true,
            content_sample_size: 4096,
            confidence_threshold: 0.7,
        }
    }
}

/// Configuration for AST processing
#[derive(Debug, Clone)]
pub struct AstConfig {
    /// Whether to include comments in AST
    pub include_comments: bool,
    /// Whether to include whitespace nodes
    pub include_whitespace: bool,
    /// Whether to normalize identifiers
    pub normalize_identifiers: bool,
    /// Maximum node count before truncation
    pub max_nodes: usize,
}

impl Default for AstConfig {
    fn default() -> Self {
        Self {
            include_comments: true,
            include_whitespace: false,
            normalize_identifiers: false,
            max_nodes: 100_000,
        }
    }
}

/// Master configuration for the TreeSitterParser
#[derive(Debug, Clone, Default)]
pub struct ParserConfig {
    pub pool: super::parser_pool::ParserPoolConfig,
    pub streaming: StreamingConfig,
    pub analysis: StreamingAnalysisConfig,
    pub language_detection: LanguageDetectionConfig,
    pub ast: AstConfig,
}