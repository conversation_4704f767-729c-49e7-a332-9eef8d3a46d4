use tree_sitter::Node;
use crate::models::{AstNode, Position, Range};

/// Builds AST representations from tree-sitter nodes
pub struct AstBuilder {
    max_depth: usize,
    max_children_per_node: usize,
    sample_large_nodes: bool,
}

impl AstBuilder {
    /// Create a new AST builder with default settings
    pub fn new() -> Self {
        Self {
            max_depth: 100,
            max_children_per_node: 1000,
            sample_large_nodes: true,
        }
    }

    /// Create a new AST builder with custom settings
    pub fn with_config(max_depth: usize, max_children_per_node: usize, sample_large_nodes: bool) -> Self {
        Self {
            max_depth,
            max_children_per_node,
            sample_large_nodes,
        }
    }

    /// Build an AST from a tree-sitter node
    pub fn build(&self, node: &Node, source: &str) -> AstNode {
        self.build_recursive(node, source, 0)
    }

    /// Build AST recursively with depth tracking
    fn build_recursive(&self, node: &Node, source: &str, depth: usize) -> AstNode {
        // Handle depth limit
        if depth > self.max_depth {
            return self.create_truncated_node(node);
        }

        // Extract node information
        let node_type = node.kind().to_string();
        let text = self.extract_node_text(node, source);
        let name = self.extract_node_name(node, source);
        let range = self.create_range(node);

        // Build children
        let children = self.build_children(node, source, depth);

        AstNode {
            node_type,
            name,
            range,
            children,
            properties: None,
            text,
        }
    }

    /// Extract text content for a node
    fn extract_node_text(&self, node: &Node, source: &str) -> Option<String> {
        // Only extract text for leaf nodes or important nodes
        let should_extract = node.child_count() == 0 || matches!(
            node.kind(),
            "string_literal" | "number_literal" | "identifier" | "comment" |
            "string" | "number" | "boolean" | "null"
        );

        if should_extract {
            node.utf8_text(source.as_bytes()).ok().map(|s| s.to_string())
        } else {
            None
        }
    }

    /// Extract the name field of a node if present
    fn extract_node_name(&self, node: &Node, source: &str) -> Option<String> {
        node.child_by_field_name("name")
            .or_else(|| node.child_by_field_name("identifier"))
            .and_then(|n| n.utf8_text(source.as_bytes()).ok())
            .map(|s| s.to_string())
    }

    /// Create a range from a tree-sitter node
    fn create_range(&self, node: &Node) -> Range {
        Range {
            start: Position {
                line: node.start_position().row as u32,
                column: node.start_position().column as u32,
                byte: node.start_byte() as u32,
            },
            end: Position {
                line: node.end_position().row as u32,
                column: node.end_position().column as u32,
                byte: node.end_byte() as u32,
            },
        }
    }

    /// Build children nodes with optional sampling for large nodes
    fn build_children(&self, node: &Node, source: &str, depth: usize) -> Vec<AstNode> {
        let child_count = node.child_count();
        
        if child_count > self.max_children_per_node && self.sample_large_nodes {
            // Sample children for extremely large nodes
            let step = (child_count / 100).max(1);
            node.children(&mut node.walk())
                .step_by(step)
                .take(100)
                .map(|child| self.build_recursive(&child, source, depth + 1))
                .collect()
        } else {
            // Build all children normally
            node.children(&mut node.walk())
                .map(|child| self.build_recursive(&child, source, depth + 1))
                .collect()
        }
    }

    /// Create a truncated node for depth limit
    fn create_truncated_node(&self, node: &Node) -> AstNode {
        AstNode {
            node_type: "truncated".to_string(),
            name: Some("...".to_string()),
            range: self.create_range(node),
            children: vec![],
            properties: None,
            text: Some("/* Deep tree truncated for performance */".to_string()),
        }
    }
}

impl Default for AstBuilder {
    fn default() -> Self {
        Self::new()
    }
}