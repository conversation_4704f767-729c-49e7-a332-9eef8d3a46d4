use tree_sitter::Node;
use crate::models::{Symbol, SymbolType, SymbolVisibility, Position, Range};

/// Extracts symbols (functions, classes, variables, etc.) from AST nodes
pub struct SymbolExtractor {
    max_depth: usize,
    skip_anonymous: bool,
    skip_private: bool,
}

impl SymbolExtractor {
    /// Create a new symbol extractor with default settings
    pub fn new() -> Self {
        Self {
            max_depth: 50,
            skip_anonymous: true,
            skip_private: false,
        }
    }

    /// Create a new symbol extractor with custom settings
    pub fn with_config(max_depth: usize, skip_anonymous: bool, skip_private: bool) -> Self {
        Self {
            max_depth,
            skip_anonymous,
            skip_private,
        }
    }

    /// Extract all symbols from a tree-sitter node
    pub fn extract(&self, node: &Node, source: &str) -> Vec<Symbol> {
        let mut symbols = Vec::new();
        self.traverse_for_symbols(node, source, &mut symbols, 0);
        symbols
    }

    /// Traverse the AST recursively to find symbols
    fn traverse_for_symbols(&self, node: &Node, source: &str, symbols: &mut Vec<Symbol>, depth: usize) {
        // Stop at maximum depth
        if depth > self.max_depth {
            return;
        }

        // Skip nodes that can't contain symbols
        let node_kind = node.kind();
        if self.is_skip_node(node_kind) {
            return;
        }

        // Check if this node represents a symbol
        if let Some(symbol_type) = self.get_symbol_type(node_kind) {
            if let Some(symbol) = self.extract_symbol(node, source, symbol_type) {
                if self.should_include_symbol(&symbol) {
                    symbols.push(symbol);
                }
            }
        }

        // Traverse children
        let mut cursor = node.walk();
        for child in node.children(&mut cursor) {
            self.traverse_for_symbols(&child, source, symbols, depth + 1);
        }
    }

    /// Determine if a node type represents a symbol
    fn get_symbol_type(&self, node_kind: &str) -> Option<SymbolType> {
        match node_kind {
            // Functions
            "function_item" | "function_declaration" | "method_definition" | 
            "function_definition" | "function" | "arrow_function" |
            "method_declaration" | "constructor_declaration" => Some(SymbolType::Function),
            
            // Classes/Structs
            "struct_item" | "class_declaration" | "class_definition" |
            "class" | "struct_declaration" | "enum_declaration" |
            "enum_item" => Some(SymbolType::Class),
            
            // Variables/Constants
            "let_declaration" | "const_item" | "variable_declarator" |
            "const_declaration" | "variable_declaration" | "static_item" |
            "field_declaration" | "property_declaration" => Some(SymbolType::Variable),
            
            // Interfaces/Traits
            "trait_item" | "interface_declaration" | "trait_declaration" |
            "protocol_declaration" => Some(SymbolType::Interface),
            
            // Modules/Namespaces
            "mod_item" | "module_declaration" | "namespace_declaration" |
            "package_declaration" => Some(SymbolType::Module),
            
            // Type aliases
            "type_alias" | "type_item" | "typedef" |
            "type_declaration" => Some(SymbolType::TypeAlias),
            
            _ => None,
        }
    }

    /// Extract symbol information from a node
    fn extract_symbol(&self, node: &Node, source: &str, symbol_type: SymbolType) -> Option<Symbol> {
        // Try to get the symbol name
        let name = self.extract_symbol_name(node, source)?;

        // Skip if anonymous and configured to do so
        if self.skip_anonymous && (name.is_empty() || name.starts_with('_')) {
            return None;
        }

        let range = Range {
            start: self.convert_position(node.start_position()),
            end: self.convert_position(node.end_position()),
        };

        let visibility = self.extract_visibility(node, source);
        let signature = self.extract_signature(node, source, symbol_type.clone());
        let documentation = self.extract_documentation(node, source);

        Some(Symbol {
            name,
            symbol_type,
            range,
            visibility,
            signature,
            documentation,
        })
    }

    /// Extract the name of a symbol
    fn extract_symbol_name(&self, node: &Node, source: &str) -> Option<String> {
        // Try common name fields
        let name_fields = ["name", "identifier", "declarator", "left"];
        
        for field in &name_fields {
            if let Some(name_node) = node.child_by_field_name(field) {
                if let Ok(name) = name_node.utf8_text(source.as_bytes()) {
                    return Some(name.to_string());
                }
            }
        }

        // For some languages, the name might be in a specific child
        if node.child_count() > 0 {
            let mut cursor = node.walk();
            for child in node.children(&mut cursor) {
                if matches!(child.kind(), "identifier" | "type_identifier" | "field_identifier") {
                    if let Ok(name) = child.utf8_text(source.as_bytes()) {
                        return Some(name.to_string());
                    }
                }
            }
        }

        None
    }

    /// Extract visibility modifiers
    fn extract_visibility(&self, node: &Node, source: &str) -> Option<SymbolVisibility> {
        // Look for visibility modifiers in children
        let mut cursor = node.walk();
        for child in node.children(&mut cursor) {
            match child.kind() {
                "visibility_modifier" | "pub" | "public" | "private" | 
                "protected" | "internal" | "export" => {
                    if let Ok(vis) = child.utf8_text(source.as_bytes()) {
                        return Some(self.parse_visibility(vis));
                    }
                }
                _ => {}
            }
        }

        // Check parent for visibility
        if let Some(parent) = node.parent() {
            if parent.kind() == "pub" {
                return Some(SymbolVisibility::Public);
            }
        }

        None
    }

    /// Parse visibility string to enum
    fn parse_visibility(&self, vis: &str) -> SymbolVisibility {
        match vis {
            "public" | "pub" | "export" => SymbolVisibility::Public,
            "private" => SymbolVisibility::Private,
            "protected" => SymbolVisibility::Protected,
            "internal" => SymbolVisibility::Internal,
            _ => SymbolVisibility::Public, // Default to public
        }
    }

    /// Extract function/method signature
    fn extract_signature(&self, node: &Node, source: &str, symbol_type: SymbolType) -> Option<String> {
        if !matches!(symbol_type, SymbolType::Function) {
            return None;
        }

        // Try to extract the full signature
        if let Ok(text) = node.utf8_text(source.as_bytes()) {
            // Extract just the signature part (first line usually)
            if let Some(first_line) = text.lines().next() {
                return Some(first_line.to_string());
            }
        }

        None
    }

    /// Extract documentation comments
    fn extract_documentation(&self, _node: &Node, _source: &str) -> Option<String> {
        // TODO: Implement documentation extraction
        // This would look for doc comments preceding the symbol
        None
    }

    /// Check if a symbol should be included based on filters
    fn should_include_symbol(&self, symbol: &Symbol) -> bool {
        if self.skip_private {
            if let Some(vis) = &symbol.visibility {
                if vis == "private" || vis.is_empty() {
                    return false;
                }
            }
        }

        true
    }

    /// Check if a node type should be skipped
    fn is_skip_node(&self, node_kind: &str) -> bool {
        matches!(
            node_kind,
            "comment" | "string" | "string_literal" | "number" | 
            "number_literal" | "boolean" | "null" | "template_string"
        )
    }

    /// Convert tree-sitter position to our Position type
    fn convert_position(&self, point: tree_sitter::Point) -> Position {
        Position {
            line: point.row as u32,
            column: point.column as u32,
            byte: 0, // Will be set during parsing
        }
    }
}

impl Default for SymbolExtractor {
    fn default() -> Self {
        Self::new()
    }
}