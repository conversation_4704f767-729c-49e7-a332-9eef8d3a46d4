// services/analysis-engine/src/parser/language_registry.rs

use crate::parser::unsafe_bindings::{load_language_unsafe, LanguageLoadError};
use once_cell::sync::Lazy;
use std::collections::HashMap;

// Include the bindings generated by the build.rs script.
// This file contains the safe wrapper calls to unsafe_bindings module.
include!(concat!(env!("OUT_DIR"), "/language_bindings.rs"));

/// Type-safe wrapper for tree-sitter languages
#[derive(Clone)]
pub struct TreeSitterLanguage(pub Language);

impl TreeSitterLanguage {
    /// Create a new language wrapper
    pub fn new(lang: Language) -> Self {
        TreeSitterLanguage(lang)
    }

    /// Get the inner language
    pub fn inner(&self) -> &Language {
        &self.0
    }
}

// The LANGUAGE_REGISTRY is kept for potential future use cases where a language
// might need to be registered manually, but it is no longer the primary mechanism.
pub static LANGUAGE_REGISTRY: Lazy<HashMap<&'static str, TreeSitterLanguage>> =
    Lazy::new(|| HashMap::new());

// The primary `get_language` function is now the one generated by our build script.
// We re-export it here for a consistent module interface.
// pub use self::get_language;

/// Get a language with proper error handling
///
/// This function provides a safe interface to language loading with detailed error information.
/// It delegates to the centralized unsafe_bindings module for actual FFI calls.
pub fn get_language_safe(name: &str) -> Result<TreeSitterLanguage, LanguageLoadError> {
    let language = load_language_unsafe(name)?;
    Ok(TreeSitterLanguage::new(language))
}

/// Check if a language is supported by checking if our generated function can find it.
pub fn is_language_supported(name: &str) -> bool {
    get_language(name).is_some()
}

/// Get all supported language names.
pub fn supported_languages() -> &'static [&'static str] {
    SUPPORTED_LANGUAGES
}

/// Languages that require custom parsers (not tree-sitter)
pub fn requires_custom_parser(name: &str) -> bool {
    matches!(name, "sql" | "toml" | "xml" | "markdown")
}

/// Languages that are known but not yet supported
pub fn is_known_unsupported(_name: &str) -> bool {
    false
}
