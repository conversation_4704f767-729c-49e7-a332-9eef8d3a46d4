use anyhow::{Context, Result};
use tokio::io::{AsyncBufReadExt, BufReader};
use tokio::fs::File;
use std::path::Path;
use std::sync::Arc;

use super::{MemoryMoni<PERSON>, ProgressReporter, DefaultProgressReporter, StreamingHasher};
use crate::parser::config::StreamingConfig;

/// Represents a chunk of file content
#[derive(Debug)]
pub struct ContentChunk {
    /// The content of this chunk
    pub content: String,
    /// Byte offset in the file where this chunk starts
    pub offset: u64,
    /// Whether this is the last chunk
    pub is_last: bool,
}

/// Handles streaming processing of large files
pub struct StreamingFileProcessor {
    config: StreamingConfig,
    memory_monitor: Option<MemoryMonitor>,
    progress_reporter: Arc<dyn ProgressReporter>,
}

impl StreamingFileProcessor {
    /// Create a new streaming file processor
    pub fn new(config: StreamingConfig) -> Self {
        let memory_monitor = if config.enable_memory_monitoring {
            Some(MemoryMonitor::new(
                config.max_memory_mb,
                config.memory_check_interval,
            ))
        } else {
            None
        };

        Self {
            config,
            memory_monitor,
            progress_reporter: Arc::new(DefaultProgressReporter::new()),
        }
    }

    /// Create with a custom progress reporter
    pub fn with_progress_reporter(
        config: StreamingConfig,
        reporter: Arc<dyn ProgressReporter>,
    ) -> Self {
        let memory_monitor = if config.enable_memory_monitoring {
            Some(MemoryMonitor::new(
                config.max_memory_mb,
                config.memory_check_interval,
            ))
        } else {
            None
        };

        Self {
            config,
            memory_monitor,
            progress_reporter: reporter,
        }
    }

    /// Process a file in streaming mode, returning chunks
    pub async fn process_file_chunked<F, T>(
        &self,
        file_path: &Path,
        file_size: u64,
        mut chunk_handler: F,
    ) -> Result<T>
    where
        F: FnMut(ContentChunk) -> Result<Option<T>>,
    {
        let start_time = std::time::Instant::now();
        
        // Start memory monitoring if enabled
        let _monitor_handle = if let Some(monitor) = &self.memory_monitor {
            Some(monitor.start_monitoring())
        } else {
            None
        };

        let file = File::open(file_path)
            .await
            .context("Failed to open file for streaming")?;
        
        let mut reader = BufReader::with_capacity(self.config.chunk_size, file);
        let mut buffer = Vec::with_capacity(self.config.chunk_size);
        let mut offset = 0u64;
        let mut bytes_processed = 0u64;

        loop {
            // Check memory usage
            if let Some(monitor) = &self.memory_monitor {
                if monitor.is_memory_exceeded() {
                    return Err(anyhow::anyhow!("Memory limit exceeded during parsing"));
                }
            }

            buffer.clear();
            let bytes_read = reader
                .read_until(b'\n', &mut buffer)
                .await
                .context("Failed to read chunk from file")?;

            if bytes_read == 0 {
                // End of file - process any remaining content
                let chunk = ContentChunk {
                    content: String::new(),
                    offset,
                    is_last: true,
                };

                if let Some(result) = chunk_handler(chunk)? {
                    let duration_ms = start_time.elapsed().as_millis() as u64;
                    self.progress_reporter
                        .report_completion(file_size, duration_ms)
                        .await;
                    return Ok(result);
                }
                break;
            }

            bytes_processed += bytes_read as u64;

            // Convert to string, handling UTF-8 errors gracefully
            let content = String::from_utf8_lossy(&buffer).into_owned();
            
            let chunk = ContentChunk {
                content,
                offset,
                is_last: false,
            };

            // Process the chunk
            if let Some(result) = chunk_handler(chunk)? {
                let duration_ms = start_time.elapsed().as_millis() as u64;
                self.progress_reporter
                    .report_completion(bytes_processed, duration_ms)
                    .await;
                return Ok(result);
            }

            offset += bytes_read as u64;

            // Report progress
            self.progress_reporter
                .report_progress(bytes_processed, file_size)
                .await;
        }

        Err(anyhow::anyhow!("File processing completed without result"))
    }

    /// Read entire file content with streaming hash calculation
    pub async fn read_with_hash(
        &self,
        file_path: &Path,
        file_size: u64,
    ) -> Result<(String, String)> {
        let mut hasher = StreamingHasher::new();
        let mut content = String::new();

        self.process_file_chunked(file_path, file_size, |chunk| {
            hasher.update(chunk.content.as_bytes());
            content.push_str(&chunk.content);
            
            if chunk.is_last {
                Ok(Some(()))
            } else {
                Ok(None)
            }
        })
        .await?;

        let hash = hasher.finalize();
        Ok((content, hash))
    }

    /// Check if memory monitoring is enabled
    pub fn is_memory_monitoring_enabled(&self) -> bool {
        self.memory_monitor.is_some()
    }

    /// Get current memory usage in MB (if monitoring is enabled)
    pub fn get_memory_usage_mb(&self) -> Option<usize> {
        self.memory_monitor.as_ref().map(|m| m.get_usage_mb())
    }
}