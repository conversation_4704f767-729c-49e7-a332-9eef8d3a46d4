use async_trait::async_trait;
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};

/// Trait for reporting parsing progress
#[async_trait]
pub trait ProgressReporter: Send + Sync {
    /// Report progress update
    async fn report_progress(&self, bytes_processed: u64, total_bytes: u64);
    
    /// Report completion
    async fn report_completion(&self, total_bytes: u64, duration_ms: u64);
    
    /// Report error
    async fn report_error(&self, error: &str);
}

/// Default progress reporter implementation
pub struct DefaultProgressReporter {
    last_reported_percentage: Arc<AtomicU64>,
}

impl DefaultProgressReporter {
    pub fn new() -> Self {
        Self {
            last_reported_percentage: Arc::new(AtomicU64::new(0)),
        }
    }
}

#[async_trait]
impl ProgressReporter for DefaultProgressReporter {
    async fn report_progress(&self, bytes_processed: u64, total_bytes: u64) {
        if total_bytes == 0 {
            return;
        }

        let percentage = (bytes_processed * 100) / total_bytes;
        let last_percentage = self.last_reported_percentage.load(Ordering::Relaxed);

        // Only report if percentage changed by at least 10%
        if percentage >= last_percentage + 10 || percentage == 100 {
            self.last_reported_percentage.store(percentage, Ordering::Relaxed);
            tracing::info!(
                "Parsing progress: {}% ({}/{} bytes)",
                percentage,
                bytes_processed,
                total_bytes
            );
        }
    }

    async fn report_completion(&self, total_bytes: u64, duration_ms: u64) {
        let throughput_mb_s = if duration_ms > 0 {
            (total_bytes as f64 / 1024.0 / 1024.0) / (duration_ms as f64 / 1000.0)
        } else {
            0.0
        };

        tracing::info!(
            "Parsing completed: {} bytes in {} ms ({:.2} MB/s)",
            total_bytes,
            duration_ms,
            throughput_mb_s
        );
    }

    async fn report_error(&self, error: &str) {
        tracing::error!("Parsing error: {}", error);
    }
}

impl Default for DefaultProgressReporter {
    fn default() -> Self {
        Self::new()
    }
}