use std::sync::Arc;
use std::sync::atomic::{AtomicBool, AtomicUsize, Ordering};
use std::time::Duration;
use tracing::{debug, warn};

/// Monitor memory usage during parsing operations
pub struct MemoryMonitor {
    max_memory_mb: usize,
    check_interval: Duration,
    should_stop: Arc<AtomicBool>,
    current_usage_mb: Arc<AtomicUsize>,
}

impl std::fmt::Debug for MemoryMonitor {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("MemoryMonitor")
            .field("max_memory_mb", &self.max_memory_mb)
            .field("check_interval", &self.check_interval)
            .finish()
    }
}

impl MemoryMonitor {
    /// Create a new memory monitor
    pub fn new(max_memory_mb: usize, check_interval: Duration) -> Self {
        Self {
            max_memory_mb,
            check_interval,
            should_stop: Arc::new(AtomicBool::new(false)),
            current_usage_mb: Arc::new(AtomicUsize::new(0)),
        }
    }

    /// Start monitoring memory usage in a background task
    pub fn start_monitoring(&self) -> tokio::task::JoinHandle<()> {
        let max_memory_mb = self.max_memory_mb;
        let check_interval = self.check_interval;
        let should_stop = Arc::clone(&self.should_stop);
        let current_usage_mb = Arc::clone(&self.current_usage_mb);

        tokio::spawn(async move {
            while !should_stop.load(Ordering::Relaxed) {
                let usage_mb = Self::get_current_memory_usage_mb();
                current_usage_mb.store(usage_mb, Ordering::Relaxed);

                if usage_mb > max_memory_mb {
                    warn!(
                        "Memory usage ({} MB) exceeds limit ({} MB)",
                        usage_mb, max_memory_mb
                    );
                    should_stop.store(true, Ordering::Relaxed);
                    break;
                }

                debug!("Current memory usage: {} MB", usage_mb);
                tokio::time::sleep(check_interval).await;
            }
        })
    }

    /// Stop memory monitoring
    pub fn stop(&self) {
        self.should_stop.store(true, Ordering::Relaxed);
    }

    /// Check if memory limit has been exceeded
    pub fn is_memory_exceeded(&self) -> bool {
        self.should_stop.load(Ordering::Relaxed)
    }

    /// Get current memory usage in MB
    pub fn get_usage_mb(&self) -> usize {
        self.current_usage_mb.load(Ordering::Relaxed)
    }

    /// Get current process memory usage in MB
    fn get_current_memory_usage_mb() -> usize {
        // Simple implementation - in production, use proper system APIs
        #[cfg(target_os = "linux")]
        {
            if let Ok(status) = std::fs::read_to_string("/proc/self/status") {
                for line in status.lines() {
                    if line.starts_with("VmRSS:") {
                        if let Some(kb_str) = line.split_whitespace().nth(1) {
                            if let Ok(kb) = kb_str.parse::<usize>() {
                                return kb / 1024;
                            }
                        }
                    }
                }
            }
        }

        // Fallback: estimate based on allocated memory (very rough)
        // In production, use proper memory profiling
        0
    }
}