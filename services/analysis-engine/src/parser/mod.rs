use crate::config::ServiceConfig;
use anyhow::Result;
use std::collections::HashMap;
use std::path::Path;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::debug;

// Re-export submodules
pub mod adapters;
pub mod language_metrics;
pub mod language_validation_test;
pub mod validation_demo;

// New modular structure
pub mod ast;
pub mod config;
pub mod language_detection;
pub mod language_registry;
pub mod parser_pool;
pub mod strategies;
pub mod streaming;
pub mod unsafe_bindings;

// Re-exports for convenience
pub use ast::{AstBuilder, ChunkExtractor, SymbolExtractor};
pub use config::{ParserConfig, StreamingAnalysisConfig, StreamingConfig};
pub use language_detection::{is_supported_language, LanguageDetector};
pub use language_registry::{
    get_language, get_language_safe, is_language_supported, supported_languages,
};
pub use parser_pool::{ParserPool, ParserPoolConfig, ParserPoolStats};
pub use streaming::{MemoryMonitor, ProgressReporter, StreamingFileProcessor};
pub use unsafe_bindings::LanguageLoadError;

use crate::models::{AstNode, FileAnalysis, ParseError, ParseErrorType, Position, Range};
use crate::parser::adapters::{MarkdownAdapter, SqlAdapter, TomlAdapter, XmlAdapter};
use crate::parser::language_metrics::LanguageMetricsCalculator;

/// Main tree-sitter based parser with pooling support
pub struct TreeSitterParser {
    /// Parser pools by language
    parser_pools: Arc<RwLock<HashMap<String, Arc<ParserPool>>>>,
    /// Configuration
    config: Arc<ServiceConfig>,
    /// Language detector
    language_detector: LanguageDetector,
    /// Metrics calculator
    metrics_calculator: LanguageMetricsCalculator,
    /// AST builder
    ast_builder: AstBuilder,
    /// Symbol extractor
    symbol_extractor: SymbolExtractor,
    /// Chunk extractor
    chunk_extractor: ChunkExtractor,
}

impl TreeSitterParser {
    /// Create a new parser with default configuration
    pub fn new(config: Arc<ServiceConfig>) -> Result<Self> {
        Self::new_with_config(config, ParserConfig::default())
    }

    /// Create a new parser with custom configuration
    pub fn new_with_config(
        service_config: Arc<ServiceConfig>,
        parser_config: ParserConfig,
    ) -> Result<Self> {
        Ok(Self {
            parser_pools: Arc::new(RwLock::new(HashMap::new())),
            language_detector: LanguageDetector::new(parser_config.language_detection.clone()),
            metrics_calculator: LanguageMetricsCalculator::new(),
            ast_builder: AstBuilder::with_config(parser_config.ast.max_nodes, 1000, true),
            symbol_extractor: SymbolExtractor::new(),
            chunk_extractor: ChunkExtractor::new(),
            config: service_config,
        })
    }

    /// Parse a file with automatic language detection
    pub async fn parse_file(&self, file_path: &Path) -> Result<FileAnalysis, ParseError> {
        self.parse_file_with_config(file_path, &config::StreamingConfig::default())
            .await
    }

    /// Parse a file with custom streaming configuration
    pub async fn parse_file_with_config(
        &self,
        file_path: &Path,
        streaming_config: &StreamingConfig,
    ) -> Result<FileAnalysis, ParseError> {
        // Get file metadata
        let metadata = tokio::fs::metadata(file_path)
            .await
            .map_err(|e| ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::Other,
                message: format!("Failed to read file metadata: {}", e),
                position: None,
            })?;

        let file_size = metadata.len();

        // Check file size limit
        if file_size > self.config.resource_limits.max_file_size_bytes {
            return Err(ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::Other,
                message: format!(
                    "File size ({} bytes) exceeds maximum allowed size ({} bytes)",
                    file_size, self.config.resource_limits.max_file_size_bytes
                ),
                position: None,
            });
        }

        // Decide parsing strategy based on file size
        if file_size > streaming_config.streaming_threshold {
            self.parse_file_streaming(file_path, file_size, streaming_config)
                .await
        } else {
            self.parse_file_regular(file_path).await
        }
    }

    /// Parse file content directly
    pub async fn parse_content(
        &self,
        file_path: &Path,
        content: &str,
    ) -> Result<FileAnalysis, ParseError> {
        let timeout_duration =
            std::time::Duration::from_secs(self.config.resource_limits.parse_timeout_seconds);

        tokio::time::timeout(timeout_duration, async {
            let file_name = file_path
                .file_name()
                .and_then(|n| n.to_str())
                .ok_or_else(|| ParseError {
                    file_path: file_path.to_string_lossy().to_string(),
                    error_type: ParseErrorType::Other,
                    message: "Invalid file path".to_string(),
                    position: None,
                })?;

            // Detect language
            let language = self
                .language_detector
                .detect(file_path, Some(content))
                .map_err(|e| ParseError {
                    file_path: file_path.to_string_lossy().to_string(),
                    error_type: ParseErrorType::UnsupportedLanguage,
                    message: format!("Failed to detect language: {}", e),
                    position: None,
                })?
                .ok_or_else(|| ParseError {
                    file_path: file_path.to_string_lossy().to_string(),
                    error_type: ParseErrorType::UnsupportedLanguage,
                    message: format!("Could not detect language for file: {}", file_name),
                    position: None,
                })?;

            // Handle custom parsers
            if language_registry::requires_custom_parser(&language) {
                return self
                    .parse_with_custom_parser(file_path, content, &language)
                    .await;
            }

            // Get parser pool for the language
            let pool = self.get_or_create_pool(&language).await?;

            // Get a parser from the pool
            let mut parser = pool.get_parser().await.map_err(|e| ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::Other,
                message: format!("Failed to get parser from pool: {}", e),
                position: None,
            })?;

            // Parse the content
            let tree = parser.parse(content, None).ok_or_else(|| ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::SyntaxError,
                message: "Failed to parse content".to_string(),
                position: None,
            })?;

            let root_node = tree.root_node();

            // Build analysis
            let ast = self.ast_builder.build(&root_node, content);
            let symbols = self.symbol_extractor.extract(&root_node, content);
            let chunks = self.chunk_extractor.extract(&ast, content, &language);
            let metrics = self
                .metrics_calculator
                .calculate_metrics(&language, content);
            let content_hash = streaming::hasher::StreamingHasher::hash_content(content);

            // Return parser to pool
            pool.return_parser(parser);

            Ok(FileAnalysis {
                path: file_path.to_string_lossy().to_string(),
                language: language.clone(),
                content_hash,
                size_bytes: Some(content.len() as u64),
                ast,
                metrics,
                chunks: Some(chunks),
                symbols: Some(symbols),
            })
        })
        .await
        .map_err(|_| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::Timeout,
            message: "Parsing timed out".to_string(),
            position: None,
        })?
    }

    /// Parse a regular-sized file
    async fn parse_file_regular(&self, file_path: &Path) -> Result<FileAnalysis, ParseError> {
        let content = tokio::fs::read_to_string(file_path)
            .await
            .map_err(|e| ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::Other,
                message: format!("Failed to read file: {}", e),
                position: None,
            })?;

        self.parse_content(file_path, &content).await
    }

    /// Parse a large file using streaming
    async fn parse_file_streaming(
        &self,
        file_path: &Path,
        _file_size: u64,
        _config: &StreamingConfig,
    ) -> Result<FileAnalysis, ParseError> {
        // For now, fall back to regular parsing
        // TODO: Implement proper streaming parsing
        debug!(
            "File {} exceeds streaming threshold, using streaming mode",
            file_path.display()
        );
        self.parse_file_regular(file_path).await
    }

    /// Parse with custom parser (SQL, XML, TOML)
    async fn parse_with_custom_parser(
        &self,
        file_path: &Path,
        content: &str,
        language: &str,
    ) -> Result<FileAnalysis, ParseError> {
        match language {
            "sql" => self.parse_sql_content(file_path, content).await,
            "xml" => self.parse_xml_content(file_path, content).await,
            "toml" => self.parse_toml_content(file_path, content).await,
            "markdown" => self.parse_markdown_content(file_path, content).await,
            _ => Err(ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::UnsupportedLanguage,
                message: format!("No custom parser for language: {}", language),
                position: None,
            }),
        }
    }

    /// Parse SQL content using sqlparser
    async fn parse_sql_content(
        &self,
        file_path: &Path,
        content: &str,
    ) -> Result<FileAnalysis, ParseError> {
        let symbols = SqlAdapter::parse_sql(content)?;
        let metrics = self.metrics_calculator.calculate_metrics("sql", content);
        let content_hash = streaming::hasher::StreamingHasher::hash_content(content);

        Ok(FileAnalysis {
            path: file_path.to_string_lossy().to_string(),
            language: "sql".to_string(),
            content_hash,
            size_bytes: Some(content.len() as u64),
            ast: AstNode {
                node_type: "root".to_string(),
                name: None,
                range: Range {
                    start: Position {
                        line: 0,
                        column: 0,
                        byte: 0,
                    },
                    end: Position {
                        line: 0,
                        column: 0,
                        byte: 0,
                    },
                },
                children: vec![],
                properties: None,
                text: None,
            },
            metrics,
            chunks: None,
            symbols: Some(symbols),
        })
    }

    /// Parse XML content using quick-xml
    async fn parse_xml_content(
        &self,
        file_path: &Path,
        content: &str,
    ) -> Result<FileAnalysis, ParseError> {
        let symbols = XmlAdapter::parse_xml(content)?;
        let metrics = self.metrics_calculator.calculate_metrics("xml", content);
        let content_hash = streaming::hasher::StreamingHasher::hash_content(content);

        Ok(FileAnalysis {
            path: file_path.to_string_lossy().to_string(),
            language: "xml".to_string(),
            content_hash,
            size_bytes: Some(content.len() as u64),
            ast: AstNode {
                node_type: "root".to_string(),
                name: None,
                range: Range {
                    start: Position {
                        line: 0,
                        column: 0,
                        byte: 0,
                    },
                    end: Position {
                        line: 0,
                        column: 0,
                        byte: 0,
                    },
                },
                children: vec![],
                properties: None,
                text: None,
            },
            metrics,
            chunks: None,
            symbols: Some(symbols),
        })
    }

    /// Parse TOML content
    async fn parse_toml_content(
        &self,
        file_path: &Path,
        content: &str,
    ) -> Result<FileAnalysis, ParseError> {
        let symbols = TomlAdapter::parse_toml(content)?;
        let metrics = self.metrics_calculator.calculate_metrics("toml", content);
        let content_hash = streaming::hasher::StreamingHasher::hash_content(content);

        Ok(FileAnalysis {
            path: file_path.to_string_lossy().to_string(),
            language: "toml".to_string(),
            content_hash,
            size_bytes: Some(content.len() as u64),
            ast: AstNode {
                node_type: "root".to_string(),
                name: None,
                range: Range {
                    start: Position {
                        line: 0,
                        column: 0,
                        byte: 0,
                    },
                    end: Position {
                        line: 0,
                        column: 0,
                        byte: 0,
                    },
                },
                children: vec![],
                properties: None,
                text: None,
            },
            metrics,
            chunks: None,
            symbols: Some(symbols),
        })
    }

    /// Parse Markdown content
    async fn parse_markdown_content(
        &self,
        file_path: &Path,
        content: &str,
    ) -> Result<FileAnalysis, ParseError> {
        let symbols = MarkdownAdapter::parse_markdown(content)?;
        let metrics = self
            .metrics_calculator
            .calculate_metrics("markdown", content);
        let content_hash = streaming::hasher::StreamingHasher::hash_content(content);

        Ok(FileAnalysis {
            path: file_path.to_string_lossy().to_string(),
            language: "markdown".to_string(),
            content_hash,
            size_bytes: Some(content.len() as u64),
            ast: AstNode {
                node_type: "root".to_string(),
                name: None,
                range: Range {
                    start: Position {
                        line: 0,
                        column: 0,
                        byte: 0,
                    },
                    end: Position {
                        line: 0,
                        column: 0,
                        byte: 0,
                    },
                },
                children: vec![],
                properties: None,
                text: None,
            },
            metrics,
            chunks: None,
            symbols: Some(symbols),
        })
    }

    /// Get or create a parser pool for a language
    async fn get_or_create_pool(&self, language_name: &str) -> Result<Arc<ParserPool>, ParseError> {
        let pools = self.parser_pools.read().await;

        if let Some(pool) = pools.get(language_name) {
            return Ok(Arc::clone(pool));
        }

        drop(pools); // Release read lock

        // Need to create a new pool
        let mut pools = self.parser_pools.write().await;

        // Double-check in case another thread created it
        if let Some(pool) = pools.get(language_name) {
            return Ok(Arc::clone(pool));
        }

        // Get the language
        let language =
            language_registry::get_language(language_name).ok_or_else(|| ParseError {
                file_path: String::new(),
                error_type: ParseErrorType::UnsupportedLanguage,
                message: format!("Language not supported: {}", language_name),
                position: None,
            })?;

        // Create new pool
        let pool = Arc::new(
            ParserPool::new_with_config(language, &ParserConfig::default().pool).map_err(|e| {
                ParseError {
                    file_path: String::new(),
                    error_type: ParseErrorType::Other,
                    message: format!("Failed to create parser pool: {}", e),
                    position: None,
                }
            })?,
        );

        pools.insert(language_name.to_string(), Arc::clone(&pool));
        Ok(pool)
    }

    /// Get pool statistics for all languages
    pub async fn get_pool_statistics(&self) -> HashMap<String, ParserPoolStats> {
        let pools = self.parser_pools.read().await;
        pools
            .iter()
            .map(|(lang, pool)| (lang.clone(), pool.get_stats()))
            .collect()
    }

    /// Preload parsers for expected languages
    pub async fn preload_languages(&self, languages: &[&str]) -> Result<()> {
        for language in languages {
            if is_language_supported(language) {
                let pool = self.get_or_create_pool(language).await?;
                pool.preload_parsers(2).map_err(|e| ParseError {
                    file_path: String::new(),
                    error_type: ParseErrorType::Other,
                    message: format!("Failed to preload parsers: {}", e),
                    position: None,
                })?;
            }
        }
        Ok(())
    }

    /// Detect the language of a file
    pub fn detect_language(&self, file_path: &Path) -> Result<String, ParseError> {
        self.language_detector
            .detect_from_extension(file_path)
            .ok_or_else(|| ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::UnsupportedLanguage,
                message: "Could not detect language from file extension".to_string(),
                position: None,
            })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_parser_creation() {
        let config = Arc::new(ServiceConfig::from_env().unwrap());
        let parser = TreeSitterParser::new(config).unwrap();
        assert!(parser.parser_pools.read().await.is_empty());
    }

    #[tokio::test]
    async fn test_language_detection() {
        let detector = LanguageDetector::default();

        let rust_file = Path::new("test.rs");
        assert_eq!(
            detector.detect_from_extension(rust_file),
            Some("rust".to_string())
        );

        let python_file = Path::new("test.py");
        assert_eq!(
            detector.detect_from_extension(python_file),
            Some("python".to_string())
        );
    }
}
