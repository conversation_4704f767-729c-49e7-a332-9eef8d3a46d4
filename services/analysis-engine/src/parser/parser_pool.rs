use anyhow::{Context, Result};
use tree_sitter::{Parser, Language};
use crossbeam_queue::SegQueue;
use std::sync::Arc;
use tokio::sync::Semaphore;
use tracing::debug;

/// Configuration for parser pools
#[derive(Debug, Clone)]
pub struct ParserPoolConfig {
    /// Maximum number of parsers per language
    pub max_parsers_per_language: usize,
    /// Whether to warm up pools on creation
    pub warm_up_on_create: bool,
    /// Target utilization percentage for dynamic scaling
    pub target_utilization: f64,
    /// Minimum utilization before shrinking
    pub min_utilization: f64,
}

impl Default for ParserPoolConfig {
    fn default() -> Self {
        Self {
            max_parsers_per_language: 10,
            warm_up_on_create: true,
            target_utilization: 80.0,
            min_utilization: 20.0,
        }
    }
}

/// Statistics for a parser pool
#[derive(Debug, <PERSON>lone)]
pub struct ParserPoolStats {
    pub total_parsers: usize,
    pub available_parsers: usize,
    pub in_use_parsers: usize,
    pub max_parsers: usize,
    pub utilization: f64,
}

/// A pool of parsers for a specific language to reduce allocation overhead
#[derive(Debug)]
pub struct ParserPool {
    /// Queue of available parsers
    parsers: SegQueue<Parser>,
    /// Language for this pool
    language: Language,
    /// Semaphore to limit concurrent parser usage
    semaphore: Arc<Semaphore>,
    /// Maximum number of parsers in the pool
    max_size: usize,
    /// Current number of parsers created
    current_size: Arc<std::sync::atomic::AtomicUsize>,
}

impl ParserPool {
    /// Create a new parser pool for a language
    pub fn new(language: Language, max_size: usize) -> Result<Self> {
        let pool = Self {
            parsers: SegQueue::new(),
            language,
            semaphore: Arc::new(Semaphore::new(max_size)),
            max_size,
            current_size: Arc::new(std::sync::atomic::AtomicUsize::new(0)),
        };

        Ok(pool)
    }

    /// Create a new parser pool with configuration
    pub fn new_with_config(language: Language, config: &ParserPoolConfig) -> Result<Self> {
        let pool = Self::new(language, config.max_parsers_per_language)?;
        
        if config.warm_up_on_create {
            pool.warm_up_pool()?;
        }

        Ok(pool)
    }

    /// Warm up the pool with initial parsers
    pub fn warm_up_pool(&self) -> Result<()> {
        let warm_up_count = (self.max_size / 4).max(1).min(4); // 25% of max size, at least 1, at most 4
        
        for _ in 0..warm_up_count {
            let mut parser = Parser::new();
            parser.set_language(&self.language)
                .context("Failed to set language for parser during warm-up")?;
            
            self.parsers.push(parser);
            self.current_size.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
        }
        
        debug!("Warmed up parser pool with {} parsers", warm_up_count);
        Ok(())
    }

    /// Get pool statistics
    pub fn get_stats(&self) -> ParserPoolStats {
        let available = self.parsers.len();
        let total = self.current_size.load(std::sync::atomic::Ordering::Relaxed);
        let in_use = total.saturating_sub(available);
        
        ParserPoolStats {
            total_parsers: total,
            available_parsers: available,
            in_use_parsers: in_use,
            max_parsers: self.max_size,
            utilization: if total > 0 { (in_use as f64 / total as f64) * 100.0 } else { 0.0 },
        }
    }

    /// Dynamically adjust pool size based on usage patterns
    pub fn adjust_pool_size(&self, target_utilization: f64) -> Result<()> {
        let stats = self.get_stats();
        
        // If utilization is too high, try to add more parsers
        if stats.utilization > target_utilization && stats.total_parsers < self.max_size {
            let needed = ((stats.in_use_parsers as f64 / target_utilization * 100.0) as usize)
                .saturating_sub(stats.total_parsers)
                .min(self.max_size - stats.total_parsers);
            
            for _ in 0..needed {
                let mut parser = Parser::new();
                parser.set_language(&self.language)
                    .context("Failed to set language for parser during expansion")?;
                
                self.parsers.push(parser);
                self.current_size.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
            }
            
            debug!("Expanded parser pool by {} parsers (utilization: {:.1}%)", needed, stats.utilization);
        }
        
        Ok(())
    }

    /// Preload parsers for expected load with intelligent sizing
    pub fn preload_parsers(&self, expected_concurrent_requests: usize) -> Result<()> {
        let current_total = self.current_size.load(std::sync::atomic::Ordering::Relaxed);
        let available = self.parsers.len();
        let in_use = current_total.saturating_sub(available);
        
        // Calculate target based on current utilization and expected load
        let utilization_factor = if current_total > 0 { in_use as f64 / current_total as f64 } else { 0.0 };
        let growth_multiplier = if utilization_factor > 0.8 { 1.5 } else { 1.2 };
        
        let target_total = ((expected_concurrent_requests as f64 * growth_multiplier) as usize)
            .min(self.max_size)
            .max(current_total);
        
        if target_total > current_total {
            let to_create = target_total - current_total;
            
            // Create parsers in batches to avoid blocking
            let batch_size = 5;
            for batch in (0..to_create).step_by(batch_size) {
                let batch_end = (batch + batch_size).min(to_create);
                
                for _ in batch..batch_end {
                    let mut parser = Parser::new();
                    parser.set_language(&self.language)
                        .context("Failed to set language for parser during preload")?;
                    
                    self.parsers.push(parser);
                    self.current_size.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
                }
                
                // Small yield to prevent blocking
                if batch_end < to_create {
                    std::thread::yield_now();
                }
            }
            
            debug!("Preloaded {} parsers for expected load (utilization: {:.1}%, target: {})", 
                   to_create, utilization_factor * 100.0, target_total);
        }
        
        Ok(())
    }
    
    /// Shrink the pool if there are too many unused parsers
    pub fn shrink_if_underutilized(&self, min_utilization: f64) -> Result<()> {
        let current_total = self.current_size.load(std::sync::atomic::Ordering::Relaxed);
        let available = self.parsers.len();
        let in_use = current_total.saturating_sub(available);
        
        if current_total > 0 {
            let utilization = in_use as f64 / current_total as f64;
            
            if utilization < min_utilization && available > 2 {
                // Keep at least 2 parsers available
                let to_remove = (available - 2).min(available / 2);
                
                for _ in 0..to_remove {
                    if self.parsers.pop().is_some() {
                        self.current_size.fetch_sub(1, std::sync::atomic::Ordering::Relaxed);
                    }
                }
                
                debug!("Shrunk parser pool by {} parsers (utilization: {:.1}%)", to_remove, utilization * 100.0);
            }
        }
        
        Ok(())
    }

    /// Get a parser from the pool, creating one if necessary
    pub async fn get_parser(&self) -> Result<Parser> {
        // Acquire semaphore permit to limit concurrent usage
        let _permit = self.semaphore.acquire().await
            .map_err(|_| crate::errors::ParserError::PoolError("Failed to acquire parser semaphore".to_string()))?;

        // Try to get an existing parser from the queue
        if let Some(parser) = self.parsers.pop() {
            return Ok(parser);
        }

        // If no parser available and we haven't reached max size, create a new one
        let current = self.current_size.load(std::sync::atomic::Ordering::Relaxed);
        if current < self.max_size {
            let mut parser = Parser::new();
            parser.set_language(&self.language)
                .context("Failed to set language for new parser")?;
            
            self.current_size.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
            return Ok(parser);
        }

        // If we've reached max size, wait for a parser to become available
        loop {
            if let Some(parser) = self.parsers.pop() {
                return Ok(parser);
            }
            tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
        }
    }

    /// Return a parser to the pool
    pub fn return_parser(&self, parser: Parser) {
        self.parsers.push(parser);
    }
}