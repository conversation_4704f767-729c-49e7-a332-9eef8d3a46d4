use std::path::Path;
use anyhow::Result;

use super::language_registry::{is_language_supported, requires_custom_parser};
use super::config::LanguageDetectionConfig;

/// Detect programming language from file path and optionally content
pub struct LanguageDetector {
    config: LanguageDetectionConfig,
}

impl LanguageDetector {
    /// Create a new language detector
    pub fn new(config: LanguageDetectionConfig) -> Self {
        Self { config }
    }

    /// Detect language from file path and optionally content
    pub fn detect(&self, file_path: &Path, content: Option<&str>) -> Result<Option<String>> {
        // First try extension-based detection if enabled
        if self.config.use_extension {
            if let Some(lang) = self.detect_from_extension(file_path) {
                return Ok(Some(lang));
            }
        }

        // Then try content-based detection if enabled and content provided
        if self.config.use_content {
            if let Some(content) = content {
                if let Some(lang) = self.detect_from_content(file_path, content)? {
                    return Ok(Some(lang));
                }
            }
        }

        Ok(None)
    }

    /// Detect language from file extension
    pub fn detect_from_extension(&self, file_path: &Path) -> Option<String> {
        let extension = file_path.extension()?.to_str()?;
        let language = match extension {
            // Core languages
            "rs" => "rust",
            "py" => "python",
            "js" | "mjs" | "cjs" => "javascript",
            "ts" | "tsx" => "typescript",
            "go" => "go",
            "java" => "java",
            "c" | "h" => "c",
            "cpp" | "cc" | "cxx" | "hpp" | "hxx" => "cpp",
            
            // Web and markup
            "html" | "htm" => "html",
            "css" | "scss" | "sass" => "css",
            "json" => "json",
            "yaml" | "yml" => "yaml",
            "rb" => "ruby",
            "sh" | "bash" => "bash",
            "md" | "markdown" => "markdown",
            
            // Mobile development
            "swift" => "swift",
            "kt" | "kts" => "kotlin",
            "m" | "mm" => "objc",
            
            // Data science
            "r" | "R" => "r",
            "jl" => "julia",
            
            // Functional programming
            "hs" => "haskell",
            "scala" | "sc" => "scala",
            "erl" | "hrl" => "erlang",
            "ex" | "exs" => "elixir",
            
            // Systems programming
            "zig" => "zig",
            "d" => "d",
            
            // Other languages
            "lua" => "lua",
            "dart" => "dart",
            "nix" => "nix",
            
            // Custom parsers
            "sql" => "sql",
            "toml" => "toml",
            "xml" => "xml",
            
            _ => return None,
        };

        Some(language.to_string())
    }

    /// Detect language from file content (shebang, etc.)
    pub fn detect_from_content(&self, file_path: &Path, content: &str) -> Result<Option<String>> {
        // Check for shebang
        if let Some(first_line) = content.lines().next() {
            if first_line.starts_with("#!") {
                if let Some(lang) = self.detect_from_shebang(first_line) {
                    return Ok(Some(lang));
                }
            }
        }

        // Check for language-specific patterns
        if let Some(lang) = self.detect_from_patterns(content)? {
            return Ok(Some(lang));
        }

        // Special case: distinguish between C and C++
        if let Some(ext) = file_path.extension() {
            if ext == "h" {
                if self.is_cpp_header(content) {
                    return Ok(Some("cpp".to_string()));
                }
            }
        }

        Ok(None)
    }

    /// Detect language from shebang line
    fn detect_from_shebang(&self, shebang: &str) -> Option<String> {
        if shebang.contains("python") {
            Some("python".to_string())
        } else if shebang.contains("node") {
            Some("javascript".to_string())
        } else if shebang.contains("ruby") {
            Some("ruby".to_string())
        } else if shebang.contains("bash") || shebang.contains("sh") {
            Some("bash".to_string())
        } else if shebang.contains("lua") {
            Some("lua".to_string())
        } else {
            None
        }
    }

    /// Detect language from content patterns
    fn detect_from_patterns(&self, content: &str) -> Result<Option<String>> {
        let sample = if content.len() > self.config.content_sample_size {
            &content[..self.config.content_sample_size]
        } else {
            content
        };

        // Check for SQL patterns
        if self.looks_like_sql(sample) {
            return Ok(Some("sql".to_string()));
        }

        // Check for XML patterns
        if sample.trim_start().starts_with("<?xml") || sample.trim_start().starts_with("<") {
            if self.looks_like_xml(sample) {
                return Ok(Some("xml".to_string()));
            }
        }

        Ok(None)
    }

    /// Check if content looks like SQL
    fn looks_like_sql(&self, content: &str) -> bool {
        let sql_keywords = ["SELECT", "INSERT", "UPDATE", "DELETE", "CREATE", "ALTER", "DROP"];
        let content_upper = content.to_uppercase();
        
        let keyword_count = sql_keywords
            .iter()
            .filter(|&kw| content_upper.contains(kw))
            .count();
        
        keyword_count >= 2
    }

    /// Check if content looks like XML
    fn looks_like_xml(&self, content: &str) -> bool {
        content.contains("</") && content.contains(">")
    }

    /// Check if a header file is C++
    fn is_cpp_header(&self, content: &str) -> bool {
        // Look for C++ specific keywords
        let cpp_keywords = ["class", "namespace", "template", "virtual", "public:", "private:", "protected:"];
        cpp_keywords.iter().any(|&keyword| content.contains(keyword))
    }
}

impl Default for LanguageDetector {
    fn default() -> Self {
        Self::new(LanguageDetectionConfig::default())
    }
}

/// Check if a detected language is supported
pub fn is_supported_language(language: &str) -> bool {
    is_language_supported(language) || requires_custom_parser(language)
}