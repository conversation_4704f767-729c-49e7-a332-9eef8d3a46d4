pub mod regular;
pub mod streaming;
pub mod chunked;

use anyhow::Result;
use std::path::Path;
use crate::models::{FileAnalysis, ParseError};
use crate::parser::config::StreamingConfig;

/// Trait for different parsing strategies
#[async_trait::async_trait]
pub trait ParsingStrategy: Send + Sync {
    /// Parse a file using this strategy
    async fn parse(
        &self,
        file_path: &Path,
        content: Option<&str>,
        config: &StreamingConfig,
    ) -> Result<FileAnalysis, ParseError>;

    /// Check if this strategy is suitable for the given file
    fn is_suitable(&self, file_size: u64, config: &StreamingConfig) -> bool;
}