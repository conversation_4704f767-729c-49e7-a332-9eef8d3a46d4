use async_trait::async_trait;
use std::path::Path;
use anyhow::Result;

use crate::models::{FileAnalysis, ParseError};
use crate::parser::config::StreamingConfig;
use super::ParsingStrategy;

/// Regular parsing strategy for small to medium files
pub struct RegularParsingStrategy;

#[async_trait]
impl ParsingStrategy for RegularParsingStrategy {
    async fn parse(
        &self,
        _file_path: &Path,
        _content: Option<&str>,
        _config: &StreamingConfig,
    ) -> Result<FileAnalysis, ParseError> {
        // TODO: Implement regular parsing
        todo!("Regular parsing strategy not yet implemented")
    }

    fn is_suitable(&self, file_size: u64, config: &StreamingConfig) -> bool {
        file_size <= config.streaming_threshold
    }
}