use anyhow::{Context, Result};
use crate::models::{FileAnalysis, SymbolType};
use crate::services::embeddings_enhancement::{EnhancedEmbeddingsService, FeatureToggles};
use serde::{Deserialize, Serialize};
use reqwest::Client;
use std::env;
use std::time::Duration;
use std::sync::Arc;
use tokio::sync::Mutex;
use chrono::{DateTime, Utc};
use std::collections::HashMap;

const GEMINI_TIMEOUT: Duration = Duration::from_secs(45);
const MAX_RETRIES: u32 = 3;
const INITIAL_RETRY_DELAY: Duration = Duration::from_millis(1000);

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct IntelligentDocumentation {
    pub repository_overview: RepositoryOverview,
    pub api_documentation: ApiDocumentation,
    pub architecture_documentation: ArchitectureDocumentation,
    pub code_explanations: Vec<CodeExplanation>,
    pub usage_examples: Vec<UsageExample>,
    pub setup_instructions: SetupInstructions,
    pub troubleshooting_guide: TroubleshootingGuide,
    pub contributing_guidelines: ContributingGuidelines,
    pub changelog: ChangelogSummary,
    pub metadata: DocumentationMetadata,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct RepositoryOverview {
    pub title: String,
    pub description: String,
    pub key_features: Vec<String>,
    pub target_audience: String,
    pub use_cases: Vec<String>,
    pub quick_start: String,
    pub badges: Vec<Badge>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Badge {
    pub label: String,
    pub message: String,
    pub color: String,
    pub link: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ApiDocumentation {
    pub modules: Vec<ModuleDocumentation>,
    pub functions: Vec<FunctionDocumentation>,
    pub classes: Vec<ClassDocumentation>,
    pub types: Vec<TypeDocumentation>,
    pub constants: Vec<ConstantDocumentation>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ModuleDocumentation {
    pub name: String,
    pub description: String,
    pub purpose: String,
    pub dependencies: Vec<String>,
    pub exports: Vec<String>,
    pub examples: Vec<String>,
    pub file_path: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct FunctionDocumentation {
    pub name: String,
    pub description: String,
    pub parameters: Vec<ParameterDocumentation>,
    pub return_type: String,
    pub return_description: String,
    pub examples: Vec<String>,
    pub complexity: String,
    pub performance_notes: Vec<String>,
    pub error_handling: String,
    pub file_path: String,
    pub line_number: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ParameterDocumentation {
    pub name: String,
    pub param_type: String,
    pub description: String,
    pub optional: bool,
    pub default_value: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ClassDocumentation {
    pub name: String,
    pub description: String,
    pub purpose: String,
    pub inheritance: Vec<String>,
    pub interfaces: Vec<String>,
    pub methods: Vec<MethodDocumentation>,
    pub properties: Vec<PropertyDocumentation>,
    pub constructor: Option<ConstructorDocumentation>,
    pub examples: Vec<String>,
    pub design_patterns: Vec<String>,
    pub file_path: String,
    pub line_number: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MethodDocumentation {
    pub name: String,
    pub description: String,
    pub visibility: String,
    pub parameters: Vec<ParameterDocumentation>,
    pub return_type: String,
    pub return_description: String,
    pub examples: Vec<String>,
    pub complexity: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PropertyDocumentation {
    pub name: String,
    pub prop_type: String,
    pub description: String,
    pub visibility: String,
    pub mutable: bool,
    pub default_value: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ConstructorDocumentation {
    pub description: String,
    pub parameters: Vec<ParameterDocumentation>,
    pub examples: Vec<String>,
    pub complexity: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TypeDocumentation {
    pub name: String,
    pub description: String,
    pub type_kind: String, // "struct", "enum", "interface", "type alias"
    pub fields: Vec<FieldDocumentation>,
    pub methods: Vec<MethodDocumentation>,
    pub examples: Vec<String>,
    pub file_path: String,
    pub line_number: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct FieldDocumentation {
    pub name: String,
    pub field_type: String,
    pub description: String,
    pub optional: bool,
    pub default_value: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ConstantDocumentation {
    pub name: String,
    pub value: String,
    pub description: String,
    pub const_type: String,
    pub usage_examples: Vec<String>,
    pub file_path: String,
    pub line_number: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ArchitectureDocumentation {
    pub overview: String,
    pub architecture_style: String,
    pub components: Vec<ComponentDocumentation>,
    pub data_flow: DataFlowDocumentation,
    pub design_decisions: Vec<DesignDecision>,
    pub diagrams: Vec<DiagramDescription>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ComponentDocumentation {
    pub name: String,
    pub description: String,
    pub responsibilities: Vec<String>,
    pub interfaces: Vec<String>,
    pub dependencies: Vec<String>,
    pub file_paths: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DataFlowDocumentation {
    pub description: String,
    pub flow_steps: Vec<FlowStep>,
    pub data_transformations: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct FlowStep {
    pub step_number: u32,
    pub component: String,
    pub action: String,
    pub input: String,
    pub output: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DesignDecision {
    pub title: String,
    pub description: String,
    pub rationale: String,
    pub alternatives: Vec<String>,
    pub consequences: Vec<String>,
    pub status: String, // "Active", "Superseded", "Deprecated"
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DiagramDescription {
    pub title: String,
    pub description: String,
    pub diagram_type: String, // "sequence", "class", "component", "flow"
    pub mermaid_code: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CodeExplanation {
    pub file_path: String,
    pub function_name: Option<String>,
    pub start_line: u32,
    pub end_line: u32,
    pub code_snippet: String,
    pub explanation: String,
    pub purpose: String,
    pub complexity_analysis: String,
    pub potential_improvements: Vec<String>,
    pub related_concepts: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UsageExample {
    pub title: String,
    pub description: String,
    pub code_language: String,
    pub code_example: String,
    pub expected_output: Option<String>,
    pub explanation: String,
    pub difficulty_level: String, // "Beginner", "Intermediate", "Advanced"
    pub prerequisites: Vec<String>,
    pub related_examples: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SetupInstructions {
    pub prerequisites: Vec<Prerequisite>,
    pub installation_steps: Vec<InstallationStep>,
    pub configuration: ConfigurationGuide,
    pub verification: VerificationSteps,
    pub troubleshooting: Vec<TroubleshootingItem>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Prerequisite {
    pub name: String,
    pub description: String,
    pub version_requirement: Option<String>,
    pub installation_link: Option<String>,
    pub optional: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct InstallationStep {
    pub step_number: u32,
    pub title: String,
    pub description: String,
    pub command: Option<String>,
    pub expected_output: Option<String>,
    pub notes: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ConfigurationGuide {
    pub description: String,
    pub config_files: Vec<ConfigFile>,
    pub environment_variables: Vec<EnvironmentVariable>,
    pub examples: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ConfigFile {
    pub name: String,
    pub path: String,
    pub description: String,
    pub format: String,
    pub required_fields: Vec<String>,
    pub example_content: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct EnvironmentVariable {
    pub name: String,
    pub description: String,
    pub required: bool,
    pub default_value: Option<String>,
    pub example_value: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct VerificationSteps {
    pub description: String,
    pub steps: Vec<VerificationStep>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct VerificationStep {
    pub step_number: u32,
    pub description: String,
    pub command: String,
    pub expected_result: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TroubleshootingGuide {
    pub common_issues: Vec<TroubleshootingItem>,
    pub debugging_tips: Vec<String>,
    pub support_resources: Vec<SupportResource>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TroubleshootingItem {
    pub issue: String,
    pub symptoms: Vec<String>,
    pub causes: Vec<String>,
    pub solutions: Vec<String>,
    pub prevention: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SupportResource {
    pub name: String,
    pub description: String,
    pub url: String,
    pub resource_type: String, // "Documentation", "Forum", "Issue Tracker", "Chat"
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ContributingGuidelines {
    pub overview: String,
    pub code_style: CodeStyleGuide,
    pub development_workflow: DevelopmentWorkflow,
    pub testing_requirements: TestingRequirements,
    pub pull_request_process: PullRequestProcess,
    pub issue_reporting: IssueReporting,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CodeStyleGuide {
    pub description: String,
    pub formatting_rules: Vec<String>,
    pub naming_conventions: Vec<String>,
    pub best_practices: Vec<String>,
    pub tools: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DevelopmentWorkflow {
    pub description: String,
    pub setup_steps: Vec<String>,
    pub branching_strategy: String,
    pub commit_conventions: Vec<String>,
    pub testing_workflow: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TestingRequirements {
    pub description: String,
    pub test_types: Vec<String>,
    pub coverage_requirements: String,
    pub testing_tools: Vec<String>,
    pub test_writing_guidelines: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PullRequestProcess {
    pub description: String,
    pub checklist: Vec<String>,
    pub review_process: Vec<String>,
    pub merge_requirements: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct IssueReporting {
    pub description: String,
    pub bug_report_template: String,
    pub feature_request_template: String,
    pub issue_labels: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ChangelogSummary {
    pub description: String,
    pub recent_changes: Vec<ChangeEntry>,
    pub version_history: Vec<VersionEntry>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ChangeEntry {
    pub change_type: String, // "Added", "Changed", "Deprecated", "Removed", "Fixed", "Security"
    pub description: String,
    pub impact: String,
    pub migration_notes: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct VersionEntry {
    pub version: String,
    pub release_date: String,
    pub highlights: Vec<String>,
    pub breaking_changes: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DocumentationMetadata {
    pub generation_method: String,
    pub confidence_score: f64,
    pub completeness_score: f64,
    pub coverage_metrics: HashMap<String, f64>,
    pub language_coverage: HashMap<String, f64>,
    pub last_updated: DateTime<Utc>,
    pub version: String,
}

// Gemini API structures (reusing from previous services)
#[derive(Debug, Serialize)]
struct GeminiDocRequest {
    contents: Vec<GeminiContent>,
    generation_config: GeminiGenerationConfig,
    safety_settings: Vec<GeminiSafetySettings>,
}

#[derive(Debug, Serialize)]
struct GeminiContent {
    parts: Vec<GeminiPart>,
    role: String,
}

#[derive(Debug, Serialize)]
struct GeminiPart {
    text: String,
}

#[derive(Debug, Serialize)]
struct GeminiGenerationConfig {
    temperature: f32,
    top_p: f32,
    top_k: i32,
    max_output_tokens: i32,
    response_mime_type: String,
}

#[derive(Debug, Serialize)]
struct GeminiSafetySettings {
    category: String,
    threshold: String,
}

#[derive(Debug, Deserialize)]
struct GeminiResponse {
    candidates: Vec<GeminiCandidate>,
}

#[derive(Debug, Deserialize)]
struct GeminiCandidate {
    content: GeminiResponseContent,
}

#[derive(Debug, Deserialize)]
struct GeminiResponseContent {
    parts: Vec<GeminiResponsePart>,
}

#[derive(Debug, Deserialize)]
struct GeminiResponsePart {
    text: String,
}

// Circuit breaker states
#[derive(Debug, Clone)]
enum CircuitState {
    Closed,
    Open(DateTime<Utc>),
    HalfOpen,
}

#[derive(Debug, Default)]
pub struct DocumentationMetrics {
    total_generations: u64,
    successful_generations: u64,
    failed_generations: u64,
    average_response_time_ms: f64,
    circuit_breaker_opens: u64,
    fallback_uses: u64,
}

pub struct IntelligentDocumentationService {
    client: Client,
    project_id: String,
    location: String,
    model_name: String,
    circuit_state: Arc<Mutex<CircuitState>>,
    failure_count: Arc<Mutex<u32>>,
    failure_threshold: u32,
    reset_timeout: Duration,
    feature_toggles: Arc<FeatureToggles>,
    metrics: Arc<Mutex<DocumentationMetrics>>,
    #[allow(dead_code)]
    embeddings_service: Arc<EnhancedEmbeddingsService>,
}

impl IntelligentDocumentationService {
    pub async fn new(embeddings_service: Arc<EnhancedEmbeddingsService>) -> Result<Self> {
        let project_id = env::var("GCP_PROJECT_ID")
            .unwrap_or_else(|_| "vibe-match-463114".to_string());
        let location = env::var("GCP_REGION").unwrap_or_else(|_| "us-central1".to_string());
        let model_name = env::var("GEMINI_MODEL_NAME")
            .unwrap_or_else(|_| "gemini-2.5-flash".to_string());

        let client = Client::builder()
            .timeout(GEMINI_TIMEOUT)
            .build()
            .context("Failed to create HTTP client")?;

        let feature_toggles = embeddings_service.get_feature_toggles();

        Ok(Self {
            client,
            project_id,
            location,
            model_name,
            circuit_state: Arc::new(Mutex::new(CircuitState::Closed)),
            failure_count: Arc::new(Mutex::new(0)),
            failure_threshold: 5,
            reset_timeout: Duration::from_secs(60),
            feature_toggles,
            metrics: Arc::new(Mutex::new(DocumentationMetrics::default())),
            embeddings_service,
        })
    }

    pub async fn generate_intelligent_documentation(
        &self,
        analyses: &[FileAnalysis],
        repository_url: &str,
        primary_language: &str,
    ) -> Result<IntelligentDocumentation> {
        if !self.feature_toggles.enable_intelligent_documentation {
            tracing::info!("Intelligent documentation disabled by feature toggle");
            return Ok(self.create_fallback_documentation(analyses, repository_url, primary_language));
        }

        // Check circuit breaker
        if !self.check_circuit_breaker().await? {
            tracing::warn!("Circuit breaker is open, using fallback documentation");
            self.record_fallback_use().await;
            return Ok(self.create_fallback_documentation(analyses, repository_url, primary_language));
        }

        let start_time = std::time::Instant::now();
        
        match self.perform_ai_documentation_generation(analyses, repository_url, primary_language).await {
            Ok(documentation) => {
                self.record_success().await;
                self.record_response_time(start_time.elapsed().as_millis() as f64).await;
                Ok(documentation)
            }
            Err(e) => {
                tracing::error!("AI documentation generation failed: {}", e);
                self.record_failure().await;
                
                // Use fallback documentation
                self.record_fallback_use().await;
                Ok(self.create_fallback_documentation(analyses, repository_url, primary_language))
            }
        }
    }

    async fn perform_ai_documentation_generation(
        &self,
        analyses: &[FileAnalysis],
        repository_url: &str,
        primary_language: &str,
    ) -> Result<IntelligentDocumentation> {
        let prompt = self.build_documentation_prompt(analyses, repository_url, primary_language)?;
        
        let mut retry_delay = INITIAL_RETRY_DELAY;
        
        for attempt in 0..MAX_RETRIES {
            match self.call_gemini_for_documentation(&prompt).await {
                Ok(documentation) => {
                    return Ok(documentation);
                }
                Err(e) => {
                    if !self.is_retryable_error(&e) || attempt == MAX_RETRIES - 1 {
                        return Err(e);
                    }
                    
                    tracing::warn!(
                        "Documentation generation attempt {} failed, retrying in {:?}: {}",
                        attempt + 1,
                        retry_delay,
                        e
                    );
                    
                    tokio::time::sleep(retry_delay).await;
                    retry_delay = std::cmp::min(retry_delay * 2, Duration::from_secs(30));
                }
            }
        }
        
        Err(anyhow::anyhow!("All documentation generation attempts exhausted"))
    }

    fn build_documentation_prompt(
        &self,
        analyses: &[FileAnalysis],
        repository_url: &str,
        primary_language: &str,
    ) -> Result<String> {
        let mut prompt = String::new();
        
        prompt.push_str("You are a technical documentation specialist. Generate comprehensive, user-friendly documentation for this codebase. Return a JSON response with the following structure:\n\n");
        
        // Include a simplified but comprehensive JSON schema
        prompt.push_str("{\n");
        prompt.push_str("  \"repository_overview\": {\n");
        prompt.push_str("    \"title\": \"Repository Name\",\n");
        prompt.push_str("    \"description\": \"Clear description of what this repository does\",\n");
        prompt.push_str("    \"key_features\": [\"Feature 1\", \"Feature 2\"],\n");
        prompt.push_str("    \"target_audience\": \"Developers, users, etc.\",\n");
        prompt.push_str("    \"use_cases\": [\"Use case 1\", \"Use case 2\"],\n");
        prompt.push_str("    \"quick_start\": \"Quick start guide\",\n");
        prompt.push_str("    \"badges\": [{\"label\": \"Build\", \"message\": \"passing\", \"color\": \"green\", \"link\": null}]\n");
        prompt.push_str("  },\n");
        
        prompt.push_str("  \"api_documentation\": {\n");
        prompt.push_str("    \"modules\": [{\"name\": \"module_name\", \"description\": \"What it does\", \"purpose\": \"Why it exists\", \"dependencies\": [], \"exports\": [], \"examples\": [], \"file_path\": \"path/to/file\"}],\n");
        prompt.push_str("    \"functions\": [{\"name\": \"function_name\", \"description\": \"What it does\", \"parameters\": [], \"return_type\": \"String\", \"return_description\": \"What it returns\", \"examples\": [], \"complexity\": \"O(n)\", \"performance_notes\": [], \"error_handling\": \"How errors are handled\", \"file_path\": \"path/to/file\", \"line_number\": 42}],\n");
        prompt.push_str("    \"classes\": [{\"name\": \"ClassName\", \"description\": \"What it does\", \"purpose\": \"Why it exists\", \"inheritance\": [], \"interfaces\": [], \"methods\": [], \"properties\": [], \"constructor\": null, \"examples\": [], \"design_patterns\": [], \"file_path\": \"path/to/file\", \"line_number\": 10}],\n");
        prompt.push_str("    \"types\": [{\"name\": \"TypeName\", \"description\": \"What it represents\", \"type_kind\": \"struct\", \"fields\": [], \"methods\": [], \"examples\": [], \"file_path\": \"path/to/file\", \"line_number\": 5}],\n");
        prompt.push_str("    \"constants\": [{\"name\": \"CONSTANT_NAME\", \"value\": \"value\", \"description\": \"What it represents\", \"const_type\": \"string\", \"usage_examples\": [], \"file_path\": \"path/to/file\", \"line_number\": 1}]\n");
        prompt.push_str("  },\n");
        
        prompt.push_str("  \"architecture_documentation\": {\n");
        prompt.push_str("    \"overview\": \"High-level architecture description\",\n");
        prompt.push_str("    \"architecture_style\": \"Microservices/Monolith/etc\",\n");
        prompt.push_str("    \"components\": [{\"name\": \"Component\", \"description\": \"What it does\", \"responsibilities\": [], \"interfaces\": [], \"dependencies\": [], \"file_paths\": []}],\n");
        prompt.push_str("    \"data_flow\": {\"description\": \"How data flows\", \"flow_steps\": [], \"data_transformations\": []},\n");
        prompt.push_str("    \"design_decisions\": [{\"title\": \"Decision\", \"description\": \"What was decided\", \"rationale\": \"Why\", \"alternatives\": [], \"consequences\": [], \"status\": \"Active\"}],\n");
        prompt.push_str("    \"diagrams\": [{\"title\": \"Diagram\", \"description\": \"What it shows\", \"diagram_type\": \"sequence\", \"mermaid_code\": \"graph TD\\n  A --> B\"}]\n");
        prompt.push_str("  },\n");
        
        prompt.push_str("  \"code_explanations\": [{\"file_path\": \"path/to/file\", \"function_name\": \"function_name\", \"start_line\": 10, \"end_line\": 20, \"code_snippet\": \"code here\", \"explanation\": \"What this code does\", \"purpose\": \"Why it exists\", \"complexity_analysis\": \"O(n)\", \"potential_improvements\": [], \"related_concepts\": []}],\n");
        
        prompt.push_str("  \"usage_examples\": [{\"title\": \"Example\", \"description\": \"What it shows\", \"code_language\": \"rust\", \"code_example\": \"let x = 5;\", \"expected_output\": \"5\", \"explanation\": \"Explanation\", \"difficulty_level\": \"Beginner\", \"prerequisites\": [], \"related_examples\": []}],\n");
        
        prompt.push_str("  \"setup_instructions\": {\n");
        prompt.push_str("    \"prerequisites\": [{\"name\": \"Rust\", \"description\": \"Programming language\", \"version_requirement\": \"1.70+\", \"installation_link\": \"https://rustup.rs/\", \"optional\": false}],\n");
        prompt.push_str("    \"installation_steps\": [{\"step_number\": 1, \"title\": \"Clone repository\", \"description\": \"Clone the repo\", \"command\": \"git clone ...\", \"expected_output\": \"Cloning...\", \"notes\": []}],\n");
        prompt.push_str("    \"configuration\": {\"description\": \"How to configure\", \"config_files\": [], \"environment_variables\": [], \"examples\": []},\n");
        prompt.push_str("    \"verification\": {\"description\": \"How to verify\", \"steps\": []},\n");
        prompt.push_str("    \"troubleshooting\": []\n");
        prompt.push_str("  },\n");
        
        prompt.push_str("  \"troubleshooting_guide\": {\n");
        prompt.push_str("    \"common_issues\": [{\"issue\": \"Issue description\", \"symptoms\": [], \"causes\": [], \"solutions\": [], \"prevention\": []}],\n");
        prompt.push_str("    \"debugging_tips\": [\"Tip 1\", \"Tip 2\"],\n");
        prompt.push_str("    \"support_resources\": [{\"name\": \"GitHub Issues\", \"description\": \"Report bugs\", \"url\": \"https://github.com/...\", \"resource_type\": \"Issue Tracker\"}]\n");
        prompt.push_str("  },\n");
        
        prompt.push_str("  \"contributing_guidelines\": {\n");
        prompt.push_str("    \"overview\": \"How to contribute\",\n");
        prompt.push_str("    \"code_style\": {\"description\": \"Code style rules\", \"formatting_rules\": [], \"naming_conventions\": [], \"best_practices\": [], \"tools\": []},\n");
        prompt.push_str("    \"development_workflow\": {\"description\": \"Development process\", \"setup_steps\": [], \"branching_strategy\": \"Git flow\", \"commit_conventions\": [], \"testing_workflow\": []},\n");
        prompt.push_str("    \"testing_requirements\": {\"description\": \"Testing requirements\", \"test_types\": [], \"coverage_requirements\": \"80%\", \"testing_tools\": [], \"test_writing_guidelines\": []},\n");
        prompt.push_str("    \"pull_request_process\": {\"description\": \"PR process\", \"checklist\": [], \"review_process\": [], \"merge_requirements\": []},\n");
        prompt.push_str("    \"issue_reporting\": {\"description\": \"How to report issues\", \"bug_report_template\": \"Template\", \"feature_request_template\": \"Template\", \"issue_labels\": []}\n");
        prompt.push_str("  },\n");
        
        prompt.push_str("  \"changelog\": {\n");
        prompt.push_str("    \"description\": \"Recent changes\",\n");
        prompt.push_str("    \"recent_changes\": [{\"change_type\": \"Added\", \"description\": \"New feature\", \"impact\": \"High\", \"migration_notes\": null}],\n");
        prompt.push_str("    \"version_history\": [{\"version\": \"1.0.0\", \"release_date\": \"2024-01-01\", \"highlights\": [], \"breaking_changes\": []}]\n");
        prompt.push_str("  },\n");
        
        prompt.push_str("  \"metadata\": {\n");
        prompt.push_str("    \"generation_method\": \"AI-powered\",\n");
        prompt.push_str("    \"confidence_score\": 85.0,\n");
        prompt.push_str("    \"completeness_score\": 90.0,\n");
        prompt.push_str("    \"coverage_metrics\": {\"functions\": 95.0, \"classes\": 90.0},\n");
        prompt.push_str("    \"language_coverage\": {\"rust\": 100.0},\n");
        prompt.push_str("    \"last_updated\": \"2024-01-01T00:00:00Z\",\n");
        prompt.push_str("    \"version\": \"1.0.0\"\n");
        prompt.push_str("  }\n");
        prompt.push_str("}\n\n");
        
        // Add repository context
        prompt.push_str(&format!("Repository URL: {}\n", repository_url));
        prompt.push_str(&format!("Primary language: {}\n", primary_language));
        prompt.push_str(&format!("Files analyzed: {}\n", analyses.len()));
        
        // Add key functions and classes
        prompt.push_str("\nKey symbols found:\n");
        let mut _function_count = 0;
        let mut _class_count = 0;
        
        for analysis in analyses.iter().take(10) {
            if let Some(symbols) = &analysis.symbols {
                prompt.push_str(&format!("\nFile: {}\n", analysis.path));
                
                for symbol in symbols.iter().take(5) {
                    match symbol.symbol_type {
                        SymbolType::Function | SymbolType::Method => {
                            _function_count += 1;
                            prompt.push_str(&format!("  Function: {} ({})\n", symbol.name, symbol.signature.as_ref().unwrap_or(&"No signature".to_string())));
                        }
                        SymbolType::Class => {
                            _class_count += 1;
                            prompt.push_str(&format!("  Class: {}\n", symbol.name));
                        }
                        SymbolType::Type => {
                            prompt.push_str(&format!("  Type: {}\n", symbol.name));
                        }
                        SymbolType::Constant => {
                            prompt.push_str(&format!("  Constant: {}\n", symbol.name));
                        }
                        _ => {}
                    }
                }
            }
        }
        
        // Add sample code
        prompt.push_str("\nSample code for context:\n");
        for analysis in analyses.iter().take(3) {
            if let Some(text) = &analysis.ast.text {
                let preview = text.chars().take(1000).collect::<String>();
                prompt.push_str(&format!("\nFile: {}\n```{}\n{}\n```\n", 
                    analysis.path, analysis.language, preview));
            }
        }
        
        prompt.push_str("\nGenerate comprehensive, user-friendly documentation that helps developers understand and use this codebase effectively. Focus on clarity, completeness, and practical examples.");
        
        Ok(prompt)
    }

    async fn call_gemini_for_documentation(&self, prompt: &str) -> Result<IntelligentDocumentation> {
        let auth_token = self.get_auth_token().await?;
        
        let endpoint = format!(
            "https://{}-aiplatform.googleapis.com/v1/projects/{}/locations/{}/publishers/google/models/{}:generateContent",
            self.location, self.project_id, self.location, self.model_name
        );

        let request = GeminiDocRequest {
            contents: vec![GeminiContent {
                parts: vec![GeminiPart {
                    text: prompt.to_string(),
                }],
                role: "user".to_string(),
            }],
            generation_config: GeminiGenerationConfig {
                temperature: 0.1,
                top_p: 0.8,
                top_k: 40,
                max_output_tokens: 8192,
                response_mime_type: "application/json".to_string(),
            },
            safety_settings: vec![
                GeminiSafetySettings {
                    category: "HARM_CATEGORY_HARASSMENT".to_string(),
                    threshold: "BLOCK_MEDIUM_AND_ABOVE".to_string(),
                },
                GeminiSafetySettings {
                    category: "HARM_CATEGORY_HATE_SPEECH".to_string(),
                    threshold: "BLOCK_MEDIUM_AND_ABOVE".to_string(),
                },
                GeminiSafetySettings {
                    category: "HARM_CATEGORY_SEXUALLY_EXPLICIT".to_string(),
                    threshold: "BLOCK_MEDIUM_AND_ABOVE".to_string(),
                },
                GeminiSafetySettings {
                    category: "HARM_CATEGORY_DANGEROUS_CONTENT".to_string(),
                    threshold: "BLOCK_MEDIUM_AND_ABOVE".to_string(),
                },
            ],
        };

        let response = self.client
            .post(&endpoint)
            .bearer_auth(&auth_token)
            .json(&request)
            .send()
            .await
            .context("Failed to send request to Gemini API")?;

        let status = response.status();
        
        if !status.is_success() {
            let error_body = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            return Err(anyhow::anyhow!(
                "Gemini API request failed with status {}: {}",
                status,
                error_body
            ));
        }

        let gemini_response: GeminiResponse = response
            .json()
            .await
            .context("Failed to parse Gemini response")?;

        if gemini_response.candidates.is_empty() {
            return Err(anyhow::anyhow!("No candidates in Gemini response"));
        }

        let response_text = &gemini_response.candidates[0].content.parts[0].text;
        
        let mut documentation: IntelligentDocumentation = serde_json::from_str(response_text)
            .context("Failed to parse documentation JSON")?;

        documentation.timestamp = Utc::now();
        
        Ok(documentation)
    }

    fn create_fallback_documentation(
        &self,
        analyses: &[FileAnalysis],
        repository_url: &str,
        primary_language: &str,
    ) -> IntelligentDocumentation {
        let repo_name = repository_url.split('/').last().unwrap_or("Repository");
        
        let mut functions = Vec::new();
        let mut classes = Vec::new();
        let mut types = Vec::new();
        let mut constants = Vec::new();
        let mut modules = Vec::new();
        
        for analysis in analyses {
            // Create module documentation
            modules.push(ModuleDocumentation {
                name: analysis.path.split('/').last().unwrap_or(&analysis.path).to_string(),
                description: format!("Module containing {} code", analysis.language),
                purpose: "Implementation of core functionality".to_string(),
                dependencies: vec![],
                exports: vec![],
                examples: vec![],
                file_path: analysis.path.clone(),
            });
            
            if let Some(symbols) = &analysis.symbols {
                for symbol in symbols {
                    match symbol.symbol_type {
                        SymbolType::Function | SymbolType::Method => {
                            functions.push(FunctionDocumentation {
                                name: symbol.name.clone(),
                                description: format!("Function {}", symbol.name),
                                parameters: vec![],
                                return_type: "Unknown".to_string(),
                                return_description: "Return value".to_string(),
                                examples: vec![],
                                complexity: "O(1)".to_string(),
                                performance_notes: vec![],
                                error_handling: "Standard error handling".to_string(),
                                file_path: analysis.path.clone(),
                                line_number: symbol.range.start.line,
                            });
                        }
                        SymbolType::Class => {
                            classes.push(ClassDocumentation {
                                name: symbol.name.clone(),
                                description: format!("Class {}", symbol.name),
                                purpose: "Core class implementation".to_string(),
                                inheritance: vec![],
                                interfaces: vec![],
                                methods: vec![],
                                properties: vec![],
                                constructor: None,
                                examples: vec![],
                                design_patterns: vec![],
                                file_path: analysis.path.clone(),
                                line_number: symbol.range.start.line,
                            });
                        }
                        SymbolType::Type => {
                            types.push(TypeDocumentation {
                                name: symbol.name.clone(),
                                description: format!("Type {}", symbol.name),
                                type_kind: "struct".to_string(),
                                fields: vec![],
                                methods: vec![],
                                examples: vec![],
                                file_path: analysis.path.clone(),
                                line_number: symbol.range.start.line,
                            });
                        }
                        SymbolType::Constant => {
                            constants.push(ConstantDocumentation {
                                name: symbol.name.clone(),
                                value: "Unknown".to_string(),
                                description: format!("Constant {}", symbol.name),
                                const_type: "Unknown".to_string(),
                                usage_examples: vec![],
                                file_path: analysis.path.clone(),
                                line_number: symbol.range.start.line,
                            });
                        }
                        _ => {}
                    }
                }
            }
        }
        
        IntelligentDocumentation {
            repository_overview: RepositoryOverview {
                title: repo_name.to_string(),
                description: format!("{} repository written in {}", repo_name, primary_language),
                key_features: vec!["Core functionality".to_string()],
                target_audience: "Developers".to_string(),
                use_cases: vec!["Software development".to_string()],
                quick_start: "Clone and build the repository".to_string(),
                badges: vec![],
            },
            api_documentation: ApiDocumentation {
                modules,
                functions,
                classes,
                types,
                constants,
            },
            architecture_documentation: ArchitectureDocumentation {
                overview: "Standard project architecture".to_string(),
                architecture_style: "Modular".to_string(),
                components: vec![],
                data_flow: DataFlowDocumentation {
                    description: "Standard data flow".to_string(),
                    flow_steps: vec![],
                    data_transformations: vec![],
                },
                design_decisions: vec![],
                diagrams: vec![],
            },
            code_explanations: vec![],
            usage_examples: vec![],
            setup_instructions: SetupInstructions {
                prerequisites: vec![],
                installation_steps: vec![],
                configuration: ConfigurationGuide {
                    description: "Standard configuration".to_string(),
                    config_files: vec![],
                    environment_variables: vec![],
                    examples: vec![],
                },
                verification: VerificationSteps {
                    description: "Verify installation".to_string(),
                    steps: vec![],
                },
                troubleshooting: vec![],
            },
            troubleshooting_guide: TroubleshootingGuide {
                common_issues: vec![],
                debugging_tips: vec!["Check logs for errors".to_string()],
                support_resources: vec![],
            },
            contributing_guidelines: ContributingGuidelines {
                overview: "Standard contribution guidelines".to_string(),
                code_style: CodeStyleGuide {
                    description: "Follow standard code style".to_string(),
                    formatting_rules: vec![],
                    naming_conventions: vec![],
                    best_practices: vec![],
                    tools: vec![],
                },
                development_workflow: DevelopmentWorkflow {
                    description: "Standard development workflow".to_string(),
                    setup_steps: vec![],
                    branching_strategy: "Git flow".to_string(),
                    commit_conventions: vec![],
                    testing_workflow: vec![],
                },
                testing_requirements: TestingRequirements {
                    description: "Standard testing requirements".to_string(),
                    test_types: vec![],
                    coverage_requirements: "80%".to_string(),
                    testing_tools: vec![],
                    test_writing_guidelines: vec![],
                },
                pull_request_process: PullRequestProcess {
                    description: "Standard PR process".to_string(),
                    checklist: vec![],
                    review_process: vec![],
                    merge_requirements: vec![],
                },
                issue_reporting: IssueReporting {
                    description: "Standard issue reporting".to_string(),
                    bug_report_template: "Bug report template".to_string(),
                    feature_request_template: "Feature request template".to_string(),
                    issue_labels: vec![],
                },
            },
            changelog: ChangelogSummary {
                description: "Recent changes".to_string(),
                recent_changes: vec![],
                version_history: vec![],
            },
            metadata: DocumentationMetadata {
                generation_method: "Fallback generation".to_string(),
                confidence_score: 60.0,
                completeness_score: 70.0,
                coverage_metrics: HashMap::new(),
                language_coverage: HashMap::new(),
                last_updated: Utc::now(),
                version: "1.0.0".to_string(),
            },
            timestamp: Utc::now(),
        }
    }

    async fn get_auth_token(&self) -> Result<String> {
        // Reuse auth token logic from previous services
        if env::var("K_SERVICE").is_ok() || env::var("GAE_ENV").is_ok() {
            let metadata_url = format!(
                "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/token?scopes={}",
                "https://www.googleapis.com/auth/cloud-platform"
            );
            
            let response = self.client
                .get(&metadata_url)
                .header("Metadata-Flavor", "Google")
                .send()
                .await
                .context("Failed to fetch token from metadata server")?;
                
            if !response.status().is_success() {
                return Err(anyhow::anyhow!(
                    "Metadata server returned error: {}",
                    response.status()
                ));
            }
            
            #[derive(Deserialize)]
            struct TokenResponse {
                access_token: String,
            }
            
            let token_response: TokenResponse = response
                .json()
                .await
                .context("Failed to parse token response")?;
                
            Ok(token_response.access_token)
        } else if let Ok(_creds_path) = env::var("GOOGLE_APPLICATION_CREDENTIALS") {
            use std::process::Command;
            
            let output = Command::new("gcloud")
                .args(&["auth", "application-default", "print-access-token"])
                .output()
                .context("Failed to run gcloud command")?;
                
            if !output.status.success() {
                return Err(anyhow::anyhow!(
                    "gcloud command failed: {}",
                    String::from_utf8_lossy(&output.stderr)
                ));
            }
            
            let token = String::from_utf8(output.stdout)
                .context("Invalid UTF-8 in token")?
                .trim()
                .to_string();
                
            Ok(token)
        } else {
            Err(anyhow::anyhow!(
                "No authentication method available. Set GOOGLE_APPLICATION_CREDENTIALS or run on Cloud Run"
            ))
        }
    }

    // Circuit breaker implementation (reuse from previous services)
    async fn check_circuit_breaker(&self) -> Result<bool> {
        let mut state = self.circuit_state.lock().await;
        
        match *state {
            CircuitState::Closed => Ok(true),
            CircuitState::Open(reset_time) => {
                if Utc::now() > reset_time {
                    *state = CircuitState::HalfOpen;
                    tracing::info!("Documentation circuit breaker transitioning to half-open");
                    Ok(true)
                } else {
                    Ok(false)
                }
            }
            CircuitState::HalfOpen => Ok(true),
        }
    }

    async fn record_success(&self) {
        let mut state = self.circuit_state.lock().await;
        let mut failures = self.failure_count.lock().await;
        let mut metrics = self.metrics.lock().await;
        
        *failures = 0;
        metrics.successful_generations += 1;
        
        if matches!(*state, CircuitState::HalfOpen) {
            *state = CircuitState::Closed;
            tracing::info!("Documentation circuit breaker closed after successful request");
        }
    }

    async fn record_failure(&self) {
        let mut state = self.circuit_state.lock().await;
        let mut failures = self.failure_count.lock().await;
        let mut metrics = self.metrics.lock().await;
        
        *failures += 1;
        metrics.failed_generations += 1;
        
        if *failures >= self.failure_threshold {
            let reset_time = Utc::now() + chrono::Duration::from_std(self.reset_timeout)
                .unwrap_or_else(|_| chrono::Duration::seconds(300));
            *state = CircuitState::Open(reset_time);
            *failures = 0;
            metrics.circuit_breaker_opens += 1;
            
            tracing::error!(
                "Documentation circuit breaker opened after {} failures, will reset at {}",
                self.failure_threshold,
                reset_time
            );
        }
    }

    async fn record_fallback_use(&self) {
        let mut metrics = self.metrics.lock().await;
        metrics.fallback_uses += 1;
    }

    async fn record_response_time(&self, response_time_ms: f64) {
        let mut metrics = self.metrics.lock().await;
        metrics.total_generations += 1;
        
        let total_generations = metrics.total_generations as f64;
        metrics.average_response_time_ms = 
            ((metrics.average_response_time_ms * (total_generations - 1.0)) + response_time_ms) / total_generations;
    }

    fn is_retryable_error(&self, error: &anyhow::Error) -> bool {
        let error_str = error.to_string().to_lowercase();
        
        error_str.contains("timeout") ||
        error_str.contains("temporarily unavailable") ||
        error_str.contains("429") || // Rate limit
        error_str.contains("500") || // Internal server error
        error_str.contains("502") || // Bad gateway
        error_str.contains("503") || // Service unavailable
        error_str.contains("504")    // Gateway timeout
    }

    pub async fn get_metrics(&self) -> DocumentationMetrics {
        let metrics = self.metrics.lock().await;
        DocumentationMetrics {
            total_generations: metrics.total_generations,
            successful_generations: metrics.successful_generations,
            failed_generations: metrics.failed_generations,
            average_response_time_ms: metrics.average_response_time_ms,
            circuit_breaker_opens: metrics.circuit_breaker_opens,
            fallback_uses: metrics.fallback_uses,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::*;

    #[tokio::test]
    async fn test_fallback_documentation_creation() {
        let embeddings_service = Arc::new(
            EnhancedEmbeddingsService::new().await.unwrap()
        );
        let doc_service = IntelligentDocumentationService::new(embeddings_service).await.unwrap();
        
        let analysis = FileAnalysis {
            path: "src/main.rs".to_string(),
            language: "rust".to_string(),
            content_hash: "hash123".to_string(),
            size_bytes: Some(1000),
            ast: AstNode {
                node_type: "root".to_string(),
                name: None,
                range: Range {
                    start: Position { line: 0, column: 0, byte: 0 },
                    end: Position { line: 10, column: 0, byte: 100 },
                },
                children: vec![],
                properties: None,
                text: Some("fn main() { println!(\"Hello!\"); }".to_string()),
            },
            metrics: FileMetrics::default(),
            chunks: None,
            symbols: Some(vec![
                Symbol {
                    name: "main".to_string(),
                    symbol_type: SymbolType::Function,
                    range: Range {
                        start: Position { line: 0, column: 0, byte: 0 },
                        end: Position { line: 2, column: 0, byte: 50 },
                    },
                    visibility: Some(crate::models::SymbolVisibility::Public),
                    signature: Some("fn main()".to_string()),
                    documentation: None,
                },
                Symbol {
                    name: "Config".to_string(),
                    symbol_type: SymbolType::Type,
                    range: Range {
                        start: Position { line: 5, column: 0, byte: 60 },
                        end: Position { line: 8, column: 0, byte: 100 },
                    },
                    visibility: Some(crate::models::SymbolVisibility::Public),
                    signature: None,
                    documentation: None,
                }
            ]),
        };
        
        let documentation = doc_service.create_fallback_documentation(&[analysis], "https://github.com/user/repo", "rust");
        
        assert!(documentation.repository_overview.title.contains("repo"));
        assert_eq!(documentation.repository_overview.target_audience, "Developers");
        assert_eq!(documentation.api_documentation.modules.len(), 1);
        assert_eq!(documentation.api_documentation.functions.len(), 1);
        assert_eq!(documentation.api_documentation.types.len(), 1);
        assert_eq!(documentation.api_documentation.functions[0].name, "main");
        assert_eq!(documentation.api_documentation.types[0].name, "Config");
        assert!(documentation.metadata.confidence_score > 0.0);
        assert!(documentation.metadata.completeness_score > 0.0);
    }

    #[test]
    fn test_documentation_serialization() {
        let documentation = IntelligentDocumentation {
            repository_overview: RepositoryOverview {
                title: "Test Repository".to_string(),
                description: "Test description".to_string(),
                key_features: vec!["Feature 1".to_string()],
                target_audience: "Developers".to_string(),
                use_cases: vec!["Development".to_string()],
                quick_start: "Clone and build".to_string(),
                badges: vec![],
            },
            api_documentation: ApiDocumentation {
                modules: vec![],
                functions: vec![],
                classes: vec![],
                types: vec![],
                constants: vec![],
            },
            architecture_documentation: ArchitectureDocumentation {
                overview: "Test architecture".to_string(),
                architecture_style: "Modular".to_string(),
                components: vec![],
                data_flow: DataFlowDocumentation {
                    description: "Test data flow".to_string(),
                    flow_steps: vec![],
                    data_transformations: vec![],
                },
                design_decisions: vec![],
                diagrams: vec![],
            },
            code_explanations: vec![],
            usage_examples: vec![],
            setup_instructions: SetupInstructions {
                prerequisites: vec![],
                installation_steps: vec![],
                configuration: ConfigurationGuide {
                    description: "Test config".to_string(),
                    config_files: vec![],
                    environment_variables: vec![],
                    examples: vec![],
                },
                verification: VerificationSteps {
                    description: "Test verification".to_string(),
                    steps: vec![],
                },
                troubleshooting: vec![],
            },
            troubleshooting_guide: TroubleshootingGuide {
                common_issues: vec![],
                debugging_tips: vec![],
                support_resources: vec![],
            },
            contributing_guidelines: ContributingGuidelines {
                overview: "Test guidelines".to_string(),
                code_style: CodeStyleGuide {
                    description: "Test style".to_string(),
                    formatting_rules: vec![],
                    naming_conventions: vec![],
                    best_practices: vec![],
                    tools: vec![],
                },
                development_workflow: DevelopmentWorkflow {
                    description: "Test workflow".to_string(),
                    setup_steps: vec![],
                    branching_strategy: "Git flow".to_string(),
                    commit_conventions: vec![],
                    testing_workflow: vec![],
                },
                testing_requirements: TestingRequirements {
                    description: "Test requirements".to_string(),
                    test_types: vec![],
                    coverage_requirements: "80%".to_string(),
                    testing_tools: vec![],
                    test_writing_guidelines: vec![],
                },
                pull_request_process: PullRequestProcess {
                    description: "Test PR process".to_string(),
                    checklist: vec![],
                    review_process: vec![],
                    merge_requirements: vec![],
                },
                issue_reporting: IssueReporting {
                    description: "Test issue reporting".to_string(),
                    bug_report_template: "Template".to_string(),
                    feature_request_template: "Template".to_string(),
                    issue_labels: vec![],
                },
            },
            changelog: ChangelogSummary {
                description: "Test changelog".to_string(),
                recent_changes: vec![],
                version_history: vec![],
            },
            metadata: DocumentationMetadata {
                generation_method: "Test".to_string(),
                confidence_score: 85.0,
                completeness_score: 90.0,
                coverage_metrics: HashMap::new(),
                language_coverage: HashMap::new(),
                last_updated: Utc::now(),
                version: "1.0.0".to_string(),
            },
            timestamp: Utc::now(),
        };
        
        let json = serde_json::to_string(&documentation).unwrap();
        let deserialized: IntelligentDocumentation = serde_json::from_str(&json).unwrap();
        
        assert_eq!(documentation.repository_overview.title, deserialized.repository_overview.title);
        assert_eq!(documentation.metadata.confidence_score, deserialized.metadata.confidence_score);
        assert_eq!(documentation.architecture_documentation.architecture_style, deserialized.architecture_documentation.architecture_style);
    }
}