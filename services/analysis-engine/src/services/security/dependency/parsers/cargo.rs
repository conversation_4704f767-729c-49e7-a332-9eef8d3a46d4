//! Cargo package file parsers (Cargo.toml, Cargo.lock)

use crate::models::production::{DependencyInfo, UpdatePriority};
use crate::models::FileAnalysis;
use crate::errors::AnalysisResult;
use toml;

/// Parse Cargo.toml file
pub fn parse_cargo_toml(file_analysis: &FileAnalysis) -> AnalysisResult<Vec<DependencyInfo>> {
    let content = file_analysis.ast.text.clone().unwrap_or_default();
    
    if content.trim().is_empty() {
        return Ok(Vec::new());
    }
    
    let cargo_toml: toml::Value = toml::from_str(&content)?;
    
    let mut dependencies = Vec::new();
    
    // Extract regular dependencies
    if let Some(deps) = cargo_toml.get("dependencies").and_then(|d| d.as_table()) {
        for (name, value) in deps {
            let version = match value {
                // Simple string version (e.g., serde = "1.0")
                toml::Value::String(v) => v.clone(),
                // Table with version field (e.g., serde = { version = "1.0", features = ["derive"] })
                toml::Value::Table(t) => {
                    t.get("version")
                        .and_then(|v| v.as_str())
                        .unwrap_or("*")
                        .to_string()
                }
                _ => "*".to_string(),
            };
            
            dependencies.push(DependencyInfo {
                name: name.clone(),
                current_version: clean_cargo_version(&version),
                latest_version: String::new(),
                vulnerability_count: 0,
                update_priority: UpdatePriority::Minor,
                registry: Some("crates.io".to_string()),
                license: None,
                description: None,
                homepage: None,
                repository_url: None,
                is_direct: true,
                is_dev: false,
                is_optional: false,
                dependency_path: None,
            });
        }
    }
    
    // Extract dev dependencies
    if let Some(dev_deps) = cargo_toml.get("dev-dependencies").and_then(|d| d.as_table()) {
        for (name, value) in dev_deps {
            let version = match value {
                toml::Value::String(v) => v.clone(),
                toml::Value::Table(t) => {
                    t.get("version")
                        .and_then(|v| v.as_str())
                        .unwrap_or("*")
                        .to_string()
                }
                _ => "*".to_string(),
            };
            
            dependencies.push(DependencyInfo {
                name: name.clone(),
                current_version: clean_cargo_version(&version),
                latest_version: String::new(),
                vulnerability_count: 0,
                update_priority: UpdatePriority::Minor,
                registry: Some("crates.io".to_string()),
                license: None,
                description: None,
                homepage: None,
                repository_url: None,
                is_direct: true,
                is_dev: true,
                is_optional: false,
                dependency_path: None,
            });
        }
    }
    
    // Extract build dependencies
    if let Some(build_deps) = cargo_toml.get("build-dependencies").and_then(|d| d.as_table()) {
        for (name, value) in build_deps {
            let version = match value {
                toml::Value::String(v) => v.clone(),
                toml::Value::Table(t) => {
                    t.get("version")
                        .and_then(|v| v.as_str())
                        .unwrap_or("*")
                        .to_string()
                }
                _ => "*".to_string(),
            };
            
            dependencies.push(DependencyInfo {
                name: name.clone(),
                current_version: clean_cargo_version(&version),
                latest_version: String::new(),
                vulnerability_count: 0,
                update_priority: UpdatePriority::Minor,
                registry: Some("crates.io".to_string()),
                license: None,
                description: None,
                homepage: None,
                repository_url: None,
                is_direct: true,
                is_dev: true,
                is_optional: false,
                dependency_path: None,
            });
        }
    }
    
    // Extract workspace dependencies
    if let Some(workspace) = cargo_toml.get("workspace").and_then(|w| w.as_table()) {
        if let Some(deps) = workspace.get("dependencies").and_then(|d| d.as_table()) {
            for (name, value) in deps {
                let version = match value {
                    toml::Value::String(v) => v.clone(),
                    toml::Value::Table(t) => {
                        t.get("version")
                            .and_then(|v| v.as_str())
                            .unwrap_or("*")
                            .to_string()
                    }
                    _ => "*".to_string(),
                };
                
                dependencies.push(DependencyInfo {
                    name: name.clone(),
                    current_version: clean_cargo_version(&version),
                    latest_version: String::new(),
                    vulnerability_count: 0,
                    update_priority: UpdatePriority::Minor,
                    registry: Some("crates.io".to_string()),
                    license: None,
                    description: None,
                    homepage: None,
                    repository_url: None,
                    is_direct: true,
                    is_dev: false,
                    is_optional: false,
                    dependency_path: None,
                });
            }
        }
    }
    
    Ok(dependencies)
}

/// Parse Cargo.lock file
pub fn parse_cargo_lock(file_analysis: &FileAnalysis) -> AnalysisResult<Vec<DependencyInfo>> {
    let content = file_analysis.ast.text.clone().unwrap_or_default();
    
    if content.trim().is_empty() {
        return Ok(Vec::new());
    }
    
    let cargo_lock: toml::Value = toml::from_str(&content)?;
    
    let mut dependencies = Vec::new();
    
    // Extract all packages from the lock file
    if let Some(packages) = cargo_lock.get("package").and_then(|p| p.as_array()) {
        for package in packages {
            if let Some(package_table) = package.as_table() {
                if let (Some(name), Some(version)) = (
                    package_table.get("name").and_then(|n| n.as_str()),
                    package_table.get("version").and_then(|v| v.as_str())
                ) {
                    // Get source information if available
                    let registry = if let Some(source) = package_table.get("source").and_then(|s| s.as_str()) {
                        if source.starts_with("registry+") {
                            Some("crates.io".to_string())
                        } else if source.starts_with("git+") {
                            Some("git".to_string())
                        } else {
                            None
                        }
                    } else {
                        None
                    };
                    
                    dependencies.push(DependencyInfo {
                        name: name.to_string(),
                        current_version: version.to_string(),
                        latest_version: String::new(),
                        vulnerability_count: 0,
                        update_priority: UpdatePriority::Minor,
                        registry: registry.or_else(|| Some("crates.io".to_string())),
                        license: None,
                        description: None,
                        homepage: None,
                        repository_url: None,
                        is_direct: false,
                        is_dev: false,
                        is_optional: false,
                        dependency_path: None,
                    });
                }
            }
        }
    }
    
    Ok(dependencies)
}

/// Clean Cargo version string
fn clean_cargo_version(version: &str) -> String {
    // Cargo versions are typically already clean, but remove any extra whitespace
    version.trim().to_string()
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_clean_cargo_version() {
        assert_eq!(clean_cargo_version("1.0.0"), "1.0.0");
        assert_eq!(clean_cargo_version(" 2.3.4 "), "2.3.4");
        assert_eq!(clean_cargo_version("*"), "*");
    }
}