//! Dependency file parsers

mod npm;
mod python;
mod cargo;
mod maven;
mod gradle;
mod dotnet;
mod php;
mod go;
mod ruby;

// Re-export all parser functions
pub use npm::{parse_package_json, parse_package_lock_json, parse_yarn_lock};
pub use python::{parse_requirements_txt, parse_pipfile, parse_pyproject_toml};
pub use cargo::{parse_cargo_toml, parse_cargo_lock};
pub use maven::parse_pom_xml;
pub use gradle::parse_gradle_build;
pub use dotnet::{parse_packages_config, parse_dotnet_project};
pub use php::{parse_composer_json, parse_composer_lock};
pub use go::{parse_go_mod, parse_go_sum};
pub use ruby::{parse_gemfile, parse_gemfile_lock};