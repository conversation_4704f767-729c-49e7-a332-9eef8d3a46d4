//! PHP package file parsers (composer.json, composer.lock)

use crate::models::production::{DependencyInfo, UpdatePriority};
use crate::models::FileAnalysis;
use crate::errors::AnalysisResult;
use serde_json;

/// Parse composer.json file
pub fn parse_composer_json(file_analysis: &FileAnalysis) -> AnalysisResult<Vec<DependencyInfo>> {
    let content = file_analysis.ast.text.clone().unwrap_or_default();
    
    if content.trim().is_empty() {
        return Ok(Vec::new());
    }
    
    let composer_json: serde_json::Value = serde_json::from_str(&content)?;
    
    let mut dependencies = Vec::new();
    
    // Extract regular dependencies
    if let Some(require) = composer_json.get("require").and_then(|r| r.as_object()) {
        for (name, version) in require {
            // Skip PHP version requirement
            if name == "php" || name.starts_with("ext-") {
                continue;
            }
            
            if let Some(version_str) = version.as_str() {
                dependencies.push(DependencyInfo {
                    name: name.clone(),
                    current_version: clean_composer_version(version_str),
                    latest_version: String::new(),
                    vulnerability_count: 0,
                    update_priority: UpdatePriority::Minor,
                    registry: Some("packagist".to_string()),
                    license: None,
                    description: None,
                    homepage: None,
                    repository_url: None,
                    is_direct: true,
                    is_dev: false,
                    is_optional: false,
                    dependency_path: None,
                });
            }
        }
    }
    
    // Extract dev dependencies
    if let Some(require_dev) = composer_json.get("require-dev").and_then(|r| r.as_object()) {
        for (name, version) in require_dev {
            if let Some(version_str) = version.as_str() {
                dependencies.push(DependencyInfo {
                    name: name.clone(),
                    current_version: clean_composer_version(version_str),
                    latest_version: String::new(),
                    vulnerability_count: 0,
                    update_priority: UpdatePriority::Minor,
                    registry: Some("packagist".to_string()),
                    license: None,
                    description: None,
                    homepage: None,
                    repository_url: None,
                    is_direct: true,
                    is_dev: true,
                    is_optional: false,
                    dependency_path: None,
                });
            }
        }
    }
    
    Ok(dependencies)
}

/// Parse composer.lock file
pub fn parse_composer_lock(file_analysis: &FileAnalysis) -> AnalysisResult<Vec<DependencyInfo>> {
    let content = file_analysis.ast.text.clone().unwrap_or_default();
    
    if content.trim().is_empty() {
        return Ok(Vec::new());
    }
    
    let composer_lock: serde_json::Value = serde_json::from_str(&content)?;
    
    let mut dependencies = Vec::new();
    
    // Extract regular packages
    if let Some(packages) = composer_lock.get("packages").and_then(|p| p.as_array()) {
        for package in packages {
            if let Some(package_obj) = package.as_object() {
                if let (Some(name), Some(version)) = (
                    package_obj.get("name").and_then(|n| n.as_str()),
                    package_obj.get("version").and_then(|v| v.as_str())
                ) {
                    let license = package_obj.get("license")
                        .and_then(|l| {
                            if let Some(arr) = l.as_array() {
                                arr.get(0).and_then(|v| v.as_str()).map(|s| s.to_string())
                            } else {
                                l.as_str().map(|s| s.to_string())
                            }
                        });
                    
                    let description = package_obj.get("description")
                        .and_then(|d| d.as_str())
                        .map(|s| s.to_string());
                    
                    dependencies.push(DependencyInfo {
                        name: name.to_string(),
                        current_version: clean_composer_version(version),
                        latest_version: String::new(),
                        vulnerability_count: 0,
                        update_priority: UpdatePriority::Minor,
                        registry: Some("packagist".to_string()),
                        license,
                        description,
                        homepage: None,
                        repository_url: None,
                        is_direct: false,
                        is_dev: false,
                        is_optional: false,
                        dependency_path: None,
                    });
                }
            }
        }
    }
    
    // Extract dev packages
    if let Some(packages_dev) = composer_lock.get("packages-dev").and_then(|p| p.as_array()) {
        for package in packages_dev {
            if let Some(package_obj) = package.as_object() {
                if let (Some(name), Some(version)) = (
                    package_obj.get("name").and_then(|n| n.as_str()),
                    package_obj.get("version").and_then(|v| v.as_str())
                ) {
                    let license = package_obj.get("license")
                        .and_then(|l| {
                            if let Some(arr) = l.as_array() {
                                arr.get(0).and_then(|v| v.as_str()).map(|s| s.to_string())
                            } else {
                                l.as_str().map(|s| s.to_string())
                            }
                        });
                    
                    let description = package_obj.get("description")
                        .and_then(|d| d.as_str())
                        .map(|s| s.to_string());
                    
                    dependencies.push(DependencyInfo {
                        name: name.to_string(),
                        current_version: clean_composer_version(version),
                        latest_version: String::new(),
                        vulnerability_count: 0,
                        update_priority: UpdatePriority::Minor,
                        registry: Some("packagist".to_string()),
                        license,
                        description,
                        homepage: None,
                        repository_url: None,
                        is_direct: false,
                        is_dev: true,
                        is_optional: false,
                        dependency_path: None,
                    });
                }
            }
        }
    }
    
    Ok(dependencies)
}

/// Clean Composer version string
fn clean_composer_version(version: &str) -> String {
    // Remove common version prefixes
    version
        .trim()
        .trim_start_matches('v')
        .trim_start_matches('^')
        .trim_start_matches('~')
        .trim_start_matches('>')
        .trim_start_matches('<')
        .trim_start_matches('=')
        .to_string()
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_clean_composer_version() {
        assert_eq!(clean_composer_version("^1.0.0"), "1.0.0");
        assert_eq!(clean_composer_version("~2.3"), "2.3");
        assert_eq!(clean_composer_version(">=3.0"), "3.0");
        assert_eq!(clean_composer_version("v4.5.6"), "4.5.6");
    }
}