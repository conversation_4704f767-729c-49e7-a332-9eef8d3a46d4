//! Gradle build file parser (build.gradle, build.gradle.kts)

use crate::models::production::{DependencyInfo, UpdatePriority};
use crate::models::FileAnalysis;
use crate::errors::AnalysisResult;
use lazy_static::lazy_static;
use regex::Regex;

// Pre-compiled regex patterns for Gradle Kotlin DSL
lazy_static! {
    static ref KOTLIN_PATTERNS: Vec<(Regex, &'static str)> = vec![
        (Regex::new(r#"implementation\s*\(\s*"([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^"]+)"\s*\)"#).unwrap(), "implementation"),
        (Regex::new(r#"testImplementation\s*\(\s*"([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^"]+)"\s*\)"#).unwrap(), "test"),
        (Regex::new(r#"api\s*\(\s*"([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^"]+)"\s*\)"#).unwrap(), "api"),
        (Regex::new(r#"compileOnly\s*\(\s*"([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^"]+)"\s*\)"#).unwrap(), "compileOnly"),
        (Regex::new(r#"runtimeOnly\s*\(\s*"([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^"]+)"\s*\)"#).unwrap(), "runtime"),
    ];
}

// Pre-compiled regex patterns for Gradle Groovy DSL
lazy_static! {
    static ref GROOVY_PATTERNS: Vec<(Regex, &'static str)> = vec![
        (Regex::new(r#"implementation\s+['"]([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^'"]+)['"]"#).unwrap(), "implementation"),
        (Regex::new(r#"testImplementation\s+['"]([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^'"]+)['"]"#).unwrap(), "test"),
        (Regex::new(r#"api\s+['"]([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^'"]+)['"]"#).unwrap(), "api"),
        (Regex::new(r#"compile\s+['"]([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^'"]+)['"]"#).unwrap(), "compile"),
        (Regex::new(r#"compileOnly\s+['"]([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^'"]+)['"]"#).unwrap(), "compileOnly"),
        (Regex::new(r#"runtime\s+['"]([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^'"]+)['"]"#).unwrap(), "runtime"),
    ];
}

// Pre-compiled platform dependency patterns
lazy_static! {
    static ref KOTLIN_PLATFORM_PATTERNS: Vec<Regex> = vec![
        Regex::new(r#"implementation\s*\(\s*platform\s*\(\s*"([^"]+)"\s*\)\s*\)"#).unwrap(),
        Regex::new(r#"implementation\s*\(\s*enforcedPlatform\s*\(\s*"([^"]+)"\s*\)\s*\)"#).unwrap(),
    ];
    
    static ref GROOVY_PLATFORM_PATTERNS: Vec<Regex> = vec![
        Regex::new(r#"implementation\s+platform\s*\(\s*['"]([^'"]+)['"]\s*\)"#).unwrap(),
        Regex::new(r#"implementation\s+enforcedPlatform\s*\(\s*['"]([^'"]+)['"]\s*\)"#).unwrap(),
    ];
}

/// Parse build.gradle or build.gradle.kts file
pub fn parse_gradle_build(file_analysis: &FileAnalysis) -> AnalysisResult<Vec<DependencyInfo>> {
    let content = file_analysis.ast.text.clone().unwrap_or_default();
    
    if content.trim().is_empty() {
        return Ok(Vec::new());
    }
    
    let mut dependencies = Vec::new();
    let is_kotlin = file_analysis.path.ends_with(".kts");

    let patterns: &Vec<(Regex, &str)> = if is_kotlin { &KOTLIN_PATTERNS } else { &GROOVY_PATTERNS };
    for (regex, scope) in patterns {
        for captures in regex.captures_iter(&content) {
            if let (Some(group), Some(artifact), Some(version)) = (
                captures.get(1).map(|m| m.as_str()),
                captures.get(2).map(|m| m.as_str()),
                captures.get(3).map(|m| m.as_str()),
            ) {
                let name = format!("{}:{}", group, artifact);
                let is_dev = *scope == "test";
                
                dependencies.push(DependencyInfo {
                    name,
                    current_version: clean_gradle_version(version),
                    latest_version: String::new(),
                    vulnerability_count: 0,
                    update_priority: UpdatePriority::Minor,
                    registry: Some("maven".to_string()),
                    license: None,
                    description: None,
                    homepage: None,
                    repository_url: None,
                    is_direct: true,
                    is_dev,
                    is_optional: false,
                    dependency_path: None,
                });
            }
        }
    }

    let platform_patterns: &Vec<Regex> = if is_kotlin { &KOTLIN_PLATFORM_PATTERNS } else { &GROOVY_PLATFORM_PATTERNS };
    for regex in platform_patterns {
        for captures in regex.captures_iter(&content) {
            if let Some(dep) = captures.get(1).map(|m| m.as_str()) {
                let parts: Vec<&str> = dep.split(':').collect();
                if parts.len() >= 3 {
                    let name = format!("{}:{}", parts[0], parts[1]);
                    let version = parts[2];
                    
                    dependencies.push(DependencyInfo {
                        name,
                        current_version: clean_gradle_version(version),
                        latest_version: String::new(),
                        vulnerability_count: 0,
                        update_priority: UpdatePriority::Minor,
                        registry: Some("maven".to_string()),
                        license: None,
                        description: None,
                        homepage: None,
                        repository_url: None,
                        is_direct: true,
                        is_dev: false,
                        is_optional: false,
                        dependency_path: None,
                    });
                }
            }
        }
    }
    
    Ok(dependencies)
}

/// Clean Gradle version string
fn clean_gradle_version(version: &str) -> String {
    // Remove variable placeholders if any
    if version.starts_with('$') {
        return version.to_string(); // Keep as is for variable references
    }
    
    version.trim().to_string()
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_clean_gradle_version() {
        assert_eq!(clean_gradle_version("1.0.0"), "1.0.0");
        assert_eq!(clean_gradle_version("$springVersion"), "$springVersion");
        assert_eq!(clean_gradle_version(" 2.3.4 "), "2.3.4");
    }
}