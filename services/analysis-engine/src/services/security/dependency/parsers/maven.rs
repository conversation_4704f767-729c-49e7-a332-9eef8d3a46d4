//! Maven package file parser (pom.xml)

use crate::models::production::{DependencyInfo, UpdatePriority};
use crate::models::FileAnalysis;
use crate::errors::AnalysisResult;
use quick_xml::events::Event;
use quick_xml::reader::Reader;

/// Create a default DependencyInfo with proper field types
fn default_dependency_info() -> DependencyInfo {
    DependencyInfo {
        name: String::new(),
        current_version: String::new(),
        latest_version: String::new(),
        vulnerability_count: 0,
        update_priority: UpdatePriority::Minor,
        repository_url: None,
        registry: None,
        license: None,
        homepage: None,
        description: None,
        dependency_path: None,
        is_optional: false,
        is_direct: true,
        is_dev: false,
    }
}

/// Parse pom.xml file
pub fn parse_pom_xml(file_analysis: &FileAnalysis) -> AnalysisResult<Vec<DependencyInfo>> {
    let content = file_analysis.ast.text.clone().unwrap_or_default();
    
    if content.trim().is_empty() {
        return Ok(Vec::new());
    }
    
    let mut reader = Reader::from_str(&content);
    reader.trim_text(true);
    
    let mut dependencies = Vec::new();
    let mut in_dependencies = false;
    let mut in_dependency = false;
    let mut current_dependency = default_dependency_info();
    let mut current_element = String::new();
    let mut is_optional = false;
    let mut scope = String::new();
    
    loop {
        match reader.read_event() {
            Ok(Event::Start(ref e)) => {
                current_element = String::from_utf8_lossy(e.name().as_ref()).to_string();
                
                match e.name().as_ref() {
                    b"dependencies" => in_dependencies = true,
                    b"dependency" if in_dependencies => {
                        in_dependency = true;
                        current_dependency = default_dependency_info();
                        is_optional = false;
                        scope.clear();
                    }
                    _ => {}
                }
            }
            Ok(Event::Text(ref e)) => {
                if in_dependency {
                    let text = e.unescape().unwrap_or_default().to_string();
                    match current_element.as_str() {
                        "groupId" => current_dependency.name = text,
                        "artifactId" => {
                            if !current_dependency.name.is_empty() {
                                current_dependency.name = format!("{}:{}", current_dependency.name, text);
                            } else {
                                current_dependency.name = text;
                            }
                        }
                        "version" => current_dependency.current_version = clean_maven_version(&text),
                        "optional" => is_optional = text.to_lowercase() == "true",
                        "scope" => scope = text,
                        _ => {}
                    }
                }
            }
            Ok(Event::End(ref e)) => {
                match e.name().as_ref() {
                    b"dependencies" => in_dependencies = false,
                    b"dependency" if in_dependency => {
                        in_dependency = false;
                        
                        // Only add if we have the required fields
                        if !current_dependency.name.is_empty() && !current_dependency.current_version.is_empty() {
                            current_dependency.latest_version = String::new();
                            current_dependency.vulnerability_count = 0;
                            current_dependency.update_priority = UpdatePriority::Minor;
                            current_dependency.registry = Some("maven".to_string());
                            current_dependency.is_direct = true;
                            current_dependency.is_dev = scope == "test";
                            current_dependency.is_optional = is_optional;
                            
                            dependencies.push(current_dependency.clone());
                        }
                    }
                    _ => {}
                }
            }
            Ok(Event::Eof) => break,
            Err(e) => {
                tracing::error!("Error parsing pom.xml: {}", e);
                break;
            }
            _ => {}
        }
    }
    
    Ok(dependencies)
}

/// Clean Maven version string
fn clean_maven_version(version: &str) -> String {
    // Remove property placeholders if any
    if version.starts_with("${") && version.ends_with('}') {
        return version.to_string(); // Keep as is for property references
    }
    
    // Clean up version ranges
    version
        .trim()
        .trim_start_matches('[')
        .trim_start_matches('(')
        .trim_end_matches(']')
        .trim_end_matches(')')
        .to_string()
}

impl Default for DependencyInfo {
    fn default() -> Self {
        Self {
            name: String::new(),
            current_version: String::new(),
            latest_version: String::new(),
            vulnerability_count: 0,
            update_priority: UpdatePriority::Minor,
            registry: None,
            license: None,
            description: None,
            homepage: None,
            repository_url: None,
            is_direct: true,
            is_dev: false,
            is_optional: false,
            dependency_path: None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_clean_maven_version() {
        assert_eq!(clean_maven_version("1.0.0"), "1.0.0");
        assert_eq!(clean_maven_version("[1.0.0,2.0.0)"), "1.0.0,2.0.0");
        assert_eq!(clean_maven_version("${spring.version}"), "${spring.version}");
    }
}