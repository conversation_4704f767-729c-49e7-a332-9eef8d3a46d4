//! Dependency vulnerability checking and database

use crate::models::security::SecuritySeverity;
use std::collections::HashMap;
use chrono::{DateTime, Utc};

/// Information about a known vulnerability in a dependency
#[derive(Debug, Clone)]
pub struct DependencyVulnInfo {
    pub cve_id: Option<String>,
    pub severity: SecuritySeverity,
    pub description: String,
    pub affected_versions: String,
    pub patched_versions: String,
    pub cvss_score: Option<f64>,
    pub cvss_vector: Option<String>,
    pub published_date: Option<DateTime<Utc>>,
    pub last_modified_date: Option<DateTime<Utc>>,
    pub workaround: Option<String>,
    pub exploit_available: bool,
    pub proof_of_concept_available: bool,
}

/// Load the vulnerability database
pub fn load_vulnerability_db() -> HashMap<String, Vec<DependencyVulnInfo>> {
    // In a real application, this would be loaded from a database or API
    let mut db = HashMap::new();
    
    // Log4j vulnerability
    db.insert(
        "log4j:log4j".to_string(),
        vec![DependencyVulnInfo {
            cve_id: Some("CVE-2021-44228".to_string()),
            severity: SecuritySeverity::Critical,
            description: "Remote code execution in Log4j".to_string(),
            affected_versions: ">=2.0, <2.15.0".to_string(),
            patched_versions: ">=2.15.0".to_string(),
            cvss_score: Some(10.0),
            cvss_vector: Some("CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:H/A:H".to_string()),
            published_date: Some(DateTime::parse_from_rfc3339("2021-12-10T00:00:00Z").unwrap().with_timezone(&Utc)),
            last_modified_date: Some(DateTime::parse_from_rfc3339("2021-12-14T00:00:00Z").unwrap().with_timezone(&Utc)),
            workaround: Some("Disable JNDI lookup".to_string()),
            exploit_available: true,
            proof_of_concept_available: true,
        }],
    );
    
    // Lodash vulnerability
    db.insert(
        "lodash".to_string(),
        vec![DependencyVulnInfo {
            cve_id: Some("CVE-2019-10744".to_string()),
            severity: SecuritySeverity::High,
            description: "Prototype pollution in lodash".to_string(),
            affected_versions: "<4.17.12".to_string(),
            patched_versions: ">=4.17.12".to_string(),
            cvss_score: Some(9.1),
            cvss_vector: Some("CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:N".to_string()),
            published_date: Some(DateTime::parse_from_rfc3339("2019-07-26T00:00:00Z").unwrap().with_timezone(&Utc)),
            last_modified_date: Some(DateTime::parse_from_rfc3339("2020-09-30T00:00:00Z").unwrap().with_timezone(&Utc)),
            workaround: None,
            exploit_available: true,
            proof_of_concept_available: true,
        }],
    );
    
    // Jackson-databind vulnerability
    db.insert(
        "com.fasterxml.jackson.core:jackson-databind".to_string(),
        vec![DependencyVulnInfo {
            cve_id: Some("CVE-2020-36518".to_string()),
            severity: SecuritySeverity::High,
            description: "Unsafe deserialization in jackson-databind".to_string(),
            affected_versions: ">=2.0.0, <********".to_string(),
            patched_versions: ">=********".to_string(),
            cvss_score: Some(7.5),
            cvss_vector: Some("CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H".to_string()),
            published_date: Some(DateTime::parse_from_rfc3339("2022-03-11T00:00:00Z").unwrap().with_timezone(&Utc)),
            last_modified_date: Some(DateTime::parse_from_rfc3339("2022-04-06T00:00:00Z").unwrap().with_timezone(&Utc)),
            workaround: Some("Disable default typing".to_string()),
            exploit_available: false,
            proof_of_concept_available: true,
        }],
    );
    
    // Django vulnerability
    db.insert(
        "Django".to_string(),
        vec![DependencyVulnInfo {
            cve_id: Some("CVE-2021-33203".to_string()),
            severity: SecuritySeverity::Medium,
            description: "Path traversal vulnerability in Django".to_string(),
            affected_versions: ">=2.2, <2.2.24".to_string(),
            patched_versions: ">=2.2.24".to_string(),
            cvss_score: Some(4.9),
            cvss_vector: Some("CVSS:3.1/AV:N/AC:L/PR:H/UI:N/S:U/C:H/I:N/A:N".to_string()),
            published_date: Some(DateTime::parse_from_rfc3339("2021-06-08T00:00:00Z").unwrap().with_timezone(&Utc)),
            last_modified_date: Some(DateTime::parse_from_rfc3339("2021-06-21T00:00:00Z").unwrap().with_timezone(&Utc)),
            workaround: None,
            exploit_available: false,
            proof_of_concept_available: false,
        }],
    );
    
    // Spring Framework vulnerability
    db.insert(
        "org.springframework:spring-beans".to_string(),
        vec![DependencyVulnInfo {
            cve_id: Some("CVE-2022-22965".to_string()),
            severity: SecuritySeverity::Critical,
            description: "Spring4Shell - Remote Code Execution".to_string(),
            affected_versions: ">=5.3.0, <5.3.18".to_string(),
            patched_versions: ">=5.3.18".to_string(),
            cvss_score: Some(9.8),
            cvss_vector: Some("CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H".to_string()),
            published_date: Some(DateTime::parse_from_rfc3339("2022-04-01T00:00:00Z").unwrap().with_timezone(&Utc)),
            last_modified_date: Some(DateTime::parse_from_rfc3339("2022-05-12T00:00:00Z").unwrap().with_timezone(&Utc)),
            workaround: Some("Upgrade to Java 9+".to_string()),
            exploit_available: true,
            proof_of_concept_available: true,
        }],
    );
    
    db
}

/// Check if a specific version is affected by vulnerabilities
pub fn check_version_vulnerability<'a>(
    package_name: &str,
    version: &str,
    vulnerability_db: &'a HashMap<String, Vec<DependencyVulnInfo>>
) -> Vec<&'a DependencyVulnInfo> {
    let mut matching_vulns = Vec::new();
    
    if let Some(vulns) = vulnerability_db.get(package_name) {
        for vuln in vulns {
            if version_matches(&vuln.affected_versions, version) {
                matching_vulns.push(vuln);
            }
        }
    }
    
    matching_vulns
}

/// Simple version matching - in production use proper semver comparison
fn version_matches(version_range: &str, version: &str) -> bool {
    // This is a simplified version matching
    // In production, use a proper semver library
    
    // Handle wildcard
    if version_range.contains("*") || version_range.is_empty() {
        return true;
    }
    
    // Handle simple exact match
    if version_range == version {
        return true;
    }
    
    // Handle comma-separated ranges like ">=2.0, <2.15.0"
    if version_range.contains(',') {
        // All constraints must be satisfied
        return version_range.split(',')
            .all(|part| {
                let part = part.trim();
                // Very basic range checking
                if part.starts_with(">=") {
                    let min_version = part.trim_start_matches(">=").trim();
                    // Simple string comparison works for basic version numbers
                    version >= min_version
                } else if part.starts_with("<") {
                    let max_version = part.trim_start_matches("<").trim();
                    // Simple string comparison
                    version < max_version
                } else {
                    part == version
                }
            });
    }
    
    // Default: check if version is contained in range string
    version_range.contains(version)
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_vulnerability_db_loading() {
        let db = load_vulnerability_db();
        assert!(!db.is_empty());
        assert!(db.contains_key("log4j:log4j"));
        assert!(db.contains_key("lodash"));
    }
    
    #[test]
    fn test_version_matching() {
        assert!(version_matches(">=2.0, <2.15.0", "2.14.0"));
        assert!(!version_matches(">=2.0, <2.15.0", "2.15.0"));
        assert!(version_matches("*", "any.version"));
    }
}