//! Core compliance checking logic

use super::rules::{ComplianceRule, load_compliance_rules};
use crate::models::security::{
    ComplianceViolation, ComplianceFramework, SecuritySeverity, RiskRating,
};
use crate::models::FileAnalysis;
use crate::errors::AnalysisResult;
use std::collections::HashMap;
use uuid::Uuid;
use chrono::Utc;

/// Checks code against compliance frameworks
pub struct ComplianceChecker {
    rules: HashMap<ComplianceFramework, Vec<ComplianceRule>>,
}

impl ComplianceChecker {
    pub fn new() -> Self {
        Self {
            rules: load_compliance_rules(),
        }
    }

    pub async fn check_compliance(
        &self,
        analysis_id: &str,
        file_analyses: &[FileAnalysis],
        framework: &ComplianceFramework,
    ) -> AnalysisResult<Vec<ComplianceViolation>> {
        let mut violations = Vec::new();
        
        if let Some(rules) = self.rules.get(framework) {
            for file_analysis in file_analyses {
                let content = file_analysis.ast.text.clone().unwrap_or_default();
                
                for rule in rules {
                    let matches = rule.find_violations(&content);
                    
                    for (line_number, line_content) in matches {
                        // Skip if this looks like a false positive
                        if self.is_likely_false_positive(&rule, &line_content) {
                            continue;
                        }
                        
                        violations.push(ComplianceViolation {
                            violation_id: Uuid::new_v4().to_string(),
                            analysis_id: analysis_id.to_string(),
                            compliance_framework: framework.clone(),
                            rule_id: rule.id.clone(),
                            rule_name: rule.name.clone(),
                            violation_type: rule.category.clone(),
                            severity: rule.severity.clone(),
                            file_path: Some(file_analysis.path.clone()),
                            line_number: Some(line_number as i64),
                            description: rule.description.clone(),
                            remediation_guidance: rule.remediation.clone(),
                            compliance_category: Some(rule.category.clone()),
                            risk_rating: self.calculate_risk_rating(&rule.severity),
                            business_impact: rule.business_impact.clone(),
                            technical_debt_hours: rule.technical_debt_hours,
                            created_at: Utc::now(),
                            updated_at: None,
                        });
                    }
                }
            }
        }
        
        Ok(violations)
    }

    fn is_likely_false_positive(&self, rule: &ComplianceRule, line: &str) -> bool {
        // Skip comments
        let trimmed = line.trim();
        if trimmed.starts_with("//") || trimmed.starts_with("/*") || trimmed.starts_with('#') {
            return true;
        }
        
        // Skip test data
        let line_lower = line.to_lowercase();
        if line_lower.contains("test") || 
           line_lower.contains("example") ||
           line_lower.contains("sample") ||
           line_lower.contains("demo") {
            return true;
        }
        
        // Rule-specific false positive checks
        match rule.id.as_str() {
            "PCI-DSS-3.2" => {
                // For PAN detection, check if it's likely a fake number
                line.contains("1234567890123456") || line.contains("0000000000000000")
            }
            _ => false,
        }
    }

    fn calculate_risk_rating(&self, severity: &SecuritySeverity) -> RiskRating {
        match severity {
            SecuritySeverity::Critical => RiskRating::Critical,
            SecuritySeverity::High => RiskRating::High,
            SecuritySeverity::Medium => RiskRating::Medium,
            SecuritySeverity::Low => RiskRating::Low,
            SecuritySeverity::Info => RiskRating::Low,
        }
    }
}

impl Default for ComplianceChecker {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_compliance_checker_creation() {
        let checker = ComplianceChecker::new();
        assert!(!checker.rules.is_empty());
    }
    
    #[test]
    fn test_risk_rating_calculation() {
        let checker = ComplianceChecker::new();
        assert!(matches!(
            checker.calculate_risk_rating(&SecuritySeverity::Critical),
            RiskRating::Critical
        ));
        assert!(matches!(
            checker.calculate_risk_rating(&SecuritySeverity::Low),
            RiskRating::Low
        ));
    }
}