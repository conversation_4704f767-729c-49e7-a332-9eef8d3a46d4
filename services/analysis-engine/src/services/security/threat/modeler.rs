//! Threat modeling logic

use crate::models::security::{
    ThreatModel, ThreatCategory, SecurityVulnerability, DependencyVulnerability,
    SecurityAssessment, SecuritySeverity, Likelihood, Impact, MitigationStatus,
    ThreatActor,
};
use crate::errors::AnalysisResult;
use uuid::Uuid;
use chrono::Utc;

/// Generates threat models based on detected vulnerabilities
pub struct ThreatModeler {
    #[allow(dead_code)]
    threat_patterns: Vec<ThreatPattern>,
}

#[derive(Debug)]
struct ThreatPattern {
    #[allow(dead_code)]
    name: String,
    #[allow(dead_code)]
    category: ThreatCategory,
    #[allow(dead_code)]
    severity_threshold: SecuritySeverity,
    #[allow(dead_code)]
    base_likelihood: Likelihood,
    #[allow(dead_code)]
    base_impact: Impact,
}

impl ThreatModeler {
    pub fn new() -> Self {
        Self {
            threat_patterns: Self::load_threat_patterns(),
        }
    }

    pub async fn generate_threat_models(
        &self,
        analysis_id: &str,
        vulnerabilities: &[SecurityVulnerability],
        dependency_vulnerabilities: &[DependencyVulnerability],
        security_assessment: &SecurityAssessment,
    ) -> AnalysisResult<Vec<ThreatModel>> {
        let mut threat_models = Vec::new();

        // Generate threats from application vulnerabilities
        threat_models.extend(self.generate_app_vulnerability_threats(
            analysis_id,
            vulnerabilities,
        ));

        // Generate threats from dependency vulnerabilities
        threat_models.extend(self.generate_dependency_threats(
            analysis_id,
            dependency_vulnerabilities,
        ));

        // Generate threats based on security assessment
        threat_models.extend(self.generate_assessment_based_threats(
            analysis_id,
            security_assessment,
        ));

        // Deduplicate and prioritize threats
        self.prioritize_threats(&mut threat_models);

        Ok(threat_models)
    }

    fn generate_app_vulnerability_threats(
        &self,
        analysis_id: &str,
        vulnerabilities: &[SecurityVulnerability],
    ) -> Vec<ThreatModel> {
        let mut threats = Vec::new();

        // Group vulnerabilities by type for better threat modeling
        let mut vuln_groups: std::collections::HashMap<String, Vec<&SecurityVulnerability>> = 
            std::collections::HashMap::new();

        for vuln in vulnerabilities.iter().filter(|v| v.severity >= SecuritySeverity::Medium) {
            let key = format!("{:?}", vuln.vulnerability_type);
            vuln_groups.entry(key).or_insert_with(Vec::new).push(vuln);
        }

        // Create threat models for each vulnerability group
        for (vuln_type, vulns) in vuln_groups {
            let severity = vulns.iter()
                .map(|v| &v.severity)
                .max()
                .cloned()
                .unwrap_or(SecuritySeverity::Medium);

            let threat_category = self.map_vuln_type_to_threat_category(&vuln_type);
            let (likelihood, impact) = self.calculate_threat_metrics(&severity, vulns.len());

            threats.push(ThreatModel {
                threat_model_id: Uuid::new_v4().to_string(),
                analysis_id: analysis_id.to_string(),
                threat_name: format!("{} Exploitation Threat", vuln_type),
                threat_description: self.generate_threat_description(&vuln_type, vulns.len()),
                threat_category,
                threat_actor: self.identify_threat_actor(&severity),
                attack_vector: vulns.first()
                    .and_then(|v| v.attack_vector.clone())
                    .or_else(|| Some("Application Layer".to_string())),
                asset_affected: Some(format!("{} files", vulns.len())),
                likelihood: likelihood.clone(),
                impact: impact.clone(),
                risk_score: self.calculate_risk_score(&likelihood, &impact),
                mitigation_status: MitigationStatus::NotMitigated,
                mitigation_strategy: Some(self.generate_mitigation_strategy(&vuln_type)),
                mitigation_measures: self.generate_mitigation_measures(&vuln_type, &vulns),
                residual_risk_score: None,
                associated_vulnerabilities: vulns.iter()
                    .map(|v| v.vulnerability_id.clone())
                    .collect(),
                exploit_likelihood: Some(self.estimate_exploit_likelihood(&severity)),
                business_impact: self.estimate_business_impact(&impact),
                created_at: Utc::now(),
                updated_at: None,
            });
        }

        threats
    }

    fn generate_dependency_threats(
        &self,
        analysis_id: &str,
        dependency_vulnerabilities: &[DependencyVulnerability],
    ) -> Vec<ThreatModel> {
        let mut threats = Vec::new();

        // Group by severity for aggregated threat modeling
        let mut severity_groups: std::collections::HashMap<SecuritySeverity, Vec<&DependencyVulnerability>> = 
            std::collections::HashMap::new();

        for dep_vuln in dependency_vulnerabilities.iter().filter(|v| v.severity >= SecuritySeverity::Medium) {
            severity_groups.entry(dep_vuln.severity.clone()).or_insert_with(Vec::new).push(dep_vuln);
        }

        for (severity, vulns) in severity_groups {
            let avg_cvss = vulns.iter()
                .filter_map(|v| v.cvss_score)
                .sum::<f64>() / vulns.len() as f64;

            let (likelihood, impact) = self.calculate_threat_metrics(&severity, vulns.len());

            threats.push(ThreatModel {
                threat_model_id: Uuid::new_v4().to_string(),
                analysis_id: analysis_id.to_string(),
                threat_name: format!("Supply Chain Attack via {} Dependencies", severity),
                threat_description: format!(
                    "Potential exploitation of {} {:?} severity vulnerabilities in third-party dependencies",
                    vulns.len(), severity
                ),
                threat_category: ThreatCategory::SupplyChainAttack,
                threat_actor: Some(ThreatActor::External),
                attack_vector: Some("Dependency Exploitation".to_string()),
                asset_affected: Some(format!("{} dependencies", vulns.len())),
                likelihood,
                impact,
                risk_score: avg_cvss,
                mitigation_status: MitigationStatus::NotMitigated,
                mitigation_strategy: Some("Implement dependency management and security scanning".to_string()),
                mitigation_measures: vec![
                    "Update vulnerable dependencies to patched versions".to_string(),
                    "Implement automated dependency scanning in CI/CD".to_string(),
                    "Use Software Bill of Materials (SBOM) for tracking".to_string(),
                ],
                residual_risk_score: None,
                associated_vulnerabilities: vulns.iter()
                    .map(|v| v.dependency_vuln_id.clone())
                    .collect(),
                exploit_likelihood: Some(if avg_cvss > 8.0 { 0.8 } else if avg_cvss > 6.0 { 0.6 } else { 0.4 }),
                business_impact: Some("Potential system compromise through vulnerable dependencies".to_string()),
                created_at: Utc::now(),
                updated_at: None,
            });
        }

        threats
    }

    fn generate_assessment_based_threats(
        &self,
        analysis_id: &str,
        assessment: &SecurityAssessment,
    ) -> Vec<ThreatModel> {
        let mut threats = Vec::new();

        // Generate threats based on security score thresholds
        if assessment.overall_security_score < 50.0 {
            threats.push(ThreatModel {
                threat_model_id: Uuid::new_v4().to_string(),
                analysis_id: analysis_id.to_string(),
                threat_name: "Overall Security Posture Weakness".to_string(),
                threat_description: "Low overall security score indicates systemic security weaknesses".to_string(),
                threat_category: ThreatCategory::SecurityMisconfiguration,
                threat_actor: Some(ThreatActor::External),
                attack_vector: Some("Multiple Attack Vectors".to_string()),
                asset_affected: Some("Entire Application".to_string()),
                likelihood: Likelihood::High,
                impact: Impact::High,
                risk_score: 8.0,
                mitigation_status: MitigationStatus::NotMitigated,
                mitigation_strategy: Some("Comprehensive security improvement program".to_string()),
                mitigation_measures: vec![
                    "Conduct thorough security audit".to_string(),
                    "Implement security development lifecycle".to_string(),
                    "Regular security training for developers".to_string(),
                ],
                residual_risk_score: None,
                associated_vulnerabilities: Vec::new(),
                exploit_likelihood: Some(0.7),
                business_impact: Some("High risk of security breach and data compromise".to_string()),
                created_at: Utc::now(),
                updated_at: None,
            });
        }

        threats
    }

    fn map_vuln_type_to_threat_category(&self, vuln_type: &str) -> ThreatCategory {
        match vuln_type {
            t if t.contains("Injection") => ThreatCategory::DataBreach,
            t if t.contains("Authentication") => ThreatCategory::UnauthorizedAccess,
            t if t.contains("Deserialization") => ThreatCategory::RemoteCodeExecution,
            t if t.contains("Misconfiguration") => ThreatCategory::SecurityMisconfiguration,
            _ => ThreatCategory::ApplicationVulnerability,
        }
    }

    fn calculate_threat_metrics(&self, severity: &SecuritySeverity, count: usize) -> (Likelihood, Impact) {
        let likelihood = match (severity, count) {
            (SecuritySeverity::Critical, _) => Likelihood::VeryHigh,
            (SecuritySeverity::High, n) if n > 5 => Likelihood::VeryHigh,
            (SecuritySeverity::High, _) => Likelihood::High,
            (SecuritySeverity::Medium, n) if n > 10 => Likelihood::High,
            (SecuritySeverity::Medium, _) => Likelihood::Medium,
            _ => Likelihood::Low,
        };

        let impact = match severity {
            SecuritySeverity::Critical => Impact::Critical,
            SecuritySeverity::High => Impact::High,
            SecuritySeverity::Medium => Impact::Medium,
            _ => Impact::Low,
        };

        (likelihood, impact)
    }

    fn calculate_risk_score(&self, likelihood: &Likelihood, impact: &Impact) -> f64 {
        let likelihood_score = match likelihood {
            Likelihood::VeryHigh => 1.0,
            Likelihood::High => 0.8,
            Likelihood::Medium => 0.5,
            Likelihood::Low => 0.3,
            Likelihood::VeryLow => 0.1,
        };

        let impact_score = match impact {
            Impact::Critical => 10.0,
            Impact::VeryHigh => 9.0,
            Impact::High => 8.0,
            Impact::Medium => 5.0,
            Impact::Low => 3.0,
            Impact::VeryLow => 2.0,
            Impact::Minimal => 1.0,
        };

        likelihood_score * impact_score
    }

    fn identify_threat_actor(&self, severity: &SecuritySeverity) -> Option<ThreatActor> {
        match severity {
            SecuritySeverity::Critical => Some(ThreatActor::NationState),
            SecuritySeverity::High => Some(ThreatActor::Cybercriminal),
            SecuritySeverity::Medium => Some(ThreatActor::ScriptKiddie),
            _ => Some(ThreatActor::External),
        }
    }

    fn generate_threat_description(&self, vuln_type: &str, count: usize) -> String {
        format!(
            "Identified {} instances of {} vulnerabilities that could be exploited by attackers to compromise the system",
            count, vuln_type
        )
    }

    fn generate_mitigation_strategy(&self, vuln_type: &str) -> String {
        match vuln_type {
            t if t.contains("Injection") => "Implement input validation and parameterized queries".to_string(),
            t if t.contains("Authentication") => "Strengthen authentication mechanisms and implement MFA".to_string(),
            t if t.contains("Deserialization") => "Avoid deserializing untrusted data".to_string(),
            _ => "Apply security best practices and patches".to_string(),
        }
    }

    fn generate_mitigation_measures(&self, vuln_type: &str, vulns: &[&SecurityVulnerability]) -> Vec<String> {
        let mut measures = Vec::new();

        // Add specific remediation advice from vulnerabilities
        for vuln in vulns.iter().take(3) {
            if let Some(remediation) = &vuln.remediation_advice {
                measures.push(remediation.clone());
            }
        }

        // Add general measures based on vulnerability type
        match vuln_type {
            t if t.contains("Injection") => {
                measures.push("Use prepared statements for all database queries".to_string());
                measures.push("Implement strict input validation".to_string());
            }
            t if t.contains("Authentication") => {
                measures.push("Implement multi-factor authentication".to_string());
                measures.push("Use secure session management".to_string());
            }
            _ => {
                measures.push("Apply security patches and updates".to_string());
            }
        }

        measures
    }

    fn estimate_exploit_likelihood(&self, severity: &SecuritySeverity) -> f64 {
        match severity {
            SecuritySeverity::Critical => 0.9,
            SecuritySeverity::High => 0.7,
            SecuritySeverity::Medium => 0.5,
            SecuritySeverity::Low => 0.3,
            SecuritySeverity::Info => 0.1,
        }
    }

    fn estimate_business_impact(&self, impact: &Impact) -> Option<String> {
        match impact {
            Impact::Critical => Some("Complete system compromise with data breach potential".to_string()),
            Impact::VeryHigh => Some("Severe operational disruption with significant data exposure risk".to_string()),
            Impact::High => Some("Significant operational disruption and data exposure risk".to_string()),
            Impact::Medium => Some("Moderate business impact with limited data exposure".to_string()),
            Impact::Low => Some("Minor operational impact".to_string()),
            Impact::VeryLow => Some("Minimal operational impact".to_string()),
            Impact::Minimal => Some("Negligible business impact".to_string()),
        }
    }

    fn prioritize_threats(&self, threats: &mut Vec<ThreatModel>) {
        // Sort threats by risk score in descending order
        threats.sort_by(|a, b| b.risk_score.partial_cmp(&a.risk_score).unwrap_or(std::cmp::Ordering::Equal));
    }

    fn load_threat_patterns() -> Vec<ThreatPattern> {
        vec![
            ThreatPattern {
                name: "Data Breach".to_string(),
                category: ThreatCategory::DataBreach,
                severity_threshold: SecuritySeverity::High,
                base_likelihood: Likelihood::High,
                base_impact: Impact::Critical,
            },
            ThreatPattern {
                name: "Remote Code Execution".to_string(),
                category: ThreatCategory::RemoteCodeExecution,
                severity_threshold: SecuritySeverity::Critical,
                base_likelihood: Likelihood::Medium,
                base_impact: Impact::Critical,
            },
            ThreatPattern {
                name: "Denial of Service".to_string(),
                category: ThreatCategory::DenialOfService,
                severity_threshold: SecuritySeverity::Medium,
                base_likelihood: Likelihood::Medium,
                base_impact: Impact::High,
            },
        ]
    }
}

impl Default for ThreatModeler {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_risk_score_calculation() {
        let modeler = ThreatModeler::new();
        
        let high_risk = modeler.calculate_risk_score(&Likelihood::High, &Impact::High);
        let low_risk = modeler.calculate_risk_score(&Likelihood::Low, &Impact::Low);
        
        assert!(high_risk > low_risk);
        assert!(high_risk > 5.0);
        assert!(low_risk < 2.0);
    }
}