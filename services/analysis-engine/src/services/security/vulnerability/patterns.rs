//! Vulnerability patterns database

use crate::models::security::{VulnerabilityType, SecuritySeverity};
use crate::services::security::types::MatchInfo;
use crate::services::security::vulnerability::regex::{
    SQL_INJECTION_1, SQL_INJECTION_2, SQL_INJECTION_3, SQL_INJECTION_4, SQL_INJECTION_5, XSS_1,
    XSS_2, XSS_3, CMD_INJECTION_1, CMD_INJECTION_2, CMD_INJECTION_3, PATH_TRAVERSAL_1,
    PATH_TRAVERSAL_2, HARDCODED_SECRET_1, HARDCODED_SECRET_2, HARDCODED_SECRET_3,
    INSECURE_RANDOM_1, INSECURE_RANDOM_2, XXE_1, LDAP_INJECTION_1, DESERIALIZATION_1,
    DESERIALIZATION_2, MISCONFIG_1, MISCONFIG_2, MISCONFIG_3, <PERSON><PERSON><PERSON><PERSON>_OVERFLOW_1,
    BUFFER_OVERFLOW_2, B<PERSON><PERSON><PERSON>_OVERFLOW_3, RESPONSE_SPLITTING_1, WEAK_CRYPTO_1, WEAK_CRYPTO_2,
    WEAK_CRYPTO_3, NOSQL_INJECTION_1, NOSQL_INJECTION_2, TEMPLATE_INJECTION_1,
    OPEN_REDIRECT_1, COOKIE_SECURITY_1,
};
use once_cell::sync::Lazy;
use regex::Regex;


/// Pattern for detecting vulnerabilities
#[derive(Debug)]
pub struct VulnerabilityPattern {
    pub regex: &'static Lazy<Regex>,
    pub vulnerability_type: VulnerabilityType,
    pub severity: SecuritySeverity,
    pub languages: Vec<String>,
    pub cve_id: Option<String>,
    pub cwe_id: Option<String>,
    pub description: String,
    pub remediation: Option<String>,
    pub owasp_category: Option<String>,
    pub attack_vector: Option<String>,
    pub exploitability_score: Option<f64>,
    pub impact_score: Option<f64>,
    pub false_positive_rate: f64,
}

impl VulnerabilityPattern {
    pub fn applies_to_language(&self, language: &str) -> bool {
        self.languages.is_empty() || self.languages.iter().any(|l| l == language)
    }
    
    pub fn find_matches(&self, content: &str) -> Vec<MatchInfo> {
        let mut matches = Vec::new();
        for (line_num, line) in content.lines().enumerate() {
            if self.regex.is_match(line) {
                matches.push(MatchInfo {
                    line_number: line_num + 1,
                    code_snippet: line.to_string(),
                });
            }
        }
        matches
    }
}

/// Load all vulnerability patterns
pub fn load_vulnerability_patterns() -> Vec<VulnerabilityPattern> {
    vec![
        VulnerabilityPattern {
            regex: &SQL_INJECTION_1,
            vulnerability_type: VulnerabilityType::SqlInjection,
            severity: SecuritySeverity::High,
            languages: vec!["javascript".to_string(), "python".to_string(), "java".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-89".to_string()),
            description: "SQL query constructed using string concatenation, vulnerable to SQL injection".to_string(),
            remediation: Some("Use parameterized queries or prepared statements".to_string()),
            owasp_category: Some("A03:2021-Injection".to_string()),
            attack_vector: Some("SQL Injection".to_string()),
            exploitability_score: Some(0.9),
            impact_score: Some(0.9),
            false_positive_rate: 0.2,
        },

        VulnerabilityPattern {
            regex: &SQL_INJECTION_2,
            vulnerability_type: VulnerabilityType::SqlInjection,
            severity: SecuritySeverity::High,
            languages: vec!["python".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-89".to_string()),
            description: "SQL query using Python string formatting, vulnerable to SQL injection".to_string(),
            remediation: Some("Use parameterized queries with ? placeholders".to_string()),
            owasp_category: Some("A03:2021-Injection".to_string()),
            attack_vector: Some("SQL Injection".to_string()),
            exploitability_score: Some(0.9),
            impact_score: Some(0.9),
            false_positive_rate: 0.15,
        },

        VulnerabilityPattern {
            regex: &SQL_INJECTION_3,
            vulnerability_type: VulnerabilityType::SqlInjection,
            severity: SecuritySeverity::High,
            languages: vec!["java".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-89".to_string()),
            description: "PreparedStatement with string concatenation defeats parameterization".to_string(),
            remediation: Some("Use proper parameterized queries with setString() methods".to_string()),
            owasp_category: Some("A03:2021-Injection".to_string()),
            attack_vector: Some("SQL Injection".to_string()),
            exploitability_score: Some(0.8),
            impact_score: Some(0.9),
            false_positive_rate: 0.1,
        },

        VulnerabilityPattern {
            regex: &SQL_INJECTION_4,
            vulnerability_type: VulnerabilityType::SqlInjection,
            severity: SecuritySeverity::High,
            languages: vec!["javascript".to_string(), "typescript".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-89".to_string()),
            description: "SQL query using template literals with variable interpolation".to_string(),
            remediation: Some("Use parameterized queries or sanitize inputs".to_string()),
            owasp_category: Some("A03:2021-Injection".to_string()),
            attack_vector: Some("SQL Injection".to_string()),
            exploitability_score: Some(0.9),
            impact_score: Some(0.9),
            false_positive_rate: 0.2,
        },

        VulnerabilityPattern {
            regex: &SQL_INJECTION_5,
            vulnerability_type: VulnerabilityType::SqlInjection,
            severity: SecuritySeverity::High,
            languages: vec!["php".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-89".to_string()),
            description: "Direct SQL query with variable concatenation in PHP".to_string(),
            remediation: Some("Use PDO or mysqli with prepared statements".to_string()),
            owasp_category: Some("A03:2021-Injection".to_string()),
            attack_vector: Some("SQL Injection".to_string()),
            exploitability_score: Some(0.9),
            impact_score: Some(0.9),
            false_positive_rate: 0.1,
        },

        VulnerabilityPattern {
            regex: &XSS_1,
            vulnerability_type: VulnerabilityType::CrossSiteScripting,
            severity: SecuritySeverity::High,
            languages: vec!["javascript".to_string(), "typescript".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-79".to_string()),
            description: "Direct innerHTML assignment without sanitization".to_string(),
            remediation: Some("Use textContent or sanitize HTML content".to_string()),
            owasp_category: Some("A03:2021-Injection".to_string()),
            attack_vector: Some("Cross-Site Scripting".to_string()),
            exploitability_score: Some(0.8),
            impact_score: Some(0.8),
            false_positive_rate: 0.25,
        },

        VulnerabilityPattern {
            regex: &XSS_2,
            vulnerability_type: VulnerabilityType::CrossSiteScripting,
            severity: SecuritySeverity::Medium,
            languages: vec!["javascript".to_string(), "typescript".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-79".to_string()),
            description: "document.write can execute arbitrary JavaScript".to_string(),
            remediation: Some("Use DOM methods like createElement and appendChild".to_string()),
            owasp_category: Some("A03:2021-Injection".to_string()),
            attack_vector: Some("Cross-Site Scripting".to_string()),
            exploitability_score: Some(0.7),
            impact_score: Some(0.7),
            false_positive_rate: 0.3,
        },

        VulnerabilityPattern {
            regex: &XSS_3,
            vulnerability_type: VulnerabilityType::CrossSiteScripting,
            severity: SecuritySeverity::High,
            languages: vec!["javascript".to_string(), "typescript".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-79".to_string()),
            description: "jQuery html() method with unsanitized input".to_string(),
            remediation: Some("Use text() method or sanitize HTML content".to_string()),
            owasp_category: Some("A03:2021-Injection".to_string()),
            attack_vector: Some("Cross-Site Scripting".to_string()),
            exploitability_score: Some(0.8),
            impact_score: Some(0.8),
            false_positive_rate: 0.2,
        },

        VulnerabilityPattern {
            regex: &CMD_INJECTION_1,
            vulnerability_type: VulnerabilityType::CommandInjection,
            severity: SecuritySeverity::Critical,
            languages: vec!["javascript".to_string(), "typescript".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-78".to_string()),
            description: "Command execution with string interpolation".to_string(),
            remediation: Some("Use spawn() with argument arrays or validate/sanitize input".to_string()),
            owasp_category: Some("A03:2021-Injection".to_string()),
            attack_vector: Some("Command Injection".to_string()),
            exploitability_score: Some(0.95),
            impact_score: Some(0.95),
            false_positive_rate: 0.15,
        },

        VulnerabilityPattern {
            regex: &CMD_INJECTION_2,
            vulnerability_type: VulnerabilityType::CommandInjection,
            severity: SecuritySeverity::High,
            languages: vec!["python".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-78".to_string()),
            description: "os.system() is vulnerable to command injection".to_string(),
            remediation: Some("Use subprocess.run() with shell=False".to_string()),
            owasp_category: Some("A03:2021-Injection".to_string()),
            attack_vector: Some("Command Injection".to_string()),
            exploitability_score: Some(0.9),
            impact_score: Some(0.9),
            false_positive_rate: 0.1,
        },

        VulnerabilityPattern {
            regex: &CMD_INJECTION_3,
            vulnerability_type: VulnerabilityType::CommandInjection,
            severity: SecuritySeverity::Critical,
            languages: vec!["ruby".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-78".to_string()),
            description: "Ruby backticks with string interpolation".to_string(),
            remediation: Some("Use system() with multiple arguments or Open3".to_string()),
            owasp_category: Some("A03:2021-Injection".to_string()),
            attack_vector: Some("Command Injection".to_string()),
            exploitability_score: Some(0.95),
            impact_score: Some(0.95),
            false_positive_rate: 0.1,
        },

        VulnerabilityPattern {
            regex: &PATH_TRAVERSAL_1,
            vulnerability_type: VulnerabilityType::PathTraversal,
            severity: SecuritySeverity::High,
            languages: vec!["javascript".to_string(), "python".to_string(), "java".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-22".to_string()),
            description: "File path constructed from user input without validation".to_string(),
            remediation: Some("Validate and sanitize file paths, use path.join()".to_string()),
            owasp_category: Some("A01:2021-Broken Access Control".to_string()),
            attack_vector: Some("Path Traversal".to_string()),
            exploitability_score: Some(0.8),
            impact_score: Some(0.8),
            false_positive_rate: 0.2,
        },

        VulnerabilityPattern {
            regex: &PATH_TRAVERSAL_2,
            vulnerability_type: VulnerabilityType::PathTraversal,
            severity: SecuritySeverity::Critical,
            languages: vec!["php".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-98".to_string()),
            description: "Dynamic file inclusion with user-controlled input".to_string(),
            remediation: Some("Use a whitelist of allowed files or avoid dynamic includes".to_string()),
            owasp_category: Some("A03:2021-Injection".to_string()),
            attack_vector: Some("Remote File Inclusion".to_string()),
            exploitability_score: Some(0.95),
            impact_score: Some(0.95),
            false_positive_rate: 0.1,
        },

        VulnerabilityPattern {
            regex: &HARDCODED_SECRET_1,
            vulnerability_type: VulnerabilityType::HardcodedSecret,
            severity: SecuritySeverity::High,
            languages: vec![],
            cve_id: None,
            cwe_id: Some("CWE-798".to_string()),
            description: "Hardcoded API key detected".to_string(),
            remediation: Some("Use environment variables or secure key management".to_string()),
            owasp_category: Some("A07:2021-Identification and Authentication Failures".to_string()),
            attack_vector: Some("Information Disclosure".to_string()),
            exploitability_score: Some(0.7),
            impact_score: Some(0.8),
            false_positive_rate: 0.2,
        },

        VulnerabilityPattern {
            regex: &HARDCODED_SECRET_2,
            vulnerability_type: VulnerabilityType::HardcodedSecret,
            severity: SecuritySeverity::Critical,
            languages: vec![],
            cve_id: None,
            cwe_id: Some("CWE-798".to_string()),
            description: "AWS Access Key ID detected".to_string(),
            remediation: Some("Use AWS IAM roles or environment variables".to_string()),
            owasp_category: Some("A07:2021-Identification and Authentication Failures".to_string()),
            attack_vector: Some("Information Disclosure".to_string()),
            exploitability_score: Some(0.9),
            impact_score: Some(0.95),
            false_positive_rate: 0.05,
        },

        VulnerabilityPattern {
            regex: &HARDCODED_SECRET_3,
            vulnerability_type: VulnerabilityType::HardcodedSecret,
            severity: SecuritySeverity::Critical,
            languages: vec![],
            cve_id: None,
            cwe_id: Some("CWE-798".to_string()),
            description: "Private key detected in source code".to_string(),
            remediation: Some("Store private keys in secure key management systems".to_string()),
            owasp_category: Some("A02:2021-Cryptographic Failures".to_string()),
            attack_vector: Some("Information Disclosure".to_string()),
            exploitability_score: Some(0.95),
            impact_score: Some(0.95),
            false_positive_rate: 0.02,
        },

        VulnerabilityPattern {
            regex: &INSECURE_RANDOM_1,
            vulnerability_type: VulnerabilityType::InsecureRandomness,
            severity: SecuritySeverity::High,
            languages: vec!["javascript".to_string(), "typescript".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-330".to_string()),
            description: "Math.random() is not cryptographically secure".to_string(),
            remediation: Some("Use crypto.getRandomValues() or crypto.randomBytes()".to_string()),
            owasp_category: Some("A02:2021-Cryptographic Failures".to_string()),
            attack_vector: Some("Weak Cryptography".to_string()),
            exploitability_score: Some(0.7),
            impact_score: Some(0.7),
            false_positive_rate: 0.15,
        },

        VulnerabilityPattern {
            regex: &INSECURE_RANDOM_2,
            vulnerability_type: VulnerabilityType::InsecureRandomness,
            severity: SecuritySeverity::High,
            languages: vec!["python".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-330".to_string()),
            description: "Python random module is not cryptographically secure".to_string(),
            remediation: Some("Use secrets module or os.urandom()".to_string()),
            owasp_category: Some("A02:2021-Cryptographic Failures".to_string()),
            attack_vector: Some("Weak Cryptography".to_string()),
            exploitability_score: Some(0.7),
            impact_score: Some(0.7),
            false_positive_rate: 0.15,
        },

        VulnerabilityPattern {
            regex: &XXE_1,
            vulnerability_type: VulnerabilityType::XmlExternalEntity,
            severity: SecuritySeverity::High,
            languages: vec!["java".to_string(), "c#".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-611".to_string()),
            description: "XML parser configured to allow external entities".to_string(),
            remediation: Some("Disable external entity processing".to_string()),
            owasp_category: Some("A05:2021-Security Misconfiguration".to_string()),
            attack_vector: Some("XXE Injection".to_string()),
            exploitability_score: Some(0.8),
            impact_score: Some(0.8),
            false_positive_rate: 0.1,
        },

        VulnerabilityPattern {
            regex: &LDAP_INJECTION_1,
            vulnerability_type: VulnerabilityType::LdapInjection,
            severity: SecuritySeverity::High,
            languages: vec!["java".to_string(), "c#".to_string(), "python".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-90".to_string()),
            description: "LDAP filter constructed with string concatenation".to_string(),
            remediation: Some("Use parameterized LDAP queries or escape special characters".to_string()),
            owasp_category: Some("A03:2021-Injection".to_string()),
            attack_vector: Some("LDAP Injection".to_string()),
            exploitability_score: Some(0.8),
            impact_score: Some(0.8),
            false_positive_rate: 0.2,
        },

        VulnerabilityPattern {
            regex: &DESERIALIZATION_1,
            vulnerability_type: VulnerabilityType::InsecureDeserialization,
            severity: SecuritySeverity::Critical,
            languages: vec!["python".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-502".to_string()),
            description: "Pickle deserialization of untrusted data".to_string(),
            remediation: Some("Use JSON or other safe serialization formats".to_string()),
            owasp_category: Some("A08:2021-Software and Data Integrity Failures".to_string()),
            attack_vector: Some("Remote Code Execution".to_string()),
            exploitability_score: Some(0.95),
            impact_score: Some(0.95),
            false_positive_rate: 0.1,
        },

        VulnerabilityPattern {
            regex: &DESERIALIZATION_2,
            vulnerability_type: VulnerabilityType::InsecureDeserialization,
            severity: SecuritySeverity::High,
            languages: vec!["java".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-502".to_string()),
            description: "Java object deserialization detected".to_string(),
            remediation: Some("Validate object types or use safe serialization".to_string()),
            owasp_category: Some("A08:2021-Software and Data Integrity Failures".to_string()),
            attack_vector: Some("Remote Code Execution".to_string()),
            exploitability_score: Some(0.8),
            impact_score: Some(0.9),
            false_positive_rate: 0.2,
        },

        VulnerabilityPattern {
            regex: &MISCONFIG_1,
            vulnerability_type: VulnerabilityType::SecurityMisconfiguration,
            severity: SecuritySeverity::Medium,
            languages: vec![],
            cve_id: None,
            cwe_id: Some("CWE-489".to_string()),
            description: "Debug mode enabled in production code".to_string(),
            remediation: Some("Disable debug mode in production".to_string()),
            owasp_category: Some("A05:2021-Security Misconfiguration".to_string()),
            attack_vector: Some("Information Disclosure".to_string()),
            exploitability_score: Some(0.5),
            impact_score: Some(0.5),
            false_positive_rate: 0.4,
        },

        VulnerabilityPattern {
            regex: &MISCONFIG_2,
            vulnerability_type: VulnerabilityType::SecurityMisconfiguration,
            severity: SecuritySeverity::High,
            languages: vec![],
            cve_id: None,
            cwe_id: Some("CWE-326".to_string()),
            description: "Weak or deprecated SSL/TLS version".to_string(),
            remediation: Some("Use TLS 1.2 or higher".to_string()),
            owasp_category: Some("A02:2021-Cryptographic Failures".to_string()),
            attack_vector: Some("Weak Encryption".to_string()),
            exploitability_score: Some(0.7),
            impact_score: Some(0.7),
            false_positive_rate: 0.1,
        },

        VulnerabilityPattern {
            regex: &MISCONFIG_3,
            vulnerability_type: VulnerabilityType::SecurityMisconfiguration,
            severity: SecuritySeverity::Medium,
            languages: vec![],
            cve_id: None,
            cwe_id: Some("CWE-942".to_string()),
            description: "CORS configured with wildcard origin".to_string(),
            remediation: Some("Specify allowed origins explicitly".to_string()),
            owasp_category: Some("A05:2021-Security Misconfiguration".to_string()),
            attack_vector: Some("CORS Misconfiguration".to_string()),
            exploitability_score: Some(0.6),
            impact_score: Some(0.6),
            false_positive_rate: 0.2,
        },

        VulnerabilityPattern {
            regex: &BUFFER_OVERFLOW_1,
            vulnerability_type: VulnerabilityType::BufferOverflow,
            severity: SecuritySeverity::High,
            languages: vec!["c".to_string(), "cpp".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-120".to_string()),
            description: "strcpy does not check buffer boundaries".to_string(),
            remediation: Some("Use strncpy or strlcpy with bounds checking".to_string()),
            owasp_category: Some("A03:2021-Injection".to_string()),
            attack_vector: Some("Buffer Overflow".to_string()),
            exploitability_score: Some(0.8),
            impact_score: Some(0.9),
            false_positive_rate: 0.1,
        },

        VulnerabilityPattern {
            regex: &BUFFER_OVERFLOW_2,
            vulnerability_type: VulnerabilityType::BufferOverflow,
            severity: SecuritySeverity::Critical,
            languages: vec!["c".to_string(), "cpp".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-120".to_string()),
            description: "gets() is inherently unsafe and deprecated".to_string(),
            remediation: Some("Use fgets() with buffer size".to_string()),
            owasp_category: Some("A03:2021-Injection".to_string()),
            attack_vector: Some("Buffer Overflow".to_string()),
            exploitability_score: Some(0.95),
            impact_score: Some(0.95),
            false_positive_rate: 0.05,
        },

        VulnerabilityPattern {
            regex: &BUFFER_OVERFLOW_3,
            vulnerability_type: VulnerabilityType::BufferOverflow,
            severity: SecuritySeverity::High,
            languages: vec!["c".to_string(), "cpp".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-120".to_string()),
            description: "sprintf does not check buffer size".to_string(),
            remediation: Some("Use snprintf with buffer size limit".to_string()),
            owasp_category: Some("A03:2021-Injection".to_string()),
            attack_vector: Some("Buffer Overflow".to_string()),
            exploitability_score: Some(0.8),
            impact_score: Some(0.9),
            false_positive_rate: 0.15,
        },

        VulnerabilityPattern {
            regex: &RESPONSE_SPLITTING_1,
            vulnerability_type: VulnerabilityType::HttpResponseSplitting,
            severity: SecuritySeverity::Medium,
            languages: vec!["javascript".to_string(), "java".to_string(), "php".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-113".to_string()),
            description: "HTTP header value constructed from user input".to_string(),
            remediation: Some("Validate and encode header values".to_string()),
            owasp_category: Some("A03:2021-Injection".to_string()),
            attack_vector: Some("HTTP Response Splitting".to_string()),
            exploitability_score: Some(0.7),
            impact_score: Some(0.7),
            false_positive_rate: 0.25,
        },

        VulnerabilityPattern {
            regex: &WEAK_CRYPTO_1,
            vulnerability_type: VulnerabilityType::WeakCryptography,
            severity: SecuritySeverity::Medium,
            languages: vec![],
            cve_id: None,
            cwe_id: Some("CWE-328".to_string()),
            description: "MD5 is cryptographically broken".to_string(),
            remediation: Some("Use SHA-256 or stronger hash functions".to_string()),
            owasp_category: Some("A02:2021-Cryptographic Failures".to_string()),
            attack_vector: Some("Weak Hash Algorithm".to_string()),
            exploitability_score: Some(0.6),
            impact_score: Some(0.6),
            false_positive_rate: 0.3,
        },

        VulnerabilityPattern {
            regex: &WEAK_CRYPTO_2,
            vulnerability_type: VulnerabilityType::WeakCryptography,
            severity: SecuritySeverity::Medium,
            languages: vec![],
            cve_id: None,
            cwe_id: Some("CWE-328".to_string()),
            description: "SHA1 is deprecated for security purposes".to_string(),
            remediation: Some("Use SHA-256 or SHA-3".to_string()),
            owasp_category: Some("A02:2021-Cryptographic Failures".to_string()),
            attack_vector: Some("Weak Hash Algorithm".to_string()),
            exploitability_score: Some(0.5),
            impact_score: Some(0.5),
            false_positive_rate: 0.35,
        },

        VulnerabilityPattern {
            regex: &WEAK_CRYPTO_3,
            vulnerability_type: VulnerabilityType::WeakCryptography,
            severity: SecuritySeverity::High,
            languages: vec![],
            cve_id: None,
            cwe_id: Some("CWE-327".to_string()),
            description: "DES encryption is broken".to_string(),
            remediation: Some("Use AES-256 or ChaCha20".to_string()),
            owasp_category: Some("A02:2021-Cryptographic Failures".to_string()),
            attack_vector: Some("Weak Encryption Algorithm".to_string()),
            exploitability_score: Some(0.8),
            impact_score: Some(0.8),
            false_positive_rate: 0.1,
        },

        VulnerabilityPattern {
            regex: &NOSQL_INJECTION_1,
            vulnerability_type: VulnerabilityType::NoSqlInjection,
            severity: SecuritySeverity::High,
            languages: vec!["javascript".to_string(), "typescript".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-943".to_string()),
            description: "MongoDB $where allows JavaScript execution".to_string(),
            remediation: Some("Avoid $where or validate/sanitize input".to_string()),
            owasp_category: Some("A03:2021-Injection".to_string()),
            attack_vector: Some("NoSQL Injection".to_string()),
            exploitability_score: Some(0.8),
            impact_score: Some(0.8),
            false_positive_rate: 0.15,
        },

        VulnerabilityPattern {
            regex: &NOSQL_INJECTION_2,
            vulnerability_type: VulnerabilityType::NoSqlInjection,
            severity: SecuritySeverity::Critical,
            languages: vec!["javascript".to_string(), "typescript".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-943".to_string()),
            description: "MongoDB eval() executes arbitrary JavaScript".to_string(),
            remediation: Some("Avoid eval() entirely".to_string()),
            owasp_category: Some("A03:2021-Injection".to_string()),
            attack_vector: Some("NoSQL Injection".to_string()),
            exploitability_score: Some(0.95),
            impact_score: Some(0.95),
            false_positive_rate: 0.05,
        },

        VulnerabilityPattern {
            regex: &TEMPLATE_INJECTION_1,
            vulnerability_type: VulnerabilityType::TemplateInjection,
            severity: SecuritySeverity::High,
            languages: vec!["python".to_string(), "html".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-1336".to_string()),
            description: "Template with unsafe filter".to_string(),
            remediation: Some("Validate template input or use autoescaping".to_string()),
            owasp_category: Some("A03:2021-Injection".to_string()),
            attack_vector: Some("Template Injection".to_string()),
            exploitability_score: Some(0.7),
            impact_score: Some(0.8),
            false_positive_rate: 0.2,
        },

        VulnerabilityPattern {
            regex: &OPEN_REDIRECT_1,
            vulnerability_type: VulnerabilityType::OpenRedirect,
            severity: SecuritySeverity::Medium,
            languages: vec!["python".to_string(), "ruby".to_string(), "javascript".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-601".to_string()),
            description: "Open redirect from user-controlled input".to_string(),
            remediation: Some("Validate redirect URLs against whitelist".to_string()),
            owasp_category: Some("A01:2021-Broken Access Control".to_string()),
            attack_vector: Some("Open Redirect".to_string()),
            exploitability_score: Some(0.7),
            impact_score: Some(0.6),
            false_positive_rate: 0.25,
        },

        VulnerabilityPattern {
            regex: &COOKIE_SECURITY_1,
            vulnerability_type: VulnerabilityType::InsecureCookie,
            severity: SecuritySeverity::Medium,
            languages: vec!["javascript".to_string(), "python".to_string(), "java".to_string(), "php".to_string()],
            cve_id: None,
            cwe_id: Some("CWE-1004".to_string()),
            description: "Cookie without HttpOnly flag is accessible via JavaScript".to_string(),
            remediation: Some("Set HttpOnly flag on sensitive cookies".to_string()),
            owasp_category: Some("A05:2021-Security Misconfiguration".to_string()),
            attack_vector: Some("Cross-Site Scripting".to_string()),
            exploitability_score: Some(0.6),
            impact_score: Some(0.6),
            false_positive_rate: 0.3,
        },
    ]
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_pattern_loading() {
        let patterns = load_vulnerability_patterns();
        assert!(!patterns.is_empty());
        
        // Check that critical patterns are present
        let has_sql_injection = patterns.iter().any(|p| matches!(p.vulnerability_type, VulnerabilityType::SqlInjection));
        let has_xss = patterns.iter().any(|p| matches!(p.vulnerability_type, VulnerabilityType::CrossSiteScripting));
        
        assert!(has_sql_injection);
        assert!(has_xss);
    }
    
    #[test]
    fn test_pattern_matching() {
        let patterns = load_vulnerability_patterns();
        let sql_pattern = patterns.iter()
            .find(|p| matches!(p.vulnerability_type, VulnerabilityType::SqlInjection))
            .expect("Should have SQL injection pattern");
        
        let test_code = r#"query("SELECT * FROM users WHERE id = '" + userId + "'")"#;
        let matches = sql_pattern.find_matches(test_code);
        assert!(!matches.is_empty());
    }
}