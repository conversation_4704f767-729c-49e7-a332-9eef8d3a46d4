//! ML-based vulnerability classification for enhanced detection

use crate::models::security::{
    SecurityVulnerability, VulnerabilityType, SecuritySeverity,
};
use crate::models::FileAnalysis;
use crate::errors::AnalysisResult;
use lazy_static::lazy_static;
use regex::Regex;
use uuid::Uuid;
use chrono::Utc;
use tracing::debug;

// Pre-compiled regex patterns for SQL Injection detection
lazy_static! {
    static ref SQL_INJECTION_PATTERNS: Vec<Regex> = vec![
        Regex::new(r#"(?i)(query|execute|exec)\s*\(\s*["'].*\+.*["']"#).unwrap(),
        Regex::new(r#"(?i)(query|execute|exec)\s*\([^)]*["']\s*\+\s*\w+"#).unwrap(), // Matches 'query("..." + var)'
        Regex::new(r#"(?i)cursor\.execute\s*\(\s*["'].*%s.*["']\s*%"#).unwrap(),
        Regex::new(r#"(?i)preparedStatement\s*=\s*connection\.prepareStatement\s*\(\s*["'].*\+.*["']"#).unwrap(),
        Regex::new(r#"(?i)(mysql_query|mysqli_query|pg_query)\s*\(\s*["'].*\$.*["']"#).unwrap(),
    ];
}

// Pre-compiled regex patterns for XSS detection
lazy_static! {
    static ref XSS_PATTERNS: Vec<Regex> = vec![
        Regex::new(r"\.innerHTML\s*=\s*.*[^(escapeHtml|sanitize)]").unwrap(),
        Regex::new(r#"(?i)document\.write\s*\(\s*["'].*\+.*["']"#).unwrap(),
        Regex::new(r#"(?i)echo\s+\$_(GET|POST|REQUEST|COOKIE)"#).unwrap(),
        Regex::new(r#"(?i)dangerouslySetInnerHTML\s*=\s*\{\{\s*__html\s*:"#).unwrap(),
    ];
}

// Pre-compiled regex patterns for Command Injection detection
lazy_static! {
    static ref CMD_INJECTION_PATTERNS: Vec<Regex> = vec![
        Regex::new(r#"(?i)os\.system\s*\(\s*["'].*\+.*["']"#).unwrap(),
        Regex::new(r#"(?i)os\.system\s*\([^)]*["']\s*\+\s*\w+"#).unwrap(), // Matches 'os.system("..." + var)'
        Regex::new(r#"(?i)(exec|spawn|execFile)\s*\(\s*["'].*\+.*["']"#).unwrap(),
        Regex::new(r#"(?i)(shell_exec|exec|system|passthru)\s*\(\s*["'].*\$.*["']"#).unwrap(),
        Regex::new(r#"(?i)Runtime\.getRuntime\(\)\.exec\s*\(\s*["'].*\+.*["']"#).unwrap(),
    ];
}

// Pre-compiled regex patterns for Path Traversal detection
lazy_static! {
    static ref PATH_TRAVERSAL_PATTERNS: Vec<(Regex, &'static str)> = vec![
        (Regex::new(r#"(?i)(open|readFile|createReadStream)\s*\(\s*["'].*\+.*["']"#).unwrap(),
         "File operation with concatenation"),
        (Regex::new(r#"(?i)(include|require|include_once|require_once)\s*\(\s*\$"#).unwrap(),
         "Dynamic file inclusion"),
        (Regex::new(r#"(?i)\.\./"#).unwrap(),
         "Path traversal sequence"),
    ];
}

// Pre-compiled regex patterns for Insecure Deserialization detection
lazy_static! {
    static ref DESERIALIZATION_PATTERNS: Vec<Regex> = vec![
        Regex::new(r#"(?i)pickle\.loads?\("#).unwrap(),
        Regex::new(r#"(?i)new\s+ObjectInputStream"#).unwrap(),
        Regex::new(r#"(?i)readObject\s*\("#).unwrap(),
        Regex::new(r#"(?i)unserialize\s*\("#).unwrap(),
    ];
}

// Pre-compiled regex patterns for Weak Cryptography detection
lazy_static! {
    static ref WEAK_CRYPTO_PATTERNS: Vec<Regex> = vec![
        Regex::new(r#"(?i)\b(MD5|md5)\s*\("#).unwrap(),
        Regex::new(r#"(?i)\b(SHA1|sha1)\s*\("#).unwrap(),
        Regex::new(r#"(?i)\b(DES|DESede)\s*\("#).unwrap(),
        Regex::new(r#"(?i)(Math\.random|random\.(random|randint|choice))"#).unwrap(),
    ];
}

// Pre-compiled regex patterns for Authentication issues
lazy_static! {
    static ref AUTH_PATTERNS: Vec<Regex> = vec![
        Regex::new(r#"(?i)password\s*==\s*["']"#).unwrap(),
        Regex::new(r#"(?i)(debug|DEBUG)\s*[:=]\s*(true|True|TRUE|1)"#).unwrap(),
        Regex::new(r#"(?i)verify_peer\s*=>\s*false"#).unwrap(),
    ];
}

// Pre-compiled regex patterns for Security Misconfiguration
lazy_static! {
    static ref CONFIG_PATTERNS: Vec<Regex> = vec![
        Regex::new(r#"(?i)(TLSv1\.0|TLSv1\.1|SSLv2|SSLv3)"#).unwrap(),
        Regex::new(r#"(?i)Access-Control-Allow-Origin.*\*"#).unwrap(),
        Regex::new(r#"(?i)X-Frame-Options\s*:\s*(ALLOW|allow)"#).unwrap(),
    ];
}

// Pre-compiled regex patterns for Race Conditions
lazy_static! {
    static ref RACE_CONDITION_PATTERNS: Vec<Regex> = vec![
        Regex::new(r#"(?i)Thread\.sleep\s*\("#).unwrap(),
        Regex::new(r#"(?i)synchronized\s*\("#).unwrap(),
    ];
}

// Pre-compiled regex patterns for Buffer Overflow (C/C++)
lazy_static! {
    static ref BUFFER_OVERFLOW_PATTERNS: Vec<Regex> = vec![
        Regex::new(r#"\bstrcpy\s*\("#).unwrap(),
        Regex::new(r#"\bgets\s*\("#).unwrap(),
        Regex::new(r#"\bsprintf\s*\("#).unwrap(),
        Regex::new(r#"\bstrcat\s*\("#).unwrap(),
    ];
}

// Language-specific SQL injection patterns
lazy_static! {
    static ref SQL_INJECTION_PYTHON: Vec<(Regex, &'static str)> = vec![
        (Regex::new(r#"(?i)cursor\.execute\s*\(\s*["'].*%s.*["']\s*%"#).unwrap(),
         "Python string formatting in SQL"),
        (Regex::new(r#"(?i)execute\s*\(\s*f["'].*\{"#).unwrap(),
         "Python f-string in SQL"),
        (Regex::new(r#"(?i)execute\s*\(\s*["'].*\.format\("#).unwrap(),
         "String format in SQL"),
    ];
    
    static ref SQL_INJECTION_JAVA: Vec<(Regex, &'static str)> = vec![
        (Regex::new(r#"(?i)preparedStatement\s*=\s*connection\.prepareStatement\s*\(\s*["'].*\+.*["']"#).unwrap(),
         "PreparedStatement with concatenation"),
        (Regex::new(r#"(?i)statement\.execute(Query|Update)?\s*\(\s*["'].*\+.*["']"#).unwrap(),
         "Statement with concatenation"),
        (Regex::new(r#"(?i)createQuery\s*\(\s*["'].*\+.*["']"#).unwrap(),
         "JPA/Hibernate query with concatenation"),
    ];
    
    static ref SQL_INJECTION_JAVASCRIPT: Vec<(Regex, &'static str)> = vec![
        (Regex::new(r#"(?i)(query|execute)\s*\(\s*["'].*\+.*["']"#).unwrap(),
         "SQL query with concatenation"),
        (Regex::new(r#"(?i)(query|execute)\s*\(\s*`.*\$\{.*\}`"#).unwrap(),
         "SQL query with template literal"),
        (Regex::new(r#"(?i)db\.(query|execute)\s*\(\s*["'].*\+"#).unwrap(),
         "Database query with concatenation"),
    ];
    
    static ref SQL_INJECTION_PHP: Vec<(Regex, &'static str)> = vec![
        (Regex::new(r#"(?i)mysql_query\s*\(\s*["'].*\.\s*\$"#).unwrap(),
         "mysql_query with concatenation"),
        (Regex::new(r#"(?i)mysqli_query\s*\(\s*\$\w+,\s*["'].*\.\s*\$"#).unwrap(),
         "mysqli_query with concatenation"),
        (Regex::new(r#"(?i)\$pdo->query\s*\(\s*["'].*\.\s*\$"#).unwrap(),
         "PDO query with concatenation"),
    ];
}

// Language-specific XSS patterns
lazy_static! {
    static ref XSS_JAVASCRIPT: Vec<(Regex, &'static str)> = vec![
        (Regex::new(r#"(?i)document\.write\s*\(\s*["'].*\+.*["']"#).unwrap(),
         "document.write with user input"),
        (Regex::new(r#"(?i)\.innerHTML\s*=\s*[^'"]*\+"#).unwrap(),
         "innerHTML with concatenation"),
        (Regex::new(r#"(?i)\$\(["'][^"']+["']\)\.html\s*\(.*\+"#).unwrap(),
         "jQuery html() with concatenation"),
    ];
    
    static ref XSS_PHP: Vec<(Regex, &'static str)> = vec![
        (Regex::new(r#"(?i)echo\s+\$_(GET|POST|REQUEST|COOKIE)"#).unwrap(),
         "Echo of unescaped user input"),
        (Regex::new(r#"(?i)print\s+\$_(GET|POST|REQUEST)"#).unwrap(),
         "Print of unescaped user input"),
        (Regex::new(r#"<\?=\s*\$_(GET|POST|REQUEST)"#).unwrap(),
         "Short echo tag with user input"),
    ];
    
    static ref XSS_REACT: Vec<(Regex, &'static str)> = vec![
        (Regex::new(r#"(?i)dangerouslySetInnerHTML\s*=\s*\{\{\s*__html\s*:"#).unwrap(),
         "React dangerouslySetInnerHTML"),
        (Regex::new(r#"(?i)createMarkup\s*\(\)\s*\{[^}]*__html\s*:"#).unwrap(),
         "React markup creation"),
    ];
}

// Language-specific command injection patterns  
lazy_static! {
    static ref CMD_INJECTION_PYTHON: Vec<(Regex, &'static str)> = vec![
        (Regex::new(r#"(?i)os\.system\s*\(\s*["'].*\+"#).unwrap(),
         "os.system with concatenation"),
        (Regex::new(r#"(?i)subprocess\.(call|run|Popen)\s*\(\s*["'].*\+"#).unwrap(),
         "subprocess with concatenation"),
        (Regex::new(r#"(?i)os\.popen\s*\(\s*["'].*\+"#).unwrap(),
         "os.popen with concatenation"),
    ];
    
    static ref CMD_INJECTION_JAVASCRIPT: Vec<(Regex, &'static str)> = vec![
        (Regex::new(r#"(?i)exec\s*\(\s*["'].*\+"#).unwrap(),
         "exec with concatenation"),
        (Regex::new(r#"(?i)spawn\s*\(\s*["'][^"']+["'],\s*\[[^\]]*\+"#).unwrap(),
         "spawn with concatenation"),
        (Regex::new(r#"(?i)execFile\s*\(\s*["'][^"']+["'],\s*\[[^\]]*\+"#).unwrap(),
         "execFile with concatenation"),
    ];
    
    static ref CMD_INJECTION_JAVA: Vec<(Regex, &'static str)> = vec![
        (Regex::new(r#"(?i)Runtime\.getRuntime\(\)\.exec\s*\(\s*["'].*\+"#).unwrap(),
         "Runtime.exec with concatenation"),
        (Regex::new(r#"(?i)ProcessBuilder\s*\(\s*["'].*\+"#).unwrap(),
         "ProcessBuilder with concatenation"),
    ];
}

/// AI vulnerability information structure
#[derive(Debug, Clone)]
pub struct AIVulnerability {
    pub vulnerability_type: VulnerabilityType,
    pub severity: SecuritySeverity,
    pub confidence: f64,
    pub line_start: Option<i64>,
    pub line_end: Option<i64>,
    pub code_snippet: Option<String>,
    pub description: String,
    pub remediation_advice: Option<String>,
    pub owasp_category: Option<String>,
    pub attack_vector: Option<String>,
    pub exploitability_score: Option<f64>,
    pub impact_score: Option<f64>,
    pub cve_id: Option<String>,
    pub cwe_id: Option<String>,
}

/// ML-based vulnerability classifier for enhanced detection
pub struct MlVulnerabilityClassifier {
    #[allow(dead_code)]
    model_version: String,
}

impl MlVulnerabilityClassifier {
    pub fn new() -> Self {
        Self {
            model_version: "v1.0.0".to_string(),
        }
    }

    pub async fn classify_vulnerabilities(
        &self,
        analysis_id: &str,
        file_analysis: &FileAnalysis,
    ) -> AnalysisResult<Vec<SecurityVulnerability>> {
        // Enhanced ML-based vulnerability classification
        let mut vulnerabilities = Vec::new();

        // Use AI pattern detection for enhanced vulnerability classification
        let ai_analysis = self.analyze_with_ai(file_analysis).await?;
        
        for ai_vuln in ai_analysis {
            if ai_vuln.confidence > 0.7 {
                let vulnerability = SecurityVulnerability {
                    vulnerability_id: Uuid::new_v4().to_string(),
                    analysis_id: analysis_id.to_string(),
                    cve_id: ai_vuln.cve_id,
                    cwe_id: ai_vuln.cwe_id,
                    vulnerability_type: ai_vuln.vulnerability_type,
                    severity: ai_vuln.severity,
                    confidence_score: ai_vuln.confidence,
                    file_path: file_analysis.path.clone(),
                    line_start: ai_vuln.line_start,
                    line_end: ai_vuln.line_end,
                    code_snippet: ai_vuln.code_snippet,
                    description: ai_vuln.description,
                    remediation_advice: ai_vuln.remediation_advice,
                    owasp_category: ai_vuln.owasp_category,
                    attack_vector: ai_vuln.attack_vector,
                    exploitability_score: ai_vuln.exploitability_score,
                    impact_score: ai_vuln.impact_score,
                    false_positive_probability: Some(1.0 - ai_vuln.confidence),
                    created_at: Utc::now(),
                    updated_at: None,
                };
                vulnerabilities.push(vulnerability);
            }
        }

        debug!("ML vulnerability classification found {} vulnerabilities", vulnerabilities.len());
        Ok(vulnerabilities)
    }

    async fn analyze_with_ai(&self, file_analysis: &FileAnalysis) -> AnalysisResult<Vec<AIVulnerability>> {
        // Enhanced AI analysis with ML-based pattern matching and context-aware vulnerability detection
        let mut ai_vulnerabilities = Vec::new();
        
        let file_content = file_analysis.ast.text.clone().unwrap_or_default();
        let file_extension = file_analysis.path.split('.').last().unwrap_or("");
        
        // Advanced SQL injection detection with ML confidence scoring and line-level detection
        let sql_vulns = self.detect_sql_injection_with_context(&file_content, &file_analysis.language)?;
        ai_vulnerabilities.extend(sql_vulns);

        // Advanced XSS detection with context analysis and DOM-based XSS detection
        let xss_vulns = self.detect_xss_with_context(&file_content, &file_analysis.language)?;
        ai_vulnerabilities.extend(xss_vulns);

        // Advanced command injection detection with shell command analysis
        let cmd_vulns = self.detect_command_injection_with_context(&file_content, &file_analysis.language)?;
        ai_vulnerabilities.extend(cmd_vulns);

        // Path traversal detection
        let path_vulns = self.detect_path_traversal_with_context(&file_content, &file_analysis.language)?;
        ai_vulnerabilities.extend(path_vulns);

        // Insecure deserialization detection
        let deser_vulns = self.detect_insecure_deserialization(&file_content, &file_analysis.language)?;
        ai_vulnerabilities.extend(deser_vulns);

        // Weak cryptography detection
        let crypto_vulns = self.detect_weak_cryptography(&file_content, &file_analysis.language)?;
        ai_vulnerabilities.extend(crypto_vulns);

        // Broken authentication detection
        let auth_vulns = self.detect_broken_authentication(&file_content, &file_analysis.language)?;
        ai_vulnerabilities.extend(auth_vulns);

        // Security misconfiguration detection
        let config_vulns = self.detect_security_misconfiguration(&file_content, file_extension)?;
        ai_vulnerabilities.extend(config_vulns);

        // Race condition detection
        let race_vulns = self.detect_race_conditions(&file_content, &file_analysis.language)?;
        ai_vulnerabilities.extend(race_vulns);

        // Buffer overflow detection (for C/C++ and similar languages)
        if matches!(file_analysis.language.as_str(), "c" | "cpp" | "c++" | "objective-c") {
            let buffer_vulns = self.detect_buffer_overflows(&file_content)?;
            ai_vulnerabilities.extend(buffer_vulns);
        }

        Ok(ai_vulnerabilities)
    }

    // Pattern detection methods
    #[allow(dead_code)]
    fn detect_sql_injection_patterns(&self, content: &str) -> bool {
        SQL_INJECTION_PATTERNS.iter().any(|r| r.is_match(content))
    }

    #[allow(dead_code)]
    fn detect_xss_patterns(&self, content: &str) -> bool {
        XSS_PATTERNS.iter().any(|r| r.is_match(content))
    }

    #[allow(dead_code)]
    fn detect_command_injection_patterns(&self, content: &str) -> bool {
        CMD_INJECTION_PATTERNS.iter().any(|r| r.is_match(content))
    }

    // Enhanced ML-based vulnerability detection methods with context awareness

    fn detect_sql_injection_with_context(&self, content: &str, language: &str) -> AnalysisResult<Vec<AIVulnerability>> {
        let mut vulnerabilities = Vec::new();
        
        let patterns: &[(Regex, &'static str)] = match language {
            "python" => &SQL_INJECTION_PYTHON[..],
            "java" => &SQL_INJECTION_JAVA[..],
            "javascript" | "typescript" => &SQL_INJECTION_JAVASCRIPT[..],
            "php" => &SQL_INJECTION_PHP[..],
            _ => return Ok(vulnerabilities),
        };

        for (regex, description) in patterns {
            for (line_num, line) in content.lines().enumerate() {
                if regex.is_match(line) {
                    vulnerabilities.push(AIVulnerability {
                        vulnerability_type: VulnerabilityType::SqlInjection,
                       severity: SecuritySeverity::High,
                       confidence: 0.85,
                       line_start: Some((line_num + 1) as i64),
                       line_end: Some((line_num + 1) as i64),
                       code_snippet: Some(line.trim().to_string()),
                       description: format!("{}: Potential SQL injection vulnerability", description),
                       remediation_advice: Some("Use parameterized queries or prepared statements".to_string()),
                       owasp_category: Some("A03:2021-Injection".to_string()),
                       attack_vector: Some("SQL Injection".to_string()),
                       exploitability_score: Some(0.9),
                       impact_score: Some(0.9),
                       cve_id: None,
                       cwe_id: Some("CWE-89".to_string()),
                   });
               }
           }
        }

        Ok(vulnerabilities)
    }

    fn detect_xss_with_context(&self, content: &str, language: &str) -> AnalysisResult<Vec<AIVulnerability>> {
        let mut vulnerabilities = Vec::new();
        
        let patterns: &[(Regex, &'static str)] = match language {
            "javascript" | "typescript" => &XSS_JAVASCRIPT[..],
            "php" => &XSS_PHP[..],
            "react" | "jsx" | "tsx" => &XSS_REACT[..],
            _ => return Ok(vulnerabilities),
        };

        for (regex, description) in patterns {
            for (line_num, line) in content.lines().enumerate() {
                if regex.is_match(line) {
                    vulnerabilities.push(AIVulnerability {
                        vulnerability_type: VulnerabilityType::CrossSiteScripting,
                       severity: SecuritySeverity::High,
                       confidence: 0.8,
                       line_start: Some((line_num + 1) as i64),
                       line_end: Some((line_num + 1) as i64),
                       code_snippet: Some(line.trim().to_string()),
                       description: format!("{}: Potential XSS vulnerability", description),
                       remediation_advice: Some("Sanitize user input and use appropriate encoding".to_string()),
                       owasp_category: Some("A03:2021-Injection".to_string()),
                       attack_vector: Some("Cross-Site Scripting".to_string()),
                       exploitability_score: Some(0.8),
                       impact_score: Some(0.8),
                       cve_id: None,
                       cwe_id: Some("CWE-79".to_string()),
                   });
               }
           }
        }

        Ok(vulnerabilities)
    }

    fn detect_command_injection_with_context(&self, content: &str, language: &str) -> AnalysisResult<Vec<AIVulnerability>> {
        let mut vulnerabilities = Vec::new();
        
        let patterns: &[(Regex, &'static str)] = match language {
            "python" => &CMD_INJECTION_PYTHON[..],
            "javascript" | "typescript" => &CMD_INJECTION_JAVASCRIPT[..],
            "java" => &CMD_INJECTION_JAVA[..],
            _ => return Ok(vulnerabilities),
        };

        for (regex, description) in patterns {
            for (line_num, line) in content.lines().enumerate() {
                if regex.is_match(line) {
                    vulnerabilities.push(AIVulnerability {
                        vulnerability_type: VulnerabilityType::CommandInjection,
                       severity: SecuritySeverity::Critical,
                       confidence: 0.85,
                       line_start: Some((line_num + 1) as i64),
                       line_end: Some((line_num + 1) as i64),
                       code_snippet: Some(line.trim().to_string()),
                       description: format!("{}: Potential command injection", description),
                       remediation_advice: Some("Use parameterized commands or validate input".to_string()),
                       owasp_category: Some("A03:2021-Injection".to_string()),
                       attack_vector: Some("Command Injection".to_string()),
                       exploitability_score: Some(0.95),
                       impact_score: Some(0.95),
                       cve_id: None,
                       cwe_id: Some("CWE-78".to_string()),
                   });
               }
           }
        }

        Ok(vulnerabilities)
    }

    fn detect_path_traversal_with_context(&self, content: &str, _language: &str) -> AnalysisResult<Vec<AIVulnerability>> {
        let mut vulnerabilities = Vec::new();

        for (regex, description) in PATH_TRAVERSAL_PATTERNS.iter() {
            for (line_num, line) in content.lines().enumerate() {
                if regex.is_match(line) && !line.contains("path.join") && !line.contains("path.resolve") {
                    vulnerabilities.push(AIVulnerability {
                        vulnerability_type: VulnerabilityType::PathTraversal,
                       severity: SecuritySeverity::High,
                       confidence: 0.75,
                       line_start: Some((line_num + 1) as i64),
                       line_end: Some((line_num + 1) as i64),
                       code_snippet: Some(line.trim().to_string()),
                       description: format!("{}: Potential path traversal", description),
                       remediation_advice: Some("Validate and sanitize file paths".to_string()),
                       owasp_category: Some("A01:2021-Broken Access Control".to_string()),
                       attack_vector: Some("Path Traversal".to_string()),
                       exploitability_score: Some(0.8),
                       impact_score: Some(0.8),
                       cve_id: None,
                       cwe_id: Some("CWE-22".to_string()),
                   });
               }
           }
        }

        Ok(vulnerabilities)
    }

    fn detect_insecure_deserialization(&self, content: &str, language: &str) -> AnalysisResult<Vec<AIVulnerability>> {
        let mut vulnerabilities = Vec::new();
        
        let patterns = match language {
            "python" => vec![
                (r#"pickle\.loads?\("#, "pickle deserialization", "CWE-502"),
                (r#"yaml\.load\s*\([^)]+\)"#, "unsafe yaml.load", "CWE-502"),
            ],
            "java" => vec![
                (r#"ObjectInputStream"#, "Java object deserialization", "CWE-502"),
            ],
            "javascript" | "typescript" => vec![
                (r#"eval\s*\(\s*[^)]+\)"#, "eval usage", "CWE-95"),
            ],
            _ => vec![],
        };

        for (pattern_str, description, cwe) in patterns {
            let regex = Regex::new(pattern_str).unwrap();
            for (line_num, line) in content.lines().enumerate() {
                if regex.is_match(line) {
                    vulnerabilities.push(AIVulnerability {
                        vulnerability_type: VulnerabilityType::InsecureDeserialization,
                       severity: SecuritySeverity::Critical,
                       confidence: 0.85,
                       line_start: Some((line_num + 1) as i64),
                       line_end: Some((line_num + 1) as i64),
                       code_snippet: Some(line.trim().to_string()),
                       description: format!("{}: Insecure deserialization", description),
                       remediation_advice: Some("Use safe serialization formats".to_string()),
                       owasp_category: Some("A08:2021-Software and Data Integrity Failures".to_string()),
                       attack_vector: Some("Remote Code Execution".to_string()),
                       exploitability_score: Some(0.9),
                       impact_score: Some(0.95),
                       cve_id: None,
                       cwe_id: Some(cwe.to_string()),
                   });
               }
           }
        }

        Ok(vulnerabilities)
    }

    fn detect_weak_cryptography(&self, content: &str, _language: &str) -> AnalysisResult<Vec<AIVulnerability>> {
        let mut vulnerabilities = Vec::new();
        
        let patterns = vec![
            (r#"(?i)\b(MD5|md5)\("#, "MD5 hash usage", SecuritySeverity::Medium, "CWE-328"),
            (r#"(?i)\b(SHA1|sha1)\("#, "SHA1 hash usage", SecuritySeverity::Medium, "CWE-328"),
            (r#"(?i)\b(DES|des)\("#, "DES encryption", SecuritySeverity::High, "CWE-327"),
            (r#"(?i)ECB"#, "ECB mode usage", SecuritySeverity::High, "CWE-327"),
        ];

        for (pattern_str, description, severity, cwe) in patterns {
            let regex = Regex::new(pattern_str).unwrap();
            for (line_num, line) in content.lines().enumerate() {
                if regex.is_match(line) {
                    vulnerabilities.push(AIVulnerability {
                        vulnerability_type: VulnerabilityType::WeakCryptography,
                       severity,
                       confidence: 0.8,
                       line_start: Some((line_num + 1) as i64),
                       line_end: Some((line_num + 1) as i64),
                       code_snippet: Some(line.trim().to_string()),
                       description: format!("{}: Weak cryptography", description),
                       remediation_advice: Some("Use strong cryptographic algorithms".to_string()),
                       owasp_category: Some("A02:2021-Cryptographic Failures".to_string()),
                       attack_vector: Some("Cryptographic Weakness".to_string()),
                       exploitability_score: Some(0.6),
                       impact_score: Some(0.7),
                       cve_id: None,
                       cwe_id: Some(cwe.to_string()),
                   });
               }
           }
        }

        Ok(vulnerabilities)
    }

    fn detect_broken_authentication(&self, content: &str, _language: &str) -> AnalysisResult<Vec<AIVulnerability>> {
        let mut vulnerabilities = Vec::new();
        
        let patterns = vec![
            (r#"(?i)password\s*=\s*["'][^"']+["']"#, "Hardcoded password"),
            (r#"(?i)(session|token).*expire.*=\s*(0|null|false)"#, "Non-expiring session"),
            (r#"(?i)bcrypt.*rounds\s*=\s*[1-9]\b"#, "Weak bcrypt rounds"),
        ];

        for (pattern_str, description) in patterns {
            let regex = Regex::new(pattern_str).unwrap();
            for (line_num, line) in content.lines().enumerate() {
                if regex.is_match(line) {
                    vulnerabilities.push(AIVulnerability {
                        vulnerability_type: VulnerabilityType::BrokenAuthentication,
                       severity: SecuritySeverity::High,
                       confidence: 0.75,
                       line_start: Some((line_num + 1) as i64),
                       line_end: Some((line_num + 1) as i64),
                       code_snippet: Some(line.trim().to_string()),
                       description: format!("{}: Authentication issue", description),
                       remediation_advice: Some("Implement secure authentication practices".to_string()),
                       owasp_category: Some("A07:2021-Identification and Authentication Failures".to_string()),
                       attack_vector: Some("Authentication Bypass".to_string()),
                       exploitability_score: Some(0.8),
                       impact_score: Some(0.85),
                       cve_id: None,
                       cwe_id: Some("CWE-287".to_string()),
                   });
               }
           }
        }

        Ok(vulnerabilities)
    }

    fn detect_security_misconfiguration(&self, content: &str, file_extension: &str) -> AnalysisResult<Vec<AIVulnerability>> {
        let mut vulnerabilities = Vec::new();
        
        let patterns = vec![
            (r#"(?i)debug\s*[:=]\s*(true|1)"#, "Debug mode enabled", "CWE-489"),
            (r#"(?i)allow.*origin.*\*"#, "CORS wildcard", "CWE-942"),
            (r#"(?i)verify.*ssl.*false"#, "SSL verification disabled", "CWE-295"),
        ];

        for (pattern_str, description, cwe) in patterns {
            let regex = Regex::new(pattern_str).unwrap();
            for (line_num, line) in content.lines().enumerate() {
                if regex.is_match(line) {
                    vulnerabilities.push(AIVulnerability {
                        vulnerability_type: VulnerabilityType::SecurityMisconfiguration,
                       severity: SecuritySeverity::Medium,
                       confidence: 0.7,
                       line_start: Some((line_num + 1) as i64),
                       line_end: Some((line_num + 1) as i64),
                       code_snippet: Some(line.trim().to_string()),
                       description: format!("{}: Security misconfiguration", description),
                       remediation_advice: Some("Review security configurations".to_string()),
                       owasp_category: Some("A05:2021-Security Misconfiguration".to_string()),
                       attack_vector: Some("Configuration Error".to_string()),
                       exploitability_score: Some(0.6),
                       impact_score: Some(0.6),
                       cve_id: None,
                       cwe_id: Some(cwe.to_string()),
                   });
               }
           }
        }

        // Check for sensitive files
        if matches!(file_extension, "env" | "properties" | "config" | "ini") {
            vulnerabilities.push(AIVulnerability {
                vulnerability_type: VulnerabilityType::SecurityMisconfiguration,
                severity: SecuritySeverity::Low,
                confidence: 0.6,
                line_start: None,
                line_end: None,
                code_snippet: None,
                description: "Configuration file exposed".to_string(),
                remediation_advice: Some("Ensure configuration files are not exposed".to_string()),
                owasp_category: Some("A05:2021-Security Misconfiguration".to_string()),
                attack_vector: Some("Information Disclosure".to_string()),
                exploitability_score: Some(0.5),
                impact_score: Some(0.5),
                cve_id: None,
                cwe_id: Some("CWE-200".to_string()),
            });
        }

        Ok(vulnerabilities)
    }

    fn detect_race_conditions(&self, content: &str, language: &str) -> AnalysisResult<Vec<AIVulnerability>> {
        let mut vulnerabilities = Vec::new();
        
        let patterns = match language {
            "python" => vec![
                (r#"threading\.Thread"#, "Thread usage without locks"),
                (r#"os\.path\.exists.*open"#, "TOCTOU race condition"),
            ],
            "java" => vec![
                (r#"synchronized\s*\("", "Empty synchronization"),
                (r#"volatile.*\+\+"#, "Non-atomic operation on volatile"),
            ],
            _ => vec![],
        };

        for (pattern_str, description) in patterns {
            let regex = Regex::new(pattern_str).unwrap();
            for (line_num, line) in content.lines().enumerate() {
                if regex.is_match(line) {
                    vulnerabilities.push(AIVulnerability {
                        vulnerability_type: VulnerabilityType::RaceCondition,
                       severity: SecuritySeverity::Medium,
                       confidence: 0.65,
                       line_start: Some((line_num + 1) as i64),
                       line_end: Some((line_num + 1) as i64),
                       code_snippet: Some(line.trim().to_string()),
                       description: format!("{}: Potential race condition", description),
                       remediation_advice: Some("Use proper synchronization".to_string()),
                       owasp_category: Some("A04:2021-Insecure Design".to_string()),
                       attack_vector: Some("Race Condition".to_string()),
                       exploitability_score: Some(0.5),
                       impact_score: Some(0.7),
                       cve_id: None,
                       cwe_id: Some("CWE-362".to_string()),
                   });
               }
           }
        }

        Ok(vulnerabilities)
    }

    fn detect_buffer_overflows(&self, content: &str) -> AnalysisResult<Vec<AIVulnerability>> {
        let mut vulnerabilities = Vec::new();
        
        let patterns = vec![
            (r#"\bstrcpy\s*\("#, "strcpy usage", "CWE-120"),
            (r#"\bstrcat\s*\("#, "strcat usage", "CWE-120"),
            (r#"\bgets\s*\("#, "gets usage", "CWE-120"),
            (r#"\bsprintf\s*\("#, "sprintf usage", "CWE-120"),
            (r#"\bscanf\s*\([^)]*%s"#, "scanf with %s", "CWE-120"),
        ];

        for (pattern_str, description, cwe) in patterns {
            let regex = Regex::new(pattern_str).unwrap();
            for (line_num, line) in content.lines().enumerate() {
                if regex.is_match(line) {
                    vulnerabilities.push(AIVulnerability {
                        vulnerability_type: VulnerabilityType::BufferOverflow,
                       severity: SecuritySeverity::High,
                       confidence: 0.85,
                       line_start: Some((line_num + 1) as i64),
                       line_end: Some((line_num + 1) as i64),
                       code_snippet: Some(line.trim().to_string()),
                       description: format!("{}: Buffer overflow risk", description),
                       remediation_advice: Some("Use safe string functions with bounds checking".to_string()),
                       owasp_category: Some("A03:2021-Injection".to_string()),
                       attack_vector: Some("Buffer Overflow".to_string()),
                       exploitability_score: Some(0.85),
                       impact_score: Some(0.9),
                       cve_id: None,
                       cwe_id: Some(cwe.to_string()),
                   });
               }
           }
        }

        Ok(vulnerabilities)
    }
}

impl Default for MlVulnerabilityClassifier {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_ml_classifier_creation() {
        let classifier = MlVulnerabilityClassifier::new();
        assert!(classifier.detect_sql_injection_patterns("query('SELECT * FROM users WHERE id = ' + userId)"));
        assert!(classifier.detect_xss_patterns("element.innerHTML = userInput"));
        assert!(classifier.detect_command_injection_patterns("os.system('ls ' + userInput)"));
    }
}