use once_cell::sync::Lazy;
use regex::Regex;

pub static SQL_INJECTION_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(query|execute|exec)\s*\(\s*["'].*\+.*["']"#).unwrap()
});

pub static SQL_INJECTION_2: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(cursor\.execute|execute)\s*\(\s*["'].*%s.*["']\s*%"#).unwrap()
});

pub static SQL_INJECTION_3: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)preparedStatement\s*=\s*connection\.prepareStatement\s*\(\s*["'].*\+.*["']"#).unwrap()
});

pub static SQL_INJECTION_4: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(query|execute)\s*\(\s*`.*\$\{.*\}`"#).unwrap()
});

pub static SQL_INJECTION_5: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)mysql_query\s*\(\s*["'].*\.\s*\$"#).unwrap()
});

pub static XSS_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)innerHTML\s*=\s*[^"']"#).unwrap()
});

pub static XSS_2: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)document\.write\s*\("#).unwrap()
});

pub static XSS_3: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)\$\s*\([^)]+\)\.html\s*\("#).unwrap()
});

pub static CMD_INJECTION_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)exec\s*\(\s*["'`].*\$"#).unwrap()
});

pub static CMD_INJECTION_2: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)os\.system\s*\("#).unwrap()
});

pub static CMD_INJECTION_3: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"`[^`]*\#\{[^}]+\}[^`]*`"#).unwrap()
});

pub static PATH_TRAVERSAL_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(open|readFile|createReadStream)\s*\(\s*["'].*\+.*["']"#).unwrap()
});

pub static PATH_TRAVERSAL_2: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(include|require|include_once|require_once)\s*\(\s*\$"#).unwrap()
});

pub static HARDCODED_SECRET_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(api[_-]?key|apikey)\s*[:=]\s*["'][0-9a-zA-Z]{20,}["']"#).unwrap()
});

pub static HARDCODED_SECRET_2: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"AKIA[0-9A-Z]{16}"#).unwrap()
});

pub static HARDCODED_SECRET_3: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"-----BEGIN (RSA |EC |DSA |OPENSSH )?PRIVATE KEY-----"#).unwrap()
});

pub static INSECURE_RANDOM_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(token|password|secret|key|salt|iv|nonce).*Math\.random\(\)"#).unwrap()
});

pub static INSECURE_RANDOM_2: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(token|password|secret|key|salt|iv|nonce).*random\.(random|randint|choice)"#).unwrap()
});

pub static XXE_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)\.setFeature\s*\(\s*["'].*external.*["']\s*,\s*true"#).unwrap()
});

pub static LDAP_INJECTION_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)ldap.*search.*filter.*\+.*["']"#).unwrap()
});

pub static DESERIALIZATION_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)pickle\.loads?\("#).unwrap()
});

pub static DESERIALIZATION_2: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)new\s+ObjectInputStream"#).unwrap()
});

pub static MISCONFIG_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(debug|DEBUG)\s*[:=]\s*(true|True|TRUE|1)"#).unwrap()
});

pub static MISCONFIG_2: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(TLSv1\.0|TLSv1\.1|SSLv2|SSLv3)"#).unwrap()
});

pub static MISCONFIG_3: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)Access-Control-Allow-Origin.*\*"#).unwrap()
});

pub static BUFFER_OVERFLOW_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"\bstrcpy\s*\("#).unwrap()
});

pub static BUFFER_OVERFLOW_2: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"\bgets\s*\("#).unwrap()
});

pub static BUFFER_OVERFLOW_3: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"\bsprintf\s*\("#).unwrap()
});

pub static RESPONSE_SPLITTING_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(setHeader|addHeader|header)\s*\([^,]+,\s*[^)]*\+[^)]*\)"#).unwrap()
});

pub static WEAK_CRYPTO_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)\b(MD5|md5)\s*\("#).unwrap()
});

pub static WEAK_CRYPTO_2: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)\b(SHA1|sha1)\s*\("#).unwrap()
});

pub static WEAK_CRYPTO_3: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)\b(DES|DESede)\s*\("#).unwrap()
});

pub static NOSQL_INJECTION_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)\.find\s*\(\s*\{[^}]*\$where"#).unwrap()
});

pub static NOSQL_INJECTION_2: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)db\.eval\s*\("#).unwrap()
});

pub static TEMPLATE_INJECTION_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)\{\{.*\|safe"#).unwrap()
});

pub static OPEN_REDIRECT_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)redirect.*request\.(GET|POST|params)\["#).unwrap()
});

pub static COOKIE_SECURITY_1: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)set[_-]?cookie"#).unwrap()
});