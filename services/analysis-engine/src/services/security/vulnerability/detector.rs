//! Core vulnerability detection logic

use super::patterns::{VulnerabilityPattern, load_vulnerability_patterns};
use super::ml_classifier::MlVulnerabilityClassifier;
use crate::models::security::{
    SecurityVulnerability, VulnerabilityType, SecuritySeverity, SecurityScanDepth,
};
use crate::models::{AstNode, FileAnalysis};
use crate::errors::AnalysisResult;
use uuid::Uuid;
use chrono::Utc;

/// Detects security vulnerabilities in source code using ML-enhanced SAST
pub struct VulnerabilityDetector {
    patterns: Vec<VulnerabilityPattern>,
    ml_classifier: Option<MlVulnerabilityClassifier>,
}

impl VulnerabilityDetector {
    pub fn new() -> Self {
        Self {
            patterns: load_vulnerability_patterns(),
            ml_classifier: Some(MlVulnerabilityClassifier::new()),
        }
    }

    pub async fn detect_vulnerabilities(
        &self,
        analysis_id: &str,
        file_analysis: &FileAnalysis,
        scan_depth: &SecurityScanDepth,
    ) -> AnalysisResult<Vec<SecurityVulnerability>> {
        let mut vulnerabilities = Vec::new();

        // Pattern-based detection
        let pattern_vulns = self.detect_pattern_vulnerabilities(analysis_id, file_analysis)?;
        vulnerabilities.extend(pattern_vulns);

        // ML-enhanced detection for deeper analysis
        if matches!(scan_depth, SecurityScanDepth::Standard | SecurityScanDepth::Deep) {
            if let Some(ref classifier) = self.ml_classifier {
                let ml_vulns = classifier
                    .classify_vulnerabilities(analysis_id, file_analysis)
                    .await?;
                vulnerabilities.extend(ml_vulns);
            }
        }

        // AST-based detection for deep scans
        if matches!(scan_depth, SecurityScanDepth::Deep) {
            let ast_vulns = self.detect_ast_vulnerabilities(analysis_id, file_analysis)?;
            vulnerabilities.extend(ast_vulns);
        }

        Ok(vulnerabilities)
    }

    fn detect_pattern_vulnerabilities(
        &self,
        analysis_id: &str,
        file_analysis: &FileAnalysis,
    ) -> AnalysisResult<Vec<SecurityVulnerability>> {
        let mut vulnerabilities = Vec::new();
        let file_content = self.extract_file_content(file_analysis);

        for pattern in &self.patterns {
            if pattern.applies_to_language(&file_analysis.language) {
                let matches = pattern.find_matches(&file_content);
                for match_info in matches {
                    let vulnerability = SecurityVulnerability {
                        vulnerability_id: Uuid::new_v4().to_string(),
                        analysis_id: analysis_id.to_string(),
                        cve_id: pattern.cve_id.clone(),
                        cwe_id: pattern.cwe_id.clone(),
                        vulnerability_type: pattern.vulnerability_type.clone(),
                        severity: pattern.severity.clone(),
                        confidence_score: 0.8, // Default confidence for regex matches
                        file_path: file_analysis.path.clone(),
                        line_start: Some(match_info.line_number as i64),
                        line_end: Some(match_info.line_number as i64),
                        code_snippet: Some(match_info.code_snippet),
                        description: pattern.description.clone(),
                        remediation_advice: pattern.remediation.clone(),
                        owasp_category: pattern.owasp_category.clone(),
                        attack_vector: pattern.attack_vector.clone(),
                        exploitability_score: pattern.exploitability_score,
                        impact_score: pattern.impact_score,
                        false_positive_probability: Some(pattern.false_positive_rate),
                        created_at: Utc::now(),
                        updated_at: None,
                    };
                    vulnerabilities.push(vulnerability);
                }
            }
        }

        Ok(vulnerabilities)
    }

    fn detect_ast_vulnerabilities(
        &self,
        analysis_id: &str,
        file_analysis: &FileAnalysis,
    ) -> AnalysisResult<Vec<SecurityVulnerability>> {
        let mut vulnerabilities = Vec::new();

        // Analyze AST for complex vulnerabilities
        self.analyze_ast_node(analysis_id, &file_analysis.ast, &file_analysis.path, &mut vulnerabilities)?;

        Ok(vulnerabilities)
    }

    fn analyze_ast_node(
        &self,
        analysis_id: &str,
        node: &AstNode,
        file_path: &str,
        vulnerabilities: &mut Vec<SecurityVulnerability>,
    ) -> AnalysisResult<()> {
        // Check for dangerous function calls
        if node.node_type == "call_expression" {
            if let Some(name) = &node.name {
                if self.is_dangerous_function(name) {
                    let vulnerability = SecurityVulnerability {
                        vulnerability_id: Uuid::new_v4().to_string(),
                        analysis_id: analysis_id.to_string(),
                        cve_id: None,
                        cwe_id: Some("CWE-676".to_string()), // Use of Potentially Dangerous Function
                        vulnerability_type: VulnerabilityType::CodeInjection,
                        severity: SecuritySeverity::Medium,
                        confidence_score: 0.7,
                        file_path: file_path.to_string(),
                        line_start: Some(node.range.start.line as i64),
                        line_end: Some(node.range.end.line as i64),
                        code_snippet: node.text.clone(),
                        description: format!("Potentially dangerous function call: {}", name),
                        remediation_advice: Some("Review the use of this function and ensure proper input validation".to_string()),
                        owasp_category: Some("A03:2021-Injection".to_string()),
                        attack_vector: Some("Code Injection".to_string()),
                        exploitability_score: Some(0.6),
                        impact_score: Some(0.8),
                        false_positive_probability: Some(0.3),
                        created_at: Utc::now(),
                        updated_at: None,
                    };
                    vulnerabilities.push(vulnerability);
                }
            }
        }

        // Recursively analyze child nodes
        for child in &node.children {
            self.analyze_ast_node(analysis_id, child, file_path, vulnerabilities)?;
        }

        Ok(())
    }

    fn is_dangerous_function(&self, function_name: &str) -> bool {
        let dangerous_functions = [
            "eval", "exec", "system", "shell_exec", "passthru",
            "strcpy", "strcat", "sprintf", "gets",
            "innerHTML", "document.write",
        ];
        dangerous_functions.contains(&function_name)
    }

    fn extract_file_content(&self, file_analysis: &FileAnalysis) -> String {
        // Extract content from AST or use placeholder
        file_analysis.ast.text.clone().unwrap_or_default()
    }
}

impl Default for VulnerabilityDetector {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_dangerous_function_detection() {
        let detector = VulnerabilityDetector::new();
        assert!(detector.is_dangerous_function("eval"));
        assert!(detector.is_dangerous_function("system"));
        assert!(!detector.is_dangerous_function("print"));
    }
}