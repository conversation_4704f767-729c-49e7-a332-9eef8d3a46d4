//! Shared types and traits for the security module

use crate::errors::AnalysisResult;
use crate::models::FileAnalysis;
use crate::models::production::DependencyInfo;
use async_trait::async_trait;

/// Information about a pattern match in source code
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct MatchInfo {
    pub line_number: usize,
    pub code_snippet: String,
}

/// Vulnerability information from dependency databases
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct DependencyVulnInfo {
    pub package_name: String,
    pub current_version: String,
    pub vulnerable_versions: Vec<String>,
    pub cve_id: Option<String>,
    pub severity: String,
    pub description: String,
    pub fixed_version: Option<String>,
    pub published_date: Option<String>,
    pub advisory_url: Option<String>,
}

/// Trait for dependency file parsers
#[async_trait]
pub trait DependencyParser: Send + Sync {
    /// Parse a dependency file and extract dependency information
    async fn parse(&self, file_analysis: &FileAnalysis) -> AnalysisResult<Vec<DependencyInfo>>;
    
    /// Check if this parser can handle the given file
    fn can_parse(&self, file_path: &str) -> bool;
    
    /// Get the package manager type this parser handles
    fn package_manager(&self) -> &'static str;
}

/// Common helper functions for security analysis
pub mod helpers {
    use regex::Regex;
    
    /// Check if a string contains a potential secret pattern
    pub fn looks_like_secret(value: &str) -> bool {
        // Quick heuristics for secret detection
        value.len() >= 16 &&
        !value.contains(' ') &&
        has_mixed_case_and_numbers(value)
    }
    
    fn has_mixed_case_and_numbers(s: &str) -> bool {
        let has_upper = s.chars().any(|c| c.is_uppercase());
        let has_lower = s.chars().any(|c| c.is_lowercase());
        let has_digit = s.chars().any(|c| c.is_numeric());
        has_upper && has_lower && has_digit
    }
    
    /// Compile a regex pattern with error handling
    pub fn compile_pattern(pattern: &str, name: &str) -> Result<Regex, String> {
        Regex::new(pattern).map_err(|e| {
            tracing::error!("Failed to compile pattern '{}': {}", name, e);
            format!("Failed to compile pattern '{}': {}", name, e)
        })
    }
}

#[cfg(test)]
mod tests {
    use super::helpers::*;
    
    #[test]
    fn test_looks_like_secret() {
        assert!(looks_like_secret("Ks9d8fKLsd9f8sdf9SD"));
        assert!(!looks_like_secret("hello world"));
        assert!(!looks_like_secret("short"));
        assert!(!looks_like_secret("ALLLOWERCASESTRING"));
    }
}