//! Core secrets detection logic

use super::patterns::{SecretPattern, load_secret_patterns};
use crate::models::security::DetectedSecret;
use crate::models::FileAnalysis;
use crate::errors::AnalysisResult;
use uuid::Uuid;
use chrono::Utc;

/// Detects hardcoded secrets and sensitive information in source code
pub struct SecretsDetector {
    patterns: Vec<SecretPattern>,
}

impl SecretsDetector {
    pub fn new() -> Self {
        Self {
            patterns: load_secret_patterns(),
        }
    }

    pub async fn detect_secrets(
        &self,
        analysis_id: &str,
        file_analysis: &FileAnalysis,
    ) -> AnalysisResult<Vec<DetectedSecret>> {
        let mut secrets = Vec::new();
        let content = file_analysis.ast.text.clone().unwrap_or_default();

        for pattern in &self.patterns {
            for captures in pattern.regex.captures_iter(&content) {
                if let Some(matched) = captures.get(0) {
                    let (line_number, line_content) = self.find_line_info(&content, matched.start());
                    
                    // Check if this looks like a false positive
                    if self.is_likely_false_positive(&line_content, matched.as_str()) {
                        continue;
                    }
                    
                    secrets.push(DetectedSecret {
                        secret_id: Uuid::new_v4().to_string(),
                        analysis_id: analysis_id.to_string(),
                        file_path: file_analysis.path.clone(),
                        secret_type: pattern.secret_type.clone(),
                        line_number: Some(line_number as i64),
                        secret_hash: None, // Could compute SHA-256 hash here
                        entropy_score: Some(self.calculate_entropy(matched.as_str())),
                        pattern_name: pattern.secret_type.as_str().to_string(),
                        confidence_score: pattern.confidence,
                        is_false_positive: false,
                        is_test_data: self.is_test_file(&file_analysis.path),
                        severity: pattern.severity.clone(),
                        context: Some(line_content),
                        masked_value: Some(self.mask_secret(matched.as_str())),
                        created_at: Utc::now(),
                        updated_at: None,
                    });
                }
            }
        }

        Ok(secrets)
    }

    fn find_line_info(&self, content: &str, position: usize) -> (usize, String) {
        let mut line_number = 1;
        let mut line_start = 0;
        for (i, c) in content.char_indices() {
            if i >= position {
                break;
            }
            if c == '\n' {
                line_number += 1;
                line_start = i + 1;
            }
        }
        let line_end = content[line_start..]
            .find('\n')
            .map(|i| line_start + i)
            .unwrap_or_else(|| content.len());
        (line_number, content[line_start..line_end].to_string())
    }

    fn is_likely_false_positive(&self, line: &str, secret: &str) -> bool {
        let line_lower = line.to_lowercase();
        
        // Check for common false positive patterns
        if line_lower.contains("example") || 
           line_lower.contains("placeholder") ||
           line_lower.contains("your-") ||
           line_lower.contains("xxxxxx") ||
           line_lower.contains("dummy") ||
           line_lower.contains("fake") ||
           line_lower.contains("mock") {
            return true;
        }
        
        // Check if the secret itself looks like a placeholder
        if secret.contains("YOUR_") || 
           secret.contains("EXAMPLE_") ||
           secret.contains("XXX") ||
           secret.contains("...") ||
           secret.contains("123456") ||
           secret == "password" ||
           secret == "secret" {
            return true;
        }
        
        false
    }

    fn is_test_file(&self, path: &str) -> bool {
        let path_lower = path.to_lowercase();
        path_lower.contains("/test/") || 
        path_lower.contains("/tests/") ||
        path_lower.contains("_test.") ||
        path_lower.contains(".test.") ||
        path_lower.contains("/spec/") ||
        path_lower.contains("_spec.")
    }

    fn mask_secret(&self, secret: &str) -> String {
        let len = secret.len();
        if len <= 4 {
            "*".repeat(len)
        } else if len <= 8 {
            format!("{}***", &secret[..2])
        } else {
            format!("{}...{}", &secret[..3], &secret[len-3..])
        }
    }

    fn calculate_entropy(&self, s: &str) -> f64 {
        let mut char_counts = std::collections::HashMap::new();
        for c in s.chars() {
            *char_counts.entry(c).or_insert(0) += 1;
        }
        
        let len = s.len() as f64;
        let mut entropy = 0.0;
        
        for count in char_counts.values() {
            let probability = *count as f64 / len;
            entropy -= probability * probability.log2();
        }
        
        entropy
    }
}

impl Default for SecretsDetector {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_entropy_calculation() {
        let detector = SecretsDetector::new();
        
        // Low entropy (repeated characters)
        assert!(detector.calculate_entropy("aaaaaaa") < 1.0);
        
        // High entropy (random-looking)
        assert!(detector.calculate_entropy("a7B#xK9$mP2@") > 3.0);
    }
    
    #[test]
    fn test_secret_masking() {
        let detector = SecretsDetector::new();
        
        assert_eq!(detector.mask_secret("abc"), "***");
        assert_eq!(detector.mask_secret("secret"), "se***");
        assert_eq!(detector.mask_secret("verylongsecret"), "ver...ret");
    }
    
    #[test]
    fn test_false_positive_detection() {
        let detector = SecretsDetector::new();
        
        assert!(detector.is_likely_false_positive("api_key = 'YOUR_API_KEY_HERE'", "YOUR_API_KEY_HERE"));
        assert!(detector.is_likely_false_positive("password = 'example123'", "example123"));
        assert!(!detector.is_likely_false_positive("api_key = 'sk_live_abcd1234'", "sk_live_abcd1234"));
    }
}