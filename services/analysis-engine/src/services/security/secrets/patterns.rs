//! Secret detection patterns

use crate::models::security::{SecretType, SecuritySeverity};
use regex::Regex;
use once_cell::sync::Lazy;

#[derive(Debug)]
pub struct SecretPattern {
    pub secret_type: SecretType,
    pub regex: &'static Lazy<Regex>,
    pub confidence: f64,
    pub severity: SecuritySeverity,
}

static API_KEY_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(api[_-]?key|secret[_-]?key)\s*[:=]\s*["'][A-Za-z0-9+/=]{20,}["']"#).unwrap()
});

static AWS_ACCESS_KEY_PATTERN: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"AKIA[0-9A-Z]{16}"#).unwrap());

static AWS_SECRET_KEY_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)aws[_-]?secret[_-]?access[_-]?key\s*[:=]\s*["'][A-Za-z0-9+/]{40}["']"#).unwrap()
});

static PRIVATE_KEY_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"-----BEGIN (RSA |EC |DSA |OPENSSH )?PRIVATE KEY-----"#).unwrap()
});

static GITHUB_TOKEN_PATTERN: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"gh[pousr]_[A-Za-z0-9_]{36}"#).unwrap());

static GITLAB_TOKEN_PATTERN: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"glpat-[A-Za-z0-9\-_]{20}"#).unwrap());

static SLACK_TOKEN_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"xox[baprs]-[0-9]{10,13}-[0-9]{10,13}-[a-zA-Z0-9]{24,34}"#).unwrap()
});

static JWT_PATTERN: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"eyJ[A-Za-z0-9-_]+\.eyJ[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+"#).unwrap());

static DATABASE_CONNECTION_STRING_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(mongodb|mysql|postgres|postgresql|mssql|oracle|redis)://[^:]+:[^@]+@[^/\s]+"#)
        .unwrap()
});

static GOOGLE_API_KEY_PATTERN: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"AIza[0-9A-Za-z\-_]{35}"#).unwrap());

static STRIPE_API_KEY_PATTERN: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"sk_live_[0-9a-zA-Z]{24,}"#).unwrap());

static AZURE_CLIENT_SECRET_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)azure[_-]?client[_-]?secret\s*[:=]\s*["'][A-Za-z0-9+/=~_.-]{34,}["']"#)
        .unwrap()
});

static SLACK_WEBHOOK_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"https://hooks\.slack\.com/services/T[A-Z0-9]{8}/B[A-Z0-9]{8}/[A-Za-z0-9]{24}"#)
        .unwrap()
});

static TWILIO_API_KEY_PATTERN: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"SK[a-f0-9]{32}"#).unwrap());

static SENDGRID_API_KEY_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"SG\.[A-Za-z0-9\-_]{22}\.[A-Za-z0-9\-_]{43}"#).unwrap()
});

static MAILGUN_API_KEY_PATTERN: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"key-[a-f0-9]{32}"#).unwrap());

static SSH_PRIVATE_KEY_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"-----BEGIN (?:RSA|DSA|EC|OPENSSH) PRIVATE KEY-----"#).unwrap()
});

static PGP_PRIVATE_KEY_PATTERN: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"-----BEGIN PGP PRIVATE KEY BLOCK-----"#).unwrap());

static PASSWORD_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(password|passwd|pwd)\s*[:=]\s*["'][^"']{8,}["']"#).unwrap()
});

static OAUTH_CLIENT_SECRET_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"(?i)(client[_-]?secret|oauth[_-]?secret)\s*[:=]\s*["'][A-Za-z0-9+/=_-]{20,}["']"#)
        .unwrap()
});

static HEROKU_API_KEY_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r#"[hH][eE][rR][oO][kK][uU].*[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}"#)
        .unwrap()
});

static DIGITALOCEAN_TOKEN_PATTERN: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"dop_v1_[a-f0-9]{64}"#).unwrap());

static NPM_TOKEN_PATTERN: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"npm_[A-Za-z0-9]{36}"#).unwrap());

static PYPI_TOKEN_PATTERN: Lazy<Regex> =
    Lazy::new(|| Regex::new(r#"pypi-[A-Za-z0-9_-]{40,}"#).unwrap());


/// Load all secret detection patterns
pub fn load_secret_patterns() -> Vec<SecretPattern> {
    vec![
        SecretPattern {
            secret_type: SecretType::ApiKey,
            regex: &API_KEY_PATTERN,
            confidence: 0.8,
            severity: SecuritySeverity::High,
        },
        SecretPattern {
            secret_type: SecretType::AwsAccessKey,
            regex: &AWS_ACCESS_KEY_PATTERN,
            confidence: 0.9,
            severity: SecuritySeverity::Critical,
        },
        SecretPattern {
            secret_type: SecretType::AwsSecretKey,
            regex: &AWS_SECRET_KEY_PATTERN,
            confidence: 0.85,
            severity: SecuritySeverity::Critical,
        },
        SecretPattern {
            secret_type: SecretType::PrivateKey,
            regex: &PRIVATE_KEY_PATTERN,
            confidence: 1.0,
            severity: SecuritySeverity::Critical,
        },
        SecretPattern {
            secret_type: SecretType::GithubToken,
            regex: &GITHUB_TOKEN_PATTERN,
            confidence: 0.95,
            severity: SecuritySeverity::Critical,
        },
        SecretPattern {
            secret_type: SecretType::GitlabToken,
            regex: &GITLAB_TOKEN_PATTERN,
            confidence: 0.95,
            severity: SecuritySeverity::Critical,
        },
        SecretPattern {
            secret_type: SecretType::SlackToken,
            regex: &SLACK_TOKEN_PATTERN,
            confidence: 0.95,
            severity: SecuritySeverity::High,
        },
        SecretPattern {
            secret_type: SecretType::Jwt,
            regex: &JWT_PATTERN,
            confidence: 0.9,
            severity: SecuritySeverity::High,
        },
        SecretPattern {
            secret_type: SecretType::DatabaseConnectionString,
            regex: &DATABASE_CONNECTION_STRING_PATTERN,
            confidence: 0.85,
            severity: SecuritySeverity::Critical,
        },
        SecretPattern {
            secret_type: SecretType::GoogleApiKey,
            regex: &GOOGLE_API_KEY_PATTERN,
            confidence: 0.9,
            severity: SecuritySeverity::High,
        },
        SecretPattern {
            secret_type: SecretType::StripeApiKey,
            regex: &STRIPE_API_KEY_PATTERN,
            confidence: 0.95,
            severity: SecuritySeverity::Critical,
        },
        SecretPattern {
            secret_type: SecretType::AzureClientSecret,
            regex: &AZURE_CLIENT_SECRET_PATTERN,
            confidence: 0.85,
            severity: SecuritySeverity::High,
        },
        SecretPattern {
            secret_type: SecretType::SlackWebhook,
            regex: &SLACK_WEBHOOK_PATTERN,
            confidence: 0.95,
            severity: SecuritySeverity::Medium,
        },
        SecretPattern {
            secret_type: SecretType::TwilioApiKey,
            regex: &TWILIO_API_KEY_PATTERN,
            confidence: 0.9,
            severity: SecuritySeverity::High,
        },
        SecretPattern {
            secret_type: SecretType::SendgridApiKey,
            regex: &SENDGRID_API_KEY_PATTERN,
            confidence: 0.95,
            severity: SecuritySeverity::High,
        },
        SecretPattern {
            secret_type: SecretType::MailgunApiKey,
            regex: &MAILGUN_API_KEY_PATTERN,
            confidence: 0.85,
            severity: SecuritySeverity::High,
        },
        SecretPattern {
            secret_type: SecretType::SshPrivateKey,
            regex: &SSH_PRIVATE_KEY_PATTERN,
            confidence: 1.0,
            severity: SecuritySeverity::Critical,
        },
        SecretPattern {
            secret_type: SecretType::PgpPrivateKey,
            regex: &PGP_PRIVATE_KEY_PATTERN,
            confidence: 1.0,
            severity: SecuritySeverity::Critical,
        },
        SecretPattern {
            secret_type: SecretType::Password,
            regex: &PASSWORD_PATTERN,
            confidence: 0.6,
            severity: SecuritySeverity::High,
        },
        SecretPattern {
            secret_type: SecretType::OauthClientSecret,
            regex: &OAUTH_CLIENT_SECRET_PATTERN,
            confidence: 0.8,
            severity: SecuritySeverity::High,
        },
        SecretPattern {
            secret_type: SecretType::HerokuApiKey,
            regex: &HEROKU_API_KEY_PATTERN,
            confidence: 0.85,
            severity: SecuritySeverity::High,
        },
        SecretPattern {
            secret_type: SecretType::DigitalOceanToken,
            regex: &DIGITALOCEAN_TOKEN_PATTERN,
            confidence: 0.95,
            severity: SecuritySeverity::High,
        },
        SecretPattern {
            secret_type: SecretType::NpmToken,
            regex: &NPM_TOKEN_PATTERN,
            confidence: 0.9,
            severity: SecuritySeverity::High,
        },
        SecretPattern {
            secret_type: SecretType::PypiToken,
            regex: &PYPI_TOKEN_PATTERN,
            confidence: 0.9,
            severity: SecuritySeverity::High,
        },
    ]
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_pattern_loading() {
        let patterns = load_secret_patterns();
        assert!(!patterns.is_empty());
        
        // Check that we have patterns for critical secret types
        let has_aws = patterns.iter().any(|p| matches!(p.secret_type, SecretType::AwsAccessKey));
        let has_private_key = patterns.iter().any(|p| matches!(p.secret_type, SecretType::PrivateKey));
        
        assert!(has_aws);
        assert!(has_private_key);
    }
}