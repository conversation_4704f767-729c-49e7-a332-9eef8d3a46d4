use crate::git::GitService;
use crate::models::*;
use crate::storage::CacheManager;
use anyhow::{Context, Result};
use chrono::{DateTime, Utc};
use std::path::{Path, PathBuf};
use std::sync::Arc;
use walkdir::WalkDir;

pub struct RepositoryManager {
    git_service: Arc<GitService>,
    cache_manager: Arc<CacheManager>,
}

impl RepositoryManager {
    pub fn new(git_service: Arc<GitService>, cache_manager: Arc<CacheManager>) -> Self {
        Self {
            git_service,
            cache_manager,
        }
    }

    /// Check cache for existing analysis results with commit hash validation
    pub async fn check_cache_and_get_result(
        &self,
        cache_key: &str,
        repository_url: &str,
        branch: &Option<String>,
        analysis_id: &str,
        start_time: DateTime<Utc>,
        warnings: &mut Vec<AnalysisWarning>,
        performance_metrics: &mut PerformanceMetrics,
    ) -> Result<Option<AnalysisResult>> {
        // Get current commit hash from remote repository
        match self.git_service.get_remote_commit_hash(repository_url, branch).await {
            Ok(current_commit_hash) => {
                if let Ok(Some(cached_analyses)) = self.cache_manager
                    .get_analysis_with_commit_check(cache_key, &current_commit_hash)
                    .await
                {
                    tracing::info!("Cache hit with fresh commit hash for repository: {}", repository_url);

                    // Return cached result
                    return Ok(Some(AnalysisResult {
                        id: analysis_id.to_string(),
                        repository_url: repository_url.to_string(),
                        branch: branch.clone().unwrap_or_else(|| "main".to_string()),
                        commit_hash: Some(current_commit_hash),
                        repository_size_bytes: None,
                        clone_time_ms: Some(0),
                        status: AnalysisStatus::Completed,
                        started_at: start_time,
                        completed_at: Some(Utc::now()),
                        duration_seconds: Some((Utc::now() - start_time).num_seconds() as u64),
                        file_count: cached_analyses.len(),
                        success_rate: 1.0,
                        progress: Some(100.0),
                        current_stage: Some("Completed".to_string()),
                        estimated_completion: None,
                        patterns: vec![],
                        languages: std::collections::HashMap::new(),
                        embeddings: None,
                        successful_analyses: Some(cached_analyses),
                        user_id: "anonymous".to_string(),
                        webhook_url: None,
                        failed_files: vec![],
                        metrics: None,
                        performance_metrics: Some(performance_metrics.clone()),
                        error_message: None,
                        warnings: vec![],
                    }));
                } else {
                    tracing::info!(
                        "Cache miss or stale cache for repository: {} (commit: {})",
                        repository_url,
                        current_commit_hash
                    );
                }
            }
            Err(e) => {
                tracing::warn!("Failed to get remote commit hash for cache validation: {}", e);
                warnings.push(AnalysisWarning::new(
                    WarningType::GitError,
                    format!("Failed to get remote commit hash: {}", e),
                    WarningSeverity::Medium,
                ));
            }
        }
        Ok(None)
    }

    /// Clone repository with timing and return path, clone time, and commit hash
    pub async fn clone_repository(
        &self,
        repository_url: &str,
        branch: &Option<String>,
        analysis_id: &str,
        warnings: &mut Vec<AnalysisWarning>,
    ) -> Result<(PathBuf, u64, String)> {
        let clone_start = std::time::Instant::now();
        let repo_path = self
            .git_service
            .clone_repository_with_auth(repository_url, branch, analysis_id)
            .await
            .context("Failed to clone repository")?;
        let clone_time_ms = clone_start.elapsed().as_millis() as u64;

        // Check for slow clone times and add warning
        if clone_time_ms > 60_000 {
            warnings.push(AnalysisWarning::new(
                WarningType::TimeoutRisk,
                format!(
                    "Repository clone took {} seconds, which may indicate a large repository",
                    clone_time_ms / 1000
                ),
                WarningSeverity::Medium,
            ));
        }

        // Get the actual commit hash from the cloned repository
        let actual_commit_hash = self
            .git_service
            .get_current_commit_hash(&repo_path)
            .context("Failed to get commit hash from cloned repository")?;
        
        tracing::info!(
            "Cloned repository {} at commit {}",
            repository_url,
            actual_commit_hash
        );

        Ok((repo_path, clone_time_ms, actual_commit_hash))
    }

    /// Calculate the total size of a directory in bytes
    pub async fn calculate_directory_size(&self, path: &Path) -> Result<u64> {
        let mut total_size = 0u64;

        for entry in WalkDir::new(path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_file() {
                if let Ok(metadata) = entry.metadata() {
                    total_size += metadata.len();
                }
            }
        }

        Ok(total_size)
    }

    /// Check repository size and add warning if needed
    pub fn check_repository_size(&self, size_bytes: u64, warnings: &mut Vec<AnalysisWarning>) {
        if size_bytes > 1_000_000_000 {
            warnings.push(AnalysisWarning::new(
                WarningType::LargeFile,
                format!(
                    "Repository size is {} MB, which may impact analysis performance",
                    size_bytes / 1_000_000
                ),
                WarningSeverity::Medium,
            ));
        }
    }

    /// Cache analysis results with patterns and embeddings
    pub async fn cache_results(
        &self,
        cache_key: &str,
        successful_analyses: &[FileAnalysis],
        commit_hash: &str,
        patterns: &[DetectedPattern],
        embeddings: &Option<Vec<CodeEmbedding>>,
    ) {
        // Cache the successful analyses for future use with commit hash
        if let Err(e) = self
            .cache_manager
            .set_analysis_with_commit(cache_key, successful_analyses, commit_hash)
            .await
        {
            tracing::warn!("Failed to cache analysis results: {}", e);
        }

        // Cache patterns separately for pattern detection reuse
        if !patterns.is_empty() {
            for analysis in successful_analyses {
                let file_patterns: Vec<String> = patterns
                    .iter()
                    .filter(|p| p.location.file_path == analysis.path)
                    .map(|p| serde_json::to_string(p).unwrap_or_default())
                    .collect();

                if !file_patterns.is_empty() {
                    if let Err(e) = self
                        .cache_manager
                        .set_patterns(&analysis.content_hash, &file_patterns)
                        .await
                    {
                        tracing::warn!(
                            "Failed to cache patterns for file {}: {}",
                            analysis.path,
                            e
                        );
                    }
                }
            }
        }

        // Cache embeddings separately for embedding reuse
        if let Some(ref emb_list) = embeddings {
            for embedding in emb_list {
                if let Err(e) = self
                    .cache_manager
                    .set_embeddings(&embedding.chunk_id, &embedding.vector)
                    .await
                {
                    tracing::warn!(
                        "Failed to cache embedding for chunk {}: {}",
                        embedding.chunk_id,
                        e
                    );
                }
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use std::fs;

    #[tokio::test]
    async fn test_calculate_directory_size() {
        let temp_dir = TempDir::new().unwrap();
        let repo_path = temp_dir.path();

        // Create test files with known sizes
        fs::create_dir_all(repo_path.join("src")).unwrap();
        fs::write(repo_path.join("src/file1.rs"), "a".repeat(1000)).unwrap();
        fs::write(repo_path.join("src/file2.rs"), "b".repeat(2000)).unwrap();
        fs::create_dir_all(repo_path.join("docs")).unwrap();
        fs::write(repo_path.join("docs/readme.md"), "c".repeat(500)).unwrap();

        // Mock services
        let git_service = Arc::new(GitService::new());
        let cache_manager = Arc::new(CacheManager::new(None));
        let repo_manager = RepositoryManager::new(git_service, cache_manager);

        let total_size = repo_manager.calculate_directory_size(repo_path).await.unwrap();
        assert_eq!(total_size, 3500); // 1000 + 2000 + 500
    }

    #[tokio::test]
    async fn test_check_repository_size_warnings() {
        let git_service = Arc::new(GitService::new());
        let cache_manager = Arc::new(CacheManager::new(None));
        let repo_manager = RepositoryManager::new(git_service, cache_manager);

        let mut warnings = Vec::new();

        // Test small repository - no warning
        repo_manager.check_repository_size(500_000_000, &mut warnings);
        assert_eq!(warnings.len(), 0);

        // Test large repository - should add warning
        repo_manager.check_repository_size(2_000_000_000, &mut warnings);
        assert_eq!(warnings.len(), 1);
        assert_eq!(warnings[0].warning_type, WarningType::LargeFile);
    }
}