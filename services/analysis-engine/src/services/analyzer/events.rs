use crate::models::AnalysisResult;
use crate::storage::PubSubOperations;
use anyhow::Result;
use std::sync::Arc;

pub struct EventManager {
    pubsub_client: Arc<PubSubOperations>,
}

impl EventManager {
    pub fn new(pubsub_client: Arc<PubSubOperations>) -> Self {
        Self { pubsub_client }
    }

    /// Publish analysis completion event to PubSub
    pub async fn publish_completion_event(&self, result: &AnalysisResult) -> Result<()> {
        self.pubsub_client.publish_analysis_event(result).await
    }

    /// Send webhook notification for analysis completion
    pub async fn send_webhook_notification(
        &self,
        webhook_url: &str,
        result: &AnalysisResult,
    ) -> Result<()> {
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()?;

        let response = client
            .post(webhook_url)
            .json(result)
            .header("Content-Type", "application/json")
            .header("X-Event-Type", "analysis-completed")
            .header("X-Analysis-Id", &result.id)
            .send()
            .await?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "Failed to read error response".to_string());

            return Err(anyhow::anyhow!(
                "Webhook notification failed with status {}: {}",
                status,
                error_text
            ));
        }

        tracing::info!(
            "Successfully sent webhook notification for analysis {} to {}",
            result.id,
            webhook_url
        );

        Ok(())
    }

    /// Publish pattern detection event
    pub async fn publish_pattern_detected_event(
        &self,
        analysis_id: &str,
        file_path: &str,
        pattern_type: &str,
        count: usize,
    ) -> Result<()> {
        self.pubsub_client
            .publish_pattern_detected(file_path, pattern_type, count)
            .await?;

        tracing::debug!(
            "Published pattern detection event for analysis {}: {} patterns of type {} in {}",
            analysis_id,
            count,
            pattern_type,
            file_path
        );

        Ok(())
    }

    /// Publish analysis failure event
    pub async fn publish_failure_event(
        &self,
        analysis_id: &str,
        error_message: &str,
        repository_url: &str,
    ) -> Result<()> {
        let failure_result = AnalysisResult {
            id: analysis_id.to_string(),
            repository_url: repository_url.to_string(),
            status: crate::models::AnalysisStatus::Failed,
            error_message: Some(error_message.to_string()),
            ..Default::default()
        };

        self.pubsub_client
            .publish_analysis_event(&failure_result)
            .await?;

        tracing::info!(
            "Published failure event for analysis {}: {}",
            analysis_id,
            error_message
        );

        Ok(())
    }

    /// Send batch webhook notifications with retry logic
    pub async fn send_batch_webhooks(
        &self,
        webhook_urls: &[String],
        result: &AnalysisResult,
    ) -> Vec<Result<()>> {
        let mut results = Vec::new();

        for webhook_url in webhook_urls {
            let result_clone = result.clone();
            let webhook_url_clone = webhook_url.clone();
            let self_clone = self.clone();

            // Send webhooks concurrently
            let handle = tokio::spawn(async move {
                // Retry logic: 3 attempts with exponential backoff
                let mut attempts = 0;
                let max_attempts = 3;
                let mut delay = tokio::time::Duration::from_secs(1);

                loop {
                    attempts += 1;
                    match self_clone
                        .send_webhook_notification(&webhook_url_clone, &result_clone)
                        .await
                    {
                        Ok(_) => return Ok(()),
                        Err(e) => {
                            if attempts >= max_attempts {
                                tracing::error!(
                                    "Failed to send webhook to {} after {} attempts: {}",
                                    webhook_url_clone,
                                    attempts,
                                    e
                                );
                                return Err(e);
                            }

                            tracing::warn!(
                                "Webhook attempt {} failed for {}: {}, retrying in {:?}",
                                attempts,
                                webhook_url_clone,
                                e,
                                delay
                            );

                            tokio::time::sleep(delay).await;
                            delay *= 2; // Exponential backoff
                        }
                    }
                }
            });

            match handle.await {
                Ok(result) => results.push(result),
                Err(e) => results.push(Err(anyhow::anyhow!("Task join error: {}", e))),
            }
        }

        results
    }

    /// Create a summary event for multiple analyses
    pub async fn publish_batch_completion_event(
        &self,
        analysis_ids: &[String],
        total_success: usize,
        total_failed: usize,
    ) -> Result<()> {
        let summary = serde_json::json!({
            "event_type": "batch_analysis_completed",
            "analysis_ids": analysis_ids,
            "total_analyses": analysis_ids.len(),
            "successful": total_success,
            "failed": total_failed,
            "timestamp": chrono::Utc::now(),
        });

        // TODO: Implement a generic publish method in PubSubOperations or use a different approach
        // self.pubsub_client
        //     .publish_raw_event("batch-analysis-completed", &summary.to_string())
        //     .await?;
        tracing::info!("Batch completion event: {}", summary);

        tracing::info!(
            "Published batch completion event for {} analyses ({} successful, {} failed)",
            analysis_ids.len(),
            total_success,
            total_failed
        );

        Ok(())
    }
}

impl Clone for EventManager {
    fn clone(&self) -> Self {
        Self {
            pubsub_client: self.pubsub_client.clone(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::GcpSettings;
    use crate::storage::gcp_clients;
    use mockito;
    use std::sync::Arc;

    fn create_test_analysis_result() -> AnalysisResult {
        AnalysisResult {
            id: "test-analysis".to_string(),
            repository_url: "https://github.com/test/repo".to_string(),
            branch: "main".to_string(),
            status: crate::models::AnalysisStatus::Completed,
            started_at: chrono::Utc::now(),
            user_id: "test-user".to_string(),
            file_count: 10,
            success_rate: 100.0,
            ..Default::default()
        }
    }

    #[tokio::test]
    async fn test_webhook_notification() {
        let mut server = mockito::Server::new_async().await;
        let mock = server
            .mock("POST", "/webhook")
            .with_status(200)
            .with_header("content-type", "application/json")
            .with_body(r#"{"status": "received"}"#)
            .create();

        let gcp_settings = GcpSettings::default();
        let pubsub_client = Arc::new(
            PubSubOperations::new(gcp_clients::create_pubsub_client(&gcp_settings).await.unwrap())
                .await
                .unwrap(),
        );
        let event_manager = EventManager::new(pubsub_client);

        let result = create_test_analysis_result();
        let webhook_url = format!("{}/webhook", server.url());

        // This should succeed with the mock
        let send_result = event_manager
            .send_webhook_notification(&webhook_url, &result)
            .await;

        mock.assert();
        assert!(send_result.is_ok());
    }

    #[tokio::test]
    async fn test_webhook_notification_failure() {
        let mut server = mockito::Server::new_async().await;
        let mock = server
            .mock("POST", "/webhook")
            .with_status(500)
            .with_body("Internal Server Error")
            .create();

        let gcp_settings = GcpSettings::default();
        let pubsub_client = Arc::new(
            PubSubOperations::new(gcp_clients::create_pubsub_client(&gcp_settings).await.unwrap())
                .await
                .unwrap(),
        );
        let event_manager = EventManager::new(pubsub_client);

        let result = create_test_analysis_result();
        let webhook_url = format!("{}/webhook", server.url());

        // This should fail with 500 status
        let send_result = event_manager
            .send_webhook_notification(&webhook_url, &result)
            .await;

        mock.assert();
        assert!(send_result.is_err());
        assert!(send_result.unwrap_err().to_string().contains("status 500"));
    }

    #[tokio::test]
    async fn test_event_manager_clone() {
        let gcp_settings = GcpSettings::default();
        let pubsub_client = Arc::new(
            PubSubOperations::new(gcp_clients::create_pubsub_client(&gcp_settings).await.unwrap())
                .await
                .unwrap(),
        );
        let event_manager = EventManager::new(pubsub_client);

        let _cloned = event_manager.clone();
        // Just verify it compiles and can be cloned
    }
}
