use crate::models::*;
use anyhow::Result;

pub struct ResultProcessor;

impl ResultProcessor {
    pub fn new() -> Self {
        Self
    }

    /// Partition analysis results into successful and failed, adding warnings as needed
    pub fn partition_results_with_warnings(
        &self,
        results: Vec<Result<FileAnalysis, ParseError>>,
        warnings: &mut Vec<AnalysisWarning>,
    ) -> (Vec<FileAnalysis>, Vec<FailedFile>) {
        let mut successful_analyses = Vec::new();
        let mut failed_files = Vec::new();
        let total_files = results.len();

        for result in results {
            match result {
                Ok(analysis) => {
                    // Check for large files and add warnings
                    if let Some(size) = analysis.size_bytes {
                        if size > 1_000_000 {
                            // Files larger than 1MB
                            warnings.push(AnalysisWarning::with_file_context(
                                WarningType::LargeFile,
                                format!(
                                    "File {} is {} KB, which may impact analysis performance",
                                    analysis.path,
                                    size / 1000
                                ),
                                WarningSeverity::Low,
                                analysis.path.clone(),
                                None,
                            ));
                        }
                    }

                    // Check for high complexity and add warnings
                    if analysis.metrics.complexity > 20 {
                        warnings.push(AnalysisWarning::with_file_context(
                            WarningType::HighComplexity,
                            format!(
                                "File {} has high cyclomatic complexity: {}",
                                analysis.path, analysis.metrics.complexity
                            ),
                            WarningSeverity::Medium,
                            analysis.path.clone(),
                            None,
                        ));
                    }

                    // Check for low maintainability index
                    if analysis.metrics.maintainability_index < 40.0 {
                        warnings.push(AnalysisWarning::with_file_context(
                            WarningType::LowMaintainability,
                            format!(
                                "File {} has low maintainability index: {:.1}",
                                analysis.path, analysis.metrics.maintainability_index
                            ),
                            WarningSeverity::Medium,
                            analysis.path.clone(),
                            None,
                        ));
                    }

                    successful_analyses.push(analysis);
                }
                Err(e) => {
                    // Add warning for parse failure
                    let warning_type = match e.error_type {
                        ParseErrorType::UnsupportedLanguage => WarningType::UnsupportedSyntax,
                        ParseErrorType::Timeout => WarningType::TimeoutRisk,
                        ParseErrorType::FileTooLarge => WarningType::LargeFile,
                        _ => WarningType::ParseError,
                    };

                    warnings.push(AnalysisWarning::with_file_context(
                        warning_type,
                        format!("Failed to parse {}: {}", e.file_path, e.message),
                        WarningSeverity::Medium,
                        e.file_path.clone(),
                        e.position.map(|p| p.line as u32),
                    ));

                    failed_files.push(FailedFile {
                        file_path: e.file_path,
                        error_message: e.message,
                        error_type: e.error_type.into(),
                    });
                }
            }
        }

        // Add summary warnings if needed
        if failed_files.len() > 0 {
            let failure_rate = (failed_files.len() as f64 / total_files as f64) * 100.0;
            if failure_rate > 20.0 {
                warnings.push(AnalysisWarning::new(
                    WarningType::HighFailureRate,
                    format!(
                        "High failure rate: {:.1}% of files failed to parse",
                        failure_rate
                    ),
                    WarningSeverity::High,
                ));
            }
        }

        (successful_analyses, failed_files)
    }

    /// Aggregate metrics from multiple file analyses
    pub fn aggregate_metrics(&self, analyses: &[FileAnalysis]) -> RepositoryMetrics {
        let total_files = analyses.len();
        let total_lines: u32 = analyses
            .iter()
            .filter_map(|a| a.metrics.total_lines)
            .sum();
        let total_loc: u32 = analyses.iter().map(|a| a.metrics.lines_of_code).sum();
        let total_functions: u32 = analyses.iter().map(|a| a.metrics.function_count).sum();
        let total_classes: u32 = analyses.iter().map(|a| a.metrics.class_count).sum();

        let avg_complexity = if total_files > 0 {
            analyses.iter().map(|a| a.metrics.complexity).sum::<u32>() as f64
                / total_files as f64
        } else {
            0.0
        };

        let avg_maintainability = if total_files > 0 {
            analyses
                .iter()
                .map(|a| a.metrics.maintainability_index)
                .sum::<f64>()
                / total_files as f64
        } else {
            0.0
        };

        let avg_comment_ratio = if total_files > 0 {
            analyses
                .iter()
                .map(|a| a.metrics.comment_ratio)
                .sum::<f64>()
                / total_files as f64
        } else {
            0.0
        };

        RepositoryMetrics {
            total_files: total_files as u32,
            total_lines,
            total_lines_of_code: total_loc,
            total_functions,
            total_classes,
            average_complexity: avg_complexity,
            average_maintainability_index: avg_maintainability,
            average_comment_ratio: avg_comment_ratio,
            language_distribution: self.calculate_language_distribution(analyses),
        }
    }

    /// Calculate language distribution from file analyses
    fn calculate_language_distribution(
        &self,
        analyses: &[FileAnalysis],
    ) -> std::collections::HashMap<String, u32> {
        let mut distribution = std::collections::HashMap::new();

        for analysis in analyses {
            *distribution.entry(analysis.language.clone()).or_insert(0) += 1;
        }

        distribution
    }

    /// Create a summary of analysis results
    pub fn create_analysis_summary(&self, result: &AnalysisResult) -> AnalysisSummary {
        let total_patterns = result.patterns.len();
        let pattern_types: std::collections::HashSet<_> = result
            .patterns
            .iter()
            .map(|p| format!("{:?}", p.pattern_type))
            .collect();

        let total_warnings = result.warnings.len();
        let warnings_by_severity = self.group_warnings_by_severity(&result.warnings);

        AnalysisSummary {
            analysis_id: result.id.clone(),
            repository_url: result.repository_url.clone(),
            branch: result.branch.clone(),
            status: result.status.clone(),
            duration_seconds: result.duration_seconds,
            file_count: result.file_count,
            success_rate: result.success_rate,
            total_patterns,
            unique_pattern_types: pattern_types.len(),
            total_warnings,
            warnings_by_severity,
            languages: result.languages.clone(),
            repository_size_mb: result
                .repository_size_bytes
                .map(|b| (b as f64 / 1024.0 / 1024.0)),
            memory_usage_mb: result
                .performance_metrics
                .as_ref()
                .map(|m| m.memory_peak_mb as f64),
        }
    }

    /// Group warnings by severity
    fn group_warnings_by_severity(
        &self,
        warnings: &[AnalysisWarning],
    ) -> std::collections::HashMap<String, usize> {
        let mut groups = std::collections::HashMap::new();

        for warning in warnings {
            let severity_str = format!("{:?}", warning.severity);
            *groups.entry(severity_str).or_insert(0) += 1;
        }

        groups
    }
}

impl Default for ResultProcessor {
    fn default() -> Self {
        Self::new()
    }
}

/// Analysis summary for reporting
#[derive(Debug, Clone)]
pub struct AnalysisSummary {
    pub analysis_id: String,
    pub repository_url: String,
    pub branch: String,
    pub status: AnalysisStatus,
    pub duration_seconds: Option<u64>,
    pub file_count: usize,
    pub success_rate: f64,
    pub total_patterns: usize,
    pub unique_pattern_types: usize,
    pub total_warnings: usize,
    pub warnings_by_severity: std::collections::HashMap<String, usize>,
    pub languages: std::collections::HashMap<String, LanguageStats>,
    pub repository_size_mb: Option<f64>,
    pub memory_usage_mb: Option<f64>,
}

/// Repository-wide metrics
#[derive(Debug, Clone)]
pub struct RepositoryMetrics {
    pub total_files: u32,
    pub total_lines: u32,
    pub total_lines_of_code: u32,
    pub total_functions: u32,
    pub total_classes: u32,
    pub average_complexity: f64,
    pub average_maintainability_index: f64,
    pub average_comment_ratio: f64,
    pub language_distribution: std::collections::HashMap<String, u32>,
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_file_analysis(path: &str, size: u64, complexity: u32) -> FileAnalysis {
        FileAnalysis {
            path: path.to_string(),
            language: "rust".to_string(),
            content_hash: "hash".to_string(),
            size_bytes: Some(size),
            ast: AstNode {
                node_type: "root".to_string(),
                name: None,
                range: Range {
                    start: Position {
                        line: 0,
                        column: 0,
                        byte: 0,
                    },
                    end: Position {
                        line: 10,
                        column: 0,
                        byte: 100,
                    },
                },
                children: vec![],
                properties: None,
                text: None,
            },
            metrics: FileMetrics {
                lines_of_code: 100,
                total_lines: Some(150),
                complexity,
                maintainability_index: 70.0,
                function_count: 5,
                class_count: 2,
                comment_ratio: 0.2,
            },
            chunks: None,
            symbols: None,
        }
    }

    #[test]
    fn test_partition_results() {
        let processor = ResultProcessor::new();
        let mut warnings = Vec::new();

        let results = vec![
            Ok(create_test_file_analysis("small.rs", 500_000, 5)),
            Ok(create_test_file_analysis("large.rs", 2_000_000, 25)),
            Err(ParseError {
                file_path: "error.rs".to_string(),
                error_type: ParseErrorType::ParseError,
                message: "Syntax error".to_string(),
                position: None,
            }),
        ];

        let (successful, failed) = processor.partition_results_with_warnings(results, &mut warnings);

        assert_eq!(successful.len(), 2);
        assert_eq!(failed.len(), 1);
        
        // Should have warnings for large file and high complexity
        assert!(warnings.iter().any(|w| w.warning_type == WarningType::LargeFile));
        assert!(warnings
            .iter()
            .any(|w| w.warning_type == WarningType::HighComplexity));
        assert!(warnings
            .iter()
            .any(|w| w.warning_type == WarningType::ParseError));
    }

    #[test]
    fn test_aggregate_metrics() {
        let processor = ResultProcessor::new();

        let analyses = vec![
            create_test_file_analysis("file1.rs", 1000, 10),
            create_test_file_analysis("file2.rs", 2000, 20),
        ];

        let metrics = processor.aggregate_metrics(&analyses);

        assert_eq!(metrics.total_files, 2);
        assert_eq!(metrics.total_lines_of_code, 200);
        assert_eq!(metrics.total_functions, 10);
        assert_eq!(metrics.total_classes, 4);
        assert_eq!(metrics.average_complexity, 15.0);
        assert_eq!(metrics.average_maintainability_index, 70.0);
        assert_eq!(metrics.language_distribution.get("rust"), Some(&2));
    }

    #[test]
    fn test_high_failure_rate_warning() {
        let processor = ResultProcessor::new();
        let mut warnings = Vec::new();

        // Create results with high failure rate (>20%)
        let mut results = vec![];
        for i in 0..7 {
            results.push(Ok(create_test_file_analysis(&format!("file{}.rs", i), 1000, 5)));
        }
        for i in 0..3 {
            results.push(Err(ParseError {
                file_path: format!("error{}.rs", i),
                error_type: ParseErrorType::ParseError,
                message: "Parse error".to_string(),
                position: None,
            }));
        }

        let (_, _) = processor.partition_results_with_warnings(results, &mut warnings);

        // Should have a high failure rate warning
        assert!(warnings
            .iter()
            .any(|w| w.warning_type == WarningType::HighFailureRate));
    }
}