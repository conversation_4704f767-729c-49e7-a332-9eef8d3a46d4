use crate::backpressure::BackpressureManager;
use crate::config::ServiceConfig;
use crate::models::{AnalysisWarning, PerformanceMetrics, WarningSeverity, WarningType};
use crate::parser::TreeSitterParser;
use anyhow::Result;
use std::sync::Arc;

pub struct PerformanceManager {
    #[allow(dead_code)]
    config: Arc<ServiceConfig>,
    backpressure_manager: Option<Arc<BackpressureManager>>,
}

impl PerformanceManager {
    pub fn new(
        config: Arc<ServiceConfig>,
        backpressure_manager: Option<Arc<BackpressureManager>>,
    ) -> Self {
        Self {
            config,
            backpressure_manager,
        }
    }

    /// Get current performance metrics
    pub async fn get_performance_metrics(
        &self,
        _parser: &TreeSitterParser,
    ) -> Result<PerformanceMetrics> {
        let mut metrics = PerformanceMetrics::default();

        // Get memory usage
        metrics.memory_peak_mb = self.get_peak_memory_usage();

        // Get backpressure metrics if available
        if let Some(bp_manager) = &self.backpressure_manager {
            let bp_metrics = bp_manager.get_metrics().await;
            metrics.memory_peak_mb = bp_metrics.memory_usage_mb;
        }

        // Parser pool utilization metrics will be available when pool stats API is added

        Ok(metrics)
    }

    /// Get available memory on the system
    pub fn get_available_memory(&self) -> u64 {
        // Platform-specific memory check
        #[cfg(target_os = "linux")]
        {
            if let Ok(contents) = std::fs::read_to_string("/proc/meminfo") {
                for line in contents.lines() {
                    if line.starts_with("MemAvailable:") {
                        if let Some(kb_str) = line.split_whitespace().nth(1) {
                            if let Ok(kb) = kb_str.parse::<u64>() {
                                return kb * 1024; // Convert KB to bytes
                            }
                        }
                    }
                }
            }
        }

        #[cfg(target_os = "macos")]
        {
            use std::process::Command;
            if let Ok(output) = Command::new("vm_stat").output() {
                if let Ok(stats) = String::from_utf8(output.stdout) {
                    let mut free_pages = 0u64;
                    let mut inactive_pages = 0u64;
                    let mut page_size = 4096u64; // Default page size

                    for line in stats.lines() {
                        if line.contains("page size of") {
                            if let Some(size_str) = line.split_whitespace().nth(7) {
                                if let Ok(size) = size_str.parse::<u64>() {
                                    page_size = size;
                                }
                            }
                        } else if line.starts_with("Pages free:") {
                            if let Some(pages_str) = line.split(':').nth(1) {
                                if let Ok(pages) = pages_str.trim().trim_end_matches('.').parse::<u64>() {
                                    free_pages = pages;
                                }
                            }
                        } else if line.starts_with("Pages inactive:") {
                            if let Some(pages_str) = line.split(':').nth(1) {
                                if let Ok(pages) = pages_str.trim().trim_end_matches('.').parse::<u64>() {
                                    inactive_pages = pages;
                                }
                            }
                        }
                    }

                    return (free_pages + inactive_pages) * page_size;
                }
            }
        }

        // Windows support would require winapi crate

        // Default to 4GB if we can't determine
        4 * 1024 * 1024 * 1024
    }

    /// Get peak memory usage of the current process
    pub fn get_peak_memory_usage(&self) -> u64 {
        // Platform-specific memory usage tracking
        #[cfg(target_os = "linux")]
        {
            if let Ok(status) = std::fs::read_to_string("/proc/self/status") {
                for line in status.lines() {
                    if line.starts_with("VmPeak:") {
                        if let Some(kb_str) = line.split_whitespace().nth(1) {
                            if let Ok(kb) = kb_str.parse::<u64>() {
                                return kb / 1024; // Convert KB to MB
                            }
                        }
                    }
                }
            }
        }

        #[cfg(target_os = "macos")]
        {
            use std::process::Command;
            use std::process;

            let pid = process::id();
            if let Ok(output) = Command::new("ps")
                .args(&["-o", "rss=", "-p", &pid.to_string()])
                .output()
            {
                if let Ok(stdout) = String::from_utf8(output.stdout) {
                    if let Ok(kb) = stdout.trim().parse::<u64>() {
                        return kb / 1024; // Convert KB to MB
                    }
                }
            }
        }

        // Windows support would require winapi crate

        // Default to 0 if we can't determine
        0
    }

    /// Add memory usage warnings based on current usage
    pub fn add_memory_warnings(&self, warnings: &mut Vec<AnalysisWarning>, memory_usage_mb: u64) {
        if memory_usage_mb > 3000 {
            // More than 3GB
            warnings.push(AnalysisWarning::new(
                WarningType::MemoryLimit,
                format!(
                    "Peak memory usage was {} MB, approaching system limits",
                    memory_usage_mb
                ),
                WarningSeverity::High,
            ));
        } else if memory_usage_mb > 2000 {
            // More than 2GB
            warnings.push(AnalysisWarning::new(
                WarningType::MemoryLimit,
                format!(
                    "Peak memory usage was {} MB, consider monitoring memory usage",
                    memory_usage_mb
                ),
                WarningSeverity::Medium,
            ));
        }
    }

    /// Get optimal batch size based on system resources and load
    pub async fn get_optimal_batch_size(&self, total_items: usize, current_load: usize) -> usize {
        let system_memory = self.get_available_memory();
        let cpu_cores = num_cpus::get();

        // Base batch size on available resources
        let memory_based_batch = (system_memory / (100 * 1024 * 1024)).max(1) as usize; // 100MB per batch
        let cpu_based_batch = cpu_cores * 2; // 2 items per core

        // Choose the minimum to avoid resource exhaustion
        let base_batch_size = memory_based_batch.min(cpu_based_batch);

        // Adjust based on current load
        let load_factor = if current_load > 0 {
            (50.0 / current_load as f64).min(2.0).max(0.5) // Scale between 0.5x and 2x
        } else {
            1.0
        };

        let adaptive_batch_size = ((base_batch_size as f64 * load_factor) as usize)
            .max(1)
            .min(total_items);

        tracing::debug!(
            "Calculated optimal batch size: {} (memory: {}, cpu: {}, load_factor: {:.2})",
            adaptive_batch_size,
            memory_based_batch,
            cpu_based_batch,
            load_factor
        );

        adaptive_batch_size
    }

    /// Check system resource availability
    pub fn check_resource_availability(&self) -> (bool, String) {
        let available_memory = self.get_available_memory();
        let cpu_count = num_cpus::get();

        // Check minimum memory requirement (500MB)
        if available_memory < 500 * 1024 * 1024 {
            return (
                false,
                format!(
                    "Insufficient memory: {} MB available, need at least 500 MB",
                    available_memory / 1024 / 1024
                ),
            );
        }

        // Check CPU availability
        if cpu_count < 1 {
            return (false, "No CPU cores available".to_string());
        }

        (
            true,
            format!(
                "Resources available: {} MB memory, {} CPU cores",
                available_memory / 1024 / 1024,
                cpu_count
            ),
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_config() -> ServiceConfig {
        ServiceConfig {
            service: crate::config::ServiceSettings {
                name: "test".to_string(),
                version: "0.1.0".to_string(),
                port: 8001,
                host: "127.0.0.1".to_string(),
                environment: crate::config::Environment::Development,
            },
            gcp: crate::config::GcpSettings {
                project_id: "test".to_string(),
                spanner_instance: "test".to_string(),
                spanner_database: "test".to_string(),
                storage_bucket: "test".to_string(),
                storage_bucket_name: "test".to_string(),
                pubsub_topic: "test".to_string(),
                region: "us-central1".to_string(),
            },
            analysis: crate::config::AnalysisSettings {
                max_concurrent_analyses: 10,
                max_repository_size_gb: 10,
                analysis_timeout_seconds: 300,
                max_file_size_mb: 10,
                temp_dir: "/tmp".to_string(),
                supported_languages: vec!["rust".to_string()],
            },
            security: crate::config::SecuritySettings {
                enable_auth: false,
                api_key_header: "x-api-key".to_string(),
                jwt_secret: Some("test".to_string()),
                cors_origins: vec!["*".to_string()],
            },
            observability: crate::config::ObservabilitySettings {
                enable_tracing: false,
                enable_metrics: false,
                log_level: "info".to_string(),
                otel_endpoint: None,
            },
            circuit_breaker: crate::config::CircuitBreakerConfig {
                failure_threshold: 5,
                reset_timeout_secs: 60,
            },
            redis: crate::config::RedisSettings::default(),
            resource_limits: crate::config::ResourceLimitConfig::default(),
        }
    }

    #[test]
    fn test_memory_warnings() {
        let config = Arc::new(create_test_config());
        let perf_manager = PerformanceManager::new(config, None);
        let mut warnings = Vec::new();

        // Test low memory usage - no warning
        perf_manager.add_memory_warnings(&mut warnings, 1000);
        assert_eq!(warnings.len(), 0);

        // Test medium memory usage - medium warning
        perf_manager.add_memory_warnings(&mut warnings, 2500);
        assert_eq!(warnings.len(), 1);
        assert_eq!(warnings[0].warning_type, WarningType::MemoryLimit);
        assert_eq!(warnings[0].severity, WarningSeverity::Medium);

        // Test high memory usage - high warning
        warnings.clear();
        perf_manager.add_memory_warnings(&mut warnings, 3500);
        assert_eq!(warnings.len(), 1);
        assert_eq!(warnings[0].warning_type, WarningType::MemoryLimit);
        assert_eq!(warnings[0].severity, WarningSeverity::High);
    }

    #[tokio::test]
    async fn test_optimal_batch_size() {
        let config = Arc::new(create_test_config());
        let perf_manager = PerformanceManager::new(config, None);

        // Test batch size calculation
        let batch_size = perf_manager.get_optimal_batch_size(100, 10).await;
        assert!(batch_size > 0);
        assert!(batch_size <= 100);

        // Test with no items
        let batch_size = perf_manager.get_optimal_batch_size(0, 10).await;
        assert_eq!(batch_size, 0);

        // Test with high load
        let batch_size_high_load = perf_manager.get_optimal_batch_size(100, 100).await;
        assert!(batch_size_high_load > 0);
        assert!(batch_size_high_load <= 100);
    }

    #[test]
    fn test_resource_availability() {
        let config = Arc::new(create_test_config());
        let perf_manager = PerformanceManager::new(config, None);

        let (available, message) = perf_manager.check_resource_availability();
        // Should generally pass on most systems
        assert!(available || message.contains("Insufficient memory"));
    }

    #[test]
    fn test_get_available_memory() {
        let config = Arc::new(create_test_config());
        let perf_manager = PerformanceManager::new(config, None);

        let memory = perf_manager.get_available_memory();
        // Should return at least the default value
        assert!(memory >= 1024 * 1024); // At least 1MB
    }
}