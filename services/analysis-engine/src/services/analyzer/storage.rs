use crate::backpressure::BackpressureManager;
use crate::models::AnalysisResult;
use crate::storage::{connection_pool::SpannerPool, StorageOperations};
use anyhow::{anyhow, Result};
use std::sync::Arc;

pub struct StorageManager {
    spanner_pool: Option<Arc<SpannerPool>>,
    storage_client: Arc<StorageOperations>,
    backpressure_manager: Option<Arc<BackpressureManager>>,
}

impl StorageManager {
    pub fn new(
        spanner_pool: Option<Arc<SpannerPool>>,
        storage_client: Arc<StorageOperations>,
        backpressure_manager: Option<Arc<BackpressureManager>>,
    ) -> Self {
        Self {
            spanner_pool,
            storage_client,
            backpressure_manager,
        }
    }

    /// Store analysis result in both Spanner and cloud storage
    pub async fn store_analysis_result(&self, result: &AnalysisResult) -> Result<()> {
        if self.spanner_pool.is_some() {
            if let Err(e) = self.store_in_spanner(result).await {
                tracing::error!(
                    "Failed to store analysis result {} in Spanner: {}. Storing in GCS as fallback.",
                    result.id,
                    e
                );
            }
        } else {
            tracing::warn!(
                "Spanner not available - skipping database storage for analysis {}",
                result.id
            );
        }
        self.storage_client.store_analysis_results(result).await?;
        Ok(())
    }

    /// Store analysis result in Spanner with backpressure control
    async fn store_in_spanner(&self, result: &AnalysisResult) -> Result<()> {
        let pool = self
            .spanner_pool
            .as_ref()
            .ok_or_else(|| anyhow!("Spanner pool not configured"))?;
        let spanner = pool
            .get()
            .await
            .map_err(|e| anyhow!("Failed to get Spanner connection from pool: {:?}", e))?;

        let _db_permit = if let Some(bp_manager) = &self.backpressure_manager {
            Some(bp_manager.acquire_database_permit().await?)
        } else {
            None
        };

        let result_clone = result.clone();
        let transaction_result = spanner
            .read_write_transaction(|tx| {
                let result = result_clone.clone();
                Box::pin(async move {
                    use google_cloud_spanner::statement::Statement;

                    // Store main analysis record
                    let mut statement = Statement::new(
                        "INSERT OR UPDATE INTO analyses (analysis_id, repository_url, branch, commit_hash, status, user_id, started_at, completed_at)
                         VALUES (@analysis_id, @repository_url, @branch, @commit_hash, @status, @user_id, @started_at, @completed_at)"
                    );
                    statement.add_param("analysis_id", &result.id);
                    statement.add_param("repository_url", &result.repository_url);
                    statement.add_param("branch", &result.branch);
                    statement.add_param("commit_hash", &result.commit_hash);
                    statement.add_param("status", &result.status.to_string());
                    statement.add_param("user_id", &result.user_id);
                    statement.add_param("started_at", &result.started_at.to_rfc3339());
                    if let Some(completed_at) = &result.completed_at {
                        statement.add_param("completed_at", &completed_at.to_rfc3339());
                    } else {
                        statement.add_param("completed_at", &Option::<String>::None);
                    }

                    tx.update(statement).await?;

                    // Store file analyses if present
                    if let Some(file_analyses) = &result.successful_analyses {
                        for file in file_analyses {
                            let metrics_json = serde_json::to_string(&file.metrics)
                                .map_err(|e| google_cloud_spanner::client::Error::GRPC(tonic::Status::internal(e.to_string())))?;

                            let mut file_statement = Statement::new(
                                "INSERT OR UPDATE INTO file_analyses (analysis_id, file_path, language, metrics_json)
                                 VALUES (@analysis_id, @file_path, @language, @metrics_json)"
                            );
                            file_statement.add_param("analysis_id", &result.id);
                            file_statement.add_param("file_path", &file.path);
                            file_statement.add_param("language", &file.language);
                            file_statement.add_param("metrics_json", &metrics_json);

                            tx.update(file_statement).await?;
                        }
                    }

                    // Store patterns if present
                    if !result.patterns.is_empty() {
                        for pattern in &result.patterns {
                            let severity = "Medium";
                            let mut pattern_statement = Statement::new(
                                "INSERT OR UPDATE INTO detected_patterns (analysis_id, file_path, line_start, line_end, pattern_type, severity)
                                 VALUES (@analysis_id, @file_path, @line_start, @line_end, @pattern_type, @severity)"
                            );
                            pattern_statement.add_param("analysis_id", &result.id);
                            pattern_statement.add_param("file_path", &pattern.location.file_path);
                            pattern_statement.add_param("line_start", &(pattern.location.range.start.line as i64));
                            pattern_statement.add_param("line_end", &(pattern.location.range.end.line as i64));
                            pattern_statement.add_param("pattern_type", &format!("{:?}", pattern.pattern_type));
                            pattern_statement.add_param("severity", &severity);

                            tx.update(pattern_statement).await?;
                        }
                    }

                    Ok::<(), google_cloud_spanner::client::Error>(())
                })
            })
            .await;

        match transaction_result {
            Ok(_) => {
                if let Some(bp_manager) = &self.backpressure_manager {
                    bp_manager.record_success("database").await;
                }
                tracing::info!("Stored analysis {} in Spanner", result.id);
                Ok(())
            }
            Err(e) => {
                if let Some(bp_manager) = &self.backpressure_manager {
                    bp_manager.record_failure("database").await;
                }
                Err(anyhow!("Failed to store analysis: {}", e))
            }
        }
    }

    /// Delete analysis result from storage
    pub async fn delete_analysis_result(&self, analysis_id: &str) -> Result<()> {
        let mut errors = Vec::new();

        if let Some(pool) = &self.spanner_pool {
            let spanner_delete = || async {

                let spanner = pool
                    .get()
                    .await
                    .map_err(|e| anyhow!("Failed to get Spanner connection from pool: {:?}", e))?;

                let analysis_id_clone = analysis_id.to_string();
                let (_, _) = spanner
                    .read_write_transaction(|tx| {
                        let analysis_id = analysis_id_clone.clone();
                        Box::pin(async move {
                            use google_cloud_spanner::statement::Statement;

                            let mut statement = Statement::new("DELETE FROM analyses WHERE analysis_id = @analysis_id");
                            statement.add_param("analysis_id", &analysis_id);

                            tx.update(statement).await?;
                            Ok::<(), google_cloud_spanner::client::Error>(())
                        })
                    })
                    .await
                    .map_err(|e| anyhow!("Spanner deletion failed: {}", e))?;
                Ok::<(), anyhow::Error>(())
            };
            if let Err(e) = spanner_delete().await {
                errors.push(format!("Spanner deletion failed: {}", e));
            }
        }

        if let Err(e) = self
            .storage_client
            .delete_analysis_results(analysis_id)
            .await
        {
            errors.push(format!("Cloud storage deletion failed: {}", e));
        }

        if !errors.is_empty() {
            return Err(anyhow!(
                "Failed to delete analysis {}: {}",
                analysis_id,
                errors.join(", ")
            ));
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::AnalysisStatus;
    use chrono::Utc;

    fn create_test_analysis_result() -> AnalysisResult {
        AnalysisResult {
            id: "test-analysis-id".to_string(),
            repository_url: "https://github.com/test/repo".to_string(),
            branch: "main".to_string(),
            commit_hash: Some("abc123".to_string()),
            repository_size_bytes: Some(1024 * 1024),
            clone_time_ms: Some(1000),
            status: AnalysisStatus::Completed,
            started_at: Utc::now(),
            completed_at: Some(Utc::now()),
            duration_seconds: Some(60),
            file_count: 10,
            success_rate: 90.0,
            progress: Some(100.0),
            current_stage: Some("Completed".to_string()),
            estimated_completion: None,
            patterns: vec![],
            languages: std::collections::HashMap::new(),
            embeddings: None,
            successful_analyses: Some(vec![]),
            user_id: "test-user".to_string(),
            webhook_url: None,
            failed_files: vec![],
            metrics: None,
            performance_metrics: None,
            error_message: None,
            warnings: vec![],
        }
    }

    #[tokio::test]
    #[ignore = "Requires real GCS credentials"]
    async fn test_store_analysis_result_without_spanner() {
        // This test requires real GCS credentials to run
        // In production tests, this would use mocked storage backends
    }

    #[tokio::test]
    #[ignore = "Requires real GCS credentials"]
    async fn test_storage_manager_creation() {
        // This test requires real GCS credentials to run
        // In production tests, this would use mocked storage backends
    }
}
