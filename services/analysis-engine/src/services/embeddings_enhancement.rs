use anyhow::{Context, Result};
use crate::models::{FileAnalysis, CodeEmbedding};
use serde::{Deserialize, Serialize};
use reqwest::Client;
use std::env;
use std::time::Duration;
use tokio::time::sleep;
use std::sync::Arc;
use tokio::sync::Mutex;
use chrono::{DateTime, Utc};

const MAX_RETRIES: u32 = 3;
const INITIAL_RETRY_DELAY: Duration = Duration::from_millis(1000);
const MAX_RETRY_DELAY: Duration = Duration::from_secs(60);
const VERTEX_AI_TIMEOUT: Duration = Duration::from_secs(30);
const EMBEDDING_DIMENSION: usize = 768; // text-embedding-005 supports 768 dimensions

#[derive(Debug, Serialize)]
struct EmbeddingRequest {
    instances: Vec<EmbeddingInstance>,
    parameters: Option<EmbeddingParameters>,
}

#[derive(Debug, Serialize)]
struct EmbeddingParameters {
    task_type: String,
    output_dimensionality: i32,
    truncate: Option<bool>,
}

#[derive(Debug, Serialize, Clone)]
struct EmbeddingInstance {
    content: String,
    task_type: Option<String>,
    title: Option<String>,
}

#[derive(Debug, Deserialize)]
struct EmbeddingResponse {
    predictions: Vec<Prediction>,
}

#[derive(Debug, Deserialize)]
struct Prediction {
    embeddings: EmbeddingValues,
    statistics: Option<EmbeddingStats>,
}

#[derive(Debug, Deserialize)]
struct EmbeddingValues {
    values: Vec<f32>,
}

#[derive(Debug, Deserialize)]
struct EmbeddingStats {
    token_count: Option<u32>,
    #[allow(dead_code)]
    truncated: Option<bool>,
}

// Circuit breaker states
#[derive(Debug, Clone)]
enum CircuitState {
    Closed,
    Open(DateTime<Utc>),
    HalfOpen,
}

// Feature toggles for AI functionality
#[derive(Debug, Clone)]
pub struct FeatureToggles {
    pub enable_embeddings: bool,
    pub enable_semantic_search: bool,
    pub enable_code_quality_assessment: bool,
    pub enable_ai_pattern_detection: bool,
    pub enable_repository_insights: bool,
    pub enable_intelligent_documentation: bool,
    pub use_fallback_embeddings: bool,
}

impl Default for FeatureToggles {
    fn default() -> Self {
        Self {
            enable_embeddings: true,
            enable_semantic_search: true,
            enable_code_quality_assessment: true,
            enable_ai_pattern_detection: true,
            enable_repository_insights: true,
            enable_intelligent_documentation: true,
            use_fallback_embeddings: true,
        }
    }
}

pub struct EnhancedEmbeddingsService {
    client: Client,
    project_id: String,
    location: String,
    circuit_state: Arc<Mutex<CircuitState>>,
    failure_count: Arc<Mutex<u32>>,
    failure_threshold: u32,
    reset_timeout: Duration,
    feature_toggles: Arc<FeatureToggles>,
    metrics: Arc<Mutex<EmbeddingMetrics>>,
}

#[derive(Debug, Default)]
pub struct EmbeddingMetrics {
    total_requests: u64,
    successful_requests: u64,
    failed_requests: u64,
    total_tokens_processed: u64,
    average_response_time_ms: f64,
    circuit_breaker_opens: u64,
    fallback_uses: u64,
}

impl EnhancedEmbeddingsService {
    pub async fn new() -> Result<Self> {
        let project_id = env::var("GCP_PROJECT_ID")
            .unwrap_or_else(|_| "vibe-match-463114".to_string());
        let location = env::var("GCP_REGION").unwrap_or_else(|_| "us-central1".to_string());

        // Create HTTP client with timeout
        let client = Client::builder()
            .timeout(VERTEX_AI_TIMEOUT)
            .build()
            .context("Failed to create HTTP client")?;

        // Load feature toggles from environment
        let feature_toggles = Arc::new(FeatureToggles {
            enable_embeddings: env::var("ENABLE_EMBEDDINGS").unwrap_or_else(|_| "true".to_string()).parse().unwrap_or(true),
            enable_semantic_search: env::var("ENABLE_SEMANTIC_SEARCH").unwrap_or_else(|_| "true".to_string()).parse().unwrap_or(true),
            enable_code_quality_assessment: env::var("ENABLE_CODE_QUALITY_ASSESSMENT").unwrap_or_else(|_| "true".to_string()).parse().unwrap_or(true),
            enable_ai_pattern_detection: env::var("ENABLE_AI_PATTERN_DETECTION").unwrap_or_else(|_| "true".to_string()).parse().unwrap_or(true),
            enable_repository_insights: env::var("ENABLE_REPOSITORY_INSIGHTS").unwrap_or_else(|_| "true".to_string()).parse().unwrap_or(true),
            enable_intelligent_documentation: env::var("ENABLE_INTELLIGENT_DOCUMENTATION").unwrap_or_else(|_| "true".to_string()).parse().unwrap_or(true),
            use_fallback_embeddings: env::var("USE_FALLBACK_EMBEDDINGS").unwrap_or_else(|_| "true".to_string()).parse().unwrap_or(true),
        });

        Ok(Self {
            client,
            project_id,
            location,
            circuit_state: Arc::new(Mutex::new(CircuitState::Closed)),
            failure_count: Arc::new(Mutex::new(0)),
            failure_threshold: 5,
            reset_timeout: Duration::from_secs(60),
            feature_toggles,
            metrics: Arc::new(Mutex::new(EmbeddingMetrics::default())),
        })
    }

    pub async fn generate_enhanced_embeddings(&self, analyses: &[FileAnalysis]) -> Result<Vec<CodeEmbedding>> {
        if !self.feature_toggles.enable_embeddings {
            tracing::info!("Embeddings disabled by feature toggle");
            return Ok(vec![]);
        }

        let mut embeddings = Vec::new();
        
        // Process in batches to avoid exceeding API limits
        const BATCH_SIZE: usize = 5;
        
        for (batch_idx, chunk) in analyses.chunks(BATCH_SIZE).enumerate() {
            // Check circuit breaker
            if !self.check_circuit_breaker().await? {
                tracing::warn!(
                    "Circuit breaker is open, skipping embedding generation for batch {}",
                    batch_idx
                );

                // Use fallback embeddings if enabled
                if self.feature_toggles.use_fallback_embeddings {
                    let fallback_embeddings = self.generate_fallback_embeddings(chunk).await?;
                    embeddings.extend(fallback_embeddings);
                    self.record_fallback_use().await;
                }
                continue;
            }

            let instances: Vec<EmbeddingInstance> = chunk
                .iter()
                .map(|analysis| {
                    let content = self.extract_enhanced_code_content(analysis);
                    EmbeddingInstance {
                        content,
                        task_type: Some("CODE_RETRIEVAL_QUERY".to_string()),
                        title: Some(analysis.path.clone()),
                    }
                })
                .collect();

            if instances.is_empty() {
                continue;
            }

            // Generate embeddings with retry logic
            let start_time = std::time::Instant::now();
            match self.call_vertex_ai_enhanced(&instances).await {
                Ok(batch_embeddings) => {
                    embeddings.extend(batch_embeddings);
                    self.record_success().await;
                    self.record_response_time(start_time.elapsed().as_millis() as f64).await;
                }
                Err(e) => {
                    tracing::error!(
                        "Failed to generate embeddings for batch {}: {}",
                        batch_idx, e
                    );
                    self.record_failure().await;

                    // Try fallback embeddings if enabled
                    if self.feature_toggles.use_fallback_embeddings && self.is_retryable_error(&e) {
                        tracing::info!("Attempting fallback embeddings for batch {}", batch_idx);
                        if let Ok(fallback_embeddings) = self.generate_fallback_embeddings(chunk).await {
                            embeddings.extend(fallback_embeddings);
                            self.record_fallback_use().await;
                        }
                    }
                }
            }
            
            // Add delay between batches to respect rate limits
            if batch_idx < analyses.chunks(BATCH_SIZE).len() - 1 {
                sleep(Duration::from_millis(500)).await;
            }
        }

        Ok(embeddings)
    }

    async fn call_vertex_ai_enhanced(&self, instances: &[EmbeddingInstance]) -> Result<Vec<CodeEmbedding>> {
        let mut retry_delay = INITIAL_RETRY_DELAY;
        
        for attempt in 0..MAX_RETRIES {
            match self.call_vertex_ai_v2(instances).await {
                Ok(embeddings) => return Ok(embeddings),
                Err(e) => {
                    // Check if error is retryable
                    if !self.is_retryable_error(&e) || attempt == MAX_RETRIES - 1 {
                        return Err(e);
                    }
                    
                    tracing::warn!(
                        "Attempt {} failed, retrying in {:?}: {}",
                        attempt + 1,
                        retry_delay,
                        e
                    );
                    
                    sleep(retry_delay).await;
                    
                    // Exponential backoff with simple jitter
                    let jitter = Duration::from_millis((attempt as u64 * 100) % 1000);
                    retry_delay = std::cmp::min(
                        retry_delay * 2 + jitter,
                        MAX_RETRY_DELAY,
                    );
                }
            }
        }
        
        Err(anyhow::anyhow!("All retry attempts exhausted"))
    }

    async fn call_vertex_ai_v2(&self, instances: &[EmbeddingInstance]) -> Result<Vec<CodeEmbedding>> {
        // Get authentication token
        let auth_token = self.get_auth_token().await?;
        
        // Use text-embedding-005 model
        let endpoint = format!(
            "https://{}-aiplatform.googleapis.com/v1/projects/{}/locations/{}/publishers/google/models/text-embedding-005:predict",
            self.location, self.project_id, self.location
        );

        let response = self.client
            .post(&endpoint)
            .bearer_auth(&auth_token)
            .json(&EmbeddingRequest {
                instances: instances.to_vec(),
                parameters: Some(EmbeddingParameters {
                    task_type: "CODE_RETRIEVAL_QUERY".to_string(),
                    output_dimensionality: 768,
                    truncate: Some(true), // Allow truncation for long inputs
                }),
            })
            .send()
            .await
            .context("Failed to send request to Vertex AI")?;

        let status = response.status();
        
        if !status.is_success() {
            let error_body = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            return Err(anyhow::anyhow!(
                "Vertex AI request failed with status {}: {}",
                status,
                error_body
            ));
        }

        let embedding_response: EmbeddingResponse = response
            .json()
            .await
            .context("Failed to parse Vertex AI response")?;

        // Convert predictions to CodeEmbedding
        let embeddings = embedding_response.predictions
            .into_iter()
            .enumerate()
            .map(|(i, prediction)| {
                // Validate embedding dimension
                if prediction.embeddings.values.len() != EMBEDDING_DIMENSION {
                    tracing::warn!(
                        "Unexpected embedding dimension: {} (expected {})",
                        prediction.embeddings.values.len(),
                        EMBEDDING_DIMENSION
                    );
                }
                
                CodeEmbedding {
                    chunk_id: format!("chunk_{:016x}", i),
                    vector: prediction.embeddings.values,
                    model: "text-embedding-005".to_string(),
                    metadata: Some(crate::models::EmbeddingMetadata {
                        tokens_used: prediction.statistics.as_ref().and_then(|s| s.token_count),
                        created_at: Some(Utc::now()),
                    }),
                }
            })
            .collect();

        Ok(embeddings)
    }

    async fn generate_fallback_embeddings(&self, analyses: &[FileAnalysis]) -> Result<Vec<CodeEmbedding>> {
        // Generate simple hash-based embeddings as fallback
        let mut embeddings = Vec::new();
        
        for (idx, analysis) in analyses.iter().enumerate() {
            let content = self.extract_enhanced_code_content(analysis);
            let embedding = self.generate_hash_embedding(&content, idx);
            embeddings.push(embedding);
        }
        
        Ok(embeddings)
    }

    fn generate_hash_embedding(&self, content: &str, idx: usize) -> CodeEmbedding {
        // Simple hash-based embedding for fallback
        let mut vector = vec![0.0; EMBEDDING_DIMENSION];
        let hash = std::collections::hash_map::DefaultHasher::new();
        use std::hash::Hash;
        use std::hash::Hasher;
        
        let mut hasher = hash;
        content.hash(&mut hasher);
        let hash_value = hasher.finish();
        
        // Distribute hash across vector dimensions
        for i in 0..EMBEDDING_DIMENSION {
            vector[i] = (((hash_value >> (i % 64)) & 1) as f32) * 2.0 - 1.0;
        }
        
        CodeEmbedding {
            chunk_id: format!("fallback_chunk_{:016x}", idx),
            vector,
            model: "fallback-hash".to_string(),
            metadata: Some(crate::models::EmbeddingMetadata {
                tokens_used: Some(content.len() as u32 / 4), // Rough estimate
                created_at: Some(Utc::now()),
            }),
        }
    }

    fn extract_enhanced_code_content(&self, analysis: &FileAnalysis) -> String {
        let mut content = String::new();
        
        // Add file path as context
        content.push_str(&format!("File: {}\n", analysis.path));
        
        // Add language
        content.push_str(&format!("Language: {}\n", analysis.language));
        
        // Add enhanced metrics
        content.push_str(&format!(
            "Metrics: {} LOC, complexity {}, maintainability {:.2}\n",
            analysis.metrics.lines_of_code,
            analysis.metrics.complexity,
            analysis.metrics.maintainability_index
        ));
        
        // Add symbols with better organization
        if let Some(symbols) = &analysis.symbols {
            content.push_str("Symbols:\n");
            let mut functions = Vec::new();
            let mut classes = Vec::new();
            let mut other = Vec::new();
            
            for symbol in symbols.iter().take(15) {
                match symbol.symbol_type {
                    crate::models::SymbolType::Function | crate::models::SymbolType::Method => {
                        functions.push(symbol);
                    }
                    crate::models::SymbolType::Class | crate::models::SymbolType::Interface => {
                        classes.push(symbol);
                    }
                    _ => other.push(symbol),
                }
            }
            
            if !classes.is_empty() {
                content.push_str("Classes: ");
                for class in classes {
                    content.push_str(&format!("{} ", class.name));
                }
                content.push('\n');
            }
            
            if !functions.is_empty() {
                content.push_str("Functions: ");
                for func in functions {
                    content.push_str(&format!("{} ", func.name));
                }
                content.push('\n');
            }
            
            if !other.is_empty() {
                content.push_str("Other: ");
                for item in other {
                    content.push_str(&format!("{} ", item.name));
                }
                content.push('\n');
            }
        }
        
        // Add code preview with better truncation
        if let Some(text) = &analysis.ast.text {
            // Take first 3000 characters for embedding (better for text-embedding-005)
            let preview = text.chars().take(3000).collect::<String>();
            content.push_str(&format!("\nCode:\n{}", preview));
        }
        
        content
    }

    async fn get_auth_token(&self) -> Result<String> {
        // Check if we're running on Cloud Run or GCE (has metadata server)
        if env::var("K_SERVICE").is_ok() || env::var("GAE_ENV").is_ok() {
            // Running on Cloud Run or App Engine - use metadata server
            let metadata_url = format!(
                "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/token?scopes={}",
                "https://www.googleapis.com/auth/cloud-platform"
            );
            
            let response = self.client
                .get(&metadata_url)
                .header("Metadata-Flavor", "Google")
                .send()
                .await
                .context("Failed to fetch token from metadata server")?;
                
            if !response.status().is_success() {
                return Err(anyhow::anyhow!(
                    "Metadata server returned error: {}",
                    response.status()
                ));
            }
            
            #[derive(Deserialize)]
            struct TokenResponse {
                access_token: String,
            }
            
            let token_response: TokenResponse = response
                .json()
                .await
                .context("Failed to parse token response")?;
                
            Ok(token_response.access_token)
        } else if let Ok(_creds_path) = env::var("GOOGLE_APPLICATION_CREDENTIALS") {
            // Local development with service account key file
            use std::process::Command;
            
            let output = Command::new("gcloud")
                .args(&["auth", "application-default", "print-access-token"])
                .output()
                .context("Failed to run gcloud command")?;
                
            if !output.status.success() {
                return Err(anyhow::anyhow!(
                    "gcloud command failed: {}",
                    String::from_utf8_lossy(&output.stderr)
                ));
            }
            
            let token = String::from_utf8(output.stdout)
                .context("Invalid UTF-8 in token")?
                .trim()
                .to_string();
                
            Ok(token)
        } else {
            // No authentication available
            Err(anyhow::anyhow!(
                "No authentication method available. Set GOOGLE_APPLICATION_CREDENTIALS or run on Cloud Run"
            ))
        }
    }

    // Circuit breaker implementation
    async fn check_circuit_breaker(&self) -> Result<bool> {
        let mut state = self.circuit_state.lock().await;
        
        match *state {
            CircuitState::Closed => Ok(true),
            CircuitState::Open(reset_time) => {
                if Utc::now() > reset_time {
                    *state = CircuitState::HalfOpen;
                    tracing::info!("Circuit breaker transitioning to half-open");
                    Ok(true)
                } else {
                    Ok(false)
                }
            }
            CircuitState::HalfOpen => Ok(true),
        }
    }

    async fn record_success(&self) {
        let mut state = self.circuit_state.lock().await;
        let mut failures = self.failure_count.lock().await;
        let mut metrics = self.metrics.lock().await;
        
        *failures = 0;
        metrics.successful_requests += 1;
        
        if matches!(*state, CircuitState::HalfOpen) {
            *state = CircuitState::Closed;
            tracing::info!("Circuit breaker closed after successful request");
        }
    }

    async fn record_failure(&self) {
        let mut state = self.circuit_state.lock().await;
        let mut failures = self.failure_count.lock().await;
        let mut metrics = self.metrics.lock().await;
        
        *failures += 1;
        metrics.failed_requests += 1;
        
        if *failures >= self.failure_threshold {
            let reset_time = Utc::now() + chrono::Duration::from_std(self.reset_timeout)
                .unwrap_or_else(|_| chrono::Duration::seconds(300));
            *state = CircuitState::Open(reset_time);
            *failures = 0;
            metrics.circuit_breaker_opens += 1;
            
            tracing::error!(
                "Circuit breaker opened after {} failures, will reset at {}",
                self.failure_threshold,
                reset_time
            );
        }
    }

    async fn record_fallback_use(&self) {
        let mut metrics = self.metrics.lock().await;
        metrics.fallback_uses += 1;
    }

    async fn record_response_time(&self, response_time_ms: f64) {
        let mut metrics = self.metrics.lock().await;
        metrics.total_requests += 1;
        
        // Calculate running average
        let total_requests = metrics.total_requests as f64;
        metrics.average_response_time_ms = 
            ((metrics.average_response_time_ms * (total_requests - 1.0)) + response_time_ms) / total_requests;
    }

    fn is_retryable_error(&self, error: &anyhow::Error) -> bool {
        // Check if error message indicates a retryable condition
        let error_str = error.to_string().to_lowercase();
        
        error_str.contains("timeout") ||
        error_str.contains("temporarily unavailable") ||
        error_str.contains("429") || // Rate limit
        error_str.contains("500") || // Internal server error
        error_str.contains("502") || // Bad gateway
        error_str.contains("503") || // Service unavailable
        error_str.contains("504")    // Gateway timeout
    }

    pub async fn get_metrics(&self) -> EmbeddingMetrics {
        let metrics = self.metrics.lock().await;
        EmbeddingMetrics {
            total_requests: metrics.total_requests,
            successful_requests: metrics.successful_requests,
            failed_requests: metrics.failed_requests,
            total_tokens_processed: metrics.total_tokens_processed,
            average_response_time_ms: metrics.average_response_time_ms,
            circuit_breaker_opens: metrics.circuit_breaker_opens,
            fallback_uses: metrics.fallback_uses,
        }
    }

    pub fn get_feature_toggles(&self) -> Arc<FeatureToggles> {
        self.feature_toggles.clone()
    }
}

// Semantic search functionality
impl EnhancedEmbeddingsService {
    pub async fn semantic_search(&self, query: &str, embeddings: &[CodeEmbedding], top_k: usize) -> Result<Vec<(usize, f32)>> {
        if !self.feature_toggles.enable_semantic_search {
            return Ok(vec![]);
        }

        // Generate query embedding
        let query_instance = EmbeddingInstance {
            content: query.to_string(),
            task_type: Some("SEMANTIC_SIMILARITY".to_string()),
            title: Some("Search Query".to_string()),
        };

        let query_embedding = self.call_vertex_ai_v2(&[query_instance]).await?;
        if query_embedding.is_empty() {
            return Ok(vec![]);
        }

        let query_vector = &query_embedding[0].vector;
        
        // Calculate cosine similarity with all embeddings
        let mut similarities = Vec::new();
        for (idx, embedding) in embeddings.iter().enumerate() {
            let similarity = cosine_similarity(query_vector, &embedding.vector);
            similarities.push((idx, similarity));
        }

        // Sort by similarity and return top_k
        similarities.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));
        similarities.truncate(top_k);

        Ok(similarities)
    }
}

fn cosine_similarity(a: &[f32], b: &[f32]) -> f32 {
    let dot_product: f32 = a.iter().zip(b.iter()).map(|(x, y)| x * y).sum();
    let norm_a: f32 = a.iter().map(|x| x * x).sum::<f32>().sqrt();
    let norm_b: f32 = b.iter().map(|x| x * x).sum::<f32>().sqrt();
    
    if norm_a == 0.0 || norm_b == 0.0 {
        0.0
    } else {
        dot_product / (norm_a * norm_b)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cosine_similarity() {
        let a = vec![1.0, 0.0, 0.0];
        let b = vec![1.0, 0.0, 0.0];
        assert_eq!(cosine_similarity(&a, &b), 1.0);

        let a = vec![1.0, 0.0, 0.0];
        let b = vec![0.0, 1.0, 0.0];
        assert_eq!(cosine_similarity(&a, &b), 0.0);
    }

    #[test]
    fn test_feature_toggles_default() {
        let toggles = FeatureToggles::default();
        assert!(toggles.enable_embeddings);
        assert!(toggles.enable_semantic_search);
        assert!(toggles.enable_code_quality_assessment);
    }
}