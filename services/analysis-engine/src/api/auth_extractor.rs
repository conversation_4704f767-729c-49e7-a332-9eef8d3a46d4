//! Production-ready authentication using Tower middleware pattern
//! This avoids the complex FromRequestParts lifetime issues

use crate::api::{
    errors::{ErrorResponse, ErrorType},
    AppState,
};
use crate::audit::{AuditAction, AuditEventBuilder, AuditLogger, AuditOutcome, AuditSeverity};
use crate::errors::AnalysisError;

use axum::{
    extract::Request,
    http::{header, StatusCode},
    middleware::Next,
    response::{IntoResponse, Response},
    Json,
};

use jsonwebtoken::{decode, Algorithm, DecodingKey, Validation};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::task::{Context, Poll};
use uuid::Uuid;

use futures::future::BoxFuture;
use tower::{Layer, Service};

/// Authenticated user information extracted from the request
#[derive(Debug, Clone)]
pub struct AuthUser {
    pub user_id: String,
    pub rate_limit: i64,
    pub auth_method: AuthMethod,
}

#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub enum AuthMethod {
    Api<PERSON><PERSON>,
    JwtToken,
    Unknown,
}

/// JWT Claims structure
#[derive(Debug, Serialize, Deserialize)]
struct Claims {
    sub: String,                // Subject (user ID)
    exp: u64,                   // Expiration time
    iat: u64,                   // Issued at
    aud: String,                // Audience
    iss: String,                // Issuer
    nbf: Option<u64>,           // Not before
    jti: Option<String>,        // JWT ID for revocation tracking
    scope: Option<String>,      // Token scope/permissions
    session_id: Option<String>, // Session identifier for revocation
    device_id: Option<String>,  // Device fingerprint for binding
}

/// Authentication error that can be converted to a response
#[derive(Debug)]
pub struct AuthError {
    pub error_type: ErrorType,
    pub message: String,
    pub status_code: StatusCode,
    pub correlation_id: String,
}

impl IntoResponse for AuthError {
    fn into_response(self) -> Response {
        let error_code = match &self.error_type {
            ErrorType::Authentication => "AUTH_FAILED",
            ErrorType::RateLimit => "RATE_LIMIT_EXCEEDED",
            _ => "AUTH_ERROR",
        }
        .to_string();

        let mut error_response = ErrorResponse::new(self.error_type, self.message);
        error_response.error_code = Some(error_code);
        error_response.user_message =
            Some("Authentication failed. Please check your credentials.".to_string());
        error_response.correlation_id = Some(self.correlation_id);

        (self.status_code, error_response).into_response()
    }
}

/// Production authentication middleware using Tower pattern
pub async fn auth_middleware(
    mut req: Request,
    next: Next,
) -> Response {
    let correlation_id = Uuid::new_v4().to_string();

    // Extract state from request extensions
    let state = match req.extensions().get::<Arc<AppState>>() {
        Some(s) => s.clone(),
        None => {
            let error = ErrorResponse::new(
                ErrorType::Internal,
                "Application state not found".to_string(),
            );
            return (StatusCode::INTERNAL_SERVER_ERROR, Json(error)).into_response();
        }
    };

    // Extract authentication from headers
    let auth_result = authenticate_request(&req, &state, &correlation_id).await;

    match auth_result {
        Ok(user) => {
            // Insert authenticated user into request extensions
            req.extensions_mut().insert(user);
            next.run(req).await
        }
        Err(auth_error) => {
            // Log authentication failure
            audit_auth_failure(&state, None, AuthMethod::Unknown, &auth_error.message).await;
            auth_error.into_response()
        }
    }
}

/// Optional authentication middleware - allows unauthenticated requests
pub async fn optional_auth_middleware(
    mut req: Request,
    next: Next,
) -> Response {
    let correlation_id = Uuid::new_v4().to_string();

    // Extract state from request extensions
    if let Some(state) = req.extensions().get::<Arc<AppState>>() {
        let state = state.clone();
        
        // Try to authenticate but don't fail if not present
        if let Ok(user) = authenticate_request(&req, &state, &correlation_id).await {
            req.extensions_mut().insert(user);
        }
    }
    // If no state found, just continue without authentication

    next.run(req).await
}

/// Extract and validate authentication from request (owned version for Tower Service)
#[allow(dead_code)]
async fn authenticate_request_owned(
    req: Request,
    state: Arc<AppState>,
    correlation_id: String,
) -> Result<AuthUser, AuthError> {
    // Check for API key first
    if let Some(api_key) = req.headers().get("x-api-key").and_then(|v| v.to_str().ok()) {
        return validate_api_key(api_key, &state, &correlation_id).await;
    }

    // Check for Bearer token
    if let Some(auth_header) = req
        .headers()
        .get(header::AUTHORIZATION)
        .and_then(|v| v.to_str().ok())
    {
        if let Some(token) = auth_header.strip_prefix("Bearer ") {
            return validate_jwt_token(token, &state, &correlation_id, &req).await;
        }
    }

    // No authentication provided
    Err(AuthError {
        error_type: ErrorType::Authentication,
        message: "No authentication provided".to_string(),
        status_code: StatusCode::UNAUTHORIZED,
        correlation_id,
    })
}

/// Extract and validate authentication from request
async fn authenticate_request(
    req: &Request,
    state: &Arc<AppState>,
    correlation_id: &str,
) -> Result<AuthUser, AuthError> {
    // Check for API key first
    if let Some(api_key) = req.headers().get("x-api-key").and_then(|v| v.to_str().ok()) {
        return validate_api_key(api_key, state, correlation_id).await;
    }

    // Check for Bearer token
    if let Some(auth_header) = req
        .headers()
        .get(header::AUTHORIZATION)
        .and_then(|v| v.to_str().ok())
    {
        if let Some(token) = auth_header.strip_prefix("Bearer ") {
            return validate_jwt_token(token, state, correlation_id, req).await;
        }
    }

    // No authentication provided
    Err(AuthError {
        error_type: ErrorType::Authentication,
        message: "No authentication provided".to_string(),
        status_code: StatusCode::UNAUTHORIZED,
        correlation_id: correlation_id.to_string(),
    })
}

/// Extension trait to extract authenticated user from request
pub trait AuthRequestExt {
    fn auth_user(&self) -> Result<&AuthUser, AnalysisError>;
    fn optional_auth_user(&self) -> Option<&AuthUser>;
}

impl AuthRequestExt for Request {
    fn auth_user(&self) -> Result<&AuthUser, AnalysisError> {
        self.extensions()
            .get::<AuthUser>()
            .ok_or_else(|| AnalysisError::auth("Authentication required"))
    }

    fn optional_auth_user(&self) -> Option<&AuthUser> {
        self.extensions().get::<AuthUser>()
    }
}

async fn validate_api_key(
    api_key: &str,
    state: &Arc<AppState>,
    correlation_id: &str,
) -> Result<AuthUser, AuthError> {
    // Validate API key format
    if !api_key.starts_with("ak_") || api_key.len() < 12 {
        return Err(AuthError {
            error_type: ErrorType::Authentication,
            message: "Invalid API key format".to_string(),
            status_code: StatusCode::UNAUTHORIZED,
            correlation_id: correlation_id.to_string(),
        });
    }

    // Extract prefix for efficient lookup
    let key_prefix = &api_key[3..11];

    // Validate against database
    if let Some(pool) = &state.spanner_pool {
        let spanner_conn = pool.get().await.map_err(|e| AuthError {
            error_type: ErrorType::Internal,
            message: format!("Failed to get database connection: {:?}", e),
            status_code: StatusCode::INTERNAL_SERVER_ERROR,
            correlation_id: correlation_id.to_string(),
        })?;
        match validate_api_key_with_db(api_key, key_prefix, &spanner_conn).await {
            Ok((user_id, rate_limit)) => {
                // Log successful authentication
                audit_auth_success(state, &user_id, AuthMethod::ApiKey).await;

                Ok(AuthUser {
                    user_id,
                    rate_limit,
                    auth_method: AuthMethod::ApiKey,
                })
            }
            Err(e) => {
                // Log failed authentication
                audit_auth_failure(state, None, AuthMethod::ApiKey, &e).await;

                Err(AuthError {
                    error_type: ErrorType::Authentication,
                    message: e,
                    status_code: StatusCode::UNAUTHORIZED,
                    correlation_id: correlation_id.to_string(),
                })
            }
        }
    } else {
        // Fallback for testing without database
        if api_key == "ak_test_key_12345678" {
            Ok(AuthUser {
                user_id: "test-user".to_string(),
                rate_limit: 100,
                auth_method: AuthMethod::ApiKey,
            })
        } else {
            Err(AuthError {
                error_type: ErrorType::Authentication,
                message: "Invalid API key".to_string(),
                status_code: StatusCode::UNAUTHORIZED,
                correlation_id: correlation_id.to_string(),
            })
        }
    }
}

async fn validate_jwt_token(
    token: &str,
    state: &Arc<AppState>,
    correlation_id: &str,
    req: &Request,
) -> Result<AuthUser, AuthError> {
    // Get JWT secret from environment
    let jwt_secret = std::env::var("JWT_SECRET").map_err(|_| AuthError {
        error_type: ErrorType::Internal,
        message: "JWT_SECRET not configured".to_string(),
        status_code: StatusCode::INTERNAL_SERVER_ERROR,
        correlation_id: correlation_id.to_string(),
    })?;

    // Set up validation
    let mut validation = Validation::new(Algorithm::HS256);
    validation.set_audience(&["ccl-analysis-engine"]);
    validation.validate_exp = true;
    validation.validate_nbf = true;

    // Decode and validate token
    let token_data = decode::<Claims>(
        token,
        &DecodingKey::from_secret(jwt_secret.as_bytes()),
        &validation,
    )
    .map_err(|e| {
        let message = match e.kind() {
            jsonwebtoken::errors::ErrorKind::ExpiredSignature => "Token has expired",
            jsonwebtoken::errors::ErrorKind::InvalidToken => "Invalid token format",
            jsonwebtoken::errors::ErrorKind::InvalidAudience => "Invalid token audience",
            jsonwebtoken::errors::ErrorKind::InvalidSignature => "Invalid token signature",
            _ => "Token validation failed",
        };

        AuthError {
            error_type: ErrorType::Authentication,
            message: message.to_string(),
            status_code: StatusCode::UNAUTHORIZED,
            correlation_id: correlation_id.to_string(),
        }
    })?;

    let claims = &token_data.claims;

    // Check token revocation
    if let Some(jti) = &claims.jti {
        if is_token_revoked(jti).await {
            return Err(AuthError {
                error_type: ErrorType::Authentication,
                message: "Token has been revoked".to_string(),
                status_code: StatusCode::UNAUTHORIZED,
                correlation_id: correlation_id.to_string(),
            });
        }
    }

    // Validate device binding if configured
    if std::env::var("JWT_REQUIRE_DEVICE_BINDING").unwrap_or_default() == "true" {
        if let Some(device_id) = &claims.device_id {
            let current_device = generate_device_fingerprint(req);
            if device_id != &current_device {
                return Err(AuthError {
                    error_type: ErrorType::Authentication,
                    message: "Device mismatch".to_string(),
                    status_code: StatusCode::UNAUTHORIZED,
                    correlation_id: correlation_id.to_string(),
                });
            }
        }
    }

    // Get user rate limit from database or default
    let rate_limit = get_user_rate_limit(&claims.sub, &state)
        .await
        .unwrap_or(100);

    // Log successful authentication
    audit_auth_success(&state, &claims.sub, AuthMethod::JwtToken).await;

    Ok(AuthUser {
        user_id: claims.sub.clone(),
        rate_limit,
        auth_method: AuthMethod::JwtToken,
    })
}

async fn validate_jwt_token_owned(
    token: String,
    state: &Arc<AppState>,
    correlation_id: &str,
    device_fingerprint: String,
) -> Result<AuthUser, AuthError> {
    // Get JWT secret from environment
    let jwt_secret = std::env::var("JWT_SECRET").map_err(|_| AuthError {
        error_type: ErrorType::Internal,
        message: "JWT_SECRET not configured".to_string(),
        status_code: StatusCode::INTERNAL_SERVER_ERROR,
        correlation_id: correlation_id.to_string(),
    })?;

    // Set up validation
    let mut validation = Validation::new(Algorithm::HS256);
    validation.set_audience(&["ccl-analysis-engine"]);
    validation.validate_exp = true;
    validation.validate_nbf = true;

    // Decode and validate token
    let token_data = decode::<Claims>(
        &token,
        &DecodingKey::from_secret(jwt_secret.as_bytes()),
        &validation,
    )
    .map_err(|e| {
        let message = match e.kind() {
            jsonwebtoken::errors::ErrorKind::ExpiredSignature => "Token has expired",
            jsonwebtoken::errors::ErrorKind::InvalidToken => "Invalid token format",
            jsonwebtoken::errors::ErrorKind::InvalidAudience => "Invalid token audience",
            jsonwebtoken::errors::ErrorKind::InvalidSignature => "Invalid token signature",
            _ => "Token validation failed",
        };

        AuthError {
            error_type: ErrorType::Authentication,
            message: message.to_string(),
            status_code: StatusCode::UNAUTHORIZED,
            correlation_id: correlation_id.to_string(),
        }
    })?;

    let claims = &token_data.claims;

    // Check token revocation
    if let Some(jti) = &claims.jti {
        if is_token_revoked(jti).await {
            return Err(AuthError {
                error_type: ErrorType::Authentication,
                message: "Token has been revoked".to_string(),
                status_code: StatusCode::UNAUTHORIZED,
                correlation_id: correlation_id.to_string(),
            });
        }
    }

    // Validate device binding if configured
    if std::env::var("JWT_REQUIRE_DEVICE_BINDING").unwrap_or_default() == "true" {
        if let Some(device_id) = &claims.device_id {
            if device_id != &device_fingerprint {
                return Err(AuthError {
                    error_type: ErrorType::Authentication,
                    message: "Device mismatch".to_string(),
                    status_code: StatusCode::UNAUTHORIZED,
                    correlation_id: correlation_id.to_string(),
                });
            }
        }
    }

    // Get user rate limit from database or default
    let rate_limit = get_user_rate_limit(&claims.sub, &state)
        .await
        .unwrap_or(100);

    // Log successful authentication
    audit_auth_success(&state, &claims.sub, AuthMethod::JwtToken).await;

    Ok(AuthUser {
        user_id: claims.sub.clone(),
        rate_limit,
        auth_method: AuthMethod::JwtToken,
    })
}

async fn validate_api_key_with_db(
    api_key: &str,
    key_prefix: &str,
    spanner_client: &google_cloud_spanner::client::Client,
) -> Result<(String, i64), String> {
    use google_cloud_spanner::statement::Statement;

    // Query by prefix for efficiency
    let mut statement = Statement::new(
        "SELECT user_id, api_key_hash, salt, rate_limit, is_active
         FROM api_keys
         WHERE key_prefix = @prefix AND is_active = true",
    );
    statement.add_param("prefix", &key_prefix);

    let mut transaction = spanner_client
        .read_only_transaction()
        .await
        .map_err(|e| format!("Database error: {}", e))?;

    let mut reader = transaction
        .query(statement)
        .await
        .map_err(|e| format!("Query error: {}", e))?;

    while let Some(row) = reader
        .next()
        .await
        .map_err(|e| format!("Read error: {}", e))?
    {
        let user_id: String = row
            .column_by_name("user_id")
            .map_err(|e| format!("Failed to get user_id: {}", e))?;
        let stored_hash: String = row
            .column_by_name("api_key_hash")
            .map_err(|e| format!("Failed to get api_key_hash: {}", e))?;
        let salt: String = row
            .column_by_name("salt")
            .map_err(|e| format!("Failed to get salt: {}", e))?;
        let rate_limit: i64 = row
            .column_by_name("rate_limit")
            .map_err(|e| format!("Failed to get rate_limit: {}", e))?;

        // Verify the API key
        if verify_api_key_hash(api_key, &stored_hash, &salt)? {
            return Ok((user_id, rate_limit));
        }
    }

    Err("Invalid API key".to_string())
}

fn verify_api_key_hash(api_key: &str, stored_hash: &str, salt: &str) -> Result<bool, String> {
    use base64::{engine::general_purpose, Engine as _};
    use sha2::{Digest, Sha256};

    let salt_bytes = general_purpose::STANDARD
        .decode(salt)
        .map_err(|e| format!("Failed to decode salt: {}", e))?;

    // Hash with 100k iterations like PBKDF2
    let mut hash = api_key.as_bytes().to_vec();
    for _ in 0..100_000 {
        let mut hasher = Sha256::new();
        hasher.update(&hash);
        hasher.update(&salt_bytes);
        hash = hasher.finalize().to_vec();
    }

    let computed_hash = general_purpose::STANDARD.encode(hash);
    Ok(computed_hash == stored_hash)
}

async fn get_user_rate_limit(user_id: &str, state: &Arc<AppState>) -> Option<i64> {
    if let Some(pool) = &state.spanner_pool {
        let spanner = pool.get().await.unwrap();
        let mut statement = google_cloud_spanner::statement::Statement::new(
            "SELECT rate_limit FROM users WHERE user_id = @user_id",
        );
        statement.add_param("user_id", &user_id);

        if let Ok(mut transaction) = spanner.read_only_transaction().await {
            if let Ok(mut reader) = transaction.query(statement).await {
                if let Ok(Some(row)) = reader.next().await {
                    if let Ok(rate_limit) = row.column_by_name::<i64>("rate_limit") {
                        return Some(rate_limit);
                    }
                }
            }
        }
    }
    None
}

fn generate_device_fingerprint(req: &Request) -> String {
    use sha2::{Digest, Sha256};

    let mut hasher = Sha256::new();
    let headers = req.headers();

    // Add user agent
    if let Some(user_agent) = headers.get("user-agent") {
        hasher.update(user_agent.as_bytes());
    }

    // Add accept headers
    if let Some(accept) = headers.get("accept") {
        hasher.update(accept.as_bytes());
    }
    if let Some(accept_lang) = headers.get("accept-language") {
        hasher.update(accept_lang.as_bytes());
    }
    if let Some(accept_enc) = headers.get("accept-encoding") {
        hasher.update(accept_enc.as_bytes());
    }

    format!("{:x}", hasher.finalize())
}

async fn is_token_revoked(_jti: &str) -> bool {
    // In production, check against Redis or database
    // For now, return false
    false
}

async fn audit_auth_success(state: &Arc<AppState>, user_id: &str, auth_method: AuthMethod) {
    let audit_logger = AuditLogger::new(state.spanner_pool.clone());
    let event = AuditEventBuilder::new(AuditAction::LoginSuccess)
        .user_id(user_id.to_string())
        .outcome(AuditOutcome::Success)
        .severity(AuditSeverity::Info)
        .metadata(serde_json::json!({
            "auth_method": format!("{:?}", auth_method),
        }))
        .build();

    if let Err(e) = audit_logger.log_event(event).await {
        tracing::error!("Failed to log auth success: {}", e);
    }
}

async fn audit_auth_failure(
    state: &Arc<AppState>,
    user_id: Option<&str>,
    auth_method: AuthMethod,
    error: &str,
) {
    let audit_logger = AuditLogger::new(state.spanner_pool.clone());
    let mut builder = AuditEventBuilder::new(AuditAction::LoginFailure)
        .outcome(AuditOutcome::Failure)
        .severity(AuditSeverity::Warning)
        .metadata(serde_json::json!({
            "auth_method": format!("{:?}", auth_method),
            "error": error,
        }));

    if let Some(uid) = user_id {
        builder = builder.user_id(uid.to_string());
    }

    let event = builder.build();

    if let Err(e) = audit_logger.log_event(event).await {
        tracing::error!("Failed to log auth failure: {}", e);
    }
}

// ==================== Tower Service Implementation ====================

/// Tower Service for optional authentication middleware
#[derive(Clone)]
pub struct OptionalAuthService<S> {
    inner: S,
    state: Arc<AppState>,
}

impl<S> Service<Request> for OptionalAuthService<S>
where
    S: Service<Request, Response = Response> + Send + 'static + Clone,
    S::Future: Send + 'static,
    S::Error: Into<Box<dyn std::error::Error + Send + Sync>> + Send + 'static,
{
    type Response = Response;
    type Error = S::Error;
    type Future = BoxFuture<'static, Result<Self::Response, Self::Error>>;

    fn poll_ready(&mut self, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        self.inner.poll_ready(cx)
    }

    fn call(&mut self, mut req: Request) -> Self::Future {
        let state = self.state.clone();
        let mut inner = self.inner.clone();

        Box::pin(async move {
            let correlation_id = Uuid::new_v4().to_string();

            // Extract auth headers before async operations
            let api_key = req.headers().get("x-api-key").and_then(|v| v.to_str().ok()).map(|s| s.to_string());
            let auth_header = req.headers().get(header::AUTHORIZATION).and_then(|v| v.to_str().ok()).map(|s| s.to_string());

            // Extract device fingerprint data for JWT validation
            let device_fingerprint = generate_device_fingerprint(&req);

            // Try to authenticate but don't fail if not present
            if let Some(api_key) = api_key {
                if let Ok(user) = validate_api_key(&api_key, &state, &correlation_id).await {
                    req.extensions_mut().insert(user);
                }
            } else if let Some(auth_header) = auth_header {
                if let Some(token) = auth_header.strip_prefix("Bearer ") {
                    if let Ok(user) = validate_jwt_token_owned(token.to_string(), &state, &correlation_id, device_fingerprint).await {
                        req.extensions_mut().insert(user);
                    }
                }
            }

            // Call the inner service
            inner.call(req).await
        })
    }
}

/// Tower Layer for optional authentication
#[derive(Clone)]
pub struct OptionalAuthLayer {
    state: Arc<AppState>,
}

impl OptionalAuthLayer {
    pub fn new(state: Arc<AppState>) -> Self {
        Self { state }
    }
}

impl<S> Layer<S> for OptionalAuthLayer {
    type Service = OptionalAuthService<S>;

    fn layer(&self, inner: S) -> Self::Service {
        OptionalAuthService {
            inner,
            state: self.state.clone(),
        }
    }
}

/// Tower Service for mandatory authentication middleware
#[derive(Clone)]
pub struct AuthService<S> {
    inner: S,
    state: Arc<AppState>,
}

impl<S> Service<Request> for AuthService<S>
where
    S: Service<Request, Response = Response> + Send + 'static + Clone,
    S::Future: Send + 'static,
    S::Error: Into<Box<dyn std::error::Error + Send + Sync>> + Send + 'static,
{
    type Response = Response;
    type Error = S::Error;
    type Future = BoxFuture<'static, Result<Self::Response, Self::Error>>;

    fn poll_ready(&mut self, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        self.inner.poll_ready(cx)
    }

    fn call(&mut self, mut req: Request) -> Self::Future {
        let state = self.state.clone();
        let mut inner = self.inner.clone();

        Box::pin(async move {
            let correlation_id = Uuid::new_v4().to_string();

            // Extract auth headers before async operations
            let api_key = req.headers().get("x-api-key").and_then(|v| v.to_str().ok()).map(|s| s.to_string());
            let auth_header = req.headers().get(header::AUTHORIZATION).and_then(|v| v.to_str().ok()).map(|s| s.to_string());

            // Extract device fingerprint data for JWT validation
            let device_fingerprint = generate_device_fingerprint(&req);

            // Try to authenticate - fail if not present
            let auth_result = if let Some(api_key) = api_key {
                validate_api_key(&api_key, &state, &correlation_id).await
            } else if let Some(auth_header) = auth_header {
                if let Some(token) = auth_header.strip_prefix("Bearer ") {
                    validate_jwt_token_owned(token.to_string(), &state, &correlation_id, device_fingerprint).await
                } else {
                    Err(AuthError {
                        error_type: ErrorType::Authentication,
                        message: "Invalid authorization header format".to_string(),
                        status_code: StatusCode::UNAUTHORIZED,
                        correlation_id: correlation_id.clone(),
                    })
                }
            } else {
                Err(AuthError {
                    error_type: ErrorType::Authentication,
                    message: "No authentication provided".to_string(),
                    status_code: StatusCode::UNAUTHORIZED,
                    correlation_id: correlation_id.clone(),
                })
            };

            match auth_result {
                Ok(user) => {
                    // Insert authenticated user into request extensions
                    req.extensions_mut().insert(user);
                    // Call the inner service
                    inner.call(req).await
                }
                Err(auth_error) => {
                    // Log authentication failure
                    audit_auth_failure(&state, None, AuthMethod::Unknown, &auth_error.message).await;
                    // Convert AuthError to Response and return
                    let response = auth_error.into_response();
                    Ok(response)
                }
            }
        })
    }
}

/// Tower Layer for mandatory authentication
#[derive(Clone)]
pub struct AuthLayer {
    state: Arc<AppState>,
}

impl AuthLayer {
    pub fn new(state: Arc<AppState>) -> Self {
        Self { state }
    }
}

impl<S> Layer<S> for AuthLayer {
    type Service = AuthService<S>;

    fn layer(&self, inner: S) -> Self::Service {
        AuthService {
            inner,
            state: self.state.clone(),
        }
    }
}
