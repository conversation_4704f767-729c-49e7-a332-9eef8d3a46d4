//! API handler for granular metrics endpoints

use axum::{
    extract::{Query, State},
    response::<PERSON><PERSON>,
    http::StatusCode,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use crate::metrics::granular::{GranularMetricsCollector, GranularMetricsReport};
use crate::api::AppState;

/// Query parameters for metrics filtering
#[derive(Debug, Deserialize)]
pub struct MetricsQuery {
    /// Filter by operation name
    pub operation: Option<String>,
    /// Filter by language
    pub language: Option<String>,
    /// Filter by time range (minutes)
    pub last_minutes: Option<u64>,
    /// Include detailed breakdowns
    pub detailed: Option<bool>,
}

/// Metrics response format
#[derive(Debug, Serialize)]
pub struct MetricsResponse {
    pub metrics: GranularMetricsReport,
    pub summary: MetricsSummary,
}

/// High-level metrics summary
#[derive(Debug, Serialize)]
pub struct MetricsSummary {
    pub total_operations: u64,
    pub success_rate: f64,
    pub avg_response_time_ms: f64,
    pub active_analyses: u64,
    pub error_rate: f64,
    pub cache_hit_rate: f64,
    pub top_slowest_operations: Vec<String>,
    pub most_error_prone_languages: Vec<String>,
}

/// Get granular performance metrics
pub async fn get_granular_metrics(
    State(_state): State<Arc<AppState>>,
    Query(params): Query<MetricsQuery>,
) -> Result<Json<MetricsResponse>, StatusCode> {
    // Get the granular metrics collector from app state
    // Note: In a real implementation, this would be stored in AppState
    let collector = GranularMetricsCollector::new();
    
    // Get the full metrics report
    let metrics = collector.get_metrics_report().await;
    
    // Calculate summary statistics
    let summary = calculate_metrics_summary(&metrics);
    
    // Apply filters if requested
    let filtered_metrics = if params.operation.is_some() || params.language.is_some() {
        filter_metrics(metrics, &params)
    } else {
        metrics
    };
    
    Ok(Json(MetricsResponse {
        metrics: filtered_metrics,
        summary,
    }))
}

/// Get metrics for a specific operation
pub async fn get_operation_metrics(
    State(_state): State<Arc<AppState>>,
    operation: String,
) -> Result<Json<OperationMetricsResponse>, StatusCode> {
    let collector = GranularMetricsCollector::new();
    let metrics = collector.get_metrics_report().await;
    
    if let Some(op_metrics) = metrics.operation_metrics.get(&operation) {
        Ok(Json(OperationMetricsResponse {
            operation: operation.clone(),
            metrics: op_metrics.clone(),
            percentiles: calculate_percentiles(op_metrics),
        }))
    } else {
        Err(StatusCode::NOT_FOUND)
    }
}

/// Get language-specific metrics
pub async fn get_language_metrics(
    State(_state): State<Arc<AppState>>,
) -> Result<Json<LanguageMetricsResponse>, StatusCode> {
    let collector = GranularMetricsCollector::new();
    let metrics = collector.get_metrics_report().await;
    
    let summary = LanguageMetricsSummary {
        total_languages: metrics.language_metrics.len(),
        total_files_parsed: metrics.language_metrics.values()
            .map(|m| m.files_parsed)
            .sum(),
        most_parsed_language: metrics.language_metrics.iter()
            .max_by_key(|(_, m)| m.files_parsed)
            .map(|(lang, _)| lang.clone()),
        highest_error_rate_language: metrics.language_metrics.iter()
            .max_by_key(|(_, m)| m.parse_errors)
            .map(|(lang, _)| lang.clone()),
    };
    
    Ok(Json(LanguageMetricsResponse {
        languages: metrics.language_metrics,
        summary,
    }))
}

/// Get error distribution metrics
pub async fn get_error_metrics(
    State(_state): State<Arc<AppState>>,
) -> Result<Json<ErrorMetricsResponse>, StatusCode> {
    let collector = GranularMetricsCollector::new();
    let metrics = collector.get_metrics_report().await;
    
    let analysis = ErrorAnalysis {
        total_errors: calculate_total_errors(&metrics.error_metrics),
        error_rate: calculate_error_rate(&metrics),
        most_common_error_type: find_most_common_error(&metrics.error_metrics),
        recovery_rate: calculate_recovery_rate(&metrics.error_metrics),
    };
    
    Ok(Json(ErrorMetricsResponse {
        errors: metrics.error_metrics,
        analysis,
    }))
}

/// Response types
#[derive(Debug, Serialize)]
pub struct OperationMetricsResponse {
    pub operation: String,
    pub metrics: crate::metrics::granular::OperationMetrics,
    pub percentiles: OperationPercentiles,
}

#[derive(Debug, Serialize)]
pub struct OperationPercentiles {
    pub p50_ms: f64,
    pub p75_ms: f64,
    pub p90_ms: f64,
    pub p95_ms: f64,
    pub p99_ms: f64,
}

#[derive(Debug, Serialize)]
pub struct LanguageMetricsResponse {
    pub languages: std::collections::HashMap<String, crate::metrics::granular::LanguageMetrics>,
    pub summary: LanguageMetricsSummary,
}

#[derive(Debug, Serialize)]
pub struct LanguageMetricsSummary {
    pub total_languages: usize,
    pub total_files_parsed: u64,
    pub most_parsed_language: Option<String>,
    pub highest_error_rate_language: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct ErrorMetricsResponse {
    pub errors: crate::metrics::granular::ErrorMetrics,
    pub analysis: ErrorAnalysis,
}

#[derive(Debug, Serialize)]
pub struct ErrorAnalysis {
    pub total_errors: u64,
    pub error_rate: f64,
    pub most_common_error_type: Option<String>,
    pub recovery_rate: f64,
}

// Helper functions

fn calculate_metrics_summary(metrics: &GranularMetricsReport) -> MetricsSummary {
    let total_operations: u64 = metrics.operation_metrics.values()
        .map(|m| m.count)
        .sum();
    
    let total_successes: u64 = metrics.operation_metrics.values()
        .map(|m| m.success_count)
        .sum();
    
    let success_rate = if total_operations > 0 {
        (total_successes as f64 / total_operations as f64) * 100.0
    } else {
        0.0
    };
    
    let avg_response_time_ms = metrics.endpoint_metrics.values()
        .map(|m| m.avg_response_time_ms)
        .sum::<f64>() / metrics.endpoint_metrics.len().max(1) as f64;
    
    let total_errors = calculate_total_errors(&metrics.error_metrics);
    let error_rate = if total_operations > 0 {
        (total_errors as f64 / total_operations as f64) * 100.0
    } else {
        0.0
    };
    
    // Find top slowest operations
    let mut operations: Vec<_> = metrics.operation_metrics.iter()
        .map(|(name, m)| (name.clone(), m.avg_duration.as_millis()))
        .collect();
    operations.sort_by_key(|(_, duration)| std::cmp::Reverse(*duration));
    let top_slowest_operations = operations.into_iter()
        .take(5)
        .map(|(name, _)| name)
        .collect();
    
    // Find most error-prone languages
    let mut languages: Vec<_> = metrics.error_metrics.errors_by_language.iter()
        .map(|(lang, count)| (lang.clone(), *count))
        .collect();
    languages.sort_by_key(|(_, count)| std::cmp::Reverse(*count));
    let most_error_prone_languages = languages.into_iter()
        .take(5)
        .map(|(lang, _)| lang)
        .collect();
    
    MetricsSummary {
        total_operations,
        success_rate,
        avg_response_time_ms,
        active_analyses: metrics.concurrency_metrics.active_analyses,
        error_rate,
        cache_hit_rate: 0.0, // Would calculate from cache metrics
        top_slowest_operations,
        most_error_prone_languages,
    }
}

fn filter_metrics(
    mut metrics: GranularMetricsReport,
    params: &MetricsQuery,
) -> GranularMetricsReport {
    // Filter by operation if specified
    if let Some(ref operation) = params.operation {
        metrics.operation_metrics.retain(|k, _| k.contains(operation));
    }
    
    // Filter by language if specified
    if let Some(ref language) = params.language {
        metrics.language_metrics.retain(|k, _| k == language);
    }
    
    metrics
}

fn calculate_percentiles(
    metrics: &crate::metrics::granular::OperationMetrics,
) -> OperationPercentiles {
    // In a real implementation, we would store duration samples
    // For now, we'll estimate based on min/max/avg
    let avg_ms = metrics.avg_duration.as_millis() as f64;
    let _min_ms = metrics.min_duration.as_millis() as f64;
    let max_ms = metrics.max_duration.as_millis() as f64;
    
    OperationPercentiles {
        p50_ms: avg_ms,
        p75_ms: avg_ms + (max_ms - avg_ms) * 0.25,
        p90_ms: avg_ms + (max_ms - avg_ms) * 0.40,
        p95_ms: avg_ms + (max_ms - avg_ms) * 0.45,
        p99_ms: avg_ms + (max_ms - avg_ms) * 0.49,
    }
}

fn calculate_total_errors(errors: &crate::metrics::granular::ErrorMetrics) -> u64 {
    errors.parser_errors +
    errors.validation_errors +
    errors.timeout_errors +
    errors.memory_errors +
    errors.network_errors +
    errors.database_errors +
    errors.authentication_errors +
    errors.rate_limit_errors
}

fn calculate_error_rate(metrics: &GranularMetricsReport) -> f64 {
    let total_operations: u64 = metrics.operation_metrics.values()
        .map(|m| m.count)
        .sum();
    
    let total_errors = calculate_total_errors(&metrics.error_metrics);
    
    if total_operations > 0 {
        (total_errors as f64 / total_operations as f64) * 100.0
    } else {
        0.0
    }
}

fn find_most_common_error(errors: &crate::metrics::granular::ErrorMetrics) -> Option<String> {
    let error_counts = vec![
        ("parser", errors.parser_errors),
        ("validation", errors.validation_errors),
        ("timeout", errors.timeout_errors),
        ("memory", errors.memory_errors),
        ("network", errors.network_errors),
        ("database", errors.database_errors),
        ("authentication", errors.authentication_errors),
        ("rate_limit", errors.rate_limit_errors),
    ];
    
    error_counts.into_iter()
        .max_by_key(|(_, count)| *count)
        .filter(|(_, count)| *count > 0)
        .map(|(name, _)| name.to_string())
}

fn calculate_recovery_rate(errors: &crate::metrics::granular::ErrorMetrics) -> f64 {
    let total_errors = errors.auto_recovered + errors.manual_intervention + errors.unrecoverable;
    
    if total_errors > 0 {
        (errors.auto_recovered as f64 / total_errors as f64) * 100.0
    } else {
        0.0
    }
}