use axum::{
    extract::{Path, Query, State},
    response::<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::{debug, error, info, instrument};
use validator::Validate;

use crate::api::errors::ApiError;
use crate::audit::{AuditAction, AuditEventBuilder, AuditLogger, AuditOutcome, AuditSeverity};
use crate::models::security::*;
use crate::services::security::SecurityAnalyzer;
use crate::storage::SpannerOperations;

/// Security analysis handler state
#[derive(Clone)]
pub struct SecurityHandlerState {
    pub security_analyzer: Arc<SecurityAnalyzer>,
    pub spanner: Arc<SpannerOperations>,
    pub audit_logger: Arc<AuditLogger>,
}

/// Request a security analysis for an existing analysis
#[instrument(skip(state))]
pub async fn start_security_analysis(
    State(state): State<SecurityHandlerState>,
    Path(analysis_id): Path<String>,
    Json(request): <PERSON><PERSON><SecurityAnalysisRequest>,
) -> Result<Json<SecurityAnalysisResponse>, ApiError> {
    if let Err(e) = request.validate() {
        return Err(ApiError::BadRequest(e.to_string()));
    }
    info!(
        analysis_id = %analysis_id,
        enable_vulnerability_detection = request.enable_vulnerability_detection,
        enable_dependency_scanning = request.enable_dependency_scanning,
        enable_secrets_detection = request.enable_secrets_detection,
        enable_compliance_checking = request.enable_compliance_checking,
        enable_threat_modeling = request.enable_threat_modeling,
        "Starting security analysis"
    );

    // Validate that the analysis exists
    let analysis_exists = state
        .spanner
        .analysis_exists(&analysis_id)
        .await
        .map_err(|e| {
            error!("Failed to check if analysis exists: {}", e);
            ApiError::InternalError("Database error".to_string())
        })?;

    if !analysis_exists {
        return Err(ApiError::NotFound(format!(
            "Analysis {} not found",
            analysis_id
        )));
    }

    // Get file analyses for the analysis
    let file_analyses = state
        .spanner
        .get_file_analyses(&analysis_id)
        .await
        .map_err(|e| {
            error!("Failed to get file analyses: {}", e);
            ApiError::InternalError("Failed to retrieve file analyses".to_string())
        })?;

    if file_analyses.is_empty() {
        return Err(ApiError::BadRequest(
            "No file analyses found for this analysis".to_string(),
        ));
    }

    // Perform security analysis
    let security_result = state
        .security_analyzer
        .analyze_security(&analysis_id, &file_analyses, &request)
        .await
        .map_err(|e| {
            error!("Security analysis failed: {}", e);

            // Log audit event for failed analysis - create future for background execution
            let audit_event = AuditEventBuilder::new(AuditAction::SecurityIncident)
                .resource("security_analysis", &analysis_id)
                .outcome(AuditOutcome::Failure)
                .severity(AuditSeverity::Error)
                .metadata(serde_json::json!({
                    "error": e.to_string(),
                    "analysis_id": analysis_id
                }))
                .build();

            let audit_logger = state.audit_logger.clone();
            tokio::spawn(async move {
                if let Err(audit_err) = audit_logger.log_event(audit_event).await {
                    error!("Failed to log audit event: {:?}", audit_err);
                }
            });

            ApiError::InternalError("Security analysis failed".to_string())
        })?;

    // Store security analysis results
    state
        .spanner
        .store_security_analysis(&security_result)
        .await
        .map_err(|e| {
            error!("Failed to store security analysis results: {}", e);
            ApiError::InternalError("Failed to store security analysis results".to_string())
        })?;

    // Log successful completion
    let audit_event = AuditEventBuilder::new(AuditAction::AnalysisCompleted)
        .resource("security_analysis", &analysis_id)
        .outcome(AuditOutcome::Success)
        .severity(AuditSeverity::Info)
        .metadata(serde_json::json!({
            "vulnerabilities_found": security_result.vulnerabilities.len(),
            "secrets_found": security_result.detected_secrets.len(),
            "overall_security_score": security_result.security_assessment.overall_security_score,
            "risk_level": security_result.security_assessment.risk_level.as_str()
        }))
        .build();

    if let Err(e) = state.audit_logger.log_event(audit_event).await {
        error!("Failed to log audit event: {}", e);
    }

    info!(
        analysis_id = %analysis_id,
        vulnerabilities_found = security_result.vulnerabilities.len(),
        secrets_found = security_result.detected_secrets.len(),
        overall_security_score = security_result.security_assessment.overall_security_score,
        "Security analysis completed successfully"
    );

    let response = SecurityAnalysisResponse {
        analysis_id: analysis_id.clone(),
        status: SecurityAnalysisStatus::Completed,
        started_at: chrono::Utc::now(),
        completed_at: Some(chrono::Utc::now()),
        overall_security_score: security_result.security_assessment.overall_security_score,
        risk_level: security_result.security_assessment.risk_level.clone(),
        total_vulnerabilities: security_result.vulnerabilities.len() as i64,
        total_secrets: security_result.detected_secrets.len() as i64,
        compliance_violations: security_result.compliance_violations.len() as i64,
        scan_duration_ms: security_result.metadata.scan_duration_ms,
    };

    Ok(Json(response))
}

/// Get security analysis results
#[instrument(skip(state))]
pub async fn get_security_analysis(
    State(state): State<SecurityHandlerState>,
    Path(analysis_id): Path<String>,
    Query(params): Query<SecurityResultsParams>,
) -> Result<Json<SecurityAnalysisResult>, ApiError> {
    if let Err(e) = params.validate() {
        return Err(ApiError::BadRequest(e.to_string()));
    }
    debug!(analysis_id = %analysis_id, "Getting security analysis results");

    let security_result = state
        .spanner
        .get_security_analysis(&analysis_id, &params)
        .await
        .map_err(|e| {
            error!("Failed to get security analysis: {}", e);
            ApiError::InternalError("Failed to retrieve security analysis".to_string())
        })?;

    match security_result {
        Some(result) => Ok(Json(result)),
        None => Err(ApiError::NotFound(format!(
            "Security analysis not found for analysis {}",
            analysis_id
        ))),
    }
}

/// Get security vulnerabilities for an analysis
#[instrument(skip(state))]
pub async fn get_vulnerabilities(
    State(state): State<SecurityHandlerState>,
    Path(analysis_id): Path<String>,
    Query(params): Query<VulnerabilityParams>,
) -> Result<Json<Vec<SecurityVulnerability>>, ApiError> {
    if let Err(e) = params.validate() {
        return Err(ApiError::BadRequest(e.to_string()));
    }
    debug!(analysis_id = %analysis_id, "Getting vulnerabilities");

    let vulnerabilities = state
        .spanner
        .get_vulnerabilities(&analysis_id, &params)
        .await
        .map_err(|e| {
            error!("Failed to get vulnerabilities: {}", e);
            ApiError::InternalError("Failed to retrieve vulnerabilities".to_string())
        })?;

    Ok(Json(vulnerabilities))
}

/// Get detected secrets for an analysis
#[instrument(skip(state))]
pub async fn get_secrets(
    State(state): State<SecurityHandlerState>,
    Path(analysis_id): Path<String>,
    Query(params): Query<SecretsParams>,
) -> Result<Json<Vec<DetectedSecret>>, ApiError> {
    if let Err(e) = params.validate() {
        return Err(ApiError::BadRequest(e.to_string()));
    }
    debug!(analysis_id = %analysis_id, "Getting detected secrets");

    let secrets = state
        .spanner
        .get_detected_secrets(&analysis_id, &params)
        .await
        .map_err(|e| {
            error!("Failed to get detected secrets: {}", e);
            ApiError::InternalError("Failed to retrieve detected secrets".to_string())
        })?;

    Ok(Json(secrets))
}

/// Get compliance violations for an analysis
#[instrument(skip(state))]
pub async fn get_compliance_violations(
    State(state): State<SecurityHandlerState>,
    Path(analysis_id): Path<String>,
    Query(params): Query<ComplianceParams>,
) -> Result<Json<Vec<ComplianceViolation>>, ApiError> {
    if let Err(e) = params.validate() {
        return Err(ApiError::BadRequest(e.to_string()));
    }
    debug!(analysis_id = %analysis_id, "Getting compliance violations");

    let violations = state
        .spanner
        .get_compliance_violations(&analysis_id, &params)
        .await
        .map_err(|e| {
            error!("Failed to get compliance violations: {}", e);
            ApiError::InternalError("Failed to retrieve compliance violations".to_string())
        })?;

    Ok(Json(violations))
}

/// Get threat models for an analysis
#[instrument(skip(state))]
pub async fn get_threat_models(
    State(state): State<SecurityHandlerState>,
    Path(analysis_id): Path<String>,
    Query(params): Query<ThreatModelParams>,
) -> Result<Json<Vec<ThreatModel>>, ApiError> {
    if let Err(e) = params.validate() {
        return Err(ApiError::BadRequest(e.to_string()));
    }
    debug!(analysis_id = %analysis_id, "Getting threat models");

    let threat_models = state
        .spanner
        .get_threat_models(&analysis_id, &params)
        .await
        .map_err(|e| {
            error!("Failed to get threat models: {}", e);
            ApiError::InternalError("Failed to retrieve threat models".to_string())
        })?;

    Ok(Json(threat_models))
}

/// Get security assessment summary
#[instrument(skip(state))]
pub async fn get_security_assessment(
    State(state): State<SecurityHandlerState>,
    Path(analysis_id): Path<String>,
) -> Result<Json<SecurityAssessment>, ApiError> {
    debug!(analysis_id = %analysis_id, "Getting security assessment");

    let assessment = state
        .spanner
        .get_security_assessment(&analysis_id)
        .await
        .map_err(|e| {
            error!("Failed to get security assessment: {}", e);
            ApiError::InternalError("Failed to retrieve security assessment".to_string())
        })?;

    match assessment {
        Some(assessment) => Ok(Json(assessment)),
        None => Err(ApiError::NotFound(format!(
            "Security assessment not found for analysis {}",
            analysis_id
        ))),
    }
}

/// Get security intelligence metadata
#[instrument(skip(state))]
pub async fn get_security_metadata(
    State(state): State<SecurityHandlerState>,
    Path(analysis_id): Path<String>,
) -> Result<Json<SecurityIntelligenceMetadata>, ApiError> {
    debug!(analysis_id = %analysis_id, "Getting security metadata");

    let metadata = state
        .spanner
        .get_security_metadata(&analysis_id)
        .await
        .map_err(|e| {
            error!("Failed to get security metadata: {}", e);
            ApiError::InternalError("Failed to retrieve security metadata".to_string())
        })?;

    match metadata {
        Some(metadata) => Ok(Json(metadata)),
        None => Err(ApiError::NotFound(format!(
            "Security metadata not found for analysis {}",
            analysis_id
        ))),
    }
}

/// Health check endpoint for security services
pub async fn security_health() -> Result<Json<SecurityHealthResponse>, ApiError> {
    Ok(Json(SecurityHealthResponse {
        status: "healthy".to_string(),
        services: vec![
            SecurityServiceStatus {
                name: "vulnerability_detector".to_string(),
                status: "operational".to_string(),
                last_updated: chrono::Utc::now(),
            },
            SecurityServiceStatus {
                name: "dependency_scanner".to_string(),
                status: "operational".to_string(),
                last_updated: chrono::Utc::now(),
            },
            SecurityServiceStatus {
                name: "secrets_detector".to_string(),
                status: "operational".to_string(),
                last_updated: chrono::Utc::now(),
            },
            SecurityServiceStatus {
                name: "compliance_checker".to_string(),
                status: "operational".to_string(),
                last_updated: chrono::Utc::now(),
            },
            SecurityServiceStatus {
                name: "threat_modeler".to_string(),
                status: "operational".to_string(),
                last_updated: chrono::Utc::now(),
            },
        ],
        threat_intel_last_update: Some(chrono::Utc::now()),
        vulnerability_db_version: "v2.1.0".to_string(),
    }))
}

// Request/Response models

#[derive(Debug, Deserialize, Validate)]
pub struct SecurityResultsParams {
    pub include_vulnerabilities: Option<bool>,
    pub include_secrets: Option<bool>,
    pub include_compliance: Option<bool>,
    pub include_threat_models: Option<bool>,
    #[validate(length(min = 1, max = 255))]
    pub severity_filter: Option<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct VulnerabilityParams {
    #[validate(length(min = 1, max = 255))]
    pub severity: Option<String>,
    #[validate(length(min = 1, max = 255))]
    pub vulnerability_type: Option<String>,
    #[validate(range(min = 0.0, max = 1.0))]
    pub confidence_threshold: Option<f64>,
    #[validate(range(min = 1, max = 1000))]
    pub limit: Option<i32>,
    #[validate(range(min = 0))]
    pub offset: Option<i32>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct SecretsParams {
    #[validate(length(min = 1, max = 255))]
    pub secret_type: Option<String>,
    #[validate(length(min = 1, max = 255))]
    pub severity: Option<String>,
    pub exclude_test_files: Option<bool>,
    #[validate(range(min = 0.0, max = 1.0))]
    pub confidence_threshold: Option<f64>,
    #[validate(range(min = 1, max = 1000))]
    pub limit: Option<i32>,
    #[validate(range(min = 0))]
    pub offset: Option<i32>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct ComplianceParams {
    #[validate(length(min = 1, max = 255))]
    pub framework: Option<String>,
    #[validate(length(min = 1, max = 255))]
    pub severity: Option<String>,
    #[validate(length(min = 1, max = 255))]
    pub category: Option<String>,
    #[validate(range(min = 1, max = 1000))]
    pub limit: Option<i32>,
    #[validate(range(min = 0))]
    pub offset: Option<i32>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct ThreatModelParams {
    #[validate(length(min = 1, max = 255))]
    pub threat_category: Option<String>,
    #[validate(range(min = 0.0, max = 1.0))]
    pub risk_threshold: Option<f64>,
    #[validate(length(min = 1, max = 255))]
    pub mitigation_status: Option<String>,
    #[validate(range(min = 1, max = 1000))]
    pub limit: Option<i32>,
    #[validate(range(min = 0))]
    pub offset: Option<i32>,
}

#[derive(Debug, Serialize)]
pub struct SecurityAnalysisResponse {
    pub analysis_id: String,
    pub status: SecurityAnalysisStatus,
    pub started_at: chrono::DateTime<chrono::Utc>,
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
    pub overall_security_score: f64,
    pub risk_level: RiskLevel,
    pub total_vulnerabilities: i64,
    pub total_secrets: i64,
    pub compliance_violations: i64,
    pub scan_duration_ms: Option<i64>,
}

#[derive(Debug, Serialize)]
pub enum SecurityAnalysisStatus {
    Pending,
    InProgress,
    Completed,
    Failed,
}

#[derive(Debug, Serialize)]
pub struct SecurityHealthResponse {
    pub status: String,
    pub services: Vec<SecurityServiceStatus>,
    pub threat_intel_last_update: Option<chrono::DateTime<chrono::Utc>>,
    pub vulnerability_db_version: String,
}

#[derive(Debug, Serialize)]
pub struct SecurityServiceStatus {
    pub name: String,
    pub status: String,
    pub last_updated: chrono::DateTime<chrono::Utc>,
}
