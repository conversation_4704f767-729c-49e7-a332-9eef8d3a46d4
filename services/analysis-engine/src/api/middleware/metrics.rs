use axum::{
    extract::MatchedPath,
    http::Request,
    middleware::Next,
    response::Response,
};
use std::time::Instant;
use crate::metrics::prometheus::record_http_request;

/// Middleware to track HTTP request metrics
pub async fn metrics_middleware(
    req: Request<axum::body::Body>,
    next: Next,
) -> Response {
    let start = Instant::now();
    let method = req.method().to_string();
    let path = req
        .extensions()
        .get::<MatchedPath>()
        .map(|path| path.as_str().to_string())
        .unwrap_or_else(|| req.uri().path().to_string());

    // Call the next middleware/handler
    let response = next.run(req).await;
    
    // Record metrics
    let duration = start.elapsed().as_secs_f64();
    let status = response.status().as_u16();
    
    record_http_request(&method, &path, status, duration);

    response
}