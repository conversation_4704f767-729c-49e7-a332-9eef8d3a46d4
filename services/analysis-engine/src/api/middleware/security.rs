use axum::{
    extract::Request,
    http::header,
    middleware::Next,
    response::Response,
};

/// Security headers middleware that adds comprehensive security headers to all responses
/// 
/// This middleware implements defense-in-depth security by adding multiple layers of protection:
/// - Content Security Policy (CSP) to prevent XSS attacks
/// - HTTP Strict Transport Security (HSTS) to enforce HTTPS
/// - X-Frame-Options to prevent clickjacking
/// - X-Content-Type-Options to prevent MIME type sniffing
/// - X-XSS-Protection for legacy browser protection
/// - Referrer-Policy to control referrer information leakage
/// - Permissions-Policy to restrict access to browser features
/// - Cache-Control headers for sensitive endpoints
pub async fn security_headers_middleware(
    request: Request,
    next: Next,
) -> Response {
    let mut response = next.run(request).await;
    let headers = response.headers_mut();

    // HTTP Strict Transport Security (HSTS)
    // Enforces HTTPS for 1 year, includes subdomains, and enables preload
    if let Ok(hsts_value) = "max-age=31536000; includeSubDomains; preload".parse() {
        headers.insert(header::STRICT_TRANSPORT_SECURITY, hsts_value);
    }

    // Content Security Policy (CSP)
    // Restrictive policy that only allows same-origin resources
    let csp_policy = [
        "default-src 'self'",
        "script-src 'self'",
        "style-src 'self' 'unsafe-inline'", // Allow inline styles for error pages
        "img-src 'self' data:",
        "font-src 'self'",
        "connect-src 'self'",
        "frame-src 'none'",
        "object-src 'none'",
        "base-uri 'self'",
        "form-action 'self'",
        "upgrade-insecure-requests",
    ].join("; ");
    
    if let Ok(csp_value) = csp_policy.parse() {
        headers.insert(header::CONTENT_SECURITY_POLICY, csp_value);
    }

    // X-Frame-Options: Prevent clickjacking attacks
    if let Ok(frame_value) = "DENY".parse() {
        headers.insert(
            axum::http::HeaderName::from_static("x-frame-options"),
            frame_value,
        );
    }

    // X-Content-Type-Options: Prevent MIME type sniffing
    if let Ok(content_type_value) = "nosniff".parse() {
        headers.insert(
            axum::http::HeaderName::from_static("x-content-type-options"),
            content_type_value,
        );
    }

    // X-XSS-Protection: Enable XSS filtering (legacy browsers)
    if let Ok(xss_value) = "1; mode=block".parse() {
        headers.insert(
            axum::http::HeaderName::from_static("x-xss-protection"),
            xss_value,
        );
    }

    // Referrer-Policy: Control referrer information leakage
    if let Ok(referrer_value) = "strict-origin-when-cross-origin".parse() {
        headers.insert(
            axum::http::HeaderName::from_static("referrer-policy"),
            referrer_value,
        );
    }

    // Permissions-Policy: Restrict access to browser features
    let permissions_policy = [
        "geolocation=()",
        "microphone=()",
        "camera=()",
        "payment=()",
        "usb=()",
        "magnetometer=()",
        "gyroscope=()",
        "speaker=()",
        "vibrate=()",
        "fullscreen=(self)",
    ].join(", ");

    if let Ok(permissions_value) = permissions_policy.parse() {
        headers.insert(
            axum::http::HeaderName::from_static("permissions-policy"),
            permissions_value,
        );
    }

    // Cross-Origin-Embedder-Policy: Require CORP for cross-origin resources
    if let Ok(embedder_value) = "require-corp".parse() {
        headers.insert(
            axum::http::HeaderName::from_static("cross-origin-embedder-policy"),
            embedder_value,
        );
    }

    // Cross-Origin-Opener-Policy: Isolate browsing context
    if let Ok(opener_value) = "same-origin".parse() {
        headers.insert(
            axum::http::HeaderName::from_static("cross-origin-opener-policy"),
            opener_value,
        );
    }

    // Cross-Origin-Resource-Policy: Control cross-origin resource sharing
    if let Ok(resource_value) = "same-origin".parse() {
        headers.insert(
            axum::http::HeaderName::from_static("cross-origin-resource-policy"),
            resource_value,
        );
    }

    // Server header removal (don't advertise server technology)
    headers.remove(header::SERVER);

    // X-Powered-By header removal (if present)
    headers.remove(axum::http::HeaderName::from_static("x-powered-by"));

    response
}

/// Additional security headers for API endpoints that handle sensitive data
pub async fn api_security_headers_middleware(
    request: Request,
    next: Next,
) -> Response {
    let mut response = next.run(request).await;
    let headers = response.headers_mut();

    // Cache-Control: Prevent caching of sensitive API responses
    if let Ok(cache_value) = "no-store, no-cache, must-revalidate, private".parse() {
        headers.insert(header::CACHE_CONTROL, cache_value);
    }

    // Pragma: Additional cache control for HTTP/1.0 compatibility
    if let Ok(pragma_value) = "no-cache".parse() {
        headers.insert(header::PRAGMA, pragma_value);
    }

    // Expires: Set expiration date in the past
    if let Ok(expires_value) = "0".parse() {
        headers.insert(header::EXPIRES, expires_value);
    }

    response
}
