#![warn(clippy::unwrap_used)]
#![warn(clippy::expect_used)]
#![warn(clippy::panic)]
#![warn(clippy::unimplemented)]
#![warn(clippy::todo)]

use anyhow::Result;
use axum::{
    Router,
    routing::{get, post, delete},
    middleware,
};
use std::net::SocketAddr;
use std::env;
use std::sync::Arc;
use tower_http::{
    cors::CorsLayer,
    trace::TraceLayer,
    compression::CompressionLayer,
};
use tracing::info;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

use analysis_engine::{
    api::{self, AppState},
    config,
    metrics::prometheus::init_metrics,
};

#[tokio::main]
async fn main() -> Result<()> {
    // Load environment variables from .env file
    dotenvy::dotenv().ok();

    // Initialize tracing
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .map_err(|e| anyhow::anyhow!("Failed to create env filter: {}", e))?,
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    // Cloud Run sets the PORT environment variable - read it with fallback
    let port = env::var("PORT")
        .unwrap_or_else(|_| "8001".to_string())
        .parse::<u16>()
        .map_err(|e| analysis_engine::errors::AnalysisError::Config(format!("Invalid PORT: {}", e)))?;
    
    info!("PORT environment variable: {}", port);

    let _config = config::ServiceConfig::from_env()?;

    // Initialize Prometheus metrics
    init_metrics();
    info!("Initialized Prometheus metrics");

    info!("Starting Analysis Engine service on port {}", port);

    // Build the application
    let app = create_app().await.map_err(|e| {
        tracing::error!("Failed to create application: {:?}", e);
        e
    })?;

    // Run the server on the port specified in the configuration
    let addr = SocketAddr::from(([0, 0, 0, 0], port));
    info!("Analysis Engine listening on {}", addr);
    
    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}

async fn create_app() -> Result<Router> {
    // Initialize services
    let state = Arc::new(AppState::new().await?);

    // Create protected routes that require authentication
    // These routes will be wrapped with auth and rate limiting middleware
    let protected_routes = Router::new()
        // Core analysis endpoints
        .route("/api/v1/analysis", post(api::handlers::create_analysis))
        .route("/api/v1/analysis", get(api::handlers::list_analyses))
        .route("/api/v1/analysis/{id}", get(api::handlers::get_analysis))
        .route("/api/v1/analysis/{id}/results", get(api::handlers::get_analysis_results))
        .route("/api/v1/analysis/{id}", delete(api::handlers::cancel_analysis))
        .route("/api/v1/analysis/{id}/status", get(api::handlers::get_analysis_status))
        .route("/api/v1/analysis/{id}/download", get(api::handlers::download_analysis))
        .route("/api/v1/analysis/{id}/metrics", get(api::handlers::get_analysis_metrics))
        .route("/api/v1/analysis/{id}/patterns", get(api::handlers::get_analysis_patterns))
        .route("/api/v1/analysis/{id}/warnings", get(api::handlers::get_analysis_warnings))

        // WebSocket endpoint for real-time progress
        .route("/ws/analysis/{id}", get(api::handlers::websocket_handler))

        // Apply auth and rate limiting middleware only to protected routes
        .layer(middleware::from_fn_with_state(state.clone(), api::rate_limit_extractor::rate_limit_middleware))
        // Tower Service-based authentication middleware (compatible with Axum 0.8)
        .layer(api::auth_extractor::OptionalAuthLayer::new(state.clone()))
        // Apply state to make this Router<()>
        .with_state(state.clone());

    // Create public routes that don't require authentication
    let public_routes = Router::new()
        // Health and monitoring endpoints
        .route("/health", get(api::handlers::health))
        .route("/health/live", get(api::handlers::liveness))
        .route("/health/auth", get(api::handlers::auth_status))
        .route("/health/detailed", get(api::handlers::detailed_health))
        .route("/health/ready", get(api::handlers::ready))
        .route("/metrics", get(api::handlers::metrics))
        .route("/backpressure", get(api::handlers::backpressure_status))
        .route("/circuit-breakers", get(api::handlers::circuit_breaker_status))
        
        // Public API endpoints
        .route("/api/v1/languages", get(api::handlers::supported_languages))
        .route("/api/v1/version", get(api::handlers::version))
        
        // Apply the same state type as protected routes
        .with_state(state.clone());

    // Build the main application by merging routes
    // Both routers now have Router<()> type since they already have state applied
    let app = Router::new()
        // Merge the routers - both have Router<()> type now
        .merge(protected_routes)
        .merge(public_routes)
        // Add global middleware layers (order matters - last added runs first)
        .layer(middleware::from_fn(api::middleware::metrics_middleware))
        .layer(middleware::from_fn(api::middleware::request_id_middleware))
        .layer(middleware::from_fn(api::middleware::security_headers_middleware))
        .layer(middleware::from_fn(api::middleware::api_security_headers_middleware))
        .layer(CorsLayer::permissive())
        .layer(CompressionLayer::new())
        .layer(TraceLayer::new_for_http());

    Ok(app)
}
