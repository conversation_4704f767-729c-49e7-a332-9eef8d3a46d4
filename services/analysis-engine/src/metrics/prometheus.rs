use lazy_static::lazy_static;
use prometheus::{
    register_counter_vec, register_gauge_vec, register_histogram_vec,
    CounterVec, GaugeVec, HistogramVec, 
    register_int_gauge, IntGauge,
    DEFAULT_BUCKETS,
};

lazy_static! {
    // HTTP Request metrics
    pub static ref HTTP_REQUESTS_TOTAL: CounterVec = register_counter_vec!(
        "http_requests_total",
        "Total number of HTTP requests",
        &["method", "endpoint", "status"]
    ).expect("Failed to register HTTP_REQUESTS_TOTAL metric");

    pub static ref HTTP_REQUEST_DURATION: HistogramVec = register_histogram_vec!(
        "http_request_duration_seconds",
        "HTTP request latency in seconds",
        &["method", "endpoint"],
        DEFAULT_BUCKETS.to_vec()
    ).expect("Failed to register HTTP_REQUEST_DURATION metric");

    // Analysis metrics
    pub static ref ANALYSIS_TOTAL: CounterVec = register_counter_vec!(
        "analysis_total",
        "Total number of analyses performed",
        &["language", "status"]
    ).expect("Failed to register ANALYSIS_TOTAL metric");

    pub static ref ANALYSIS_DURATION: HistogramVec = register_histogram_vec!(
        "analysis_duration_seconds",
        "Analysis duration in seconds",
        &["language"],
        vec![0.1, 0.5, 1.0, 2.5, 5.0, 10.0, 30.0, 60.0, 120.0, 300.0]
    ).expect("Failed to register ANALYSIS_DURATION metric");

    pub static ref ACTIVE_ANALYSES: IntGauge = register_int_gauge!(
        "active_analyses",
        "Number of currently active analyses"
    ).expect("Failed to register ACTIVE_ANALYSES metric");

    // Language processing metrics
    pub static ref LANGUAGE_PROCESSING_TOTAL: CounterVec = register_counter_vec!(
        "language_processing_total",
        "Total number of files processed by language",
        &["language"]
    ).expect("Failed to register LANGUAGE_PROCESSING_TOTAL metric");

    // System metrics
    pub static ref MEMORY_USAGE_BYTES: IntGauge = register_int_gauge!(
        "memory_usage_bytes",
        "Current memory usage in bytes"
    ).expect("Failed to register MEMORY_USAGE_BYTES metric");

    pub static ref CPU_USAGE_PERCENT: GaugeVec = register_gauge_vec!(
        "cpu_usage_percent",
        "Current CPU usage percentage",
        &["core"]
    ).expect("Failed to register CPU_USAGE_PERCENT metric");

    // Database connection pool metrics
    pub static ref DB_CONNECTIONS_ACTIVE: IntGauge = register_int_gauge!(
        "database_connections_active",
        "Number of active database connections"
    ).expect("Failed to register DB_CONNECTIONS_ACTIVE metric");

    pub static ref DB_CONNECTIONS_IDLE: IntGauge = register_int_gauge!(
        "database_connections_idle",
        "Number of idle database connections"
    ).expect("Failed to register DB_CONNECTIONS_IDLE metric");

    // Cache metrics
    pub static ref CACHE_HITS: CounterVec = register_counter_vec!(
        "cache_hits_total",
        "Total number of cache hits",
        &["cache_type"]
    ).expect("Failed to register CACHE_HITS metric");

    pub static ref CACHE_MISSES: CounterVec = register_counter_vec!(
        "cache_misses_total",
        "Total number of cache misses",
        &["cache_type"]
    ).expect("Failed to register CACHE_MISSES metric");

    // Error metrics
    pub static ref ERRORS_TOTAL: CounterVec = register_counter_vec!(
        "errors_total",
        "Total number of errors",
        &["type", "service"]
    ).expect("Failed to register ERRORS_TOTAL metric");

    // Backpressure metrics
    pub static ref BACKPRESSURE_REJECTIONS: CounterVec = register_counter_vec!(
        "backpressure_rejections_total",
        "Total number of requests rejected due to backpressure",
        &["reason"]
    ).expect("Failed to register BACKPRESSURE_REJECTIONS metric");

    pub static ref CIRCUIT_BREAKER_STATE: GaugeVec = register_gauge_vec!(
        "circuit_breaker_state",
        "Circuit breaker state (0=closed, 1=open, 2=half-open)",
        &["service"]
    ).expect("Failed to register CIRCUIT_BREAKER_STATE metric");
}

/// Initialize all Prometheus metrics
pub fn init_metrics() {
    // Force lazy_static initialization
    lazy_static::initialize(&HTTP_REQUESTS_TOTAL);
    lazy_static::initialize(&HTTP_REQUEST_DURATION);
    lazy_static::initialize(&ANALYSIS_TOTAL);
    lazy_static::initialize(&ANALYSIS_DURATION);
    lazy_static::initialize(&ACTIVE_ANALYSES);
    lazy_static::initialize(&LANGUAGE_PROCESSING_TOTAL);
    lazy_static::initialize(&MEMORY_USAGE_BYTES);
    lazy_static::initialize(&CPU_USAGE_PERCENT);
    lazy_static::initialize(&DB_CONNECTIONS_ACTIVE);
    lazy_static::initialize(&DB_CONNECTIONS_IDLE);
    lazy_static::initialize(&CACHE_HITS);
    lazy_static::initialize(&CACHE_MISSES);
    lazy_static::initialize(&ERRORS_TOTAL);
    lazy_static::initialize(&BACKPRESSURE_REJECTIONS);
    lazy_static::initialize(&CIRCUIT_BREAKER_STATE);
}

/// Helper function to record HTTP request metrics
pub fn record_http_request(method: &str, endpoint: &str, status: u16, duration: f64) {
    HTTP_REQUESTS_TOTAL
        .with_label_values(&[method, endpoint, &status.to_string()])
        .inc();
    
    HTTP_REQUEST_DURATION
        .with_label_values(&[method, endpoint])
        .observe(duration);
}

/// Helper function to record analysis metrics
pub fn record_analysis(language: &str, status: &str, duration: f64) {
    ANALYSIS_TOTAL
        .with_label_values(&[language, status])
        .inc();
    
    if status == "success" {
        ANALYSIS_DURATION
            .with_label_values(&[language])
            .observe(duration);
    }
}

/// Helper function to update system metrics
pub fn update_system_metrics(memory_bytes: i64, cpu_percent: f64) {
    MEMORY_USAGE_BYTES.set(memory_bytes);
    CPU_USAGE_PERCENT
        .with_label_values(&["total"])
        .set(cpu_percent);
}