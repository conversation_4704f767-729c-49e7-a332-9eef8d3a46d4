//! Granular performance metrics collection
//! 
//! This module provides fine-grained performance metrics for detailed monitoring
//! and optimization of the analysis engine operations.

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use serde::{Serialize, Deserialize};
use prometheus::{IntCounter, Histogram, HistogramOpts, Registry, Gauge};
use lazy_static::lazy_static;

lazy_static! {
    /// Parser operation metrics
    static ref PARSER_OPERATION_HISTOGRAM: Histogram = Histogram::with_opts(
        HistogramOpts::new("parser_operation_duration_seconds", "Parser operation duration in seconds")
            .buckets(vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0])
    ).unwrap();
    
    /// Security scan metrics
    static ref SECURITY_SCAN_HISTOGRAM: Histogram = Histogram::with_opts(
        HistogramOpts::new("security_scan_duration_seconds", "Security scan duration in seconds")
            .buckets(vec![0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0, 10.0])
    ).unwrap();
    
    /// Pattern detection metrics
    static ref PATTERN_DETECTION_HISTOGRAM: Histogram = Histogram::with_opts(
        HistogramOpts::new("pattern_detection_duration_seconds", "Pattern detection duration in seconds")
            .buckets(vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0])
    ).unwrap();
    
    /// Cache operation metrics
    static ref CACHE_HIT_COUNTER: IntCounter = IntCounter::new("cache_hits_total", "Total cache hits").unwrap();
    static ref CACHE_MISS_COUNTER: IntCounter = IntCounter::new("cache_misses_total", "Total cache misses").unwrap();
    
    /// Database query metrics
    static ref DB_QUERY_HISTOGRAM: Histogram = Histogram::with_opts(
        HistogramOpts::new("db_query_duration_seconds", "Database query duration in seconds")
            .buckets(vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0])
    ).unwrap();
    
    /// Memory allocation gauge
    static ref MEMORY_ALLOCATED_BYTES: Gauge = Gauge::new("memory_allocated_bytes", "Current allocated memory in bytes").unwrap();
    
    /// Active goroutines/tasks gauge
    static ref ACTIVE_TASKS: Gauge = Gauge::new("active_tasks", "Number of active async tasks").unwrap();
}

/// Granular metrics collector for detailed performance tracking
pub struct GranularMetricsCollector {
    /// Operation-specific metrics
    operation_metrics: Arc<RwLock<HashMap<String, OperationMetrics>>>,
    /// Language-specific parsing metrics
    language_metrics: Arc<RwLock<HashMap<String, LanguageMetrics>>>,
    /// File size distribution metrics
    file_size_metrics: Arc<RwLock<FileSizeMetrics>>,
    /// Concurrency metrics
    concurrency_metrics: Arc<RwLock<ConcurrencyMetrics>>,
    /// Error distribution metrics
    error_metrics: Arc<RwLock<ErrorMetrics>>,
    /// API endpoint metrics
    endpoint_metrics: Arc<RwLock<HashMap<String, EndpointMetrics>>>,
}

/// Metrics for specific operations
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(default)]
pub struct OperationMetrics {
    /// Operation name
    pub operation: String,
    /// Total execution count
    pub count: u64,
    /// Total execution time
    pub total_duration: Duration,
    /// Min execution time
    pub min_duration: Duration,
    /// Max execution time
    pub max_duration: Duration,
    /// Average execution time
    pub avg_duration: Duration,
    /// P50 latency
    pub p50_duration: Duration,
    /// P95 latency
    pub p95_duration: Duration,
    /// P99 latency
    pub p99_duration: Duration,
    /// Success count
    pub success_count: u64,
    /// Failure count
    pub failure_count: u64,
    /// Last execution time
    #[serde(skip_serializing, skip_deserializing)]
    pub last_execution: Instant,
}

impl Default for OperationMetrics {
    fn default() -> Self {
        Self {
            operation: String::new(),
            count: 0,
            total_duration: Duration::default(),
            min_duration: Duration::default(),
            max_duration: Duration::default(),
            avg_duration: Duration::default(),
            p50_duration: Duration::default(),
            p95_duration: Duration::default(),
            p99_duration: Duration::default(),
            success_count: 0,
            failure_count: 0,
            last_execution: Instant::now(),
        }
    }
}

/// Language-specific parsing metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LanguageMetrics {
    /// Language name
    pub language: String,
    /// Files parsed
    pub files_parsed: u64,
    /// Total lines parsed
    pub lines_parsed: u64,
    /// Average parse time per file
    pub avg_parse_time_ms: f64,
    /// Parse errors
    pub parse_errors: u64,
    /// AST node count statistics
    pub avg_ast_nodes: u64,
    pub max_ast_nodes: u64,
    /// Memory usage per language
    pub avg_memory_per_file_kb: u64,
}

/// File size distribution metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileSizeMetrics {
    /// Files by size bucket
    pub tiny_files: u64,      // < 1KB
    pub small_files: u64,     // 1KB - 10KB
    pub medium_files: u64,    // 10KB - 100KB
    pub large_files: u64,     // 100KB - 1MB
    pub huge_files: u64,      // > 1MB
    /// Performance by size
    pub tiny_avg_parse_ms: f64,
    pub small_avg_parse_ms: f64,
    pub medium_avg_parse_ms: f64,
    pub large_avg_parse_ms: f64,
    pub huge_avg_parse_ms: f64,
}

/// Concurrency and parallelism metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConcurrencyMetrics {
    /// Current active analyses
    pub active_analyses: u64,
    /// Peak concurrent analyses
    pub peak_concurrent_analyses: u64,
    /// Parser pool utilization
    pub parser_pool_size: u64,
    pub parser_pool_busy: u64,
    pub parser_pool_idle: u64,
    /// Task queue metrics
    pub queued_tasks: u64,
    pub completed_tasks: u64,
    pub rejected_tasks: u64,
    /// Thread pool statistics
    pub thread_pool_size: u64,
    pub active_threads: u64,
    /// Lock contention metrics
    pub lock_acquisitions: u64,
    pub lock_contentions: u64,
    pub avg_lock_wait_time_us: u64,
}

/// Error distribution and categorization
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorMetrics {
    /// Errors by category
    pub parser_errors: u64,
    pub validation_errors: u64,
    pub timeout_errors: u64,
    pub memory_errors: u64,
    pub network_errors: u64,
    pub database_errors: u64,
    pub authentication_errors: u64,
    pub rate_limit_errors: u64,
    /// Error patterns
    pub errors_by_language: HashMap<String, u64>,
    pub errors_by_file_size: HashMap<String, u64>,
    pub errors_by_operation: HashMap<String, u64>,
    /// Recovery metrics
    pub auto_recovered: u64,
    pub manual_intervention: u64,
    pub unrecoverable: u64,
}

/// API endpoint-specific metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EndpointMetrics {
    /// Endpoint path
    pub endpoint: String,
    /// Request count
    pub request_count: u64,
    /// Response time distribution
    pub avg_response_time_ms: f64,
    pub p50_response_time_ms: f64,
    pub p95_response_time_ms: f64,
    pub p99_response_time_ms: f64,
    /// Status code distribution
    pub status_2xx: u64,
    pub status_3xx: u64,
    pub status_4xx: u64,
    pub status_5xx: u64,
    /// Request size statistics
    pub avg_request_size_bytes: u64,
    pub avg_response_size_bytes: u64,
}

impl GranularMetricsCollector {
    /// Create a new granular metrics collector
    pub fn new() -> Self {
        Self {
            operation_metrics: Arc::new(RwLock::new(HashMap::new())),
            language_metrics: Arc::new(RwLock::new(HashMap::new())),
            file_size_metrics: Arc::new(RwLock::new(FileSizeMetrics::default())),
            concurrency_metrics: Arc::new(RwLock::new(ConcurrencyMetrics::default())),
            error_metrics: Arc::new(RwLock::new(ErrorMetrics::default())),
            endpoint_metrics: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Record operation timing with detailed metrics
    pub async fn record_operation(&self, operation: &str, duration: Duration, success: bool) {
        let mut metrics = self.operation_metrics.write().await;
        let entry = metrics.entry(operation.to_string()).or_insert_with(|| {
            OperationMetrics {
                operation: operation.to_string(),
                count: 0,
                total_duration: Duration::ZERO,
                min_duration: Duration::MAX,
                max_duration: Duration::ZERO,
                avg_duration: Duration::ZERO,
                p50_duration: Duration::ZERO,
                p95_duration: Duration::ZERO,
                p99_duration: Duration::ZERO,
                success_count: 0,
                failure_count: 0,
                last_execution: Instant::now(),
            }
        });

        entry.count += 1;
        entry.total_duration += duration;
        entry.min_duration = entry.min_duration.min(duration);
        entry.max_duration = entry.max_duration.max(duration);
        entry.avg_duration = entry.total_duration / entry.count as u32;
        entry.last_execution = Instant::now();

        if success {
            entry.success_count += 1;
        } else {
            entry.failure_count += 1;
        }

        // Update Prometheus metrics
        match operation {
            "parse_file" => PARSER_OPERATION_HISTOGRAM.observe(duration.as_secs_f64()),
            "security_scan" => SECURITY_SCAN_HISTOGRAM.observe(duration.as_secs_f64()),
            "pattern_detection" => PATTERN_DETECTION_HISTOGRAM.observe(duration.as_secs_f64()),
            "db_query" => DB_QUERY_HISTOGRAM.observe(duration.as_secs_f64()),
            _ => {}
        }
    }

    /// Record language-specific parsing metrics
    pub async fn record_language_parse(
        &self,
        language: &str,
        file_size: u64,
        line_count: u64,
        parse_time: Duration,
        ast_nodes: u64,
        success: bool,
    ) {
        let mut metrics = self.language_metrics.write().await;
        let entry = metrics.entry(language.to_string()).or_insert_with(|| {
            LanguageMetrics {
                language: language.to_string(),
                files_parsed: 0,
                lines_parsed: 0,
                avg_parse_time_ms: 0.0,
                parse_errors: 0,
                avg_ast_nodes: 0,
                max_ast_nodes: 0,
                avg_memory_per_file_kb: 0,
            }
        });

        if success {
            entry.files_parsed += 1;
            entry.lines_parsed += line_count;
            
            // Update averages
            let total_parse_time_ms = entry.avg_parse_time_ms * (entry.files_parsed - 1) as f64;
            entry.avg_parse_time_ms = (total_parse_time_ms + parse_time.as_millis() as f64) / entry.files_parsed as f64;
            
            // Update AST statistics
            let total_ast_nodes = entry.avg_ast_nodes * (entry.files_parsed - 1);
            entry.avg_ast_nodes = (total_ast_nodes + ast_nodes) / entry.files_parsed;
            entry.max_ast_nodes = entry.max_ast_nodes.max(ast_nodes);
            
            // Estimate memory usage
            entry.avg_memory_per_file_kb = file_size / 1024;
        } else {
            entry.parse_errors += 1;
        }

        // Update file size metrics
        self.record_file_size_metrics(file_size, parse_time).await;
    }

    /// Record file size distribution metrics
    async fn record_file_size_metrics(&self, file_size: u64, parse_time: Duration) {
        let mut metrics = self.file_size_metrics.write().await;
        let parse_ms = parse_time.as_millis() as f64;

        match file_size {
            0..=1024 => {
                metrics.tiny_files += 1;
                metrics.tiny_avg_parse_ms = 
                    (metrics.tiny_avg_parse_ms * (metrics.tiny_files - 1) as f64 + parse_ms) 
                    / metrics.tiny_files as f64;
            }
            1025..=10240 => {
                metrics.small_files += 1;
                metrics.small_avg_parse_ms = 
                    (metrics.small_avg_parse_ms * (metrics.small_files - 1) as f64 + parse_ms) 
                    / metrics.small_files as f64;
            }
            10241..=102400 => {
                metrics.medium_files += 1;
                metrics.medium_avg_parse_ms = 
                    (metrics.medium_avg_parse_ms * (metrics.medium_files - 1) as f64 + parse_ms) 
                    / metrics.medium_files as f64;
            }
            102401..=1048576 => {
                metrics.large_files += 1;
                metrics.large_avg_parse_ms = 
                    (metrics.large_avg_parse_ms * (metrics.large_files - 1) as f64 + parse_ms) 
                    / metrics.large_files as f64;
            }
            _ => {
                metrics.huge_files += 1;
                metrics.huge_avg_parse_ms = 
                    (metrics.huge_avg_parse_ms * (metrics.huge_files - 1) as f64 + parse_ms) 
                    / metrics.huge_files as f64;
            }
        }
    }

    /// Update concurrency metrics
    pub async fn update_concurrency_metrics(
        &self,
        active_analyses: u64,
        parser_pool_busy: u64,
        parser_pool_size: u64,
        queued_tasks: u64,
    ) {
        let mut metrics = self.concurrency_metrics.write().await;
        
        metrics.active_analyses = active_analyses;
        metrics.peak_concurrent_analyses = metrics.peak_concurrent_analyses.max(active_analyses);
        metrics.parser_pool_busy = parser_pool_busy;
        metrics.parser_pool_size = parser_pool_size;
        metrics.parser_pool_idle = parser_pool_size.saturating_sub(parser_pool_busy);
        metrics.queued_tasks = queued_tasks;
        
        // Update Prometheus gauges
        ACTIVE_TASKS.set(active_analyses as f64);
    }

    /// Record error occurrence with categorization
    pub async fn record_error(&self, error_type: &str, language: Option<&str>, operation: Option<&str>) {
        let mut metrics = self.error_metrics.write().await;
        
        // Categorize error
        match error_type {
            "parser" => metrics.parser_errors += 1,
            "validation" => metrics.validation_errors += 1,
            "timeout" => metrics.timeout_errors += 1,
            "memory" => metrics.memory_errors += 1,
            "network" => metrics.network_errors += 1,
            "database" => metrics.database_errors += 1,
            "auth" => metrics.authentication_errors += 1,
            "rate_limit" => metrics.rate_limit_errors += 1,
            _ => {}
        }
        
        // Track by language if provided
        if let Some(lang) = language {
            *metrics.errors_by_language.entry(lang.to_string()).or_insert(0) += 1;
        }
        
        // Track by operation if provided
        if let Some(op) = operation {
            *metrics.errors_by_operation.entry(op.to_string()).or_insert(0) += 1;
        }
    }

    /// Record API endpoint metrics
    pub async fn record_endpoint_metrics(
        &self,
        endpoint: &str,
        response_time: Duration,
        status_code: u16,
        request_size: u64,
        response_size: u64,
    ) {
        let mut metrics = self.endpoint_metrics.write().await;
        let entry = metrics.entry(endpoint.to_string()).or_insert_with(|| {
            EndpointMetrics {
                endpoint: endpoint.to_string(),
                request_count: 0,
                avg_response_time_ms: 0.0,
                p50_response_time_ms: 0.0,
                p95_response_time_ms: 0.0,
                p99_response_time_ms: 0.0,
                status_2xx: 0,
                status_3xx: 0,
                status_4xx: 0,
                status_5xx: 0,
                avg_request_size_bytes: 0,
                avg_response_size_bytes: 0,
            }
        });
        
        entry.request_count += 1;
        
        // Update response time average
        let response_ms = response_time.as_millis() as f64;
        entry.avg_response_time_ms = 
            (entry.avg_response_time_ms * (entry.request_count - 1) as f64 + response_ms) 
            / entry.request_count as f64;
        
        // Update status code counters
        match status_code {
            200..=299 => entry.status_2xx += 1,
            300..=399 => entry.status_3xx += 1,
            400..=499 => entry.status_4xx += 1,
            500..=599 => entry.status_5xx += 1,
            _ => {}
        }
        
        // Update size averages
        entry.avg_request_size_bytes = 
            (entry.avg_request_size_bytes * (entry.request_count - 1) + request_size) 
            / entry.request_count;
        entry.avg_response_size_bytes = 
            (entry.avg_response_size_bytes * (entry.request_count - 1) + response_size) 
            / entry.request_count;
    }

    /// Record cache operation
    pub async fn record_cache_operation(&self, hit: bool) {
        if hit {
            CACHE_HIT_COUNTER.inc();
        } else {
            CACHE_MISS_COUNTER.inc();
        }
    }

    /// Update memory allocation metrics
    pub async fn update_memory_metrics(&self, allocated_bytes: u64) {
        MEMORY_ALLOCATED_BYTES.set(allocated_bytes as f64);
    }

    /// Get comprehensive metrics report
    pub async fn get_metrics_report(&self) -> GranularMetricsReport {
        GranularMetricsReport {
            operation_metrics: self.operation_metrics.read().await.clone(),
            language_metrics: self.language_metrics.read().await.clone(),
            file_size_metrics: self.file_size_metrics.read().await.clone(),
            concurrency_metrics: self.concurrency_metrics.read().await.clone(),
            error_metrics: self.error_metrics.read().await.clone(),
            endpoint_metrics: self.endpoint_metrics.read().await.clone(),
            timestamp: chrono::Utc::now(),
        }
    }

    /// Register Prometheus metrics
    pub fn register_prometheus_metrics(registry: &Registry) -> Result<(), Box<dyn std::error::Error>> {
        registry.register(Box::new(PARSER_OPERATION_HISTOGRAM.clone()))?;
        registry.register(Box::new(SECURITY_SCAN_HISTOGRAM.clone()))?;
        registry.register(Box::new(PATTERN_DETECTION_HISTOGRAM.clone()))?;
        registry.register(Box::new(CACHE_HIT_COUNTER.clone()))?;
        registry.register(Box::new(CACHE_MISS_COUNTER.clone()))?;
        registry.register(Box::new(DB_QUERY_HISTOGRAM.clone()))?;
        registry.register(Box::new(MEMORY_ALLOCATED_BYTES.clone()))?;
        registry.register(Box::new(ACTIVE_TASKS.clone()))?;
        Ok(())
    }
}

/// Comprehensive granular metrics report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GranularMetricsReport {
    pub operation_metrics: HashMap<String, OperationMetrics>,
    pub language_metrics: HashMap<String, LanguageMetrics>,
    pub file_size_metrics: FileSizeMetrics,
    pub concurrency_metrics: ConcurrencyMetrics,
    pub error_metrics: ErrorMetrics,
    pub endpoint_metrics: HashMap<String, EndpointMetrics>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl Default for FileSizeMetrics {
    fn default() -> Self {
        Self {
            tiny_files: 0,
            small_files: 0,
            medium_files: 0,
            large_files: 0,
            huge_files: 0,
            tiny_avg_parse_ms: 0.0,
            small_avg_parse_ms: 0.0,
            medium_avg_parse_ms: 0.0,
            large_avg_parse_ms: 0.0,
            huge_avg_parse_ms: 0.0,
        }
    }
}

impl Default for ConcurrencyMetrics {
    fn default() -> Self {
        Self {
            active_analyses: 0,
            peak_concurrent_analyses: 0,
            parser_pool_size: 0,
            parser_pool_busy: 0,
            parser_pool_idle: 0,
            queued_tasks: 0,
            completed_tasks: 0,
            rejected_tasks: 0,
            thread_pool_size: 0,
            active_threads: 0,
            lock_acquisitions: 0,
            lock_contentions: 0,
            avg_lock_wait_time_us: 0,
        }
    }
}

impl Default for ErrorMetrics {
    fn default() -> Self {
        Self {
            parser_errors: 0,
            validation_errors: 0,
            timeout_errors: 0,
            memory_errors: 0,
            network_errors: 0,
            database_errors: 0,
            authentication_errors: 0,
            rate_limit_errors: 0,
            errors_by_language: HashMap::new(),
            errors_by_file_size: HashMap::new(),
            errors_by_operation: HashMap::new(),
            auto_recovered: 0,
            manual_intervention: 0,
            unrecoverable: 0,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_operation_metrics_recording() {
        let collector = GranularMetricsCollector::new();
        
        // Record some operations
        collector.record_operation("parse_file", Duration::from_millis(50), true).await;
        collector.record_operation("parse_file", Duration::from_millis(100), true).await;
        collector.record_operation("parse_file", Duration::from_millis(75), false).await;
        
        let report = collector.get_metrics_report().await;
        let parse_metrics = report.operation_metrics.get("parse_file").unwrap();
        
        assert_eq!(parse_metrics.count, 3);
        assert_eq!(parse_metrics.success_count, 2);
        assert_eq!(parse_metrics.failure_count, 1);
        assert_eq!(parse_metrics.min_duration, Duration::from_millis(50));
        assert_eq!(parse_metrics.max_duration, Duration::from_millis(100));
    }

    #[tokio::test]
    async fn test_language_metrics() {
        let collector = GranularMetricsCollector::new();
        
        // Record Rust file parsing
        collector.record_language_parse(
            "rust",
            5000,  // 5KB file
            150,   // 150 lines
            Duration::from_millis(25),
            500,   // 500 AST nodes
            true
        ).await;
        
        let report = collector.get_metrics_report().await;
        let rust_metrics = report.language_metrics.get("rust").unwrap();
        
        assert_eq!(rust_metrics.files_parsed, 1);
        assert_eq!(rust_metrics.lines_parsed, 150);
        assert_eq!(rust_metrics.avg_parse_time_ms, 25.0);
        assert_eq!(rust_metrics.avg_ast_nodes, 500);
    }

    #[tokio::test]
    async fn test_error_categorization() {
        let collector = GranularMetricsCollector::new();
        
        // Record various errors
        collector.record_error("parser", Some("python"), Some("parse_file")).await;
        collector.record_error("timeout", None, Some("analyze")).await;
        collector.record_error("database", None, None).await;
        
        let report = collector.get_metrics_report().await;
        
        assert_eq!(report.error_metrics.parser_errors, 1);
        assert_eq!(report.error_metrics.timeout_errors, 1);
        assert_eq!(report.error_metrics.database_errors, 1);
        assert_eq!(*report.error_metrics.errors_by_language.get("python").unwrap(), 1);
    }
}