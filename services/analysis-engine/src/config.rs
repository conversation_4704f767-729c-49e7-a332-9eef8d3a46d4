use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debug, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct ServiceConfig {
    pub service: ServiceSettings,
    pub gcp: GcpSettings,
    pub analysis: AnalysisSettings,
    pub security: SecuritySettings,
    pub observability: ObservabilitySettings,
    pub circuit_breaker: CircuitBreakerConfig,
    pub redis: RedisSettings,
    pub resource_limits: ResourceLimitConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ServiceSettings {
    pub name: String,
    pub version: String,
    pub port: u16,
    pub host: String,
    pub environment: Environment,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum Environment {
    Development,
    Staging,
    Production,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Default)]
pub struct GcpSettings {
    pub project_id: String,
    pub spanner_instance: String,
    pub spanner_database: String,
    pub storage_bucket: String,
    pub storage_bucket_name: String,
    pub pubsub_topic: String,
    pub region: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AnalysisSettings {
    pub max_concurrent_analyses: usize,
    pub max_repository_size_gb: u64,
    pub analysis_timeout_seconds: u64,
    pub max_file_size_mb: u64,
    pub temp_dir: String,
    pub supported_languages: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecuritySettings {
    pub enable_auth: bool,
    pub api_key_header: String,
    pub jwt_secret: Option<String>,
    pub cors_origins: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ObservabilitySettings {
    pub enable_tracing: bool,
    pub enable_metrics: bool,
    pub log_level: String,
    pub otel_endpoint: Option<String>,
}

impl ServiceConfig {
    pub fn from_env() -> Result<Self, config::ConfigError> {
        let environment = env::var("ENVIRONMENT")
            .or_else(|_| env::var("ENV"))  // Fallback for compatibility
            .unwrap_or_else(|_| "development".to_string());
        
        let environment = match environment.as_str() {
            "production" => Environment::Production,
            "staging" => Environment::Staging,
            _ => Environment::Development,
        };

        Ok(ServiceConfig {
            service: ServiceSettings {
                name: "analysis-engine".to_string(),
                version: env!("CARGO_PKG_VERSION").to_string(),
                port: env::var("PORT")
                    .unwrap_or_else(|_| "8001".to_string())
                    .parse()
                    .unwrap_or(8001),
                host: env::var("HOST")
                    .unwrap_or_else(|_| "0.0.0.0".to_string()),
                environment,
            },
            gcp: GcpSettings {
                project_id: env::var("GCP_PROJECT_ID")
                    .unwrap_or_else(|_| "ccl-platform".to_string()),
                spanner_instance: env::var("SPANNER_INSTANCE")
                    .unwrap_or_else(|_| "ccl-production".to_string()),
                spanner_database: env::var("SPANNER_DATABASE")
                    .unwrap_or_else(|_| "ccl-main".to_string()),
                storage_bucket: env::var("STORAGE_BUCKET")
                    .unwrap_or_else(|_| "ccl-analysis-artifacts".to_string()),
                storage_bucket_name: env::var("STORAGE_BUCKET_NAME")
                    .unwrap_or_else(|_| format!("ccl-analysis-{}", env::var("GCP_PROJECT_ID").unwrap_or_else(|_| "ccl-platform".to_string()))),
                pubsub_topic: env::var("PUBSUB_TOPIC")
                    .unwrap_or_else(|_| "analysis-events".to_string()),
                region: env::var("GCP_REGION")
                    .unwrap_or_else(|_| "us-central1".to_string()),
            },
            analysis: AnalysisSettings {
                max_concurrent_analyses: env::var("MAX_CONCURRENT_ANALYSES")
                    .unwrap_or_else(|_| "50".to_string())
                    .parse()
                    .unwrap_or(50),
                max_repository_size_gb: env::var("MAX_REPOSITORY_SIZE_GB")
                    .unwrap_or_else(|_| "10".to_string())
                    .parse()
                    .unwrap_or(10),
                analysis_timeout_seconds: env::var("ANALYSIS_TIMEOUT_SECONDS")
                    .unwrap_or_else(|_| "300".to_string())
                    .parse()
                    .unwrap_or(300),
                max_file_size_mb: env::var("MAX_FILE_SIZE_MB")
                    .unwrap_or_else(|_| "50".to_string())
                    .parse()
                    .unwrap_or(50),
                temp_dir: env::var("TEMP_DIR")
                    .unwrap_or_else(|_| "/tmp/ccl-analysis".to_string()),
                supported_languages: vec![
                    "rust", "javascript", "typescript", "python", "go", 
                    "java", "c", "cpp", "c#", "php", "ruby", "swift", 
                    "kotlin", "scala", "haskell", "elixir", "erlang",
                    "clojure", "r", "julia", "dart", "lua", "perl",
                    "shell", "sql", "html", "css", "json", "yaml",
                    "toml", "xml", "markdown"
                ].into_iter().map(String::from).collect(),
            },
            security: SecuritySettings {
                enable_auth: env::var("ENABLE_AUTH")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
                api_key_header: env::var("API_KEY_HEADER")
                    .unwrap_or_else(|_| "x-api-key".to_string()),
                jwt_secret: env::var("JWT_SECRET").ok(),
                cors_origins: env::var("CORS_ORIGINS")
                    .unwrap_or_else(|_| "*".to_string())
                    .split(',')
                    .map(|s| s.trim().to_string())
                    .collect(),
            },
            observability: ObservabilitySettings {
                enable_tracing: env::var("ENABLE_TRACING")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
                enable_metrics: env::var("ENABLE_METRICS")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
                log_level: env::var("LOG_LEVEL")
                    .unwrap_or_else(|_| "info".to_string()),
                otel_endpoint: env::var("OTEL_ENDPOINT").ok(),
            },
            circuit_breaker: CircuitBreakerConfig::default(),
            redis: RedisSettings {
                url: env::var("REDIS_URL").unwrap_or_else(|_| "redis://127.0.0.1/".to_string()),
            },
            resource_limits: ResourceLimitConfig::from_env(),
        })
    }
}

/// Configuration for the circuit breaker
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircuitBreakerConfig {
    /// Number of failures before opening the circuit
    pub failure_threshold: u32,
    /// Timeout in seconds before resetting the circuit
    pub reset_timeout_secs: u64,
}

impl Default for CircuitBreakerConfig {
    fn default() -> Self {
        Self {
            failure_threshold: 5,
            reset_timeout_secs: 60,
        }
    }
}

/// Configuration for Redis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RedisSettings {
    /// The URL of the Redis server
    pub url: String,
}

impl Default for RedisSettings {
    fn default() -> Self {
        Self {
            url: "redis://127.0.0.1/".to_string(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceLimitConfig {
    pub max_file_size_bytes: u64,
    pub parse_timeout_seconds: u64,
    pub max_analysis_memory_mb: u64,
    pub max_dependency_count: usize,
}

impl ResourceLimitConfig {
    pub fn from_env() -> Self {
        Self {
            max_file_size_bytes: env::var("MAX_FILE_SIZE_BYTES")
                .unwrap_or_else(|_| "10000000".to_string()) // 10MB
                .parse()
                .unwrap_or(10_000_000),
            parse_timeout_seconds: env::var("PARSE_TIMEOUT_SECONDS")
                .unwrap_or_else(|_| "30".to_string())
                .parse()
                .unwrap_or(30),
            max_analysis_memory_mb: env::var("MAX_ANALYSIS_MEMORY_MB")
                .unwrap_or_else(|_| "2048".to_string()) // 2GB
                .parse()
                .unwrap_or(2048),
            max_dependency_count: env::var("MAX_DEPENDENCY_COUNT")
                .unwrap_or_else(|_| "10000".to_string()) // 10K dependencies
                .parse()
                .unwrap_or(10_000),
        }
    }
}

impl Default for ResourceLimitConfig {
    fn default() -> Self {
        Self::from_env()
    }
}

impl Default for ServiceConfig {
    fn default() -> Self {
        Self::from_env().unwrap_or_else(|_| {
            // Fallback configuration for testing
            Self {
                service: ServiceSettings {
                    name: "analysis-engine".to_string(),
                    version: "0.1.0".to_string(),
                    port: 8080,
                    host: "0.0.0.0".to_string(),
                    environment: Environment::Development,
                },
                gcp: GcpSettings {
                    project_id: "test-project".to_string(),
                    spanner_instance: "test-instance".to_string(),
                    spanner_database: "test-database".to_string(),
                    storage_bucket: "test-bucket".to_string(),
                    storage_bucket_name: "test-bucket".to_string(),
                    pubsub_topic: "test-topic".to_string(),
                    region: "us-central1".to_string(),
                },
                analysis: AnalysisSettings {
                    max_concurrent_analyses: 10,
                    max_repository_size_gb: 1,
                    analysis_timeout_seconds: 300,
                    max_file_size_mb: 10,
                    temp_dir: "/tmp".to_string(),
                    supported_languages: vec!["rust".to_string(), "javascript".to_string()],
                },
                security: SecuritySettings {
                    enable_auth: false,
                    api_key_header: "X-API-Key".to_string(),
                    jwt_secret: None,
                    cors_origins: vec!["*".to_string()],
                },
                observability: ObservabilitySettings {
                    enable_tracing: true,
                    enable_metrics: true,
                    log_level: "info".to_string(),
                    otel_endpoint: None,
                },
                circuit_breaker: CircuitBreakerConfig::default(),
                redis: RedisSettings::default(),
                resource_limits: ResourceLimitConfig::default(),
            }
        })
    }
}
