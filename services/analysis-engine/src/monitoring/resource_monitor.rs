//! Resource monitoring for DoS prevention

use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{warn, error};

/// Resource monitor for tracking memory and CPU usage during analysis
#[derive(Debug)]
pub struct ResourceMonitor {
    /// Maximum memory allowed per analysis in bytes
    max_memory_bytes: u64,
    /// Current memory usage in bytes
    current_memory: Arc<AtomicU64>,
    /// Analysis start time
    start_time: Arc<RwLock<Option<Instant>>>,
    /// Maximum analysis duration
    max_duration: Duration,
}

impl ResourceMonitor {
    /// Create a new resource monitor with specified limits
    pub fn new(max_memory_mb: u64, max_duration_seconds: u64) -> Self {
        Self {
            max_memory_bytes: max_memory_mb * 1024 * 1024,
            current_memory: Arc::new(AtomicU64::new(0)),
            start_time: Arc::new(RwLock::new(None)),
            max_duration: Duration::from_secs(max_duration_seconds),
        }
    }

    /// Start monitoring for a new analysis
    pub async fn start_monitoring(&self) {
        let mut start_time = self.start_time.write().await;
        *start_time = Some(Instant::now());
        self.current_memory.store(0, Ordering::SeqCst);
    }

    /// Check if resource limits are exceeded
    pub async fn check_limits(&self) -> Result<(), ResourceLimitError> {
        // Check memory limit
        let current_mem = self.get_current_memory_usage();
        if current_mem > self.max_memory_bytes {
            error!(
                "Memory limit exceeded: {} MB > {} MB",
                current_mem / 1024 / 1024,
                self.max_memory_bytes / 1024 / 1024
            );
            return Err(ResourceLimitError::MemoryLimitExceeded {
                current: current_mem,
                limit: self.max_memory_bytes,
            });
        }

        // Check time limit
        let start_time = self.start_time.read().await;
        if let Some(start) = *start_time {
            let elapsed = start.elapsed();
            if elapsed > self.max_duration {
                error!(
                    "Time limit exceeded: {:?} > {:?}",
                    elapsed, self.max_duration
                );
                return Err(ResourceLimitError::TimeLimitExceeded {
                    elapsed,
                    limit: self.max_duration,
                });
            }
        }

        Ok(())
    }

    /// Update memory usage estimate
    pub fn add_memory_usage(&self, bytes: u64) {
        let previous = self.current_memory.fetch_add(bytes, Ordering::SeqCst);
        let new_total = previous + bytes;
        
        if new_total > self.max_memory_bytes * 90 / 100 {
            warn!(
                "Memory usage approaching limit: {} MB / {} MB ({}%)",
                new_total / 1024 / 1024,
                self.max_memory_bytes / 1024 / 1024,
                (new_total * 100) / self.max_memory_bytes
            );
        }
    }

    /// Get current memory usage
    pub fn get_current_memory_usage(&self) -> u64 {
        self.current_memory.load(Ordering::SeqCst)
    }

    /// Get system memory info (not implemented without sysinfo crate)
    pub fn get_system_memory_info() -> Option<SystemMemoryInfo> {
        // TODO: Add sysinfo crate for proper system memory monitoring
        // For now, return None to avoid compilation errors
        None
    }

    /// Estimate memory usage for a file
    pub fn estimate_file_memory(file_size: u64) -> u64 {
        // Rough estimate: file content + AST nodes + analysis overhead
        // AST typically 5-10x file size for complex files
        file_size * 8
    }
}

/// System memory information
#[derive(Debug, Clone)]
pub struct SystemMemoryInfo {
    pub total_memory: u64,
    pub used_memory: u64,
    pub available_memory: u64,
}

/// Resource limit errors
#[derive(Debug, thiserror::Error)]
pub enum ResourceLimitError {
    #[error("Memory limit exceeded: {current} bytes > {limit} bytes")]
    MemoryLimitExceeded { current: u64, limit: u64 },
    
    #[error("Time limit exceeded: {elapsed:?} > {limit:?}")]
    TimeLimitExceeded {
        elapsed: Duration,
        limit: Duration,
    },
}

/// Resource usage tracker for individual files
#[derive(Debug, Default)]
pub struct FileResourceTracker {
    pub file_path: String,
    pub file_size: u64,
    pub parse_time_ms: u64,
    pub memory_estimate: u64,
    pub ast_node_count: usize,
}

impl FileResourceTracker {
    pub fn new(file_path: String, file_size: u64) -> Self {
        Self {
            file_path,
            file_size,
            memory_estimate: ResourceMonitor::estimate_file_memory(file_size),
            ..Default::default()
        }
    }

    pub fn set_parse_time(&mut self, duration: Duration) {
        self.parse_time_ms = duration.as_millis() as u64;
    }

    pub fn set_ast_nodes(&mut self, count: usize) {
        self.ast_node_count = count;
        // Refine memory estimate based on AST size
        self.memory_estimate = self.file_size + (count as u64 * 256); // ~256 bytes per AST node
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_memory_limit_check() {
        let monitor = ResourceMonitor::new(100, 60); // 100MB, 60s
        monitor.start_monitoring().await;
        
        // Add memory usage
        monitor.add_memory_usage(50 * 1024 * 1024); // 50MB
        assert!(monitor.check_limits().await.is_ok());
        
        // Exceed limit
        monitor.add_memory_usage(60 * 1024 * 1024); // +60MB = 110MB total
        assert!(monitor.check_limits().await.is_err());
    }

    #[test]
    fn test_memory_estimation() {
        let estimate = ResourceMonitor::estimate_file_memory(1024 * 1024); // 1MB file
        assert_eq!(estimate, 8 * 1024 * 1024); // Should estimate 8MB
    }
}