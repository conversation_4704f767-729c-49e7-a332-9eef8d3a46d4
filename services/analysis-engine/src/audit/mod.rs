use crate::storage::connection_pool::SpannerConnectionManager;
use anyhow::{Context, Result};
use bb8::Pool;
use chrono::{DateTime, Utc};
// Removed mutation imports to avoid prost version conflicts
// use google_cloud_googleapis::spanner::v1::{mutation, Mutation};
// use prost_types::{ListValue, Value};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuditEvent {
    pub log_id: String,
    pub user_id: Option<String>,
    pub organization_id: Option<String>,
    pub action: String,
    pub resource_type: Option<String>,
    pub resource_id: Option<String>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub request_id: Option<String>,
    pub session_id: Option<String>,
    pub outcome: AuditOutcome,
    pub severity: AuditSeverity,
    pub metadata: Option<serde_json::Value>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AuditOutcome {
    Success,
    Failure,
    Partial,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AuditSeverity {
    Info,
    Warning,
    Error,
    Critical,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AuditAction {
    // Authentication events
    LoginAttempt,
    LoginSuccess,
    LoginFailure,
    TokenGenerated,
    TokenRevoked,
    ApiKeyUsed,
    ApiKeyCreated,
    ApiKeyRevoked,

    // Authorization events
    AccessGranted,
    AccessDenied,
    PermissionChanged,
    RoleAssigned,
    RoleRevoked,

    // Analysis events
    AnalysisStarted,
    AnalysisCompleted,
    AnalysisFailed,
    AnalysisCancelled,

    // Data access events
    RepositoryAccessed,
    PatternAccessed,
    DataExported,
    DataDeleted,

    // Administrative events
    UserCreated,
    UserModified,
    UserDeleted,
    ConfigurationChanged,
    SecurityPolicyUpdated,

    // System events
    ServiceStarted,
    ServiceStopped,
    DatabaseMigration,
    SecurityIncident,

    // Security Intelligence events
    SecurityScanStarted,
    SecurityScanCompleted,
    VulnerabilityDetected,
    SecretFound,
    ComplianceViolationDetected,
    ThreatModelGenerated,
    SecurityAssessmentCompleted,
    ThreatIntelligenceUpdated,
    SecurityPolicyViolation,
    FalsePositiveReported,
    SecurityReportGenerated,
}

impl AuditAction {
    pub fn as_str(&self) -> &'static str {
        match self {
            AuditAction::LoginAttempt => "login_attempt",
            AuditAction::LoginSuccess => "login_success",
            AuditAction::LoginFailure => "login_failure",
            AuditAction::TokenGenerated => "token_generated",
            AuditAction::TokenRevoked => "token_revoked",
            AuditAction::ApiKeyUsed => "api_key_used",
            AuditAction::ApiKeyCreated => "api_key_created",
            AuditAction::ApiKeyRevoked => "api_key_revoked",
            AuditAction::AccessGranted => "access_granted",
            AuditAction::AccessDenied => "access_denied",
            AuditAction::PermissionChanged => "permission_changed",
            AuditAction::RoleAssigned => "role_assigned",
            AuditAction::RoleRevoked => "role_revoked",
            AuditAction::AnalysisStarted => "analysis_started",
            AuditAction::AnalysisCompleted => "analysis_completed",
            AuditAction::AnalysisFailed => "analysis_failed",
            AuditAction::AnalysisCancelled => "analysis_cancelled",
            AuditAction::RepositoryAccessed => "repository_accessed",
            AuditAction::PatternAccessed => "pattern_accessed",
            AuditAction::DataExported => "data_exported",
            AuditAction::DataDeleted => "data_deleted",
            AuditAction::UserCreated => "user_created",
            AuditAction::UserModified => "user_modified",
            AuditAction::UserDeleted => "user_deleted",
            AuditAction::ConfigurationChanged => "configuration_changed",
            AuditAction::SecurityPolicyUpdated => "security_policy_updated",
            AuditAction::ServiceStarted => "service_started",
            AuditAction::ServiceStopped => "service_stopped",
            AuditAction::DatabaseMigration => "database_migration",
            AuditAction::SecurityIncident => "security_incident",

            // Security Intelligence events
            AuditAction::SecurityScanStarted => "security_scan_started",
            AuditAction::SecurityScanCompleted => "security_scan_completed",
            AuditAction::VulnerabilityDetected => "vulnerability_detected",
            AuditAction::SecretFound => "secret_found",
            AuditAction::ComplianceViolationDetected => "compliance_violation_detected",
            AuditAction::ThreatModelGenerated => "threat_model_generated",
            AuditAction::SecurityAssessmentCompleted => "security_assessment_completed",
            AuditAction::ThreatIntelligenceUpdated => "threat_intelligence_updated",
            AuditAction::SecurityPolicyViolation => "security_policy_violation",
            AuditAction::FalsePositiveReported => "false_positive_reported",
            AuditAction::SecurityReportGenerated => "security_report_generated",
        }
    }
}

pub struct AuditLogger {
    spanner_pool: Option<Arc<Pool<SpannerConnectionManager>>>,
}

impl AuditLogger {
    pub fn new(spanner_pool: Option<Arc<Pool<SpannerConnectionManager>>>) -> Self {
        Self { spanner_pool }
    }

    pub async fn log_event(&self, event: AuditEvent) -> Result<()> {
        // Always log to structured logging
        self.log_to_tracing(&event);

        // Store in database if available
        if let Some(spanner_pool) = &self.spanner_pool {
            if let Err(e) = self.store_in_database(spanner_pool, &event).await {
                tracing::error!("Failed to store audit event in database: {:?}", e);
            }
        }

        // Check for security incidents
        if self.is_security_incident(&event) {
            self.handle_security_incident(&event).await?;
        }

        Ok(())
    }

    fn log_to_tracing(&self, event: &AuditEvent) {
        let metadata = event
            .metadata
            .as_ref()
            .and_then(|m| serde_json::to_string(m).ok())
            .unwrap_or_else(|| "{}".to_string());

        match event.severity {
            AuditSeverity::Info => {
                tracing::info!(
                    audit_event = true,
                    log_id = %event.log_id,
                    user_id = ?event.user_id,
                    action = %event.action,
                    resource_type = ?event.resource_type,
                    resource_id = ?event.resource_id,
                    outcome = ?event.outcome,
                    ip_address = ?event.ip_address,
                    metadata = %metadata,
                    "Audit event logged"
                );
            }
            AuditSeverity::Warning => {
                tracing::warn!(
                    audit_event = true,
                    log_id = %event.log_id,
                    user_id = ?event.user_id,
                    action = %event.action,
                    resource_type = ?event.resource_type,
                    resource_id = ?event.resource_id,
                    outcome = ?event.outcome,
                    ip_address = ?event.ip_address,
                    metadata = %metadata,
                    "Audit warning logged"
                );
            }
            AuditSeverity::Error => {
                tracing::error!(
                    audit_event = true,
                    log_id = %event.log_id,
                    user_id = ?event.user_id,
                    action = %event.action,
                    resource_type = ?event.resource_type,
                    resource_id = ?event.resource_id,
                    outcome = ?event.outcome,
                    ip_address = ?event.ip_address,
                    metadata = %metadata,
                    "Audit error logged"
                );
            }
            AuditSeverity::Critical => {
                tracing::error!(
                    audit_event = true,
                    log_id = %event.log_id,
                    user_id = ?event.user_id,
                    action = %event.action,
                    resource_type = ?event.resource_type,
                    resource_id = ?event.resource_id,
                    outcome = ?event.outcome,
                    ip_address = ?event.ip_address,
                    metadata = %metadata,
                    "CRITICAL audit event logged"
                );
            }
        }
    }

    async fn store_in_database(
        &self,
        spanner_pool: &Pool<SpannerConnectionManager>,
        event: &AuditEvent,
    ) -> Result<()> {
        let spanner = spanner_pool.get().await.map_err(|e| -> anyhow::Error {
            anyhow::Error::msg(anyhow::__private::format!(
                "Failed to get spanner connection for audit log: {}",
                format!("{:?}", e)
            ))
        })?;


        let metadata_json = event
            .metadata
            .as_ref()
            .map(|m| serde_json::to_string(m).unwrap_or_else(|_| "{}".to_string()))
            .unwrap_or_else(|| "{}".to_string());

        // Use direct SQL statement approach instead of mutations for better compatibility
        // TODO: Implement mutation-based approach when prost version conflicts are resolved

        let event_clone = event.clone();
        let metadata_json_clone = metadata_json.clone();
        let (_, _) = spanner
            .read_write_transaction(|tx| {
                let event = event_clone.clone();
                let metadata_json = metadata_json_clone.clone();
                Box::pin(async move {
                    use google_cloud_spanner::statement::Statement;
                    let mut statement = Statement::new(
                        "INSERT INTO audit_logs (log_id, user_id, organization_id, action, resource_type, resource_id, ip_address, user_agent, created_at, metadata)
                         VALUES (@log_id, @user_id, @organization_id, @action, @resource_type, @resource_id, @ip_address, @user_agent, PENDING_COMMIT_TIMESTAMP(), @metadata)"
                    );
                    statement.add_param("log_id", &event.log_id);
                    statement.add_param("user_id", &event.user_id);
                    statement.add_param("organization_id", &event.organization_id);
                    statement.add_param("action", &event.action);
                    statement.add_param("resource_type", &event.resource_type);
                    statement.add_param("resource_id", &event.resource_id);
                    statement.add_param("ip_address", &event.ip_address);
                    statement.add_param("user_agent", &event.user_agent);
                    statement.add_param("metadata", &metadata_json);

                    tx.update(statement).await?;
                    Ok::<(), google_cloud_spanner::client::Error>(())
                })
            })
            .await
            .context("Failed to commit audit log event")?;

        tracing::debug!("Stored audit event in database: {}", event.log_id);
        Ok(())
    }

    fn is_security_incident(&self, event: &AuditEvent) -> bool {
        matches!(event.severity, AuditSeverity::Critical)
            || matches!(
                event.action.as_str(),
                "login_failure"
                    | "access_denied"
                    | "security_incident"
                    | "vulnerability_detected"
                    | "secret_found"
                    | "compliance_violation_detected"
                    | "security_policy_violation"
            )
            || (matches!(event.outcome, AuditOutcome::Failure)
                && matches!(
                    event.action.as_str(),
                    "api_key_used" | "token_generated" | "security_scan_started"
                ))
    }

    async fn handle_security_incident(&self, event: &AuditEvent) -> Result<()> {
        // Log security incident
        tracing::error!(
            security_incident = true,
            log_id = %event.log_id,
            user_id = ?event.user_id,
            action = %event.action,
            ip_address = ?event.ip_address,
            "Security incident detected"
        );

        // TODO: In production, integrate with security monitoring systems
        // - Send alerts to security team
        // - Trigger automated response (e.g., rate limiting, account lockout)
        // - Create incident tickets
        // - Notify compliance systems

        Ok(())
    }
}

// Builder pattern for creating audit events
pub struct AuditEventBuilder {
    event: AuditEvent,
}

impl AuditEventBuilder {
    pub fn new(action: AuditAction) -> Self {
        Self {
            event: AuditEvent {
                log_id: Uuid::new_v4().to_string(),
                user_id: None,
                organization_id: None,
                action: action.as_str().to_string(),
                resource_type: None,
                resource_id: None,
                ip_address: None,
                user_agent: None,
                request_id: None,
                session_id: None,
                outcome: AuditOutcome::Success,
                severity: AuditSeverity::Info,
                metadata: None,
                created_at: Utc::now(),
            },
        }
    }

    pub fn user_id(mut self, user_id: impl Into<String>) -> Self {
        self.event.user_id = Some(user_id.into());
        self
    }

    pub fn organization_id(mut self, org_id: impl Into<String>) -> Self {
        self.event.organization_id = Some(org_id.into());
        self
    }

    pub fn resource(
        mut self,
        resource_type: impl Into<String>,
        resource_id: impl Into<String>,
    ) -> Self {
        self.event.resource_type = Some(resource_type.into());
        self.event.resource_id = Some(resource_id.into());
        self
    }

    pub fn ip_address(mut self, ip: impl Into<String>) -> Self {
        self.event.ip_address = Some(ip.into());
        self
    }

    pub fn user_agent(mut self, ua: impl Into<String>) -> Self {
        self.event.user_agent = Some(ua.into());
        self
    }

    pub fn request_id(mut self, req_id: impl Into<String>) -> Self {
        self.event.request_id = Some(req_id.into());
        self
    }

    pub fn outcome(mut self, outcome: AuditOutcome) -> Self {
        self.event.outcome = outcome;
        self
    }

    pub fn severity(mut self, severity: AuditSeverity) -> Self {
        self.event.severity = severity;
        self
    }

    pub fn metadata(mut self, metadata: serde_json::Value) -> Self {
        self.event.metadata = Some(metadata);
        self
    }

    pub fn build(self) -> AuditEvent {
        self.event
    }
}
