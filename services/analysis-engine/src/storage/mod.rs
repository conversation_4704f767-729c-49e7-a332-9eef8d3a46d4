pub mod gcp_clients;
pub mod spanner;
pub mod storage;
pub mod pubsub;
pub mod redis_client;
pub mod cache;
pub mod connection_pool;

// Re-export the wrapper types with clear names
pub use spanner::SpannerOperations;
pub use storage::StorageOperations;
pub use pubsub::PubSubOperations;
pub use redis_client::RedisClient;
pub use cache::CacheManager;

// Re-export the actual GCP clients from gcp_clients module
