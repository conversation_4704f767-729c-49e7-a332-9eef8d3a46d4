use anyhow::{Context, Result};
use crate::storage::redis_client::RedisClient;
use crate::models::FileAnalysis;
use std::sync::Arc;
use serde::{Serialize, Deserialize};

#[derive(Serialize, Deserialize)]
pub struct CachedAnalysis {
    pub commit_hash: String,
    pub analyses: Vec<FileAnalysis>,
    pub cached_at: chrono::DateTime<chrono::Utc>,
}

/// Cache key prefixes for different types of data
const ANALYSIS_CACHE_PREFIX: &str = "analysis:";
const PATTERN_CACHE_PREFIX: &str = "patterns:";
const EMBEDDING_CACHE_PREFIX: &str = "embeddings:";

/// Default TTL values in seconds
const ANALYSIS_CACHE_TTL: u64 = 3600; // 1 hour
const PATTERN_CACHE_TTL: u64 = 7200; // 2 hours
const EMBEDDING_CACHE_TTL: u64 = 86400; // 24 hours
#[allow(dead_code)]
const METADATA_CACHE_TTL: u64 = 1800; // 30 minutes
#[allow(dead_code)]
const POPULAR_CACHE_TTL: u64 = 43200; // 12 hours

/// Cache tier levels for hierarchical storage
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub enum CacheTier {
    Hot,    // Frequently accessed, in memory
    Warm,   // Moderately accessed, Redis
    Cold,   // Rarely accessed, persistent storage
}

/// Cache entry metadata for intelligent management
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheEntryMetadata {
    pub key: String,
    pub size_bytes: u64,
    pub access_count: u64,
    pub last_access: chrono::DateTime<chrono::Utc>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub hit_rate: f64,
    pub tier: CacheTier,
    pub popularity_score: f64,
}

impl CacheEntryMetadata {
    pub fn new(key: String, size_bytes: u64) -> Self {
        let now = chrono::Utc::now();
        Self {
            key,
            size_bytes,
            access_count: 1,
            last_access: now,
            created_at: now,
            hit_rate: 0.0,
            tier: CacheTier::Hot,
            popularity_score: 1.0,
        }
    }

    pub fn update_access(&mut self) {
        self.access_count += 1;
        self.last_access = chrono::Utc::now();
        self.update_popularity_score();
    }

    fn update_popularity_score(&mut self) {
        let age_hours = (chrono::Utc::now() - self.created_at).num_hours() as f64;
        let recency_factor = (chrono::Utc::now() - self.last_access).num_hours() as f64;
        
        // Calculate popularity based on access frequency and recency
        self.popularity_score = (self.access_count as f64 / (age_hours + 1.0)) 
            * (1.0 / (recency_factor + 1.0));
    }

    pub fn should_promote(&self) -> bool {
        self.popularity_score > 5.0 && self.tier != CacheTier::Hot
    }

    pub fn should_demote(&self) -> bool {
        self.popularity_score < 0.5 && self.tier != CacheTier::Cold
    }
}

pub struct CacheManager {
    redis_client: Option<Arc<RedisClient>>,
    /// In-memory cache for hot data
    hot_cache: Arc<tokio::sync::RwLock<std::collections::HashMap<String, (String, CacheEntryMetadata)>>>,
    /// Cache metadata for intelligent management
    metadata_store: Arc<tokio::sync::RwLock<std::collections::HashMap<String, CacheEntryMetadata>>>,
    /// Cache warming configuration
    warming_config: CacheWarmingConfig,
    /// Cache statistics
    stats: Arc<tokio::sync::RwLock<CacheStats>>,
}

/// Configuration for cache warming
#[derive(Debug, Clone)]
pub struct CacheWarmingConfig {
    /// Enable predictive cache warming
    pub enable_predictive_warming: bool,
    /// Maximum size for hot cache in bytes
    pub hot_cache_max_size: u64,
    /// Warm up popular items on startup
    pub warm_up_on_startup: bool,
    /// Warming interval in seconds
    pub warming_interval: u64,
    /// Number of top items to keep hot
    pub hot_items_count: usize,
}

impl Default for CacheWarmingConfig {
    fn default() -> Self {
        Self {
            enable_predictive_warming: true,
            hot_cache_max_size: 100 * 1024 * 1024, // 100MB
            warm_up_on_startup: true,
            warming_interval: 300, // 5 minutes
            hot_items_count: 1000,
        }
    }
}

/// Cache statistics
#[derive(Debug, Default, Clone)]
pub struct CacheStats {
    pub hit_count: u64,
    pub miss_count: u64,
    pub eviction_count: u64,
    pub promotion_count: u64,
    pub demotion_count: u64,
    pub warming_count: u64,
    pub hot_cache_size: u64,
    pub total_entries: u64,
    pub avg_access_time_ms: f64,
}

impl CacheStats {
    pub fn hit_rate(&self) -> f64 {
        let total = self.hit_count + self.miss_count;
        if total > 0 {
            self.hit_count as f64 / total as f64
        } else {
            0.0
        }
    }

    pub fn update_hit(&mut self) {
        self.hit_count += 1;
    }

    pub fn update_miss(&mut self) {
        self.miss_count += 1;
    }

    pub fn update_eviction(&mut self) {
        self.eviction_count += 1;
    }

    pub fn update_promotion(&mut self) {
        self.promotion_count += 1;
    }

    pub fn update_demotion(&mut self) {
        self.demotion_count += 1;
    }

    pub fn update_warming(&mut self) {
        self.warming_count += 1;
    }
}

impl CacheManager {
    pub fn new(redis_client: Option<Arc<RedisClient>>) -> Self {
        Self::new_with_config(redis_client, CacheWarmingConfig::default())
    }

    pub fn new_with_config(redis_client: Option<Arc<RedisClient>>, config: CacheWarmingConfig) -> Self {
        let cache_manager = Self {
            redis_client,
            hot_cache: Arc::new(tokio::sync::RwLock::new(std::collections::HashMap::new())),
            metadata_store: Arc::new(tokio::sync::RwLock::new(std::collections::HashMap::new())),
            warming_config: config.clone(),
            stats: Arc::new(tokio::sync::RwLock::new(CacheStats::default())),
        };

        // Start cache warming if enabled
        if config.enable_predictive_warming {
            cache_manager.start_cache_warming();
        }

        cache_manager
    }

    pub fn new_without_redis() -> Self {
        Self::new(None)
    }

    /// Start background cache warming process
    fn start_cache_warming(&self) {
        let metadata_store = self.metadata_store.clone();
        let hot_cache = self.hot_cache.clone();
        let redis_client = self.redis_client.clone();
        let stats = self.stats.clone();
        let config = self.warming_config.clone();

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(
                std::time::Duration::from_secs(config.warming_interval)
            );

            loop {
                interval.tick().await;
                
                if let Err(e) = Self::perform_cache_warming(
                    &metadata_store,
                    &hot_cache,
                    &redis_client,
                    &stats,
                    &config,
                ).await {
                    tracing::warn!("Cache warming failed: {}", e);
                }
            }
        });
    }

    /// Perform cache warming operation
    async fn perform_cache_warming(
        metadata_store: &Arc<tokio::sync::RwLock<std::collections::HashMap<String, CacheEntryMetadata>>>,
        hot_cache: &Arc<tokio::sync::RwLock<std::collections::HashMap<String, (String, CacheEntryMetadata)>>>,
        redis_client: &Option<Arc<RedisClient>>,
        stats: &Arc<tokio::sync::RwLock<CacheStats>>,
        config: &CacheWarmingConfig,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        tracing::debug!("Starting cache warming cycle");

        // Get metadata and identify hot items
        let metadata = metadata_store.read().await;
        let mut entries: Vec<_> = metadata.values().cloned().collect();
        
        // Sort by popularity score
        entries.sort_by(|a, b| b.popularity_score.partial_cmp(&a.popularity_score).unwrap_or(std::cmp::Ordering::Equal));

        // Warm up top items
        let mut warmed_count = 0;
        for entry in entries.iter().take(config.hot_items_count) {
            if entry.tier != CacheTier::Hot {
                // Try to load from Redis and promote to hot cache
                if let Some(redis) = redis_client {
                    let key = &entry.key;
                    if let Ok(Some(data)) = redis.get_cached_analysis(key).await {
                        let mut hot_cache_guard = hot_cache.write().await;
                        let mut metadata_guard = metadata_store.write().await;
                        
                        // Check if hot cache has space
                        let current_size: u64 = hot_cache_guard.values()
                            .map(|(_, meta)| meta.size_bytes)
                            .sum();
                        
                        if current_size + entry.size_bytes <= config.hot_cache_max_size {
                            // Add to hot cache
                            hot_cache_guard.insert(key.clone(), (data, entry.clone()));
                            
                            // Update metadata
                            if let Some(meta) = metadata_guard.get_mut(key) {
                                meta.tier = CacheTier::Hot;
                            }
                            
                            warmed_count += 1;
                            
                            // Update stats
                            let mut stats_guard = stats.write().await;
                            stats_guard.update_warming();
                            stats_guard.update_promotion();
                        }
                    }
                }
            }
        }

        drop(metadata);
        
        // Perform cache eviction if needed
        Self::perform_cache_eviction(hot_cache, metadata_store, stats, config).await?;

        tracing::debug!("Cache warming completed: {} items warmed", warmed_count);
        Ok(())
    }

    /// Perform cache eviction based on LRU and popularity
    async fn perform_cache_eviction(
        hot_cache: &Arc<tokio::sync::RwLock<std::collections::HashMap<String, (String, CacheEntryMetadata)>>>,
        metadata_store: &Arc<tokio::sync::RwLock<std::collections::HashMap<String, CacheEntryMetadata>>>,
        stats: &Arc<tokio::sync::RwLock<CacheStats>>,
        config: &CacheWarmingConfig,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut hot_cache_guard = hot_cache.write().await;
        let mut metadata_guard = metadata_store.write().await;

        // Check if eviction is needed
        let current_size: u64 = hot_cache_guard.values()
            .map(|(_, meta)| meta.size_bytes)
            .sum();

        if current_size > config.hot_cache_max_size {
            // Collect items sorted by popularity (lowest first)
            let mut items: Vec<_> = hot_cache_guard.iter()
                .map(|(k, (_, meta))| (k.clone(), meta.popularity_score, meta.size_bytes))
                .collect();
            
            items.sort_by(|a, b| a.1.partial_cmp(&b.1).unwrap_or(std::cmp::Ordering::Equal));

            // Evict items until we're under the limit
            let mut evicted_size = 0u64;
            let mut evicted_count = 0;
            
            for (key, _, size) in items {
                if current_size - evicted_size <= config.hot_cache_max_size * 8 / 10 {
                    break; // Stop at 80% capacity
                }
                
                hot_cache_guard.remove(&key);
                evicted_size += size;
                evicted_count += 1;
                
                // Update metadata tier
                if let Some(meta) = metadata_guard.get_mut(&key) {
                    meta.tier = CacheTier::Warm;
                }
            }

            // Update stats
            let mut stats_guard = stats.write().await;
            stats_guard.eviction_count += evicted_count;
            stats_guard.hot_cache_size = current_size - evicted_size;

            tracing::debug!("Evicted {} items ({} bytes) from hot cache", evicted_count, evicted_size);
        }

        Ok(())
    }

    /// Get cache statistics
    pub async fn get_stats(&self) -> CacheStats {
        self.stats.read().await.clone()
    }

    /// Get current cache size
    pub async fn get_cache_size(&self) -> u64 {
        let hot_cache = self.hot_cache.read().await;
        hot_cache.values().map(|(_, meta)| meta.size_bytes).sum()
    }

    /// Pre-warm cache with expected analysis requests
    pub async fn warm_cache_for_repositories(&self, repository_urls: &[String]) -> Result<()> {
        tracing::info!("Pre-warming cache for {} repositories", repository_urls.len());
        
        let mut warmed_count = 0;
        
        for repo_url in repository_urls {
            // Create cache key pattern for this repository
            let cache_key = format!("{}{}:main", ANALYSIS_CACHE_PREFIX, repo_url);
            
            // Try to load from Redis if available
            if let Some(redis) = &self.redis_client {
                if let Ok(Some(data)) = redis.get_cached_analysis(&cache_key).await {
                    // Add to hot cache
                    let size_bytes = data.len() as u64;
                    let metadata = CacheEntryMetadata::new(cache_key.clone(), size_bytes);
                    
                    let mut hot_cache = self.hot_cache.write().await;
                    let mut metadata_store = self.metadata_store.write().await;
                    
                    hot_cache.insert(cache_key.clone(), (data, metadata.clone()));
                    metadata_store.insert(cache_key, metadata);
                    
                    warmed_count += 1;
                    
                    // Update stats
                    let mut stats = self.stats.write().await;
                    stats.update_warming();
                }
            }
        }
        
        tracing::info!("Pre-warmed {} repositories in cache", warmed_count);
        Ok(())
    }
    
    /// Get cached analysis results with intelligent tiered caching
    pub async fn get_analysis(&self, analysis_id: &str) -> Result<Option<Vec<FileAnalysis>>> {
        let start_time = std::time::Instant::now();
        let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
        
        // First check hot cache (fastest access)
        {
            let hot_cache = self.hot_cache.read().await;
            if let Some((data, _)) = hot_cache.get(&key) {
                // Update access metadata
                let mut metadata_store = self.metadata_store.write().await;
                if let Some(metadata) = metadata_store.get_mut(&key) {
                    metadata.update_access();
                    
                    // Check if item should be promoted to higher tier
                    if metadata.should_promote() {
                        // Already in hot tier, just update stats
                        let mut stats = self.stats.write().await;
                        stats.update_hit();
                    }
                }
                
                // Update stats
                let mut stats = self.stats.write().await;
                stats.update_hit();
                stats.avg_access_time_ms = (stats.avg_access_time_ms + start_time.elapsed().as_millis() as f64) / 2.0;
                
                tracing::debug!("Cache hit in HOT tier for key: {}", key);
                
                // Parse and return data
                if let Ok(cached_analysis) = serde_json::from_str::<CachedAnalysis>(data) {
                    return Ok(Some(cached_analysis.analyses));
                } else if let Ok(analyses) = serde_json::from_str::<Vec<FileAnalysis>>(data) {
                    return Ok(Some(analyses));
                }
            }
        }
        
        // Check warm cache (Redis) if available
        if let Some(redis) = &self.redis_client {
            match redis.get_cached_analysis(&key).await {
                Ok(Some(data)) => {
                    // Parse data
                    let analyses = if let Ok(cached_analysis) = serde_json::from_str::<CachedAnalysis>(&data) {
                        cached_analysis.analyses
                    } else {
                        serde_json::from_str(&data)
                            .context("Failed to deserialize cached analysis")?
                    };
                    
                    tracing::debug!("Cache hit in WARM tier for key: {}", key);
                    
                    // Promote to hot cache if there's space and item is popular
                    let size_bytes = data.len() as u64;
                    let current_size = self.get_cache_size().await;
                    
                    if current_size + size_bytes <= self.warming_config.hot_cache_max_size {
                        let mut hot_cache = self.hot_cache.write().await;
                        let mut metadata_store = self.metadata_store.write().await;
                        
                        let mut metadata = metadata_store.get(&key).cloned()
                            .unwrap_or_else(|| CacheEntryMetadata::new(key.clone(), size_bytes));
                        
                        metadata.update_access();
                        
                        // Promote to hot tier if popular enough
                        if metadata.should_promote() {
                            metadata.tier = CacheTier::Hot;
                            hot_cache.insert(key.clone(), (data, metadata.clone()));
                            metadata_store.insert(key.clone(), metadata);
                            
                            // Update stats
                            let mut stats = self.stats.write().await;
                            stats.update_promotion();
                            
                            tracing::debug!("Promoted cache entry to HOT tier: {}", key);
                        }
                    }
                    
                    // Update cache stats
                    let mut stats = self.stats.write().await;
                    stats.update_hit();
                    stats.avg_access_time_ms = (stats.avg_access_time_ms + start_time.elapsed().as_millis() as f64) / 2.0;
                    
                    return Ok(Some(analyses));
                }
                Ok(None) => {
                    // Update miss stats
                    let mut stats = self.stats.write().await;
                    stats.update_miss();
                }
                Err(e) => {
                    tracing::warn!("Failed to get cached analysis: {}", e);
                    let mut stats = self.stats.write().await;
                    stats.update_miss();
                }
            }
        } else {
            // Update miss stats
            let mut stats = self.stats.write().await;
            stats.update_miss();
        }
        
        tracing::debug!("Cache miss for key: {}", key);
        Ok(None)
    }

    /// Get cached analysis with commit hash validation
    pub async fn get_analysis_with_commit_check(&self, analysis_id: &str, current_commit_hash: &str) -> Result<Option<Vec<FileAnalysis>>> {
        if let Some(redis) = &self.redis_client {
            let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
            match redis.get_cached_analysis(&key).await {
                Ok(Some(data)) => {
                    if let Ok(cached_analysis) = serde_json::from_str::<CachedAnalysis>(&data) {
                        // Check if commit hash matches
                        if cached_analysis.commit_hash == current_commit_hash {
                            tracing::info!("Cache hit with matching commit hash for analysis: {}", analysis_id);
                            Ok(Some(cached_analysis.analyses))
                        } else {
                            tracing::info!("Cache hit but commit hash mismatch for analysis: {} (cached: {}, current: {})",
                                analysis_id, cached_analysis.commit_hash, current_commit_hash);
                            Ok(None) // Cache is stale
                        }
                    } else {
                        // Old format cache - consider it stale
                        tracing::info!("Cache hit but old format for analysis: {}, considering stale", analysis_id);
                        Ok(None)
                    }
                }
                Ok(None) => Ok(None),
                Err(e) => {
                    tracing::warn!("Failed to get cached analysis: {}", e);
                    Ok(None) // Graceful fallback
                }
            }
        } else {
            Ok(None)
        }
    }
    
    /// Set cached analysis results
    pub async fn set_analysis(&self, analysis_id: &str, analyses: &[FileAnalysis]) -> Result<()> {
        if let Some(redis) = &self.redis_client {
            let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
            let data = serde_json::to_string(analyses)
                .context("Failed to serialize analysis for caching")?;

            match redis.set_cached_analysis(&key, &data, ANALYSIS_CACHE_TTL).await {
                Ok(()) => Ok(()),
                Err(e) => {
                    tracing::warn!("Failed to cache analysis: {}", e);
                    Ok(()) // Graceful fallback - don't fail the operation
                }
            }
        } else {
            Ok(())
        }
    }

    /// Set cached analysis results with commit hash
    pub async fn set_analysis_with_commit(&self, analysis_id: &str, analyses: &[FileAnalysis], commit_hash: &str) -> Result<()> {
        if let Some(redis) = &self.redis_client {
            let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
            let cached_analysis = CachedAnalysis {
                commit_hash: commit_hash.to_string(),
                analyses: analyses.to_vec(),
                cached_at: chrono::Utc::now(),
            };
            let data = serde_json::to_string(&cached_analysis)
                .context("Failed to serialize analysis with commit hash for caching")?;

            match redis.set_cached_analysis(&key, &data, ANALYSIS_CACHE_TTL).await {
                Ok(()) => {
                    tracing::info!("Cached analysis {} with commit hash {}", analysis_id, commit_hash);
                    Ok(())
                },
                Err(e) => {
                    tracing::warn!("Failed to cache analysis: {}", e);
                    Ok(()) // Graceful fallback - don't fail the operation
                }
            }
        } else {
            Ok(())
        }
    }
    
    /// Get cached patterns for a file
    #[allow(dead_code)]
    pub async fn get_patterns(&self, file_hash: &str) -> Result<Option<Vec<String>>> {
        if let Some(redis) = &self.redis_client {
            let key = format!("{}{}", PATTERN_CACHE_PREFIX, file_hash);
            match redis.get_cached_analysis(&key).await {
                Ok(Some(data)) => {
                    let patterns: Vec<String> = serde_json::from_str(&data)
                        .context("Failed to deserialize cached patterns")?;
                    Ok(Some(patterns))
                }
                Ok(None) => Ok(None),
                Err(e) => {
                    tracing::warn!("Failed to get cached patterns: {}", e);
                    Ok(None)
                }
            }
        } else {
            Ok(None)
        }
    }
    
    /// Set cached patterns for a file
    pub async fn set_patterns(&self, file_hash: &str, patterns: &[String]) -> Result<()> {
        if let Some(redis) = &self.redis_client {
            let key = format!("{}{}", PATTERN_CACHE_PREFIX, file_hash);
            let data = serde_json::to_string(patterns)
                .context("Failed to serialize patterns for caching")?;
            
            match redis.set_cached_analysis(&key, &data, PATTERN_CACHE_TTL).await {
                Ok(()) => Ok(()),
                Err(e) => {
                    tracing::warn!("Failed to cache patterns: {}", e);
                    Ok(())
                }
            }
        } else {
            Ok(())
        }
    }
    
    /// Get cached embeddings
    #[allow(dead_code)]
    pub async fn get_embeddings(&self, chunk_id: &str) -> Result<Option<Vec<f32>>> {
        if let Some(redis) = &self.redis_client {
            let key = format!("{}{}", EMBEDDING_CACHE_PREFIX, chunk_id);
            match redis.get_cached_analysis(&key).await {
                Ok(Some(data)) => {
                    let embeddings: Vec<f32> = serde_json::from_str(&data)
                        .context("Failed to deserialize cached embeddings")?;
                    Ok(Some(embeddings))
                }
                Ok(None) => Ok(None),
                Err(e) => {
                    tracing::warn!("Failed to get cached embeddings: {}", e);
                    Ok(None)
                }
            }
        } else {
            Ok(None)
        }
    }
    
    /// Set cached embeddings
    pub async fn set_embeddings(&self, chunk_id: &str, embeddings: &[f32]) -> Result<()> {
        if let Some(redis) = &self.redis_client {
            let key = format!("{}{}", EMBEDDING_CACHE_PREFIX, chunk_id);
            let data = serde_json::to_string(embeddings)
                .context("Failed to serialize embeddings for caching")?;
            
            match redis.set_cached_analysis(&key, &data, EMBEDDING_CACHE_TTL).await {
                Ok(()) => Ok(()),
                Err(e) => {
                    tracing::warn!("Failed to cache embeddings: {}", e);
                    Ok(())
                }
            }
        } else {
            Ok(())
        }
    }
    
    /// Check if cache is available
    #[allow(dead_code)]
    pub fn is_available(&self) -> bool {
        self.redis_client.is_some()
    }
    
    /// Clear all cache for an analysis
    #[allow(dead_code)]
    pub async fn clear_analysis_cache(&self, analysis_id: &str) -> Result<()> {
        if let Some(redis) = &self.redis_client {
            let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
            // Redis doesn't have a direct delete method in our client, 
            // so we'll set with 1 second TTL to effectively delete
            match redis.set_cached_analysis(&key, "", 1).await {
                Ok(()) => Ok(()),
                Err(e) => {
                    tracing::warn!("Failed to clear analysis cache: {}", e);
                    Ok(())
                }
            }
        } else {
            Ok(())
        }
    }
    
    /// Prepare cache for concurrent load with pre-warming and capacity planning
    pub async fn prepare_for_concurrent_load(&self, expected_concurrent_analyses: usize) -> Result<()> {
        tracing::info!("Preparing cache for {} concurrent analyses", expected_concurrent_analyses);
        
        // Adjust hot cache size based on expected load
        let target_hot_cache_size = (expected_concurrent_analyses as u64 * 2) * 1024 * 1024; // 2MB per analysis
        
        // Update warming config for high concurrency
        let mut stats = self.stats.write().await;
        stats.total_entries = expected_concurrent_analyses as u64;
        drop(stats);
        
        // Pre-warm popular analysis patterns if we have redis
        if let Some(redis) = &self.redis_client {
            tracing::info!("Pre-warming cache with popular analysis patterns");
            
            // Try to load frequently accessed patterns into hot cache
            let popular_patterns = vec![
                "analysis:popular:rust",
                "analysis:popular:python", 
                "analysis:popular:javascript",
                "analysis:popular:typescript",
                "analysis:popular:go",
            ];
            
            for pattern in popular_patterns {
                if let Ok(Some(data)) = redis.get_cached_analysis(pattern).await {
                    let size_bytes = data.len() as u64;
                    if size_bytes < target_hot_cache_size / 10 { // Don't take more than 10% for patterns
                        let metadata = CacheEntryMetadata::new(pattern.to_string(), size_bytes);
                        let mut hot_cache = self.hot_cache.write().await;
                        let mut metadata_store = self.metadata_store.write().await;
                        
                        hot_cache.insert(pattern.to_string(), (data, metadata.clone()));
                        metadata_store.insert(pattern.to_string(), metadata);
                    }
                }
            }
        }
        
        tracing::info!("Cache prepared for concurrent load with target hot cache size: {} MB", 
                      target_hot_cache_size / 1024 / 1024);
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_cache_manager_without_redis() -> Result<()> {
        let cache = CacheManager::new(None);
        
        // Should gracefully handle no Redis
        assert!(!cache.is_available());
        
        let result = cache.get_analysis("test-id").await?;
        assert!(result.is_none());
        
        // Set should not fail
        let analyses = vec![];
        cache.set_analysis("test-id", &analyses).await?;
        Ok(())
    }
}
