use anyhow::{Context, Result};
use redis::{AsyncCommands, Client};
use std::env;

pub struct RedisClient {
    client: Client,
}

impl RedisClient {
    pub async fn new() -> Result<Self> {
        let redis_url = env::var("REDIS_URL")
            .unwrap_or_else(|_| "redis://127.0.0.1:6379".to_string());
        
        tracing::info!("Attempting to connect to Red<PERSON> at: {}", redis_url);
        
        let client = Client::open(redis_url.clone())
            .context("Failed to create Redis client")?;
        
        // Test connection
        match client.get_multiplexed_async_connection().await {
            Ok(mut conn) => {
                // Test with PING command
                match redis::cmd("PING")
                    .query_async::<_, String>(&mut conn)
                    .await
                {
                    Ok(response) => {
                        tracing::info!("Redis connection successful, PING response: {}", response);
                        Ok(Self { client })
                    }
                    Err(e) => {
                        tracing::error!("Redis PING failed: {}", e);
                        Err(anyhow::anyhow!("Redis ping failed: {}", e))
                    }
                }
            }
            Err(e) => {
                tracing::error!("Failed to connect to Redis at {}: {}", redis_url, e);
                Err(anyhow::anyhow!("Failed to connect to Redis: {}", e))
            }
        }
    }
    
    /// Check and update rate limit for a given key
    /// Returns (allowed, remaining_requests, reset_time_seconds)
    pub async fn check_rate_limit(
        &self,
        key: &str,
        limit: i64,
        window_seconds: u64,
    ) -> Result<(bool, i64, u64)> {
        let mut conn = self.client.get_multiplexed_async_connection().await
            .context("Failed to get Redis connection")?;
        
        let rate_limit_key = format!("rate_limit:{}", key);
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .context("Failed to get current timestamp")?
            .as_secs();
        
        // Use sliding window algorithm with Redis sorted sets
        let window_start = now - window_seconds;
        
        // Remove old entries using ZREMRANGEBYSCORE command
        let _: i64 = redis::cmd("ZREMRANGEBYSCORE")
            .arg(&rate_limit_key)
            .arg(0)
            .arg(window_start)
            .query_async(&mut conn)
            .await?;
        
        // Count current requests in window
        let count: i64 = conn.zcard(&rate_limit_key).await?;
        
        if count < limit {
            // Add current request
            let _: i64 = conn.zadd(&rate_limit_key, now, now).await?;
            
            // Set expiry
            let _: () = conn.expire(&rate_limit_key, window_seconds as i64).await?;
            
            let remaining = limit - count - 1;
            let reset_time = now + window_seconds;
            
            Ok((true, remaining, reset_time))
        } else {
            // Get oldest entry to determine reset time
            let oldest: Vec<(String, f64)> = conn.zrange_withscores(&rate_limit_key, 0, 0).await?;
            let reset_time = if let Some((_, score)) = oldest.first() {
                (*score as u64) + window_seconds
            } else {
                now + window_seconds
            };
            
            Ok((false, 0, reset_time))
        }
    }
    
    /// Get cached analysis result
    pub async fn get_cached_analysis(&self, key: &str) -> Result<Option<String>> {
        let mut conn = self.client.get_multiplexed_async_connection().await
            .context("Failed to get Redis connection")?;
        
        let result: Option<String> = conn.get(key).await
            .context("Failed to get cached value")?;
        
        Ok(result)
    }
    
    /// Set cached analysis result with TTL
    pub async fn set_cached_analysis(
        &self,
        key: &str,
        value: &str,
        ttl_seconds: u64,
    ) -> Result<()> {
        let mut conn = self.client.get_multiplexed_async_connection().await
            .context("Failed to get Redis connection")?;
        
        let _: () = conn.set_ex(key, value, ttl_seconds).await
            .context("Failed to set cached value")?;
        
        Ok(())
    }
    
    /// Check if Redis is healthy
    #[allow(dead_code)]
    pub async fn health_check(&self) -> Result<()> {
        let mut conn = self.client.get_multiplexed_async_connection().await
            .context("Failed to get Redis connection")?;

        let _: String = redis::cmd("PING")
            .query_async(&mut conn)
            .await
            .context("Redis health check failed")?;

        Ok(())
    }

    /// Advanced rate limiting with burst allowance and multiple time windows
    pub async fn check_rate_limit_advanced(
        &self,
        key: &str,
        limit: i64,
        window_seconds: u64,
        burst_limit: Option<i64>,
        burst_window_seconds: Option<u64>,
    ) -> Result<(bool, i64, u64, Option<String>)> {
        let mut conn = self.client.get_multiplexed_async_connection().await
            .context("Failed to get Redis connection")?;

        let _rate_limit_key = format!("rate_limit:{}", key);
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .context("Failed to get current timestamp")?
            .as_secs();

        // Check main rate limit
        let (allowed, remaining, reset_time) = self.check_rate_limit(key, limit, window_seconds).await?;

        if !allowed {
            return Ok((false, 0, reset_time, Some("Rate limit exceeded".to_string())));
        }

        // Check burst limit if specified
        if let (Some(burst_limit), Some(burst_window)) = (burst_limit, burst_window_seconds) {
            let burst_key = format!("burst:{}", key);
            let burst_window_start = now - burst_window;

            // Remove old burst entries
            let _: i64 = redis::cmd("ZREMRANGEBYSCORE")
                .arg(&burst_key)
                .arg(0)
                .arg(burst_window_start)
                .query_async(&mut conn)
                .await?;

            // Count burst requests
            let burst_count: i64 = conn.zcard(&burst_key).await?;

            if burst_count >= burst_limit {
                return Ok((false, 0, now + burst_window, Some("Burst limit exceeded".to_string())));
            }

            // Add to burst tracking
            let _: i64 = conn.zadd(&burst_key, now, now).await?;
            let _: () = conn.expire(&burst_key, burst_window as i64).await?;
        }

        Ok((true, remaining, reset_time, None))
    }

    /// Get rate limit statistics for monitoring
    pub async fn get_rate_limit_stats(&self, key: &str) -> Result<RateLimitStats> {
        let mut conn = self.client.get_multiplexed_async_connection().await
            .context("Failed to get Redis connection")?;

        let rate_limit_key = format!("rate_limit:{}", key);
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .context("Failed to get current timestamp")?
            .as_secs();

        // Get current count
        let current_count: i64 = conn.zcard(&rate_limit_key).await.unwrap_or(0);

        // Get requests in last minute, hour, day
        let minute_ago = now - 60;
        let hour_ago = now - 3600;
        let day_ago = now - 86400;

        let last_minute: i64 = conn.zcount(&rate_limit_key, minute_ago, now).await.unwrap_or(0);
        let last_hour: i64 = conn.zcount(&rate_limit_key, hour_ago, now).await.unwrap_or(0);
        let last_day: i64 = conn.zcount(&rate_limit_key, day_ago, now).await.unwrap_or(0);

        Ok(RateLimitStats {
            current_window_count: current_count,
            last_minute_count: last_minute,
            last_hour_count: last_hour,
            last_day_count: last_day,
            timestamp: now,
        })
    }

    /// Reset rate limit for a specific key (admin function)
    pub async fn reset_rate_limit(&self, key: &str) -> Result<()> {
        let mut conn = self.client.get_multiplexed_async_connection().await
            .context("Failed to get Redis connection")?;

        let rate_limit_key = format!("rate_limit:{}", key);
        let burst_key = format!("burst:{}", key);

        let _: () = conn.del(&[&rate_limit_key, &burst_key]).await
            .context("Failed to reset rate limit")?;

        tracing::info!("Rate limit reset for key: {}", key);
        Ok(())
    }
}

#[derive(Debug, Clone)]
pub struct RateLimitStats {
    pub current_window_count: i64,
    pub last_minute_count: i64,
    pub last_hour_count: i64,
    pub last_day_count: i64,
    pub timestamp: u64,
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    #[ignore] // Requires Redis to be running
    async fn test_rate_limiting() {
        let client = RedisClient::new().await.unwrap();
        
        // Test rate limiting
        let key = "test_user";
        let limit = 5;
        let window = 60; // 1 minute
        
        // First 5 requests should be allowed
        for i in 0..5 {
            let (allowed, remaining, _) = client.check_rate_limit(key, limit, window).await.unwrap();
            assert!(allowed, "Request {} should be allowed", i + 1);
            assert_eq!(remaining, limit - i - 1);
        }
        
        // 6th request should be blocked
        let (allowed, remaining, _) = client.check_rate_limit(key, limit, window).await.unwrap();
        assert!(!allowed, "6th request should be blocked");
        assert_eq!(remaining, 0);
    }
}