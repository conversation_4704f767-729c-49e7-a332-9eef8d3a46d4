use crate::models::{AnalysisResult, ProgressUpdate};
use crate::storage::gcp_clients;
use anyhow::{Context, Result};
use google_cloud_googleapis::pubsub::v1::PubsubMessage;
use google_cloud_pubsub::publisher::{Publisher, PublisherConfig};
use google_cloud_pubsub::topic::Topic;
use std::collections::HashMap;

pub struct PubSubOperations {
    #[allow(dead_code)]
    client: gcp_clients::PubSubClient,
    topics: HashMap<String, Topic>,
    publishers: HashMap<String, Publisher>,
    #[allow(dead_code)]
    project_id: String,
}

impl PubSubOperations {
    pub async fn new(client: gcp_clients::PubSubClient) -> Result<Self> {
        let project_id = std::env::var("GCP_PROJECT_ID")
            .context("GCP_PROJECT_ID environment variable not set")?;

        let mut topics = HashMap::new();
        let mut publishers = HashMap::new();

        // Create topics and publishers for each event type
        // Get topic names from environment variables with fallbacks
        let topic_names = vec![
            std::env::var("PUBSUB_TOPIC_EVENTS").unwrap_or_else(|_| "analysis-events".to_string()),
            std::env::var("PUBSUB_TOPIC_PROGRESS").unwrap_or_else(|_| "analysis-progress".to_string()),
            std::env::var("PUBSUB_TOPIC_PATTERNS").unwrap_or_else(|_| "pattern-detected".to_string()),
        ];

        for topic_name in topic_names {
            let topic_path = format!("projects/{}/topics/{}", project_id, topic_name);
            let topic = client.topic(&topic_path);
            
            // Configure publisher with batch settings for efficiency
            let publisher_config = PublisherConfig {
                bundle_size: 100,
                flush_interval: std::time::Duration::from_millis(100),
                ..Default::default()
            };
            
            let publisher = topic.new_publisher(Some(publisher_config));
            
            topics.insert(topic_name.to_string(), topic);
            publishers.insert(topic_name.to_string(), publisher);
        }

        Ok(Self {
            client,
            topics,
            publishers,
            project_id,
        })
    }

    pub async fn publish_analysis_event(&self, analysis: &AnalysisResult) -> Result<()> {
        let topic_name = std::env::var("PUBSUB_TOPIC_EVENTS")
            .unwrap_or_else(|_| "analysis-events".to_string());
        let publisher = self.publishers
            .get(&topic_name)
            .context("Analysis events publisher not found")?;

        let event_data = serde_json::json!({
            "event_type": "analysis.completed",
            "analysis_id": analysis.id,
            "repository_url": analysis.repository_url,
            "status": analysis.status,
            "timestamp": chrono::Utc::now().to_rfc3339(),
            "user_id": analysis.user_id,
            "metrics": {
                "file_count": analysis.file_count,
                "success_rate": analysis.success_rate,
                "duration_seconds": analysis.duration_seconds,
            },
        });

        let message = PubsubMessage {
            data: serde_json::to_vec(&event_data)?,
            attributes: HashMap::from([
                ("event_type".to_string(), "analysis.completed".to_string()),
                ("analysis_id".to_string(), analysis.id.clone()),
            ]),
            ..Default::default()
        };

        let awaiter = publisher.publish(message).await;
        awaiter.get().await.context("Failed to publish analysis event")?;
        
        tracing::debug!(
            "Published analysis event for {} with status {:?}",
            analysis.id,
            analysis.status
        );
        
        Ok(())
    }

    pub async fn publish_progress(&self, progress: &ProgressUpdate) -> Result<()> {
        let topic_name = std::env::var("PUBSUB_TOPIC_PROGRESS")
            .unwrap_or_else(|_| "analysis-progress".to_string());
        let publisher = self.publishers
            .get(&topic_name)
            .context("Progress publisher not found")?;

        let message = PubsubMessage {
            data: serde_json::to_vec(progress)?,
            attributes: HashMap::from([
                ("analysis_id".to_string(), progress.analysis_id.clone()),
                ("stage".to_string(), progress.stage.clone()),
                ("progress".to_string(), progress.progress.to_string()),
            ]),
            ..Default::default()
        };

        let awaiter = publisher.publish(message).await;
        awaiter.get().await.context("Failed to publish progress update")?;
        
        Ok(())
    }

    pub async fn publish_pattern_detected(&self, analysis_id: &str, pattern_type: &str, pattern_count: usize) -> Result<()> {
        let topic_name = std::env::var("PUBSUB_TOPIC_PATTERNS")
            .unwrap_or_else(|_| "pattern-detected".to_string());
        let publisher = self.publishers
            .get(&topic_name)
            .context("Pattern detected publisher not found")?;

        let event_data = serde_json::json!({
            "event_type": "pattern.detected",
            "analysis_id": analysis_id,
            "pattern_type": pattern_type,
            "pattern_count": pattern_count,
            "timestamp": chrono::Utc::now().to_rfc3339(),
        });

        let message = PubsubMessage {
            data: serde_json::to_vec(&event_data)?,
            attributes: HashMap::from([
                ("event_type".to_string(), "pattern.detected".to_string()),
                ("analysis_id".to_string(), analysis_id.to_string()),
                ("pattern_type".to_string(), pattern_type.to_string()),
            ]),
            ..Default::default()
        };

        let awaiter = publisher.publish(message).await;
        awaiter.get().await.context("Failed to publish pattern detected event")?;
        
        tracing::debug!(
            "Published pattern detected event for analysis {} with {} {} patterns",
            analysis_id,
            pattern_count,
            pattern_type
        );
        
        Ok(())
    }

    pub async fn health_check(&self) -> Result<()> {
        let mut warnings = Vec::new();
        let mut all_accessible = true;
        
        // Check if all topics exist and are accessible
        for (name, topic) in &self.topics {
            match topic.exists(None).await {
                Ok(true) => {
                    // Topic exists and we can check it
                    tracing::debug!("PubSub topic {} exists and is accessible", name);
                }
                Ok(false) => {
                    // Topic doesn't exist
                    tracing::error!("PubSub topic {} does not exist", name);
                    all_accessible = false;
                }
                Err(e) => {
                    let error_str = e.to_string();
                    // Check if it's a permission error
                    if error_str.contains("User not authorized") || 
                       error_str.contains("Permission") || 
                       error_str.contains("permission") ||
                       error_str.contains("403") {
                        // Permission issue - we can't verify existence but assume it's there
                        warnings.push(format!("Cannot verify topic {} due to missing pubsub.topics.get permission", name));
                        tracing::warn!("PubSub health check: Cannot verify topic {} - missing viewer permission. Consider granting roles/pubsub.viewer.", name);
                    } else {
                        // Some other error occurred
                        tracing::error!("Failed to check PubSub topic {}: {}", name, e);
                        all_accessible = false;
                    }
                }
            }
        }
        
        // Log warnings if any
        if !warnings.is_empty() {
            tracing::info!("PubSub health check completed with warnings: {}", warnings.join(", "));
        }
        
        // Only fail if we have actual errors (not just permission warnings)
        if all_accessible {
            Ok(())
        } else {
            Err(anyhow::anyhow!("PubSub health check failed: one or more topics are not accessible"))
        }
    }
}