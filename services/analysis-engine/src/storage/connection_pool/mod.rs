//! Connection pooling for Spanner and Redis
use async_trait::async_trait;
use bb8::{ManageConnection, Pool};
use google_cloud_spanner::client::Client;
use redis::aio::MultiplexedConnection as Connection;

use crate::config::{GcpSettings, RedisSettings};
use crate::storage::gcp_clients;
use crate::models::{AnalysisResult, ListAnalysesParams, RepositoryMetrics, DetectedPattern};
use anyhow::Result;
use std::collections::HashMap;

/// Manages Spanner connections for the connection pool
pub struct SpannerConnectionManager {
    config: GcpSettings,
}

impl SpannerConnectionManager {
    pub fn new(config: GcpSettings) -> Self {
        Self { config }
    }
}

#[async_trait]
impl ManageConnection for SpannerConnectionManager {
    type Connection = Client;
    type Error = anyhow::Error;

    async fn connect(&self) -> Result<Self::Connection, Self::Error> {
        gcp_clients::create_spanner_client(&self.config).await
    }

    async fn is_valid(&self, conn: &mut Self::Connection) -> Result<(), Self::Error> {
        // Spanner client doesn't have a simple ping, so we'll assume it's valid
        // if we can get a session. This is a bit heavy, so we'll do it infrequently.
        let _ = conn.batch_read_only_transaction().await?;
        Ok(())
    }

    fn has_broken(&self, _conn: &mut Self::Connection) -> bool {
        false
    }
}

/// Manages Redis connections for the connection pool
pub struct RedisConnectionManager {
    config: RedisSettings,
}

impl RedisConnectionManager {
    pub fn new(config: RedisSettings) -> Self {
        Self { config }
    }
}

#[async_trait]
impl ManageConnection for RedisConnectionManager {
    type Connection = Connection;
    type Error = redis::RedisError;

    async fn connect(&self) -> Result<Self::Connection, Self::Error> {
        let client = redis::Client::open(self.config.url.as_str())?;
        client.get_multiplexed_async_connection().await
    }

    async fn is_valid(&self, conn: &mut Self::Connection) -> Result<(), Self::Error> {
        let mut conn = conn.clone();
        redis::cmd("PING").query_async::<_, ()>(&mut conn).await?;
        Ok(())
    }

    fn has_broken(&self, _conn: &mut Self::Connection) -> bool {
        false
    }
}

pub type SpannerPool = Pool<SpannerConnectionManager>;
pub type RedisPool = Pool<RedisConnectionManager>;

/// Extension trait to add SpannerOperations methods to Client
#[async_trait]
pub trait SpannerClientExt {
    async fn get_analysis(&self, analysis_id: &str) -> Result<Option<AnalysisResult>>;
    async fn get_analyses(&self, params: &ListAnalysesParams) -> Result<Vec<AnalysisResult>>;
    async fn store_analysis(&self, analysis: &AnalysisResult) -> Result<()>;
    async fn get_analysis_metrics(&self, analysis_id: &str) -> Result<Option<RepositoryMetrics>>;
    async fn get_analysis_patterns(&self, analysis_id: &str) -> Result<Option<Vec<DetectedPattern>>>;
}

#[async_trait]
impl SpannerClientExt for Client {
    async fn get_analysis(&self, analysis_id: &str) -> Result<Option<AnalysisResult>> {
        use google_cloud_spanner::statement::Statement;

        let mut tx = self.read_only_transaction().await?;
        let mut statement = Statement::new("SELECT * FROM analyses WHERE analysis_id = @analysis_id");
        statement.add_param("analysis_id", &analysis_id.to_string());

        let mut reader = tx.query(statement).await?;
        if let Some(_row) = reader.next().await? {
            // For now, return a minimal AnalysisResult - this should be properly implemented
            // with row conversion logic from SpannerOperations
            let analysis = AnalysisResult {
                id: analysis_id.to_string(),
                repository_url: "".to_string(),
                branch: "main".to_string(),
                commit_hash: Some("".to_string()),
                repository_size_bytes: None,
                clone_time_ms: Some(0),
                status: crate::models::AnalysisStatus::Completed,
                started_at: chrono::Utc::now(),
                completed_at: Some(chrono::Utc::now()),
                duration_seconds: Some(0),
                progress: None,
                current_stage: Some("completed".to_string()),
                estimated_completion: None,
                metrics: None,
                patterns: Vec::new(),
                languages: HashMap::new(),
                embeddings: None,
                error_message: None,
                failed_files: Vec::new(),
                successful_analyses: None,
                user_id: "".to_string(),
                webhook_url: None,
                file_count: 0,
                success_rate: 1.0,
                performance_metrics: None,
                warnings: Vec::new(),
            };
            return Ok(Some(analysis));
        }
        Ok(None)
    }

    async fn get_analyses(&self, _params: &ListAnalysesParams) -> Result<Vec<AnalysisResult>> {
        // Simplified implementation - should be properly implemented with filtering
        Ok(Vec::new())
    }

    async fn store_analysis(&self, analysis: &AnalysisResult) -> Result<()> {
        use google_cloud_spanner::statement::Statement;

        let analysis_clone = analysis.clone();
        let (_, _) = self
            .read_write_transaction(|tx| {
                let analysis = analysis_clone.clone();
                Box::pin(async move {
                    let mut statement = Statement::new(
                        "INSERT OR UPDATE INTO analyses (analysis_id, repository_url, branch, commit_hash, status, user_id, started_at, completed_at)
                         VALUES (@analysis_id, @repository_url, @branch, @commit_hash, @status, @user_id, @started_at, @completed_at)"
                    );
                    statement.add_param("analysis_id", &analysis.id);
                    statement.add_param("repository_url", &analysis.repository_url);
                    statement.add_param("branch", &analysis.branch);
                    statement.add_param("commit_hash", &analysis.commit_hash);
                    statement.add_param("status", &analysis.status.to_string());
                    statement.add_param("user_id", &analysis.user_id);
                    statement.add_param("started_at", &analysis.started_at.to_rfc3339());
                    if let Some(completed_at) = &analysis.completed_at {
                        statement.add_param("completed_at", &completed_at.to_rfc3339());
                    } else {
                        statement.add_param("completed_at", &Option::<String>::None);
                    }

                    tx.update(statement).await?;
                    Ok::<(), google_cloud_spanner::client::Error>(())
                })
            })
            .await?;
        Ok(())
    }

    async fn get_analysis_metrics(&self, _analysis_id: &str) -> Result<Option<RepositoryMetrics>> {
        // Simplified implementation
        Ok(None)
    }

    async fn get_analysis_patterns(&self, _analysis_id: &str) -> Result<Option<Vec<DetectedPattern>>> {
        // Simplified implementation
        Ok(None)
    }
}