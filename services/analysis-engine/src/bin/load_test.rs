//! Load testing binary for the analysis engine
//! 
//! Usage:
//!   cargo run --bin load_test -- --test-type standard
//!   cargo run --bin load_test -- --test-type stress --concurrent 50

use analysis_engine::config::ServiceConfig;
use clap::{Parser, ValueEnum};
use std::sync::Arc;
use tracing::{info, error};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

#[path = "../../tests/load_test.rs"]
mod load_test;

use load_test::{LoadTestConfig, LoadTestRunner};

#[derive(Parser, Debug)]
#[command(author, version, about = "Load testing for analysis engine", long_about = None)]
struct Args {
    /// Type of load test to run
    #[arg(short, long, value_enum, default_value = "standard")]
    test_type: TestType,

    /// Number of concurrent analyses
    #[arg(short, long)]
    concurrent: Option<usize>,

    /// Maximum test duration in seconds
    #[arg(short, long)]
    duration: Option<u64>,

    /// Output format for results
    #[arg(short, long, value_enum, default_value = "text")]
    output: OutputFormat,
}

#[derive(Copy, Clone, PartialEq, Eq, PartialOrd, Ord, ValueEnum, Debug)]
enum TestType {
    /// Standard 1M LOC test
    Standard,
    /// Stress test with high concurrency
    Stress,
    /// Quick smoke test
    Smoke,
}

#[derive(Copy, Clone, PartialEq, Eq, PartialOrd, Ord, ValueEnum, Debug)]
enum OutputFormat {
    /// Human-readable text
    Text,
    /// JSON format
    Json,
    /// CSV format
    Csv,
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // Initialize tracing
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::fmt::layer()
                .with_target(false)
                .with_thread_ids(true)
                .with_level(true)
        )
        .init();

    let args = Args::parse();

    info!("Starting load test: {:?}", args.test_type);

    // Create test configuration
    let mut config = match args.test_type {
        TestType::Standard => LoadTestConfig::standard_1m_loc_test(),
        TestType::Stress => LoadTestConfig::stress_test(),
        TestType::Smoke => create_smoke_test_config(),
    };

    // Override with command line arguments
    if let Some(concurrent) = args.concurrent {
        config.concurrent_analyses = concurrent;
    }

    if let Some(duration) = args.duration {
        config.max_duration = std::time::Duration::from_secs(duration);
    }

    // Run the load test
    let runner = LoadTestRunner::new(config).await?;
    let results = runner.run().await?;

    // Output results
    match args.output {
        OutputFormat::Text => {
            println!("{}", results.generate_report());
            
            // Validate against production requirements
            match results.validate_production_requirements() {
                Ok(_) => {
                    info!("✅ Load test PASSED all production requirements");
                    std::process::exit(0);
                }
                Err(e) => {
                    error!("❌ Load test FAILED: {}", e);
                    std::process::exit(1);
                }
            }
        }
        OutputFormat::Json => {
            let json = serde_json::json!({
                "total_analyses": results.total_analyses,
                "successful_analyses": results.successful_analyses,
                "failed_analyses": results.failed_analyses,
                "total_loc_processed": results.total_loc_processed,
                "total_files_processed": results.total_files_processed,
                "average_analysis_time_ms": results.average_analysis_time.as_millis(),
                "p95_analysis_time_ms": results.p95_analysis_time.as_millis(),
                "p99_analysis_time_ms": results.p99_analysis_time.as_millis(),
                "max_memory_usage_mb": results.max_memory_usage / 1024 / 1024,
                "error_breakdown": results.error_breakdown,
                "validation_passed": results.validate_production_requirements().is_ok(),
            });
            println!("{}", serde_json::to_string_pretty(&json)?);
        }
        OutputFormat::Csv => {
            println!("metric,value");
            println!("total_analyses,{}", results.total_analyses);
            println!("successful_analyses,{}", results.successful_analyses);
            println!("failed_analyses,{}", results.failed_analyses);
            println!("total_loc_processed,{}", results.total_loc_processed);
            println!("total_files_processed,{}", results.total_files_processed);
            println!("average_analysis_time_ms,{}", results.average_analysis_time.as_millis());
            println!("p95_analysis_time_ms,{}", results.p95_analysis_time.as_millis());
            println!("p99_analysis_time_ms,{}", results.p99_analysis_time.as_millis());
            println!("max_memory_usage_mb,{}", results.max_memory_usage / 1024 / 1024);
        }
    }

    Ok(())
}

fn create_smoke_test_config() -> LoadTestConfig {
    LoadTestConfig {
        concurrent_analyses: 2,
        test_repositories: vec![
            load_test::TestRepository {
                name: "Small Test Repo".to_string(),
                url: "https://github.com/rust-lang/mdBook.git".to_string(),
                expected_loc: 10_000,
                expected_files: 100,
                languages: vec!["rust".to_string()],
            },
        ],
        max_duration: std::time::Duration::from_secs(300), // 5 minutes
        test_memory_limits: false,
        test_timeouts: false,
    }
}