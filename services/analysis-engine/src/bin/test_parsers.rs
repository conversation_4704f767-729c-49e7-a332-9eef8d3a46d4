use analysis_engine::parser::{TreeSitterParser, ParserConfig};
use std::sync::Arc;

#[tokio::main]
async fn main() {
    println!("Testing TreeSitter parsers...\n");
    
    let config = ParserConfig::default();
    let parser = match TreeSitterParser::new_with_config(config) {
        Ok(p) => Arc::new(p),
        Err(e) => {
            println!("Failed to create parser: {}", e);
            return;
        }
    };
    
    // Test all supported languages
    let test_cases = vec![
        ("rust", r#"
fn main() {
    println!("Hello, Rust!");
}
"#),
        ("python", r#"
def main():
    print("Hello, Python!")
"#),
        ("javascript", r#"
function main() {
    console.log("Hello, JavaScript!");
}
"#),
        ("typescript", r#"
function main(): void {
    console.log("Hello, TypeScript!");
}
"#),
        ("go", r#"
package main

func main() {
    fmt.Println("Hello, Go!")
}
"#),
        ("java", r#"
public class Main {
    public static void main(String[] args) {
        System.out.println("Hello, Java!");
    }
}
"#),
        ("c", r#"
#include <stdio.h>

int main() {
    printf("Hello, C!\n");
    return 0;
}
"#),
        ("cpp", r#"
#include <iostream>

int main() {
    std::cout << "Hello, C++!" << std::endl;
    return 0;
}
"#),
        ("ruby", r#"
def main
    puts "Hello, Ruby!"
end
"#),
        ("swift", r#"
func main() {
    print("Hello, Swift!")
}
"#),
        ("r", r#"
main <- function() {
    print("Hello, R!")
}
"#),
        ("julia", r#"
function main()
    println("Hello, Julia!")
end
"#),
        ("scala", r#"
object Main {
    def main(args: Array[String]): Unit = {
        println("Hello, Scala!")
    }
}
"#),
        ("elixir", r#"
defmodule Main do
    def main do
        IO.puts "Hello, Elixir!"
    end
end
"#),
        ("zig", r#"
const std = @import("std");

pub fn main() void {
    std.debug.print("Hello, Zig!\n", .{});
}
"#),
        ("nix", r#"
{
  hello = "Hello, Nix!";
}
"#),
    ];
    
    let mut success_count = 0;
    let mut total_count = 0;
    
    for (lang, code) in test_cases {
        total_count += 1;
        print!("Testing {} parser... ", lang);
        
        let file_path = format!("test.{}", match lang {
            "rust" => "rs",
            "python" => "py",
            "javascript" => "js",
            "typescript" => "ts",
            "go" => "go",
            "java" => "java",
            "c" => "c",
            "cpp" => "cpp",
            "ruby" => "rb",
            "swift" => "swift",
            "r" => "r",
            "julia" => "jl",
            "scala" => "scala",
            "elixir" => "ex",
            "zig" => "zig",
            "nix" => "nix",
            _ => lang,
        });
        
        match parser.parse_file(&file_path, code.to_string()).await {
            Ok(analysis) => {
                if analysis.language == lang {
                    println!("✓ SUCCESS - parsed {} nodes", analysis.ast.root.descendant_count());
                    success_count += 1;
                } else {
                    println!("✗ FAILED - detected as {} instead of {}", analysis.language, lang);
                }
            }
            Err(e) => {
                println!("✗ FAILED - {}", e);
            }
        }
    }
    
    println!("\n=== SUMMARY ===");
    println!("Tested {} parsers: {} succeeded, {} failed", 
             total_count, success_count, total_count - success_count);
    
    if success_count == total_count {
        println!("All parsers working correctly! ✓");
    } else {
        println!("Some parsers failed! ✗");
    }
}