use anyhow::{Context, Result};
use google_cloud_spanner::{client::Client, statement::Statement};
use std::collections::HashSet;
use std::fs;
use std::path::Path;
use tracing::{info, warn};

// Note: This is a simplified migration manager for demonstration
// In production, you would use gcloud CLI or a proper migration tool

/// Migration manager for handling database schema updates
pub struct MigrationManager {
    client: Client,
}

#[derive(Debug, Clone)]
pub struct Migration {
    pub version: String,
    pub description: String,
    pub sql: String,
    pub file_path: String,
}

impl MigrationManager {
    pub fn new(client: Client) -> Self {
        Self { client }
    }

    /// Initialize the schema_migrations table if it doesn't exist
    /// Note: In production, this should be done via gcloud CLI
    pub async fn initialize_migrations_table(&self) -> Result<()> {
        info!("Migration table initialization should be done via gcloud CLI");
        info!("Run: gcloud spanner databases ddl update ccl_main --instance=ccl-instance --ddl='CREATE TABLE IF NOT EXISTS schema_migrations (version STRING(50) NOT NULL, applied_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true), description STRING(255)) PRIMARY KEY (version)'");
        Ok(())
    }

    /// Get list of applied migrations from database
    pub async fn get_applied_migrations(&self) -> Result<HashSet<String>> {
        let statement = Statement::new("SELECT version FROM schema_migrations ORDER BY version");

        let mut tx = self.client.read_only_transaction().await
            .context("Failed to create read transaction")?;

        let mut reader = tx.query(statement).await
            .context("Failed to query applied migrations")?;

        let mut applied = HashSet::new();
        while let Some(row) = reader.next().await.context("Failed to read migration row")? {
            let version: String = row.column_by_name("version")
                .context("Failed to read version column")?;
            applied.insert(version);
        }

        Ok(applied)
    }

    /// Load migration files from the migrations directory
    pub fn load_migration_files(&self) -> Result<Vec<Migration>> {
        let migrations_dir = Path::new("migrations");
        if !migrations_dir.exists() {
            return Ok(Vec::new());
        }

        let mut migrations = Vec::new();
        let entries = fs::read_dir(migrations_dir)
            .context("Failed to read migrations directory")?;

        for entry in entries {
            let entry = entry.context("Failed to read directory entry")?;
            let path = entry.path();
            
            if path.extension().and_then(|s| s.to_str()) == Some("sql") {
                let file_name = path.file_stem()
                    .and_then(|s| s.to_str())
                    .context("Invalid migration file name")?;

                // Parse version and description from filename
                let parts: Vec<&str> = file_name.splitn(2, '_').collect();
                if parts.len() != 2 {
                    warn!("Skipping migration file with invalid name format: {}", file_name);
                    continue;
                }

                let version = parts[0].to_string();
                let description = parts[1].replace('_', " ");
                
                let sql = fs::read_to_string(&path)
                    .with_context(|| format!("Failed to read migration file: {:?}", path))?;

                migrations.push(Migration {
                    version,
                    description,
                    sql,
                    file_path: path.to_string_lossy().to_string(),
                });
            }
        }

        // Sort migrations by version
        migrations.sort_by(|a, b| a.version.cmp(&b.version));
        Ok(migrations)
    }

    /// Apply a single migration
    /// Note: In production, migrations should be applied via gcloud CLI
    pub async fn apply_migration(&self, migration: &Migration) -> Result<()> {
        info!("Migration {}: {}", migration.version, migration.description);
        info!("To apply this migration, run:");
        info!("gcloud spanner databases ddl update ccl_main --instance=ccl-instance --ddl-file={}", migration.file_path);

        // For now, just record that we would apply the migration
        // In a real implementation, you would use the gcloud CLI or admin API
        warn!("Migration application is disabled - use gcloud CLI for production");
        Ok(())
    }

    /// Apply all pending migrations
    pub async fn apply_pending_migrations(&self) -> Result<()> {
        // Initialize migrations table
        self.initialize_migrations_table().await?;

        // Get applied migrations
        let applied = self.get_applied_migrations().await?;
        
        // Load migration files
        let migrations = self.load_migration_files()?;
        
        // Find pending migrations
        let pending: Vec<&Migration> = migrations
            .iter()
            .filter(|m| !applied.contains(&m.version))
            .collect();

        if pending.is_empty() {
            info!("No pending migrations to apply");
            return Ok(());
        }

        info!("Found {} pending migrations", pending.len());

        // Apply each pending migration
        for migration in pending {
            self.apply_migration(migration).await?;
        }

        info!("All pending migrations applied successfully");
        Ok(())
    }

    /// Get migration status (applied vs pending)
    pub async fn get_migration_status(&self) -> Result<(Vec<String>, Vec<String>)> {
        let applied = self.get_applied_migrations().await?;
        let migrations = self.load_migration_files()?;
        
        let mut applied_list = Vec::new();
        let mut pending_list = Vec::new();
        
        for migration in migrations {
            if applied.contains(&migration.version) {
                applied_list.push(format!("{}: {}", migration.version, migration.description));
            } else {
                pending_list.push(format!("{}: {}", migration.version, migration.description));
            }
        }
        
        Ok((applied_list, pending_list))
    }
}
