groups:
  - name: analysis_engine_alerts
    interval: 30s
    rules:
      # High error rate alert
      - alert: HighErrorRate
        expr: |
          (
            sum(rate(analysis_requests_total{status="error"}[5m]))
            /
            sum(rate(analysis_requests_total[5m]))
          ) > 0.01
        for: 5m
        labels:
          severity: critical
          service: analysis-engine
        annotations:
          summary: "High error rate detected ({{ $value | humanizePercentage }})"
          description: "Error rate is above 1% for the last 5 minutes"

      # Analysis duration too high
      - alert: SlowAnalysis
        expr: |
          histogram_quantile(0.95, 
            sum(rate(analysis_duration_histogram_bucket[5m])) by (le)
          ) > 300000
        for: 10m
        labels:
          severity: warning
          service: analysis-engine
        annotations:
          summary: "P95 analysis duration exceeds 5 minutes"
          description: "95th percentile analysis duration is {{ $value | humanizeDuration }}"

      # Memory usage too high
      - alert: HighMemoryUsage
        expr: |
          (process_resident_memory_bytes / 1024 / 1024) > 3584
        for: 5m
        labels:
          severity: warning
          service: analysis-engine
        annotations:
          summary: "Memory usage exceeds 3.5GB"
          description: "Current memory usage: {{ $value | humanize }}MB"

      # Circuit breaker open
      - alert: CircuitBreakerOpen
        expr: |
          circuit_breaker_state{state="open"} == 1
        for: 5m
        labels:
          severity: critical
          service: analysis-engine
        annotations:
          summary: "Circuit breaker is open for {{ $labels.service }}"
          description: "Circuit breaker has been open for more than 5 minutes"

      # Too many concurrent analyses
      - alert: HighConcurrency
        expr: |
          concurrent_analyses > 45
        for: 5m
        labels:
          severity: warning
          service: analysis-engine
        annotations:
          summary: "High concurrent analysis count"
          description: "Currently running {{ $value }} concurrent analyses (limit: 50)"

      # Pod restart
      - alert: PodRestarting
        expr: |
          rate(kube_pod_container_status_restarts_total{pod=~"analysis-engine.*"}[15m]) > 0
        for: 5m
        labels:
          severity: warning
          service: analysis-engine
        annotations:
          summary: "Analysis engine pod is restarting"
          description: "Pod {{ $labels.pod }} has restarted {{ $value }} times in the last 15 minutes"

      # Spanner errors
      - alert: SpannerErrors
        expr: |
          sum(rate(spanner_operation_errors_total[5m])) > 0.1
        for: 5m
        labels:
          severity: critical
          service: analysis-engine
        annotations:
          summary: "High Spanner error rate"
          description: "Spanner operations failing at {{ $value | humanize }} ops/sec"

      # Redis connection issues
      - alert: RedisConnectionFailure
        expr: |
          redis_connection_pool_active == 0
        for: 5m
        labels:
          severity: critical
          service: analysis-engine
        annotations:
          summary: "Redis connection pool is empty"
          description: "No active Redis connections available"

      # File size limit violations
      - alert: FileSizeLimitViolations
        expr: |
          sum(rate(file_size_limit_violations_total[5m])) > 1
        for: 5m
        labels:
          severity: warning
          service: analysis-engine
        annotations:
          summary: "File size limit violations detected"
          description: "{{ $value | humanize }} files/sec exceeding size limit"

      # Parse timeout violations
      - alert: ParseTimeouts
        expr: |
          sum(rate(parse_timeout_violations_total[5m])) > 0.5
        for: 5m
        labels:
          severity: warning
          service: analysis-engine
        annotations:
          summary: "Parse timeout violations detected"
          description: "{{ $value | humanize }} parse operations/sec timing out"