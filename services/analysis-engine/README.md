# Analysis Engine Service

This is the Analysis Engine service implementation directory.

## 📚 Documentation

All documentation has been consolidated and organized in the main documentation directory:

**➡️ [Main Documentation](/docs/analysis-engine/)**

### Quick Links

- [Getting Started](/docs/analysis-engine/README.md)
- [API Reference](/docs/analysis-engine/api/reference.md)
- [Deployment Guide](/docs/analysis-engine/deployment/production-deployment.md)
- [Operations Runbook](/docs/analysis-engine/operations/runbook.md)
- [Architecture Guide](/docs/analysis-engine/architecture/system-design.md)

## 🚀 Quick Start

```bash
# Build the service
cargo build --release

# Run tests
cargo test

# Start locally
cargo run --bin analysis-engine

# Deploy to production
./deploy-production.sh
```

## 📁 Directory Structure

```
services/analysis-engine/
├── src/                    # Source code
├── tests/                  # Test suites
├── scripts/                # Operational scripts
├── docs/                   # Implementation notes
├── Cargo.toml             # Rust dependencies
├── Dockerfile.simple      # Container definition
└── deploy-production.sh   # Deployment script
```

## 🔧 Development

For development guidelines and contribution instructions, see:
- [Development Guide](/docs/analysis-engine/development/contributing.md)
- [Testing Guide](/docs/analysis-engine/development/testing.md)

## 📊 Current Status

- **Production URL**: https://analysis-engine-************.us-central1.run.app
- **Status**: ✅ Fully Operational
- **Version**: 1.0.0

---

For all other documentation, please refer to the [main documentation directory](/docs/analysis-engine/).