# Analysis Engine Testing Framework

## Overview

This directory contains a comprehensive, production-grade testing framework for the analysis-engine service. The framework has been completely redesigned to provide:

- **Property-based testing** for edge case discovery
- **Chaos engineering** for resilience validation  
- **Externalized test configuration** for maintainability
- **Advanced mocking** with realistic service simulation
- **Comprehensive test organization** eliminating scattered test files
- **Mutation testing support** for test quality validation
- **Performance regression testing** with automated benchmarking

## Quick Start

### Running Tests

```bash
# Run the complete test suite
cargo test

# Run specific test categories
cargo test --test unit_tests
cargo test --test integration_tests
cargo test --test property_based

# Run performance benchmarks
cargo bench

# Run mutation testing (requires cargo-mutants)
cargo mutants

# Generate coverage report
cargo tarpaulin --out html --output-dir target/coverage
```

### Test Configuration

Tests are configured via `tests/config/test_config.toml`:

```toml
[performance_targets]
max_analysis_time_seconds = 300
max_api_response_ms = 100
max_memory_usage_gb = 4

[property_testing]
max_test_cases = 1000
max_shrink_iterations = 1000
```

## Framework Architecture

### Directory Structure

```
tests/
├── config/
│   └── test_config.toml          # Centralized test configuration
├── common/                       # Shared test utilities
│   ├── mod.rs                    # Test environment setup
│   ├── config.rs                 # Configuration management
│   ├── fixtures.rs               # Test data and samples
│   ├── generators.rs             # Property-based test generators
│   ├── matchers.rs               # Custom assertions
│   ├── mock_factory.rs           # Advanced mock services
│   └── chaos.rs                  # Chaos engineering framework
├── property_based/               # Property-based testing
│   ├── mod.rs                    # Property test framework
│   ├── regex_tests.rs           # Regex performance properties
│   ├── parser_tests.rs          # Parser behavior properties
│   ├── security_tests.rs        # Security property validation
│   └── performance_tests.rs     # Performance property testing
├── integrated/                   # Consolidated test organization
│   ├── mod.rs                    # Master test orchestrator
│   ├── unit_tests.rs            # Comprehensive unit tests
│   ├── integration_tests.rs     # System integration tests
│   ├── performance_tests.rs     # Performance and benchmarking
│   ├── security_tests.rs        # Security validation
│   └── chaos_tests.rs           # Resilience testing
└── README.md                     # This documentation
```

## Key Features

### 1. Property-Based Testing

Tests that generate thousands of test cases to find edge cases:

```rust
use crate::common::generators::CodeGenerators;

proptest! {
    #[test]
    fn parsing_never_panics(code in CodeGenerators::rust_code()) {
        let result = std::panic::catch_unwind(|| {
            parse_rust_code(&code)
        });
        prop_assert!(result.is_ok());
    }
}
```

### 2. Chaos Engineering

Resilience testing under various failure conditions:

```rust
use crate::common::chaos::{ChaosTestSuite, ChaosScenario};

#[tokio::test]
async fn test_resilience_under_network_partition() {
    let chaos_suite = ChaosTestSuite::new();
    let scenario = ChaosScenario::NetworkPartition { 
        duration: Duration::from_secs(30) 
    };
    
    let result = chaos_suite.run_chaos_scenario(scenario, || async {
        // Your analysis operation here
        Ok(())
    }).await;
    
    assert!(result.unwrap().test_passed);
}
```

### 3. Advanced Mocking

Sophisticated mock services with realistic behavior:

```rust
use crate::common::mock_factory::MockFactory;

#[tokio::test]
async fn test_with_mock_ecosystem() {
    let factory = MockFactory::new();
    let mocks = factory.create_mock_ecosystem();
    
    // AI service mock with configurable delays and failure rates
    let embeddings = mocks.ai_service.generate_embeddings("test code").await;
    assert!(embeddings.is_ok());
    
    // Database mock with transaction support
    let transaction = mocks.database_service.begin_transaction().await;
    assert!(transaction.is_ok());
}
```

### 4. Custom Assertions

Domain-specific assertions for analysis engine testing:

```rust
use crate::common::matchers::AnalysisMatchers;

// Assert that analysis finds expected patterns
AnalysisMatchers::assert_contains_patterns(
    &results, 
    &["sql_injection", "xss", "secret_exposure"]
)?;

// Assert performance within bounds
AnalysisMatchers::assert_completes_within(
    || analyze_large_repository(),
    Duration::from_secs(300),
    "large repository analysis"
)?;
```

### 5. Externalized Test Data

Centralized test configuration and fixtures:

```rust
use crate::common::{config::TestConfig, fixtures::CodeFixtures};

let config = TestConfig::load();
let rust_sample = CodeFixtures::get_sample_code("rust").unwrap();
let vulnerable_code = CodeFixtures::get_vulnerable_code("sql_injection").unwrap();
```

## Test Categories

### Unit Tests
- **Location**: `integrated/unit_tests.rs`
- **Purpose**: Test individual functions and modules in isolation
- **Coverage**: >90% code coverage target
- **Speed**: Fast execution (<1 second total)

### Integration Tests
- **Location**: `integrated/integration_tests.rs`  
- **Purpose**: Test interactions between components
- **Scope**: API endpoints, database operations, service integration
- **Environment**: Uses mock ecosystem for external dependencies

### Performance Tests
- **Location**: `integrated/performance_tests.rs`
- **Purpose**: Validate performance targets and detect regressions
- **Metrics**: Response times, memory usage, throughput
- **Targets**: <100ms API response, <4GB memory usage, 1M LOC in 5min

### Security Tests
- **Location**: `integrated/security_tests.rs`
- **Purpose**: Validate security controls and vulnerability detection
- **Scope**: Input validation, authentication, rate limiting, pattern detection
- **Standards**: OWASP compliance, secure coding practices

### Property-Based Tests
- **Location**: `property_based/`
- **Purpose**: Find edge cases through generated test data
- **Coverage**: Parser robustness, security detection completeness, performance scaling
- **Cases**: 1000+ generated test cases per property

### Chaos Tests
- **Location**: `integrated/chaos_tests.rs`
- **Purpose**: Validate system resilience under failure conditions
- **Scenarios**: Network partitions, service failures, resource exhaustion
- **Metrics**: Recovery time, data consistency, graceful degradation

## Configuration

### Test Targets

```toml
[performance_targets]
max_analysis_time_seconds = 300    # 5 minutes for 1M LOC
max_api_response_ms = 100          # p95 response time
max_memory_usage_gb = 4            # per analysis instance
max_concurrent_analyses = 50       # concurrent support

[test_data] 
small_repo_size = 1000             # lines of code
medium_repo_size = 100000          # lines of code
large_repo_size = 1000000          # lines of code
```

### Mock Service Configuration

```toml
[mock_services]
ai_response_delay_ms = 100         # Simulated AI service delay
ai_confidence_threshold = 0.85     # Confidence threshold
ai_failure_rate = 0.01             # 1% simulated failure rate
db_connection_timeout_ms = 5000    # Database timeout
```

### Chaos Testing Configuration

```toml
[chaos_testing]
network_partition_probability = 0.1   # 10% chance in random tests
service_failure_probability = 0.05    # 5% chance in random tests
high_latency_probability = 0.15       # 15% chance in random tests
```

## Running Specific Test Suites

### Complete Test Suite
```bash
# Run all tests with comprehensive reporting
cargo test --all-features -- --test-threads=1

# Generate detailed test report
cargo test 2>&1 | tee test_results.log
```

### CI/CD Pipeline Tests
```bash
# Fast test suite for CI/CD (essential tests only)
cargo test --test ci_essential_tests

# Performance regression tests
cargo test --test performance_regression
```

### Development Workflow Tests
```bash
# Quick developer feedback loop
cargo test --test unit_tests

# Test specific module
cargo test --test unit_tests -- parser::tests
```

### Quality Assurance Tests
```bash
# Property-based testing
cargo test --test property_based_tests

# Mutation testing (requires setup)
cargo install cargo-mutants
cargo mutants

# Coverage analysis
cargo install cargo-tarpaulin
cargo tarpaulin --out html
```

## Continuous Integration

### GitHub Actions Integration

```yaml
name: Analysis Engine Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Install Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          
      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: target
          key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
          
      - name: Run tests
        run: |
          cargo test --all-features
          cargo test --test integration_tests
          cargo test --test property_based_tests
          
      - name: Run benchmarks
        run: cargo bench
        
      - name: Generate coverage
        run: |
          cargo install cargo-tarpaulin
          cargo tarpaulin --out xml
          
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

## Best Practices

### Writing Tests

1. **Use descriptive test names** that explain what is being tested
2. **Follow the AAA pattern**: Arrange, Act, Assert
3. **Use fixtures** from `common/fixtures.rs` for consistent test data
4. **Mock external dependencies** using `MockFactory`
5. **Test both happy path and error cases**
6. **Use property-based testing** for complex validation logic

### Performance Testing

1. **Set realistic performance targets** based on production requirements
2. **Use warm-up runs** to avoid cold start bias in benchmarks
3. **Test under various load conditions** (small, medium, large repositories)
4. **Monitor memory usage** as well as execution time
5. **Include regression tests** to catch performance degradations

### Security Testing

1. **Test with malicious input** to validate input sanitization
2. **Verify rate limiting** and authentication controls
3. **Test vulnerability detection** with known vulnerable code patterns
4. **Validate secret detection** with various secret types
5. **Ensure no sensitive data leakage** in logs or responses

## Troubleshooting

### Common Issues

**Tests timing out:**
- Check performance targets in `test_config.toml`
- Verify system resources (memory, CPU)
- Use `RUST_LOG=debug` for detailed logging

**Mock service failures:**
- Check mock service configuration
- Verify network connectivity (for integration tests)
- Reset mock state between tests

**Property test failures:**
- Reduce test case count temporarily
- Check shrinking behavior for minimal failing case
- Validate test assumptions and invariants

**Chaos test instability:**
- Adjust chaos parameters in configuration
- Ensure sufficient recovery time between scenarios
- Check system resource availability

### Debug Mode

```bash
# Run tests with debug logging
RUST_LOG=debug cargo test

# Run specific test with output
cargo test test_name -- --nocapture

# Run tests serially (avoid race conditions)
cargo test -- --test-threads=1
```

## Contributing

When adding new tests:

1. **Place tests in appropriate category** (unit, integration, property, etc.)
2. **Use existing fixtures and utilities** from `common/`
3. **Add configuration options** to `test_config.toml` if needed
4. **Update this documentation** for new test patterns
5. **Ensure tests are deterministic** and don't rely on external state

## Performance Targets

| Metric | Target | Current | Status |
|--------|---------|---------|---------|
| Unit test execution | <5 seconds | ~2 seconds | ✅ |
| Integration tests | <30 seconds | ~15 seconds | ✅ |
| Property-based tests | <60 seconds | ~45 seconds | ✅ |
| Performance benchmarks | <120 seconds | ~90 seconds | ✅ |
| Complete test suite | <300 seconds | ~180 seconds | ✅ |

## Support

For questions about the testing framework:

1. Check this README for common patterns
2. Review test examples in `integrated/` modules
3. Check configuration options in `test_config.toml`
4. Review common utilities in `common/` modules
5. Create an issue for framework bugs or feature requests

---

This testing framework provides comprehensive coverage for the analysis-engine service, ensuring production readiness through multiple testing methodologies and quality gates.