// Common Test Utilities - Improved Testing Framework
// Centralizes test utilities, configuration, and property-based testing

pub mod config;
pub mod fixtures;
pub mod generators;
pub mod matchers;
pub mod mock_factory;
pub mod chaos;

use std::sync::Once;
use tracing_subscriber;

static INIT: Once = Once::new();

/// Initialize test logging and tracing
pub fn init_test_logging() {
    INIT.call_once(|| {
        tracing_subscriber::fmt()
            .with_max_level(tracing::Level::DEBUG)
            .with_test_writer()
            .init();
    });
}

/// Test result type for consistent error handling
pub type TestResult<T = ()> = Result<T, Box<dyn std::error::Error + Send + Sync>>;

/// Common test assertions and utilities
pub struct TestAssertions;

impl TestAssertions {
    /// Assert performance is within acceptable bounds
    pub fn assert_performance<T>(
        operation: impl FnOnce() -> T,
        max_duration_ms: u64,
        operation_name: &str,
    ) -> TestResult<T> {
        let start = std::time::Instant::now();
        let result = operation();
        let duration = start.elapsed();
        
        if duration.as_millis() > max_duration_ms as u128 {
            return Err(format!(
                "{} took {}ms, expected <{}ms",
                operation_name,
                duration.as_millis(),
                max_duration_ms
            ).into());
        }
        
        Ok(result)
    }
    
    /// Assert memory usage is within bounds
    pub fn assert_memory_usage<T>(
        operation: impl FnOnce() -> T,
        max_memory_mb: u64,
        operation_name: &str,
    ) -> TestResult<T> {
        // Note: This is a simplified memory check
        // In practice, you'd use more sophisticated memory profiling
        let start_memory = Self::get_memory_usage();
        let result = operation();
        let end_memory = Self::get_memory_usage();
        
        let memory_used = end_memory.saturating_sub(start_memory);
        if memory_used > max_memory_mb * 1024 * 1024 {
            return Err(format!(
                "{} used {}MB memory, expected <{}MB",
                operation_name,
                memory_used / (1024 * 1024),
                max_memory_mb
            ).into());
        }
        
        Ok(result)
    }
    
    /// Get current memory usage (simplified implementation)
    fn get_memory_usage() -> u64 {
        // This is a placeholder - in real implementation you'd use
        // memory profiling tools like jemalloc or system APIs
        0
    }
}

/// Test environment setup and cleanup
pub struct TestEnvironment {
    temp_dir: Option<tempfile::TempDir>,
}

impl TestEnvironment {
    pub fn new() -> TestResult<Self> {
        Ok(Self {
            temp_dir: Some(tempfile::tempdir()?),
        })
    }
    
    pub fn temp_path(&self) -> &std::path::Path {
        self.temp_dir.as_ref().unwrap().path()
    }
    
    pub fn create_test_file(&self, name: &str, content: &str) -> TestResult<std::path::PathBuf> {
        let file_path = self.temp_path().join(name);
        std::fs::write(&file_path, content)?;
        Ok(file_path)
    }
}

impl Drop for TestEnvironment {
    fn drop(&mut self) {
        // Cleanup happens automatically with TempDir
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_performance_assertion() {
        let result = TestAssertions::assert_performance(
            || std::thread::sleep(std::time::Duration::from_millis(10)),
            50,
            "sleep test"
        );
        assert!(result.is_ok());
    }
    
    #[test]  
    fn test_environment_setup() {
        let env = TestEnvironment::new().unwrap();
        let file_path = env.create_test_file("test.txt", "hello world").unwrap();
        assert!(file_path.exists());
    }
}