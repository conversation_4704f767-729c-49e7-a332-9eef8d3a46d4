// Test Fixtures - Centralized Test Data
// Provides reusable test data and scenarios

use std::collections::HashMap;
use super::config::TestConfig;

/// Code sample fixtures for testing
pub struct CodeFixtures;

impl CodeFixtures {
    /// Get sample code for a specific language
    pub fn get_sample_code(language: &str) -> Option<&'static str> {
        match language {
            "rust" => Some(RUST_SAMPLE),
            "javascript" => Some(JAVASCRIPT_SAMPLE),
            "python" => Some(PYTHON_SAMPLE),
            "java" => Some(JAVA_SAMPLE),
            "go" => Some(GO_SAMPLE),
            "typescript" => Some(TYPESCRIPT_SAMPLE),
            _ => None,
        }
    }
    
    /// Get vulnerable code sample for security testing
    pub fn get_vulnerable_code(vulnerability_type: &str) -> Option<&'static str> {
        match vulnerability_type {
            "sql_injection" => Some(SQL_INJECTION_SAMPLE),
            "xss" => Some(XSS_SAMPLE),
            "path_traversal" => Some(PATH_TRAVERSAL_SAMPLE),
            "command_injection" => Some(COMMAND_INJECTION_SAMPLE),
            _ => None,
        }
    }
    
    /// Get code with embedded secrets
    pub fn get_code_with_secret(secret_type: &str) -> Option<&'static str> {
        match secret_type {
            "aws_key" => Some(AWS_KEY_SAMPLE),
            "jwt_token" => Some(JWT_TOKEN_SAMPLE),
            "api_key" => Some(API_KEY_SAMPLE),
            "database_url" => Some(DATABASE_URL_SAMPLE),
            _ => None,
        }
    }
    
    /// Get all supported languages from config
    pub fn get_all_supported_languages() -> &'static [&'static str] {
        &["rust", "javascript", "python", "java", "go", "typescript", "c", "cpp"]
    }
}

/// Repository structure fixtures
pub struct RepositoryFixtures;

impl RepositoryFixtures {
    /// Get a typical web application structure
    pub fn web_app_structure() -> Vec<(&'static str, &'static str)> {
        vec![
            ("src/main.rs", RUST_SAMPLE),
            ("src/lib.rs", "pub mod api;\npub mod models;"),
            ("src/api/mod.rs", "pub mod handlers;"),
            ("src/models/user.rs", RUST_USER_MODEL),
            ("Cargo.toml", CARGO_TOML_SAMPLE),
            ("README.md", "# Web Application\n\nA sample web application."),
            (".gitignore", "target/\n*.lock"),
        ]
    }
    
    /// Get a typical microservice structure  
    pub fn microservice_structure() -> Vec<(&'static str, &'static str)> {
        vec![
            ("src/main.js", JAVASCRIPT_SAMPLE),
            ("src/routes/api.js", JAVASCRIPT_API_ROUTES),
            ("src/models/user.js", JAVASCRIPT_USER_MODEL),
            ("package.json", PACKAGE_JSON_SAMPLE),
            ("docker-compose.yml", DOCKER_COMPOSE_SAMPLE),
            ("README.md", "# Microservice\n\nA sample microservice."),
        ]
    }
    
    /// Get a large repository structure for performance testing
    pub fn large_repository_structure() -> Vec<(&'static str, &'static str)> {
        let mut files = Vec::new();
        
        // Generate many files for performance testing
        for i in 0..100 {
            files.push((
                Box::leak(format!("src/module_{}.rs", i).into_boxed_str()),
                RUST_SAMPLE,
            ));
        }
        
        // Add some variety
        for i in 0..50 {
            files.push((
                Box::leak(format!("frontend/component_{}.js", i).into_boxed_str()),
                JAVASCRIPT_SAMPLE,
            ));
        }
        
        files
    }
}

/// Performance test fixtures
pub struct PerformanceFixtures;

impl PerformanceFixtures {
    /// Generate code of specific size for memory testing
    pub fn generate_code_of_size(size_lines: usize) -> String {
        let base_line = "    let x = 42;\n";
        let mut code = String::from("fn test_function() {\n");
        
        for _ in 0..size_lines {
            code.push_str(base_line);
        }
        
        code.push_str("}\n");
        code
    }
    
    /// Get code with many patterns for pattern detection testing
    pub fn get_pattern_heavy_code() -> &'static str {
        PATTERN_HEAVY_CODE
    }
    
    /// Get nested code structure for parser stress testing
    pub fn get_deeply_nested_code() -> &'static str {
        DEEPLY_NESTED_CODE
    }
}

// Language sample constants
const RUST_SAMPLE: &str = r#"
use std::collections::HashMap;

#[derive(Debug, Clone)]
pub struct User {
    pub id: u64,
    pub name: String,
    pub email: String,
}

impl User {
    pub fn new(id: u64, name: String, email: String) -> Self {
        Self { id, name, email }
    }
    
    pub fn validate_email(&self) -> bool {
        self.email.contains('@')
    }
}

pub fn create_user_map() -> HashMap<u64, User> {
    let mut users = HashMap::new();
    users.insert(1, User::new(1, "Alice".to_string(), "<EMAIL>".to_string()));
    users.insert(2, User::new(2, "Bob".to_string(), "<EMAIL>".to_string()));
    users
}
"#;

const JAVASCRIPT_SAMPLE: &str = r#"
class User {
    constructor(id, name, email) {
        this.id = id;
        this.name = name;
        this.email = email;
    }
    
    validateEmail() {
        return this.email.includes('@');
    }
    
    static fromJSON(json) {
        return new User(json.id, json.name, json.email);
    }
}

function createUserMap() {
    const users = new Map();
    users.set(1, new User(1, 'Alice', '<EMAIL>'));
    users.set(2, new User(2, 'Bob', '<EMAIL>'));
    return users;
}

module.exports = { User, createUserMap };
"#;

const PYTHON_SAMPLE: &str = r#"
from typing import Dict, Optional
import re

class User:
    def __init__(self, id: int, name: str, email: str):
        self.id = id
        self.name = name
        self.email = email
    
    def validate_email(self) -> bool:
        return '@' in self.email and '.' in self.email
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'User':
        return cls(data['id'], data['name'], data['email'])

def create_user_map() -> Dict[int, User]:
    users = {}
    users[1] = User(1, 'Alice', '<EMAIL>')
    users[2] = User(2, 'Bob', '<EMAIL>')
    return users
"#;

const JAVA_SAMPLE: &str = r#"
import java.util.HashMap;
import java.util.Map;

public class User {
    private Long id;
    private String name;
    private String email;
    
    public User(Long id, String name, String email) {
        this.id = id;
        this.name = name;
        this.email = email;
    }
    
    public boolean validateEmail() {
        return email != null && email.contains("@");
    }
    
    public static Map<Long, User> createUserMap() {
        Map<Long, User> users = new HashMap<>();
        users.put(1L, new User(1L, "Alice", "<EMAIL>"));
        users.put(2L, new User(2L, "Bob", "<EMAIL>"));
        return users;
    }
}
"#;

const GO_SAMPLE: &str = r#"
package main

import (
    "fmt"
    "strings"
)

type User struct {
    ID    uint64 `json:"id"`
    Name  string `json:"name"`
    Email string `json:"email"`
}

func (u *User) ValidateEmail() bool {
    return strings.Contains(u.Email, "@")
}

func CreateUserMap() map[uint64]*User {
    users := make(map[uint64]*User)
    users[1] = &User{ID: 1, Name: "Alice", Email: "<EMAIL>"}
    users[2] = &User{ID: 2, Name: "Bob", Email: "<EMAIL>"}
    return users
}

func main() {
    users := CreateUserMap()
    fmt.Printf("Created %d users\n", len(users))
}
"#;

const TYPESCRIPT_SAMPLE: &str = r#"
interface IUser {
    id: number;
    name: string;
    email: string;
}

class User implements IUser {
    constructor(
        public id: number,
        public name: string,
        public email: string
    ) {}
    
    validateEmail(): boolean {
        return this.email.includes('@');
    }
    
    static fromJSON(json: any): User {
        return new User(json.id, json.name, json.email);
    }
}

function createUserMap(): Map<number, User> {
    const users = new Map<number, User>();
    users.set(1, new User(1, 'Alice', '<EMAIL>'));
    users.set(2, new User(2, 'Bob', '<EMAIL>'));
    return users;
}

export { User, IUser, createUserMap };
"#;

// Vulnerability samples
const SQL_INJECTION_SAMPLE: &str = r#"
// SQL Injection vulnerability
const query = "SELECT * FROM users WHERE id = " + userId;
db.query(query, callback);

// Another SQL injection
const sql = `DELETE FROM posts WHERE author = '${username}'`;
connection.execute(sql);
"#;

const XSS_SAMPLE: &str = r#"
// XSS vulnerability
document.getElementById('content').innerHTML = userInput;

// Another XSS vulnerability  
const html = `<div>${userComment}</div>`;
element.innerHTML = html;
"#;

const PATH_TRAVERSAL_SAMPLE: &str = r#"
// Path traversal vulnerability
const filePath = path.join(baseDir, userProvidedPath);
fs.readFile(filePath, callback);

// Another path traversal
const fullPath = "/uploads/" + filename;
res.sendFile(fullPath);
"#;

const COMMAND_INJECTION_SAMPLE: &str = r#"
// Command injection vulnerability
const command = "ls -la " + userInput;
exec(command, callback);

// Another command injection
subprocess.call("rm -rf " + directory, shell=True)
"#;

// Secret samples
const AWS_KEY_SAMPLE: &str = r#"
const AWS_ACCESS_KEY_ID = "AKIAIOSFODNN7EXAMPLE";
const AWS_SECRET_ACCESS_KEY = "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY";

// In configuration
aws_access_key_id = AKIAIOSFODNN7EXAMPLE
aws_secret_access_key = wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
"#;

const JWT_TOKEN_SAMPLE: &str = r#"
const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";

// Authorization header
const authHeader = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";
"#;

const API_KEY_SAMPLE: &str = r#"
const API_KEY = "sk-1234567890abcdef1234567890abcdef";
const STRIPE_KEY = "pk_test_1234567890abcdef1234567890abcdef";

# In environment file
API_KEY=sk-1234567890abcdef1234567890abcdef
STRIPE_PUBLISHABLE_KEY=pk_test_1234567890abcdef1234567890abcdef
"#;

const DATABASE_URL_SAMPLE: &str = r#"
const DATABASE_URL = "postgres://username:password@localhost:5432/database";
const MONGODB_URI = "****************************************************";

# Connection strings
DATABASE_URL=postgres://myuser:<EMAIL>:5432/production
REDIS_URL=redis://user:<EMAIL>:6379
"#;

// Additional fixtures
const RUST_USER_MODEL: &str = r#"
#[derive(Debug, Serialize, Deserialize)]
pub struct User {
    pub id: Uuid,
    pub username: String,
    pub email: String,
    pub created_at: DateTime<Utc>,
    pub is_active: bool,
}

impl User {
    pub fn new(username: String, email: String) -> Self {
        Self {
            id: Uuid::new_v4(),
            username,
            email,
            created_at: Utc::now(),
            is_active: true,
        }
    }
}
"#;

const JAVASCRIPT_API_ROUTES: &str = r#"
const express = require('express');
const router = express.Router();

router.get('/users', async (req, res) => {
    try {
        const users = await User.findAll();
        res.json(users);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

router.post('/users', async (req, res) => {
    try {
        const user = await User.create(req.body);
        res.status(201).json(user);
    } catch (error) {
        res.status(400).json({ error: error.message });
    }
});

module.exports = router;
"#;

const JAVASCRIPT_USER_MODEL: &str = r#"
const { DataTypes } = require('sequelize');

const User = sequelize.define('User', {
    id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
    },
    username: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
    },
    email: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        validate: {
            isEmail: true,
        },
    },
});

module.exports = User;
"#;

const CARGO_TOML_SAMPLE: &str = r#"
[package]
name = "web-app"
version = "0.1.0"
edition = "2021"

[dependencies]
axum = "0.7"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
uuid = { version = "1.0", features = ["v4"] }
"#;

const PACKAGE_JSON_SAMPLE: &str = r#"
{
  "name": "microservice",
  "version": "1.0.0",
  "description": "A sample microservice",
  "main": "src/main.js",
  "dependencies": {
    "express": "^4.18.0",
    "sequelize": "^6.21.0",
    "pg": "^8.7.0"
  },
  "devDependencies": {
    "jest": "^28.1.0",
    "supertest": "^6.2.0"
  }
}
"#;

const DOCKER_COMPOSE_SAMPLE: &str = r#"
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=****************************/app
    depends_on:
      - db
  
  db:
    image: postgres:14
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
      - POSTGRES_DB=app
"#;

const PATTERN_HEAVY_CODE: &str = r#"
// This code contains many patterns for testing pattern detection performance
function processUserData(userData) {
    // SQL injection pattern
    const query = "SELECT * FROM users WHERE id = " + userData.id;
    
    // XSS pattern
    document.getElementById('output').innerHTML = userData.comment;
    
    // Hardcoded credentials
    const apiKey = "sk-1234567890abcdef";
    const dbPassword = "admin123";
    
    // Command injection
    const command = "ls -la " + userData.directory;
    exec(command);
    
    // Path traversal
    const filePath = "/uploads/" + userData.filename;
    
    // More patterns...
    const jwt = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...";
    const awsKey = "AKIAIOSFODNN7EXAMPLE";
    
    return processData(userData);
}
"#;

const DEEPLY_NESTED_CODE: &str = r#"
function deeplyNested() {
    if (condition1) {
        if (condition2) {
            if (condition3) {
                if (condition4) {
                    if (condition5) {
                        if (condition6) {
                            if (condition7) {
                                if (condition8) {
                                    if (condition9) {
                                        if (condition10) {
                                            return "deeply nested";
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    return "not so deep";
}
"#;

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_code_fixtures() {
        assert!(CodeFixtures::get_sample_code("rust").is_some());
        assert!(CodeFixtures::get_sample_code("javascript").is_some());
        assert!(CodeFixtures::get_sample_code("nonexistent").is_none());
    }
    
    #[test]
    fn test_vulnerability_fixtures() {
        assert!(CodeFixtures::get_vulnerable_code("sql_injection").is_some());
        assert!(CodeFixtures::get_vulnerable_code("xss").is_some());
        assert!(CodeFixtures::get_vulnerable_code("nonexistent").is_none());
    }
    
    #[test]
    fn test_secret_fixtures() {
        assert!(CodeFixtures::get_code_with_secret("aws_key").is_some());
        assert!(CodeFixtures::get_code_with_secret("jwt_token").is_some());
        assert!(CodeFixtures::get_code_with_secret("nonexistent").is_none());
    }
    
    #[test]
    fn test_repository_fixtures() {
        let web_app = RepositoryFixtures::web_app_structure();
        assert!(!web_app.is_empty());
        assert!(web_app.iter().any(|(path, _)| path.contains("main.rs")));
        
        let microservice = RepositoryFixtures::microservice_structure();
        assert!(!microservice.is_empty());
        assert!(microservice.iter().any(|(path, _)| path.contains("main.js")));
    }
    
    #[test]
    fn test_performance_fixtures() {
        let code = PerformanceFixtures::generate_code_of_size(100);
        assert!(code.contains("fn test_function"));
        assert!(code.lines().count() > 100);
        
        let pattern_code = PerformanceFixtures::get_pattern_heavy_code();
        assert!(pattern_code.contains("SQL injection"));
        assert!(pattern_code.contains("XSS pattern"));
    }
}