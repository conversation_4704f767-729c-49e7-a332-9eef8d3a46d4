// Custom Test Matchers and Assertions
// Enhanced assertion helpers for analysis engine testing

use std::time::Duration;
use pretty_assertions::{assert_eq, assert_ne};

/// Custom matchers for analysis engine testing
pub struct AnalysisMatchers;

impl AnalysisMatchers {
    /// Assert that analysis results contain expected patterns
    pub fn assert_contains_patterns(
        results: &[String],
        expected_patterns: &[&str],
    ) -> Result<(), String> {
        for pattern in expected_patterns {
            if !results.iter().any(|r| r.contains(pattern)) {
                return Err(format!("Expected pattern '{}' not found in results", pattern));
            }
        }
        Ok(())
    }
    
    /// Assert that analysis completes within time limit
    pub fn assert_completes_within<F, R>(
        operation: F,
        max_duration: Duration,
        operation_name: &str,
    ) -> Result<R, String>
    where
        F: FnOnce() -> R,
    {
        let start = std::time::Instant::now();
        let result = operation();
        let elapsed = start.elapsed();
        
        if elapsed > max_duration {
            return Err(format!(
                "{} took {:.2}s, expected <{:.2}s",
                operation_name,
                elapsed.as_secs_f64(),
                max_duration.as_secs_f64()
            ));
        }
        
        Ok(result)
    }
    
    /// Assert that vulnerability detection finds expected vulnerabilities
    pub fn assert_finds_vulnerabilities(
        detected: &[VulnerabilityResult],
        expected_types: &[&str],
    ) -> Result<(), String> {
        for expected_type in expected_types {
            if !detected.iter().any(|v| v.vulnerability_type == *expected_type) {
                return Err(format!(
                    "Expected vulnerability type '{}' not detected",
                    expected_type
                ));
            }
        }
        Ok(())
    }
    
    /// Assert that secret detection finds expected secrets
    pub fn assert_finds_secrets(
        detected: &[SecretResult],
        expected_types: &[&str],
    ) -> Result<(), String> {
        for expected_type in expected_types {
            if !detected.iter().any(|s| s.secret_type == *expected_type) {
                return Err(format!(
                    "Expected secret type '{}' not detected",
                    expected_type
                ));
            }
        }
        Ok(())
    }
    
    /// Assert memory usage is within bounds
    pub fn assert_memory_usage_bounded(
        before_kb: u64,
        after_kb: u64,
        max_increase_kb: u64,
        operation_name: &str,
    ) -> Result<(), String> {
        let increase = after_kb.saturating_sub(before_kb);
        if increase > max_increase_kb {
            return Err(format!(
                "{} used {}KB memory, expected <{}KB increase",
                operation_name, increase, max_increase_kb
            ));
        }
        Ok(())
    }
    
    /// Assert API response format is correct
    pub fn assert_api_response_format(
        response: &serde_json::Value,
        required_fields: &[&str],
    ) -> Result<(), String> {
        for field in required_fields {
            if !response.get(field).is_some() {
                return Err(format!("Required field '{}' missing from response", field));
            }
        }
        Ok(())
    }
    
    /// Assert that error handling is graceful
    pub fn assert_graceful_error_handling<F, T, E>(
        operation: F,
    ) -> Result<(), String>
    where
        F: FnOnce() -> Result<T, E>,
        E: std::fmt::Debug,
    {
        match std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| operation())) {
            Ok(_) => Ok(()), // Operation completed (success or error) without panic
            Err(_) => Err("Operation panicked instead of returning an error".to_string()),
        }
    }
}

/// Performance assertion helpers
pub struct PerformanceMatchers;

impl PerformanceMatchers {
    /// Assert that operations scale sub-linearly
    pub fn assert_sub_linear_scaling(
        small_input_size: usize,
        small_duration: Duration,
        large_input_size: usize,
        large_duration: Duration,
        operation_name: &str,
    ) -> Result<(), String> {
        let size_ratio = large_input_size as f64 / small_input_size as f64;
        let time_ratio = large_duration.as_secs_f64() / small_duration.as_secs_f64();
        
        // Allow up to 2x linear scaling (sub-quadratic)
        let max_acceptable_ratio = size_ratio * 2.0;
        
        if time_ratio > max_acceptable_ratio {
            return Err(format!(
                "{} scales poorly: {}x time increase for {}x size increase (max acceptable: {}x)",
                operation_name, time_ratio, size_ratio, max_acceptable_ratio
            ));
        }
        
        Ok(())
    }
    
    /// Assert that cached operations are faster
    pub fn assert_caching_improves_performance(
        first_run_duration: Duration,
        cached_run_duration: Duration,
        min_improvement_factor: f64,
        operation_name: &str,
    ) -> Result<(), String> {
        let improvement_factor = first_run_duration.as_secs_f64() / cached_run_duration.as_secs_f64();
        
        if improvement_factor < min_improvement_factor {
            return Err(format!(
                "{} caching provides {}x improvement, expected at least {}x",
                operation_name, improvement_factor, min_improvement_factor
            ));
        }
        
        Ok(())
    }
    
    /// Assert consistent performance across runs
    pub fn assert_consistent_performance(
        durations: &[Duration],
        max_variance_percent: f64,
        operation_name: &str,
    ) -> Result<(), String> {
        if durations.is_empty() {
            return Err("No durations provided for consistency check".to_string());
        }
        
        let avg_duration = durations.iter().sum::<Duration>().as_secs_f64() / durations.len() as f64;
        
        for (i, duration) in durations.iter().enumerate() {
            let variance = (duration.as_secs_f64() - avg_duration).abs() / avg_duration;
            if variance > max_variance_percent / 100.0 {
                return Err(format!(
                    "{} run {} has {:.1}% variance from average, max allowed: {:.1}%",
                    operation_name, i + 1, variance * 100.0, max_variance_percent
                ));
            }
        }
        
        Ok(())
    }
}

/// Security assertion helpers
pub struct SecurityMatchers;

impl SecurityMatchers {
    /// Assert that sensitive data is not logged
    pub fn assert_no_sensitive_data_in_logs(
        log_content: &str,
        sensitive_patterns: &[&str],
    ) -> Result<(), String> {
        for pattern in sensitive_patterns {
            if log_content.contains(pattern) {
                return Err(format!("Sensitive data '{}' found in logs", pattern));
            }
        }
        Ok(())
    }
    
    /// Assert that security headers are present
    pub fn assert_security_headers_present(
        headers: &std::collections::HashMap<String, String>,
        required_headers: &[&str],
    ) -> Result<(), String> {
        for header in required_headers {
            if !headers.contains_key(*header) {
                return Err(format!("Required security header '{}' missing", header));
            }
        }
        Ok(())
    }
    
    /// Assert that input validation rejects malicious input
    pub fn assert_rejects_malicious_input<F, T>(
        validator: F,
        malicious_inputs: &[&str],
    ) -> Result<(), String>
    where
        F: Fn(&str) -> Result<T, String>,
    {
        for input in malicious_inputs {
            match validator(input) {
                Ok(_) => return Err(format!("Validator accepted malicious input: {}", input)),
                Err(_) => {} // Expected rejection
            }
        }
        Ok(())
    }
    
    /// Assert that rate limiting is enforced
    pub fn assert_rate_limiting_enforced<F>(
        operation: F,
        requests_per_window: usize,
        window_duration: Duration,
    ) -> Result<(), String>
    where
        F: Fn() -> Result<(), String>,
    {
        let start = std::time::Instant::now();
        let mut successful_requests = 0;
        let mut rate_limited_requests = 0;
        
        // Try to exceed rate limit
        for _ in 0..(requests_per_window * 2) {
            match operation() {
                Ok(_) => successful_requests += 1,
                Err(err) if err.contains("rate limit") => rate_limited_requests += 1,
                Err(err) => return Err(format!("Unexpected error: {}", err)),
            }
            
            // Stop if we've used up the time window
            if start.elapsed() > window_duration {
                break;
            }
        }
        
        if rate_limited_requests == 0 {
            return Err("No rate limiting detected".to_string());
        }
        
        if successful_requests > requests_per_window {
            return Err(format!(
                "Rate limiting ineffective: {} requests succeeded, limit: {}",
                successful_requests, requests_per_window
            ));
        }
        
        Ok(())
    }
}

/// Language-specific matchers
pub struct LanguageMatchers;

impl LanguageMatchers {
    /// Assert that language detection is accurate
    pub fn assert_language_detection_accurate(
        detected_language: &str,
        expected_language: &str,
        confidence: f64,
        min_confidence: f64,
    ) -> Result<(), String> {
        if detected_language != expected_language {
            return Err(format!(
                "Language detection incorrect: detected '{}', expected '{}'",
                detected_language, expected_language
            ));
        }
        
        if confidence < min_confidence {
            return Err(format!(
                "Language detection confidence too low: {:.2}, minimum: {:.2}",
                confidence, min_confidence
            ));
        }
        
        Ok(())
    }
    
    /// Assert that parsing produces valid AST
    pub fn assert_valid_ast_structure(
        ast_json: &serde_json::Value,
        required_nodes: &[&str],
    ) -> Result<(), String> {
        // Check that AST has expected structure
        if !ast_json.is_object() {
            return Err("AST root is not an object".to_string());
        }
        
        for node_type in required_nodes {
            if !Self::ast_contains_node_type(ast_json, node_type) {
                return Err(format!("AST missing required node type: {}", node_type));
            }
        }
        
        Ok(())
    }
    
    fn ast_contains_node_type(ast: &serde_json::Value, node_type: &str) -> bool {
        match ast {
            serde_json::Value::Object(obj) => {
                if let Some(type_val) = obj.get("type") {
                    if type_val.as_str() == Some(node_type) {
                        return true;
                    }
                }
                // Recursively search children
                obj.values().any(|child| Self::ast_contains_node_type(child, node_type))
            }
            serde_json::Value::Array(arr) => {
                arr.iter().any(|child| Self::ast_contains_node_type(child, node_type))
            }
            _ => false,
        }
    }
}

// Result types for matchers
#[derive(Debug, Clone)]
pub struct VulnerabilityResult {
    pub vulnerability_type: String,
    pub severity: String,
    pub line_number: u32,
    pub confidence: f64,
}

#[derive(Debug, Clone)]
pub struct SecretResult {
    pub secret_type: String,
    pub masked_value: String,
    pub line_number: u32,
    pub confidence: f64,
}

/// Macro for creating custom assertions
#[macro_export]
macro_rules! assert_analysis_result {
    ($result:expr, contains_patterns: $patterns:expr) => {
        $crate::common::matchers::AnalysisMatchers::assert_contains_patterns($result, $patterns)
            .map_err(|e| format!("Assertion failed: {}", e))?;
    };
    
    ($result:expr, finds_vulnerabilities: $vuln_types:expr) => {
        $crate::common::matchers::AnalysisMatchers::assert_finds_vulnerabilities($result, $vuln_types)
            .map_err(|e| format!("Assertion failed: {}", e))?;
    };
    
    ($result:expr, finds_secrets: $secret_types:expr) => {
        $crate::common::matchers::AnalysisMatchers::assert_finds_secrets($result, $secret_types)
            .map_err(|e| format!("Assertion failed: {}", e))?;
    };
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;
    
    #[test]
    fn test_pattern_assertion() {
        let results = vec!["found pattern A".to_string(), "found pattern B".to_string()];
        let expected = &["pattern A", "pattern B"];
        
        assert!(AnalysisMatchers::assert_contains_patterns(&results, expected).is_ok());
        
        let missing = &["pattern A", "pattern C"];
        assert!(AnalysisMatchers::assert_contains_patterns(&results, missing).is_err());
    }
    
    #[test]
    fn test_performance_assertion() {
        let operation = || {
            std::thread::sleep(Duration::from_millis(10));
            "result"
        };
        
        let result = AnalysisMatchers::assert_completes_within(
            operation,
            Duration::from_millis(50),
            "test operation"
        );
        
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "result");
    }
    
    #[test]
    fn test_memory_assertion() {
        let result = AnalysisMatchers::assert_memory_usage_bounded(
            1000, // before
            1500, // after  
            1000, // max increase
            "test operation"
        );
        
        assert!(result.is_ok());
        
        let excessive_usage = AnalysisMatchers::assert_memory_usage_bounded(
            1000, // before
            3000, // after
            1000, // max increase  
            "test operation"
        );
        
        assert!(excessive_usage.is_err());
    }
    
    #[test]
    fn test_scaling_assertion() {
        let result = PerformanceMatchers::assert_sub_linear_scaling(
            100,                            // small input
            Duration::from_millis(10),      // small duration
            1000,                           // large input (10x)
            Duration::from_millis(150),     // large duration (15x - acceptable)
            "test operation"
        );
        
        assert!(result.is_ok());
        
        let poor_scaling = PerformanceMatchers::assert_sub_linear_scaling(
            100,                            // small input
            Duration::from_millis(10),      // small duration
            1000,                           // large input (10x)
            Duration::from_millis(1000),    // large duration (100x - poor)
            "test operation"
        );
        
        assert!(poor_scaling.is_err());
    }
    
    #[test]
    fn test_security_headers_assertion() {
        let mut headers = HashMap::new();
        headers.insert("X-Content-Type-Options".to_string(), "nosniff".to_string());
        headers.insert("X-Frame-Options".to_string(), "DENY".to_string());
        
        let required = &["X-Content-Type-Options", "X-Frame-Options"];
        let result = SecurityMatchers::assert_security_headers_present(&headers, required);
        assert!(result.is_ok());
        
        let missing_required = &["X-Content-Type-Options", "X-XSS-Protection"];
        let missing_result = SecurityMatchers::assert_security_headers_present(&headers, missing_required);
        assert!(missing_result.is_err());
    }
    
    #[test]
    fn test_language_detection_assertion() {
        let result = LanguageMatchers::assert_language_detection_accurate(
            "rust",
            "rust", 
            0.95,
            0.8
        );
        assert!(result.is_ok());
        
        let low_confidence = LanguageMatchers::assert_language_detection_accurate(
            "rust",
            "rust",
            0.5,
            0.8
        );
        assert!(low_confidence.is_err());
    }
}