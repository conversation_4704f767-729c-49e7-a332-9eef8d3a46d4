// Mock Factory - Enhanced Mock Service Creation
// Provides sophisticated mocking capabilities for all external dependencies

use std::sync::Arc;
use std::time::Duration;
use std::collections::HashMap;
use tokio::sync::RwLock;
use super::config::TestConfig;

/// Central factory for creating mock services
pub struct MockFactory {
    config: &'static TestConfig,
}

impl MockFactory {
    pub fn new() -> Self {
        Self {
            config: TestConfig::load(),
        }
    }
    
    /// Create a complete mock service ecosystem
    pub fn create_mock_ecosystem(&self) -> MockEcosystem {
        MockEcosystem {
            ai_service: Arc::new(self.create_ai_service_mock()),
            database_service: Arc::new(self.create_database_mock()),
            storage_service: Arc::new(self.create_storage_mock()),
            cache_service: Arc::new(self.create_cache_mock()),
            circuit_breaker: Arc::new(self.create_circuit_breaker_mock()),
            metrics_service: Arc::new(self.create_metrics_mock()),
        }
    }
    
    /// Create AI service mock with configurable behavior
    pub fn create_ai_service_mock(&self) -> MockAiService {
        MockAiService::new(MockAiConfig {
            response_delay: Duration::from_millis(self.config.mock_services.ai_response_delay_ms),
            confidence_threshold: self.config.mock_services.ai_confidence_threshold,
            failure_rate: self.config.mock_services.ai_failure_rate,
            embeddings_enabled: true,
            analysis_enabled: true,
        })
    }
    
    /// Create database mock with transaction support
    pub fn create_database_mock(&self) -> MockDatabaseService {
        MockDatabaseService::new(MockDatabaseConfig {
            connection_timeout: Duration::from_millis(self.config.mock_services.db_connection_timeout_ms),
            query_timeout: Duration::from_millis(self.config.mock_services.db_query_timeout_ms),
            failure_rate: self.config.mock_services.db_failure_rate,
            transaction_support: true,
            data_persistence: false, // Use in-memory for tests
        })
    }
    
    /// Create storage service mock
    pub fn create_storage_mock(&self) -> MockStorageService {
        MockStorageService::new(MockStorageConfig {
            upload_delay: Duration::from_millis(50),
            download_delay: Duration::from_millis(30),
            failure_rate: 0.01,
            storage_limit_mb: 1000,
        })
    }
    
    /// Create cache service mock
    pub fn create_cache_mock(&self) -> MockCacheService {
        MockCacheService::new(MockCacheConfig {
            hit_rate: 0.8,
            response_delay: Duration::from_millis(5),
            max_entries: 10000,
            ttl_seconds: 3600,
        })
    }
    
    /// Create circuit breaker mock
    pub fn create_circuit_breaker_mock(&self) -> MockCircuitBreaker {
        MockCircuitBreaker::new(MockCircuitBreakerConfig {
            failure_threshold: self.config.mock_services.circuit_breaker_failure_threshold,
            timeout: Duration::from_secs(self.config.mock_services.circuit_breaker_timeout_seconds),
            half_open_max_calls: self.config.mock_services.circuit_breaker_half_open_max_calls,
        })
    }
    
    /// Create metrics service mock
    pub fn create_metrics_mock(&self) -> MockMetricsService {
        MockMetricsService::new(MockMetricsConfig {
            collection_interval: Duration::from_secs(10),
            retention_days: 7,
            export_enabled: false,
        })
    }
}

/// Complete mock ecosystem
#[derive(Clone)]
pub struct MockEcosystem {
    pub ai_service: Arc<MockAiService>,
    pub database_service: Arc<MockDatabaseService>,
    pub storage_service: Arc<MockStorageService>,
    pub cache_service: Arc<MockCacheService>,
    pub circuit_breaker: Arc<MockCircuitBreaker>,
    pub metrics_service: Arc<MockMetricsService>,
}

// AI Service Mock
#[derive(Debug)]
pub struct MockAiConfig {
    pub response_delay: Duration,
    pub confidence_threshold: f64,
    pub failure_rate: f64,
    pub embeddings_enabled: bool,
    pub analysis_enabled: bool,
}

pub struct MockAiService {
    config: MockAiConfig,
    call_count: Arc<RwLock<u64>>,
    embeddings_cache: Arc<RwLock<HashMap<String, Vec<f32>>>>,
}

impl MockAiService {
    pub fn new(config: MockAiConfig) -> Self {
        Self {
            config,
            call_count: Arc::new(RwLock::new(0)),
            embeddings_cache: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    pub async fn generate_embeddings(&self, text: &str) -> Result<Vec<f32>, MockAiError> {
        self.simulate_delay().await;
        self.increment_call_count().await;
        
        if self.should_fail().await {
            return Err(MockAiError::ServiceUnavailable);
        }
        
        // Check cache first
        {
            let cache = self.embeddings_cache.read().await;
            if let Some(cached) = cache.get(text) {
                return Ok(cached.clone());
            }
        }
        
        // Generate mock embeddings (deterministic for testing)
        let embeddings = self.generate_mock_embeddings(text);
        
        // Cache the result
        {
            let mut cache = self.embeddings_cache.write().await;
            cache.insert(text.to_string(), embeddings.clone());
        }
        
        Ok(embeddings)
    }
    
    pub async fn analyze_code(&self, code: &str, analysis_type: &str) -> Result<AiAnalysisResponse, MockAiError> {
        self.simulate_delay().await;
        self.increment_call_count().await;
        
        if self.should_fail().await {
            return Err(MockAiError::AnalysisError("Mock analysis failure".to_string()));
        }
        
        Ok(AiAnalysisResponse {
            analysis_type: analysis_type.to_string(),
            confidence: self.calculate_mock_confidence(code),
            patterns: self.generate_mock_patterns(code),
            suggestions: self.generate_mock_suggestions(code),
            complexity_score: self.calculate_mock_complexity(code),
        })
    }
    
    pub async fn get_call_count(&self) -> u64 {
        *self.call_count.read().await
    }
    
    pub async fn reset_state(&self) {
        *self.call_count.write().await = 0;
        self.embeddings_cache.write().await.clear();
    }
    
    // Private helper methods
    async fn simulate_delay(&self) {
        tokio::time::sleep(self.config.response_delay).await;
    }
    
    async fn increment_call_count(&self) {
        *self.call_count.write().await += 1;
    }
    
    async fn should_fail(&self) -> bool {
        use rand::Rng;
        let mut rng = rand::thread_rng();
        rng.gen::<f64>() < self.config.failure_rate
    }
    
    fn generate_mock_embeddings(&self, text: &str) -> Vec<f32> {
        // Generate deterministic embeddings based on text hash
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        text.hash(&mut hasher);
        let hash = hasher.finish();
        
        // Generate 384-dimensional embeddings (typical size)
        (0..384)
            .map(|i| {
                let seed = hash.wrapping_add(i as u64);
                ((seed % 10000) as f32 - 5000.0) / 10000.0 // Normalize to [-0.5, 0.5]
            })
            .collect()
    }
    
    fn calculate_mock_confidence(&self, code: &str) -> f64 {
        // Mock confidence based on code length and complexity
        let base_confidence = 0.7;
        let length_factor = (code.len() as f64 / 1000.0).min(1.0) * 0.2;
        let complexity_factor = code.lines().count() as f64 / 100.0 * 0.1;
        
        (base_confidence + length_factor + complexity_factor).min(1.0)
    }
    
    fn generate_mock_patterns(&self, code: &str) -> Vec<String> {
        let mut patterns = Vec::new();
        
        if code.contains("function") || code.contains("fn ") {
            patterns.push("function_definition".to_string());
        }
        if code.contains("class") || code.contains("struct") {
            patterns.push("type_definition".to_string());
        }
        if code.contains("if ") || code.contains("match") {
            patterns.push("conditional_logic".to_string());
        }
        if code.contains("for ") || code.contains("while") {
            patterns.push("loop_construct".to_string());
        }
        
        patterns
    }
    
    fn generate_mock_suggestions(&self, code: &str) -> Vec<String> {
        let mut suggestions = Vec::new();
        
        if code.len() > 1000 {
            suggestions.push("Consider breaking large functions into smaller ones".to_string());
        }
        if code.lines().count() > 50 {
            suggestions.push("Function might be too long, consider refactoring".to_string());
        }
        if !code.contains("//") && !code.contains("/*") {
            suggestions.push("Consider adding code comments for better maintainability".to_string());
        }
        
        suggestions
    }
    
    fn calculate_mock_complexity(&self, code: &str) -> u32 {
        // Simple cyclomatic complexity estimation
        let mut complexity = 1; // Base complexity
        
        complexity += code.matches("if ").count() as u32;
        complexity += code.matches("else").count() as u32;
        complexity += code.matches("for ").count() as u32;
        complexity += code.matches("while").count() as u32;
        complexity += code.matches("match").count() as u32;
        complexity += code.matches("catch").count() as u32;
        
        complexity
    }
}

// Database Service Mock
#[derive(Debug)]
pub struct MockDatabaseConfig {
    pub connection_timeout: Duration,
    pub query_timeout: Duration,
    pub failure_rate: f64,
    pub transaction_support: bool,
    pub data_persistence: bool,
}

pub struct MockDatabaseService {
    config: MockDatabaseConfig,
    data: Arc<RwLock<HashMap<String, serde_json::Value>>>,
    transaction_count: Arc<RwLock<u64>>,
}

impl MockDatabaseService {
    pub fn new(config: MockDatabaseConfig) -> Self {
        Self {
            config,
            data: Arc::new(RwLock::new(HashMap::new())),
            transaction_count: Arc::new(RwLock::new(0)),
        }
    }
    
    pub async fn execute_query(&self, query: &str) -> Result<QueryResult, MockDatabaseError> {
        self.simulate_query_delay().await;
        
        if self.should_fail().await {
            return Err(MockDatabaseError::ConnectionTimeout);
        }
        
        // Parse mock query and return appropriate result
        if query.contains("INSERT") {
            self.handle_insert(query).await
        } else if query.contains("SELECT") {
            self.handle_select(query).await
        } else if query.contains("UPDATE") {
            self.handle_update(query).await
        } else if query.contains("DELETE") {
            self.handle_delete(query).await
        } else {
            Err(MockDatabaseError::InvalidQuery(query.to_string()))
        }
    }
    
    pub async fn begin_transaction(&self) -> Result<MockTransaction, MockDatabaseError> {
        *self.transaction_count.write().await += 1;
        Ok(MockTransaction::new())
    }
    
    // Private methods
    async fn simulate_query_delay(&self) {
        let delay = Duration::from_millis(10); // Fast for tests
        tokio::time::sleep(delay).await;
    }
    
    async fn should_fail(&self) -> bool {
        use rand::Rng;
        let mut rng = rand::thread_rng();
        rng.gen::<f64>() < self.config.failure_rate
    }
    
    async fn handle_insert(&self, _query: &str) -> Result<QueryResult, MockDatabaseError> {
        Ok(QueryResult {
            rows_affected: 1,
            last_insert_id: Some(42),
            data: Vec::new(),
        })
    }
    
    async fn handle_select(&self, query: &str) -> Result<QueryResult, MockDatabaseError> {
        // Mock SELECT behavior
        let data = if query.contains("analyses") {
            vec![
                serde_json::json!({
                    "id": "analysis-1",
                    "status": "completed",
                    "created_at": "2023-01-01T00:00:00Z"
                })
            ]
        } else {
            Vec::new()
        };
        
        Ok(QueryResult {
            rows_affected: data.len(),
            last_insert_id: None,
            data,
        })
    }
    
    async fn handle_update(&self, _query: &str) -> Result<QueryResult, MockDatabaseError> {
        Ok(QueryResult {
            rows_affected: 1,
            last_insert_id: None,
            data: Vec::new(),
        })
    }
    
    async fn handle_delete(&self, _query: &str) -> Result<QueryResult, MockDatabaseError> {
        Ok(QueryResult {
            rows_affected: 1,
            last_insert_id: None,
            data: Vec::new(),
        })
    }
}

// Storage Service Mock
#[derive(Debug)]
pub struct MockStorageConfig {
    pub upload_delay: Duration,
    pub download_delay: Duration,
    pub failure_rate: f64,
    pub storage_limit_mb: u64,
}

pub struct MockStorageService {
    config: MockStorageConfig,
    stored_files: Arc<RwLock<HashMap<String, Vec<u8>>>>,
}

impl MockStorageService {
    pub fn new(config: MockStorageConfig) -> Self {
        Self {
            config,
            stored_files: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    pub async fn upload_file(&self, path: &str, data: &[u8]) -> Result<(), MockStorageError> {
        tokio::time::sleep(self.config.upload_delay).await;
        
        if self.should_fail().await {
            return Err(MockStorageError::UploadFailed);
        }
        
        let mut files = self.stored_files.write().await;
        files.insert(path.to_string(), data.to_vec());
        Ok(())
    }
    
    pub async fn download_file(&self, path: &str) -> Result<Vec<u8>, MockStorageError> {
        tokio::time::sleep(self.config.download_delay).await;
        
        if self.should_fail().await {
            return Err(MockStorageError::DownloadFailed);
        }
        
        let files = self.stored_files.read().await;
        files.get(path)
            .cloned()
            .ok_or(MockStorageError::FileNotFound)
    }
    
    async fn should_fail(&self) -> bool {
        use rand::Rng;
        let mut rng = rand::thread_rng();
        rng.gen::<f64>() < self.config.failure_rate
    }
}

// Additional mock services (Cache, Circuit Breaker, Metrics) with similar patterns...

// Result and Error types
#[derive(Debug, Clone)]
pub struct AiAnalysisResponse {
    pub analysis_type: String,
    pub confidence: f64,
    pub patterns: Vec<String>,
    pub suggestions: Vec<String>,
    pub complexity_score: u32,
}

#[derive(Debug)]
pub struct QueryResult {
    pub rows_affected: usize,
    pub last_insert_id: Option<i64>,
    pub data: Vec<serde_json::Value>,
}

#[derive(Debug)]
pub struct MockTransaction {
    pub id: String,
}

impl MockTransaction {
    fn new() -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
        }
    }
}

// Error types
#[derive(Debug, thiserror::Error)]
pub enum MockAiError {
    #[error("AI service unavailable")]
    ServiceUnavailable,
    #[error("Analysis error: {0}")]
    AnalysisError(String),
}

#[derive(Debug, thiserror::Error)]
pub enum MockDatabaseError {
    #[error("Connection timeout")]
    ConnectionTimeout,
    #[error("Invalid query: {0}")]
    InvalidQuery(String),
}

#[derive(Debug, thiserror::Error)]
pub enum MockStorageError {
    #[error("Upload failed")]
    UploadFailed,
    #[error("Download failed")]
    DownloadFailed,
    #[error("File not found")]
    FileNotFound,
}

// Placeholder for remaining mock services
pub struct MockCacheService {
    config: MockCacheConfig,
}

pub struct MockCircuitBreaker {
    config: MockCircuitBreakerConfig,
}

pub struct MockMetricsService {
    config: MockMetricsConfig,
}

#[derive(Debug)]
pub struct MockCacheConfig {
    pub hit_rate: f64,
    pub response_delay: Duration,
    pub max_entries: usize,
    pub ttl_seconds: u64,
}

#[derive(Debug)]
pub struct MockCircuitBreakerConfig {
    pub failure_threshold: u32,
    pub timeout: Duration,
    pub half_open_max_calls: u32,
}

#[derive(Debug)]
pub struct MockMetricsConfig {
    pub collection_interval: Duration,
    pub retention_days: u32,
    pub export_enabled: bool,
}

impl MockCacheService {
    pub fn new(config: MockCacheConfig) -> Self {
        Self { config }
    }
}

impl MockCircuitBreaker {
    pub fn new(config: MockCircuitBreakerConfig) -> Self {
        Self { config }
    }
}

impl MockMetricsService {
    pub fn new(config: MockMetricsConfig) -> Self {
        Self { config }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_mock_factory_creation() {
        let factory = MockFactory::new();
        let ecosystem = factory.create_mock_ecosystem();
        
        assert!(ecosystem.ai_service.get_call_count().await == 0);
    }
    
    #[tokio::test]
    async fn test_ai_service_mock() {
        let factory = MockFactory::new();
        let ai_service = factory.create_ai_service_mock();
        
        let embeddings = ai_service.generate_embeddings("test code").await;
        assert!(embeddings.is_ok());
        assert_eq!(embeddings.unwrap().len(), 384);
        assert_eq!(ai_service.get_call_count().await, 1);
        
        let analysis = ai_service.analyze_code("fn main() {}", "complexity").await;
        assert!(analysis.is_ok());
        assert_eq!(ai_service.get_call_count().await, 2);
    }
    
    #[tokio::test]
    async fn test_database_mock() {
        let factory = MockFactory::new();
        let db_service = factory.create_database_mock();
        
        let result = db_service.execute_query("SELECT * FROM analyses").await;
        assert!(result.is_ok());
        
        let query_result = result.unwrap();
        assert!(query_result.data.len() > 0);
    }
    
    #[tokio::test]
    async fn test_storage_mock() {
        let factory = MockFactory::new();
        let storage_service = factory.create_storage_mock();
        
        let test_data = b"test file content";
        let upload_result = storage_service.upload_file("test.txt", test_data).await;
        assert!(upload_result.is_ok());
        
        let download_result = storage_service.download_file("test.txt").await;
        assert!(download_result.is_ok());
        assert_eq!(download_result.unwrap(), test_data);
    }
}