// Property-Based Test Generators
// Advanced test data generation for comprehensive edge case testing

use proptest::prelude::*;
use fake::{Fake, Faker, faker::lorem::en::*};
use fake::faker::internet::en::*;
use fake::faker::name::en::*;
use super::config::TestConfig;

/// Code content generators for property-based testing
pub struct CodeGenerators;

impl CodeGenerators {
    /// Generate valid Rust code snippets
    pub fn rust_code() -> impl Strategy<Value = String> {
        prop_oneof![
            Self::rust_function(),
            Self::rust_struct(),
            Self::rust_impl_block(),
            Self::rust_use_statements(),
        ]
    }
    
    /// Generate valid JavaScript code snippets  
    pub fn javascript_code() -> impl Strategy<Value = String> {
        prop_oneof![
            Self::js_function(),
            Self::js_class(),
            Self::js_object(),
            Self::js_import_export(),
        ]
    }
    
    /// Generate code with potential vulnerabilities
    pub fn vulnerable_code() -> impl Strategy<Value = String> {
        prop_oneof![
            Self::sql_injection_vulnerable(),
            Self::xss_vulnerable(), 
            Self::path_traversal_vulnerable(),
            Self::command_injection_vulnerable(),
        ]
    }
    
    /// Generate code with secrets
    pub fn code_with_secrets() -> impl Strategy<Value = String> {
        prop_oneof![
            Self::aws_key_in_code(),
            Self::jwt_token_in_code(),
            Self::api_key_in_code(),
            Self::database_url_in_code(),
        ]
    }
    
    // Rust code generators
    fn rust_function() -> impl Strategy<Value = String> {
        (
            "[a-z][a-z0-9_]{2,20}",           // function name
            prop::collection::vec("\\w+", 0..5), // parameters
            "[a-zA-Z0-9 +\\-*/;.(){}\\[\\]\"']*" // body
        ).prop_map(|(name, params, body)| {
            format!(
                "fn {}({}) {{\n    {}\n}}",
                name,
                params.join(", "),
                body
            )
        })
    }
    
    fn rust_struct() -> impl Strategy<Value = String> {
        (
            "[A-Z][a-zA-Z0-9]{2,20}",        // struct name
            prop::collection::vec(
                ("[a-z][a-z0-9_]*", "[A-Za-z0-9<>]+"), // field name, type
                1..10
            )
        ).prop_map(|(name, fields)| {
            let field_defs: Vec<String> = fields
                .into_iter()
                .map(|(field_name, field_type)| format!("    pub {}: {},", field_name, field_type))
                .collect();
            
            format!(
                "#[derive(Debug, Clone)]\npub struct {} {{\n{}\n}}",
                name,
                field_defs.join("\n")
            )
        })
    }
    
    fn rust_impl_block() -> impl Strategy<Value = String> {
        (
            "[A-Z][a-zA-Z0-9]{2,20}",        // struct name
            prop::collection::vec(
                "[a-z][a-z0-9_]{2,20}",      // method names
                1..5
            )
        ).prop_map(|(struct_name, methods)| {
            let method_defs: Vec<String> = methods
                .into_iter()
                .map(|method| format!("    pub fn {}(&self) {{\n        // TODO\n    }}", method))
                .collect();
            
            format!(
                "impl {} {{\n{}\n}}",
                struct_name,
                method_defs.join("\n\n")
            )
        })
    }
    
    fn rust_use_statements() -> impl Strategy<Value = String> {
        prop::collection::vec(
            "[a-z][a-z0-9_:]*",              // module path
            1..10
        ).prop_map(|modules| {
            modules
                .into_iter()
                .map(|module| format!("use {};", module))
                .collect::<Vec<_>>()
                .join("\n")
        })
    }
    
    // JavaScript code generators
    fn js_function() -> impl Strategy<Value = String> {
        (
            "[a-z][a-zA-Z0-9]{2,20}",        // function name
            prop::collection::vec("[a-z]+", 0..5), // parameters
            "[a-zA-Z0-9 +\\-*/;.(){}\\[\\]\"']*" // body
        ).prop_map(|(name, params, body)| {
            format!(
                "function {}({}) {{\n    {}\n}}",
                name,
                params.join(", "),
                body
            )
        })
    }
    
    fn js_class() -> impl Strategy<Value = String> {
        (
            "[A-Z][a-zA-Z0-9]{2,20}",        // class name
            prop::collection::vec("[a-z]+", 1..5) // methods
        ).prop_map(|(name, methods)| {
            let method_defs: Vec<String> = methods
                .into_iter()
                .map(|method| format!("  {}() {{\n    // TODO\n  }}", method))
                .collect();
            
            format!(
                "class {} {{\n{}\n}}",
                name,
                method_defs.join("\n\n")
            )
        })
    }
    
    fn js_object() -> impl Strategy<Value = String> {
        prop::collection::vec(
            ("[a-z]+", "[a-zA-Z0-9\"']*"), // key, value
            1..10
        ).prop_map(|pairs| {
            let props: Vec<String> = pairs
                .into_iter()
                .map(|(key, value)| format!("  {}: \"{}\"", key, value))
                .collect();
            
            format!("const obj = {{\n{}\n}};", props.join(",\n"))
        })
    }
    
    fn js_import_export() -> impl Strategy<Value = String> {
        prop_oneof![
            "[a-z][a-zA-Z0-9./]*".prop_map(|path| format!("import {{ something }} from '{}';", path)),
            "[a-z][a-zA-Z0-9]*".prop_map(|name| format!("export const {} = 'value';", name)),
        ]
    }
    
    // Vulnerability generators
    fn sql_injection_vulnerable() -> impl Strategy<Value = String> {
        "[a-zA-Z0-9_]+".prop_map(|table| {
            format!(
                "query = \"SELECT * FROM {} WHERE id = \" + userId;",
                table
            )
        })
    }
    
    fn xss_vulnerable() -> impl Strategy<Value = String> {
        "[a-zA-Z0-9_]+".prop_map(|var| {
            format!(
                "document.innerHTML = {}; // XSS vulnerability",
                var
            )
        })
    }
    
    fn path_traversal_vulnerable() -> impl Strategy<Value = String> {
        "[a-zA-Z0-9_]+".prop_map(|filename| {
            format!(
                "file_path = base_path + \"/\" + {}; // Path traversal",
                filename
            )
        })
    }
    
    fn command_injection_vulnerable() -> impl Strategy<Value = String> {
        "[a-zA-Z0-9_]+".prop_map(|cmd| {
            format!(
                "subprocess.call(\"rm -rf \" + {}) # Command injection",
                cmd
            )
        })
    }
    
    // Secret generators
    fn aws_key_in_code() -> impl Strategy<Value = String> {
        "AKIA[A-Z0-9]{16}".prop_map(|key| {
            format!("const AWS_ACCESS_KEY = \"{}\";", key)
        })
    }
    
    fn jwt_token_in_code() -> impl Strategy<Value = String> {
        "eyJ[A-Za-z0-9\\-_]+\\.[A-Za-z0-9\\-_]+\\.[A-Za-z0-9\\-_]+".prop_map(|token| {
            format!("const JWT_TOKEN = \"{}\";", token)
        })
    }
    
    fn api_key_in_code() -> impl Strategy<Value = String> {
        "[a-zA-Z0-9]{32}".prop_map(|key| {
            format!("api_key = \"{}\"", key)
        })
    }
    
    fn database_url_in_code() -> impl Strategy<Value = String> {
        (
            "[a-z]+",                        // username
            "[a-zA-Z0-9]+",                  // password
            "[a-z]+\\.[a-z]+\\.[a-z]+",      // host
            "[a-z_]+",                       // database
        ).prop_map(|(user, pass, host, db)| {
            format!("DATABASE_URL = \"postgres://{}:{}@{}/{}\"", user, pass, host, db)
        })
    }
}

/// File structure generators
pub struct FileGenerators;

impl FileGenerators {
    /// Generate realistic file paths
    pub fn file_path() -> impl Strategy<Value = String> {
        (
            prop::collection::vec("[a-z]+", 1..5),    // directories
            "[a-z]+",                                 // filename
            prop_oneof!["rs", "js", "py", "java", "go", "cpp", "h"] // extension
        ).prop_map(|(dirs, name, ext)| {
            format!("{}/{}.{}", dirs.join("/"), name, ext)
        })
    }
    
    /// Generate repository structures
    pub fn repository_structure() -> impl Strategy<Value = Vec<String>> {
        prop::collection::vec(Self::file_path(), 1..100)
    }
    
    /// Generate file with size constraints
    pub fn file_content(min_size: usize, max_size: usize) -> impl Strategy<Value = String> {
        prop::collection::vec(
            "[a-zA-Z0-9 \\n\\t\\r!@#$%^&*()\\[\\]{};:'\",.<>?/]*",
            min_size..max_size
        ).prop_map(|lines| lines.join("\n"))
    }
}

/// Analysis request generators
pub struct AnalysisGenerators;

impl AnalysisGenerators {
    /// Generate valid analysis requests
    pub fn analysis_request() -> impl Strategy<Value = serde_json::Value> {
        (
            "https://github\\.com/[a-z]+/[a-z]+",    // repository URL
            prop::option::of("[a-z]+"),              // branch
            prop::collection::vec("[a-z]+", 0..10),  // patterns
            prop::collection::vec("[a-z]+", 0..5),   // languages
        ).prop_map(|(repo_url, branch, patterns, languages)| {
            serde_json::json!({
                "repository_url": repo_url,
                "branch": branch,
                "patterns": patterns,
                "languages": languages
            })
        })
    }
    
    /// Generate edge case analysis requests
    pub fn edge_case_analysis_request() -> impl Strategy<Value = serde_json::Value> {
        prop_oneof![
            // Empty request
            Just(serde_json::json!({})),
            // Missing fields
            Just(serde_json::json!({"repository_url": "https://github.com/test/repo"})),
            // Invalid URLs
            "[a-z]+".prop_map(|s| serde_json::json!({"repository_url": s})),
            // Very long inputs
            prop::collection::vec("[a-z]", 10000..20000)
                .prop_map(|s| serde_json::json!({"repository_url": s.join("")})),
        ]
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use proptest::test_runner::TestRunner;
    
    #[test]
    fn test_rust_code_generation() {
        let mut runner = TestRunner::default();
        let strategy = CodeGenerators::rust_code();
        
        for _ in 0..10 {
            let code = strategy.new_tree(&mut runner).unwrap().current();
            assert!(!code.is_empty());
            // Basic validation that it looks like Rust code
            assert!(code.contains("fn ") || code.contains("struct ") || code.contains("impl "));
        }
    }
    
    #[test]
    fn test_vulnerability_generation() {
        let mut runner = TestRunner::default();
        let strategy = CodeGenerators::vulnerable_code();
        
        for _ in 0..10 {
            let code = strategy.new_tree(&mut runner).unwrap().current();
            assert!(!code.is_empty());
            // Should contain vulnerability patterns
            assert!(
                code.contains("SELECT") ||
                code.contains("innerHTML") ||
                code.contains("base_path") ||
                code.contains("subprocess")
            );
        }
    }
    
    #[test] 
    fn test_secret_generation() {
        let mut runner = TestRunner::default();
        let strategy = CodeGenerators::code_with_secrets();
        
        for _ in 0..10 {
            let code = strategy.new_tree(&mut runner).unwrap().current();
            assert!(!code.is_empty());
            // Should contain secret patterns
            assert!(
                code.contains("AKIA") ||
                code.contains("eyJ") ||
                code.contains("api_key") ||
                code.contains("postgres://")
            );
        }
    }
}