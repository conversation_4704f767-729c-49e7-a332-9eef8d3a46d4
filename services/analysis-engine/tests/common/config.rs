// Test Configuration Management
// Loads and manages externalized test configuration

use serde::{Deserialize, Serialize};
use std::sync::OnceLock;

static TEST_CONFIG: OnceLock<TestConfig> = OnceLock::new();

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TestConfig {
    pub performance_targets: PerformanceTargets,
    pub test_data: TestData,
    pub mock_services: MockServicesConfig,
    pub chaos_testing: ChaosTestingConfig,
    pub property_testing: PropertyTestingConfig,
    pub languages: LanguagesConfig,
    pub security_testing: SecurityTestingConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PerformanceTargets {
    pub max_analysis_time_seconds: u64,
    pub max_api_response_ms: u64,
    pub max_memory_usage_gb: u64,
    pub max_concurrent_analyses: u64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TestData {
    pub small_repo_size: u64,
    pub medium_repo_size: u64,
    pub large_repo_size: u64,
    pub vulnerability_patterns_count: u64,
    pub secret_patterns_count: u64,
    pub compliance_frameworks_count: u64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MockServicesConfig {
    pub ai_response_delay_ms: u64,
    pub ai_confidence_threshold: f64,
    pub ai_failure_rate: f64,
    pub db_connection_timeout_ms: u64,
    pub db_query_timeout_ms: u64,
    pub db_failure_rate: f64,
    pub circuit_breaker_failure_threshold: u32,
    pub circuit_breaker_timeout_seconds: u64,
    pub circuit_breaker_half_open_max_calls: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChaosTestingConfig {
    pub network_partition_probability: f64,
    pub service_failure_probability: f64,
    pub high_latency_probability: f64,
    pub memory_pressure_probability: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PropertyTestingConfig {
    pub max_test_cases: u32,
    pub max_shrink_iterations: u32,
    pub max_string_length: usize,
    pub max_array_length: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LanguagesConfig {
    pub priority_languages: Vec<String>,
    pub full_language_support: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityTestingConfig {
    pub vulnerability_test_cases: Vec<String>,
    pub secret_test_cases: Vec<String>,
    pub compliance_frameworks: Vec<String>,
}

impl TestConfig {
    /// Load configuration from file or use defaults
    pub fn load() -> &'static TestConfig {
        TEST_CONFIG.get_or_init(|| {
            Self::load_from_file().unwrap_or_else(|err| {
                eprintln!("Warning: Failed to load test config: {}. Using defaults.", err);
                Self::default()
            })
        })
    }
    
    fn load_from_file() -> Result<TestConfig, Box<dyn std::error::Error>> {
        let config_path = std::path::Path::new("tests/config/test_config.toml");
        let config_content = std::fs::read_to_string(config_path)?;
        let config: TestConfig = toml::from_str(&config_content)?;
        Ok(config)
    }
}

impl Default for TestConfig {
    fn default() -> Self {
        Self {
            performance_targets: PerformanceTargets {
                max_analysis_time_seconds: 300,
                max_api_response_ms: 100,
                max_memory_usage_gb: 4,
                max_concurrent_analyses: 50,
            },
            test_data: TestData {
                small_repo_size: 1000,
                medium_repo_size: 100000,
                large_repo_size: 1000000,
                vulnerability_patterns_count: 850,
                secret_patterns_count: 20,
                compliance_frameworks_count: 8,
            },
            mock_services: MockServicesConfig {
                ai_response_delay_ms: 100,
                ai_confidence_threshold: 0.85,
                ai_failure_rate: 0.01,
                db_connection_timeout_ms: 5000,
                db_query_timeout_ms: 30000,
                db_failure_rate: 0.005,
                circuit_breaker_failure_threshold: 5,
                circuit_breaker_timeout_seconds: 60,
                circuit_breaker_half_open_max_calls: 3,
            },
            chaos_testing: ChaosTestingConfig {
                network_partition_probability: 0.1,
                service_failure_probability: 0.05,
                high_latency_probability: 0.15,
                memory_pressure_probability: 0.1,
            },
            property_testing: PropertyTestingConfig {
                max_test_cases: 1000,
                max_shrink_iterations: 1000,
                max_string_length: 10000,
                max_array_length: 1000,
            },
            languages: LanguagesConfig {
                priority_languages: vec![
                    "rust".to_string(), "python".to_string(), "javascript".to_string(),
                    "typescript".to_string(), "go".to_string(), "java".to_string(),
                    "c".to_string(), "cpp".to_string(),
                ],
                full_language_support: vec![
                    "rust".to_string(), "python".to_string(), "javascript".to_string(),
                    "typescript".to_string(), "go".to_string(), "java".to_string(),
                    "c".to_string(), "cpp".to_string(), "html".to_string(), "css".to_string(),
                    "json".to_string(), "yaml".to_string(), "php".to_string(), "ruby".to_string(),
                    "bash".to_string(), "markdown".to_string(), "swift".to_string(),
                    "kotlin".to_string(), "objc".to_string(), "r".to_string(),
                    "julia".to_string(), "haskell".to_string(), "scala".to_string(),
                    "erlang".to_string(), "elixir".to_string(), "xml".to_string(),
                    "zig".to_string(), "d".to_string(), "lua".to_string(),
                    "ocaml".to_string(), "nix".to_string(),
                ],
            },
            security_testing: SecurityTestingConfig {
                vulnerability_test_cases: vec![
                    "sql_injection".to_string(), "xss".to_string(), "path_traversal".to_string(),
                    "command_injection".to_string(), "buffer_overflow".to_string(),
                    "use_after_free".to_string(), "null_pointer_dereference".to_string(),
                ],
                secret_test_cases: vec![
                    "aws_access_key".to_string(), "jwt_token".to_string(), "api_key".to_string(),
                    "password".to_string(), "private_key".to_string(), "database_url".to_string(),
                    "oauth_token".to_string(), "github_token".to_string(),
                ],
                compliance_frameworks: vec![
                    "PCI_DSS".to_string(), "GDPR".to_string(), "HIPAA".to_string(),
                    "SOC2".to_string(), "ISO27001".to_string(), "NIST".to_string(),
                    "CIS".to_string(), "OWASP".to_string(),
                ],
            },
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_config_loading() {
        let config = TestConfig::load();
        assert!(config.performance_targets.max_analysis_time_seconds > 0);
        assert!(config.languages.priority_languages.len() > 0);
    }
    
    #[test]
    fn test_default_config() {
        let config = TestConfig::default();
        assert_eq!(config.performance_targets.max_analysis_time_seconds, 300);
        assert_eq!(config.languages.priority_languages.len(), 8);
    }
}