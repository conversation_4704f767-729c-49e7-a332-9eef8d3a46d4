// Chaos Engineering Testing Framework
// Tests system resilience under various failure conditions

use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;
use super::config::TestConfig;

/// Chaos engineering test scenarios
#[derive(Debug, Clone)]
pub enum ChaosScenario {
    NetworkPartition { duration: Duration },
    ServiceFailure { service: String, duration: Duration },
    HighLatency { latency_ms: u64, duration: Duration },
    MemoryPressure { memory_mb: u64, duration: Duration },
    DiskSpaceExhaustion { disk_usage_percent: u8 },
    CircuitBreakerTriggered { service: String },
}

/// Chaos engineering test executor
pub struct ChaosTestRunner {
    config: &'static TestConfig,
}

impl ChaosTestRunner {
    pub fn new() -> Self {
        Self {
            config: TestConfig::load(),
        }
    }
    
    /// Execute a chaos scenario and test system resilience
    pub async fn run_chaos_scenario<F, R>(
        &self,
        scenario: ChaosScenario,
        test_operation: F,
    ) -> Result<ChaosTestResult, Box<dyn std::error::Error>>
    where
        F: FnOnce() -> R + Send + 'static,
        R: std::future::Future<Output = Result<(), Box<dyn std::error::Error>>> + Send,
    {
        println!("🌪️  Running chaos scenario: {:?}", scenario);
        
        // Start chaos injection
        let chaos_handle = self.inject_chaos(&scenario).await?;
        
        // Run the test operation under chaos
        let start_time = std::time::Instant::now();
        let test_result = test_operation().await;
        let execution_time = start_time.elapsed();
        
        // Stop chaos injection
        self.stop_chaos(chaos_handle).await?;
        
        // Analyze results
        let result = ChaosTestResult {
            scenario: scenario.clone(),
            test_passed: test_result.is_ok(),
            execution_time,
            error_message: test_result.err().map(|e| e.to_string()),
            resilience_score: self.calculate_resilience_score(&scenario, &test_result, execution_time),
        };
        
        println!("📊 Chaos test result: {:?}", result);
        Ok(result)
    }
    
    /// Inject chaos into the system
    async fn inject_chaos(&self, scenario: &ChaosScenario) -> Result<ChaosHandle, Box<dyn std::error::Error>> {
        match scenario {
            ChaosScenario::NetworkPartition { duration } => {
                self.simulate_network_partition(*duration).await
            },
            ChaosScenario::ServiceFailure { service, duration } => {
                self.simulate_service_failure(service, *duration).await
            },
            ChaosScenario::HighLatency { latency_ms, duration } => {
                self.simulate_high_latency(*latency_ms, *duration).await
            },
            ChaosScenario::MemoryPressure { memory_mb, duration } => {
                self.simulate_memory_pressure(*memory_mb, *duration).await
            },
            ChaosScenario::DiskSpaceExhaustion { disk_usage_percent } => {
                self.simulate_disk_exhaustion(*disk_usage_percent).await
            },
            ChaosScenario::CircuitBreakerTriggered { service } => {
                self.trigger_circuit_breaker(service).await
            },
        }
    }
    
    /// Stop chaos injection
    async fn stop_chaos(&self, handle: ChaosHandle) -> Result<(), Box<dyn std::error::Error>> {
        match handle {
            ChaosHandle::NetworkPartition(task) => {
                task.abort();
                Ok(())
            },
            ChaosHandle::ServiceFailure(task) => {
                task.abort();
                Ok(())
            },
            ChaosHandle::HighLatency(task) => {
                task.abort();
                Ok(())
            },
            ChaosHandle::MemoryPressure(allocations) => {
                // Drop memory allocations
                drop(allocations);
                Ok(())
            },
            ChaosHandle::DiskExhaustion(temp_files) => {
                // Clean up temporary files
                for file in temp_files {
                    let _ = std::fs::remove_file(file);
                }
                Ok(())
            },
            ChaosHandle::CircuitBreaker => {
                // Circuit breaker recovery happens automatically
                Ok(())
            },
        }
    }
    
    // Chaos injection implementations
    
    async fn simulate_network_partition(&self, duration: Duration) -> Result<ChaosHandle, Box<dyn std::error::Error>> {
        let task = tokio::spawn(async move {
            // Simulate network partition by introducing delays/failures
            // In a real implementation, this would modify network traffic
            sleep(duration).await;
        });
        
        Ok(ChaosHandle::NetworkPartition(task))
    }
    
    async fn simulate_service_failure(&self, service: &str, duration: Duration) -> Result<ChaosHandle, Box<dyn std::error::Error>> {
        let service = service.to_string();
        let task = tokio::spawn(async move {
            // Simulate service failure
            // In practice, this would stop/modify service behavior
            println!("🚨 Service {} failed for {:?}", service, duration);
            sleep(duration).await;
            println!("✅ Service {} recovered", service);
        });
        
        Ok(ChaosHandle::ServiceFailure(task))
    }
    
    async fn simulate_high_latency(&self, latency_ms: u64, duration: Duration) -> Result<ChaosHandle, Box<dyn std::error::Error>> {
        let task = tokio::spawn(async move {
            let end_time = std::time::Instant::now() + duration;
            while std::time::Instant::now() < end_time {
                // Introduce artificial delays in critical paths
                sleep(Duration::from_millis(latency_ms)).await;
                sleep(Duration::from_millis(100)).await; // Check interval
            }
        });
        
        Ok(ChaosHandle::HighLatency(task))
    }
    
    async fn simulate_memory_pressure(&self, memory_mb: u64, duration: Duration) -> Result<ChaosHandle, Box<dyn std::error::Error>> {
        // Allocate memory to create pressure
        let mut allocations = Vec::new();
        let chunk_size = 1024 * 1024; // 1MB chunks
        
        for _ in 0..memory_mb {
            let allocation = vec![0u8; chunk_size];
            allocations.push(allocation);
        }
        
        println!("💾 Allocated {}MB for memory pressure test", memory_mb);
        
        // Keep allocations alive for the duration
        tokio::spawn(async move {
            sleep(duration).await;
        });
        
        Ok(ChaosHandle::MemoryPressure(allocations))
    }
    
    async fn simulate_disk_exhaustion(&self, disk_usage_percent: u8) -> Result<ChaosHandle, Box<dyn std::error::Error>> {
        // Create temporary files to consume disk space
        let temp_dir = tempfile::tempdir()?;
        let mut temp_files = Vec::new();
        
        // Calculate approximate file size needed
        let file_size = 10 * 1024 * 1024; // 10MB per file
        let num_files = (disk_usage_percent as usize * 10) / 100; // Simplified calculation
        
        for i in 0..num_files {
            let file_path = temp_dir.path().join(format!("chaos_file_{}.tmp", i));
            let data = vec![0u8; file_size];
            std::fs::write(&file_path, data)?;
            temp_files.push(file_path);
        }
        
        println!("💽 Created {} files for disk exhaustion test", num_files);
        Ok(ChaosHandle::DiskExhaustion(temp_files))
    }
    
    async fn trigger_circuit_breaker(&self, service: &str) -> Result<ChaosHandle, Box<dyn std::error::Error>> {
        println!("⚡ Triggering circuit breaker for service: {}", service);
        
        // In practice, this would send failure requests to trigger circuit breaker
        // For now, we'll simulate the circuit breaker being open
        
        Ok(ChaosHandle::CircuitBreaker)
    }
    
    /// Calculate resilience score based on test results
    fn calculate_resilience_score(
        &self,
        scenario: &ChaosScenario,
        test_result: &Result<(), Box<dyn std::error::Error>>,
        execution_time: Duration,
    ) -> ResilienceScore {
        let base_score = if test_result.is_ok() { 80.0 } else { 20.0 };
        
        // Adjust score based on execution time
        let time_penalty = match execution_time.as_secs() {
            0..=5 => 0.0,
            6..=15 => -10.0,
            16..=30 => -20.0,
            _ => -30.0,
        };
        
        // Scenario-specific adjustments
        let scenario_modifier = match scenario {
            ChaosScenario::NetworkPartition { .. } => 1.2, // More critical
            ChaosScenario::ServiceFailure { .. } => 1.1,
            ChaosScenario::HighLatency { .. } => 1.0,
            ChaosScenario::MemoryPressure { .. } => 0.9,
            ChaosScenario::DiskSpaceExhaustion { .. } => 0.8,
            ChaosScenario::CircuitBreakerTriggered { .. } => 1.3, // Most critical
        };
        
        let final_score = ((base_score + time_penalty) * scenario_modifier).clamp(0.0, 100.0);
        
        ResilienceScore {
            overall: final_score,
            availability: if test_result.is_ok() { 100.0 } else { 0.0 },
            performance: 100.0 - (execution_time.as_millis() as f64 / 100.0).clamp(0.0, 100.0),
            recoverability: if test_result.is_ok() { 100.0 } else { 50.0 },
        }
    }
}

/// Handle for controlling chaos injection
#[derive(Debug)]
pub enum ChaosHandle {
    NetworkPartition(tokio::task::JoinHandle<()>),
    ServiceFailure(tokio::task::JoinHandle<()>),
    HighLatency(tokio::task::JoinHandle<()>),
    MemoryPressure(Vec<Vec<u8>>),
    DiskExhaustion(Vec<std::path::PathBuf>),
    CircuitBreaker,
}

/// Result of a chaos engineering test
#[derive(Debug, Clone)]
pub struct ChaosTestResult {
    pub scenario: ChaosScenario,
    pub test_passed: bool,
    pub execution_time: Duration,
    pub error_message: Option<String>,
    pub resilience_score: ResilienceScore,
}

/// Resilience scoring metrics
#[derive(Debug, Clone)]
pub struct ResilienceScore {
    pub overall: f64,      // 0-100
    pub availability: f64, // 0-100
    pub performance: f64,  // 0-100  
    pub recoverability: f64, // 0-100
}

/// Chaos test suite for comprehensive resilience testing
pub struct ChaosTestSuite {
    runner: ChaosTestRunner,
}

impl ChaosTestSuite {
    pub fn new() -> Self {
        Self {
            runner: ChaosTestRunner::new(),
        }
    }
    
    /// Run comprehensive chaos testing suite
    pub async fn run_all_scenarios<F, R>(
        &self,
        test_operation: F,
    ) -> Result<Vec<ChaosTestResult>, Box<dyn std::error::Error>>
    where
        F: Fn() -> R + Send + Clone + 'static,
        R: std::future::Future<Output = Result<(), Box<dyn std::error::Error>>> + Send + 'static,
    {
        let scenarios = vec![
            ChaosScenario::NetworkPartition { 
                duration: Duration::from_secs(30) 
            },
            ChaosScenario::ServiceFailure { 
                service: "spanner".to_string(), 
                duration: Duration::from_secs(15) 
            },
            ChaosScenario::HighLatency { 
                latency_ms: 1000, 
                duration: Duration::from_secs(20) 
            },
            ChaosScenario::MemoryPressure { 
                memory_mb: 500, 
                duration: Duration::from_secs(30) 
            },
            ChaosScenario::CircuitBreakerTriggered { 
                service: "ai_service".to_string() 
            },
        ];
        
        let mut results = Vec::new();
        
        for scenario in scenarios {
            let test_op = test_operation.clone();
            let result = self.runner.run_chaos_scenario(scenario, move || test_op()).await?;
            results.push(result);
            
            // Brief recovery period between tests
            sleep(Duration::from_secs(5)).await;
        }
        
        Ok(results)
    }
    
    /// Generate resilience report
    pub fn generate_report(&self, results: &[ChaosTestResult]) -> ResilienceReport {
        let total_tests = results.len() as f64;
        let passed_tests = results.iter().filter(|r| r.test_passed).count() as f64;
        
        let average_score = results
            .iter()
            .map(|r| r.resilience_score.overall)
            .sum::<f64>() / total_tests;
        
        let average_response_time = results
            .iter()
            .map(|r| r.execution_time.as_millis() as f64)
            .sum::<f64>() / total_tests;
        
        ResilienceReport {
            total_scenarios: results.len(),
            passed_scenarios: passed_tests as usize,
            overall_resilience_score: average_score,
            average_response_time_ms: average_response_time,
            detailed_results: results.to_vec(),
        }
    }
}

/// Comprehensive resilience report
#[derive(Debug)]
pub struct ResilienceReport {
    pub total_scenarios: usize,
    pub passed_scenarios: usize,
    pub overall_resilience_score: f64,
    pub average_response_time_ms: f64,
    pub detailed_results: Vec<ChaosTestResult>,
}

impl ResilienceReport {
    pub fn print_summary(&self) {
        println!("\n🔬 CHAOS ENGINEERING RESILIENCE REPORT");
        println!("=====================================");
        println!("📊 Total Scenarios: {}", self.total_scenarios);
        println!("✅ Passed Scenarios: {}", self.passed_scenarios);
        println!("📈 Overall Resilience Score: {:.1}/100", self.overall_resilience_score);
        println!("⏱️  Average Response Time: {:.1}ms", self.average_response_time_ms);
        println!("🎯 Success Rate: {:.1}%", 
            (self.passed_scenarios as f64 / self.total_scenarios as f64) * 100.0);
        
        println!("\n📋 Detailed Results:");
        for result in &self.detailed_results {
            let status = if result.test_passed { "✅ PASS" } else { "❌ FAIL" };
            println!("  {} {:?} - Score: {:.1}/100 - Time: {}ms",
                status,
                result.scenario,
                result.resilience_score.overall,
                result.execution_time.as_millis());
        }
        
        if self.overall_resilience_score >= 80.0 {
            println!("\n🏆 EXCELLENT: System demonstrates high resilience!");
        } else if self.overall_resilience_score >= 60.0 {
            println!("\n⚠️  GOOD: System shows decent resilience with room for improvement.");
        } else {
            println!("\n🚨 NEEDS IMPROVEMENT: System resilience requires attention.");
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_chaos_runner_creation() {
        let runner = ChaosTestRunner::new();
        assert!(runner.config.chaos_testing.network_partition_probability > 0.0);
    }
    
    #[tokio::test]
    async fn test_memory_pressure_scenario() {
        let runner = ChaosTestRunner::new();
        
        let result = runner.run_chaos_scenario(
            ChaosScenario::MemoryPressure { 
                memory_mb: 10, 
                duration: Duration::from_millis(100) 
            },
            || async { Ok(()) }
        ).await;
        
        assert!(result.is_ok());
        assert!(result.unwrap().test_passed);
    }
    
    #[tokio::test]
    async fn test_resilience_score_calculation() {
        let runner = ChaosTestRunner::new();
        let scenario = ChaosScenario::NetworkPartition { 
            duration: Duration::from_secs(10) 
        };
        
        let score = runner.calculate_resilience_score(
            &scenario,
            &Ok(()),
            Duration::from_secs(5)
        );
        
        assert!(score.overall > 70.0); // Should be a good score for passing test
        assert_eq!(score.availability, 100.0); // Test passed
    }
}