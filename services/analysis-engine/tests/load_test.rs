//! Load testing framework for 1M LOC validation
//! 
//! This module provides comprehensive load testing to validate the analysis engine
//! can handle production workloads including repositories with 1M+ lines of code.

use analysis_engine::{
    api::AppState,
    config::ServiceConfig,
    models::{AnalysisRequest, AnalysisResult},
    parser::TreeSitterParser,
    services::analyzer::AnalysisService,
};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Semaphore;
use tracing::{info, warn, error};

/// Load test configuration
#[derive(Debug, Clone)]
pub struct LoadTestConfig {
    /// Number of concurrent analyses to run
    pub concurrent_analyses: usize,
    /// Target repository URLs for testing
    pub test_repositories: Vec<TestRepository>,
    /// Maximum test duration
    pub max_duration: Duration,
    /// Whether to test memory limits
    pub test_memory_limits: bool,
    /// Whether to test timeout scenarios
    pub test_timeouts: bool,
}

/// Test repository configuration
#[derive(Debug, <PERSON><PERSON>)]
pub struct TestRepository {
    pub name: String,
    pub url: String,
    pub expected_loc: usize,
    pub expected_files: usize,
    pub languages: Vec<String>,
}

/// Load test results
#[derive(Debug, Default)]
pub struct LoadTestResults {
    pub total_analyses: usize,
    pub successful_analyses: usize,
    pub failed_analyses: usize,
    pub total_loc_processed: usize,
    pub total_files_processed: usize,
    pub average_analysis_time: Duration,
    pub p95_analysis_time: Duration,
    pub p99_analysis_time: Duration,
    pub max_memory_usage: usize,
    pub error_breakdown: std::collections::HashMap<String, usize>,
}

impl LoadTestConfig {
    /// Create a standard 1M LOC test configuration
    pub fn standard_1m_loc_test() -> Self {
        Self {
            concurrent_analyses: 10,
            test_repositories: vec![
                TestRepository {
                    name: "Linux Kernel".to_string(),
                    url: "https://github.com/torvalds/linux.git".to_string(),
                    expected_loc: 20_000_000, // ~20M LOC
                    expected_files: 70_000,
                    languages: vec!["c".to_string(), "cpp".to_string(), "shell".to_string()],
                },
                TestRepository {
                    name: "Chromium".to_string(),
                    url: "https://github.com/chromium/chromium.git".to_string(),
                    expected_loc: 10_000_000, // ~10M LOC
                    expected_files: 50_000,
                    languages: vec!["cpp".to_string(), "python".to_string(), "javascript".to_string()],
                },
                TestRepository {
                    name: "Rust".to_string(),
                    url: "https://github.com/rust-lang/rust.git".to_string(),
                    expected_loc: 1_000_000, // ~1M LOC
                    expected_files: 15_000,
                    languages: vec!["rust".to_string()],
                },
            ],
            max_duration: Duration::from_secs(3600), // 1 hour
            test_memory_limits: true,
            test_timeouts: true,
        }
    }

    /// Create a stress test configuration
    pub fn stress_test() -> Self {
        Self {
            concurrent_analyses: 50,
            test_repositories: vec![
                // Add many smaller repositories to test concurrency
                TestRepository {
                    name: "React".to_string(),
                    url: "https://github.com/facebook/react.git".to_string(),
                    expected_loc: 200_000,
                    expected_files: 2_000,
                    languages: vec!["javascript".to_string(), "typescript".to_string()],
                },
                TestRepository {
                    name: "Vue".to_string(),
                    url: "https://github.com/vuejs/vue.git".to_string(),
                    expected_loc: 100_000,
                    expected_files: 1_000,
                    languages: vec!["javascript".to_string(), "typescript".to_string()],
                },
                // Repeat with variations to reach 50 concurrent
            ],
            max_duration: Duration::from_secs(1800), // 30 minutes
            test_memory_limits: true,
            test_timeouts: false,
        }
    }
}

/// Main load testing runner
pub struct LoadTestRunner {
    config: LoadTestConfig,
    analysis_service: Arc<AnalysisService>,
    semaphore: Arc<Semaphore>,
    results: Arc<tokio::sync::Mutex<LoadTestResults>>,
}

impl LoadTestRunner {
    /// Create a new load test runner
    pub async fn new(config: LoadTestConfig) -> anyhow::Result<Self> {
        let service_config = Arc::new(ServiceConfig::from_env()?);
        let analysis_service = Arc::new(AnalysisService::new(service_config).await?);
        let semaphore = Arc::new(Semaphore::new(config.concurrent_analyses));
        
        Ok(Self {
            config,
            analysis_service,
            semaphore,
            results: Arc::new(tokio::sync::Mutex::new(LoadTestResults::default())),
        })
    }

    /// Run the load test
    pub async fn run(&self) -> anyhow::Result<LoadTestResults> {
        info!("Starting load test with {} concurrent analyses", self.config.concurrent_analyses);
        
        let start_time = Instant::now();
        let mut handles = vec![];
        
        // Create test scenarios
        let scenarios = self.generate_test_scenarios();
        
        // Launch concurrent analyses
        for (idx, scenario) in scenarios.into_iter().enumerate() {
            let permit = self.semaphore.clone().acquire_owned().await?;
            let service = self.analysis_service.clone();
            let results = self.results.clone();
            
            let handle = tokio::spawn(async move {
                let _permit = permit; // Hold permit until done
                
                let analysis_start = Instant::now();
                let result = Self::run_single_analysis(service, scenario).await;
                let analysis_duration = analysis_start.elapsed();
                
                // Update results
                let mut results_guard = results.lock().await;
                results_guard.total_analyses += 1;
                
                match result {
                    Ok(analysis_result) => {
                        results_guard.successful_analyses += 1;
                        results_guard.total_loc_processed += analysis_result.metrics.total_loc;
                        results_guard.total_files_processed += analysis_result.file_count;
                        
                        // Update timing statistics
                        Self::update_timing_stats(&mut results_guard, analysis_duration);
                        
                        info!(
                            "Analysis {} completed in {:?} - {} LOC, {} files",
                            idx, analysis_duration, 
                            analysis_result.metrics.total_loc,
                            analysis_result.file_count
                        );
                    }
                    Err(e) => {
                        results_guard.failed_analyses += 1;
                        let error_type = format!("{:?}", e);
                        *results_guard.error_breakdown.entry(error_type).or_insert(0) += 1;
                        
                        warn!("Analysis {} failed: {}", idx, e);
                    }
                }
            });
            
            handles.push(handle);
            
            // Check if we've exceeded max duration
            if start_time.elapsed() > self.config.max_duration {
                warn!("Maximum test duration reached, stopping new analyses");
                break;
            }
        }
        
        // Wait for all analyses to complete
        for handle in handles {
            let _ = handle.await;
        }
        
        let total_duration = start_time.elapsed();
        let results = self.results.lock().await.clone();
        
        info!(
            "Load test completed in {:?} - {} successful, {} failed",
            total_duration, results.successful_analyses, results.failed_analyses
        );
        
        Ok(results)
    }

    /// Generate test scenarios based on configuration
    fn generate_test_scenarios(&self) -> Vec<AnalysisRequest> {
        let mut scenarios = vec![];
        
        // Standard repository tests
        for repo in &self.config.test_repositories {
            scenarios.push(AnalysisRequest {
                repository_url: repo.url.clone(),
                branch: Some("main".to_string()),
                languages: Some(repo.languages.clone()),
                include_patterns: None,
                exclude_patterns: Some(vec![
                    "**/test/**".to_string(),
                    "**/tests/**".to_string(),
                    "**/vendor/**".to_string(),
                    "**/node_modules/**".to_string(),
                ]),
                enable_pattern_detection: Some(true),
                enable_embeddings: Some(false), // Disable for load testing
                webhook_url: None,
            });
        }
        
        // Add memory limit test scenarios
        if self.config.test_memory_limits {
            scenarios.push(Self::create_memory_stress_scenario());
        }
        
        // Add timeout test scenarios
        if self.config.test_timeouts {
            scenarios.push(Self::create_timeout_scenario());
        }
        
        // Repeat scenarios to reach desired concurrency
        let mut final_scenarios = vec![];
        while final_scenarios.len() < self.config.concurrent_analyses * 2 {
            final_scenarios.extend(scenarios.clone());
        }
        
        final_scenarios
    }

    /// Run a single analysis
    async fn run_single_analysis(
        service: Arc<AnalysisService>,
        request: AnalysisRequest,
    ) -> anyhow::Result<AnalysisResult> {
        let (tx, mut rx) = tokio::sync::mpsc::channel(100);
        
        // Spawn progress monitoring
        tokio::spawn(async move {
            while let Some(progress) = rx.recv().await {
                tracing::debug!("Progress: {:?}", progress);
            }
        });
        
        service.analyze(request, tx, "load-test-user").await
    }

    /// Create a scenario that stresses memory limits
    fn create_memory_stress_scenario() -> AnalysisRequest {
        AnalysisRequest {
            repository_url: "https://github.com/microsoft/vscode.git".to_string(),
            branch: Some("main".to_string()),
            languages: None, // Analyze all languages
            include_patterns: Some(vec!["**/*.ts".to_string(), "**/*.js".to_string()]),
            exclude_patterns: None, // Don't exclude anything
            enable_pattern_detection: Some(true),
            enable_embeddings: Some(true), // Enable to increase memory usage
            webhook_url: None,
        }
    }

    /// Create a scenario that tests timeout handling
    fn create_timeout_scenario() -> AnalysisRequest {
        AnalysisRequest {
            repository_url: "https://github.com/apache/spark.git".to_string(),
            branch: Some("master".to_string()),
            languages: None,
            include_patterns: None,
            exclude_patterns: None,
            enable_pattern_detection: Some(true),
            enable_embeddings: Some(true),
            webhook_url: None,
        }
    }

    /// Update timing statistics
    fn update_timing_stats(results: &mut LoadTestResults, duration: Duration) {
        // Simple running average for now
        if results.successful_analyses == 1 {
            results.average_analysis_time = duration;
            results.p95_analysis_time = duration;
            results.p99_analysis_time = duration;
        } else {
            // Update average
            let total_time = results.average_analysis_time.as_millis() as u64 
                * (results.successful_analyses - 1) as u64
                + duration.as_millis() as u64;
            results.average_analysis_time = Duration::from_millis(
                total_time / results.successful_analyses as u64
            );
            
            // Update percentiles (simplified - in production use proper percentile calculation)
            if duration > results.p95_analysis_time {
                results.p95_analysis_time = duration;
            }
            if duration > results.p99_analysis_time {
                results.p99_analysis_time = duration;
            }
        }
    }
}

/// Performance assertions for load test results
impl LoadTestResults {
    /// Validate results against production requirements
    pub fn validate_production_requirements(&self) -> anyhow::Result<()> {
        // Success rate must be >99%
        let success_rate = self.successful_analyses as f64 / self.total_analyses as f64;
        if success_rate < 0.99 {
            return Err(anyhow::anyhow!(
                "Success rate {:.2}% is below 99% requirement",
                success_rate * 100.0
            ));
        }
        
        // Average analysis time for 1M LOC should be <5 minutes
        if self.average_analysis_time > Duration::from_secs(300) {
            return Err(anyhow::anyhow!(
                "Average analysis time {:?} exceeds 5 minute target",
                self.average_analysis_time
            ));
        }
        
        // P99 should be <10 minutes
        if self.p99_analysis_time > Duration::from_secs(600) {
            return Err(anyhow::anyhow!(
                "P99 analysis time {:?} exceeds 10 minute target",
                self.p99_analysis_time
            ));
        }
        
        info!("✅ All production requirements validated successfully");
        Ok(())
    }

    /// Generate a detailed report
    pub fn generate_report(&self) -> String {
        format!(
            r#"
# Load Test Results Report

## Summary
- Total Analyses: {}
- Successful: {} ({:.2}%)
- Failed: {} ({:.2}%)

## Performance Metrics
- Total LOC Processed: {} ({} MLOC)
- Total Files Processed: {}
- Average Analysis Time: {:?}
- P95 Analysis Time: {:?}
- P99 Analysis Time: {:?}
- Throughput: {:.2} analyses/hour

## Error Breakdown
{}

## Memory Usage
- Max Memory: {} MB

## Validation
- Success Rate Target (>99%): {}
- Analysis Time Target (<5min avg): {}
- P99 Target (<10min): {}
"#,
            self.total_analyses,
            self.successful_analyses,
            self.successful_analyses as f64 / self.total_analyses as f64 * 100.0,
            self.failed_analyses,
            self.failed_analyses as f64 / self.total_analyses as f64 * 100.0,
            self.total_loc_processed,
            self.total_loc_processed / 1_000_000,
            self.total_files_processed,
            self.average_analysis_time,
            self.p95_analysis_time,
            self.p99_analysis_time,
            self.successful_analyses as f64 / self.average_analysis_time.as_secs_f64() * 3600.0,
            self.format_error_breakdown(),
            self.max_memory_usage / 1024 / 1024,
            if self.successful_analyses as f64 / self.total_analyses as f64 >= 0.99 { "✅ PASS" } else { "❌ FAIL" },
            if self.average_analysis_time <= Duration::from_secs(300) { "✅ PASS" } else { "❌ FAIL" },
            if self.p99_analysis_time <= Duration::from_secs(600) { "✅ PASS" } else { "❌ FAIL" },
        )
    }

    fn format_error_breakdown(&self) -> String {
        if self.error_breakdown.is_empty() {
            return "No errors recorded".to_string();
        }
        
        self.error_breakdown
            .iter()
            .map(|(error, count)| format!("- {}: {}", error, count))
            .collect::<Vec<_>>()
            .join("\n")
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_load_test_config() {
        let config = LoadTestConfig::standard_1m_loc_test();
        assert_eq!(config.concurrent_analyses, 10);
        assert!(!config.test_repositories.is_empty());
    }

    #[test]
    fn test_results_validation() {
        let mut results = LoadTestResults::default();
        results.total_analyses = 100;
        results.successful_analyses = 99;
        results.failed_analyses = 1;
        results.average_analysis_time = Duration::from_secs(240);
        results.p99_analysis_time = Duration::from_secs(480);
        
        assert!(results.validate_production_requirements().is_ok());
    }
}