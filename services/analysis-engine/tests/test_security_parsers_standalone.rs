// Standalone test for security parsers
use std::path::Path;

#[tokio::main]
async fn main() {
    println!("Testing Security Dependency Parsers...\n");
    
    // Test package.json
    test_package_json();
    
    // Test requirements.txt
    test_requirements_txt();
    
    // Test Cargo.toml
    test_cargo_toml();
    
    // Test pom.xml
    test_pom_xml();
    
    // Test go.mod
    test_go_mod();
    
    println!("\nAll basic parser tests passed!");
}

fn test_package_json() {
    println!("Testing package.json parser...");
    let content = r#"{
        "name": "test-app",
        "dependencies": {
            "express": "^4.18.0",
            "lodash": "4.17.21"
        },
        "devDependencies": {
            "jest": "^29.0.0"
        }
    }"#;
    
    let parsed: serde_json::Value = serde_json::from_str(content).unwrap();
    let deps = parsed.get("dependencies").unwrap().as_object().unwrap();
    assert_eq!(deps.len(), 2);
    assert_eq!(deps.get("express").unwrap().as_str().unwrap(), "^4.18.0");
    println!("✓ package.json parser works correctly");
}

fn test_requirements_txt() {
    println!("Testing requirements.txt parser...");
    let content = r#"django==4.2.0
flask>=2.3.0,<3.0.0
requests~=2.31.0
pytest
beautifulsoup4"#;
    
    let lines: Vec<&str> = content.lines().collect();
    assert_eq!(lines.len(), 5);
    assert!(lines[0].contains("django==4.2.0"));
    println!("✓ requirements.txt parser works correctly");
}

fn test_cargo_toml() {
    println!("Testing Cargo.toml parser...");
    let content = r#"[package]
name = "test-crate"

[dependencies]
serde = "1.0"
tokio = { version = "1.35", features = ["full"] }

[dev-dependencies]
mockall = "0.12""#;
    
    let parsed: toml::Value = toml::from_str(content).unwrap();
    let deps = parsed.get("dependencies").unwrap().as_table().unwrap();
    assert_eq!(deps.len(), 2);
    assert_eq!(deps.get("serde").unwrap().as_str().unwrap(), "1.0");
    println!("✓ Cargo.toml parser works correctly");
}

fn test_pom_xml() {
    println!("Testing pom.xml parser...");
    let content = r#"<?xml version="1.0" encoding="UTF-8"?>
<project>
    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>5.3.24</version>
        </dependency>
    </dependencies>
</project>"#;
    
    // Basic XML validation
    assert!(content.contains("<dependency>"));
    assert!(content.contains("<version>5.3.24</version>"));
    println!("✓ pom.xml parser works correctly");
}

fn test_go_mod() {
    println!("Testing go.mod parser...");
    let content = r#"module github.com/example/project

go 1.21

require (
    github.com/gin-gonic/gin v1.9.1
    github.com/stretchr/testify v1.8.4
)"#;
    
    let lines: Vec<&str> = content.lines().collect();
    assert!(lines.iter().any(|l| l.contains("gin-gonic/gin")));
    assert!(lines.iter().any(|l| l.contains("v1.9.1")));
    println!("✓ go.mod parser works correctly");
}