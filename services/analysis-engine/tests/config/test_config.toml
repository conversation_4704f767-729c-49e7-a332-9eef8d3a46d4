# Test Configuration - Externalized Test Data Management
# This centralizes all test configuration and makes tests more maintainable

[performance_targets]
# Analysis performance targets
max_analysis_time_seconds = 300  # 5 minutes for 1M LOC
max_api_response_ms = 100        # p95 response time
max_memory_usage_gb = 4          # per analysis instance
max_concurrent_analyses = 50     # concurrent support

[test_data]
# Repository test data
small_repo_size = 1000      # lines of code
medium_repo_size = 100000   # lines of code  
large_repo_size = 1000000   # lines of code

# Pattern detection test data
vulnerability_patterns_count = 850
secret_patterns_count = 20
compliance_frameworks_count = 8

[mock_services]
# AI service mock configuration
ai_response_delay_ms = 100
ai_confidence_threshold = 0.85
ai_failure_rate = 0.01  # 1% simulated failure rate

# Database mock configuration
db_connection_timeout_ms = 5000
db_query_timeout_ms = 30000
db_failure_rate = 0.005  # 0.5% simulated failure rate

# Circuit breaker configuration
circuit_breaker_failure_threshold = 5
circuit_breaker_timeout_seconds = 60
circuit_breaker_half_open_max_calls = 3

[chaos_testing]
# Chaos engineering test configuration
network_partition_probability = 0.1
service_failure_probability = 0.05
high_latency_probability = 0.15
memory_pressure_probability = 0.1

[property_testing]
# Property-based testing configuration
max_test_cases = 1000
max_shrink_iterations = 1000
max_string_length = 10000
max_array_length = 1000

[languages]
# Supported languages for testing
priority_languages = [
    "rust", "python", "javascript", "typescript", "go", "java", "c", "cpp"
]

full_language_support = [
    "rust", "python", "javascript", "typescript", "go", "java", "c", "cpp",
    "html", "css", "json", "yaml", "php", "ruby", "bash", "markdown",
    "swift", "kotlin", "objc", "r", "julia", "haskell", "scala", "erlang", 
    "elixir", "xml", "zig", "d", "lua", "ocaml", "nix"
]

[security_testing]
# Security test patterns
vulnerability_test_cases = [
    "sql_injection", "xss", "path_traversal", "command_injection",
    "buffer_overflow", "use_after_free", "null_pointer_dereference"
]

secret_test_cases = [
    "aws_access_key", "jwt_token", "api_key", "password", "private_key",
    "database_url", "oauth_token", "github_token"
]

compliance_frameworks = [
    "PCI_DSS", "GDPR", "HIPAA", "SOC2", "ISO27001", "NIST", "CIS", "OWASP"
]