use analysis_engine::models::security::*;
use analysis_engine::services::security_analyzer::SecurityAnalyzer;
use analysis_engine::models::{FileAnalysis, AstNode, FileMetrics, Range, Position};
use chrono::Utc;
use std::collections::HashMap;

#[tokio::test]
async fn test_security_analyzer_creation() {
    let analyzer = SecurityAnalyzer::new();
    // Test that the analyzer can be created successfully
    assert!(true, "SecurityAnalyzer should be created successfully");
}

#[tokio::test]
async fn test_vulnerability_detection() {
    let analyzer = SecurityAnalyzer::new();
    let file_analysis = create_test_file_analysis_with_vulnerability();
    
    let request = SecurityAnalysisRequest {
        analysis_id: "test_analysis".to_string(),
        enable_vulnerability_detection: true,
        enable_dependency_scanning: false,
        enable_secrets_detection: false,
        enable_compliance_checking: false,
        enable_threat_modeling: false,
        threat_intel_enabled: false,
        compliance_frameworks: vec![],
        scan_depth: SecurityScanDepth::Standard,
    };

    let result = analyzer
        .analyze_security("test_analysis", &[file_analysis], &request)
        .await;

    assert!(result.is_ok(), "Security analysis should complete successfully");
    let security_result = result.unwrap();
    
    // Should detect at least one vulnerability in the test file
    assert!(
        !security_result.vulnerabilities.is_empty(),
        "Should detect vulnerabilities in test code"
    );
    
    // Check that security assessment is generated
    assert!(
        security_result.security_assessment.overall_security_score >= 0.0 
        && security_result.security_assessment.overall_security_score <= 100.0,
        "Security score should be between 0 and 100"
    );
}

#[tokio::test]
async fn test_secrets_detection() {
    let analyzer = SecurityAnalyzer::new();
    let file_analysis = create_test_file_analysis_with_secrets();
    
    let request = SecurityAnalysisRequest {
        analysis_id: "test_analysis".to_string(),
        enable_vulnerability_detection: false,
        enable_dependency_scanning: false,
        enable_secrets_detection: true,
        enable_compliance_checking: false,
        enable_threat_modeling: false,
        threat_intel_enabled: false,
        compliance_frameworks: vec![],
        scan_depth: SecurityScanDepth::Standard,
    };

    let result = analyzer
        .analyze_security("test_analysis", &[file_analysis], &request)
        .await;

    assert!(result.is_ok(), "Security analysis should complete successfully");
    let security_result = result.unwrap();
    
    // Should detect at least one secret in the test file
    assert!(
        !security_result.detected_secrets.is_empty(),
        "Should detect secrets in test code"
    );
    
    // Check that the detected secret has proper masking
    let secret = &security_result.detected_secrets[0];
    assert!(
        secret.masked_value.is_some(),
        "Secret should have a masked value"
    );
    
    assert!(
        secret.confidence_score > 0.0,
        "Secret detection should have confidence score"
    );
}

#[tokio::test]
async fn test_dependency_scanning() {
    let analyzer = SecurityAnalyzer::new();
    let file_analysis = create_test_package_json();
    
    let request = SecurityAnalysisRequest {
        analysis_id: "test_analysis".to_string(),
        enable_vulnerability_detection: false,
        enable_dependency_scanning: true,
        enable_secrets_detection: false,
        enable_compliance_checking: false,
        enable_threat_modeling: false,
        threat_intel_enabled: false,
        compliance_frameworks: vec![],
        scan_depth: SecurityScanDepth::Standard,
    };

    let result = analyzer
        .analyze_security("test_analysis", &[file_analysis], &request)
        .await;

    assert!(result.is_ok(), "Security analysis should complete successfully");
    let security_result = result.unwrap();
    
    // Check that metadata is properly generated
    assert_eq!(
        security_result.metadata.analysis_id,
        "test_analysis"
    );
    
    assert!(
        security_result.metadata.scan_duration_ms.is_some(),
        "Scan duration should be recorded"
    );
}

#[tokio::test]
async fn test_compliance_checking() {
    let analyzer = SecurityAnalyzer::new();
    let file_analysis = create_test_file_analysis_with_compliance_violation();
    
    let request = SecurityAnalysisRequest {
        analysis_id: "test_analysis".to_string(),
        enable_vulnerability_detection: false,
        enable_dependency_scanning: false,
        enable_secrets_detection: false,
        enable_compliance_checking: true,
        enable_threat_modeling: false,
        threat_intel_enabled: false,
        compliance_frameworks: vec![ComplianceFramework::OWASP],
        scan_depth: SecurityScanDepth::Standard,
    };

    let result = analyzer
        .analyze_security("test_analysis", &[file_analysis], &request)
        .await;

    assert!(result.is_ok(), "Security analysis should complete successfully");
    let security_result = result.unwrap();
    
    // Check that security assessment includes compliance data
    assert!(
        security_result.security_assessment.compliance_score >= 0.0,
        "Compliance score should be calculated"
    );
}

#[tokio::test]
async fn test_threat_modeling() {
    let analyzer = SecurityAnalyzer::new();
    let file_analysis = create_test_file_analysis_with_vulnerability();
    
    let request = SecurityAnalysisRequest {
        analysis_id: "test_analysis".to_string(),
        enable_vulnerability_detection: true,
        enable_dependency_scanning: false,
        enable_secrets_detection: false,
        enable_compliance_checking: false,
        enable_threat_modeling: true,
        threat_intel_enabled: false,
        compliance_frameworks: vec![],
        scan_depth: SecurityScanDepth::Deep,
    };

    let result = analyzer
        .analyze_security("test_analysis", &[file_analysis], &request)
        .await;

    assert!(result.is_ok(), "Security analysis should complete successfully");
    let security_result = result.unwrap();
    
    // Should generate threat models when vulnerabilities are detected
    if !security_result.vulnerabilities.is_empty() {
        assert!(
            !security_result.threat_models.is_empty(),
            "Should generate threat models for detected vulnerabilities"
        );
        
        let threat_model = &security_result.threat_models[0];
        assert!(
            threat_model.risk_score > 0.0,
            "Threat model should have risk score"
        );
        
        assert!(
            !threat_model.mitigation_measures.is_empty(),
            "Threat model should include mitigation measures"
        );
    }
}

#[tokio::test]
async fn test_security_assessment_scoring() {
    let analyzer = SecurityAnalyzer::new();
    let file_analysis = create_test_file_analysis_comprehensive();
    
    let request = SecurityAnalysisRequest {
        analysis_id: "test_analysis".to_string(),
        enable_vulnerability_detection: true,
        enable_dependency_scanning: true,
        enable_secrets_detection: true,
        enable_compliance_checking: true,
        enable_threat_modeling: true,
        threat_intel_enabled: false,
        compliance_frameworks: vec![ComplianceFramework::OWASP, ComplianceFramework::CWE],
        scan_depth: SecurityScanDepth::Deep,
    };

    let result = analyzer
        .analyze_security("test_analysis", &[file_analysis], &request)
        .await;

    assert!(result.is_ok(), "Security analysis should complete successfully");
    let security_result = result.unwrap();
    
    let assessment = &security_result.security_assessment;
    
    // Validate assessment structure
    assert!(
        assessment.overall_security_score >= 0.0 && assessment.overall_security_score <= 100.0,
        "Overall security score should be between 0 and 100"
    );
    
    assert!(
        assessment.vulnerability_score >= 0.0 && assessment.vulnerability_score <= 100.0,
        "Vulnerability score should be between 0 and 100"
    );
    
    assert!(
        assessment.dependency_score >= 0.0 && assessment.dependency_score <= 100.0,
        "Dependency score should be between 0 and 100"
    );
    
    assert!(
        assessment.secrets_score >= 0.0 && assessment.secrets_score <= 100.0,
        "Secrets score should be between 0 and 100"
    );
    
    assert!(
        assessment.compliance_score >= 0.0 && assessment.compliance_score <= 100.0,
        "Compliance score should be between 0 and 100"
    );
    
    // Validate risk level is properly assigned
    match assessment.risk_level {
        RiskLevel::Low | RiskLevel::Medium | RiskLevel::High | RiskLevel::Critical => {
            // Valid risk level
        }
    }
    
    // Validate counters
    assert!(
        assessment.total_vulnerabilities >= 0,
        "Total vulnerabilities count should be non-negative"
    );
    
    assert!(
        assessment.total_secrets_found >= 0,
        "Total secrets count should be non-negative"
    );
    
    assert!(
        assessment.compliance_violations_count >= 0,
        "Compliance violations count should be non-negative"
    );
    
    // Validate recommendations are provided
    assert!(
        !assessment.improvement_recommendations.is_empty(),
        "Should provide improvement recommendations"
    );
}

#[tokio::test]
async fn test_security_metadata_generation() {
    let analyzer = SecurityAnalyzer::new();
    let file_analysis = create_test_file_analysis_with_vulnerability();
    
    let request = SecurityAnalysisRequest {
        analysis_id: "test_analysis".to_string(),
        enable_vulnerability_detection: true,
        enable_dependency_scanning: false,
        enable_secrets_detection: false,
        enable_compliance_checking: false,
        enable_threat_modeling: false,
        threat_intel_enabled: true,
        compliance_frameworks: vec![],
        scan_depth: SecurityScanDepth::Deep,
    };

    let result = analyzer
        .analyze_security("test_analysis", &[file_analysis], &request)
        .await;

    assert!(result.is_ok(), "Security analysis should complete successfully");
    let security_result = result.unwrap();
    
    let metadata = &security_result.metadata;
    
    // Validate metadata fields
    assert_eq!(metadata.analysis_id, "test_analysis");
    
    assert!(
        !metadata.threat_intel_sources.is_empty(),
        "Should list threat intelligence sources"
    );
    
    assert!(
        metadata.last_threat_intel_update.is_some(),
        "Should record threat intelligence update time"
    );
    
    assert!(
        !metadata.vulnerability_databases_used.is_empty(),
        "Should list vulnerability databases used"
    );
    
    assert!(
        metadata.scan_duration_ms.is_some() && metadata.scan_duration_ms.unwrap() > 0,
        "Should record scan duration"
    );
    
    assert!(
        metadata.total_files_scanned.is_some() && metadata.total_files_scanned.unwrap() > 0,
        "Should record files scanned count"
    );
    
    assert!(
        metadata.false_positive_rate.is_some() 
        && metadata.false_positive_rate.unwrap() >= 0.0 
        && metadata.false_positive_rate.unwrap() <= 1.0,
        "False positive rate should be between 0 and 1"
    );
    
    assert!(
        metadata.detection_accuracy.is_some() 
        && metadata.detection_accuracy.unwrap() >= 0.0 
        && metadata.detection_accuracy.unwrap() <= 1.0,
        "Detection accuracy should be between 0 and 1"
    );
}

// Helper functions to create test data

fn create_test_file_analysis_with_vulnerability() -> FileAnalysis {
    FileAnalysis {
        path: "test_file.js".to_string(),
        language: "javascript".to_string(),
        content_hash: "test_hash".to_string(),
        size_bytes: Some(1024),
        ast: AstNode {
            node_type: "program".to_string(),
            name: None,
            range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 10, column: 0, byte: 100 },
            },
            children: vec![
                AstNode {
                    node_type: "call_expression".to_string(),
                    name: Some("eval".to_string()),
                    range: Range {
                        start: Position { line: 5, column: 0, byte: 50 },
                        end: Position { line: 5, column: 20, byte: 70 },
                    },
                    children: vec![],
                    properties: None,
                    text: Some("eval(userInput)".to_string()),
                }
            ],
            properties: None,
            text: Some("const result = eval(userInput); console.log(result);".to_string()),
        },
        metrics: FileMetrics::default(),
        chunks: None,
        symbols: None,
    }
}

fn create_test_file_analysis_with_secrets() -> FileAnalysis {
    FileAnalysis {
        path: "config.js".to_string(),
        language: "javascript".to_string(),
        content_hash: "test_hash_secrets".to_string(),
        size_bytes: Some(512),
        ast: AstNode {
            node_type: "program".to_string(),
            name: None,
            range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 5, column: 0, byte: 100 },
            },
            children: vec![],
            properties: None,
            text: Some("const apiKey = 'AKIA1234567890ABCDEF';\nconst password = 'secret123456';".to_string()),
        },
        metrics: FileMetrics::default(),
        chunks: None,
        symbols: None,
    }
}

fn create_test_package_json() -> FileAnalysis {
    FileAnalysis {
        path: "package.json".to_string(),
        language: "json".to_string(),
        content_hash: "test_hash_package".to_string(),
        size_bytes: Some(256),
        ast: AstNode {
            node_type: "document".to_string(),
            name: None,
            range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 10, column: 0, byte: 200 },
            },
            children: vec![],
            properties: None,
            text: Some(r#"{"dependencies": {"lodash": "4.17.20"}}"#.to_string()),
        },
        metrics: FileMetrics::default(),
        chunks: None,
        symbols: None,
    }
}

fn create_test_file_analysis_with_compliance_violation() -> FileAnalysis {
    FileAnalysis {
        path: "admin.js".to_string(),
        language: "javascript".to_string(),
        content_hash: "test_hash_compliance".to_string(),
        size_bytes: Some(512),
        ast: AstNode {
            node_type: "program".to_string(),
            name: None,
            range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 5, column: 0, byte: 100 },
            },
            children: vec![],
            properties: None,
            text: Some("const admin = true;\nif (admin) { /* privileged operations */ }".to_string()),
        },
        metrics: FileMetrics::default(),
        chunks: None,
        symbols: None,
    }
}

fn create_test_file_analysis_comprehensive() -> FileAnalysis {
    FileAnalysis {
        path: "app.js".to_string(),
        language: "javascript".to_string(),
        content_hash: "test_hash_comprehensive".to_string(),
        size_bytes: Some(2048),
        ast: AstNode {
            node_type: "program".to_string(),
            name: None,
            range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 20, column: 0, byte: 500 },
            },
            children: vec![
                AstNode {
                    node_type: "call_expression".to_string(),
                    name: Some("eval".to_string()),
                    range: Range {
                        start: Position { line: 5, column: 0, byte: 50 },
                        end: Position { line: 5, column: 20, byte: 70 },
                    },
                    children: vec![],
                    properties: None,
                    text: Some("eval(userInput)".to_string()),
                }
            ],
            properties: None,
            text: Some(r#"
                const apiKey = 'AKIA1234567890ABCDEF';
                const password = 'secret123456';
                const admin = true;
                
                function processInput(userInput) {
                    return eval(userInput);
                }
                
                if (admin) {
                    processInput(document.innerHTML);
                }
            "#.to_string()),
        },
        metrics: FileMetrics {
            lines_of_code: 15,
            total_lines: Some(20),
            complexity: 3,
            maintainability_index: 65.0,
            function_count: 1,
            class_count: 0,
            comment_ratio: 0.1,
        },
        chunks: None,
        symbols: None,
    }
}