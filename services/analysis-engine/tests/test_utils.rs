// Test utilities for analysis engine tests
use analysis_engine::models::*;
use std::collections::HashMap;

pub fn create_mock_file_analysis(path: &str, language: &str) -> FileAnalysis {
    FileAnalysis {
        path: path.to_string(),
        language: language.to_string(),
        content_hash: "mock_hash".to_string(),
        size_bytes: Some(100),
        ast: create_mock_ast(),
        metrics: create_mock_metrics(),
        chunks: None,
        symbols: None,
    }
}

pub fn create_mock_ast() -> AstNode {
    AstNode {
        node_type: "program".to_string(),
        name: None,
        range: Range {
            start: Position { line: 0, column: 0, byte: 0 },
            end: Position { line: 10, column: 0, byte: 100 },
        },
        children: vec![],
        properties: None,
        text: None,
    }
}

pub fn create_mock_metrics() -> FileMetrics {
    FileMetrics {
        lines_of_code: 50,
        total_lines: Some(60),
        complexity: 5,
        maintainability_index: 75.0,
        function_count: 3,
        class_count: 1,
        comment_ratio: 0.1,
    }
}