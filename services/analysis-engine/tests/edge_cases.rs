//! Edge case tests for extreme scenarios
//! 
//! This module tests the analysis engine's behavior under extreme conditions
//! including very large files, malformed inputs, and resource exhaustion scenarios.

use analysis_engine::models::{AnalysisRequest, FileAnalysis};
use analysis_engine::services::parser::{Pa<PERSON><PERSON>, ParserConfig};
use analysis_engine::services::security::SecurityAnalyzer;
use analysis_engine::errors::{AnalysisError, ParserError};
use tempfile::tempdir;
use std::fs;
use std::time::Duration;
use tokio::time::timeout;

#[cfg(test)]
mod extreme_file_sizes {
    use super::*;

    #[tokio::test]
    async fn test_file_at_size_limit() {
        // Test file exactly at the 10MB limit
        let config = ParserConfig {
            max_file_size_bytes: 10 * 1024 * 1024, // 10MB
            parse_timeout_seconds: 30,
            ..Default::default()
        };
        
        let parser = Parser::new(config);
        let temp_dir = tempdir().unwrap();
        let file_path = temp_dir.path().join("large_file.rs");
        
        // Create a file exactly at the limit
        let content = "fn main() {\n".repeat(10 * 1024 * 1024 / 12); // ~10MB
        fs::write(&file_path, &content).unwrap();
        
        // Should parse successfully
        let result = parser.parse_file(&file_path, "rust").await;
        assert!(result.is_ok(), "File at size limit should parse successfully");
    }

    #[tokio::test]
    async fn test_file_exceeds_size_limit() {
        // Test file that exceeds the 10MB limit
        let config = ParserConfig {
            max_file_size_bytes: 10 * 1024 * 1024, // 10MB
            parse_timeout_seconds: 30,
            ..Default::default()
        };
        
        let parser = Parser::new(config);
        let temp_dir = tempdir().unwrap();
        let file_path = temp_dir.path().join("huge_file.rs");
        
        // Create a file that exceeds the limit
        let content = "fn main() {\n".repeat(11 * 1024 * 1024 / 12); // ~11MB
        fs::write(&file_path, &content).unwrap();
        
        // Should fail with FileTooLarge error
        let result = parser.parse_file(&file_path, "rust").await;
        match result {
            Err(AnalysisError::Parser(ParserError::FileTooLarge { size, limit })) => {
                assert!(size > limit, "File size should exceed limit");
                assert_eq!(limit, 10 * 1024 * 1024);
            }
            _ => panic!("Expected FileTooLarge error"),
        }
    }

    #[tokio::test]
    async fn test_empty_file() {
        // Test completely empty file
        let parser = Parser::new(ParserConfig::default());
        let temp_dir = tempdir().unwrap();
        let file_path = temp_dir.path().join("empty.rs");
        
        // Create empty file
        fs::write(&file_path, "").unwrap();
        
        // Should parse successfully but with minimal AST
        let result = parser.parse_file(&file_path, "rust").await;
        assert!(result.is_ok(), "Empty file should parse successfully");
        
        let analysis = result.unwrap();
        assert_eq!(analysis.line_count, 0);
        assert!(analysis.functions.is_empty());
        assert!(analysis.classes.is_empty());
    }

    #[tokio::test]
    async fn test_file_with_null_bytes() {
        // Test file containing null bytes
        let parser = Parser::new(ParserConfig::default());
        let temp_dir = tempdir().unwrap();
        let file_path = temp_dir.path().join("null_bytes.rs");
        
        // Create file with null bytes
        let mut content = Vec::from("fn main() {\n".as_bytes());
        content.push(0); // null byte
        content.extend_from_slice("println!(\"test\");\n}".as_bytes());
        fs::write(&file_path, &content).unwrap();
        
        // Should handle gracefully
        let result = parser.parse_file(&file_path, "rust").await;
        match result {
            Err(AnalysisError::Parser(ParserError::InvalidEncoding(_))) => {
                // Expected error for files with null bytes
            }
            Ok(_) => {
                // Some parsers might handle null bytes
            }
            Err(e) => panic!("Unexpected error: {:?}", e),
        }
    }
}

#[cfg(test)]
mod extreme_parse_timeouts {
    use super::*;

    #[tokio::test]
    async fn test_parse_timeout_enforcement() {
        // Test that parse timeout is enforced
        let config = ParserConfig {
            max_file_size_bytes: 10 * 1024 * 1024,
            parse_timeout_seconds: 1, // Very short timeout
            ..Default::default()
        };
        
        let parser = Parser::new(config);
        let temp_dir = tempdir().unwrap();
        let file_path = temp_dir.path().join("complex.rs");
        
        // Create a file with extremely complex nested structures
        let mut content = String::from("fn main() {\n");
        for _ in 0..10000 {
            content.push_str("    if true { if true { if true { if true {\n");
        }
        for _ in 0..10000 {
            content.push_str("    }}}}\n");
        }
        content.push_str("}\n");
        fs::write(&file_path, &content).unwrap();
        
        // Should timeout
        let result = timeout(
            Duration::from_secs(2),
            parser.parse_file(&file_path, "rust")
        ).await;
        
        match result {
            Ok(Err(AnalysisError::Parser(ParserError::Timeout { timeout_seconds }))) => {
                assert_eq!(timeout_seconds, 1);
            }
            Err(_) => {
                // Timeout at tokio level is also acceptable
            }
            _ => panic!("Expected timeout error"),
        }
    }
}

#[cfg(test)]
mod malformed_input_tests {
    use super::*;

    #[tokio::test]
    async fn test_invalid_utf8() {
        // Test file with invalid UTF-8
        let parser = Parser::new(ParserConfig::default());
        let temp_dir = tempdir().unwrap();
        let file_path = temp_dir.path().join("invalid_utf8.rs");
        
        // Create file with invalid UTF-8
        let invalid_utf8 = vec![0xFF, 0xFE, 0xFD];
        fs::write(&file_path, &invalid_utf8).unwrap();
        
        // Should fail with encoding error
        let result = parser.parse_file(&file_path, "rust").await;
        match result {
            Err(AnalysisError::Parser(ParserError::InvalidEncoding(_))) => {
                // Expected
            }
            _ => panic!("Expected InvalidEncoding error"),
        }
    }

    #[tokio::test]
    async fn test_deeply_nested_structures() {
        // Test extremely deep nesting that might cause stack overflow
        let parser = Parser::new(ParserConfig::default());
        let temp_dir = tempdir().unwrap();
        let file_path = temp_dir.path().join("deeply_nested.json");
        
        // Create deeply nested JSON
        let mut content = String::new();
        for _ in 0..5000 {
            content.push_str("{\"a\":");
        }
        content.push_str("null");
        for _ in 0..5000 {
            content.push('}');
        }
        fs::write(&file_path, &content).unwrap();
        
        // Should handle without stack overflow
        let result = parser.parse_file(&file_path, "json").await;
        // Either succeeds or fails gracefully, but shouldn't panic
        match result {
            Ok(_) => println!("Handled deep nesting successfully"),
            Err(e) => println!("Failed gracefully: {:?}", e),
        }
    }

    #[tokio::test]
    async fn test_mixed_line_endings() {
        // Test file with mixed line endings (CRLF, LF, CR)
        let parser = Parser::new(ParserConfig::default());
        let temp_dir = tempdir().unwrap();
        let file_path = temp_dir.path().join("mixed_endings.py");
        
        // Create file with mixed line endings
        let content = "def func1():\r\n    pass\n\rdef func2():\r    pass\n";
        fs::write(&file_path, content).unwrap();
        
        // Should parse successfully
        let result = parser.parse_file(&file_path, "python").await;
        assert!(result.is_ok(), "Should handle mixed line endings");
        
        let analysis = result.unwrap();
        assert_eq!(analysis.functions.len(), 2);
    }
}

#[cfg(test)]
mod resource_exhaustion_tests {
    use super::*;

    #[tokio::test]
    async fn test_maximum_dependency_count() {
        // Test file with exactly the maximum allowed dependencies
        let config = ParserConfig {
            max_dependency_count: 10000,
            ..Default::default()
        };
        
        let parser = Parser::new(config);
        let temp_dir = tempdir().unwrap();
        let file_path = temp_dir.path().join("package.json");
        
        // Create package.json with 10000 dependencies
        let mut deps = String::from("{\n  \"dependencies\": {\n");
        for i in 0..9999 {
            deps.push_str(&format!("    \"dep{}\": \"1.0.0\",\n", i));
        }
        deps.push_str("    \"dep9999\": \"1.0.0\"\n  }\n}");
        fs::write(&file_path, &deps).unwrap();
        
        // Should parse successfully
        let result = parser.parse_file(&file_path, "json").await;
        assert!(result.is_ok(), "Should handle maximum dependencies");
    }

    #[tokio::test]
    async fn test_exceeds_dependency_limit() {
        // Test file that exceeds dependency limit
        let config = ParserConfig {
            max_dependency_count: 10000,
            ..Default::default()
        };
        
        let parser = Parser::new(config);
        let temp_dir = tempdir().unwrap();
        let file_path = temp_dir.path().join("package.json");
        
        // Create package.json with 10001 dependencies
        let mut deps = String::from("{\n  \"dependencies\": {\n");
        for i in 0..10000 {
            deps.push_str(&format!("    \"dep{}\": \"1.0.0\",\n", i));
        }
        deps.push_str("    \"dep10000\": \"1.0.0\"\n  }\n}");
        fs::write(&file_path, &deps).unwrap();
        
        // Should fail with dependency limit error
        let result = parser.parse_file(&file_path, "json").await;
        assert!(result.is_err(), "Should fail when exceeding dependency limit");
    }
}

#[cfg(test)]
mod concurrent_analysis_tests {
    use super::*;
    use futures::future::join_all;

    #[tokio::test]
    async fn test_many_concurrent_analyses() {
        // Test handling many concurrent parse operations
        let parser = Parser::new(ParserConfig::default());
        let temp_dir = tempdir().unwrap();
        
        // Create 100 small files
        let mut file_paths = Vec::new();
        for i in 0..100 {
            let file_path = temp_dir.path().join(format!("file{}.rs", i));
            let content = format!("fn func{}() {{ println!(\"{}\"); }}", i, i);
            fs::write(&file_path, &content).unwrap();
            file_paths.push(file_path);
        }
        
        // Parse all files concurrently
        let parse_futures: Vec<_> = file_paths
            .iter()
            .map(|path| parser.parse_file(path, "rust"))
            .collect();
        
        let results = join_all(parse_futures).await;
        
        // All should succeed
        let successful = results.iter().filter(|r| r.is_ok()).count();
        assert_eq!(successful, 100, "All concurrent parses should succeed");
    }
}

#[cfg(test)]
mod security_edge_cases {
    use super::*;

    #[tokio::test]
    async fn test_zip_bomb_detection() {
        // Test detection of potential zip bomb patterns
        let security = SecurityAnalyzer::new();
        let temp_dir = tempdir().unwrap();
        let file_path = temp_dir.path().join("potential_bomb.py");
        
        // Create file with zip bomb-like pattern
        let content = r#"
import zipfile
z = zipfile.ZipFile('bomb.zip', 'w')
for i in range(1000):
    z.writestr(f'file{i}.txt', 'A' * 1000000)
"#;
        fs::write(&file_path, content).unwrap();
        
        // Parse and analyze
        let parser = Parser::new(ParserConfig::default());
        let file_analysis = parser.parse_file(&file_path, "python").await.unwrap();
        
        let security_request = Default::default();
        let result = security
            .analyze_security("test-id", &[file_analysis], &security_request)
            .await;
        
        assert!(result.is_ok(), "Should analyze potential security risks");
    }

    #[tokio::test]
    async fn test_regex_dos_patterns() {
        // Test detection of ReDoS vulnerable patterns
        let security = SecurityAnalyzer::new();
        let temp_dir = tempdir().unwrap();
        let file_path = temp_dir.path().join("regex_dos.js");
        
        // Create file with ReDoS vulnerable regex
        let content = r#"
const emailRegex = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
const dangerousRegex = /(a+)+$/;  // ReDoS vulnerable
"#;
        fs::write(&file_path, content).unwrap();
        
        let parser = Parser::new(ParserConfig::default());
        let file_analysis = parser.parse_file(&file_path, "javascript").await.unwrap();
        
        let security_request = Default::default();
        let result = security
            .analyze_security("test-id", &[file_analysis], &security_request)
            .await;
        
        assert!(result.is_ok(), "Should detect ReDoS patterns");
        // The security analyzer should identify the vulnerable regex
    }
}

#[cfg(test)]
mod language_edge_cases {
    use super::*;

    #[tokio::test]
    async fn test_unknown_language() {
        // Test handling of unknown language
        let parser = Parser::new(ParserConfig::default());
        let temp_dir = tempdir().unwrap();
        let file_path = temp_dir.path().join("unknown.xyz");
        
        fs::write(&file_path, "some content").unwrap();
        
        let result = parser.parse_file(&file_path, "xyz").await;
        match result {
            Err(AnalysisError::Parser(ParserError::UnsupportedLanguage { language })) => {
                assert_eq!(language, "xyz");
            }
            _ => panic!("Expected UnsupportedLanguage error"),
        }
    }

    #[tokio::test]
    async fn test_polyglot_file() {
        // Test file that's valid in multiple languages
        let parser = Parser::new(ParserConfig::default());
        let temp_dir = tempdir().unwrap();
        let file_path = temp_dir.path().join("polyglot.txt");
        
        // This is valid Python and Ruby
        let content = "print(\"Hello, World!\")";
        fs::write(&file_path, content).unwrap();
        
        // Try parsing as Python
        let py_result = parser.parse_file(&file_path, "python").await;
        assert!(py_result.is_ok(), "Should parse as Python");
        
        // Try parsing as Ruby  
        let rb_result = parser.parse_file(&file_path, "ruby").await;
        assert!(rb_result.is_ok(), "Should parse as Ruby");
    }
}

#[test]
fn test_edge_cases_compile() {
    // Ensure all edge case tests compile
    println!("Edge case tests compiled successfully");
}