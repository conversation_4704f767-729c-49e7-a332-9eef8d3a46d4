use analysis_engine::storage::{StorageOperations, CacheManager};
use analysis_engine::models::*;
use analysis_engine::storage::redis_client::RedisClient;
use std::sync::Arc;
use tempfile::TempDir;
use uuid::Uuid;

#[tokio::test]
async fn test_storage_operations_creation() {
    // Test creating storage operations without actual GCP credentials
    // This tests the structure and basic functionality
    let result = std::panic::catch_unwind(|| {
        // This will fail without proper credentials, but we can test the structure
        tokio::runtime::Runtime::new().unwrap().block_on(async {
            // Just test that the types are properly defined
            let _storage_type = std::marker::PhantomData::<StorageOperations>;
            true
        })
    });
    
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_cache_manager_without_redis() {
    // Test cache manager behavior when Redis is not available
    let cache = CacheManager::new_without_redis();
    
    // Test getting non-existent analysis
    let result = cache.get_analysis("non-existent").await;
    assert!(result.is_ok());
    assert!(result.unwrap().is_none());
    
    // Test setting and getting analysis
    let analyses = vec![create_test_file_analysis()];
    let set_result = cache.set_analysis("test-id", &analyses).await;
    assert!(set_result.is_ok());
    
    let get_result = cache.get_analysis("test-id").await;
    assert!(get_result.is_ok());
    // Without Redis, this will return None as it falls back to no-op
    assert!(get_result.unwrap().is_none());
}

#[tokio::test]
async fn test_cache_manager_error_handling() {
    let cache = CacheManager::new_without_redis();
    
    // Test with invalid data
    let empty_analyses: Vec<FileAnalysis> = vec![];
    let result = cache.set_analysis("", &empty_analyses).await;
    assert!(result.is_ok()); // Should handle gracefully
    
    // Test with very long key
    let long_key = "a".repeat(1000);
    let result = cache.get_analysis(&long_key).await;
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_analysis_result_serialization() {
    let analysis_result = create_test_analysis_result();
    
    // Test JSON serialization
    let json_result = serde_json::to_string(&analysis_result);
    assert!(json_result.is_ok());
    
    let json_str = json_result.unwrap();
    assert!(!json_str.is_empty());
    
    // Test deserialization
    let deserialized: Result<AnalysisResult, _> = serde_json::from_str(&json_str);
    assert!(deserialized.is_ok());
    
    let deserialized_result = deserialized.unwrap();
    assert_eq!(deserialized_result.analysis_id, analysis_result.analysis_id);
    assert_eq!(deserialized_result.repository_url, analysis_result.repository_url);
}

#[tokio::test]
async fn test_file_analysis_validation() {
    let mut file_analysis = create_test_file_analysis();
    
    // Test valid file analysis
    assert!(!file_analysis.path.is_empty());
    assert!(!file_analysis.language.is_empty());
    assert!(!file_analysis.content_hash.is_empty());
    
    // Test with invalid data
    file_analysis.path = String::new();
    // Should still be valid structurally, just with empty path
    assert!(file_analysis.path.is_empty());
}

#[tokio::test]
async fn test_metrics_calculation() {
    let file_analysis = create_test_file_analysis();
    
    // Test metrics are properly set
    assert!(file_analysis.metrics.lines_of_code > 0);
    assert!(file_analysis.metrics.complexity > 0);
    assert!(file_analysis.metrics.maintainability_index >= 0.0);
    assert!(file_analysis.metrics.maintainability_index <= 100.0);
}

#[tokio::test]
async fn test_symbol_extraction() {
    let file_analysis = create_test_file_analysis();
    
    if let Some(symbols) = &file_analysis.symbols {
        for symbol in symbols {
            assert!(!symbol.name.is_empty());
            assert!(symbol.range.start.line <= symbol.range.end.line);
            if symbol.range.start.line == symbol.range.end.line {
                assert!(symbol.range.start.column <= symbol.range.end.column);
            }
        }
    }
}

#[tokio::test]
async fn test_chunk_extraction() {
    let file_analysis = create_test_file_analysis();
    
    if let Some(chunks) = &file_analysis.chunks {
        for chunk in chunks {
            assert!(!chunk.chunk_id.is_empty());
            assert!(!chunk.content.is_empty());
            assert!(chunk.range.start.line <= chunk.range.end.line);
        }
    }
}

#[tokio::test]
async fn test_warning_creation() {
    let warning = AnalysisWarning {
        warning_type: WarningType::ParseError,
        severity: WarningSeverity::High,
        message: "Test warning".to_string(),
        file_path: Some("test.rs".to_string()),
        line_number: Some(10),
        column_number: Some(5),
        context: None,
    };
    
    assert_eq!(warning.warning_type, WarningType::ParseError);
    assert_eq!(warning.severity, WarningSeverity::High);
    assert!(!warning.message.is_empty());
}

#[tokio::test]
async fn test_progress_update_creation() {
    let progress = ProgressUpdate {
        analysis_id: "test-123".to_string(),
        progress: 0.5,
        stage: "parsing".to_string(),
        message: Some("Processing files".to_string()),
        timestamp: chrono::Utc::now(),
        files_processed: Some(50),
        total_files: Some(100),
    };
    
    assert_eq!(progress.analysis_id, "test-123");
    assert_eq!(progress.progress, 0.5);
    assert_eq!(progress.stage, "parsing");
}

// Helper functions
fn create_test_file_analysis() -> FileAnalysis {
    FileAnalysis {
        path: "src/main.rs".to_string(),
        language: "rust".to_string(),
        content_hash: "abc123".to_string(),
        size_bytes: Some(1024),
        ast: AstNode {
            node_type: "source_file".to_string(),
            name: None,
            range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 10, column: 0, byte: 1024 },
            },
            children: vec![],
            properties: None,
            text: None,
        },
        metrics: FileMetrics {
            lines_of_code: 100,
            total_lines: Some(120),
            complexity: 5,
            maintainability_index: 75.0,
            function_count: Some(3),
            class_count: Some(1),
            comment_ratio: Some(0.1),
        },
        chunks: Some(vec![]),
        symbols: Some(vec![]),
    }
}

fn create_test_analysis_result() -> AnalysisResult {
    AnalysisResult {
        analysis_id: Uuid::new_v4().to_string(),
        repository_url: "https://github.com/test/repo".to_string(),
        commit_hash: Some("abc123".to_string()),
        branch: Some("main".to_string()),
        status: AnalysisStatus::Completed,
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
        completed_at: Some(chrono::Utc::now()),
        user_id: "user123".to_string(),
        files: vec![create_test_file_analysis()],
        metrics: RepositoryMetrics {
            total_files: 1,
            total_lines: 120,
            total_complexity: 5,
            average_complexity: 5.0,
            maintainability_score: 75.0,
            technical_debt_minutes: 30,
            test_coverage_estimate: 0.8,
        },
        languages: LanguageBreakdown {
            total_lines: 120,
            languages: std::collections::HashMap::new(),
        },
        embeddings: vec![],
        patterns: vec![],
        warnings: vec![],
        performance: PerformanceMetrics::default(),
        repository_size_bytes: Some(1024),
        clone_time_ms: Some(1000),
    }
}
