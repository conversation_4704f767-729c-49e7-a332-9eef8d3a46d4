use analysis_engine::backpressure::{BackpressureManager, BackpressureConfig, BackpressureDecision, BackpressureReason};
use std::time::Duration;
use tokio::time::sleep;

#[tokio::test]
async fn test_backpressure_manager_creation() {
    let config = BackpressureConfig::default();
    let manager = BackpressureManager::new(config);
    
    // Test that manager is created successfully
    let metrics = manager.get_metrics().await;
    assert_eq!(metrics.memory_usage_mb, 0);
    assert_eq!(metrics.cpu_usage_percent, 0.0);
}

#[tokio::test]
async fn test_analysis_request_allowed() {
    let config = BackpressureConfig::default();
    let manager = BackpressureManager::new(config);
    
    // Should allow request under normal conditions
    let decision = manager.check_analysis_request().await;
    assert!(matches!(decision, BackpressureDecision::Allow));
}

#[tokio::test]
async fn test_memory_pressure_rejection() {
    let mut config = BackpressureConfig::default();
    config.memory_threshold_mb = 100; // Low threshold for testing
    let manager = BackpressureManager::new(config);
    
    // Simulate high memory usage
    let high_memory_metrics = analysis_engine::backpressure::BackpressureMetrics {
        memory_usage_mb: 200, // Above threshold
        cpu_usage_percent: 50.0,
        queued_requests: 10,
        active_requests: 5,
        last_updated: 0,
    };
    
    manager.update_metrics(high_memory_metrics).await.unwrap();
    
    let decision = manager.check_analysis_request().await;
    assert!(matches!(decision, BackpressureDecision::Reject(BackpressureReason::MemoryPressure)));
}

#[tokio::test]
async fn test_cpu_pressure_rejection() {
    let mut config = BackpressureConfig::default();
    config.cpu_threshold_percent = 70.0; // Lower threshold for testing
    let manager = BackpressureManager::new(config);
    
    // Simulate high CPU usage
    let high_cpu_metrics = analysis_engine::backpressure::BackpressureMetrics {
        memory_usage_mb: 1000,
        cpu_usage_percent: 85.0, // Above threshold
        queued_requests: 10,
        active_requests: 5,
        last_updated: 0,
    };
    
    manager.update_metrics(high_cpu_metrics).await.unwrap();
    
    let decision = manager.check_analysis_request().await;
    assert!(matches!(decision, BackpressureDecision::Reject(BackpressureReason::CpuPressure)));
}

#[tokio::test]
async fn test_queue_overflow_rejection() {
    let mut config = BackpressureConfig::default();
    config.queue_size_threshold = 50; // Lower threshold for testing
    let manager = BackpressureManager::new(config);
    
    // Simulate queue overflow
    let queue_overflow_metrics = analysis_engine::backpressure::BackpressureMetrics {
        memory_usage_mb: 1000,
        cpu_usage_percent: 50.0,
        queued_requests: 100, // Above threshold
        active_requests: 5,
        last_updated: 0,
    };
    
    manager.update_metrics(queue_overflow_metrics).await.unwrap();
    
    let decision = manager.check_analysis_request().await;
    assert!(matches!(decision, BackpressureDecision::Reject(BackpressureReason::QueueOverflow)));
}

#[tokio::test]
async fn test_permit_acquisition() {
    let config = BackpressureConfig::default();
    let manager = BackpressureManager::new(config);
    
    // Test acquiring analysis permit
    let permit = manager.acquire_analysis_permit().await;
    assert!(permit.is_ok());
    
    // Test acquiring parsing permit
    let permit = manager.acquire_parsing_permit().await;
    assert!(permit.is_ok());
    
    // Test acquiring database permit
    let permit = manager.acquire_database_permit().await;
    assert!(permit.is_ok());
    
    // Test acquiring storage permit
    let permit = manager.acquire_storage_permit().await;
    assert!(permit.is_ok());
}

#[tokio::test]
async fn test_circuit_breaker_functionality() {
    let config = BackpressureConfig {
        circuit_breaker_failure_threshold: 2,
        circuit_breaker_timeout: Duration::from_millis(100),
        ..Default::default()
    };
    let manager = BackpressureManager::new(config);
    
    // Record failures to trigger circuit breaker
    manager.record_failure("database").await;
    manager.record_failure("database").await;
    
    // Circuit breaker should be open now
    let decision = manager.check_analysis_request().await;
    assert!(matches!(decision, BackpressureDecision::Reject(BackpressureReason::CircuitBreakerOpen(_))));
    
    // Wait for circuit breaker timeout
    sleep(Duration::from_millis(150)).await;
    
    // Should transition to half-open and allow requests
    let decision = manager.check_analysis_request().await;
    assert!(matches!(decision, BackpressureDecision::Allow));
    
    // Record success to close circuit breaker
    manager.record_success("database").await;
    
    let decision = manager.check_analysis_request().await;
    assert!(matches!(decision, BackpressureDecision::Allow));
}

#[tokio::test]
async fn test_execute_with_circuit_breaker() {
    let config = BackpressureConfig::default();
    let manager = BackpressureManager::new(config);
    
    // Test successful operation
    let result = manager.execute_with_circuit_breaker("test", || async {
        Ok::<i32, &str>(42)
    }).await;
    
    assert!(result.is_ok());
    assert_eq!(result.unwrap(), 42);
    
    // Test failing operation
    let result = manager.execute_with_circuit_breaker("test", || async {
        Err::<i32, &str>("test error")
    }).await;
    
    assert!(result.is_err());
}

#[tokio::test]
async fn test_throttling_behavior() {
    let config = BackpressureConfig::default();
    let manager = BackpressureManager::new(config);
    
    // Simulate moderate load that should trigger throttling
    let moderate_load_metrics = analysis_engine::backpressure::BackpressureMetrics {
        memory_usage_mb: 2500, // Below threshold but high
        cpu_usage_percent: 70.0, // Below threshold but high
        queued_requests: 800, // Below threshold but high
        active_requests: 40,
        last_updated: 0,
    };
    
    manager.update_metrics(moderate_load_metrics).await.unwrap();
    
    let decision = manager.check_analysis_request().await;
    // Should either allow or throttle, but not reject
    assert!(!matches!(decision, BackpressureDecision::Reject(_)));
}
