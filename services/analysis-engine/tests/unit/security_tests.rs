use analysis_engine::api::middleware::auth::*;
use analysis_engine::audit::{AuditLogger, AuditEvent, AuditAction, AuditOutcome, AuditSeverity};
use std::collections::HashMap;

#[tokio::test]
async fn test_api_key_generation() {
    // Test API key generation functionality
    let result = generate_api_key();
    assert!(result.is_ok());
    
    let (api_key, salt, hash) = result.unwrap();
    assert!(!api_key.is_empty());
    assert!(!salt.is_empty());
    assert!(!hash.is_empty());
    
    // API key should be reasonable length
    assert!(api_key.len() >= 32);
    assert!(salt.len() >= 16);
    assert!(hash.len() >= 32);
}

#[tokio::test]
async fn test_api_key_hashing_verification() {
    let api_key = "test-api-key-12345";
    let salt = generate_salt();
    
    // Test hashing
    let hash_result = hash_api_key_with_salt(api_key, &salt);
    assert!(hash_result.is_ok());
    
    let hash = hash_result.unwrap();
    assert!(!hash.is_empty());
    
    // Test verification with correct key
    let verify_result = verify_api_key(api_key, &hash, &salt);
    assert!(verify_result.is_ok());
    assert!(verify_result.unwrap());
    
    // Test verification with incorrect key
    let verify_result = verify_api_key("wrong-key", &hash, &salt);
    assert!(verify_result.is_ok());
    assert!(!verify_result.unwrap());
}

#[tokio::test]
async fn test_salt_generation() {
    let salt1 = generate_salt();
    let salt2 = generate_salt();
    
    // Salts should be different
    assert_ne!(salt1, salt2);
    
    // Salts should be reasonable length
    assert!(salt1.len() >= 16);
    assert!(salt2.len() >= 16);
}

#[tokio::test]
async fn test_jwt_key_management() {
    let mut manager = KeyManager::new();
    
    // Test adding a key
    let key = DecodingKey::from_secret(b"test-secret");
    manager.add_key("test-key".to_string(), key, Algorithm::HS256, None);
    
    // Test getting the key
    let retrieved_key = manager.get_key("test-key");
    assert!(retrieved_key.is_some());
    
    // Test getting non-existent key
    let non_existent = manager.get_key("non-existent");
    assert!(non_existent.is_none());
}

#[tokio::test]
async fn test_audit_event_creation() {
    let event = AuditEvent {
        event_id: "test-event-123".to_string(),
        timestamp: chrono::Utc::now(),
        user_id: Some("user123".to_string()),
        action: AuditAction::AnalysisCreated,
        outcome: AuditOutcome::Success,
        severity: AuditSeverity::Info,
        resource_type: "analysis".to_string(),
        resource_id: Some("analysis-456".to_string()),
        ip_address: Some("***********".to_string()),
        user_agent: Some("test-agent".to_string()),
        request_id: Some("req-789".to_string()),
        session_id: Some("session-abc".to_string()),
        organization_id: Some("org-def".to_string()),
        details: HashMap::new(),
        error_message: None,
    };
    
    assert_eq!(event.action, AuditAction::AnalysisCreated);
    assert_eq!(event.outcome, AuditOutcome::Success);
    assert_eq!(event.severity, AuditSeverity::Info);
    assert!(!event.event_id.is_empty());
}

#[tokio::test]
async fn test_audit_logger_creation() {
    // Test creating audit logger without database
    let logger = AuditLogger::new_without_database();
    
    // Test logging an event
    let event = AuditEvent {
        event_id: "test-event-123".to_string(),
        timestamp: chrono::Utc::now(),
        user_id: Some("user123".to_string()),
        action: AuditAction::AuthenticationAttempt,
        outcome: AuditOutcome::Success,
        severity: AuditSeverity::Info,
        resource_type: "auth".to_string(),
        resource_id: None,
        ip_address: Some("***********".to_string()),
        user_agent: Some("test-agent".to_string()),
        request_id: Some("req-789".to_string()),
        session_id: Some("session-abc".to_string()),
        organization_id: Some("org-def".to_string()),
        details: HashMap::new(),
        error_message: None,
    };
    
    let result = logger.log_event(event).await;
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_security_headers_parsing() {
    // Test that security header values can be parsed correctly
    let test_values = vec![
        "nosniff",
        "DENY",
        "1; mode=block",
        "no-store, no-cache, must-revalidate, private",
        "no-cache",
        "0",
        "max-age=31536000; includeSubDomains; preload",
        "strict-origin-when-cross-origin",
        "require-corp",
        "same-origin",
    ];
    
    for value in test_values {
        let parse_result: Result<axum::http::HeaderValue, _> = value.parse();
        assert!(parse_result.is_ok(), "Failed to parse header value: {}", value);
    }
}

#[tokio::test]
async fn test_device_fingerprint_generation() {
    // Test device fingerprint generation with mock request data
    let fingerprint1 = generate_test_device_fingerprint("***********", "Mozilla/5.0");
    let fingerprint2 = generate_test_device_fingerprint("***********", "Mozilla/5.0");
    let fingerprint3 = generate_test_device_fingerprint("***********", "Chrome/91.0");
    
    // Different IPs should generate different fingerprints
    assert_ne!(fingerprint1, fingerprint2);
    
    // Different user agents should generate different fingerprints
    assert_ne!(fingerprint1, fingerprint3);
    
    // Same inputs should generate same fingerprint
    let fingerprint4 = generate_test_device_fingerprint("***********", "Mozilla/5.0");
    assert_eq!(fingerprint1, fingerprint4);
}

#[tokio::test]
async fn test_rate_limit_error_response() {
    let error = create_rate_limit_error_response(
        "Rate limit exceeded".to_string(),
        Some(60),
    );
    
    // Test that error response is properly formatted
    assert!(error.to_string().contains("Rate limit exceeded"));
}

#[tokio::test]
async fn test_auth_error_response() {
    let error = create_auth_error_response(
        "Invalid token".to_string(),
        "INVALID_TOKEN".to_string(),
    );
    
    // Test that auth error response is properly formatted
    assert!(error.to_string().contains("Invalid token"));
}

#[tokio::test]
async fn test_token_revocation() {
    // Test token revocation functionality
    let jti = "test-token-123";
    
    // Test revoking a token
    let result = revoke_token(jti);
    assert!(result.is_ok());
    
    // Test checking if token is revoked
    let is_revoked = is_token_revoked(jti);
    assert!(is_revoked);
    
    // Test revoking a session
    let session_id = "test-session-456";
    let result = revoke_session(session_id);
    assert!(result.is_ok());
    
    let is_revoked = is_session_revoked(session_id);
    assert!(is_revoked);
}

#[tokio::test]
async fn test_revocation_cleanup() {
    // Test cleanup of revoked tokens
    cleanup_revoked_tokens();
    
    // Test getting revocation stats
    let (token_count, session_count) = get_revocation_stats();
    assert!(token_count >= 0);
    assert!(session_count >= 0);
}

// Helper functions for testing
fn generate_test_device_fingerprint(ip: &str, user_agent: &str) -> String {
    use sha2::{Sha256, Digest};
    let mut hasher = Sha256::new();
    hasher.update(ip.as_bytes());
    hasher.update(user_agent.as_bytes());
    format!("{:x}", hasher.finalize())
}

// Mock implementations for testing
use jsonwebtoken::{DecodingKey, Algorithm};

struct KeyManager {
    keys: HashMap<String, JwtKey>,
}

#[derive(Clone)]
struct JwtKey {
    kid: String,
    key: DecodingKey,
}

impl KeyManager {
    fn new() -> Self {
        Self {
            keys: HashMap::new(),
        }
    }
    
    fn add_key(&mut self, kid: String, key: DecodingKey, _algorithm: Algorithm, _expiry: Option<chrono::DateTime<chrono::Utc>>) {
        self.keys.insert(kid.clone(), JwtKey { kid, key });
    }
    
    fn get_key(&self, kid: &str) -> Option<&JwtKey> {
        self.keys.get(kid)
    }
}
