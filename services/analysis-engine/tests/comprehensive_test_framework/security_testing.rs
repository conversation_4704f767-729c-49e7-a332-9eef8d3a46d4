//! Security testing module for the Analysis Engine
//! 
//! This module provides comprehensive security testing capabilities including:
//! - Known vulnerability detection accuracy testing
//! - Security scanner effectiveness validation
//! - False positive rate measurement
//! - Security compliance testing
//! - Threat modeling validation
//! - Security intelligence integration testing

use std::collections::HashMap;
use std::time::{Duration, Instant};
use serde_json::{json, Value};
use uuid::Uuid;

use super::test_utils::*;
use super::mock_services::*;

/// Security testing coordinator
pub struct SecurityTestRunner {
    client: reqwest::Client,
    base_url: String,
    api_key: String,
    config: SecurityTestConfig,
}

/// Configuration for security tests
#[derive(Clone, Debug)]
pub struct SecurityTestConfig {
    pub enable_vulnerability_detection: bool,
    pub enable_secrets_scanning: bool,
    pub enable_compliance_checking: bool,
    pub enable_threat_modeling: bool,
    pub target_detection_accuracy: f64,
    pub max_false_positive_rate: f64,
    pub security_thresholds: HashMap<String, f64>,
}

impl Default for SecurityTestConfig {
    fn default() -> Self {
        let mut thresholds = HashMap::new();
        thresholds.insert("min_vulnerability_detection_rate".to_string(), 95.0);
        thresholds.insert("max_false_positive_rate".to_string(), 5.0);
        thresholds.insert("min_security_score".to_string(), 80.0);
        thresholds.insert("max_scan_time_seconds".to_string(), 60.0);
        
        Self {
            enable_vulnerability_detection: true,
            enable_secrets_scanning: true,
            enable_compliance_checking: true,
            enable_threat_modeling: true,
            target_detection_accuracy: 95.0,
            max_false_positive_rate: 5.0,
            security_thresholds: thresholds,
        }
    }
}

impl SecurityTestRunner {
    pub fn new(base_url: String, api_key: String) -> Self {
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(120))
            .build()
            .expect("Failed to create HTTP client");

        Self {
            client,
            base_url,
            api_key,
            config: SecurityTestConfig::default(),
        }
    }

    /// Run comprehensive security test suite
    pub async fn run_security_tests(&self) -> Result<SecurityTestResults, SecurityTestError> {
        let mut results = SecurityTestResults::new();
        
        println!("🔒 Starting security test suite...");
        
        // Test 1: Known Vulnerability Detection
        println!("🐛 Testing known vulnerability detection...");
        let vuln_result = self.test_known_vulnerability_detection().await?;
        results.add_test_result("known_vulnerability_detection", vuln_result);
        
        // Test 2: Secrets Detection
        println!("🔑 Testing secrets detection...");
        let secrets_result = self.test_secrets_detection().await?;
        results.add_test_result("secrets_detection", secrets_result);
        
        // Test 3: False Positive Rate Validation
        println!("✅ Testing false positive rate...");
        let false_positive_result = self.test_false_positive_rate().await?;
        results.add_test_result("false_positive_rate", false_positive_result);
        
        // Test 4: Security Compliance Testing
        println!("📋 Testing security compliance...");
        let compliance_result = self.test_security_compliance().await?;
        results.add_test_result("security_compliance", compliance_result);
        
        // Test 5: Threat Modeling Validation
        println!("🎯 Testing threat modeling...");
        let threat_modeling_result = self.test_threat_modeling().await?;
        results.add_test_result("threat_modeling", threat_modeling_result);
        
        // Test 6: Security Intelligence Integration
        println!("🧠 Testing security intelligence integration...");
        let security_intel_result = self.test_security_intelligence_integration().await?;
        results.add_test_result("security_intelligence", security_intel_result);
        
        // Test 7: Real-world Vulnerability Scenarios
        println!("🌍 Testing real-world vulnerability scenarios...");
        let realworld_result = self.test_realworld_vulnerability_scenarios().await?;
        results.add_test_result("realworld_vulnerabilities", realworld_result);
        
        // Test 8: Security Performance Under Load
        println!("⚡ Testing security performance under load...");
        let performance_result = self.test_security_performance_under_load().await?;
        results.add_test_result("security_performance", performance_result);
        
        results.finalize();
        Ok(results)
    }

    /// Test known vulnerability detection accuracy
    pub async fn test_known_vulnerability_detection(&self) -> Result<SecurityTestResult, SecurityTestError> {
        let start_time = Instant::now();
        let vulnerability_test_cases = TestFixtures::known_vulnerabilities_test_cases();
        
        let mut detection_results = Vec::new();
        let mut total_vulnerabilities = 0;
        let mut detected_vulnerabilities = 0;
        let mut false_positives = 0;
        
        for test_case in &vulnerability_test_cases {
            println!("  Testing {} vulnerability detection...", test_case.name);
            
            // Create a temporary test repository with the vulnerable code
            let analysis_id = TestDataGenerator::generate_analysis_id();
            let request_body = json!({
                "analysis_id": analysis_id,
                "repository_url": "https://github.com/test/vulnerable-code.git",
                "branch": "main",
                "include_patterns": [format!("**/*.{}", Self::get_file_extension(&test_case.language))],
                "exclude_patterns": [],
                "languages": [test_case.language],
                "enable_patterns": false,
                "enable_embeddings": false,
                "enable_security_scanning": true,
                "test_code": test_case.code,
                "analysis_depth": "Deep"
            });

            let response = self.client
                .post(&format!("{}/api/v1/analysis", self.base_url))
                .header("X-API-Key", &self.api_key)
                .json(&request_body)
                .send()
                .await?;

            if response.status().is_success() {
                let analysis_result: Value = response.json().await?;
                let analysis_id = analysis_result["analysis_id"].as_str().unwrap();
                
                // Wait for analysis to complete
                let security_results = self.wait_for_security_analysis(analysis_id).await?;
                
                // Check if the expected vulnerability was detected
                let detected = self.check_vulnerability_detected(&security_results, &test_case.expected_vulnerability);
                
                total_vulnerabilities += 1;
                if detected {
                    detected_vulnerabilities += 1;
                }
                
                detection_results.push(VulnerabilityDetectionResult {
                    test_case: test_case.clone(),
                    detected,
                    confidence: security_results.get("confidence").and_then(|v| v.as_f64()).unwrap_or(0.0),
                    scan_time: Duration::from_secs(1), // Would be measured in real implementation
                });
            }
        }
        
        let detection_rate = (detected_vulnerabilities as f64 / total_vulnerabilities as f64) * 100.0;
        let false_positive_rate = (false_positives as f64 / total_vulnerabilities as f64) * 100.0;
        
        let total_duration = start_time.elapsed();
        
        let mut metrics = HashMap::new();
        metrics.insert("total_vulnerabilities".to_string(), total_vulnerabilities as f64);
        metrics.insert("detected_vulnerabilities".to_string(), detected_vulnerabilities as f64);
        metrics.insert("detection_rate".to_string(), detection_rate);
        metrics.insert("false_positive_rate".to_string(), false_positive_rate);
        metrics.insert("false_positives".to_string(), false_positives as f64);
        
        // Check if test passed based on thresholds
        let passed = detection_rate >= self.config.security_thresholds["min_vulnerability_detection_rate"] &&
                    false_positive_rate <= self.config.security_thresholds["max_false_positive_rate"];
        
        Ok(SecurityTestResult {
            name: "Known Vulnerability Detection".to_string(),
            passed,
            duration: total_duration,
            metrics,
            details: format!(
                "Detected {}/{} vulnerabilities ({:.1}% detection rate, {:.1}% false positive rate)",
                detected_vulnerabilities, total_vulnerabilities, detection_rate, false_positive_rate
            ),
            vulnerabilities_tested: vulnerability_test_cases.len(),
            detection_accuracy: detection_rate / 100.0,
        })
    }

    /// Test secrets detection capabilities
    pub async fn test_secrets_detection(&self) -> Result<SecurityTestResult, SecurityTestError> {
        let start_time = Instant::now();
        
        // Test cases for different types of secrets
        let secret_test_cases = vec![
            SecretTestCase {
                name: "AWS Access Key".to_string(),
                code: "const AWS_ACCESS_KEY_ID = 'AKIA1234567890ABCDEF';".to_string(),
                expected_secret_type: "aws_access_key".to_string(),
                should_detect: true,
            },
            SecretTestCase {
                name: "GitHub Token".to_string(),
                code: "const GITHUB_TOKEN = 'ghp_1234567890abcdef1234567890abcdef12345678';".to_string(),
                expected_secret_type: "github_token".to_string(),
                should_detect: true,
            },
            SecretTestCase {
                name: "Database Password".to_string(),
                code: "const DB_PASSWORD = 'super_secret_password_123';".to_string(),
                expected_secret_type: "password".to_string(),
                should_detect: true,
            },
            SecretTestCase {
                name: "API Key".to_string(),
                code: "const API_KEY = 'sk-1234567890abcdef1234567890abcdef';".to_string(),
                expected_secret_type: "api_key".to_string(),
                should_detect: true,
            },
            SecretTestCase {
                name: "False Positive Test".to_string(),
                code: "const EXAMPLE_KEY = 'this_is_just_an_example_not_a_real_secret';".to_string(),
                expected_secret_type: "".to_string(),
                should_detect: false,
            },
        ];
        
        let mut detection_results = Vec::new();
        let mut total_secrets = 0;
        let mut detected_secrets = 0;
        let mut false_positives = 0;
        
        for test_case in &secret_test_cases {
            println!("  Testing {} detection...", test_case.name);
            
            let analysis_id = TestDataGenerator::generate_analysis_id();
            let request_body = json!({
                "analysis_id": analysis_id,
                "repository_url": "https://github.com/test/secrets-test.git",
                "branch": "main",
                "include_patterns": ["**/*.js"],
                "exclude_patterns": [],
                "languages": ["javascript"],
                "enable_patterns": false,
                "enable_embeddings": false,
                "enable_security_scanning": true,
                "enable_secrets_detection": true,
                "test_code": test_case.code,
                "analysis_depth": "Deep"
            });

            let response = self.client
                .post(&format!("{}/api/v1/analysis", self.base_url))
                .header("X-API-Key", &self.api_key)
                .json(&request_body)
                .send()
                .await?;

            if response.status().is_success() {
                let analysis_result: Value = response.json().await?;
                let analysis_id = analysis_result["analysis_id"].as_str().unwrap();
                
                let security_results = self.wait_for_security_analysis(analysis_id).await?;
                
                let detected = self.check_secret_detected(&security_results, &test_case.expected_secret_type);
                
                if test_case.should_detect {
                    total_secrets += 1;
                    if detected {
                        detected_secrets += 1;
                    }
                } else if detected {
                    false_positives += 1;
                }
                
                detection_results.push(SecretDetectionResult {
                    test_case: test_case.clone(),
                    detected,
                    confidence: security_results.get("confidence").and_then(|v| v.as_f64()).unwrap_or(0.0),
                });
            }
        }
        
        let detection_rate = if total_secrets > 0 {
            (detected_secrets as f64 / total_secrets as f64) * 100.0
        } else {
            0.0
        };
        
        let false_positive_rate = (false_positives as f64 / secret_test_cases.len() as f64) * 100.0;
        
        let total_duration = start_time.elapsed();
        
        let mut metrics = HashMap::new();
        metrics.insert("total_secrets".to_string(), total_secrets as f64);
        metrics.insert("detected_secrets".to_string(), detected_secrets as f64);
        metrics.insert("detection_rate".to_string(), detection_rate);
        metrics.insert("false_positive_rate".to_string(), false_positive_rate);
        metrics.insert("false_positives".to_string(), false_positives as f64);
        
        let passed = detection_rate >= 90.0 && false_positive_rate <= 10.0;
        
        Ok(SecurityTestResult {
            name: "Secrets Detection".to_string(),
            passed,
            duration: total_duration,
            metrics,
            details: format!(
                "Detected {}/{} secrets ({:.1}% detection rate, {:.1}% false positive rate)",
                detected_secrets, total_secrets, detection_rate, false_positive_rate
            ),
            vulnerabilities_tested: secret_test_cases.len(),
            detection_accuracy: detection_rate / 100.0,
        })
    }

    /// Test false positive rate validation
    pub async fn test_false_positive_rate(&self) -> Result<SecurityTestResult, SecurityTestError> {
        let start_time = Instant::now();
        
        // Test with clean code that should not trigger security warnings
        let clean_code_samples = vec![
            CleanCodeSample {
                name: "Simple Function".to_string(),
                language: "rust".to_string(),
                code: "fn add(a: i32, b: i32) -> i32 { a + b }".to_string(),
            },
            CleanCodeSample {
                name: "Safe String Handling".to_string(),
                language: "javascript".to_string(),
                code: "const message = 'Hello, World!'; console.log(message);".to_string(),
            },
            CleanCodeSample {
                name: "Secure Authentication".to_string(),
                language: "python".to_string(),
                code: "import hashlib\ndef hash_password(password):\n    return hashlib.sha256(password.encode()).hexdigest()".to_string(),
            },
            CleanCodeSample {
                name: "Memory Safe Code".to_string(),
                language: "rust".to_string(),
                code: "fn process_data(data: &[u8]) -> Vec<u8> { data.to_vec() }".to_string(),
            },
        ];
        
        let mut false_positives = 0;
        let mut total_samples = clean_code_samples.len();
        
        for sample in &clean_code_samples {
            println!("  Testing clean code sample: {}", sample.name);
            
            let analysis_id = TestDataGenerator::generate_analysis_id();
            let request_body = json!({
                "analysis_id": analysis_id,
                "repository_url": "https://github.com/test/clean-code.git",
                "branch": "main",
                "include_patterns": [format!("**/*.{}", Self::get_file_extension(&sample.language))],
                "exclude_patterns": [],
                "languages": [sample.language],
                "enable_patterns": false,
                "enable_embeddings": false,
                "enable_security_scanning": true,
                "test_code": sample.code,
                "analysis_depth": "Deep"
            });

            let response = self.client
                .post(&format!("{}/api/v1/analysis", self.base_url))
                .header("X-API-Key", &self.api_key)
                .json(&request_body)
                .send()
                .await?;

            if response.status().is_success() {
                let analysis_result: Value = response.json().await?;
                let analysis_id = analysis_result["analysis_id"].as_str().unwrap();
                
                let security_results = self.wait_for_security_analysis(analysis_id).await?;
                
                // Check if any vulnerabilities were incorrectly detected
                if let Some(vulnerabilities) = security_results.get("vulnerabilities") {
                    if vulnerabilities.as_array().map(|v| v.len()).unwrap_or(0) > 0 {
                        false_positives += 1;
                        println!("    ⚠️  False positive detected in {}", sample.name);
                    }
                }
            }
        }
        
        let false_positive_rate = (false_positives as f64 / total_samples as f64) * 100.0;
        let total_duration = start_time.elapsed();
        
        let mut metrics = HashMap::new();
        metrics.insert("total_samples".to_string(), total_samples as f64);
        metrics.insert("false_positives".to_string(), false_positives as f64);
        metrics.insert("false_positive_rate".to_string(), false_positive_rate);
        
        let passed = false_positive_rate <= self.config.max_false_positive_rate;
        
        Ok(SecurityTestResult {
            name: "False Positive Rate".to_string(),
            passed,
            duration: total_duration,
            metrics,
            details: format!(
                "False positive rate: {:.1}% ({}/{} samples triggered false positives)",
                false_positive_rate, false_positives, total_samples
            ),
            vulnerabilities_tested: total_samples,
            detection_accuracy: 1.0 - (false_positive_rate / 100.0),
        })
    }

    /// Test security compliance checking
    pub async fn test_security_compliance(&self) -> Result<SecurityTestResult, SecurityTestError> {
        let start_time = Instant::now();
        
        // Test compliance with various security frameworks
        let compliance_frameworks = vec!["OWASP", "CWE", "NIST", "ISO27001"];
        let mut compliance_results = HashMap::new();
        
        for framework in &compliance_frameworks {
            println!("  Testing {} compliance...", framework);
            
            let analysis_id = TestDataGenerator::generate_analysis_id();
            let request_body = json!({
                "analysis_id": analysis_id,
                "repository_url": "https://github.com/test/compliance-test.git",
                "branch": "main",
                "include_patterns": ["**/*.js", "**/*.ts", "**/*.py"],
                "exclude_patterns": [],
                "languages": ["javascript", "typescript", "python"],
                "enable_patterns": false,
                "enable_embeddings": false,
                "enable_security_scanning": true,
                "enable_compliance_checking": true,
                "compliance_frameworks": [framework],
                "analysis_depth": "Deep"
            });

            let response = self.client
                .post(&format!("{}/api/v1/analysis", self.base_url))
                .header("X-API-Key", &self.api_key)
                .json(&request_body)
                .send()
                .await?;

            if response.status().is_success() {
                let analysis_result: Value = response.json().await?;
                let analysis_id = analysis_result["analysis_id"].as_str().unwrap();
                
                let security_results = self.wait_for_security_analysis(analysis_id).await?;
                
                // Extract compliance score
                let compliance_score = security_results
                    .get("compliance_assessment")
                    .and_then(|ca| ca.get("score"))
                    .and_then(|s| s.as_f64())
                    .unwrap_or(0.0);
                
                compliance_results.insert(framework.to_string(), compliance_score);
            }
        }
        
        let total_duration = start_time.elapsed();
        
        let avg_compliance_score = compliance_results.values().sum::<f64>() / compliance_results.len() as f64;
        
        let mut metrics = HashMap::new();
        metrics.insert("frameworks_tested".to_string(), compliance_frameworks.len() as f64);
        metrics.insert("avg_compliance_score".to_string(), avg_compliance_score);
        
        for (framework, score) in &compliance_results {
            metrics.insert(format!("{}_compliance_score", framework.to_lowercase()), *score);
        }
        
        let passed = avg_compliance_score >= self.config.security_thresholds["min_security_score"];
        
        Ok(SecurityTestResult {
            name: "Security Compliance".to_string(),
            passed,
            duration: total_duration,
            metrics,
            details: format!(
                "Average compliance score: {:.1}% across {} frameworks",
                avg_compliance_score, compliance_frameworks.len()
            ),
            vulnerabilities_tested: compliance_frameworks.len(),
            detection_accuracy: avg_compliance_score / 100.0,
        })
    }

    /// Test threat modeling capabilities
    pub async fn test_threat_modeling(&self) -> Result<SecurityTestResult, SecurityTestError> {
        let start_time = Instant::now();
        
        // Test threat modeling with various attack scenarios
        let threat_scenarios = vec![
            ThreatScenario {
                name: "SQL Injection Attack".to_string(),
                code: "SELECT * FROM users WHERE id = " + userId,
                expected_threats: vec!["data_breach".to_string(), "unauthorized_access".to_string()],
            },
            ThreatScenario {
                name: "Cross-Site Scripting".to_string(),
                code: "document.innerHTML = userInput;".to_string(),
                expected_threats: vec!["xss_attack".to_string(), "session_hijacking".to_string()],
            },
            ThreatScenario {
                name: "Command Injection".to_string(),
                code: "os.system('rm -rf ' + user_path)".to_string(),
                expected_threats: vec!["command_injection".to_string(), "privilege_escalation".to_string()],
            },
        ];
        
        let mut threat_modeling_results = Vec::new();
        let mut total_threats = 0;
        let mut detected_threats = 0;
        
        for scenario in &threat_scenarios {
            println!("  Testing threat scenario: {}", scenario.name);
            
            let analysis_id = TestDataGenerator::generate_analysis_id();
            let request_body = json!({
                "analysis_id": analysis_id,
                "repository_url": "https://github.com/test/threat-modeling.git",
                "branch": "main",
                "include_patterns": ["**/*.py", "**/*.js"],
                "exclude_patterns": [],
                "languages": ["python", "javascript"],
                "enable_patterns": false,
                "enable_embeddings": false,
                "enable_security_scanning": true,
                "enable_threat_modeling": true,
                "test_code": scenario.code,
                "analysis_depth": "Deep"
            });

            let response = self.client
                .post(&format!("{}/api/v1/analysis", self.base_url))
                .header("X-API-Key", &self.api_key)
                .json(&request_body)
                .send()
                .await?;

            if response.status().is_success() {
                let analysis_result: Value = response.json().await?;
                let analysis_id = analysis_result["analysis_id"].as_str().unwrap();
                
                let security_results = self.wait_for_security_analysis(analysis_id).await?;
                
                // Check for threat models
                if let Some(threat_models) = security_results.get("threat_models") {
                    if let Some(threat_array) = threat_models.as_array() {
                        for threat in threat_array {
                            if let Some(threat_type) = threat.get("threat_type").and_then(|t| t.as_str()) {
                                if scenario.expected_threats.contains(&threat_type.to_string()) {
                                    detected_threats += 1;
                                }
                            }
                        }
                    }
                }
                
                total_threats += scenario.expected_threats.len();
            }
        }
        
        let threat_detection_rate = if total_threats > 0 {
            (detected_threats as f64 / total_threats as f64) * 100.0
        } else {
            0.0
        };
        
        let total_duration = start_time.elapsed();
        
        let mut metrics = HashMap::new();
        metrics.insert("scenarios_tested".to_string(), threat_scenarios.len() as f64);
        metrics.insert("total_threats".to_string(), total_threats as f64);
        metrics.insert("detected_threats".to_string(), detected_threats as f64);
        metrics.insert("threat_detection_rate".to_string(), threat_detection_rate);
        
        let passed = threat_detection_rate >= 80.0;
        
        Ok(SecurityTestResult {
            name: "Threat Modeling".to_string(),
            passed,
            duration: total_duration,
            metrics,
            details: format!(
                "Threat detection rate: {:.1}% ({}/{} threats detected)",
                threat_detection_rate, detected_threats, total_threats
            ),
            vulnerabilities_tested: threat_scenarios.len(),
            detection_accuracy: threat_detection_rate / 100.0,
        })
    }

    /// Test security intelligence integration
    pub async fn test_security_intelligence_integration(&self) -> Result<SecurityTestResult, SecurityTestError> {
        let start_time = Instant::now();
        
        // Test integration with threat intelligence feeds
        let analysis_id = TestDataGenerator::generate_analysis_id();
        let request_body = json!({
            "analysis_id": analysis_id,
            "repository_url": "https://github.com/test/security-intelligence.git",
            "branch": "main",
            "include_patterns": ["**/*.js", "**/*.py", "**/*.java"],
            "exclude_patterns": [],
            "languages": ["javascript", "python", "java"],
            "enable_patterns": false,
            "enable_embeddings": false,
            "enable_security_scanning": true,
            "enable_threat_intelligence": true,
            "analysis_depth": "Deep"
        });

        let response = self.client
            .post(&format!("{}/api/v1/analysis", self.base_url))
            .header("X-API-Key", &self.api_key)
            .json(&request_body)
            .send()
            .await?;

        let mut intelligence_features_tested = 0;
        let mut intelligence_features_working = 0;
        
        if response.status().is_success() {
            let analysis_result: Value = response.json().await?;
            let analysis_id = analysis_result["analysis_id"].as_str().unwrap();
            
            let security_results = self.wait_for_security_analysis(analysis_id).await?;
            
            // Check for threat intelligence data
            if let Some(metadata) = security_results.get("metadata") {
                // Test threat intelligence sources
                intelligence_features_tested += 1;
                if metadata.get("threat_intel_sources").is_some() {
                    intelligence_features_working += 1;
                }
                
                // Test vulnerability databases
                intelligence_features_tested += 1;
                if metadata.get("vulnerability_databases_used").is_some() {
                    intelligence_features_working += 1;
                }
                
                // Test threat intelligence update timestamp
                intelligence_features_tested += 1;
                if metadata.get("last_threat_intel_update").is_some() {
                    intelligence_features_working += 1;
                }
            }
        }
        
        let intelligence_integration_rate = if intelligence_features_tested > 0 {
            (intelligence_features_working as f64 / intelligence_features_tested as f64) * 100.0
        } else {
            0.0
        };
        
        let total_duration = start_time.elapsed();
        
        let mut metrics = HashMap::new();
        metrics.insert("features_tested".to_string(), intelligence_features_tested as f64);
        metrics.insert("features_working".to_string(), intelligence_features_working as f64);
        metrics.insert("integration_rate".to_string(), intelligence_integration_rate);
        
        let passed = intelligence_integration_rate >= 80.0;
        
        Ok(SecurityTestResult {
            name: "Security Intelligence Integration".to_string(),
            passed,
            duration: total_duration,
            metrics,
            details: format!(
                "Security intelligence integration rate: {:.1}% ({}/{} features working)",
                intelligence_integration_rate, intelligence_features_working, intelligence_features_tested
            ),
            vulnerabilities_tested: intelligence_features_tested,
            detection_accuracy: intelligence_integration_rate / 100.0,
        })
    }

    /// Test real-world vulnerability scenarios
    pub async fn test_realworld_vulnerability_scenarios(&self) -> Result<SecurityTestResult, SecurityTestError> {
        let start_time = Instant::now();
        
        // Test with real-world vulnerable repositories
        let vulnerable_repos = vec![
            VulnerableRepoTestCase {
                name: "WebGoat".to_string(),
                url: "https://github.com/OWASP/WebGoat.git",
                branch: "main".to_string(),
                expected_vulnerability_count: 10,
                patterns: vec!["src/**/*.java".to_string()],
                languages: vec!["java".to_string()],
            },
            VulnerableRepoTestCase {
                name: "DVWA".to_string(),
                url: "https://github.com/digininja/DVWA.git",
                branch: "master".to_string(),
                expected_vulnerability_count: 8,
                patterns: vec!["**/*.php".to_string()],
                languages: vec!["php".to_string()],
            },
            VulnerableRepoTestCase {
                name: "NodeGoat".to_string(),
                url: "https://github.com/OWASP/NodeGoat.git",
                branch: "master".to_string(),
                expected_vulnerability_count: 15,
                patterns: vec!["**/*.js".to_string()],
                languages: vec!["javascript".to_string()],
            },
        ];
        
        let mut total_expected_vulnerabilities = 0;
        let mut total_detected_vulnerabilities = 0;
        
        for repo_test in &vulnerable_repos {
            println!("  Testing real-world vulnerabilities in {}...", repo_test.name);
            
            let analysis_id = TestDataGenerator::generate_analysis_id();
            let request_body = json!({
                "analysis_id": analysis_id,
                "repository_url": repo_test.url,
                "branch": repo_test.branch,
                "include_patterns": repo_test.patterns,
                "exclude_patterns": ["**/tests/**", "**/node_modules/**"],
                "languages": repo_test.languages,
                "enable_patterns": false,
                "enable_embeddings": false,
                "enable_security_scanning": true,
                "analysis_depth": "Deep"
            });

            let response = self.client
                .post(&format!("{}/api/v1/analysis", self.base_url))
                .header("X-API-Key", &self.api_key)
                .json(&request_body)
                .send()
                .await?;

            if response.status().is_success() {
                let analysis_result: Value = response.json().await?;
                let analysis_id = analysis_result["analysis_id"].as_str().unwrap();
                
                let security_results = self.wait_for_security_analysis(analysis_id).await?;
                
                // Count detected vulnerabilities
                let detected_count = security_results
                    .get("vulnerabilities")
                    .and_then(|v| v.as_array())
                    .map(|arr| arr.len())
                    .unwrap_or(0);
                
                total_expected_vulnerabilities += repo_test.expected_vulnerability_count;
                total_detected_vulnerabilities += detected_count;
                
                println!("    Detected {}/{} expected vulnerabilities", 
                        detected_count, repo_test.expected_vulnerability_count);
            }
        }
        
        let detection_rate = if total_expected_vulnerabilities > 0 {
            (total_detected_vulnerabilities as f64 / total_expected_vulnerabilities as f64) * 100.0
        } else {
            0.0
        };
        
        let total_duration = start_time.elapsed();
        
        let mut metrics = HashMap::new();
        metrics.insert("repos_tested".to_string(), vulnerable_repos.len() as f64);
        metrics.insert("expected_vulnerabilities".to_string(), total_expected_vulnerabilities as f64);
        metrics.insert("detected_vulnerabilities".to_string(), total_detected_vulnerabilities as f64);
        metrics.insert("detection_rate".to_string(), detection_rate);
        
        let passed = detection_rate >= 70.0; // Lower threshold for real-world scenarios
        
        Ok(SecurityTestResult {
            name: "Real-world Vulnerability Scenarios".to_string(),
            passed,
            duration: total_duration,
            metrics,
            details: format!(
                "Real-world vulnerability detection rate: {:.1}% ({}/{} vulnerabilities detected)",
                detection_rate, total_detected_vulnerabilities, total_expected_vulnerabilities
            ),
            vulnerabilities_tested: vulnerable_repos.len(),
            detection_accuracy: detection_rate / 100.0,
        })
    }

    /// Test security performance under load
    pub async fn test_security_performance_under_load(&self) -> Result<SecurityTestResult, SecurityTestError> {
        let start_time = Instant::now();
        
        // Test security scanning performance under concurrent load
        let concurrent_requests = 20;
        let mut handles = Vec::new();
        
        for i in 0..concurrent_requests {
            let client = self.client.clone();
            let base_url = self.base_url.clone();
            let api_key = self.api_key.clone();
            
            let handle = tokio::spawn(async move {
                let analysis_id = TestDataGenerator::generate_analysis_id();
                let request_body = json!({
                    "analysis_id": analysis_id,
                    "repository_url": format!("https://github.com/test/security-load-test-{}.git", i),
                    "branch": "main",
                    "include_patterns": ["**/*.js", "**/*.py"],
                    "exclude_patterns": [],
                    "languages": ["javascript", "python"],
                    "enable_patterns": false,
                    "enable_embeddings": false,
                    "enable_security_scanning": true,
                    "analysis_depth": "Standard"
                });

                let request_start = Instant::now();
                let response = client
                    .post(&format!("{}/api/v1/analysis", base_url))
                    .header("X-API-Key", &api_key)
                    .json(&request_body)
                    .send()
                    .await;
                
                let duration = request_start.elapsed();
                (response.is_ok() && response.unwrap().status().is_success(), duration)
            });
            
            handles.push(handle);
        }
        
        let results = futures::future::join_all(handles).await;
        
        let successful_requests = results.iter().filter(|r| r.is_ok() && r.as_ref().unwrap().0).count();
        let avg_response_time = results.iter()
            .filter_map(|r| r.as_ref().ok())
            .map(|(_, duration)| duration.as_millis() as f64)
            .sum::<f64>() / results.len() as f64;
        
        let success_rate = (successful_requests as f64 / concurrent_requests as f64) * 100.0;
        
        let total_duration = start_time.elapsed();
        
        let mut metrics = HashMap::new();
        metrics.insert("concurrent_requests".to_string(), concurrent_requests as f64);
        metrics.insert("successful_requests".to_string(), successful_requests as f64);
        metrics.insert("success_rate".to_string(), success_rate);
        metrics.insert("avg_response_time_ms".to_string(), avg_response_time);
        
        let passed = success_rate >= 90.0 && 
                    avg_response_time <= self.config.security_thresholds["max_scan_time_seconds"] * 1000.0;
        
        Ok(SecurityTestResult {
            name: "Security Performance Under Load".to_string(),
            passed,
            duration: total_duration,
            metrics,
            details: format!(
                "Security scan performance: {:.1}% success rate, {:.2}ms avg response time",
                success_rate, avg_response_time
            ),
            vulnerabilities_tested: concurrent_requests,
            detection_accuracy: success_rate / 100.0,
        })
    }

    /// Helper method to wait for security analysis completion
    async fn wait_for_security_analysis(&self, analysis_id: &str) -> Result<Value, SecurityTestError> {
        let max_wait_time = Duration::from_secs(120);
        let start_time = Instant::now();
        
        loop {
            let response = self.client
                .get(&format!("{}/api/v1/analysis/{}", self.base_url, analysis_id))
                .header("X-API-Key", &self.api_key)
                .send()
                .await?;
            
            if response.status().is_success() {
                let result: Value = response.json().await?;
                
                if let Some(status) = result.get("status").and_then(|s| s.as_str()) {
                    if status == "Completed" {
                        return Ok(result);
                    } else if status == "Failed" {
                        return Err(SecurityTestError::AnalysisError("Analysis failed".to_string()));
                    }
                }
            }
            
            if start_time.elapsed() > max_wait_time {
                return Err(SecurityTestError::TimeoutError("Analysis timeout".to_string()));
            }
            
            tokio::time::sleep(Duration::from_secs(5)).await;
        }
    }

    /// Helper method to check if a vulnerability was detected
    fn check_vulnerability_detected(&self, security_results: &Value, expected_cwe: &str) -> bool {
        if let Some(vulnerabilities) = security_results.get("vulnerabilities") {
            if let Some(vuln_array) = vulnerabilities.as_array() {
                for vuln in vuln_array {
                    if let Some(cwe_id) = vuln.get("cwe_id").and_then(|c| c.as_str()) {
                        if cwe_id == expected_cwe {
                            return true;
                        }
                    }
                }
            }
        }
        false
    }

    /// Helper method to check if a secret was detected
    fn check_secret_detected(&self, security_results: &Value, expected_secret_type: &str) -> bool {
        if let Some(secrets) = security_results.get("detected_secrets") {
            if let Some(secrets_array) = secrets.as_array() {
                for secret in secrets_array {
                    if let Some(secret_type) = secret.get("secret_type").and_then(|s| s.as_str()) {
                        if secret_type == expected_secret_type {
                            return true;
                        }
                    }
                }
            }
        }
        false
    }

    /// Helper method to get file extension for a language
    fn get_file_extension(language: &str) -> &str {
        match language {
            "rust" => "rs",
            "javascript" => "js",
            "typescript" => "ts",
            "python" => "py",
            "java" => "java",
            "c" => "c",
            "cpp" => "cpp",
            "php" => "php",
            "go" => "go",
            _ => "txt",
        }
    }
}

/// Data structures for security test results
#[derive(Debug)]
pub struct SecurityTestResults {
    pub test_results: Vec<SecurityTestResult>,
    pub overall_passed: bool,
    pub total_duration: Duration,
    pub summary_metrics: HashMap<String, f64>,
}

impl SecurityTestResults {
    pub fn new() -> Self {
        Self {
            test_results: Vec::new(),
            overall_passed: true,
            total_duration: Duration::from_secs(0),
            summary_metrics: HashMap::new(),
        }
    }

    pub fn add_test_result(&mut self, _test_name: &str, result: SecurityTestResult) {
        self.overall_passed = self.overall_passed && result.passed;
        self.total_duration += result.duration;
        self.test_results.push(result);
    }

    pub fn finalize(&mut self) {
        let total_tests = self.test_results.len() as f64;
        let passed_tests = self.test_results.iter().filter(|r| r.passed).count() as f64;
        let avg_detection_accuracy = self.test_results.iter()
            .map(|r| r.detection_accuracy)
            .sum::<f64>() / total_tests;
        
        self.summary_metrics.insert("total_tests".to_string(), total_tests);
        self.summary_metrics.insert("passed_tests".to_string(), passed_tests);
        self.summary_metrics.insert("success_rate".to_string(), (passed_tests / total_tests) * 100.0);
        self.summary_metrics.insert("avg_detection_accuracy".to_string(), avg_detection_accuracy * 100.0);
        self.summary_metrics.insert("total_duration_seconds".to_string(), self.total_duration.as_secs() as f64);
    }
}

#[derive(Debug)]
pub struct SecurityTestResult {
    pub name: String,
    pub passed: bool,
    pub duration: Duration,
    pub metrics: HashMap<String, f64>,
    pub details: String,
    pub vulnerabilities_tested: usize,
    pub detection_accuracy: f64,
}

/// Test case data structures
#[derive(Debug, Clone)]
pub struct VulnerabilityDetectionResult {
    pub test_case: VulnerabilityTestCase,
    pub detected: bool,
    pub confidence: f64,
    pub scan_time: Duration,
}

#[derive(Debug, Clone)]
pub struct SecretTestCase {
    pub name: String,
    pub code: String,
    pub expected_secret_type: String,
    pub should_detect: bool,
}

#[derive(Debug, Clone)]
pub struct SecretDetectionResult {
    pub test_case: SecretTestCase,
    pub detected: bool,
    pub confidence: f64,
}

#[derive(Debug, Clone)]
pub struct CleanCodeSample {
    pub name: String,
    pub language: String,
    pub code: String,
}

#[derive(Debug, Clone)]
pub struct ThreatScenario {
    pub name: String,
    pub code: String,
    pub expected_threats: Vec<String>,
}

#[derive(Debug, Clone)]
pub struct VulnerableRepoTestCase {
    pub name: String,
    pub url: String,
    pub branch: String,
    pub expected_vulnerability_count: usize,
    pub patterns: Vec<String>,
    pub languages: Vec<String>,
}

/// Security test error types
#[derive(Debug, thiserror::Error)]
pub enum SecurityTestError {
    #[error("HTTP request failed: {0}")]
    HttpError(#[from] reqwest::Error),
    #[error("Analysis execution failed: {0}")]
    AnalysisError(String),
    #[error("Test timeout: {0}")]
    TimeoutError(String),
    #[error("Configuration error: {0}")]
    ConfigError(String),
    #[error("Assertion failed: {0}")]
    AssertionError(String),
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_security_test_config() {
        let config = SecurityTestConfig::default();
        assert!(config.enable_vulnerability_detection);
        assert!(config.enable_secrets_scanning);
        assert_eq!(config.target_detection_accuracy, 95.0);
    }

    #[tokio::test]
    async fn test_security_test_results() {
        let mut results = SecurityTestResults::new();
        
        let test_result = SecurityTestResult {
            name: "Test".to_string(),
            passed: true,
            duration: Duration::from_secs(10),
            metrics: HashMap::new(),
            details: "Test details".to_string(),
            vulnerabilities_tested: 5,
            detection_accuracy: 0.95,
        };
        
        results.add_test_result("test", test_result);
        results.finalize();
        
        assert_eq!(results.test_results.len(), 1);
        assert!(results.overall_passed);
        assert_eq!(results.summary_metrics.get("total_tests"), Some(&1.0));
    }

    #[test]
    fn test_file_extension_mapping() {
        assert_eq!(SecurityTestRunner::get_file_extension("rust"), "rs");
        assert_eq!(SecurityTestRunner::get_file_extension("javascript"), "js");
        assert_eq!(SecurityTestRunner::get_file_extension("python"), "py");
        assert_eq!(SecurityTestRunner::get_file_extension("unknown"), "txt");
    }

    #[tokio::test]
    async fn test_vulnerability_test_cases() {
        let test_cases = TestFixtures::known_vulnerabilities_test_cases();
        assert!(!test_cases.is_empty());
        
        // Check that we have diverse vulnerability types
        let vuln_types: std::collections::HashSet<_> = test_cases.iter()
            .map(|tc| tc.expected_vulnerability.as_str())
            .collect();
        assert!(vuln_types.len() > 5);
    }
}