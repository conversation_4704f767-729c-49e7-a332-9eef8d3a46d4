//! Mock services for testing AI features and external dependencies
//! 
//! This module provides mock implementations of external services to enable
//! reliable testing without depending on external APIs or services.

use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::Duration;
use serde_json::{json, Value};
use tokio::time::sleep;
use uuid::Uuid;

/// Mock services container that manages all mock implementations
pub struct MockServices {
    pub ai_service: Arc<MockAiService>,
    pub database_service: Arc<MockDatabaseService>,
    pub external_api_service: Arc<MockExternalApiService>,
    pub circuit_breaker: Arc<MockCircuitBreaker>,
}

impl MockServices {
    pub fn new() -> Self {
        Self {
            ai_service: Arc::new(MockAiService::new()),
            database_service: Arc::new(MockDatabaseService::new()),
            external_api_service: Arc::new(MockExternalApiService::new()),
            circuit_breaker: Arc::new(MockCircuitBreaker::new()),
        }
    }

    /// Reset all mock services to their initial state
    pub fn reset_all(&self) {
        self.ai_service.reset();
        self.database_service.reset();
        self.external_api_service.reset();
        self.circuit_breaker.reset();
    }

    /// Configure all mock services for a specific test scenario
    pub fn configure_for_scenario(&self, scenario: &str) {
        match scenario {
            "ai_failure" => {
                self.ai_service.set_failure_mode(true);
                self.circuit_breaker.set_failure_threshold(3);
            }
            "database_slowdown" => {
                self.database_service.set_response_delay(Duration::from_secs(5));
            }
            "external_api_timeout" => {
                self.external_api_service.set_timeout_mode(true);
            }
            "circuit_breaker_open" => {
                self.circuit_breaker.force_open();
            }
            _ => {
                // Default configuration
                self.reset_all();
            }
        }
    }
}

/// Mock AI service for testing LLM integration and AI features
pub struct MockAiService {
    state: Arc<Mutex<MockAiServiceState>>,
}

struct MockAiServiceState {
    failure_mode: bool,
    response_delay: Duration,
    call_count: u64,
    responses: HashMap<String, Value>,
    confidence_scores: HashMap<String, f64>,
}

impl MockAiService {
    pub fn new() -> Self {
        let mut responses = HashMap::new();
        let mut confidence_scores = HashMap::new();

        // Pre-configured responses for different AI features
        responses.insert("code_explanation".to_string(), json!({
            "explanation": "This is a recursive function that calculates the factorial of a number.",
            "complexity": "O(n) time complexity, O(n) space complexity due to recursion stack",
            "suggestions": ["Consider iterative approach for better space efficiency"]
        }));
        confidence_scores.insert("code_explanation".to_string(), 0.95);

        responses.insert("bug_detection".to_string(), json!({
            "bugs": [{
                "type": "undefined_variable",
                "variable": "y",
                "line": 1,
                "severity": "error",
                "suggestion": "Define variable 'y' before using it"
            }],
            "severity": "high"
        }));
        confidence_scores.insert("bug_detection".to_string(), 0.98);

        responses.insert("optimization_suggestion".to_string(), json!({
            "optimizations": [{
                "type": "iterator_usage",
                "current": "for i in 0..vec.len() { println!(\"{}\", vec[i]); }",
                "suggested": "for item in &vec { println!(\"{}\", item); }",
                "benefit": "Avoids bounds checking and more idiomatic"
            }],
            "performance_impact": "medium"
        }));
        confidence_scores.insert("optimization_suggestion".to_string(), 0.85);

        responses.insert("security_warning".to_string(), json!({
            "vulnerabilities": [{
                "type": "code_injection",
                "severity": "critical",
                "cwe_id": "CWE-94",
                "description": "Use of eval() with user input can lead to code injection",
                "remediation": "Use safe parsing methods instead of eval()"
            }],
            "risk_level": "critical"
        }));
        confidence_scores.insert("security_warning".to_string(), 0.99);

        responses.insert("code_completion".to_string(), json!({
            "completions": [
                "vec![1, 2, 3];",
                "String::new();",
                "HashMap::new();",
                "5;"
            ],
            "context": "variable_initialization"
        }));
        confidence_scores.insert("code_completion".to_string(), 0.75);

        Self {
            state: Arc::new(Mutex::new(MockAiServiceState {
                failure_mode: false,
                response_delay: Duration::from_millis(100),
                call_count: 0,
                responses,
                confidence_scores,
            }))
        }
    }

    /// Set failure mode for testing error handling
    pub fn set_failure_mode(&self, enabled: bool) {
        let mut state = self.state.lock().unwrap();
        state.failure_mode = enabled;
    }

    /// Set response delay for testing timeout scenarios
    pub fn set_response_delay(&self, delay: Duration) {
        let mut state = self.state.lock().unwrap();
        state.response_delay = delay;
    }

    /// Reset the mock service to initial state
    pub fn reset(&self) {
        let mut state = self.state.lock().unwrap();
        state.failure_mode = false;
        state.response_delay = Duration::from_millis(100);
        state.call_count = 0;
    }

    /// Get call count for testing
    pub fn get_call_count(&self) -> u64 {
        self.state.lock().unwrap().call_count
    }

    /// Mock AI analysis request
    pub async fn analyze_code(&self, code: &str, analysis_type: &str) -> Result<AiAnalysisResponse, MockAiError> {
        let mut state = self.state.lock().unwrap();
        state.call_count += 1;
        
        if state.failure_mode {
            return Err(MockAiError::ServiceUnavailable);
        }

        let delay = state.response_delay;
        let response = state.responses.get(analysis_type).cloned()
            .unwrap_or_else(|| json!({"error": "Unknown analysis type"}));
        let confidence = state.confidence_scores.get(analysis_type).copied().unwrap_or(0.5);
        
        drop(state);

        // Simulate processing time
        sleep(delay).await;

        Ok(AiAnalysisResponse {
            analysis_id: Uuid::new_v4().to_string(),
            analysis_type: analysis_type.to_string(),
            code_snippet: code.to_string(),
            response,
            confidence,
            processing_time_ms: delay.as_millis() as u64,
        })
    }

    /// Mock pattern detection request
    pub async fn detect_patterns(&self, code: &str) -> Result<Vec<PatternDetectionResult>, MockAiError> {
        let mut state = self.state.lock().unwrap();
        state.call_count += 1;
        
        if state.failure_mode {
            return Err(MockAiError::ServiceUnavailable);
        }

        let delay = state.response_delay;
        drop(state);

        sleep(delay).await;

        // Generate mock patterns based on code content
        let patterns = if code.contains("fn ") {
            vec![
                PatternDetectionResult {
                    pattern_type: "function_definition".to_string(),
                    confidence: 0.95,
                    location: PatternLocation { line: 1, column: 1 },
                    description: "Function definition pattern detected".to_string(),
                },
                PatternDetectionResult {
                    pattern_type: "recursive_pattern".to_string(),
                    confidence: 0.85,
                    location: PatternLocation { line: 2, column: 5 },
                    description: "Recursive function call pattern detected".to_string(),
                },
            ]
        } else if code.contains("class ") {
            vec![
                PatternDetectionResult {
                    pattern_type: "class_definition".to_string(),
                    confidence: 0.98,
                    location: PatternLocation { line: 1, column: 1 },
                    description: "Class definition pattern detected".to_string(),
                },
            ]
        } else {
            vec![]
        };

        Ok(patterns)
    }

    /// Mock semantic search request
    pub async fn semantic_search(&self, query: &str) -> Result<Vec<SemanticSearchResult>, MockAiError> {
        let mut state = self.state.lock().unwrap();
        state.call_count += 1;
        
        if state.failure_mode {
            return Err(MockAiError::ServiceUnavailable);
        }

        let delay = state.response_delay;
        drop(state);

        sleep(delay).await;

        // Generate mock search results
        let results = vec![
            SemanticSearchResult {
                file_path: "src/main.rs".to_string(),
                line_number: 10,
                code_snippet: "fn main() { println!(\"Hello, world!\"); }".to_string(),
                relevance_score: 0.92,
                semantic_similarity: 0.88,
            },
            SemanticSearchResult {
                file_path: "src/lib.rs".to_string(),
                line_number: 25,
                code_snippet: "pub fn process_data(data: &str) -> Result<String, Error>".to_string(),
                relevance_score: 0.78,
                semantic_similarity: 0.82,
            },
        ];

        Ok(results)
    }
}

/// Mock database service for testing database operations
pub struct MockDatabaseService {
    state: Arc<Mutex<MockDatabaseServiceState>>,
}

struct MockDatabaseServiceState {
    response_delay: Duration,
    failure_mode: bool,
    stored_data: HashMap<String, Value>,
    call_count: u64,
}

impl MockDatabaseService {
    pub fn new() -> Self {
        Self {
            state: Arc::new(Mutex::new(MockDatabaseServiceState {
                response_delay: Duration::from_millis(50),
                failure_mode: false,
                stored_data: HashMap::new(),
                call_count: 0,
            }))
        }
    }

    /// Set response delay for testing slow database scenarios
    pub fn set_response_delay(&self, delay: Duration) {
        let mut state = self.state.lock().unwrap();
        state.response_delay = delay;
    }

    /// Set failure mode for testing database errors
    pub fn set_failure_mode(&self, enabled: bool) {
        let mut state = self.state.lock().unwrap();
        state.failure_mode = enabled;
    }

    /// Reset the mock database service
    pub fn reset(&self) {
        let mut state = self.state.lock().unwrap();
        state.response_delay = Duration::from_millis(50);
        state.failure_mode = false;
        state.stored_data.clear();
        state.call_count = 0;
    }

    /// Get call count for testing
    pub fn get_call_count(&self) -> u64 {
        self.state.lock().unwrap().call_count
    }

    /// Mock database store operation
    pub async fn store(&self, key: &str, value: Value) -> Result<(), MockDatabaseError> {
        let mut state = self.state.lock().unwrap();
        state.call_count += 1;
        
        if state.failure_mode {
            return Err(MockDatabaseError::ConnectionError);
        }

        let delay = state.response_delay;
        state.stored_data.insert(key.to_string(), value);
        drop(state);

        sleep(delay).await;
        Ok(())
    }

    /// Mock database retrieve operation
    pub async fn retrieve(&self, key: &str) -> Result<Option<Value>, MockDatabaseError> {
        let mut state = self.state.lock().unwrap();
        state.call_count += 1;
        
        if state.failure_mode {
            return Err(MockDatabaseError::ConnectionError);
        }

        let delay = state.response_delay;
        let value = state.stored_data.get(key).cloned();
        drop(state);

        sleep(delay).await;
        Ok(value)
    }

    /// Mock database query operation
    pub async fn query(&self, _query: &str) -> Result<Vec<Value>, MockDatabaseError> {
        let mut state = self.state.lock().unwrap();
        state.call_count += 1;
        
        if state.failure_mode {
            return Err(MockDatabaseError::ConnectionError);
        }

        let delay = state.response_delay;
        drop(state);

        sleep(delay).await;
        
        // Return mock query results
        Ok(vec![
            json!({"id": "1", "name": "test_analysis", "status": "completed"}),
            json!({"id": "2", "name": "test_analysis_2", "status": "pending"}),
        ])
    }
}

/// Mock external API service for testing third-party integrations
pub struct MockExternalApiService {
    state: Arc<Mutex<MockExternalApiServiceState>>,
}

struct MockExternalApiServiceState {
    timeout_mode: bool,
    rate_limit_mode: bool,
    call_count: u64,
    responses: HashMap<String, Value>,
}

impl MockExternalApiService {
    pub fn new() -> Self {
        let mut responses = HashMap::new();
        responses.insert("github_api".to_string(), json!({
            "name": "test-repo",
            "full_name": "test-user/test-repo",
            "private": false,
            "html_url": "https://github.com/test-user/test-repo",
            "default_branch": "main"
        }));
        
        responses.insert("vulnerability_db".to_string(), json!({
            "vulnerabilities": [{
                "cve_id": "CVE-2023-1234",
                "severity": "HIGH",
                "description": "Test vulnerability",
                "affected_versions": ["1.0.0", "1.1.0"]
            }]
        }));

        Self {
            state: Arc::new(Mutex::new(MockExternalApiServiceState {
                timeout_mode: false,
                rate_limit_mode: false,
                call_count: 0,
                responses,
            }))
        }
    }

    /// Set timeout mode for testing timeout scenarios
    pub fn set_timeout_mode(&self, enabled: bool) {
        let mut state = self.state.lock().unwrap();
        state.timeout_mode = enabled;
    }

    /// Set rate limit mode for testing rate limiting
    pub fn set_rate_limit_mode(&self, enabled: bool) {
        let mut state = self.state.lock().unwrap();
        state.rate_limit_mode = enabled;
    }

    /// Reset the mock external API service
    pub fn reset(&self) {
        let mut state = self.state.lock().unwrap();
        state.timeout_mode = false;
        state.rate_limit_mode = false;
        state.call_count = 0;
    }

    /// Get call count for testing
    pub fn get_call_count(&self) -> u64 {
        self.state.lock().unwrap().call_count
    }

    /// Mock external API call
    pub async fn call_api(&self, endpoint: &str) -> Result<Value, MockExternalApiError> {
        let mut state = self.state.lock().unwrap();
        state.call_count += 1;
        
        if state.timeout_mode {
            sleep(Duration::from_secs(30)).await;
            return Err(MockExternalApiError::Timeout);
        }

        if state.rate_limit_mode && state.call_count > 5 {
            return Err(MockExternalApiError::RateLimited);
        }

        let response = state.responses.get(endpoint).cloned()
            .unwrap_or_else(|| json!({"error": "Endpoint not found"}));
        
        drop(state);

        sleep(Duration::from_millis(200)).await;
        Ok(response)
    }
}

/// Mock circuit breaker for testing resilience patterns
pub struct MockCircuitBreaker {
    state: Arc<Mutex<MockCircuitBreakerState>>,
}

struct MockCircuitBreakerState {
    state: CircuitBreakerState,
    failure_count: u32,
    failure_threshold: u32,
    success_count: u32,
    last_failure_time: Option<std::time::Instant>,
    timeout_duration: Duration,
}

#[derive(Clone, Debug)]
pub enum CircuitBreakerState {
    Closed,
    Open,
    HalfOpen,
}

impl MockCircuitBreaker {
    pub fn new() -> Self {
        Self {
            state: Arc::new(Mutex::new(MockCircuitBreakerState {
                state: CircuitBreakerState::Closed,
                failure_count: 0,
                failure_threshold: 5,
                success_count: 0,
                last_failure_time: None,
                timeout_duration: Duration::from_secs(60),
            }))
        }
    }

    /// Set failure threshold for testing
    pub fn set_failure_threshold(&self, threshold: u32) {
        let mut state = self.state.lock().unwrap();
        state.failure_threshold = threshold;
    }

    /// Force circuit breaker to open state
    pub fn force_open(&self) {
        let mut state = self.state.lock().unwrap();
        state.state = CircuitBreakerState::Open;
        state.last_failure_time = Some(std::time::Instant::now());
    }

    /// Reset circuit breaker to closed state
    pub fn reset(&self) {
        let mut state = self.state.lock().unwrap();
        state.state = CircuitBreakerState::Closed;
        state.failure_count = 0;
        state.success_count = 0;
        state.last_failure_time = None;
    }

    /// Get current circuit breaker state
    pub fn get_state(&self) -> CircuitBreakerState {
        self.state.lock().unwrap().state.clone()
    }

    /// Record a successful operation
    pub fn record_success(&self) {
        let mut state = self.state.lock().unwrap();
        state.success_count += 1;
        state.failure_count = 0;
        
        if matches!(state.state, CircuitBreakerState::HalfOpen) && state.success_count >= 3 {
            state.state = CircuitBreakerState::Closed;
        }
    }

    /// Record a failed operation
    pub fn record_failure(&self) {
        let mut state = self.state.lock().unwrap();
        state.failure_count += 1;
        state.last_failure_time = Some(std::time::Instant::now());
        
        if state.failure_count >= state.failure_threshold {
            state.state = CircuitBreakerState::Open;
        }
    }

    /// Check if operation should be allowed
    pub fn allow_request(&self) -> bool {
        let mut state = self.state.lock().unwrap();
        
        match state.state {
            CircuitBreakerState::Closed => true,
            CircuitBreakerState::Open => {
                if let Some(last_failure) = state.last_failure_time {
                    if last_failure.elapsed() > state.timeout_duration {
                        state.state = CircuitBreakerState::HalfOpen;
                        true
                    } else {
                        false
                    }
                } else {
                    true
                }
            }
            CircuitBreakerState::HalfOpen => true,
        }
    }
}

/// Data structures for mock responses
#[derive(Clone, Debug)]
pub struct AiAnalysisResponse {
    pub analysis_id: String,
    pub analysis_type: String,
    pub code_snippet: String,
    pub response: Value,
    pub confidence: f64,
    pub processing_time_ms: u64,
}

#[derive(Clone, Debug)]
pub struct PatternDetectionResult {
    pub pattern_type: String,
    pub confidence: f64,
    pub location: PatternLocation,
    pub description: String,
}

#[derive(Clone, Debug)]
pub struct PatternLocation {
    pub line: u32,
    pub column: u32,
}

#[derive(Clone, Debug)]
pub struct SemanticSearchResult {
    pub file_path: String,
    pub line_number: u32,
    pub code_snippet: String,
    pub relevance_score: f64,
    pub semantic_similarity: f64,
}

/// Error types for mock services
#[derive(Debug, thiserror::Error)]
pub enum MockAiError {
    #[error("AI service is unavailable")]
    ServiceUnavailable,
    #[error("Request timeout")]
    Timeout,
    #[error("Rate limit exceeded")]
    RateLimited,
    #[error("Invalid input: {0}")]
    InvalidInput(String),
}

#[derive(Debug, thiserror::Error)]
pub enum MockDatabaseError {
    #[error("Database connection error")]
    ConnectionError,
    #[error("Query execution failed")]
    QueryError,
    #[error("Transaction failed")]
    TransactionError,
}

#[derive(Debug, thiserror::Error)]
pub enum MockExternalApiError {
    #[error("API request timeout")]
    Timeout,
    #[error("Rate limit exceeded")]
    RateLimited,
    #[error("API endpoint not found")]
    NotFound,
    #[error("Authentication failed")]
    AuthenticationError,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_mock_ai_service() {
        let ai_service = MockAiService::new();
        
        let result = ai_service.analyze_code("fn test() {}", "code_explanation").await;
        assert!(result.is_ok());
        
        let response = result.unwrap();
        assert_eq!(response.analysis_type, "code_explanation");
        assert!(response.confidence > 0.0);
        assert_eq!(ai_service.get_call_count(), 1);
    }

    #[tokio::test]
    async fn test_mock_database_service() {
        let db_service = MockDatabaseService::new();
        
        let test_data = json!({"test": "data"});
        let store_result = db_service.store("test_key", test_data.clone()).await;
        assert!(store_result.is_ok());
        
        let retrieve_result = db_service.retrieve("test_key").await;
        assert!(retrieve_result.is_ok());
        assert_eq!(retrieve_result.unwrap(), Some(test_data));
        
        assert_eq!(db_service.get_call_count(), 2);
    }

    #[tokio::test]
    async fn test_circuit_breaker() {
        let circuit_breaker = MockCircuitBreaker::new();
        circuit_breaker.set_failure_threshold(3);
        
        // Initially closed
        assert!(matches!(circuit_breaker.get_state(), CircuitBreakerState::Closed));
        
        // Record failures to open circuit
        circuit_breaker.record_failure();
        circuit_breaker.record_failure();
        circuit_breaker.record_failure();
        
        assert!(matches!(circuit_breaker.get_state(), CircuitBreakerState::Open));
        assert!(!circuit_breaker.allow_request());
    }

    #[tokio::test]
    async fn test_mock_services_configuration() {
        let mock_services = MockServices::new();
        
        // Test AI failure scenario
        mock_services.configure_for_scenario("ai_failure");
        mock_services.ai_service.set_failure_mode(true);
        
        let result = mock_services.ai_service.analyze_code("test", "code_explanation").await;
        assert!(result.is_err());
        
        // Reset and test normal operation
        mock_services.reset_all();
        let result = mock_services.ai_service.analyze_code("test", "code_explanation").await;
        assert!(result.is_ok());
    }
}