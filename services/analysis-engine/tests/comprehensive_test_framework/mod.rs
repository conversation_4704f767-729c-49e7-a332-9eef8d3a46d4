//! Comprehensive Test Framework Module
//! 
//! This module provides a complete testing and validation framework for the Analysis Engine.
//! It includes modules for different types of testing and validation.

pub mod test_utils;
pub mod mock_services;
pub mod performance_testing;
pub mod security_testing;
pub mod language_validation;
pub mod ai_feature_testing;
pub mod database_testing;
pub mod e2e_testing;
pub mod ci_cd_testing;
pub mod metrics_collection;

// Re-export key types for convenience
pub use test_utils::*;
pub use mock_services::*;
pub use performance_testing::*;
pub use security_testing::*;
pub use language_validation::*;
pub use ai_feature_testing::*;
pub use database_testing::*;
pub use e2e_testing::*;
pub use ci_cd_testing::*;
pub use metrics_collection::*;