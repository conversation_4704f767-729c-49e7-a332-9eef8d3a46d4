//! Test utilities for the comprehensive testing framework
//! 
//! This module provides common utilities, fixtures, and helper functions
//! used across all test modules in the comprehensive testing framework.

use std::collections::HashMap;
use std::time::Duration;
use serde_json::{json, Value};
use uuid::Uuid;

/// Test data fixtures for various scenarios
pub struct TestFixtures;

impl TestFixtures {
    /// Create a small test repository request
    pub fn small_repository_request() -> Value {
        json!({
            "repository_url": "https://github.com/rust-lang/log.git",
            "branch": "master",
            "include_patterns": ["src/**/*.rs"],
            "exclude_patterns": ["target/**", "tests/**"],
            "languages": ["rust"],
            "enable_patterns": true,
            "enable_embeddings": false,
            "analysis_depth": "Standard"
        })
    }

    /// Create a medium test repository request
    pub fn medium_repository_request() -> Value {
        json!({
            "repository_url": "https://github.com/tokio-rs/tokio.git",
            "branch": "master",
            "include_patterns": ["tokio/src/**/*.rs"],
            "exclude_patterns": ["**/tests/**", "**/benches/**"],
            "languages": ["rust"],
            "enable_patterns": true,
            "enable_embeddings": true,
            "analysis_depth": "Deep"
        })
    }

    /// Create a large test repository request
    pub fn large_repository_request() -> Value {
        json!({
            "repository_url": "https://github.com/microsoft/vscode.git",
            "branch": "main",
            "include_patterns": ["src/**/*.ts", "extensions/**/*.js"],
            "exclude_patterns": ["**/node_modules/**", "**/out/**", "**/dist/**"],
            "languages": ["typescript", "javascript"],
            "enable_patterns": true,
            "enable_embeddings": true,
            "analysis_depth": "Deep"
        })
    }

    /// Create a multi-language repository request
    pub fn multi_language_repository_request() -> Value {
        json!({
            "repository_url": "https://github.com/denoland/deno.git",
            "branch": "main",
            "include_patterns": [
                "cli/**/*.rs",
                "core/**/*.js",
                "ext/**/*.ts",
                "runtime/**/*.rs"
            ],
            "exclude_patterns": [
                "**/tests/**",
                "**/testdata/**",
                "**/target/**",
                "**/node_modules/**"
            ],
            "languages": ["rust", "javascript", "typescript"],
            "enable_patterns": true,
            "enable_embeddings": true,
            "analysis_depth": "Deep"
        })
    }

    /// Create a repository request with security focus
    pub fn security_focused_repository_request() -> Value {
        json!({
            "repository_url": "https://github.com/owasp/owasp-testing-guide.git",
            "branch": "master",
            "include_patterns": ["**/*.js", "**/*.ts", "**/*.py", "**/*.java"],
            "exclude_patterns": ["**/node_modules/**", "**/dist/**"],
            "languages": ["javascript", "typescript", "python", "java"],
            "enable_patterns": true,
            "enable_embeddings": false,
            "enable_security_scanning": true,
            "analysis_depth": "Deep"
        })
    }

    /// Create a repository request with known vulnerabilities
    pub fn vulnerable_repository_request() -> Value {
        json!({
            "repository_url": "https://github.com/OWASP/WebGoat.git",
            "branch": "main",
            "include_patterns": ["src/**/*.java", "src/**/*.js"],
            "exclude_patterns": ["**/test/**", "**/target/**"],
            "languages": ["java", "javascript"],
            "enable_patterns": true,
            "enable_embeddings": false,
            "enable_security_scanning": true,
            "analysis_depth": "Deep"
        })
    }

    /// Create a performance benchmarking repository request
    pub fn performance_benchmark_repository_request() -> Value {
        json!({
            "repository_url": "https://github.com/rust-lang/rust-clippy.git",
            "branch": "master",
            "include_patterns": ["clippy_lints/src/**/*.rs"],
            "exclude_patterns": ["**/tests/**", "**/target/**"],
            "languages": ["rust"],
            "enable_patterns": true,
            "enable_embeddings": false,
            "analysis_depth": "Shallow"
        })
    }

    /// Create test data for all supported languages
    pub fn all_languages_test_data() -> Vec<LanguageTestData> {
        vec![
            LanguageTestData::new("rust", "https://github.com/rust-lang/rust.git", "library/**/*.rs"),
            LanguageTestData::new("javascript", "https://github.com/nodejs/node.git", "lib/**/*.js"),
            LanguageTestData::new("typescript", "https://github.com/microsoft/TypeScript.git", "src/**/*.ts"),
            LanguageTestData::new("python", "https://github.com/python/cpython.git", "Lib/**/*.py"),
            LanguageTestData::new("go", "https://github.com/golang/go.git", "src/**/*.go"),
            LanguageTestData::new("java", "https://github.com/openjdk/jdk.git", "src/**/*.java"),
            LanguageTestData::new("c", "https://github.com/torvalds/linux.git", "kernel/**/*.c"),
            LanguageTestData::new("cpp", "https://github.com/microsoft/calculator.git", "src/**/*.cpp"),
            LanguageTestData::new("html", "https://github.com/mdn/content.git", "files/**/*.html"),
            LanguageTestData::new("css", "https://github.com/twbs/bootstrap.git", "scss/**/*.scss"),
            LanguageTestData::new("json", "https://github.com/json-schema-org/json-schema-spec.git", "**/*.json"),
            LanguageTestData::new("yaml", "https://github.com/kubernetes/kubernetes.git", "**/*.yaml"),
            LanguageTestData::new("php", "https://github.com/laravel/laravel.git", "app/**/*.php"),
            LanguageTestData::new("ruby", "https://github.com/rails/rails.git", "activerecord/**/*.rb"),
            LanguageTestData::new("bash", "https://github.com/ohmyzsh/ohmyzsh.git", "**/*.sh"),
            LanguageTestData::new("swift", "https://github.com/apple/swift.git", "lib/**/*.swift"),
            LanguageTestData::new("kotlin", "https://github.com/JetBrains/kotlin.git", "compiler/**/*.kt"),
            LanguageTestData::new("r", "https://github.com/wch/r-source.git", "src/**/*.r"),
            LanguageTestData::new("julia", "https://github.com/JuliaLang/julia.git", "base/**/*.jl"),
            LanguageTestData::new("haskell", "https://github.com/ghc/ghc.git", "compiler/**/*.hs"),
            LanguageTestData::new("scala", "https://github.com/scala/scala.git", "src/**/*.scala"),
            LanguageTestData::new("erlang", "https://github.com/erlang/otp.git", "lib/**/*.erl"),
            LanguageTestData::new("elixir", "https://github.com/elixir-lang/elixir.git", "lib/**/*.ex"),
            LanguageTestData::new("xml", "https://github.com/w3c/xml-spec.git", "**/*.xml"),
            LanguageTestData::new("zig", "https://github.com/ziglang/zig.git", "src/**/*.zig"),
            LanguageTestData::new("dart", "https://github.com/dart-lang/sdk.git", "pkg/**/*.dart"),
            LanguageTestData::new("lua", "https://github.com/lua/lua.git", "src/**/*.lua"),
            LanguageTestData::new("ocaml", "https://github.com/ocaml/ocaml.git", "typing/**/*.ml"),
            LanguageTestData::new("nix", "https://github.com/NixOS/nixpkgs.git", "pkgs/**/*.nix"),
            // Additional emerging languages
            LanguageTestData::new("carbon", "https://github.com/carbon-language/carbon-lang.git", "**/*.carbon"),
            LanguageTestData::new("mojo", "https://github.com/modularml/mojo.git", "**/*.mojo"),
            LanguageTestData::new("v", "https://github.com/vlang/v.git", "**/*.v"),
            LanguageTestData::new("nim", "https://github.com/nim-lang/Nim.git", "compiler/**/*.nim"),
            LanguageTestData::new("gleam", "https://github.com/gleam-lang/gleam.git", "compiler/**/*.gleam"),
            LanguageTestData::new("odin", "https://github.com/odin-lang/Odin.git", "core/**/*.odin"),
        ]
    }

    /// Create known vulnerability test cases
    pub fn known_vulnerabilities_test_cases() -> Vec<VulnerabilityTestCase> {
        vec![
            VulnerabilityTestCase {
                name: "SQL Injection".to_string(),
                language: "javascript".to_string(),
                code: "const query = 'SELECT * FROM users WHERE id = ' + userId;".to_string(),
                expected_vulnerability: "CWE-89".to_string(),
                severity: "High".to_string(),
            },
            VulnerabilityTestCase {
                name: "Cross-Site Scripting (XSS)".to_string(),
                language: "javascript".to_string(),
                code: "document.innerHTML = userInput;".to_string(),
                expected_vulnerability: "CWE-79".to_string(),
                severity: "Medium".to_string(),
            },
            VulnerabilityTestCase {
                name: "Buffer Overflow".to_string(),
                language: "c".to_string(),
                code: "char buffer[10]; strcpy(buffer, userInput);".to_string(),
                expected_vulnerability: "CWE-120".to_string(),
                severity: "Critical".to_string(),
            },
            VulnerabilityTestCase {
                name: "Use After Free".to_string(),
                language: "c".to_string(),
                code: "free(ptr); return *ptr;".to_string(),
                expected_vulnerability: "CWE-416".to_string(),
                severity: "High".to_string(),
            },
            VulnerabilityTestCase {
                name: "Command Injection".to_string(),
                language: "python".to_string(),
                code: "os.system('ls ' + user_input)".to_string(),
                expected_vulnerability: "CWE-78".to_string(),
                severity: "High".to_string(),
            },
            VulnerabilityTestCase {
                name: "Path Traversal".to_string(),
                language: "java".to_string(),
                code: "File file = new File(baseDir + filename);".to_string(),
                expected_vulnerability: "CWE-22".to_string(),
                severity: "Medium".to_string(),
            },
            VulnerabilityTestCase {
                name: "Insecure Random".to_string(),
                language: "java".to_string(),
                code: "Random random = new Random(); int token = random.nextInt();".to_string(),
                expected_vulnerability: "CWE-330".to_string(),
                severity: "Medium".to_string(),
            },
            VulnerabilityTestCase {
                name: "Hardcoded Password".to_string(),
                language: "python".to_string(),
                code: "password = 'admin123'".to_string(),
                expected_vulnerability: "CWE-259".to_string(),
                severity: "High".to_string(),
            },
            VulnerabilityTestCase {
                name: "Unvalidated Redirect".to_string(),
                language: "javascript".to_string(),
                code: "window.location = userProvidedUrl;".to_string(),
                expected_vulnerability: "CWE-601".to_string(),
                severity: "Medium".to_string(),
            },
            VulnerabilityTestCase {
                name: "Insecure Deserialization".to_string(),
                language: "java".to_string(),
                code: "ObjectInputStream ois = new ObjectInputStream(input); Object obj = ois.readObject();".to_string(),
                expected_vulnerability: "CWE-502".to_string(),
                severity: "High".to_string(),
            },
        ]
    }

    /// Create AI feature test scenarios
    pub fn ai_feature_test_scenarios() -> Vec<AiFeatureTestCase> {
        vec![
            AiFeatureTestCase {
                name: "Code Explanation".to_string(),
                input_code: "fn factorial(n: u32) -> u32 { if n <= 1 { 1 } else { n * factorial(n - 1) } }".to_string(),
                expected_response_type: "code_explanation".to_string(),
                expected_confidence: 0.9,
            },
            AiFeatureTestCase {
                name: "Bug Detection".to_string(),
                input_code: "let mut x = 5; x += 1; println!(\"{}\", y);".to_string(),
                expected_response_type: "bug_detection".to_string(),
                expected_confidence: 0.95,
            },
            AiFeatureTestCase {
                name: "Performance Optimization".to_string(),
                input_code: "for i in 0..vec.len() { println!(\"{}\", vec[i]); }".to_string(),
                expected_response_type: "optimization_suggestion".to_string(),
                expected_confidence: 0.8,
            },
            AiFeatureTestCase {
                name: "Security Analysis".to_string(),
                input_code: "eval(user_input)".to_string(),
                expected_response_type: "security_warning".to_string(),
                expected_confidence: 0.95,
            },
            AiFeatureTestCase {
                name: "Code Completion".to_string(),
                input_code: "fn main() { let x = ".to_string(),
                expected_response_type: "code_completion".to_string(),
                expected_confidence: 0.7,
            },
        ]
    }

    /// Create database migration test scenarios
    pub fn database_migration_test_scenarios() -> Vec<MigrationTestCase> {
        vec![
            MigrationTestCase {
                name: "Forward Migration".to_string(),
                migration_type: MigrationType::Forward,
                from_version: "000".to_string(),
                to_version: "001".to_string(),
                expected_tables: vec!["analyses".to_string(), "files".to_string()],
                expected_success: true,
            },
            MigrationTestCase {
                name: "Rollback Migration".to_string(),
                migration_type: MigrationType::Rollback,
                from_version: "001".to_string(),
                to_version: "000".to_string(),
                expected_tables: vec!["analyses".to_string()],
                expected_success: true,
            },
            MigrationTestCase {
                name: "Multi-Step Migration".to_string(),
                migration_type: MigrationType::Forward,
                from_version: "000".to_string(),
                to_version: "004".to_string(),
                expected_tables: vec![
                    "analyses".to_string(),
                    "files".to_string(),
                    "file_analyses".to_string(),
                    "security_vulnerabilities".to_string(),
                ],
                expected_success: true,
            },
        ]
    }
}

/// Language test data structure
#[derive(Clone, Debug)]
pub struct LanguageTestData {
    pub language: String,
    pub repository_url: String,
    pub include_pattern: String,
}

impl LanguageTestData {
    pub fn new(language: &str, repository_url: &str, include_pattern: &str) -> Self {
        Self {
            language: language.to_string(),
            repository_url: repository_url.to_string(),
            include_pattern: include_pattern.to_string(),
        }
    }
}

/// Vulnerability test case structure
#[derive(Clone, Debug)]
pub struct VulnerabilityTestCase {
    pub name: String,
    pub language: String,
    pub code: String,
    pub expected_vulnerability: String,
    pub severity: String,
}

/// AI feature test case structure
#[derive(Clone, Debug)]
pub struct AiFeatureTestCase {
    pub name: String,
    pub input_code: String,
    pub expected_response_type: String,
    pub expected_confidence: f64,
}

/// Database migration test case structure
#[derive(Clone, Debug)]
pub struct MigrationTestCase {
    pub name: String,
    pub migration_type: MigrationType,
    pub from_version: String,
    pub to_version: String,
    pub expected_tables: Vec<String>,
    pub expected_success: bool,
}

/// Migration type enumeration
#[derive(Clone, Debug)]
pub enum MigrationType {
    Forward,
    Rollback,
}

/// Test assertion utilities
pub struct TestAssertions;

impl TestAssertions {
    /// Assert that a response is successful
    pub fn assert_success_response(response: &reqwest::Response) -> Result<(), String> {
        if response.status().is_success() {
            Ok(())
        } else {
            Err(format!("Expected successful response, got: {}", response.status()))
        }
    }

    /// Assert that a response contains expected fields
    pub fn assert_response_contains_fields(response: &Value, expected_fields: &[&str]) -> Result<(), String> {
        for field in expected_fields {
            if !response.get(field).is_some() {
                return Err(format!("Response missing expected field: {}", field));
            }
        }
        Ok(())
    }

    /// Assert that a test duration is within acceptable bounds
    pub fn assert_duration_within_bounds(duration: Duration, max_duration: Duration) -> Result<(), String> {
        if duration <= max_duration {
            Ok(())
        } else {
            Err(format!("Test duration {:?} exceeded maximum {:?}", duration, max_duration))
        }
    }

    /// Assert that a metric is within expected range
    pub fn assert_metric_in_range(metric: f64, min: f64, max: f64, metric_name: &str) -> Result<(), String> {
        if metric >= min && metric <= max {
            Ok(())
        } else {
            Err(format!("Metric {} value {} is outside expected range [{}, {}]", metric_name, metric, min, max))
        }
    }

    /// Assert that a list has expected minimum length
    pub fn assert_minimum_length<T>(list: &[T], min_length: usize, list_name: &str) -> Result<(), String> {
        if list.len() >= min_length {
            Ok(())
        } else {
            Err(format!("List {} has length {}, expected at least {}", list_name, list.len(), min_length))
        }
    }

    /// Assert that a vulnerability was detected
    pub fn assert_vulnerability_detected(vulnerabilities: &[Value], expected_cwe: &str) -> Result<(), String> {
        for vuln in vulnerabilities {
            if let Some(cwe) = vuln.get("cwe_id") {
                if cwe.as_str() == Some(expected_cwe) {
                    return Ok(());
                }
            }
        }
        Err(format!("Expected vulnerability {} not detected", expected_cwe))
    }

    /// Assert that language is supported
    pub fn assert_language_supported(supported_languages: &[String], language: &str) -> Result<(), String> {
        if supported_languages.contains(&language.to_string()) {
            Ok(())
        } else {
            Err(format!("Language {} is not supported", language))
        }
    }
}

/// Test data generation utilities
pub struct TestDataGenerator;

impl TestDataGenerator {
    /// Generate a unique analysis ID
    pub fn generate_analysis_id() -> String {
        format!("test_analysis_{}", Uuid::new_v4().to_string()[..8].to_string())
    }

    /// Generate a unique repository URL for testing
    pub fn generate_test_repository_url(index: usize) -> String {
        format!("https://github.com/test/repo-{}.git", index)
    }

    /// Generate realistic file paths for testing
    pub fn generate_test_file_paths(language: &str, count: usize) -> Vec<String> {
        let extension = match language {
            "rust" => "rs",
            "javascript" => "js",
            "typescript" => "ts",
            "python" => "py",
            "go" => "go",
            "java" => "java",
            "c" => "c",
            "cpp" => "cpp",
            _ => "txt",
        };

        (0..count)
            .map(|i| format!("src/module_{}/file_{}.{}", i / 10, i, extension))
            .collect()
    }

    /// Generate test metrics
    pub fn generate_test_metrics() -> HashMap<String, f64> {
        let mut metrics = HashMap::new();
        metrics.insert("lines_of_code".to_string(), 1000.0);
        metrics.insert("complexity".to_string(), 15.0);
        metrics.insert("maintainability".to_string(), 75.0);
        metrics.insert("test_coverage".to_string(), 85.0);
        metrics.insert("performance_score".to_string(), 90.0);
        metrics
    }

    /// Generate test analysis result
    pub fn generate_test_analysis_result(analysis_id: &str, language: &str) -> Value {
        json!({
            "analysis_id": analysis_id,
            "status": "Completed",
            "language": language,
            "progress": 100,
            "metrics": Self::generate_test_metrics(),
            "patterns": [],
            "vulnerabilities": [],
            "suggestions": [],
            "completion_time": chrono::Utc::now().to_rfc3339(),
        })
    }
}

/// Performance benchmarking utilities
pub struct PerformanceBenchmarks;

impl PerformanceBenchmarks {
    /// Define performance thresholds for different operation types
    pub fn get_performance_thresholds() -> HashMap<String, Duration> {
        let mut thresholds = HashMap::new();
        thresholds.insert("small_repo_analysis".to_string(), Duration::from_secs(30));
        thresholds.insert("medium_repo_analysis".to_string(), Duration::from_secs(120));
        thresholds.insert("large_repo_analysis".to_string(), Duration::from_secs(300));
        thresholds.insert("concurrent_analysis".to_string(), Duration::from_secs(180));
        thresholds.insert("language_detection".to_string(), Duration::from_secs(5));
        thresholds.insert("vulnerability_scan".to_string(), Duration::from_secs(60));
        thresholds.insert("pattern_detection".to_string(), Duration::from_secs(45));
        thresholds.insert("ai_analysis".to_string(), Duration::from_secs(90));
        thresholds
    }

    /// Define memory usage thresholds
    pub fn get_memory_thresholds() -> HashMap<String, f64> {
        let mut thresholds = HashMap::new();
        thresholds.insert("peak_memory_mb".to_string(), 2048.0);
        thresholds.insert("average_memory_mb".to_string(), 1024.0);
        thresholds.insert("memory_growth_rate".to_string(), 0.2);
        thresholds
    }

    /// Define throughput thresholds
    pub fn get_throughput_thresholds() -> HashMap<String, f64> {
        let mut thresholds = HashMap::new();
        thresholds.insert("analyses_per_minute".to_string(), 50.0);
        thresholds.insert("files_per_second".to_string(), 100.0);
        thresholds.insert("patterns_per_second".to_string(), 1000.0);
        thresholds
    }
}

/// Test configuration utilities
pub struct TestConfig;

impl TestConfig {
    /// Get test configuration from environment or defaults
    pub fn get_test_config() -> HashMap<String, String> {
        let mut config = HashMap::new();
        config.insert("base_url".to_string(), 
                     std::env::var("TEST_BASE_URL").unwrap_or_else(|_| "http://localhost:8001".to_string()));
        config.insert("api_key".to_string(), 
                     std::env::var("TEST_API_KEY").unwrap_or_else(|_| "test-api-key".to_string()));
        config.insert("timeout_seconds".to_string(), 
                     std::env::var("TEST_TIMEOUT_SECONDS").unwrap_or_else(|_| "300".to_string()));
        config.insert("max_concurrent_tests".to_string(), 
                     std::env::var("TEST_MAX_CONCURRENT").unwrap_or_else(|_| "10".to_string()));
        config.insert("enable_performance_tests".to_string(), 
                     std::env::var("TEST_ENABLE_PERFORMANCE").unwrap_or_else(|_| "true".to_string()));
        config.insert("enable_security_tests".to_string(), 
                     std::env::var("TEST_ENABLE_SECURITY").unwrap_or_else(|_| "true".to_string()));
        config.insert("enable_ai_tests".to_string(), 
                     std::env::var("TEST_ENABLE_AI").unwrap_or_else(|_| "true".to_string()));
        config
    }

    /// Parse boolean configuration value
    pub fn parse_bool_config(value: &str) -> bool {
        matches!(value.to_lowercase().as_str(), "true" | "1" | "yes" | "on")
    }

    /// Parse duration configuration value
    pub fn parse_duration_config(value: &str) -> Duration {
        Duration::from_secs(value.parse::<u64>().unwrap_or(300))
    }
}