use analysis_engine::services::security_analyzer::DependencyScanner;
use analysis_engine::models::FileAnalysis;
use analysis_engine::models::security::PackageManager;
use analysis_engine::models::{AstNode, FileMetrics, Range, Position};

fn create_test_file_analysis(content: &str) -> FileAnalysis {
    FileAnalysis {
        path: "test.file".to_string(),
        language: "test".to_string(),
        content_hash: "test_hash".to_string(),
        size_bytes: Some(content.len() as u64),
        ast: AstNode {
            node_type: "root".to_string(),
            name: None,
            range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 0, column: 0, byte: 0 },
            },
            children: vec![],
            properties: None,
            text: Some(content.to_string()),
        },
        metrics: FileMetrics {
            lines_of_code: 0,
            total_lines: Some(content.lines().count() as u32),
            complexity: 0,
            maintainability_index: 100.0,
            function_count: 0,
            class_count: 0,
            comment_ratio: 0.0,
        },
        chunks: None,
        symbols: None,
    }
}

#[tokio::test]
async fn test_parse_package_json() {
    let scanner = DependencyScanner::new();
    let content = r#"{
        "name": "test-app",
        "dependencies": {
            "express": "^4.18.0",
            "lodash": "4.17.21"
        },
        "devDependencies": {
            "jest": "^29.0.0"
        },
        "peerDependencies": {
            "react": "^18.0.0"
        }
    }"#;

    let mut file_analysis = create_test_file_analysis(content);
    file_analysis.path = "package.json".to_string();
    let deps = scanner.extract_dependencies(&[file_analysis]).await.unwrap();

    assert_eq!(deps.len(), 4);
    assert!(deps.iter().any(|d| d.name == "express" && d.version == "^4.18.0"));
    assert!(deps.iter().any(|d| d.name == "lodash" && d.version == "4.17.21"));
    assert!(deps.iter().any(|d| d.name == "jest" && d.version == "^29.0.0"));
    assert!(deps.iter().any(|d| d.name == "react" && d.version == "^18.0.0"));
    assert!(deps.iter().all(|d| d.package_manager == PackageManager::Npm));
}

#[tokio::test]
async fn test_parse_requirements_txt() {
    let scanner = DependencyScanner::new();
    let content = r#"# Python dependencies
django==4.2.0
flask>=2.3.0,<3.0.0
requests~=2.31.0
pytest  # No version specified
git+https://github.com/user/repo.git  # Should be skipped
beautifulsoup4"#;

    let mut file_analysis = create_test_file_analysis(content);
    file_analysis.path = "requirements.txt".to_string();
    
    let deps = scanner.extract_dependencies(&[file_analysis]).await.unwrap();

    assert_eq!(deps.len(), 5);
    assert!(deps.iter().any(|d| d.name == "django" && d.version == "==4.2.0"));
    assert!(deps.iter().any(|d| d.name == "flask" && d.version == ">=2.3.0,<3.0.0"));
    assert!(deps.iter().any(|d| d.name == "requests" && d.version == "~=2.31.0"));
    assert!(deps.iter().any(|d| d.name == "pytest" && d.version == "*"));
    assert!(deps.iter().any(|d| d.name == "beautifulsoup4" && d.version == "*"));
    assert!(deps.iter().all(|d| d.package_manager == PackageManager::Pip));
}

#[tokio::test]
async fn test_parse_cargo_toml() {
    let scanner = DependencyScanner::new();
    let content = r#"[package]
name = "test-crate"
version = "0.1.0"

[dependencies]
serde = "1.0"
tokio = { version = "1.35", features = ["full"] }

[dev-dependencies]
mockall = "0.12"

[build-dependencies]
cc = "1.0"

[target.'cfg(windows)'.dependencies]
winapi = "0.3""#;

    let mut file_analysis = create_test_file_analysis(content);
    file_analysis.path = "Cargo.toml".to_string();
    
    let deps = scanner.extract_dependencies(&[file_analysis]).await.unwrap();

    assert_eq!(deps.len(), 5);
    assert!(deps.iter().any(|d| d.name == "serde" && d.version == "1.0"));
    assert!(deps.iter().any(|d| d.name == "tokio" && d.version == "1.35"));
    assert!(deps.iter().any(|d| d.name == "mockall" && d.version == "0.12"));
    assert!(deps.iter().any(|d| d.name == "cc" && d.version == "1.0"));
    assert!(deps.iter().any(|d| d.name == "winapi" && d.version == "0.3"));
    assert!(deps.iter().all(|d| d.package_manager == PackageManager::Cargo));
}

#[tokio::test]
async fn test_parse_pom_xml() {
    let scanner = DependencyScanner::new();
    let content = r#"<?xml version="1.0" encoding="UTF-8"?>
<project>
    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>5.3.24</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <!-- Version managed by parent -->
        </dependency>
    </dependencies>
</project>"#;

    let mut file_analysis = create_test_file_analysis(content);
    file_analysis.path = "pom.xml".to_string();
    
    let deps = scanner.extract_dependencies(&[file_analysis]).await.unwrap();

    assert_eq!(deps.len(), 3);
    assert!(deps.iter().any(|d| d.name == "org.springframework:spring-core" && d.version == "5.3.24"));
    assert!(deps.iter().any(|d| d.name == "junit:junit" && d.version == "4.13.2"));
    assert!(deps.iter().any(|d| d.name == "com.fasterxml.jackson.core:jackson-databind" && d.version == "*"));
    assert!(deps.iter().all(|d| d.package_manager == PackageManager::Maven));
}

#[tokio::test]
async fn test_parse_go_mod() {
    let scanner = DependencyScanner::new();
    let content = r#"module github.com/example/project

go 1.21

require (
    github.com/gin-gonic/gin v1.9.1
    github.com/stretchr/testify v1.8.4
    golang.org/x/crypto v0.14.0
)

require github.com/spf13/cobra v1.7.0

// indirect dependencies should be skipped
require (
    github.com/davecgh/go-spew v1.1.1 // indirect
)"#;

    let mut file_analysis = create_test_file_analysis(content);
    file_analysis.path = "go.mod".to_string();
    
    let deps = scanner.extract_dependencies(&[file_analysis]).await.unwrap();

    assert_eq!(deps.len(), 4);
    assert!(deps.iter().any(|d| d.name == "github.com/gin-gonic/gin" && d.version == "1.9.1"));
    assert!(deps.iter().any(|d| d.name == "github.com/stretchr/testify" && d.version == "1.8.4"));
    assert!(deps.iter().any(|d| d.name == "golang.org/x/crypto" && d.version == "0.14.0"));
    assert!(deps.iter().any(|d| d.name == "github.com/spf13/cobra" && d.version == "1.7.0"));
    assert!(deps.iter().all(|d| d.package_manager == PackageManager::Go));
}

#[tokio::test]
async fn test_parse_gemfile() {
    let scanner = DependencyScanner::new();
    let content = r#"source 'https://rubygems.org'

gem 'rails', '7.0.8'
gem 'pg', '~> 1.5'
gem "sidekiq", version: "7.1.6"
gem 'redis'

group :development, :test do
  gem 'rspec-rails', '~> 6.0'
end"#;

    let mut file_analysis = create_test_file_analysis(content);
    file_analysis.path = "Gemfile".to_string();
    
    let deps = scanner.extract_dependencies(&[file_analysis]).await.unwrap();

    assert_eq!(deps.len(), 5);
    assert!(deps.iter().any(|d| d.name == "rails" && d.version == "7.0.8"));
    assert!(deps.iter().any(|d| d.name == "pg" && d.version == "~> 1.5"));
    assert!(deps.iter().any(|d| d.name == "sidekiq" && d.version == "7.1.6"));
    assert!(deps.iter().any(|d| d.name == "redis" && d.version == "*"));
    assert!(deps.iter().any(|d| d.name == "rspec-rails" && d.version == "~> 6.0"));
    assert!(deps.iter().all(|d| d.package_manager == PackageManager::Ruby));
}

#[tokio::test]
async fn test_parse_pyproject_toml() {
    let scanner = DependencyScanner::new();
    let content = r#"[tool.poetry]
name = "test-project"

[tool.poetry.dependencies]
python = "^3.11"
django = "^4.2"
requests = { version = "^2.31", extras = ["security"] }

[tool.poetry.dev-dependencies]
pytest = "^7.4"

[project]
name = "alt-project"
dependencies = [
    "flask >= 2.3.0",
    "sqlalchemy ~= 2.0"
]"#;

    let mut file_analysis = create_test_file_analysis(content);
    file_analysis.path = "pyproject.toml".to_string();
    
    let deps = scanner.extract_dependencies(&[file_analysis]).await.unwrap();

    // Should get 5 dependencies (excluding python version constraint)
    assert_eq!(deps.len(), 5);
    assert!(deps.iter().any(|d| d.name == "django" && d.version == "^4.2"));
    assert!(deps.iter().any(|d| d.name == "requests" && d.version == "^2.31"));
    assert!(deps.iter().any(|d| d.name == "pytest" && d.version == "^7.4"));
    assert!(deps.iter().any(|d| d.name == "flask" && d.version == ">= 2.3.0"));
    assert!(deps.iter().any(|d| d.name == "sqlalchemy" && d.version == "~= 2.0"));
    assert!(deps.iter().all(|d| d.package_manager == PackageManager::Pip));
}

#[tokio::test]
async fn test_parse_gradle_build() {
    let scanner = DependencyScanner::new();
    let content = r#"plugins {
    id 'java'
    id 'org.springframework.boot' version '3.1.5'
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web:3.1.5'
    implementation("com.google.guava:guava:32.1.3-jre")
    testImplementation 'junit:junit:4.13.2'
    runtimeOnly 'com.h2database:h2:2.2.224'
    implementation platform('org.springframework.cloud:spring-cloud-dependencies:2022.0.4')
}"#;

    let mut file_analysis = create_test_file_analysis(content);
    file_analysis.path = "build.gradle".to_string();
    
    let deps = scanner.extract_dependencies(&[file_analysis]).await.unwrap();

    assert_eq!(deps.len(), 5);
    assert!(deps.iter().any(|d| d.name == "org.springframework.boot:spring-boot-starter-web" && d.version == "3.1.5"));
    assert!(deps.iter().any(|d| d.name == "com.google.guava:guava" && d.version == "32.1.3-jre"));
    assert!(deps.iter().any(|d| d.name == "junit:junit" && d.version == "4.13.2"));
    assert!(deps.iter().any(|d| d.name == "com.h2database:h2" && d.version == "2.2.224"));
    assert!(deps.iter().all(|d| d.package_manager == PackageManager::Gradle));
}

#[tokio::test]
async fn test_parse_composer_json() {
    let scanner = DependencyScanner::new();
    let content = r#"{
    "name": "test/app",
    "require": {
        "php": ">=8.1",
        "symfony/framework-bundle": "6.3.*",
        "doctrine/orm": "^2.16"
    },
    "require-dev": {
        "phpunit/phpunit": "^10.4",
        "symfony/debug-bundle": "6.3.*"
    }
}"#;

    let mut file_analysis = create_test_file_analysis(content);
    file_analysis.path = "composer.json".to_string();
    
    let deps = scanner.extract_dependencies(&[file_analysis]).await.unwrap();

    // Should get 4 dependencies (excluding PHP version)
    assert_eq!(deps.len(), 4);
    assert!(deps.iter().any(|d| d.name == "symfony/framework-bundle" && d.version == "6.3.*"));
    assert!(deps.iter().any(|d| d.name == "doctrine/orm" && d.version == "^2.16"));
    assert!(deps.iter().any(|d| d.name == "phpunit/phpunit" && d.version == "^10.4"));
    assert!(deps.iter().any(|d| d.name == "symfony/debug-bundle" && d.version == "6.3.*"));
    assert!(deps.iter().all(|d| d.package_manager == PackageManager::Composer));
}

#[tokio::test]
async fn test_parse_packages_config() {
    let scanner = DependencyScanner::new();
    let content = r#"<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.9" targetFramework="net48" />
  <package id="NUnit" version="3.13.3" targetFramework="net48" />
</packages>"#;

    let mut file_analysis = create_test_file_analysis(content);
    file_analysis.path = "packages.config".to_string();
    
    let deps = scanner.extract_dependencies(&[file_analysis]).await.unwrap();

    assert_eq!(deps.len(), 3);
    assert!(deps.iter().any(|d| d.name == "Newtonsoft.Json" && d.version == "13.0.3"));
    assert!(deps.iter().any(|d| d.name == "Microsoft.AspNet.WebApi.Core" && d.version == "5.2.9"));
    assert!(deps.iter().any(|d| d.name == "NUnit" && d.version == "3.13.3"));
    assert!(deps.iter().all(|d| d.package_manager == PackageManager::Nuget));
}

#[tokio::test]
async fn test_empty_file_handling() {
    let scanner = DependencyScanner::new();
    let file_analysis = create_test_file_analysis("");
    
    // Test with various file types
    for path in &["package.json", "requirements.txt", "Cargo.toml", "pom.xml"] {
        let mut fa = file_analysis.clone();
        fa.path = path.to_string();
        let deps = scanner.extract_dependencies(&[fa]).await.unwrap();
        assert_eq!(deps.len(), 0, "Empty {} should return no dependencies", path);
    }
}

#[tokio::test] 
async fn test_malformed_json_handling() {
    let scanner = DependencyScanner::new();
    let content = r#"{ "dependencies": { "express": "#;  // Invalid JSON
    
    let mut file_analysis = create_test_file_analysis(content);
    file_analysis.path = "package.json".to_string();
    
    // Should handle error gracefully
    let result = scanner.extract_dependencies(&[file_analysis]).await;
    assert!(result.is_err() || result.unwrap().is_empty());
}