# 🔬 Jules AI: Analysis Engine Surgical Refactoring Mission

## Mission Statement

You are a **Senior Rust Engineer** performing critical refactoring surgery on the Analysis Engine codebase. Like a surgeon operating on a live patient, one wrong cut could kill the system. Your mission is to systematically refactor 18 files (7 critical, 11 high-priority) following a strict 3-phase surgical approach with **ZERO downtime** and **100% backwards compatibility**.

## Critical Context

**Current State:**
- 7 files exceed 1,000 lines (CRITICAL)
- Largest offender: `storage/spanner.rs` (2,010 lines) - a God object handling ALL database operations
- System is in PRODUCTION with active users
- Any failure affects data integrity and service availability

**Your Constraints:**
- Maximum PR size: 200 lines
- Each refactor MUST have 100% test coverage BEFORE changes
- ALL changes behind feature flags
- Deploy after EVERY successful PR

## Phase 1: Safety Net Construction (Days 1-5)

### 1.1 Test Coverage Audit
```bash
# First, measure current coverage for critical files
cargo tarpaulin --out Html --output-dir coverage-report

# For each critical file, create comprehensive test suites
```

### 1.2 Write Missing Tests (Priority Order - Start with LOWEST risk)

#### Day 1-2: Low Risk Files
1. **services/repository_insights.rs** (1,261 lines)
   ```rust
   // Create: tests/refactoring/repository_insights_behavior_tests.rs
   // Must test ALL public functions with:
   // - Happy path scenarios
   // - Edge cases (empty repos, huge repos)
   // - Error conditions
   // - Concurrent access patterns
   ```

2. **services/intelligent_documentation.rs** (1,369 lines)
   ```rust
   // Create: tests/refactoring/intelligent_documentation_behavior_tests.rs
   // Focus on:
   // - AI service mocking
   // - Documentation generation accuracy
   // - Performance under load
   ```

#### Day 3-4: Medium Risk Files
3. **parser/language_metrics.rs** (1,309 lines)
   ```rust
   // Create: tests/refactoring/language_metrics_behavior_tests.rs
   // Critical tests:
   // - Metric calculation accuracy
   // - Multi-language support
   // - Performance benchmarks
   ```

#### Day 5: High Risk Files (Database & Auth)
4. **storage/spanner.rs** (2,010 lines) - EXTREME CAUTION
   ```rust
   // Create: tests/refactoring/spanner_behavior_tests.rs
   // MUST test:
   // - ALL database operations
   // - Transaction boundaries
   // - Connection pooling
   // - Error recovery
   // - Data integrity
   
   // Use testcontainers for real Spanner testing:
   use testcontainers::clients::Cli;
   use testcontainers_modules::google_cloud_sdk_emulators::CloudSdk;
   ```

### 1.3 Feature Flag Infrastructure
```rust
// Create: src/feature_flags.rs
pub struct FeatureFlags {
    refactored_spanner_enabled: bool,
    refactored_auth_enabled: bool,
    refactored_insights_enabled: bool,
    // ... one flag per refactored module
}

impl FeatureFlags {
    pub fn from_env() -> Self {
        Self {
            refactored_spanner_enabled: env::var("FF_REFACTORED_SPANNER")
                .map(|v| v == "true")
                .unwrap_or(false),
            // ... etc
        }
    }
}

// In main.rs, add feature flag checks:
let flags = FeatureFlags::from_env();
if flags.refactored_spanner_enabled {
    app = app.service(refactored_spanner_module);
} else {
    app = app.service(legacy_spanner_module);
}
```

## Phase 2: Surgical Planning (Days 6-8)

### 2.1 Dependency Mapping
For each file, create a dependency graph:

```rust
// Create: docs/refactoring/dependency_maps/spanner_dependencies.md
// Document:
// - Which modules depend on spanner.rs
// - Which external crates are used
// - Database schema dependencies
// - Critical paths that CANNOT fail
```

### 2.2 Identify Natural Boundaries

#### Example: Splitting spanner.rs
```rust
// Current: storage/spanner.rs (2,010 lines)
// Proposed modules:
// - storage/spanner/connection_pool.rs (200 lines)
// - storage/spanner/transactions.rs (300 lines)
// - storage/spanner/queries/analysis.rs (400 lines)
// - storage/spanner/queries/metadata.rs (300 lines)
// - storage/spanner/error_handling.rs (150 lines)
// - storage/spanner/migrations.rs (200 lines)
// - storage/spanner/mod.rs (100 lines) - public interface only
```

### 2.3 Risk Assessment Matrix
| File | Risk Level | Reason | Mitigation Strategy |
|------|------------|--------|-------------------|
| spanner.rs | CRITICAL | Database operations | Feature flags + canary deployment |
| auth_layer.rs | CRITICAL | Security | Extensive pentesting |
| repository_insights.rs | LOW | Read-only operations | Standard testing |

## Phase 3: Incremental Execution (Days 9-30)

### 3.1 Refactoring Order (LOWEST to HIGHEST risk)

#### Week 1: Low-Risk Targets
**PR #1: Extract Documentation Types** (Target: <200 lines)
```rust
// From: services/intelligent_documentation.rs
// Extract: services/intelligent_documentation/types.rs
pub struct DocumentationRequest { ... }
pub struct DocumentationResponse { ... }
pub enum DocumentationType { ... }

// Create PR with:
// - Old types marked #[deprecated]
// - New module with clean types
// - Tests for serialization/deserialization
```

**PR #2: Extract Documentation Generator** (Target: <200 lines)
```rust
// From: services/intelligent_documentation.rs
// Extract: services/intelligent_documentation/generator.rs
pub trait DocumentationGenerator {
    async fn generate(&self, request: DocumentationRequest) -> Result<DocumentationResponse>;
}

// With feature flag:
if flags.refactored_documentation_enabled {
    Box::new(RefactoredDocGenerator::new())
} else {
    Box::new(LegacyDocGenerator::new())
}
```

#### Week 2: Medium-Risk Targets
**PR #3-5: Refactor Language Metrics**
```rust
// Split language_metrics.rs into:
// - metrics/collectors/mod.rs
// - metrics/calculators/mod.rs
// - metrics/aggregators/mod.rs
```

#### Week 3-4: HIGH-RISK Database Refactoring
**PR #6-15: Spanner Refactoring** (10 micro-PRs)
```rust
// CRITICAL: Use Strangler Fig Pattern
// Step 1: Create new module structure WITHOUT removing old code
// Step 2: Route traffic through feature flags
// Step 3: Monitor for 48 hours before proceeding
// Step 4: Remove old code only after 1 week stable

// Example Strangler Fig implementation:
pub struct SpannerClient {
    legacy: Option<LegacySpanner>,
    refactored: Option<RefactoredSpanner>,
    flags: FeatureFlags,
}

impl SpannerClient {
    pub async fn get_analysis(&self, id: &str) -> Result<Analysis> {
        if self.flags.refactored_spanner_enabled {
            self.refactored.as_ref()
                .ok_or_else(|| Error::ConfigError)?
                .get_analysis(id)
                .await
        } else {
            self.legacy.as_ref()
                .ok_or_else(|| Error::ConfigError)?
                .get_analysis(id)
                .await
        }
    }
}
```

### 3.2 PR Structure Template
Every PR MUST follow this structure:
```markdown
## PR Title: refactor(module): Extract [specific functionality] - Part X/Y

### Changes
- Extracted [specific functions] to [new module]
- Added comprehensive tests (coverage: XX%)
- Feature flag: FF_REFACTORED_[MODULE]

### Testing
- [ ] All existing tests pass
- [ ] New tests added (link to test file)
- [ ] Manual testing completed
- [ ] Performance benchmarks show no regression

### Rollback Plan
- Disable feature flag: FF_REFACTORED_[MODULE]=false
- Revert this PR if critical issues found

### Metrics to Monitor
- Error rate on [specific endpoints]
- Database query performance
- Memory usage
```

## Success Criteria

### Per-PR Success Metrics
- [ ] PR size < 200 lines
- [ ] Test coverage > 95% for changed code
- [ ] All existing tests pass
- [ ] Performance benchmarks show <5% regression
- [ ] Feature flag tested in both states
- [ ] Code review approved by 2 engineers

### Overall Success Metrics
- [ ] All files < 500 lines
- [ ] Overall test coverage > 90%
- [ ] Zero production incidents
- [ ] Performance improved or maintained
- [ ] Code complexity reduced by 40%

## Monitoring & Rollback

### Monitoring Dashboard
Create Grafana dashboard tracking:
- Error rates per module
- Response time percentiles
- Feature flag states
- Memory usage per service

### Rollback Procedures
```bash
# Immediate rollback (< 5 minutes)
export FF_REFACTORED_[MODULE]=false
kubectl rollout restart deployment/analysis-engine

# Full rollback (if needed)
git revert [PR commit]
./scripts/deploy.sh production --emergency
```

## Communication Protocol

### Daily Updates
Post in #refactoring-progress:
```
Day X Update:
- Completed: [PR links]
- In Progress: [current work]
- Blockers: [any issues]
- Metrics: [error rate, performance]
- Next: [tomorrow's plan]
```

### Weekly Reviews
- Architecture review meeting
- Performance analysis
- Risk assessment update
- Adjust plan if needed

## Code Examples & Patterns

### 1. Test Pattern for Behavior Coverage
```rust
#[cfg(test)]
mod behavioral_tests {
    use super::*;
    use proptest::prelude::*;
    
    #[test]
    fn test_complete_user_journey() {
        // Arrange
        let system = setup_test_system();
        
        // Act - test the BEHAVIOR not the implementation
        let result = system.analyze_repository("test-repo").await;
        
        // Assert
        assert!(result.is_ok());
        assert_eq!(result.files_analyzed, 42);
    }
    
    proptest! {
        #[test]
        fn test_never_panics(input: String) {
            let result = std::panic::catch_unwind(|| {
                parse_input(&input)
            });
            assert!(result.is_ok());
        }
    }
}
```

### 2. Feature Flag Pattern
```rust
pub struct FeatureFlaggedService<T, U> {
    legacy: T,
    refactored: U,
    flag_enabled: bool,
}

impl<T, U> Service for FeatureFlaggedService<T, U>
where
    T: Service,
    U: Service,
{
    async fn handle(&self, req: Request) -> Result<Response> {
        if self.flag_enabled {
            self.refactored.handle(req).await
        } else {
            self.legacy.handle(req).await
        }
    }
}
```

### 3. Incremental Migration Pattern
```rust
// Step 1: Create new trait
pub trait SpannerOperations: Send + Sync {
    async fn get_analysis(&self, id: &str) -> Result<Analysis>;
    async fn store_analysis(&self, analysis: &Analysis) -> Result<()>;
}

// Step 2: Implement for both old and new
impl SpannerOperations for LegacySpanner { ... }
impl SpannerOperations for RefactoredSpanner { ... }

// Step 3: Use trait object with feature flag
pub fn create_spanner_client(flags: &FeatureFlags) -> Box<dyn SpannerOperations> {
    if flags.refactored_spanner_enabled {
        Box::new(RefactoredSpanner::new())
    } else {
        Box::new(LegacySpanner::new())
    }
}
```

## Rust-Specific Best Practices (2024 Standards)

### Module Organization Principles
1. **Single Responsibility**: Each module should have ONE clear purpose
2. **File Naming**: Use snake_case for files, match module names
3. **Directory Structure**: Group related modules in subdirectories
4. **Public Interface**: Keep mod.rs minimal - only re-exports and traits

### God Object Refactoring Patterns

#### Pattern 1: Extract Domain Services
```rust
// Before: Single 2000-line spanner.rs doing everything
// After: Domain-specific services

// storage/spanner/services/analysis_service.rs
pub struct AnalysisService {
    pool: Arc<ConnectionPool>,
}

impl AnalysisService {
    pub async fn get_analysis(&self, id: &str) -> Result<Analysis> {
        // Only analysis-related queries
    }
}

// storage/spanner/services/metadata_service.rs
pub struct MetadataService {
    pool: Arc<ConnectionPool>,
}

// storage/spanner/mod.rs - Facade pattern
pub struct SpannerClient {
    analysis: AnalysisService,
    metadata: MetadataService,
    security: SecurityService,
}
```

#### Pattern 2: Use Traits for Abstraction
```rust
// Define clear interfaces
pub trait AnalysisRepository: Send + Sync {
    async fn get(&self, id: &str) -> Result<Analysis>;
    async fn store(&self, analysis: &Analysis) -> Result<()>;
    async fn list(&self, filter: Filter) -> Result<Vec<AnalysisSummary>>;
}

// Implement for both legacy and refactored
impl AnalysisRepository for LegacySpanner { ... }
impl AnalysisRepository for RefactoredSpanner { ... }
```

### Workspace Organization for Large Refactoring
```toml
# Consider creating a workspace structure:
[workspace]
members = [
    "services/analysis-engine",
    "services/analysis-engine-core",     # Extract core logic
    "services/analysis-engine-storage",   # Extract storage layer
    "services/analysis-engine-api",       # Extract API layer
]
```

### Common Pitfalls to AVOID

1. **Over-Engineering**: Don't create 50 tiny modules - find the right balance
2. **Circular Dependencies**: Plan module hierarchy to prevent cycles
3. **Public Everything**: Keep implementation details private
4. **Ignoring Lifetime Complexity**: Refactoring can introduce lifetime issues

### Ownership & Borrowing During Refactoring
```rust
// BAD: Passing ownership back and forth
fn process_data(data: Vec<u8>) -> Vec<u8> { ... }

// GOOD: Use references where possible
fn process_data(data: &[u8]) -> Vec<u8> { ... }

// BETTER: Use builders for complex operations
struct AnalysisBuilder<'a> {
    data: &'a [u8],
    config: Config,
}
```

## Monitoring & Success Metrics

### Key Metrics to Track
1. **Compilation Time**: Should decrease as modules become smaller
2. **Test Execution Time**: Parallel test execution should improve
3. **Memory Usage**: Smaller modules = better memory locality
4. **Developer Velocity**: Time to implement new features

### Performance Benchmarks
```rust
// Add benchmarks before refactoring
#[bench]
fn bench_analysis_query(b: &mut Bencher) {
    b.iter(|| {
        // Current implementation
    });
}

// Compare after each refactoring
```

## Communication Templates

### PR Description Template
```markdown
## 🔬 Refactoring: [Module Name] - Part X of Y

### 📊 Metrics
- Lines removed: XXX
- Lines added: YYY
- Net reduction: ZZZ
- Test coverage: Before X% → After Y%
- Compilation time: Before Xs → After Ys

### 🏗️ Architecture Changes
[Diagram or description of new structure]

### 🧪 Testing Strategy
- [ ] All existing tests pass
- [ ] New behavioral tests added
- [ ] Performance benchmarks show no regression
- [ ] Feature flag tested in both states

### 🚨 Risk Assessment
- **Risk Level**: LOW/MEDIUM/HIGH
- **Rollback Time**: < X minutes
- **Monitoring**: [Specific metrics to watch]
```

## Final Instructions

1. **Start TODAY** with the lowest-risk file test writing
2. **Create a PR within 24 hours** to maintain momentum
3. **NEVER** refactor without tests
4. **ALWAYS** use feature flags
5. **MEASURE** everything - performance, errors, coverage
6. **COMMUNICATE** progress daily
7. **FOLLOW** Rust idioms - don't fight the borrow checker
8. **DOCUMENT** architectural decisions in ADRs

Remember: You're performing surgery on a live patient. The goal is ZERO downtime with improved code quality. Each cut must be precise, tested, and reversible.

**Success = Smaller modules + Better tests + Maintained performance + Happy developers**

Good luck, Doctor! 🔬🦀