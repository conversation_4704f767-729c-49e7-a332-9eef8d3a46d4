    Checking regex v1.11.1
    Checking hyper v1.6.0
    Checking tower v0.5.2
    Checking url v2.5.4
    Checking num-traits v0.2.19
    Checking axum-core v0.4.5
    Checking tower v0.4.13
    Checking h2 v0.3.27
    Checking futures-executor v0.3.31
    Checking globset v0.4.16
    Checking tokio-tungstenite v0.26.2
   Compiling analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
    Checking futures v0.3.31
    Checking governor v0.6.3
error[E0599]: no function or associated item named `custom` found for struct `serde_json::Error` in the current scope
   --> build.rs:134:32
    |
134 |             serde_json::Error::custom("packages field not found or not array")
    |                                ^^^^^^ function or associated item not found in `Error`
    |
note: if you're trying to build a new `serde_json::Error`, consider using `serde_json::Error::io` which returns `serde_json::Error`
   --> /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde_json-1.0.140/src/error.rs:326:5
    |
326 |     pub fn io(error: io::Error) -> Self {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    = help: items from traits can only be used if the trait is in scope
help: the following traits which provide `custom` are implemented but not in scope; perhaps you want to import one of them
    |
3   + use serde::de::Error;
    |
3   + use serde::ser::Error;
    |

error[E0599]: no function or associated item named `custom` found for struct `serde_json::Error` in the current scope
   --> build.rs:142:36
    |
142 |                 serde_json::Error::custom("package name not found or not string")
    |                                    ^^^^^^ function or associated item not found in `Error`
    |
note: if you're trying to build a new `serde_json::Error`, consider using `serde_json::Error::io` which returns `serde_json::Error`
   --> /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde_json-1.0.140/src/error.rs:326:5
    |
326 |     pub fn io(error: io::Error) -> Self {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    = help: items from traits can only be used if the trait is in scope
help: the following traits which provide `custom` are implemented but not in scope; perhaps you want to import one of them
    |
3   + use serde::de::Error;
    |
3   + use serde::ser::Error;
    |

error[E0599]: no function or associated item named `custom` found for struct `serde_json::Error` in the current scope
   --> build.rs:148:40
    |
148 |                     serde_json::Error::custom("manifest_path not found or not string")
    |                                        ^^^^^^ function or associated item not found in `Error`
    |
note: if you're trying to build a new `serde_json::Error`, consider using `serde_json::Error::io` which returns `serde_json::Error`
   --> /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/serde_json-1.0.140/src/error.rs:326:5
    |
326 |     pub fn io(error: io::Error) -> Self {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    = help: items from traits can only be used if the trait is in scope
help: the following traits which provide `custom` are implemented but not in scope; perhaps you want to import one of them
    |
3   + use serde::de::Error;
    |
3   + use serde::ser::Error;
    |

For more information about this error, try `rustc --explain E0599`.
error: could not compile `analysis-engine` (build script) due to 3 previous errors
warning: build failed, waiting for other jobs to finish...
