#!/bin/bash
# Fix deployment for analysis-engine on Cloud Run
set -euo pipefail

echo "🚀 Fixing Analysis Engine deployment on Cloud Run..."

# Configuration
PROJECT_ID="vibe-match-463114"
SERVICE_NAME="analysis-engine"
REGION="us-central1"
IMAGE_TAG="fix-$(date +%Y%m%d-%H%M%S)"

# Check if we're in the right directory
if [ ! -f "Cargo.toml" ]; then
    echo "❌ Error: Must run from services/analysis-engine directory"
    exit 1
fi

echo "📦 Building Docker image for Cloud Run..."
# Build with standard runtime first to debug
docker build \
    --platform linux/amd64 \
    --build-arg RUNTIME_TARGET=standard \
    -t gcr.io/${PROJECT_ID}/${SERVICE_NAME}:${IMAGE_TAG} \
    -f Dockerfile \
    .

echo "🧪 Testing container locally..."
# Test the container locally
docker run -d \
    --name test-analysis-engine \
    -p 8001:8001 \
    -e PORT=8001 \
    -e RUST_LOG=info \
    -e GCP_PROJECT_ID=${PROJECT_ID} \
    gcr.io/${PROJECT_ID}/${SERVICE_NAME}:${IMAGE_TAG}

# Wait for startup
echo "⏳ Waiting for container to start..."
sleep 5

# Test health endpoint
if curl -f http://localhost:8001/health; then
    echo "✅ Local test passed!"
else
    echo "❌ Local test failed. Checking logs..."
    docker logs test-analysis-engine
    docker stop test-analysis-engine
    docker rm test-analysis-engine
    exit 1
fi

# Clean up test container
docker stop test-analysis-engine
docker rm test-analysis-engine

echo "🔧 Configuring Docker for GCR..."
gcloud auth configure-docker --quiet

echo "📤 Pushing image to Container Registry..."
docker push gcr.io/${PROJECT_ID}/${SERVICE_NAME}:${IMAGE_TAG}

echo "🚀 Deploying to Cloud Run..."
# Deploy with minimal config first to isolate issues
gcloud run deploy ${SERVICE_NAME} \
    --image gcr.io/${PROJECT_ID}/${SERVICE_NAME}:${IMAGE_TAG} \
    --platform managed \
    --region ${REGION} \
    --memory 2Gi \
    --cpu 2 \
    --timeout 300 \
    --max-instances 10 \
    --set-env-vars "RUST_LOG=info" \
    --set-env-vars "PORT=8001" \
    --allow-unauthenticated \
    --project ${PROJECT_ID}

echo "🔍 Getting service URL..."
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} \
    --platform managed \
    --region ${REGION} \
    --project ${PROJECT_ID} \
    --format 'value(status.url)')

echo "Service URL: $SERVICE_URL"

echo "🏥 Testing health endpoint..."
sleep 10  # Give Cloud Run time to start

if curl -f "${SERVICE_URL}/health"; then
    echo "✅ Deployment successful!"
    echo "🎉 Analysis Engine is running at: ${SERVICE_URL}"
else
    echo "❌ Health check failed. Checking logs..."
    gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=${SERVICE_NAME}" \
        --limit 20 \
        --project ${PROJECT_ID} \
        --format "table(timestamp,severity,textPayload)"
fi