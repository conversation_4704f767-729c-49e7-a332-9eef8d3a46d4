.PHONY: help dev test build docker-dev docker-build deploy clean lint fmt check migrate migrate-status migrate-test migrate-rollback validate-indexes

# Default target
help:
	@echo "Analysis Engine Development Commands"
	@echo "===================================="
	@echo "make dev          - Run locally with credentials"
	@echo "make test         - Run all tests"
	@echo "make build        - Build release binary"
	@echo "make docker-dev   - Run with Docker Compose"
	@echo "make docker-build - Build Docker image"
	@echo "make deploy       - Deploy to Cloud Run"
	@echo "make clean        - Clean build artifacts"
	@echo "make lint         - Run linters"
	@echo "make fmt          - Format code"
	@echo "make check        - Run pre-commit checks"
	@echo ""
	@echo "Database Migration Commands"
	@echo "=========================="
	@echo "make migrate         - Run pending migrations"
	@echo "make migrate-status  - Show migration status"
	@echo "make migrate-test    - Test migrations in development"
	@echo "make migrate-rollback VERSION=xxx - Rollback to specific version"
	@echo "make validate-indexes - Validate database indexes"

# Local development with credentials
dev:
	@echo "Starting analysis-engine in development mode..."
	@if [ ! -f "../../vibe-match-463114-dbda8d8a6cb9.json" ]; then \
		echo "ERROR: Service account key not found at ../../vibe-match-463114-dbda8d8a6cb9.json"; \
		echo "Please ensure the credentials file exists"; \
		exit 1; \
	fi
	@if [ ! -f ".env" ]; then \
		echo "Creating .env from .env.example..."; \
		cp .env.example .env; \
	fi
	GOOGLE_APPLICATION_CREDENTIALS=../../vibe-match-463114-dbda8d8a6cb9.json \
	RUST_LOG=analysis_engine=debug,tower_http=debug \
	cargo run

# Run tests
test:
	@echo "Running tests..."
	cargo test --all-features

# Build release binary
build:
	@echo "Building release binary..."
	cargo build --release

# Run with Docker Compose
docker-dev:
	@echo "Starting with Docker Compose..."
	@if [ ! -f "../../vibe-match-463114-dbda8d8a6cb9.json" ]; then \
		echo "ERROR: Service account key not found at ../../vibe-match-463114-dbda8d8a6cb9.json"; \
		echo "Please ensure the credentials file exists"; \
		exit 1; \
	fi
	docker-compose up --build

# Build Docker image
docker-build:
	@echo "Building Docker image..."
	docker build -f Dockerfile.cloudrun -t analysis-engine:latest .

# Deploy to Cloud Run
deploy:
	@echo "Deploying to Cloud Run..."
	@if [ -z "$(PROJECT_ID)" ]; then \
		echo "Using default project ID: vibe-match-463114"; \
		export PROJECT_ID=vibe-match-463114; \
	fi
	./deploy.sh

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	cargo clean
	rm -rf tmp/
	docker-compose down -v 2>/dev/null || true

# Run linters
lint:
	@echo "Running linters..."
	cargo clippy -- -D warnings

# Format code
fmt:
	@echo "Formatting code..."
	cargo fmt

# Run all pre-commit checks
check: fmt lint test
	@echo "All checks passed!"

# Check authentication status
auth-check:
	@echo "Checking authentication status..."
	@if [ -f ".env" ]; then \
		source .env && curl -s http://localhost:8001/health/auth | jq .; \
	else \
		echo "No .env file found. Run 'make dev' first."; \
	fi

# View logs
logs:
	docker-compose logs -f analysis-engine

# Stop all services
stop:
	docker-compose down

# Restart services
restart: stop docker-dev

# Database migration commands
migrate:
	@echo "Running database migrations..."
	./run_migrations.sh run

migrate-status:
	@echo "Checking migration status..."
	./run_migrations.sh status

migrate-test:
	@echo "Testing migrations in development environment..."
	./test_migrations.sh

migrate-rollback:
	@echo "Rolling back to version $(VERSION)..."
	@if [ -z "$(VERSION)" ]; then \
		echo "ERROR: VERSION parameter is required"; \
		echo "Usage: make migrate-rollback VERSION=001"; \
		exit 1; \
	fi
	./rollback_migration.sh rollback-to $(VERSION)

validate-indexes:
	@echo "Validating database indexes..."
	./validate_indexes.sh

# Production migration commands (with explicit project/instance)
migrate-prod:
	@echo "Running migrations in production..."
	@if [ -z "$(PROJECT_ID)" ] || [ -z "$(INSTANCE_ID)" ]; then \
		echo "ERROR: PROJECT_ID and INSTANCE_ID are required for production migrations"; \
		echo "Usage: make migrate-prod PROJECT_ID=ccl-production INSTANCE_ID=ccl-instance"; \
		exit 1; \
	fi
	./run_migrations.sh -p $(PROJECT_ID) -i $(INSTANCE_ID) run

migrate-status-prod:
	@echo "Checking production migration status..."
	@if [ -z "$(PROJECT_ID)" ] || [ -z "$(INSTANCE_ID)" ]; then \
		echo "ERROR: PROJECT_ID and INSTANCE_ID are required"; \
		echo "Usage: make migrate-status-prod PROJECT_ID=ccl-production INSTANCE_ID=ccl-instance"; \
		exit 1; \
	fi
	./run_migrations.sh -p $(PROJECT_ID) -i $(INSTANCE_ID) status

validate-indexes-prod:
	@echo "Validating production database indexes..."
	@if [ -z "$(PROJECT_ID)" ] || [ -z "$(INSTANCE_ID)" ]; then \
		echo "ERROR: PROJECT_ID and INSTANCE_ID are required"; \
		echo "Usage: make validate-indexes-prod PROJECT_ID=ccl-production INSTANCE_ID=ccl-instance"; \
		exit 1; \
	fi
	./validate_indexes.sh -p $(PROJECT_ID) -i $(INSTANCE_ID)