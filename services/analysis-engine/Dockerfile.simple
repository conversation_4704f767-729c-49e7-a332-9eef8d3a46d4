# Simple Dockerfile for Analysis Engine
FROM rust:latest AS builder

WORKDIR /usr/src/app

# Install build dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    ca-certificates \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy all source files
COPY . .

# Build only the main binary, not the test binaries
RUN cargo build --release --bin analysis-engine

# Runtime stage
FROM debian:bookworm-slim

RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    && rm -rf /var/lib/apt/lists/*

# Copy the binary
COPY --from=builder /usr/src/app/target/release/analysis-engine /app/analysis-engine

# Make it executable
RUN chmod +x /app/analysis-engine

# Environment variables
ENV RUST_LOG=info
ENV RUST_BACKTRACE=1

# Use port 8080 for Cloud Run
EXPOSE 8080

# Run the binary
ENTRYPOINT ["/app/analysis-engine"]