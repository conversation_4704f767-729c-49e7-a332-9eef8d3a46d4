# Simple Dockerfile for Tower Service deployment
FROM rust:latest as builder

WORKDIR /usr/src/app

# Copy dependency files
COPY Cargo.toml Cargo.lock ./
COPY build.rs ./
COPY src ./src
COPY benches ./benches

# Build only the main binary 
RUN cargo build --release --bin analysis-engine

# Runtime stage
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN useradd -m -u 1001 appuser

# Copy the binary
COPY --from=builder /usr/src/app/target/release/analysis-engine /usr/local/bin/analysis-engine

# Set ownership
RUN chown -R appuser:appuser /usr/local/bin/analysis-engine

# Use non-root user
USER appuser

# Expose port
EXPOSE 8001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8001/health || exit 1

# Run the application
CMD ["analysis-engine"]