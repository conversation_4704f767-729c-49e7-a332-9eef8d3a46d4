# MCP Server Usage Guide for Episteme Project

This document tracks which MCP servers are used for specific purposes in the Episteme Analysis Engine project.

## 🔌 Active MCP Servers

### 1. Context7 (Library Documentation)
**Status**: ✅ Enabled  
**Purpose**: Official library documentation and examples

**Common Uses**:
- Tree-sitter parser documentation
- Rust crate documentation (tokio, actix-web, etc.)
- Google Cloud library references
- Framework best practices

**Example Commands**:
```bash
/analyze --c7  # Research library patterns
/build --feature --c7  # Build with official docs
/troubleshoot --c7  # Debug with documentation
```

### 2. Sequential (Complex Analysis)
**Status**: ✅ Enabled  
**Purpose**: Multi-step problem solving and architectural thinking

**Common Uses**:
- Security vulnerability analysis
- Performance bottleneck investigation
- Architectural design decisions
- Complex debugging scenarios

**Example Commands**:
```bash
/analyze --seq  # Deep system analysis
/design --arch --seq --ultrathink  # Architecture planning
/troubleshoot --seq --evidence  # Root cause analysis
```

### 3. Magic (UI Components)
**Status**: ✅ Enabled  
**Purpose**: UI component generation (currently unused - backend focus)

**Potential Uses**:
- Admin dashboard components
- Monitoring UI elements
- Documentation site components

**Example Commands**:
```bash
/build --dashboard --magic  # Generate dashboard components
/design --ui --magic  # Design UI components
```

### 4. Puppeteer (Browser Automation)
**Status**: ✅ Enabled  
**Purpose**: E2E testing and performance validation

**Common Uses**:
- API endpoint testing
- Performance metrics collection
- Integration testing
- Load testing validation

**Example Commands**:
```bash
/test --e2e --pup  # End-to-end testing
/analyze --performance --pup  # Performance analysis
/validate --api --pup  # API validation
```

### 5. GCP-MCP (Google Cloud Integration)
**Status**: ✅ Enabled  
**Purpose**: Google Cloud Platform operations

**Common Uses**:
- Spanner database operations
- Cloud Storage management
- Cloud Run deployment
- Project configuration

**Example Commands**:
```bash
# Integrated into deployment workflows
/deploy --env prod  # Uses GCP-MCP internally
/migrate --database  # Spanner operations
```

### 6. Vertex AI (AI/ML Operations)
**Status**: ✅ Enabled  
**Purpose**: AI/ML operations and embeddings

**Common Uses**:
- Code embeddings generation
- Pattern detection enhancement
- Documentation analysis
- Semantic search

**Integrated Features**:
- Automatic embedding generation for code analysis
- ML-based vulnerability detection
- Intelligent pattern matching

## 📊 Usage Patterns

### By Frequency (Estimated)
1. **Sequential** (40%) - Complex analysis tasks
2. **Context7** (30%) - Documentation lookups
3. **GCP-MCP** (15%) - Deployment operations
4. **Vertex AI** (10%) - Embeddings/ML
5. **Puppeteer** (4%) - Testing
6. **Magic** (1%) - UI (minimal use)

### By Task Type
- **Analysis**: Sequential + Context7
- **Development**: Context7 + GCP-MCP
- **Testing**: Puppeteer + Sequential
- **Deployment**: GCP-MCP + Vertex AI
- **Security**: Sequential + Context7
- **Performance**: Sequential + Puppeteer

## 🎯 Best Practices

1. **Combine MCP Servers**: Use multiple servers for comprehensive analysis
   ```bash
   /analyze --security --seq --c7  # Security with docs
   ```

2. **Token Optimization**: Be aware of token costs
   - Context7: Low-Medium
   - Sequential: Medium-High
   - Magic: Medium
   - Puppeteer: Low

3. **Parallel Execution**: Independent MCP calls run simultaneously
   ```bash
   /analyze --all-mcp  # Parallel analysis
   ```

4. **Caching**: Leverage built-in caching
   - Context7: 1 hour TTL
   - Sequential: Session duration
   - Magic: 2 hours TTL

## 🔧 Configuration

All MCP servers are configured in:
- `.claude/settings.local.json` - Project-specific settings
- `~/.claude/mcp.json` - Global MCP configuration

To disable specific servers:
```json
"disabledMcpjsonServers": ["magic"]  // If not using UI components
```

---
*Last Updated: January 2025 | Episteme Analysis Engine*