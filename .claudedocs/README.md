# SuperClaude Documentation Directory

This directory follows the SuperClaude documentation structure for AI-assisted development.

## Directory Structure

- **reports/** - Analysis reports, security scans, performance audits
- **metrics/** - Performance metrics, code quality metrics, coverage reports  
- **summaries/** - Session summaries, feature summaries, decision logs
- **checkpoints/** - Project checkpoints, milestone documentation
- **tasks/** - Level 1 persistent tasks for multi-session feature tracking

## Task Management

SuperClaude uses a two-tier task management system:

1. **Level 1 Tasks** (Persistent)
   - Stored in `tasks/` directory
   - Track features across multiple sessions
   - Git branch association
   - Requirement tracking

2. **Level 2 Todos** (Session-specific)
   - Managed via TodoWrite/TodoRead
   - Real-time execution tracking
   - Auto-triggered for complex operations (3+ steps)

## Usage

Tasks are automatically created when:
- Complex operations require 3+ steps
- High-risk changes (database, deployments)
- Long tasks (>30 minutes estimated)
- Multi-file operations (6+ files)

## Integration with SuperClaude

This structure enables:
- Evidence-based development tracking
- Performance monitoring over time
- Decision documentation
- Cross-session continuity