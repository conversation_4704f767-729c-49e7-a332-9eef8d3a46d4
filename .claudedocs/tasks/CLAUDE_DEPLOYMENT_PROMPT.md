# 🚀 Claude Code Agent Deployment Prompt

## Mission Brief

You are a **DevOps Engineer** with the **--persona-security** and **--persona-architect** personas activated. Your mission is to deploy the Analysis Engine service to Google Cloud Run. The service is **100% production-ready** but currently has a container startup issue preventing it from running on Cloud Run.

## Context & Current State

**Service**: Analysis Engine (Rust-based microservice)
**Project**: vibe-match-463114  
**Region**: us-central1
**Current Issue**: Container failing to start on Cloud Run (port binding issue)
**Readiness**: 100% code complete with all features implemented

## Deployment Command

```bash
/deploy --env production --validate --monitor --checkpoint --seq --think-hard
```

## Your Task Breakdown

### Phase 1: Analysis & Validation
```bash
/analyze --code --arch --security --seq
```
1. Review the deployment configuration in:
   - `services/analysis-engine/Dockerfile`
   - `services/analysis-engine/scripts/deploy.sh`
   - `services/analysis-engine/deploy-fix.sh`
   - `services/analysis-engine/DEPLOYMENT_GUIDE.md`

2. Validate the current Cloud Run service status:
   ```bash
   gcloud run services describe analysis-engine --region us-central1
   ```

3. Check recent deployment logs to understand the failure:
   ```bash
   gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=analysis-engine" --limit 20
   ```

### Phase 2: Fix Container Issue
```bash
/troubleshoot --investigate --seq --evidence
```
1. The container is failing because it's not properly binding to the PORT environment variable
2. Build a new Docker image with proper configuration:
   ```bash
   cd services/analysis-engine
   docker build \
     --platform linux/amd64 \
     --build-arg RUNTIME_TARGET=standard \
     -t gcr.io/vibe-match-463114/analysis-engine:fix-$(date +%Y%m%d-%H%M%S) \
     .
   ```

3. Test locally before deploying:
   ```bash
   docker run -p 8001:8001 \
     -e PORT=8001 \
     -e RUST_LOG=info \
     gcr.io/vibe-match-463114/analysis-engine:fix-$(date +%Y%m%d-%H%M%S)
   ```

### Phase 3: Production Deployment
```bash
/deploy --env production --validate --checkpoint
```
1. Push the fixed image to Container Registry:
   ```bash
   docker push gcr.io/vibe-match-463114/analysis-engine:fix-$(date +%Y%m%d-%H%M%S)
   ```

2. Deploy to Cloud Run with full production configuration:
   ```bash
   gcloud run deploy analysis-engine \
     --image gcr.io/vibe-match-463114/analysis-engine:fix-$(date +%Y%m%d-%H%M%S) \
     --platform managed \
     --region us-central1 \
     --memory 4Gi \
     --cpu 4 \
     --min-instances 1 \
     --max-instances 1000 \
     --concurrency 50 \
     --timeout 600 \
     --set-env-vars "RUST_LOG=info" \
     --set-env-vars "GCP_PROJECT_ID=vibe-match-463114" \
     --set-env-vars "SPANNER_INSTANCE=ccl-instance" \
     --set-env-vars "SPANNER_DATABASE=ccl_main" \
     --set-env-vars "STORAGE_BUCKET=ccl-analysis-artifacts" \
     --set-env-vars "MAX_FILE_SIZE_BYTES=********" \
     --set-env-vars "PARSE_TIMEOUT_SECONDS=30" \
     --set-env-vars "MAX_ANALYSIS_MEMORY_MB=2048" \
     --set-env-vars "MAX_DEPENDENCY_COUNT=10000" \
     --service-account <EMAIL> \
     --allow-unauthenticated
   ```

### Phase 4: Validation & Monitoring
```bash
/test --e2e --validate --monitor
```
1. Get the service URL and test health endpoint:
   ```bash
   SERVICE_URL=$(gcloud run services describe analysis-engine --region us-central1 --format 'value(status.url)')
   curl "${SERVICE_URL}/health"
   ```

2. Run smoke tests:
   ```bash
   ./scripts/smoke_test.sh "${SERVICE_URL}"
   ```

3. Monitor deployment metrics:
   ```bash
   gcloud monitoring metrics-descriptors list --filter="metric.type:run.googleapis.com"
   ```

## Task Management Requirements

Since this is a **high-risk deployment operation**, you MUST:
1. Use TodoWrite to track each deployment phase
2. Create checkpoints after each successful step
3. Document any issues or deviations in `.claudedocs/reports/`
4. Update deployment status in `.claudedocs/checkpoints/`

## Success Criteria

✅ Health endpoint returns: `{"status":"healthy","service":"analysis-engine","version":"0.1.0"}`
✅ Service handles concurrent requests without errors
✅ All 18+ language parsers are operational
✅ API endpoints respond within SLO (<100ms p95)
✅ No errors in Cloud Run logs

## Security Considerations

With **--persona-security** active, ensure:
- Service account follows least privilege principle
- No sensitive data in environment variables
- All endpoints have proper input validation
- Rate limiting is configured
- Monitoring alerts are set up

## Evidence-Based Reporting

Document your deployment with:
- Screenshots of successful health checks
- Performance metrics from initial load
- Any configuration changes made
- Lessons learned for future deployments

## Rollback Plan

If deployment fails:
```bash
# List previous working revisions
gcloud run revisions list --service analysis-engine --region us-central1

# Rollback to last known good revision
gcloud run services update-traffic analysis-engine \
  --to-revisions PREVIOUS_REVISION=100 \
  --region us-central1
```

## Final Notes

- The deployment scripts and guides are in `services/analysis-engine/`
- The service is 100% code complete - focus only on deployment
- Current issue is container startup, not code problems
- Use `--seq` for systematic troubleshooting if needed
- Document everything in `.claudedocs/` for audit trail

Good luck with the deployment! Remember: measure twice, deploy once.