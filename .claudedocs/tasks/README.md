# Level 1 Task Management

This directory contains persistent tasks that span multiple Claude sessions.

## Task Format

Each task is stored as a separate markdown file with the following structure:

```markdown
# TASK-001: [Task Title]

## Status: [Planning|In Progress|Blocked|Completed]
## Priority: [High|Medium|Low]
## Created: [Date]
## Branch: [Git branch name]

### Description
[Detailed task description]

### Requirements
- [ ] Requirement 1
- [ ] Requirement 2

### Session Log
- Session 1 (Date): [Progress made]
- Session 2 (Date): [Progress made]
```

## Task Lifecycle

1. **Creation**: Auto-triggered for complex features
2. **Tracking**: Updated across multiple sessions
3. **Completion**: Moved to `completed/` subdirectory
4. **Archival**: Old completed tasks archived monthly

## Integration with Level 2 Todos

- Level 1: Strategic feature tracking (this directory)
- Level 2: Tactical execution steps (TodoWrite/TodoRead)

## Auto-Creation Triggers

Tasks are automatically created when:
- Feature spans multiple sessions
- Complexity requires planning documentation
- Multiple team members need visibility
- Architectural decisions need recording