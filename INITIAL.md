# INITIAL.md - Feature Request Template

This file serves as a template for creating feature requests that will be processed through the Context Engineering workflow. Copy this template and modify it for your specific feature requirements.

## FEATURE:
[Describe what you want to build - be specific about functionality and requirements]

**Example:**
```
Build an async repository analysis service that processes Git repositories, extracts AST information using Tree-sitter, performs security analysis, and stores results in Spanner with Redis caching. The service should handle repositories up to 1M LOC within 5 minutes and support 50+ concurrent analyses.
```

## EXAMPLES:
[List any example files in the examples/ folder and explain how they should be used]

**Example:**
```
- examples/analysis-engine/service_pattern.rs - Follow this pattern for service structure
- examples/analysis-engine/api_handler.rs - Use this for API endpoint implementation
- examples/analysis-engine/error_handling.rs - Apply these error handling patterns
- examples/testing/integration_test.rs - Use this testing approach
```

## DOCUMENTATION:
[Include links to relevant research directory documentation and official sources - REQUIRED for Context Engineering compliance]

**Research Directory References (REQUIRED):**
```
- research/rust/security-best-practices.md - Official Rust security guidelines and vulnerability management
- research/rust/unsafe-patterns.md - Safe unsafe block patterns and documentation standards
- research/rust/performance-optimization.md - Production performance patterns and benchmarking
- research/google-cloud/cloud-run-production.md - Cloud Run production deployment best practices
- research/security/vulnerability-management.md - Dependency auditing and upgrade strategies
```

**Current Project Context (CRITICAL):**
```
- validation-results/analysis-engine-prod-readiness/ - Current validation evidence and findings
- TASK.md - Current security vulnerabilities and research coordination priorities
- Analysis-engine has critical security issues (idna, protobuf) blocking production deployment
```

**Official Documentation (Fresh Research Required):**
```
- https://doc.rust-lang.org/nomicon/safe-unsafe-meaning.html - Unsafe Rust documentation
- https://rustsec.org/ - Rust security advisory database
- https://docs.rs/tokio/latest/tokio/runtime/ - Async runtime optimization
```

## OTHER CONSIDERATIONS:
[Context Engineering requirements and current project critical factors]

**CRITICAL SECURITY CONTEXT:**
```
- Analysis-engine has critical security vulnerabilities that MUST be addressed first
- idna 0.4.0 → >=1.0.0 (critical security vulnerability)
- protobuf 2.28.0 → >=3.7.2 (security vulnerability with data exposure risk)
- 22 undocumented unsafe blocks require SAFETY comments per official Rust documentation
- All implementations must be research-backed with evidence from /research/ directory
```

**Context Engineering Requirements:**
```
- Must reference official documentation from research/ directory for all decisions
- Implement validation loops for self-correction and quality assurance
- Use evidence-based development with systematic validation checkpoints
- Support multi-agent coordination with complete project context
- Build upon existing validation-results/analysis-engine-prod-readiness/ framework
```

**Production Requirements:**
```
- Security fixes are critical path blockers for all other work
- Performance requirement: <5 minutes for 1M LOC repositories (after security fixes)
- Memory safety: All unsafe blocks must be documented with SAFETY comments
- Build quality: cargo clippy must pass with -D warnings flag
- Monitoring: Comprehensive Prometheus metrics and structured logging required
```

---

## How to Use This Template

1. **Copy this file** to create your feature request (e.g., `FEATURE_NAME.md`)
2. **Fill in each section** with specific details about your feature
3. **Reference existing examples** that should be followed
4. **Include relevant documentation** from the research directory
5. **Mention important gotchas** and requirements
6. **Generate PRP** using `/generate-prp YOUR_FEATURE.md`
7. **Execute PRP** using `/execute-prp PRPs/your-feature.md`

## Context Engineering Integration

This template is designed to work with the Context Engineering workflow:

- **PRP Generation** - The `/generate-prp` command will use this information to create a comprehensive Product Requirements Prompt
- **Research Integration** - References to the research directory ensure fresh, official documentation is used
- **Example Patterns** - References to examples ensure consistent implementation patterns
- **Validation Loops** - Considerations help identify validation requirements

## Best Practices for Feature Requests

1. **Be Specific** - Provide detailed requirements and success criteria
2. **Reference Examples** - Point to relevant patterns in the examples directory
3. **Include Documentation** - Reference official documentation from research directory
4. **Mention Gotchas** - Include common pitfalls and important considerations
5. **Define Performance** - Specify performance and resource requirements
6. **Security Considerations** - Include security requirements and validation needs

## Example Feature Request

Here's an example of a well-structured feature request:

```markdown
## FEATURE:
Implement a code complexity analyzer that calculates cyclomatic complexity for parsed AST nodes, stores metrics in Spanner, and provides API endpoints for querying complexity data with Redis caching.

## EXAMPLES:
- examples/analysis-engine/ast_parser.rs - AST traversal patterns
- examples/analysis-engine/api_handler.rs - API endpoint structure
- examples/analysis-engine/service_pattern.rs - Service organization
- examples/database/spanner_queries.rs - Database query patterns

## DOCUMENTATION:
- research/tree-sitter/core.md - AST node traversal methods
- research/google-cloud/spanner.md - Database schema and queries
- research/rust/tokio.md - Async processing patterns
- https://en.wikipedia.org/wiki/Cyclomatic_complexity - Algorithm reference

## OTHER CONSIDERATIONS:
- Must handle all 18+ supported languages consistently
- Implement caching with 1-hour TTL for complexity results
- Add Prometheus metrics for complexity calculation performance
- Ensure graceful handling of malformed AST nodes
- Performance target: <1 second for complexity calculation per file
- API should support batch complexity queries for multiple files
- Include proper error handling for unsupported language constructs
```

This example shows how to provide comprehensive context while being specific about requirements and referencing existing patterns.
