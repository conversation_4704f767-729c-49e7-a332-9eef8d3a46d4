# INITIAL.md - Feature Request Template

This file serves as a template for creating feature requests that will be processed through the Context Engineering workflow. Copy this template and modify it for your specific feature requirements.

## FEATURE:
[Describe what you want to build - be specific about functionality and requirements]

**Example:**
```
Build an async repository analysis service that processes Git repositories, extracts AST information using Tree-sitter, performs security analysis, and stores results in Spanner with Redis caching. The service should handle repositories up to 1M LOC within 5 minutes and support 50+ concurrent analyses.
```

## EXAMPLES:
[List any example files in the examples/ folder and explain how they should be used]

**Example:**
```
- examples/analysis-engine/service_pattern.rs - Follow this pattern for service structure
- examples/analysis-engine/api_handler.rs - Use this for API endpoint implementation
- examples/analysis-engine/error_handling.rs - Apply these error handling patterns
- examples/testing/integration_test.rs - Use this testing approach
```

## DOCUMENTATION:
[Include links to relevant documentation, APIs, or research directory resources]

**Example:**
```
- research/rust/tokio.md - Async runtime patterns and best practices
- research/google-cloud/spanner.md - Database integration and query patterns
- research/tree-sitter/core.md - AST parsing implementation details
- https://docs.rs/axum/latest/axum/ - Web framework documentation
```

## OTHER CONSIDERATIONS:
[Mention any gotchas, specific requirements, or things AI assistants commonly miss]

**Example:**
```
- Must handle Tree-sitter unsafe blocks properly (see examples/analysis-engine/ast_parser.rs)
- Implement graceful Redis degradation when cache is unavailable
- Enforce resource limits: 10MB max file size, 30s parse timeout
- Follow existing authentication middleware patterns
- Ensure proper error logging with structured tracing
- Performance requirement: <5 minutes for 1M LOC repositories
- Security requirement: All inputs must be validated and sanitized
```

---

## How to Use This Template

1. **Copy this file** to create your feature request (e.g., `FEATURE_NAME.md`)
2. **Fill in each section** with specific details about your feature
3. **Reference existing examples** that should be followed
4. **Include relevant documentation** from the research directory
5. **Mention important gotchas** and requirements
6. **Generate PRP** using `/generate-prp YOUR_FEATURE.md`
7. **Execute PRP** using `/execute-prp PRPs/your-feature.md`

## Context Engineering Integration

This template is designed to work with the Context Engineering workflow:

- **PRP Generation** - The `/generate-prp` command will use this information to create a comprehensive Product Requirements Prompt
- **Research Integration** - References to the research directory ensure fresh, official documentation is used
- **Example Patterns** - References to examples ensure consistent implementation patterns
- **Validation Loops** - Considerations help identify validation requirements

## Best Practices for Feature Requests

1. **Be Specific** - Provide detailed requirements and success criteria
2. **Reference Examples** - Point to relevant patterns in the examples directory
3. **Include Documentation** - Reference official documentation from research directory
4. **Mention Gotchas** - Include common pitfalls and important considerations
5. **Define Performance** - Specify performance and resource requirements
6. **Security Considerations** - Include security requirements and validation needs

## Example Feature Request

Here's an example of a well-structured feature request:

```markdown
## FEATURE:
Implement a code complexity analyzer that calculates cyclomatic complexity for parsed AST nodes, stores metrics in Spanner, and provides API endpoints for querying complexity data with Redis caching.

## EXAMPLES:
- examples/analysis-engine/ast_parser.rs - AST traversal patterns
- examples/analysis-engine/api_handler.rs - API endpoint structure
- examples/analysis-engine/service_pattern.rs - Service organization
- examples/database/spanner_queries.rs - Database query patterns

## DOCUMENTATION:
- research/tree-sitter/core.md - AST node traversal methods
- research/google-cloud/spanner.md - Database schema and queries
- research/rust/tokio.md - Async processing patterns
- https://en.wikipedia.org/wiki/Cyclomatic_complexity - Algorithm reference

## OTHER CONSIDERATIONS:
- Must handle all 18+ supported languages consistently
- Implement caching with 1-hour TTL for complexity results
- Add Prometheus metrics for complexity calculation performance
- Ensure graceful handling of malformed AST nodes
- Performance target: <1 second for complexity calculation per file
- API should support batch complexity queries for multiple files
- Include proper error handling for unsupported language constructs
```

This example shows how to provide comprehensive context while being specific about requirements and referencing existing patterns.
