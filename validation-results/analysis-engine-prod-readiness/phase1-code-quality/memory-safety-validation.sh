#!/bin/bash
# Phase 1.2: Memory Safety Validation Script
# Validates memory safety and unsafe block usage

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
EVIDENCE_DIR="${SCRIPT_DIR}/../evidence/phase1-memory-safety"
SERVICE_DIR="${SCRIPT_DIR}/../../../services/analysis-engine"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Create evidence directory
mkdir -p "${EVIDENCE_DIR}"

# Start validation report
REPORT_FILE="${EVIDENCE_DIR}/memory-safety-${TIMESTAMP}.txt"
echo "=== Analysis Engine Memory Safety Validation ===" > "${REPORT_FILE}"
echo "Timestamp: $(date)" >> "${REPORT_FILE}"
echo "" >> "${REPORT_FILE}"

# Function to log results
log_result() {
    local test_name=$1
    local status=$2
    local details=$3
    echo "[${status}] ${test_name}" | tee -a "${REPORT_FILE}"
    echo "  Details: ${details}" | tee -a "${REPORT_FILE}"
    echo "" | tee -a "${REPORT_FILE}"
}

cd "${SERVICE_DIR}"

echo "Starting memory safety validation..." | tee -a "${REPORT_FILE}"

# Test 1: Count unsafe blocks
echo "=== Test 1: Analyzing unsafe code blocks ===" | tee -a "${REPORT_FILE}"
UNSAFE_BLOCKS_FILE="${EVIDENCE_DIR}/unsafe-blocks-${TIMESTAMP}.txt"

# Find all unsafe blocks with context
if command -v rg &> /dev/null; then
    rg -t rust 'unsafe\s*\{' src/ -A 5 -B 2 > "${UNSAFE_BLOCKS_FILE}" 2>/dev/null || true
    UNSAFE_COUNT=$(rg -t rust 'unsafe\s*\{' src/ -c | wc -l)
else
    find src/ -name "*.rs" -exec grep -B 2 -A 5 'unsafe\s*{' {} \; > "${UNSAFE_BLOCKS_FILE}" 2>/dev/null || true
    UNSAFE_COUNT=$(find src/ -name "*.rs" -exec grep -l 'unsafe\s*{' {} \; | wc -l)
fi

UNSAFE_COUNT=$(echo "$UNSAFE_COUNT" | tr -d '[:space:]')

if [ "$UNSAFE_COUNT" -eq 0 ]; then
    log_result "Unsafe block count" "INFO" "No unsafe blocks found (excellent!)"
else
    log_result "Unsafe block count" "INFO" "Found unsafe blocks in ${UNSAFE_COUNT} files"
    echo "Unsafe blocks saved to: ${UNSAFE_BLOCKS_FILE}" >> "${REPORT_FILE}"
fi

# Test 2: Verify unsafe block documentation
echo "=== Test 2: Checking unsafe block documentation ===" | tee -a "${REPORT_FILE}"
UNDOCUMENTED_COUNT=0

if [ -s "${UNSAFE_BLOCKS_FILE}" ]; then
    # Check each file with unsafe blocks
    if command -v rg &> /dev/null; then
        FILES_WITH_UNSAFE=$(rg -l 'unsafe\s*\{' src/)
    else
        FILES_WITH_UNSAFE=$(find src/ -name "*.rs" -exec grep -l 'unsafe\s*{' {} \;)
    fi
    
    for file in $FILES_WITH_UNSAFE; do
        echo "Checking $file..." >> "${REPORT_FILE}"
        
        # Check if SAFETY comment exists before unsafe block
        if command -v rg &> /dev/null; then
            UNSAFE_LINES=$(rg -n 'unsafe\s*\{' "$file" | cut -d: -f1)
        else
            UNSAFE_LINES=$(grep -n 'unsafe\s*{' "$file" | cut -d: -f1)
        fi
        
        for line in $UNSAFE_LINES; do
            # Check previous 3 lines for SAFETY comment
            START_LINE=$((line - 3))
            if [ $START_LINE -lt 1 ]; then START_LINE=1; fi
            
            if ! sed -n "${START_LINE},${line}p" "$file" | grep -Ei '// SAFETY:|// Safety:' > /dev/null; then
                echo "  WARNING: Undocumented unsafe block at $file:$line" | tee -a "${REPORT_FILE}"
                UNDOCUMENTED_COUNT=$((UNDOCUMENTED_COUNT + 1))
            fi
        done
    done
fi

if [ "$UNDOCUMENTED_COUNT" -eq 0 ]; then
    log_result "Unsafe documentation" "PASS" "All unsafe blocks are documented"
else
    log_result "Unsafe documentation" "FAIL" "Found ${UNDOCUMENTED_COUNT} undocumented unsafe blocks"
fi

# Test 3: Check for common memory safety issues
echo "=== Test 3: Checking for common memory safety patterns ===" | tee -a "${REPORT_FILE}"

# Check for raw pointer usage
if command -v rg &> /dev/null; then
    RAW_PTR_COUNT=$(rg -t rust '\*mut\s+|\*const\s+' src/ -c | wc -l)
else
    RAW_PTR_COUNT=$(find src/ -name "*.rs" -exec grep -l '\*mut\s\+\|\*const\s\+' {} \; | wc -l)
fi
RAW_PTR_COUNT=$(echo "$RAW_PTR_COUNT" | tr -d '[:space:]')

# Check for transmute usage
if command -v rg &> /dev/null; then
    TRANSMUTE_COUNT=$(rg -t rust 'transmute|transmute_copy' src/ -c | wc -l)
else
    TRANSMUTE_COUNT=$(find src/ -name "*.rs" -exec grep -l 'transmute\|transmute_copy' {} \; | wc -l)
fi
TRANSMUTE_COUNT=$(echo "$TRANSMUTE_COUNT" | tr -d '[:space:]')

# Check for uninitialized memory
if command -v rg &> /dev/null; then
    UNINIT_COUNT=$(rg -t rust 'MaybeUninit|uninitialized' src/ -c | wc -l)
else
    UNINIT_COUNT=$(find src/ -name "*.rs" -exec grep -l 'MaybeUninit\|uninitialized' {} \; | wc -l)
fi
UNINIT_COUNT=$(echo "$UNINIT_COUNT" | tr -d '[:space:]')

log_result "Memory safety patterns" "INFO" "Raw pointers: ${RAW_PTR_COUNT} files, Transmute: ${TRANSMUTE_COUNT} files, Uninitialized: ${UNINIT_COUNT} files"

# Test 4: Check for proper Drop implementations
echo "=== Test 4: Checking Drop implementations ===" | tee -a "${REPORT_FILE}"
if command -v rg &> /dev/null; then
    DROP_IMPL_COUNT=$(rg -t rust 'impl\s+Drop' src/ -c | wc -l)
else
    DROP_IMPL_COUNT=$(find src/ -name "*.rs" -exec grep -l 'impl\s\+Drop' {} \; | wc -l)
fi
DROP_IMPL_COUNT=$(echo "$DROP_IMPL_COUNT" | tr -d '[:space:]')

if [ "$DROP_IMPL_COUNT" -gt 0 ]; then
    log_result "Drop implementations" "INFO" "Found ${DROP_IMPL_COUNT} custom Drop implementations"
    
    # Save Drop implementations for review
    if command -v rg &> /dev/null; then
        rg -t rust 'impl\s+Drop' src/ -A 10 > "${EVIDENCE_DIR}/drop-implementations.txt"
    else
        find src/ -name "*.rs" -exec grep -A 10 'impl\s\+Drop' {} \; > "${EVIDENCE_DIR}/drop-implementations.txt"
    fi
else
    log_result "Drop implementations" "INFO" "No custom Drop implementations found"
fi

# Test 5: Check for proper Send/Sync implementations
echo "=== Test 5: Checking Send/Sync implementations ===" | tee -a "${REPORT_FILE}"
if command -v rg &> /dev/null; then
    SEND_SYNC_COUNT=$(rg -t rust 'unsafe\s+impl.*(?:Send|Sync)' src/ -c | wc -l)
else
    SEND_SYNC_COUNT=$(find src/ -name "*.rs" -exec grep -l 'unsafe\s\+impl.*\(Send\|Sync\)' {} \; | wc -l)
fi
SEND_SYNC_COUNT=$(echo "$SEND_SYNC_COUNT" | tr -d '[:space:]')

if [ "$SEND_SYNC_COUNT" -gt 0 ]; then
    log_result "Send/Sync implementations" "WARN" "Found ${SEND_SYNC_COUNT} unsafe Send/Sync implementations - review required"
    
    # Save Send/Sync implementations for review
    if command -v rg &> /dev/null; then
        rg -t rust 'unsafe\s+impl.*(?:Send|Sync)' src/ -B 2 -A 10 > "${EVIDENCE_DIR}/send-sync-implementations.txt"
    else
        find src/ -name "*.rs" -exec grep -B 2 -A 10 'unsafe\s\+impl.*\(Send\|Sync\)' {} \; > "${EVIDENCE_DIR}/send-sync-implementations.txt"
    fi
else
    log_result "Send/Sync implementations" "PASS" "No unsafe Send/Sync implementations found"
fi

# Test 6: Tree-sitter specific safety checks
echo "=== Test 6: Tree-sitter integration safety ===" | tee -a "${REPORT_FILE}"
TREE_SITTER_ISSUES=0

# Check for proper language cleanup
if command -v rg &> /dev/null; then
    LANGUAGE_NEW=$(rg -t rust 'tree_sitter_\w+\(\)' src/ -c | wc -l)
    LANGUAGE_DELETE=$(rg -t rust 'ts_language_delete|language.*drop' src/ -c | wc -l)
else
    LANGUAGE_NEW=$(find src/ -name "*.rs" -exec grep -l 'tree_sitter_\w\+()' {} \; | wc -l)
    LANGUAGE_DELETE=$(find src/ -name "*.rs" -exec grep -l 'ts_language_delete\|language.*drop' {} \; | wc -l)
fi

LANGUAGE_NEW=$(echo "$LANGUAGE_NEW" | tr -d '[:space:]')
LANGUAGE_DELETE=$(echo "$LANGUAGE_DELETE" | tr -d '[:space:]')

if [ "$LANGUAGE_NEW" -gt 0 ] && [ "$LANGUAGE_DELETE" -eq 0 ]; then
    log_result "Tree-sitter cleanup" "WARN" "Found language creation but no cleanup code"
    TREE_SITTER_ISSUES=$((TREE_SITTER_ISSUES + 1))
else
    log_result "Tree-sitter cleanup" "INFO" "Tree-sitter language management appears safe"
fi

# Test 7: Run Miri if available (memory safety checker)
echo "=== Test 7: Running Miri memory safety checker ===" | tee -a "${REPORT_FILE}"
if command -v cargo +nightly &> /dev/null && cargo +nightly miri --version &> /dev/null 2>&1; then
    echo "Running Miri on critical modules..." | tee -a "${REPORT_FILE}"
    
    # Run miri on specific test modules if available
    if cargo +nightly miri test --lib parser:: --no-fail-fast 2>&1 | tee "${EVIDENCE_DIR}/miri-output.txt"; then
        log_result "Miri safety check" "PASS" "Miri found no memory safety issues"
    else
        log_result "Miri safety check" "FAIL" "Miri detected potential memory safety issues"
    fi
else
    log_result "Miri safety check" "SKIP" "Miri not available - install with: rustup +nightly component add miri"
fi

# Generate summary
echo "" | tee -a "${REPORT_FILE}"
echo "=== MEMORY SAFETY SUMMARY ===" | tee -a "${REPORT_FILE}"
echo "Report saved to: ${REPORT_FILE}" | tee -a "${REPORT_FILE}"

# Count results
PASS_COUNT=$(grep -c "\[PASS\]" "${REPORT_FILE}" || echo "0")
FAIL_COUNT=$(grep -c "\[FAIL\]" "${REPORT_FILE}" || echo "0")
WARN_COUNT=$(grep -c "\[WARN\]" "${REPORT_FILE}" || echo "0")
INFO_COUNT=$(grep -c "\[INFO\]" "${REPORT_FILE}" || echo "0")

echo "Results: ${PASS_COUNT} PASS, ${FAIL_COUNT} FAIL, ${WARN_COUNT} WARN, ${INFO_COUNT} INFO" | tee -a "${REPORT_FILE}"

# Exit with error if any critical failures
if [ "$FAIL_COUNT" -gt 0 ]; then
    echo "Memory safety validation failed with ${FAIL_COUNT} critical issues"
    exit 1
else
    echo "Memory safety validation completed successfully"
    exit 0
fi