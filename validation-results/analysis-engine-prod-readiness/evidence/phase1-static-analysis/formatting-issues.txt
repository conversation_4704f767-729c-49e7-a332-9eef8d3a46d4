Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:1:
 //! Benchmarks for analysis engine
 
[31m-use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
(B[m use analysis_engine::parser::TreeSitterParser;
[31m-use analysis_engine::services::pattern_detector::PatternDetector;
(B[m use analysis_engine::services::language_detector::LanguageDetector;
[32m+use analysis_engine::services::pattern_detector::PatternDetector;
(B[m[32m+use criterion::{black_box, criterion_group, criterion_main, BenchmarkId, Criterion};
(B[m 
[31m-use std::path::PathBuf;
(B[m use std::fs;
[32m+use std::path::PathBuf;
(B[m use tempfile::TempDir;
 
 // Sample code snippets of various sizes
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:86:
 
 fn generate_large_code(lines: usize) -> String {
     let mut code = String::new();
[31m-    
(B[m[32m+
(B[m     for i in 0..lines / 10 {
         code.push_str(&format!(
             r#"
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:105:
             i = i
         ));
     }
[31m-    
(B[m[32m+
(B[m     code
 }
 
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:118:
 
 fn bench_ast_parsing(c: &mut Criterion) {
     let parser = TreeSitterParser::new().unwrap();
[31m-    
(B[m[32m+
(B[m     let mut group = c.benchmark_group("ast_parsing");
[31m-    
(B[m[32m+
(B[m     // Small file benchmark
     let (_temp_dir, small_file) = setup_test_file(SMALL_RUST_CODE);
     group.bench_with_input(
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:135:
             });
         },
     );
[31m-    
(B[m[32m+
(B[m     // Medium file benchmark
     let (_temp_dir2, medium_file) = setup_test_file(MEDIUM_RUST_CODE);
     group.bench_with_input(
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:150:
             });
         },
     );
[31m-    
(B[m[32m+
(B[m     // Large file benchmark (10K lines)
     let large_code = generate_large_code(10_000);
     let (_temp_dir3, large_file) = setup_test_file(&large_code);
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:166:
             });
         },
     );
[31m-    
(B[m[32m+
(B[m     group.finish();
 }
 
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:173:
 fn bench_pattern_detection(c: &mut Criterion) {
     let detector = PatternDetector::new();
     let parser = TreeSitterParser::new().unwrap();
[31m-    
(B[m[32m+
(B[m     let mut group = c.benchmark_group("pattern_detection");
[31m-    
(B[m[32m+
(B[m     // Parse medium file once for pattern detection
     let (_temp_dir, medium_file) = setup_test_file(MEDIUM_RUST_CODE);
     let runtime = tokio::runtime::Runtime::new().unwrap();
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:182:
     let file_analysis = runtime.block_on(parser.parse_file(&medium_file)).unwrap();
[31m-    
(B[m[32m+
(B[m     group.bench_function("detect_patterns_medium", |b| {
         b.iter(|| {
             let patterns = detector.detect_patterns(&file_analysis);
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:187:
             black_box(patterns)
         });
     });
[31m-    
(B[m[32m+
(B[m     // Large file pattern detection
     let large_code = generate_large_code(5_000);
     let (_temp_dir2, large_file) = setup_test_file(&large_code);
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:194:
     let large_analysis = runtime.block_on(parser.parse_file(&large_file)).unwrap();
[31m-    
(B[m[32m+
(B[m     group.bench_function("detect_patterns_large", |b| {
         b.iter(|| {
             let patterns = detector.detect_patterns(&large_analysis);
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:199:
             black_box(patterns)
         });
     });
[31m-    
(B[m[32m+
(B[m     group.finish();
 }
 
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:206:
 fn bench_language_detection(c: &mut Criterion) {
     let detector = LanguageDetector::new();
[31m-    
(B[m[32m+
(B[m     let mut group = c.benchmark_group("language_detection");
[31m-    
(B[m[32m+
(B[m     // Create a directory with multiple file types
     let temp_dir = TempDir::new().unwrap();
     fs::write(temp_dir.path().join("main.rs"), SMALL_RUST_CODE).unwrap();
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:215:
     fs::write(temp_dir.path().join("style.css"), "body { margin: 0; }").unwrap();
     fs::write(temp_dir.path().join("index.html"), "<html></html>").unwrap();
     fs::write(temp_dir.path().join("script.py"), "print('hello')").unwrap();
[31m-    
(B[m[32m+
(B[m     group.bench_function("detect_languages", |b| {
         b.iter(|| {
             let result = detector.detect_languages_with_stats(temp_dir.path());
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:222:
             black_box(result)
         });
     });
[31m-    
(B[m[32m+
(B[m     group.bench_function("detect_languages_with_stats", |b| {
         b.iter(|| {
             let result = detector.detect_languages_with_stats(temp_dir.path());
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:229:
             black_box(result)
         });
     });
[31m-    
(B[m[32m+
(B[m     group.finish();
 }
 
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:236:
 fn bench_concurrent_parsing(c: &mut Criterion) {
     use rayon::prelude::*;
[31m-    
(B[m[32m+
(B[m     let parser = TreeSitterParser::new().unwrap();
[31m-    
(B[m[32m+
(B[m     let mut group = c.benchmark_group("concurrent_parsing");
     group.sample_size(10); // Reduce sample size for longer benchmarks
[31m-    
(B[m[32m+
(B[m     // Create multiple test files
     let temp_dir = TempDir::new().unwrap();
     let files: Vec<PathBuf> = (0..50)
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:257:
             file_path
         })
         .collect();
[31m-    
(B[m[32m+
(B[m     group.bench_function("parse_50_files_sequential", |b| {
         b.iter(|| {
             let runtime = tokio::runtime::Runtime::new().unwrap();
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:268:
             black_box(results)
         });
     });
[31m-    
(B[m[32m+
(B[m     group.bench_function("parse_50_files_parallel", |b| {
         b.iter(|| {
             let results: Vec<_> = files
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:281:
             black_box(results)
         });
     });
[31m-    
(B[m[32m+
(B[m     group.finish();
 }
 
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:288:
 fn bench_memory_usage(c: &mut Criterion) {
     let parser = TreeSitterParser::new().unwrap();
[31m-    
(B[m[32m+
(B[m     let mut group = c.benchmark_group("memory_usage");
     group.sample_size(10);
[31m-    
(B[m[32m+
(B[m     // Benchmark memory usage for different file sizes
     let sizes = vec![1_000, 10_000, 50_000];
[31m-    
(B[m[32m+
(B[m     for size in sizes {
         let code = generate_large_code(size);
         let (_temp_dir, file_path) = setup_test_file(&code);
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:300:
[31m-        
(B[m[32m+
(B[m         group.bench_with_input(
             BenchmarkId::new("parse_lines", size),
             &file_path,
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:313:
             },
         );
     }
[31m-    
(B[m[32m+
(B[m     group.finish();
 }
 
Diff in /Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/benches/analysis_bench.rs:326:
     bench_memory_usage
 );
 criterion_main!(benches);
[32m+
(B[m