=== Analysis Engine Static Analysis Validation ===
Timestamp: Tue Jul 15 00:26:24 EEST 2025

Starting static analysis validation...
=== Test 1: Checking for unwrap()/expect() usage ===
[PASS] unwrap/expect check
  Details: No unwrap() or expect() found in production code

=== Test 2: Verifying Result<T, E> usage ===
[INFO] Result<T, E> usage
  Details: 194/1148 functions (16%) return Result

=== Test 3: Running clippy with strict settings ===
[FAIL] Clippy strict mode
  Details: Clippy warnings found - see /Users/<USER>/Documents/GitHub/episteme/validation-results/analysis-engine-prod-readiness/phase1-code-quality/../evidence/phase1-static-analysis/clippy-output-20250715_002624.txt

=== Test 4: Checking for incomplete implementations ===
[WARN] TODO/FIXME check
  Details: Found 12 files with TODO/FIXME items

=== Test 5: Checking code formatting ===
[FAIL] Code formatting
  Details: Code needs formatting

=== Test 6: Analyzing function complexity ===
[PASS] Function complexity
  Details: No overly complex functions found

=== Test 7: Checking error handling patterns ===
[INFO] Error handling
  Details: Using anyhow in 43 files, thiserror in 1 files

=== Test 8: Checking test coverage ===
[PASS] Test coverage
  Details: Found 78 test files


=== STATIC ANALYSIS SUMMARY ===
Report saved to: /Users/<USER>/Documents/GitHub/episteme/validation-results/analysis-engine-prod-readiness/phase1-code-quality/../evidence/phase1-static-analysis/static-analysis-20250715_002624.txt
Results: 3 PASS, 2 FAIL, 1 WARN
