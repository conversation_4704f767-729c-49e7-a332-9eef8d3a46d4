    //     let lang_fn = tree_sitter_rust::LANGUAGE;
    //     // LANGUAGE is likely a LanguageFn type
    //     let _lang = unsafe { lang_fn() };
    //     println!("✓ rust has LANGUAGE constant (LanguageFn type)");
    // }
    
    // Check newer crates pattern
    println!("\nChecking pattern for newer crates:");
--
    println!("- Older crates (< 0.20): export language() function");
    println!("- Newer crates (>= 0.20): export LANGUAGE as LanguageFn");
    println!("- LanguageFn needs unsafe {{ LANGUAGE() }} to get Language");
}
    
    // These have newer versions and should use LANGUAGE constant
    println!("✓ rust (v0.24.0) - unsafe {{ tree_sitter_rust::LANGUAGE() }}");
    println!("✓ python (v0.23.6) - unsafe {{ tree_sitter_python::LANGUAGE() }}");
    println!("✓ javascript (v0.23.1) - unsafe {{ tree_sitter_javascript::LANGUAGE() }}");
    println!("✓ typescript (v0.23.2) - unsafe {{ tree_sitter_typescript::LANGUAGE_TYPESCRIPT() }}");
    println!("✓ go (v0.23.4) - unsafe {{ tree_sitter_go::LANGUAGE() }}");
    println!("✓ java (v0.23.5) - unsafe {{ tree_sitter_java::LANGUAGE() }}");
    println!("✓ c (v0.24.1) - unsafe {{ tree_sitter_c::LANGUAGE() }}");
    println!("✓ cpp (v0.23.4) - unsafe {{ tree_sitter_cpp::LANGUAGE() }}");
    println!("✓ ruby (v0.23.1) - unsafe {{ tree_sitter_ruby::LANGUAGE() }}");
    println!("✓ bash (v0.23.3) - unsafe {{ tree_sitter_bash::LANGUAGE() }}");
    println!("✓ objc (v3.0.2) - unsafe {{ tree_sitter_objc::LANGUAGE() }}");
    println!("✓ r (v1.2.0) - unsafe {{ tree_sitter_r::LANGUAGE() }}");
    println!("✓ julia (v0.23.1) - unsafe {{ tree_sitter_julia::LANGUAGE() }}");
    println!("✓ scala (v0.23.4) - unsafe {{ tree_sitter_scala::LANGUAGE() }}");
    println!("✓ zig (v1.1.2) - unsafe {{ tree_sitter_zig::LANGUAGE() }}");
    println!("✓ php (v0.23.11) - unsafe {{ tree_sitter_php::LANGUAGE() }}");
    println!("✓ ocaml (v0.24.2) - unsafe {{ tree_sitter_ocaml::LANGUAGE_OCAML() }}");
}
    // For newer crates, the correct pattern is:
    // let lang_fn: LanguageFn = tree_sitter_rust::LANGUAGE;
    // let language: Language = unsafe { lang_fn() };
    
    // Or more concisely:
    // let language = unsafe { tree_sitter_rust::LANGUAGE() };
    
    println!("Based on the errors, LanguageFn cannot be dereferenced.");
    println!("It should be called directly as a function.");
}
    // 3. Guaranteed to return valid Language structs
    // 4. Part of the tree-sitter C library contract
    let language = unsafe {
        match name {
            "erlang" => tree_sitter_erlang(),
            "scala" => tree_sitter_scala(),
            "rust" => tree_sitter_rust(),
            "ocaml" => tree_sitter_ocaml(),
