name: "Episteme PRP Template v1 - Context Engineering for Analysis Engine"
description: |
  ## Purpose
  Template optimized for AI agents to implement features in the Episteme analysis engine
  with sufficient context and self-validation capabilities to achieve working code through
  iterative refinement.

  ## Core Principles
  1. **Context is King**: Include ALL necessary documentation, examples, and caveats
  2. **Validation Loops**: Provide executable tests/lints the AI can run and fix
  3. **Information Dense**: Use keywords and patterns from the Episteme codebase
  4. **Progressive Success**: Start simple, validate, then enhance
  5. **Evidence-Based**: Follow Context Engineering standards with official documentation

---

## Goal
[What needs to be built - be specific about the end state and desired functionality]

## Why - Business Value
- [Business value and user impact]
- [Integration with existing Episteme features]
- [Problems this solves and for whom]
- [Alignment with analysis engine objectives]

## What - Technical Requirements
[User-visible behavior and technical requirements]

### Success Criteria
- [ ] [Specific measurable outcomes]
- [ ] [Performance requirements (e.g., <5min for 1M LOC)]
- [ ] [Security requirements (e.g., input validation)]
- [ ] [Integration requirements (e.g., Spanner, Redis)]

## All Needed Context

### Documentation & References
```yaml
# MUST READ - Include these in your context window
research_docs:
  - file: research/rust/tokio.md
    why: [Async patterns and error handling]
  - file: research/google-cloud/spanner.md
    why: [Database integration patterns]
  - file: research/tree-sitter/core.md
    why: [Parsing implementation details]

examples:
  - file: examples/analysis-engine/service_pattern.rs
    why: [Service structure and organization]
  - file: examples/analysis-engine/api_handler.rs
    why: [API endpoint patterns]
  - file: examples/analysis-engine/error_handling.rs
    why: [Error handling patterns]

official_docs:
  - url: [Official API docs URL]
    section: [Specific sections/methods needed]
    critical: [Key insights that prevent common errors]
```

### Current Codebase Structure
```bash
# Run `tree services/analysis-engine/src -L 3` for overview
services/analysis-engine/src/
├── api/                    # Axum handlers and middleware
├── services/               # Business logic services
├── storage/                # Database and cache layers
├── models/                 # Domain models and schemas
├── metrics/                # Prometheus monitoring
└── main.rs                 # Application entry point
```

### Desired Codebase Changes
```bash
# Files to be added/modified with responsibilities
services/analysis-engine/src/
├── [new_module]/           # [Description of new module]
│   ├── mod.rs             # [Module definition and exports]
│   ├── [component].rs     # [Main component implementation]
│   └── tests.rs           # [Unit tests for module]
└── [existing_file].rs      # [Modifications needed]
```

### Known Gotchas & Library Quirks
```rust
// CRITICAL: Episteme-specific patterns and requirements
// Example: Tree-sitter requires specific unsafe block patterns
// Example: Spanner client needs connection pooling for performance
// Example: Redis graceful degradation when unavailable
// Example: Resource limits enforced (10MB files, 30s timeout)
```

## Implementation Blueprint

### Data Models and Structure
```rust
// Create core data models ensuring type safety and consistency
// Examples:
// - Domain models for new feature
// - Request/response schemas
// - Database entity definitions
// - Validation rules and constraints
```

### Task List - Implementation Order
```yaml
Task 1: "Setup module structure"
  - CREATE src/[module]/mod.rs
  - DEFINE public interface and exports
  - ADD module to main.rs or parent module
  - PATTERN: Follow examples/analysis-engine/service_pattern.rs

Task 2: "Implement core functionality"
  - CREATE src/[module]/[component].rs
  - IMPLEMENT main business logic
  - FOLLOW error handling patterns from examples/
  - INTEGRATE with existing services

Task 3: "Add API endpoints (if needed)"
  - CREATE src/api/[feature].rs
  - IMPLEMENT Axum handlers
  - ADD authentication middleware
  - FOLLOW examples/analysis-engine/api_handler.rs

Task 4: "Database integration"
  - ADD Spanner queries and mutations
  - IMPLEMENT connection pooling
  - ADD Redis caching layer
  - FOLLOW storage/ patterns

Task 5: "Testing implementation"
  - CREATE comprehensive unit tests
  - ADD integration tests
  - IMPLEMENT property-based tests
  - FOLLOW examples/analysis-engine/testing_pattern.rs

Task 6: "Monitoring and metrics"
  - ADD Prometheus metrics
  - IMPLEMENT health checks
  - ADD structured logging
  - FOLLOW metrics/ patterns
```

### Per-Task Implementation Details
```rust
// Task 1: Module Setup
// PATTERN: Always follow existing module organization
pub mod [module] {
    // CRITICAL: Export only necessary public interfaces
    pub use self::[component]::[PublicType];
    
    mod [component];
    
    #[cfg(test)]
    mod tests;
}

// Task 2: Core Implementation
// PATTERN: Use Result<T, E> for all fallible operations
pub async fn [function_name](param: Type) -> Result<ReturnType, Error> {
    // PATTERN: Always validate input first
    let validated = validate_input(param)?;
    
    // PATTERN: Use connection pooling for database operations
    let mut conn = get_spanner_connection().await?;
    
    // CRITICAL: Implement proper error handling
    match operation(&mut conn, validated).await {
        Ok(result) => Ok(result),
        Err(e) => {
            // PATTERN: Log errors with context
            tracing::error!("Operation failed: {}", e);
            Err(e.into())
        }
    }
}
```

### Integration Points
```yaml
DATABASE:
  - spanner_queries: "Add queries to storage/spanner.rs"
  - migrations: "Create migration scripts if schema changes needed"
  - connection_pool: "Use existing pool from storage/mod.rs"

CACHE:
  - redis_integration: "Add caching layer with graceful degradation"
  - cache_keys: "Follow existing key naming conventions"
  - ttl_strategy: "Implement appropriate TTL for data type"

API:
  - routes: "Add to src/api/mod.rs router configuration"
  - middleware: "Use existing auth and rate limiting middleware"
  - documentation: "Update OpenAPI specs if applicable"

MONITORING:
  - metrics: "Add Prometheus metrics to src/metrics/mod.rs"
  - health_checks: "Update health check endpoints"
  - logging: "Use structured logging with tracing crate"
```

## Validation Loop

### Level 1: Syntax & Style
```bash
# Run these FIRST - fix any errors before proceeding
cargo fmt                           # Format code
cargo clippy -- -D warnings        # Lint with warnings as errors
cargo check                         # Type checking

# Expected: No errors. If errors exist, READ and fix before continuing.
```

### Level 2: Unit Tests
```rust
// CREATE comprehensive test suite following examples/analysis-engine/testing_pattern.rs
#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_happy_path() {
        // Test basic functionality works
        let result = function_under_test("valid_input").await;
        assert!(result.is_ok());
    }
    
    #[tokio::test]
    async fn test_validation_error() {
        // Test invalid input handling
        let result = function_under_test("").await;
        assert!(result.is_err());
    }
    
    #[tokio::test]
    async fn test_database_error() {
        // Test database failure handling
        // Use mocking or test database
        // Verify graceful error handling
    }
}
```

```bash
# Run and iterate until passing
cargo test [module_name] -- --nocapture
# If failing: Read error, understand root cause, fix code, re-run
```

### Level 3: Integration Test
```bash
# Start the service in development mode
cargo run --bin analysis-engine

# Test the endpoints/functionality
curl -X POST http://localhost:8001/api/v1/[endpoint] \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token" \
  -d '{"param": "test_value"}'

# Expected: Successful response with expected data structure
# If error: Check logs and fix issues
```

### Level 4: Performance & Resource Validation
```bash
# Validate resource limits and performance
# Test with sample data within limits (10MB files, 30s timeout)
# Monitor memory usage and response times
# Ensure graceful handling of resource exhaustion
```

## Final Validation Checklist
- [ ] All tests pass: `cargo test`
- [ ] No linting errors: `cargo clippy`
- [ ] Code formatted: `cargo fmt --check`
- [ ] Integration test successful: [specific curl command]
- [ ] Error cases handled gracefully
- [ ] Resource limits respected
- [ ] Monitoring metrics implemented
- [ ] Documentation updated if needed
- [ ] Performance requirements met

---

## Anti-Patterns to Avoid
- ❌ Don't use `unwrap()` or `expect()` in production code
- ❌ Don't skip input validation
- ❌ Don't ignore error cases - handle them explicitly
- ❌ Don't create new patterns when existing ones work
- ❌ Don't skip validation loops - iterate until all tests pass
- ❌ Don't hardcode values that should be configurable
- ❌ Don't break existing API contracts
- ❌ Don't ignore resource limits and performance requirements
