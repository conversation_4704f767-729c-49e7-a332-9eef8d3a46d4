# Authorization Implementation

name: "Authorization Implementation"
description: |
  Comprehensive authorization system for CCL platform implementing role-based access control (RBAC) and attribute-based access control (ABAC) with fine-grained permissions.
  
  Core Principles:
  - **Principle of Least Privilege**: Users get minimum required permissions
  - **Defense in Depth**: Multiple authorization layers
  - **Resource-Based Permissions**: Fine-grained access control
  - **Scalable Hierarchies**: Support for complex organizational structures
  - **Audit Trail**: Complete authorization decision logging

## Goal

Implement a flexible authorization system that controls access to resources based on user roles, organization membership, and resource ownership with comprehensive audit logging.

## Why

Authorization is essential for:
- Protecting sensitive data and operations
- Meeting enterprise security requirements
- Supporting multi-tenant architecture
- Enabling fine-grained access control
- Compliance with security standards

This provides:
- Secure resource protection
- Scalable permission management
- Enterprise-ready access control
- Audit and compliance support
- Flexible permission models

## What

### User-Visible Behavior
- Role-based dashboard access
- Resource-specific permissions
- Organization member management
- Permission inheritance
- Access denied messages with clear explanations

### Technical Requirements
- [ ] Role-based access control (RBAC)
- [ ] Resource-based permissions
- [ ] Organization hierarchy support
- [ ] Permission inheritance
- [ ] Dynamic permission evaluation
- [ ] API endpoint protection
- [ ] Field-level access control
- [ ] Audit logging for all decisions

### Success Criteria
- [ ] <10ms authorization decision time
- [ ] Support for 1M+ permission checks/second
- [ ] Zero unauthorized access incidents
- [ ] Complete audit trail
- [ ] Enterprise-ready role management

## All Needed Context

### Documentation & References
- url: https://auth0.com/docs/manage-users/access-control/rbac
  why: RBAC implementation best practices
- url: https://cloud.google.com/iam/docs/understanding-roles
  why: Google Cloud IAM role patterns
- url: https://www.nist.gov/publications/role-based-access-control
  why: NIST RBAC standard
- file: docs/security/README.md
  why: Current security requirements

### Permission Model

```yaml
Authorization Model:
  subjects:
    - users
    - service_accounts
    - api_keys
    - ml_services
    
  objects:
    - repositories
    - analyses
    - patterns
    - conversations
    - organizations
    - ml_models
    - ai_endpoints
    - embeddings
    
  actions:
    - read
    - write
    - delete
    - admin
    - execute
    - train
    - infer
    - embed
    
  contexts:
    - ownership
    - organization_membership
    - resource_sharing
    - time_constraints
    - data_sensitivity
    - model_version

Roles:
  - Viewer: Read-only access
  - Developer: Read/write code
  - ML_Engineer: Train/deploy models
  - AI_Analyst: Run AI analysis
  - Admin: Full repository control
  - Owner: Billing and user management

ML/AI Roles:
  - Model_Trainer: Train and evaluate models
  - Model_Deployer: Deploy models to production
  - AI_Consumer: Use AI services
  - Data_Scientist: Full ML/AI access

Attributes:
  - IP address restrictions
  - Time-based access
  - Repository ownership
  - Organization membership
  - Data sensitivity level
  - Model access level
  - AI quota limits
```

### Known Gotchas & Library Quirks
- **CRITICAL**: Always check permissions at resource level
- **CRITICAL**: Validate organization membership in real-time
- **GOTCHA**: Permission inheritance can create security holes
- **GOTCHA**: Cached permissions can become stale
- **WARNING**: Overly complex permissions hurt performance
- **TIP**: Use permission caching with short TTL
- **TIP**: Implement permission debugging tools

## Implementation Blueprint

### Core Authorization Service

```typescript
// services/authorizationService.ts
export interface Permission {
  id: string;
  resource: string;
  action: string;
  effect: 'allow' | 'deny';
  conditions?: PermissionCondition[];
}

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
  inheritsFrom?: string[];
  scope: 'system' | 'organization' | 'repository';
}

export interface PermissionCondition {
  field: string;
  operator: 'equals' | 'contains' | 'in' | 'not_in' | 'greater_than' | 'less_than';
  value: any;
}

export interface AuthorizationContext {
  user: User;
  resource?: any;
  action: string;
  environment?: {
    ipAddress?: string;
    timeOfDay?: string;
    deviceType?: string;
  };
}

export interface AuthorizationResult {
  allowed: boolean;
  reason?: string;
  appliedRules?: string[];
  debugInfo?: any;
}

export class AuthorizationService {
  constructor(
    private roleRepository: RoleRepository,
    private permissionRepository: PermissionRepository,
    private organizationRepository: OrganizationRepository,
    private resourceRepository: ResourceRepository,
    private auditLogger: AuditLogger,
    private cache: CacheService
  ) {}
  
  async authorize(context: AuthorizationContext, resourceType: string, resourceId?: string): Promise<AuthorizationResult> {
    const startTime = Date.now();
    
    try {
      // Get user's effective permissions
      const userPermissions = await this.getUserEffectivePermissions(context.user.id);
      
      // Find applicable permissions for this resource and action
      const applicablePermissions = userPermissions.filter(permission => 
        this.matchesResourceAndAction(permission, resourceType, context.action)
      );
      
      if (applicablePermissions.length === 0) {
        const result = {
          allowed: false,
          reason: `No permissions found for ${context.action} on ${resourceType}`,
          appliedRules: []
        };
        
        await this.logAuthorizationDecision(context, result, Date.now() - startTime);
        return result;
      }
      
      // Load resource if needed for condition evaluation
      let resource = context.resource;
      if (!resource && resourceId) {
        resource = await this.loadResource(resourceType, resourceId);
      }
      
      // Evaluate permissions with conditions
      const evaluationResults = await Promise.all(
        applicablePermissions.map(permission => 
          this.evaluatePermission(permission, context, resource)
        )
      );
      
      // Apply permission precedence (deny overrides allow)
      const hasExplicitDeny = evaluationResults.some(result => 
        result.matched && result.permission.effect === 'deny'
      );
      
      const hasExplicitAllow = evaluationResults.some(result => 
        result.matched && result.permission.effect === 'allow'
      );
      
      const allowed = hasExplicitAllow && !hasExplicitDeny;
      const appliedRules = evaluationResults
        .filter(result => result.matched)
        .map(result => result.permission.id);
      
      const result: AuthorizationResult = {
        allowed,
        reason: allowed 
          ? `Authorized by rules: ${appliedRules.join(', ')}`
          : hasExplicitDeny 
            ? 'Access explicitly denied'
            : 'No matching allow rules',
        appliedRules
      };
      
      await this.logAuthorizationDecision(context, result, Date.now() - startTime);
      return result;
      
    } catch (error) {
      const result = {
        allowed: false,
        reason: 'Authorization system error',
        appliedRules: []
      };
      
      await this.logAuthorizationDecision(context, result, Date.now() - startTime, error);
      return result;
    }
  }
  
  async checkRepositoryAccess(
    userId: string, 
    repositoryId: string, 
    action: string
  ): Promise<AuthorizationResult> {
    const repository = await this.resourceRepository.getRepository(repositoryId);
    if (!repository) {
      return {
        allowed: false,
        reason: 'Repository not found'
      };
    }
    
    const user = await this.getUserById(userId);
    const context: AuthorizationContext = {
      user,
      resource: repository,
      action
    };
    
    return this.authorize(context, 'repository', repositoryId);
  }
  
  async checkOrganizationAccess(
    userId: string, 
    organizationId: string, 
    action: string
  ): Promise<AuthorizationResult> {
    const organization = await this.organizationRepository.findById(organizationId);
    if (!organization) {
      return {
        allowed: false,
        reason: 'Organization not found'
      };
    }
    
    const user = await this.getUserById(userId);
    const context: AuthorizationContext = {
      user,
      resource: organization,
      action
    };
    
    return this.authorize(context, 'organization', organizationId);
  }
  
  private async getUserEffectivePermissions(userId: string): Promise<Permission[]> {
    const cacheKey = `user_permissions:${userId}`;
    
    // Try cache first
    const cached = await this.cache.get<Permission[]>(cacheKey);
    if (cached) {
      return cached;
    }
    
    const permissions: Permission[] = [];
    
    // Get user's direct roles
    const userRoles = await this.roleRepository.getUserRoles(userId);
    
    // Get organization roles
    const organizationMemberships = await this.organizationRepository.getUserMemberships(userId);
    for (const membership of organizationMemberships) {
      const orgRoles = await this.roleRepository.getOrganizationRoles(membership.organizationId, userId);
      userRoles.push(...orgRoles);
    }
    
    // Resolve role inheritance and collect permissions
    const resolvedRoles = await this.resolveRoleInheritance(userRoles);
    
    for (const role of resolvedRoles) {
      permissions.push(...role.permissions);
    }
    
    // Remove duplicate permissions
    const uniquePermissions = this.deduplicatePermissions(permissions);
    
    // Cache for 5 minutes
    await this.cache.set(cacheKey, uniquePermissions, 300);
    
    return uniquePermissions;
  }
  
  private async resolveRoleInheritance(roles: Role[]): Promise<Role[]> {
    const resolvedRoles: Role[] = [];
    const processed = new Set<string>();
    
    const processRole = async (role: Role): Promise<void> => {
      if (processed.has(role.id)) {
        return;
      }
      
      processed.add(role.id);
      
      // Process inherited roles first
      if (role.inheritsFrom) {
        for (const parentRoleId of role.inheritsFrom) {
          const parentRole = await this.roleRepository.findById(parentRoleId);
          if (parentRole) {
            await processRole(parentRole);
          }
        }
      }
      
      resolvedRoles.push(role);
    };
    
    for (const role of roles) {
      await processRole(role);
    }
    
    return resolvedRoles;
  }
  
  private matchesResourceAndAction(permission: Permission, resourceType: string, action: string): boolean {
    // Check resource pattern matching
    if (permission.resource !== '*' && permission.resource !== resourceType) {
      // Support resource patterns like "repository.*"
      if (!permission.resource.includes('*')) {
        return false;
      }
      
      const pattern = permission.resource.replace('*', '.*');
      const regex = new RegExp(`^${pattern}$`);
      if (!regex.test(resourceType)) {
        return false;
      }
    }
    
    // Check action pattern matching
    if (permission.action !== '*' && permission.action !== action) {
      if (!permission.action.includes('*')) {
        return false;
      }
      
      const pattern = permission.action.replace('*', '.*');
      const regex = new RegExp(`^${pattern}$`);
      if (!regex.test(action)) {
        return false;
      }
    }
    
    return true;
  }
  
  private async evaluatePermission(
    permission: Permission,
    context: AuthorizationContext,
    resource: any
  ): Promise<{ matched: boolean; permission: Permission; reason?: string }> {
    
    // If no conditions, permission matches
    if (!permission.conditions || permission.conditions.length === 0) {
      return { matched: true, permission };
    }
    
    // Evaluate all conditions
    for (const condition of permission.conditions) {
      const conditionResult = await this.evaluateCondition(condition, context, resource);
      if (!conditionResult.matched) {
        return { 
          matched: false, 
          permission, 
          reason: conditionResult.reason 
        };
      }
    }
    
    return { matched: true, permission };
  }
  
  private async evaluateCondition(
    condition: PermissionCondition,
    context: AuthorizationContext,
    resource: any
  ): Promise<{ matched: boolean; reason?: string }> {
    
    let actualValue: any;
    
    // Get the actual value based on the field
    if (condition.field.startsWith('user.')) {
      const field = condition.field.substring(5);
      actualValue = this.getNestedProperty(context.user, field);
    } else if (condition.field.startsWith('resource.')) {
      const field = condition.field.substring(9);
      actualValue = this.getNestedProperty(resource, field);
    } else if (condition.field.startsWith('env.')) {
      const field = condition.field.substring(4);
      actualValue = this.getNestedProperty(context.environment, field);
    } else {
      // Direct field access
      actualValue = resource?.[condition.field];
    }
    
    // Evaluate condition based on operator
    let matched = false;
    
    switch (condition.operator) {
      case 'equals':
        matched = actualValue === condition.value;
        break;
      case 'contains':
        matched = Array.isArray(actualValue) 
          ? actualValue.includes(condition.value)
          : String(actualValue).includes(String(condition.value));
        break;
      case 'in':
        matched = Array.isArray(condition.value) 
          ? condition.value.includes(actualValue)
          : false;
        break;
      case 'not_in':
        matched = Array.isArray(condition.value) 
          ? !condition.value.includes(actualValue)
          : true;
        break;
      case 'greater_than':
        matched = Number(actualValue) > Number(condition.value);
        break;
      case 'less_than':
        matched = Number(actualValue) < Number(condition.value);
        break;
      default:
        matched = false;
    }
    
    return {
      matched,
      reason: matched ? undefined : `Condition failed: ${condition.field} ${condition.operator} ${condition.value}`
    };
  }
  
  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }
  
  private deduplicatePermissions(permissions: Permission[]): Permission[] {
    const seen = new Set<string>();
    return permissions.filter(permission => {
      const key = `${permission.resource}:${permission.action}:${permission.effect}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }
  
  private async logAuthorizationDecision(
    context: AuthorizationContext,
    result: AuthorizationResult,
    durationMs: number,
    error?: Error
  ): Promise<void> {
    await this.auditLogger.log({
      event: 'authorization_decision',
      userId: context.user.id,
      action: context.action,
      resource: context.resource?.id || 'unknown',
      allowed: result.allowed,
      reason: result.reason,
      appliedRules: result.appliedRules,
      durationMs,
      error: error?.message,
      timestamp: new Date()
    });
  }
}
```

### Resource-Specific Authorization

```typescript
// services/resourceAuthorizationService.ts
export class ResourceAuthorizationService {
  constructor(private authorizationService: AuthorizationService) {}
  
  // Repository-specific authorization
  async canAccessRepository(userId: string, repositoryId: string, action: string): Promise<boolean> {
    const result = await this.authorizationService.checkRepositoryAccess(userId, repositoryId, action);
    return result.allowed;
  }
  
  async canCreateRepository(userId: string, organizationId?: string): Promise<boolean> {
    if (organizationId) {
      // Check organization permissions
      const orgResult = await this.authorizationService.checkOrganizationAccess(
        userId, 
        organizationId, 
        'create_repository'
      );
      return orgResult.allowed;
    }
    
    // Check user's global permissions
    const context: AuthorizationContext = {
      user: await this.getUserById(userId),
      action: 'create'
    };
    
    const result = await this.authorizationService.authorize(context, 'repository');
    return result.allowed;
  }
  
  async canModifyRepository(userId: string, repositoryId: string): Promise<boolean> {
    return this.canAccessRepository(userId, repositoryId, 'write');
  }
  
  async canDeleteRepository(userId: string, repositoryId: string): Promise<boolean> {
    return this.canAccessRepository(userId, repositoryId, 'delete');
  }
  
  // Analysis-specific authorization
  async canStartAnalysis(userId: string, repositoryId: string): Promise<boolean> {
    // Must have write access to repository
    return this.canAccessRepository(userId, repositoryId, 'write');
  }
  
  async canViewAnalysis(userId: string, analysisId: string): Promise<boolean> {
    const analysis = await this.getAnalysisById(analysisId);
    if (!analysis) {
      return false;
    }
    
    return this.canAccessRepository(userId, analysis.repositoryId, 'read');
  }
  
  // Pattern-specific authorization
  async canViewPatterns(userId: string, repositoryId: string): Promise<boolean> {
    return this.canAccessRepository(userId, repositoryId, 'read');
  }
  
  async canCreatePattern(userId: string, repositoryId: string): Promise<boolean> {
    return this.canAccessRepository(userId, repositoryId, 'write');
  }
  
  async canPublishPattern(userId: string, patternId: string): Promise<boolean> {
    const pattern = await this.getPatternById(patternId);
    if (!pattern) {
      return false;
    }
    
    // Must be repository owner or have admin access
    return this.canAccessRepository(userId, pattern.repositoryId, 'admin');
  }
  
  // Query/Conversation authorization
  async canCreateConversation(userId: string, repositoryId?: string): Promise<boolean> {
    if (repositoryId) {
      return this.canAccessRepository(userId, repositoryId, 'read');
    }
    
    // Global conversations allowed for authenticated users
    return true;
  }
  
  async canViewConversation(userId: string, conversationId: string): Promise<boolean> {
    const conversation = await this.getConversationById(conversationId);
    if (!conversation) {
      return false;
    }
    
    // Owner can always view
    if (conversation.userId === userId) {
      return true;
    }
    
    // Repository-scoped conversations require repository access
    if (conversation.repositoryId) {
      return this.canAccessRepository(userId, conversation.repositoryId, 'read');
    }
    
    return false;
  }
  
  // Organization-specific authorization
  async canViewOrganization(userId: string, organizationId: string): Promise<boolean> {
    const result = await this.authorizationService.checkOrganizationAccess(
      userId, 
      organizationId, 
      'read'
    );
    return result.allowed;
  }
  
  async canManageOrganization(userId: string, organizationId: string): Promise<boolean> {
    const result = await this.authorizationService.checkOrganizationAccess(
      userId, 
      organizationId, 
      'admin'
    );
    return result.allowed;
  }
  
  async canInviteToOrganization(userId: string, organizationId: string): Promise<boolean> {
    const result = await this.authorizationService.checkOrganizationAccess(
      userId, 
      organizationId, 
      'invite_members'
    );
    return result.allowed;
  }
  
  // ML/AI-specific authorization
  async canTrainModel(userId: string, datasetId: string, modelType: string): Promise<boolean> {
    // Check user has ML training permissions
    const userPermissions = await this.getUserPermissions(userId);
    if (!userPermissions.includes('ml:train')) {
      return false;
    }
    
    // Check dataset access
    const dataset = await this.getDatasetById(datasetId);
    if (!dataset) {
      return false;
    }
    
    // Check data sensitivity level
    if (dataset.sensitivity === 'restricted' || dataset.sensitivity === 'confidential') {
      // Require additional approval
      const hasApproval = await this.checkMLApproval(userId, datasetId, 'train');
      if (!hasApproval) {
        return false;
      }
    }
    
    // Check model type restrictions
    const allowedModelTypes = await this.getAllowedModelTypes(userId);
    if (!allowedModelTypes.includes(modelType)) {
      return false;
    }
    
    return true;
  }
  
  async canAccessAIEndpoint(userId: string, endpoint: string, operation: string): Promise<boolean> {
    const context: AuthorizationContext = {
      user: await this.getUserById(userId),
      action: operation,
      environment: {
        endpoint,
        timestamp: new Date().toISOString()
      }
    };
    
    const result = await this.authorizationService.authorize(
      context, 
      'ai_endpoint',
      endpoint
    );
    
    // Additional checks for AI endpoints
    if (result.allowed && operation === 'generate') {
      // Check AI generation quota
      const quotaExceeded = await this.checkAIQuota(userId, 'generation');
      if (quotaExceeded) {
        return false;
      }
    }
    
    return result.allowed;
  }
  
  async canAccessModel(userId: string, modelId: string, purpose: string): Promise<boolean> {
    const model = await this.getModelById(modelId);
    if (!model) {
      return false;
    }
    
    // Check basic model access
    const hasAccess = await this.authorizationService.checkResourceAccess(
      userId,
      'ml_model',
      modelId,
      'read'
    );
    
    if (!hasAccess.allowed) {
      return false;
    }
    
    // Check purpose-specific permissions
    switch (purpose) {
      case 'inference':
        return this.canRunInference(userId, modelId);
      case 'evaluation':
        return this.canEvaluateModel(userId, modelId);
      case 'export':
        return this.canExportModel(userId, modelId);
      case 'update':
        return this.canUpdateModel(userId, modelId);
      default:
        return false;
    }
  }
  
  async canProcessSensitiveData(userId: string, dataLevel: string): Promise<boolean> {
    const user = await this.getUserById(userId);
    
    // Check user's data access level
    const userDataLevel = user.attributes?.dataAccessLevel || 'public';
    const levelHierarchy = ['public', 'internal', 'confidential', 'restricted'];
    
    const userLevelIndex = levelHierarchy.indexOf(userDataLevel);
    const requiredLevelIndex = levelHierarchy.indexOf(dataLevel);
    
    if (userLevelIndex < requiredLevelIndex) {
      return false;
    }
    
    // Additional checks for restricted data
    if (dataLevel === 'restricted') {
      // Require MFA
      if (!user.mfaVerified || user.mfaVerifiedAt < new Date(Date.now() - 30 * 60 * 1000)) {
        return false;
      }
      
      // Require approval
      const hasApproval = await this.checkDataAccessApproval(userId, 'restricted');
      if (!hasApproval) {
        return false;
      }
    }
    
    return true;
  }
  
  private async checkAIQuota(userId: string, quotaType: string): Promise<boolean> {
    const usage = await this.getAIUsage(userId, quotaType);
    const limit = await this.getAIQuotaLimit(userId, quotaType);
    
    return usage >= limit;
  }
  
  private async canRunInference(userId: string, modelId: string): Promise<boolean> {
    const permissions = await this.getUserPermissions(userId);
    return permissions.includes('ml:inference') || permissions.includes('ml:*');
  }
  
  private async canExportModel(userId: string, modelId: string): Promise<boolean> {
    const permissions = await this.getUserPermissions(userId);
    const model = await this.getModelById(modelId);
    
    // Only model owner or admin can export
    return model.ownerId === userId || permissions.includes('admin');
  }
}
```

### Express Middleware Integration

```typescript
// middleware/authorization.ts
export function requirePermission(resource: string, action: string) {
  return async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required'
      });
    }
    
    const resourceId = req.params.id || req.params.repositoryId || req.params.organizationId;
    
    const context: AuthorizationContext = {
      user: req.user,
      action,
      environment: {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    };
    
    const result = await authorizationService.authorize(context, resource, resourceId);
    
    if (!result.allowed) {
      return res.status(403).json({
        error: 'Insufficient permissions',
        message: result.reason,
        required_permission: `${action} on ${resource}`
      });
    }
    
    // Add authorization info to request
    req.authorization = {
      result,
      appliedRules: result.appliedRules
    };
    
    next();
  };
}

// Usage examples
app.get('/repositories/:id', 
  authenticate,
  requirePermission('repository', 'read'),
  getRepository
);

app.post('/repositories', 
  authenticate,
  requirePermission('repository', 'create'),
  createRepository
);

app.delete('/repositories/:id',
  authenticate,
  requirePermission('repository', 'delete'),
  deleteRepository
);
```

### GraphQL Authorization

```typescript
// graphql/authorization.ts
export function createAuthorizationDirective() {
  return {
    requirePermission: (next: any, source: any, args: any, context: any, info: any) => {
      return async (requiredPermission: { resource: string; action: string }) => {
        if (!context.user) {
          throw new AuthenticationError('Authentication required');
        }
        
        const resourceId = args.id || source.id;
        
        const authContext: AuthorizationContext = {
          user: context.user,
          action: requiredPermission.action,
          resource: source
        };
        
        const result = await authorizationService.authorize(
          authContext, 
          requiredPermission.resource, 
          resourceId
        );
        
        if (!result.allowed) {
          throw new ForbiddenError(`Insufficient permissions: ${result.reason}`);
        }
        
        return next();
      };
    }
  };
}

// Usage in resolvers
const resolvers = {
  Repository: {
    patterns: requirePermission({ resource: 'repository', action: 'read' })(
      async (parent, args, context) => {
        return context.dataSources.patternAPI.findByRepository(parent.id);
      }
    )
  },
  
  Mutation: {
    deleteRepository: requirePermission({ resource: 'repository', action: 'delete' })(
      async (parent, args, context) => {
        return context.dataSources.repositoryAPI.delete(args.id);
      }
    )
  }
};
```

### Role Management System

```typescript
// services/roleManagementService.ts
export interface RoleDefinition {
  name: string;
  description: string;
  permissions: {
    resource: string;
    actions: string[];
    conditions?: PermissionCondition[];
  }[];
  inheritsFrom?: string[];
  scope: 'system' | 'organization' | 'repository';
}

export class RoleManagementService {
  constructor(
    private roleRepository: RoleRepository,
    private permissionRepository: PermissionRepository
  ) {}
  
  async createRole(definition: RoleDefinition): Promise<Role> {
    // Validate role definition
    await this.validateRoleDefinition(definition);
    
    // Create permissions
    const permissions: Permission[] = [];
    for (const permDef of definition.permissions) {
      for (const action of permDef.actions) {
        permissions.push({
          id: crypto.randomUUID(),
          resource: permDef.resource,
          action,
          effect: 'allow',
          conditions: permDef.conditions
        });
      }
    }
    
    // Create role
    const role: Role = {
      id: crypto.randomUUID(),
      name: definition.name,
      description: definition.description,
      permissions,
      inheritsFrom: definition.inheritsFrom,
      scope: definition.scope
    };
    
    await this.roleRepository.save(role);
    return role;
  }
  
  async assignRoleToUser(userId: string, roleId: string, scope?: { organizationId?: string; repositoryId?: string }): Promise<void> {
    const role = await this.roleRepository.findById(roleId);
    if (!role) {
      throw new Error('Role not found');
    }
    
    // Validate scope matches role scope
    if (role.scope === 'organization' && !scope?.organizationId) {
      throw new Error('Organization ID required for organization-scoped role');
    }
    
    if (role.scope === 'repository' && !scope?.repositoryId) {
      throw new Error('Repository ID required for repository-scoped role');
    }
    
    await this.roleRepository.assignToUser(userId, roleId, scope);
    
    // Clear user permissions cache
    await this.clearUserPermissionsCache(userId);
  }
  
  async removeRoleFromUser(userId: string, roleId: string, scope?: { organizationId?: string; repositoryId?: string }): Promise<void> {
    await this.roleRepository.removeFromUser(userId, roleId, scope);
    await this.clearUserPermissionsCache(userId);
  }
  
  // Predefined system roles
  async createSystemRoles(): Promise<void> {
    const systemRoles: RoleDefinition[] = [
      {
        name: 'System Admin',
        description: 'Full system administration access',
        permissions: [{
          resource: '*',
          actions: ['*']
        }],
        scope: 'system'
      },
      {
        name: 'User',
        description: 'Basic user permissions',
        permissions: [
          {
            resource: 'repository',
            actions: ['read'],
            conditions: [{
              field: 'resource.ownerId',
              operator: 'equals',
              value: '${user.id}'
            }]
          },
          {
            resource: 'conversation',
            actions: ['read', 'write', 'create']
          }
        ],
        scope: 'system'
      },
      {
        name: 'Organization Admin',
        description: 'Full organization administration',
        permissions: [
          {
            resource: 'organization',
            actions: ['read', 'write', 'admin', 'invite_members']
          },
          {
            resource: 'repository',
            actions: ['read', 'write', 'create', 'delete'],
            conditions: [{
              field: 'resource.organizationId',
              operator: 'equals',
              value: '${scope.organizationId}'
            }]
          }
        ],
        scope: 'organization'
      },
      {
        name: 'Organization Member',
        description: 'Basic organization member access',
        permissions: [
          {
            resource: 'organization',
            actions: ['read']
          },
          {
            resource: 'repository',
            actions: ['read'],
            conditions: [{
              field: 'resource.organizationId',
              operator: 'equals',
              value: '${scope.organizationId}'
            }]
          }
        ],
        scope: 'organization'
      },
      {
        name: 'Repository Owner',
        description: 'Full repository control',
        permissions: [{
          resource: 'repository',
          actions: ['read', 'write', 'admin', 'delete'],
          conditions: [{
            field: 'resource.id',
            operator: 'equals',
            value: '${scope.repositoryId}'
          }]
        }],
        scope: 'repository'
      },
      {
        name: 'Repository Collaborator',
        description: 'Repository read/write access',
        permissions: [{
          resource: 'repository',
          actions: ['read', 'write'],
          conditions: [{
            field: 'resource.id',
            operator: 'equals',
            value: '${scope.repositoryId}'
          }]
        }],
        scope: 'repository'
      }
    ];
    
    for (const roleDef of systemRoles) {
      const existing = await this.roleRepository.findByName(roleDef.name);
      if (!existing) {
        await this.createRole(roleDef);
      }
    }
  }
  
  private async validateRoleDefinition(definition: RoleDefinition): Promise<void> {
    // Validate role name uniqueness
    const existing = await this.roleRepository.findByName(definition.name);
    if (existing) {
      throw new Error(`Role with name '${definition.name}' already exists`);
    }
    
    // Validate inheritance
    if (definition.inheritsFrom) {
      for (const parentRoleId of definition.inheritsFrom) {
        const parentRole = await this.roleRepository.findById(parentRoleId);
        if (!parentRole) {
          throw new Error(`Parent role '${parentRoleId}' not found`);
        }
        
        // Prevent circular inheritance
        if (await this.hasCircularInheritance(definition.name, parentRoleId)) {
          throw new Error('Circular role inheritance detected');
        }
      }
    }
  }
  
  private async hasCircularInheritance(roleName: string, parentRoleId: string): Promise<boolean> {
    const visited = new Set<string>();
    
    const checkCircular = async (roleId: string): Promise<boolean> => {
      if (visited.has(roleId)) {
        return true;
      }
      
      visited.add(roleId);
      
      const role = await this.roleRepository.findById(roleId);
      if (!role || !role.inheritsFrom) {
        return false;
      }
      
      for (const parentId of role.inheritsFrom) {
        if (await checkCircular(parentId)) {
          return true;
        }
      }
      
      return false;
    };
    
    return checkCircular(parentRoleId);
  }
  
  private async clearUserPermissionsCache(userId: string): Promise<void> {
    const cacheKey = `user_permissions:${userId}`;
    await this.cache.delete(cacheKey);
  }
}
```

## Validation Loop

### Level 1: Permission Testing
```typescript
// Test authorization decisions
describe('Authorization Service', () => {
  test('repository owner can access repository', async () => {
    const result = await authorizationService.checkRepositoryAccess(
      'owner-user-id',
      'repo-123',
      'read'
    );
    
    expect(result.allowed).toBe(true);
    expect(result.appliedRules).toContain('repository-owner-read');
  });
  
  test('non-member cannot access private repository', async () => {
    const result = await authorizationService.checkRepositoryAccess(
      'random-user-id',
      'private-repo-123',
      'read'
    );
    
    expect(result.allowed).toBe(false);
    expect(result.reason).toContain('No permissions found');
  });
  
  test('organization member can read org repositories', async () => {
    const result = await authorizationService.checkRepositoryAccess(
      'org-member-id',
      'org-repo-123',
      'read'
    );
    
    expect(result.allowed).toBe(true);
  });
});
```

### Level 2: Role Inheritance Testing
```typescript
// Test role inheritance
describe('Role Management', () => {
  test('role inheritance works correctly', async () => {
    // Create parent role
    const parentRole = await roleService.createRole({
      name: 'Base User',
      description: 'Base permissions',
      permissions: [{
        resource: 'repository',
        actions: ['read']
      }],
      scope: 'system'
    });
    
    // Create child role
    const childRole = await roleService.createRole({
      name: 'Advanced User',
      description: 'Advanced permissions',
      permissions: [{
        resource: 'repository',
        actions: ['write']
      }],
      inheritsFrom: [parentRole.id],
      scope: 'system'
    });
    
    // Assign child role to user
    await roleService.assignRoleToUser('user-123', childRole.id);
    
    // User should have both read and write permissions
    const readResult = await authorizationService.checkRepositoryAccess(
      'user-123', 'repo-123', 'read'
    );
    const writeResult = await authorizationService.checkRepositoryAccess(
      'user-123', 'repo-123', 'write'
    );
    
    expect(readResult.allowed).toBe(true);
    expect(writeResult.allowed).toBe(true);
  });
});
```

### Level 3: Performance Testing
```typescript
// Test authorization performance
describe('Authorization Performance', () => {
  test('authorization decisions under 10ms', async () => {
    const startTime = Date.now();
    
    await authorizationService.checkRepositoryAccess(
      'user-123',
      'repo-123',
      'read'
    );
    
    const duration = Date.now() - startTime;
    expect(duration).toBeLessThan(10);
  });
  
  test('handles high concurrency', async () => {
    const requests = Array.from({ length: 1000 }, () =>
      authorizationService.checkRepositoryAccess(
        'user-123',
        'repo-123',
        'read'
      )
    );
    
    const startTime = Date.now();
    const results = await Promise.all(requests);
    const duration = Date.now() - startTime;
    
    expect(results).toHaveLength(1000);
    expect(duration).toBeLessThan(1000); // 1000 requests in under 1 second
  });
});
```

## Final Validation Checklist

- [ ] Role-based access control implemented
- [ ] Resource-based permissions working
- [ ] Permission inheritance functional
- [ ] Organization hierarchy support
- [ ] API endpoint protection active
- [ ] GraphQL authorization integrated
- [ ] Performance requirements met
- [ ] Audit logging comprehensive
- [ ] Security testing passed
- [ ] Enterprise features ready

## Anti-Patterns to Avoid

1. **DON'T hardcode permissions** - Use dynamic permission evaluation
2. **DON'T skip permission caching** - Performance will suffer
3. **DON'T ignore permission inheritance** - Can create security holes
4. **DON'T allow permission escalation** - Validate all role changes
5. **DON'T skip audit logging** - Essential for compliance
6. **DON'T trust client-side permissions** - Always validate server-side
7. **DON'T use overly complex conditions** - Keep them simple and fast
8. **DON'T forget to clear cache** - Stale permissions are dangerous
9. **DON'T allow circular role inheritance** - Causes infinite loops
10. **DON'T expose internal permission details** - Security through obscurity
