# ML/AI Platform Security Implementation

name: "ML/AI Platform Security Implementation"
description: |
  Comprehensive security framework for ML/AI platforms handling sensitive code analysis with Google Gemini 2.5 Flash API integration.
  
  Core Principles:
  - **Data Privacy**: Protect enterprise code during AI analysis
  - **API Security**: Secure Google AI API communications
  - **Model Security**: Protect ML models and prevent adversarial attacks
  - **Compliance**: Meet enterprise ML/AI security requirements
  - **Zero Trust AI**: Never trust AI outputs without validation

## Goal

Implement robust security measures for ML/AI components that protect sensitive code data during analysis, secure API communications with Google AI services, and ensure compliance with enterprise security standards.

## Why

ML/AI security is critical for:
- Protecting proprietary code during AI analysis
- Preventing data leakage through AI APIs
- Meeting enterprise AI governance requirements
- Ensuring model integrity and preventing tampering
- Building trust in AI-powered code analysis

This provides:
- Secure AI processing pipeline
- Data privacy during ML operations
- Protected model deployment
- Compliant AI operations
- Trustworthy code intelligence

## What

### User-Visible Behavior
- Transparent security for AI operations
- Data privacy guarantees
- Secure code analysis results
- AI audit trails
- Privacy-preserving pattern detection

### Technical Requirements
- [ ] Secure Google Gemini API integration
- [ ] Code sanitization before AI processing
- [ ] Encrypted model storage and deployment
- [ ] AI request/response validation
- [ ] Privacy-preserving embeddings
- [ ] Adversarial attack prevention
- [ ] Model versioning and integrity
- [ ] AI audit logging

### Success Criteria
- [ ] Zero code leakage incidents
- [ ] 100% encrypted AI communications
- [ ] Complete AI audit trail
- [ ] Model integrity verification
- [ ] Enterprise compliance met

## All Needed Context

### Documentation & References
- url: https://cloud.google.com/vertex-ai/docs/generative-ai/model-reference/gemini
  why: Google Gemini API security guidelines
- url: https://cloud.google.com/dlp/docs
  why: Data Loss Prevention for sensitive data
- url: https://www.nist.gov/itl/ai-risk-management-framework
  why: NIST AI Risk Management Framework
- file: PRPs/security/authentication.md
  why: Platform authentication patterns

### ML/AI Security Architecture

```yaml
AI Security Layers:
  data_protection:
    - code_sanitization
    - pii_removal
    - secret_scanning
    - data_minimization
    
  api_security:
    - encrypted_transport
    - api_key_rotation
    - rate_limiting
    - request_validation
    
  model_security:
    - encrypted_storage
    - access_control
    - versioning
    - integrity_checks
    
  privacy_preservation:
    - differential_privacy
    - secure_aggregation
    - homomorphic_encryption
    - federated_learning

Security Controls:
  - Input validation and sanitization
  - Output filtering and validation
  - Model access logging
  - Anomaly detection
  - Data retention policies
```

### Google AI API Security

```yaml
Gemini API Security:
  authentication:
    method: API Key + OAuth2
    rotation: Every 90 days
    storage: Google Secret Manager
    
  data_handling:
    encryption: TLS 1.3
    retention: 30 days max
    location: US regions only
    
  content_filtering:
    - Remove secrets/credentials
    - Sanitize PII
    - Filter proprietary markers
    - Validate outputs
    
  rate_limiting:
    - 60 requests/minute
    - Exponential backoff
    - Circuit breaker pattern
```

### Known Gotchas & Security Risks
- **CRITICAL**: Never send raw credentials to AI APIs
- **CRITICAL**: Always validate AI outputs for injections
- **GOTCHA**: AI can memorize sensitive data
- **GOTCHA**: Prompt injection attacks possible
- **WARNING**: Model outputs may contain biased data
- **TIP**: Use structured outputs to prevent injections
- **TIP**: Implement content filtering on both ends

## Implementation Blueprint

### Secure AI Service Integration

```typescript
// services/secureAIService.ts
import { GoogleGenerativeAI } from '@google/generative-ai';
import { SecretManagerServiceClient } from '@google-cloud/secret-manager';
import crypto from 'crypto';

export interface AISecurityConfig {
  projectId: string;
  location: string;
  keyRotationDays: number;
  maxRetentionDays: number;
  enableAudit: boolean;
}

export interface SecureAIRequest {
  content: string;
  userId: string;
  context: string;
  sensitivity: 'public' | 'internal' | 'confidential' | 'restricted';
}

export interface SecureAIResponse {
  result: string;
  filtered: boolean;
  auditId: string;
  processingTime: number;
}

export class SecureAIService {
  private genAI: GoogleGenerativeAI;
  private secretManager: SecretManagerServiceClient;
  private encryptionService: EncryptionService;
  private auditLogger: AuditLogger;
  
  constructor(
    private config: AISecurityConfig,
    private sanitizer: CodeSanitizer,
    private validator: AIResponseValidator
  ) {
    this.secretManager = new SecretManagerServiceClient();
    this.initializeAI();
  }
  
  private async initializeAI(): Promise<void> {
    // Retrieve API key from Secret Manager
    const apiKey = await this.getSecureAPIKey();
    
    this.genAI = new GoogleGenerativeAI(apiKey);
    
    // Schedule key rotation
    this.scheduleKeyRotation();
  }
  
  private async getSecureAPIKey(): Promise<string> {
    const secretName = `projects/${this.config.projectId}/secrets/gemini-api-key/versions/latest`;
    
    try {
      const [version] = await this.secretManager.accessSecretVersion({
        name: secretName,
      });
      
      const apiKey = version.payload?.data?.toString();
      if (!apiKey) {
        throw new Error('API key not found in Secret Manager');
      }
      
      return apiKey;
    } catch (error) {
      this.auditLogger.logSecurityEvent({
        event: 'api_key_retrieval_failed',
        error: error.message,
        timestamp: new Date()
      });
      throw new Error('Failed to retrieve API key securely');
    }
  }
  
  async processSecurely(request: SecureAIRequest): Promise<SecureAIResponse> {
    const startTime = Date.now();
    const auditId = crypto.randomUUID();
    
    try {
      // Step 1: Validate request
      this.validateRequest(request);
      
      // Step 2: Sanitize content
      const sanitized = await this.sanitizeContent(request.content, request.sensitivity);
      
      // Step 3: Create secure prompt
      const securePrompt = this.createSecurePrompt(sanitized.content, request.context);
      
      // Step 4: Log request (without sensitive data)
      await this.auditLogger.logAIRequest({
        auditId,
        userId: request.userId,
        context: request.context,
        sensitivity: request.sensitivity,
        sanitizedTokens: sanitized.removedTokens,
        timestamp: new Date()
      });
      
      // Step 5: Call AI API with timeout and retry
      const model = this.genAI.getGenerativeModel({ 
        model: 'gemini-2.0-flash',
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        },
        safetySettings: [
          {
            category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
        ],
      });
      
      const result = await this.callWithRetry(async () => {
        const response = await model.generateContent(securePrompt);
        return response.response.text();
      });
      
      // Step 6: Validate and filter response
      const validated = await this.validator.validateResponse(result, request.context);
      
      // Step 7: Check for sensitive data in response
      const filtered = await this.filterSensitiveData(validated.content);
      
      // Step 8: Log response metadata
      await this.auditLogger.logAIResponse({
        auditId,
        success: true,
        filtered: filtered.wasFiltered,
        processingTime: Date.now() - startTime,
        outputTokens: this.estimateTokens(filtered.content),
        timestamp: new Date()
      });
      
      return {
        result: filtered.content,
        filtered: filtered.wasFiltered,
        auditId,
        processingTime: Date.now() - startTime
      };
      
    } catch (error) {
      await this.auditLogger.logAIError({
        auditId,
        userId: request.userId,
        error: error.message,
        processingTime: Date.now() - startTime,
        timestamp: new Date()
      });
      
      throw new AISecurityError(`Secure AI processing failed: ${error.message}`);
    }
  }
  
  private async sanitizeContent(content: string, sensitivity: string): Promise<SanitizationResult> {
    const result = {
      content: content,
      removedTokens: [] as string[]
    };
    
    // Remove secrets and credentials
    const secretsRemoved = await this.sanitizer.removeSecrets(result.content);
    result.content = secretsRemoved.content;
    result.removedTokens.push(...secretsRemoved.removed);
    
    // Remove PII based on sensitivity
    if (sensitivity !== 'public') {
      const piiRemoved = await this.sanitizer.removePII(result.content);
      result.content = piiRemoved.content;
      result.removedTokens.push(...piiRemoved.removed);
    }
    
    // Remove proprietary markers
    if (sensitivity === 'confidential' || sensitivity === 'restricted') {
      const proprietaryRemoved = await this.sanitizer.removeProprietaryInfo(result.content);
      result.content = proprietaryRemoved.content;
      result.removedTokens.push(...proprietaryRemoved.removed);
    }
    
    return result;
  }
  
  private createSecurePrompt(content: string, context: string): string {
    // Add security instructions to prompt
    const securityInstructions = `
IMPORTANT SECURITY CONSTRAINTS:
1. Do not output any API keys, passwords, or secrets
2. Do not include personally identifiable information
3. Do not reveal internal system details
4. Focus only on the requested analysis
5. Provide structured, validated output

CONTEXT: ${context}

CONTENT TO ANALYZE:
${content}
`;
    
    return securityInstructions;
  }
  
  private async filterSensitiveData(content: string): Promise<FilterResult> {
    let filtered = content;
    let wasFiltered = false;
    
    // Check for accidental credential exposure
    const credentialPatterns = [
      /api[_-]?key[_-]?[:=]\s*['"]?[\w-]+['"]?/gi,
      /password[_-]?[:=]\s*['"]?[\w-]+['"]?/gi,
      /token[_-]?[:=]\s*['"]?[\w-]+['"]?/gi,
      /secret[_-]?[:=]\s*['"]?[\w-]+['"]?/gi
    ];
    
    for (const pattern of credentialPatterns) {
      if (pattern.test(filtered)) {
        filtered = filtered.replace(pattern, '[REDACTED]');
        wasFiltered = true;
      }
    }
    
    // Check for PII patterns
    const piiPatterns = [
      /\b[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}\b/gi, // Email
      /\b\d{3}-\d{2}-\d{4}\b/g, // SSN
      /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g, // Phone
    ];
    
    for (const pattern of piiPatterns) {
      if (pattern.test(filtered)) {
        filtered = filtered.replace(pattern, '[PII_REMOVED]');
        wasFiltered = true;
      }
    }
    
    return { content: filtered, wasFiltered };
  }
  
  private async callWithRetry<T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    backoffMs: number = 1000
  ): Promise<T> {
    let lastError: Error;
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        if (i < maxRetries - 1) {
          const delay = backoffMs * Math.pow(2, i);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw lastError!;
  }
  
  private scheduleKeyRotation(): void {
    setInterval(async () => {
      try {
        await this.rotateAPIKey();
      } catch (error) {
        this.auditLogger.logSecurityEvent({
          event: 'api_key_rotation_failed',
          error: error.message,
          timestamp: new Date()
        });
      }
    }, this.config.keyRotationDays * 24 * 60 * 60 * 1000);
  }
  
  private async rotateAPIKey(): Promise<void> {
    // Implementation would rotate the API key in Secret Manager
    // and reinitialize the AI client
    await this.auditLogger.logSecurityEvent({
      event: 'api_key_rotated',
      timestamp: new Date()
    });
  }
}
```

### Code Sanitization Service

```typescript
// services/codeSanitizer.ts
export class CodeSanitizer {
  private secretPatterns: RegExp[] = [
    // API Keys
    /(?:api[_-]?key|apikey|api_secret)[\s]*[:=][\s]*['"]?([a-zA-Z0-9_\-]{20,})['"]?/gi,
    // AWS Keys
    /(?:AKIA|A3T|AGPA|AIDA|AROA|AIPA|ANPA|ANVA|ASIA)[A-Z0-9]{16}/g,
    // Private Keys
    /-----BEGIN (?:RSA |EC |DSA |OPENSSH )?PRIVATE KEY-----/g,
    // JWT Tokens
    /eyJ[A-Za-z0-9_-]{10,}\.[A-Za-z0-9_-]{10,}\.[A-Za-z0-9_-]{10,}/g,
    // Database URLs
    /(?:mongodb|postgres|mysql|redis):\/\/[^:]+:[^@]+@[^/]+/gi,
    // Generic Secrets
    /(?:password|passwd|pwd|secret|token)[\s]*[:=][\s]*['"]?([^'"\s]{8,})['"]?/gi
  ];
  
  private piiPatterns: RegExp[] = [
    // Email addresses
    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
    // SSN
    /\b\d{3}-\d{2}-\d{4}\b/g,
    // Credit card numbers
    /\b(?:\d{4}[\s-]?){3}\d{4}\b/g,
    // Phone numbers
    /\b(?:\+?1?\s*)?(?:\([0-9]{3}\)|[0-9]{3})[\s.-]?[0-9]{3}[\s.-]?[0-9]{4}\b/g,
    // IP addresses
    /\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b/g
  ];
  
  async removeSecrets(code: string): Promise<SanitizationResult> {
    let sanitized = code;
    const removed: string[] = [];
    
    for (const pattern of this.secretPatterns) {
      const matches = sanitized.match(pattern);
      if (matches) {
        removed.push(...matches.map(m => this.maskSecret(m)));
        sanitized = sanitized.replace(pattern, (match) => {
          return `[SECRET_REMOVED:${this.getSecretType(match)}]`;
        });
      }
    }
    
    return { content: sanitized, removed };
  }
  
  async removePII(code: string): Promise<SanitizationResult> {
    let sanitized = code;
    const removed: string[] = [];
    
    for (const pattern of this.piiPatterns) {
      const matches = sanitized.match(pattern);
      if (matches) {
        removed.push(...matches);
        sanitized = sanitized.replace(pattern, '[PII_REMOVED]');
      }
    }
    
    return { content: sanitized, removed };
  }
  
  async removeProprietaryInfo(code: string): Promise<SanitizationResult> {
    let sanitized = code;
    const removed: string[] = [];
    
    // Remove copyright headers
    const copyrightPattern = /Copyright\s*(?:\(c\)|©)?\s*\d{4}[^\\n]*/gi;
    sanitized = sanitized.replace(copyrightPattern, '[COPYRIGHT_REMOVED]');
    
    // Remove proprietary comments
    const proprietaryPattern = /(?:proprietary|confidential|internal use only|trade secret)[^\\n]*/gi;
    sanitized = sanitized.replace(proprietaryPattern, '[PROPRIETARY_REMOVED]');
    
    // Remove company-specific identifiers (customize based on needs)
    const companyPatterns = [
      /(?:com\.mycompany\.[a-zA-Z0-9_.]+)/g,
      /(?:@mycompany\.com)/g
    ];
    
    for (const pattern of companyPatterns) {
      sanitized = sanitized.replace(pattern, '[COMPANY_IDENTIFIER_REMOVED]');
    }
    
    return { content: sanitized, removed };
  }
  
  private getSecretType(match: string): string {
    if (match.includes('AKIA') || match.includes('aws')) return 'AWS_KEY';
    if (match.includes('api_key') || match.includes('apikey')) return 'API_KEY';
    if (match.includes('BEGIN') && match.includes('PRIVATE KEY')) return 'PRIVATE_KEY';
    if (match.startsWith('eyJ')) return 'JWT_TOKEN';
    if (match.includes('mongodb') || match.includes('postgres')) return 'DATABASE_URL';
    return 'GENERIC_SECRET';
  }
  
  private maskSecret(secret: string): string {
    if (secret.length <= 8) return '*'.repeat(secret.length);
    return secret.substring(0, 4) + '*'.repeat(secret.length - 8) + secret.substring(secret.length - 4);
  }
}
```

### ML Model Security

```typescript
// services/modelSecurityService.ts
export class ModelSecurityService {
  constructor(
    private encryptionService: EncryptionService,
    private integrityService: IntegrityService,
    private accessControl: AccessControlService,
    private auditLogger: AuditLogger
  ) {}
  
  async secureModel(
    model: Buffer,
    modelId: string,
    metadata: ModelMetadata
  ): Promise<SecuredModel> {
    try {
      // Step 1: Calculate model hash for integrity
      const modelHash = await this.integrityService.calculateHash(model);
      
      // Step 2: Encrypt model
      const encryptedModel = await this.encryptionService.encryptLargeData(
        model,
        `model:${modelId}`
      );
      
      // Step 3: Generate access policy
      const accessPolicy = this.generateAccessPolicy(metadata);
      
      // Step 4: Create signed metadata
      const signedMetadata = await this.integrityService.signMetadata({
        ...metadata,
        modelHash,
        encryptedAt: new Date(),
        encryptionKeyId: encryptedModel.keyId
      });
      
      // Step 5: Store securely
      const securedModel: SecuredModel = {
        id: modelId,
        encryptedData: encryptedModel,
        metadata: signedMetadata,
        accessPolicy,
        createdAt: new Date()
      };
      
      await this.auditLogger.logModelSecurity({
        event: 'model_secured',
        modelId,
        modelType: metadata.type,
        size: model.length,
        hash: modelHash,
        timestamp: new Date()
      });
      
      return securedModel;
      
    } catch (error) {
      await this.auditLogger.logModelSecurity({
        event: 'model_security_failed',
        modelId,
        error: error.message,
        timestamp: new Date()
      });
      throw new ModelSecurityError(`Failed to secure model: ${error.message}`);
    }
  }
  
  async loadSecureModel(
    modelId: string,
    userId: string,
    purpose: string
  ): Promise<Buffer> {
    try {
      // Step 1: Check access permissions
      const hasAccess = await this.accessControl.checkModelAccess(
        userId,
        modelId,
        purpose
      );
      
      if (!hasAccess) {
        throw new AccessDeniedError('Insufficient permissions to access model');
      }
      
      // Step 2: Retrieve encrypted model
      const securedModel = await this.getSecuredModel(modelId);
      
      // Step 3: Verify integrity
      const integrityValid = await this.integrityService.verifyMetadata(
        securedModel.metadata
      );
      
      if (!integrityValid) {
        throw new IntegrityError('Model integrity check failed');
      }
      
      // Step 4: Decrypt model
      const decryptedModel = await this.encryptionService.decryptLargeData(
        securedModel.encryptedData,
        `model:${modelId}`
      );
      
      // Step 5: Verify model hash
      const currentHash = await this.integrityService.calculateHash(decryptedModel);
      if (currentHash !== securedModel.metadata.modelHash) {
        throw new IntegrityError('Model data corrupted');
      }
      
      // Step 6: Log access
      await this.auditLogger.logModelAccess({
        event: 'model_accessed',
        modelId,
        userId,
        purpose,
        timestamp: new Date()
      });
      
      return decryptedModel;
      
    } catch (error) {
      await this.auditLogger.logModelAccess({
        event: 'model_access_failed',
        modelId,
        userId,
        purpose,
        error: error.message,
        timestamp: new Date()
      });
      throw error;
    }
  }
  
  private generateAccessPolicy(metadata: ModelMetadata): AccessPolicy {
    return {
      allowedRoles: ['ml_engineer', 'data_scientist', 'admin'],
      allowedPurposes: ['inference', 'evaluation', 'debugging'],
      restrictions: {
        maxAccessesPerDay: 1000,
        requiresMFA: metadata.sensitivity === 'high',
        requiresApproval: metadata.type === 'financial_model',
        dataResidency: metadata.dataResidency || 'us-central1'
      },
      expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year
    };
  }
}
```

### Privacy-Preserving Embeddings

```typescript
// services/privacyPreservingEmbeddings.ts
export class PrivacyPreservingEmbeddingService {
  constructor(
    private embeddingModel: EmbeddingModel,
    private differentialPrivacy: DifferentialPrivacyService,
    private homomorphicEncryption: HomomorphicEncryptionService
  ) {}
  
  async generatePrivateEmbeddings(
    text: string,
    privacyBudget: number = 1.0
  ): Promise<PrivateEmbedding> {
    try {
      // Step 1: Sanitize input
      const sanitized = await this.sanitizeForEmbedding(text);
      
      // Step 2: Generate base embedding
      const embedding = await this.embeddingModel.embed(sanitized);
      
      // Step 3: Apply differential privacy
      const privateEmbedding = await this.differentialPrivacy.addNoise(
        embedding,
        privacyBudget
      );
      
      // Step 4: Optional homomorphic encryption for computation
      const encryptedEmbedding = await this.homomorphicEncryption.encrypt(
        privateEmbedding
      );
      
      return {
        embedding: privateEmbedding,
        encrypted: encryptedEmbedding,
        privacyBudget,
        sanitizationApplied: true,
        timestamp: new Date()
      };
      
    } catch (error) {
      throw new PrivacyError(`Failed to generate private embedding: ${error.message}`);
    }
  }
  
  async computeSecureSimilarity(
    embedding1: PrivateEmbedding,
    embedding2: PrivateEmbedding
  ): Promise<number> {
    // Compute similarity on encrypted embeddings
    if (embedding1.encrypted && embedding2.encrypted) {
      return await this.homomorphicEncryption.computeCosineSimilarity(
        embedding1.encrypted,
        embedding2.encrypted
      );
    }
    
    // Fallback to differentially private similarity
    return this.computeDPSimilarity(
      embedding1.embedding,
      embedding2.embedding
    );
  }
  
  private async sanitizeForEmbedding(text: string): Promise<string> {
    // Remove identifiers and sensitive information
    let sanitized = text;
    
    // Remove variable names and function names
    sanitized = sanitized.replace(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g, (match) => {
      if (this.isKeyword(match)) return match;
      return 'IDENTIFIER';
    });
    
    // Remove string literals
    sanitized = sanitized.replace(/["']([^"']*?)["']/g, 'STRING_LITERAL');
    
    // Remove numbers
    sanitized = sanitized.replace(/\b\d+\b/g, 'NUMBER');
    
    return sanitized;
  }
  
  private isKeyword(word: string): boolean {
    const keywords = [
      'function', 'class', 'if', 'else', 'for', 'while', 'return',
      'import', 'export', 'const', 'let', 'var', 'async', 'await'
    ];
    return keywords.includes(word);
  }
  
  private computeDPSimilarity(emb1: number[], emb2: number[]): number {
    // Compute cosine similarity with added noise
    const dotProduct = emb1.reduce((sum, val, i) => sum + val * emb2[i], 0);
    const norm1 = Math.sqrt(emb1.reduce((sum, val) => sum + val * val, 0));
    const norm2 = Math.sqrt(emb2.reduce((sum, val) => sum + val * val, 0));
    
    const similarity = dotProduct / (norm1 * norm2);
    
    // Add small noise for additional privacy
    const noise = (Math.random() - 0.5) * 0.01;
    return Math.max(0, Math.min(1, similarity + noise));
  }
}
```

### AI Security Monitoring

```typescript
// services/aiSecurityMonitoring.ts
export class AISecurityMonitor {
  private anomalyDetector: AnomalyDetector;
  private threatDetector: ThreatDetector;
  
  constructor(
    private metricsService: MetricsService,
    private alertingService: AlertingService,
    private auditLogger: AuditLogger
  ) {
    this.anomalyDetector = new AnomalyDetector();
    this.threatDetector = new ThreatDetector();
  }
  
  async monitorAIRequest(request: AISecurityRequest): Promise<MonitoringResult> {
    const metrics: SecurityMetrics = {
      requestSize: request.content.length,
      sensitivityLevel: request.sensitivity,
      userId: request.userId,
      timestamp: new Date()
    };
    
    // Check for anomalies
    const anomalies = await this.anomalyDetector.detect({
      userBehavior: await this.getUserBehaviorProfile(request.userId),
      requestPattern: this.analyzeRequestPattern(request),
      timePattern: this.analyzeTimePattern(request.userId)
    });
    
    // Check for threats
    const threats = await this.threatDetector.detect({
      promptInjection: this.detectPromptInjection(request.content),
      dataExfiltration: this.detectDataExfiltration(request.content),
      adversarialInput: this.detectAdversarialInput(request.content)
    });
    
    // Log metrics
    await this.metricsService.recordAISecurityMetrics(metrics);
    
    // Alert on critical issues
    if (anomalies.severity === 'high' || threats.length > 0) {
      await this.alertingService.sendSecurityAlert({
        type: 'ai_security_threat',
        severity: 'critical',
        userId: request.userId,
        anomalies,
        threats,
        timestamp: new Date()
      });
    }
    
    return {
      safe: anomalies.severity === 'low' && threats.length === 0,
      anomalies,
      threats,
      recommendations: this.generateRecommendations(anomalies, threats)
    };
  }
  
  private detectPromptInjection(content: string): ThreatIndicator | null {
    const injectionPatterns = [
      /ignore\s+previous\s+instructions/i,
      /system\s*:\s*override/i,
      /\\x[0-9a-fA-F]{2}/g, // Hex encoding
      /eval\s*\(/i,
      /exec\s*\(/i
    ];
    
    for (const pattern of injectionPatterns) {
      if (pattern.test(content)) {
        return {
          type: 'prompt_injection',
          confidence: 0.8,
          pattern: pattern.source
        };
      }
    }
    
    return null;
  }
  
  private detectDataExfiltration(content: string): ThreatIndicator | null {
    // Check for attempts to extract large amounts of data
    const exfiltrationPatterns = [
      /select\s+\*\s+from/i,
      /dump\s+database/i,
      /export\s+all/i,
      /download\s+everything/i
    ];
    
    for (const pattern of exfiltrationPatterns) {
      if (pattern.test(content)) {
        return {
          type: 'data_exfiltration',
          confidence: 0.7,
          pattern: pattern.source
        };
      }
    }
    
    return null;
  }
  
  private detectAdversarialInput(content: string): ThreatIndicator | null {
    // Check for adversarial patterns
    const suspiciousPatterns = [
      // Unicode direction override characters
      /[\u202A-\u202E\u2066-\u2069]/,
      // Excessive special characters
      /[^\w\s]{50,}/,
      // Repeated patterns that might cause issues
      /(.)\1{100,}/
    ];
    
    for (const pattern of suspiciousPatterns) {
      if (pattern.test(content)) {
        return {
          type: 'adversarial_input',
          confidence: 0.6,
          pattern: pattern.source
        };
      }
    }
    
    return null;
  }
}
```

## Validation Loop

### Level 1: AI Security Testing
```typescript
// Test AI security measures
describe('AI Security Service', () => {
  test('sanitizes sensitive data before AI processing', async () => {
    const request: SecureAIRequest = {
      content: 'const apiKey = "sk-1234567890"; password = "secret123"',
      userId: 'user123',
      context: 'code analysis',
      sensitivity: 'confidential'
    };
    
    const response = await secureAIService.processSecurely(request);
    
    expect(response.result).not.toContain('sk-1234567890');
    expect(response.result).not.toContain('secret123');
    expect(response.filtered).toBe(true);
  });
  
  test('validates AI responses for injections', async () => {
    const maliciousResponse = 'Result: <script>alert("xss")</script>';
    const validated = await validator.validateResponse(maliciousResponse, 'analysis');
    
    expect(validated.content).not.toContain('<script>');
    expect(validated.wasFiltered).toBe(true);
  });
  
  test('enforces rate limiting for AI requests', async () => {
    const requests = Array.from({ length: 65 }, (_, i) => 
      secureAIService.processSecurely({
        content: `test ${i}`,
        userId: 'user123',
        context: 'test',
        sensitivity: 'public'
      })
    );
    
    await expect(Promise.all(requests)).rejects.toThrow('Rate limit exceeded');
  });
});
```

### Level 2: Model Security Testing
```typescript
// Test model security
describe('Model Security', () => {
  test('encrypts models at rest', async () => {
    const model = Buffer.from('model data');
    const secured = await modelSecurity.secureModel(
      model,
      'model-123',
      { type: 'classification', sensitivity: 'high' }
    );
    
    expect(secured.encryptedData).toBeDefined();
    expect(secured.metadata.modelHash).toBeDefined();
    expect(secured.accessPolicy.requiresMFA).toBe(true);
  });
  
  test('verifies model integrity on load', async () => {
    const tamperedModel = await modelSecurity.loadSecureModel(
      'tampered-model',
      'user123',
      'inference'
    );
    
    await expect(tamperedModel).rejects.toThrow('Model integrity check failed');
  });
});
```

### Level 3: Privacy Testing
```typescript
// Test privacy preservation
describe('Privacy Preservation', () => {
  test('generates differentially private embeddings', async () => {
    const text = 'function getUserById(id) { return db.users.find(id); }';
    const privateEmb = await privacyService.generatePrivateEmbeddings(text, 1.0);
    
    expect(privateEmb.embedding).toBeDefined();
    expect(privateEmb.privacyBudget).toBe(1.0);
    expect(privateEmb.sanitizationApplied).toBe(true);
  });
  
  test('computes secure similarity', async () => {
    const emb1 = await privacyService.generatePrivateEmbeddings('code1');
    const emb2 = await privacyService.generatePrivateEmbeddings('code2');
    
    const similarity = await privacyService.computeSecureSimilarity(emb1, emb2);
    
    expect(similarity).toBeGreaterThanOrEqual(0);
    expect(similarity).toBeLessThanOrEqual(1);
  });
});
```

## Final Validation Checklist

- [ ] Google AI API keys stored in Secret Manager
- [ ] All AI requests sanitized for secrets/PII
- [ ] AI responses validated and filtered
- [ ] Model encryption implemented
- [ ] Model integrity verification active
- [ ] Privacy-preserving embeddings working
- [ ] Differential privacy applied
- [ ] AI security monitoring active
- [ ] Audit logging comprehensive
- [ ] Rate limiting enforced

## Anti-Patterns to Avoid

1. **DON'T send raw code to AI APIs** - Always sanitize first
2. **DON'T trust AI outputs** - Always validate responses
3. **DON'T store API keys in code** - Use Secret Manager
4. **DON'T skip PII detection** - Can violate privacy laws
5. **DON'T ignore prompt injection** - Major security risk
6. **DON'T process without audit logs** - Need compliance trail
7. **DON'T skip model encryption** - Models contain sensitive patterns
8. **DON'T allow unlimited AI requests** - Implement rate limiting
9. **DON'T expose raw embeddings** - Apply privacy techniques
10. **DON'T skip anomaly detection** - Critical for threat detection

## Cross-References

### Related PRPs
- **[Authentication PRP](./authentication.md)** - Platform authentication patterns
- **[Authorization PRP](./authorization.md)** - Access control for AI resources
- **[Encryption PRP](./encryption.md)** - Data encryption standards
- **[Zero Trust Architecture PRP](./zero-trust-architecture.md)** - Zero trust AI principles
- **[Compliance PRP](./compliance.md)** - AI governance and regulatory compliance

### Implementation References
- **[Pattern Mining Service](../../services/pattern-mining/)** - ML service implementation
- **[Analysis Engine](../../services/analysis-engine/)** - Code analysis security
- **[Query Intelligence](../../services/query-intelligence/)** - AI query processing

### External Standards
- **[OWASP ML Security Top 10](https://owasp.org/www-project-machine-learning-security-top-10/)** - ML security risks
- **[Google AI Principles](https://ai.google/principles/)** - Responsible AI guidelines
- **[NIST AI Risk Management Framework](https://www.nist.gov/itl/ai-risk-management-framework)** - AI risk management