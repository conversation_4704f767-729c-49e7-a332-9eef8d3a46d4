# Pattern Mining Service - Enterprise Operations Guide

## Table of Contents
1. [Executive Summary](#executive-summary)
2. [Architecture Overview](#architecture-overview)
3. [Deployment Procedures](#deployment-procedures)
4. [Google AI Platform Integration](#google-ai-platform-integration)
5. [Database Operations](#database-operations)
6. [Monitoring and Alerting](#monitoring-and-alerting)
7. [Troubleshooting Guide](#troubleshooting-guide)
8. [Incident Response Procedures](#incident-response-procedures)
9. [Scaling Operations](#scaling-operations)
10. [Performance Tuning](#performance-tuning)
11. [Security Operations](#security-operations)
12. [Disaster Recovery](#disaster-recovery)
13. [Cost Optimization](#cost-optimization)
14. [Maintenance Procedures](#maintenance-procedures)

---

## Executive Summary

The CCL Enterprise AI Platform's Pattern Mining Service is a production-grade, GPU-accelerated system deployed on Google Kubernetes Engine (GKE) with comprehensive AI/ML capabilities. This guide provides operational procedures for maintaining 99.9% uptime while processing millions of code patterns daily.

### Key Operational Metrics
- **Availability Target**: 99.9% uptime
- **Response Time**: <25ms (p95)
- **Processing Capacity**: 1M+ LOC/minute
- **GPU Utilization**: >70%
- **Cost Target**: <$0.02 per repository

---

## Architecture Overview

### Production Infrastructure
```
┌─────────────────────────────────────────────────────────────┐
│                    External Load Balancer                    │
│                   (patterns.episteme.ai)                     │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                    GKE Autopilot Cluster                    │
│  ┌─────────────────────────┐  ┌─────────────────────────┐  │
│  │   CPU Node Pool (n=10)  │  │  GPU Node Pool (n=5)    │  │
│  │   - Pattern Mining API   │  │  - ML Model Inference   │  │
│  │   - Feature Extraction   │  │  - GPU Acceleration     │  │
│  │   - Cache Management     │  │  - Batch Processing     │  │
│  └─────────────────────────┘  └─────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                    Google Cloud Services                     │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────────┐  │
│  │   BigQuery   │  │ Redis Cache  │  │  Google AI APIs  │  │
│  │  Analytics   │  │  Distributed │  │  - Gemini 2.5    │  │
│  │   Storage    │  │   Caching    │  │  - Vertex AI     │  │
│  └──────────────┘  └──────────────┘  └──────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### Service Components
1. **Pattern Mining API**: FastAPI-based REST/WebSocket service
2. **ML Pipeline**: Ray-distributed GPU-accelerated processing
3. **Cache Layer**: Redis cluster with intelligent caching
4. **Analytics Engine**: BigQuery with continuous queries
5. **AI Integration**: Google Gemini 2.5 Pro for pattern analysis

---

## Deployment Procedures

### Prerequisites Validation
```bash
#!/bin/bash
# validate-prerequisites.sh

echo "Validating deployment prerequisites..."

# Check GCloud CLI
if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud CLI not found"
    exit 1
fi

# Check kubectl
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl not found"
    exit 1
fi

# Validate GCP project
PROJECT_ID=$(gcloud config get-value project)
if [ -z "$PROJECT_ID" ]; then
    echo "❌ No GCP project set"
    exit 1
fi

# Check required APIs
REQUIRED_APIS=(
    "container.googleapis.com"
    "bigquery.googleapis.com"
    "aiplatform.googleapis.com"
    "redis.googleapis.com"
)

for api in "${REQUIRED_APIS[@]}"; do
    if ! gcloud services list --enabled | grep -q "$api"; then
        echo "❌ API not enabled: $api"
        exit 1
    fi
done

echo "✅ All prerequisites validated"
```

### Production Deployment Process

#### 1. Pre-deployment Checks
```bash
# Run pre-deployment validation
./scripts/pre-deploy-check.sh

# Expected output:
# ✅ Docker images built and scanned
# ✅ Security vulnerabilities: 0 critical, 0 high
# ✅ All tests passing (coverage: 96.5%)
# ✅ Configuration validated
# ✅ Database migrations ready
```

#### 2. Blue-Green Deployment
```bash
# Deploy to staging (green) environment
kubectl apply -f k8s/environments/staging/ -n pattern-mining-staging

# Validate staging deployment
./scripts/validate-staging.sh

# Switch traffic to new version
kubectl patch service pattern-mining-service \
  -n pattern-mining \
  -p '{"spec":{"selector":{"version":"v2.0.0"}}}'

# Monitor deployment
kubectl rollout status deployment/pattern-mining-service -n pattern-mining
```

#### 3. Canary Deployment (Alternative)
```yaml
# k8s/canary-deployment.yaml
apiVersion: flagger.app/v1beta1
kind: Canary
metadata:
  name: pattern-mining-canary
  namespace: pattern-mining
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: pattern-mining-service
  service:
    port: 8000
  analysis:
    interval: 1m
    threshold: 10
    maxWeight: 50
    stepWeight: 10
    metrics:
    - name: request-success-rate
      thresholdRange:
        min: 99
      interval: 1m
    - name: request-duration
      thresholdRange:
        max: 25
      interval: 1m
```

### Rollback Procedures
```bash
#!/bin/bash
# rollback.sh

# Quick rollback to previous version
kubectl rollout undo deployment/pattern-mining-service -n pattern-mining

# Rollback to specific revision
kubectl rollout undo deployment/pattern-mining-service \
  --to-revision=42 -n pattern-mining

# Verify rollback
kubectl get pods -n pattern-mining -l app=pattern-mining
kubectl logs -f deployment/pattern-mining-service -n pattern-mining
```

---

## Google AI Platform Integration

### Gemini API Configuration

#### Rate Limiting Management
```python
# src/pattern_mining/ml/gemini_rate_limiter.py
from datetime import datetime, timedelta
import asyncio
from typing import Dict, Optional

class GeminiRateLimiter:
    """Production rate limiter for Gemini API"""
    
    def __init__(self):
        self.limits = {
            'gemini-2.5-pro': {
                'requests_per_minute': 60,
                'tokens_per_minute': 1_000_000,
                'concurrent_requests': 10
            },
            'gemini-2.5-flash': {
                'requests_per_minute': 360,
                'tokens_per_minute': 4_000_000,
                'concurrent_requests': 20
            }
        }
        self.request_history: Dict[str, list] = {}
        self.token_usage: Dict[str, int] = {}
        self.semaphores: Dict[str, asyncio.Semaphore] = {}
        
    async def acquire(self, model: str, estimated_tokens: int):
        """Acquire rate limit slot"""
        if model not in self.semaphores:
            limit = self.limits[model]['concurrent_requests']
            self.semaphores[model] = asyncio.Semaphore(limit)
            
        # Check requests per minute
        await self._check_rpm_limit(model)
        
        # Check tokens per minute
        await self._check_tpm_limit(model, estimated_tokens)
        
        # Acquire concurrent request slot
        await self.semaphores[model].acquire()
        
    async def _check_rpm_limit(self, model: str):
        """Check and enforce requests per minute limit"""
        now = datetime.utcnow()
        minute_ago = now - timedelta(minutes=1)
        
        # Clean old requests
        if model in self.request_history:
            self.request_history[model] = [
                req for req in self.request_history[model]
                if req > minute_ago
            ]
        else:
            self.request_history[model] = []
            
        # Check limit
        rpm_limit = self.limits[model]['requests_per_minute']
        while len(self.request_history[model]) >= rpm_limit:
            # Wait until oldest request expires
            oldest = self.request_history[model][0]
            wait_time = (oldest + timedelta(minutes=1) - now).total_seconds()
            if wait_time > 0:
                await asyncio.sleep(wait_time)
            # Re-clean
            self.request_history[model] = [
                req for req in self.request_history[model]
                if req > datetime.utcnow() - timedelta(minutes=1)
            ]
            
        # Record request
        self.request_history[model].append(now)
```

#### Error Handling and Retries
```python
# src/pattern_mining/ml/gemini_client.py
import backoff
from google.api_core import exceptions

class ProductionGeminiClient:
    """Production-ready Gemini client with comprehensive error handling"""
    
    @backoff.on_exception(
        backoff.expo,
        (exceptions.ResourceExhausted, exceptions.ServiceUnavailable),
        max_tries=5,
        max_time=300
    )
    async def analyze_pattern(self, code: str, context: Dict) -> Dict:
        """Analyze code pattern with automatic retries"""
        try:
            # Acquire rate limit
            await self.rate_limiter.acquire(
                self.model,
                self._estimate_tokens(code)
            )
            
            # Make API call
            response = await self._call_api(code, context)
            
            # Validate response
            if not self._validate_response(response):
                raise ValueError("Invalid API response")
                
            return response
            
        except exceptions.InvalidArgument as e:
            # Log and return fallback
            logger.error(f"Invalid argument: {e}")
            return self._get_fallback_response(code)
            
        except exceptions.PermissionDenied as e:
            # Alert ops team
            await self.alert_manager.send_alert(
                severity="critical",
                message=f"Gemini API permission denied: {e}"
            )
            raise
            
        finally:
            # Release semaphore
            if hasattr(self, 'rate_limiter'):
                self.rate_limiter.release(self.model)
```

### Operational Monitoring for AI APIs

#### Metrics Collection
```yaml
# monitoring/gemini-metrics.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: gemini-metrics-config
  namespace: pattern-mining
data:
  metrics.yaml: |
    metrics:
      - name: gemini_api_latency
        type: histogram
        buckets: [0.1, 0.5, 1, 2, 5, 10, 30]
        labels: [model, operation, status]
        
      - name: gemini_api_errors
        type: counter
        labels: [model, error_type, severity]
        
      - name: gemini_token_usage
        type: counter
        labels: [model, operation]
        
      - name: gemini_rate_limit_remaining
        type: gauge
        labels: [model, limit_type]
        
    alerts:
      - name: GeminiAPIHighErrorRate
        expr: |
          rate(gemini_api_errors[5m]) > 0.05
        severity: warning
        annotations:
          summary: "High Gemini API error rate"
          
      - name: GeminiAPIRateLimitApproaching
        expr: |
          gemini_rate_limit_remaining < 10
        severity: warning
        annotations:
          summary: "Approaching Gemini API rate limit"
```

---

## Database Operations

### BigQuery Operations

#### Schema Management
```sql
-- migrations/v2.0.0_pattern_tables.sql
CREATE OR REPLACE TABLE `pattern_detection_v2.patterns` (
  pattern_id STRING NOT NULL,
  repository_id STRING NOT NULL,
  detected_at TIMESTAMP NOT NULL,
  pattern_type STRING NOT NULL,
  confidence FLOAT64 NOT NULL,
  
  -- Pattern details
  code_snippet STRING,
  file_path STRING,
  start_line INT64,
  end_line INT64,
  
  -- ML features
  embedding ARRAY<FLOAT64>,
  feature_vector ARRAY<FLOAT64>,
  
  -- Metadata
  language STRING,
  framework STRING,
  complexity_score FLOAT64,
  
  -- AI analysis
  gemini_analysis JSON,
  security_score FLOAT64,
  quality_score FLOAT64,
  
  -- Partitioning
  created_date DATE
)
PARTITION BY created_date
CLUSTER BY pattern_type, repository_id
OPTIONS(
  description="Pattern detection results with AI analysis",
  partition_expiration_days=90
);

-- Create materialized view for analytics
CREATE MATERIALIZED VIEW `pattern_detection_v2.pattern_analytics_mv`
AS
SELECT
  pattern_type,
  language,
  DATE(detected_at) as detection_date,
  COUNT(*) as pattern_count,
  AVG(confidence) as avg_confidence,
  AVG(security_score) as avg_security_score,
  APPROX_QUANTILES(confidence, 100)[OFFSET(95)] as p95_confidence
FROM
  `pattern_detection_v2.patterns`
WHERE
  detected_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
GROUP BY
  pattern_type, language, detection_date;
```

#### Continuous Query Monitoring
```python
# src/pattern_mining/database/bigquery_monitor.py
class BigQueryOperationsMonitor:
    """Monitor BigQuery operations and performance"""
    
    async def monitor_query_performance(self):
        """Monitor query performance and costs"""
        query = """
        SELECT
          user_email,
          query,
          total_bytes_processed,
          total_slot_ms,
          creation_time,
          end_time,
          TIMESTAMP_DIFF(end_time, creation_time, SECOND) as duration_seconds,
          total_bytes_billed / POW(10, 9) * 5 as estimated_cost_usd
        FROM
          `region-us.INFORMATION_SCHEMA.JOBS_BY_PROJECT`
        WHERE
          creation_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
          AND job_type = 'QUERY'
          AND state = 'DONE'
        ORDER BY
          total_bytes_processed DESC
        LIMIT 100
        """
        
        results = await self.client.query(query)
        
        for row in results:
            if row.estimated_cost_usd > 10:
                await self.alert_manager.send_alert(
                    severity="warning",
                    message=f"Expensive query detected: ${row.estimated_cost_usd:.2f}",
                    details={
                        "query": row.query[:200],
                        "user": row.user_email,
                        "bytes_processed": row.total_bytes_processed
                    }
                )
```

### Redis Cache Operations

#### Cache Warming Strategy
```python
# src/pattern_mining/cache/cache_warmer.py
class CacheWarmer:
    """Production cache warming strategies"""
    
    async def warm_pattern_cache(self):
        """Warm cache with frequently accessed patterns"""
        # Get top patterns from BigQuery
        query = """
        SELECT
          pattern_id,
          pattern_type,
          code_snippet,
          gemini_analysis
        FROM
          `pattern_detection_v2.patterns`
        WHERE
          detected_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
        ORDER BY
          access_count DESC
        LIMIT 1000
        """
        
        patterns = await self.bigquery_client.query(query)
        
        # Warm cache in batches
        batch_size = 100
        for i in range(0, len(patterns), batch_size):
            batch = patterns[i:i + batch_size]
            await self._warm_batch(batch)
            
    async def _warm_batch(self, patterns: List[Dict]):
        """Warm a batch of patterns"""
        pipeline = self.redis_client.pipeline()
        
        for pattern in patterns:
            key = f"pattern:{pattern['pattern_id']}"
            value = json.dumps(pattern)
            pipeline.setex(key, 3600, value)  # 1 hour TTL
            
        await pipeline.execute()
```

#### Cache Monitoring
```yaml
# monitoring/redis-dashboard.json
{
  "dashboard": {
    "title": "Redis Cache Operations",
    "panels": [
      {
        "title": "Cache Hit Rate",
        "targets": [{
          "expr": "rate(redis_keyspace_hits_total[5m]) / (rate(redis_keyspace_hits_total[5m]) + rate(redis_keyspace_misses_total[5m]))"
        }]
      },
      {
        "title": "Memory Usage",
        "targets": [{
          "expr": "redis_memory_used_bytes / redis_memory_max_bytes"
        }]
      },
      {
        "title": "Evicted Keys",
        "targets": [{
          "expr": "rate(redis_evicted_keys_total[5m])"
        }]
      },
      {
        "title": "Connected Clients",
        "targets": [{
          "expr": "redis_connected_clients"
        }]
      }
    ]
  }
}
```

---

## Monitoring and Alerting

### Prometheus Configuration
```yaml
# monitoring/prometheus-rules.yaml
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: pattern-mining-alerts
  namespace: pattern-mining
spec:
  groups:
  - name: pattern-mining.rules
    interval: 30s
    rules:
    # API Performance
    - alert: HighAPILatency
      expr: |
        histogram_quantile(0.95, 
          rate(pattern_detection_api_latency_seconds_bucket[5m])
        ) > 0.025
      for: 5m
      labels:
        severity: warning
        team: platform
      annotations:
        summary: "API latency exceeds 25ms (p95)"
        description: "95th percentile latency is {{ $value }}s"
        
    # GPU Utilization
    - alert: LowGPUUtilization
      expr: |
        avg(pattern_detection_gpu_utilization_percent) < 70
      for: 15m
      labels:
        severity: warning
        team: ml
      annotations:
        summary: "GPU utilization below 70%"
        description: "Current utilization: {{ $value }}%"
        
    # Model Performance
    - alert: ModelConfidenceDrift
      expr: |
        abs(
          avg(pattern_detection_model_confidence) - 
          avg_over_time(pattern_detection_model_confidence[24h])
        ) > 0.1
      for: 30m
      labels:
        severity: critical
        team: ml
      annotations:
        summary: "Model confidence drift detected"
        description: "Confidence deviation: {{ $value }}"
        
    # Resource Usage
    - alert: HighMemoryUsage
      expr: |
        container_memory_usage_bytes{pod=~"pattern-mining-.*"} / 
        container_spec_memory_limit_bytes{pod=~"pattern-mining-.*"} > 0.9
      for: 5m
      labels:
        severity: warning
        team: platform
      annotations:
        summary: "High memory usage in {{ $labels.pod }}"
        description: "Memory usage: {{ $value | humanizePercentage }}"
```

### Custom Metrics Implementation
```python
# src/pattern_mining/monitoring/custom_metrics.py
from prometheus_client import Counter, Histogram, Gauge, Info
import time

# Pattern detection metrics
pattern_detections = Counter(
    'pattern_detections_total',
    'Total number of patterns detected',
    ['pattern_type', 'language', 'confidence_bucket']
)

detection_latency = Histogram(
    'pattern_detection_latency_seconds',
    'Pattern detection latency',
    ['operation', 'model'],
    buckets=[0.01, 0.025, 0.05, 0.1, 0.5, 1.0, 2.5, 5.0, 10.0]
)

gpu_utilization = Gauge(
    'pattern_detection_gpu_utilization_percent',
    'GPU utilization percentage',
    ['gpu_id', 'model']
)

model_confidence = Histogram(
    'pattern_detection_model_confidence',
    'Model confidence distribution',
    ['model', 'pattern_type'],
    buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 0.95, 0.99, 1.0]
)

# Business metrics
repositories_analyzed = Counter(
    'repositories_analyzed_total',
    'Total repositories analyzed',
    ['language', 'size_category']
)

api_usage = Counter(
    'pattern_api_usage_total',
    'API usage by endpoint and client',
    ['endpoint', 'client_id', 'status_code']
)

# Cost tracking
cloud_costs = Gauge(
    'pattern_detection_cloud_costs_usd',
    'Cloud service costs in USD',
    ['service', 'resource_type']
)
```

### Grafana Dashboards

#### Executive Dashboard
```json
{
  "dashboard": {
    "title": "Pattern Mining - Executive View",
    "panels": [
      {
        "title": "Service Health Score",
        "targets": [{
          "expr": "(1 - rate(pattern_api_errors_total[5m])) * 100"
        }]
      },
      {
        "title": "Daily Pattern Detections",
        "targets": [{
          "expr": "increase(pattern_detections_total[1d])"
        }]
      },
      {
        "title": "Cost per Repository",
        "targets": [{
          "expr": "sum(cloud_costs) / sum(increase(repositories_analyzed_total[1d]))"
        }]
      },
      {
        "title": "SLA Compliance",
        "targets": [{
          "expr": "avg_over_time(up{job='pattern-mining'}[30d]) * 100"
        }]
      }
    ]
  }
}
```

---

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. High API Latency
```bash
# Diagnosis script
#!/bin/bash
echo "Diagnosing high API latency..."

# Check pod resources
kubectl top pods -n pattern-mining -l app=pattern-mining

# Check cache hit rate
redis-cli --cluster call redis-cluster:6379 INFO stats | grep keyspace

# Check GPU utilization
kubectl exec -it deployment/pattern-mining-gpu-service -- nvidia-smi

# Analyze slow queries
kubectl logs -n pattern-mining -l app=pattern-mining --tail=1000 | \
  grep "slow_query" | jq .
```

**Resolution Steps:**
1. Scale up pods if CPU/memory constrained
2. Increase cache TTL for frequently accessed patterns
3. Enable GPU acceleration for complex patterns
4. Optimize BigQuery queries with proper clustering

#### 2. Model Loading Failures
```python
# src/scripts/diagnose_model_loading.py
async def diagnose_model_loading():
    """Diagnose model loading issues"""
    
    print("Checking model availability...")
    
    # Check model cache
    cache_dir = "/models"
    if not os.path.exists(cache_dir):
        print("❌ Model cache directory missing")
        return
        
    # Check model files
    required_models = [
        "starcoder2-15b",
        "graphcodebert-base",
        "codebert-base"
    ]
    
    for model in required_models:
        model_path = f"{cache_dir}/{model}"
        if not os.path.exists(model_path):
            print(f"❌ Model missing: {model}")
            # Attempt download
            await download_model(model)
        else:
            # Verify model integrity
            checksum = calculate_checksum(model_path)
            if checksum != EXPECTED_CHECKSUMS[model]:
                print(f"❌ Model corrupted: {model}")
                await redownload_model(model)
```

#### 3. BigQuery Connection Issues
```bash
# Check service account permissions
gcloud projects get-iam-policy $PROJECT_ID \
  --flatten="bindings[].members" \
  --filter="bindings.members:serviceAccount:pattern-mining-sa@*"

# Test BigQuery connection
kubectl exec -it deployment/pattern-mining-service -- \
  bq query --use_legacy_sql=false "SELECT 1"

# Check BigQuery job history
bq ls -j -a -n 50 | grep FAILURE
```

#### 4. Memory Pressure
```yaml
# k8s/memory-optimization.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: memory-optimization
  namespace: pattern-mining
data:
  settings.yaml: |
    # Reduce memory usage
    batch_processing:
      max_batch_size: 50  # Reduced from 100
      max_concurrent_batches: 3  # Reduced from 5
      
    model_loading:
      lazy_loading: true
      offload_to_disk: true
      max_models_in_memory: 2
      
    caching:
      max_memory_mb: 1024  # Limit cache memory
      eviction_policy: "lru"
      
    garbage_collection:
      threshold: 0.8  # Trigger GC at 80% memory
      aggressive_mode: true
```

---

## Incident Response Procedures

### Incident Classification

| Severity | Response Time | Examples |
|----------|--------------|----------|
| P1 - Critical | 15 minutes | Service down, data loss |
| P2 - High | 1 hour | Performance degradation >50% |
| P3 - Medium | 4 hours | Non-critical feature failure |
| P4 - Low | 24 hours | Minor bugs, cosmetic issues |

### P1 Incident Response Playbook

#### 1. Initial Response (0-15 minutes)
```bash
#!/bin/bash
# p1-response.sh

# Page on-call engineer
./scripts/page-oncall.sh "P1: Pattern Mining Service Down"

# Create incident channel
slack-cli create-channel "incident-$(date +%Y%m%d-%H%M%S)"

# Start incident timeline
echo "$(date): Incident detected" >> incident.log

# Gather initial diagnostics
kubectl get pods -n pattern-mining -o wide
kubectl describe pods -n pattern-mining
kubectl logs -n pattern-mining -l app=pattern-mining --tail=500
```

#### 2. Triage and Mitigation (15-30 minutes)
```python
# src/incident/auto_mitigation.py
class AutoMitigation:
    """Automated incident mitigation"""
    
    async def execute_p1_mitigation(self):
        """Execute P1 mitigation steps"""
        
        # 1. Enable circuit breaker
        await self.enable_circuit_breaker()
        
        # 2. Scale up healthy pods
        healthy_pods = await self.get_healthy_pods()
        if len(healthy_pods) < 3:
            await self.scale_deployment(5)
            
        # 3. Redirect traffic to healthy pods
        await self.update_service_endpoints(healthy_pods)
        
        # 4. Clear problematic cache entries
        await self.clear_cache_namespace("pattern:*")
        
        # 5. Enable fallback mode
        await self.enable_fallback_models()
        
        # 6. Notify stakeholders
        await self.send_incident_notification({
            "severity": "P1",
            "status": "Mitigation in progress",
            "impact": await self.calculate_impact()
        })
```

#### 3. Root Cause Analysis Template
```markdown
# Incident Post-Mortem: [INCIDENT-ID]

## Summary
- **Date**: [DATE]
- **Duration**: [DURATION]
- **Impact**: [DESCRIPTION OF IMPACT]
- **Root Cause**: [ROOT CAUSE]

## Timeline
- [TIME]: Initial detection
- [TIME]: On-call paged
- [TIME]: Mitigation started
- [TIME]: Service restored
- [TIME]: Incident resolved

## Root Cause Analysis
### What Happened
[Detailed description]

### Why It Happened
[5 Whys analysis]

### Contributing Factors
- [Factor 1]
- [Factor 2]

## Action Items
- [ ] [Action 1] - Owner: [NAME] - Due: [DATE]
- [ ] [Action 2] - Owner: [NAME] - Due: [DATE]

## Lessons Learned
- [Lesson 1]
- [Lesson 2]
```

---

## Scaling Operations

### Horizontal Pod Autoscaling
```yaml
# k8s/advanced-hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: pattern-mining-advanced-hpa
  namespace: pattern-mining
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: pattern-mining-service
  minReplicas: 3
  maxReplicas: 50
  metrics:
  # CPU-based scaling
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  # Memory-based scaling
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  # Custom metrics
  - type: Pods
    pods:
      metric:
        name: pattern_detection_queue_depth
      target:
        type: AverageValue
        averageValue: "30"
  - type: External
    external:
      metric:
        name: pubsub_queue_depth
        selector:
          matchLabels:
            queue: "pattern-analysis"
      target:
        type: Value
        value: "100"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Min
    scaleUp:
      stabilizationWindowSeconds: 0
      policies:
      - type: Percent
        value: 100
        periodSeconds: 30
      - type: Pods
        value: 10
        periodSeconds: 30
      selectPolicy: Max
```

### Vertical Pod Autoscaling
```yaml
# k8s/vpa.yaml
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: pattern-mining-vpa
  namespace: pattern-mining
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: pattern-mining-service
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: pattern-mining
      minAllowed:
        cpu: 500m
        memory: 1Gi
      maxAllowed:
        cpu: 4000m
        memory: 16Gi
      controlledResources: ["cpu", "memory"]
```

### GPU Scaling Strategy
```python
# src/scaling/gpu_autoscaler.py
class GPUAutoscaler:
    """Intelligent GPU scaling based on workload"""
    
    async def scale_gpu_nodes(self):
        """Scale GPU nodes based on queue depth and complexity"""
        
        # Get current metrics
        queue_depth = await self.get_queue_depth()
        avg_complexity = await self.get_average_complexity()
        gpu_utilization = await self.get_gpu_utilization()
        
        # Calculate required GPUs
        required_gpus = self.calculate_required_gpus(
            queue_depth, 
            avg_complexity,
            gpu_utilization
        )
        
        # Scale node pool
        current_gpus = await self.get_current_gpu_count()
        if required_gpus > current_gpus:
            await self.scale_up_gpu_nodes(required_gpus - current_gpus)
        elif required_gpus < current_gpus * 0.7:  # Scale down threshold
            await self.scale_down_gpu_nodes(current_gpus - required_gpus)
            
    def calculate_required_gpus(self, queue_depth, complexity, utilization):
        """Calculate required GPU count"""
        # Base calculation
        base_gpus = math.ceil(queue_depth / 1000)  # 1 GPU per 1000 items
        
        # Adjust for complexity
        complexity_factor = 1.0 + (complexity - 0.5) * 2
        adjusted_gpus = base_gpus * complexity_factor
        
        # Consider current utilization
        if utilization > 0.9:
            adjusted_gpus *= 1.5  # Add 50% more capacity
            
        return max(1, min(int(adjusted_gpus), 20))  # Cap at 20 GPUs
```

---

## Performance Tuning

### Query Optimization
```sql
-- Optimize pattern detection queries
CREATE OR REPLACE TABLE FUNCTION `pattern_detection_v2.optimize_pattern_query`(
  start_date DATE,
  end_date DATE,
  pattern_types ARRAY<STRING>
)
AS (
  WITH ranked_patterns AS (
    SELECT
      pattern_id,
      pattern_type,
      confidence,
      ROW_NUMBER() OVER (
        PARTITION BY repository_id, pattern_type 
        ORDER BY confidence DESC
      ) as rank
    FROM
      `pattern_detection_v2.patterns`
    WHERE
      created_date BETWEEN start_date AND end_date
      AND pattern_type IN UNNEST(pattern_types)
  )
  SELECT * FROM ranked_patterns WHERE rank = 1
);

-- Create clustering recommendation
CALL BQ.RECOMMEND_CLUSTER_COLUMNS(
  'pattern_detection_v2.patterns',
  'repository_id,pattern_type,language'
);
```

### Model Optimization
```python
# src/optimization/model_optimizer.py
class ModelOptimizer:
    """Optimize ML model performance"""
    
    async def optimize_model_inference(self):
        """Apply model optimization techniques"""
        
        # 1. Quantization
        quantized_model = self.quantize_model(
            self.model,
            quantization_config={
                "bits": 8,
                "group_size": 128,
                "symmetric": True
            }
        )
        
        # 2. Compilation with TorchScript
        scripted_model = torch.jit.script(quantized_model)
        scripted_model = torch.jit.optimize_for_inference(scripted_model)
        
        # 3. ONNX conversion for faster inference
        dummy_input = torch.randn(1, 512)
        torch.onnx.export(
            scripted_model,
            dummy_input,
            "optimized_model.onnx",
            opset_version=17,
            do_constant_folding=True,
            input_names=['input'],
            output_names=['output'],
            dynamic_axes={'input': {0: 'batch_size'}}
        )
        
        # 4. TensorRT optimization for GPU
        if torch.cuda.is_available():
            trt_model = self.convert_to_tensorrt(
                "optimized_model.onnx",
                precision="fp16",
                max_batch_size=64
            )
            
        return trt_model
```

### Caching Optimization
```python
# src/cache/intelligent_cache.py
class IntelligentCache:
    """Advanced caching with ML-based eviction"""
    
    def __init__(self):
        self.access_predictor = self.load_access_predictor()
        self.value_estimator = self.load_value_estimator()
        
    async def adaptive_caching(self, key: str, value: Any):
        """Cache with ML-predicted TTL"""
        
        # Predict access pattern
        access_probability = self.access_predictor.predict(key)
        value_score = self.value_estimator.estimate(value)
        
        # Calculate optimal TTL
        base_ttl = 3600  # 1 hour
        ttl = int(base_ttl * access_probability * value_score)
        
        # Set with computed TTL
        await self.redis_client.setex(key, ttl, value)
        
        # Update access patterns for learning
        await self.update_access_patterns(key)
```

---

## Security Operations

### Security Monitoring
```yaml
# monitoring/security-alerts.yaml
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: security-alerts
  namespace: pattern-mining
spec:
  groups:
  - name: security.rules
    rules:
    - alert: SuspiciousAPIAccess
      expr: |
        rate(pattern_api_requests_total{status_code=~"401|403"}[5m]) > 10
      labels:
        severity: warning
        team: security
      annotations:
        summary: "High rate of unauthorized API access"
        
    - alert: AnomalousDataAccess
      expr: |
        rate(bigquery_bytes_scanned[5m]) > 1e12  # 1TB
      labels:
        severity: critical
        team: security
      annotations:
        summary: "Anomalous data access detected"
        
    - alert: PotentialDataExfiltration
      expr: |
        rate(pattern_api_response_size_bytes[5m]) > 1e9  # 1GB
      labels:
        severity: critical
        team: security
      annotations:
        summary: "Potential data exfiltration detected"
```

### Security Incident Response
```python
# src/security/incident_response.py
class SecurityIncidentHandler:
    """Handle security incidents"""
    
    async def handle_security_incident(self, incident_type: str, details: Dict):
        """Respond to security incidents"""
        
        incident_id = str(uuid.uuid4())
        logger.security(f"Security incident {incident_id}: {incident_type}")
        
        # 1. Immediate containment
        if incident_type == "data_exfiltration":
            await self.block_suspicious_ips(details['source_ips'])
            await self.revoke_api_keys(details['api_keys'])
            
        elif incident_type == "unauthorized_access":
            await self.enable_strict_auth_mode()
            await self.force_reauthentication()
            
        # 2. Investigation
        evidence = await self.collect_forensic_data(incident_id, details)
        
        # 3. Notification
        await self.notify_security_team(incident_id, evidence)
        
        # 4. Remediation
        await self.apply_security_patches(incident_type)
        
        return incident_id
```

---

## Disaster Recovery

### Backup Procedures
```bash
#!/bin/bash
# backup-pattern-mining.sh

# Backup BigQuery datasets
bq extract \
  --destination_format=AVRO \
  --compression=SNAPPY \
  pattern_detection_v2.patterns \
  gs://episteme-backups/bigquery/patterns/$(date +%Y%m%d)/*.avro

# Backup Redis data
kubectl exec -n pattern-mining redis-master-0 -- \
  redis-cli BGSAVE

# Backup Kubernetes configurations
kubectl get all -n pattern-mining -o yaml > \
  k8s-backup-$(date +%Y%m%d).yaml

# Backup ML models
gsutil -m cp -r /models/* \
  gs://episteme-backups/models/$(date +%Y%m%d)/

# Backup configurations
kubectl get configmaps,secrets -n pattern-mining -o yaml | \
  kubectl neat > configs-backup-$(date +%Y%m%d).yaml
```

### Recovery Procedures
```python
# src/disaster_recovery/recovery_orchestrator.py
class DisasterRecoveryOrchestrator:
    """Orchestrate disaster recovery procedures"""
    
    async def execute_recovery_plan(self, failure_type: str):
        """Execute recovery based on failure type"""
        
        if failure_type == "region_failure":
            return await self.failover_to_secondary_region()
            
        elif failure_type == "data_corruption":
            return await self.restore_from_backup()
            
        elif failure_type == "service_failure":
            return await self.rebuild_service()
            
    async def failover_to_secondary_region(self):
        """Failover to secondary region"""
        
        logger.info("Initiating regional failover...")
        
        # 1. Update DNS to point to secondary region
        await self.update_dns_records("us-east1")
        
        # 2. Restore data in secondary region
        await self.restore_bigquery_data("us-east1")
        
        # 3. Deploy services to secondary region
        await self.deploy_to_region("us-east1")
        
        # 4. Verify service health
        await self.verify_service_health("us-east1")
        
        # 5. Update monitoring
        await self.update_monitoring_targets("us-east1")
        
        return "Failover completed successfully"
```

### RTO/RPO Targets
| Scenario | RTO | RPO | Strategy |
|----------|-----|-----|----------|
| Pod Failure | 30s | 0 | Kubernetes auto-recovery |
| Node Failure | 2m | 0 | Node auto-replacement |
| Zone Failure | 5m | 0 | Multi-zone deployment |
| Region Failure | 30m | 5m | Cross-region replication |
| Data Corruption | 1h | 1h | Point-in-time recovery |

---

## Cost Optimization

### Cost Monitoring
```python
# src/cost_optimization/cost_monitor.py
class CostMonitor:
    """Monitor and optimize cloud costs"""
    
    async def analyze_costs(self):
        """Analyze costs and identify optimization opportunities"""
        
        # Get cost data from BigQuery
        query = """
        SELECT
          service.description as service,
          sku.description as resource,
          SUM(cost) as total_cost,
          SUM(usage.amount) as usage_amount,
          usage.unit as usage_unit
        FROM
          `cloud-billing-dataset.billing.gcp_billing_export_v1`
        WHERE
          DATE(usage_start_time) >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)
          AND project.id = 'episteme-prod'
        GROUP BY
          service, resource, usage_unit
        ORDER BY
          total_cost DESC
        LIMIT 20
        """
        
        results = await self.bigquery_client.query(query)
        
        # Identify optimization opportunities
        opportunities = []
        for row in results:
            if "GPU" in row.resource and row.total_cost > 1000:
                opportunities.append({
                    "type": "gpu_optimization",
                    "resource": row.resource,
                    "current_cost": row.total_cost,
                    "recommendation": "Consider using preemptible GPUs"
                })
                
        return opportunities
```

### Cost Optimization Strategies
```yaml
# k8s/cost-optimization.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: cost-optimization-config
  namespace: pattern-mining
data:
  strategies.yaml: |
    # Preemptible instances
    node_pools:
      gpu_pool:
        preemptible: true
        max_price_per_hour: 2.0
        
    # Resource rightsizing
    resource_optimization:
      cpu_target_utilization: 0.8
      memory_target_utilization: 0.85
      
    # Storage optimization
    storage:
      bigquery:
        partition_expiration_days: 90
        clustering_fields: ["pattern_type", "repository_id"]
      redis:
        eviction_policy: "allkeys-lru"
        maxmemory: "10gb"
        
    # Scheduled scaling
    scheduled_scaling:
      - schedule: "0 22 * * 1-5"  # 10 PM weekdays
        min_replicas: 1
        max_replicas: 5
      - schedule: "0 6 * * 1-5"   # 6 AM weekdays
        min_replicas: 3
        max_replicas: 50
```

---

## Maintenance Procedures

### Regular Maintenance Tasks
```yaml
# k8s/cronjobs/maintenance.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: pattern-mining-maintenance
  namespace: pattern-mining
spec:
  schedule: "0 2 * * 0"  # Weekly at 2 AM Sunday
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: maintenance
            image: gcr.io/episteme-prod/pattern-mining-maintenance:latest
            command:
            - /bin/bash
            - -c
            - |
              # Clean up old patterns
              python -m src.maintenance.cleanup_old_patterns
              
              # Optimize BigQuery tables
              python -m src.maintenance.optimize_tables
              
              # Update ML model cache
              python -m src.maintenance.refresh_models
              
              # Clean Redis cache
              python -m src.maintenance.clean_cache
              
              # Generate maintenance report
              python -m src.maintenance.generate_report
```

### Version Upgrade Procedures
```python
# src/maintenance/rolling_upgrade.py
class RollingUpgradeManager:
    """Manage zero-downtime upgrades"""
    
    async def perform_rolling_upgrade(self, new_version: str):
        """Perform rolling upgrade with validation"""
        
        # 1. Pre-upgrade validation
        if not await self.validate_new_version(new_version):
            raise ValueError(f"Version {new_version} failed validation")
            
        # 2. Create canary deployment
        await self.deploy_canary(new_version, traffic_percent=5)
        
        # 3. Monitor canary metrics
        canary_healthy = await self.monitor_canary(duration_minutes=30)
        
        if not canary_healthy:
            await self.rollback_canary()
            raise RuntimeError("Canary deployment failed")
            
        # 4. Gradual rollout
        for traffic_percent in [25, 50, 75, 100]:
            await self.update_traffic_split(new_version, traffic_percent)
            await asyncio.sleep(300)  # 5 minutes between increases
            
            if not await self.check_health_metrics():
                await self.rollback_deployment()
                raise RuntimeError(f"Rollout failed at {traffic_percent}%")
                
        # 5. Cleanup old version
        await self.cleanup_old_deployment()
        
        return f"Successfully upgraded to {new_version}"
```

### Database Migration Procedures
```sql
-- migrations/v2.1.0_add_performance_indexes.sql
BEGIN TRANSACTION;

-- Add indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_patterns_repo_type_confidence 
ON `pattern_detection_v2.patterns` (repository_id, pattern_type, confidence);

-- Add materialized view for dashboard
CREATE MATERIALIZED VIEW IF NOT EXISTS `pattern_detection_v2.dashboard_stats_mv`
AS
SELECT
  DATE(detected_at) as date,
  COUNT(DISTINCT repository_id) as repositories_analyzed,
  COUNT(*) as patterns_detected,
  AVG(confidence) as avg_confidence,
  APPROX_QUANTILES(confidence, 100)[OFFSET(95)] as p95_confidence,
  SUM(CASE WHEN pattern_type = 'security' THEN 1 ELSE 0 END) as security_patterns
FROM
  `pattern_detection_v2.patterns`
WHERE
  detected_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 90 DAY)
GROUP BY
  date;

COMMIT;
```

---

## Appendices

### A. Emergency Contacts
| Role | Name | Phone | Email |
|------|------|-------|-------|
| Platform Lead | John Smith | ******-0100 | <EMAIL> |
| ML Lead | Sarah Johnson | ******-0101 | <EMAIL> |
| Security Lead | Mike Chen | ******-0102 | <EMAIL> |
| Google TAM | Lisa Park | ******-0103 | <EMAIL> |

### B. Useful Commands
```bash
# Quick health check
curl -s https://patterns.episteme.ai/health | jq .

# Get service metrics
curl -s https://patterns.episteme.ai/metrics | grep pattern_

# Check GPU status
kubectl exec -it deployment/pattern-mining-gpu-service -- nvidia-smi

# View recent errors
kubectl logs -n pattern-mining -l app=pattern-mining --since=1h | grep ERROR

# Force cache clear
kubectl exec -it redis-master-0 -- redis-cli FLUSHDB

# Emergency scale
kubectl scale deployment pattern-mining-service --replicas=10 -n pattern-mining
```

### C. Reference Architecture
- [Google Cloud Architecture Framework](https://cloud.google.com/architecture/framework)
- [Kubernetes Best Practices](https://kubernetes.io/docs/concepts/cluster-administration/manage-deployment/)
- [SRE Principles](https://sre.google/sre-book/table-of-contents/)

---

## Document Version
- **Version**: 2.0.0
- **Last Updated**: July 2025
- **Next Review**: October 2025
- **Owner**: Platform Engineering Team
- **Approval**: CTO, VP Engineering

---

*This operations guide is a living document. Please submit updates via pull request to the `episteme/docs` repository.*