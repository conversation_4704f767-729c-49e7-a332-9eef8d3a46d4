# PRP: Health Check Enhancement - Comprehensive Service Status Monitoring

**Created**: 2025-07-14
**Confidence Score**: 9/10
**Complexity**: Low-Medium
**Estimated Implementation**: 2-3 hours

## Goal
Add a comprehensive health check endpoint that reports the status of all critical services (Spanner, Redis, Tree-sitter language registry) with detailed metrics and response times, enabling better monitoring and debugging of the analysis engine.

## Why - Business Value
- **Operational Visibility**: Provides detailed insight into service health for operations teams
- **Debugging Support**: Helps identify which services are failing during issues
- **Monitoring Integration**: Enables better alerting and monitoring dashboards
- **Production Readiness**: Essential for production deployment health monitoring

## What - Technical Requirements
Implement GET /api/v1/health/detailed endpoint that returns comprehensive service status information.

### Success Criteria
- [ ] Endpoint responds within 500ms under normal conditions
- [ ] Returns JSON with status of Spanner, Redis, and Tree-sitter registry
- [ ] Includes response times and error counts for each service
- [ ] Implements proper error handling for service unavailability
- [ ] Adds Prometheus metrics for health check performance
- [ ] Does not impact performance of main analysis endpoints

## All Needed Context

### Documentation & References
```yaml
research_docs:
  - file: research/rust/axum.md
    why: [Web framework patterns and JSON response handling]
  - file: research/google-cloud/spanner.md
    why: [Database connection health check patterns]
  - file: research/databases/redis.md
    why: [Redis connection validation methods]

examples:
  - file: examples/analysis-engine/ast_parser.rs
    why: [Error handling patterns and Result types]
  - file: examples/analysis-engine/service_pattern.rs
    why: [Service structure and organization patterns]
  - file: examples/analysis-engine/api_handler.rs
    why: [API endpoint implementation patterns]

codebase_patterns:
  - location: services/analysis-engine/src/api/
    why: [Existing API structure and middleware patterns]
  - location: services/analysis-engine/src/storage/
    why: [Database and cache connection patterns]
```

### Current Codebase Structure
```bash
services/analysis-engine/src/
├── api/                    # Axum handlers and middleware
│   ├── mod.rs             # Router configuration
│   ├── health.rs          # Basic health endpoint (to enhance)
│   └── middleware/        # Authentication and rate limiting
├── services/               # Business logic services
├── storage/                # Database and cache layers
│   ├── spanner.rs         # Spanner connection management
│   └── redis.rs           # Redis connection management
├── models/                 # Domain models and schemas
└── metrics/                # Prometheus monitoring
```

### Desired Codebase Changes
```bash
services/analysis-engine/src/
├── api/
│   ├── health.rs          # ENHANCE: Add detailed health endpoint
│   └── mod.rs             # UPDATE: Add new route
├── services/
│   └── health_service.rs  # CREATE: Health check service logic
└── models/
    └── health.rs          # CREATE: Health response models
```

### Known Gotchas & Library Quirks
```rust
// CRITICAL: Episteme-specific patterns
// - Spanner connections use connection pooling - test pool health, not individual connections
// - Redis graceful degradation - service should work even if Redis is down
// - Tree-sitter language registry is initialized at startup - check if languages are loaded
// - Health checks should not block main service operations
// - Use timeouts to prevent health checks from hanging
```

## Implementation Blueprint

### Data Models and Structure
```rust
// Health response models ensuring type safety
#[derive(Debug, Serialize)]
pub struct DetailedHealthResponse {
    pub status: OverallStatus,
    pub timestamp: DateTime<Utc>,
    pub services: ServiceHealthMap,
    pub response_time_ms: u64,
}

#[derive(Debug, Serialize)]
pub struct ServiceHealth {
    pub status: ServiceStatus,
    pub response_time_ms: Option<u64>,
    pub error_message: Option<String>,
    pub last_success: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize)]
pub enum ServiceStatus {
    Healthy,
    Degraded,
    Unhealthy,
}
```

### Task List - Implementation Order
```yaml
Task 1: "Create health response models"
  - CREATE src/models/health.rs
  - DEFINE response structures with proper serialization
  - ADD validation and helper methods
  - PATTERN: Follow existing model patterns in models/

Task 2: "Implement health service logic"
  - CREATE src/services/health_service.rs
  - IMPLEMENT service health check methods
  - ADD timeout handling and error management
  - INTEGRATE with existing storage connections

Task 3: "Enhance health API endpoint"
  - MODIFY src/api/health.rs
  - ADD detailed health endpoint handler
  - IMPLEMENT proper error responses
  - FOLLOW existing API patterns

Task 4: "Add Prometheus metrics"
  - UPDATE src/metrics/mod.rs
  - ADD health check performance metrics
  - IMPLEMENT metric collection in health service
  - FOLLOW existing metrics patterns

Task 5: "Update routing configuration"
  - MODIFY src/api/mod.rs
  - ADD new health endpoint to router
  - ENSURE proper middleware application
  - TEST endpoint accessibility

Task 6: "Comprehensive testing"
  - CREATE unit tests for health service
  - ADD integration tests for endpoint
  - TEST error scenarios and timeouts
  - VALIDATE performance requirements
```

### Per-Task Implementation Details
```rust
// Task 1: Health Models
use serde::Serialize;
use chrono::{DateTime, Utc};
use std::collections::HashMap;

#[derive(Debug, Serialize)]
pub struct DetailedHealthResponse {
    pub status: OverallStatus,
    pub timestamp: DateTime<Utc>,
    pub services: HashMap<String, ServiceHealth>,
    pub response_time_ms: u64,
}

// Task 2: Health Service
pub struct HealthService {
    spanner_pool: Arc<SpannerPool>,
    redis_client: Arc<RedisClient>,
    language_registry: Arc<LanguageRegistry>,
}

impl HealthService {
    pub async fn check_all_services(&self) -> Result<DetailedHealthResponse> {
        let start_time = Instant::now();
        
        // PATTERN: Run health checks concurrently with timeouts
        let (spanner_health, redis_health, registry_health) = tokio::join!(
            self.check_spanner_health(),
            self.check_redis_health(),
            self.check_language_registry_health()
        );
        
        // PATTERN: Aggregate results and determine overall status
        let services = self.aggregate_service_health(spanner_health, redis_health, registry_health);
        let overall_status = self.determine_overall_status(&services);
        
        Ok(DetailedHealthResponse {
            status: overall_status,
            timestamp: Utc::now(),
            services,
            response_time_ms: start_time.elapsed().as_millis() as u64,
        })
    }
}
```

### Integration Points
```yaml
API:
  - route: "GET /api/v1/health/detailed"
  - middleware: "Apply existing middleware stack"
  - authentication: "Use public endpoint (no auth required)"

MONITORING:
  - metrics: "Add to src/metrics/mod.rs"
  - prometheus_metrics:
    - "health_check_duration_seconds"
    - "service_health_status"
    - "health_check_requests_total"

STORAGE:
  - spanner_integration: "Use existing connection pool from storage/spanner.rs"
  - redis_integration: "Use existing client from storage/redis.rs"
  - timeout_strategy: "5 second timeout per service check"
```

## Validation Loop

### Level 1: Syntax & Style
```bash
cargo fmt                           # Format code
cargo clippy -- -D warnings        # Lint with warnings as errors
cargo check                         # Type checking
```

### Level 2: Unit Tests
```rust
#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_health_service_all_healthy() {
        let health_service = create_test_health_service().await;
        let response = health_service.check_all_services().await.unwrap();
        assert_eq!(response.status, OverallStatus::Healthy);
    }
    
    #[tokio::test]
    async fn test_health_service_redis_down() {
        // Test graceful handling when Redis is unavailable
        let health_service = create_test_health_service_redis_down().await;
        let response = health_service.check_all_services().await.unwrap();
        assert_eq!(response.status, OverallStatus::Degraded);
    }
    
    #[tokio::test]
    async fn test_health_endpoint_response_time() {
        // Test that health check responds within 500ms
        let start = Instant::now();
        let response = health_service.check_all_services().await.unwrap();
        assert!(start.elapsed().as_millis() < 500);
    }
}
```

### Level 3: Integration Test
```bash
# Start the service
cargo run --bin analysis-engine &

# Test the detailed health endpoint
curl -X GET http://localhost:8001/api/v1/health/detailed \
  -H "Accept: application/json"

# Expected: JSON response with service statuses and response times
# Validate response structure and performance
```

## Final Validation Checklist
- [ ] All tests pass: `cargo test health`
- [ ] No linting errors: `cargo clippy`
- [ ] Endpoint responds within 500ms
- [ ] JSON response includes all required fields
- [ ] Graceful handling of service failures
- [ ] Prometheus metrics are collected
- [ ] No impact on main service performance
- [ ] Integration test successful

---

## Research Summary
- **Documentation Reviewed**: Axum web framework patterns, Spanner health checks, Redis validation
- **Examples Referenced**: AST parser error handling, service patterns, API handlers
- **Codebase Analysis**: Existing health endpoint, storage connections, metrics patterns
- **Integration Points**: API routing, middleware, monitoring integration

## Implementation Confidence
- **Context Completeness**: 9/10 - Comprehensive context with existing patterns
- **Pattern Clarity**: 9/10 - Clear examples and existing code structure
- **Validation Coverage**: 9/10 - Complete validation loops with performance testing
- **Risk Factors**: Low - Simple enhancement with well-established patterns

## Anti-Patterns to Avoid
- ❌ Don't block main service operations with health checks
- ❌ Don't use unwrap() for service connections - handle failures gracefully
- ❌ Don't skip timeout handling - prevent hanging health checks
- ❌ Don't ignore Redis unavailability - implement graceful degradation
- ❌ Don't skip performance validation - ensure <500ms response time
