# RESEARCH_COORDINATION_MASTER.md - Multi-Agent Research Orchestration

## FEATURE:
Orchestrate comprehensive research gathering using Context Engineering multi-agent patterns to collect fresh, official documentation for production readiness implementation. Deploy 6 specialized research agents to gather 30-100+ pages of documentation per technology area, ensuring evidence-based development with current best practices.

### Specific Requirements:
- **Multi-Agent Research**: Deploy 6 specialized research agents with parallel execution
- **Comprehensive Coverage**: 30-100+ pages per technology area (Rust, Python, GCP, ML/NLP, Security, Deployment)
- **Official Sources Only**: Use only official documentation pages and authoritative sources
- **Quality Validation**: Verify scraped content is complete and accurate, re-scrape on failure
- **Organized Storage**: Store research in technology-specific directories with metadata
- **Fresh Documentation**: Ensure all documentation reflects latest versions and best practices

### Success Criteria:
- [ ] 6 research agents deployed with specialized focus areas
- [ ] 200+ pages of official documentation scraped and organized
- [ ] Research directory populated with current, comprehensive documentation
- [ ] Quality validation completed for all scraped content
- [ ] Technology-specific research ready for PRP generation
- [ ] Evidence-based foundation established for production readiness implementation

## EXAMPLES:
Reference these Context Engineering research patterns:

- **research/README.md** - Research methodology and organization standards
- **examples/research/multi-agent-scraping.md** - Parallel research agent patterns
- **examples/research/quality-validation.md** - Documentation quality assurance
- **.claudedocs/reports/research-coordination.md** - Previous research coordination patterns

## DOCUMENTATION:
Consult these sources for research coordination:

### Context Engineering Guide References:
- **Multi-Agent Research Approach**: "Can you spin up multiple research agents and do this all at the same time"
- **Comprehensive Documentation**: 30-100+ pages scraped per technology for complete context
- **Official Sources Only**: Use only official documentation pages for accuracy
- **Quality Standards**: Verify content completeness and re-scrape on failure

### Research Targets:
- **Rust Ecosystem**: Security, performance, unsafe patterns, production deployment
- **Python/NLP**: FastAPI, transformers, vector search, LLM integration
- **Google Cloud**: Cloud Run, Spanner, Redis, monitoring, deployment
- **Security**: Vulnerability management, dependency auditing, secure deployment
- **Performance**: Benchmarking, optimization, resource management
- **Integration**: Microservices, API design, monitoring, observability

## OTHER CONSIDERATIONS:
Critical factors for comprehensive research coordination:

### Research Agent Specialization:
- **Rust Research Agent**: Security, performance, unsafe patterns, production best practices
- **Python/NLP Research Agent**: FastAPI, ML frameworks, NLP pipelines, LLM integration
- **Google Cloud Research Agent**: Cloud Run, Spanner, monitoring, deployment patterns
- **Security Research Agent**: Vulnerability management, secure deployment, compliance
- **Performance Research Agent**: Benchmarking, optimization, resource management
- **Integration Research Agent**: Microservices, API design, monitoring, observability

### Research Quality Standards:
- **Official Sources Only**: No unofficial blogs, tutorials, or third-party documentation
- **Comprehensive Scraping**: 30-100+ pages per technology area for complete coverage
- **Quality Validation**: Verify scraped content is complete, accurate, and current
- **Re-scraping Protocol**: If page 404s or has minimal content, try again until successful
- **Version Tracking**: Note documentation versions and scraping dates for freshness

### Research Organization:
```
research/
├── rust/
│   ├── security-best-practices.md
│   ├── unsafe-patterns.md
│   ├── performance-optimization.md
│   ├── production-deployment.md
│   └── dependency-management.md
├── python/
│   ├── fastapi-production.md
│   ├── ml-frameworks.md
│   ├── nlp-pipelines.md
│   └── async-patterns.md
├── google-cloud/
│   ├── cloud-run-production.md
│   ├── spanner-optimization.md
│   ├── monitoring-best-practices.md
│   └── deployment-strategies.md
├── security/
│   ├── vulnerability-management.md
│   ├── secure-deployment.md
│   └── compliance-standards.md
├── performance/
│   ├── benchmarking-strategies.md
│   ├── optimization-patterns.md
│   └── resource-management.md
└── integration/
    ├── microservices-patterns.md
    ├── api-design-best-practices.md
    └── monitoring-observability.md
```

### Research Agent Deployment Protocol:
```bash
# Deploy Rust Research Agent
/deploy-research-agent rust --focus=security-performance-production --pages=50+ --official-only

# Deploy Python/NLP Research Agent  
/deploy-research-agent python --focus=fastapi-ml-nlp-production --pages=50+ --official-only

# Deploy Google Cloud Research Agent
/deploy-research-agent gcp --focus=cloud-run-spanner-monitoring --pages=50+ --official-only

# Deploy Security Research Agent
/deploy-research-agent security --focus=vulnerability-secure-deployment --pages=30+ --official-only

# Deploy Performance Research Agent
/deploy-research-agent performance --focus=benchmarking-optimization --pages=30+ --official-only

# Deploy Integration Research Agent
/deploy-research-agent integration --focus=microservices-api-monitoring --pages=30+ --official-only
```

### Research Validation Framework:
- **Content Completeness**: Verify scraped pages contain full documentation
- **Source Authenticity**: Confirm all sources are official documentation sites
- **Version Currency**: Ensure documentation reflects latest stable versions
- **Coverage Depth**: Validate comprehensive coverage of focus areas
- **Quality Metrics**: Track successful scrapes vs. failures, content length, completeness

### SuperClaude Optimization for Research:
- **--c7**: Use Context7 MCP for official documentation research
- **--seq**: Sequential thinking for systematic research planning
- **--ultrathink**: Deep analysis of research requirements and coverage
- **Parallel Execution**: Deploy multiple research agents simultaneously

### Research Coordination Workflow:
```yaml
Phase 1: Research Agent Deployment
  - Deploy 6 specialized research agents with clear focus areas
  - Establish research targets and quality standards
  - Initialize parallel research execution

Phase 2: Parallel Research Execution
  - Each agent scrapes 30-100+ pages of official documentation
  - Quality validation and re-scraping as needed
  - Organized storage in technology-specific directories

Phase 3: Research Validation and Organization
  - Comprehensive quality review of all scraped content
  - Organization and metadata addition
  - Cross-reference validation and gap identification

Phase 4: Research Completion and Handoff
  - Final research directory organization
  - Research summary and coverage report
  - Handoff to PRP generation with complete research foundation
```

### Context Engineering Integration:
- **Evidence-Based Foundation**: Research provides official documentation for all PRP decisions
- **Comprehensive Context**: 200+ pages of research ensures complete context for PRP generation
- **Quality Assurance**: Official sources only prevent outdated or incorrect information
- **Systematic Coverage**: Specialized agents ensure no critical areas are missed

This research coordination follows Context Engineering principles for systematic, comprehensive documentation gathering before implementation planning.
