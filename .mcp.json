{"mcpServers": {"github": {"command": "npx", "args": ["@modelcontextprotocol/server-github"], "env": {"GITHUB_TOKEN": "${GITHUB_TOKEN}"}}, "puppeteer": {"command": "npx", "args": ["@modelcontextprotocol/server-puppeteer"]}, "sequential-thinking": {"command": "npx", "args": ["@modelcontextprotocol/server-sequential-thinking"]}, "google": {"command": "npx", "args": ["@modelcontextprotocol/server-google"], "env": {"GOOGLE_API_KEY": "${GOOGLE_API_KEY}"}}, "postgres": {"command": "npx", "args": ["@modelcontextprotocol/server-postgres"], "env": {"DATABASE_URL": "${DATABASE_URL}"}}}}