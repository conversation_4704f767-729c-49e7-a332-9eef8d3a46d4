name: Deploy Analysis Engine

on:
  push:
    branches:
      - main
      - develop
    paths:
      - 'services/analysis-engine/**'
      - '.github/workflows/deploy-analysis-engine.yml'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - development
          - staging
          - production
      deployment_mode:
        description: 'Deployment strategy'
        required: true
        default: 'canary'
        type: choice
        options:
          - canary
          - blue-green
          - direct

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  REGION: us-central1
  SERVICE_NAME: analysis-engine
  REPOSITORY: ccl-services

jobs:
  build-and-test:
    name: Build and Test
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          override: true
          components: rustfmt, clippy
          
      - name: Cache cargo dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            services/analysis-engine/target/
          key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            ${{ runner.os }}-cargo-
            
      - name: Run tests
        working-directory: services/analysis-engine
        run: |
          cargo test --all-features
          
      - name: Run clippy
        working-directory: services/analysis-engine
        run: |
          cargo clippy -- -D warnings
          
      - name: Check formatting
        working-directory: services/analysis-engine
        run: |
          cargo fmt -- --check
          
      - name: Build release binary
        working-directory: services/analysis-engine
        run: |
          cargo build --release
          
      - name: Upload binary artifact
        uses: actions/upload-artifact@v3
        with:
          name: analysis-engine-binary
          path: services/analysis-engine/target/release/analysis-engine

  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: build-and-test
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: 'services/analysis-engine'
          format: 'sarif'
          output: 'trivy-results.sarif'
          
      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

  build-container:
    name: Build Container Image
    runs-on: ubuntu-latest
    needs: [build-and-test, security-scan]
    outputs:
      image-tag: ${{ steps.image.outputs.tag }}
      
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}
          
      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        
      - name: Configure Docker for Artifact Registry
        run: |
          gcloud auth configure-docker ${REGION}-docker.pkg.dev
          
      - name: Generate image tag
        id: image
        run: |
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            TAG="${{ github.event.inputs.environment }}-${GITHUB_SHA::7}-$(date +%Y%m%d%H%M%S)"
          else
            TAG="${GITHUB_REF_NAME}-${GITHUB_SHA::7}-$(date +%Y%m%d%H%M%S)"
          fi
          echo "tag=${TAG}" >> $GITHUB_OUTPUT
          
      - name: Build and push container image
        uses: docker/build-push-action@v5
        with:
          context: services/analysis-engine
          file: services/analysis-engine/Dockerfile.optimized
          push: true
          tags: |
            ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE_NAME }}:${{ steps.image.outputs.tag }}
            ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE_NAME }}:latest
          cache-from: type=registry,ref=${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE_NAME }}:buildcache
          cache-to: type=registry,ref=${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.SERVICE_NAME }}:buildcache,mode=max

  deploy:
    name: Deploy to Cloud Run
    runs-on: ubuntu-latest
    needs: build-container
    environment: ${{ github.event.inputs.environment || 'staging' }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}
          
      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        
      - name: Deploy to Cloud Run
        id: deploy
        run: |
          ENVIRONMENT=${{ github.event.inputs.environment || 'staging' }}
          DEPLOYMENT_MODE=${{ github.event.inputs.deployment_mode || 'canary' }}
          IMAGE_TAG=${{ needs.build-container.outputs.image-tag }}
          
          # Set environment-specific variables
          case $ENVIRONMENT in
            production)
              MIN_INSTANCES=3
              MAX_INSTANCES=1000
              MEMORY=8Gi
              CPU=4
              ;;
            staging)
              MIN_INSTANCES=1
              MAX_INSTANCES=100
              MEMORY=4Gi
              CPU=2
              ;;
            development)
              MIN_INSTANCES=0
              MAX_INSTANCES=10
              MEMORY=2Gi
              CPU=1
              ;;
          esac
          
          # Deploy service
          gcloud run deploy ${SERVICE_NAME}-${ENVIRONMENT} \
            --image ${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/${SERVICE_NAME}:${IMAGE_TAG} \
            --platform managed \
            --region ${REGION} \
            --memory ${MEMORY} \
            --cpu ${CPU} \
            --min-instances ${MIN_INSTANCES} \
            --max-instances ${MAX_INSTANCES} \
            --timeout 300s \
            --concurrency 1000 \
            --port 8080 \
            --cpu-boost \
            --tag ${IMAGE_TAG} \
            --no-traffic \
            --set-env-vars "ENVIRONMENT=${ENVIRONMENT}" \
            --service-account "${SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" \
            --allow-unauthenticated
            
          # Get service URL
          SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME}-${ENVIRONMENT} \
            --platform managed \
            --region ${REGION} \
            --format 'value(status.url)')
            
          echo "service-url=${SERVICE_URL}" >> $GITHUB_OUTPUT
          echo "deployment-tag=${IMAGE_TAG}" >> $GITHUB_OUTPUT
          
      - name: Run smoke tests
        run: |
          SERVICE_URL=${{ steps.deploy.outputs.service-url }}
          TAG=${{ steps.deploy.outputs.deployment-tag }}
          
          # Test tagged version
          TAGGED_URL="${SERVICE_URL/https:\/\//https://${TAG}---}"
          
          echo "Testing deployment at: ${TAGGED_URL}"
          
          # Health check
          curl -f ${TAGGED_URL}/health || exit 1
          
          # Ready check
          curl -f ${TAGGED_URL}/ready || exit 1
          
      - name: Gradual traffic rollout
        if: success()
        run: |
          ENVIRONMENT=${{ github.event.inputs.environment || 'staging' }}
          DEPLOYMENT_MODE=${{ github.event.inputs.deployment_mode || 'canary' }}
          TAG=${{ steps.deploy.outputs.deployment-tag }}
          
          case $DEPLOYMENT_MODE in
            canary)
              # 10% -> 50% -> 100%
              for PERCENTAGE in 10 50 100; do
                echo "Routing ${PERCENTAGE}% traffic to new version"
                
                if [ $PERCENTAGE -eq 100 ]; then
                  gcloud run services update-traffic ${SERVICE_NAME}-${ENVIRONMENT} \
                    --region ${REGION} \
                    --to-latest
                else
                  gcloud run services update-traffic ${SERVICE_NAME}-${ENVIRONMENT} \
                    --region ${REGION} \
                    --to-tags ${TAG}=${PERCENTAGE}
                    
                  # Wait for stability
                  sleep 60
                fi
              done
              ;;
            blue-green)
              # Direct 100% switch
              gcloud run services update-traffic ${SERVICE_NAME}-${ENVIRONMENT} \
                --region ${REGION} \
                --to-tags ${TAG}=100
              ;;
            direct)
              # Immediate rollout
              gcloud run services update-traffic ${SERVICE_NAME}-${ENVIRONMENT} \
                --region ${REGION} \
                --to-latest
              ;;
          esac
          
      - name: Post-deployment validation
        if: success()
        run: |
          SERVICE_URL=${{ steps.deploy.outputs.service-url }}
          
          # Final health check
          curl -f ${SERVICE_URL}/health
          
          # Log deployment info
          echo "Deployment completed successfully!"
          echo "Service URL: ${SERVICE_URL}"
          echo "Environment: ${{ github.event.inputs.environment || 'staging' }}"
          echo "Deployment Mode: ${{ github.event.inputs.deployment_mode || 'canary' }}"

  rollback:
    name: Rollback on Failure
    runs-on: ubuntu-latest
    needs: deploy
    if: failure()
    
    steps:
      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}
          
      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        
      - name: Rollback deployment
        run: |
          ENVIRONMENT=${{ github.event.inputs.environment || 'staging' }}
          
          echo "Rolling back deployment..."
          
          # Route all traffic back to stable version
          gcloud run services update-traffic ${SERVICE_NAME}-${ENVIRONMENT} \
            --region ${REGION} \
            --to-revisions LATEST=100
            
          echo "Rollback completed"