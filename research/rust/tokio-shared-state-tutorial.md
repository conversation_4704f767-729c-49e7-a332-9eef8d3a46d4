# Tokio Shared State Tutorial

## Overview

So far, we have spawned tasks to concurrently handle incoming connections. However, up until now, tasks have been largely independent. In this chapter, we will explore strategies for sharing state between tasks in Tokio applications.

## Strategies for Sharing State

Generally, there are a couple of different ways to share state in Tokio:

1. **Guard the shared state with a `Mutex`**
2. **Spawn a task to manage the state and use message passing**

## Using Arc<Mutex<T>> for Simple State

### Basic Implementation

```rust
use tokio::net::TcpListener;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};

#[tokio::main]
async fn main() {
    let listener = TcpListener::bind("127.0.0.1:6379").await.unwrap();

    println!("Listening");

    let db = Arc::new(Mutex::new(HashMap::new()));

    loop {
        let (socket, _) = listener.accept().await.unwrap();
        // Clone the handle to the hash map
        let db = db.clone();

        println!("Accepted");
        tokio::spawn(async move {
            process(socket, db).await;
        });
    }
}
```

### Type Alias for Clarity

```rust
use bytes::Bytes;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};

type Db = Arc<Mutex<HashMap<String, Bytes>>>;
```

### Processing with Shared State

```rust
use tokio::net::TcpStream;
use mini_redis::{Connection, Frame};

async fn process(socket: TcpStream, db: Db) {
    use mini_redis::Command::{self, Get, Set};

    let mut connection = Connection::new(socket);

    while let Some(frame) = connection.read_frame().await.unwrap() {
        let response = match Command::from_frame(frame).unwrap() {
            Set(cmd) => {
                let mut db = db.lock().unwrap();
                db.insert(cmd.key().to_string(), cmd.value().clone());
                Frame::Simple("OK".to_string())
            }
            Get(cmd) => {
                let db = db.lock().unwrap();
                if let Some(value) = db.get(cmd.key()) {
                    Frame::Bulk(value.clone())
                } else {
                    Frame::Null
                }
            }
            cmd => panic!("unimplemented {:?}", cmd),
        };

        connection.write_frame(&response).await.unwrap();
    }
}
```

## Why std::sync::Mutex?

You might wonder why we're using `std::sync::Mutex` instead of `tokio::sync::Mutex`. Here's why:

### Key Differences

1. **Async-aware**: `tokio::sync::Mutex` can be held across `.await` points
2. **Performance**: `std::sync::Mutex` is generally faster when you don't need to hold it across `.await`

### When to Use Each

**Use `std::sync::Mutex` when:**
- The critical section is short
- No async operations are performed while holding the lock
- You want better performance

**Use `tokio::sync::Mutex` when:**
- You need to hold the lock across `.await` points
- The critical section includes async operations

### Important Warning

**Never do this with `std::sync::Mutex`:**
```rust
use std::sync::{Mutex, MutexGuard};

async fn increment_and_do_stuff(mutex: &Mutex<i32>) {
    let mut lock: MutexGuard<i32> = mutex.lock().unwrap();
    *lock += 1;

    do_something_async().await; // DANGER: May cause deadlock!
} // lock goes out of scope here
```

This can cause:
- Compilation errors (if the future isn't `Send`)
- Deadlocks at runtime
- Other tasks being blocked

## Sharding for Performance

When contention becomes an issue, you can shard the mutex:

```rust
type ShardedDb = Arc<Vec<Mutex<HashMap<String, Vec<u8>>>>>;

fn new_sharded_db(num_shards: usize) -> ShardedDb {
    let mut db = Vec::with_capacity(num_shards);
    for _ in 0..num_shards {
        db.push(Mutex::new(HashMap::new()));
    }
    Arc::new(db)
}
```

### Determining the Shard

```rust
fn hash(key: &str) -> usize {
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};

    let mut hasher = DefaultHasher::new();
    key.hash(&mut hasher);
    hasher.finish() as usize
}

// Usage
let shard = hash(key) % db.len();
let mut shard_db = db[shard].lock().unwrap();
shard_db.insert(key.to_string(), value);
```

## Alternatives to Mutex

### Dedicated Task Manager

Instead of using a mutex, spawn a task to manage state and use message passing:

```rust
use tokio::sync::mpsc;

#[tokio::main]
async fn main() {
    let (tx, mut rx) = mpsc::channel(32);
    
    // Spawn state manager
    tokio::spawn(async move {
        let mut state = HashMap::new();
        
        while let Some(cmd) = rx.recv().await {
            // Process commands and manage state
        }
    });
    
    // Send commands to state manager
    tx.send(cmd).await.unwrap();
}
```

### When to Use Message Passing

Choose message passing when:
- Synchronizing access to I/O resources
- Complex state management is needed
- You want to avoid lock contention
- The state manager needs to perform async operations

## Production-Ready Solutions

For production applications, consider using specialized concurrent data structures:

- **[dashmap](https://docs.rs/dashmap)**: Concurrent HashMap implementation
- **[leapfrog](https://docs.rs/leapfrog)**: Concurrent HashMap with consistent ordering
- **[flurry](https://docs.rs/flurry)**: Port of Java's ConcurrentHashMap

## Best Practices

1. **Keep critical sections small**: Minimize the time holding a mutex
2. **Avoid holding locks across `.await`**: Use `tokio::sync::Mutex` if necessary
3. **Consider alternatives**: Message passing or specialized data structures
4. **Profile your application**: Measure contention before optimizing
5. **Use type aliases**: Make complex types more readable

## Summary

- `Arc<Mutex<T>>` is suitable for simple shared state
- Use `std::sync::Mutex` for synchronous critical sections
- Consider sharding or message passing for high contention
- Never hold a `std::sync::Mutex` guard across `.await` points
- Profile and measure before optimizing