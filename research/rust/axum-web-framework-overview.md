# Axum Web Framework Overview

## Introduction

Axum is a web application framework that focuses on ergonomics and modularity. It's designed to work with tokio and hyper, leveraging the Tower ecosystem for middleware.

## Key Features

- **Macro-free routing**: Define routes using function calls, not macros
- **Declarative request parsing**: Use extractors to parse requests
- **Simple error handling**: Errors are just responses
- **Minimal boilerplate**: Focus on your application logic
- **Tower integration**: Use any Tower middleware

## Installation

```toml
[dependencies]
axum = "0.7"
tokio = { version = "1", features = ["full"] }
```

## Basic Example

```rust
use axum::{
    routing::{get, post},
    http::StatusCode,
    Json, Router,
};
use serde::{Deserialize, Serialize};
use std::net::SocketAddr;

#[tokio::main]
async fn main() {
    // Build our application with routes
    let app = Router::new()
        .route("/", get(root))
        .route("/users", post(create_user));

    // Run it
    let addr = SocketAddr::from(([127, 0, 0, 1], 3000));
    println!("listening on {}", addr);
    let listener = tokio::net::TcpListener::bind(addr).await.unwrap();
    axum::serve(listener, app).await.unwrap();
}

// Basic handler that responds with static text
async fn root() -> &'static str {
    "Hello, World!"
}

// Handler that accepts and returns JSON
async fn create_user(Json(payload): Json<CreateUser>) -> (StatusCode, Json<User>) {
    let user = User {
        id: 1337,
        username: payload.username,
    };

    (StatusCode::CREATED, Json(user))
}

#[derive(Deserialize)]
struct CreateUser {
    username: String,
}

#[derive(Serialize)]
struct User {
    id: u64,
    username: String,
}
```

## Routing

### Basic Routing

```rust
use axum::{Router, routing::{get, post, put, delete}};

let app = Router::new()
    .route("/", get(index))
    .route("/users", get(list_users).post(create_user))
    .route("/users/:id", get(get_user).put(update_user).delete(delete_user));
```

### Nested Routes

```rust
use axum::{Router, routing::get};

let user_routes = Router::new()
    .route("/", get(list_users))
    .route("/:id", get(get_user));

let api_routes = Router::new()
    .nest("/users", user_routes);

let app = Router::new()
    .nest("/api", api_routes);
// Results in routes: /api/users and /api/users/:id
```

### Wildcard Routes

```rust
let app = Router::new()
    .route("/assets/*path", get(serve_asset));

async fn serve_asset(Path(path): Path<String>) -> String {
    format!("Serving asset: {}", path)
}
```

## Handlers

Handlers are async functions that process requests and return responses.

### Basic Handler

```rust
async fn hello() -> &'static str {
    "Hello, World!"
}
```

### Handler with Multiple Extractors

```rust
use axum::{
    extract::{Path, Query},
    http::HeaderMap,
};
use serde::Deserialize;

#[derive(Deserialize)]
struct Params {
    sort: Option<String>,
}

async fn get_user(
    Path(user_id): Path<u32>,
    Query(params): Query<Params>,
    headers: HeaderMap,
) -> String {
    format!("User {} with sort {:?}", user_id, params.sort)
}
```

## Extractors

Extractors parse different parts of the request.

### Path Parameters

```rust
use axum::extract::Path;

async fn get_user(Path(user_id): Path<u32>) -> String {
    format!("User {}", user_id)
}

// Multiple path parameters
async fn get_post(Path((user_id, post_id)): Path<(u32, u32)>) -> String {
    format!("User {} Post {}", user_id, post_id)
}
```

### Query Parameters

```rust
use axum::extract::Query;
use serde::Deserialize;

#[derive(Deserialize)]
struct Pagination {
    page: Option<u32>,
    per_page: Option<u32>,
}

async fn list_things(Query(pagination): Query<Pagination>) -> String {
    let page = pagination.page.unwrap_or(1);
    let per_page = pagination.per_page.unwrap_or(20);
    format!("Page {} with {} items", page, per_page)
}
```

### JSON Bodies

```rust
use axum::Json;
use serde::{Deserialize, Serialize};

#[derive(Deserialize)]
struct CreateUser {
    username: String,
    email: String,
}

#[derive(Serialize)]
struct User {
    id: u64,
    username: String,
    email: String,
}

async fn create_user(Json(payload): Json<CreateUser>) -> Json<User> {
    let user = User {
        id: 1,
        username: payload.username,
        email: payload.email,
    };
    Json(user)
}
```

### Headers

```rust
use axum::http::HeaderMap;

async fn handler(headers: HeaderMap) -> String {
    match headers.get("user-agent") {
        Some(value) => format!("User-Agent: {:?}", value),
        None => "No User-Agent header".to_string(),
    }
}
```

### Request Extensions

```rust
use axum::{extract::Extension, middleware};

// Add data to request extensions
async fn add_user_id(mut req: Request, next: Next) -> Response {
    req.extensions_mut().insert(UserId(1234));
    next.run(req).await
}

// Extract in handler
async fn handler(Extension(user_id): Extension<UserId>) -> String {
    format!("User ID: {}", user_id.0)
}
```

## State Management

### Using State Extractor (Recommended)

```rust
use axum::{
    extract::State,
    routing::get,
    Router,
};
use std::sync::Arc;

#[derive(Clone)]
struct AppState {
    // Use Arc for shared ownership
    db: Arc<DatabaseConnection>,
}

#[tokio::main]
async fn main() {
    let shared_state = AppState {
        db: Arc::new(create_db_connection()),
    };

    let app = Router::new()
        .route("/", get(handler))
        .with_state(shared_state);

    // Run the server...
}

async fn handler(State(state): State<AppState>) -> String {
    // Use state.db here
    "Using shared state".to_string()
}
```

### Multiple State Types

```rust
use axum::extract::{State, FromRef};

#[derive(Clone)]
struct AppState {
    db: DatabaseConnection,
    redis: RedisConnection,
}

// Implement FromRef to extract substates
impl FromRef<AppState> for DatabaseConnection {
    fn from_ref(state: &AppState) -> Self {
        state.db.clone()
    }
}

async fn handler(
    State(db): State<DatabaseConnection>,
) -> String {
    // Use just the database connection
    "OK".to_string()
}
```

## Middleware

### Using Tower Middleware

```rust
use axum::{
    middleware,
    routing::get,
    Router,
};
use tower::ServiceBuilder;
use tower_http::{
    trace::TraceLayer,
    compression::CompressionLayer,
    cors::CorsLayer,
};

let app = Router::new()
    .route("/", get(handler))
    .layer(
        ServiceBuilder::new()
            .layer(TraceLayer::new_for_http())
            .layer(CompressionLayer::new())
            .layer(CorsLayer::permissive())
    );
```

### Custom Middleware

```rust
use axum::{
    middleware::{self, Next},
    response::Response,
    routing::get,
    Router,
};

async fn auth_middleware(
    req: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // Check authentication
    let auth_header = req.headers()
        .get("authorization")
        .and_then(|h| h.to_str().ok());

    match auth_header {
        Some(h) if h.starts_with("Bearer ") => {
            // Valid auth, continue
            Ok(next.run(req).await)
        }
        _ => Err(StatusCode::UNAUTHORIZED),
    }
}

let app = Router::new()
    .route("/protected", get(protected_handler))
    .layer(middleware::from_fn(auth_middleware));
```

## Error Handling

### Basic Error Handling

```rust
use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
};

// Define custom error type
enum AppError {
    BadRequest,
    NotFound,
    InternalError,
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let (status, message) = match self {
            AppError::BadRequest => (StatusCode::BAD_REQUEST, "Bad request"),
            AppError::NotFound => (StatusCode::NOT_FOUND, "Not found"),
            AppError::InternalError => (StatusCode::INTERNAL_SERVER_ERROR, "Internal error"),
        };

        (status, message).into_response()
    }
}

// Use in handler
async fn handler() -> Result<String, AppError> {
    // Return Ok or Err
    Err(AppError::NotFound)
}
```

### Error Handling with anyhow

```rust
use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
};

// Make anyhow errors convertible to responses
struct AppError(anyhow::Error);

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            format!("Something went wrong: {}", self.0),
        ).into_response()
    }
}

// Enable using `?` operator
impl<E> From<E> for AppError
where
    E: Into<anyhow::Error>,
{
    fn from(err: E) -> Self {
        Self(err.into())
    }
}

async fn handler() -> Result<String, AppError> {
    let data = std::fs::read_to_string("file.txt")?; // Uses ?
    Ok(data)
}
```

## Testing

```rust
use axum::{
    body::Body,
    http::{Request, StatusCode},
    Router,
};
use tower::ServiceExt; // for `oneshot`

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_hello() {
        let app = Router::new().route("/", get(|| async { "Hello, World!" }));

        let response = app
            .oneshot(Request::builder().uri("/").body(Body::empty()).unwrap())
            .await
            .unwrap();

        assert_eq!(response.status(), StatusCode::OK);

        let body = hyper::body::to_bytes(response.into_body()).await.unwrap();
        assert_eq!(&body[..], b"Hello, World!");
    }
}
```

## Advanced Features

### WebSockets

```rust
use axum::{
    extract::ws::{WebSocket, WebSocketUpgrade},
    response::Response,
};

async fn ws_handler(ws: WebSocketUpgrade) -> Response {
    ws.on_upgrade(handle_socket)
}

async fn handle_socket(mut socket: WebSocket) {
    while let Some(msg) = socket.recv().await {
        if let Ok(msg) = msg {
            // Echo the message back
            socket.send(msg).await.unwrap();
        }
    }
}
```

### Server-Sent Events

```rust
use axum::{
    response::sse::{Event, KeepAlive, Sse},
    routing::get,
};
use futures::stream::{self, Stream};
use std::convert::Infallible;

async fn sse_handler() -> Sse<impl Stream<Item = Result<Event, Infallible>>> {
    let stream = stream::repeat_with(|| {
        Event::default().data("hello")
    });

    Sse::new(stream).keep_alive(KeepAlive::default())
}
```

## Best Practices

1. **Use extractors for parsing**: Let Axum handle request parsing
2. **Leverage Tower middleware**: Don't reinvent the wheel
3. **Handle errors gracefully**: Implement proper error types
4. **Use state management**: Share resources properly
5. **Test your routes**: Use the testing utilities provided
6. **Keep handlers focused**: One handler, one responsibility
7. **Use type safety**: Leverage Rust's type system

## Feature Flags

Common feature flags to enable:

```toml
[dependencies]
axum = { version = "0.7", features = ["ws", "multipart", "macros"] }
tower = { version = "0.4", features = ["full"] }
tower-http = { version = "0.5", features = ["fs", "trace", "compression", "cors"] }
```

## Summary

Axum provides a powerful, type-safe way to build web services in Rust. Its integration with Tower and focus on ergonomics make it an excellent choice for building production-ready APIs and web applications.