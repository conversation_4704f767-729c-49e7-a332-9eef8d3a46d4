# Axum Extractors - Detailed Guide

## Overview

Extractors are how you get data from incoming requests in Axum. They are types that implement the `FromRequest` or `FromRequestParts` traits and appear as arguments to your handler functions.

## Built-in Extractors

### Path - Extract Path Parameters

```rust
use axum::{
    extract::Path,
    routing::get,
    Router,
};

// Single parameter
async fn get_user(Path(id): Path<u32>) -> String {
    format!("User {}", id)
}

// Multiple parameters
async fn get_post(Path((user_id, post_id)): Path<(u32, u32)>) -> String {
    format!("User {} Post {}", user_id, post_id)
}

// Using a struct
#[derive(Deserialize)]
struct PathParams {
    user_id: u32,
    post_id: u32,
}

async fn get_post_struct(Path(params): Path<PathParams>) -> String {
    format!("User {} Post {}", params.user_id, params.post_id)
}

let app = Router::new()
    .route("/users/:id", get(get_user))
    .route("/users/:user_id/posts/:post_id", get(get_post));
```

### Query - Extract Query Parameters

```rust
use axum::extract::Query;
use serde::Deserialize;

#[derive(Deserialize)]
struct Pagination {
    page: Option<u32>,
    per_page: Option<u32>,
}

async fn list_users(Query(params): Query<Pagination>) -> String {
    let page = params.page.unwrap_or(1);
    let per_page = params.per_page.unwrap_or(30);
    format!("Page {}, {} items per page", page, per_page)
}

// URL: /users?page=2&per_page=50
```

### Json - Parse JSON Request Bodies

```rust
use axum::Json;
use serde::{Deserialize, Serialize};

#[derive(Deserialize)]
struct CreateUser {
    name: String,
    email: String,
}

#[derive(Serialize)]
struct User {
    id: u64,
    name: String,
    email: String,
}

async fn create_user(Json(payload): Json<CreateUser>) -> Json<User> {
    let user = User {
        id: 1337,
        name: payload.name,
        email: payload.email,
    };
    Json(user)
}
```

### Form - Parse Form Data

```rust
use axum::Form;
use serde::Deserialize;

#[derive(Deserialize)]
struct LoginForm {
    username: String,
    password: String,
}

async fn login(Form(login): Form<LoginForm>) -> String {
    format!("Logging in {}", login.username)
}
```

### Headers - Access Request Headers

```rust
use axum::http::{HeaderMap, HeaderValue};

async fn handler(headers: HeaderMap) -> String {
    match headers.get("x-custom-header") {
        Some(value) => format!("Custom header: {:?}", value),
        None => "No custom header".to_string(),
    }
}

// Extract specific header
use axum::TypedHeader;
use headers::UserAgent;

async fn get_user_agent(TypedHeader(user_agent): TypedHeader<UserAgent>) -> String {
    format!("User agent: {}", user_agent)
}
```

### Request Parts

```rust
use axum::{
    extract::Request,
    http::{Method, Uri, Version},
};

async fn handler(method: Method, uri: Uri, version: Version) -> String {
    format!("{:?} {} {:?}", method, uri, version)
}
```

### Body Extractors

```rust
use axum::body::{Body, Bytes};

// Raw bytes
async fn bytes_body(bytes: Bytes) -> String {
    format!("Got {} bytes", bytes.len())
}

// String body
async fn string_body(body: String) -> String {
    format!("Got string: {}", body)
}

// Stream body
use futures::StreamExt;

async fn stream_body(mut stream: Body) -> String {
    let mut total = 0;
    while let Some(chunk) = stream.next().await {
        if let Ok(bytes) = chunk {
            total += bytes.len();
        }
    }
    format!("Streamed {} bytes", total)
}
```

### State Extraction

```rust
use axum::{
    extract::State,
    routing::get,
    Router,
};

#[derive(Clone)]
struct AppState {
    database_url: String,
}

async fn handler(State(state): State<AppState>) -> String {
    format!("Database URL: {}", state.database_url)
}

let state = AppState {
    database_url: "postgres://localhost/mydb".to_string(),
};

let app = Router::new()
    .route("/", get(handler))
    .with_state(state);
```

### Extension - Shared Data

```rust
use axum::{
    extract::Extension,
    middleware,
    routing::get,
    Router,
};

#[derive(Clone)]
struct CurrentUser {
    id: u64,
    name: String,
}

// Middleware to add extension
async fn add_user(mut req: Request, next: Next) -> Response {
    req.extensions_mut().insert(CurrentUser {
        id: 1,
        name: "Alice".to_string(),
    });
    next.run(req).await
}

// Handler using extension
async fn handler(Extension(user): Extension<CurrentUser>) -> String {
    format!("Hello, {}", user.name)
}

let app = Router::new()
    .route("/", get(handler))
    .layer(middleware::from_fn(add_user));
```

## Optional Extractors

Use `Option<T>` for extractors that might fail:

```rust
use axum::extract::{Query, Path};

#[derive(Deserialize)]
struct OptionalParams {
    filter: Option<String>,
}

async fn handler(
    // Optional path param (won't compile - path params are always required)
    // Path(id): Path<Option<u32>>, // DON'T DO THIS
    
    // Optional query params - this is fine
    Query(params): Query<OptionalParams>,
    
    // Optional JSON body
    json: Option<Json<Value>>,
) -> String {
    let filter = params.filter.unwrap_or_else(|| "none".to_string());
    match json {
        Some(Json(data)) => format!("Got JSON: {:?}", data),
        None => "No JSON body".to_string(),
    }
}
```

## Result Extractors

Handle extraction failures explicitly:

```rust
use axum::{
    extract::{Path, rejection::PathRejection},
    http::StatusCode,
};

async fn handler(
    result: Result<Path<u32>, PathRejection>,
) -> Result<String, StatusCode> {
    match result {
        Ok(Path(id)) => Ok(format!("User {}", id)),
        Err(_) => Err(StatusCode::BAD_REQUEST),
    }
}
```

## Custom Extractors

### Implementing FromRequestParts (doesn't consume body)

```rust
use axum::{
    async_trait,
    extract::FromRequestParts,
    http::{header::USER_AGENT, request::Parts, StatusCode},
};

struct ExtractUserAgent(String);

#[async_trait]
impl<S> FromRequestParts<S> for ExtractUserAgent
where
    S: Send + Sync,
{
    type Rejection = (StatusCode, &'static str);

    async fn from_request_parts(parts: &mut Parts, _state: &S) -> Result<Self, Self::Rejection> {
        let user_agent = parts
            .headers
            .get(USER_AGENT)
            .and_then(|value| value.to_str().ok())
            .ok_or((StatusCode::BAD_REQUEST, "Missing User-Agent header"))?;

        Ok(ExtractUserAgent(user_agent.to_string()))
    }
}

// Usage
async fn handler(ExtractUserAgent(user_agent): ExtractUserAgent) -> String {
    format!("User agent: {}", user_agent)
}
```

### Implementing FromRequest (consumes body)

```rust
use axum::{
    async_trait,
    body::{Body, Bytes},
    extract::FromRequest,
    http::{Request, StatusCode},
};

struct ValidatedJson<T>(T);

#[async_trait]
impl<S, T> FromRequest<S> for ValidatedJson<T>
where
    S: Send + Sync,
    T: DeserializeOwned + Validate, // Assuming you have a Validate trait
{
    type Rejection = (StatusCode, String);

    async fn from_request(req: Request<Body>, state: &S) -> Result<Self, Self::Rejection> {
        let bytes = Bytes::from_request(req, state)
            .await
            .map_err(|_| (StatusCode::BAD_REQUEST, "Invalid body".to_string()))?;

        let value: T = serde_json::from_slice(&bytes)
            .map_err(|e| (StatusCode::BAD_REQUEST, format!("Invalid JSON: {}", e)))?;

        value
            .validate()
            .map_err(|e| (StatusCode::BAD_REQUEST, format!("Validation failed: {}", e)))?;

        Ok(ValidatedJson(value))
    }
}
```

## Extractor Ordering Rules

**Important**: The order of extractors in your function signature matters!

1. **Body can only be consumed once**
2. **Body-consuming extractors must come last**
3. **Extractors run from left to right**

```rust
// ✅ GOOD - body extractor last
async fn handler(
    Path(id): Path<u32>,
    Query(params): Query<Params>,
    headers: HeaderMap,
    Json(body): Json<Value>, // Body consumer last
) -> String {
    "OK".to_string()
}

// ❌ BAD - body extractor not last
async fn handler(
    Json(body): Json<Value>, // This will cause compilation error
    Path(id): Path<u32>,
) -> String {
    "Won't compile".to_string()
}

// ✅ Multiple non-body extractors are fine
async fn handler(
    method: Method,
    uri: Uri,
    headers: HeaderMap,
    Path(id): Path<u32>,
    Query(params): Query<Params>,
    Extension(user): Extension<User>,
    State(state): State<AppState>,
) -> String {
    "All good!".to_string()
}
```

## Error Handling

### Default Rejection Behavior

```rust
// If extraction fails, Axum returns an appropriate HTTP error:
// - Path extraction fails: 404 Not Found
// - Query extraction fails: 400 Bad Request
// - Json extraction fails: 400 Bad Request
// - Header extraction fails: 400 Bad Request
```

### Custom Error Responses

```rust
use axum::{
    extract::{rejection::JsonRejection, Json},
    http::StatusCode,
    response::{IntoResponse, Response},
};

struct ApiError {
    message: String,
    status: StatusCode,
}

impl IntoResponse for ApiError {
    fn into_response(self) -> Response {
        (self.status, Json(json!({ "error": self.message }))).into_response()
    }
}

async fn handler(
    result: Result<Json<CreateUser>, JsonRejection>,
) -> Result<Json<User>, ApiError> {
    let Json(payload) = result.map_err(|e| ApiError {
        message: format!("Invalid JSON: {}", e),
        status: StatusCode::BAD_REQUEST,
    })?;

    // Process payload...
    Ok(Json(user))
}
```

## Request Body Limits

By default, Axum limits request bodies to 2MB. To change this:

```rust
use axum::{
    body::Body,
    extract::{DefaultBodyLimit, Request},
    routing::post,
    Router,
};

let app = Router::new()
    .route("/upload", post(upload))
    // Set 10MB limit for all routes
    .layer(DefaultBodyLimit::max(10 * 1024 * 1024));

// Or disable limit for specific route
let app = Router::new()
    .route("/upload", post(upload).layer(DefaultBodyLimit::disable()));
```

## Best Practices

1. **Use typed extractors**: Leverage Rust's type system
   ```rust
   // Good
   async fn handler(Path(id): Path<UserId>) -> Result<Json<User>, ApiError>
   
   // Less ideal
   async fn handler(Path(id): Path<String>) -> String
   ```

2. **Handle extraction failures**: Use `Result` for production code
   ```rust
   async fn handler(
       user: Result<Json<User>, JsonRejection>,
   ) -> Result<Json<Response>, ApiError> {
       let user = user.map_err(|e| ApiError::from(e))?;
       // ...
   }
   ```

3. **Validate extracted data**: Don't trust user input
   ```rust
   #[derive(Deserialize, Validate)]
   struct CreateUser {
       #[validate(length(min = 3, max = 20))]
       username: String,
       #[validate(email)]
       email: String,
   }
   ```

4. **Use appropriate extractors**: 
   - `State` for shared app state
   - `Extension` for request-scoped data
   - `Path`/`Query` for URL parameters
   - `Json`/`Form` for body data

5. **Order matters**: Always put body-consuming extractors last

## Summary

Extractors are a powerful feature of Axum that provide:
- Type-safe request parsing
- Composable data extraction
- Clear error handling
- Extensibility through custom implementations

By understanding and properly using extractors, you can build robust, type-safe web applications with clear data flow and error handling.