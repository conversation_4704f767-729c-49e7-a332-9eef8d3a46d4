# Rust Secure Coding Practices - Official Documentation

**Source**: https://doc.rust-lang.org/nomicon/, https://rust-lang.github.io/api-guidelines/
**Version**: Rust 1.x
**Scraped**: 2025-07-14
**Last Updated**: 2025-07-14

## Overview

This document provides comprehensive secure coding practices for Rust, compiled from official Rust documentation sources including the Rustonomicon and API Guidelines.

## Safe vs Unsafe Rust

### Core Safety Guarantee

"No matter what, Safe Rust can't cause Undefined Behavior" - The Rustonomicon

### Safe Rust

Safe Rust provides memory safety guarantees through:
- Ownership system
- Borrowing rules  
- Type system
- Lifetime validation

### Unsafe Rust

Unsafe Rust allows operations that bypass compiler safety checks:

```rust
unsafe {
    // Operations requiring manual safety verification
}
```

#### Unsafe Operations

1. **Dereferencing raw pointers**
   ```rust
   let raw_ptr: *const i32 = &42;
   unsafe {
       let val = *raw_ptr; // Requires unsafe
   }
   ```

2. **Calling unsafe functions**
   ```rust
   unsafe fn dangerous_operation() { /* ... */ }
   unsafe {
       dangerous_operation();
   }
   ```

3. **Implementing unsafe traits**
   ```rust
   unsafe impl Send for MyType {}
   unsafe impl Sync for MyType {}
   ```

4. **Accessing or modifying mutable static variables**
   ```rust
   static mut COUNTER: i32 = 0;
   unsafe {
       COUNTER += 1;
   }
   ```

5. **Using union fields**

## Security-Critical Traits

### Send and Sync

These marker traits are fundamental to Rust's thread safety:

```rust
// Test that your types properly implement Send/Sync
#[test]
fn test_send() {
    fn assert_send<T: Send>() {}
    assert_send::<MyType>();
}

#[test]
fn test_sync() {
    fn assert_sync<T: Sync>() {}
    assert_sync::<MyType>();
}
```

## API Security Guidelines

### Type Safety Over Runtime Checks

Prefer compile-time validation through the type system:

```rust
// Good: Type-safe at compile time
struct Ascii(String);

impl Ascii {
    pub fn new(s: &str) -> Result<Self, InvalidAscii> {
        if s.is_ascii() {
            Ok(Ascii(s.to_string()))
        } else {
            Err(InvalidAscii)
        }
    }
}

fn process_ascii(input: Ascii) {
    // Guaranteed to be ASCII
}

// Avoid: Runtime validation only
fn process_string(input: &str) {
    assert!(input.is_ascii()); // Can panic
}
```

### Secure Builder Patterns

Use builder patterns for complex initialization with validation:

```rust
pub struct ConnectionBuilder {
    host: Option<String>,
    port: Option<u16>,
    tls: bool,
    timeout: Duration,
}

impl ConnectionBuilder {
    pub fn new() -> Self {
        ConnectionBuilder {
            host: None,
            port: None,
            tls: true, // Secure by default
            timeout: Duration::from_secs(30),
        }
    }

    pub fn host(mut self, host: String) -> Self {
        self.host = Some(host);
        self
    }

    pub fn build(self) -> Result<Connection, Error> {
        let host = self.host.ok_or(Error::MissingHost)?;
        let port = self.port.ok_or(Error::MissingPort)?;
        
        // Validate and create secure connection
        Connection::new_secure(host, port, self.tls, self.timeout)
    }
}
```

### Error Handling Best Practices

Never expose internal implementation details in errors:

```rust
// Good: Public error type hides internals
#[derive(Debug)]
pub struct PublicError {
    kind: ErrorKind,
    message: String,
}

// Internal error type
struct InternalError {
    cause: io::Error,
    context: String,
}

// Convert internal to public, sanitizing details
#[doc(hidden)]
impl From<InternalError> for PublicError {
    fn from(err: InternalError) -> PublicError {
        PublicError {
            kind: ErrorKind::Internal,
            message: "An internal error occurred".to_string(),
        }
    }
}
```

## Input Validation

### Newtype Pattern for Validation

Use newtypes to enforce invariants:

```rust
pub struct Email(String);

impl Email {
    pub fn parse(s: &str) -> Result<Self, EmailError> {
        // Validate email format
        if validate_email(s) {
            Ok(Email(s.to_string()))
        } else {
            Err(EmailError::InvalidFormat)
        }
    }
}

// Now functions can require valid emails
fn send_notification(email: Email) {
    // Email is guaranteed valid
}
```

### Bitflags for Type-Safe Flags

Use the `bitflags` crate for type-safe flag handling:

```rust
use bitflags::bitflags;

bitflags! {
    pub struct Permissions: u32 {
        const READ    = 0b00000001;
        const WRITE   = 0b00000010;
        const EXECUTE = 0b00000100;
    }
}

fn check_access(perms: Permissions) {
    if perms.contains(Permissions::WRITE) {
        // Type-safe permission check
    }
}
```

## Memory Safety Patterns

### Avoiding Uninitialized Memory

```rust
// Bad: Uninitialized memory
let mut data: [u8; 1024];
unsafe {
    // Reading uninitialized memory is UB
    let first = data[0];
}

// Good: Explicitly initialized
let mut data = [0u8; 1024];
let first = data[0]; // Safe
```

### Safe Abstractions Over Unsafe Code

When wrapping unsafe code, ensure the safe API upholds all invariants:

```rust
pub struct SafeBuffer {
    ptr: *mut u8,
    len: usize,
    capacity: usize,
}

impl SafeBuffer {
    pub fn new(size: usize) -> Self {
        let layout = Layout::array::<u8>(size).unwrap();
        let ptr = unsafe { alloc(layout) };
        
        SafeBuffer {
            ptr,
            len: 0,
            capacity: size,
        }
    }
    
    // Safe API that maintains invariants
    pub fn push(&mut self, value: u8) -> Result<(), BufferFull> {
        if self.len >= self.capacity {
            return Err(BufferFull);
        }
        
        unsafe {
            self.ptr.add(self.len).write(value);
        }
        self.len += 1;
        Ok(())
    }
}

impl Drop for SafeBuffer {
    fn drop(&mut self) {
        let layout = Layout::array::<u8>(self.capacity).unwrap();
        unsafe {
            dealloc(self.ptr, layout);
        }
    }
}
```

## Documentation Requirements

### Documenting Unsafe Code

All unsafe code must be thoroughly documented:

```rust
/// Reads a value from the buffer without bounds checking.
///
/// # Safety
///
/// The caller must ensure that:
/// - `index` is less than the buffer's length
/// - The buffer has been properly initialized at `index`
/// - No other references to this index exist
///
/// # Example
///
/// ```rust
/// let buffer = SafeBuffer::from_slice(&[1, 2, 3]);
/// // Safe because index 1 < length 3
/// let value = unsafe { buffer.get_unchecked(1) };
/// assert_eq!(value, 2);
/// ```
pub unsafe fn get_unchecked(&self, index: usize) -> u8 {
    *self.ptr.add(index)
}
```

### Documenting Panics

Document all conditions that can cause panics:

```rust
/// Inserts an element at position `index`.
///
/// # Panics
///
/// Panics if `index > len`.
///
/// # Example
///
/// ```rust
/// let mut vec = vec![1, 2, 3];
/// vec.insert(1, 4);
/// assert_eq!(vec, [1, 4, 2, 3]);
/// ```
pub fn insert(&mut self, index: usize, element: T) {
    assert!(index <= self.len(), "index out of bounds");
    // Implementation
}
```

## Security Checklist

### Before Publishing a Crate

1. **Run security audits**
   ```bash
   cargo audit
   cargo deny check
   ```

2. **Validate unsafe code**
   - All unsafe blocks have safety comments
   - Unsafe functions document safety requirements
   - No unnecessary unsafe code

3. **Check for common vulnerabilities**
   - Integer overflow handling
   - Proper bounds checking
   - No use of deprecated/vulnerable dependencies

4. **Review error handling**
   - No sensitive information in error messages
   - Proper error propagation
   - No unwrap() in production code

5. **Verify input validation**
   - All external inputs validated
   - Use type system for validation where possible
   - Document validation requirements

## Further Reading

- [The Rustonomicon](https://doc.rust-lang.org/nomicon/) - The Dark Arts of Unsafe Rust
- [Rust API Guidelines](https://rust-lang.github.io/api-guidelines/) - Best practices for Rust APIs
- [RustSec Advisory Database](https://rustsec.org/) - Security advisories for Rust crates