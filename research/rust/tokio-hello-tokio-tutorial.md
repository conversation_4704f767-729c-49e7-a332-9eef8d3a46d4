# Hello Tokio Tutorial

## Overview

This tutorial walks through writing a basic Tokio application. It connects to the Mini-Redis server, sets the value of the key `hello` to `world`, and then reads back the key.

## Project Setup

### 1. Generate a New Crate

```bash
$ cargo new my-redis
$ cd my-redis
```

### 2. Add Dependencies

Edit `Cargo.toml` to add the following dependencies:

```toml
[dependencies]
tokio = { version = "1", features = ["full"] }
mini-redis = "0.4"
```

### 3. Write the Code

Replace the contents of `src/main.rs` with:

```rust
use mini_redis::{client, Result};

#[tokio::main]
async fn main() -> Result<()> {
    // Open a connection to the mini-redis address.
    let mut client = client::connect("127.0.0.1:6379").await?;

    // Set the key "hello" with value "world"
    client.set("hello", "world".into()).await?;

    // Get key "hello"
    let result = client.get("hello").await?;

    println!("got value from the server; result={:?}", result);

    Ok(())
}
```

### 4. Install and Run Mini-Redis

Install the Mini-Redis server:

```bash
$ cargo install mini-redis
```

Run the server:

```bash
$ mini-redis-server
```

### 5. Run Your Application

In another terminal window:

```bash
$ cargo run
```

Output should be:
```
got value from the server; result=Some(b"world")
```

## Breaking It Down

### Async Main Function

```rust
#[tokio::main]
async fn main() -> Result<()> {
    // async code here
}
```

The `#[tokio::main]` function is a macro. It transforms the async `main()` function into a synchronous `main()` function that initializes a runtime instance and executes the async main function.

### Under the Hood

The macro expands to something like:

```rust
fn main() {
    let mut rt = tokio::runtime::Runtime::new().unwrap();
    rt.block_on(async {
        // async main code here
    })
}
```

### Cargo Features

In `Cargo.toml`, we enable the `full` feature flag:

```toml
tokio = { version = "1", features = ["full"] }
```

Tokio has many features that can be enabled individually:
- `net`: TCP/UDP/Unix sockets
- `fs`: Filesystem operations  
- `sync`: Synchronization primitives
- `macros`: Procedural macros like `#[tokio::main]`
- `rt`: Tokio runtime
- `rt-multi-thread`: Multi-threaded runtime

## Async/Await Primer

### Key Concepts

1. **Async Functions**: Functions that execute asynchronously
   ```rust
   async fn say_world() {
       println!("world");
   }
   ```

2. **The `.await` Operator**: Waits for async operations to complete
   ```rust
   async fn main() {
       say_world().await;
   }
   ```

3. **Lazy Execution**: Async functions don't execute until awaited
   ```rust
   async fn main() {
       let op = say_world(); // Does NOT print "world"
       op.await;             // NOW it prints "world"
   }
   ```

### What is Asynchronous Programming?

Most programs perform I/O operations (network requests, file operations). These operations can be slow. Rather than blocking the thread while waiting, async programming allows other work to be done during the wait.

**Synchronous Example**:
```rust
use std::io::prelude::*;
use std::net::TcpStream;

fn main() {
    let mut stream = TcpStream::connect("127.0.0.1:6379").unwrap();
    
    stream.write(b"hello world").unwrap();
    stream.read(&mut [0; 128]).unwrap(); // Thread blocks here
}
```

**Asynchronous Example**:
```rust
use tokio::net::TcpStream;
use tokio::io::{AsyncWriteExt, AsyncReadExt};

#[tokio::main]
async fn main() {
    let mut stream = TcpStream::connect("127.0.0.1:6379").await.unwrap();
    
    stream.write_all(b"hello world").await.unwrap();
    stream.read(&mut [0; 128]).await.unwrap(); // Thread doesn't block
}
```

### Runtime

To run async functions, they must be executed by a runtime. The runtime contains:
- The async task scheduler
- Evented I/O drivers
- Timers

The runtime doesn't automatically start - it must be initialized explicitly. The `#[tokio::main]` macro handles this initialization for you.