# thiserror - Error Derivation Crate

## Overview

`thiserror` provides a convenient derive macro for the standard library's `std::error::Error` trait. It's designed to reduce boilerplate when defining custom error types in Rust libraries.

## Installation

```toml
[dependencies]
thiserror = "1.0"
```

## Basic Usage

### Simple Enum Errors

```rust
use thiserror::Error;

#[derive(Error, Debug)]
pub enum DataStoreError {
    #[error("data store disconnected")]
    Disconnect(#[from] std::io::Error),
    
    #[error("the data for key `{0}` is not available")]
    NotFound(String),
    
    #[error("invalid header (expected {expected:?}, found {found:?})")]
    InvalidHeader {
        expected: String,
        found: String,
    },
    
    #[error("unknown data store error")]
    Unknown,
}
```

### Struct Errors

```rust
use thiserror::Error;

#[derive(Erro<PERSON>, Debug)]
#[error("io error occurred at {path}")]
pub struct IoErrorWithPath {
    path: String,
    #[source]
    source: std::io::Error,
}
```

## Error Message Formatting

### Field Interpolation

```rust
#[derive(Error, Debug)]
pub enum Error {
    // Simple field reference
    #[error("invalid rdo_lookahead_frames {0} (expected < {max})", max = 10)]
    InvalidLookahead(u32),
    
    // Multiple fields
    #[error("first letter must be lowercase but was {first:?}")]
    InvalidFirstLetter {
        first: char,
    },
    
    // Tuple struct fields
    #[error("invalid index {0}")]
    OutOfBounds(usize),
}
```

### Advanced Formatting

```rust
#[derive(Error, Debug)]
pub enum Error {
    // Using methods on fields
    #[error("data size {size} exceeds maximum {}", .size.max())]
    TooLarge { size: DataSize },
    
    // Conditional messages
    #[error("file not found{}", if .path.is_some() { 
        format!(": {}", .path.as_ref().unwrap()) 
    } else { 
        String::new() 
    })]
    FileNotFound { path: Option<String> },
}
```

## Source Errors and From Conversions

### Using #[source]

```rust
#[derive(Error, Debug)]
pub enum Error {
    #[error("database error")]
    Database {
        #[source]
        error: sqlx::Error,
    },
    
    // Alternative: rename the field to 'source'
    #[error("network error")]
    Network {
        source: std::io::Error,
    },
}
```

### Using #[from]

```rust
#[derive(Error, Debug)]
pub enum Error {
    // Automatically implements From<std::io::Error>
    #[error("io error")]
    Io(#[from] std::io::Error),
    
    // With source field
    #[error("parse error")]
    Parse {
        #[from]
        source: std::num::ParseIntError,
    },
}

// Now you can use ? operator with io::Error
fn read_file() -> Result<String, Error> {
    let contents = std::fs::read_to_string("file.txt")?; // Converts io::Error to Error::Io
    Ok(contents)
}
```

## Transparent Errors

Use `#[error(transparent)]` to forward both `Display` and `source` to an underlying error:

```rust
#[derive(Error, Debug)]
pub enum Error {
    #[error(transparent)]
    Other(#[from] anyhow::Error),
}

// Or with structs
#[derive(Error, Debug)]
#[error(transparent)]
pub struct MyError(#[from] std::io::Error);
```

## Backtrace Support

Available on Rust 1.73+ with `std::backtrace::Backtrace`:

```rust
use std::backtrace::Backtrace;

#[derive(Error, Debug)]
pub enum Error {
    #[error("internal error")]
    Internal {
        #[backtrace]
        backtrace: Backtrace,
    },
}

// Or automatically capture with #[from]
#[derive(Error, Debug)]
pub enum Error {
    #[error("io error")]
    Io {
        #[from]
        source: std::io::Error,
        backtrace: Backtrace, // Automatically captured
    },
}
```

## Complex Examples

### Multi-layer Error Handling

```rust
use thiserror::Error;

#[derive(Error, Debug)]
pub enum ServiceError {
    #[error("validation failed: {0}")]
    Validation(String),
    
    #[error("authentication failed")]
    Authentication(#[from] AuthError),
    
    #[error("database operation failed")]
    Database(#[from] DatabaseError),
    
    #[error("external API error: {message}")]
    ExternalApi {
        message: String,
        #[source]
        error: reqwest::Error,
    },
}

#[derive(Error, Debug)]
pub enum AuthError {
    #[error("invalid credentials")]
    InvalidCredentials,
    
    #[error("token expired at {0}")]
    TokenExpired(chrono::DateTime<chrono::Utc>),
    
    #[error("insufficient permissions")]
    InsufficientPermissions,
}

#[derive(Error, Debug)]
pub enum DatabaseError {
    #[error("connection failed")]
    Connection(#[source] std::io::Error),
    
    #[error("query failed: {query}")]
    QueryFailed {
        query: String,
        #[source]
        error: sqlx::Error,
    },
    
    #[error("transaction rolled back")]
    TransactionRollback,
}
```

### Configuration Error Example

```rust
use thiserror::Error;
use std::path::PathBuf;

#[derive(Error, Debug)]
pub enum ConfigError {
    #[error("config file not found: {path}")]
    NotFound {
        path: PathBuf,
    },
    
    #[error("invalid configuration")]
    Invalid(#[from] toml::de::Error),
    
    #[error("missing required field: {field}")]
    MissingField {
        field: &'static str,
    },
    
    #[error("invalid value for {field}: {value}")]
    InvalidValue {
        field: &'static str,
        value: String,
    },
    
    #[error(transparent)]
    Io(#[from] std::io::Error),
}
```

## Best Practices

### 1. Use thiserror for Library Errors

```rust
// In a library crate
use thiserror::Error;

#[derive(Error, Debug)]
pub enum MyLibError {
    #[error("operation failed: {reason}")]
    OperationFailed { reason: String },
    
    #[error("invalid input: {0}")]
    InvalidInput(String),
}

// Public API returns specific error type
pub fn my_lib_function() -> Result<String, MyLibError> {
    // ...
}
```

### 2. Combine with anyhow for Applications

```rust
// In library
use thiserror::Error;

#[derive(Error, Debug)]
pub enum LibError {
    #[error("specific error: {0}")]
    Specific(String),
}

// In application
use anyhow::{Context, Result};

fn main() -> Result<()> {
    my_lib_function()
        .context("Failed to perform library operation")?;
    Ok(())
}
```

### 3. Provide Meaningful Error Messages

```rust
#[derive(Error, Debug)]
pub enum Error {
    // Good: Provides context
    #[error("failed to parse config file at {path}: {reason}")]
    ConfigParse { path: String, reason: String },
    
    // Avoid: Too generic
    #[error("error occurred")]
    Generic,
}
```

### 4. Use Source Chains Properly

```rust
#[derive(Error, Debug)]
pub enum Error {
    #[error("failed to read user data")]
    ReadUser {
        #[source]
        error: DatabaseError,
    },
}

// This allows error chaining:
// "failed to read user data"
//   caused by: "database connection timeout"
//     caused by: "io error: connection refused"
```

### 5. Consider Display vs Debug

```rust
#[derive(Error, Debug)]
pub enum Error {
    // Display for end users
    #[error("Unable to process your request. Please try again later.")]
    ProcessingError {
        // Debug info for developers
        #[source]
        internal: InternalError,
    },
}
```

## Common Patterns

### Wrapping External Errors

```rust
#[derive(Error, Debug)]
pub enum Error {
    #[error("network request failed")]
    Network(#[from] reqwest::Error),
    
    #[error("json parsing failed")]
    Json(#[from] serde_json::Error),
    
    #[error("database error")]
    Database(#[from] sqlx::Error),
}
```

### Validation Errors

```rust
#[derive(Error, Debug)]
pub enum ValidationError {
    #[error("field '{field}' is required")]
    Required { field: String },
    
    #[error("field '{field}' must be between {min} and {max}")]
    OutOfRange {
        field: String,
        min: i32,
        max: i32,
    },
    
    #[error("invalid email format: {0}")]
    InvalidEmail(String),
}
```

## Summary

`thiserror` is ideal for:
- Library error definitions
- Creating well-structured error hierarchies
- Reducing boilerplate in error implementations
- Maintaining good error hygiene in larger codebases

Key benefits:
- Automatic `Error` trait implementation
- Flexible error message formatting
- Source error chaining
- From conversions for ergonomic error handling
- Works seamlessly with the `?` operator