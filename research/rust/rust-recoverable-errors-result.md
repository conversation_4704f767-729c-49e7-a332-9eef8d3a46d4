# Recoverable Errors with Result

## The Result Enum

Most errors aren't serious enough to require the program to stop entirely. The `Result` enum is defined as having two variants, `Ok` and `Err`:

```rust
enum Result<T, E> {
    Ok(T),
    Err(E),
}
```

- `T` represents the type of the value that will be returned in a success case within the `Ok` variant
- `E` represents the type of the error that will be returned in a failure case within the `Err` variant

## Basic Example: Opening a File

```rust
use std::fs::File;

fn main() {
    let greeting_file_result = File::open("hello.txt");
}
```

The return type of `File::open` is `Result<File, Error>`:
- On success: Returns `Ok` instance containing a file handle
- On failure: Returns `Err` instance containing error information

## Handling Results with match

```rust
use std::fs::File;

fn main() {
    let greeting_file_result = File::open("hello.txt");

    let greeting_file = match greeting_file_result {
        Ok(file) => file,
        Err(error) => panic!("Problem opening the file: {error:?}"),
    };
}
```

## Matching on Different Errors

You can take different actions for different failure reasons:

```rust
use std::fs::File;
use std::io::ErrorKind;

fn main() {
    let greeting_file_result = File::open("hello.txt");

    let greeting_file = match greeting_file_result {
        Ok(file) => file,
        Err(error) => match error.kind() {
            ErrorKind::NotFound => match File::create("hello.txt") {
                Ok(fc) => fc,
                Err(e) => panic!("Problem creating the file: {e:?}"),
            },
            other_error => {
                panic!("Problem opening the file: {other_error:?}");
            }
        },
    };
}
```

### Alternative: Using Closures

A more concise approach using closures and the `unwrap_or_else` method:

```rust
use std::fs::File;
use std::io::ErrorKind;

fn main() {
    let greeting_file = File::open("hello.txt").unwrap_or_else(|error| {
        if error.kind() == ErrorKind::NotFound {
            File::create("hello.txt").unwrap_or_else(|error| {
                panic!("Problem creating the file: {error:?}");
            })
        } else {
            panic!("Problem opening the file: {error:?}");
        }
    });
}
```

## Shortcuts for Panic on Error

### unwrap()

If the `Result` value is `Ok`, `unwrap` returns the value inside. If it's `Err`, `unwrap` calls `panic!`:

```rust
use std::fs::File;

fn main() {
    let greeting_file = File::open("hello.txt").unwrap();
}
```

### expect()

Similar to `unwrap`, but lets you choose the panic error message:

```rust
use std::fs::File;

fn main() {
    let greeting_file = File::open("hello.txt")
        .expect("hello.txt should be included in this project");
}
```

## Propagating Errors

Instead of handling errors within a function, you can return the error to the calling code:

```rust
use std::fs::File;
use std::io::{self, Read};

fn read_username_from_file() -> Result<String, io::Error> {
    let username_file_result = File::open("hello.txt");

    let mut username_file = match username_file_result {
        Ok(file) => file,
        Err(e) => return Err(e),
    };

    let mut username = String::new();

    match username_file.read_to_string(&mut username) {
        Ok(_) => Ok(username),
        Err(e) => Err(e),
    }
}
```

## The ? Operator

A shortcut for propagating errors:

```rust
use std::fs::File;
use std::io::{self, Read};

fn read_username_from_file() -> Result<String, io::Error> {
    let mut username_file = File::open("hello.txt")?;
    let mut username = String::new();
    username_file.read_to_string(&mut username)?;
    Ok(username)
}
```

### How ? Works

- If the value is `Ok`, the value inside is returned
- If the value is `Err`, the error is returned from the whole function
- The error is converted using the `From` trait

### Chaining with ?

```rust
use std::fs::File;
use std::io::{self, Read};

fn read_username_from_file() -> Result<String, io::Error> {
    let mut username = String::new();

    File::open("hello.txt")?.read_to_string(&mut username)?;

    Ok(username)
}
```

### Even Shorter with fs::read_to_string

```rust
use std::fs;
use std::io;

fn read_username_from_file() -> Result<String, io::Error> {
    fs::read_to_string("hello.txt")
}
```

## Where The ? Operator Can Be Used

The `?` operator can only be used in functions that return `Result` or `Option` (or another type that implements `FromResidual`).

### Error: Using ? in main

This won't compile:

```rust
use std::fs::File;

fn main() {
    let greeting_file = File::open("hello.txt")?; // Error!
}
```

### Solution 1: Change main Return Type

```rust
use std::error::Error;
use std::fs::File;

fn main() -> Result<(), Box<dyn Error>> {
    let greeting_file = File::open("hello.txt")?;

    Ok(())
}
```

### Solution 2: Handle the Result

```rust
use std::fs::File;

fn main() {
    let greeting_file = match File::open("hello.txt") {
        Ok(file) => file,
        Err(error) => {
            eprintln!("Error opening file: {}", error);
            return;
        }
    };
}
```

## Using ? with Option

The `?` operator also works with `Option<T>`:

```rust
fn last_char_of_first_line(text: &str) -> Option<char> {
    text.lines().next()?.chars().last()
}
```

## Best Practices

1. **Use `Result` for operations that can fail**: File I/O, network operations, parsing
2. **Propagate errors when appropriate**: Let the caller decide how to handle errors
3. **Use `expect` with meaningful messages**: Help future debugging
4. **Convert between error types**: Use the `From` trait for error conversion
5. **Consider using error handling crates**: `anyhow` and `thiserror` for better ergonomics

## Summary

- `Result<T, E>` is used for recoverable errors
- Use `match` for explicit error handling
- Use `unwrap()` or `expect()` when panicking is appropriate
- Use `?` for error propagation
- The `?` operator can only be used in functions returning `Result` or `Option`