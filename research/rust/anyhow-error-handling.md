# Anyhow Error Handling Crate

## Overview

`anyhow` provides a trait object-based error type for easy idiomatic error handling in Rust applications. It's designed to make error handling ergonomic and straightforward, especially in application code.

## Installation

```toml
[dependencies]
anyhow = "1.0"
```

## Core Features

### 1. Simplified Error Type

Use `anyhow::Result<T>` as a return type for any function that may produce an error:

```rust
use anyhow::Result;

fn get_cluster_info() -> Result<ClusterMap> {
    let config = std::fs::read_to_string("cluster.json")?;
    let map: ClusterMap = serde_json::from_str(&config)?;
    Ok(map)
}
```

This is equivalent to `std::result::Result<T, anyhow::Error>`.

### 2. Error Propagation with ?

Works seamlessly with any error type that implements `std::error::Error`:

```rust
use anyhow::Result;

fn main() -> Result<()> {
    let config = std::fs::read_to_string("cluster.json")?;
    let map: ClusterMap = serde_json::from_str(&config)?;
    println!("cluster info: {:#?}", map);
    Ok(())
}
```

### 3. Adding Context to Errors

Use the `Context` trait to add context to errors:

```rust
use anyhow::{Context, Result};

fn main() -> Result<()> {
    let path = "cluster.json";
    let content = std::fs::read(path)
        .with_context(|| format!("Failed to read instrs from {}", path))?;
    Ok(())
}
```

Error output with context:
```
Error: Failed to read instrs from cluster.json

Caused by:
    No such file or directory (os error 2)
```

### 4. Creating Ad-hoc Errors

Use `anyhow!` macro for creating errors on the fly:

```rust
use anyhow::{anyhow, Result};

fn verify_user(user_id: u32) -> Result<()> {
    if user_id != 42 {
        return Err(anyhow!("Invalid user ID: {}", user_id));
    }
    Ok(())
}
```

### 5. Early Returns with bail!

Use `bail!` for early error returns:

```rust
use anyhow::{bail, Result};

fn check_range(x: i32) -> Result<()> {
    if x < 0 {
        bail!("x must be non-negative, got {}", x);
    }
    Ok(())
}
```

### 6. Conditional Errors with ensure!

Use `ensure!` for assertion-like error checking:

```rust
use anyhow::{ensure, Result};

fn divide(a: f64, b: f64) -> Result<f64> {
    ensure!(b != 0.0, "Cannot divide by zero");
    Ok(a / b)
}
```

## Error Downcasting

You can downcast errors to their concrete types:

```rust
use anyhow::{anyhow, Result};

#[derive(Debug)]
struct DataStoreError;

fn foo() -> Result<()> {
    Err(anyhow!(DataStoreError))
}

fn main() {
    match foo() {
        Ok(_) => {}
        Err(error) => {
            if let Some(e) = error.downcast_ref::<DataStoreError>() {
                // Handle DataStoreError specifically
                println!("Got a DataStoreError: {:?}", e);
            } else {
                // Handle other errors
                println!("Got another error: {}", error);
            }
        }
    }
}
```

## Working with Backtraces

Anyhow automatically captures backtraces (Rust 1.65+):

```rust
use anyhow::Result;

fn main() -> Result<()> {
    let result = do_something()?;
    Ok(())
}

fn do_something() -> Result<()> {
    std::env::set_var("RUST_BACKTRACE", "1"); // Enable backtraces
    anyhow::bail!("Something went wrong")
}
```

## Anyhow vs thiserror

### When to use `anyhow`:
- **Application code**: When you want simple, flexible error handling
- **Main functions**: Perfect for `main() -> Result<()>`
- **Rapid prototyping**: When you don't need structured errors yet
- **Scripts and CLI tools**: Where detailed error types aren't necessary

### When to use `thiserror`:
- **Library code**: When you need well-defined error types
- **Public APIs**: When consumers need to match on specific errors
- **Domain modeling**: When errors are part of your business logic

### Using Both Together

Common pattern: Use `thiserror` for defining errors, `anyhow` for handling them:

```rust
// Define errors with thiserror
use thiserror::Error;

#[derive(Error, Debug)]
pub enum DataStoreError {
    #[error("data store disconnected")]
    Disconnect(#[from] std::io::Error),
    #[error("the data for key `{0}` is not available")]
    NotFound(String),
}

// Use anyhow in application code
use anyhow::{Context, Result};

fn main() -> Result<()> {
    let data = fetch_data("key")
        .context("Failed to fetch critical data")?;
    Ok(())
}
```

## Best Practices

### 1. Add Context Liberally

```rust
use anyhow::{Context, Result};

fn process_file(path: &str) -> Result<String> {
    let content = std::fs::read_to_string(path)
        .context("Failed to read file")?;
    
    let processed = parse_content(&content)
        .with_context(|| format!("Failed to parse content from {}", path))?;
    
    Ok(processed)
}
```

### 2. Use Specific Error Types in Libraries

```rust
// In library code
pub fn parse_config(data: &str) -> std::result::Result<Config, ParseError> {
    // ...
}

// In application code
use anyhow::{Context, Result};

fn load_config() -> Result<Config> {
    let data = std::fs::read_to_string("config.toml")?;
    let config = parse_config(&data)
        .context("Invalid configuration format")?;
    Ok(config)
}
```

### 3. Chain Multiple Contexts

```rust
use anyhow::{Context, Result};

fn complex_operation() -> Result<()> {
    step_one()
        .context("Step one failed")?;
    
    step_two()
        .context("Step two failed")
        .context("Complex operation aborted")?;
    
    Ok(())
}
```

### 4. Format Errors for Display

```rust
use anyhow::Result;

fn main() {
    if let Err(error) = run() {
        eprintln!("Error: {:?}", error); // Full error chain with backtrace
        eprintln!("Error: {:#}", error);  // Pretty-printed error chain
        
        // Print each error in the chain
        eprintln!("Error: {}", error);
        for cause in error.chain().skip(1) {
            eprintln!("  Caused by: {}", cause);
        }
    }
}
```

## No-std Support

For no-std environments:

```toml
[dependencies]
anyhow = { version = "1.0", default-features = false }
```

Note: This disables `std::error::Error` integration and backtrace support.

## Summary

`anyhow` is ideal for:
- Application-level error handling
- Rapid development and prototyping
- CLI tools and scripts
- Any situation where you want simple, ergonomic error handling

Key benefits:
- Single error type for all errors
- Automatic error conversion with `?`
- Rich context and error chaining
- Backtrace support
- Works well with other error handling approaches