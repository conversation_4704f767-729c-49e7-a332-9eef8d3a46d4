# Tokio Spawning Tutorial

## Overview

A Tokio task is an asynchronous green thread. They are created by passing an `async` block to `tokio::spawn`. The `tokio::spawn` function returns a `Join<PERSON>andle`, which the caller may use to interact with the spawned task.

## Basic Spawning Example

```rust
#[tokio::main]
async fn main() {
    let handle = tokio::spawn(async {
        // do some async work
        "return value"
    });

    // Do some other work

    let out = handle.await.unwrap();
    println!("GOT {}", out);
}
```

## Accepting Connections Concurrently

To process connections concurrently, a new task is spawned for each inbound connection:

```rust
use tokio::net::{TcpListener, TcpStream};
use std::io;

#[tokio::main]
async fn main() -> io::Result<()> {
    let listener = TcpListener::bind("127.0.0.1:6379").await?;

    loop {
        let (socket, _) = listener.accept().await?;
        
        // A new task is spawned for each inbound socket
        tokio::spawn(async move {
            process(socket).await;
        });
    }
}
```

## Building a Mini-Redis Server

### Dependencies

```toml
[dependencies]
tokio = { version = "1", features = ["full"] }
mini-redis = "0.4"
```

### Full Server Implementation

```rust
use tokio::net::{TcpListener, TcpStream};
use mini_redis::{Connection, Frame, Command};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};

#[tokio::main]
async fn main() {
    let listener = TcpListener::bind("127.0.0.1:6379").await.unwrap();

    let db = Arc::new(Mutex::new(HashMap::new()));

    loop {
        let (socket, _) = listener.accept().await.unwrap();
        let db = db.clone();

        tokio::spawn(async move {
            process(socket, db).await;
        });
    }
}

async fn process(socket: TcpStream, db: Arc<Mutex<HashMap<String, Vec<u8>>>>) {
    use mini_redis::Command::{self, Get, Set};

    let mut connection = Connection::new(socket);

    while let Some(frame) = connection.read_frame().await.unwrap() {
        let response = match Command::from_frame(frame).unwrap() {
            Set(cmd) => {
                let mut db = db.lock().unwrap();
                db.insert(cmd.key().to_string(), cmd.value().to_vec());
                Frame::Simple("OK".to_string())
            }
            Get(cmd) => {
                let db = db.lock().unwrap();
                if let Some(value) = db.get(cmd.key()) {
                    Frame::Bulk(value.clone().into())
                } else {
                    Frame::Null
                }
            }
            cmd => panic!("unimplemented {:?}", cmd),
        };

        connection.write_frame(&response).await.unwrap();
    }
}
```

## Task Constraints

### The `'static` Bound

When you spawn a task on the Tokio runtime, its type's lifetime must be `'static`. This means the spawned task must not contain any references to data owned outside the task.

**This will NOT compile:**
```rust
#[tokio::main]
async fn main() {
    let v = vec![1, 2, 3];

    tokio::spawn(async {
        println!("Here's a vec: {:?}", v); // Error: v doesn't live long enough
    });
}
```

**Fixed version with `move`:**
```rust
#[tokio::main]
async fn main() {
    let v = vec![1, 2, 3];

    tokio::spawn(async move {
        println!("Here's a vec: {:?}", v); // Ownership transferred
    });
}
```

### The `Send` Bound

Tasks spawned by `tokio::spawn` must implement `Send`. This allows the Tokio runtime to move tasks between threads.

**Common error with `Rc`:**
```rust
use std::rc::Rc;

#[tokio::main]
async fn main() {
    tokio::spawn(async {
        let rc = Rc::new("hello");
        println!("{}", rc); // Error: Rc is not Send
    });
}
```

**Solution: Use `Arc` instead:**
```rust
use std::sync::Arc;

#[tokio::main]
async fn main() {
    tokio::spawn(async {
        let arc = Arc::new("hello");
        println!("{}", arc); // Works: Arc is Send
    });
}
```

## Important Notes

### Tasks vs Threads

- A Tokio task is very lightweight (requires only 64 bytes of allocation)
- Creating millions of tasks is possible
- Tasks are scheduled cooperatively by the Tokio runtime
- Spawning a task is significantly cheaper than spawning a thread

### When to Use Spawn

Use `tokio::spawn` when you want:
- Concurrent execution of independent operations
- To process multiple requests simultaneously
- Background tasks that don't block the main flow

### Error Handling

The `JoinHandle` returned by `spawn` implements `Future<Output = Result<T, JoinError>>`:

```rust
let handle = tokio::spawn(async {
    panic!("something bad happened!")
});

// This will return Err
match handle.await {
    Ok(_) => println!("Task completed successfully"),
    Err(e) => println!("Task panicked: {}", e),
}
```

### The `move` Keyword

When spawning tasks, you'll almost always need `move` to transfer ownership:

```rust
let value = String::from("hello");

tokio::spawn(async move {
    // value is now owned by this task
    println!("{}", value);
});
```