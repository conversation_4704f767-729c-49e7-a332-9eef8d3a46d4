# Tokio Channels Tutorial

## Overview

Tokio provides a number of channels that can be used to pass messages between tasks. This chapter explores message passing as an alternative to shared state for managing concurrent data access.

## Channel Types in Tokio

### 1. mpsc (Multi-Producer, Single-Consumer)
- Many values can be sent
- Many producers, one consumer
- Created with capacity: `mpsc::channel(32)`

### 2. oneshot
- Single value send
- Optimized for one-time use
- Perfect for request/response patterns

### 3. broadcast
- Multi-producer, multi-consumer
- Each receiver sees every value
- Good for event notifications

### 4. watch
- Multi-producer, multi-consumer
- No history kept
- Receivers only see most recent value

## Basic MPSC Example

```rust
use tokio::sync::mpsc;

#[tokio::main]
async fn main() {
    let (tx, mut rx) = mpsc::channel(32);
    let tx2 = tx.clone();

    tokio::spawn(async move {
        tx.send("sending from first task").await.unwrap();
    });

    tokio::spawn(async move {
        tx2.send("sending from second task").await.unwrap();
    });

    while let Some(message) = rx.recv().await {
        println!("GOT = {}", message);
    }
}
```

## Message Passing Pattern for Redis

### Define Message Types

```rust
use bytes::Bytes;
use mini_redis::client;
use tokio::sync::{mpsc, oneshot};

#[derive(Debug)]
enum Command {
    Get {
        key: String,
        resp: Responder<Option<Bytes>>,
    },
    Set {
        key: String,
        val: Bytes,
        resp: Responder<()>,
    },
}

type Responder<T> = oneshot::Sender<mini_redis::Result<T>>;
```

### Spawn Manager Task

```rust
#[tokio::main]
async fn main() {
    let (tx, mut rx) = mpsc::channel(32);

    // Spawn manager task
    let manager = tokio::spawn(async move {
        // Establish connection to server
        let mut client = client::connect("127.0.0.1:6379").await.unwrap();

        while let Some(cmd) = rx.recv().await {
            match cmd {
                Command::Get { key, resp } => {
                    let res = client.get(&key).await;
                    let _ = resp.send(res);
                }
                Command::Set { key, val, resp } => {
                    let res = client.set(&key, val).await;
                    let _ = resp.send(res);
                }
            }
        }
    });

    // Clone transmitters for multiple tasks
    let tx2 = tx.clone();

    // Spawn tasks to send commands
    let t1 = tokio::spawn(async move {
        let (resp_tx, resp_rx) = oneshot::channel();
        let cmd = Command::Get {
            key: "foo".to_string(),
            resp: resp_tx,
        };

        if tx.send(cmd).await.is_err() {
            eprintln!("connection task shutdown");
            return;
        }

        let res = resp_rx.await;
        println!("GOT (Get) = {:?}", res);
    });

    let t2 = tokio::spawn(async move {
        let (resp_tx, resp_rx) = oneshot::channel();
        let cmd = Command::Set {
            key: "foo".to_string(),
            val: "bar".into(),
            resp: resp_tx,
        };

        if tx2.send(cmd).await.is_err() {
            eprintln!("connection task shutdown");
            return;
        }

        let res = resp_rx.await;
        println!("GOT (Set) = {:?}", res);
    });

    t1.await.unwrap();
    t2.await.unwrap();
    manager.await.unwrap();
}
```

## Backpressure and Bounded Channels

### The Problem

Without bounds, tasks can send messages faster than they can be processed:

```rust
// Unbounded channel - DANGEROUS!
let (tx, mut rx) = mpsc::unbounded_channel();

tokio::spawn(async move {
    while let Some(message) = rx.recv().await {
        // Slow processing
        tokio::time::sleep(Duration::from_millis(100)).await;
    }
});

// Fast sending can cause memory issues
for i in 0..10000 {
    tx.send(i).await.unwrap();
}
```

### The Solution: Bounded Channels

```rust
// Bounded channel with capacity of 32
let (tx, mut rx) = mpsc::channel(32);

// Send will wait if channel is full
tx.send(value).await.unwrap();
```

### Choosing Channel Capacity

Factors to consider:
1. **Expected message rate**: Higher rates need larger buffers
2. **Processing time**: Slower processing needs larger buffers
3. **Memory constraints**: Larger buffers use more memory
4. **Latency requirements**: Smaller buffers provide faster feedback

Common patterns:
- **Small (1-10)**: Tight coupling, immediate backpressure
- **Medium (32-128)**: Good default for most applications
- **Large (1000+)**: Buffering bursts, but watch memory usage

## Actor Model Pattern

Encapsulate state management in an actor:

```rust
struct DbActor {
    receiver: mpsc::Receiver<Command>,
    db: HashMap<String, Bytes>,
}

impl DbActor {
    fn new(receiver: mpsc::Receiver<Command>) -> Self {
        DbActor {
            receiver,
            db: HashMap::new(),
        }
    }

    async fn run(&mut self) {
        while let Some(cmd) = self.receiver.recv().await {
            match cmd {
                Command::Get { key, resp } => {
                    let value = self.db.get(&key).cloned();
                    let _ = resp.send(Ok(value));
                }
                Command::Set { key, val, resp } => {
                    self.db.insert(key, val);
                    let _ = resp.send(Ok(()));
                }
            }
        }
    }
}
```

## Advanced Patterns

### Select Between Multiple Channels

```rust
use tokio::sync::mpsc;
use tokio::select;

#[tokio::main]
async fn main() {
    let (tx1, mut rx1) = mpsc::channel::<&str>(128);
    let (tx2, mut rx2) = mpsc::channel::<&str>(128);

    tokio::spawn(async move {
        loop {
            select! {
                Some(msg) = rx1.recv() => {
                    println!("Received from rx1: {}", msg);
                }
                Some(msg) = rx2.recv() => {
                    println!("Received from rx2: {}", msg);
                }
                else => break,
            }
        }
    });
}
```

### Timeout on Channel Operations

```rust
use tokio::time::{timeout, Duration};

let result = timeout(
    Duration::from_secs(1),
    rx.recv()
).await;

match result {
    Ok(Some(msg)) => println!("Got message: {}", msg),
    Ok(None) => println!("Channel closed"),
    Err(_) => println!("Timeout!"),
}
```

## Best Practices

1. **Choose the Right Channel Type**
   - `mpsc`: General purpose, most common
   - `oneshot`: Request/response patterns
   - `broadcast`: Event notifications
   - `watch`: Latest state updates

2. **Handle Errors Properly**
   ```rust
   if tx.send(msg).await.is_err() {
       // Receiver dropped, handle gracefully
       return;
   }
   ```

3. **Avoid Unbounded Channels**
   - Always prefer bounded channels
   - Unbounded can cause memory issues
   - Bounded channels provide natural backpressure

4. **Clean Shutdown**
   ```rust
   // Drop all senders to signal shutdown
   drop(tx);
   
   // Receiver will return None when all senders dropped
   while let Some(msg) = rx.recv().await {
       // Process remaining messages
   }
   ```

5. **Consider Alternatives**
   - Shared state with mutex for simple cases
   - Channels for complex coordination
   - Actor pattern for encapsulated state

## Summary

Message passing with channels provides:
- **Isolation**: No shared memory between tasks
- **Type safety**: Compile-time guarantees
- **Flexibility**: Multiple communication patterns
- **Backpressure**: Natural flow control with bounded channels

Choose channels when you need:
- Complex coordination between tasks
- Isolation of mutable state
- Natural ordering of operations
- Built-in backpressure handling