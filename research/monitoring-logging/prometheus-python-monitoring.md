# Prometheus Python Monitoring and Production Instrumentation

**Sources**: 
- Prometheus Python Client: https://github.com/prometheus/client_python
- Official Prometheus documentation and production patterns
**Scraped Date**: 2025-07-15
**Trust Score**: 9.7 (Official Prometheus Library)

## Table of Contents

1. [Installation and Basic Setup](#installation-and-basic-setup)
2. [Core Metric Types](#core-metric-types)
3. [Production Instrumentation Patterns](#production-instrumentation-patterns)
4. [HTTP Server Integration](#http-server-integration)
5. [Multiprocess Applications](#multiprocess-applications)
6. [Custom Collectors](#custom-collectors)
7. [FastAPI Integration](#fastapi-integration)
8. [Advanced Features](#advanced-features)
9. [Production Best Practices](#production-best-practices)
10. [Error Handling and Debugging](#error-handling-and-debugging)

## Installation and Basic Setup

### Installation

```bash
# Install prometheus client
pip install prometheus-client

# For performance optimizations
pip install "prometheus-client[twisted]"

# For multiprocess support
pip install prometheus-client
```

### Basic Setup

```python
from prometheus_client import start_http_server, Counter, Histogram, Gauge, Summary
import time
import random

# Start metrics server
start_http_server(8000)

# Basic usage example
REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint'])
REQUEST_LATENCY = Histogram('http_request_duration_seconds', 'HTTP request latency')

# Usage
REQUEST_COUNT.labels(method='GET', endpoint='/api/users').inc()
REQUEST_LATENCY.observe(0.25)
```

## Core Metric Types

### Counter - Monotonically Increasing Values

```python
from prometheus_client import Counter

# Basic counter
http_requests = Counter('http_requests_total', 'Total HTTP requests')

# Counter with labels
http_requests_labeled = Counter(
    'http_requests_total', 
    'Total HTTP requests', 
    ['method', 'endpoint', 'status']
)

# Usage
http_requests.inc()                # Increment by 1
http_requests.inc(5)              # Increment by 5

# With labels
http_requests_labeled.labels('GET', '/api/users', '200').inc()
http_requests_labeled.labels(method='POST', endpoint='/api/orders', status='201').inc()

# Exception counting
@http_requests.count_exceptions()
def risky_operation():
    if random.random() < 0.1:
        raise ValueError("Random error")
    return "success"

# Context manager for exception counting
with http_requests.count_exceptions():
    risky_operation()

# Count specific exception types
with http_requests.count_exceptions(ValueError):
    risky_operation()
```

### Gauge - Up and Down Values

```python
from prometheus_client import Gauge

# Basic gauge
active_connections = Gauge('active_connections', 'Current active connections')
memory_usage = Gauge('memory_usage_bytes', 'Current memory usage in bytes')

# Gauge with labels
queue_size = Gauge('queue_size', 'Current queue size', ['queue_name'])

# Usage
active_connections.set(42)        # Set to specific value
active_connections.inc()          # Increment by 1
active_connections.inc(5)         # Increment by 5
active_connections.dec()          # Decrement by 1
active_connections.dec(3)         # Decrement by 3

# Set to current time
active_connections.set_to_current_time()

# Track in-progress operations
@active_connections.track_inprogress()
def long_running_task():
    time.sleep(2)
    return "completed"

# Context manager
with active_connections.track_inprogress():
    time.sleep(1)

# Callback-based gauge
task_dict = {'pending': 10, 'running': 5, 'completed': 100}
pending_tasks = Gauge('pending_tasks', 'Number of pending tasks')
pending_tasks.set_function(lambda: task_dict['pending'])
```

### Histogram - Request Duration and Size Distributions

```python
from prometheus_client import Histogram

# Request latency histogram
request_latency = Histogram(
    'http_request_duration_seconds',
    'HTTP request latency distribution',
    ['method', 'endpoint']
)

# Custom buckets for specific use case
response_size = Histogram(
    'http_response_size_bytes',
    'HTTP response size distribution',
    buckets=(100, 1000, 10000, 100000, 1000000, float('inf'))
)

# Usage
request_latency.observe(0.25)     # Record 250ms latency
request_latency.labels('GET', '/api/users').observe(0.1)

# Timing decorator
@request_latency.time()
def handle_request():
    time.sleep(random.uniform(0.1, 0.5))
    return "response"

# Context manager
with request_latency.time():
    handle_request()

# Custom timing
start_time = time.time()
handle_request()
request_latency.observe(time.time() - start_time)
```

### Summary - Request Duration and Size with Quantiles

```python
from prometheus_client import Summary

# Request latency summary with quantiles
request_latency = Summary(
    'http_request_duration_seconds',
    'HTTP request latency summary',
    ['method', 'endpoint']
)

# Custom quantiles
custom_summary = Summary(
    'processing_time_seconds',
    'Processing time summary',
    registry=None,  # Use custom registry if needed
)

# Usage
request_latency.observe(0.25)
request_latency.labels('POST', '/api/orders').observe(0.15)

# Timing patterns (same as Histogram)
@request_latency.time()
def process_order():
    time.sleep(random.uniform(0.05, 0.3))
    return "processed"

with request_latency.time():
    process_order()
```

### Info - Build and Version Information

```python
from prometheus_client import Info

# Application info
app_info = Info('app_info', 'Application information')
app_info.info({
    'version': '1.2.3',
    'build_date': '2025-01-15',
    'git_commit': 'abc123',
    'environment': 'production'
})

# Python runtime info
python_info = Info('python_info', 'Python runtime information')
import sys
python_info.info({
    'version': sys.version,
    'implementation': sys.implementation.name,
    'platform': sys.platform
})
```

### Enum - State Tracking

```python
from prometheus_client import Enum

# Task state tracking
task_state = Enum(
    'background_task_state',
    'Current state of background task',
    states=['idle', 'running', 'failed', 'completed']
)

# Usage
task_state.state('running')
task_state.state('completed')

# With labels
service_state = Enum(
    'service_state',
    'Current service state',
    ['service_name'],
    states=['starting', 'healthy', 'unhealthy', 'stopping']
)

service_state.labels('database').state('healthy')
service_state.labels('cache').state('unhealthy')
```

## Production Instrumentation Patterns

### FastAPI Application Instrumentation

```python
from fastapi import FastAPI, Request, Response
from prometheus_client import Counter, Histogram, Gauge, generate_latest, CONTENT_TYPE_LATEST
import time

app = FastAPI()

# Metrics
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status_code']
)

REQUEST_LATENCY = Histogram(
    'http_request_duration_seconds',
    'HTTP request latency',
    ['method', 'endpoint']
)

ACTIVE_REQUESTS = Gauge(
    'http_requests_active',
    'Currently active HTTP requests'
)

@app.middleware("http")
async def prometheus_middleware(request: Request, call_next):
    start_time = time.time()
    
    # Track active requests
    ACTIVE_REQUESTS.inc()
    
    try:
        response = await call_next(request)
        
        # Record metrics
        REQUEST_COUNT.labels(
            method=request.method,
            endpoint=request.url.path,
            status_code=response.status_code
        ).inc()
        
        REQUEST_LATENCY.labels(
            method=request.method,
            endpoint=request.url.path
        ).observe(time.time() - start_time)
        
        return response
    
    finally:
        ACTIVE_REQUESTS.dec()

@app.get("/metrics")
async def get_metrics():
    return Response(
        generate_latest(),
        media_type=CONTENT_TYPE_LATEST
    )

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.get("/api/users")
async def get_users():
    # Simulate some processing
    await asyncio.sleep(0.1)
    return {"users": []}
```

### Database Connection Pool Monitoring

```python
from prometheus_client import Gauge, Counter, Histogram
import asyncpg
import time

# Database metrics
DB_CONNECTIONS_ACTIVE = Gauge(
    'db_connections_active',
    'Active database connections'
)

DB_CONNECTIONS_IDLE = Gauge(
    'db_connections_idle', 
    'Idle database connections'
)

DB_QUERY_DURATION = Histogram(
    'db_query_duration_seconds',
    'Database query duration',
    ['operation', 'table']
)

DB_QUERY_COUNT = Counter(
    'db_queries_total',
    'Total database queries',
    ['operation', 'table', 'status']
)

class MonitoredConnectionPool:
    def __init__(self, pool):
        self.pool = pool
    
    async def execute_query(self, query, *args, operation="select", table="unknown"):
        start_time = time.time()
        DB_CONNECTIONS_ACTIVE.inc()
        
        try:
            async with self.pool.acquire() as conn:
                result = await conn.fetch(query, *args)
                
            DB_QUERY_COUNT.labels(
                operation=operation,
                table=table,
                status="success"
            ).inc()
            
            return result
            
        except Exception as e:
            DB_QUERY_COUNT.labels(
                operation=operation,
                table=table,
                status="error"
            ).inc()
            raise
        
        finally:
            DB_CONNECTIONS_ACTIVE.dec()
            DB_QUERY_DURATION.labels(
                operation=operation,
                table=table
            ).observe(time.time() - start_time)
    
    def update_pool_metrics(self):
        """Call this periodically to update pool metrics"""
        if hasattr(self.pool, '_queue'):
            DB_CONNECTIONS_IDLE.set(self.pool._queue.qsize())

# Usage
async def create_monitored_pool():
    pool = await asyncpg.create_pool(
        "postgresql://user:pass@localhost/db",
        min_size=5,
        max_size=20
    )
    return MonitoredConnectionPool(pool)
```

### Cache Performance Monitoring

```python
from prometheus_client import Counter, Histogram, Gauge
import redis
import time
import functools

# Cache metrics
CACHE_OPERATIONS = Counter(
    'cache_operations_total',
    'Total cache operations',
    ['operation', 'status']
)

CACHE_LATENCY = Histogram(
    'cache_operation_duration_seconds',
    'Cache operation latency',
    ['operation']
)

CACHE_SIZE = Gauge(
    'cache_size_bytes',
    'Current cache size in bytes'
)

CACHE_HIT_RATIO = Gauge(
    'cache_hit_ratio',
    'Cache hit ratio'
)

class MonitoredRedisClient:
    def __init__(self, redis_client):
        self.client = redis_client
        self.hits = 0
        self.misses = 0
    
    def _update_hit_ratio(self):
        total = self.hits + self.misses
        if total > 0:
            CACHE_HIT_RATIO.set(self.hits / total)
    
    async def get(self, key):
        start_time = time.time()
        
        try:
            result = await self.client.get(key)
            
            if result is not None:
                self.hits += 1
                CACHE_OPERATIONS.labels('get', 'hit').inc()
            else:
                self.misses += 1
                CACHE_OPERATIONS.labels('get', 'miss').inc()
            
            self._update_hit_ratio()
            return result
            
        except Exception as e:
            CACHE_OPERATIONS.labels('get', 'error').inc()
            raise
        
        finally:
            CACHE_LATENCY.labels('get').observe(time.time() - start_time)
    
    async def set(self, key, value, *args, **kwargs):
        start_time = time.time()
        
        try:
            result = await self.client.set(key, value, *args, **kwargs)
            CACHE_OPERATIONS.labels('set', 'success').inc()
            return result
            
        except Exception as e:
            CACHE_OPERATIONS.labels('set', 'error').inc()
            raise
        
        finally:
            CACHE_LATENCY.labels('set').observe(time.time() - start_time)

# Decorator for automatic cache monitoring
def monitor_cache_function(cache_client):
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = await func(*args, **kwargs)
                CACHE_OPERATIONS.labels('function', 'success').inc()
                return result
            
            except Exception as e:
                CACHE_OPERATIONS.labels('function', 'error').inc()
                raise
            
            finally:
                CACHE_LATENCY.labels('function').observe(time.time() - start_time)
        
        return wrapper
    return decorator
```

## HTTP Server Integration

### Basic HTTP Server

```python
from prometheus_client import start_http_server, generate_latest, CONTENT_TYPE_LATEST

# Start server on port 8000
start_http_server(8000)

# With custom address
start_http_server(8080, addr='0.0.0.0')

# HTTPS server
start_http_server(8443, certfile="server.crt", keyfile="server.key")

# Graceful shutdown
server, thread = start_http_server(8000)
# ... application code ...
server.shutdown()
thread.join()
```

### WSGI Integration

```python
from prometheus_client import make_wsgi_app, start_wsgi_server
from wsgiref.simple_server import make_server

# Create WSGI app
app = make_wsgi_app()

# Serve with wsgiref
httpd = make_server('', 8000, app)
httpd.serve_forever()

# Or start in thread
start_wsgi_server(8000)
```

### Flask Integration

```python
from flask import Flask
from werkzeug.middleware.dispatcher import DispatcherMiddleware
from prometheus_client import make_wsgi_app

app = Flask(__name__)

# Mount metrics endpoint
app.wsgi_app = DispatcherMiddleware(app.wsgi_app, {
    '/metrics': make_wsgi_app()
})

@app.route('/')
def hello():
    return 'Hello World!'

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
```

## Multiprocess Applications

### Gunicorn Configuration

```python
# gunicorn_config.py
from prometheus_client import multiprocess

def child_exit(server, worker):
    """Mark worker as dead when it exits"""
    multiprocess.mark_process_dead(worker.pid)

bind = "0.0.0.0:8000"
workers = 4
worker_class = "uvicorn.workers.UvicornWorker"
```

### FastAPI with Multiprocess Support

```python
from fastapi import FastAPI
from prometheus_client import multiprocess, CollectorRegistry, make_asgi_app
import os

app = FastAPI()

# Environment variable for multiprocess
os.environ['PROMETHEUS_MULTIPROC_DIR'] = '/tmp/prometheus_multiproc'

def make_metrics_app():
    """Create metrics app for multiprocess"""
    registry = CollectorRegistry()
    multiprocess.MultiProcessCollector(registry)
    return make_asgi_app(registry=registry)

# Mount metrics endpoint
metrics_app = make_metrics_app()
app.mount("/metrics", metrics_app)

# Application metrics
from prometheus_client import Counter, Gauge

REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total requests',
    ['method', 'endpoint']
)

WORKER_PROCESSES = Gauge(
    'worker_processes',
    'Number of worker processes',
    multiprocess_mode='livesum'
)

@app.on_event("startup")
async def startup():
    WORKER_PROCESSES.inc()

@app.on_event("shutdown") 
async def shutdown():
    WORKER_PROCESSES.dec()
```

### Multiprocess Gauge Modes

```python
from prometheus_client import Gauge

# Sum values from all processes
total_connections = Gauge(
    'total_connections',
    'Total connections across all workers',
    multiprocess_mode='sum'
)

# Sum values only from living processes
active_connections = Gauge(
    'active_connections', 
    'Active connections from living workers',
    multiprocess_mode='livesum'
)

# Use maximum value across processes
max_memory = Gauge(
    'max_memory_usage',
    'Maximum memory usage',
    multiprocess_mode='max'
)

# Use minimum value across processes  
min_response_time = Gauge(
    'min_response_time',
    'Minimum response time',
    multiprocess_mode='min'
)

# Use most recent value
latest_timestamp = Gauge(
    'latest_update',
    'Latest update timestamp', 
    multiprocess_mode='livelatest'
)
```

## Custom Collectors

### Basic Custom Collector

```python
from prometheus_client.core import GaugeMetricFamily, CounterMetricFamily
from prometheus_client.registry import Collector, REGISTRY
import psutil

class SystemMetricsCollector(Collector):
    """Collect system metrics using psutil"""
    
    def collect(self):
        # CPU usage
        cpu_usage = GaugeMetricFamily(
            'system_cpu_usage_percent',
            'CPU usage percentage',
            labels=['cpu']
        )
        
        for i, percent in enumerate(psutil.cpu_percent(percpu=True)):
            cpu_usage.add_metric([f'cpu{i}'], percent)
        
        yield cpu_usage
        
        # Memory usage
        memory = psutil.virtual_memory()
        memory_usage = GaugeMetricFamily(
            'system_memory_usage_bytes',
            'Memory usage in bytes',
            labels=['type']
        )
        
        memory_usage.add_metric(['total'], memory.total)
        memory_usage.add_metric(['available'], memory.available)
        memory_usage.add_metric(['used'], memory.used)
        memory_usage.add_metric(['free'], memory.free)
        
        yield memory_usage
        
        # Disk usage
        disk_usage = GaugeMetricFamily(
            'system_disk_usage_bytes',
            'Disk usage in bytes',
            labels=['mountpoint', 'type']
        )
        
        for partition in psutil.disk_partitions():
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                disk_usage.add_metric([partition.mountpoint, 'total'], usage.total)
                disk_usage.add_metric([partition.mountpoint, 'used'], usage.used)
                disk_usage.add_metric([partition.mountpoint, 'free'], usage.free)
            except PermissionError:
                continue
        
        yield disk_usage

# Register the collector
REGISTRY.register(SystemMetricsCollector())
```

### Database Metrics Collector

```python
from prometheus_client.core import GaugeMetricFamily, CounterMetricFamily
from prometheus_client.registry import Collector
import asyncpg
import asyncio

class PostgreSQLMetricsCollector(Collector):
    """Collect PostgreSQL metrics"""
    
    def __init__(self, connection_string):
        self.connection_string = connection_string
    
    def collect(self):
        """Collect metrics synchronously"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            metrics = loop.run_until_complete(self._collect_async())
            for metric in metrics:
                yield metric
        finally:
            loop.close()
    
    async def _collect_async(self):
        """Async metrics collection"""
        metrics = []
        
        try:
            conn = await asyncpg.connect(self.connection_string)
            
            # Connection count
            result = await conn.fetchrow("""
                SELECT count(*) as connections,
                       count(*) FILTER (WHERE state = 'active') as active,
                       count(*) FILTER (WHERE state = 'idle') as idle
                FROM pg_stat_activity
            """)
            
            connections = GaugeMetricFamily(
                'postgresql_connections',
                'PostgreSQL connections',
                labels=['state']
            )
            
            connections.add_metric(['total'], result['connections'])
            connections.add_metric(['active'], result['active'])
            connections.add_metric(['idle'], result['idle'])
            metrics.append(connections)
            
            # Database size
            db_sizes = await conn.fetch("""
                SELECT datname, pg_database_size(datname) as size
                FROM pg_database
                WHERE datistemplate = false
            """)
            
            database_size = GaugeMetricFamily(
                'postgresql_database_size_bytes',
                'PostgreSQL database size in bytes',
                labels=['database']
            )
            
            for row in db_sizes:
                database_size.add_metric([row['datname']], row['size'])
            
            metrics.append(database_size)
            
            await conn.close()
            
        except Exception as e:
            # Return error metric
            error_metric = GaugeMetricFamily(
                'postgresql_collector_errors_total',
                'PostgreSQL collector errors'
            )
            error_metric.add_metric([], 1)
            metrics.append(error_metric)
        
        return metrics

# Usage
collector = PostgreSQLMetricsCollector("postgresql://user:pass@localhost/db")
REGISTRY.register(collector)
```

## Advanced Features

### Exemplars Support

```python
from prometheus_client import Counter, Histogram

# Counter with exemplars
request_counter = Counter(
    'http_requests_total',
    'HTTP requests',
    ['method', 'endpoint']
)

# Add exemplar with trace ID
request_counter.labels('GET', '/api/users').inc(
    exemplar={'trace_id': 'abc123', 'span_id': 'def456'}
)

# Histogram with exemplars
request_latency = Histogram(
    'http_request_duration_seconds',
    'Request duration'
)

# Observe with exemplar
request_latency.observe(0.25, {'trace_id': 'trace123'})
```

### Registry Management

```python
from prometheus_client import CollectorRegistry, Counter, generate_latest

# Custom registry
custom_registry = CollectorRegistry()

# Metrics with custom registry
app_requests = Counter(
    'app_requests_total',
    'Application requests',
    registry=custom_registry
)

# Generate metrics for custom registry only
metrics_output = generate_latest(custom_registry)

# Restricted registry (filter metrics)
from prometheus_client import REGISTRY
restricted = REGISTRY.restricted_registry([
    'http_requests_total',
    'python_info'
])
filtered_metrics = generate_latest(restricted)
```

### Disabling Default Collectors

```python
import prometheus_client

# Disable default collectors
prometheus_client.REGISTRY.unregister(prometheus_client.GC_COLLECTOR)
prometheus_client.REGISTRY.unregister(prometheus_client.PLATFORM_COLLECTOR)
prometheus_client.REGISTRY.unregister(prometheus_client.PROCESS_COLLECTOR)

# Disable created metrics
from prometheus_client import disable_created_metrics
disable_created_metrics()
```

### Textfile Export for Node Exporter

```python
from prometheus_client import CollectorRegistry, Gauge, write_to_textfile

# Create separate registry for textfile export
textfile_registry = CollectorRegistry()

# Create metrics
raid_status = Gauge(
    'raid_status',
    'RAID array status (1=OK, 0=Failed)',
    registry=textfile_registry
)

backup_timestamp = Gauge(
    'backup_last_success_timestamp',
    'Last successful backup timestamp',
    registry=textfile_registry
)

# Set values
raid_status.set(1)  # RAID is OK
backup_timestamp.set_to_current_time()

# Write to textfile
write_to_textfile('/var/lib/node_exporter/textfile_collector/custom.prom', textfile_registry)
```

## Production Best Practices

### Metric Naming Conventions

```python
from prometheus_client import Counter, Histogram, Gauge

# Good metric names (follow Prometheus conventions)
http_requests_total = Counter(
    'http_requests_total',          # Suffix with _total for counters
    'Total HTTP requests'
)

http_request_duration_seconds = Histogram(
    'http_request_duration_seconds', # Use base units (seconds, bytes)
    'HTTP request duration'
)

memory_usage_bytes = Gauge(
    'memory_usage_bytes',           # Use base units
    'Memory usage in bytes'
)

# Use consistent label names
request_counter = Counter(
    'api_requests_total',
    'API requests',
    ['method', 'endpoint', 'status_code']  # Consistent label naming
)

# Avoid high cardinality labels
# BAD: user_id as label (potentially millions of values)
# GOOD: endpoint, method, status_code (limited values)
```

### Error Handling and Monitoring

```python
from prometheus_client import Counter, Histogram
import logging
import functools

# Error metrics
ERRORS_TOTAL = Counter(
    'application_errors_total',
    'Total application errors',
    ['error_type', 'component']
)

OPERATION_DURATION = Histogram(
    'operation_duration_seconds',
    'Operation duration',
    ['operation', 'status']
)

def monitor_errors(component='unknown'):
    """Decorator to monitor errors and duration"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                OPERATION_DURATION.labels(
                    operation=func.__name__,
                    status='success'
                ).observe(time.time() - start_time)
                return result
                
            except Exception as e:
                # Record error
                ERRORS_TOTAL.labels(
                    error_type=type(e).__name__,
                    component=component
                ).inc()
                
                OPERATION_DURATION.labels(
                    operation=func.__name__,
                    status='error'
                ).observe(time.time() - start_time)
                
                # Log error with context
                logging.error(
                    f"Error in {component}.{func.__name__}: {e}",
                    exc_info=True,
                    extra={
                        'component': component,
                        'operation': func.__name__,
                        'error_type': type(e).__name__
                    }
                )
                raise
        
        return wrapper
    return decorator

# Usage
@monitor_errors(component='user_service')
def create_user(user_data):
    # User creation logic
    if not user_data.get('email'):
        raise ValueError("Email is required")
    return {"id": 123, "email": user_data['email']}
```

### Performance Monitoring

```python
import time
import psutil
from prometheus_client import Gauge, Info, Counter
from threading import Thread

# System performance metrics
CPU_USAGE = Gauge('cpu_usage_percent', 'CPU usage percentage')
MEMORY_USAGE = Gauge('memory_usage_percent', 'Memory usage percentage') 
DISK_USAGE = Gauge('disk_usage_percent', 'Disk usage percentage', ['mountpoint'])

# Application performance
GC_COLLECTIONS = Counter('gc_collections_total', 'Garbage collections', ['generation'])
THREAD_COUNT = Gauge('thread_count', 'Number of threads')

class PerformanceMonitor:
    def __init__(self, interval=30):
        self.interval = interval
        self.running = False
        self.thread = None
    
    def start(self):
        """Start performance monitoring"""
        self.running = True
        self.thread = Thread(target=self._monitor_loop, daemon=True)
        self.thread.start()
    
    def stop(self):
        """Stop performance monitoring"""
        self.running = False
        if self.thread:
            self.thread.join()
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.running:
            try:
                # CPU usage
                CPU_USAGE.set(psutil.cpu_percent())
                
                # Memory usage
                memory = psutil.virtual_memory()
                MEMORY_USAGE.set(memory.percent)
                
                # Disk usage
                for partition in psutil.disk_partitions():
                    try:
                        usage = psutil.disk_usage(partition.mountpoint)
                        percent = (usage.used / usage.total) * 100
                        DISK_USAGE.labels(partition.mountpoint).set(percent)
                    except PermissionError:
                        continue
                
                # Thread count
                THREAD_COUNT.set(psutil.Process().num_threads())
                
                # GC stats
                import gc
                for i, count in enumerate(gc.get_stats()):
                    GC_COLLECTIONS.labels(str(i)).inc(count.get('collections', 0))
                
            except Exception as e:
                logging.error(f"Error in performance monitoring: {e}")
            
            time.sleep(self.interval)

# Start monitoring
monitor = PerformanceMonitor(interval=60)
monitor.start()
```

### Health Check Integration

```python
from prometheus_client import Gauge, Info, Counter, Enum
import asyncio
import aiohttp
import time

# Health check metrics
HEALTH_CHECK_STATUS = Gauge(
    'health_check_status',
    'Health check status (1=healthy, 0=unhealthy)',
    ['service', 'check_type']
)

HEALTH_CHECK_DURATION = Histogram(
    'health_check_duration_seconds',
    'Health check duration',
    ['service', 'check_type']
)

HEALTH_CHECK_FAILURES = Counter(
    'health_check_failures_total',
    'Health check failures',
    ['service', 'check_type', 'reason']
)

class HealthChecker:
    def __init__(self):
        self.checks = []
    
    def add_check(self, name, check_func, interval=60, timeout=5):
        """Add a health check"""
        self.checks.append({
            'name': name,
            'func': check_func,
            'interval': interval,
            'timeout': timeout
        })
    
    async def run_checks(self):
        """Run all health checks continuously"""
        tasks = []
        for check in self.checks:
            task = asyncio.create_task(self._run_single_check(check))
            tasks.append(task)
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _run_single_check(self, check):
        """Run a single health check repeatedly"""
        while True:
            start_time = time.time()
            
            try:
                # Run check with timeout
                await asyncio.wait_for(
                    check['func'](),
                    timeout=check['timeout']
                )
                
                # Mark as healthy
                HEALTH_CHECK_STATUS.labels(
                    service=check['name'],
                    check_type='availability'
                ).set(1)
                
            except asyncio.TimeoutError:
                HEALTH_CHECK_STATUS.labels(
                    service=check['name'],
                    check_type='availability'
                ).set(0)
                
                HEALTH_CHECK_FAILURES.labels(
                    service=check['name'],
                    check_type='availability',
                    reason='timeout'
                ).inc()
                
            except Exception as e:
                HEALTH_CHECK_STATUS.labels(
                    service=check['name'],
                    check_type='availability'
                ).set(0)
                
                HEALTH_CHECK_FAILURES.labels(
                    service=check['name'],
                    check_type='availability',
                    reason=type(e).__name__
                ).inc()
            
            finally:
                HEALTH_CHECK_DURATION.labels(
                    service=check['name'],
                    check_type='availability'
                ).observe(time.time() - start_time)
            
            await asyncio.sleep(check['interval'])

# Health check functions
async def check_database():
    """Check database connectivity"""
    # Database health check logic
    await asyncio.sleep(0.1)  # Simulate DB check

async def check_redis():
    """Check Redis connectivity"""
    # Redis health check logic
    await asyncio.sleep(0.05)  # Simulate Redis check

async def check_external_api():
    """Check external API availability"""
    async with aiohttp.ClientSession() as session:
        async with session.get('https://api.example.com/health') as response:
            if response.status != 200:
                raise Exception(f"API returned {response.status}")

# Setup health checker
health_checker = HealthChecker()
health_checker.add_check('database', check_database, interval=30)
health_checker.add_check('redis', check_redis, interval=15)
health_checker.add_check('external_api', check_external_api, interval=60)

# Start health checks
# asyncio.create_task(health_checker.run_checks())
```

## Error Handling and Debugging

### Metric Collection Debugging

```python
from prometheus_client import REGISTRY, generate_latest
import logging

def debug_metrics():
    """Debug current metrics state"""
    try:
        # Generate all metrics
        metrics_output = generate_latest(REGISTRY)
        
        print("Current metrics:")
        print(metrics_output.decode('utf-8'))
        
        # List all collectors
        print("\nRegistered collectors:")
        for collector in REGISTRY._collector_to_names:
            print(f"- {collector.__class__.__name__}")
        
    except Exception as e:
        logging.error(f"Error generating metrics: {e}")

def validate_metric_names():
    """Validate metric naming conventions"""
    import re
    
    valid_name_pattern = re.compile(r'^[a-zA-Z_:][a-zA-Z0-9_:]*$')
    
    for collector in REGISTRY._collector_to_names:
        try:
            for metric_family in collector.collect():
                if not valid_name_pattern.match(metric_family.name):
                    print(f"Invalid metric name: {metric_family.name}")
                
                # Check for common issues
                if metric_family.name.endswith('_total') and metric_family.type != 'counter':
                    print(f"Non-counter metric with _total suffix: {metric_family.name}")
                
                if 'seconds' in metric_family.name and metric_family.type not in ['histogram', 'summary']:
                    print(f"Time metric should be histogram/summary: {metric_family.name}")
                    
        except Exception as e:
            print(f"Error collecting from {collector}: {e}")

# Usage
debug_metrics()
validate_metric_names()
```

### Safe Metric Updates

```python
from prometheus_client import Counter, Gauge
import logging
import threading

class SafeCounter:
    """Thread-safe counter with error handling"""
    
    def __init__(self, name, description, labels=None):
        self.counter = Counter(name, description, labels or [])
        self.lock = threading.Lock()
    
    def inc(self, amount=1, labels=None, **label_kwargs):
        """Safely increment counter"""
        try:
            with self.lock:
                if labels:
                    self.counter.labels(*labels).inc(amount)
                elif label_kwargs:
                    self.counter.labels(**label_kwargs).inc(amount)
                else:
                    self.counter.inc(amount)
        except Exception as e:
            logging.error(f"Error incrementing counter {self.counter._name}: {e}")

class SafeGauge:
    """Thread-safe gauge with error handling"""
    
    def __init__(self, name, description, labels=None):
        self.gauge = Gauge(name, description, labels or [])
        self.lock = threading.Lock()
    
    def set(self, value, labels=None, **label_kwargs):
        """Safely set gauge value"""
        try:
            with self.lock:
                if labels:
                    self.gauge.labels(*labels).set(value)
                elif label_kwargs:
                    self.gauge.labels(**label_kwargs).set(value)
                else:
                    self.gauge.set(value)
        except Exception as e:
            logging.error(f"Error setting gauge {self.gauge._name}: {e}")
    
    def inc(self, amount=1, labels=None, **label_kwargs):
        """Safely increment gauge"""
        try:
            with self.lock:
                if labels:
                    self.gauge.labels(*labels).inc(amount)
                elif label_kwargs:
                    self.gauge.labels(**label_kwargs).inc(amount)
                else:
                    self.gauge.inc(amount)
        except Exception as e:
            logging.error(f"Error incrementing gauge {self.gauge._name}: {e}")

# Usage
safe_requests = SafeCounter(
    'safe_requests_total',
    'Safe request counter',
    ['method', 'endpoint']
)

safe_memory = SafeGauge(
    'safe_memory_usage',
    'Safe memory gauge'
)

# Thread-safe operations
safe_requests.inc(labels=['GET', '/api/users'])
safe_memory.set(1024 * 1024 * 100)  # 100MB
```

This comprehensive documentation provides production-ready Prometheus monitoring patterns for Python applications, covering all major use cases from basic instrumentation to advanced multiprocess deployments with proper error handling and performance monitoring.