# Python Asyncio Comprehensive Documentation

**Source**: Python Official Documentation (cpython)
**Version**: Python 3.14+
**Scraped Date**: 2025-07-14
**Documentation Type**: Core Library Reference

## Overview

This document contains comprehensive official documentation for Python's `asyncio` library, covering asynchronous programming patterns, event loops, coroutines, tasks, and futures.

## Table of Contents

1. [Basic Concepts](#basic-concepts)
2. [Event Loop](#event-loop)
3. [Coroutines and Tasks](#coroutines-and-tasks)
4. [Futures](#futures)
5. [Streams](#streams)
6. [Synchronization Primitives](#synchronization-primitives)
7. [Queues](#queues)
8. [Subprocesses](#subprocesses)
9. [Exception Handling](#exception-handling)
10. [Platform Support](#platform-support)

## Basic Concepts

### Running Asyncio Programs

#### Basic Hello World
```python
import asyncio

async def main():
    print('Hello ...')
    await asyncio.sleep(1)
    print('... World!')

asyncio.run(main())
```

#### Using the Asyncio REPL
```bash
$ python -m asyncio
asyncio REPL ...
Use "await" directly instead of "asyncio.run()".
Type "help", "copyright", "credits" or "license" for more information.
>>> import asyncio
>>> await asyncio.sleep(10, result='hello')
'hello'
```

### asyncio.run() Function

```python
asyncio.run(coro, *, debug=None, loop_factory=None)
```
- Execute coro in an asyncio event loop and return the result
- Handles event loop management, finalizing asynchronous generators, and closing the executor
- Cannot be called when another asyncio event loop is running in the same thread

**Parameters:**
- `coro`: Any awaitable object
- `debug`: bool or None. If True, run in debug mode
- `loop_factory`: Callable to create a new event loop

## Event Loop

### Getting and Managing Event Loops

```python
asyncio.get_running_loop()  # Preferred - get the running event loop
asyncio.get_event_loop()    # Get an event loop instance
asyncio.set_event_loop()    # Set the event loop as current
asyncio.new_event_loop()    # Create a new event loop
```

### Event Loop Lifecycle Methods

```python
loop.run_until_complete(future)  # Run a Future/Task/awaitable until complete
loop.run_forever()               # Run the event loop forever
loop.stop()                      # Stop the event loop
loop.close()                     # Close the event loop
loop.is_running()                # Return True if running
loop.is_closed()                 # Return True if closed
await loop.shutdown_asyncgens()  # Close asynchronous generators
```

### Task and Future Management

```python
loop.create_future()     # Create a Future object
loop.create_task()       # Schedule coroutine as a Task
loop.set_task_factory()  # Set a factory for creating Tasks
loop.get_task_factory()  # Get the current task factory
```

### Callback Scheduling

```python
loop.call_soon(callback, *args)              # Invoke a callback soon
loop.call_soon_threadsafe(callback, *args)   # Thread-safe variant
loop.call_later(delay, callback, *args)      # Invoke after delay
loop.call_at(when, callback, *args)          # Invoke at specific time
```

### Executor Integration

```python
await loop.run_in_executor(executor, func, *args)
```
- Run CPU-bound or blocking function in a concurrent.futures executor
- Returns an asyncio.Future object
- Default executor is ThreadPoolExecutor if not specified

## Coroutines and Tasks

### Awaitables

Three types of awaitable objects:
1. **Coroutines**: async def functions or objects returned by them
2. **Tasks**: Used to schedule coroutines concurrently
3. **Futures**: Low-level awaitable objects

### Creating and Managing Tasks

```python
# Create a task
task = asyncio.create_task(coro)

# Get current task
current = asyncio.current_task()

# Get all tasks
tasks = asyncio.all_tasks()

# Check if object is a coroutine
asyncio.iscoroutine(obj)
```

### Task Class

```python
Task(coro, *, loop=None, name=None, context=None, eager_start=False)
```
- Future-like object that runs a Python coroutine
- Not thread-safe
- Event loops use cooperative scheduling

### Running Coroutines Concurrently

#### Using asyncio.gather()
```python
results = await asyncio.gather(*aws, return_exceptions=False)
```
- Run awaitable objects concurrently
- If `return_exceptions=False`, first exception propagates
- If `return_exceptions=True`, exceptions included in results

#### Using asyncio.wait()
```python
done, pending = await asyncio.wait(aws, *, timeout=None, return_when=ALL_COMPLETED)
```
- `return_when` options: `FIRST_COMPLETED`, `FIRST_EXCEPTION`, `ALL_COMPLETED`
- Does not raise TimeoutError

#### Using asyncio.as_completed()
```python
# Async iteration (Python 3.13+)
async for task in asyncio.as_completed(tasks):
    result = await task

# Plain iteration
for coro in asyncio.as_completed(tasks):
    result = await coro
```

### Timeouts and Cancellation

```python
# With timeout
result = await asyncio.wait_for(aw, timeout)

# Shield from cancellation
result = await asyncio.shield(aw)

# Sleep
await asyncio.sleep(delay, result=None)
```

### Thread Integration

```python
# Run in thread
result = await asyncio.to_thread(func, *args, **kwargs)

# Submit coroutine from another thread
future = asyncio.run_coroutine_threadsafe(coro, loop)
```

## Futures

### Future Objects

```python
# Check if object is Future-like
asyncio.isfuture(obj)

# Ensure object is a Future or Task
future = asyncio.ensure_future(obj, *, loop=None)

# Wrap concurrent.futures.Future
asyncio_future = asyncio.wrap_future(concurrent_future, *, loop=None)
```

### Future Example
```python
async def set_after(fut, delay, value):
    await asyncio.sleep(delay)
    fut.set_result(value)

async def main():
    loop = asyncio.get_running_loop()
    fut = loop.create_future()
    
    loop.create_task(set_after(fut, 1, '... world'))
    print('hello ...')
    print(await fut)

asyncio.run(main())
```

## Streams

### Opening Connections

```python
reader, writer = await asyncio.open_connection(host, port, *, 
    ssl=None, family=0, proto=0, flags=0, sock=None, 
    local_addr=None, server_hostname=None)
```

### Creating Servers

```python
server = await asyncio.start_server(client_connected_cb, host, port, *,
    family=socket.AF_UNSPEC, flags=socket.AI_PASSIVE, sock=None,
    backlog=100, ssl=None, reuse_address=None, reuse_port=None)
```

### StreamWriter Methods

```python
writer.write(data)              # Write data to stream
writer.writelines(lines)        # Write multiple lines
await writer.drain()            # Wait until appropriate to resume writing
writer.close()                  # Close the stream
await writer.wait_closed()      # Wait until stream is closed
writer.write_eof()              # Close write end after flushing
```

### StreamReader Methods

```python
data = await reader.read(n=-1)        # Read up to n bytes
line = await reader.readline()        # Read one line
data = await reader.readexactly(n)    # Read exactly n bytes
data = await reader.readuntil(sep)    # Read until separator
reader.at_eof()                       # Check if at EOF
```

### Stream Examples

#### TCP Echo Client
```python
async def tcp_echo_client(message):
    reader, writer = await asyncio.open_connection('127.0.0.1', 8888)
    
    print(f'Send: {message!r}')
    writer.write(message.encode())
    await writer.drain()
    
    data = await reader.read(100)
    print(f'Received: {data.decode()!r}')
    
    writer.close()
    await writer.wait_closed()

asyncio.run(tcp_echo_client('Hello World!'))
```

#### TCP Echo Server
```python
async def handle_echo(reader, writer):
    data = await reader.read(100)
    message = data.decode()
    addr = writer.get_extra_info('peername')
    
    print(f"Received {message!r} from {addr!r}")
    writer.write(data)
    await writer.drain()
    
    writer.close()
    await writer.wait_closed()

async def main():
    server = await asyncio.start_server(
        handle_echo, '127.0.0.1', 8888)
    
    async with server:
        await server.serve_forever()

asyncio.run(main())
```

## Synchronization Primitives

### Lock

```python
lock = asyncio.Lock()

# Using async with
async with lock:
    # Critical section
    pass

# Manual acquire/release
await lock.acquire()
try:
    # Critical section
    pass
finally:
    lock.release()
```

### Event

```python
event = asyncio.Event()

# Wait for event
await event.wait()

# Set event
event.set()

# Clear event
event.clear()

# Check if set
if event.is_set():
    pass
```

### Condition

```python
cond = asyncio.Condition(lock=None)

async with cond:
    await cond.wait()           # Wait until notified
    await cond.wait_for(pred)   # Wait until predicate true

cond.notify(n=1)    # Wake up n tasks
cond.notify_all()   # Wake up all tasks
```

### Semaphore

```python
sem = asyncio.Semaphore(value=1)

async with sem:
    # Limited access section
    pass

# BoundedSemaphore prevents releases above initial value
sem = asyncio.BoundedSemaphore(value=1)
```

### Barrier

```python
barrier = asyncio.Barrier(parties)

# Wait at barrier
await barrier.wait()

# Using async with
async with barrier:
    pass
```

## Queues

### Queue Types

```python
# FIFO Queue
queue = asyncio.Queue(maxsize=0)

# LIFO Queue (Stack)
queue = asyncio.LifoQueue(maxsize=0)

# Priority Queue
queue = asyncio.PriorityQueue(maxsize=0)
```

### Queue Operations

```python
# Put/Get operations
await queue.put(item)
item = await queue.get()

# Non-blocking variants
queue.put_nowait(item)  # Raises QueueFull
item = queue.get_nowait()  # Raises QueueEmpty

# Queue state
queue.empty()
queue.full()
queue.qsize()

# Task tracking
queue.task_done()
await queue.join()

# Shutdown (3.13+)
queue.shutdown(immediate=False)
```

## Subprocesses

### Creating Subprocesses

```python
# Using shell
process = await asyncio.create_subprocess_shell(
    cmd, stdin=None, stdout=None, stderr=None)

# Using exec
process = await asyncio.create_subprocess_exec(
    program, *args, stdin=None, stdout=None, stderr=None)
```

### Subprocess Communication

```python
# Communicate
stdout, stderr = await process.communicate(input=None)

# Wait for completion
await process.wait()

# Terminate
process.terminate()
process.kill()
```

## Exception Handling

### Asyncio Exceptions

```python
asyncio.CancelledError      # Task was cancelled
asyncio.TimeoutError        # Operation exceeded timeout
asyncio.InvalidStateError   # Invalid state for operation
```

### Queue Exceptions

```python
asyncio.QueueEmpty     # get_nowait() on empty queue
asyncio.QueueFull      # put_nowait() on full queue
asyncio.QueueShutDown  # Operation on shut down queue
```

## Platform Support

### Windows Limitations

**ProactorEventLoop (default on Windows):**
- No `add_reader()` or `add_writer()` support
- Supports subprocesses

**SelectorEventLoop:**
- Limited to 512 sockets
- No subprocess support
- Only socket handles for `add_reader()`/`add_writer()`

### Unix/macOS

- Full support for all asyncio features
- macOS <= 10.8: Use SelectSelector or PollSelector for character devices

### General Limitations

All platforms:
- `add_reader()`/`add_writer()` cannot monitor file I/O

## Best Practices

### Debug Mode

```python
# Enable debug mode
asyncio.run(main(), debug=True)

# Or set environment variable
# PYTHONASYNCIODEBUG=1

# Adjust logger level
import logging
logging.getLogger("asyncio").setLevel(logging.WARNING)
```

### Error Handling Patterns

```python
# Handle exceptions in tasks
try:
    result = await some_task
except asyncio.CancelledError:
    # Handle cancellation
    raise
except Exception as e:
    # Handle other exceptions
    logger.error(f"Task failed: {e}")
```

### Resource Cleanup

```python
# Always use try/finally or async with
writer = await get_writer()
try:
    # Use writer
    pass
finally:
    writer.close()
    await writer.wait_closed()
```

## Performance Optimization

### Eager Task Factory

```python
# Set eager task factory for better performance
loop.set_task_factory(asyncio.eager_task_factory)
```

### Connection Pooling

```python
# Reuse connections
class ConnectionPool:
    def __init__(self, size=10):
        self._connections = asyncio.Queue(maxsize=size)
    
    async def acquire(self):
        return await self._connections.get()
    
    async def release(self, conn):
        await self._connections.put(conn)
```

## Free-Threading Support (3.14+)

- First-class support for free-threading builds
- Multiple event loops can run in parallel across threads
- Linear scaling with number of threads

---

*This documentation represents the current state of Python's asyncio library as of Python 3.14, with comprehensive coverage of all major features and patterns.*