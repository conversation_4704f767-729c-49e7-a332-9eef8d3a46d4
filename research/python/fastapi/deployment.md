# FastAPI Deployment Documentation

**Source**: Official FastAPI Documentation - /tiangolo/fastapi
**Version**: 0.115.12
**Scraped Date**: 2025-07-14
**Trust Score**: 9.9

## Table of Contents
1. [Running FastAPI in Production](#running-fastapi-in-production)
2. [Installation](#installation)
3. [Docker Deployment](#docker-deployment)
4. [Multiple Workers](#multiple-workers)
5. [Process Managers](#process-managers)
6. [Version Pinning](#version-pinning)
7. [Production Configuration](#production-configuration)
8. [Behind a Proxy](#behind-a-proxy)

## Running FastAPI in Production

### Basic Production Start
```bash
fastapi run
```

### Using Uvicorn Directly
```bash
uvicorn main:app --host 0.0.0.0 --port 80
```

### Development Mode
```bash
fastapi dev main.py
```

## Installation

### Uvicorn with Standard Dependencies
```bash
pip install "uvicorn[standard]"
```

### Uvicorn and Gunicorn for Production
```bash
pip install "uvicorn[standard]" gunicorn
```

### Alternative ASGI Servers
```bash
# Hypercorn
pip install hypercorn

# Hypercorn with Trio support
pip install "hypercorn[trio]"
```

## Docker Deployment

### Basic Dockerfile
```dockerfile
FROM python:3.9

WORKDIR /code

COPY ./requirements.txt /code/requirements.txt

RUN pip install --no-cache-dir --upgrade -r /code/requirements.txt

COPY ./app /code/app

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "80"]
```

### Multi-Stage Build with Poetry
```dockerfile
# Stage 1: Requirements
FROM python:3.9 as requirements-stage

WORKDIR /tmp

RUN pip install poetry

COPY ./pyproject.toml ./poetry.lock* /tmp/

RUN poetry export -f requirements.txt --output requirements.txt --without-hashes

# Stage 2: Final image
FROM python:3.9

WORKDIR /code

COPY --from=requirements-stage /tmp/requirements.txt /code/requirements.txt

RUN pip install --no-cache-dir --upgrade -r /code/requirements.txt

COPY ./app /code/app

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "80"]
```

### Docker with Multiple Workers
```dockerfile
FROM python:3.9

WORKDIR /code

COPY ./requirements.txt /code/requirements.txt

RUN pip install --no-cache-dir --upgrade -r /code/requirements.txt

COPY ./app /code/app

CMD ["fastapi", "run", "app/main.py", "--port", "80", "--workers", "4"]
```

### Behind TLS Proxy
```dockerfile
CMD ["uvicorn", "app.main:app", "--proxy-headers", "--host", "0.0.0.0", "--port", "80"]
```

### Docker Commands
```bash
# Build image
docker build -t myimage .

# Run container
docker run -d --name mycontainer -p 80:80 myimage
```

## Multiple Workers

### Using FastAPI CLI
```bash
fastapi run --workers 4 main.py
```

### Using Uvicorn Directly
```bash
uvicorn main:app --host 0.0.0.0 --port 8080 --workers 4
```

### Using Gunicorn with Uvicorn Workers
```bash
gunicorn main:app --workers 4 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:80
```

### Gunicorn Configuration in Python
```python
# Import Uvicorn worker
import uvicorn.workers.UvicornWorker

# Import FastAPI app
from main import app
```

## Process Managers

### Using Hypercorn
```bash
hypercorn main:app --bind 0.0.0.0:80
```

## Version Pinning

### In requirements.txt
```txt
# Pin to exact version
fastapi[standard]==0.112.0

# Pin to minor version range
fastapi[standard]>=0.112.0,<0.113.0

# Pin to compatible version range
fastapi>=0.45.0,<0.46.0

# Pin Pydantic
pydantic>=2.7.0,<3.0.0
```

## Production Configuration

### Root Path Configuration (Behind Proxy)
```python
from fastapi import FastAPI, Request

app = FastAPI()

@app.get("/app")
async def read_root(request: Request):
    return {"message": "Hello World", "root_path": request.scope.get("root_path")}
```

### Running with Root Path
```bash
fastapi run main.py --root-path /api/v1
```

### Multiple Servers Configuration
```python
from fastapi import FastAPI

app = FastAPI(
    servers=[
        {"url": "https://stag.example.com", "description": "Staging environment"},
        {"url": "https://prod.example.com", "description": "Production environment"},
    ]
)
```

### Database Initialization
```python
@app.on_event("startup")
def on_startup():
    create_db_and_tables()
```

### Environment Variables with Pydantic Settings
```bash
pip install pydantic-settings
```

```python
from functools import lru_cache
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    admin_email: str = "<EMAIL>"

@lru_cache()
def get_settings():
    return Settings()
```

### Setting Environment Variables
```bash
# Temporary for single command
MY_NAME="Wade Wilson" python main.py

# In .env file
ADMIN_EMAIL=<EMAIL>
```

## Behind a Proxy

### OpenAPI Schema with Servers
```json
{
    "openapi": "3.1.0",
    "servers": [
        {
            "url": "/api/v1"
        },
        {
            "url": "https://stag.example.com",
            "description": "Staging environment"
        },
        {
            "url": "https://prod.example.com",
            "description": "Production environment"
        }
    ],
    "paths": {}
}
```

## Best Practices

1. **Always use production ASGI server** - Never use development server in production
2. **Use process managers** - Gunicorn or systemd for process management
3. **Configure workers** - Multiple workers for better performance
4. **Pin versions** - Ensure reproducible deployments
5. **Use Docker** - Consistent deployment environments
6. **Configure for proxy** - Use --proxy-headers when behind reverse proxy
7. **Environment variables** - For configuration management
8. **Health checks** - Implement health check endpoints
9. **Logging** - Configure proper logging for production
10. **No reload in production** - Never use --reload flag in production

## Production Checklist

- [ ] Install production dependencies (uvicorn[standard])
- [ ] Configure multiple workers
- [ ] Set up process manager (Gunicorn/systemd)
- [ ] Create Dockerfile with optimized layers
- [ ] Pin all dependency versions
- [ ] Configure environment variables
- [ ] Set up reverse proxy if needed
- [ ] Implement health checks
- [ ] Configure logging
- [ ] Test deployment locally with Docker