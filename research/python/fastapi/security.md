# FastAPI Security Documentation

**Source**: Official FastAPI Documentation - /tiangolo/fastapi
**Version**: 0.115.12
**Scraped Date**: 2025-07-14
**Trust Score**: 9.9

## Table of Contents
1. [CORS (Cross-Origin Resource Sharing)](#cors-cross-origin-resource-sharing)
2. [HTTP Basic Authentication](#http-basic-authentication)
3. [OAuth2 Password Flow](#oauth2-password-flow)
4. [JWT Token Authentication](#jwt-token-authentication)
5. [OAuth2 Scopes](#oauth2-scopes)
6. [Middleware](#middleware)
7. [API Key Authentication](#api-key-authentication)
8. [Security Best Practices](#security-best-practices)

## CORS (Cross-Origin Resource Sharing)

### Basic CORS Configuration
```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI()

origins = [
    "http://localhost:8080",
    "https://localhost:8080",
    "http://localhost",
    "https://localhost",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### Alternative Import
```python
from starlette.middleware.cors import CORSMiddleware
```

### CORSMiddleware Parameters
- `allow_origins`: List of allowed origins or ["*"] for all
- `allow_origin_regex`: Regex string to match origins
- `allow_methods`: List of allowed HTTP methods (default: ['GET'])
- `allow_headers`: List of allowed headers or ["*"] for all
- `allow_credentials`: Support cookies (default: False)
- `expose_headers`: Response headers accessible to browser
- `max_age`: Max time browsers cache CORS responses (default: 600)

## HTTP Basic Authentication

### Basic Setup
```python
from fastapi import Depends, FastAPI, HTTPException, status
from fastapi.security import HTTPBasic, HTTPBasicCredentials

app = FastAPI()

security = HTTPBasic()

@app.get("/items/")
def read_items(credentials: HTTPBasicCredentials = Depends(security)):
    return {"username": credentials.username}
```

### Secure Verification (Timing Attack Prevention)
```python
import secrets

def get_current_username(credentials: HTTPBasicCredentials = Depends(security)):
    correct_username = secrets.compare_digest(
        credentials.username.encode("utf8"), 
        "stanleyjobson".encode("utf8")
    )
    correct_password = secrets.compare_digest(
        credentials.password.encode("utf8"), 
        "swordfish".encode("utf8")
    )
    if not (correct_username and correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username
```

## OAuth2 Password Flow

### Basic OAuth2 Setup
```python
from fastapi import Depends, FastAPI
from fastapi.security import OAuth2PasswordBearer

app = FastAPI()

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

@app.get("/items/")
async def read_items(token: str = Depends(oauth2_scheme)):
    return {"token": token}
```

### Token Endpoint
```python
from fastapi.security import OAuth2PasswordRequestForm

@app.post("/token")
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    # Validate credentials
    if not authenticate_user(form_data.username, form_data.password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return {"access_token": form_data.username, "token_type": "bearer"}
```

### User Authentication Dependencies
```python
async def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    # Decode and validate token
    user = decode_token(token)
    if user is None:
        raise credentials_exception
    return user

async def get_current_active_user(current_user: dict = Depends(get_current_user)):
    if current_user.get("disabled"):
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user
```

## JWT Token Authentication

### Installation
```bash
pip install "passlib[bcrypt]"
pip install "python-jose[cryptography]"
```

### Password Hashing
```python
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)
```

### JWT Token Creation
```python
from datetime import datetime, timedelta
from jose import JWTError, jwt

SECRET_KEY = "your-secret-key"  # Change in production!
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

def create_access_token(data: dict, expires_delta: timedelta | None = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt
```

### JWT Token Verification
```python
async def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = get_user(username=username)
    if user is None:
        raise credentials_exception
    return user
```

### Complete Token Endpoint
```python
@app.post("/token")
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    user = authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}
```

## OAuth2 Scopes

### Using Security with Scopes
```python
from fastapi import Security
from fastapi.security import SecurityScopes

@app.get("/users/me/items/")
async def read_own_items(
    current_user: User = Security(get_current_active_user, scopes=["items"])
):
    return [{"item_id": "Foo", "owner": current_user.username}]
```

### Validating Scopes in Dependencies
```python
async def get_current_user(
    security_scopes: SecurityScopes,
    token: str = Depends(oauth2_scheme)
):
    if security_scopes.scopes:
        authenticate_value = f'Bearer scope="{security_scopes.scope_str}"'
    else:
        authenticate_value = "Bearer"
    
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": authenticate_value},
    )
    
    # Decode token and get scopes
    payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
    token_scopes = payload.get("scopes", [])
    
    # Validate scopes
    for scope in security_scopes.scopes:
        if scope not in token_scopes:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough permissions",
                headers={"WWW-Authenticate": authenticate_value},
            )
    
    return user
```

### Token with Scopes
```python
@app.post("/token")
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    # form_data.scopes contains requested scopes
    access_token = create_access_token(
        data={
            "sub": user.username,
            "scopes": form_data.scopes
        }
    )
    return {"access_token": access_token, "token_type": "bearer"}
```

## Middleware

### Basic Middleware Structure
```python
from fastapi import FastAPI, Request

app = FastAPI()

@app.middleware("http")
async def my_middleware(request: Request, call_next):
    # Code before request
    response = await call_next(request)
    # Code after request
    return response
```

### Process Time Header Middleware
```python
import time

@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.perf_counter()
    response = await call_next(request)
    process_time = time.perf_counter() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response
```

### Built-in Security Middleware

#### HTTPS Redirect
```python
from fastapi.middleware.httpsredirect import HTTPSRedirectMiddleware

app.add_middleware(HTTPSRedirectMiddleware)
```

#### Trusted Host
```python
from fastapi.middleware.trustedhost import TrustedHostMiddleware

app.add_middleware(
    TrustedHostMiddleware, 
    allowed_hosts=["example.com", "*.example.com"]
)
```

#### GZip Compression
```python
from fastapi.middleware.gzip import GZipMiddleware

app.add_middleware(GZipMiddleware, minimum_size=1000)
```

### Middleware Execution Order
```python
app.add_middleware(MiddlewareA)
app.add_middleware(MiddlewareB)
# Order: Request: B -> A -> route
#        Response: route -> A -> B
```

## API Key Authentication

### Available Classes
```python
from fastapi.security import (
    APIKeyCookie,
    APIKeyHeader,
    APIKeyQuery,
)
```

### Header-based API Key
```python
from fastapi.security import APIKeyHeader

api_key_header = APIKeyHeader(name="X-API-Key")

@app.get("/items/")
async def read_items(api_key: str = Depends(api_key_header)):
    if api_key != "secret-api-key":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid API Key"
        )
    return {"items": ["Foo", "Bar"]}
```

## Security Best Practices

### 1. Environment Variables
```python
import os
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    secret_key: str = os.getenv("SECRET_KEY", "change-me")
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
```

### 2. Password Requirements
```python
def validate_password(password: str) -> bool:
    # Implement password strength requirements
    if len(password) < 8:
        return False
    # Add more validation rules
    return True
```

### 3. Rate Limiting
```python
# Use a middleware or dependency for rate limiting
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)
```

### 4. HTTPS Configuration
- Always use HTTPS in production
- Use --proxy-headers when behind a proxy
- Configure proper certificates

### 5. Security Headers
```python
@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    response = await call_next(request)
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    return response
```

## OpenAPI Security Schemes

FastAPI supports all OpenAPI security schemes:
- `apiKey`: Query parameter, header, or cookie
- `http`: Bearer token, Basic auth, Digest
- `oauth2`: All OAuth2 flows
- `openIdConnect`: OpenID Connect discovery

## Error Responses

### Standard Authentication Errors
```json
{
  "detail": "Not authenticated"
}
```

```json
{
  "detail": "Incorrect username or password"
}
```

```json
{
  "detail": "Inactive user"
}
```

```json
{
  "detail": "Not enough permissions"
}
```

## Security Checklist

- [ ] Use HTTPS in production
- [ ] Implement proper authentication (OAuth2/JWT)
- [ ] Use secure password hashing (bcrypt)
- [ ] Prevent timing attacks with secrets.compare_digest()
- [ ] Configure CORS properly
- [ ] Add security headers
- [ ] Implement rate limiting
- [ ] Use environment variables for secrets
- [ ] Validate and sanitize all inputs
- [ ] Keep dependencies updated
- [ ] Use scopes for fine-grained permissions
- [ ] Log security events
- [ ] Handle errors without exposing sensitive info