# FastAPI Docker Deployment

**Source**: https://fastapi.tiangolo.com/deployment/docker/
**Scraped**: 2025-07-14
**Library**: FastAPI
**Type**: Official Documentation

## Container Fundamentals

Containers are lightweight, isolated environments that package applications with dependencies. A container image is a static package; a container is the running instance. Best practice is to run a single process per container.

## Dockerfile Best Practices

### Basic Dockerfile Template

```dockerfile
FROM python:3.9
WORKDIR /code
COPY ./requirements.txt /code/requirements.txt
RUN pip install --no-cache-dir --upgrade -r /code/requirements.txt
COPY ./app /code/app
CMD ["fastapi", "run", "app/main.py", "--port", "80"]
```

### Key Recommendations

- Use official Python base image
- Copy requirements file first to leverage Docker cache
- Install dependencies before copying application code
- Use exec form for CMD instruction
- Add `--proxy-headers` when behind a load balancer

## Deployment Strategies

### Replication Approaches

- **Single process per container** (recommended for Kubernetes/distributed systems)
- **Multiple workers per container** for simpler deployments
- Use `--workers` flag to configure worker processes

## Deployment Considerations

- HTTPS typically handled externally (e.g., Traefik)
- Automatic restart and startup managed by container orchestration
- Memory management at container or cluster level

## Deployment Options

- Docker Compose
- Kubernetes
- Cloud container services
- Docker Swarm Mode

## Recommended Deployment Pattern

1. Build minimal, single-process container
2. Use container orchestration for scaling
3. Handle HTTPS and load balancing externally

## Pro Tips

- Optimize Dockerfile for build cache
- Use `--no-cache-dir` with pip
- Consider memory limits for complex applications

The documentation emphasizes flexibility and recommends adapting these patterns to specific project requirements.