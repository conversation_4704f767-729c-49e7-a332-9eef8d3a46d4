# Python/ML/NLP Documentation Research

This directory contains comprehensive official documentation for Python, ML, and NLP libraries used in the query-intelligence service.

## Directory Structure

- `core/` - Python language features, asyncio, typing
- `web-frameworks/` - FastAPI, pydantic, websockets  
- `ml-frameworks/` - Core ML libraries documentation
- `nlp/` - Natural language processing libraries
- `vector-databases/` - Pinecone and alternatives
- `utilities/` - Supporting libraries

## Documentation Sources

All documentation is scraped from official sources only:
- Python.org official documentation
- FastAPI official documentation
- Library-specific official documentation sites

## Metadata

Each file includes:
- Source URL
- Version information
- Scraping date
- Content type designation

Last updated: 2025-07-14