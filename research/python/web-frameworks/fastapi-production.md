# FastAPI Production Documentation

**Source**: https://fastapi.tiangolo.com/
**Version**: 0.115.12
**Scraped Date**: 2025-07-14
**Trust Score**: 9.9

## Table of Contents

1. [Production Deployment](#production-deployment)
2. [Docker Deployment](#docker-deployment)
3. [Security Configuration](#security-configuration)
4. [Middleware and CORS](#middleware-and-cors)
5. [Testing Strategies](#testing-strategies)
6. [Performance Optimization](#performance-optimization)
7. [Monitoring and Logging](#monitoring-and-logging)
8. [Common Patterns](#common-patterns)

## Production Deployment

### Running FastAPI in Production

```bash
# Install production dependencies
pip install "uvicorn[standard]" gunicorn

# Run with Gunicorn managing Uvicorn workers
gunicorn main:app --workers 4 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:80

# Or use FastAPI CLI (simpler)
fastapi run --workers 4 main.py

# Behind a proxy (Nginx/Traefik)
uvicorn app.main:app --proxy-headers --host 0.0.0.0 --port 80
```

### Worker Configuration

```python
# Import Uvicorn worker for Gunicorn
import uvicorn.workers.UvicornWorker

# Run with multiple workers
# Workers = (2 * CPU cores) + 1
```

## Docker Deployment

### Optimized Dockerfile

```dockerfile
FROM python:3.9

WORKDIR /code

# Copy requirements first for better caching
COPY ./requirements.txt /code/requirements.txt

# Install dependencies
RUN pip install --no-cache-dir --upgrade -r /code/requirements.txt

# Copy application code last
COPY ./app /code/app

# Run with Uvicorn
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "80"]

# If behind a proxy (Nginx/Traefik)
# CMD ["uvicorn", "app.main:app", "--proxy-headers", "--host", "0.0.0.0", "--port", "80"]
```

### Multi-stage Build with Poetry

```dockerfile
# Stage 1: Generate requirements.txt from Poetry
FROM python:3.9 as requirements-stage

WORKDIR /tmp

RUN pip install poetry

COPY ./pyproject.toml ./poetry.lock* /tmp/

RUN poetry export -f requirements.txt --output requirements.txt --without-hashes

# Stage 2: Final image
FROM python:3.9

WORKDIR /code

COPY --from=requirements-stage /tmp/requirements.txt /code/requirements.txt

RUN pip install --no-cache-dir --upgrade -r /code/requirements.txt

COPY ./app /code/app

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "80"]
```

### Docker Commands

```bash
# Build image
docker build -t myimage .

# Run container
docker run -d --name mycontainer -p 80:80 myimage

# With multiple workers in single container (special cases only)
CMD ["fastapi", "run", "app/main.py", "--port", "80", "--workers", "4"]
```

### Project Structure

```
.
├── app
│   ├── __init__.py
│   └── main.py
├── Dockerfile
└── requirements.txt
```

## Security Configuration

### OAuth2 with Password Flow

```python
from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm

app = FastAPI()

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

@app.get("/users/me")
async def read_users_me(token: str = Depends(oauth2_scheme)):
    return {"token": token}
```

### OAuth2 with Scopes

```python
from fastapi import Security
from fastapi.security import SecurityScopes

oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="token",
    scopes={
        "me": "Read information about the current user",
        "items": "Read items"
    }
)

async def get_current_user(
    security_scopes: SecurityScopes,
    token: str = Depends(oauth2_scheme)
):
    # Validate scopes
    for scope in security_scopes.scopes:
        if scope not in token_data.scopes:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough permissions",
                headers={"WWW-Authenticate": f'Bearer scope="{security_scopes.scope_str}"'},
            )
    return user

@app.get("/users/me/items/")
async def read_own_items(
    current_user: User = Security(get_current_active_user, scopes=["items"])
):
    return [{"item_id": "Foo", "owner": current_user.username}]
```

### JWT Token Handling

```python
from jose import JWTError, jwt
from datetime import datetime, timedelta

SECRET_KEY = "your-super-secret-key"  # Generate with: openssl rand -hex 32
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt
```

### HTTP Basic Authentication

```python
from fastapi.security import HTTPBasic, HTTPBasicCredentials
import secrets

security = HTTPBasic()

def verify_credentials(credentials: HTTPBasicCredentials = Depends(security)):
    # Use secrets.compare_digest to prevent timing attacks
    correct_username = secrets.compare_digest(credentials.username, "stanleyjobson")
    correct_password = secrets.compare_digest(credentials.password, "swordfish")
    if not (correct_username and correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username
```

## Middleware and CORS

### CORS Middleware

```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://example.com"],  # or ["*"] for all origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### Custom Middleware

```python
import time
from fastapi import FastAPI, Request

app = FastAPI()

@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.perf_counter()
    response = await call_next(request)
    process_time = time.perf_counter() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response
```

### Common Middleware

```python
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.middleware.httpsredirect import HTTPSRedirectMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

# GZip compression
app.add_middleware(GZipMiddleware, minimum_size=1000)

# HTTPS redirect
app.add_middleware(HTTPSRedirectMiddleware)

# Trusted host validation
app.add_middleware(
    TrustedHostMiddleware, 
    allowed_hosts=["example.com", "*.example.com"]
)
```

### Middleware Execution Order

```python
# Last added middleware executes first on request, last on response
app.add_middleware(MiddlewareA)
app.add_middleware(MiddlewareB)
# Order: Request: B -> A -> route
#        Response: route -> A -> B
```

## Testing Strategies

### Basic Testing with TestClient

```python
from fastapi import FastAPI
from fastapi.testclient import TestClient

app = FastAPI()

@app.get("/")
async def read_main():
    return {"msg": "Hello World"}

client = TestClient(app)

def test_read_main():
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"msg": "Hello World"}
```

### Testing with Headers and Authentication

```python
def test_read_items():
    response = client.get(
        "/items/", 
        headers={"X-Token": "fake-super-secret-token"}
    )
    assert response.status_code == 200
    assert response.json() == {"message": "Hello, items!"}

def test_create_item():
    response = client.post(
        "/items/",
        headers={"X-Token": "fake-super-secret-token"},
        json={"name": "Foo", "description": "The Foo Fighters"}
    )
    assert response.status_code == 200
```

### Testing WebSockets

```python
from fastapi.testclient import TestClient

def test_websocket():
    with client.websocket_connect("/ws") as websocket:
        websocket.send_text("Hello WebSocket")
        data = websocket.receive_text()
        assert data == "Message received from WebSocket"
```

### Testing with Dependency Overrides

```python
from fastapi.testclient import TestClient

def override_get_settings():
    return Settings(admin_email="<EMAIL>")

app.dependency_overrides[get_settings] = override_get_settings

# Test with overridden dependency
client = TestClient(app)
response = client.get("/info")

# Reset overrides after test
app.dependency_overrides = {}
```

### Async Tests

```python
import pytest
from httpx import AsyncClient

@pytest.mark.asyncio
async def test_async_endpoint():
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/")
    assert response.status_code == 200
```

## Performance Optimization

### Connection Pooling

```python
# Use connection pooling for database connections
# Example with asyncpg:
import asyncpg

async def create_pool():
    return await asyncpg.create_pool(
        host="localhost",
        port=5432,
        user="user",
        password="password",
        database="db",
        min_size=10,
        max_size=20
    )
```

### Caching Strategies

```python
from functools import lru_cache
from fastapi import Depends

@lru_cache()
def get_settings():
    return Settings()

@app.get("/info")
def info(settings: Settings = Depends(get_settings)):
    return {
        "app_name": settings.app_name,
        "admin_email": settings.admin_email,
    }
```

### Background Tasks

```python
from fastapi import BackgroundTasks

def write_notification(email: str, message=""):
    with open("log.txt", mode="w") as email_file:
        content = f"notification for {email}: {message}"
        email_file.write(content)

@app.post("/send-notification/{email}")
async def send_notification(
    email: str, 
    background_tasks: BackgroundTasks
):
    background_tasks.add_task(write_notification, email, message="some notification")
    return {"message": "Notification sent in the background"}
```

## Monitoring and Logging

### Structured Logging

```python
import logging
import json

class JSONFormatter(logging.Formatter):
    def format(self, record):
        log_obj = {
            "timestamp": self.formatTime(record),
            "level": record.levelname,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
        }
        return json.dumps(log_obj)

# Configure logging
logger = logging.getLogger("fastapi")
handler = logging.StreamHandler()
handler.setFormatter(JSONFormatter())
logger.addHandler(handler)
logger.setLevel(logging.INFO)
```

### Request ID Tracking

```python
import uuid
from fastapi import Request

@app.middleware("http")
async def add_request_id(request: Request, call_next):
    request_id = str(uuid.uuid4())
    request.state.request_id = request_id
    response = await call_next(request)
    response.headers["X-Request-ID"] = request_id
    return response
```

### Health Checks

```python
@app.get("/health")
async def health_check():
    # Check database connection
    # Check external services
    # Check disk space, memory, etc.
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow(),
        "version": app.version
    }
```

## Common Patterns

### Dependency Injection

```python
from typing import Annotated

async def common_parameters(
    q: str | None = None, 
    skip: int = 0, 
    limit: int = 100
):
    return {"q": q, "skip": skip, "limit": limit}

@app.get("/items/")
async def read_items(commons: Annotated[dict, Depends(common_parameters)]):
    return commons
```

### Error Handling

```python
from fastapi import HTTPException

@app.exception_handler(ValueError)
async def value_error_handler(request: Request, exc: ValueError):
    return JSONResponse(
        status_code=400,
        content={"message": f"Invalid value: {str(exc)}"}
    )

@app.get("/items/{item_id}")
async def read_item(item_id: int):
    if item_id < 1:
        raise HTTPException(status_code=400, detail="Item ID must be positive")
    return {"item_id": item_id}
```

### Startup and Shutdown Events

```python
@app.on_event("startup")
async def startup_event():
    # Initialize database connection pool
    # Load ML models
    # Warm up caches
    app.state.db = await create_pool()

@app.on_event("shutdown")
async def shutdown_event():
    # Close database connections
    # Clean up resources
    await app.state.db.close()
```

### API Versioning

```python
from fastapi import APIRouter

# Version 1
v1_router = APIRouter(prefix="/api/v1")

@v1_router.get("/users/")
async def get_users_v1():
    return {"version": "1.0", "users": []}

# Version 2
v2_router = APIRouter(prefix="/api/v2")

@v2_router.get("/users/")
async def get_users_v2():
    return {"version": "2.0", "users": [], "total": 0}

app.include_router(v1_router)
app.include_router(v2_router)
```

### Behind a Proxy Configuration

```python
from fastapi import FastAPI

app = FastAPI(
    root_path="/api/v1",  # If behind a path-stripping proxy
    servers=[
        {"url": "https://stag.example.com", "description": "Staging environment"},
        {"url": "https://prod.example.com", "description": "Production environment"},
    ]
)
```

### Rate Limiting Pattern

```python
from datetime import datetime, timedelta
from collections import defaultdict

request_counts = defaultdict(list)

async def rate_limit(request: Request):
    client_ip = request.client.host
    now = datetime.now()
    minute_ago = now - timedelta(minutes=1)
    
    # Clean old entries
    request_counts[client_ip] = [
        req_time for req_time in request_counts[client_ip] 
        if req_time > minute_ago
    ]
    
    if len(request_counts[client_ip]) >= 60:  # 60 requests per minute
        raise HTTPException(
            status_code=429,
            detail="Rate limit exceeded"
        )
    
    request_counts[client_ip].append(now)
```

## Production Best Practices

1. **Always use environment variables** for configuration
2. **Implement comprehensive error handling** with proper status codes
3. **Use connection pooling** for databases and external services
4. **Add request IDs** for tracing and debugging
5. **Implement health checks** for monitoring
6. **Use structured logging** in JSON format
7. **Set up proper CORS** configuration
8. **Use HTTPS in production** with proper certificates
9. **Implement rate limiting** to prevent abuse
10. **Monitor performance** with metrics and APM tools
11. **Use background tasks** for long-running operations
12. **Implement proper authentication** and authorization
13. **Version your APIs** for backward compatibility
14. **Document with OpenAPI** (automatic in FastAPI)
15. **Test thoroughly** with TestClient and pytest