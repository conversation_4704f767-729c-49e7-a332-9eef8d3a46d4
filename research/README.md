# Research Directory - Third-Party Documentation

This directory contains scraped and organized documentation from third-party APIs, libraries, and services used in the Episteme project. This documentation serves as the **source of truth** for implementation.

## Purpose

The research directory is critical for Context Engineering because:

1. **Documentation is Source of Truth** - AI knowledge is often outdated; fresh documentation ensures accuracy
2. **Official Sources Only** - Only official documentation pages are used for implementation
3. **Comprehensive Coverage** - 30-100+ pages scraped per technology for complete context
4. **Organized by Technology** - Each technology gets its own directory for easy reference

## Directory Structure

```
research/
├── README.md                   # This file
├── rust/                      # Rust language and ecosystem
│   ├── std_library.md         # Standard library documentation
│   ├── tokio.md              # Async runtime documentation
│   ├── axum.md               # Web framework documentation
│   └── serde.md              # Serialization documentation
├── google-cloud/              # Google Cloud Platform services
│   ├── spanner.md            # Cloud Spanner documentation
│   ├── cloud-run.md          # Cloud Run documentation
│   ├── storage.md            # Cloud Storage documentation
│   └── pubsub.md             # Pub/Sub documentation
├── tree-sitter/               # Tree-sitter parsing library
│   ├── core.md               # Core tree-sitter documentation
│   ├── rust-bindings.md      # Rust bindings documentation
│   └── language-grammars.md  # Language grammar documentation
├── databases/                 # Database technologies
│   ├── spanner-client.md     # Spanner client library
│   ├── redis.md              # Redis documentation
│   └── sql-patterns.md       # SQL best practices
├── security/                  # Security libraries and practices
│   ├── jwt.md                # JWT implementation
│   ├── oauth.md              # OAuth patterns
│   └── rate-limiting.md      # Rate limiting strategies
└── monitoring/                # Monitoring and observability
    ├── prometheus.md         # Prometheus metrics
    ├── cloud-monitoring.md   # Google Cloud Monitoring
    └── logging.md            # Structured logging
```

## Research Methodology

### Documentation Scraping Process

1. **Identify Official Sources** - Only use official documentation pages
2. **Comprehensive Scraping** - Scrape 30-100+ pages per technology
3. **Quality Validation** - Verify scraped content is complete and accurate
4. **Re-scrape on Failure** - If a page 404s or has minimal content, try again
5. **Organize by Technology** - Store each technology in its own directory
6. **Update Regularly** - Keep documentation current with latest versions

### Scraping Commands

```bash
# Example scraping command using Jina
curl "https://r.jina.ai/https://docs.rs/tokio/latest/tokio/" \
  -H "Authorization: Bearer jina_token"

# Store output in appropriate directory
# research/rust/tokio.md
```

### Quality Standards

- **Complete Content** - Ensure scraped pages contain full documentation
- **Proper Formatting** - Maintain readable markdown format
- **Code Examples** - Include all code examples from documentation
- **Version Information** - Note the version of documentation scraped
- **Last Updated** - Include scraping date for freshness tracking

## Usage Guidelines

### For AI Assistants

1. **Always Check Research First** - Before implementing any third-party integration, check the research directory
2. **Use as Source of Truth** - Never rely on outdated AI knowledge; use fresh documentation
3. **Reference Specific Sections** - Point to specific documentation sections in PRPs
4. **Validate Against Documentation** - Ensure implementations match official patterns

### For Developers

1. **Update When Needed** - Re-scrape documentation when versions change
2. **Add New Technologies** - Create new directories for new dependencies
3. **Maintain Organization** - Keep files organized by technology and purpose
4. **Document Sources** - Include original URLs and scraping dates

## Integration with Context Engineering

### PRP Generation
- Research directory is referenced during PRP creation
- Official documentation patterns are included in implementation blueprints
- API examples and gotchas are incorporated into PRPs

### Implementation Validation
- Code implementations are validated against official documentation
- API usage patterns follow documented best practices
- Error handling matches official recommendations

### Continuous Updates
- Documentation is re-scraped when dependencies are updated
- New features reference latest official documentation
- Breaking changes are identified through documentation comparison

## File Naming Conventions

- **Technology directories**: `{technology-name}/` (e.g., `rust/`, `google-cloud/`)
- **Documentation files**: `{component}.md` (e.g., `tokio.md`, `spanner.md`)
- **Multi-part docs**: `{component}-{section}.md` (e.g., `spanner-client.md`)

## Metadata Format

Each documentation file should include metadata:

```markdown
# Technology Name - Component Documentation

**Source**: https://official-docs-url.com
**Version**: 1.2.3
**Scraped**: 2025-07-14
**Last Updated**: 2025-07-14

## Overview
[Documentation content...]
```

## Best Practices

1. **Official Sources Only** - Never use unofficial or third-party documentation
2. **Complete Scraping** - Scrape comprehensive sections, not just summaries
3. **Regular Updates** - Keep documentation current with project dependencies
4. **Quality Validation** - Verify scraped content is complete and accurate
5. **Organized Storage** - Maintain clear directory structure and naming
6. **Version Tracking** - Note versions and update dates for all documentation

## Common Technologies

### Current Research Priorities

1. **Rust Ecosystem** - Core language, Tokio, Axum, Serde, Tree-sitter
2. **Google Cloud** - Spanner, Cloud Run, Storage, Pub/Sub, Monitoring
3. **Security** - JWT, OAuth, rate limiting, input validation
4. **Databases** - Spanner client, Redis, connection pooling
5. **Monitoring** - Prometheus, Cloud Monitoring, structured logging

### Future Research Areas

- Machine Learning APIs (Vertex AI)
- Additional parsing libraries
- Performance optimization tools
- Testing frameworks
- CI/CD platforms

---

**Note**: This directory is essential for Context Engineering success. Fresh, official documentation ensures accurate implementations and reduces AI hallucination risks.
