# WebFetch Implementation Guide - Research Coordination System

**Created**: 2025-07-15
**Version**: 1.0
**Purpose**: Executable implementation of WebFetch wrapper with retry logic

## Implementation Pattern

### Core WebFetch Wrapper with Retry Logic

```python
import time
import re
from typing import Dict, Optional, Any
from datetime import datetime

class WebFetchWrapper:
    """Resilient WebFetch wrapper for research coordination"""
    
    def __init__(self):
        self.official_domains = [
            'docs.rs', 'rust-lang.org', 'tokio.rs', 
            'docs.python.org', 'fastapi.tiangolo.com', 'huggingface.co',
            'cloud.google.com', 'owasp.org', 'prometheus.io',
            'opentelemetry.io', 'brendangregg.com'
        ]
        self.min_content_length = 500
        self.max_retries = 3
        
    def calculate_backoff(self, attempt: int) -> float:
        """Calculate exponential backoff with jitter"""
        base_delay = 2 ** attempt
        jitter = base_delay * 0.1 * (0.5 - random.random())
        return min(base_delay + jitter, 16.0)
    
    def is_official_source(self, url: str) -> bool:
        """Verify URL is from official documentation source"""
        return any(domain in url for domain in self.official_domains)
    
    def validate_content(self, content: str, url: str) -> Dict[str, Any]:
        """Validate scraped content quality"""
        validation = {
            'passed': True,
            'issues': [],
            'metrics': {}
        }
        
        # Check content length
        content_length = len(content)
        validation['metrics']['length'] = content_length
        
        if content_length < self.min_content_length:
            validation['passed'] = False
            validation['issues'].append(f'Content too short: {content_length} chars')
        
        # Check for error indicators
        error_patterns = [
            r'404.*not found', r'access denied', r'authentication required',
            r'error.*occurred', r'page.*unavailable'
        ]
        
        for pattern in error_patterns:
            if re.search(pattern, content.lower()):
                validation['passed'] = False
                validation['issues'].append(f'Error pattern detected: {pattern}')
        
        # Verify official source
        if not self.is_official_source(url):
            validation['passed'] = False
            validation['issues'].append('Not an official documentation source')
        
        return validation
    
    def extract_metadata(self, url: str, content: str) -> Dict[str, Any]:
        """Extract metadata from scraped content"""
        # Extract title
        title_match = re.search(r'#\s+(.+?)(?:\n|$)', content)
        title = title_match.group(1) if title_match else 'Untitled'
        
        # Calculate metrics
        word_count = len(content.split())
        line_count = content.count('\n')
        
        return {
            'url': url,
            'title': title,
            'word_count': word_count,
            'line_count': line_count,
            'scrape_timestamp': datetime.utcnow().isoformat(),
            'domain': re.search(r'https?://([^/]+)', url).group(1)
        }
    
    def resilient_fetch(self, url: str, prompt: str) -> Dict[str, Any]:
        """Execute WebFetch with retry logic and validation"""
        
        for attempt in range(self.max_retries + 1):
            try:
                # Execute WebFetch (simulated here)
                result = self.webfetch(url, prompt)
                
                # Handle redirects
                if 'redirect' in result:
                    url = result['redirect_url']
                    continue
                
                content = result.get('content', '')
                
                # Validate content
                validation = self.validate_content(content, url)
                metadata = self.extract_metadata(url, content)
                
                if validation['passed']:
                    return {
                        'success': True,
                        'content': content,
                        'metadata': metadata,
                        'validation': validation,
                        'attempts': attempt + 1
                    }
                
                # Retry on validation failure
                if attempt < self.max_retries:
                    delay = self.calculate_backoff(attempt)
                    time.sleep(delay)
                    continue
                    
            except Exception as e:
                if attempt < self.max_retries:
                    delay = self.calculate_backoff(attempt)
                    time.sleep(delay)
                    continue
                else:
                    return {
                        'success': False,
                        'error': str(e),
                        'url': url,
                        'attempts': attempt + 1
                    }
        
        # Max retries exceeded
        return {
            'success': False,
            'content': None,
            'metadata': {'url': url},
            'validation': validation,
            'attempts': self.max_retries + 1,
            'error': 'Max retries exceeded'
        }
```

## Usage Examples

### Basic Documentation Scraping
```python
wrapper = WebFetchWrapper()

# Scrape Rust documentation
result = wrapper.resilient_fetch(
    url="https://doc.rust-lang.org/book/ch04-01-what-is-ownership.html",
    prompt="Extract the complete ownership documentation including all code examples"
)

if result['success']:
    print(f"Successfully scraped: {result['metadata']['title']}")
    print(f"Word count: {result['metadata']['word_count']}")
    print(f"Attempts: {result['attempts']}")
else:
    print(f"Failed to scrape: {result['error']}")
```

### Batch Documentation Gathering
```python
def gather_documentation(urls: list) -> list:
    """Gather documentation from multiple URLs"""
    wrapper = WebFetchWrapper()
    results = []
    
    for url in urls:
        result = wrapper.resilient_fetch(
            url=url,
            prompt="Extract complete documentation with examples and best practices"
        )
        results.append(result)
        
        # Rate limiting between requests
        time.sleep(1)
    
    return results

# Example usage
rust_urls = [
    "https://tokio.rs/tokio/tutorial",
    "https://docs.rs/axum/latest/axum/",
    "https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html"
]

results = gather_documentation(rust_urls)
successful = [r for r in results if r['success']]
print(f"Successfully scraped {len(successful)}/{len(results)} pages")
```

## Error Handling Patterns

### Handle Specific Failures
```python
def handle_scraping_failures(result: Dict[str, Any]) -> Optional[str]:
    """Handle different types of scraping failures"""
    
    if not result['success']:
        error = result.get('error', 'Unknown error')
        
        if 'rate limit' in error.lower():
            # Implement longer backoff
            return "rate_limit"
        elif 'authentication' in error.lower():
            # Skip authenticated content
            return "skip"
        elif 'timeout' in error.lower():
            # Retry with different parameters
            return "retry_modified"
        else:
            # Log and continue
            return "log_continue"
    
    return None
```

## Quality Assurance Integration

### Content Quality Scoring
```python
def calculate_quality_score(result: Dict[str, Any]) -> float:
    """Calculate quality score for scraped content"""
    
    if not result['success']:
        return 0.0
    
    score = 1.0
    metadata = result['metadata']
    
    # Penalize short content
    if metadata['word_count'] < 1000:
        score *= 0.8
    
    # Reward comprehensive content
    if metadata['word_count'] > 5000:
        score *= 1.2
    
    # Check for code examples
    if '```' in result['content']:
        score *= 1.1
    
    # Validation issues reduce score
    issues = result['validation'].get('issues', [])
    score *= (1 - 0.1 * len(issues))
    
    return min(max(score, 0.0), 1.0)
```

## Integration with Research Agents

### Agent WebFetch Usage
```python
class ResearchAgent:
    """Base research agent with WebFetch integration"""
    
    def __init__(self, name: str, target_pages: int):
        self.name = name
        self.target_pages = target_pages
        self.wrapper = WebFetchWrapper()
        self.results = []
    
    def scrape_documentation(self, urls: list) -> Dict[str, Any]:
        """Scrape documentation from provided URLs"""
        
        for url in urls[:self.target_pages]:
            result = self.wrapper.resilient_fetch(
                url=url,
                prompt=self.get_scraping_prompt()
            )
            
            if result['success']:
                self.results.append(result)
                self.save_documentation(result)
            else:
                self.log_failure(result)
        
        return self.generate_report()
    
    def get_scraping_prompt(self) -> str:
        """Get technology-specific scraping prompt"""
        return f"Extract complete {self.name} documentation including code examples, best practices, and implementation patterns"
    
    def save_documentation(self, result: Dict[str, Any]):
        """Save scraped documentation to appropriate directory"""
        # Implementation specific to agent type
        pass
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate agent scraping report"""
        successful = [r for r in self.results if r['success']]
        
        return {
            'agent': self.name,
            'total_attempts': len(self.results),
            'successful': len(successful),
            'total_words': sum(r['metadata']['word_count'] for r in successful),
            'average_quality': sum(calculate_quality_score(r) for r in successful) / len(successful) if successful else 0
        }
```

## Validation Commands

### Test WebFetch Wrapper
```bash
# Test single URL scraping
echo "Testing WebFetch wrapper with Rust documentation..."
# Expected: Successfully scrapes content with retry logic

# Test validation
echo "Testing content validation..."
# Expected: Detects short content, error pages, unofficial sources

# Test retry logic
echo "Testing exponential backoff..."
# Expected: Retries with increasing delays on failure
```

### Integration Test
```bash
# Test agent integration
echo "Testing research agent with WebFetch..."
# Expected: Agent successfully uses wrapper for documentation gathering

# Test parallel execution
echo "Testing concurrent WebFetch calls..."
# Expected: Multiple agents can use wrapper simultaneously
```

## Next Steps

1. Integrate with actual WebFetch tool
2. Implement rate limiting per domain
3. Add persistent caching for successful scrapes
4. Create domain-specific scraping strategies
5. Implement progress tracking and reporting

This implementation provides a robust foundation for resilient documentation scraping with comprehensive error handling and quality validation.