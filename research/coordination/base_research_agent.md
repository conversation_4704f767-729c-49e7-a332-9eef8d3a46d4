# Base Research Agent Template - Comprehensive Documentation Gathering

**Created**: 2025-07-15
**Version**: 1.0
**Purpose**: Base template for specialized research agents with WebFetch integration

## Overview

This base research agent template provides the foundation for all specialized research agents in the coordination system. It includes WebFetch integration, validation pipelines, progress tracking, and reporting capabilities.

## Agent Architecture

### Core Components
```yaml
base_agent_structure:
  configuration:
    name: "Agent identifier"
    technology_focus: "Specific technology area"
    target_pages: "30-100 pages"
    official_sources: "List of authorized domains"
    
  capabilities:
    - documentation_scraping
    - content_validation
    - progress_tracking
    - failure_handling
    - report_generation
    
  integration:
    - webfetch_wrapper
    - content_validator
    - source_verifier
    - storage_manager
```

## Base Agent Implementation

### Agent Class Template
```python
class BaseResearchAgent:
    """Base template for specialized research agents"""
    
    def __init__(self, config: Dict[str, Any]):
        self.name = config['name']
        self.technology_focus = config['technology_focus']
        self.target_pages = config['target_pages']
        self.official_sources = config['official_sources']
        
        # Integration components
        self.webfetch = WebFetchWrapper()
        self.validator = ContentValidator()
        self.verifier = SourceVerifier()
        
        # State tracking
        self.scraped_pages = []
        self.failed_urls = []
        self.retry_queue = []
        self.progress = {
            'total_target': self.target_pages,
            'scraped': 0,
            'validated': 0,
            'failed': 0
        }
    
    def gather_documentation(self, urls: List[str]) -> Dict[str, Any]:
        """Main documentation gathering method"""
        
        # Initialize gathering session
        session_start = datetime.utcnow()
        
        # Process URLs
        for url in urls:
            if self.progress['scraped'] >= self.target_pages:
                break
                
            result = self.scrape_and_validate(url)
            
            if result['success']:
                self.process_successful_scrape(result)
            else:
                self.handle_failed_scrape(url, result)
        
        # Process retry queue
        self.process_retries()
        
        # Generate final report
        return self.generate_gathering_report(session_start)
    
    def scrape_and_validate(self, url: str) -> Dict[str, Any]:
        """Scrape URL with validation pipeline"""
        
        # Verify source authority
        if not self.verifier.is_official_source(url, self.technology_focus):
            return {
                'success': False,
                'reason': 'Unofficial source',
                'url': url
            }
        
        # Execute WebFetch with retry logic
        fetch_result = self.webfetch.resilient_fetch(
            url=url,
            prompt=self.generate_scraping_prompt()
        )
        
        if not fetch_result['success']:
            return fetch_result
        
        # Validate content quality
        validation = self.validator.validate_content(
            content=fetch_result['content'],
            technology=self.technology_focus
        )
        
        fetch_result['validation'] = validation
        
        # Calculate quality score
        quality_score = self.calculate_quality_score(fetch_result)
        fetch_result['quality_score'] = quality_score
        
        # Determine success based on quality threshold
        fetch_result['success'] = quality_score >= 0.7
        
        return fetch_result
    
    def generate_scraping_prompt(self) -> str:
        """Generate technology-specific scraping prompt"""
        return f"""
        Extract complete {self.technology_focus} documentation including:
        - Full technical content and explanations
        - All code examples with proper formatting
        - Implementation patterns and best practices
        - Configuration examples and options
        - Error handling patterns
        - Performance considerations
        - Security guidelines if present
        
        Preserve all markdown formatting, code blocks, and structure.
        Include version information and last update date if available.
        """
    
    def process_successful_scrape(self, result: Dict[str, Any]):
        """Process and store successful scrape"""
        
        # Update progress
        self.progress['scraped'] += 1
        if result['validation']['passed']:
            self.progress['validated'] += 1
        
        # Store content
        self.store_documentation(result)
        
        # Track for reporting
        self.scraped_pages.append({
            'url': result['metadata']['url'],
            'title': result['metadata']['title'],
            'word_count': result['metadata']['word_count'],
            'quality_score': result['quality_score'],
            'timestamp': result['metadata']['scrape_timestamp']
        })
    
    def handle_failed_scrape(self, url: str, result: Dict[str, Any]):
        """Handle scraping failures with retry logic"""
        
        self.progress['failed'] += 1
        
        # Determine if retry is appropriate
        if self.should_retry(result):
            self.retry_queue.append({
                'url': url,
                'attempts': result.get('attempts', 1),
                'reason': result.get('error', 'Unknown')
            })
        else:
            self.failed_urls.append({
                'url': url,
                'reason': result.get('error', 'Unknown'),
                'attempts': result.get('attempts', 1)
            })
    
    def should_retry(self, result: Dict[str, Any]) -> bool:
        """Determine if URL should be retried"""
        
        error = result.get('error', '').lower()
        
        # Retry on temporary errors
        if any(term in error for term in ['timeout', 'rate limit', 'temporary']):
            return True
        
        # Don't retry on permanent errors
        if any(term in error for term in ['404', 'authentication', 'forbidden']):
            return False
        
        # Retry if quality was close to threshold
        if result.get('quality_score', 0) >= 0.6:
            return True
        
        return False
    
    def process_retries(self):
        """Process retry queue with modified strategies"""
        
        for retry_item in self.retry_queue[:]:
            if self.progress['scraped'] >= self.target_pages:
                break
            
            # Implement backoff
            time.sleep(2 ** retry_item['attempts'])
            
            # Retry with modified approach
            result = self.scrape_and_validate(retry_item['url'])
            
            if result['success']:
                self.process_successful_scrape(result)
                self.retry_queue.remove(retry_item)
            else:
                retry_item['attempts'] += 1
                if retry_item['attempts'] > 3:
                    self.failed_urls.append(retry_item)
                    self.retry_queue.remove(retry_item)
    
    def store_documentation(self, result: Dict[str, Any]):
        """Store scraped documentation in appropriate directory"""
        
        # Generate filename from URL and title
        filename = self.generate_filename(result['metadata'])
        
        # Determine storage path
        storage_path = f"research/{self.technology_focus.lower()}/{filename}"
        
        # Prepare content with metadata header
        content = self.format_documentation(result)
        
        # Store file (simulated here)
        # In actual implementation, use Write tool
        print(f"Storing documentation at: {storage_path}")
    
    def generate_filename(self, metadata: Dict[str, Any]) -> str:
        """Generate appropriate filename from metadata"""
        
        # Extract meaningful name from title or URL
        title = metadata.get('title', 'untitled')
        title_clean = re.sub(r'[^a-zA-Z0-9-]', '-', title.lower())
        title_clean = re.sub(r'-+', '-', title_clean).strip('-')
        
        # Limit length and add extension
        filename = title_clean[:50] + '.md'
        
        return filename
    
    def format_documentation(self, result: Dict[str, Any]) -> str:
        """Format documentation with metadata header"""
        
        metadata = result['metadata']
        
        header = f"""# {metadata['title']}

**Source**: {metadata['url']}
**Scraped**: {metadata['scrape_timestamp']}
**Word Count**: {metadata['word_count']}
**Quality Score**: {result['quality_score']:.2f}

---

"""
        
        return header + result['content']
    
    def calculate_quality_score(self, result: Dict[str, Any]) -> float:
        """Calculate comprehensive quality score"""
        
        score = 0.0
        weights = {
            'content_length': 0.3,
            'code_examples': 0.2,
            'structure': 0.2,
            'completeness': 0.2,
            'currency': 0.1
        }
        
        # Content length score
        word_count = result['metadata']['word_count']
        if word_count >= 2000:
            score += weights['content_length']
        elif word_count >= 1000:
            score += weights['content_length'] * 0.7
        elif word_count >= 500:
            score += weights['content_length'] * 0.4
        
        # Code examples score
        content = result['content']
        code_blocks = content.count('```')
        if code_blocks >= 6:
            score += weights['code_examples']
        elif code_blocks >= 3:
            score += weights['code_examples'] * 0.7
        elif code_blocks >= 1:
            score += weights['code_examples'] * 0.4
        
        # Structure score (headers, sections)
        headers = len(re.findall(r'^#+\s', content, re.MULTILINE))
        if headers >= 5:
            score += weights['structure']
        elif headers >= 3:
            score += weights['structure'] * 0.7
        
        # Completeness (from validation)
        if result.get('validation', {}).get('passed', False):
            score += weights['completeness']
        
        # Currency (prefer recent documentation)
        # This would check documentation date in actual implementation
        score += weights['currency'] * 0.8  # Default assumption
        
        return min(score, 1.0)
    
    def generate_gathering_report(self, session_start: datetime) -> Dict[str, Any]:
        """Generate comprehensive gathering report"""
        
        session_duration = (datetime.utcnow() - session_start).total_seconds()
        
        # Calculate statistics
        total_words = sum(page['word_count'] for page in self.scraped_pages)
        avg_quality = sum(page['quality_score'] for page in self.scraped_pages) / len(self.scraped_pages) if self.scraped_pages else 0
        
        report = {
            'agent': self.name,
            'technology': self.technology_focus,
            'session': {
                'start': session_start.isoformat(),
                'duration_seconds': session_duration,
                'pages_per_minute': (self.progress['scraped'] / session_duration) * 60 if session_duration > 0 else 0
            },
            'progress': self.progress,
            'statistics': {
                'total_words': total_words,
                'average_quality_score': avg_quality,
                'success_rate': (self.progress['scraped'] / (self.progress['scraped'] + self.progress['failed'])) if (self.progress['scraped'] + self.progress['failed']) > 0 else 0
            },
            'scraped_pages': self.scraped_pages,
            'failed_urls': self.failed_urls,
            'coverage': {
                'target': self.target_pages,
                'achieved': self.progress['scraped'],
                'percentage': (self.progress['scraped'] / self.target_pages) * 100
            }
        }
        
        return report
```

## Agent Specialization Pattern

### Creating Specialized Agents
```python
class RustResearchAgent(BaseResearchAgent):
    """Specialized agent for Rust documentation"""
    
    def __init__(self):
        config = {
            'name': 'rust_research_agent',
            'technology_focus': 'Rust',
            'target_pages': 50,
            'official_sources': [
                'doc.rust-lang.org',
                'docs.rs',
                'tokio.rs',
                'rust-lang.github.io',
                'rustsec.org'
            ]
        }
        super().__init__(config)
        
        # Rust-specific URL patterns
        self.url_patterns = [
            'https://doc.rust-lang.org/book/',
            'https://doc.rust-lang.org/reference/',
            'https://doc.rust-lang.org/std/',
            'https://tokio.rs/tokio/tutorial',
            'https://docs.rs/axum/latest/'
        ]
    
    def generate_scraping_prompt(self) -> str:
        """Rust-specific scraping prompt"""
        return """
        Extract complete Rust documentation including:
        - Memory safety patterns and ownership rules
        - Error handling with Result<T, E> patterns
        - Async/await implementations with Tokio
        - Unsafe code guidelines and best practices
        - Performance optimization techniques
        - Security considerations
        - All code examples with cargo commands
        - Production deployment patterns
        
        Pay special attention to:
        - Lifetime annotations
        - Trait implementations
        - Macro usage
        - FFI boundaries
        """
```

## Integration Examples

### Deploy Agent with Orchestrator
```python
# Create specialized agent
rust_agent = RustResearchAgent()

# Prepare URL list
rust_urls = [
    "https://doc.rust-lang.org/book/ch04-01-what-is-ownership.html",
    "https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html",
    "https://tokio.rs/tokio/tutorial/async",
    "https://docs.rs/axum/latest/axum/",
    # ... more URLs
]

# Execute documentation gathering
report = rust_agent.gather_documentation(rust_urls)

# Display results
print(f"Agent: {report['agent']}")
print(f"Pages scraped: {report['progress']['scraped']}/{report['coverage']['target']}")
print(f"Total words: {report['statistics']['total_words']:,}")
print(f"Average quality: {report['statistics']['average_quality_score']:.2%}")
print(f"Success rate: {report['statistics']['success_rate']:.2%}")
```

## Validation Patterns

### Pre-Scraping Validation
```python
def validate_url_list(urls: List[str], agent: BaseResearchAgent) -> List[str]:
    """Validate and filter URL list before scraping"""
    
    valid_urls = []
    
    for url in urls:
        # Check domain authority
        if agent.verifier.is_official_source(url, agent.technology_focus):
            valid_urls.append(url)
        else:
            print(f"Skipping unofficial source: {url}")
    
    return valid_urls
```

### Post-Scraping Quality Check
```python
def quality_assurance_check(report: Dict[str, Any]) -> bool:
    """Perform quality assurance on gathering report"""
    
    # Check coverage
    if report['coverage']['percentage'] < 80:
        print(f"Warning: Low coverage {report['coverage']['percentage']:.1f}%")
        return False
    
    # Check quality
    if report['statistics']['average_quality_score'] < 0.7:
        print(f"Warning: Low quality score {report['statistics']['average_quality_score']:.2f}")
        return False
    
    # Check success rate
    if report['statistics']['success_rate'] < 0.8:
        print(f"Warning: Low success rate {report['statistics']['success_rate']:.2%}")
        return False
    
    return True
```

## Error Recovery Patterns

### Handling Rate Limits
```python
def handle_rate_limit(agent: BaseResearchAgent, domain: str):
    """Handle rate limiting for specific domain"""
    
    # Implement exponential backoff
    backoff_seconds = 60
    
    print(f"Rate limited on {domain}. Waiting {backoff_seconds}s...")
    time.sleep(backoff_seconds)
    
    # Continue with reduced rate
    agent.webfetch.rate_limit_delay = 5  # seconds between requests
```

### Recovery from Partial Failures
```python
def recover_partial_gathering(agent: BaseResearchAgent, report: Dict[str, Any]) -> Dict[str, Any]:
    """Attempt to recover from partial gathering failure"""
    
    if report['coverage']['percentage'] < 100:
        # Identify gaps
        remaining_target = agent.target_pages - report['progress']['scraped']
        
        # Generate additional URLs
        additional_urls = generate_fallback_urls(
            agent.technology_focus, 
            remaining_target
        )
        
        # Attempt supplementary gathering
        supplementary_report = agent.gather_documentation(additional_urls)
        
        # Merge reports
        return merge_gathering_reports(report, supplementary_report)
    
    return report
```

## Next Steps

1. Implement specialized agents for each technology
2. Create URL generation strategies per technology
3. Implement parallel agent execution
4. Add persistent state management
5. Create comprehensive reporting dashboard

This base template provides a robust foundation for all research agents with comprehensive error handling, validation, and reporting capabilities.