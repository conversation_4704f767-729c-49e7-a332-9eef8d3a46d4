# Google Cloud Python Client Libraries Documentation

**Sources**: 
- Google Cloud Python Client: https://github.com/googleapis/google-cloud-python
- Google Cloud Storage Python Client: https://github.com/googleapis/python-storage
- Official Google Cloud Python documentation and examples
**Scraped Date**: 2025-07-15
**Trust Score**: 8.7 (Official Google Cloud Libraries)

## Table of Contents

1. [Authentication and Setup](#authentication-and-setup)
2. [Google Cloud Storage](#google-cloud-storage)
3. [Google Cloud Pub/Sub](#google-cloud-pubsub)
4. [Google Cloud Firestore](#google-cloud-firestore)
5. [Google Cloud BigQuery](#google-cloud-bigquery)
6. [Common Patterns](#common-patterns)
7. [Async Operations](#async-operations)
8. [Error Handling and Retries](#error-handling-and-retries)
9. [Monitoring and Observability](#monitoring-and-observability)
10. [Production Best Practices](#production-best-practices)

## Authentication and Setup

### Installation

```bash
# Install individual services
pip install google-cloud-storage
pip install google-cloud-pubsub
pip install google-cloud-firestore
pip install google-cloud-bigquery

# Install with optional dependencies
pip install google-cloud-storage[tracing]

# Install multiple services at once
pip install google-cloud-storage google-cloud-pubsub google-cloud-firestore

# Create virtual environment
python3 -m venv <your-env>
source <your-env>/bin/activate  # Mac/Linux
# .\<your-env>\Scripts\activate  # Windows
```

### Authentication Setup

#### Service Account Key File

```bash
# Set environment variable for authentication
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/service-account-key.json"

# Verify authentication
gcloud auth application-default print-access-token
```

#### Application Default Credentials

```python
from google.cloud import storage
from google.auth import default

# Use Application Default Credentials
credentials, project = default()
client = storage.Client(credentials=credentials, project=project)

# Or let the client auto-detect
client = storage.Client()  # Uses environment credentials automatically
```

#### Explicit Credentials

```python
from google.cloud import storage
from google.oauth2 import service_account

# Load credentials from file
credentials = service_account.Credentials.from_service_account_file(
    '/path/to/service-account-key.json'
)

client = storage.Client(credentials=credentials, project='your-project-id')

# From JSON string
import json
credentials_info = json.loads(credentials_json_string)
credentials = service_account.Credentials.from_service_account_info(
    credentials_info
)
```

## Google Cloud Storage

### Basic Client Setup

```python
from google.cloud import storage
import os

# Initialize client
client = storage.Client()

# Get bucket reference
bucket_name = "my-bucket"
bucket = client.bucket(bucket_name)
# or
bucket = client.get_bucket(bucket_name)
```

### Bucket Operations

#### Creating and Managing Buckets

```python
from google.cloud import storage
from google.cloud.storage import Bucket

client = storage.Client()

# Create bucket
bucket_name = "my-new-bucket"
bucket = client.create_bucket(bucket_name)
print(f"Bucket {bucket.name} created.")

# Create bucket with specific location and storage class
bucket = client.create_bucket(
    bucket_name,
    location="US-CENTRAL1",
    storage_class="STANDARD"
)

# Create dual-region bucket
bucket = storage.Bucket(client, name="dual-region-bucket")
bucket.location = "US"
bucket.storage_class = "STANDARD"
bucket.create(
    location="us-central1",
    data_residency_type="DUAL_REGION",
    placement={"data_locations": ["us-central1", "us-east1"]}
)

# List buckets
for bucket in client.list_buckets():
    print(f"Bucket: {bucket.name}")

# Get bucket metadata
bucket = client.get_bucket("my-bucket")
print(f"Bucket location: {bucket.location}")
print(f"Storage class: {bucket.storage_class}")
print(f"Time created: {bucket.time_created}")

# Delete bucket
bucket.delete()
```

#### Bucket Configuration

```python
# Enable versioning
bucket.versioning_enabled = True
bucket.patch()

# Set lifecycle management
rule = {
    "action": {"type": "Delete"},
    "condition": {"age": 30}
}
bucket.lifecycle_rules = [rule]
bucket.patch()

# Set CORS configuration
cors = [
    {
        "origin": ["https://mydomain.com"],
        "responseHeader": ["Content-Type", "x-goog-resumable"],
        "method": ["GET", "POST"],
        "maxAgeSeconds": 3600
    }
]
bucket.cors = cors
bucket.patch()

# Set default KMS key
bucket.default_kms_key_name = "projects/my-project/locations/global/keyRings/my-ring/cryptoKeys/my-key"
bucket.patch()

# Set retention policy
bucket.retention_period = 100  # seconds
bucket.patch()

# Lock retention policy (irreversible)
bucket.lock_retention_policy()
```

### Blob (File) Operations

#### Upload Operations

```python
# Upload from file
blob_name = "my-file.txt"
blob = bucket.blob(blob_name)

# Upload from local file
with open("local-file.txt", "rb") as f:
    blob.upload_from_file(f)

# Upload from filename
blob.upload_from_filename("local-file.txt")

# Upload from string/bytes
content = "Hello, World!"
blob.upload_from_string(content)

# Upload with metadata
blob.metadata = {
    "category": "documents",
    "author": "user123"
}
blob.content_type = "text/plain"
blob.upload_from_string(content)

# Upload with encryption
encryption_key = "c7f7fbaafa5d966...your-256-bit-key"
blob.upload_from_filename(
    "local-file.txt",
    encryption_key=encryption_key
)

# Upload with KMS key
blob.kms_key_name = "projects/my-project/locations/global/keyRings/my-ring/cryptoKeys/my-key"
blob.upload_from_filename("local-file.txt")

# Chunked/resumable upload for large files
chunk_size = 256 * 1024  # 256KB chunks
blob.chunk_size = chunk_size
blob.upload_from_filename("large-file.zip")
```

#### Download Operations

```python
# Download to file
blob = bucket.blob("my-file.txt")
blob.download_to_filename("downloaded-file.txt")

# Download to memory
content = blob.download_as_bytes()
text_content = blob.download_as_text()

# Download with file-like object
with open("downloaded-file.txt", "wb") as f:
    blob.download_to_file(f)

# Download byte range
start_byte = 100
end_byte = 199
partial_content = blob.download_as_bytes(start=start_byte, end=end_byte)

# Download encrypted file
blob.download_to_filename(
    "decrypted-file.txt",
    encryption_key=encryption_key
)

# Download public file (no authentication needed)
from google.cloud.storage import Client
client = Client.create_anonymous_client()
bucket = client.bucket("public-bucket")
blob = bucket.blob("public-file.txt")
blob.download_to_filename("public-file.txt")
```

#### File Management Operations

```python
# List blobs
for blob in bucket.list_blobs():
    print(f"File: {blob.name}, Size: {blob.size}")

# List with prefix
for blob in bucket.list_blobs(prefix="documents/"):
    print(f"Document: {blob.name}")

# Copy blob
source_bucket = client.bucket("source-bucket")
source_blob = source_bucket.blob("source-file.txt")
destination_bucket = client.bucket("destination-bucket")

copied_blob = destination_bucket.copy_blob(
    source_blob,
    destination_bucket,
    "copied-file.txt"
)

# Move/rename blob
bucket.rename_blob(blob, "new-name.txt")

# Delete blob
blob.delete()

# Compose multiple blobs into one
blob1 = bucket.blob("file1.txt")
blob2 = bucket.blob("file2.txt")
composed_blob = bucket.blob("composed.txt")

composed_blob.compose([blob1, blob2])

# Get blob metadata
blob = bucket.blob("my-file.txt")
blob.reload()  # Refresh metadata
print(f"Content type: {blob.content_type}")
print(f"Size: {blob.size}")
print(f"Created: {blob.time_created}")
print(f"Updated: {blob.updated}")
print(f"MD5 hash: {blob.md5_hash}")
```

### Access Control and Security

#### Bucket-level IAM

```python
from google.cloud import storage

# Get bucket IAM policy
bucket = client.bucket("my-bucket")
policy = bucket.get_iam_policy(requested_policy_version=3)

# Add IAM binding
policy.bindings.append({
    "role": "roles/storage.objectViewer",
    "members": {"user:<EMAIL>", "group:<EMAIL>"}
})

# Add conditional binding
from google.cloud.storage import iam
policy.bindings.append({
    "role": "roles/storage.objectViewer",
    "members": {"user:<EMAIL>"},
    "condition": iam.Condition(
        title="Time-based access",
        description="Access expires on 2024-12-31",
        expression='request.time < timestamp("2024-12-31T23:59:59Z")'
    )
})

# Set policy
bucket.set_iam_policy(policy)

# Test permissions
permissions = [
    "storage.objects.get",
    "storage.objects.create",
    "storage.objects.delete"
]
allowed = bucket.test_iam_permissions(permissions)
print(f"Allowed permissions: {allowed}")
```

#### Signed URLs

```python
from datetime import datetime, timedelta
import google.auth

# Generate signed URL for download
blob = bucket.blob("my-file.txt")
expiration = datetime.now() + timedelta(hours=1)

# V4 signed URL (recommended)
url = blob.generate_signed_url(
    version="v4",
    expiration=expiration,
    method="GET"
)

# Signed URL for upload
upload_url = blob.generate_signed_url(
    version="v4",
    expiration=expiration,
    method="PUT",
    content_type="text/plain"
)

# Signed POST policy for web uploads
from google.cloud.storage import generate_signed_post_policy_v4

policy = generate_signed_post_policy_v4(
    bucket_name="my-bucket",
    blob_name="user-upload.txt",
    expiration=expiration,
    conditions=[
        ["content-length-range", 0, 1000000],  # Max 1MB
        ["starts-with", "$Content-Type", "text/"]
    ]
)

print(f"POST URL: {policy['url']}")
print(f"Form fields: {policy['fields']}")
```

#### Access Control Lists (ACLs)

```python
# Get bucket ACL
bucket = client.bucket("my-bucket")
acl = bucket.acl

# Grant permissions
acl.user("<EMAIL>").grant_read()
acl.group("<EMAIL>").grant_write()
acl.all_authenticated().grant_read()
acl.save()

# File-level ACL
blob = bucket.blob("my-file.txt")
blob_acl = blob.acl
blob_acl.user("<EMAIL>").grant_read()
blob_acl.save()

# List ACL entries
for entry in acl:
    print(f"Entity: {entry['entity']}, Role: {entry['role']}")
```

### Advanced Storage Features

#### Notification Configuration

```python
from google.cloud import pubsub_v1

# Create Pub/Sub topic first
publisher = pubsub_v1.PublisherClient()
topic_path = publisher.topic_path("my-project", "storage-notifications")
publisher.create_topic(request={"name": topic_path})

# Create bucket notification
notification = bucket.notification(
    topic_name=topic_path,
    event_types=[
        "OBJECT_FINALIZE",
        "OBJECT_DELETE"
    ],
    blob_name_prefix="documents/"
)
notification.create()

# List notifications
for notification in bucket.list_notifications():
    print(f"Notification: {notification.notification_id}")
    print(f"Topic: {notification.topic_name}")

# Delete notification
notification.delete()
```

#### Transfer Operations

```python
# Transfer between buckets
from google.cloud import storage_transfer

transfer_client = storage_transfer.StorageTransferServiceClient()

# Create transfer job
transfer_job = {
    "description": "Transfer from bucket A to bucket B",
    "status": "ENABLED",
    "project_id": "my-project",
    "transfer_spec": {
        "gcs_data_source": {
            "bucket_name": "source-bucket"
        },
        "gcs_data_sink": {
            "bucket_name": "destination-bucket"
        },
        "transfer_options": {
            "delete_objects_unique_in_sink": False
        }
    },
    "schedule": {
        "schedule_start_date": {"year": 2024, "month": 1, "day": 1},
        "schedule_end_date": {"year": 2024, "month": 12, "day": 31}
    }
}

operation = transfer_client.create_transfer_job(
    parent=f"projects/my-project",
    transfer_job=transfer_job
)
```

## Google Cloud Pub/Sub

### Basic Setup

```python
from google.cloud import pubsub_v1
import json

# Initialize clients
publisher = pubsub_v1.PublisherClient()
subscriber = pubsub_v1.SubscriberClient()

project_id = "my-project"
topic_id = "my-topic"
subscription_id = "my-subscription"

# Create topic and subscription paths
topic_path = publisher.topic_path(project_id, topic_id)
subscription_path = subscriber.subscription_path(project_id, subscription_id)
```

### Topic Operations

```python
# Create topic
try:
    topic = publisher.create_topic(request={"name": topic_path})
    print(f"Created topic: {topic.name}")
except Exception as e:
    print(f"Topic creation failed: {e}")

# List topics
for topic in publisher.list_topics(request={"project": f"projects/{project_id}"}):
    print(f"Topic: {topic.name}")

# Get topic
topic = publisher.get_topic(request={"topic": topic_path})
print(f"Topic: {topic.name}")

# Delete topic
publisher.delete_topic(request={"topic": topic_path})
```

### Publishing Messages

```python
# Publish simple message
message_data = "Hello, Pub/Sub!"
future = publisher.publish(topic_path, message_data.encode("utf-8"))
message_id = future.result()
print(f"Published message ID: {message_id}")

# Publish with attributes
message_data = json.dumps({
    "user_id": "12345",
    "action": "login",
    "timestamp": "2024-01-01T12:00:00Z"
})

future = publisher.publish(
    topic_path,
    message_data.encode("utf-8"),
    user_id="12345",
    event_type="login",
    source="web"
)

# Batch publishing
futures = []
for i in range(10):
    message_data = f"Message {i}"
    future = publisher.publish(
        topic_path,
        message_data.encode("utf-8"),
        message_number=str(i)
    )
    futures.append(future)

# Wait for all messages to be published
for future in futures:
    try:
        message_id = future.result(timeout=10)
        print(f"Published: {message_id}")
    except Exception as e:
        print(f"Publishing failed: {e}")

# Configure publisher settings
publish_options = pubsub_v1.types.PublisherOptions(
    enable_message_ordering=True,
    flow_control=pubsub_v1.types.PublishFlowControl(
        message_limit=1000,
        byte_limit=1024 * 1024  # 1MB
    )
)

publisher = pubsub_v1.PublisherClient(
    publisher_options=publish_options
)

# Publish with ordering key
future = publisher.publish(
    topic_path,
    message_data.encode("utf-8"),
    ordering_key="user-12345"
)
```

### Subscription Operations

```python
# Create subscription
try:
    subscription = subscriber.create_subscription(
        request={
            "name": subscription_path,
            "topic": topic_path,
            "ack_deadline_seconds": 60,
            "message_retention_duration": {"seconds": 7 * 24 * 60 * 60},  # 7 days
            "retain_acked_messages": False,
            "enable_message_ordering": True
        }
    )
    print(f"Created subscription: {subscription.name}")
except Exception as e:
    print(f"Subscription creation failed: {e}")

# Create push subscription
push_config = pubsub_v1.types.PushConfig(
    push_endpoint="https://myapp.com/webhook/pubsub",
    attributes={
        "x-goog-version": "v1"
    }
)

subscription = subscriber.create_subscription(
    request={
        "name": subscription_path,
        "topic": topic_path,
        "push_config": push_config
    }
)

# Create subscription with dead letter policy
dead_letter_policy = pubsub_v1.types.DeadLetterPolicy(
    dead_letter_topic=dead_letter_topic_path,
    max_delivery_attempts=5
)

subscription = subscriber.create_subscription(
    request={
        "name": subscription_path,
        "topic": topic_path,
        "dead_letter_policy": dead_letter_policy
    }
)

# List subscriptions
for subscription in subscriber.list_subscriptions(
    request={"project": f"projects/{project_id}"}
):
    print(f"Subscription: {subscription.name}")
```

### Message Consumption

```python
import time
from concurrent.futures import ThreadPoolExecutor

# Simple pull
def callback(message):
    print(f"Received message: {message.data.decode('utf-8')}")
    print(f"Message ID: {message.message_id}")
    print(f"Attributes: {message.attributes}")
    print(f"Publish time: {message.publish_time}")
    
    # Acknowledge message
    message.ack()

# Start listening
flow_control = pubsub_v1.types.FlowControl(max_messages=100)
streaming_pull_future = subscriber.pull(
    request={"subscription": subscription_path, "max_messages": 10},
    timeout=300.0
)

print("Listening for messages...")

try:
    # Keep the main thread running
    streaming_pull_future.result()
except KeyboardInterrupt:
    streaming_pull_future.cancel()
    print("Cancelled message pulling")

# Advanced message processing
def process_message(message):
    try:
        # Process message data
        data = json.loads(message.data.decode('utf-8'))
        
        # Simulate processing
        time.sleep(1)
        
        # Acknowledge successful processing
        message.ack()
        print(f"Processed message: {message.message_id}")
        
    except Exception as e:
        # Negative acknowledge on failure
        message.nack()
        print(f"Failed to process message: {e}")

# Concurrent message processing
def callback_with_concurrency(message):
    with ThreadPoolExecutor(max_workers=4) as executor:
        executor.submit(process_message, message)

# Advanced flow control
flow_control = pubsub_v1.types.FlowControl(
    max_messages=1000,
    max_bytes=1024 * 1024 * 1024,  # 1GB
    use_legacy_flow_control=False
)

# Synchronous pull (for testing/debugging)
response = subscriber.pull(
    request={
        "subscription": subscription_path,
        "max_messages": 10
    },
    timeout=30.0
)

for received_message in response.received_messages:
    message = received_message.message
    print(f"Data: {message.data.decode('utf-8')}")
    
    # Acknowledge
    subscriber.acknowledge(
        request={
            "subscription": subscription_path,
            "ack_ids": [received_message.ack_id]
        }
    )
```

### Async Pub/Sub Operations

```python
import asyncio
from google.cloud import pubsub_v1

async def async_publish():
    # Async publisher
    publisher = pubsub_v1.PublisherClient()
    
    async def publish_message(data):
        future = publisher.publish(topic_path, data.encode("utf-8"))
        return await asyncio.wrap_future(future)
    
    # Publish multiple messages concurrently
    tasks = []
    for i in range(10):
        task = publish_message(f"Async message {i}")
        tasks.append(task)
    
    message_ids = await asyncio.gather(*tasks)
    print(f"Published message IDs: {message_ids}")

# Async subscriber
async def async_callback(message):
    print(f"Async received: {message.data.decode('utf-8')}")
    message.ack()

async def async_subscribe():
    # Note: Subscriber doesn't have native async support
    # Use asyncio.run_in_executor for CPU-bound work
    loop = asyncio.get_event_loop()
    
    def blocking_pull():
        response = subscriber.pull(
            request={
                "subscription": subscription_path,
                "max_messages": 10
            },
            timeout=30.0
        )
        return response
    
    response = await loop.run_in_executor(None, blocking_pull)
    
    for received_message in response.received_messages:
        await async_callback(received_message.message)

# Run async operations
asyncio.run(async_publish())
asyncio.run(async_subscribe())
```

## Google Cloud Firestore

### Client Setup

```python
from google.cloud import firestore
from google.cloud.firestore_v1 import FieldFilter, Query
import datetime

# Initialize client
db = firestore.Client(project="my-project")

# Initialize with custom settings
db = firestore.Client(
    project="my-project",
    database="my-database"  # For multiple databases
)
```

### Document Operations

```python
# Add document with auto-generated ID
doc_ref = db.collection("users").add({
    "name": "John Doe",
    "email": "<EMAIL>",
    "age": 30,
    "created_at": firestore.SERVER_TIMESTAMP
})
print(f"Document ID: {doc_ref[1].id}")

# Add document with specific ID
doc_ref = db.collection("users").document("user123")
doc_ref.set({
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "age": 25,
    "preferences": {
        "theme": "dark",
        "notifications": True
    },
    "tags": ["developer", "python", "gcp"]
})

# Update document
doc_ref.update({
    "age": 26,
    "last_login": firestore.SERVER_TIMESTAMP
})

# Update nested fields
doc_ref.update({
    "preferences.theme": "light",
    "preferences.language": "en"
})

# Increment field
doc_ref.update({
    "login_count": firestore.Increment(1)
})

# Array operations
doc_ref.update({
    "tags": firestore.ArrayUnion(["firebase"]),
    "old_tags": firestore.ArrayRemove(["deprecated"])
})

# Get document
doc = doc_ref.get()
if doc.exists:
    print(f"Document data: {doc.to_dict()}")
    print(f"Document ID: {doc.id}")
    print(f"Update time: {doc.update_time}")
else:
    print("Document not found")

# Delete document
doc_ref.delete()

# Delete specific fields
doc_ref.update({
    "temporary_field": firestore.DELETE_FIELD
})
```

### Batch Operations

```python
# Batch writes
batch = db.batch()

# Add multiple operations to batch
users_ref = db.collection("users")

for i in range(5):
    doc_ref = users_ref.document(f"user{i}")
    batch.set(doc_ref, {
        "name": f"User {i}",
        "created_at": firestore.SERVER_TIMESTAMP
    })

# Update in batch
batch.update(users_ref.document("user1"), {"status": "active"})

# Delete in batch
batch.delete(users_ref.document("user2"))

# Commit batch
batch.commit()

# Batch get (read multiple documents)
doc_refs = [
    db.collection("users").document("user1"),
    db.collection("users").document("user2"),
    db.collection("users").document("user3")
]

docs = db.get_all(doc_refs)
for doc in docs:
    if doc.exists:
        print(f"{doc.id}: {doc.to_dict()}")
```

### Transactions

```python
from google.cloud.firestore_v1.base_transaction import BaseTransaction

@firestore.transactional
def transfer_money(transaction, from_account_ref, to_account_ref, amount):
    # Read documents in transaction
    from_account = from_account_ref.get(transaction=transaction)
    to_account = to_account_ref.get(transaction=transaction)
    
    if not from_account.exists or not to_account.exists:
        raise ValueError("Account not found")
    
    from_balance = from_account.get("balance")
    to_balance = to_account.get("balance")
    
    if from_balance < amount:
        raise ValueError("Insufficient funds")
    
    # Update documents in transaction
    transaction.update(from_account_ref, {
        "balance": from_balance - amount,
        "last_transaction": firestore.SERVER_TIMESTAMP
    })
    
    transaction.update(to_account_ref, {
        "balance": to_balance + amount,
        "last_transaction": firestore.SERVER_TIMESTAMP
    })

# Execute transaction
transaction = db.transaction()
from_ref = db.collection("accounts").document("account1")
to_ref = db.collection("accounts").document("account2")

try:
    transfer_money(transaction, from_ref, to_ref, 100)
    print("Transfer completed successfully")
except Exception as e:
    print(f"Transfer failed: {e}")

# Manual transaction control
transaction = db.transaction()

try:
    # Read phase
    doc_ref = db.collection("counters").document("global")
    doc = doc_ref.get(transaction=transaction)
    
    current_count = doc.get("count") if doc.exists else 0
    new_count = current_count + 1
    
    # Write phase
    if doc.exists:
        transaction.update(doc_ref, {"count": new_count})
    else:
        transaction.set(doc_ref, {"count": new_count})
    
    # Commit
    transaction.commit()
    print(f"Counter updated to: {new_count}")
    
except Exception as e:
    print(f"Transaction failed: {e}")
```

### Queries

```python
# Simple queries
users_ref = db.collection("users")

# Filter queries
active_users = users_ref.where("status", "==", "active").get()
for user in active_users:
    print(f"Active user: {user.to_dict()}")

# Multiple filters
young_developers = (users_ref
                   .where("age", "<", 30)
                   .where("role", "==", "developer")
                   .get())

# Array contains
python_users = users_ref.where("skills", "array_contains", "python").get()

# Array contains any
tech_users = users_ref.where("skills", "array_contains_any", ["python", "javascript"]).get()

# In operator
specific_users = users_ref.where("user_id", "in", ["user1", "user2", "user3"]).get()

# Range queries
users_ref.where("age", ">=", 18).where("age", "<=", 65).get()

# Order and limit
recent_users = (users_ref
               .order_by("created_at", direction=firestore.Query.DESCENDING)
               .limit(10)
               .get())

# Pagination
first_page = users_ref.order_by("name").limit(10).get()
last_doc = first_page[-1] if first_page else None

if last_doc:
    next_page = (users_ref
                .order_by("name")
                .start_after(last_doc)
                .limit(10)
                .get())

# Complex queries with FieldFilter (v1)
from google.cloud.firestore_v1 import FieldFilter

query = users_ref.where(
    filter=FieldFilter("age", ">", 18)
).where(
    filter=FieldFilter("status", "==", "active")
)

# Composite filters
from google.cloud.firestore_v1 import And, Or

complex_query = users_ref.where(
    filter=And(filters=[
        FieldFilter("age", ">", 18),
        Or(filters=[
            FieldFilter("role", "==", "admin"),
            FieldFilter("role", "==", "moderator")
        ])
    ])
)
```

### Real-time Listeners

```python
import threading

# Document listener
def on_document_snapshot(doc_snapshot, changes, read_time):
    for doc in doc_snapshot:
        print(f"Document data: {doc.to_dict()}")

doc_ref = db.collection("users").document("user123")
doc_watch = doc_ref.on_snapshot(on_document_snapshot)

# Collection listener
def on_collection_snapshot(col_snapshot, changes, read_time):
    print("Collection snapshot received:")
    for change in changes:
        if change.type.name == "ADDED":
            print(f"New document: {change.document.id}")
        elif change.type.name == "MODIFIED":
            print(f"Modified document: {change.document.id}")
        elif change.type.name == "REMOVED":
            print(f"Removed document: {change.document.id}")

# Listen to collection changes
collection_watch = users_ref.on_snapshot(on_collection_snapshot)

# Listen to query changes
query = users_ref.where("status", "==", "active")
query_watch = query.on_snapshot(on_collection_snapshot)

# Stop listening
# doc_watch.unsubscribe()
# collection_watch.unsubscribe()
# query_watch.unsubscribe()

# Listener with error handling
def on_snapshot_error(error):
    print(f"Listener error: {error}")

doc_watch = doc_ref.on_snapshot(
    on_document_snapshot,
    on_error=on_snapshot_error
)

# Keep listeners alive
try:
    # Keep the main thread alive
    threading.Event().wait()
except KeyboardInterrupt:
    print("Stopping listeners...")
    doc_watch.unsubscribe()
    collection_watch.unsubscribe()
```

### Collection Groups

```python
# Query across all collections with the same name
# Example: cities/LA/landmarks, cities/NY/landmarks, etc.

landmarks_query = db.collection_group("landmarks")

# Find all landmarks with specific criteria
famous_landmarks = (landmarks_query
                   .where("famous", "==", True)
                   .order_by("visitors")
                   .limit(10)
                   .get())

for landmark in famous_landmarks:
    print(f"Landmark: {landmark.to_dict()}")
    print(f"Path: {landmark.reference.path}")

# Complex collection group query
popular_landmarks = (landmarks_query
                    .where("visitors", ">", 1000000)
                    .where("rating", ">=", 4.5)
                    .order_by("rating", direction=firestore.Query.DESCENDING)
                    .get())
```

## Google Cloud BigQuery

### Client Setup

```python
from google.cloud import bigquery
from google.cloud.bigquery import LoadJobConfig, SchemaField
import pandas as pd

# Initialize client
client = bigquery.Client(project="my-project")

# Get dataset reference
dataset_id = "my_dataset"
dataset_ref = client.dataset(dataset_id)

# Create dataset
dataset = bigquery.Dataset(dataset_ref)
dataset.location = "US"
dataset.description = "My dataset description"

try:
    dataset = client.create_dataset(dataset, timeout=30)
    print(f"Created dataset {client.project}.{dataset.dataset_id}")
except Exception as e:
    print(f"Dataset creation failed: {e}")
```

### Table Operations

```python
# Create table
table_id = "my_table"
table_ref = dataset_ref.table(table_id)

schema = [
    SchemaField("id", "INTEGER", mode="REQUIRED"),
    SchemaField("name", "STRING", mode="REQUIRED"),
    SchemaField("email", "STRING", mode="NULLABLE"),
    SchemaField("created_at", "TIMESTAMP", mode="REQUIRED"),
    SchemaField("metadata", "JSON", mode="NULLABLE"),
]

table = bigquery.Table(table_ref, schema=schema)
table.description = "User data table"

# Set partitioning
table.time_partitioning = bigquery.TimePartitioning(
    type_=bigquery.TimePartitioningType.DAY,
    field="created_at",
    expiration_ms=7 * 24 * 60 * 60 * 1000  # 7 days
)

# Set clustering
table.clustering_fields = ["name", "email"]

table = client.create_table(table)
print(f"Created table {table.project}.{table.dataset_id}.{table.table_id}")

# Get table info
table = client.get_table(table_ref)
print(f"Table has {table.num_rows} rows and {len(table.schema)} columns")

# List tables
tables = list(client.list_tables(dataset))
for table in tables:
    print(f"Table: {table.table_id}")

# Update table schema (adding columns)
original_schema = table.schema
new_schema = original_schema[:]  # Copy existing schema
new_schema.append(SchemaField("new_column", "STRING"))

table.schema = new_schema
table = client.update_table(table, ["schema"])
```

### Data Loading

```python
# Load from local file
def load_data_from_file(file_path, table_id):
    job_config = LoadJobConfig(
        source_format=bigquery.SourceFormat.CSV,
        skip_leading_rows=1,
        autodetect=True,
        write_disposition=bigquery.WriteDisposition.WRITE_TRUNCATE
    )
    
    with open(file_path, "rb") as source_file:
        job = client.load_table_from_file(
            source_file, 
            table_ref, 
            job_config=job_config
        )
    
    job.result()  # Wait for job to complete
    
    table = client.get_table(table_ref)
    print(f"Loaded {table.num_rows} rows into {table_id}")

# Load from Cloud Storage
def load_from_gcs(uri, table_id):
    job_config = LoadJobConfig(
        source_format=bigquery.SourceFormat.PARQUET,
        write_disposition=bigquery.WriteDisposition.WRITE_APPEND
    )
    
    load_job = client.load_table_from_uri(
        uri, table_ref, job_config=job_config
    )
    
    load_job.result()
    print(f"Loaded data from {uri}")

# Load from DataFrame
def load_from_dataframe(df, table_id):
    job_config = LoadJobConfig(
        write_disposition=bigquery.WriteDisposition.WRITE_APPEND,
        schema_update_options=[
            bigquery.SchemaUpdateOption.ALLOW_FIELD_ADDITION
        ]
    )
    
    job = client.load_table_from_dataframe(
        df, table_ref, job_config=job_config
    )
    
    job.result()
    print(f"Loaded {len(df)} rows")

# Streaming inserts
rows_to_insert = [
    {"id": 1, "name": "John", "email": "<EMAIL>"},
    {"id": 2, "name": "Jane", "email": "<EMAIL>"},
]

errors = client.insert_rows_json(table, rows_to_insert)
if not errors:
    print("New rows have been added.")
else:
    print(f"Encountered errors: {errors}")

# Batch streaming with auto-retry
def stream_data_batch(rows):
    table = client.get_table(table_ref)
    
    # Insert in batches
    batch_size = 1000
    for i in range(0, len(rows), batch_size):
        batch = rows[i:i + batch_size]
        errors = client.insert_rows_json(table, batch)
        
        if errors:
            print(f"Errors in batch {i//batch_size + 1}: {errors}")
        else:
            print(f"Successfully inserted batch {i//batch_size + 1}")
```

### Querying Data

```python
# Simple query
def run_query(sql):
    query_job = client.query(sql)
    results = query_job.result()
    
    for row in results:
        print(dict(row))

# Query with parameters
sql = """
    SELECT name, email, created_at
    FROM `my-project.my_dataset.my_table`
    WHERE created_at >= @start_date
    AND name LIKE @name_pattern
    ORDER BY created_at DESC
    LIMIT @limit
"""

job_config = bigquery.QueryJobConfig(
    query_parameters=[
        bigquery.ScalarQueryParameter("start_date", "DATE", "2024-01-01"),
        bigquery.ScalarQueryParameter("name_pattern", "STRING", "John%"),
        bigquery.ScalarQueryParameter("limit", "INT64", 100),
    ]
)

query_job = client.query(sql, job_config=job_config)
results = query_job.result()

# Convert to DataFrame
df = results.to_dataframe()
print(df.head())

# Query with job configuration
job_config = bigquery.QueryJobConfig(
    destination=table_ref,
    write_disposition=bigquery.WriteDisposition.WRITE_TRUNCATE,
    use_query_cache=True,
    maximum_bytes_billed=1000000  # 1MB limit
)

query_job = client.query(sql, job_config=job_config)
query_job.result()

# Dry run query (estimate costs)
def estimate_query_cost(sql):
    job_config = bigquery.QueryJobConfig(dry_run=True)
    query_job = client.query(sql, job_config=job_config)
    
    bytes_processed = query_job.total_bytes_processed
    print(f"Query will process {bytes_processed} bytes")
    
    # Estimate cost (pricing varies by region)
    estimated_cost = (bytes_processed / 1024**4) * 5  # $5 per TB
    print(f"Estimated cost: ${estimated_cost:.4f}")

# Query with retry and timeout
from google.api_core import retry
from google.api_core.exceptions import GoogleAPIError

@retry.Retry(
    predicate=retry.if_exception_type(GoogleAPIError),
    deadline=300.0  # 5 minutes
)
def run_query_with_retry(sql):
    job_config = bigquery.QueryJobConfig(
        job_timeout_ms=120000,  # 2 minutes
        use_legacy_sql=False
    )
    
    query_job = client.query(sql, job_config=job_config)
    return query_job.result()
```

### Data Export

```python
# Export to Cloud Storage
def export_table_to_gcs(table_id, gcs_uri):
    table_ref = dataset_ref.table(table_id)
    
    job_config = bigquery.ExtractJobConfig(
        destination_format=bigquery.DestinationFormat.PARQUET,
        compression=bigquery.Compression.SNAPPY
    )
    
    extract_job = client.extract_table(
        table_ref, gcs_uri, job_config=job_config
    )
    
    extract_job.result()
    print(f"Exported table to {gcs_uri}")

# Export query results
def export_query_to_gcs(sql, gcs_uri):
    job_config = bigquery.QueryJobConfig(
        destination=table_ref,
        write_disposition=bigquery.WriteDisposition.WRITE_TRUNCATE
    )
    
    # First, save query results to table
    query_job = client.query(sql, job_config=job_config)
    query_job.result()
    
    # Then export table
    export_table_to_gcs(table_ref.table_id, gcs_uri)

# Export to local file
def export_to_local(sql, file_path):
    query_job = client.query(sql)
    df = query_job.result().to_dataframe()
    
    if file_path.endswith('.csv'):
        df.to_csv(file_path, index=False)
    elif file_path.endswith('.parquet'):
        df.to_parquet(file_path, index=False)
    elif file_path.endswith('.json'):
        df.to_json(file_path, orient='records', lines=True)
    
    print(f"Exported {len(df)} rows to {file_path}")
```

## Common Patterns

### Environment Configuration

```python
import os
from google.cloud import storage, pubsub_v1, firestore, bigquery

class CloudConfig:
    def __init__(self):
        self.project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
        self.credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        self.environment = os.getenv('ENVIRONMENT', 'development')
        
        # Service-specific configs
        self.storage_bucket = os.getenv('STORAGE_BUCKET')
        self.pubsub_topic = os.getenv('PUBSUB_TOPIC')
        self.firestore_database = os.getenv('FIRESTORE_DATABASE', '(default)')
        self.bigquery_dataset = os.getenv('BIGQUERY_DATASET')
    
    def get_storage_client(self):
        return storage.Client(project=self.project_id)
    
    def get_pubsub_publisher(self):
        return pubsub_v1.PublisherClient()
    
    def get_pubsub_subscriber(self):
        return pubsub_v1.SubscriberClient()
    
    def get_firestore_client(self):
        return firestore.Client(
            project=self.project_id,
            database=self.firestore_database
        )
    
    def get_bigquery_client(self):
        return bigquery.Client(project=self.project_id)

# Usage
config = CloudConfig()
storage_client = config.get_storage_client()
```

### Connection Pooling and Resource Management

```python
import threading
from contextlib import contextmanager
from google.cloud import storage

class ConnectionPool:
    def __init__(self, max_connections=10):
        self._pool = []
        self._lock = threading.Lock()
        self._max_connections = max_connections
        
    @contextmanager
    def get_client(self):
        with self._lock:
            if self._pool:
                client = self._pool.pop()
            else:
                client = storage.Client()
        
        try:
            yield client
        finally:
            with self._lock:
                if len(self._pool) < self._max_connections:
                    self._pool.append(client)

# Singleton pattern for clients
class GoogleCloudClients:
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.storage_client = storage.Client()
            self.pubsub_publisher = pubsub_v1.PublisherClient()
            self.pubsub_subscriber = pubsub_v1.SubscriberClient()
            self.firestore_client = firestore.Client()
            self.bigquery_client = bigquery.Client()
            self._initialized = True

# Usage
clients = GoogleCloudClients()
bucket = clients.storage_client.bucket("my-bucket")
```

### Structured Logging

```python
import logging
import json
from google.cloud import logging as cloud_logging

class GoogleCloudLogger:
    def __init__(self, project_id, service_name):
        # Initialize Cloud Logging
        logging_client = cloud_logging.Client(project=project_id)
        logging_client.setup_logging()
        
        # Create structured logger
        self.logger = logging.getLogger(service_name)
        self.logger.setLevel(logging.INFO)
        
        # Add custom handler for structured logging
        handler = logging.StreamHandler()
        formatter = self.StructuredFormatter(service_name)
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    class StructuredFormatter(logging.Formatter):
        def __init__(self, service_name):
            self.service_name = service_name
            super().__init__()
        
        def format(self, record):
            log_obj = {
                'timestamp': self.formatTime(record),
                'severity': record.levelname,
                'service': self.service_name,
                'message': record.getMessage(),
                'module': record.module,
                'function': record.funcName,
                'line': record.lineno
            }
            
            # Add custom fields if present
            if hasattr(record, 'trace_id'):
                log_obj['trace_id'] = record.trace_id
            if hasattr(record, 'user_id'):
                log_obj['user_id'] = record.user_id
            
            return json.dumps(log_obj)
    
    def info(self, message, **kwargs):
        extra = {k: v for k, v in kwargs.items()}
        self.logger.info(message, extra=extra)
    
    def error(self, message, error=None, **kwargs):
        extra = {k: v for k, v in kwargs.items()}
        if error:
            extra['error'] = str(error)
            extra['error_type'] = type(error).__name__
        self.logger.error(message, extra=extra)

# Usage
logger = GoogleCloudLogger("my-project", "my-service")
logger.info("Processing request", user_id="12345", trace_id="abc123")
```

## Async Operations

### Async Storage Operations

```python
import asyncio
import aiofiles
from google.cloud import storage
from concurrent.futures import ThreadPoolExecutor

class AsyncStorageClient:
    def __init__(self, max_workers=10):
        self.client = storage.Client()
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
    
    async def upload_file_async(self, bucket_name, blob_name, file_path):
        def _upload():
            bucket = self.client.bucket(bucket_name)
            blob = bucket.blob(blob_name)
            blob.upload_from_filename(file_path)
            return blob
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, _upload)
    
    async def download_file_async(self, bucket_name, blob_name, file_path):
        def _download():
            bucket = self.client.bucket(bucket_name)
            blob = bucket.blob(blob_name)
            blob.download_to_filename(file_path)
            return file_path
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, _download)
    
    async def list_blobs_async(self, bucket_name, prefix=None):
        def _list():
            bucket = self.client.bucket(bucket_name)
            return list(bucket.list_blobs(prefix=prefix))
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, _list)
    
    async def batch_upload_async(self, upload_tasks):
        """Upload multiple files concurrently"""
        tasks = []
        for bucket_name, blob_name, file_path in upload_tasks:
            task = self.upload_file_async(bucket_name, blob_name, file_path)
            tasks.append(task)
        
        return await asyncio.gather(*tasks, return_exceptions=True)

# Usage
async def main():
    async_client = AsyncStorageClient()
    
    # Single upload
    await async_client.upload_file_async(
        "my-bucket", 
        "async-file.txt", 
        "local-file.txt"
    )
    
    # Batch upload
    upload_tasks = [
        ("my-bucket", "file1.txt", "local1.txt"),
        ("my-bucket", "file2.txt", "local2.txt"),
        ("my-bucket", "file3.txt", "local3.txt"),
    ]
    
    results = await async_client.batch_upload_async(upload_tasks)
    print(f"Upload results: {results}")

asyncio.run(main())
```

### Async Pub/Sub with AsyncIO Integration

```python
import asyncio
import json
from google.cloud import pubsub_v1
from concurrent.futures import ThreadPoolExecutor

class AsyncPubSubClient:
    def __init__(self, project_id, max_workers=10):
        self.project_id = project_id
        self.publisher = pubsub_v1.PublisherClient()
        self.subscriber = pubsub_v1.SubscriberClient()
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
    
    async def publish_async(self, topic_name, message_data, **attributes):
        def _publish():
            topic_path = self.publisher.topic_path(self.project_id, topic_name)
            future = self.publisher.publish(
                topic_path,
                message_data.encode('utf-8'),
                **attributes
            )
            return future.result()
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, _publish)
    
    async def publish_batch_async(self, topic_name, messages):
        tasks = []
        for message in messages:
            if isinstance(message, dict):
                data = json.dumps(message)
                attributes = {}
            else:
                data = str(message)
                attributes = {}
            
            task = self.publish_async(topic_name, data, **attributes)
            tasks.append(task)
        
        return await asyncio.gather(*tasks, return_exceptions=True)
    
    async def pull_messages_async(self, subscription_name, max_messages=10):
        def _pull():
            subscription_path = self.subscriber.subscription_path(
                self.project_id, subscription_name
            )
            response = self.subscriber.pull(
                request={
                    "subscription": subscription_path,
                    "max_messages": max_messages
                },
                timeout=30.0
            )
            return response.received_messages
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, _pull)
    
    async def process_messages_async(self, subscription_name, message_handler):
        """Process messages asynchronously with custom handler"""
        messages = await self.pull_messages_async(subscription_name)
        
        async def process_single_message(received_message):
            try:
                # Decode message
                message_data = received_message.message.data.decode('utf-8')
                attributes = dict(received_message.message.attributes)
                
                # Process with custom handler
                await message_handler(message_data, attributes)
                
                # Acknowledge message
                ack_ids = [received_message.ack_id]
                subscription_path = self.subscriber.subscription_path(
                    self.project_id, subscription_name
                )
                
                def _ack():
                    self.subscriber.acknowledge(
                        request={
                            "subscription": subscription_path,
                            "ack_ids": ack_ids
                        }
                    )
                
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(self.executor, _ack)
                
                return {"status": "success", "message_id": received_message.message.message_id}
                
            except Exception as e:
                return {"status": "error", "error": str(e)}
        
        # Process all messages concurrently
        tasks = [process_single_message(msg) for msg in messages]
        return await asyncio.gather(*tasks, return_exceptions=True)

# Usage
async def message_handler(data, attributes):
    """Custom async message handler"""
    print(f"Processing message: {data}")
    print(f"Attributes: {attributes}")
    
    # Simulate async processing
    await asyncio.sleep(1)
    
    # Process the message
    message = json.loads(data)
    print(f"Processed: {message}")

async def main():
    client = AsyncPubSubClient("my-project")
    
    # Publish messages
    messages = [
        {"user_id": "123", "action": "login"},
        {"user_id": "456", "action": "logout"},
        {"user_id": "789", "action": "purchase"}
    ]
    
    results = await client.publish_batch_async("my-topic", messages)
    print(f"Published {len(results)} messages")
    
    # Process messages
    processing_results = await client.process_messages_async(
        "my-subscription", 
        message_handler
    )
    print(f"Processing results: {processing_results}")

asyncio.run(main())
```

## Error Handling and Retries

### Comprehensive Error Handling

```python
from google.api_core import exceptions, retry
from google.cloud import storage, pubsub_v1
import logging
import time
from typing import Optional, Callable

class CloudErrorHandler:
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
    
    def handle_storage_errors(self, operation_name: str):
        """Decorator for handling Google Cloud Storage errors"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except exceptions.NotFound as e:
                    self.logger.error(f"{operation_name} failed - Resource not found: {e}")
                    raise
                except exceptions.Forbidden as e:
                    self.logger.error(f"{operation_name} failed - Permission denied: {e}")
                    raise
                except exceptions.TooManyRequests as e:
                    self.logger.warning(f"{operation_name} failed - Rate limited: {e}")
                    raise
                except exceptions.InternalServerError as e:
                    self.logger.error(f"{operation_name} failed - Server error: {e}")
                    raise
                except exceptions.BadGateway as e:
                    self.logger.error(f"{operation_name} failed - Bad gateway: {e}")
                    raise
                except exceptions.ServiceUnavailable as e:
                    self.logger.error(f"{operation_name} failed - Service unavailable: {e}")
                    raise
                except Exception as e:
                    self.logger.error(f"{operation_name} failed - Unexpected error: {e}")
                    raise
            return wrapper
        return decorator
    
    def handle_pubsub_errors(self, operation_name: str):
        """Decorator for handling Pub/Sub errors"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except exceptions.DeadlineExceeded as e:
                    self.logger.error(f"{operation_name} failed - Timeout: {e}")
                    raise
                except exceptions.ResourceExhausted as e:
                    self.logger.error(f"{operation_name} failed - Quota exceeded: {e}")
                    raise
                except exceptions.InvalidArgument as e:
                    self.logger.error(f"{operation_name} failed - Invalid argument: {e}")
                    raise
                except Exception as e:
                    self.logger.error(f"{operation_name} failed - Unexpected error: {e}")
                    raise
            return wrapper
        return decorator

# Custom retry configurations
def create_retry_policy(
    max_retries: int = 3,
    initial_delay: float = 1.0,
    max_delay: float = 60.0,
    multiplier: float = 2.0
):
    """Create custom retry policy"""
    
    def is_retryable(exception):
        return isinstance(exception, (
            exceptions.TooManyRequests,
            exceptions.InternalServerError,
            exceptions.BadGateway,
            exceptions.ServiceUnavailable,
            exceptions.DeadlineExceeded
        ))
    
    return retry.Retry(
        predicate=is_retryable,
        initial=initial_delay,
        maximum=max_delay,
        multiplier=multiplier,
        deadline=max_delay * max_retries
    )

# Resilient client wrapper
class ResilientStorageClient:
    def __init__(self, max_retries: int = 3):
        self.client = storage.Client()
        self.retry_policy = create_retry_policy(max_retries)
        self.error_handler = CloudErrorHandler()
    
    @CloudErrorHandler().handle_storage_errors("upload_blob")
    def upload_blob_with_retry(self, bucket_name: str, blob_name: str, 
                             source_file_name: str):
        """Upload blob with automatic retry"""
        @self.retry_policy
        def _upload():
            bucket = self.client.bucket(bucket_name)
            blob = bucket.blob(blob_name)
            blob.upload_from_filename(source_file_name)
            return blob
        
        return _upload()
    
    @CloudErrorHandler().handle_storage_errors("download_blob")
    def download_blob_with_retry(self, bucket_name: str, blob_name: str, 
                                destination_file_name: str):
        """Download blob with automatic retry"""
        @self.retry_policy
        def _download():
            bucket = self.client.bucket(bucket_name)
            blob = bucket.blob(blob_name)
            blob.download_to_filename(destination_file_name)
            return destination_file_name
        
        return _download()
    
    def upload_with_circuit_breaker(self, bucket_name: str, blob_name: str, 
                                   source_file_name: str):
        """Upload with circuit breaker pattern"""
        max_failures = 5
        failure_timeout = 60  # seconds
        
        if not hasattr(self, '_failure_count'):
            self._failure_count = 0
            self._last_failure_time = 0
        
        # Check circuit breaker
        if self._failure_count >= max_failures:
            if time.time() - self._last_failure_time < failure_timeout:
                raise Exception("Circuit breaker is open")
            else:
                # Reset circuit breaker
                self._failure_count = 0
        
        try:
            result = self.upload_blob_with_retry(bucket_name, blob_name, source_file_name)
            self._failure_count = 0  # Reset on success
            return result
        except Exception as e:
            self._failure_count += 1
            self._last_failure_time = time.time()
            raise

# Usage
resilient_client = ResilientStorageClient(max_retries=5)

try:
    resilient_client.upload_blob_with_retry(
        "my-bucket", 
        "important-file.txt", 
        "local-file.txt"
    )
    print("Upload successful")
except Exception as e:
    print(f"Upload failed after retries: {e}")
```

### Bulk Operations with Error Recovery

```python
import asyncio
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any
import json

class BulkOperationManager:
    def __init__(self, max_workers: int = 10):
        self.max_workers = max_workers
        self.storage_client = storage.Client()
        self.logger = logging.getLogger(__name__)
    
    def bulk_upload_with_recovery(self, upload_tasks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Bulk upload with automatic retry and error recovery"""
        
        def upload_single_file(task):
            try:
                bucket_name = task['bucket_name']
                blob_name = task['blob_name']
                file_path = task['file_path']
                
                bucket = self.storage_client.bucket(bucket_name)
                blob = bucket.blob(blob_name)
                
                # Add retry logic
                @create_retry_policy()
                def _upload():
                    blob.upload_from_filename(file_path)
                    return {
                        'status': 'success',
                        'bucket': bucket_name,
                        'blob': blob_name,
                        'file_path': file_path
                    }
                
                return _upload()
                
            except Exception as e:
                return {
                    'status': 'error',
                    'bucket': task.get('bucket_name', 'unknown'),
                    'blob': task.get('blob_name', 'unknown'),
                    'file_path': task.get('file_path', 'unknown'),
                    'error': str(e),
                    'error_type': type(e).__name__
                }
        
        results = {
            'successful': [],
            'failed': [],
            'summary': {
                'total': len(upload_tasks),
                'successful': 0,
                'failed': 0
            }
        }
        
        # Execute uploads with thread pool
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_task = {
                executor.submit(upload_single_file, task): task 
                for task in upload_tasks
            }
            
            # Process completed tasks
            for future in as_completed(future_to_task):
                result = future.result()
                
                if result['status'] == 'success':
                    results['successful'].append(result)
                    results['summary']['successful'] += 1
                    self.logger.info(f"Successfully uploaded {result['blob']}")
                else:
                    results['failed'].append(result)
                    results['summary']['failed'] += 1
                    self.logger.error(f"Failed to upload {result['blob']}: {result['error']}")
        
        # Retry failed uploads
        if results['failed']:
            self.logger.info(f"Retrying {len(results['failed'])} failed uploads...")
            retry_results = self._retry_failed_uploads(results['failed'])
            
            # Update results
            for retry_result in retry_results:
                if retry_result['status'] == 'success':
                    results['successful'].append(retry_result)
                    results['summary']['successful'] += 1
                    results['summary']['failed'] -= 1
                    
                    # Remove from failed list
                    results['failed'] = [
                        f for f in results['failed'] 
                        if f['blob'] != retry_result['blob']
                    ]
        
        return results
    
    def _retry_failed_uploads(self, failed_tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Retry failed uploads with exponential backoff"""
        retry_results = []
        
        for task in failed_tasks:
            for attempt in range(3):  # Maximum 3 retry attempts
                try:
                    time.sleep(2 ** attempt)  # Exponential backoff
                    
                    bucket = self.storage_client.bucket(task['bucket'])
                    blob = bucket.blob(task['blob'])
                    blob.upload_from_filename(task['file_path'])
                    
                    retry_results.append({
                        'status': 'success',
                        'bucket': task['bucket'],
                        'blob': task['blob'],
                        'file_path': task['file_path'],
                        'retry_attempt': attempt + 1
                    })
                    break
                    
                except Exception as e:
                    if attempt == 2:  # Last attempt
                        retry_results.append({
                            'status': 'error',
                            'bucket': task['bucket'],
                            'blob': task['blob'],
                            'file_path': task['file_path'],
                            'error': str(e),
                            'retry_attempts': 3
                        })
        
        return retry_results

# Usage
bulk_manager = BulkOperationManager(max_workers=20)

upload_tasks = [
    {
        'bucket_name': 'my-bucket',
        'blob_name': f'file_{i}.txt',
        'file_path': f'local_file_{i}.txt'
    }
    for i in range(100)
]

results = bulk_manager.bulk_upload_with_recovery(upload_tasks)

print(f"Upload Summary:")
print(f"Total: {results['summary']['total']}")
print(f"Successful: {results['summary']['successful']}")
print(f"Failed: {results['summary']['failed']}")

# Save failed uploads for manual review
if results['failed']:
    with open('failed_uploads.json', 'w') as f:
        json.dump(results['failed'], f, indent=2)
```

## Monitoring and Observability

### OpenTelemetry Integration

```python
from opentelemetry import trace, metrics
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.exporter.cloud_trace import CloudTraceSpanExporter
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.exporter.cloud_monitoring import CloudMonitoringMetricsExporter
from opentelemetry.instrumentation.requests import RequestsInstrumentor
import time

# Setup tracing
tracer_provider = TracerProvider()
tracer_provider.add_span_processor(
    BatchSpanProcessor(CloudTraceSpanExporter())
)
trace.set_tracer_provider(tracer_provider)

# Setup metrics
metrics.set_meter_provider(MeterProvider())
meter = metrics.get_meter(__name__)

# Create custom metrics
upload_counter = meter.create_counter(
    name="gcs_uploads_total",
    description="Total number of GCS uploads",
    unit="1"
)

upload_duration = meter.create_histogram(
    name="gcs_upload_duration_seconds",
    description="Duration of GCS uploads",
    unit="s"
)

# Instrument requests
RequestsInstrumentor().instrument()

class TracedStorageClient:
    def __init__(self):
        self.client = storage.Client()
        self.tracer = trace.get_tracer(__name__)
    
    def upload_blob_traced(self, bucket_name: str, blob_name: str, 
                          source_file_name: str):
        with self.tracer.start_as_current_span("gcs_upload") as span:
            # Add span attributes
            span.set_attribute("gcs.bucket_name", bucket_name)
            span.set_attribute("gcs.blob_name", blob_name)
            span.set_attribute("gcs.operation", "upload")
            
            start_time = time.time()
            
            try:
                bucket = self.client.bucket(bucket_name)
                blob = bucket.blob(blob_name)
                blob.upload_from_filename(source_file_name)
                
                # Record success
                span.set_attribute("gcs.status", "success")
                upload_counter.add(1, {"status": "success", "bucket": bucket_name})
                
            except Exception as e:
                # Record error
                span.set_attribute("gcs.status", "error")
                span.set_attribute("gcs.error", str(e))
                upload_counter.add(1, {"status": "error", "bucket": bucket_name})
                raise
            
            finally:
                # Record duration
                duration = time.time() - start_time
                upload_duration.record(duration, {"bucket": bucket_name})
                span.set_attribute("gcs.duration_seconds", duration)

# Enable tracing for existing storage client
import os
os.environ['ENABLE_GCS_PYTHON_CLIENT_OTEL_TRACES'] = 'True'

# Usage
traced_client = TracedStorageClient()
traced_client.upload_blob_traced("my-bucket", "traced-file.txt", "local-file.txt")
```

### Health Checks and Monitoring

```python
import time
import threading
from typing import Dict, Any
from google.cloud import storage, pubsub_v1, firestore

class HealthChecker:
    def __init__(self, project_id: str):
        self.project_id = project_id
        self.storage_client = storage.Client()
        self.publisher = pubsub_v1.PublisherClient()
        self.firestore_client = firestore.Client()
        
        self.health_status = {
            'storage': {'status': 'unknown', 'last_check': None, 'latency': None},
            'pubsub': {'status': 'unknown', 'last_check': None, 'latency': None},
            'firestore': {'status': 'unknown', 'last_check': None, 'latency': None},
        }
        
        self.check_interval = 60  # seconds
        self.running = False
    
    def check_storage_health(self) -> Dict[str, Any]:
        """Check Google Cloud Storage health"""
        try:
            start_time = time.time()
            
            # List buckets (lightweight operation)
            list(self.storage_client.list_buckets())
            
            latency = time.time() - start_time
            
            return {
                'status': 'healthy',
                'latency': latency,
                'last_check': time.time()
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'last_check': time.time()
            }
    
    def check_pubsub_health(self) -> Dict[str, Any]:
        """Check Pub/Sub health"""
        try:
            start_time = time.time()
            
            # List topics (lightweight operation)
            list(self.publisher.list_topics(
                request={"project": f"projects/{self.project_id}"}
            ))
            
            latency = time.time() - start_time
            
            return {
                'status': 'healthy',
                'latency': latency,
                'last_check': time.time()
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'last_check': time.time()
            }
    
    def check_firestore_health(self) -> Dict[str, Any]:
        """Check Firestore health"""
        try:
            start_time = time.time()
            
            # Simple read operation
            collections = list(self.firestore_client.collections())
            
            latency = time.time() - start_time
            
            return {
                'status': 'healthy',
                'latency': latency,
                'last_check': time.time()
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'last_check': time.time()
            }
    
    def perform_health_checks(self):
        """Perform all health checks"""
        self.health_status['storage'] = self.check_storage_health()
        self.health_status['pubsub'] = self.check_pubsub_health()
        self.health_status['firestore'] = self.check_firestore_health()
    
    def start_monitoring(self):
        """Start continuous health monitoring"""
        self.running = True
        
        def monitor():
            while self.running:
                self.perform_health_checks()
                time.sleep(self.check_interval)
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def stop_monitoring(self):
        """Stop health monitoring"""
        self.running = False
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get current health status"""
        overall_status = 'healthy'
        
        for service, status in self.health_status.items():
            if status.get('status') == 'unhealthy':
                overall_status = 'unhealthy'
                break
            elif status.get('status') == 'unknown':
                overall_status = 'unknown'
        
        return {
            'overall_status': overall_status,
            'services': self.health_status,
            'timestamp': time.time()
        }

# Usage with Flask health endpoint
from flask import Flask, jsonify

app = Flask(__name__)
health_checker = HealthChecker("my-project")
health_checker.start_monitoring()

@app.route('/health')
def health():
    status = health_checker.get_health_status()
    http_status = 200 if status['overall_status'] == 'healthy' else 503
    return jsonify(status), http_status

@app.route('/health/detailed')
def health_detailed():
    # Perform fresh health checks
    health_checker.perform_health_checks()
    return jsonify(health_checker.get_health_status())

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080)
```

## Production Best Practices

### Configuration Management

```python
import os
from dataclasses import dataclass
from typing import Optional

@dataclass
class GoogleCloudConfig:
    """Production configuration for Google Cloud services"""
    project_id: str
    region: str = "us-central1"
    
    # Storage configuration
    storage_bucket: Optional[str] = None
    storage_location: str = "US"
    storage_class: str = "STANDARD"
    
    # Pub/Sub configuration
    pubsub_topic: Optional[str] = None
    pubsub_subscription: Optional[str] = None
    pubsub_ack_deadline: int = 60
    pubsub_message_retention: int = 604800  # 7 days
    
    # Firestore configuration
    firestore_database: str = "(default)"
    
    # BigQuery configuration
    bigquery_dataset: Optional[str] = None
    bigquery_location: str = "US"
    
    # Performance tuning
    max_workers: int = 10
    connection_pool_size: int = 10
    request_timeout: int = 300
    
    # Retry configuration
    max_retries: int = 3
    initial_retry_delay: float = 1.0
    max_retry_delay: float = 60.0
    
    @classmethod
    def from_environment(cls) -> 'GoogleCloudConfig':
        """Load configuration from environment variables"""
        return cls(
            project_id=os.getenv('GOOGLE_CLOUD_PROJECT', ''),
            region=os.getenv('GOOGLE_CLOUD_REGION', 'us-central1'),
            storage_bucket=os.getenv('STORAGE_BUCKET'),
            pubsub_topic=os.getenv('PUBSUB_TOPIC'),
            pubsub_subscription=os.getenv('PUBSUB_SUBSCRIPTION'),
            firestore_database=os.getenv('FIRESTORE_DATABASE', '(default)'),
            bigquery_dataset=os.getenv('BIGQUERY_DATASET'),
            max_workers=int(os.getenv('MAX_WORKERS', '10')),
            max_retries=int(os.getenv('MAX_RETRIES', '3')),
        )
    
    def validate(self):
        """Validate configuration"""
        if not self.project_id:
            raise ValueError("GOOGLE_CLOUD_PROJECT must be set")
        
        if self.max_workers <= 0:
            raise ValueError("max_workers must be positive")
        
        if self.max_retries < 0:
            raise ValueError("max_retries cannot be negative")

# Production client factory
class GoogleCloudClientFactory:
    def __init__(self, config: GoogleCloudConfig):
        self.config = config
        config.validate()
        
        # Initialize clients lazily
        self._storage_client = None
        self._pubsub_publisher = None
        self._pubsub_subscriber = None
        self._firestore_client = None
        self._bigquery_client = None
    
    @property
    def storage_client(self) -> storage.Client:
        if self._storage_client is None:
            self._storage_client = storage.Client(project=self.config.project_id)
        return self._storage_client
    
    @property
    def pubsub_publisher(self) -> pubsub_v1.PublisherClient:
        if self._pubsub_publisher is None:
            publisher_options = pubsub_v1.types.PublisherOptions(
                flow_control=pubsub_v1.types.PublishFlowControl(
                    message_limit=self.config.max_workers * 100,
                    byte_limit=1024 * 1024 * 10  # 10MB
                )
            )
            self._pubsub_publisher = pubsub_v1.PublisherClient(
                publisher_options=publisher_options
            )
        return self._pubsub_publisher
    
    @property
    def pubsub_subscriber(self) -> pubsub_v1.SubscriberClient:
        if self._pubsub_subscriber is None:
            subscriber_options = pubsub_v1.types.SubscriberOptions(
                flow_control=pubsub_v1.types.FlowControl(
                    max_messages=self.config.max_workers * 10
                )
            )
            self._pubsub_subscriber = pubsub_v1.SubscriberClient(
                subscriber_options=subscriber_options
            )
        return self._pubsub_subscriber
    
    @property
    def firestore_client(self) -> firestore.Client:
        if self._firestore_client is None:
            self._firestore_client = firestore.Client(
                project=self.config.project_id,
                database=self.config.firestore_database
            )
        return self._firestore_client
    
    @property
    def bigquery_client(self) -> bigquery.Client:
        if self._bigquery_client is None:
            self._bigquery_client = bigquery.Client(project=self.config.project_id)
        return self._bigquery_client

# Usage
config = GoogleCloudConfig.from_environment()
clients = GoogleCloudClientFactory(config)

# Access clients
storage_client = clients.storage_client
publisher = clients.pubsub_publisher
```

### Production Service Example

```python
import logging
import signal
import sys
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel
import uvicorn

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Google Cloud clients
config = GoogleCloudConfig.from_environment()
clients = GoogleCloudClientFactory(config)
health_checker = HealthChecker(config.project_id)

# Pydantic models
class FileUploadRequest(BaseModel):
    bucket_name: str
    blob_name: str
    content: str
    content_type: str = "text/plain"

class MessagePublishRequest(BaseModel):
    topic_name: str
    message: dict
    attributes: dict = {}

# FastAPI app with lifespan management
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Starting up service...")
    health_checker.start_monitoring()
    
    yield
    
    # Shutdown
    logger.info("Shutting down service...")
    health_checker.stop_monitoring()

app = FastAPI(
    title="Google Cloud Service",
    description="Production Google Cloud service example",
    version="1.0.0",
    lifespan=lifespan
)

# API endpoints
@app.get("/health")
async def health():
    """Health check endpoint"""
    status = health_checker.get_health_status()
    if status['overall_status'] != 'healthy':
        raise HTTPException(status_code=503, detail=status)
    return status

@app.post("/storage/upload")
async def upload_file(request: FileUploadRequest, background_tasks: BackgroundTasks):
    """Upload file to Cloud Storage"""
    try:
        bucket = clients.storage_client.bucket(request.bucket_name)
        blob = bucket.blob(request.blob_name)
        blob.upload_from_string(
            request.content,
            content_type=request.content_type
        )
        
        logger.info(f"Uploaded {request.blob_name} to {request.bucket_name}")
        
        return {
            "status": "success",
            "bucket": request.bucket_name,
            "blob": request.blob_name,
            "size": len(request.content)
        }
        
    except Exception as e:
        logger.error(f"Upload failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/pubsub/publish")
async def publish_message(request: MessagePublishRequest):
    """Publish message to Pub/Sub"""
    try:
        topic_path = clients.pubsub_publisher.topic_path(
            config.project_id, 
            request.topic_name
        )
        
        future = clients.pubsub_publisher.publish(
            topic_path,
            json.dumps(request.message).encode('utf-8'),
            **request.attributes
        )
        
        message_id = future.result(timeout=30)
        
        logger.info(f"Published message {message_id} to {request.topic_name}")
        
        return {
            "status": "success",
            "message_id": message_id,
            "topic": request.topic_name
        }
        
    except Exception as e:
        logger.error(f"Publish failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Graceful shutdown
def signal_handler(signum, frame):
    logger.info(f"Received signal {signum}, shutting down...")
    sys.exit(0)

signal.signal(signal.SIGTERM, signal_handler)
signal.signal(signal.SIGINT, signal_handler)

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8080,
        workers=1,
        access_log=True,
        log_level="info"
    )
```

This comprehensive documentation covers the essential patterns and best practices for using Google Cloud Python client libraries in production environments, including authentication, error handling, monitoring, and scalable service architecture.