#!/bin/bash
# Setup SuperClaude structure for Episteme project

echo "Setting up SuperClaude structure for Episteme project..."

# Create required directories
echo "Creating SuperClaude directories..."
mkdir -p .claude/shared
mkdir -p .claude/commands/shared

# Option 1: Create symbolic links to global SuperClaude (recommended)
echo "Creating symbolic links to global SuperClaude configuration..."
ln -sfn ~/.claude/shared/*.yml .claude/shared/
ln -sfn ~/.claude/commands/shared/*.yml .claude/commands/shared/

# Option 2: Copy files (uncomment if you prefer copies instead of links)
# echo "Copying SuperClaude configuration files..."
# cp ~/.claude/shared/*.yml .claude/shared/
# cp ~/.claude/commands/shared/*.yml .claude/commands/shared/

# Create a SuperClaude-compatible CLAUDE.md alongside the existing one
cat > .claude/SUPERCLAUDE.md << 'EOF'
# CLAUDE.md - Episteme SuperClaude Configuration

This file provides SuperClaude configuration while maintaining the existing CLAUDE.md for deployment documentation.

## Core Configuration
@include shared/superclaude-core.yml#Core_Philosophy

## Thinking Modes
@include commands/shared/flag-inheritance.yml#Universal Flags (All Commands)

## Advanced Token Economy
@include shared/superclaude-core.yml#Advanced_Token_Economy

## Task Management
@include shared/superclaude-core.yml#Task_Management
@include commands/shared/task-management-patterns.yml#Task_Management_Hierarchy

## Performance Standards
@include shared/superclaude-core.yml#Performance_Standards
@include commands/shared/compression-performance-patterns.yml#Performance_Baselines

## Security Standards
@include shared/superclaude-rules.yml#Security_Standards
@include commands/shared/security-patterns.yml#OWASP_Top_10

## MCP Integration
@include shared/superclaude-mcp.yml#Server_Capabilities_Extended

## Personas
@include shared/superclaude-personas.yml#All_Personas

---
Note: The original CLAUDE.md contains the Analysis Engine Production Deployment Guide.
EOF

echo "SuperClaude structure setup complete!"
echo ""
echo "Created:"
echo "  - .claude/shared/ (linked to ~/.claude/shared/)"
echo "  - .claude/commands/shared/ (linked to ~/.claude/commands/shared/)"
echo "  - .claude/SUPERCLAUDE.md (SuperClaude-compatible configuration)"
echo ""
echo "Your original CLAUDE.md remains unchanged for deployment documentation."
echo "The project now supports both custom and SuperClaude configurations."