[{"label": "Analysis Engine (Rust)", "adapter": "CodeLLDB", "request": "launch", "build": {"command": "cargo", "args": ["build", "--bin=analysis-engine", "--package=analysis-engine"]}, "sourceLanguages": ["rust"], "cwd": "$ZED_WORKTREE_ROOT/services/analysis-engine", "env": {"RUST_LOG": "debug", "DATABASE_URL": "postgresql://ccl_dev:dev_password@localhost:5432/ccl_local", "REDIS_URL": "redis://localhost:6379"}, "args": []}]