# Phase 4: Pattern Detection MVP - Production-Ready Implementation (July 2025)

## Context
You are implementing the Pattern Detection service, a production-ready system that uses cutting-edge machine learning models to identify code patterns, anti-patterns, and security vulnerabilities at scale. This service is written in **Python** and leverages the latest transformer models (StarCoder2, GraphCodeBERT), Graph Neural Networks, BigQuery ML with Gemini 2.0 Flash integration, GPU-accelerated processing with NVIDIA H200/RAPIDS, and real-time streaming capabilities for enterprise-grade pattern recognition.

## Pre-Implementation Checklist

### Essential Files to Read First (In Order)
1. **Architecture & Planning**
   - `PLANNING.md` - Pattern Detection service overview
   - `PHASED-DEVELOPMENT-APPROACH.md` - Phase 4 Pattern Detection sprint
   - `PRPs/architecture-patterns.md` - ML pipeline patterns

2. **Service-Specific PRPs**
   - `PRPs/services/pattern-mining.md` - Complete service specification
   - `PRPs/ai-ml/pattern-recognition.md` - ML approach for patterns
   - `PRPs/database/bigquery-analytics.md` - BigQuery ML usage
   - `PRPs/implementation-guide.md` - Python coding standards

3. **Integration Contracts** (CRITICAL)
   - `contracts/README.md` - Service integration map
   - `contracts/schemas/ast-output-v1.json` - Input from Repository Analysis
   - `contracts/schemas/pattern-input-v1.json` - Expected input format
   - `contracts/schemas/pattern-output-v1.json` - Output format you MUST produce
   - `contracts/implementation/pattern-detection-guide.md` - Implementation specifics

4. **ML Infrastructure**
   - `ml/training-data/` - Training data strategy (IMPORTANT)
   - `ml/training-data/data-pipeline.py` - Data pipeline implementation
   - `ml/training-data/labeling/weak-supervision.py` - Labeling framework
   - `ml/training-data/quality/validation-pipeline.py` - Quality checks
   - `ml/training-data/storage/schema.sql` - BigQuery schema

5. **Existing Infrastructure**
   - `services/pattern-mining/` - Current scaffolding
   - `services/pattern-mining/requirements.txt` - Dependencies
   - `services/pattern-mining/Dockerfile.dev` - Development container
   - `.github/workflows/pattern-mining-ci.yml` - CI pipeline

## Technical Requirements

### Core Features to Implement

1. **Pattern Types to Detect (75+ Patterns Across 10 Categories)**
   ```python
   PATTERN_CATEGORIES = {
       "design_patterns": [
           "singleton", "factory", "observer", "strategy",
           "decorator", "adapter", "facade", "proxy",
           "repository", "unit_of_work", "specification",
           "cqrs", "event_sourcing", "saga_pattern"
       ],
       "anti_patterns": [
           "god_class", "spaghetti_code", "copy_paste",
           "dead_code", "long_method", "large_class",
           "circular_dependency", "anemic_domain_model",
           "primitive_obsession", "feature_envy", "data_clumps"
       ],
       "security_patterns": [
           "sql_injection", "xss_vulnerable", "hardcoded_secrets",
           "weak_crypto", "path_traversal", "unsafe_deserialization",
           "llm_prompt_injection", "api_key_exposure", "jwt_vulnerabilities",
           "supply_chain_attacks", "container_escape_vectors",
           "quantum_vulnerable_crypto", "homomorphic_encryption_misuse",
           "multimodal_injection", "federated_learning_poisoning"
       ],
       "performance_patterns": [
           "n_plus_one", "memory_leak", "inefficient_loop",
           "blocking_io", "unnecessary_allocation",
           "unoptimized_queries", "cache_misuse", "excessive_api_calls",
           "gpu_memory_overflow", "inefficient_tensor_ops",
           "suboptimal_batching", "redundant_computations"
       ],
       "code_smells": [
           "duplicate_code", "long_parameter_list", "feature_envy",
           "inappropriate_intimacy", "refused_bequest",
           "primitive_obsession", "data_clumps", "message_chains",
           "middle_man", "incomplete_library_class"
       ],
       "ml_specific_patterns": [
           "data_leakage", "improper_validation_split", "missing_seed",
           "unscaled_features", "class_imbalance_ignored",
           "overfitting_indicators", "underfitting_patterns",
           "gradient_explosion", "vanishing_gradients", "mode_collapse",
           "catastrophic_forgetting", "distribution_shift"
       ],
       "async_patterns": [
           "missing_await", "sync_in_async", "unclosed_resources",
           "race_conditions", "deadlock_potential",
           "event_loop_blocking", "improper_cancellation",
           "asyncio_footguns", "concurrent_mutation", "starvation"
       ],
       "cloud_native_patterns": [
           "stateful_microservice", "missing_circuit_breaker",
           "improper_retry_logic", "missing_rate_limiting",
           "inadequate_health_checks", "poor_observability",
           "missing_distributed_tracing", "improper_secret_management"
       ],
       "quantum_patterns": [
           "quantum_key_distribution", "post_quantum_crypto",
           "quantum_resistant_algorithms", "quantum_entanglement_patterns",
           "quantum_circuit_optimization", "quantum_error_correction"
       ],
       "sustainable_computing": [
           "inefficient_energy_usage", "missing_carbon_awareness",
           "redundant_computation", "inefficient_caching",
           "excessive_logging", "unoptimized_container_images",
           "wasteful_resource_allocation"
       ]
   }
   ```

2. **Production-Ready ML Pipeline Architecture**
   - **Multi-modal Feature Extraction**:
     - AST features using LibCST v1.8.2 (Python) and tree-sitter v0.24.0 (40+ languages)
     - Graph features using PyTorch Geometric with GPU acceleration
     - Semantic features using StarCoder2 (15B) and GraphCodeBERT embeddings
     - Code metrics with RAPIDS cuML for 150x speedup
   - **State-of-the-Art Detection Stack**:
     - Rule-based detection with ast-grep v0.26.0 (deterministic baseline)
     - StarCoder2 (15B parameters) with FlashAttention-3
     - GraphCodeBERT with 4096 token context window
     - Graphormer variants for structural pattern analysis
     - Gemini 2.0 Flash for complex reasoning (1M token context)
     - HDBSCAN with cuML GPU acceleration for pattern discovery
     - CodeQL AI integration for security patterns (>90% auto-fix rate)
   - **Advanced Ensemble Architecture**:
     - Dynamic weight adjustment based on pattern complexity
     - Confidence calibration with temperature scaling
     - A/B testing framework for model updates
   - **Specialized Integrations**:
     - MLScent for ML-specific anti-patterns
     - Semgrep AI for custom rule generation
     - CodeQL for vulnerability detection

3. **Enterprise BigQuery ML Integration (July 2025)**
   - **Gemini 2.0 Flash Integration**:
     - ML.GENERATE_TEXT with supervised tuning
     - Batch processing for cost optimization
     - Intelligent caching for similar patterns
   - **Real-time Processing**:
     - Continuous queries with 1-minute refresh intervals
     - Streaming pattern detection (<5s latency)
     - Event-driven architecture with Pub/Sub
   - **Advanced Analytics**:
     - ARIMA_PLUS_XREG for trend analysis
     - Vector search with TreeAH index (sub-50ms)
     - BI Engine 2.0 for sub-second dashboards
   - **Cost Optimization**:
     - Enterprise Plus edition with slot autoscaling
     - Partitioned tables with 90-day retention
     - Materialized views for common queries
     - Search indexes with LOG_ANALYZER
     - Iceberg tables for efficient updates
   - **Performance Features**:
     - Dask-BigQuery for distributed ML
     - GPU acceleration via Dataproc
     - Multi-region replication for global scale

4. **Enhanced REST API Endpoints (v2)**
   ```
   # Core Detection
   POST /api/v1/detect - Detect patterns in repository
   POST /api/v2/detect/stream - Stream pattern detection results
   POST /api/v2/detect/batch - Batch process multiple repositories
   GET  /api/v1/detect/{id} - Get detection status
   
   # Pattern Management
   GET  /api/v1/patterns - List detected patterns
   GET  /api/v1/patterns/{id} - Get pattern details
   GET  /api/v2/patterns/trending - Get trending patterns across repos
   GET  /api/v2/patterns/explain - Get AI-generated pattern explanations
   POST /api/v2/patterns/suggest-fix - Get AI-generated fixes
   
   # Model Management
   POST /api/v1/patterns/feedback - User feedback
   POST /api/v1/train - Trigger model retraining
   POST /api/v2/models/finetune - Fine-tune models on custom patterns
   GET  /api/v1/models - List active models
   GET  /api/v2/models/performance - Get model performance metrics
   
   # System
   GET  /api/v1/health - Health check
   GET  /api/v2/metrics - Get system performance metrics
   ```

5. **Advanced Feature Engineering**
   - **AST-based features**:
     - LibCST for lossless AST with formatting preservation
     - Tree-sitter for incremental parsing and multi-language support
     - Graph-based AST representations for GNN processing
   - **Code metrics**:
     - Traditional: LOC, cyclomatic/cognitive complexity
     - Advanced: Halstead metrics, maintainability index
     - ML-specific: Data flow complexity, tensor shapes
   - **Semantic features**:
     - Transformer embeddings (CodeT5+, GraphCodeBERT)
     - Contextual variable/function naming patterns
     - Documentation quality scores
   - **Structural features**:
     - Graph Neural Network embeddings
     - Control/data flow graph features
     - Architectural pattern indicators

### Integration Requirements

1. **Input Processing**
   - Subscribe to Pub/Sub topic: `repository-analysis-complete`
   - Process full AST output
   - Handle large repositories with streaming

2. **Output Publishing**
   - Publish to topic: `patterns-detected`
   - Store results in BigQuery
   - Cache in Redis for API queries
   - Send to Marketplace when ready

3. **Production Performance Requirements**
   - Process 100k LOC in <8 seconds (H200 GPU with FlashAttention-3)
   - Process 1M LOC in <90 seconds (distributed with Ray)
   - Maintain >97% precision, >91% recall
   - Real-time streaming detection with <2s latency
   - Handle 1000+ concurrent analyses with Ray scaling
   - Batch process 100+ repositories simultaneously
   - Sub-25ms API response time with BI Engine
   - 99.9% uptime with auto-scaling and failover

### Phase 3 Issues to Address
From `docs/phase4-prep-issues.md`:
- Extend AST parsing beyond Python (coordinate with Repository Analysis team)
- Implement pattern evolution tracking system
- Add cost tracking for BigQuery ML operations

## Implementation Steps

### Step 1: Enhanced Service Foundation
```python
# In services/pattern-mining/src/main.py
from fastapi import FastAPI
from fastapi.responses import StreamingResponse
from opentelemetry import trace
import structlog
import asyncio
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await initialize_ml_models()
    await setup_gpu_context()
    await connect_bigquery_with_gemini()
    yield
    # Shutdown
    await cleanup_resources()

app = FastAPI(
    title="Pattern Detection Service v2",
    lifespan=lifespan,
    docs_url="/api/docs"
)
logger = structlog.get_logger()

# 1. Set up FastAPI with async/streaming support
# 2. Configure OpenTelemetry with custom spans
# 3. Initialize BigQuery with Gemini 2.5 Pro
# 4. Set up GPU context for RAPIDS
# 5. Load transformer models (CodeT5+, GraphCodeBERT)
# 6. Initialize GNN models
# 7. Set up Pub/Sub with batch subscribers
```

### Step 2: Advanced Feature Extraction Pipeline
```python
# Create services/pattern-mining/src/features/extractor.py
import libcst as cst
from tree_sitter import Parser
import torch
from transformers import AutoModel, AutoTokenizer
import cupy as cp  # GPU-accelerated numpy

class AdvancedFeatureExtractor:
    def __init__(self):
        self.libcst_visitor = LibCSTFeatureVisitor()
        self.tree_sitter_parser = TreeSitterMultiLangParser()
        self.codet5_model = AutoModel.from_pretrained("Salesforce/codet5p-770m")
        self.graphcodebert = AutoModel.from_pretrained("microsoft/graphcodebert-base")
        self.gnn_encoder = GraphNeuralEncoder()
        
    async def extract_features(self, code: str, language: str) -> torch.Tensor:
        # Parallel feature extraction using asyncio.gather
        ast_features, graph_features, semantic_features = await asyncio.gather(
            self.extract_ast_features(code, language),
            self.extract_graph_features(code),
            self.extract_semantic_embeddings(code)
        )
        
        # GPU-accelerated feature combination
        combined = self.combine_features_gpu(
            ast_features, graph_features, semantic_features
        )
        return combined
        
    async def extract_ast_features(self, code: str, language: str):
        # LibCST for Python (preserves formatting)
        if language == "python":
            tree = cst.parse_module(code)
            return self.libcst_visitor.extract_features(tree)
        
        # Tree-sitter for other languages
        return self.tree_sitter_parser.parse_and_extract(code, language)
```

### Step 3: Hybrid Pattern Detectors
```python
# Create services/pattern-mining/src/detectors/base.py
from abc import ABC, abstractmethod
import torch
from typing import List, Dict, Tuple

class HybridPatternDetector(ABC):
    @abstractmethod
    async def detect(self, 
                    features: torch.Tensor,
                    code: str,
                    context: Dict) -> List[Pattern]:
        pass

# Create specific detectors
# services/pattern-mining/src/detectors/transformer_detector.py
class TransformerPatternDetector(HybridPatternDetector):
    def __init__(self):
        self.ast_grep_rules = self.load_ast_grep_rules()
        self.codet5_classifier = CodeT5PatternClassifier()
        self.graphcodebert_analyzer = GraphCodeBERTAnalyzer()
        self.gnn_detector = GNNStructuralDetector()
        self.mlscent_integration = MLScentAdapter()
        self.ensemble = WeightedEnsemble()
    
    async def detect(self, features: torch.Tensor, 
                    code: str, context: Dict) -> List[Pattern]:
        # Parallel detection with multiple approaches
        results = await asyncio.gather(
            self.ast_grep_detection(code),
            self.transformer_classification(features),
            self.gnn_structural_analysis(features),
            self.mlscent_ml_specific(code) if context.get('is_ml') else None
        )
        
        # Weighted ensemble with confidence calibration
        patterns = self.ensemble.combine(results, weights={
            'ast_grep': 0.15,      # Fast, deterministic baseline
            'transformer': 0.40,    # High accuracy
            'gnn': 0.35,           # Structural patterns
            'mlscent': 0.10        # ML-specific
        })
        
        # Gemini 2.5 Pro for complex/uncertain cases
        if any(p.confidence < 0.7 for p in patterns):
            patterns = await self.enhance_with_gemini(patterns, code)
            
        return patterns
```

### Step 4: Enhanced BigQuery ML Integration
```python
# Create services/pattern-mining/src/ml/bigquery_ml.py
from google.cloud import bigquery
from google.cloud.bigquery import QueryJobConfig
import dask_bigquery

class EnhancedBigQueryMLPipeline:
    def __init__(self):
        self.client = bigquery.Client()
        self.dataset = "pattern_detection"
        self.dask_client = dask_bigquery.Client()
        
    async def setup_continuous_detection(self):
        """Set up real-time pattern detection with continuous queries"""
        query = """
        CREATE CONTINUOUS QUERY pattern_detection_stream
        WITH (
            job_id_prefix = "pattern_stream_",
            enable_refresh = TRUE,
            refresh_interval_minutes = 1
        ) AS
        SELECT 
            repository_id,
            file_path,
            -- Use Gemini 2.5 Pro for complex pattern analysis
            ML.GENERATE_TEXT(
                MODEL `pattern_detection.gemini_2_5_pro`,
                PROMPT => CONCAT(
                    'Analyze this code for patterns. Context: ', 
                    JSON_EXTRACT_SCALAR(metadata, '$.language'),
                    '\nCode:\n', code_content
                ),
                temperature => 0.1,
                max_output_tokens => 2048
            ) AS gemini_analysis,
            -- Traditional ML model predictions
            ML.PREDICT(
                MODEL `pattern_detection.ensemble_model`,
                (SELECT * FROM UNNEST([STRUCT(
                    features AS features,
                    metrics AS metrics
                )]))
            ) AS ml_predictions,
            CURRENT_TIMESTAMP() AS detection_time
        FROM `repository_analysis.code_stream`
        WHERE event_type = 'new_code'
        """
        await self.client.query(query).result()
    
    async def vector_similarity_search(self, code_embedding: List[float]):
        """Find similar patterns using vector search"""
        query = """
        SELECT 
            pattern_id,
            pattern_type,
            description,
            COSINE_DISTANCE(embedding, @target_embedding) AS similarity
        FROM `pattern_detection.pattern_embeddings`
        WHERE COSINE_DISTANCE(embedding, @target_embedding) < 0.3
        ORDER BY similarity
        LIMIT 10
        """
        job_config = QueryJobConfig(
            query_parameters=[
                bigquery.ArrayQueryParameter(
                    "target_embedding", "FLOAT64", code_embedding
                )
            ]
        )
        return await self.client.query(query, job_config=job_config).result()
```

### Step 5: Advanced Clustering with GPU Acceleration
```python
# Create services/pattern-mining/src/ml/clustering.py
import hdbscan
import cuml  # RAPIDS ML for GPU acceleration
from cuml.cluster import HDBSCAN as cuHDBSCAN
import torch

class AdvancedPatternClusterer:
    def __init__(self):
        # CPU fallback
        self.cpu_clusterer = hdbscan.HDBSCAN(
            min_cluster_size=5,
            min_samples=3,
            metric='euclidean',
            cluster_selection_method='eom'
        )
        
        # GPU-accelerated clustering
        if torch.cuda.is_available():
            self.gpu_clusterer = cuHDBSCAN(
                min_cluster_size=5,
                min_samples=3,
                metric='euclidean',
                gen_min_span_tree=True
            )
            self.use_gpu = True
        else:
            self.use_gpu = False
    
    async def find_new_patterns(self, 
                               features: torch.Tensor,
                               code_samples: List[str]):
        """Discover unknown patterns using advanced clustering"""
        
        # Convert to appropriate format
        if self.use_gpu:
            # GPU clustering with RAPIDS
            features_gpu = cuml.common.input_to_cuml_array(features).array
            clusters = self.gpu_clusterer.fit_predict(features_gpu)
        else:
            # CPU clustering
            features_np = features.cpu().numpy()
            clusters = self.cpu_clusterer.fit_predict(features_np)
        
        # Analyze clusters
        new_patterns = []
        unique_clusters = set(clusters[clusters >= 0])
        
        for cluster_id in unique_clusters:
            cluster_samples = [
                code_samples[i] 
                for i, c in enumerate(clusters) if c == cluster_id
            ]
            
            if len(cluster_samples) >= 5:  # Minimum support
                # Use Gemini to understand the pattern
                pattern_description = await self.analyze_cluster_with_gemini(
                    cluster_samples
                )
                
                new_patterns.append({
                    'cluster_id': cluster_id,
                    'sample_count': len(cluster_samples),
                    'description': pattern_description,
                    'confidence': self.calculate_cluster_confidence(cluster_id),
                    'samples': cluster_samples[:5]  # Top 5 examples
                })
        
        return new_patterns
```

### Step 6: Enhanced API Implementation
```python
# Create services/pattern-mining/src/api/routes.py
from fastapi import BackgroundTasks
from fastapi.responses import StreamingResponse
import asyncio
from typing import AsyncIterator

@app.post("/api/v1/detect")
async def detect_patterns(request: DetectionRequest, 
                         background_tasks: BackgroundTasks):
    """Standard async pattern detection"""
    job_id = str(uuid.uuid4())
    background_tasks.add_task(process_detection, job_id, request)
    return {"job_id": job_id, "status": "processing"}

@app.post("/api/v2/detect/stream")
async def detect_patterns_stream(request: DetectionRequest):
    """Real-time streaming pattern detection"""
    async def generate_patterns() -> AsyncIterator[str]:
        detector = get_pattern_detector()
        
        async for pattern in detector.detect_streaming(request):
            yield f"data: {pattern.model_dump_json()}\n\n"
            
    return StreamingResponse(
        generate_patterns(),
        media_type="text/event-stream"
    )

@app.post("/api/v2/detect/batch")
async def detect_patterns_batch(requests: List[DetectionRequest]):
    """Batch processing for multiple repositories"""
    # Use Dask for distributed processing
    from dask.distributed import as_completed
    
    futures = []
    for req in requests:
        future = dask_client.submit(process_detection_gpu, req)
        futures.append(future)
    
    results = []
    async for future in as_completed(futures):
        result = await future
        results.append(result)
        
    return {"batch_id": str(uuid.uuid4()), "results": results}

@app.get("/api/v2/patterns/explain")
async def explain_pattern(pattern_id: str, code_context: str = None):
    """Get AI-generated explanations for patterns"""
    pattern = await get_pattern_by_id(pattern_id)
    
    explanation = await gemini_client.generate(
        prompt=f"""
        Explain this {pattern.type} pattern:
        Pattern: {pattern.name}
        Description: {pattern.description}
        {f'Code context: {code_context}' if code_context else ''}
        
        Provide:
        1. Why this pattern exists
        2. Potential impacts
        3. Best practices to address it
        """
    )
    
    return {
        "pattern": pattern,
        "explanation": explanation,
        "references": await get_pattern_references(pattern_id)
    }

@app.post("/api/v2/patterns/suggest-fix")
async def suggest_pattern_fix(request: FixRequest):
    """Generate AI-powered fixes for detected patterns"""
    fixes = await fix_generator.generate_fixes(
        pattern_type=request.pattern_type,
        code=request.code,
        context=request.context
    )
    
    return {
        "fixes": fixes,
        "confidence": calculate_fix_confidence(fixes),
        "test_suggestions": generate_test_cases(fixes)
    }
```

### Step 7: Advanced Continuous Learning System
```python
# Create services/pattern-mining/src/ml/feedback_loop.py
from typing import Dict, List
import wandb  # Weights & Biases for ML tracking

class AdvancedFeedbackProcessor:
    def __init__(self):
        self.drift_detector = ModelDriftDetector()
        self.active_learner = ActiveLearningSelector()
        self.wandb_run = wandb.init(project="pattern-detection")
        
    async def process_feedback(self, pattern_id: str, feedback: Feedback):
        """Process user feedback with advanced ML tracking"""
        
        # 1. Validate and enrich feedback
        enriched = await self.enrich_feedback(pattern_id, feedback)
        
        # 2. Update training data in BigQuery
        await self.update_training_data(enriched)
        
        # 3. Check for concept drift
        drift_score = await self.drift_detector.check_drift(
            pattern_type=enriched.pattern_type,
            recent_feedback=await self.get_recent_feedback()
        )
        
        # 4. Active learning - identify uncertain cases
        if enriched.confidence < 0.7:
            await self.active_learner.add_uncertain_sample(
                pattern_id, enriched
            )
        
        # 5. Trigger retraining if needed
        if drift_score > 0.3 or self.active_learner.has_enough_samples():
            await self.trigger_incremental_training(
                pattern_type=enriched.pattern_type,
                priority="high" if drift_score > 0.5 else "medium"
            )
        
        # 6. Update model confidence calibration
        await self.update_confidence_calibration(enriched)
        
        # 7. Log to Weights & Biases
        wandb.log({
            "feedback_type": feedback.type,
            "pattern_type": enriched.pattern_type,
            "confidence": enriched.confidence,
            "drift_score": drift_score,
            "model_version": self.get_model_version()
        })
        
    async def trigger_incremental_training(self, 
                                         pattern_type: str,
                                         priority: str):
        """Trigger model retraining with new data"""
        
        # Fine-tune transformer models
        if pattern_type in ["design_patterns", "anti_patterns"]:
            await self.finetune_codet5(pattern_type)
            
        # Update GNN for structural patterns
        if pattern_type in ["structural_patterns", "architectural_patterns"]:
            await self.update_gnn_model(pattern_type)
            
        # Retrain ensemble weights
        await self.optimize_ensemble_weights()
        
        # A/B test new model version
        await self.setup_ab_test(
            pattern_type=pattern_type,
            traffic_split=0.1  # 10% to new model initially
        )
```

## Validation & Testing

### Required Tests
1. **Unit Tests** (90% coverage)
   ```python
   # tests/test_feature_extraction.py
   def test_ast_feature_extraction():
       # Test with various AST structures
   
   # tests/test_pattern_detection.py
   def test_singleton_detection():
       # Test specific pattern detection
   ```

2. **Integration Tests**
   ```python
   # tests/test_integration.py
   async def test_full_pipeline():
       # 1. Mock AST input
       # 2. Run detection
       # 3. Verify output format
       # 4. Check BigQuery writes
   ```

3. **ML Model Tests**
   ```python
   # tests/test_ml_models.py
   def test_model_performance():
       # Test precision/recall
       # Test with adversarial examples
       # Verify confidence calibration
   ```

### Validation Commands
```bash
# Run tests
cd services/pattern-mining
python -m pytest tests/ -v --cov=src

# Type checking
mypy src/

# Linting
ruff check src/
black --check src/

# Run locally
python -m uvicorn src.main:app --reload --port 8002

# Test pattern detection
curl -X POST http://localhost:8002/api/v1/detect \
  -H "Content-Type: application/json" \
  -d '{"repository_id": "repo_123", "analysis_id": "analysis_456"}'
```

## Enhanced BigQuery Cost Optimization (July 2025)

### Advanced Cost Management Strategies
```sql
-- 1. Partitioned and clustered tables with auto-expiration
CREATE TABLE pattern_results
PARTITION BY DATE(detected_at)
CLUSTER BY repository_id, pattern_type, confidence_score
OPTIONS(
    partition_expiration_days=90,
    require_partition_filter=true
);

-- 2. BI Engine reservation for frequent queries
CREATE RESERVATION pattern_detection_bi_engine
OPTIONS(
    size_gb=100,
    preferred_tables=['pattern_results', 'pattern_embeddings']
);

-- 3. Materialized views for common aggregations
CREATE MATERIALIZED VIEW pattern_trends AS
SELECT 
    pattern_type,
    DATE_TRUNC(detected_at, DAY) as detection_day,
    COUNT(*) as pattern_count,
    AVG(confidence_score) as avg_confidence
FROM pattern_results
WHERE detected_at >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
GROUP BY pattern_type, detection_day;

-- 4. Search indexes for pattern lookups
CREATE SEARCH INDEX pattern_search_idx
ON pattern_results(description, pattern_type)
OPTIONS(analyzer='LOG_ANALYZER');
```

### Enhanced ML Cost Tracking & Optimization
```python
class EnhancedCostMonitor:
    def __init__(self):
        self.cost_thresholds = {
            'gemini_2_5_pro': 0.05,  # $ per 1K tokens
            'bigquery_ml': 0.001,    # $ per MB scanned
            'gpu_compute': 0.10,     # $ per hour
            'storage': 0.02          # $ per GB/month
        }
        
    async def track_ml_operation(self, operation: str, metrics: Dict):
        """Track costs with granular metrics"""
        
        # Calculate costs by resource type
        costs = {
            'compute': self.calculate_compute_cost(metrics),
            'storage': self.calculate_storage_cost(metrics),
            'api_calls': self.calculate_api_cost(metrics),
            'data_processed': self.calculate_data_cost(metrics)
        }
        
        total_cost = sum(costs.values())
        
        # Log to BigQuery with detailed breakdown
        await self.log_detailed_costs(operation, costs, metrics)
        
        # Intelligent alerting
        if total_cost > self.get_dynamic_threshold(operation):
            await self.send_cost_alert(operation, costs)
            
        # Auto-optimization suggestions
        if self.should_optimize(operation, costs):
            optimizations = await self.suggest_optimizations(operation, metrics)
            await self.apply_auto_optimizations(optimizations)
            
    async def suggest_optimizations(self, operation: str, metrics: Dict):
        """AI-powered cost optimization suggestions"""
        
        suggestions = []
        
        # Use lighter models for simple patterns
        if metrics.get('pattern_complexity', 0) < 0.3:
            suggestions.append({
                'action': 'downgrade_model',
                'from': 'gemini_2_5_pro',
                'to': 'rule_based',
                'savings': 0.90
            })
            
        # Batch small requests
        if metrics.get('request_size', 0) < 1000:
            suggestions.append({
                'action': 'enable_batching',
                'batch_size': 50,
                'savings': 0.60
            })
            
        # Use cached embeddings
        if metrics.get('cache_hit_rate', 0) < 0.5:
            suggestions.append({
                'action': 'increase_cache_size',
                'recommended_size': '10GB',
                'savings': 0.40
            })
            
        return suggestions
```

## Development Workflow

### Local Development
```bash
# Set up environment
cd services/pattern-mining
python -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Configure BigQuery emulator
export BIGQUERY_EMULATOR_HOST=localhost:9050

# Run with sample data
make seed-patterns
make dev-pattern-mining
```

### Training Data Management
```bash
# Use training data pipeline
cd ml/training-data
python data-pipeline.py --source github --pattern-type design_patterns

# Validate data quality
python quality/validation-pipeline.py --dataset pattern_training_v1

# Apply weak supervision
python labeling/weak-supervision.py --unlabeled-data raw_patterns
```

## Important Notes

### Service Boundaries
- Only detect patterns, don't fix them
- Depend on Repository Analysis for AST
- Don't store source code
- Provide evidence, not opinions

### ML Best Practices
- Version all models
- Track data lineage
- Monitor for drift
- A/B test new models
- Maintain interpretability

### Error Handling
```python
class PatternDetectionError(Exception):
    pass

class ModelNotFoundError(PatternDetectionError):
    pass

class FeatureExtractionError(PatternDetectionError):
    pass

# Graceful degradation
async def detect_with_fallback(features):
    try:
        return await ml_detect(features)
    except ModelNotFoundError:
        logger.warning("ML model unavailable, using rules")
        return await rule_based_detect(features)
```

## Enhanced Success Criteria (July 2025)
- [ ] Detects 50+ pattern types across 8 categories
- [ ] >92% precision, >85% recall (validated on diverse codebases)
- [ ] Processes 100k LOC in <15 seconds with GPU acceleration
- [ ] Real-time streaming detection with <5 second latency
- [ ] Handles 500 concurrent analyses
- [ ] Advanced clustering discovers 5+ new patterns monthly
- [ ] Continuous learning with drift detection
- [ ] Cost <$0.05 per repository (50% reduction)
- [ ] Full ML pipeline observability with Weights & Biases
- [ ] A/B testing with automatic winner selection
- [ ] Gemini 2.5 Pro integration for complex reasoning
- [ ] Multi-language support via tree-sitter
- [ ] 99.9% API uptime with auto-scaling

## Enhanced Deliverables (July 2025)
1. **Pattern Detection API v2** with streaming and batch endpoints
2. **Advanced ML Pipeline**:
   - Transformer models (CodeT5+, GraphCodeBERT)
   - Graph Neural Networks for structural analysis
   - Gemini 2.5 Pro integration
   - GPU-accelerated processing with RAPIDS
3. **50+ Pattern Detectors** across 8 categories including ML-specific
4. **Comprehensive Test Suite**:
   - Unit tests with 95%+ coverage
   - Integration tests with BigQuery
   - ML model performance tests
   - Load tests for 500 concurrent users
5. **Performance Benchmarks**:
   - Sub-15 second processing for 100k LOC
   - Real-time streaming capabilities
   - GPU vs CPU comparison metrics
6. **Cost Optimization Report**:
   - 50% cost reduction strategies
   - Auto-optimization recommendations
   - ROI analysis
7. **Enhanced Documentation**:
   - Pattern catalog with AI explanations
   - API documentation with examples
   - ML model architecture guide
   - Integration playbooks
8. **Advanced Integrations**:
   - Repository Analysis service
   - Marketplace for pattern sharing
   - IDE plugins via WebSocket
9. **Continuous Learning System**:
   - Automated retraining pipeline
   - A/B testing framework
   - Drift detection monitoring
   - Active learning interface
10. **Monitoring & Observability**:
    - Weights & Biases dashboards
    - Cost tracking dashboard
    - Performance metrics in Grafana
    - Alert system integration

Remember: This service provides intelligence about code quality. Focus on accuracy, explainability, and continuous improvement. False positives erode user trust.